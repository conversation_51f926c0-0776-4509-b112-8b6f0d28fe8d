package utils

import (
	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"strconv"
	"strings"
	"time"
)

//redis链接
func ConnectClusterRedis() *redis.Client {
	DB, _ := strconv.Atoi(config.GetString("redis.DB"))
	client := redis.NewClient(&redis.Options{
		Addr:     config.GetString("redis.Addr"),
		Password: config.GetString("redis.Password"),
		DB:       DB,
	})
	//影响性能问题的判断--先去掉
	//_, err := client.Ping().Result()
	//if err != nil {
	//	panic(err)
	//}
	return client
}

func getRedisDsn(dsn ...string) string {
	if len(dsn) > 0 {
		return dsn[0]
	}

	dsnSlice := []string{
		config.GetString("redis.Addr"),
		config.GetString("redis.Password"),
		config.GetString("redis.DB"),
	}
	//dsnSlice = []string{"172.30.128.56:6379", "MkdGH*3ldf", "0"} //sit1

	//glog.Info(fmt.Sprintf("redis连接参数:%s,%s,%s", config.GetString("redis.Addr"), config.GetString("redis.Password"), config.GetString("redis.DB")))

	return strings.Join(dsnSlice, "|")
}

var (
	redisHandle *kit.DBEngine
)

// 连接池勿关闭
// 获取长链接redis集群客户端
func GetLongRedisConn(dsn ...string) *redis.Client {
	redisDns := getRedisDsn(dsn...)

	startTime := time.Now()
	defer func() {
		if p := recover(); p != nil {
			glog.Errorf("GetRedisConn panic:%+v  redisDns:%s 耗时:%s \n ", p, redisDns, time.Since(startTime))
			//panic(p)
		}
	}()

	if redisHandle != nil {
		_, err := redisHandle.Engine.(*redis.Client).Ping().Result()
		if err == nil {
			return redisHandle.Engine.(*redis.Client)
		}
	}

	redisHandle = kit.NewRedisEngine(redisDns)

	return redisHandle.Engine.(*redis.Client)
}
