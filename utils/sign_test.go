package utils

import "testing"

func TestHmacSha256ToBase64(t *testing.T) {
	type args struct {
		key  string
		data string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "aaaa",
			args: args{
				key:  "h70PzIrW9xtGgKP5RZ5iwhE32Nb1WdAz",
				data: "123456",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := HmacSha256ToBase64(tt.args.key, tt.args.data); got != tt.want {
				t.Errorf("%v", got)
				t.<PERSON><PERSON>("HmacSha256ToBase64() = %v, want %v", got, tt.want)
			}
		})
	}
}
