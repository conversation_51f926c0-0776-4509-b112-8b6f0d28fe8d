package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"math/rand"
	"mime/multipart"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"

	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

var Client60Second *http.Client

// InterfaceToJSON interface 转化成 json 字符串
func InterfaceToJSON(req interface{}) string {
	reqByte, _ := json.Marshal(req)
	return string(reqByte)
}
func init() {

	Client60Second = &http.Client{Timeout: time.Second * 30, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
}

// 通用的post请求
func Post(url string, body []byte, header http.Header) ([]byte, error) {
	if _, has := header["Content-Type"]; !has {
		header["Content-Type"] = []string{"application/json"}
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	req.Header = header

	httpResp, err := Client60Second.Do(req)
	if err != nil {
		return nil, err
	}
	defer httpResp.Body.Close()

	resBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, err
	}

	return resBody, nil
}

// 生成区间随机数
func RandInt64(min, max int64) int64 {
	if min >= max || min == 0 || max == 0 {
		return max
	}
	return rand.Int63n(max-min) + min
}

// 根据环境变量生成特殊字符串
func GenerateSpecialStrByEnv() string {
	newStr := ""
	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	switch env {
	case "sit1":
		newStr = "sit_awen_"
	case "uat":
		newStr = "uat_awen_"
	case "pro":
		newStr = "awen_"
	default:
		newStr = "sit_awen_"
	}
	return newStr
}

// 子龙请求
// dataJson : 数据对象转化成json字符串
func HttpPostZl(url string, dataJson []byte, Headers string) ([]byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	//client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	req.Header.Set("Content-Type", "application/json")

	if len(Headers) > 0 {
		strlist := strings.Split(Headers, "&")
		for i := 0; i < len(strlist); i++ {
			v := strlist[i]
			valuelist := strings.Split(v, "|")
			req.Header.Set(valuelist[0], valuelist[1])
		}
	}

	for k, v := range BjSignMap(url) {
		req.Header.Set(k, v)
	}

	res, err := Client60Second.Do(req)
	if err != nil {
		glog.Error("调用子龙接口失败：", err)
		return []byte(""), err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	return body, err
}

// GetBJZLRequestHeader 北京处理签名头信息
// 1.拼装参数
// Str1： apiSecret={#apiSecret#}&apiStr={#apiStr#}&apiId={#apiId#}&timestamp={#timestamp#}&apiSecret={#apiSecret#}
//
// 参与签名的参数说明，其中如{#apiSecret#}为参数信息，
// apiSecret 加密秘钥（由业务方提供）
// apiId 加密apiID（由业务方提供）
// apiStr 随机字符串（16位）
// timestamp 当前时间时间戳
//
// 2. 进行MD5加密
// Str2:  md5(Str1)
//
// 3. 将加密后的字符串转换为大写
// Str3： strtoupper(Str2);
//
// 生成Str3就是接口签名Sign
//
// 将以下参数通过header传递
// Sign  对应生成的签名字符串，即Str3的值
// Appid  对应 apiId 加密apiID的值
// Apistr 对应 apiStr的值
// Timestamp 对应 timestamp 的值
func GetBJZLRequestHeader() map[string]string {
	apiId := config.GetString("bj_his_api_id")
	apiSecret := config.GetString("bj_his_api_secret")
	apiStr := GenerateRandomStr(16)
	timeStamp := int32(time.Now().Unix())
	signStr := fmt.Sprintf("apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%d&apiSecret=%s", apiSecret, apiStr, apiId, timeStamp, apiSecret)
	h := md5.New()
	h.Write([]byte(signStr))
	sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	bjHeaders := make(map[string]string, 0)
	bjHeaders["BrandId"] = "13"
	bjHeaders["Netorgid"] = "99999"
	bjHeaders["Appid"] = apiId
	bjHeaders["Apistr"] = apiStr
	bjHeaders["Timestamp"] = cast.ToString(timeStamp)
	bjHeaders["Sign"] = sign
	return bjHeaders
}

// 随机字符串
func GenerateRandomStr(size int) string {
	iKind, kinds, result := 3, [][]int{{10, 48}, {26, 97}, {26, 65}}, make([]byte, size)
	rand.Seed(time.Now().UnixNano())
	for i := 0; i < size; i++ {
		iKind = rand.Intn(3)
		scope, base := kinds[iKind][0], kinds[iKind][1]
		result[i] = uint8(base + rand.Intn(scope))
	}
	return string(result)
}

// 获取本周一日期
func GetFirstDateOfWeek() (weekMonday string) {
	now := time.Now()

	offset := int(time.Monday - now.Weekday())
	if offset > 0 {
		offset = -6
	}

	weekStartDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, offset)
	weekMonday = weekStartDate.Format("2006-01-02")
	return
}

// 获取本周日 日期
func GetLastWeekDate() (weekMonday string) {
	thisWeekMonday := GetFirstDateOfWeek()
	TimeMonday, _ := time.Parse("2006-01-02", thisWeekMonday)
	lastWeekMonday := TimeMonday.AddDate(0, 0, +6)
	weekMonday = lastWeekMonday.Format("2006-01-02")
	return
}

// 计算健康值 = 四舍五入(payAmount(四舍五入)*0.8)
func CalculateHealthVal(payAmount float64) int64 {
	return int64(math.Round(payAmount * 0.8))
}

func ArrayStringDiff(array1 []string, arrayOthers ...[]string) []string {
	c := make(map[string]bool)
	for i := 0; i < len(array1); i++ {
		if _, hasKey := c[array1[i]]; hasKey {
			c[array1[i]] = true
		} else {
			c[array1[i]] = false
		}
	}
	for i := 0; i < len(arrayOthers); i++ {
		for j := 0; j < len(arrayOthers[i]); j++ {
			if _, hasKey := c[arrayOthers[i][j]]; hasKey {
				c[arrayOthers[i][j]] = true
			} else {
				c[arrayOthers[i][j]] = false
			}
		}
	}
	result := make([]string, 0)
	for k, v := range c {
		if !v {
			result = append(result, k)
		}
	}
	return result
}

func InArrayString(s string, strs []string) bool {
	for _, v := range strs {
		if s == v {
			return true
		}
	}
	return false
}

func StringToArrrayInt(str string) []int {
	arr := make([]int, 0)
	strArr := strings.Split(str, ",")
	for _, v := range strArr {
		if i := cast.ToInt(v); i > 0 {
			arr = append(arr, i)
		}
	}
	return arr
}

// StrToTime 任意日期时间（至少包含年月日）字符串转时间
func StrToTime(str string) (*time.Time, error) {
	// -----------------------年-------月-----------日-----------时----------------分--------------秒----------------
	reg, _ := regexp.Compile(`^(\d{4})[/-](\d{1,2})[/-](\d{1,2})(?:\s+(\d{1,2}))?(?:\:(\d{1,2}))?(?:\:(\d{1,2}))?$`)
	subs := reg.FindStringSubmatch(str)
	if len(subs) == 0 {
		return nil, errors.New("时间格式错误")
	}

	subs = subs[1:]
	// 补全数据
	for i, sub := range subs {
		subs[i] = fmt.Sprintf("%02s", sub)
	}

	if t, err := time.ParseInLocation("20060102150405", strings.Join(subs, ""), time.Local); err != nil {
		return nil, errors.New("时间格式错误")
	} else {
		return &t, nil
	}
}

// 返回一个32位md5加密后的字符串
func GetMD5Encode(data string) string {
	h := md5.New()
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// 返回一个16位md5加密后的字符串
func Get16MD5Encode(data string) string {
	return strings.ToUpper(GetMD5Encode(data)[8:24])
}

// UploadFile 上传文件需要的信息
// 文件路径方法 reader, err := os.Open(fileName)
// excel方式 b,err := file.WriteToBuffer();reader = bytes.NewReader(b.Bytes())
type UploadFile struct {
	Name   string
	Reader io.Reader
}

// UploadQiNiuResponse 上传到七牛响应
type UploadQiNiuResponse struct {
	FileName string
	Size     int
	Url      string
	Error    string
}

// UploadExcelToQiNiu1 上传excel文件到七牛云 流的方式
func UploadExcelToQiNiu1(file *excelize.File, name string) (url string, err error) {
	b, err := file.WriteToBuffer()
	if err != nil {
		return
	}
	if len(name) < 1 {
		name = kit.GetGuid36() + ".xlsx"
	}
	uf := &UploadFile{
		Name:   name,
		Reader: bytes.NewReader(b.Bytes()),
	}

	return uf.ToQiNiu()
}

// ToQiNiu 上传文件到七牛云
func (uf *UploadFile) ToQiNiu() (url string, err error) {
	if len(uf.Name) < 1 {
		return "", errors.New("文件名称不能为空")
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", uf.Name)
	if _, err = io.Copy(part, uf.Reader); err != nil {
		return
	}
	if err = writer.Close(); err != nil {
		return
	}

	host := config.GetString("file-upload-url")
	if len(host) == 0 {
		host = "https://api.rp-pet.com"
	}
	httpResp, err := http.Post(host+"/fss/newup", writer.FormDataContentType(), body)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()

	resBody, err := ioutil.ReadAll(httpResp.Body)
	if err != nil {
		return
	}

	res := new(UploadQiNiuResponse)
	if err = json.Unmarshal(resBody, res); err != nil {
		return "", errors.New("解析响应body出错 " + err.Error())
	}
	if httpResp.StatusCode >= 400 {
		if len(res.Error) == 0 {
			res.Error = httpResp.Status
		}
		return "", errors.New("请求出错 " + res.Error)
	}

	res.Url = strings.Replace(res.Url, "http://", "https://", 1)

	return res.Url, nil
}

// 手机号星号代替
func MobileReplaceWithStar(mobile string) string {
	if len(mobile) == 0 {
		return mobile
	}
	if len(mobile) < 8 {
		return mobile + strings.Repeat("*", 11-len(mobile))
	}
	return mobile[0:3] + "****" + mobile[7:]
}
