package utils

import (
	"errors"
	"fmt"
	kit "github.com/tricobbler/rp-kit"
	"runtime"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/streadway/amqp"
	"github.com/tricobbler/rp-kit/cast"
)

//开启一个mq链接
func NewMqConn() *amqp.Connection {
	url := config.GetString("mq.oneself")
	//url = "************************************************************" //临时写死 配置中心加不了配置
	glog.Info("RabbitMQ地址：", url)
	conn, err := amqp.Dial(url)
	if err != nil {
		panic("RabbitMQ dial fail. err : " + err.Error())
	}
	return conn
}

func NewMqChannel(conn *amqp.Connection) *amqp.Channel {
	channel, err := conn.Channel()
	if err != nil {
		panic("RabbitMQ Get Channel fail. err : " + err.Error())
	}
	return channel
}

/***************************************MQ********************************************/
//发布推送消息到RabbitMQ，参数：队列名和内容
func PublishRabbitMQ(queue, content, exchange string) bool {
	defer func() {
		if errPanic := recover(); errPanic != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER] %v %s\n", errPanic, stack[:length])
		}
	}()

	glog.Info(fmt.Sprintf("推送MQ开始！队列名，%s，内容：%s，Exchange：%s", queue, content, exchange))
	//开启连接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	// 定义交换“direct”、“fanout”、“topic”和“headers”
	if err := ch.ExchangeDeclare(exchange, amqp.ExchangeDirect, true, false, false, false, nil); nil != err {
		glog.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
		return false
	}
	// name,durable,delete when unused,exclusive,no-wait,arguments
	q, err := ch.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueDeclare fail, err : ", q.Name, err)
		return false
	}
	err = ch.QueueBind(queue, queue, exchange, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueBind fail, err : ", err)
		return false
	}

	_publishing := amqp.Publishing{
		ContentType:  "text/plain",
		Body:         []byte(content),
		DeliveryMode: amqp.Persistent, // Transient 性能好，但是会丢数据。故用 Persistent
	}
	//a.b.c.d.e 为发布key,以.分割；
	if err = ch.Publish(exchange, queue, false, false, _publishing); nil != err {
		glog.Error("RabbitMQ Publish fail, err : ", err)
		return false
	}
	glog.Info(fmt.Sprintf("推送MQ结束！队列名，%s，内容：%s，Exchange：%s", queue, content, exchange))
	return true
}

//发送可自定义参数消息
func PublishRabbitMQV2(routeKey, exchange, content string, amqpParams ...interface{}) (err error) {
	defer func() {
		if errPanic := recover(); errPanic != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER] %v %s\n", errPanic, stack[:length])

			err = errors.New("异常：" + fmt.Sprintln(errPanic))
		}
	}()

	glog.Infof("推送MQ开始！Exchange：%s，route，%s，内容：%s, %v", exchange, routeKey, content, kit.JsonEncode(amqpParams))
	//开启连接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	_publishing := amqp.Publishing{
		ContentType:  "text/plain",
		Body:         []byte(content),
		DeliveryMode: amqp.Persistent, // Transient 性能好，但是会丢数据。故用 Persistent
	}

	//设置过期时间
	if len(amqpParams) > 0 {
		_publishing.Expiration = cast.ToString(amqpParams[0])

	}

	if err = ch.Publish(exchange, routeKey, false, false, _publishing); nil != err {
		glog.Error("RabbitMQ Publish fail, err : ", err)
	}

	return
}

func PublishRabbitMQV1(routeKey, exchange, content string, amqpParams ...interface{}) (err error) {
	defer func() {
		if errPanic := recover(); errPanic != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER] %v %s\n", errPanic, stack[:length])

			err = errors.New("异常：" + fmt.Sprintln(errPanic))
		}
	}()

	glog.Infof("推送MQ开始！Exchange：%s，route，%s，内容：%s, %v", exchange, routeKey, content, kit.JsonEncode(amqpParams))
	//开启连接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	_publishing := amqp.Publishing{
		ContentType:  "text/plain",
		Body:         []byte(content),
		DeliveryMode: amqp.Persistent, // Transient 性能好，但是会丢数据。故用 Persistent
	}

	//设置过期时间
	if len(amqpParams) > 0 {
		//_publishing.Expiration = cast.ToString(amqpParams[0])

		_publishing.Headers = amqp.Table{"x-delay": cast.ToInt32(amqpParams[0])}
	}

	if err = ch.Publish(exchange, routeKey, false, false, _publishing); nil != err {
		glog.Error("RabbitMQ Publish fail, err : ", err)
	}

	return
}

//订阅mq消息
func SubscribeRabbitMQ(queue, exchange string) ([]byte, error) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error(err)
		}
	}()

	//glog.Info(fmt.Sprint("订阅MQ开始！队列名，%s，Exchange：%s", queue, exchange))
	//开启链接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	err := ch.ExchangeDeclare(exchange, "direct", true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
		return nil, err
	}
	// name// durable// delete when unused // exclusive// no-wait // arguments
	q, err := ch.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueDeclare fail, err : ", q.Name, err)
		return nil, err
	}
	err = ch.QueueBind(queue, queue, exchange, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueBind fail, err : ", q.Name, err)
		return nil, err
	}

	if delivery, err := ch.Consume(queue, queue, false, false, false, false, nil); err != nil {
		glog.Error("RabbitMQ Consume fail, err : ", q.Name, err)
		return nil, err
	} else {
		for d := range delivery {
			func() {
				defer func() {
					if err := recover(); err != nil {
						glog.Error(err)
					}
				}()
				//todo 处理业务逻辑
				fmt.Println(d)

			}()
		}
	}
	return nil, nil
}

func Consume(queue, key, exchange string, fun func(request amqp.Delivery) (response string, err error)) {
	conn := NewMqConn()
	if conn == nil {
		glog.Error("conn is nil")

		return
	}
	defer conn.Close()

	ch := NewMqChannel(conn)
	if ch == nil {
		glog.Error("ch is nil")
		return
	}
	defer ch.Close()

	err := ch.QueueBind(queue, key, exchange, false, nil)
	if err != nil {
		glog.Error(queue, "，", key, "，", exchange, "，", err.Error())
		return
	}

	delivery, err := ch.Consume(queue, queue, false, false, false, false, nil)
	if err != nil {
		glog.Error(err)
	}

	for {
		for d := range delivery {
			func() {
				defer func() {
					if err := recover(); err != nil {
						glog.Error(err)
					}
				}()

			}()
			fun(d) // 业务处理
		}
		time.Sleep(time.Millisecond * 500)
	}
}
