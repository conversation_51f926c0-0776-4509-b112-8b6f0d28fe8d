package utils

const (
	DATE_LAYOUT      = "2006-01-02"
	TIME_LAYOUT      = "15:04:05"
	DATE_TIME_LAYOUT = "2006-01-02 15:04:05"

	// 订单推送子龙失败重推
	QueueZlOrderBinLog = "dc-sz-customer-center-zl-order-binlog"

	MQExchange = "customercenter"
)

var (
	//子龙线下支付类型
	PaymentType = map[int32]struct{}{
		1:  {},
		2:  {},
		3:  {},
		4:  {},
		5:  {},
		6:  {},
		7:  {},
		8:  {},
		9:  {},
		10: {},
		11: {},
		12: {},
		36: {},
		37: {},
		38: {},
		39: {},
		40: {},
		42: {},
		43: {},
		44: {},
	}
	MessageTitle = map[string]string{
		"user-level-change":     "会员等级变更通知",
		"user-level-expire":     "会员等级过期通知",
		"user-integral-change":  "积分变更通知",
		"user-integral-expire":  "积分到期提醒",
		"user-voucher-expire":   "优惠券即将过期提醒",
		"register-queuing":      "医院预约队列提醒",
		"register-confirm":      "门店预约确认提醒",
		"register-tobe-confirm": "门店预约待确认通知",
		"register-store-remind": "门店预约到店提醒",
		"vr-code-use":           "兑换码核销通知",
		"vr-code-expire":        "兑换码即将过期通知",
		"vip-card-expire":       "会员权益过期通知",
	}
)
