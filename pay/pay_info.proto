syntax = "proto3";
package pay;
import "google/api/annotations.proto";
import "pay/common.proto";


message UnifiedOrderRequest{
  // 支付方式    1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）
  // 13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付 17：标准支付宝小程序支付 18：百度小程序支付
  int32 TransType = 1;
  //商户流水号
  string OutTradeNo = 2;
  //交易金额
  int32 PayPrice = 3;
  //总金额
  int32 TotalPrice = 4;
  //优惠金额
  int32 Discount = 5;
  string ProductId = 6;
  //商品名称
  string ProductName = 7;
  //商品描述
  string ProductDesc = 8;
  string ClientIP = 9;
  //微信 JSAPI 支付时必传
  string Openid = 10;
  //商户号
  string MerchantId = 11;
  //后台回调地址（支付中心回调电商）
  string NotifyUrl = 12;
  //扩展信息 预留字段，JSON 格式
  string ExtendInfo = 13;
  //子商户公众账号 ID
  string SubAppId = 14;
  //商户订单号
  string OrderId = 15;
  //订单有限时间(分钟)
  int32 ValidTime = 16;
  //交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
  string OrderPayType = 17;
  // appId 应用id，1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS，6：上海兽丘，7：极宠家
  int32 AppId = 18;
}

message UnifiedOrderReturnData{
  //微信appid
  string appId = 1;
  //随机字符串，不长于 32位
  string nonceStr = 2;
  //订单详情扩展字符串
  string package = 3;
  //签名
  string paySign = 4;
  //签名方式
  string signType = 5;
  //时间戳
  string timeStamp = 6;
}

message UnifiedOrderResponse {
  int32 code = 1;
  string message = 2;
  UnifiedOrderResponseData details = 3;
  //支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
  int32 pay_type = 4;
}
message UnifiedOrderResponseData {
  //当支付方式为wx_jsapi返回
  UnifiedOrderReturnData wx_jsapi = 1;
  string order_id = 2;
  //当支付方式为WX_NATIVE,AL_NATIVE返回
  UnifiedOrderWxNative wx_native = 3;
  //当支付方式为WX_JSAPP返回
  UnifiedOrderWxJsApp wx_js_app = 4;
  //当支付方式是网银
  string bank = 5;
  // 支付宝小程序交易单号
  string aliTradeNo = 6;
  // 百度支付订单信息
  UnifiedOrderBdPay bd_order_info = 7;
}

message PayInfoQueryRequest{
  //商户订单号
  string order_id = 1;
  //商户号
  string merchant_id = 2;
  // 应用id，1：阿闻，2：子龙，3：R1，4：互联网
  int32 app_id = 3;
  //支付方式
  int32 pay_type =4;
  //商品描述
  string  product_desc=5;
}
message PayInfoQueryResponse{
  int32 code = 1;

  string message = 2;
  payInfo data = 3;
  message payInfo{
    // 原交易订单号
    string order_id = 1;
    // 交易状态
    string trans_state = 2;
    // 交易金额
    int32 trans_amt = 3;
    // 交易手续费(返回空就是手续费没结算)
    string fee_amt = 7;
    // 订单创建时间
    string order_time = 4;
    // 支付完成时间
    string pay_time = 5;
    // 支付流水号
    string trade_no = 6;
  }
}

message  UnifiedOrderWxJsApp{
  //transType 为 WX_JSAPP/AL_JSAPP 时返回
  string jsAppId = 1;
  //transType 为 WX_JSAPP/AL_JSAPP 时返回
  string jsAppUrl = 2;
}

message UnifiedOrderWxNative{
  //支付链接
  string payUrl = 1;
}
// 百度支付信息
message UnifiedOrderBdPay {
  string dealId = 1;       // 跳转百度收银台支付必带参数之一，是百度收银台的财务结算凭证
  string appKey = 2;          // 支付能力开通后分配的支付 appKey，用以表示应用身份的唯一 ID
  string totalAmount = 3;     // 订单金额（单位：人民币分）。注：小程序测试包测试金额不可超过 1000 分
  string tpOrderId = 4;       // 小程序开发者系统创建的唯一订单 ID ，当支付状态发生变化时，会通过此订单 ID 通知开发者。
  string notifyUrl = 5;       // 通知开发者支付状态的回调地址，必须是合法的 URL ，与开发者平台填写的支付回调地址作用一致，未填写的以平台回调地址为准
  string dealTitle = 6;       // 订单的名称
  string signFieldsRange = 7; // 用于区分验签字段范围，signFieldsRange 的值：0：原验签字段 appKey+dealId+tpOrderId；1：包含 totalAmount 的验签，验签字段包括appKey+dealId+tpOrderId+totalAmount。固定值为 1
  string rsaSign = 8;         // 对appKey+dealId+totalAmount+tpOrderId进行 RSA 加密后的签名，防止订单被伪造
  UnifiedOrderBdPayBizInfo bizInfo  = 9;
}
message UnifiedOrderBdPayBizInfo {
  BizInfoTpData tpData = 1;
}
message BizInfoTpData {
  string dealId = 1;
  string appKey = 2;
  string totalAmount = 3;
  string tpOrderId = 4;
}

message BaiduOrderRefundGetRequest {
  string tradeNo = 1;
}
message BaiduOrderRefundGetResponse {
  // 开发者退款批次id
  string bizRefundBatchId = 1;
  // 退款批次id
  int64 refundBatchId = 2;
  // 退款状态 1 退款中 2 退款成功 3 退款失败
  int64 refundStatus = 3;
  // 百度退款订单号
  int64 orderId = 4;
  // 退款用户id
  int64 userId = 5;
}


message PayAsynNoticeRequest{
  //交易状态 S 成功(失败不通知)
  string transState = 1;
  //商户订单号 仅能用大小写字母与数字，且在商户系统具有唯一性
  string orderId = 2;
  //签名
  //支付流水号,电银生成的流水号
  string tradeNo = 3;
  //扣款通道返回的流水号
  string channelNo = 4;
  //支付时间
  string payTime = 5;
}

// @Desc			    支付订单状态查询请求
// <AUTHOR>
// @Date		 		2020-06-24
message PayQueryRequest{
  string MerchantId = 1;       // 商户号
  string TradeNo = 2;       // 商户订单号
  string Sign = 3;       // 签名
}

// @Desc			    支付订单查询响应
// <AUTHOR>
// @Date		 		2020-06-24
message PayQueryResponse{
  string  order_id = 1;  // 订单号
  string  order_time = 2;  // 交易时间
  string  pay_trade_no = 3;  // 交易流水号
  int32   total_price = 4;  // 订单金额
  int32   pay_price = 5;  // 支付金额
  int32   refund = 6;  // 退款金额
  int32   pay_status = 7;  // 订单状态
  string  status = 8;  // 状态
  repeated PayRefundDetail refund_detail = 9;  // 订单退款明细
}

// @Desc			    订单退款明细
// <AUTHOR>
// @Date		 		2020-06-24
message PayRefundDetail{
  string  order_id = 1;  // 订单号
  string  refund_trade_no = 2;  // 退款流水
  int32   refund_status = 3;  // 退款状态
  int32   refund_amount = 4;  // 退款金额
  string  extend_info = 5;  // 商户私有域：交易返回时原样返回给商户网站，给商户备用
  string  back_param = 6;  // 扩展信息：预留字段，JSON 格式
  string  status = 7;  // 状态
}
message StdBindingRequest{
  //机构号（需要电银提供）
  string orgNumber = 1;
  //*必传，机具编号(自定义)，新规规则，需要带前缀90000011，示例：90000011RP0045
  string tsn = 2;
  //电银商户号（需要电银提供）
  string dyMchNo = 3;
  //机具来源
  //1 – 外部代理商(默认)
  //2 – 电银代理商
  //（当机具来源为“电银代理商”时, 外部终
  //端号、终端厂家、终端型号非必传）
  string snSource = 4;
  //终端号（自定义） 以绑定结果返回的终端号为准
  string dyTermNo = 5;
  //机具厂商编号（需要电银提供）
  string termFactory = 6;
  //机具型号编号（需要电银提供）
  string termModel = 7;
  //*必传，终端名称（门店名称）
  string termName = 8;
  //*必传，终端地址（门店地址）
  string termAddress = 9;
  //*必传，门店财务编码
  string shop_id = 10;
  //终端类型，默认15
  string termType = 11;
  //支付方式
  int32 payType = 12;
}
message StdBindingResponse{
  //返回码
  int32 code = 1;
  //终端号
  string dyTermNo = 2;
  string message = 3;
}

message StdUnBindingRequest{
  //机构号
  string orgNumber = 1;
  //机具编号
  string tsn = 2;
  //电银商户号
  string dyMchNo = 3;
  //支付方式
  int32 payType = 4;
}
message QueryStsRequest{
  //机构号
  string orgNumber = 1;
  //机具编号
  string tsn = 2;
}
message QueryStsResponse{
  //返回码
  int32 code = 1;
  string message = 2;
  //电银商户号
  string dyMchNo = 3;
  //电银终端号
  string dyTermNo = 4;
  //电银状态0正常 1关闭
  string status = 5;
}
message QueryPayStatusRequest{
  //支付中心流水号
  string trade_no = 1;
  //交易码，默认值：PF0 银联云闪付：QRY1 动码查询：PF420 云闪付动码：QRY4
  string trancde = 2;
  //请求头信息
  ScanHeadBase head_base = 3;
  // 应用id，1：阿闻，2：子龙，3：R1，4：互联网
  int32 app_id = 4;
}
message QueryPayStatusResponse{
  //返回码
  int32 code = 1;
  string message = 2;
  //商户订单号
  string trade_no = 3;
  //订单总金额 单位：分
  string pay_amount = 4;
  //支付结果 I：待支付 S：成功 R：正在执行 F：失败 T：成功有退款 C：已撤销 O：交易关闭
  string pay_result = 5;
  //电银流水号
  string pay_no = 6;
  //支付时间
  string pay_time = 7;
  //订单号
  string order_no = 8;
}

message PayForB2CRequest{
  //订单号
  string mer_order_no = 1;
  //付款码
  string bar_code = 2;
  //支付方式 1：微信 2：支付宝 3: 银联
  int32 pay_type = 3;
  //实际支付金额
  int32 pay_amount = 4;
  //不参与优惠金额
  string undiscountable_amount = 5;
  //订单名称 银联时否
  string order_name = 6;
  //订单描述
  string order_desc = 7;
  //订单有效期单位 00-分 01-小时 02-日 03-月
  string validUnit = 8;
  //订单有效期单位 结合单位一起使用
  string validNum = 9;
  //扫码请求头信息
  ScanHeadBase head_base = 13;
  //外部订单号
  string out_order_no = 14;
  //订单总金额
  int32 total_amount = 15;
  //优惠
  int32 discount = 16;
  //后台回调地址（支付中心回调电商）
  string NotifyUrl = 17;
  //终端类型，默认15
  string termType = 18;
  // ip
  string client_ip = 19;
  // 应用id，1：阿闻，2：子龙，3：R1，4：互联网
  int32 app_id = 20;
  // 扩展字段
  string extend_info = 21;
  string location=22;
}
message YLGoodsDetail{
  //订单信息
  YLOrderInfo orderInfo = 1;
  //商品明细
  repeated YLGoodsInfo goodsInfo = 2;
}
message YLOrderInfo{
  //订单标题
  string title = 1;
  //订单描述
  string description = 2;
  //优惠金额
  string dctAmount = 3;
  //附加内容
  string addnInfo = 4;
}
message YLGoodsInfo{
  //商品编号
  string id = 1;
  //商品名称
  string name = 2;
  //商品单价
  string price = 3;
  //商品数量
  string quantity = 4;
  //商品类目
  string category = 5;
  //附加信息
  string addnInfo = 6;
}
message WXGoodsDetail{
  //订单原价
  string cost_price = 1;
  //商品小票ID
  string receipt_id = 2;
  //商品列表
  repeated WXGoodsInfo goods_detail = 3;
}
message WXGoodsInfo{
  //商品编号（由半角的大小写字母、数字、中划线、下划线中的一种或几种组成）
  string goods_id = 1;
  //微信侧商品编码，微信支付定义的统一商品编号（没有可不传）
  string wxpay_goods_id = 2;
  //商品名称
  string goods_name = 3;
  //商品数量
  string quantity = 4;
  //商品单价（单位为：分。如果商户有优惠，需传输商户优惠后的 单价(例如：用户对一笔 100 元的订单使用了商场发的优惠券 100-50，则活动商品的单价应为原单价-50)）
  string price = 5;
}
message AliGoodsDetail{
  //商品编号（由半角的大小写字母、数字、中划线、下划线中的一种或几种组成）
  string goods_id = 1;
  //微信侧商品编码，微信支付定义的统一商品编号（没有可不传）
  string alipay_goods_id = 2;
  //商品名称
  string goods_name = 3;
  //商品数量
  string quantity = 4;
  //商品单价（单位为：分。如果商户有优惠，需传输商户优惠后的 单价(例如：用户对一笔 100 元的订单使用了商场发的优惠券 100-50，则活动商品的单价应为原单价-50)）
  string price = 5;
  //类目
  string goods_category = 6;
  //展示内容
  string body = 7;
  //展示地址
  string show_url = 8;
}

message PayForB2CResponse{
  //返回码
  int32 code = 1;
  string message = 2;
  //商户订单号
  string mer_order_no = 3;
  //订单总金额 单位：分
  string pay_amount = 4;
  //支付结果 S：成功 R：正在执行 F：失败
  string pay_result = 5;
  //电银流水号
  string pay_no = 6;
  //支付时间
  string pay_time = 7;
  //支付中心流水号
  string trade_no = 8;
}

message DYPayRequest{
  //订单号
  string order_no = 1;
  //交易码,P00：默认值，支持微信、支付宝、云闪付、电银支付等；CSU01：仅支持云闪付（老接口兼容性保留值）；
  string trancde = 2;
  //付款码,用户用银联支付宝、微信生成的付款码
  string bar_code = 3;
  //实付金额,单位：分
  int32 pay_amount = 4;
  //订单名称
  string order_name = 5;
  //支付方式 1：微信 2：支付宝 3: 银联
  int32 pay_type = 6;
  //总金额,单位：分
  int32 pay_total = 7;
  //优惠金额,单位：分
  int32 discount = 8;
  //回调地址
  string notify_url = 9;
  //传机具编号（tsn）
  string trm_sn = 10;
  //终端号，传标准终端绑定接口返回的dyTermNo
  string trm_id = 11;
}

message DYPayResponse{
  //返回码
  int32 code = 1;
  string message = 2;
  //商户订单号
  string order_no = 3;
  //订单总金额 单位：分
  string pay_amount = 4;
  //支付结果 S：成功 R：正在执行 F：失败
  string pay_result = 5;
  //电银流水号
  string pay_no = 6;
  //支付时间
  string pay_time = 7;
  //支付中心订单号
  string trade_no = 8;
}


message QueryPwdRequest {
  string mobile = 1;
  int32 type = 2;
  string order_sn = 3;
}

message QueryPwdResponse{
  //返回码
  int32 code = 1;
  string message = 2;
  //1设置过  0未设置
  int32 is_pwd = 3;
}

message SetPwdRequest {
  //手机号
  string mobile = 1;
  //密码（MD5）
  string password = 2;
  //scrm_id
  string scrm_id = 3;
}

message UpdateByMobileRequest {
  //手机号
  string mobile = 1;
  //密码（MD5）
  string password = 2;
  //短信验证码
  string code = 3;
  //scrm_id
  string scrm_id = 4;
}

message UpdateByPwdRequest {
  //手机号
  string mobile = 1;
  //原密码（MD5）
  string old_password = 2;
  //新密码（MD5）
  string new_password = 3;
  //scrm_id
  string scrm_id = 4;
}

message PayListResponse {
  int32 code = 1;
  string message = 2;
  repeated PayMethod data = 3;
}

message PayMethod {
  int32 id = 1;
  // 支付方式
  string pay_method = 2;
  //是否开启
  int32 is_open = 3;
}

message PayConfigResponse {
  int32 code = 1;
  string message = 2;
  repeated PayConfig data = 3;
}

message PayConfig {
  int32 id = 1;
  // 支付方式
  string pay_method = 2;
  //是否开启
  int32 is_open = 3;
  //分组
  int32 group_id = 4;
}

message CheckCodeRequest {
  // 手机号
  string mobile = 1;
  // 验证码
  string code = 2;
}

message CheckCodeResponse {
  int32 code = 1;
  string message = 2;
  // 验证码是否正确 1是 0否
  int32 is_true = 3;
}

message GetCardsBalanceReq{
  string scrm_id = 1;
}

message GetCardsBalanceRes{
  int32 code = 1;
  string message = 2;
  //
  CardsBalanceData data = 3;
}
message CardsBalanceData {
  //余额（本金+赠送金额)
  string balance = 1;
  //可用余额（本金-（本日充值金额0.3））
  string available_balance = 2;
  //赠送金额
  string gift_amount = 3;
}

message GetCardsReq{
  string scrm_id = 1;
  //财务编码
  string finance_code = 2;
  //订单支付金额（单位分）
  int32 pay_money = 3;
  //渠道Id 阿闻：1，阿闻电商：5
  int32 channel_id = 4;
}

message GetCardsRes{
  int32 code = 1;
  string message = 2;
  //
  GetCardsData data = 3;
}
message GetCardsData{
  //是否可以支付 1:是,0:否
  int32 is_pay = 1;
  //财务编码
  repeated CardData  cards = 2;
}
message CardData{
  int32 card_id = 1;
  //卡号
  string card_number = 2;
  //卡名称
  string card_name = 3;
  //创建日期（根据有效期判断卡是否过期）
  string create_time = 4;
  //有效期（根据有效期判断卡是否过期）
  string expiry_date = 5;
  //本金余额(单位元)
  float corpus_balance = 6;
  //赠送余额(单位元)
  float present_balance = 7;
  //是否连锁 1：是 0：否
  int32 chained = 8;
  //开卡门店(财务编码)
  string org_id = 9;
  //开卡门店名
  string org_name = 10;
  //是否可用 1：是 0：否
  int32 is_available = 11;
  //是否选中 1：是 0：否
  int32 is_checked = 12;
  //选中的卡支付金额(单位分)
  int32 pay_amount = 13;
}

message CardPayReq{
  //财务编码
  string finance_code = 1;
  //交易流水号（调用支付中心返回）
  string order_sn = 2;
  //scrm_id
  string scrm_id = 3;
  //订单来源:01商城，02阿闻到家，03挂号
  string order_source = 4;
  //交易类型
  string order_type = 5;
  //支付密码
  string pay_pwd = 6;
  //赠送金额(单元分)
  int32 present_money = 7;
  //是否连锁 1：是 0：否
  repeated CardsPayDetails cards = 8;
  //支付方式 0支付密码支付  1短信验证支付
  int32 pay_type = 9;
  //手机号
  string mobile = 10;
  //短信验证码
  string sms_code = 11;
}
message CardsPayDetails{
  int32 card_id = 1;
  //支付金额(单位元)
  int32 pay_money = 2;
}

message CardsRefundReq{
  //退款订单号
  string refund_order_sn = 1;
  //退款金额(单位分)
  int32 refund_money = 2;
  //财务编码
  string finance_code = 3;
  //scrm_id
  string scrm_id = 4;
  //订单来源:01商城，02阿闻到家，03挂号
  string order_source = 5;
  //订单号
  string order_sn = 6;
}

message CardOrderRecordsReq{
  //订单号
  string order_sn = 1;
  //交易类型3：消费扣款 4：消费退款
  int32 operate_type = 2;
}

message  CardOrderRecordsRes{
  int32 code = 1;
  string message = 2;
  //
  repeated CardOrderRecordData data = 3;
}

message CardOrderRecordData{
  //发生时间
  string occur_time = 1;
  //发生地点（医院机构Id）
  string occur_org_id = 2;
  //本金发生金额
  float corpus_change = 3;
  //交易类型,3：消费扣款 4：消费退款
  string operate_type = 4;
  //卡id
  int32 card_id = 5;
  //卡号
  string card_number = 6;
  //卡名称
  string card_name = 7;
}

message CardRecordsReq{
  int32 card_id = 1;
  int32 page_index = 2;
  //
  int32 page_size = 3;
}
message CardRecordsRes{
  int32 code = 1;
  string message = 2;
  //
  repeated CardRecordData data = 3;

  int32 data_count = 4;
}

message CardRecordData{
  int32 card_id = 1;
  //卡号
  string card_number = 2;
  //流水号（子龙)
  string record_number = 3;
  //第三方流水号
  string pay_number = 4;
  //发生时间
  string occur_time = 5;
  //发生地点（财务编码）
  string occur_org_id = 6;
  //赠送余额(单位元)
  float corpus_before_change = 7;
  //本金部分发生金额
  float corpus_change = 8;
  //本金余额
  float corpus_after_change = 9;
  //赠送部分发生前金额
  float present_before_change = 10;
  //赠送部分发生金额
  float present_change = 11;
  //赠送部分余额
  float present_after_change = 12;
  //交易类型 1：开卡 ， 2：充值  3：消费扣款  4：消费退款，6：客户退卡
  int32 operate_type = 14;
  //发生地点（医院机构名称）
  string occur_org_name = 15;
}

message PaySetRequest {
  repeated PaySetData data = 1;
}

message PaySetData {
  int32 id = 1;
  // 1开启，2关闭
  int32 is_open = 2;
}


message  CheckPayTypeReq{
  //财务编码
  string finance_code = 1;
  //支付金额(单位分)
  int32 pay_money = 2;
  //scrm_id
  string scrm_id = 3;
  //订单来源:01商城，02阿闻到家，03挂号
  string order_source = 4;
}

message  CheckPayTypeRes{
  int32 code = 1;
  string message = 2;
  PayTypeData data = 3;
}


message PayTypeData {
  //支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
  int32 pay_type = 1;

  //是否跳转收银台
  bool is_jump_cashier = 2;

}

// 通过订单号查询支付
message PayQueryByOrderIdRequest {
  // 支付订单号
  string order_id = 1;
  // 是否查询所有状态
  bool all_status = 2;
}
message PayQueryByOrderIdResponse{
  // 订单号
  string order_id = 1;
  // 交易流水号
  string trade_no = 2;
  // 商户流水号
  string out_trade_no = 3;
  // 订单金额
  int32 total_price = 4;
  // 支付金额
  int32 pay_price = 5;
  // 退款金额
  int32 refund = 6;
  // 状态
  int32 status = 7;
  // 交易时间
  int32 add_time = 8;
  // 交易时间
  int32 pay_time = 9;
  // 支付类型
  int32 pay_type = 10;
  // 支付中心分配的商户号
  string merchant_id = 11;
}


message StandardPayStatusResponse {
  //业务订单号
  string order_no = 1;
  //支付金额 单位：分
  int64 pay_amount = 2;
  //退款金额 单位：分
  int64 refund_amount = 3;
  //第三方支付流水号
  string third_pay_no = 4;
  //第三方退款流水号
  string third_refund_no = 5;
  //支付状态 ： 0、未支付，1、已支付，2、部分退款，3、全部退款， 4、处理中， 5、支付失败，6、交易关闭
  //退款退款 ： 0、未退款，1、退款成功，2、退款处理中，3、退款失败
  string status = 6;
  //支付时间
  string pay_time = 7;
  //退款时间
  string refund_time = 8;
  //支付中心订单号
  string trade_no = 9;
  //扣款通道返回的流水号
  string channel_no = 10;
}


message StandardPayRefundResponse {
  //退款金额 单位：分
  int64 refund_amount = 1;
  //支付中心退款流水号
  string refund_trade_no = 2;
  //第三方退款流水号
  string third_refund_no = 3;
  //结果 0：未退款 1：退款成功 2：退款处理中 3：退款失败
  string result = 4;
  //退款返回信息
  string result_msg = 5;
  //支付中心流水号
  string trade_no = 6;
}
message StandardPayResponse {
  //商户订单号
  string order_no = 1;
  //支付金额 单位：分
  int64 pay_amount = 2;
  //第三方支付流水号
  string third_pay_no = 3;
  //结果 0： 未支付 1：成功 2：正在执行 3：失败
  string result = 4;
  //支付时间
  string pay_time = 5;
  //支付中心订单号
  string trade_no = 6;
  // 统一支付下单返回参数
  UnifiedOrderResponseData details = 7;
  //支付方式 1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）
  int32 trans_type = 8;
}

message JsonStrRequest{
  // json字节
  bytes json_byte =1;
}
message JsonStrResponse{
  // json字节
  bytes json_byte =1;
}

message StandardPayCloseRequest {
  // 1：阿闻，2：子龙，3：R1，4：互联网
  int32 app_id = 1;
  // 支付中心单号
  string trade_no = 2;
  // 签名
  string sign = 3;
}

service PayInfo {
  //统一订单
  rpc UnifiedOrder (UnifiedOrderRequest) returns (UnifiedOrderResponse) {}

  //支付异步回调
  rpc PayAsynNotice (PayAsynNoticeRequest) returns (BaseResponse) {}

  // @Desc			    支付查询
  // <AUTHOR>
  // @Date		 		2020-06-24
  rpc PayQuery (PayQueryRequest) returns (PayQueryResponse) {}
  rpc PayQueryByOrderId (PayQueryByOrderIdRequest) returns (PayQueryByOrderIdResponse) {}
  rpc PayInfoQuery (PayInfoQueryRequest) returns (PayInfoQueryResponse) {}

  // 标准终端绑定
  rpc StdBinding (StdBindingRequest) returns (StdBindingResponse) {}
  // 标准终端解绑
  rpc StdUnBinding (StdUnBindingRequest) returns (BaseResponse) {}
  // 终端状态查询
  rpc QuerySts (QueryStsRequest) returns (QueryStsResponse) {}
  // 发起支付B2C
  rpc PayForB2C (PayForB2CRequest) returns (PayForB2CResponse) {}
  // 支付状态查询
  rpc QueryPayStatus (QueryPayStatusRequest) returns (QueryPayStatusResponse) {}
  // 查询是否设置过支付密码
  rpc QueryPwd (QueryPwdRequest) returns (QueryPwdResponse) {}
  // 设置支付密码-首次
  rpc SetPwd (SetPwdRequest) returns (BaseResponse) {}
  // 通过手机验证码修改支付密码
  rpc UpdateByMobile (UpdateByMobileRequest) returns (BaseResponse) {}
  // 通过原密码修改支付密码
  rpc UpdateByPwd (UpdateByPwdRequest) returns (BaseResponse) {}
  // 获取支付方式列表-小程序
  rpc PayList (QueryPwdRequest) returns (PayListResponse) {}
  // 获取支付方式配置
  rpc PayConfig (QueryPwdRequest) returns (PayConfigResponse) {}
  // 支付设置
  rpc PaySet (PaySetRequest) returns (BaseResponse) {}

  //获取储蓄卡余额
  rpc GetCardsBalance(GetCardsBalanceReq) returns(GetCardsBalanceRes){}
  //获取储蓄卡列表
  rpc GetCards(GetCardsReq) returns(GetCardsRes){}
  //储蓄卡支付
  rpc CardPay(CardPayReq) returns(BaseResponse){}
  //储蓄卡退款
  rpc CardRefund(CardsRefundReq) returns(BaseResponse){}
  //储蓄卡订单交易记录
  rpc CardOrderRecords(CardOrderRecordsReq) returns(CardOrderRecordsRes){}
  //储蓄卡交易流水明细
  rpc CardRecords(CardRecordsReq) returns(CardRecordsRes){}

  //选定支付方式(默认储蓄卡支付)
  rpc CheckPayType(CheckPayTypeReq) returns(CheckPayTypeRes){}

  // 支付中心-标准支付统一入口
  rpc StandardPay(JsonStrRequest) returns(StandardPayResponse){}
  // 退款
  rpc StandardPayRefund(JsonStrRequest) returns(StandardPayRefundResponse){}
  // 状态查询
  rpc StandardPayStatus(JsonStrRequest) returns(StandardPayStatusResponse){}
  // 关闭支付
  rpc StandardPayClose(StandardPayCloseRequest) returns(BaseResponse){}
  // 百度支付退款查询
  rpc BaiduOrderRefundGet(BaiduOrderRefundGetRequest) returns(BaiduOrderRefundGetResponse){}


}