// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pay/pay_refund.proto

package pay

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// @Desc    			支付订单退款请求
// <AUTHOR>
// @Date		 			2020-06-11
type PayRefundRequest struct {
	// 支付中心流水号
	TradeNo string `protobuf:"bytes,1,opt,name=TradeNo,proto3" json:"TradeNo"`
	// 退款金额
	RefundAmt int32 `protobuf:"varint,2,opt,name=RefundAmt,proto3" json:"RefundAmt"`
	// 后台回调地址
	CallbackUrl string `protobuf:"bytes,3,opt,name=CallbackUrl,proto3" json:"CallbackUrl"`
	// 商户私有域
	BackParam string `protobuf:"bytes,4,opt,name=BackParam,proto3" json:"BackParam"`
	// 扩展信息
	ExtendInfo string `protobuf:"bytes,5,opt,name=ExtendInfo,proto3" json:"ExtendInfo"`
	// 商户号
	MerchantNo string `protobuf:"bytes,6,opt,name=MerchantNo,proto3" json:"MerchantNo"`
	// 客户端 IP
	ClientIP string `protobuf:"bytes,7,opt,name=ClientIP,proto3" json:"ClientIP"`
	// 签名
	Sign string `protobuf:"bytes,8,opt,name=Sign,proto3" json:"Sign"`
	// 请求域名
	Domain string `protobuf:"bytes,9,opt,name=domain,proto3" json:"domain"`
	// 应用id
	AppId int32 `protobuf:"varint,10,opt,name=AppId,proto3" json:"AppId"`
	// 商户退款订单号
	RefundId             string   `protobuf:"bytes,11,opt,name=refund_id,json=refundId,proto3" json:"refund_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayRefundRequest) Reset()         { *m = PayRefundRequest{} }
func (m *PayRefundRequest) String() string { return proto.CompactTextString(m) }
func (*PayRefundRequest) ProtoMessage()    {}
func (*PayRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{0}
}

func (m *PayRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRefundRequest.Unmarshal(m, b)
}
func (m *PayRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRefundRequest.Marshal(b, m, deterministic)
}
func (m *PayRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRefundRequest.Merge(m, src)
}
func (m *PayRefundRequest) XXX_Size() int {
	return xxx_messageInfo_PayRefundRequest.Size(m)
}
func (m *PayRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PayRefundRequest proto.InternalMessageInfo

func (m *PayRefundRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *PayRefundRequest) GetRefundAmt() int32 {
	if m != nil {
		return m.RefundAmt
	}
	return 0
}

func (m *PayRefundRequest) GetCallbackUrl() string {
	if m != nil {
		return m.CallbackUrl
	}
	return ""
}

func (m *PayRefundRequest) GetBackParam() string {
	if m != nil {
		return m.BackParam
	}
	return ""
}

func (m *PayRefundRequest) GetExtendInfo() string {
	if m != nil {
		return m.ExtendInfo
	}
	return ""
}

func (m *PayRefundRequest) GetMerchantNo() string {
	if m != nil {
		return m.MerchantNo
	}
	return ""
}

func (m *PayRefundRequest) GetClientIP() string {
	if m != nil {
		return m.ClientIP
	}
	return ""
}

func (m *PayRefundRequest) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func (m *PayRefundRequest) GetDomain() string {
	if m != nil {
		return m.Domain
	}
	return ""
}

func (m *PayRefundRequest) GetAppId() int32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *PayRefundRequest) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

// @Desc    			支付订单退款查询请求
// <AUTHOR>
// @Date		 			2020-06-10
type PayRefundQueryRequest struct {
	// 字符集 固定值：00，代表 GBK
	Charset string `protobuf:"bytes,1,opt,name=charset,proto3" json:"charset"`
	// 版本号 固定值：1.1
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`
	// 签名类型 固定值：RSA
	SignType string `protobuf:"bytes,3,opt,name=signType,proto3" json:"signType"`
	// 商户号
	MerchantNo string `protobuf:"bytes,4,opt,name=MerchantNo,proto3" json:"MerchantNo"`
	// 支付中心流水号
	TransactionNo string `protobuf:"bytes,5,opt,name=TransactionNo,proto3" json:"TransactionNo"`
	// 客户端 IP
	ClientIP string `protobuf:"bytes,6,opt,name=ClientIP,proto3" json:"ClientIP"`
	// 退款订单号
	RefundId string `protobuf:"bytes,7,opt,name=RefundId,proto3" json:"RefundId"`
	// 退款金额
	RefundAmt int32 `protobuf:"varint,8,opt,name=RefundAmt,proto3" json:"RefundAmt"`
	// 签名
	Sign                 string   `protobuf:"bytes,9,opt,name=Sign,proto3" json:"Sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayRefundQueryRequest) Reset()         { *m = PayRefundQueryRequest{} }
func (m *PayRefundQueryRequest) String() string { return proto.CompactTextString(m) }
func (*PayRefundQueryRequest) ProtoMessage()    {}
func (*PayRefundQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{1}
}

func (m *PayRefundQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRefundQueryRequest.Unmarshal(m, b)
}
func (m *PayRefundQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRefundQueryRequest.Marshal(b, m, deterministic)
}
func (m *PayRefundQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRefundQueryRequest.Merge(m, src)
}
func (m *PayRefundQueryRequest) XXX_Size() int {
	return xxx_messageInfo_PayRefundQueryRequest.Size(m)
}
func (m *PayRefundQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRefundQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PayRefundQueryRequest proto.InternalMessageInfo

func (m *PayRefundQueryRequest) GetCharset() string {
	if m != nil {
		return m.Charset
	}
	return ""
}

func (m *PayRefundQueryRequest) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *PayRefundQueryRequest) GetSignType() string {
	if m != nil {
		return m.SignType
	}
	return ""
}

func (m *PayRefundQueryRequest) GetMerchantNo() string {
	if m != nil {
		return m.MerchantNo
	}
	return ""
}

func (m *PayRefundQueryRequest) GetTransactionNo() string {
	if m != nil {
		return m.TransactionNo
	}
	return ""
}

func (m *PayRefundQueryRequest) GetClientIP() string {
	if m != nil {
		return m.ClientIP
	}
	return ""
}

func (m *PayRefundQueryRequest) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *PayRefundQueryRequest) GetRefundAmt() int32 {
	if m != nil {
		return m.RefundAmt
	}
	return 0
}

func (m *PayRefundQueryRequest) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

// @Desc    			电银退款结果异步回调通知请求
// <AUTHOR>
// @Date		 			2020-06-10
type RefundNoticeRequest struct {
	// 字符集 固定值：00，代表 GBK
	Charset string `protobuf:"bytes,1,opt,name=charset,proto3" json:"charset"`
	// 版本号 固定值：1.1
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`
	// 签名类型 固定值：RSA
	SignType string `protobuf:"bytes,3,opt,name=signType,proto3" json:"signType"`
	// 服务证书 服务器端的公钥证书
	ServerCert string `protobuf:"bytes,4,opt,name=serverCert,proto3" json:"serverCert"`
	// 签名 详见：安全规范
	ServerSign string `protobuf:"bytes,5,opt,name=serverSign,proto3" json:"serverSign"`
	// 商户订单号 仅能用大小写字母与数字，且在商户系统具有唯一性
	OrderId string `protobuf:"bytes,6,opt,name=orderId,proto3" json:"orderId"`
	// 退款订单号
	RefundId string `protobuf:"bytes,7,opt,name=refundId,proto3" json:"refundId"`
	// 退款流水号 电银生成的退款流水号
	TradeNo string `protobuf:"bytes,8,opt,name=tradeNo,proto3" json:"tradeNo"`
	// 商户号 支付平台给合作平台分配的唯一标识
	MerchantId string `protobuf:"bytes,9,opt,name=merchantId,proto3" json:"merchantId"`
	// 退款金额 单位为分
	TransAmt int32 `protobuf:"varint,10,opt,name=transAmt,proto3" json:"transAmt"`
	// 退款时间 YYYYMMDD
	RefundDate string `protobuf:"bytes,11,opt,name=refundDate,proto3" json:"refundDate"`
	// 退款状态 S: 成功(失败不通知)
	TransState string `protobuf:"bytes,12,opt,name=transState,proto3" json:"transState"`
	// 商户私有域 原样返回给商户
	BackParam string `protobuf:"bytes,13,opt,name=backParam,proto3" json:"backParam"`
	// 扩展信息 预留字段，JSON 格式
	ExtendInfo           string   `protobuf:"bytes,14,opt,name=extendInfo,proto3" json:"extendInfo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundNoticeRequest) Reset()         { *m = RefundNoticeRequest{} }
func (m *RefundNoticeRequest) String() string { return proto.CompactTextString(m) }
func (*RefundNoticeRequest) ProtoMessage()    {}
func (*RefundNoticeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{2}
}

func (m *RefundNoticeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundNoticeRequest.Unmarshal(m, b)
}
func (m *RefundNoticeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundNoticeRequest.Marshal(b, m, deterministic)
}
func (m *RefundNoticeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundNoticeRequest.Merge(m, src)
}
func (m *RefundNoticeRequest) XXX_Size() int {
	return xxx_messageInfo_RefundNoticeRequest.Size(m)
}
func (m *RefundNoticeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundNoticeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RefundNoticeRequest proto.InternalMessageInfo

func (m *RefundNoticeRequest) GetCharset() string {
	if m != nil {
		return m.Charset
	}
	return ""
}

func (m *RefundNoticeRequest) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *RefundNoticeRequest) GetSignType() string {
	if m != nil {
		return m.SignType
	}
	return ""
}

func (m *RefundNoticeRequest) GetServerCert() string {
	if m != nil {
		return m.ServerCert
	}
	return ""
}

func (m *RefundNoticeRequest) GetServerSign() string {
	if m != nil {
		return m.ServerSign
	}
	return ""
}

func (m *RefundNoticeRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RefundNoticeRequest) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *RefundNoticeRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *RefundNoticeRequest) GetMerchantId() string {
	if m != nil {
		return m.MerchantId
	}
	return ""
}

func (m *RefundNoticeRequest) GetTransAmt() int32 {
	if m != nil {
		return m.TransAmt
	}
	return 0
}

func (m *RefundNoticeRequest) GetRefundDate() string {
	if m != nil {
		return m.RefundDate
	}
	return ""
}

func (m *RefundNoticeRequest) GetTransState() string {
	if m != nil {
		return m.TransState
	}
	return ""
}

func (m *RefundNoticeRequest) GetBackParam() string {
	if m != nil {
		return m.BackParam
	}
	return ""
}

func (m *RefundNoticeRequest) GetExtendInfo() string {
	if m != nil {
		return m.ExtendInfo
	}
	return ""
}

// @Desc    			电银回调通知 HTTP 响应结果 Data
// <AUTHOR>
// @Date		 		  2020-06-11
type RefundNoticeResponse struct {
	Result               string   `protobuf:"bytes,1,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundNoticeResponse) Reset()         { *m = RefundNoticeResponse{} }
func (m *RefundNoticeResponse) String() string { return proto.CompactTextString(m) }
func (*RefundNoticeResponse) ProtoMessage()    {}
func (*RefundNoticeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{3}
}

func (m *RefundNoticeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundNoticeResponse.Unmarshal(m, b)
}
func (m *RefundNoticeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundNoticeResponse.Marshal(b, m, deterministic)
}
func (m *RefundNoticeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundNoticeResponse.Merge(m, src)
}
func (m *RefundNoticeResponse) XXX_Size() int {
	return xxx_messageInfo_RefundNoticeResponse.Size(m)
}
func (m *RefundNoticeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundNoticeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RefundNoticeResponse proto.InternalMessageInfo

func (m *RefundNoticeResponse) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

// @Desc    			HTTP 响应结果 Data
// <AUTHOR>
// @Date		 			2020-06-11
type PayRefundReturnData struct {
	// 商户私有域：交易返回时原样返回给商户网站，给商户备用
	BackParam string `protobuf:"bytes,1,opt,name=backParam,proto3" json:"backParam"`
	// 后台回调地址
	CallbackUrl string `protobuf:"bytes,2,opt,name=callbackUrl,proto3" json:"callbackUrl"`
	// 客户端 IP
	ClientIP string `protobuf:"bytes,3,opt,name=clientIP,proto3" json:"clientIP"`
	// 扩展信息：预留字段，JSON 格式
	ExtendInfo string `protobuf:"bytes,4,opt,name=extendInfo,proto3" json:"extendInfo"`
	// 退款金额，以分为单位
	RefundAmt string `protobuf:"bytes,5,opt,name=refundAmt,proto3" json:"refundAmt"`
	// 退款订单号
	RefundId string `protobuf:"bytes,6,opt,name=refundId,proto3" json:"refundId"`
	// 退款状态 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败
	RspCode string `protobuf:"bytes,7,opt,name=rspCode,proto3" json:"rspCode"`
	// 返回信息
	RspMessage string `protobuf:"bytes,8,opt,name=rspMessage,proto3" json:"rspMessage"`
	// 交易流水号
	TransactionNo string `protobuf:"bytes,9,opt,name=transactionNo,proto3" json:"transactionNo"`
	// 商户号
	MerchantId string `protobuf:"bytes,10,opt,name=merchantId,proto3" json:"merchantId"`
	// 签名
	Sign                 string   `protobuf:"bytes,11,opt,name=sign,proto3" json:"sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayRefundReturnData) Reset()         { *m = PayRefundReturnData{} }
func (m *PayRefundReturnData) String() string { return proto.CompactTextString(m) }
func (*PayRefundReturnData) ProtoMessage()    {}
func (*PayRefundReturnData) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{4}
}

func (m *PayRefundReturnData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRefundReturnData.Unmarshal(m, b)
}
func (m *PayRefundReturnData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRefundReturnData.Marshal(b, m, deterministic)
}
func (m *PayRefundReturnData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRefundReturnData.Merge(m, src)
}
func (m *PayRefundReturnData) XXX_Size() int {
	return xxx_messageInfo_PayRefundReturnData.Size(m)
}
func (m *PayRefundReturnData) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRefundReturnData.DiscardUnknown(m)
}

var xxx_messageInfo_PayRefundReturnData proto.InternalMessageInfo

func (m *PayRefundReturnData) GetBackParam() string {
	if m != nil {
		return m.BackParam
	}
	return ""
}

func (m *PayRefundReturnData) GetCallbackUrl() string {
	if m != nil {
		return m.CallbackUrl
	}
	return ""
}

func (m *PayRefundReturnData) GetClientIP() string {
	if m != nil {
		return m.ClientIP
	}
	return ""
}

func (m *PayRefundReturnData) GetExtendInfo() string {
	if m != nil {
		return m.ExtendInfo
	}
	return ""
}

func (m *PayRefundReturnData) GetRefundAmt() string {
	if m != nil {
		return m.RefundAmt
	}
	return ""
}

func (m *PayRefundReturnData) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *PayRefundReturnData) GetRspCode() string {
	if m != nil {
		return m.RspCode
	}
	return ""
}

func (m *PayRefundReturnData) GetRspMessage() string {
	if m != nil {
		return m.RspMessage
	}
	return ""
}

func (m *PayRefundReturnData) GetTransactionNo() string {
	if m != nil {
		return m.TransactionNo
	}
	return ""
}

func (m *PayRefundReturnData) GetMerchantId() string {
	if m != nil {
		return m.MerchantId
	}
	return ""
}

func (m *PayRefundReturnData) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

// @Desc    			HTTP请求响应
// <AUTHOR>
// @Date		 			2020-06-11
type PayRefundResponse struct {
	Code                 int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *PayRefundReturnData      `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	BaiduData            *PayRefundReturnBaiduData `protobuf:"bytes,4,opt,name=baiduData,proto3" json:"baiduData"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *PayRefundResponse) Reset()         { *m = PayRefundResponse{} }
func (m *PayRefundResponse) String() string { return proto.CompactTextString(m) }
func (*PayRefundResponse) ProtoMessage()    {}
func (*PayRefundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{5}
}

func (m *PayRefundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRefundResponse.Unmarshal(m, b)
}
func (m *PayRefundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRefundResponse.Marshal(b, m, deterministic)
}
func (m *PayRefundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRefundResponse.Merge(m, src)
}
func (m *PayRefundResponse) XXX_Size() int {
	return xxx_messageInfo_PayRefundResponse.Size(m)
}
func (m *PayRefundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRefundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PayRefundResponse proto.InternalMessageInfo

func (m *PayRefundResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PayRefundResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PayRefundResponse) GetData() *PayRefundReturnData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PayRefundResponse) GetBaiduData() *PayRefundReturnBaiduData {
	if m != nil {
		return m.BaiduData
	}
	return nil
}

type PayRefundReturnBaiduData struct {
	// 平台退款批次号
	RefundBatchId string `protobuf:"bytes,1,opt,name=refundBatchId,proto3" json:"refundBatchId"`
	// 平台可退退款金额【分为单位】
	RefundPayMoney       int64    `protobuf:"varint,2,opt,name=refundPayMoney,proto3" json:"refundPayMoney"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayRefundReturnBaiduData) Reset()         { *m = PayRefundReturnBaiduData{} }
func (m *PayRefundReturnBaiduData) String() string { return proto.CompactTextString(m) }
func (*PayRefundReturnBaiduData) ProtoMessage()    {}
func (*PayRefundReturnBaiduData) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{6}
}

func (m *PayRefundReturnBaiduData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRefundReturnBaiduData.Unmarshal(m, b)
}
func (m *PayRefundReturnBaiduData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRefundReturnBaiduData.Marshal(b, m, deterministic)
}
func (m *PayRefundReturnBaiduData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRefundReturnBaiduData.Merge(m, src)
}
func (m *PayRefundReturnBaiduData) XXX_Size() int {
	return xxx_messageInfo_PayRefundReturnBaiduData.Size(m)
}
func (m *PayRefundReturnBaiduData) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRefundReturnBaiduData.DiscardUnknown(m)
}

var xxx_messageInfo_PayRefundReturnBaiduData proto.InternalMessageInfo

func (m *PayRefundReturnBaiduData) GetRefundBatchId() string {
	if m != nil {
		return m.RefundBatchId
	}
	return ""
}

func (m *PayRefundReturnBaiduData) GetRefundPayMoney() int64 {
	if m != nil {
		return m.RefundPayMoney
	}
	return 0
}

type QueryRefundStatusRequest struct {
	//商户订单号(原交易)
	MerOrderNo string `protobuf:"bytes,1,opt,name=mer_order_no,json=merOrderNo,proto3" json:"mer_order_no"`
	//商户退款单号
	MerRefundOrderNo string `protobuf:"bytes,2,opt,name=mer_refund_order_no,json=merRefundOrderNo,proto3" json:"mer_refund_order_no"`
	//交易码,非银联：PF2 银联：QRY3 说明：PF2 为被扫交易类型，支持微信、支付宝、云闪付、电银支付等类型； QRY3 仅支持云闪付（老接口兼容性保留值）
	Trancde string `protobuf:"bytes,3,opt,name=trancde,proto3" json:"trancde"`
	//请求头信息
	HeadBase             *ScanHeadBase `protobuf:"bytes,4,opt,name=head_base,json=headBase,proto3" json:"head_base"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QueryRefundStatusRequest) Reset()         { *m = QueryRefundStatusRequest{} }
func (m *QueryRefundStatusRequest) String() string { return proto.CompactTextString(m) }
func (*QueryRefundStatusRequest) ProtoMessage()    {}
func (*QueryRefundStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{7}
}

func (m *QueryRefundStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryRefundStatusRequest.Unmarshal(m, b)
}
func (m *QueryRefundStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryRefundStatusRequest.Marshal(b, m, deterministic)
}
func (m *QueryRefundStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryRefundStatusRequest.Merge(m, src)
}
func (m *QueryRefundStatusRequest) XXX_Size() int {
	return xxx_messageInfo_QueryRefundStatusRequest.Size(m)
}
func (m *QueryRefundStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryRefundStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryRefundStatusRequest proto.InternalMessageInfo

func (m *QueryRefundStatusRequest) GetMerOrderNo() string {
	if m != nil {
		return m.MerOrderNo
	}
	return ""
}

func (m *QueryRefundStatusRequest) GetMerRefundOrderNo() string {
	if m != nil {
		return m.MerRefundOrderNo
	}
	return ""
}

func (m *QueryRefundStatusRequest) GetTrancde() string {
	if m != nil {
		return m.Trancde
	}
	return ""
}

func (m *QueryRefundStatusRequest) GetHeadBase() *ScanHeadBase {
	if m != nil {
		return m.HeadBase
	}
	return nil
}

type QueryRefundStatusResponse struct {
	//返回码
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//商户订单号
	MerOrderNo string `protobuf:"bytes,3,opt,name=mer_order_no,json=merOrderNo,proto3" json:"mer_order_no"`
	//商户退款单号
	MerRefundOrderNo string `protobuf:"bytes,4,opt,name=mer_refund_order_no,json=merRefundOrderNo,proto3" json:"mer_refund_order_no"`
	//退款金额 单位：分
	RefundAmount string `protobuf:"bytes,5,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	//支付结果 S：成功 R：正在执行 F：失败
	RefundResult string `protobuf:"bytes,6,opt,name=refund_result,json=refundResult,proto3" json:"refund_result"`
	//实际退款金额 单位：分
	ActualRefundAmount string `protobuf:"bytes,7,opt,name=actual_refund_amount,json=actualRefundAmount,proto3" json:"actual_refund_amount"`
	//退款时间
	RefundTime string `protobuf:"bytes,8,opt,name=refund_time,json=refundTime,proto3" json:"refund_time"`
	//退款流水号
	RefundNo             string   `protobuf:"bytes,9,opt,name=refund_no,json=refundNo,proto3" json:"refund_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryRefundStatusResponse) Reset()         { *m = QueryRefundStatusResponse{} }
func (m *QueryRefundStatusResponse) String() string { return proto.CompactTextString(m) }
func (*QueryRefundStatusResponse) ProtoMessage()    {}
func (*QueryRefundStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{8}
}

func (m *QueryRefundStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryRefundStatusResponse.Unmarshal(m, b)
}
func (m *QueryRefundStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryRefundStatusResponse.Marshal(b, m, deterministic)
}
func (m *QueryRefundStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryRefundStatusResponse.Merge(m, src)
}
func (m *QueryRefundStatusResponse) XXX_Size() int {
	return xxx_messageInfo_QueryRefundStatusResponse.Size(m)
}
func (m *QueryRefundStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryRefundStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryRefundStatusResponse proto.InternalMessageInfo

func (m *QueryRefundStatusResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryRefundStatusResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryRefundStatusResponse) GetMerOrderNo() string {
	if m != nil {
		return m.MerOrderNo
	}
	return ""
}

func (m *QueryRefundStatusResponse) GetMerRefundOrderNo() string {
	if m != nil {
		return m.MerRefundOrderNo
	}
	return ""
}

func (m *QueryRefundStatusResponse) GetRefundAmount() string {
	if m != nil {
		return m.RefundAmount
	}
	return ""
}

func (m *QueryRefundStatusResponse) GetRefundResult() string {
	if m != nil {
		return m.RefundResult
	}
	return ""
}

func (m *QueryRefundStatusResponse) GetActualRefundAmount() string {
	if m != nil {
		return m.ActualRefundAmount
	}
	return ""
}

func (m *QueryRefundStatusResponse) GetRefundTime() string {
	if m != nil {
		return m.RefundTime
	}
	return ""
}

func (m *QueryRefundStatusResponse) GetRefundNo() string {
	if m != nil {
		return m.RefundNo
	}
	return ""
}

type NewRefundRequest struct {
	//商户订单号(原交易)
	MerOrderNo string `protobuf:"bytes,1,opt,name=mer_order_no,json=merOrderNo,proto3" json:"mer_order_no"`
	//交易码：非银联时（微信，支付宝）：P02 银联：CSU03
	Trancde string `protobuf:"bytes,2,opt,name=trancde,proto3" json:"trancde"`
	//退款金额  单位：分
	RefundAmount string `protobuf:"bytes,3,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	//退款备注
	RefundRemark string `protobuf:"bytes,4,opt,name=refund_remark,json=refundRemark,proto3" json:"refund_remark"`
	//请求头信息
	HeadBase *ScanHeadBase `protobuf:"bytes,5,opt,name=head_base,json=headBase,proto3" json:"head_base"`
	//后台回调地址（支付中心回调电商）
	NotifyUrl string `protobuf:"bytes,6,opt,name=notify_url,json=notifyUrl,proto3" json:"notify_url"`
	// 客户端 IP
	ClientIP             string   `protobuf:"bytes,7,opt,name=clientIP,proto3" json:"clientIP"`
	BackParam            string   `protobuf:"bytes,8,opt,name=backParam,proto3" json:"backParam"`
	ExtendInfo           string   `protobuf:"bytes,9,opt,name=extendInfo,proto3" json:"extendInfo"`
	AppId                int32    `protobuf:"varint,10,opt,name=AppId,proto3" json:"AppId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewRefundRequest) Reset()         { *m = NewRefundRequest{} }
func (m *NewRefundRequest) String() string { return proto.CompactTextString(m) }
func (*NewRefundRequest) ProtoMessage()    {}
func (*NewRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{9}
}

func (m *NewRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewRefundRequest.Unmarshal(m, b)
}
func (m *NewRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewRefundRequest.Marshal(b, m, deterministic)
}
func (m *NewRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewRefundRequest.Merge(m, src)
}
func (m *NewRefundRequest) XXX_Size() int {
	return xxx_messageInfo_NewRefundRequest.Size(m)
}
func (m *NewRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewRefundRequest proto.InternalMessageInfo

func (m *NewRefundRequest) GetMerOrderNo() string {
	if m != nil {
		return m.MerOrderNo
	}
	return ""
}

func (m *NewRefundRequest) GetTrancde() string {
	if m != nil {
		return m.Trancde
	}
	return ""
}

func (m *NewRefundRequest) GetRefundAmount() string {
	if m != nil {
		return m.RefundAmount
	}
	return ""
}

func (m *NewRefundRequest) GetRefundRemark() string {
	if m != nil {
		return m.RefundRemark
	}
	return ""
}

func (m *NewRefundRequest) GetHeadBase() *ScanHeadBase {
	if m != nil {
		return m.HeadBase
	}
	return nil
}

func (m *NewRefundRequest) GetNotifyUrl() string {
	if m != nil {
		return m.NotifyUrl
	}
	return ""
}

func (m *NewRefundRequest) GetClientIP() string {
	if m != nil {
		return m.ClientIP
	}
	return ""
}

func (m *NewRefundRequest) GetBackParam() string {
	if m != nil {
		return m.BackParam
	}
	return ""
}

func (m *NewRefundRequest) GetExtendInfo() string {
	if m != nil {
		return m.ExtendInfo
	}
	return ""
}

func (m *NewRefundRequest) GetAppId() int32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

type MercDetail struct {
	//分账类型 固定：MERCHANT_ID
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type"`
	//商户号
	RecMerchantId string `protobuf:"bytes,2,opt,name=recMerchantId,proto3" json:"recMerchantId"`
	//分账金额
	PayAmt string `protobuf:"bytes,3,opt,name=payAmt,proto3" json:"payAmt"`
	//分账描述
	Description          string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MercDetail) Reset()         { *m = MercDetail{} }
func (m *MercDetail) String() string { return proto.CompactTextString(m) }
func (*MercDetail) ProtoMessage()    {}
func (*MercDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{10}
}

func (m *MercDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MercDetail.Unmarshal(m, b)
}
func (m *MercDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MercDetail.Marshal(b, m, deterministic)
}
func (m *MercDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MercDetail.Merge(m, src)
}
func (m *MercDetail) XXX_Size() int {
	return xxx_messageInfo_MercDetail.Size(m)
}
func (m *MercDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_MercDetail.DiscardUnknown(m)
}

var xxx_messageInfo_MercDetail proto.InternalMessageInfo

func (m *MercDetail) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *MercDetail) GetRecMerchantId() string {
	if m != nil {
		return m.RecMerchantId
	}
	return ""
}

func (m *MercDetail) GetPayAmt() string {
	if m != nil {
		return m.PayAmt
	}
	return ""
}

func (m *MercDetail) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

type NewRefundResponse struct {
	//返回码
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//商户订单号
	MerOrderNo string `protobuf:"bytes,3,opt,name=mer_order_no,json=merOrderNo,proto3" json:"mer_order_no"`
	//商户退款单号
	MerRefundOrderNo string `protobuf:"bytes,4,opt,name=mer_refund_order_no,json=merRefundOrderNo,proto3" json:"mer_refund_order_no"`
	//退款金额 单位：分
	RefundAmount string `protobuf:"bytes,5,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	//支付结果 S：成功 R：正在执行 F：失败
	RefundResult string `protobuf:"bytes,6,opt,name=refund_result,json=refundResult,proto3" json:"refund_result"`
	//实际退款金额 单位：分
	ActualRefundAmount string `protobuf:"bytes,7,opt,name=actual_refund_amount,json=actualRefundAmount,proto3" json:"actual_refund_amount"`
	//退款时间
	RefundTime string `protobuf:"bytes,8,opt,name=refund_time,json=refundTime,proto3" json:"refund_time"`
	//退款流水号
	RefundNo             string   `protobuf:"bytes,9,opt,name=refund_no,json=refundNo,proto3" json:"refund_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewRefundResponse) Reset()         { *m = NewRefundResponse{} }
func (m *NewRefundResponse) String() string { return proto.CompactTextString(m) }
func (*NewRefundResponse) ProtoMessage()    {}
func (*NewRefundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{11}
}

func (m *NewRefundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewRefundResponse.Unmarshal(m, b)
}
func (m *NewRefundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewRefundResponse.Marshal(b, m, deterministic)
}
func (m *NewRefundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewRefundResponse.Merge(m, src)
}
func (m *NewRefundResponse) XXX_Size() int {
	return xxx_messageInfo_NewRefundResponse.Size(m)
}
func (m *NewRefundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewRefundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewRefundResponse proto.InternalMessageInfo

func (m *NewRefundResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewRefundResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NewRefundResponse) GetMerOrderNo() string {
	if m != nil {
		return m.MerOrderNo
	}
	return ""
}

func (m *NewRefundResponse) GetMerRefundOrderNo() string {
	if m != nil {
		return m.MerRefundOrderNo
	}
	return ""
}

func (m *NewRefundResponse) GetRefundAmount() string {
	if m != nil {
		return m.RefundAmount
	}
	return ""
}

func (m *NewRefundResponse) GetRefundResult() string {
	if m != nil {
		return m.RefundResult
	}
	return ""
}

func (m *NewRefundResponse) GetActualRefundAmount() string {
	if m != nil {
		return m.ActualRefundAmount
	}
	return ""
}

func (m *NewRefundResponse) GetRefundTime() string {
	if m != nil {
		return m.RefundTime
	}
	return ""
}

func (m *NewRefundResponse) GetRefundNo() string {
	if m != nil {
		return m.RefundNo
	}
	return ""
}

// @Desc    			HTTP请求响应
// <AUTHOR>
// @Date		 			2023-04-15
type DYRefundInfoResponse struct {
	Code                 int32                                  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *DYRefundInfoResponse_DYRefundInfoData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *DYRefundInfoResponse) Reset()         { *m = DYRefundInfoResponse{} }
func (m *DYRefundInfoResponse) String() string { return proto.CompactTextString(m) }
func (*DYRefundInfoResponse) ProtoMessage()    {}
func (*DYRefundInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{12}
}

func (m *DYRefundInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DYRefundInfoResponse.Unmarshal(m, b)
}
func (m *DYRefundInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DYRefundInfoResponse.Marshal(b, m, deterministic)
}
func (m *DYRefundInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DYRefundInfoResponse.Merge(m, src)
}
func (m *DYRefundInfoResponse) XXX_Size() int {
	return xxx_messageInfo_DYRefundInfoResponse.Size(m)
}
func (m *DYRefundInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DYRefundInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DYRefundInfoResponse proto.InternalMessageInfo

func (m *DYRefundInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DYRefundInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DYRefundInfoResponse) GetData() *DYRefundInfoResponse_DYRefundInfoData {
	if m != nil {
		return m.Data
	}
	return nil
}

type DYRefundInfoResponse_DYRefundInfoData struct {
	//退款状态  S:退款成功  F:退款失败  P:退款处理中
	RefundState string `protobuf:"bytes,1,opt,name=refund_state,json=refundState,proto3" json:"refund_state"`
	//支付订单号
	Order_Id string `protobuf:"bytes,2,opt,name=order_Id,json=orderId,proto3" json:"order_Id"`
	//退款手续费(返回空就是手续费没结算)
	FeeAmt string `protobuf:"bytes,3,opt,name=fee_amt,json=feeAmt,proto3" json:"fee_amt"`
	//退款订单号
	TradeNo              string   `protobuf:"bytes,4,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DYRefundInfoResponse_DYRefundInfoData) Reset()         { *m = DYRefundInfoResponse_DYRefundInfoData{} }
func (m *DYRefundInfoResponse_DYRefundInfoData) String() string { return proto.CompactTextString(m) }
func (*DYRefundInfoResponse_DYRefundInfoData) ProtoMessage()    {}
func (*DYRefundInfoResponse_DYRefundInfoData) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{12, 0}
}

func (m *DYRefundInfoResponse_DYRefundInfoData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DYRefundInfoResponse_DYRefundInfoData.Unmarshal(m, b)
}
func (m *DYRefundInfoResponse_DYRefundInfoData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DYRefundInfoResponse_DYRefundInfoData.Marshal(b, m, deterministic)
}
func (m *DYRefundInfoResponse_DYRefundInfoData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DYRefundInfoResponse_DYRefundInfoData.Merge(m, src)
}
func (m *DYRefundInfoResponse_DYRefundInfoData) XXX_Size() int {
	return xxx_messageInfo_DYRefundInfoResponse_DYRefundInfoData.Size(m)
}
func (m *DYRefundInfoResponse_DYRefundInfoData) XXX_DiscardUnknown() {
	xxx_messageInfo_DYRefundInfoResponse_DYRefundInfoData.DiscardUnknown(m)
}

var xxx_messageInfo_DYRefundInfoResponse_DYRefundInfoData proto.InternalMessageInfo

func (m *DYRefundInfoResponse_DYRefundInfoData) GetRefundState() string {
	if m != nil {
		return m.RefundState
	}
	return ""
}

func (m *DYRefundInfoResponse_DYRefundInfoData) GetOrder_Id() string {
	if m != nil {
		return m.Order_Id
	}
	return ""
}

func (m *DYRefundInfoResponse_DYRefundInfoData) GetFeeAmt() string {
	if m != nil {
		return m.FeeAmt
	}
	return ""
}

func (m *DYRefundInfoResponse_DYRefundInfoData) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

type DYRefundInfoRequest struct {
	MerchantId           string   `protobuf:"bytes,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id"`
	TradeNo              string   `protobuf:"bytes,2,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DYRefundInfoRequest) Reset()         { *m = DYRefundInfoRequest{} }
func (m *DYRefundInfoRequest) String() string { return proto.CompactTextString(m) }
func (*DYRefundInfoRequest) ProtoMessage()    {}
func (*DYRefundInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bdf7f42439b782dc, []int{13}
}

func (m *DYRefundInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DYRefundInfoRequest.Unmarshal(m, b)
}
func (m *DYRefundInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DYRefundInfoRequest.Marshal(b, m, deterministic)
}
func (m *DYRefundInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DYRefundInfoRequest.Merge(m, src)
}
func (m *DYRefundInfoRequest) XXX_Size() int {
	return xxx_messageInfo_DYRefundInfoRequest.Size(m)
}
func (m *DYRefundInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DYRefundInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DYRefundInfoRequest proto.InternalMessageInfo

func (m *DYRefundInfoRequest) GetMerchantId() string {
	if m != nil {
		return m.MerchantId
	}
	return ""
}

func (m *DYRefundInfoRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func init() {
	proto.RegisterType((*PayRefundRequest)(nil), "pay.PayRefundRequest")
	proto.RegisterType((*PayRefundQueryRequest)(nil), "pay.PayRefundQueryRequest")
	proto.RegisterType((*RefundNoticeRequest)(nil), "pay.RefundNoticeRequest")
	proto.RegisterType((*RefundNoticeResponse)(nil), "pay.RefundNoticeResponse")
	proto.RegisterType((*PayRefundReturnData)(nil), "pay.PayRefundReturnData")
	proto.RegisterType((*PayRefundResponse)(nil), "pay.PayRefundResponse")
	proto.RegisterType((*PayRefundReturnBaiduData)(nil), "pay.PayRefundReturnBaiduData")
	proto.RegisterType((*QueryRefundStatusRequest)(nil), "pay.QueryRefundStatusRequest")
	proto.RegisterType((*QueryRefundStatusResponse)(nil), "pay.QueryRefundStatusResponse")
	proto.RegisterType((*NewRefundRequest)(nil), "pay.NewRefundRequest")
	proto.RegisterType((*MercDetail)(nil), "pay.MercDetail")
	proto.RegisterType((*NewRefundResponse)(nil), "pay.NewRefundResponse")
	proto.RegisterType((*DYRefundInfoResponse)(nil), "pay.DYRefundInfoResponse")
	proto.RegisterType((*DYRefundInfoResponse_DYRefundInfoData)(nil), "pay.DYRefundInfoResponse.DYRefundInfoData")
	proto.RegisterType((*DYRefundInfoRequest)(nil), "pay.DYRefundInfoRequest")
}

func init() { proto.RegisterFile("pay/pay_refund.proto", fileDescriptor_bdf7f42439b782dc) }

var fileDescriptor_bdf7f42439b782dc = []byte{
	// 1262 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x57, 0xcd, 0x6e, 0xe4, 0x44,
	0x10, 0xde, 0xf9, 0xc9, 0x64, 0xa6, 0x26, 0xbb, 0x24, 0x9d, 0xec, 0xe2, 0x0c, 0xfb, 0x13, 0x0c,
	0x42, 0x2b, 0x04, 0x09, 0x0a, 0x27, 0xc4, 0x0a, 0x69, 0x93, 0x2c, 0xda, 0x39, 0x64, 0xc8, 0x3a,
	0xe1, 0xc0, 0x69, 0xd4, 0xb1, 0x7b, 0x32, 0x56, 0xc6, 0xdd, 0xa6, 0xdd, 0xb3, 0xe0, 0x1b, 0x07,
	0x6e, 0x3c, 0x00, 0x2f, 0x81, 0xc4, 0x89, 0x47, 0xe0, 0xc6, 0x11, 0x89, 0x23, 0xcf, 0xc0, 0x1b,
	0xa0, 0xfe, 0xb3, 0xdb, 0x9e, 0x49, 0x82, 0x82, 0xb8, 0x71, 0x73, 0x7d, 0x55, 0x6e, 0x77, 0x7f,
	0x5f, 0x55, 0x75, 0x19, 0xb6, 0x52, 0x9c, 0xef, 0xa5, 0x38, 0x1f, 0x73, 0x32, 0x99, 0xd3, 0x68,
	0x37, 0xe5, 0x4c, 0x30, 0xd4, 0x4a, 0x71, 0x3e, 0x78, 0x78, 0xc1, 0xd8, 0xc5, 0x8c, 0xec, 0xe1,
	0x34, 0xde, 0xc3, 0x94, 0x32, 0x81, 0x45, 0xcc, 0x68, 0xa6, 0x43, 0x06, 0xeb, 0xf2, 0xc5, 0x90,
	0x25, 0x09, 0xa3, 0x1a, 0xf1, 0x7f, 0x6d, 0xc2, 0xfa, 0x09, 0xce, 0x03, 0xb5, 0x50, 0x40, 0xbe,
	0x9e, 0x93, 0x4c, 0x20, 0x0f, 0x56, 0xcf, 0x38, 0x8e, 0xc8, 0x88, 0x79, 0x8d, 0x9d, 0xc6, 0xd3,
	0x5e, 0x60, 0x4d, 0xf4, 0x10, 0x7a, 0x3a, 0xf4, 0x79, 0x22, 0xbc, 0xe6, 0x4e, 0xe3, 0xe9, 0x4a,
	0x50, 0x02, 0x68, 0x07, 0xfa, 0x87, 0x78, 0x36, 0x3b, 0xc7, 0xe1, 0xe5, 0x97, 0x7c, 0xe6, 0xb5,
	0xd4, 0xbb, 0x2e, 0x24, 0xdf, 0x3f, 0xc0, 0xe1, 0xe5, 0x09, 0xe6, 0x38, 0xf1, 0xda, 0xca, 0x5f,
	0x02, 0xe8, 0x31, 0xc0, 0x8b, 0x6f, 0x05, 0xa1, 0xd1, 0x90, 0x4e, 0x98, 0xb7, 0xa2, 0xdc, 0x0e,
	0x22, 0xfd, 0xc7, 0x84, 0x87, 0x53, 0x4c, 0xc5, 0x88, 0x79, 0x1d, 0xed, 0x2f, 0x11, 0x34, 0x80,
	0xee, 0xe1, 0x2c, 0x26, 0x54, 0x0c, 0x4f, 0xbc, 0x55, 0xe5, 0x2d, 0x6c, 0x84, 0xa0, 0x7d, 0x1a,
	0x5f, 0x50, 0xaf, 0xab, 0x70, 0xf5, 0x8c, 0x1e, 0x40, 0x27, 0x62, 0x09, 0x8e, 0xa9, 0xd7, 0x53,
	0xa8, 0xb1, 0xd0, 0x16, 0xac, 0x3c, 0x4f, 0xd3, 0x61, 0xe4, 0x81, 0x3a, 0xa1, 0x36, 0xd0, 0x5b,
	0xd0, 0xd3, 0x7c, 0x8f, 0xe3, 0xc8, 0xeb, 0xeb, 0xe5, 0x35, 0x30, 0x8c, 0xfc, 0x1f, 0x9b, 0x70,
	0xbf, 0xe0, 0xf1, 0xd5, 0x9c, 0xf0, 0xdc, 0x21, 0x33, 0x9c, 0x62, 0x9e, 0x11, 0x61, 0xc9, 0x34,
	0xa6, 0xf4, 0xbc, 0x26, 0x3c, 0x8b, 0x19, 0x55, 0x54, 0xf6, 0x02, 0x6b, 0xca, 0x83, 0x64, 0xf1,
	0x05, 0x3d, 0xcb, 0x53, 0x62, 0x58, 0x2c, 0xec, 0x1a, 0x09, 0xed, 0x05, 0x12, 0xde, 0x85, 0xbb,
	0x67, 0x1c, 0xd3, 0x0c, 0x87, 0x52, 0xf9, 0x91, 0xe5, 0xb1, 0x0a, 0x56, 0xa8, 0xea, 0xd4, 0xa8,
	0x1a, 0x40, 0x37, 0x30, 0xe7, 0xb2, 0x34, 0x5a, 0xbb, 0x9a, 0x00, 0xdd, 0x7a, 0x02, 0x58, 0x92,
	0x7b, 0x25, 0xc9, 0xfe, 0xcf, 0x2d, 0xd8, 0xd4, 0x11, 0x23, 0x26, 0xe2, 0x90, 0xfc, 0x87, 0xbc,
	0x64, 0x84, 0xbf, 0x26, 0xfc, 0x90, 0x70, 0x61, 0x79, 0x29, 0x91, 0xd2, 0xaf, 0x76, 0xb8, 0xe2,
	0xfa, 0x55, 0x32, 0x78, 0xb0, 0xca, 0x78, 0x44, 0xf8, 0x30, 0x32, 0x84, 0x58, 0x53, 0x7e, 0x95,
	0xd7, 0xf8, 0xb0, 0xb6, 0x7c, 0x4b, 0x98, 0x52, 0xd1, 0x99, 0x65, 0x4d, 0xf9, 0xbd, 0xc4, 0xa8,
	0x32, 0x8c, 0x0c, 0x23, 0x0e, 0x22, 0x57, 0x15, 0x52, 0x12, 0x49, 0xa4, 0xce, 0xb3, 0xc2, 0x96,
	0xef, 0xea, 0x2f, 0x1c, 0x61, 0x41, 0x4c, 0xae, 0x39, 0x88, 0xf4, 0xab, 0xd8, 0x53, 0x21, 0xfd,
	0x6b, 0xda, 0x5f, 0x22, 0x52, 0xa5, 0xf3, 0xa2, 0xcc, 0xee, 0xea, 0x32, 0x3b, 0x77, 0xcb, 0x8c,
	0x94, 0x65, 0x76, 0x4f, 0xbf, 0x5d, 0x22, 0xfe, 0x2e, 0x6c, 0x55, 0x05, 0xcb, 0x52, 0x46, 0x33,
	0x22, 0xcb, 0x85, 0x93, 0x6c, 0x3e, 0xb3, 0x82, 0x19, 0xcb, 0xff, 0xb3, 0x09, 0x9b, 0x4e, 0x0f,
	0x11, 0x73, 0x4e, 0x8f, 0xb0, 0xc0, 0xd5, 0x5d, 0x34, 0xea, 0xbb, 0xd8, 0x81, 0x7e, 0xe8, 0x34,
	0x0b, 0xad, 0xb4, 0x0b, 0x49, 0x86, 0x42, 0x9b, 0xa3, 0x46, 0x6d, 0x6b, 0xd7, 0xce, 0xd0, 0xae,
	0x9f, 0x41, 0x7e, 0x9b, 0x17, 0x79, 0xaa, 0xc5, 0x2e, 0x81, 0x8a, 0xa2, 0x9d, 0x45, 0x45, 0x79,
	0x96, 0x1e, 0xb2, 0x88, 0x18, 0xb1, 0xad, 0xa9, 0x54, 0xc9, 0xd2, 0x63, 0x92, 0x65, 0xf8, 0x82,
	0x18, 0xb9, 0x1d, 0x44, 0x56, 0x9e, 0xa8, 0x54, 0x9e, 0x16, 0xbd, 0x0a, 0xd6, 0xf2, 0x02, 0x16,
	0xf2, 0x02, 0x41, 0x5b, 0xe6, 0xb4, 0x51, 0x5d, 0x3d, 0xfb, 0x3f, 0x35, 0x60, 0xc3, 0x61, 0xd8,
	0xe8, 0x81, 0xa0, 0x1d, 0xca, 0x6d, 0x36, 0x54, 0xf6, 0xa8, 0x67, 0xb9, 0xfb, 0xc4, 0x6c, 0xd0,
	0xd4, 0x8e, 0x31, 0xd1, 0x07, 0xd0, 0x8e, 0xb0, 0xc0, 0x8a, 0xc9, 0xfe, 0xbe, 0xb7, 0x9b, 0xe2,
	0x7c, 0x77, 0x89, 0x6a, 0x81, 0x8a, 0x42, 0x9f, 0x4a, 0xed, 0xe2, 0x68, 0x2e, 0x21, 0x45, 0x6f,
	0x7f, 0xff, 0xd1, 0xb2, 0x57, 0x0e, 0x6c, 0x50, 0x50, 0xc6, 0xfb, 0x53, 0xf0, 0xae, 0x0a, 0x93,
	0x24, 0x69, 0xaa, 0x0f, 0xb0, 0x08, 0xa7, 0xc3, 0xc8, 0x24, 0x46, 0x15, 0x44, 0xef, 0xc1, 0x3d,
	0x0d, 0x9c, 0xe0, 0xfc, 0x98, 0x51, 0x92, 0xab, 0xd3, 0xb4, 0x82, 0x1a, 0xea, 0xff, 0xd2, 0x00,
	0xcf, 0x74, 0x5b, 0x89, 0xcb, 0xec, 0x9f, 0x67, 0xb6, 0xc3, 0xec, 0xc0, 0x5a, 0x42, 0xf8, 0x58,
	0x95, 0xf1, 0x98, 0xda, 0xbb, 0x4c, 0x72, 0xfd, 0x85, 0x84, 0x46, 0x0c, 0x7d, 0x08, 0x9b, 0x32,
	0xc2, 0xb4, 0xf5, 0x22, 0x50, 0x33, 0xb7, 0x9e, 0x10, 0xae, 0x97, 0xb5, 0xe1, 0xba, 0xd8, 0x69,
	0x18, 0xd9, 0xee, 0x63, 0x4d, 0xb4, 0x0b, 0xbd, 0x29, 0xc1, 0xd1, 0xf8, 0x1c, 0x67, 0xc4, 0xd0,
	0xb5, 0xa1, 0xe8, 0x3a, 0x0d, 0x31, 0x7d, 0x49, 0x70, 0x74, 0x80, 0x33, 0x12, 0x74, 0xa7, 0xe6,
	0xc9, 0xff, 0xbd, 0x09, 0xdb, 0x4b, 0xf6, 0x7d, 0x2b, 0x61, 0xeb, 0xc7, 0x6c, 0xfd, 0xd3, 0x63,
	0xb6, 0xaf, 0x38, 0xe6, 0x3b, 0x56, 0xa2, 0x31, 0x4e, 0xd8, 0x9c, 0xda, 0xfa, 0x59, 0xb3, 0xf5,
	0x23, 0x31, 0x27, 0xc8, 0xf4, 0x84, 0x8e, 0x1b, 0x14, 0x28, 0x0c, 0x7d, 0x04, 0x5b, 0x38, 0x14,
	0x73, 0x3c, 0x1b, 0x57, 0x17, 0xd4, 0x85, 0x85, 0xb4, 0x2f, 0x70, 0x97, 0x7d, 0x02, 0x7d, 0x13,
	0x2a, 0xe2, 0xa4, 0x2c, 0x32, 0x05, 0x9d, 0xc5, 0x09, 0x71, 0x6e, 0x61, 0x6a, 0x0b, 0xcc, 0xd4,
	0xee, 0x88, 0xf9, 0x7f, 0x34, 0x61, 0x7d, 0x44, 0xbe, 0xa9, 0x4e, 0x33, 0x37, 0xa7, 0x81, 0xa3,
	0x6b, 0xb3, 0xaa, 0xeb, 0x02, 0x15, 0xad, 0x1b, 0xa8, 0x48, 0x30, 0xbf, 0x34, 0xc4, 0x16, 0x54,
	0x48, 0xac, 0x9a, 0x21, 0x2b, 0x37, 0x66, 0x08, 0x7a, 0x04, 0x40, 0x99, 0x88, 0x27, 0xf9, 0x78,
	0xce, 0x67, 0x86, 0xdc, 0x9e, 0x46, 0xea, 0xbd, 0x71, 0xb5, 0xd6, 0x1b, 0x2b, 0x7d, 0xb7, 0x7b,
	0x7d, 0xf7, 0xef, 0x2d, 0x74, 0xce, 0xa5, 0xc3, 0x8f, 0xff, 0x5d, 0x43, 0x8f, 0x1d, 0x47, 0x44,
	0xe0, 0x78, 0x26, 0x33, 0x54, 0xc8, 0x4b, 0x58, 0x73, 0xa9, 0x9e, 0x75, 0x65, 0x87, 0xc7, 0x65,
	0x6f, 0x6b, 0xda, 0xca, 0x76, 0x40, 0x79, 0x89, 0xa4, 0x38, 0x97, 0x5d, 0x59, 0x53, 0x69, 0x2c,
	0x79, 0x1d, 0x44, 0x24, 0x0b, 0x79, 0x9c, 0xca, 0x3e, 0x69, 0x28, 0x74, 0x21, 0xff, 0xb7, 0x26,
	0x6c, 0x38, 0xe2, 0xfe, 0x5f, 0x2b, 0xff, 0xae, 0x56, 0x7e, 0x68, 0xc2, 0xd6, 0xd1, 0x57, 0x66,
	0xb0, 0xa3, 0x13, 0x76, 0x4b, 0x46, 0x3f, 0xab, 0x5c, 0x2b, 0xef, 0xab, 0x94, 0x5e, 0xb6, 0x6c,
	0x05, 0x2c, 0x2f, 0x9a, 0xc1, 0xf7, 0x0d, 0x58, 0xaf, 0xbb, 0xd0, 0xdb, 0x60, 0xb8, 0x19, 0x67,
	0x6a, 0xc2, 0xd1, 0x69, 0x66, 0x4e, 0xab, 0x47, 0x9c, 0x6d, 0xe8, 0x6a, 0x71, 0x8a, 0x44, 0x2b,
	0xe6, 0xb5, 0x37, 0x61, 0x75, 0x42, 0xc8, 0x18, 0x97, 0x39, 0x36, 0x21, 0x44, 0xe6, 0xd8, 0xb6,
	0x1a, 0xb9, 0x22, 0x52, 0x0a, 0x6a, 0xa7, 0x35, 0xff, 0x15, 0x6c, 0x56, 0x77, 0xad, 0x7b, 0xc7,
	0x13, 0xe8, 0xdb, 0xab, 0x59, 0x4e, 0xfd, 0x8d, 0x85, 0xdb, 0xda, 0x5d, 0xb2, 0x59, 0x59, 0x72,
	0xff, 0xaf, 0x16, 0xf4, 0x8a, 0x6b, 0x10, 0x3d, 0x73, 0x8d, 0xfb, 0xf5, 0xab, 0x54, 0x7d, 0x6d,
	0xf0, 0xa0, 0x0e, 0x6b, 0xea, 0xfc, 0x3b, 0xe8, 0x73, 0xb8, 0x57, 0xfd, 0xbb, 0x40, 0x83, 0x6a,
	0xac, 0xfb, 0xcb, 0x71, 0xcd, 0x3a, 0x2f, 0xe1, 0x8d, 0x02, 0xd6, 0xd3, 0x1d, 0xd2, 0x93, 0xc0,
	0x92, 0x09, 0x7d, 0xb0, 0xbd, 0xc4, 0x53, 0xac, 0xf4, 0x0c, 0x7a, 0x45, 0x31, 0x9a, 0xf3, 0xd4,
	0x3b, 0xef, 0x35, 0xfb, 0x38, 0x83, 0x8d, 0x85, 0xeb, 0x0f, 0xe9, 0x01, 0xe3, 0xaa, 0xeb, 0x7c,
	0xf0, 0xf8, 0x2a, 0x77, 0xb1, 0xea, 0x27, 0xd0, 0xb9, 0x2d, 0xc1, 0x2f, 0x60, 0xcd, 0xd5, 0xdf,
	0xb0, 0xb2, 0x24, 0x25, 0x0c, 0x2b, 0xcb, 0x52, 0xdc, 0xbf, 0x73, 0xde, 0x51, 0x7f, 0xd5, 0x1f,
	0xff, 0x1d, 0x00, 0x00, 0xff, 0xff, 0x31, 0xe2, 0x1b, 0x52, 0xa2, 0x0f, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PayRefundClient is the client API for PayRefund service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PayRefundClient interface {
	// @Desc    			支付退款请求
	// <AUTHOR>
	// @Date		 			2020-06-11
	PayRefund(ctx context.Context, in *PayRefundRequest, opts ...grpc.CallOption) (*PayRefundResponse, error)
	// @Desc    			支付退款查询
	// <AUTHOR>
	// @Date		 			2020-06-11
	PayRefundQuery(ctx context.Context, in *PayRefundQueryRequest, opts ...grpc.CallOption) (*PayRefundResponse, error)
	// @Desc    			电银退款结果异步回调通知
	// <AUTHOR>
	// @Date		 			2020-06-11
	PayRefundNotice(ctx context.Context, in *RefundNoticeRequest, opts ...grpc.CallOption) (*RefundNoticeResponse, error)
	// 发起退款
	NewRefund(ctx context.Context, in *NewRefundRequest, opts ...grpc.CallOption) (*PayRefundResponse, error)
	// 退款查询
	QueryRefundStatus(ctx context.Context, in *QueryRefundStatusRequest, opts ...grpc.CallOption) (*QueryRefundStatusResponse, error)
	Refund(ctx context.Context, in *PayRefundRequest, opts ...grpc.CallOption) (*PayRefundResponse, error)
	// @Desc    			退款查询（手续费）暂时只能查询电银线上帐号的退款
	// <AUTHOR>
	// @Date		 			2023-04-15
	DYRefundInfo(ctx context.Context, in *DYRefundInfoRequest, opts ...grpc.CallOption) (*DYRefundInfoResponse, error)
}

type payRefundClient struct {
	cc *grpc.ClientConn
}

func NewPayRefundClient(cc *grpc.ClientConn) PayRefundClient {
	return &payRefundClient{cc}
}

func (c *payRefundClient) PayRefund(ctx context.Context, in *PayRefundRequest, opts ...grpc.CallOption) (*PayRefundResponse, error) {
	out := new(PayRefundResponse)
	err := c.cc.Invoke(ctx, "/pay.PayRefund/PayRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payRefundClient) PayRefundQuery(ctx context.Context, in *PayRefundQueryRequest, opts ...grpc.CallOption) (*PayRefundResponse, error) {
	out := new(PayRefundResponse)
	err := c.cc.Invoke(ctx, "/pay.PayRefund/PayRefundQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payRefundClient) PayRefundNotice(ctx context.Context, in *RefundNoticeRequest, opts ...grpc.CallOption) (*RefundNoticeResponse, error) {
	out := new(RefundNoticeResponse)
	err := c.cc.Invoke(ctx, "/pay.PayRefund/PayRefundNotice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payRefundClient) NewRefund(ctx context.Context, in *NewRefundRequest, opts ...grpc.CallOption) (*PayRefundResponse, error) {
	out := new(PayRefundResponse)
	err := c.cc.Invoke(ctx, "/pay.PayRefund/NewRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payRefundClient) QueryRefundStatus(ctx context.Context, in *QueryRefundStatusRequest, opts ...grpc.CallOption) (*QueryRefundStatusResponse, error) {
	out := new(QueryRefundStatusResponse)
	err := c.cc.Invoke(ctx, "/pay.PayRefund/QueryRefundStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payRefundClient) Refund(ctx context.Context, in *PayRefundRequest, opts ...grpc.CallOption) (*PayRefundResponse, error) {
	out := new(PayRefundResponse)
	err := c.cc.Invoke(ctx, "/pay.PayRefund/Refund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payRefundClient) DYRefundInfo(ctx context.Context, in *DYRefundInfoRequest, opts ...grpc.CallOption) (*DYRefundInfoResponse, error) {
	out := new(DYRefundInfoResponse)
	err := c.cc.Invoke(ctx, "/pay.PayRefund/DYRefundInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PayRefundServer is the server API for PayRefund service.
type PayRefundServer interface {
	// @Desc    			支付退款请求
	// <AUTHOR>
	// @Date		 			2020-06-11
	PayRefund(context.Context, *PayRefundRequest) (*PayRefundResponse, error)
	// @Desc    			支付退款查询
	// <AUTHOR>
	// @Date		 			2020-06-11
	PayRefundQuery(context.Context, *PayRefundQueryRequest) (*PayRefundResponse, error)
	// @Desc    			电银退款结果异步回调通知
	// <AUTHOR>
	// @Date		 			2020-06-11
	PayRefundNotice(context.Context, *RefundNoticeRequest) (*RefundNoticeResponse, error)
	// 发起退款
	NewRefund(context.Context, *NewRefundRequest) (*PayRefundResponse, error)
	// 退款查询
	QueryRefundStatus(context.Context, *QueryRefundStatusRequest) (*QueryRefundStatusResponse, error)
	Refund(context.Context, *PayRefundRequest) (*PayRefundResponse, error)
	// @Desc    			退款查询（手续费）暂时只能查询电银线上帐号的退款
	// <AUTHOR>
	// @Date		 			2023-04-15
	DYRefundInfo(context.Context, *DYRefundInfoRequest) (*DYRefundInfoResponse, error)
}

// UnimplementedPayRefundServer can be embedded to have forward compatible implementations.
type UnimplementedPayRefundServer struct {
}

func (*UnimplementedPayRefundServer) PayRefund(ctx context.Context, req *PayRefundRequest) (*PayRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayRefund not implemented")
}
func (*UnimplementedPayRefundServer) PayRefundQuery(ctx context.Context, req *PayRefundQueryRequest) (*PayRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayRefundQuery not implemented")
}
func (*UnimplementedPayRefundServer) PayRefundNotice(ctx context.Context, req *RefundNoticeRequest) (*RefundNoticeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayRefundNotice not implemented")
}
func (*UnimplementedPayRefundServer) NewRefund(ctx context.Context, req *NewRefundRequest) (*PayRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewRefund not implemented")
}
func (*UnimplementedPayRefundServer) QueryRefundStatus(ctx context.Context, req *QueryRefundStatusRequest) (*QueryRefundStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRefundStatus not implemented")
}
func (*UnimplementedPayRefundServer) Refund(ctx context.Context, req *PayRefundRequest) (*PayRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (*UnimplementedPayRefundServer) DYRefundInfo(ctx context.Context, req *DYRefundInfoRequest) (*DYRefundInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DYRefundInfo not implemented")
}

func RegisterPayRefundServer(s *grpc.Server, srv PayRefundServer) {
	s.RegisterService(&_PayRefund_serviceDesc, srv)
}

func _PayRefund_PayRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayRefundServer).PayRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayRefund/PayRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayRefundServer).PayRefund(ctx, req.(*PayRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayRefund_PayRefundQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayRefundQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayRefundServer).PayRefundQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayRefund/PayRefundQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayRefundServer).PayRefundQuery(ctx, req.(*PayRefundQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayRefund_PayRefundNotice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundNoticeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayRefundServer).PayRefundNotice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayRefund/PayRefundNotice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayRefundServer).PayRefundNotice(ctx, req.(*RefundNoticeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayRefund_NewRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayRefundServer).NewRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayRefund/NewRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayRefundServer).NewRefund(ctx, req.(*NewRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayRefund_QueryRefundStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRefundStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayRefundServer).QueryRefundStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayRefund/QueryRefundStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayRefundServer).QueryRefundStatus(ctx, req.(*QueryRefundStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayRefund_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayRefundServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayRefund/Refund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayRefundServer).Refund(ctx, req.(*PayRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayRefund_DYRefundInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DYRefundInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayRefundServer).DYRefundInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayRefund/DYRefundInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayRefundServer).DYRefundInfo(ctx, req.(*DYRefundInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PayRefund_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pay.PayRefund",
	HandlerType: (*PayRefundServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PayRefund",
			Handler:    _PayRefund_PayRefund_Handler,
		},
		{
			MethodName: "PayRefundQuery",
			Handler:    _PayRefund_PayRefundQuery_Handler,
		},
		{
			MethodName: "PayRefundNotice",
			Handler:    _PayRefund_PayRefundNotice_Handler,
		},
		{
			MethodName: "NewRefund",
			Handler:    _PayRefund_NewRefund_Handler,
		},
		{
			MethodName: "QueryRefundStatus",
			Handler:    _PayRefund_QueryRefundStatus_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _PayRefund_Refund_Handler,
		},
		{
			MethodName: "DYRefundInfo",
			Handler:    _PayRefund_DYRefundInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pay/pay_refund.proto",
}
