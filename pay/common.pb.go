// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pay/common.proto

package pay

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type BaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f24d3f40e539ee83, []int{0}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type ScanHeadBase struct {
	//商户号
	MercId string `protobuf:"bytes,1,opt,name=merc_id,json=mercId,proto3" json:"merc_id"`
	//机构号
	OrgId string `protobuf:"bytes,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	//传机具编号（tsn）
	TrmSn string `protobuf:"bytes,3,opt,name=trm_sn,json=trmSn,proto3" json:"trm_sn"`
	//终端号，传标准终端绑定接口返回的dyTermNo
	TrmId                string   `protobuf:"bytes,4,opt,name=trm_id,json=trmId,proto3" json:"trm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanHeadBase) Reset()         { *m = ScanHeadBase{} }
func (m *ScanHeadBase) String() string { return proto.CompactTextString(m) }
func (*ScanHeadBase) ProtoMessage()    {}
func (*ScanHeadBase) Descriptor() ([]byte, []int) {
	return fileDescriptor_f24d3f40e539ee83, []int{1}
}

func (m *ScanHeadBase) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanHeadBase.Unmarshal(m, b)
}
func (m *ScanHeadBase) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanHeadBase.Marshal(b, m, deterministic)
}
func (m *ScanHeadBase) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanHeadBase.Merge(m, src)
}
func (m *ScanHeadBase) XXX_Size() int {
	return xxx_messageInfo_ScanHeadBase.Size(m)
}
func (m *ScanHeadBase) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanHeadBase.DiscardUnknown(m)
}

var xxx_messageInfo_ScanHeadBase proto.InternalMessageInfo

func (m *ScanHeadBase) GetMercId() string {
	if m != nil {
		return m.MercId
	}
	return ""
}

func (m *ScanHeadBase) GetOrgId() string {
	if m != nil {
		return m.OrgId
	}
	return ""
}

func (m *ScanHeadBase) GetTrmSn() string {
	if m != nil {
		return m.TrmSn
	}
	return ""
}

func (m *ScanHeadBase) GetTrmId() string {
	if m != nil {
		return m.TrmId
	}
	return ""
}

type PayHeadBase struct {
	//消息类型,交易类型：消费、撤销、退货、查询的消息类型统一上送 0200;签名主密钥获取接口是 0800
	MsgType string `protobuf:"bytes,1,opt,name=msgType,proto3" json:"msgType"`
	//机具编号,智能 POS 终端的机具编号
	Termidm string `protobuf:"bytes,2,opt,name=termidm,proto3" json:"termidm"`
	//商户号,电银内部商户号
	Mercode string `protobuf:"bytes,3,opt,name=mercode,proto3" json:"mercode"`
	//终端号,电银内部终端号
	Termcde string `protobuf:"bytes,4,opt,name=termcde,proto3" json:"termcde"`
	//设备唯一标识,终端设备的唯一标识
	Imei string `protobuf:"bytes,5,opt,name=imei,proto3" json:"imei"`
	//交易发起时间,时间格式:年月日时分秒YYYYMMDDHHMMSS
	SendTime string `protobuf:"bytes,6,opt,name=sendTime,proto3" json:"sendTime"`
	//基站信息,格式：MCC|MNC|LAC|CID|STH,wifi信号时为MAC|STH (STH 为信号强度)
	StationInfo          string   `protobuf:"bytes,7,opt,name=stationInfo,proto3" json:"stationInfo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayHeadBase) Reset()         { *m = PayHeadBase{} }
func (m *PayHeadBase) String() string { return proto.CompactTextString(m) }
func (*PayHeadBase) ProtoMessage()    {}
func (*PayHeadBase) Descriptor() ([]byte, []int) {
	return fileDescriptor_f24d3f40e539ee83, []int{2}
}

func (m *PayHeadBase) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayHeadBase.Unmarshal(m, b)
}
func (m *PayHeadBase) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayHeadBase.Marshal(b, m, deterministic)
}
func (m *PayHeadBase) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayHeadBase.Merge(m, src)
}
func (m *PayHeadBase) XXX_Size() int {
	return xxx_messageInfo_PayHeadBase.Size(m)
}
func (m *PayHeadBase) XXX_DiscardUnknown() {
	xxx_messageInfo_PayHeadBase.DiscardUnknown(m)
}

var xxx_messageInfo_PayHeadBase proto.InternalMessageInfo

func (m *PayHeadBase) GetMsgType() string {
	if m != nil {
		return m.MsgType
	}
	return ""
}

func (m *PayHeadBase) GetTermidm() string {
	if m != nil {
		return m.Termidm
	}
	return ""
}

func (m *PayHeadBase) GetMercode() string {
	if m != nil {
		return m.Mercode
	}
	return ""
}

func (m *PayHeadBase) GetTermcde() string {
	if m != nil {
		return m.Termcde
	}
	return ""
}

func (m *PayHeadBase) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *PayHeadBase) GetSendTime() string {
	if m != nil {
		return m.SendTime
	}
	return ""
}

func (m *PayHeadBase) GetStationInfo() string {
	if m != nil {
		return m.StationInfo
	}
	return ""
}

func init() {
	proto.RegisterType((*BaseResponse)(nil), "pay.BaseResponse")
	proto.RegisterType((*ScanHeadBase)(nil), "pay.ScanHeadBase")
	proto.RegisterType((*PayHeadBase)(nil), "pay.PayHeadBase")
}

func init() { proto.RegisterFile("pay/common.proto", fileDescriptor_f24d3f40e539ee83) }

var fileDescriptor_f24d3f40e539ee83 = []byte{
	// 271 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x4c, 0x91, 0x31, 0x6f, 0xb3, 0x30,
	0x10, 0x86, 0xc5, 0x47, 0x20, 0x5f, 0x2e, 0x19, 0x2a, 0xab, 0x55, 0xad, 0x4e, 0x88, 0x29, 0x53,
	0x3b, 0xf4, 0x1f, 0x74, 0x2a, 0x5b, 0x45, 0xb2, 0x47, 0x2e, 0xbe, 0x22, 0x4b, 0xb5, 0x0f, 0x9d,
	0xbd, 0xf0, 0x0b, 0xfb, 0xb7, 0x2a, 0x1b, 0x8c, 0xba, 0xf9, 0x79, 0x0e, 0xde, 0xe3, 0xc5, 0x70,
	0x37, 0xa9, 0xf9, 0x65, 0x20, 0x6b, 0xc9, 0x3d, 0x4f, 0x4c, 0x81, 0x44, 0x39, 0xa9, 0xb9, 0xed,
	0xe1, 0xf4, 0xa6, 0x3c, 0xf6, 0xe8, 0x27, 0x72, 0x1e, 0x85, 0x80, 0xdd, 0x40, 0x1a, 0x65, 0xd1,
	0x14, 0xe7, 0xaa, 0x4f, 0x67, 0x21, 0x61, 0x6f, 0xd1, 0x7b, 0x35, 0xa2, 0xfc, 0xd7, 0x14, 0xe7,
	0x43, 0x9f, 0x51, 0xdc, 0x43, 0x85, 0xcc, 0xc4, 0xb2, 0x4c, 0x7e, 0x81, 0xf6, 0x1b, 0x4e, 0x97,
	0x41, 0xb9, 0x77, 0x54, 0x3a, 0x66, 0x8b, 0xc7, 0xf8, 0x3e, 0x0f, 0x37, 0xa3, 0x53, 0xec, 0xa1,
	0xaf, 0x23, 0x76, 0x5a, 0x3c, 0x40, 0x4d, 0x3c, 0x46, 0xbf, 0xe4, 0x56, 0xc4, 0xe3, 0xa2, 0x03,
	0xdb, 0x9b, 0x77, 0x39, 0x36, 0xb0, 0xbd, 0xb8, 0xac, 0x8d, 0x96, 0xbb, 0x4d, 0x77, 0xba, 0xfd,
	0x29, 0xe0, 0xf8, 0xa1, 0xe6, 0x6d, 0x5b, 0xfc, 0x5a, 0x3f, 0x5e, 0xe7, 0x09, 0xd7, 0x6d, 0x19,
	0xe3, 0x24, 0x20, 0x5b, 0xa3, 0x6d, 0xee, 0xb1, 0xe2, 0xd2, 0x90, 0x53, 0xf1, 0x32, 0x37, 0xe4,
	0xdc, 0x3d, 0x3e, 0x34, 0x68, 0x5c, 0xb7, 0x66, 0x8c, 0x7f, 0xca, 0x58, 0x34, 0xb2, 0x4a, 0x3a,
	0x9d, 0xc5, 0x13, 0xfc, 0xf7, 0xe8, 0xf4, 0xd5, 0x58, 0x94, 0x75, 0xf2, 0x1b, 0x8b, 0x06, 0x8e,
	0x3e, 0xa8, 0x60, 0xc8, 0x75, 0xee, 0x8b, 0xe4, 0x3e, 0x8d, 0xff, 0xaa, 0xcf, 0x3a, 0xdd, 0xcb,
	0xeb, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0a, 0x2d, 0xd0, 0xf4, 0xab, 0x01, 0x00, 0x00,
}
