// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pay/pay_info.proto

package pay

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type UnifiedOrderRequest struct {
	// 支付方式    1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）
	// 13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付 17：标准支付宝小程序支付 18：百度小程序支付
	TransType int32 `protobuf:"varint,1,opt,name=TransType,proto3" json:"TransType"`
	//商户流水号
	OutTradeNo string `protobuf:"bytes,2,opt,name=OutTradeNo,proto3" json:"OutTradeNo"`
	//交易金额
	PayPrice int32 `protobuf:"varint,3,opt,name=PayPrice,proto3" json:"PayPrice"`
	//总金额
	TotalPrice int32 `protobuf:"varint,4,opt,name=TotalPrice,proto3" json:"TotalPrice"`
	//优惠金额
	Discount  int32  `protobuf:"varint,5,opt,name=Discount,proto3" json:"Discount"`
	ProductId string `protobuf:"bytes,6,opt,name=ProductId,proto3" json:"ProductId"`
	//商品名称
	ProductName string `protobuf:"bytes,7,opt,name=ProductName,proto3" json:"ProductName"`
	//商品描述
	ProductDesc string `protobuf:"bytes,8,opt,name=ProductDesc,proto3" json:"ProductDesc"`
	ClientIP    string `protobuf:"bytes,9,opt,name=ClientIP,proto3" json:"ClientIP"`
	//微信 JSAPI 支付时必传
	Openid string `protobuf:"bytes,10,opt,name=Openid,proto3" json:"Openid"`
	//商户号
	MerchantId string `protobuf:"bytes,11,opt,name=MerchantId,proto3" json:"MerchantId"`
	//后台回调地址（支付中心回调电商）
	NotifyUrl string `protobuf:"bytes,12,opt,name=NotifyUrl,proto3" json:"NotifyUrl"`
	//扩展信息 预留字段，JSON 格式
	ExtendInfo string `protobuf:"bytes,13,opt,name=ExtendInfo,proto3" json:"ExtendInfo"`
	//子商户公众账号 ID
	SubAppId string `protobuf:"bytes,14,opt,name=SubAppId,proto3" json:"SubAppId"`
	//商户订单号
	OrderId string `protobuf:"bytes,15,opt,name=OrderId,proto3" json:"OrderId"`
	//订单有限时间(分钟)
	ValidTime int32 `protobuf:"varint,16,opt,name=ValidTime,proto3" json:"ValidTime"`
	//交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
	OrderPayType string `protobuf:"bytes,17,opt,name=OrderPayType,proto3" json:"OrderPayType"`
	// appId 应用id，1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS，6：上海兽丘，7：极宠家
	AppId                int32    `protobuf:"varint,18,opt,name=AppId,proto3" json:"AppId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedOrderRequest) Reset()         { *m = UnifiedOrderRequest{} }
func (m *UnifiedOrderRequest) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderRequest) ProtoMessage()    {}
func (*UnifiedOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{0}
}

func (m *UnifiedOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderRequest.Unmarshal(m, b)
}
func (m *UnifiedOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderRequest.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderRequest.Merge(m, src)
}
func (m *UnifiedOrderRequest) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderRequest.Size(m)
}
func (m *UnifiedOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderRequest proto.InternalMessageInfo

func (m *UnifiedOrderRequest) GetTransType() int32 {
	if m != nil {
		return m.TransType
	}
	return 0
}

func (m *UnifiedOrderRequest) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *UnifiedOrderRequest) GetPayPrice() int32 {
	if m != nil {
		return m.PayPrice
	}
	return 0
}

func (m *UnifiedOrderRequest) GetTotalPrice() int32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *UnifiedOrderRequest) GetDiscount() int32 {
	if m != nil {
		return m.Discount
	}
	return 0
}

func (m *UnifiedOrderRequest) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *UnifiedOrderRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *UnifiedOrderRequest) GetProductDesc() string {
	if m != nil {
		return m.ProductDesc
	}
	return ""
}

func (m *UnifiedOrderRequest) GetClientIP() string {
	if m != nil {
		return m.ClientIP
	}
	return ""
}

func (m *UnifiedOrderRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *UnifiedOrderRequest) GetMerchantId() string {
	if m != nil {
		return m.MerchantId
	}
	return ""
}

func (m *UnifiedOrderRequest) GetNotifyUrl() string {
	if m != nil {
		return m.NotifyUrl
	}
	return ""
}

func (m *UnifiedOrderRequest) GetExtendInfo() string {
	if m != nil {
		return m.ExtendInfo
	}
	return ""
}

func (m *UnifiedOrderRequest) GetSubAppId() string {
	if m != nil {
		return m.SubAppId
	}
	return ""
}

func (m *UnifiedOrderRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UnifiedOrderRequest) GetValidTime() int32 {
	if m != nil {
		return m.ValidTime
	}
	return 0
}

func (m *UnifiedOrderRequest) GetOrderPayType() string {
	if m != nil {
		return m.OrderPayType
	}
	return ""
}

func (m *UnifiedOrderRequest) GetAppId() int32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

type UnifiedOrderReturnData struct {
	//微信appid
	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId"`
	//随机字符串，不长于 32位
	NonceStr string `protobuf:"bytes,2,opt,name=nonceStr,proto3" json:"nonceStr"`
	//订单详情扩展字符串
	Package string `protobuf:"bytes,3,opt,name=package,proto3" json:"package"`
	//签名
	PaySign string `protobuf:"bytes,4,opt,name=paySign,proto3" json:"paySign"`
	//签名方式
	SignType string `protobuf:"bytes,5,opt,name=signType,proto3" json:"signType"`
	//时间戳
	TimeStamp            string   `protobuf:"bytes,6,opt,name=timeStamp,proto3" json:"timeStamp"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedOrderReturnData) Reset()         { *m = UnifiedOrderReturnData{} }
func (m *UnifiedOrderReturnData) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderReturnData) ProtoMessage()    {}
func (*UnifiedOrderReturnData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{1}
}

func (m *UnifiedOrderReturnData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderReturnData.Unmarshal(m, b)
}
func (m *UnifiedOrderReturnData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderReturnData.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderReturnData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderReturnData.Merge(m, src)
}
func (m *UnifiedOrderReturnData) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderReturnData.Size(m)
}
func (m *UnifiedOrderReturnData) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderReturnData.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderReturnData proto.InternalMessageInfo

func (m *UnifiedOrderReturnData) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetNonceStr() string {
	if m != nil {
		return m.NonceStr
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetPackage() string {
	if m != nil {
		return m.Package
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetPaySign() string {
	if m != nil {
		return m.PaySign
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetSignType() string {
	if m != nil {
		return m.SignType
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetTimeStamp() string {
	if m != nil {
		return m.TimeStamp
	}
	return ""
}

type UnifiedOrderResponse struct {
	Code    int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Details *UnifiedOrderResponseData `protobuf:"bytes,3,opt,name=details,proto3" json:"details"`
	//支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
	PayType              int32    `protobuf:"varint,4,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedOrderResponse) Reset()         { *m = UnifiedOrderResponse{} }
func (m *UnifiedOrderResponse) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderResponse) ProtoMessage()    {}
func (*UnifiedOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{2}
}

func (m *UnifiedOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderResponse.Unmarshal(m, b)
}
func (m *UnifiedOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderResponse.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderResponse.Merge(m, src)
}
func (m *UnifiedOrderResponse) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderResponse.Size(m)
}
func (m *UnifiedOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderResponse proto.InternalMessageInfo

func (m *UnifiedOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UnifiedOrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UnifiedOrderResponse) GetDetails() *UnifiedOrderResponseData {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *UnifiedOrderResponse) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

type UnifiedOrderResponseData struct {
	//当支付方式为wx_jsapi返回
	WxJsapi *UnifiedOrderReturnData `protobuf:"bytes,1,opt,name=wx_jsapi,json=wxJsapi,proto3" json:"wx_jsapi"`
	OrderId string                  `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//当支付方式为WX_NATIVE,AL_NATIVE返回
	WxNative *UnifiedOrderWxNative `protobuf:"bytes,3,opt,name=wx_native,json=wxNative,proto3" json:"wx_native"`
	//当支付方式为WX_JSAPP返回
	WxJsApp *UnifiedOrderWxJsApp `protobuf:"bytes,4,opt,name=wx_js_app,json=wxJsApp,proto3" json:"wx_js_app"`
	//当支付方式是网银
	Bank string `protobuf:"bytes,5,opt,name=bank,proto3" json:"bank"`
	// 支付宝小程序交易单号
	AliTradeNo string `protobuf:"bytes,6,opt,name=aliTradeNo,proto3" json:"aliTradeNo"`
	// 百度支付订单信息
	BdOrderInfo          *UnifiedOrderBdPay `protobuf:"bytes,7,opt,name=bd_order_info,json=bdOrderInfo,proto3" json:"bd_order_info"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UnifiedOrderResponseData) Reset()         { *m = UnifiedOrderResponseData{} }
func (m *UnifiedOrderResponseData) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderResponseData) ProtoMessage()    {}
func (*UnifiedOrderResponseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{3}
}

func (m *UnifiedOrderResponseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderResponseData.Unmarshal(m, b)
}
func (m *UnifiedOrderResponseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderResponseData.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderResponseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderResponseData.Merge(m, src)
}
func (m *UnifiedOrderResponseData) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderResponseData.Size(m)
}
func (m *UnifiedOrderResponseData) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderResponseData.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderResponseData proto.InternalMessageInfo

func (m *UnifiedOrderResponseData) GetWxJsapi() *UnifiedOrderReturnData {
	if m != nil {
		return m.WxJsapi
	}
	return nil
}

func (m *UnifiedOrderResponseData) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UnifiedOrderResponseData) GetWxNative() *UnifiedOrderWxNative {
	if m != nil {
		return m.WxNative
	}
	return nil
}

func (m *UnifiedOrderResponseData) GetWxJsApp() *UnifiedOrderWxJsApp {
	if m != nil {
		return m.WxJsApp
	}
	return nil
}

func (m *UnifiedOrderResponseData) GetBank() string {
	if m != nil {
		return m.Bank
	}
	return ""
}

func (m *UnifiedOrderResponseData) GetAliTradeNo() string {
	if m != nil {
		return m.AliTradeNo
	}
	return ""
}

func (m *UnifiedOrderResponseData) GetBdOrderInfo() *UnifiedOrderBdPay {
	if m != nil {
		return m.BdOrderInfo
	}
	return nil
}

type PayInfoQueryRequest struct {
	//商户订单号
	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//商户号
	MerchantId string `protobuf:"bytes,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id"`
	// 应用id，1：阿闻，2：子龙，3：R1，4：互联网
	AppId int32 `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id"`
	//支付方式
	PayType int32 `protobuf:"varint,4,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	//商品描述
	ProductDesc          string   `protobuf:"bytes,5,opt,name=product_desc,json=productDesc,proto3" json:"product_desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayInfoQueryRequest) Reset()         { *m = PayInfoQueryRequest{} }
func (m *PayInfoQueryRequest) String() string { return proto.CompactTextString(m) }
func (*PayInfoQueryRequest) ProtoMessage()    {}
func (*PayInfoQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{4}
}

func (m *PayInfoQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayInfoQueryRequest.Unmarshal(m, b)
}
func (m *PayInfoQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayInfoQueryRequest.Marshal(b, m, deterministic)
}
func (m *PayInfoQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayInfoQueryRequest.Merge(m, src)
}
func (m *PayInfoQueryRequest) XXX_Size() int {
	return xxx_messageInfo_PayInfoQueryRequest.Size(m)
}
func (m *PayInfoQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PayInfoQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PayInfoQueryRequest proto.InternalMessageInfo

func (m *PayInfoQueryRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PayInfoQueryRequest) GetMerchantId() string {
	if m != nil {
		return m.MerchantId
	}
	return ""
}

func (m *PayInfoQueryRequest) GetAppId() int32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *PayInfoQueryRequest) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *PayInfoQueryRequest) GetProductDesc() string {
	if m != nil {
		return m.ProductDesc
	}
	return ""
}

type PayInfoQueryResponse struct {
	Code                 int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *PayInfoQueryResponsePayInfo `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PayInfoQueryResponse) Reset()         { *m = PayInfoQueryResponse{} }
func (m *PayInfoQueryResponse) String() string { return proto.CompactTextString(m) }
func (*PayInfoQueryResponse) ProtoMessage()    {}
func (*PayInfoQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{5}
}

func (m *PayInfoQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayInfoQueryResponse.Unmarshal(m, b)
}
func (m *PayInfoQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayInfoQueryResponse.Marshal(b, m, deterministic)
}
func (m *PayInfoQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayInfoQueryResponse.Merge(m, src)
}
func (m *PayInfoQueryResponse) XXX_Size() int {
	return xxx_messageInfo_PayInfoQueryResponse.Size(m)
}
func (m *PayInfoQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PayInfoQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PayInfoQueryResponse proto.InternalMessageInfo

func (m *PayInfoQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PayInfoQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PayInfoQueryResponse) GetData() *PayInfoQueryResponsePayInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type PayInfoQueryResponsePayInfo struct {
	// 原交易订单号
	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 交易状态
	TransState string `protobuf:"bytes,2,opt,name=trans_state,json=transState,proto3" json:"trans_state"`
	// 交易金额
	TransAmt int32 `protobuf:"varint,3,opt,name=trans_amt,json=transAmt,proto3" json:"trans_amt"`
	// 交易手续费(返回空就是手续费没结算)
	FeeAmt string `protobuf:"bytes,7,opt,name=fee_amt,json=feeAmt,proto3" json:"fee_amt"`
	// 订单创建时间
	OrderTime string `protobuf:"bytes,4,opt,name=order_time,json=orderTime,proto3" json:"order_time"`
	// 支付完成时间
	PayTime string `protobuf:"bytes,5,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	// 支付流水号
	TradeNo              string   `protobuf:"bytes,6,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayInfoQueryResponsePayInfo) Reset()         { *m = PayInfoQueryResponsePayInfo{} }
func (m *PayInfoQueryResponsePayInfo) String() string { return proto.CompactTextString(m) }
func (*PayInfoQueryResponsePayInfo) ProtoMessage()    {}
func (*PayInfoQueryResponsePayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{5, 0}
}

func (m *PayInfoQueryResponsePayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayInfoQueryResponsePayInfo.Unmarshal(m, b)
}
func (m *PayInfoQueryResponsePayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayInfoQueryResponsePayInfo.Marshal(b, m, deterministic)
}
func (m *PayInfoQueryResponsePayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayInfoQueryResponsePayInfo.Merge(m, src)
}
func (m *PayInfoQueryResponsePayInfo) XXX_Size() int {
	return xxx_messageInfo_PayInfoQueryResponsePayInfo.Size(m)
}
func (m *PayInfoQueryResponsePayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PayInfoQueryResponsePayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PayInfoQueryResponsePayInfo proto.InternalMessageInfo

func (m *PayInfoQueryResponsePayInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PayInfoQueryResponsePayInfo) GetTransState() string {
	if m != nil {
		return m.TransState
	}
	return ""
}

func (m *PayInfoQueryResponsePayInfo) GetTransAmt() int32 {
	if m != nil {
		return m.TransAmt
	}
	return 0
}

func (m *PayInfoQueryResponsePayInfo) GetFeeAmt() string {
	if m != nil {
		return m.FeeAmt
	}
	return ""
}

func (m *PayInfoQueryResponsePayInfo) GetOrderTime() string {
	if m != nil {
		return m.OrderTime
	}
	return ""
}

func (m *PayInfoQueryResponsePayInfo) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *PayInfoQueryResponsePayInfo) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

type UnifiedOrderWxJsApp struct {
	//transType 为 WX_JSAPP/AL_JSAPP 时返回
	JsAppId string `protobuf:"bytes,1,opt,name=jsAppId,proto3" json:"jsAppId"`
	//transType 为 WX_JSAPP/AL_JSAPP 时返回
	JsAppUrl             string   `protobuf:"bytes,2,opt,name=jsAppUrl,proto3" json:"jsAppUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedOrderWxJsApp) Reset()         { *m = UnifiedOrderWxJsApp{} }
func (m *UnifiedOrderWxJsApp) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderWxJsApp) ProtoMessage()    {}
func (*UnifiedOrderWxJsApp) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{6}
}

func (m *UnifiedOrderWxJsApp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderWxJsApp.Unmarshal(m, b)
}
func (m *UnifiedOrderWxJsApp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderWxJsApp.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderWxJsApp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderWxJsApp.Merge(m, src)
}
func (m *UnifiedOrderWxJsApp) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderWxJsApp.Size(m)
}
func (m *UnifiedOrderWxJsApp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderWxJsApp.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderWxJsApp proto.InternalMessageInfo

func (m *UnifiedOrderWxJsApp) GetJsAppId() string {
	if m != nil {
		return m.JsAppId
	}
	return ""
}

func (m *UnifiedOrderWxJsApp) GetJsAppUrl() string {
	if m != nil {
		return m.JsAppUrl
	}
	return ""
}

type UnifiedOrderWxNative struct {
	//支付链接
	PayUrl               string   `protobuf:"bytes,1,opt,name=payUrl,proto3" json:"payUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedOrderWxNative) Reset()         { *m = UnifiedOrderWxNative{} }
func (m *UnifiedOrderWxNative) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderWxNative) ProtoMessage()    {}
func (*UnifiedOrderWxNative) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{7}
}

func (m *UnifiedOrderWxNative) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderWxNative.Unmarshal(m, b)
}
func (m *UnifiedOrderWxNative) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderWxNative.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderWxNative) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderWxNative.Merge(m, src)
}
func (m *UnifiedOrderWxNative) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderWxNative.Size(m)
}
func (m *UnifiedOrderWxNative) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderWxNative.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderWxNative proto.InternalMessageInfo

func (m *UnifiedOrderWxNative) GetPayUrl() string {
	if m != nil {
		return m.PayUrl
	}
	return ""
}

// 百度支付信息
type UnifiedOrderBdPay struct {
	DealId               string                    `protobuf:"bytes,1,opt,name=dealId,proto3" json:"dealId"`
	AppKey               string                    `protobuf:"bytes,2,opt,name=appKey,proto3" json:"appKey"`
	TotalAmount          string                    `protobuf:"bytes,3,opt,name=totalAmount,proto3" json:"totalAmount"`
	TpOrderId            string                    `protobuf:"bytes,4,opt,name=tpOrderId,proto3" json:"tpOrderId"`
	NotifyUrl            string                    `protobuf:"bytes,5,opt,name=notifyUrl,proto3" json:"notifyUrl"`
	DealTitle            string                    `protobuf:"bytes,6,opt,name=dealTitle,proto3" json:"dealTitle"`
	SignFieldsRange      string                    `protobuf:"bytes,7,opt,name=signFieldsRange,proto3" json:"signFieldsRange"`
	RsaSign              string                    `protobuf:"bytes,8,opt,name=rsaSign,proto3" json:"rsaSign"`
	BizInfo              *UnifiedOrderBdPayBizInfo `protobuf:"bytes,9,opt,name=bizInfo,proto3" json:"bizInfo"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UnifiedOrderBdPay) Reset()         { *m = UnifiedOrderBdPay{} }
func (m *UnifiedOrderBdPay) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderBdPay) ProtoMessage()    {}
func (*UnifiedOrderBdPay) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{8}
}

func (m *UnifiedOrderBdPay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderBdPay.Unmarshal(m, b)
}
func (m *UnifiedOrderBdPay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderBdPay.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderBdPay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderBdPay.Merge(m, src)
}
func (m *UnifiedOrderBdPay) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderBdPay.Size(m)
}
func (m *UnifiedOrderBdPay) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderBdPay.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderBdPay proto.InternalMessageInfo

func (m *UnifiedOrderBdPay) GetDealId() string {
	if m != nil {
		return m.DealId
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetAppKey() string {
	if m != nil {
		return m.AppKey
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetTotalAmount() string {
	if m != nil {
		return m.TotalAmount
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetTpOrderId() string {
	if m != nil {
		return m.TpOrderId
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetNotifyUrl() string {
	if m != nil {
		return m.NotifyUrl
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetDealTitle() string {
	if m != nil {
		return m.DealTitle
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetSignFieldsRange() string {
	if m != nil {
		return m.SignFieldsRange
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetRsaSign() string {
	if m != nil {
		return m.RsaSign
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetBizInfo() *UnifiedOrderBdPayBizInfo {
	if m != nil {
		return m.BizInfo
	}
	return nil
}

type UnifiedOrderBdPayBizInfo struct {
	TpData               *BizInfoTpData `protobuf:"bytes,1,opt,name=tpData,proto3" json:"tpData"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UnifiedOrderBdPayBizInfo) Reset()         { *m = UnifiedOrderBdPayBizInfo{} }
func (m *UnifiedOrderBdPayBizInfo) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderBdPayBizInfo) ProtoMessage()    {}
func (*UnifiedOrderBdPayBizInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{9}
}

func (m *UnifiedOrderBdPayBizInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderBdPayBizInfo.Unmarshal(m, b)
}
func (m *UnifiedOrderBdPayBizInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderBdPayBizInfo.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderBdPayBizInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderBdPayBizInfo.Merge(m, src)
}
func (m *UnifiedOrderBdPayBizInfo) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderBdPayBizInfo.Size(m)
}
func (m *UnifiedOrderBdPayBizInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderBdPayBizInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderBdPayBizInfo proto.InternalMessageInfo

func (m *UnifiedOrderBdPayBizInfo) GetTpData() *BizInfoTpData {
	if m != nil {
		return m.TpData
	}
	return nil
}

type BizInfoTpData struct {
	DealId               string   `protobuf:"bytes,1,opt,name=dealId,proto3" json:"dealId"`
	AppKey               string   `protobuf:"bytes,2,opt,name=appKey,proto3" json:"appKey"`
	TotalAmount          string   `protobuf:"bytes,3,opt,name=totalAmount,proto3" json:"totalAmount"`
	TpOrderId            string   `protobuf:"bytes,4,opt,name=tpOrderId,proto3" json:"tpOrderId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BizInfoTpData) Reset()         { *m = BizInfoTpData{} }
func (m *BizInfoTpData) String() string { return proto.CompactTextString(m) }
func (*BizInfoTpData) ProtoMessage()    {}
func (*BizInfoTpData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{10}
}

func (m *BizInfoTpData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BizInfoTpData.Unmarshal(m, b)
}
func (m *BizInfoTpData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BizInfoTpData.Marshal(b, m, deterministic)
}
func (m *BizInfoTpData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BizInfoTpData.Merge(m, src)
}
func (m *BizInfoTpData) XXX_Size() int {
	return xxx_messageInfo_BizInfoTpData.Size(m)
}
func (m *BizInfoTpData) XXX_DiscardUnknown() {
	xxx_messageInfo_BizInfoTpData.DiscardUnknown(m)
}

var xxx_messageInfo_BizInfoTpData proto.InternalMessageInfo

func (m *BizInfoTpData) GetDealId() string {
	if m != nil {
		return m.DealId
	}
	return ""
}

func (m *BizInfoTpData) GetAppKey() string {
	if m != nil {
		return m.AppKey
	}
	return ""
}

func (m *BizInfoTpData) GetTotalAmount() string {
	if m != nil {
		return m.TotalAmount
	}
	return ""
}

func (m *BizInfoTpData) GetTpOrderId() string {
	if m != nil {
		return m.TpOrderId
	}
	return ""
}

type BaiduOrderRefundGetRequest struct {
	TradeNo              string   `protobuf:"bytes,1,opt,name=tradeNo,proto3" json:"tradeNo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaiduOrderRefundGetRequest) Reset()         { *m = BaiduOrderRefundGetRequest{} }
func (m *BaiduOrderRefundGetRequest) String() string { return proto.CompactTextString(m) }
func (*BaiduOrderRefundGetRequest) ProtoMessage()    {}
func (*BaiduOrderRefundGetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{11}
}

func (m *BaiduOrderRefundGetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaiduOrderRefundGetRequest.Unmarshal(m, b)
}
func (m *BaiduOrderRefundGetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaiduOrderRefundGetRequest.Marshal(b, m, deterministic)
}
func (m *BaiduOrderRefundGetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaiduOrderRefundGetRequest.Merge(m, src)
}
func (m *BaiduOrderRefundGetRequest) XXX_Size() int {
	return xxx_messageInfo_BaiduOrderRefundGetRequest.Size(m)
}
func (m *BaiduOrderRefundGetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaiduOrderRefundGetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaiduOrderRefundGetRequest proto.InternalMessageInfo

func (m *BaiduOrderRefundGetRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

type BaiduOrderRefundGetResponse struct {
	// 开发者退款批次id
	BizRefundBatchId string `protobuf:"bytes,1,opt,name=bizRefundBatchId,proto3" json:"bizRefundBatchId"`
	// 退款批次id
	RefundBatchId int64 `protobuf:"varint,2,opt,name=refundBatchId,proto3" json:"refundBatchId"`
	// 退款状态 1 退款中 2 退款成功 3 退款失败
	RefundStatus int64 `protobuf:"varint,3,opt,name=refundStatus,proto3" json:"refundStatus"`
	// 百度退款订单号
	OrderId int64 `protobuf:"varint,4,opt,name=orderId,proto3" json:"orderId"`
	// 退款用户id
	UserId               int64    `protobuf:"varint,5,opt,name=userId,proto3" json:"userId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaiduOrderRefundGetResponse) Reset()         { *m = BaiduOrderRefundGetResponse{} }
func (m *BaiduOrderRefundGetResponse) String() string { return proto.CompactTextString(m) }
func (*BaiduOrderRefundGetResponse) ProtoMessage()    {}
func (*BaiduOrderRefundGetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{12}
}

func (m *BaiduOrderRefundGetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaiduOrderRefundGetResponse.Unmarshal(m, b)
}
func (m *BaiduOrderRefundGetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaiduOrderRefundGetResponse.Marshal(b, m, deterministic)
}
func (m *BaiduOrderRefundGetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaiduOrderRefundGetResponse.Merge(m, src)
}
func (m *BaiduOrderRefundGetResponse) XXX_Size() int {
	return xxx_messageInfo_BaiduOrderRefundGetResponse.Size(m)
}
func (m *BaiduOrderRefundGetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaiduOrderRefundGetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaiduOrderRefundGetResponse proto.InternalMessageInfo

func (m *BaiduOrderRefundGetResponse) GetBizRefundBatchId() string {
	if m != nil {
		return m.BizRefundBatchId
	}
	return ""
}

func (m *BaiduOrderRefundGetResponse) GetRefundBatchId() int64 {
	if m != nil {
		return m.RefundBatchId
	}
	return 0
}

func (m *BaiduOrderRefundGetResponse) GetRefundStatus() int64 {
	if m != nil {
		return m.RefundStatus
	}
	return 0
}

func (m *BaiduOrderRefundGetResponse) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *BaiduOrderRefundGetResponse) GetUserId() int64 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type PayAsynNoticeRequest struct {
	//交易状态 S 成功(失败不通知)
	TransState string `protobuf:"bytes,1,opt,name=transState,proto3" json:"transState"`
	//商户订单号 仅能用大小写字母与数字，且在商户系统具有唯一性
	OrderId string `protobuf:"bytes,2,opt,name=orderId,proto3" json:"orderId"`
	//签名
	//支付流水号,电银生成的流水号
	TradeNo string `protobuf:"bytes,3,opt,name=tradeNo,proto3" json:"tradeNo"`
	//扣款通道返回的流水号
	ChannelNo string `protobuf:"bytes,4,opt,name=channelNo,proto3" json:"channelNo"`
	//支付时间
	PayTime              string   `protobuf:"bytes,5,opt,name=payTime,proto3" json:"payTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayAsynNoticeRequest) Reset()         { *m = PayAsynNoticeRequest{} }
func (m *PayAsynNoticeRequest) String() string { return proto.CompactTextString(m) }
func (*PayAsynNoticeRequest) ProtoMessage()    {}
func (*PayAsynNoticeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{13}
}

func (m *PayAsynNoticeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayAsynNoticeRequest.Unmarshal(m, b)
}
func (m *PayAsynNoticeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayAsynNoticeRequest.Marshal(b, m, deterministic)
}
func (m *PayAsynNoticeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayAsynNoticeRequest.Merge(m, src)
}
func (m *PayAsynNoticeRequest) XXX_Size() int {
	return xxx_messageInfo_PayAsynNoticeRequest.Size(m)
}
func (m *PayAsynNoticeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PayAsynNoticeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PayAsynNoticeRequest proto.InternalMessageInfo

func (m *PayAsynNoticeRequest) GetTransState() string {
	if m != nil {
		return m.TransState
	}
	return ""
}

func (m *PayAsynNoticeRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PayAsynNoticeRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *PayAsynNoticeRequest) GetChannelNo() string {
	if m != nil {
		return m.ChannelNo
	}
	return ""
}

func (m *PayAsynNoticeRequest) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

// @Desc			    支付订单状态查询请求
// <AUTHOR>
// @Date		 		2020-06-24
type PayQueryRequest struct {
	MerchantId           string   `protobuf:"bytes,1,opt,name=MerchantId,proto3" json:"MerchantId"`
	TradeNo              string   `protobuf:"bytes,2,opt,name=TradeNo,proto3" json:"TradeNo"`
	Sign                 string   `protobuf:"bytes,3,opt,name=Sign,proto3" json:"Sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayQueryRequest) Reset()         { *m = PayQueryRequest{} }
func (m *PayQueryRequest) String() string { return proto.CompactTextString(m) }
func (*PayQueryRequest) ProtoMessage()    {}
func (*PayQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{14}
}

func (m *PayQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayQueryRequest.Unmarshal(m, b)
}
func (m *PayQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayQueryRequest.Marshal(b, m, deterministic)
}
func (m *PayQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayQueryRequest.Merge(m, src)
}
func (m *PayQueryRequest) XXX_Size() int {
	return xxx_messageInfo_PayQueryRequest.Size(m)
}
func (m *PayQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PayQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PayQueryRequest proto.InternalMessageInfo

func (m *PayQueryRequest) GetMerchantId() string {
	if m != nil {
		return m.MerchantId
	}
	return ""
}

func (m *PayQueryRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *PayQueryRequest) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

// @Desc			    支付订单查询响应
// <AUTHOR>
// @Date		 		2020-06-24
type PayQueryResponse struct {
	OrderId              string             `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	OrderTime            string             `protobuf:"bytes,2,opt,name=order_time,json=orderTime,proto3" json:"order_time"`
	PayTradeNo           string             `protobuf:"bytes,3,opt,name=pay_trade_no,json=payTradeNo,proto3" json:"pay_trade_no"`
	TotalPrice           int32              `protobuf:"varint,4,opt,name=total_price,json=totalPrice,proto3" json:"total_price"`
	PayPrice             int32              `protobuf:"varint,5,opt,name=pay_price,json=payPrice,proto3" json:"pay_price"`
	Refund               int32              `protobuf:"varint,6,opt,name=refund,proto3" json:"refund"`
	PayStatus            int32              `protobuf:"varint,7,opt,name=pay_status,json=payStatus,proto3" json:"pay_status"`
	Status               string             `protobuf:"bytes,8,opt,name=status,proto3" json:"status"`
	RefundDetail         []*PayRefundDetail `protobuf:"bytes,9,rep,name=refund_detail,json=refundDetail,proto3" json:"refund_detail"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PayQueryResponse) Reset()         { *m = PayQueryResponse{} }
func (m *PayQueryResponse) String() string { return proto.CompactTextString(m) }
func (*PayQueryResponse) ProtoMessage()    {}
func (*PayQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{15}
}

func (m *PayQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayQueryResponse.Unmarshal(m, b)
}
func (m *PayQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayQueryResponse.Marshal(b, m, deterministic)
}
func (m *PayQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayQueryResponse.Merge(m, src)
}
func (m *PayQueryResponse) XXX_Size() int {
	return xxx_messageInfo_PayQueryResponse.Size(m)
}
func (m *PayQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PayQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PayQueryResponse proto.InternalMessageInfo

func (m *PayQueryResponse) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PayQueryResponse) GetOrderTime() string {
	if m != nil {
		return m.OrderTime
	}
	return ""
}

func (m *PayQueryResponse) GetPayTradeNo() string {
	if m != nil {
		return m.PayTradeNo
	}
	return ""
}

func (m *PayQueryResponse) GetTotalPrice() int32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *PayQueryResponse) GetPayPrice() int32 {
	if m != nil {
		return m.PayPrice
	}
	return 0
}

func (m *PayQueryResponse) GetRefund() int32 {
	if m != nil {
		return m.Refund
	}
	return 0
}

func (m *PayQueryResponse) GetPayStatus() int32 {
	if m != nil {
		return m.PayStatus
	}
	return 0
}

func (m *PayQueryResponse) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *PayQueryResponse) GetRefundDetail() []*PayRefundDetail {
	if m != nil {
		return m.RefundDetail
	}
	return nil
}

// @Desc			    订单退款明细
// <AUTHOR>
// @Date		 		2020-06-24
type PayRefundDetail struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	RefundTradeNo        string   `protobuf:"bytes,2,opt,name=refund_trade_no,json=refundTradeNo,proto3" json:"refund_trade_no"`
	RefundStatus         int32    `protobuf:"varint,3,opt,name=refund_status,json=refundStatus,proto3" json:"refund_status"`
	RefundAmount         int32    `protobuf:"varint,4,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	ExtendInfo           string   `protobuf:"bytes,5,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info"`
	BackParam            string   `protobuf:"bytes,6,opt,name=back_param,json=backParam,proto3" json:"back_param"`
	Status               string   `protobuf:"bytes,7,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayRefundDetail) Reset()         { *m = PayRefundDetail{} }
func (m *PayRefundDetail) String() string { return proto.CompactTextString(m) }
func (*PayRefundDetail) ProtoMessage()    {}
func (*PayRefundDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{16}
}

func (m *PayRefundDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRefundDetail.Unmarshal(m, b)
}
func (m *PayRefundDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRefundDetail.Marshal(b, m, deterministic)
}
func (m *PayRefundDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRefundDetail.Merge(m, src)
}
func (m *PayRefundDetail) XXX_Size() int {
	return xxx_messageInfo_PayRefundDetail.Size(m)
}
func (m *PayRefundDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRefundDetail.DiscardUnknown(m)
}

var xxx_messageInfo_PayRefundDetail proto.InternalMessageInfo

func (m *PayRefundDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PayRefundDetail) GetRefundTradeNo() string {
	if m != nil {
		return m.RefundTradeNo
	}
	return ""
}

func (m *PayRefundDetail) GetRefundStatus() int32 {
	if m != nil {
		return m.RefundStatus
	}
	return 0
}

func (m *PayRefundDetail) GetRefundAmount() int32 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *PayRefundDetail) GetExtendInfo() string {
	if m != nil {
		return m.ExtendInfo
	}
	return ""
}

func (m *PayRefundDetail) GetBackParam() string {
	if m != nil {
		return m.BackParam
	}
	return ""
}

func (m *PayRefundDetail) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type StdBindingRequest struct {
	//机构号（需要电银提供）
	OrgNumber string `protobuf:"bytes,1,opt,name=orgNumber,proto3" json:"orgNumber"`
	//*必传，机具编号(自定义)，新规规则，需要带前缀90000011，示例：90000011RP0045
	Tsn string `protobuf:"bytes,2,opt,name=tsn,proto3" json:"tsn"`
	//电银商户号（需要电银提供）
	DyMchNo string `protobuf:"bytes,3,opt,name=dyMchNo,proto3" json:"dyMchNo"`
	//机具来源
	//1 – 外部代理商(默认)
	//2 – 电银代理商
	//（当机具来源为“电银代理商”时, 外部终
	//端号、终端厂家、终端型号非必传）
	SnSource string `protobuf:"bytes,4,opt,name=snSource,proto3" json:"snSource"`
	//终端号（自定义） 以绑定结果返回的终端号为准
	DyTermNo string `protobuf:"bytes,5,opt,name=dyTermNo,proto3" json:"dyTermNo"`
	//机具厂商编号（需要电银提供）
	TermFactory string `protobuf:"bytes,6,opt,name=termFactory,proto3" json:"termFactory"`
	//机具型号编号（需要电银提供）
	TermModel string `protobuf:"bytes,7,opt,name=termModel,proto3" json:"termModel"`
	//*必传，终端名称（门店名称）
	TermName string `protobuf:"bytes,8,opt,name=termName,proto3" json:"termName"`
	//*必传，终端地址（门店地址）
	TermAddress string `protobuf:"bytes,9,opt,name=termAddress,proto3" json:"termAddress"`
	//*必传，门店财务编码
	ShopId string `protobuf:"bytes,10,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//终端类型，默认15
	TermType string `protobuf:"bytes,11,opt,name=termType,proto3" json:"termType"`
	//支付方式
	PayType              int32    `protobuf:"varint,12,opt,name=payType,proto3" json:"payType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StdBindingRequest) Reset()         { *m = StdBindingRequest{} }
func (m *StdBindingRequest) String() string { return proto.CompactTextString(m) }
func (*StdBindingRequest) ProtoMessage()    {}
func (*StdBindingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{17}
}

func (m *StdBindingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StdBindingRequest.Unmarshal(m, b)
}
func (m *StdBindingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StdBindingRequest.Marshal(b, m, deterministic)
}
func (m *StdBindingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StdBindingRequest.Merge(m, src)
}
func (m *StdBindingRequest) XXX_Size() int {
	return xxx_messageInfo_StdBindingRequest.Size(m)
}
func (m *StdBindingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StdBindingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StdBindingRequest proto.InternalMessageInfo

func (m *StdBindingRequest) GetOrgNumber() string {
	if m != nil {
		return m.OrgNumber
	}
	return ""
}

func (m *StdBindingRequest) GetTsn() string {
	if m != nil {
		return m.Tsn
	}
	return ""
}

func (m *StdBindingRequest) GetDyMchNo() string {
	if m != nil {
		return m.DyMchNo
	}
	return ""
}

func (m *StdBindingRequest) GetSnSource() string {
	if m != nil {
		return m.SnSource
	}
	return ""
}

func (m *StdBindingRequest) GetDyTermNo() string {
	if m != nil {
		return m.DyTermNo
	}
	return ""
}

func (m *StdBindingRequest) GetTermFactory() string {
	if m != nil {
		return m.TermFactory
	}
	return ""
}

func (m *StdBindingRequest) GetTermModel() string {
	if m != nil {
		return m.TermModel
	}
	return ""
}

func (m *StdBindingRequest) GetTermName() string {
	if m != nil {
		return m.TermName
	}
	return ""
}

func (m *StdBindingRequest) GetTermAddress() string {
	if m != nil {
		return m.TermAddress
	}
	return ""
}

func (m *StdBindingRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *StdBindingRequest) GetTermType() string {
	if m != nil {
		return m.TermType
	}
	return ""
}

func (m *StdBindingRequest) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

type StdBindingResponse struct {
	//返回码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//终端号
	DyTermNo             string   `protobuf:"bytes,2,opt,name=dyTermNo,proto3" json:"dyTermNo"`
	Message              string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StdBindingResponse) Reset()         { *m = StdBindingResponse{} }
func (m *StdBindingResponse) String() string { return proto.CompactTextString(m) }
func (*StdBindingResponse) ProtoMessage()    {}
func (*StdBindingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{18}
}

func (m *StdBindingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StdBindingResponse.Unmarshal(m, b)
}
func (m *StdBindingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StdBindingResponse.Marshal(b, m, deterministic)
}
func (m *StdBindingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StdBindingResponse.Merge(m, src)
}
func (m *StdBindingResponse) XXX_Size() int {
	return xxx_messageInfo_StdBindingResponse.Size(m)
}
func (m *StdBindingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StdBindingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StdBindingResponse proto.InternalMessageInfo

func (m *StdBindingResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StdBindingResponse) GetDyTermNo() string {
	if m != nil {
		return m.DyTermNo
	}
	return ""
}

func (m *StdBindingResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type StdUnBindingRequest struct {
	//机构号
	OrgNumber string `protobuf:"bytes,1,opt,name=orgNumber,proto3" json:"orgNumber"`
	//机具编号
	Tsn string `protobuf:"bytes,2,opt,name=tsn,proto3" json:"tsn"`
	//电银商户号
	DyMchNo string `protobuf:"bytes,3,opt,name=dyMchNo,proto3" json:"dyMchNo"`
	//支付方式
	PayType              int32    `protobuf:"varint,4,opt,name=payType,proto3" json:"payType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StdUnBindingRequest) Reset()         { *m = StdUnBindingRequest{} }
func (m *StdUnBindingRequest) String() string { return proto.CompactTextString(m) }
func (*StdUnBindingRequest) ProtoMessage()    {}
func (*StdUnBindingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{19}
}

func (m *StdUnBindingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StdUnBindingRequest.Unmarshal(m, b)
}
func (m *StdUnBindingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StdUnBindingRequest.Marshal(b, m, deterministic)
}
func (m *StdUnBindingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StdUnBindingRequest.Merge(m, src)
}
func (m *StdUnBindingRequest) XXX_Size() int {
	return xxx_messageInfo_StdUnBindingRequest.Size(m)
}
func (m *StdUnBindingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StdUnBindingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StdUnBindingRequest proto.InternalMessageInfo

func (m *StdUnBindingRequest) GetOrgNumber() string {
	if m != nil {
		return m.OrgNumber
	}
	return ""
}

func (m *StdUnBindingRequest) GetTsn() string {
	if m != nil {
		return m.Tsn
	}
	return ""
}

func (m *StdUnBindingRequest) GetDyMchNo() string {
	if m != nil {
		return m.DyMchNo
	}
	return ""
}

func (m *StdUnBindingRequest) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

type QueryStsRequest struct {
	//机构号
	OrgNumber string `protobuf:"bytes,1,opt,name=orgNumber,proto3" json:"orgNumber"`
	//机具编号
	Tsn                  string   `protobuf:"bytes,2,opt,name=tsn,proto3" json:"tsn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryStsRequest) Reset()         { *m = QueryStsRequest{} }
func (m *QueryStsRequest) String() string { return proto.CompactTextString(m) }
func (*QueryStsRequest) ProtoMessage()    {}
func (*QueryStsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{20}
}

func (m *QueryStsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryStsRequest.Unmarshal(m, b)
}
func (m *QueryStsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryStsRequest.Marshal(b, m, deterministic)
}
func (m *QueryStsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryStsRequest.Merge(m, src)
}
func (m *QueryStsRequest) XXX_Size() int {
	return xxx_messageInfo_QueryStsRequest.Size(m)
}
func (m *QueryStsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryStsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryStsRequest proto.InternalMessageInfo

func (m *QueryStsRequest) GetOrgNumber() string {
	if m != nil {
		return m.OrgNumber
	}
	return ""
}

func (m *QueryStsRequest) GetTsn() string {
	if m != nil {
		return m.Tsn
	}
	return ""
}

type QueryStsResponse struct {
	//返回码
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//电银商户号
	DyMchNo string `protobuf:"bytes,3,opt,name=dyMchNo,proto3" json:"dyMchNo"`
	//电银终端号
	DyTermNo string `protobuf:"bytes,4,opt,name=dyTermNo,proto3" json:"dyTermNo"`
	//电银状态0正常 1关闭
	Status               string   `protobuf:"bytes,5,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryStsResponse) Reset()         { *m = QueryStsResponse{} }
func (m *QueryStsResponse) String() string { return proto.CompactTextString(m) }
func (*QueryStsResponse) ProtoMessage()    {}
func (*QueryStsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{21}
}

func (m *QueryStsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryStsResponse.Unmarshal(m, b)
}
func (m *QueryStsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryStsResponse.Marshal(b, m, deterministic)
}
func (m *QueryStsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryStsResponse.Merge(m, src)
}
func (m *QueryStsResponse) XXX_Size() int {
	return xxx_messageInfo_QueryStsResponse.Size(m)
}
func (m *QueryStsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryStsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryStsResponse proto.InternalMessageInfo

func (m *QueryStsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryStsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryStsResponse) GetDyMchNo() string {
	if m != nil {
		return m.DyMchNo
	}
	return ""
}

func (m *QueryStsResponse) GetDyTermNo() string {
	if m != nil {
		return m.DyTermNo
	}
	return ""
}

func (m *QueryStsResponse) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type QueryPayStatusRequest struct {
	//支付中心流水号
	TradeNo string `protobuf:"bytes,1,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	//交易码，默认值：PF0 银联云闪付：QRY1 动码查询：PF420 云闪付动码：QRY4
	Trancde string `protobuf:"bytes,2,opt,name=trancde,proto3" json:"trancde"`
	//请求头信息
	HeadBase *ScanHeadBase `protobuf:"bytes,3,opt,name=head_base,json=headBase,proto3" json:"head_base"`
	// 应用id，1：阿闻，2：子龙，3：R1，4：互联网
	AppId                int32    `protobuf:"varint,4,opt,name=app_id,json=appId,proto3" json:"app_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPayStatusRequest) Reset()         { *m = QueryPayStatusRequest{} }
func (m *QueryPayStatusRequest) String() string { return proto.CompactTextString(m) }
func (*QueryPayStatusRequest) ProtoMessage()    {}
func (*QueryPayStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{22}
}

func (m *QueryPayStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPayStatusRequest.Unmarshal(m, b)
}
func (m *QueryPayStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPayStatusRequest.Marshal(b, m, deterministic)
}
func (m *QueryPayStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPayStatusRequest.Merge(m, src)
}
func (m *QueryPayStatusRequest) XXX_Size() int {
	return xxx_messageInfo_QueryPayStatusRequest.Size(m)
}
func (m *QueryPayStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPayStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPayStatusRequest proto.InternalMessageInfo

func (m *QueryPayStatusRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *QueryPayStatusRequest) GetTrancde() string {
	if m != nil {
		return m.Trancde
	}
	return ""
}

func (m *QueryPayStatusRequest) GetHeadBase() *ScanHeadBase {
	if m != nil {
		return m.HeadBase
	}
	return nil
}

func (m *QueryPayStatusRequest) GetAppId() int32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

type QueryPayStatusResponse struct {
	//返回码
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//商户订单号
	TradeNo string `protobuf:"bytes,3,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	//订单总金额 单位：分
	PayAmount string `protobuf:"bytes,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//支付结果 I：待支付 S：成功 R：正在执行 F：失败 T：成功有退款 C：已撤销 O：交易关闭
	PayResult string `protobuf:"bytes,5,opt,name=pay_result,json=payResult,proto3" json:"pay_result"`
	//电银流水号
	PayNo string `protobuf:"bytes,6,opt,name=pay_no,json=payNo,proto3" json:"pay_no"`
	//支付时间
	PayTime string `protobuf:"bytes,7,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//订单号
	OrderNo              string   `protobuf:"bytes,8,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPayStatusResponse) Reset()         { *m = QueryPayStatusResponse{} }
func (m *QueryPayStatusResponse) String() string { return proto.CompactTextString(m) }
func (*QueryPayStatusResponse) ProtoMessage()    {}
func (*QueryPayStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{23}
}

func (m *QueryPayStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPayStatusResponse.Unmarshal(m, b)
}
func (m *QueryPayStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPayStatusResponse.Marshal(b, m, deterministic)
}
func (m *QueryPayStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPayStatusResponse.Merge(m, src)
}
func (m *QueryPayStatusResponse) XXX_Size() int {
	return xxx_messageInfo_QueryPayStatusResponse.Size(m)
}
func (m *QueryPayStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPayStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPayStatusResponse proto.InternalMessageInfo

func (m *QueryPayStatusResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryPayStatusResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryPayStatusResponse) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *QueryPayStatusResponse) GetPayAmount() string {
	if m != nil {
		return m.PayAmount
	}
	return ""
}

func (m *QueryPayStatusResponse) GetPayResult() string {
	if m != nil {
		return m.PayResult
	}
	return ""
}

func (m *QueryPayStatusResponse) GetPayNo() string {
	if m != nil {
		return m.PayNo
	}
	return ""
}

func (m *QueryPayStatusResponse) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *QueryPayStatusResponse) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

type PayForB2CRequest struct {
	//订单号
	MerOrderNo string `protobuf:"bytes,1,opt,name=mer_order_no,json=merOrderNo,proto3" json:"mer_order_no"`
	//付款码
	BarCode string `protobuf:"bytes,2,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	//支付方式 1：微信 2：支付宝 3: 银联
	PayType int32 `protobuf:"varint,3,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	//实际支付金额
	PayAmount int32 `protobuf:"varint,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//不参与优惠金额
	UndiscountableAmount string `protobuf:"bytes,5,opt,name=undiscountable_amount,json=undiscountableAmount,proto3" json:"undiscountable_amount"`
	//订单名称 银联时否
	OrderName string `protobuf:"bytes,6,opt,name=order_name,json=orderName,proto3" json:"order_name"`
	//订单描述
	OrderDesc string `protobuf:"bytes,7,opt,name=order_desc,json=orderDesc,proto3" json:"order_desc"`
	//订单有效期单位 00-分 01-小时 02-日 03-月
	ValidUnit string `protobuf:"bytes,8,opt,name=validUnit,proto3" json:"validUnit"`
	//订单有效期单位 结合单位一起使用
	ValidNum string `protobuf:"bytes,9,opt,name=validNum,proto3" json:"validNum"`
	//扫码请求头信息
	HeadBase *ScanHeadBase `protobuf:"bytes,13,opt,name=head_base,json=headBase,proto3" json:"head_base"`
	//外部订单号
	OutOrderNo string `protobuf:"bytes,14,opt,name=out_order_no,json=outOrderNo,proto3" json:"out_order_no"`
	//订单总金额
	TotalAmount int32 `protobuf:"varint,15,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount"`
	//优惠
	Discount int32 `protobuf:"varint,16,opt,name=discount,proto3" json:"discount"`
	//后台回调地址（支付中心回调电商）
	NotifyUrl string `protobuf:"bytes,17,opt,name=NotifyUrl,proto3" json:"NotifyUrl"`
	//终端类型，默认15
	TermType string `protobuf:"bytes,18,opt,name=termType,proto3" json:"termType"`
	// ip
	ClientIp string `protobuf:"bytes,19,opt,name=client_ip,json=clientIp,proto3" json:"client_ip"`
	// 应用id，1：阿闻，2：子龙，3：R1，4：互联网
	AppId int32 `protobuf:"varint,20,opt,name=app_id,json=appId,proto3" json:"app_id"`
	// 扩展字段
	ExtendInfo           string   `protobuf:"bytes,21,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info"`
	Location             string   `protobuf:"bytes,22,opt,name=location,proto3" json:"location"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayForB2CRequest) Reset()         { *m = PayForB2CRequest{} }
func (m *PayForB2CRequest) String() string { return proto.CompactTextString(m) }
func (*PayForB2CRequest) ProtoMessage()    {}
func (*PayForB2CRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{24}
}

func (m *PayForB2CRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayForB2CRequest.Unmarshal(m, b)
}
func (m *PayForB2CRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayForB2CRequest.Marshal(b, m, deterministic)
}
func (m *PayForB2CRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayForB2CRequest.Merge(m, src)
}
func (m *PayForB2CRequest) XXX_Size() int {
	return xxx_messageInfo_PayForB2CRequest.Size(m)
}
func (m *PayForB2CRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PayForB2CRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PayForB2CRequest proto.InternalMessageInfo

func (m *PayForB2CRequest) GetMerOrderNo() string {
	if m != nil {
		return m.MerOrderNo
	}
	return ""
}

func (m *PayForB2CRequest) GetBarCode() string {
	if m != nil {
		return m.BarCode
	}
	return ""
}

func (m *PayForB2CRequest) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *PayForB2CRequest) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *PayForB2CRequest) GetUndiscountableAmount() string {
	if m != nil {
		return m.UndiscountableAmount
	}
	return ""
}

func (m *PayForB2CRequest) GetOrderName() string {
	if m != nil {
		return m.OrderName
	}
	return ""
}

func (m *PayForB2CRequest) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *PayForB2CRequest) GetValidUnit() string {
	if m != nil {
		return m.ValidUnit
	}
	return ""
}

func (m *PayForB2CRequest) GetValidNum() string {
	if m != nil {
		return m.ValidNum
	}
	return ""
}

func (m *PayForB2CRequest) GetHeadBase() *ScanHeadBase {
	if m != nil {
		return m.HeadBase
	}
	return nil
}

func (m *PayForB2CRequest) GetOutOrderNo() string {
	if m != nil {
		return m.OutOrderNo
	}
	return ""
}

func (m *PayForB2CRequest) GetTotalAmount() int32 {
	if m != nil {
		return m.TotalAmount
	}
	return 0
}

func (m *PayForB2CRequest) GetDiscount() int32 {
	if m != nil {
		return m.Discount
	}
	return 0
}

func (m *PayForB2CRequest) GetNotifyUrl() string {
	if m != nil {
		return m.NotifyUrl
	}
	return ""
}

func (m *PayForB2CRequest) GetTermType() string {
	if m != nil {
		return m.TermType
	}
	return ""
}

func (m *PayForB2CRequest) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *PayForB2CRequest) GetAppId() int32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *PayForB2CRequest) GetExtendInfo() string {
	if m != nil {
		return m.ExtendInfo
	}
	return ""
}

func (m *PayForB2CRequest) GetLocation() string {
	if m != nil {
		return m.Location
	}
	return ""
}

type YLGoodsDetail struct {
	//订单信息
	OrderInfo *YLOrderInfo `protobuf:"bytes,1,opt,name=orderInfo,proto3" json:"orderInfo"`
	//商品明细
	GoodsInfo            []*YLGoodsInfo `protobuf:"bytes,2,rep,name=goodsInfo,proto3" json:"goodsInfo"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *YLGoodsDetail) Reset()         { *m = YLGoodsDetail{} }
func (m *YLGoodsDetail) String() string { return proto.CompactTextString(m) }
func (*YLGoodsDetail) ProtoMessage()    {}
func (*YLGoodsDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{25}
}

func (m *YLGoodsDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YLGoodsDetail.Unmarshal(m, b)
}
func (m *YLGoodsDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YLGoodsDetail.Marshal(b, m, deterministic)
}
func (m *YLGoodsDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YLGoodsDetail.Merge(m, src)
}
func (m *YLGoodsDetail) XXX_Size() int {
	return xxx_messageInfo_YLGoodsDetail.Size(m)
}
func (m *YLGoodsDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_YLGoodsDetail.DiscardUnknown(m)
}

var xxx_messageInfo_YLGoodsDetail proto.InternalMessageInfo

func (m *YLGoodsDetail) GetOrderInfo() *YLOrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *YLGoodsDetail) GetGoodsInfo() []*YLGoodsInfo {
	if m != nil {
		return m.GoodsInfo
	}
	return nil
}

type YLOrderInfo struct {
	//订单标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	//订单描述
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description"`
	//优惠金额
	DctAmount string `protobuf:"bytes,3,opt,name=dctAmount,proto3" json:"dctAmount"`
	//附加内容
	AddnInfo             string   `protobuf:"bytes,4,opt,name=addnInfo,proto3" json:"addnInfo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YLOrderInfo) Reset()         { *m = YLOrderInfo{} }
func (m *YLOrderInfo) String() string { return proto.CompactTextString(m) }
func (*YLOrderInfo) ProtoMessage()    {}
func (*YLOrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{26}
}

func (m *YLOrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YLOrderInfo.Unmarshal(m, b)
}
func (m *YLOrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YLOrderInfo.Marshal(b, m, deterministic)
}
func (m *YLOrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YLOrderInfo.Merge(m, src)
}
func (m *YLOrderInfo) XXX_Size() int {
	return xxx_messageInfo_YLOrderInfo.Size(m)
}
func (m *YLOrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YLOrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YLOrderInfo proto.InternalMessageInfo

func (m *YLOrderInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *YLOrderInfo) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *YLOrderInfo) GetDctAmount() string {
	if m != nil {
		return m.DctAmount
	}
	return ""
}

func (m *YLOrderInfo) GetAddnInfo() string {
	if m != nil {
		return m.AddnInfo
	}
	return ""
}

type YLGoodsInfo struct {
	//商品编号
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	//商品名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//商品单价
	Price string `protobuf:"bytes,3,opt,name=price,proto3" json:"price"`
	//商品数量
	Quantity string `protobuf:"bytes,4,opt,name=quantity,proto3" json:"quantity"`
	//商品类目
	Category string `protobuf:"bytes,5,opt,name=category,proto3" json:"category"`
	//附加信息
	AddnInfo             string   `protobuf:"bytes,6,opt,name=addnInfo,proto3" json:"addnInfo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YLGoodsInfo) Reset()         { *m = YLGoodsInfo{} }
func (m *YLGoodsInfo) String() string { return proto.CompactTextString(m) }
func (*YLGoodsInfo) ProtoMessage()    {}
func (*YLGoodsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{27}
}

func (m *YLGoodsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YLGoodsInfo.Unmarshal(m, b)
}
func (m *YLGoodsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YLGoodsInfo.Marshal(b, m, deterministic)
}
func (m *YLGoodsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YLGoodsInfo.Merge(m, src)
}
func (m *YLGoodsInfo) XXX_Size() int {
	return xxx_messageInfo_YLGoodsInfo.Size(m)
}
func (m *YLGoodsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YLGoodsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YLGoodsInfo proto.InternalMessageInfo

func (m *YLGoodsInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *YLGoodsInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *YLGoodsInfo) GetPrice() string {
	if m != nil {
		return m.Price
	}
	return ""
}

func (m *YLGoodsInfo) GetQuantity() string {
	if m != nil {
		return m.Quantity
	}
	return ""
}

func (m *YLGoodsInfo) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *YLGoodsInfo) GetAddnInfo() string {
	if m != nil {
		return m.AddnInfo
	}
	return ""
}

type WXGoodsDetail struct {
	//订单原价
	CostPrice string `protobuf:"bytes,1,opt,name=cost_price,json=costPrice,proto3" json:"cost_price"`
	//商品小票ID
	ReceiptId string `protobuf:"bytes,2,opt,name=receipt_id,json=receiptId,proto3" json:"receipt_id"`
	//商品列表
	GoodsDetail          []*WXGoodsInfo `protobuf:"bytes,3,rep,name=goods_detail,json=goodsDetail,proto3" json:"goods_detail"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *WXGoodsDetail) Reset()         { *m = WXGoodsDetail{} }
func (m *WXGoodsDetail) String() string { return proto.CompactTextString(m) }
func (*WXGoodsDetail) ProtoMessage()    {}
func (*WXGoodsDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{28}
}

func (m *WXGoodsDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WXGoodsDetail.Unmarshal(m, b)
}
func (m *WXGoodsDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WXGoodsDetail.Marshal(b, m, deterministic)
}
func (m *WXGoodsDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WXGoodsDetail.Merge(m, src)
}
func (m *WXGoodsDetail) XXX_Size() int {
	return xxx_messageInfo_WXGoodsDetail.Size(m)
}
func (m *WXGoodsDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_WXGoodsDetail.DiscardUnknown(m)
}

var xxx_messageInfo_WXGoodsDetail proto.InternalMessageInfo

func (m *WXGoodsDetail) GetCostPrice() string {
	if m != nil {
		return m.CostPrice
	}
	return ""
}

func (m *WXGoodsDetail) GetReceiptId() string {
	if m != nil {
		return m.ReceiptId
	}
	return ""
}

func (m *WXGoodsDetail) GetGoodsDetail() []*WXGoodsInfo {
	if m != nil {
		return m.GoodsDetail
	}
	return nil
}

type WXGoodsInfo struct {
	//商品编号（由半角的大小写字母、数字、中划线、下划线中的一种或几种组成）
	GoodsId string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	//微信侧商品编码，微信支付定义的统一商品编号（没有可不传）
	WxpayGoodsId string `protobuf:"bytes,2,opt,name=wxpay_goods_id,json=wxpayGoodsId,proto3" json:"wxpay_goods_id"`
	//商品名称
	GoodsName string `protobuf:"bytes,3,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//商品数量
	Quantity string `protobuf:"bytes,4,opt,name=quantity,proto3" json:"quantity"`
	//商品单价（单位为：分。如果商户有优惠，需传输商户优惠后的 单价(例如：用户对一笔 100 元的订单使用了商场发的优惠券 100-50，则活动商品的单价应为原单价-50)）
	Price                string   `protobuf:"bytes,5,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WXGoodsInfo) Reset()         { *m = WXGoodsInfo{} }
func (m *WXGoodsInfo) String() string { return proto.CompactTextString(m) }
func (*WXGoodsInfo) ProtoMessage()    {}
func (*WXGoodsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{29}
}

func (m *WXGoodsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WXGoodsInfo.Unmarshal(m, b)
}
func (m *WXGoodsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WXGoodsInfo.Marshal(b, m, deterministic)
}
func (m *WXGoodsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WXGoodsInfo.Merge(m, src)
}
func (m *WXGoodsInfo) XXX_Size() int {
	return xxx_messageInfo_WXGoodsInfo.Size(m)
}
func (m *WXGoodsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WXGoodsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WXGoodsInfo proto.InternalMessageInfo

func (m *WXGoodsInfo) GetGoodsId() string {
	if m != nil {
		return m.GoodsId
	}
	return ""
}

func (m *WXGoodsInfo) GetWxpayGoodsId() string {
	if m != nil {
		return m.WxpayGoodsId
	}
	return ""
}

func (m *WXGoodsInfo) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *WXGoodsInfo) GetQuantity() string {
	if m != nil {
		return m.Quantity
	}
	return ""
}

func (m *WXGoodsInfo) GetPrice() string {
	if m != nil {
		return m.Price
	}
	return ""
}

type AliGoodsDetail struct {
	//商品编号（由半角的大小写字母、数字、中划线、下划线中的一种或几种组成）
	GoodsId string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	//微信侧商品编码，微信支付定义的统一商品编号（没有可不传）
	AlipayGoodsId string `protobuf:"bytes,2,opt,name=alipay_goods_id,json=alipayGoodsId,proto3" json:"alipay_goods_id"`
	//商品名称
	GoodsName string `protobuf:"bytes,3,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//商品数量
	Quantity string `protobuf:"bytes,4,opt,name=quantity,proto3" json:"quantity"`
	//商品单价（单位为：分。如果商户有优惠，需传输商户优惠后的 单价(例如：用户对一笔 100 元的订单使用了商场发的优惠券 100-50，则活动商品的单价应为原单价-50)）
	Price string `protobuf:"bytes,5,opt,name=price,proto3" json:"price"`
	//类目
	GoodsCategory string `protobuf:"bytes,6,opt,name=goods_category,json=goodsCategory,proto3" json:"goods_category"`
	//展示内容
	Body string `protobuf:"bytes,7,opt,name=body,proto3" json:"body"`
	//展示地址
	ShowUrl              string   `protobuf:"bytes,8,opt,name=show_url,json=showUrl,proto3" json:"show_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AliGoodsDetail) Reset()         { *m = AliGoodsDetail{} }
func (m *AliGoodsDetail) String() string { return proto.CompactTextString(m) }
func (*AliGoodsDetail) ProtoMessage()    {}
func (*AliGoodsDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{30}
}

func (m *AliGoodsDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AliGoodsDetail.Unmarshal(m, b)
}
func (m *AliGoodsDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AliGoodsDetail.Marshal(b, m, deterministic)
}
func (m *AliGoodsDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AliGoodsDetail.Merge(m, src)
}
func (m *AliGoodsDetail) XXX_Size() int {
	return xxx_messageInfo_AliGoodsDetail.Size(m)
}
func (m *AliGoodsDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_AliGoodsDetail.DiscardUnknown(m)
}

var xxx_messageInfo_AliGoodsDetail proto.InternalMessageInfo

func (m *AliGoodsDetail) GetGoodsId() string {
	if m != nil {
		return m.GoodsId
	}
	return ""
}

func (m *AliGoodsDetail) GetAlipayGoodsId() string {
	if m != nil {
		return m.AlipayGoodsId
	}
	return ""
}

func (m *AliGoodsDetail) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *AliGoodsDetail) GetQuantity() string {
	if m != nil {
		return m.Quantity
	}
	return ""
}

func (m *AliGoodsDetail) GetPrice() string {
	if m != nil {
		return m.Price
	}
	return ""
}

func (m *AliGoodsDetail) GetGoodsCategory() string {
	if m != nil {
		return m.GoodsCategory
	}
	return ""
}

func (m *AliGoodsDetail) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

func (m *AliGoodsDetail) GetShowUrl() string {
	if m != nil {
		return m.ShowUrl
	}
	return ""
}

type PayForB2CResponse struct {
	//返回码
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//商户订单号
	MerOrderNo string `protobuf:"bytes,3,opt,name=mer_order_no,json=merOrderNo,proto3" json:"mer_order_no"`
	//订单总金额 单位：分
	PayAmount string `protobuf:"bytes,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//支付结果 S：成功 R：正在执行 F：失败
	PayResult string `protobuf:"bytes,5,opt,name=pay_result,json=payResult,proto3" json:"pay_result"`
	//电银流水号
	PayNo string `protobuf:"bytes,6,opt,name=pay_no,json=payNo,proto3" json:"pay_no"`
	//支付时间
	PayTime string `protobuf:"bytes,7,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//支付中心流水号
	TradeNo              string   `protobuf:"bytes,8,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayForB2CResponse) Reset()         { *m = PayForB2CResponse{} }
func (m *PayForB2CResponse) String() string { return proto.CompactTextString(m) }
func (*PayForB2CResponse) ProtoMessage()    {}
func (*PayForB2CResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{31}
}

func (m *PayForB2CResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayForB2CResponse.Unmarshal(m, b)
}
func (m *PayForB2CResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayForB2CResponse.Marshal(b, m, deterministic)
}
func (m *PayForB2CResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayForB2CResponse.Merge(m, src)
}
func (m *PayForB2CResponse) XXX_Size() int {
	return xxx_messageInfo_PayForB2CResponse.Size(m)
}
func (m *PayForB2CResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PayForB2CResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PayForB2CResponse proto.InternalMessageInfo

func (m *PayForB2CResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PayForB2CResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PayForB2CResponse) GetMerOrderNo() string {
	if m != nil {
		return m.MerOrderNo
	}
	return ""
}

func (m *PayForB2CResponse) GetPayAmount() string {
	if m != nil {
		return m.PayAmount
	}
	return ""
}

func (m *PayForB2CResponse) GetPayResult() string {
	if m != nil {
		return m.PayResult
	}
	return ""
}

func (m *PayForB2CResponse) GetPayNo() string {
	if m != nil {
		return m.PayNo
	}
	return ""
}

func (m *PayForB2CResponse) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *PayForB2CResponse) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

type DYPayRequest struct {
	//订单号
	OrderNo string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	//交易码,P00：默认值，支持微信、支付宝、云闪付、电银支付等；CSU01：仅支持云闪付（老接口兼容性保留值）；
	Trancde string `protobuf:"bytes,2,opt,name=trancde,proto3" json:"trancde"`
	//付款码,用户用银联支付宝、微信生成的付款码
	BarCode string `protobuf:"bytes,3,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	//实付金额,单位：分
	PayAmount int32 `protobuf:"varint,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//订单名称
	OrderName string `protobuf:"bytes,5,opt,name=order_name,json=orderName,proto3" json:"order_name"`
	//支付方式 1：微信 2：支付宝 3: 银联
	PayType int32 `protobuf:"varint,6,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	//总金额,单位：分
	PayTotal int32 `protobuf:"varint,7,opt,name=pay_total,json=payTotal,proto3" json:"pay_total"`
	//优惠金额,单位：分
	Discount int32 `protobuf:"varint,8,opt,name=discount,proto3" json:"discount"`
	//回调地址
	NotifyUrl string `protobuf:"bytes,9,opt,name=notify_url,json=notifyUrl,proto3" json:"notify_url"`
	//传机具编号（tsn）
	TrmSn string `protobuf:"bytes,10,opt,name=trm_sn,json=trmSn,proto3" json:"trm_sn"`
	//终端号，传标准终端绑定接口返回的dyTermNo
	TrmId                string   `protobuf:"bytes,11,opt,name=trm_id,json=trmId,proto3" json:"trm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DYPayRequest) Reset()         { *m = DYPayRequest{} }
func (m *DYPayRequest) String() string { return proto.CompactTextString(m) }
func (*DYPayRequest) ProtoMessage()    {}
func (*DYPayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{32}
}

func (m *DYPayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DYPayRequest.Unmarshal(m, b)
}
func (m *DYPayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DYPayRequest.Marshal(b, m, deterministic)
}
func (m *DYPayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DYPayRequest.Merge(m, src)
}
func (m *DYPayRequest) XXX_Size() int {
	return xxx_messageInfo_DYPayRequest.Size(m)
}
func (m *DYPayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DYPayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DYPayRequest proto.InternalMessageInfo

func (m *DYPayRequest) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *DYPayRequest) GetTrancde() string {
	if m != nil {
		return m.Trancde
	}
	return ""
}

func (m *DYPayRequest) GetBarCode() string {
	if m != nil {
		return m.BarCode
	}
	return ""
}

func (m *DYPayRequest) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *DYPayRequest) GetOrderName() string {
	if m != nil {
		return m.OrderName
	}
	return ""
}

func (m *DYPayRequest) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *DYPayRequest) GetPayTotal() int32 {
	if m != nil {
		return m.PayTotal
	}
	return 0
}

func (m *DYPayRequest) GetDiscount() int32 {
	if m != nil {
		return m.Discount
	}
	return 0
}

func (m *DYPayRequest) GetNotifyUrl() string {
	if m != nil {
		return m.NotifyUrl
	}
	return ""
}

func (m *DYPayRequest) GetTrmSn() string {
	if m != nil {
		return m.TrmSn
	}
	return ""
}

func (m *DYPayRequest) GetTrmId() string {
	if m != nil {
		return m.TrmId
	}
	return ""
}

type DYPayResponse struct {
	//返回码
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//商户订单号
	OrderNo string `protobuf:"bytes,3,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	//订单总金额 单位：分
	PayAmount string `protobuf:"bytes,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//支付结果 S：成功 R：正在执行 F：失败
	PayResult string `protobuf:"bytes,5,opt,name=pay_result,json=payResult,proto3" json:"pay_result"`
	//电银流水号
	PayNo string `protobuf:"bytes,6,opt,name=pay_no,json=payNo,proto3" json:"pay_no"`
	//支付时间
	PayTime string `protobuf:"bytes,7,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//支付中心订单号
	TradeNo              string   `protobuf:"bytes,8,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DYPayResponse) Reset()         { *m = DYPayResponse{} }
func (m *DYPayResponse) String() string { return proto.CompactTextString(m) }
func (*DYPayResponse) ProtoMessage()    {}
func (*DYPayResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{33}
}

func (m *DYPayResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DYPayResponse.Unmarshal(m, b)
}
func (m *DYPayResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DYPayResponse.Marshal(b, m, deterministic)
}
func (m *DYPayResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DYPayResponse.Merge(m, src)
}
func (m *DYPayResponse) XXX_Size() int {
	return xxx_messageInfo_DYPayResponse.Size(m)
}
func (m *DYPayResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DYPayResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DYPayResponse proto.InternalMessageInfo

func (m *DYPayResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DYPayResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DYPayResponse) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *DYPayResponse) GetPayAmount() string {
	if m != nil {
		return m.PayAmount
	}
	return ""
}

func (m *DYPayResponse) GetPayResult() string {
	if m != nil {
		return m.PayResult
	}
	return ""
}

func (m *DYPayResponse) GetPayNo() string {
	if m != nil {
		return m.PayNo
	}
	return ""
}

func (m *DYPayResponse) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *DYPayResponse) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

type QueryPwdRequest struct {
	Mobile               string   `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	Type                 int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	OrderSn              string   `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPwdRequest) Reset()         { *m = QueryPwdRequest{} }
func (m *QueryPwdRequest) String() string { return proto.CompactTextString(m) }
func (*QueryPwdRequest) ProtoMessage()    {}
func (*QueryPwdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{34}
}

func (m *QueryPwdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPwdRequest.Unmarshal(m, b)
}
func (m *QueryPwdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPwdRequest.Marshal(b, m, deterministic)
}
func (m *QueryPwdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPwdRequest.Merge(m, src)
}
func (m *QueryPwdRequest) XXX_Size() int {
	return xxx_messageInfo_QueryPwdRequest.Size(m)
}
func (m *QueryPwdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPwdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPwdRequest proto.InternalMessageInfo

func (m *QueryPwdRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *QueryPwdRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *QueryPwdRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type QueryPwdResponse struct {
	//返回码
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//1设置过  0未设置
	IsPwd                int32    `protobuf:"varint,3,opt,name=is_pwd,json=isPwd,proto3" json:"is_pwd"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPwdResponse) Reset()         { *m = QueryPwdResponse{} }
func (m *QueryPwdResponse) String() string { return proto.CompactTextString(m) }
func (*QueryPwdResponse) ProtoMessage()    {}
func (*QueryPwdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{35}
}

func (m *QueryPwdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPwdResponse.Unmarshal(m, b)
}
func (m *QueryPwdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPwdResponse.Marshal(b, m, deterministic)
}
func (m *QueryPwdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPwdResponse.Merge(m, src)
}
func (m *QueryPwdResponse) XXX_Size() int {
	return xxx_messageInfo_QueryPwdResponse.Size(m)
}
func (m *QueryPwdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPwdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPwdResponse proto.InternalMessageInfo

func (m *QueryPwdResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryPwdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryPwdResponse) GetIsPwd() int32 {
	if m != nil {
		return m.IsPwd
	}
	return 0
}

type SetPwdRequest struct {
	//手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//密码（MD5）
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password"`
	//scrm_id
	ScrmId               string   `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPwdRequest) Reset()         { *m = SetPwdRequest{} }
func (m *SetPwdRequest) String() string { return proto.CompactTextString(m) }
func (*SetPwdRequest) ProtoMessage()    {}
func (*SetPwdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{36}
}

func (m *SetPwdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPwdRequest.Unmarshal(m, b)
}
func (m *SetPwdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPwdRequest.Marshal(b, m, deterministic)
}
func (m *SetPwdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPwdRequest.Merge(m, src)
}
func (m *SetPwdRequest) XXX_Size() int {
	return xxx_messageInfo_SetPwdRequest.Size(m)
}
func (m *SetPwdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPwdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetPwdRequest proto.InternalMessageInfo

func (m *SetPwdRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *SetPwdRequest) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *SetPwdRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type UpdateByMobileRequest struct {
	//手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//密码（MD5）
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password"`
	//短信验证码
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	//scrm_id
	ScrmId               string   `protobuf:"bytes,4,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateByMobileRequest) Reset()         { *m = UpdateByMobileRequest{} }
func (m *UpdateByMobileRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateByMobileRequest) ProtoMessage()    {}
func (*UpdateByMobileRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{37}
}

func (m *UpdateByMobileRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateByMobileRequest.Unmarshal(m, b)
}
func (m *UpdateByMobileRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateByMobileRequest.Marshal(b, m, deterministic)
}
func (m *UpdateByMobileRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateByMobileRequest.Merge(m, src)
}
func (m *UpdateByMobileRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateByMobileRequest.Size(m)
}
func (m *UpdateByMobileRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateByMobileRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateByMobileRequest proto.InternalMessageInfo

func (m *UpdateByMobileRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *UpdateByMobileRequest) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *UpdateByMobileRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *UpdateByMobileRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type UpdateByPwdRequest struct {
	//手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//原密码（MD5）
	OldPassword string `protobuf:"bytes,2,opt,name=old_password,json=oldPassword,proto3" json:"old_password"`
	//新密码（MD5）
	NewPassword string `protobuf:"bytes,3,opt,name=new_password,json=newPassword,proto3" json:"new_password"`
	//scrm_id
	ScrmId               string   `protobuf:"bytes,4,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateByPwdRequest) Reset()         { *m = UpdateByPwdRequest{} }
func (m *UpdateByPwdRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateByPwdRequest) ProtoMessage()    {}
func (*UpdateByPwdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{38}
}

func (m *UpdateByPwdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateByPwdRequest.Unmarshal(m, b)
}
func (m *UpdateByPwdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateByPwdRequest.Marshal(b, m, deterministic)
}
func (m *UpdateByPwdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateByPwdRequest.Merge(m, src)
}
func (m *UpdateByPwdRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateByPwdRequest.Size(m)
}
func (m *UpdateByPwdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateByPwdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateByPwdRequest proto.InternalMessageInfo

func (m *UpdateByPwdRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *UpdateByPwdRequest) GetOldPassword() string {
	if m != nil {
		return m.OldPassword
	}
	return ""
}

func (m *UpdateByPwdRequest) GetNewPassword() string {
	if m != nil {
		return m.NewPassword
	}
	return ""
}

func (m *UpdateByPwdRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type PayListResponse struct {
	Code                 int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*PayMethod `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PayListResponse) Reset()         { *m = PayListResponse{} }
func (m *PayListResponse) String() string { return proto.CompactTextString(m) }
func (*PayListResponse) ProtoMessage()    {}
func (*PayListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{39}
}

func (m *PayListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayListResponse.Unmarshal(m, b)
}
func (m *PayListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayListResponse.Marshal(b, m, deterministic)
}
func (m *PayListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayListResponse.Merge(m, src)
}
func (m *PayListResponse) XXX_Size() int {
	return xxx_messageInfo_PayListResponse.Size(m)
}
func (m *PayListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PayListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PayListResponse proto.InternalMessageInfo

func (m *PayListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PayListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PayListResponse) GetData() []*PayMethod {
	if m != nil {
		return m.Data
	}
	return nil
}

type PayMethod struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 支付方式
	PayMethod string `protobuf:"bytes,2,opt,name=pay_method,json=payMethod,proto3" json:"pay_method"`
	//是否开启
	IsOpen               int32    `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayMethod) Reset()         { *m = PayMethod{} }
func (m *PayMethod) String() string { return proto.CompactTextString(m) }
func (*PayMethod) ProtoMessage()    {}
func (*PayMethod) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{40}
}

func (m *PayMethod) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayMethod.Unmarshal(m, b)
}
func (m *PayMethod) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayMethod.Marshal(b, m, deterministic)
}
func (m *PayMethod) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayMethod.Merge(m, src)
}
func (m *PayMethod) XXX_Size() int {
	return xxx_messageInfo_PayMethod.Size(m)
}
func (m *PayMethod) XXX_DiscardUnknown() {
	xxx_messageInfo_PayMethod.DiscardUnknown(m)
}

var xxx_messageInfo_PayMethod proto.InternalMessageInfo

func (m *PayMethod) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PayMethod) GetPayMethod() string {
	if m != nil {
		return m.PayMethod
	}
	return ""
}

func (m *PayMethod) GetIsOpen() int32 {
	if m != nil {
		return m.IsOpen
	}
	return 0
}

type PayConfigResponse struct {
	Code                 int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*PayConfig `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PayConfigResponse) Reset()         { *m = PayConfigResponse{} }
func (m *PayConfigResponse) String() string { return proto.CompactTextString(m) }
func (*PayConfigResponse) ProtoMessage()    {}
func (*PayConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{41}
}

func (m *PayConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayConfigResponse.Unmarshal(m, b)
}
func (m *PayConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayConfigResponse.Marshal(b, m, deterministic)
}
func (m *PayConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayConfigResponse.Merge(m, src)
}
func (m *PayConfigResponse) XXX_Size() int {
	return xxx_messageInfo_PayConfigResponse.Size(m)
}
func (m *PayConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PayConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PayConfigResponse proto.InternalMessageInfo

func (m *PayConfigResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PayConfigResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PayConfigResponse) GetData() []*PayConfig {
	if m != nil {
		return m.Data
	}
	return nil
}

type PayConfig struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 支付方式
	PayMethod string `protobuf:"bytes,2,opt,name=pay_method,json=payMethod,proto3" json:"pay_method"`
	//是否开启
	IsOpen int32 `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	//分组
	GroupId              int32    `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayConfig) Reset()         { *m = PayConfig{} }
func (m *PayConfig) String() string { return proto.CompactTextString(m) }
func (*PayConfig) ProtoMessage()    {}
func (*PayConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{42}
}

func (m *PayConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayConfig.Unmarshal(m, b)
}
func (m *PayConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayConfig.Marshal(b, m, deterministic)
}
func (m *PayConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayConfig.Merge(m, src)
}
func (m *PayConfig) XXX_Size() int {
	return xxx_messageInfo_PayConfig.Size(m)
}
func (m *PayConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PayConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PayConfig proto.InternalMessageInfo

func (m *PayConfig) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PayConfig) GetPayMethod() string {
	if m != nil {
		return m.PayMethod
	}
	return ""
}

func (m *PayConfig) GetIsOpen() int32 {
	if m != nil {
		return m.IsOpen
	}
	return 0
}

func (m *PayConfig) GetGroupId() int32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type CheckCodeRequest struct {
	// 手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	// 验证码
	Code                 string   `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCodeRequest) Reset()         { *m = CheckCodeRequest{} }
func (m *CheckCodeRequest) String() string { return proto.CompactTextString(m) }
func (*CheckCodeRequest) ProtoMessage()    {}
func (*CheckCodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{43}
}

func (m *CheckCodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCodeRequest.Unmarshal(m, b)
}
func (m *CheckCodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCodeRequest.Marshal(b, m, deterministic)
}
func (m *CheckCodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCodeRequest.Merge(m, src)
}
func (m *CheckCodeRequest) XXX_Size() int {
	return xxx_messageInfo_CheckCodeRequest.Size(m)
}
func (m *CheckCodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCodeRequest proto.InternalMessageInfo

func (m *CheckCodeRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CheckCodeRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

type CheckCodeResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 验证码是否正确 1是 0否
	IsTrue               int32    `protobuf:"varint,3,opt,name=is_true,json=isTrue,proto3" json:"is_true"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCodeResponse) Reset()         { *m = CheckCodeResponse{} }
func (m *CheckCodeResponse) String() string { return proto.CompactTextString(m) }
func (*CheckCodeResponse) ProtoMessage()    {}
func (*CheckCodeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{44}
}

func (m *CheckCodeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCodeResponse.Unmarshal(m, b)
}
func (m *CheckCodeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCodeResponse.Marshal(b, m, deterministic)
}
func (m *CheckCodeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCodeResponse.Merge(m, src)
}
func (m *CheckCodeResponse) XXX_Size() int {
	return xxx_messageInfo_CheckCodeResponse.Size(m)
}
func (m *CheckCodeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCodeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCodeResponse proto.InternalMessageInfo

func (m *CheckCodeResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CheckCodeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CheckCodeResponse) GetIsTrue() int32 {
	if m != nil {
		return m.IsTrue
	}
	return 0
}

type GetCardsBalanceReq struct {
	ScrmId               string   `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCardsBalanceReq) Reset()         { *m = GetCardsBalanceReq{} }
func (m *GetCardsBalanceReq) String() string { return proto.CompactTextString(m) }
func (*GetCardsBalanceReq) ProtoMessage()    {}
func (*GetCardsBalanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{45}
}

func (m *GetCardsBalanceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCardsBalanceReq.Unmarshal(m, b)
}
func (m *GetCardsBalanceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCardsBalanceReq.Marshal(b, m, deterministic)
}
func (m *GetCardsBalanceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCardsBalanceReq.Merge(m, src)
}
func (m *GetCardsBalanceReq) XXX_Size() int {
	return xxx_messageInfo_GetCardsBalanceReq.Size(m)
}
func (m *GetCardsBalanceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCardsBalanceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCardsBalanceReq proto.InternalMessageInfo

func (m *GetCardsBalanceReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type GetCardsBalanceRes struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//
	Data                 *CardsBalanceData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetCardsBalanceRes) Reset()         { *m = GetCardsBalanceRes{} }
func (m *GetCardsBalanceRes) String() string { return proto.CompactTextString(m) }
func (*GetCardsBalanceRes) ProtoMessage()    {}
func (*GetCardsBalanceRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{46}
}

func (m *GetCardsBalanceRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCardsBalanceRes.Unmarshal(m, b)
}
func (m *GetCardsBalanceRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCardsBalanceRes.Marshal(b, m, deterministic)
}
func (m *GetCardsBalanceRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCardsBalanceRes.Merge(m, src)
}
func (m *GetCardsBalanceRes) XXX_Size() int {
	return xxx_messageInfo_GetCardsBalanceRes.Size(m)
}
func (m *GetCardsBalanceRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCardsBalanceRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetCardsBalanceRes proto.InternalMessageInfo

func (m *GetCardsBalanceRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCardsBalanceRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetCardsBalanceRes) GetData() *CardsBalanceData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CardsBalanceData struct {
	//余额（本金+赠送金额)
	Balance string `protobuf:"bytes,1,opt,name=balance,proto3" json:"balance"`
	//可用余额（本金-（本日充值金额0.3））
	AvailableBalance string `protobuf:"bytes,2,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance"`
	//赠送金额
	GiftAmount           string   `protobuf:"bytes,3,opt,name=gift_amount,json=giftAmount,proto3" json:"gift_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardsBalanceData) Reset()         { *m = CardsBalanceData{} }
func (m *CardsBalanceData) String() string { return proto.CompactTextString(m) }
func (*CardsBalanceData) ProtoMessage()    {}
func (*CardsBalanceData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{47}
}

func (m *CardsBalanceData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardsBalanceData.Unmarshal(m, b)
}
func (m *CardsBalanceData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardsBalanceData.Marshal(b, m, deterministic)
}
func (m *CardsBalanceData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardsBalanceData.Merge(m, src)
}
func (m *CardsBalanceData) XXX_Size() int {
	return xxx_messageInfo_CardsBalanceData.Size(m)
}
func (m *CardsBalanceData) XXX_DiscardUnknown() {
	xxx_messageInfo_CardsBalanceData.DiscardUnknown(m)
}

var xxx_messageInfo_CardsBalanceData proto.InternalMessageInfo

func (m *CardsBalanceData) GetBalance() string {
	if m != nil {
		return m.Balance
	}
	return ""
}

func (m *CardsBalanceData) GetAvailableBalance() string {
	if m != nil {
		return m.AvailableBalance
	}
	return ""
}

func (m *CardsBalanceData) GetGiftAmount() string {
	if m != nil {
		return m.GiftAmount
	}
	return ""
}

type GetCardsReq struct {
	ScrmId string `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	//财务编码
	FinanceCode string `protobuf:"bytes,2,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//订单支付金额（单位分）
	PayMoney int32 `protobuf:"varint,3,opt,name=pay_money,json=payMoney,proto3" json:"pay_money"`
	//渠道Id 阿闻：1，阿闻电商：5
	ChannelId            int32    `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCardsReq) Reset()         { *m = GetCardsReq{} }
func (m *GetCardsReq) String() string { return proto.CompactTextString(m) }
func (*GetCardsReq) ProtoMessage()    {}
func (*GetCardsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{48}
}

func (m *GetCardsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCardsReq.Unmarshal(m, b)
}
func (m *GetCardsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCardsReq.Marshal(b, m, deterministic)
}
func (m *GetCardsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCardsReq.Merge(m, src)
}
func (m *GetCardsReq) XXX_Size() int {
	return xxx_messageInfo_GetCardsReq.Size(m)
}
func (m *GetCardsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCardsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCardsReq proto.InternalMessageInfo

func (m *GetCardsReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *GetCardsReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GetCardsReq) GetPayMoney() int32 {
	if m != nil {
		return m.PayMoney
	}
	return 0
}

func (m *GetCardsReq) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetCardsRes struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//
	Data                 *GetCardsData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCardsRes) Reset()         { *m = GetCardsRes{} }
func (m *GetCardsRes) String() string { return proto.CompactTextString(m) }
func (*GetCardsRes) ProtoMessage()    {}
func (*GetCardsRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{49}
}

func (m *GetCardsRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCardsRes.Unmarshal(m, b)
}
func (m *GetCardsRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCardsRes.Marshal(b, m, deterministic)
}
func (m *GetCardsRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCardsRes.Merge(m, src)
}
func (m *GetCardsRes) XXX_Size() int {
	return xxx_messageInfo_GetCardsRes.Size(m)
}
func (m *GetCardsRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCardsRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetCardsRes proto.InternalMessageInfo

func (m *GetCardsRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCardsRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetCardsRes) GetData() *GetCardsData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetCardsData struct {
	//是否可以支付 1:是,0:否
	IsPay int32 `protobuf:"varint,1,opt,name=is_pay,json=isPay,proto3" json:"is_pay"`
	//财务编码
	Cards                []*CardData `protobuf:"bytes,2,rep,name=cards,proto3" json:"cards"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetCardsData) Reset()         { *m = GetCardsData{} }
func (m *GetCardsData) String() string { return proto.CompactTextString(m) }
func (*GetCardsData) ProtoMessage()    {}
func (*GetCardsData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{50}
}

func (m *GetCardsData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCardsData.Unmarshal(m, b)
}
func (m *GetCardsData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCardsData.Marshal(b, m, deterministic)
}
func (m *GetCardsData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCardsData.Merge(m, src)
}
func (m *GetCardsData) XXX_Size() int {
	return xxx_messageInfo_GetCardsData.Size(m)
}
func (m *GetCardsData) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCardsData.DiscardUnknown(m)
}

var xxx_messageInfo_GetCardsData proto.InternalMessageInfo

func (m *GetCardsData) GetIsPay() int32 {
	if m != nil {
		return m.IsPay
	}
	return 0
}

func (m *GetCardsData) GetCards() []*CardData {
	if m != nil {
		return m.Cards
	}
	return nil
}

type CardData struct {
	CardId int32 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	//卡号
	CardNumber string `protobuf:"bytes,2,opt,name=card_number,json=cardNumber,proto3" json:"card_number"`
	//卡名称
	CardName string `protobuf:"bytes,3,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	//创建日期（根据有效期判断卡是否过期）
	CreateTime string `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//有效期（根据有效期判断卡是否过期）
	ExpiryDate string `protobuf:"bytes,5,opt,name=expiry_date,json=expiryDate,proto3" json:"expiry_date"`
	//本金余额(单位元)
	CorpusBalance float32 `protobuf:"fixed32,6,opt,name=corpus_balance,json=corpusBalance,proto3" json:"corpus_balance"`
	//赠送余额(单位元)
	PresentBalance float32 `protobuf:"fixed32,7,opt,name=present_balance,json=presentBalance,proto3" json:"present_balance"`
	//是否连锁 1：是 0：否
	Chained int32 `protobuf:"varint,8,opt,name=chained,proto3" json:"chained"`
	//开卡门店(财务编码)
	OrgId string `protobuf:"bytes,9,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	//开卡门店名
	OrgName string `protobuf:"bytes,10,opt,name=org_name,json=orgName,proto3" json:"org_name"`
	//是否可用 1：是 0：否
	IsAvailable int32 `protobuf:"varint,11,opt,name=is_available,json=isAvailable,proto3" json:"is_available"`
	//是否选中 1：是 0：否
	IsChecked int32 `protobuf:"varint,12,opt,name=is_checked,json=isChecked,proto3" json:"is_checked"`
	//选中的卡支付金额(单位分)
	PayAmount            int32    `protobuf:"varint,13,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardData) Reset()         { *m = CardData{} }
func (m *CardData) String() string { return proto.CompactTextString(m) }
func (*CardData) ProtoMessage()    {}
func (*CardData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{51}
}

func (m *CardData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardData.Unmarshal(m, b)
}
func (m *CardData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardData.Marshal(b, m, deterministic)
}
func (m *CardData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardData.Merge(m, src)
}
func (m *CardData) XXX_Size() int {
	return xxx_messageInfo_CardData.Size(m)
}
func (m *CardData) XXX_DiscardUnknown() {
	xxx_messageInfo_CardData.DiscardUnknown(m)
}

var xxx_messageInfo_CardData proto.InternalMessageInfo

func (m *CardData) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *CardData) GetCardNumber() string {
	if m != nil {
		return m.CardNumber
	}
	return ""
}

func (m *CardData) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *CardData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *CardData) GetExpiryDate() string {
	if m != nil {
		return m.ExpiryDate
	}
	return ""
}

func (m *CardData) GetCorpusBalance() float32 {
	if m != nil {
		return m.CorpusBalance
	}
	return 0
}

func (m *CardData) GetPresentBalance() float32 {
	if m != nil {
		return m.PresentBalance
	}
	return 0
}

func (m *CardData) GetChained() int32 {
	if m != nil {
		return m.Chained
	}
	return 0
}

func (m *CardData) GetOrgId() string {
	if m != nil {
		return m.OrgId
	}
	return ""
}

func (m *CardData) GetOrgName() string {
	if m != nil {
		return m.OrgName
	}
	return ""
}

func (m *CardData) GetIsAvailable() int32 {
	if m != nil {
		return m.IsAvailable
	}
	return 0
}

func (m *CardData) GetIsChecked() int32 {
	if m != nil {
		return m.IsChecked
	}
	return 0
}

func (m *CardData) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

type CardPayReq struct {
	//财务编码
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//交易流水号（调用支付中心返回）
	OrderSn string `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//scrm_id
	ScrmId string `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	//订单来源:01商城，02阿闻到家，03挂号
	OrderSource string `protobuf:"bytes,4,opt,name=order_source,json=orderSource,proto3" json:"order_source"`
	//交易类型
	OrderType string `protobuf:"bytes,5,opt,name=order_type,json=orderType,proto3" json:"order_type"`
	//支付密码
	PayPwd string `protobuf:"bytes,6,opt,name=pay_pwd,json=payPwd,proto3" json:"pay_pwd"`
	//赠送金额(单元分)
	PresentMoney int32 `protobuf:"varint,7,opt,name=present_money,json=presentMoney,proto3" json:"present_money"`
	//是否连锁 1：是 0：否
	Cards []*CardsPayDetails `protobuf:"bytes,8,rep,name=cards,proto3" json:"cards"`
	//支付方式 0支付密码支付  1短信验证支付
	PayType int32 `protobuf:"varint,9,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	//手机号
	Mobile string `protobuf:"bytes,10,opt,name=mobile,proto3" json:"mobile"`
	//短信验证码
	SmsCode              string   `protobuf:"bytes,11,opt,name=sms_code,json=smsCode,proto3" json:"sms_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardPayReq) Reset()         { *m = CardPayReq{} }
func (m *CardPayReq) String() string { return proto.CompactTextString(m) }
func (*CardPayReq) ProtoMessage()    {}
func (*CardPayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{52}
}

func (m *CardPayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardPayReq.Unmarshal(m, b)
}
func (m *CardPayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardPayReq.Marshal(b, m, deterministic)
}
func (m *CardPayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardPayReq.Merge(m, src)
}
func (m *CardPayReq) XXX_Size() int {
	return xxx_messageInfo_CardPayReq.Size(m)
}
func (m *CardPayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardPayReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardPayReq proto.InternalMessageInfo

func (m *CardPayReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *CardPayReq) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CardPayReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *CardPayReq) GetOrderSource() string {
	if m != nil {
		return m.OrderSource
	}
	return ""
}

func (m *CardPayReq) GetOrderType() string {
	if m != nil {
		return m.OrderType
	}
	return ""
}

func (m *CardPayReq) GetPayPwd() string {
	if m != nil {
		return m.PayPwd
	}
	return ""
}

func (m *CardPayReq) GetPresentMoney() int32 {
	if m != nil {
		return m.PresentMoney
	}
	return 0
}

func (m *CardPayReq) GetCards() []*CardsPayDetails {
	if m != nil {
		return m.Cards
	}
	return nil
}

func (m *CardPayReq) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *CardPayReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CardPayReq) GetSmsCode() string {
	if m != nil {
		return m.SmsCode
	}
	return ""
}

type CardsPayDetails struct {
	CardId int32 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	//支付金额(单位元)
	PayMoney             int32    `protobuf:"varint,2,opt,name=pay_money,json=payMoney,proto3" json:"pay_money"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardsPayDetails) Reset()         { *m = CardsPayDetails{} }
func (m *CardsPayDetails) String() string { return proto.CompactTextString(m) }
func (*CardsPayDetails) ProtoMessage()    {}
func (*CardsPayDetails) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{53}
}

func (m *CardsPayDetails) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardsPayDetails.Unmarshal(m, b)
}
func (m *CardsPayDetails) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardsPayDetails.Marshal(b, m, deterministic)
}
func (m *CardsPayDetails) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardsPayDetails.Merge(m, src)
}
func (m *CardsPayDetails) XXX_Size() int {
	return xxx_messageInfo_CardsPayDetails.Size(m)
}
func (m *CardsPayDetails) XXX_DiscardUnknown() {
	xxx_messageInfo_CardsPayDetails.DiscardUnknown(m)
}

var xxx_messageInfo_CardsPayDetails proto.InternalMessageInfo

func (m *CardsPayDetails) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *CardsPayDetails) GetPayMoney() int32 {
	if m != nil {
		return m.PayMoney
	}
	return 0
}

type CardsRefundReq struct {
	//退款订单号
	RefundOrderSn string `protobuf:"bytes,1,opt,name=refund_order_sn,json=refundOrderSn,proto3" json:"refund_order_sn"`
	//退款金额(单位分)
	RefundMoney int32 `protobuf:"varint,2,opt,name=refund_money,json=refundMoney,proto3" json:"refund_money"`
	//财务编码
	FinanceCode string `protobuf:"bytes,3,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//scrm_id
	ScrmId string `protobuf:"bytes,4,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	//订单来源:01商城，02阿闻到家，03挂号
	OrderSource string `protobuf:"bytes,5,opt,name=order_source,json=orderSource,proto3" json:"order_source"`
	//订单号
	OrderSn              string   `protobuf:"bytes,6,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardsRefundReq) Reset()         { *m = CardsRefundReq{} }
func (m *CardsRefundReq) String() string { return proto.CompactTextString(m) }
func (*CardsRefundReq) ProtoMessage()    {}
func (*CardsRefundReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{54}
}

func (m *CardsRefundReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardsRefundReq.Unmarshal(m, b)
}
func (m *CardsRefundReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardsRefundReq.Marshal(b, m, deterministic)
}
func (m *CardsRefundReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardsRefundReq.Merge(m, src)
}
func (m *CardsRefundReq) XXX_Size() int {
	return xxx_messageInfo_CardsRefundReq.Size(m)
}
func (m *CardsRefundReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardsRefundReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardsRefundReq proto.InternalMessageInfo

func (m *CardsRefundReq) GetRefundOrderSn() string {
	if m != nil {
		return m.RefundOrderSn
	}
	return ""
}

func (m *CardsRefundReq) GetRefundMoney() int32 {
	if m != nil {
		return m.RefundMoney
	}
	return 0
}

func (m *CardsRefundReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *CardsRefundReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *CardsRefundReq) GetOrderSource() string {
	if m != nil {
		return m.OrderSource
	}
	return ""
}

func (m *CardsRefundReq) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type CardOrderRecordsReq struct {
	//订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//交易类型3：消费扣款 4：消费退款
	OperateType          int32    `protobuf:"varint,2,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardOrderRecordsReq) Reset()         { *m = CardOrderRecordsReq{} }
func (m *CardOrderRecordsReq) String() string { return proto.CompactTextString(m) }
func (*CardOrderRecordsReq) ProtoMessage()    {}
func (*CardOrderRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{55}
}

func (m *CardOrderRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardOrderRecordsReq.Unmarshal(m, b)
}
func (m *CardOrderRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardOrderRecordsReq.Marshal(b, m, deterministic)
}
func (m *CardOrderRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardOrderRecordsReq.Merge(m, src)
}
func (m *CardOrderRecordsReq) XXX_Size() int {
	return xxx_messageInfo_CardOrderRecordsReq.Size(m)
}
func (m *CardOrderRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardOrderRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardOrderRecordsReq proto.InternalMessageInfo

func (m *CardOrderRecordsReq) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CardOrderRecordsReq) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

type CardOrderRecordsRes struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//
	Data                 []*CardOrderRecordData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *CardOrderRecordsRes) Reset()         { *m = CardOrderRecordsRes{} }
func (m *CardOrderRecordsRes) String() string { return proto.CompactTextString(m) }
func (*CardOrderRecordsRes) ProtoMessage()    {}
func (*CardOrderRecordsRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{56}
}

func (m *CardOrderRecordsRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardOrderRecordsRes.Unmarshal(m, b)
}
func (m *CardOrderRecordsRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardOrderRecordsRes.Marshal(b, m, deterministic)
}
func (m *CardOrderRecordsRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardOrderRecordsRes.Merge(m, src)
}
func (m *CardOrderRecordsRes) XXX_Size() int {
	return xxx_messageInfo_CardOrderRecordsRes.Size(m)
}
func (m *CardOrderRecordsRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CardOrderRecordsRes.DiscardUnknown(m)
}

var xxx_messageInfo_CardOrderRecordsRes proto.InternalMessageInfo

func (m *CardOrderRecordsRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CardOrderRecordsRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CardOrderRecordsRes) GetData() []*CardOrderRecordData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CardOrderRecordData struct {
	//发生时间
	OccurTime string `protobuf:"bytes,1,opt,name=occur_time,json=occurTime,proto3" json:"occur_time"`
	//发生地点（医院机构Id）
	OccurOrgId string `protobuf:"bytes,2,opt,name=occur_org_id,json=occurOrgId,proto3" json:"occur_org_id"`
	//本金发生金额
	CorpusChange float32 `protobuf:"fixed32,3,opt,name=corpus_change,json=corpusChange,proto3" json:"corpus_change"`
	//交易类型,3：消费扣款 4：消费退款
	OperateType string `protobuf:"bytes,4,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	//卡id
	CardId int32 `protobuf:"varint,5,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	//卡号
	CardNumber string `protobuf:"bytes,6,opt,name=card_number,json=cardNumber,proto3" json:"card_number"`
	//卡名称
	CardName             string   `protobuf:"bytes,7,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardOrderRecordData) Reset()         { *m = CardOrderRecordData{} }
func (m *CardOrderRecordData) String() string { return proto.CompactTextString(m) }
func (*CardOrderRecordData) ProtoMessage()    {}
func (*CardOrderRecordData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{57}
}

func (m *CardOrderRecordData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardOrderRecordData.Unmarshal(m, b)
}
func (m *CardOrderRecordData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardOrderRecordData.Marshal(b, m, deterministic)
}
func (m *CardOrderRecordData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardOrderRecordData.Merge(m, src)
}
func (m *CardOrderRecordData) XXX_Size() int {
	return xxx_messageInfo_CardOrderRecordData.Size(m)
}
func (m *CardOrderRecordData) XXX_DiscardUnknown() {
	xxx_messageInfo_CardOrderRecordData.DiscardUnknown(m)
}

var xxx_messageInfo_CardOrderRecordData proto.InternalMessageInfo

func (m *CardOrderRecordData) GetOccurTime() string {
	if m != nil {
		return m.OccurTime
	}
	return ""
}

func (m *CardOrderRecordData) GetOccurOrgId() string {
	if m != nil {
		return m.OccurOrgId
	}
	return ""
}

func (m *CardOrderRecordData) GetCorpusChange() float32 {
	if m != nil {
		return m.CorpusChange
	}
	return 0
}

func (m *CardOrderRecordData) GetOperateType() string {
	if m != nil {
		return m.OperateType
	}
	return ""
}

func (m *CardOrderRecordData) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *CardOrderRecordData) GetCardNumber() string {
	if m != nil {
		return m.CardNumber
	}
	return ""
}

func (m *CardOrderRecordData) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

type CardRecordsReq struct {
	CardId    int32 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	PageIndex int32 `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardRecordsReq) Reset()         { *m = CardRecordsReq{} }
func (m *CardRecordsReq) String() string { return proto.CompactTextString(m) }
func (*CardRecordsReq) ProtoMessage()    {}
func (*CardRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{58}
}

func (m *CardRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardRecordsReq.Unmarshal(m, b)
}
func (m *CardRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardRecordsReq.Marshal(b, m, deterministic)
}
func (m *CardRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardRecordsReq.Merge(m, src)
}
func (m *CardRecordsReq) XXX_Size() int {
	return xxx_messageInfo_CardRecordsReq.Size(m)
}
func (m *CardRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardRecordsReq proto.InternalMessageInfo

func (m *CardRecordsReq) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *CardRecordsReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CardRecordsReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type CardRecordsRes struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//
	Data                 []*CardRecordData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	DataCount            int32             `protobuf:"varint,4,opt,name=data_count,json=dataCount,proto3" json:"data_count"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CardRecordsRes) Reset()         { *m = CardRecordsRes{} }
func (m *CardRecordsRes) String() string { return proto.CompactTextString(m) }
func (*CardRecordsRes) ProtoMessage()    {}
func (*CardRecordsRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{59}
}

func (m *CardRecordsRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardRecordsRes.Unmarshal(m, b)
}
func (m *CardRecordsRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardRecordsRes.Marshal(b, m, deterministic)
}
func (m *CardRecordsRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardRecordsRes.Merge(m, src)
}
func (m *CardRecordsRes) XXX_Size() int {
	return xxx_messageInfo_CardRecordsRes.Size(m)
}
func (m *CardRecordsRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CardRecordsRes.DiscardUnknown(m)
}

var xxx_messageInfo_CardRecordsRes proto.InternalMessageInfo

func (m *CardRecordsRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CardRecordsRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CardRecordsRes) GetData() []*CardRecordData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *CardRecordsRes) GetDataCount() int32 {
	if m != nil {
		return m.DataCount
	}
	return 0
}

type CardRecordData struct {
	CardId int32 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	//卡号
	CardNumber string `protobuf:"bytes,2,opt,name=card_number,json=cardNumber,proto3" json:"card_number"`
	//流水号（子龙)
	RecordNumber string `protobuf:"bytes,3,opt,name=record_number,json=recordNumber,proto3" json:"record_number"`
	//第三方流水号
	PayNumber string `protobuf:"bytes,4,opt,name=pay_number,json=payNumber,proto3" json:"pay_number"`
	//发生时间
	OccurTime string `protobuf:"bytes,5,opt,name=occur_time,json=occurTime,proto3" json:"occur_time"`
	//发生地点（财务编码）
	OccurOrgId string `protobuf:"bytes,6,opt,name=occur_org_id,json=occurOrgId,proto3" json:"occur_org_id"`
	//赠送余额(单位元)
	CorpusBeforeChange float32 `protobuf:"fixed32,7,opt,name=corpus_before_change,json=corpusBeforeChange,proto3" json:"corpus_before_change"`
	//本金部分发生金额
	CorpusChange float32 `protobuf:"fixed32,8,opt,name=corpus_change,json=corpusChange,proto3" json:"corpus_change"`
	//本金余额
	CorpusAfterChange float32 `protobuf:"fixed32,9,opt,name=corpus_after_change,json=corpusAfterChange,proto3" json:"corpus_after_change"`
	//赠送部分发生前金额
	PresentBeforeChange float32 `protobuf:"fixed32,10,opt,name=present_before_change,json=presentBeforeChange,proto3" json:"present_before_change"`
	//赠送部分发生金额
	PresentChange float32 `protobuf:"fixed32,11,opt,name=present_change,json=presentChange,proto3" json:"present_change"`
	//赠送部分余额
	PresentAfterChange float32 `protobuf:"fixed32,12,opt,name=present_after_change,json=presentAfterChange,proto3" json:"present_after_change"`
	//交易类型 1：开卡 ， 2：充值  3：消费扣款  4：消费退款，6：客户退卡
	OperateType int32 `protobuf:"varint,14,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	//发生地点（医院机构名称）
	OccurOrgName         string   `protobuf:"bytes,15,opt,name=occur_org_name,json=occurOrgName,proto3" json:"occur_org_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardRecordData) Reset()         { *m = CardRecordData{} }
func (m *CardRecordData) String() string { return proto.CompactTextString(m) }
func (*CardRecordData) ProtoMessage()    {}
func (*CardRecordData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{60}
}

func (m *CardRecordData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardRecordData.Unmarshal(m, b)
}
func (m *CardRecordData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardRecordData.Marshal(b, m, deterministic)
}
func (m *CardRecordData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardRecordData.Merge(m, src)
}
func (m *CardRecordData) XXX_Size() int {
	return xxx_messageInfo_CardRecordData.Size(m)
}
func (m *CardRecordData) XXX_DiscardUnknown() {
	xxx_messageInfo_CardRecordData.DiscardUnknown(m)
}

var xxx_messageInfo_CardRecordData proto.InternalMessageInfo

func (m *CardRecordData) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *CardRecordData) GetCardNumber() string {
	if m != nil {
		return m.CardNumber
	}
	return ""
}

func (m *CardRecordData) GetRecordNumber() string {
	if m != nil {
		return m.RecordNumber
	}
	return ""
}

func (m *CardRecordData) GetPayNumber() string {
	if m != nil {
		return m.PayNumber
	}
	return ""
}

func (m *CardRecordData) GetOccurTime() string {
	if m != nil {
		return m.OccurTime
	}
	return ""
}

func (m *CardRecordData) GetOccurOrgId() string {
	if m != nil {
		return m.OccurOrgId
	}
	return ""
}

func (m *CardRecordData) GetCorpusBeforeChange() float32 {
	if m != nil {
		return m.CorpusBeforeChange
	}
	return 0
}

func (m *CardRecordData) GetCorpusChange() float32 {
	if m != nil {
		return m.CorpusChange
	}
	return 0
}

func (m *CardRecordData) GetCorpusAfterChange() float32 {
	if m != nil {
		return m.CorpusAfterChange
	}
	return 0
}

func (m *CardRecordData) GetPresentBeforeChange() float32 {
	if m != nil {
		return m.PresentBeforeChange
	}
	return 0
}

func (m *CardRecordData) GetPresentChange() float32 {
	if m != nil {
		return m.PresentChange
	}
	return 0
}

func (m *CardRecordData) GetPresentAfterChange() float32 {
	if m != nil {
		return m.PresentAfterChange
	}
	return 0
}

func (m *CardRecordData) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

func (m *CardRecordData) GetOccurOrgName() string {
	if m != nil {
		return m.OccurOrgName
	}
	return ""
}

type PaySetRequest struct {
	Data                 []*PaySetData `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PaySetRequest) Reset()         { *m = PaySetRequest{} }
func (m *PaySetRequest) String() string { return proto.CompactTextString(m) }
func (*PaySetRequest) ProtoMessage()    {}
func (*PaySetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{61}
}

func (m *PaySetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PaySetRequest.Unmarshal(m, b)
}
func (m *PaySetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PaySetRequest.Marshal(b, m, deterministic)
}
func (m *PaySetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PaySetRequest.Merge(m, src)
}
func (m *PaySetRequest) XXX_Size() int {
	return xxx_messageInfo_PaySetRequest.Size(m)
}
func (m *PaySetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PaySetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PaySetRequest proto.InternalMessageInfo

func (m *PaySetRequest) GetData() []*PaySetData {
	if m != nil {
		return m.Data
	}
	return nil
}

type PaySetData struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 1开启，2关闭
	IsOpen               int32    `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PaySetData) Reset()         { *m = PaySetData{} }
func (m *PaySetData) String() string { return proto.CompactTextString(m) }
func (*PaySetData) ProtoMessage()    {}
func (*PaySetData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{62}
}

func (m *PaySetData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PaySetData.Unmarshal(m, b)
}
func (m *PaySetData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PaySetData.Marshal(b, m, deterministic)
}
func (m *PaySetData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PaySetData.Merge(m, src)
}
func (m *PaySetData) XXX_Size() int {
	return xxx_messageInfo_PaySetData.Size(m)
}
func (m *PaySetData) XXX_DiscardUnknown() {
	xxx_messageInfo_PaySetData.DiscardUnknown(m)
}

var xxx_messageInfo_PaySetData proto.InternalMessageInfo

func (m *PaySetData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PaySetData) GetIsOpen() int32 {
	if m != nil {
		return m.IsOpen
	}
	return 0
}

type CheckPayTypeReq struct {
	//财务编码
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//支付金额(单位分)
	PayMoney int32 `protobuf:"varint,2,opt,name=pay_money,json=payMoney,proto3" json:"pay_money"`
	//scrm_id
	ScrmId string `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	//订单来源:01商城，02阿闻到家，03挂号
	OrderSource          string   `protobuf:"bytes,4,opt,name=order_source,json=orderSource,proto3" json:"order_source"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPayTypeReq) Reset()         { *m = CheckPayTypeReq{} }
func (m *CheckPayTypeReq) String() string { return proto.CompactTextString(m) }
func (*CheckPayTypeReq) ProtoMessage()    {}
func (*CheckPayTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{63}
}

func (m *CheckPayTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPayTypeReq.Unmarshal(m, b)
}
func (m *CheckPayTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPayTypeReq.Marshal(b, m, deterministic)
}
func (m *CheckPayTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPayTypeReq.Merge(m, src)
}
func (m *CheckPayTypeReq) XXX_Size() int {
	return xxx_messageInfo_CheckPayTypeReq.Size(m)
}
func (m *CheckPayTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPayTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPayTypeReq proto.InternalMessageInfo

func (m *CheckPayTypeReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *CheckPayTypeReq) GetPayMoney() int32 {
	if m != nil {
		return m.PayMoney
	}
	return 0
}

func (m *CheckPayTypeReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *CheckPayTypeReq) GetOrderSource() string {
	if m != nil {
		return m.OrderSource
	}
	return ""
}

type CheckPayTypeRes struct {
	Code                 int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *PayTypeData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckPayTypeRes) Reset()         { *m = CheckPayTypeRes{} }
func (m *CheckPayTypeRes) String() string { return proto.CompactTextString(m) }
func (*CheckPayTypeRes) ProtoMessage()    {}
func (*CheckPayTypeRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{64}
}

func (m *CheckPayTypeRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPayTypeRes.Unmarshal(m, b)
}
func (m *CheckPayTypeRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPayTypeRes.Marshal(b, m, deterministic)
}
func (m *CheckPayTypeRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPayTypeRes.Merge(m, src)
}
func (m *CheckPayTypeRes) XXX_Size() int {
	return xxx_messageInfo_CheckPayTypeRes.Size(m)
}
func (m *CheckPayTypeRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPayTypeRes.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPayTypeRes proto.InternalMessageInfo

func (m *CheckPayTypeRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CheckPayTypeRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CheckPayTypeRes) GetData() *PayTypeData {
	if m != nil {
		return m.Data
	}
	return nil
}

type PayTypeData struct {
	//支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
	PayType int32 `protobuf:"varint,1,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	//是否跳转收银台
	IsJumpCashier        bool     `protobuf:"varint,2,opt,name=is_jump_cashier,json=isJumpCashier,proto3" json:"is_jump_cashier"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayTypeData) Reset()         { *m = PayTypeData{} }
func (m *PayTypeData) String() string { return proto.CompactTextString(m) }
func (*PayTypeData) ProtoMessage()    {}
func (*PayTypeData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{65}
}

func (m *PayTypeData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayTypeData.Unmarshal(m, b)
}
func (m *PayTypeData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayTypeData.Marshal(b, m, deterministic)
}
func (m *PayTypeData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayTypeData.Merge(m, src)
}
func (m *PayTypeData) XXX_Size() int {
	return xxx_messageInfo_PayTypeData.Size(m)
}
func (m *PayTypeData) XXX_DiscardUnknown() {
	xxx_messageInfo_PayTypeData.DiscardUnknown(m)
}

var xxx_messageInfo_PayTypeData proto.InternalMessageInfo

func (m *PayTypeData) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *PayTypeData) GetIsJumpCashier() bool {
	if m != nil {
		return m.IsJumpCashier
	}
	return false
}

// 通过订单号查询支付
type PayQueryByOrderIdRequest struct {
	// 支付订单号
	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 是否查询所有状态
	AllStatus            bool     `protobuf:"varint,2,opt,name=all_status,json=allStatus,proto3" json:"all_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayQueryByOrderIdRequest) Reset()         { *m = PayQueryByOrderIdRequest{} }
func (m *PayQueryByOrderIdRequest) String() string { return proto.CompactTextString(m) }
func (*PayQueryByOrderIdRequest) ProtoMessage()    {}
func (*PayQueryByOrderIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{66}
}

func (m *PayQueryByOrderIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayQueryByOrderIdRequest.Unmarshal(m, b)
}
func (m *PayQueryByOrderIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayQueryByOrderIdRequest.Marshal(b, m, deterministic)
}
func (m *PayQueryByOrderIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayQueryByOrderIdRequest.Merge(m, src)
}
func (m *PayQueryByOrderIdRequest) XXX_Size() int {
	return xxx_messageInfo_PayQueryByOrderIdRequest.Size(m)
}
func (m *PayQueryByOrderIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PayQueryByOrderIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PayQueryByOrderIdRequest proto.InternalMessageInfo

func (m *PayQueryByOrderIdRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PayQueryByOrderIdRequest) GetAllStatus() bool {
	if m != nil {
		return m.AllStatus
	}
	return false
}

type PayQueryByOrderIdResponse struct {
	// 订单号
	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 交易流水号
	TradeNo string `protobuf:"bytes,2,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	// 商户流水号
	OutTradeNo string `protobuf:"bytes,3,opt,name=out_trade_no,json=outTradeNo,proto3" json:"out_trade_no"`
	// 订单金额
	TotalPrice int32 `protobuf:"varint,4,opt,name=total_price,json=totalPrice,proto3" json:"total_price"`
	// 支付金额
	PayPrice int32 `protobuf:"varint,5,opt,name=pay_price,json=payPrice,proto3" json:"pay_price"`
	// 退款金额
	Refund int32 `protobuf:"varint,6,opt,name=refund,proto3" json:"refund"`
	// 状态
	Status int32 `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	// 交易时间
	AddTime int32 `protobuf:"varint,8,opt,name=add_time,json=addTime,proto3" json:"add_time"`
	// 交易时间
	PayTime int32 `protobuf:"varint,9,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	// 支付类型
	PayType int32 `protobuf:"varint,10,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	// 支付中心分配的商户号
	MerchantId           string   `protobuf:"bytes,11,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayQueryByOrderIdResponse) Reset()         { *m = PayQueryByOrderIdResponse{} }
func (m *PayQueryByOrderIdResponse) String() string { return proto.CompactTextString(m) }
func (*PayQueryByOrderIdResponse) ProtoMessage()    {}
func (*PayQueryByOrderIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{67}
}

func (m *PayQueryByOrderIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayQueryByOrderIdResponse.Unmarshal(m, b)
}
func (m *PayQueryByOrderIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayQueryByOrderIdResponse.Marshal(b, m, deterministic)
}
func (m *PayQueryByOrderIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayQueryByOrderIdResponse.Merge(m, src)
}
func (m *PayQueryByOrderIdResponse) XXX_Size() int {
	return xxx_messageInfo_PayQueryByOrderIdResponse.Size(m)
}
func (m *PayQueryByOrderIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PayQueryByOrderIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PayQueryByOrderIdResponse proto.InternalMessageInfo

func (m *PayQueryByOrderIdResponse) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PayQueryByOrderIdResponse) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *PayQueryByOrderIdResponse) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *PayQueryByOrderIdResponse) GetTotalPrice() int32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *PayQueryByOrderIdResponse) GetPayPrice() int32 {
	if m != nil {
		return m.PayPrice
	}
	return 0
}

func (m *PayQueryByOrderIdResponse) GetRefund() int32 {
	if m != nil {
		return m.Refund
	}
	return 0
}

func (m *PayQueryByOrderIdResponse) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PayQueryByOrderIdResponse) GetAddTime() int32 {
	if m != nil {
		return m.AddTime
	}
	return 0
}

func (m *PayQueryByOrderIdResponse) GetPayTime() int32 {
	if m != nil {
		return m.PayTime
	}
	return 0
}

func (m *PayQueryByOrderIdResponse) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

func (m *PayQueryByOrderIdResponse) GetMerchantId() string {
	if m != nil {
		return m.MerchantId
	}
	return ""
}

type StandardPayStatusResponse struct {
	//业务订单号
	OrderNo string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	//支付金额 单位：分
	PayAmount int64 `protobuf:"varint,2,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//退款金额 单位：分
	RefundAmount int64 `protobuf:"varint,3,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	//第三方支付流水号
	ThirdPayNo string `protobuf:"bytes,4,opt,name=third_pay_no,json=thirdPayNo,proto3" json:"third_pay_no"`
	//第三方退款流水号
	ThirdRefundNo string `protobuf:"bytes,5,opt,name=third_refund_no,json=thirdRefundNo,proto3" json:"third_refund_no"`
	//支付状态 ： 0、未支付，1、已支付，2、部分退款，3、全部退款， 4、处理中， 5、支付失败，6、交易关闭
	//退款退款 ： 0、未退款，1、退款成功，2、退款处理中，3、退款失败
	Status string `protobuf:"bytes,6,opt,name=status,proto3" json:"status"`
	//支付时间
	PayTime string `protobuf:"bytes,7,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//退款时间
	RefundTime string `protobuf:"bytes,8,opt,name=refund_time,json=refundTime,proto3" json:"refund_time"`
	//支付中心订单号
	TradeNo string `protobuf:"bytes,9,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	//扣款通道返回的流水号
	ChannelNo            string   `protobuf:"bytes,10,opt,name=channel_no,json=channelNo,proto3" json:"channel_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StandardPayStatusResponse) Reset()         { *m = StandardPayStatusResponse{} }
func (m *StandardPayStatusResponse) String() string { return proto.CompactTextString(m) }
func (*StandardPayStatusResponse) ProtoMessage()    {}
func (*StandardPayStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{68}
}

func (m *StandardPayStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StandardPayStatusResponse.Unmarshal(m, b)
}
func (m *StandardPayStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StandardPayStatusResponse.Marshal(b, m, deterministic)
}
func (m *StandardPayStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StandardPayStatusResponse.Merge(m, src)
}
func (m *StandardPayStatusResponse) XXX_Size() int {
	return xxx_messageInfo_StandardPayStatusResponse.Size(m)
}
func (m *StandardPayStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StandardPayStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StandardPayStatusResponse proto.InternalMessageInfo

func (m *StandardPayStatusResponse) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *StandardPayStatusResponse) GetPayAmount() int64 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *StandardPayStatusResponse) GetRefundAmount() int64 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *StandardPayStatusResponse) GetThirdPayNo() string {
	if m != nil {
		return m.ThirdPayNo
	}
	return ""
}

func (m *StandardPayStatusResponse) GetThirdRefundNo() string {
	if m != nil {
		return m.ThirdRefundNo
	}
	return ""
}

func (m *StandardPayStatusResponse) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *StandardPayStatusResponse) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *StandardPayStatusResponse) GetRefundTime() string {
	if m != nil {
		return m.RefundTime
	}
	return ""
}

func (m *StandardPayStatusResponse) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *StandardPayStatusResponse) GetChannelNo() string {
	if m != nil {
		return m.ChannelNo
	}
	return ""
}

type StandardPayRefundResponse struct {
	//退款金额 单位：分
	RefundAmount int64 `protobuf:"varint,1,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	//支付中心退款流水号
	RefundTradeNo string `protobuf:"bytes,2,opt,name=refund_trade_no,json=refundTradeNo,proto3" json:"refund_trade_no"`
	//第三方退款流水号
	ThirdRefundNo string `protobuf:"bytes,3,opt,name=third_refund_no,json=thirdRefundNo,proto3" json:"third_refund_no"`
	//结果 0：未退款 1：退款成功 2：退款处理中 3：退款失败
	Result string `protobuf:"bytes,4,opt,name=result,proto3" json:"result"`
	//退款返回信息
	ResultMsg string `protobuf:"bytes,5,opt,name=result_msg,json=resultMsg,proto3" json:"result_msg"`
	//支付中心流水号
	TradeNo              string   `protobuf:"bytes,6,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StandardPayRefundResponse) Reset()         { *m = StandardPayRefundResponse{} }
func (m *StandardPayRefundResponse) String() string { return proto.CompactTextString(m) }
func (*StandardPayRefundResponse) ProtoMessage()    {}
func (*StandardPayRefundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{69}
}

func (m *StandardPayRefundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StandardPayRefundResponse.Unmarshal(m, b)
}
func (m *StandardPayRefundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StandardPayRefundResponse.Marshal(b, m, deterministic)
}
func (m *StandardPayRefundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StandardPayRefundResponse.Merge(m, src)
}
func (m *StandardPayRefundResponse) XXX_Size() int {
	return xxx_messageInfo_StandardPayRefundResponse.Size(m)
}
func (m *StandardPayRefundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StandardPayRefundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StandardPayRefundResponse proto.InternalMessageInfo

func (m *StandardPayRefundResponse) GetRefundAmount() int64 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *StandardPayRefundResponse) GetRefundTradeNo() string {
	if m != nil {
		return m.RefundTradeNo
	}
	return ""
}

func (m *StandardPayRefundResponse) GetThirdRefundNo() string {
	if m != nil {
		return m.ThirdRefundNo
	}
	return ""
}

func (m *StandardPayRefundResponse) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *StandardPayRefundResponse) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

func (m *StandardPayRefundResponse) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

type StandardPayResponse struct {
	//商户订单号
	OrderNo string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	//支付金额 单位：分
	PayAmount int64 `protobuf:"varint,2,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//第三方支付流水号
	ThirdPayNo string `protobuf:"bytes,3,opt,name=third_pay_no,json=thirdPayNo,proto3" json:"third_pay_no"`
	//结果 0： 未支付 1：成功 2：正在执行 3：失败
	Result string `protobuf:"bytes,4,opt,name=result,proto3" json:"result"`
	//支付时间
	PayTime string `protobuf:"bytes,5,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//支付中心订单号
	TradeNo string `protobuf:"bytes,6,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	// 统一支付下单返回参数
	Details *UnifiedOrderResponseData `protobuf:"bytes,7,opt,name=details,proto3" json:"details"`
	//支付方式 1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）
	TransType            int32    `protobuf:"varint,8,opt,name=trans_type,json=transType,proto3" json:"trans_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StandardPayResponse) Reset()         { *m = StandardPayResponse{} }
func (m *StandardPayResponse) String() string { return proto.CompactTextString(m) }
func (*StandardPayResponse) ProtoMessage()    {}
func (*StandardPayResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{70}
}

func (m *StandardPayResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StandardPayResponse.Unmarshal(m, b)
}
func (m *StandardPayResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StandardPayResponse.Marshal(b, m, deterministic)
}
func (m *StandardPayResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StandardPayResponse.Merge(m, src)
}
func (m *StandardPayResponse) XXX_Size() int {
	return xxx_messageInfo_StandardPayResponse.Size(m)
}
func (m *StandardPayResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StandardPayResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StandardPayResponse proto.InternalMessageInfo

func (m *StandardPayResponse) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *StandardPayResponse) GetPayAmount() int64 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *StandardPayResponse) GetThirdPayNo() string {
	if m != nil {
		return m.ThirdPayNo
	}
	return ""
}

func (m *StandardPayResponse) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *StandardPayResponse) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *StandardPayResponse) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *StandardPayResponse) GetDetails() *UnifiedOrderResponseData {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *StandardPayResponse) GetTransType() int32 {
	if m != nil {
		return m.TransType
	}
	return 0
}

type JsonStrRequest struct {
	// json字节
	JsonByte             []byte   `protobuf:"bytes,1,opt,name=json_byte,json=jsonByte,proto3" json:"json_byte"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JsonStrRequest) Reset()         { *m = JsonStrRequest{} }
func (m *JsonStrRequest) String() string { return proto.CompactTextString(m) }
func (*JsonStrRequest) ProtoMessage()    {}
func (*JsonStrRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{71}
}

func (m *JsonStrRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JsonStrRequest.Unmarshal(m, b)
}
func (m *JsonStrRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JsonStrRequest.Marshal(b, m, deterministic)
}
func (m *JsonStrRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JsonStrRequest.Merge(m, src)
}
func (m *JsonStrRequest) XXX_Size() int {
	return xxx_messageInfo_JsonStrRequest.Size(m)
}
func (m *JsonStrRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JsonStrRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JsonStrRequest proto.InternalMessageInfo

func (m *JsonStrRequest) GetJsonByte() []byte {
	if m != nil {
		return m.JsonByte
	}
	return nil
}

type JsonStrResponse struct {
	// json字节
	JsonByte             []byte   `protobuf:"bytes,1,opt,name=json_byte,json=jsonByte,proto3" json:"json_byte"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JsonStrResponse) Reset()         { *m = JsonStrResponse{} }
func (m *JsonStrResponse) String() string { return proto.CompactTextString(m) }
func (*JsonStrResponse) ProtoMessage()    {}
func (*JsonStrResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{72}
}

func (m *JsonStrResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JsonStrResponse.Unmarshal(m, b)
}
func (m *JsonStrResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JsonStrResponse.Marshal(b, m, deterministic)
}
func (m *JsonStrResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JsonStrResponse.Merge(m, src)
}
func (m *JsonStrResponse) XXX_Size() int {
	return xxx_messageInfo_JsonStrResponse.Size(m)
}
func (m *JsonStrResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JsonStrResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JsonStrResponse proto.InternalMessageInfo

func (m *JsonStrResponse) GetJsonByte() []byte {
	if m != nil {
		return m.JsonByte
	}
	return nil
}

type StandardPayCloseRequest struct {
	// 1：阿闻，2：子龙，3：R1，4：互联网
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id"`
	// 支付中心单号
	TradeNo string `protobuf:"bytes,2,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	// 签名
	Sign                 string   `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StandardPayCloseRequest) Reset()         { *m = StandardPayCloseRequest{} }
func (m *StandardPayCloseRequest) String() string { return proto.CompactTextString(m) }
func (*StandardPayCloseRequest) ProtoMessage()    {}
func (*StandardPayCloseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a59e059b23e4f499, []int{73}
}

func (m *StandardPayCloseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StandardPayCloseRequest.Unmarshal(m, b)
}
func (m *StandardPayCloseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StandardPayCloseRequest.Marshal(b, m, deterministic)
}
func (m *StandardPayCloseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StandardPayCloseRequest.Merge(m, src)
}
func (m *StandardPayCloseRequest) XXX_Size() int {
	return xxx_messageInfo_StandardPayCloseRequest.Size(m)
}
func (m *StandardPayCloseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StandardPayCloseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StandardPayCloseRequest proto.InternalMessageInfo

func (m *StandardPayCloseRequest) GetAppId() int32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *StandardPayCloseRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *StandardPayCloseRequest) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func init() {
	proto.RegisterType((*UnifiedOrderRequest)(nil), "pay.UnifiedOrderRequest")
	proto.RegisterType((*UnifiedOrderReturnData)(nil), "pay.UnifiedOrderReturnData")
	proto.RegisterType((*UnifiedOrderResponse)(nil), "pay.UnifiedOrderResponse")
	proto.RegisterType((*UnifiedOrderResponseData)(nil), "pay.UnifiedOrderResponseData")
	proto.RegisterType((*PayInfoQueryRequest)(nil), "pay.PayInfoQueryRequest")
	proto.RegisterType((*PayInfoQueryResponse)(nil), "pay.PayInfoQueryResponse")
	proto.RegisterType((*PayInfoQueryResponsePayInfo)(nil), "pay.PayInfoQueryResponse.payInfo")
	proto.RegisterType((*UnifiedOrderWxJsApp)(nil), "pay.UnifiedOrderWxJsApp")
	proto.RegisterType((*UnifiedOrderWxNative)(nil), "pay.UnifiedOrderWxNative")
	proto.RegisterType((*UnifiedOrderBdPay)(nil), "pay.UnifiedOrderBdPay")
	proto.RegisterType((*UnifiedOrderBdPayBizInfo)(nil), "pay.UnifiedOrderBdPayBizInfo")
	proto.RegisterType((*BizInfoTpData)(nil), "pay.BizInfoTpData")
	proto.RegisterType((*BaiduOrderRefundGetRequest)(nil), "pay.BaiduOrderRefundGetRequest")
	proto.RegisterType((*BaiduOrderRefundGetResponse)(nil), "pay.BaiduOrderRefundGetResponse")
	proto.RegisterType((*PayAsynNoticeRequest)(nil), "pay.PayAsynNoticeRequest")
	proto.RegisterType((*PayQueryRequest)(nil), "pay.PayQueryRequest")
	proto.RegisterType((*PayQueryResponse)(nil), "pay.PayQueryResponse")
	proto.RegisterType((*PayRefundDetail)(nil), "pay.PayRefundDetail")
	proto.RegisterType((*StdBindingRequest)(nil), "pay.StdBindingRequest")
	proto.RegisterType((*StdBindingResponse)(nil), "pay.StdBindingResponse")
	proto.RegisterType((*StdUnBindingRequest)(nil), "pay.StdUnBindingRequest")
	proto.RegisterType((*QueryStsRequest)(nil), "pay.QueryStsRequest")
	proto.RegisterType((*QueryStsResponse)(nil), "pay.QueryStsResponse")
	proto.RegisterType((*QueryPayStatusRequest)(nil), "pay.QueryPayStatusRequest")
	proto.RegisterType((*QueryPayStatusResponse)(nil), "pay.QueryPayStatusResponse")
	proto.RegisterType((*PayForB2CRequest)(nil), "pay.PayForB2CRequest")
	proto.RegisterType((*YLGoodsDetail)(nil), "pay.YLGoodsDetail")
	proto.RegisterType((*YLOrderInfo)(nil), "pay.YLOrderInfo")
	proto.RegisterType((*YLGoodsInfo)(nil), "pay.YLGoodsInfo")
	proto.RegisterType((*WXGoodsDetail)(nil), "pay.WXGoodsDetail")
	proto.RegisterType((*WXGoodsInfo)(nil), "pay.WXGoodsInfo")
	proto.RegisterType((*AliGoodsDetail)(nil), "pay.AliGoodsDetail")
	proto.RegisterType((*PayForB2CResponse)(nil), "pay.PayForB2CResponse")
	proto.RegisterType((*DYPayRequest)(nil), "pay.DYPayRequest")
	proto.RegisterType((*DYPayResponse)(nil), "pay.DYPayResponse")
	proto.RegisterType((*QueryPwdRequest)(nil), "pay.QueryPwdRequest")
	proto.RegisterType((*QueryPwdResponse)(nil), "pay.QueryPwdResponse")
	proto.RegisterType((*SetPwdRequest)(nil), "pay.SetPwdRequest")
	proto.RegisterType((*UpdateByMobileRequest)(nil), "pay.UpdateByMobileRequest")
	proto.RegisterType((*UpdateByPwdRequest)(nil), "pay.UpdateByPwdRequest")
	proto.RegisterType((*PayListResponse)(nil), "pay.PayListResponse")
	proto.RegisterType((*PayMethod)(nil), "pay.PayMethod")
	proto.RegisterType((*PayConfigResponse)(nil), "pay.PayConfigResponse")
	proto.RegisterType((*PayConfig)(nil), "pay.PayConfig")
	proto.RegisterType((*CheckCodeRequest)(nil), "pay.CheckCodeRequest")
	proto.RegisterType((*CheckCodeResponse)(nil), "pay.CheckCodeResponse")
	proto.RegisterType((*GetCardsBalanceReq)(nil), "pay.GetCardsBalanceReq")
	proto.RegisterType((*GetCardsBalanceRes)(nil), "pay.GetCardsBalanceRes")
	proto.RegisterType((*CardsBalanceData)(nil), "pay.CardsBalanceData")
	proto.RegisterType((*GetCardsReq)(nil), "pay.GetCardsReq")
	proto.RegisterType((*GetCardsRes)(nil), "pay.GetCardsRes")
	proto.RegisterType((*GetCardsData)(nil), "pay.GetCardsData")
	proto.RegisterType((*CardData)(nil), "pay.CardData")
	proto.RegisterType((*CardPayReq)(nil), "pay.CardPayReq")
	proto.RegisterType((*CardsPayDetails)(nil), "pay.CardsPayDetails")
	proto.RegisterType((*CardsRefundReq)(nil), "pay.CardsRefundReq")
	proto.RegisterType((*CardOrderRecordsReq)(nil), "pay.CardOrderRecordsReq")
	proto.RegisterType((*CardOrderRecordsRes)(nil), "pay.CardOrderRecordsRes")
	proto.RegisterType((*CardOrderRecordData)(nil), "pay.CardOrderRecordData")
	proto.RegisterType((*CardRecordsReq)(nil), "pay.CardRecordsReq")
	proto.RegisterType((*CardRecordsRes)(nil), "pay.CardRecordsRes")
	proto.RegisterType((*CardRecordData)(nil), "pay.CardRecordData")
	proto.RegisterType((*PaySetRequest)(nil), "pay.PaySetRequest")
	proto.RegisterType((*PaySetData)(nil), "pay.PaySetData")
	proto.RegisterType((*CheckPayTypeReq)(nil), "pay.CheckPayTypeReq")
	proto.RegisterType((*CheckPayTypeRes)(nil), "pay.CheckPayTypeRes")
	proto.RegisterType((*PayTypeData)(nil), "pay.PayTypeData")
	proto.RegisterType((*PayQueryByOrderIdRequest)(nil), "pay.PayQueryByOrderIdRequest")
	proto.RegisterType((*PayQueryByOrderIdResponse)(nil), "pay.PayQueryByOrderIdResponse")
	proto.RegisterType((*StandardPayStatusResponse)(nil), "pay.StandardPayStatusResponse")
	proto.RegisterType((*StandardPayRefundResponse)(nil), "pay.StandardPayRefundResponse")
	proto.RegisterType((*StandardPayResponse)(nil), "pay.StandardPayResponse")
	proto.RegisterType((*JsonStrRequest)(nil), "pay.JsonStrRequest")
	proto.RegisterType((*JsonStrResponse)(nil), "pay.JsonStrResponse")
	proto.RegisterType((*StandardPayCloseRequest)(nil), "pay.StandardPayCloseRequest")
}

func init() { proto.RegisterFile("pay/pay_info.proto", fileDescriptor_a59e059b23e4f499) }

var fileDescriptor_a59e059b23e4f499 = []byte{
	// 4474 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3b, 0x4b, 0x8c, 0x23, 0x49,
	0x56, 0x63, 0x57, 0xd9, 0x2e, 0x3f, 0xbb, 0x7e, 0x59, 0x5d, 0xd5, 0x6e, 0xf7, 0xd4, 0x74, 0x77,
	0xf6, 0xcc, 0xec, 0x30, 0xb0, 0x3d, 0x43, 0xcf, 0xee, 0xac, 0x96, 0xcf, 0xa2, 0xaa, 0xea, 0x9e,
	0xde, 0xea, 0x99, 0xae, 0x36, 0x76, 0x35, 0xb3, 0xb3, 0x42, 0x58, 0x51, 0xce, 0x28, 0x57, 0xf6,
	0xd8, 0x99, 0x39, 0x99, 0xe9, 0xa9, 0x76, 0x5f, 0x56, 0x42, 0x82, 0x0b, 0x82, 0x0b, 0x07, 0x90,
	0x38, 0x80, 0x38, 0x70, 0x82, 0x2b, 0xe2, 0x00, 0x47, 0x4e, 0x48, 0x08, 0x81, 0x38, 0x72, 0x59,
	0x24, 0x4e, 0x88, 0x03, 0x12, 0x1c, 0x38, 0xa1, 0xf7, 0xe2, 0x45, 0x66, 0x44, 0xda, 0xae, 0xee,
	0xae, 0xd9, 0x81, 0x3d, 0x39, 0xe3, 0xc5, 0x8b, 0x88, 0x17, 0x2f, 0x5e, 0xbc, 0x5f, 0x3c, 0x83,
	0x13, 0x89, 0xe9, 0x7b, 0x91, 0x98, 0xf6, 0xfd, 0xe0, 0x34, 0xbc, 0x13, 0xc5, 0x61, 0x1a, 0x3a,
	0x4b, 0x91, 0x98, 0xb6, 0x5f, 0x1f, 0x86, 0xe1, 0x70, 0x24, 0xdf, 0x13, 0x91, 0xff, 0x9e, 0x08,
	0x82, 0x30, 0x15, 0xa9, 0x1f, 0x06, 0x89, 0x42, 0x69, 0x6f, 0xe0, 0xb0, 0x41, 0x38, 0x1e, 0x87,
	0x81, 0x82, 0xb8, 0x7f, 0xb1, 0x0c, 0x5b, 0x4f, 0x02, 0xff, 0xd4, 0x97, 0xde, 0xe3, 0xd8, 0x93,
	0x71, 0x57, 0x7e, 0x31, 0x91, 0x49, 0xea, 0xbc, 0x0e, 0xf5, 0xe3, 0x58, 0x04, 0xc9, 0xf1, 0x34,
	0x92, 0xad, 0xd2, 0xcd, 0xd2, 0x3b, 0x95, 0x6e, 0x0e, 0x70, 0xde, 0x00, 0x78, 0x3c, 0x49, 0x8f,
	0x63, 0xe1, 0xc9, 0xa3, 0xb0, 0x55, 0xbe, 0x59, 0x7a, 0xa7, 0xde, 0x35, 0x20, 0x4e, 0x1b, 0x56,
	0x3a, 0x62, 0xda, 0x89, 0xfd, 0x81, 0x6c, 0x2d, 0xd1, 0xe0, 0xac, 0x8d, 0x63, 0x8f, 0xc3, 0x54,
	0x8c, 0x54, 0xef, 0x32, 0xf5, 0x1a, 0x10, 0x1c, 0x7b, 0xcf, 0x4f, 0x06, 0xe1, 0x24, 0x48, 0x5b,
	0x15, 0x35, 0x56, 0xb7, 0x91, 0xaa, 0x4e, 0x1c, 0x7a, 0x93, 0x41, 0x7a, 0xe8, 0xb5, 0xaa, 0xb4,
	0x6c, 0x0e, 0x70, 0x6e, 0x42, 0x83, 0x1b, 0x47, 0x62, 0x2c, 0x5b, 0x35, 0xea, 0x37, 0x41, 0x06,
	0xc6, 0x3d, 0x99, 0x0c, 0x5a, 0x2b, 0x16, 0x06, 0x82, 0x70, 0xf5, 0x83, 0x91, 0x2f, 0x83, 0xf4,
	0xb0, 0xd3, 0xaa, 0x53, 0x77, 0xd6, 0x76, 0x76, 0xa0, 0xfa, 0x38, 0x92, 0x81, 0xef, 0xb5, 0x80,
	0x7a, 0xb8, 0x85, 0x3b, 0x7a, 0x24, 0xe3, 0xc1, 0x99, 0x08, 0x90, 0xac, 0x86, 0xe2, 0x46, 0x0e,
	0x41, 0xaa, 0x8f, 0xc2, 0xd4, 0x3f, 0x9d, 0x3e, 0x89, 0x47, 0xad, 0xa6, 0xa2, 0x3a, 0x03, 0xe0,
	0xe8, 0xfb, 0xcf, 0x52, 0x19, 0x78, 0x87, 0xc1, 0x69, 0xd8, 0x5a, 0x55, 0xa3, 0x73, 0x08, 0x52,
	0xd4, 0x9b, 0x9c, 0xec, 0x45, 0xd1, 0xa1, 0xd7, 0x5a, 0x53, 0x14, 0xe9, 0xb6, 0xd3, 0x82, 0x1a,
	0x9d, 0xda, 0xa1, 0xd7, 0x5a, 0xa7, 0x2e, 0xdd, 0xc4, 0x35, 0x7f, 0x4d, 0x8c, 0x7c, 0xef, 0xd8,
	0x1f, 0xcb, 0xd6, 0x86, 0x3a, 0xbf, 0x0c, 0xe0, 0xb8, 0xd0, 0x24, 0xc4, 0x8e, 0x98, 0xd2, 0x01,
	0x6f, 0xd2, 0x60, 0x0b, 0xe6, 0x5c, 0x81, 0x8a, 0x5a, 0xd4, 0xa1, 0xd1, 0xaa, 0xe1, 0xfe, 0x55,
	0x09, 0x76, 0x6c, 0x79, 0x49, 0x27, 0x71, 0x70, 0x4f, 0xa4, 0x02, 0x07, 0x08, 0x1a, 0x50, 0xa2,
	0xd9, 0x54, 0x03, 0xc9, 0x0f, 0xc2, 0x60, 0x20, 0x7b, 0x69, 0xcc, 0x82, 0x92, 0xb5, 0x91, 0xfc,
	0x48, 0x0c, 0x3e, 0x17, 0x43, 0x25, 0x25, 0xf5, 0xae, 0x6e, 0xaa, 0x9e, 0x69, 0xcf, 0x1f, 0x06,
	0x24, 0x21, 0xd4, 0x43, 0x4d, 0x9c, 0x2f, 0xf1, 0x87, 0x01, 0x91, 0x5d, 0x51, 0xf3, 0xe9, 0x36,
	0x6e, 0x3a, 0xf5, 0xc7, 0xb2, 0x97, 0x8a, 0x71, 0xa4, 0xc5, 0x23, 0x03, 0xb8, 0x7f, 0x58, 0x82,
	0x2b, 0x36, 0xe9, 0x49, 0x14, 0x06, 0x89, 0x74, 0x1c, 0x58, 0x1e, 0x84, 0x9e, 0x16, 0x73, 0xfa,
	0x46, 0x02, 0xc6, 0x32, 0x49, 0x90, 0x34, 0x45, 0xb5, 0x6e, 0x3a, 0xdf, 0x81, 0x9a, 0x27, 0x53,
	0xe1, 0x8f, 0x12, 0x22, 0xba, 0x71, 0x77, 0xf7, 0x4e, 0x24, 0xa6, 0x77, 0xe6, 0xcd, 0x8c, 0x6c,
	0xe9, 0x6a, 0x6c, 0xe7, 0x1a, 0xac, 0xe0, 0x8d, 0x4d, 0x91, 0x72, 0x25, 0xf6, 0xb8, 0x29, 0x24,
	0xdc, 0xfd, 0xbb, 0x32, 0xb4, 0x16, 0x4d, 0xe0, 0x7c, 0x08, 0x2b, 0xe7, 0xcf, 0xfa, 0x4f, 0x13,
	0x11, 0xf9, 0x44, 0x62, 0xe3, 0xee, 0xf5, 0x39, 0x2b, 0xea, 0x63, 0xe8, 0xd6, 0xce, 0x9f, 0x3d,
	0x44, 0x5c, 0x5c, 0x2f, 0xc4, 0xbe, 0xbe, 0xef, 0xe9, 0x3d, 0x84, 0x2c, 0x1d, 0x1f, 0x42, 0xfd,
	0xfc, 0x59, 0x3f, 0x10, 0xa9, 0xff, 0xa5, 0xe4, 0x5d, 0x5c, 0x9b, 0x99, 0xf3, 0xd3, 0x67, 0x47,
	0x84, 0xd0, 0x5d, 0x39, 0xe7, 0x2f, 0xe7, 0x5b, 0x34, 0xee, 0x69, 0xd2, 0x17, 0x51, 0x44, 0x7b,
	0x68, 0xdc, 0x6d, 0xcd, 0x19, 0xf7, 0x30, 0xd9, 0x8b, 0x22, 0x45, 0xc8, 0x5e, 0x14, 0x21, 0x7f,
	0x4f, 0x44, 0xf0, 0x39, 0x1f, 0x17, 0x7d, 0xa3, 0xd4, 0x8b, 0x91, 0xaf, 0x35, 0x88, 0x3a, 0x2b,
	0x03, 0xe2, 0xfc, 0x02, 0xac, 0x9e, 0x78, 0x7d, 0xa6, 0x1f, 0x2f, 0x46, 0x8d, 0x56, 0xdb, 0x99,
	0x59, 0x6d, 0xdf, 0xeb, 0x88, 0x69, 0xb7, 0x71, 0xa2, 0x5a, 0x78, 0x63, 0xdc, 0x3f, 0x2b, 0xc1,
	0x56, 0x47, 0x4c, 0xf1, 0xfb, 0x57, 0x27, 0x32, 0x9e, 0x6a, 0x9d, 0x66, 0x32, 0xa4, 0x64, 0x33,
	0xe4, 0x06, 0x34, 0xc6, 0x7c, 0x61, 0x73, 0x76, 0xc1, 0x38, 0xbf, 0xc3, 0xdb, 0x50, 0x15, 0x51,
	0x84, 0x7d, 0x4a, 0x9f, 0xb1, 0x74, 0x2f, 0x3e, 0x53, 0xe7, 0x16, 0x34, 0x23, 0xa5, 0x58, 0xfa,
	0x1e, 0x2a, 0x1b, 0xb5, 0xfb, 0x46, 0x94, 0x2b, 0x1b, 0xf7, 0xef, 0xcb, 0x70, 0xc5, 0x26, 0xf4,
	0x52, 0x12, 0xf9, 0x6d, 0x58, 0xf6, 0x44, 0x2a, 0xf8, 0x20, 0x6f, 0x11, 0x8b, 0xe6, 0x4d, 0x8b,
	0x1d, 0x08, 0xec, 0x12, 0x7a, 0xfb, 0x1f, 0x4a, 0x74, 0xc9, 0x48, 0xc9, 0x5c, 0xcc, 0x9a, 0x14,
	0x15, 0x7f, 0x3f, 0x49, 0x45, 0xaa, 0xd7, 0x06, 0x02, 0xf5, 0x10, 0xe2, 0x5c, 0x87, 0xba, 0x42,
	0x10, 0xe3, 0x54, 0x6b, 0x7b, 0x02, 0xec, 0x8d, 0x53, 0xe7, 0x2a, 0xd4, 0x4e, 0xa5, 0xa4, 0x2e,
	0xa5, 0x8f, 0xab, 0xa7, 0x52, 0x62, 0xc7, 0x2e, 0x80, 0x5a, 0x11, 0x2f, 0x28, 0x5f, 0xf2, 0x3a,
	0x41, 0x48, 0x43, 0x69, 0xc6, 0x62, 0x67, 0x25, 0xd3, 0x00, 0xba, 0x2b, 0x45, 0x29, 0xe9, 0x07,
	0x5a, 0x70, 0x6a, 0xa9, 0x92, 0x1a, 0xf7, 0x63, 0xdb, 0x98, 0xb1, 0x24, 0x22, 0xeb, 0x9e, 0x26,
	0x7b, 0x86, 0x6e, 0xd2, 0x4d, 0xd4, 0x26, 0xf4, 0x89, 0x9a, 0x99, 0xb5, 0x93, 0x6e, 0xbb, 0x77,
	0x6c, 0x75, 0xa1, 0xaf, 0x03, 0x9a, 0x81, 0x48, 0x90, 0x2e, 0x57, 0x93, 0x71, 0xcb, 0xfd, 0xeb,
	0x32, 0x6c, 0xce, 0x48, 0x26, 0x62, 0x7b, 0x52, 0x8c, 0xb2, 0xa5, 0xb9, 0x85, 0x70, 0x11, 0x45,
	0x1f, 0xcb, 0x29, 0xaf, 0xcb, 0x2d, 0x34, 0x51, 0x29, 0x1a, 0xc3, 0xbd, 0x31, 0x59, 0x40, 0xa5,
	0x17, 0x4d, 0x10, 0x69, 0xb9, 0x48, 0xab, 0x7d, 0x66, 0x5c, 0x06, 0xc0, 0xde, 0x20, 0x33, 0x36,
	0x8a, 0x73, 0x39, 0x00, 0x7b, 0x71, 0xfd, 0x63, 0x3f, 0x1d, 0x49, 0xad, 0x21, 0x33, 0x80, 0xf3,
	0x0e, 0xac, 0xa3, 0x2e, 0xfd, 0xc8, 0x97, 0x23, 0x2f, 0xe9, 0x8a, 0x60, 0xa8, 0x8d, 0x68, 0x11,
	0x8c, 0x1c, 0x8d, 0x13, 0x41, 0xfa, 0x59, 0x19, 0x51, 0xdd, 0x44, 0xf5, 0x78, 0xe2, 0x3f, 0x27,
	0x5b, 0x56, 0x5f, 0xa0, 0x1e, 0x89, 0x31, 0xfb, 0x0a, 0xa9, 0xab, 0xb1, 0xdd, 0x8f, 0x6c, 0x15,
	0x68, 0x22, 0x39, 0xef, 0x42, 0x35, 0x8d, 0x50, 0xbb, 0xb1, 0x02, 0x74, 0x68, 0x4e, 0xee, 0x3d,
	0xa6, 0x9e, 0x2e, 0x63, 0xb8, 0x3f, 0x82, 0x55, 0xab, 0xe3, 0xff, 0xfa, 0x04, 0xdc, 0x0f, 0xa1,
	0xbd, 0x2f, 0x7c, 0x6f, 0xc2, 0x8a, 0xf9, 0x74, 0x12, 0x78, 0x0f, 0x64, 0xaa, 0x95, 0x50, 0x0b,
	0xb4, 0xb4, 0x6a, 0x59, 0xd4, 0xc2, 0xfb, 0xb7, 0x25, 0xb8, 0x3e, 0x77, 0x20, 0x2b, 0x85, 0x77,
	0x61, 0xe3, 0xc4, 0x7f, 0xae, 0xe0, 0xfb, 0x22, 0x1d, 0x9c, 0x65, 0x3b, 0x9a, 0x81, 0x3b, 0x6f,
	0xc2, 0x6a, 0x6c, 0x21, 0xe2, 0x16, 0x97, 0xba, 0x36, 0x10, 0xdd, 0x00, 0x05, 0xc0, 0x8b, 0x3c,
	0x51, 0xf6, 0x6c, 0xa9, 0x6b, 0xc1, 0x90, 0xde, 0xd0, 0xd8, 0xe9, 0x52, 0xae, 0x18, 0x76, 0xa0,
	0x3a, 0x49, 0xa8, 0xa3, 0x42, 0x1d, 0xdc, 0x72, 0xff, 0xb4, 0x44, 0x5a, 0x6d, 0x2f, 0x99, 0x06,
	0xe8, 0xe5, 0x0c, 0xa4, 0xde, 0xfa, 0x1b, 0x60, 0xa8, 0x0d, 0x26, 0xdd, 0x54, 0x24, 0xc6, 0x52,
	0x05, 0x7b, 0x65, 0x30, 0x6d, 0xc9, 0x62, 0x1a, 0x1e, 0x05, 0xaa, 0xe8, 0x40, 0x8e, 0x8e, 0x42,
	0x7d, 0x14, 0x19, 0x80, 0xdd, 0x88, 0xe3, 0x59, 0x25, 0xe2, 0xf6, 0x61, 0xbd, 0x23, 0xa6, 0x96,
	0x79, 0xb0, 0xdd, 0xb8, 0xd2, 0x8c, 0x1b, 0xd7, 0x82, 0x9a, 0xed, 0xf1, 0xea, 0x26, 0xaa, 0x6b,
	0xba, 0x0a, 0x8a, 0x36, 0xfa, 0x76, 0xff, 0xb2, 0x0c, 0x1b, 0xf9, 0x0a, 0x7c, 0x84, 0x17, 0xa8,
	0x59, 0x5b, 0x1f, 0x96, 0x8b, 0xfa, 0xf0, 0x26, 0x34, 0x49, 0x1f, 0x6a, 0xc5, 0xa7, 0x96, 0x02,
	0xdc, 0x0e, 0x13, 0x71, 0x83, 0xc5, 0xb6, 0x1f, 0x99, 0x8e, 0x75, 0x9a, 0x3b, 0xd6, 0xd7, 0xa1,
	0x8e, 0x53, 0xa8, 0x6e, 0xf6, 0xac, 0x23, 0xed, 0x95, 0xef, 0x40, 0x55, 0x1d, 0x3b, 0x69, 0x85,
	0x4a, 0x97, 0x5b, 0x48, 0x16, 0x0e, 0x4a, 0x94, 0x80, 0xd4, 0x94, 0x23, 0x89, 0xbe, 0x98, 0x92,
	0x8e, 0x1d, 0xa8, 0x72, 0x97, 0x52, 0x03, 0xdc, 0x72, 0xbe, 0xab, 0xe5, 0xaf, 0xaf, 0xbc, 0x9f,
	0x56, 0xfd, 0xe6, 0xd2, 0x3b, 0x8d, 0xbb, 0x57, 0xb4, 0x6d, 0x52, 0xd2, 0x7a, 0x8f, 0xfa, 0xb4,
	0xc0, 0xa9, 0x96, 0xfb, 0xdf, 0x25, 0x3a, 0x1a, 0x13, 0xe3, 0x22, 0xbe, 0xbd, 0x0d, 0xeb, 0xbc,
	0x52, 0xc6, 0x1b, 0xc5, 0x3c, 0x26, 0x40, 0xb3, 0xe7, 0x76, 0x46, 0x51, 0x92, 0x0b, 0x7b, 0xa5,
	0x20, 0xec, 0x39, 0x92, 0x50, 0x97, 0x7f, 0xd9, 0x44, 0xe2, 0xdb, 0x7f, 0x03, 0x1a, 0x92, 0xdc,
	0x73, 0xe5, 0x98, 0x28, 0xc1, 0x02, 0x99, 0x7b, 0xec, 0xbb, 0x00, 0x27, 0x62, 0xf0, 0x79, 0x3f,
	0x12, 0xb1, 0x18, 0x6b, 0x2d, 0x8b, 0x90, 0x0e, 0x02, 0x0c, 0x9e, 0xd5, 0x4c, 0x9e, 0xb9, 0x3f,
	0x2e, 0xc3, 0x66, 0x2f, 0xf5, 0xf6, 0xfd, 0xc0, 0xf3, 0x83, 0xa1, 0x11, 0x88, 0x85, 0xf1, 0xf0,
	0x68, 0x32, 0x3e, 0x91, 0x31, 0xef, 0x3d, 0x07, 0x38, 0x1b, 0xb0, 0x94, 0x26, 0x01, 0xef, 0x18,
	0x3f, 0x51, 0x4a, 0xbd, 0xe9, 0xa3, 0xc1, 0x59, 0x7e, 0x55, 0xb8, 0x49, 0x9e, 0x73, 0xd0, 0x0b,
	0x27, 0xf1, 0x40, 0xdb, 0xdb, 0xac, 0x8d, 0x7d, 0xde, 0xf4, 0x58, 0xc6, 0xe3, 0x23, 0xbd, 0xa1,
	0xac, 0x4d, 0xfa, 0x50, 0xc6, 0xe3, 0x8f, 0xc4, 0x20, 0x0d, 0xe3, 0x29, 0xef, 0xc7, 0x04, 0x91,
	0x3e, 0x94, 0xf1, 0xf8, 0x51, 0xe8, 0xc9, 0x11, 0x6f, 0x2a, 0x07, 0xe0, 0xdc, 0xd8, 0xa0, 0x98,
	0x4c, 0x49, 0x49, 0xd6, 0xd6, 0x73, 0xef, 0x79, 0x5e, 0x2c, 0x93, 0x84, 0x23, 0x2e, 0x13, 0x84,
	0x0e, 0x44, 0x72, 0x16, 0x92, 0xe7, 0xc5, 0x51, 0x17, 0x36, 0x95, 0xe9, 0x46, 0x3c, 0x0a, 0x04,
	0x1a, 0xf9, 0xb4, 0xe4, 0x7b, 0xf1, 0xbd, 0xc7, 0xae, 0xa6, 0xed, 0x69, 0xff, 0x06, 0x38, 0x26,
	0x8f, 0x2f, 0xf0, 0xb7, 0x4c, 0x96, 0x94, 0x0b, 0x2c, 0x31, 0x7c, 0xb1, 0x25, 0xcb, 0x17, 0x73,
	0xa7, 0xb0, 0xd5, 0x4b, 0xbd, 0x27, 0xc1, 0xd7, 0x76, 0x8a, 0xc6, 0xd6, 0x0a, 0x41, 0xc4, 0x1e,
	0xac, 0x93, 0xb6, 0xe9, 0xa5, 0xc9, 0x25, 0x97, 0x75, 0x7f, 0xaf, 0x04, 0x1b, 0xf9, 0x1c, 0x97,
	0x72, 0x46, 0x2f, 0x94, 0xbf, 0x8c, 0xa1, 0xcb, 0x05, 0x86, 0xe6, 0x77, 0xa2, 0x62, 0xdd, 0x89,
	0xdf, 0x2f, 0xc1, 0x36, 0x11, 0xd4, 0xd1, 0x2a, 0xc7, 0x70, 0xe6, 0xb3, 0x0b, 0x6f, 0x1b, 0x52,
	0xb6, 0x16, 0xc1, 0xc0, 0xcb, 0x88, 0xe3, 0xa6, 0x73, 0x07, 0xea, 0x67, 0x52, 0x78, 0xfd, 0x13,
	0x91, 0xe8, 0xb8, 0x67, 0x93, 0x54, 0x52, 0x6f, 0x20, 0x82, 0xef, 0x4b, 0xe1, 0xed, 0x8b, 0x44,
	0x76, 0x57, 0xce, 0xf8, 0xcb, 0xf0, 0xfa, 0x97, 0x0d, 0xaf, 0xdf, 0xfd, 0xf7, 0x12, 0xec, 0x14,
	0xa9, 0xba, 0x14, 0xb3, 0xcc, 0x4d, 0x14, 0x0c, 0x1b, 0x2b, 0x5e, 0x43, 0x0f, 0xd5, 0x49, 0xf1,
	0xb2, 0x12, 0xe2, 0xee, 0x58, 0x26, 0x93, 0x51, 0xaa, 0xfd, 0xbc, 0x08, 0xd5, 0x26, 0x02, 0x90,
	0x70, 0xec, 0xce, 0x3c, 0xe4, 0x4a, 0x24, 0xa6, 0x47, 0xa1, 0xe5, 0x55, 0xd7, 0x66, 0xbc, 0x6a,
	0xa5, 0x62, 0x83, 0x50, 0xbb, 0x74, 0xd4, 0x3e, 0x0a, 0xdd, 0xff, 0x5c, 0x26, 0x53, 0xf6, 0x51,
	0x18, 0xef, 0xdf, 0x3d, 0xd0, 0xfc, 0xbf, 0x09, 0xcd, 0xb1, 0x8c, 0xfb, 0xd9, 0x98, 0x52, 0x16,
	0x32, 0x3d, 0x56, 0xc3, 0x70, 0xc6, 0x13, 0x11, 0xf7, 0x89, 0x1d, 0xbc, 0xef, 0x13, 0x11, 0x1f,
	0x20, 0x47, 0xcc, 0xb0, 0x69, 0xc9, 0x0e, 0x9b, 0x66, 0xf7, 0x5d, 0x31, 0xf7, 0xfd, 0x01, 0x6c,
	0x4f, 0x02, 0x8f, 0xf3, 0x41, 0xe2, 0x64, 0x24, 0x35, 0xa6, 0x62, 0xc1, 0x15, 0xbb, 0x33, 0x67,
	0x16, 0xd3, 0x89, 0x3a, 0xa8, 0x6a, 0xd8, 0x56, 0x52, 0x42, 0x59, 0x37, 0xc5, 0x69, 0x35, 0xa3,
	0x9b, 0x52, 0x42, 0xaf, 0x43, 0xfd, 0x4b, 0x31, 0xf2, 0xbd, 0x27, 0x81, 0x9f, 0x32, 0x6b, 0x72,
	0x00, 0x4a, 0x35, 0x35, 0x8e, 0x26, 0x63, 0x9d, 0x30, 0xd2, 0x6d, 0x5b, 0xdc, 0x56, 0x5f, 0x2c,
	0x6e, 0x37, 0xa1, 0x19, 0x4e, 0xd2, 0x9c, 0xa7, 0x2a, 0xdd, 0x03, 0xe1, 0x24, 0xd5, 0x3c, 0xbd,
	0x05, 0x4d, 0x65, 0xe4, 0x79, 0xd7, 0xeb, 0xc4, 0x1f, 0xcb, 0x39, 0xc5, 0x6b, 0xa6, 0xf3, 0x67,
	0x2a, 0xf1, 0x93, 0xb5, 0xed, 0x4c, 0xd4, 0x66, 0x31, 0x13, 0x65, 0x6a, 0x54, 0xa7, 0xa0, 0x51,
	0xaf, 0x43, 0x7d, 0x40, 0x79, 0xb0, 0xbe, 0x1f, 0xb5, 0xb6, 0x54, 0xa7, 0x02, 0x1c, 0x46, 0xc6,
	0x35, 0xb9, 0x62, 0x06, 0xc7, 0x05, 0x43, 0xb9, 0x3d, 0x63, 0x28, 0xdb, 0xb0, 0x32, 0x0a, 0x07,
	0x94, 0xa1, 0x6c, 0xed, 0xa8, 0x39, 0x75, 0xdb, 0x0d, 0x61, 0xf5, 0xb3, 0x4f, 0x1e, 0x84, 0xa1,
	0x97, 0xb0, 0x0f, 0x70, 0x07, 0xd4, 0x99, 0x50, 0x68, 0xa1, 0xc2, 0x80, 0x0d, 0x62, 0xe6, 0x67,
	0x9f, 0x64, 0xa1, 0x7f, 0x37, 0x47, 0x41, 0xfc, 0x21, 0x0e, 0x27, 0xfc, 0x32, 0xb9, 0x1f, 0x1a,
	0xff, 0x81, 0x86, 0x77, 0x73, 0x14, 0xf7, 0x47, 0xd0, 0x30, 0x66, 0x72, 0xae, 0x40, 0x25, 0xa5,
	0x28, 0x89, 0xb3, 0x59, 0xd4, 0x40, 0x7b, 0x85, 0x42, 0x12, 0xfb, 0x11, 0x11, 0xad, 0xc4, 0xda,
	0x04, 0x51, 0x84, 0x35, 0x48, 0xad, 0xd8, 0x21, 0x07, 0xe0, 0x8e, 0x85, 0xe7, 0x05, 0x44, 0x13,
	0xeb, 0x40, 0xdd, 0x76, 0xff, 0xa8, 0x84, 0x14, 0x64, 0xb4, 0x39, 0x6b, 0x50, 0xce, 0xdc, 0x9d,
	0xb2, 0xef, 0xa1, 0x6a, 0x21, 0xf9, 0x55, 0x8b, 0xd2, 0x37, 0x52, 0x19, 0x65, 0x59, 0x56, 0xbc,
	0xe6, 0x3a, 0x85, 0xfa, 0xc5, 0x44, 0x04, 0xa9, 0x9f, 0x4e, 0xf5, 0x2a, 0xba, 0x8d, 0x7d, 0x03,
	0x91, 0xca, 0x21, 0x9a, 0x72, 0xb6, 0xf4, 0xba, 0x6d, 0x51, 0x57, 0x2d, 0x50, 0xf7, 0x9b, 0x25,
	0x58, 0xfd, 0xf4, 0x07, 0xe6, 0x81, 0xec, 0x02, 0x0c, 0xc2, 0x24, 0x65, 0x87, 0x92, 0xad, 0x0b,
	0x42, 0x94, 0x47, 0xb9, 0x0b, 0x10, 0xcb, 0x81, 0xf4, 0x23, 0x23, 0xa3, 0x52, 0x67, 0xc8, 0xa1,
	0xe7, 0x7c, 0x00, 0x4d, 0xe2, 0xbd, 0x76, 0x10, 0x97, 0x8c, 0x13, 0xe2, 0x75, 0xe8, 0x84, 0x1a,
	0xc3, 0x7c, 0x49, 0xf7, 0x8f, 0x4b, 0xd0, 0x30, 0x3a, 0x51, 0x8f, 0xa8, 0x49, 0x72, 0xbf, 0x50,
	0x1d, 0x28, 0x46, 0x40, 0x6b, 0xe7, 0xcf, 0x50, 0x93, 0x64, 0x08, 0x8a, 0x84, 0x26, 0x41, 0x1f,
	0x30, 0xd6, 0x2e, 0x80, 0xea, 0x27, 0xce, 0xf2, 0x71, 0x11, 0x84, 0x34, 0xc3, 0x45, 0x8c, 0xcc,
	0x58, 0x5f, 0x31, 0x58, 0xef, 0xfe, 0x4f, 0x09, 0xd6, 0xf6, 0x46, 0xbe, 0xc9, 0xa7, 0x0b, 0x88,
	0x7c, 0x1b, 0xd6, 0xc5, 0xc8, 0x9f, 0x43, 0xe5, 0xaa, 0x02, 0x7f, 0x5d, 0x64, 0x3a, 0x6f, 0xc1,
	0x9a, 0x9a, 0x30, 0x93, 0x05, 0x75, 0xde, 0xab, 0x04, 0x3d, 0xd0, 0x02, 0xe1, 0xc0, 0xf2, 0x49,
	0xe8, 0x4d, 0x59, 0x27, 0xd2, 0x37, 0x6e, 0x27, 0x39, 0x0b, 0xcf, 0xfb, 0x93, 0x78, 0xa4, 0x0d,
	0x05, 0xb6, 0x9f, 0xc4, 0x23, 0xf7, 0x3f, 0x4a, 0xb0, 0x69, 0x18, 0x8a, 0x4b, 0x99, 0xc4, 0xa2,
	0x5d, 0x59, 0x9a, 0xb1, 0x2b, 0xff, 0x0f, 0x96, 0x31, 0x33, 0xd2, 0x2b, 0x76, 0xc8, 0xfe, 0x37,
	0x65, 0x68, 0xde, 0xfb, 0x8c, 0xa2, 0x95, 0x42, 0x8a, 0x31, 0xf7, 0x4a, 0xd8, 0x8a, 0x5e, 0xe0,
	0x95, 0x98, 0x86, 0x72, 0xc9, 0x36, 0x94, 0x2f, 0xb0, 0x86, 0xb6, 0x61, 0xab, 0x14, 0x0d, 0x9b,
	0x69, 0x66, 0xab, 0xb6, 0x99, 0xe5, 0x60, 0x90, 0x0c, 0x07, 0x87, 0x75, 0x88, 0x4b, 0xef, 0x30,
	0x96, 0x09, 0x59, 0x29, 0x98, 0x90, 0x5d, 0x00, 0x95, 0x4e, 0x22, 0x01, 0xa8, 0x17, 0x13, 0x4c,
	0xdb, 0x50, 0x4d, 0xe3, 0x71, 0x3f, 0x09, 0xd8, 0x5b, 0xaf, 0xa4, 0xf1, 0xb8, 0x17, 0x68, 0xb0,
	0xaf, 0x9f, 0x47, 0x10, 0x7c, 0xe8, 0xb9, 0xff, 0x5a, 0x82, 0x55, 0xe6, 0xdf, 0x65, 0xfd, 0xa7,
	0x82, 0xa0, 0x64, 0xec, 0xfe, 0x29, 0x92, 0x92, 0x1f, 0xb0, 0x63, 0xde, 0x39, 0xf7, 0xb4, 0x9c,
	0xec, 0x40, 0x75, 0x1c, 0x9e, 0xf8, 0x99, 0x79, 0xe1, 0x16, 0x6e, 0x9f, 0x4e, 0xab, 0xac, 0xb6,
	0x8f, 0xdf, 0xf9, 0x26, 0x93, 0xc0, 0xda, 0x64, 0x2f, 0x70, 0x3f, 0x65, 0x77, 0x9d, 0x66, 0xbe,
	0x14, 0x07, 0xb7, 0xa1, 0xea, 0x27, 0xfd, 0xe8, 0x3c, 0xcb, 0x6b, 0xfb, 0x49, 0xe7, 0xdc, 0x73,
	0x7f, 0x1d, 0x56, 0x7b, 0x32, 0x7d, 0x09, 0x82, 0xdb, 0xc8, 0x91, 0x24, 0x39, 0x0f, 0x63, 0xad,
	0xba, 0xb2, 0x36, 0x85, 0x6e, 0x03, 0x75, 0xea, 0x4b, 0xec, 0xd5, 0x0f, 0xe8, 0xd8, 0x9f, 0xc1,
	0xf6, 0x93, 0xc8, 0x13, 0xa9, 0xdc, 0x9f, 0x3e, 0xa2, 0x69, 0xbe, 0xca, 0x2a, 0x7a, 0xbf, 0x9c,
	0x7c, 0xa1, 0xfd, 0x1a, 0x2b, 0x2f, 0x5b, 0x2b, 0xff, 0x4e, 0x09, 0x1c, 0xbd, 0xf4, 0x4b, 0xec,
	0xee, 0x16, 0x34, 0xc3, 0x91, 0xd7, 0x2f, 0xac, 0xdd, 0x08, 0x47, 0x5e, 0x47, 0x2f, 0x7f, 0x0b,
	0x9a, 0x81, 0x3c, 0xcf, 0x51, 0x38, 0x5d, 0x18, 0xc8, 0xf3, 0xce, 0x1c, 0x3e, 0xd8, 0xd4, 0x0c,
	0x28, 0xd3, 0xf1, 0x89, 0x9f, 0xa4, 0x97, 0x3c, 0x3d, 0x37, 0xcb, 0xfc, 0xa3, 0xf1, 0x5c, 0xd3,
	0xd9, 0x95, 0x47, 0x32, 0x3d, 0x0b, 0x3d, 0x95, 0xe6, 0x77, 0x7b, 0x50, 0xcf, 0x40, 0x86, 0x4f,
	0x51, 0x21, 0x9f, 0x82, 0xaf, 0xc1, 0x98, 0x7a, 0xb5, 0x91, 0x8e, 0x32, 0xf4, 0xab, 0x50, 0xf3,
	0x93, 0x7e, 0x18, 0xc9, 0x80, 0xc5, 0xa3, 0xea, 0x27, 0x8f, 0x23, 0x19, 0xb8, 0x92, 0x14, 0xfd,
	0x41, 0x18, 0x9c, 0xfa, 0xc3, 0x9f, 0x2c, 0xed, 0x3c, 0xa7, 0xa2, 0x3d, 0x20, 0xda, 0x15, 0xe8,
	0x27, 0x45, 0x3b, 0xd9, 0xe3, 0x38, 0x9c, 0x18, 0x61, 0x5d, 0x8d, 0xda, 0x87, 0x9e, 0xfb, 0x3d,
	0xd8, 0x38, 0x38, 0x93, 0x83, 0xcf, 0x51, 0xf7, 0xbe, 0xc4, 0x55, 0x35, 0x42, 0x1b, 0xfa, 0x76,
	0x7f, 0x08, 0x9b, 0xc6, 0xf8, 0x4b, 0xb1, 0x45, 0x91, 0x9d, 0xc6, 0x13, 0x99, 0x93, 0x7d, 0x1c,
	0x4f, 0xa4, 0xfb, 0x4d, 0x70, 0x1e, 0xc8, 0xf4, 0x40, 0xc4, 0x5e, 0xb2, 0x2f, 0x46, 0x22, 0xa0,
	0xbc, 0xaa, 0x29, 0x5b, 0x25, 0x4b, 0xb6, 0xc6, 0x73, 0xd0, 0x93, 0x57, 0xa4, 0xe5, 0x67, 0xac,
	0x87, 0xa5, 0x6d, 0x3a, 0x22, 0x73, 0x46, 0xca, 0xbb, 0xab, 0x93, 0x7a, 0x0e, 0x1b, 0xc5, 0x1e,
	0x9c, 0xf8, 0x44, 0x35, 0xb5, 0x2d, 0xe4, 0xa6, 0xf3, 0xb3, 0xb0, 0x29, 0xbe, 0x14, 0xfe, 0x88,
	0x02, 0x38, 0x8d, 0xa3, 0x16, 0xdf, 0xc8, 0x3a, 0x78, 0x2a, 0x0c, 0x23, 0x86, 0xfe, 0x69, 0xaa,
	0x55, 0x39, 0x3b, 0x04, 0x08, 0x52, 0xba, 0xdc, 0xfd, 0xad, 0x12, 0x34, 0xf4, 0x5e, 0x2f, 0xe2,
	0x09, 0xde, 0xd5, 0x53, 0x3f, 0xc0, 0x49, 0xcd, 0xa8, 0xb4, 0xc1, 0x30, 0x32, 0xb8, 0x6c, 0x17,
	0xc7, 0x61, 0x20, 0xa7, 0xfa, 0x31, 0x0b, 0x65, 0x0a, 0xdb, 0xe4, 0xf1, 0xaa, 0xdc, 0x72, 0x2e,
	0x3b, 0x3a, 0xdb, 0x7c, 0xe8, 0xb9, 0x27, 0x26, 0x19, 0xaf, 0xca, 0xeb, 0xb7, 0x2c, 0x5e, 0xab,
	0x30, 0x51, 0xcf, 0x66, 0xf0, 0xf9, 0x21, 0x34, 0x4d, 0xa8, 0xd6, 0xdf, 0x62, 0xca, 0xcb, 0xa0,
	0xfe, 0x16, 0x53, 0xe7, 0x36, 0x54, 0x06, 0x88, 0xc3, 0x81, 0xcf, 0x6a, 0x76, 0x74, 0x34, 0x95,
	0xea, 0x73, 0xff, 0x64, 0x09, 0x56, 0x34, 0x0c, 0x99, 0x86, 0xd0, 0x7e, 0x76, 0xc5, 0xaa, 0xd8,
	0x54, 0x51, 0x1c, 0x75, 0x04, 0x2a, 0x8b, 0xc4, 0xef, 0x7f, 0x08, 0xe2, 0x34, 0x12, 0x86, 0x86,
	0x84, 0x90, 0xfb, 0xa6, 0x2b, 0xd4, 0x8d, 0x2e, 0x08, 0x8e, 0x8e, 0xa5, 0x48, 0xa5, 0xf9, 0xce,
	0x07, 0x0a, 0x44, 0x76, 0x93, 0x82, 0xc4, 0xc8, 0x8f, 0xa7, 0x7d, 0xd4, 0xca, 0x79, 0x36, 0x15,
	0x41, 0xf7, 0x44, 0x4a, 0xae, 0xea, 0x20, 0x8c, 0xa3, 0x49, 0x92, 0x09, 0x0a, 0x9a, 0xe4, 0x72,
	0x77, 0x55, 0x41, 0xb5, 0x94, 0x7c, 0x03, 0xd6, 0xa3, 0x58, 0x26, 0x18, 0xa1, 0x6a, 0xbc, 0x1a,
	0xe1, 0xad, 0x31, 0x58, 0x23, 0xb6, 0xa0, 0x36, 0x38, 0x13, 0x7e, 0x20, 0x3d, 0xf6, 0x6d, 0x74,
	0x13, 0x79, 0x19, 0xc6, 0x43, 0xe4, 0x80, 0x72, 0x6b, 0x2a, 0x61, 0x3c, 0x54, 0x6f, 0xbc, 0x08,
	0xa6, 0xed, 0x81, 0xb6, 0xbf, 0x43, 0xda, 0xdd, 0x2d, 0x68, 0xfa, 0x49, 0x3f, 0x93, 0x58, 0x72,
	0x6e, 0x2a, 0xdd, 0x86, 0x9f, 0xec, 0x69, 0x10, 0xca, 0x8c, 0x9f, 0xf4, 0x07, 0xa8, 0x15, 0xa4,
	0xc7, 0xd9, 0xc8, 0xba, 0x9f, 0x1c, 0x28, 0x40, 0xc1, 0x4d, 0x59, 0x2d, 0x38, 0x78, 0xee, 0xbf,
	0x94, 0x01, 0xf0, 0x88, 0x94, 0x8b, 0x39, 0x23, 0xc0, 0xa5, 0x59, 0x01, 0x36, 0xbd, 0x85, 0xb2,
	0xe5, 0x2d, 0x2c, 0xb4, 0xc7, 0x64, 0xe6, 0xd4, 0x18, 0x33, 0x3b, 0xdc, 0x50, 0xe3, 0x54, 0x82,
	0x38, 0x7f, 0x9e, 0xc8, 0x0b, 0x2f, 0xf8, 0x79, 0x02, 0x7d, 0x94, 0xab, 0x94, 0x95, 0x24, 0x3f,
	0xa2, 0x9a, 0x3d, 0x8a, 0x76, 0xce, 0x3d, 0xe7, 0x36, 0xac, 0xea, 0x63, 0x51, 0x77, 0x4a, 0xf9,
	0x9a, 0x4d, 0x06, 0xaa, 0x7b, 0xf5, 0xae, 0x96, 0xd6, 0x15, 0xe3, 0x95, 0x80, 0x64, 0xbc, 0x23,
	0xa6, 0x2a, 0x8a, 0x4a, 0x58, 0x68, 0x2d, 0x9f, 0xb6, 0x6e, 0xfb, 0xb4, 0xb9, 0xa6, 0x06, 0x4b,
	0x53, 0x63, 0xc4, 0x32, 0x4e, 0x14, 0xc7, 0x1a, 0x1c, 0xb1, 0x8c, 0x13, 0xe4, 0x96, 0xfb, 0x00,
	0xd6, 0x0b, 0xeb, 0x2c, 0xbe, 0x08, 0x96, 0x6a, 0x28, 0xdb, 0xaa, 0xc1, 0xfd, 0xe7, 0x12, 0xac,
	0xf1, 0xcd, 0x3f, 0x9d, 0x04, 0xe8, 0x58, 0x18, 0x2f, 0x13, 0xd9, 0x81, 0x94, 0xcc, 0x97, 0x89,
	0xc7, 0x7c, 0x2c, 0xb7, 0xf4, 0x2b, 0x9c, 0x35, 0x75, 0x43, 0xc1, 0x14, 0x83, 0x8a, 0xe7, 0xbe,
	0x34, 0x7b, 0xee, 0x8b, 0x9c, 0x8c, 0x99, 0xc3, 0xad, 0xcc, 0x1e, 0xae, 0x29, 0x33, 0x55, 0xdb,
	0xc3, 0xec, 0xc1, 0x16, 0x6e, 0x8b, 0x9f, 0x24, 0x07, 0x21, 0xab, 0x58, 0x73, 0x44, 0xc9, 0x96,
	0x32, 0x5c, 0x2f, 0x92, 0x31, 0x5d, 0xf9, 0xdc, 0x95, 0x6d, 0x30, 0x8c, 0x32, 0xd5, 0x5f, 0xcc,
	0x9b, 0xf4, 0x55, 0x15, 0xe6, 0xcf, 0x59, 0xfe, 0x43, 0x2b, 0x93, 0x19, 0x63, 0x56, 0x43, 0x6f,
	0xfe, 0x57, 0x69, 0x66, 0x4d, 0x52, 0x7b, 0x28, 0xd7, 0x83, 0xc1, 0x84, 0x9f, 0xdd, 0x74, 0x8a,
	0x1c, 0x21, 0xfa, 0xd9, 0x4d, 0x75, 0xb3, 0x62, 0x60, 0xed, 0x47, 0xb0, 0xc7, 0xa4, 0x1d, 0x6e,
	0x03, 0x2b, 0xa2, 0x3e, 0x1a, 0x02, 0x7e, 0x10, 0x28, 0x77, 0x9b, 0x0a, 0x78, 0x40, 0xb0, 0x19,
	0x9e, 0xe8, 0x0b, 0x96, 0xf3, 0xc4, 0x14, 0xbb, 0xca, 0x45, 0xfa, 0xb7, 0x7a, 0xb1, 0xfe, 0xad,
	0xd9, 0xfa, 0xd7, 0x95, 0x4a, 0x2c, 0x8d, 0xa3, 0x5b, 0x28, 0xdf, 0xa4, 0x8a, 0x86, 0xb2, 0xef,
	0x07, 0x9e, 0x7c, 0xc6, 0xc7, 0x56, 0x47, 0xc8, 0x21, 0x02, 0x94, 0xf8, 0x0f, 0x65, 0x3f, 0xf1,
	0x9f, 0xcb, 0xdc, 0x32, 0x0e, 0x65, 0xcf, 0x7f, 0x2e, 0xdd, 0xdf, 0x2e, 0x15, 0xd6, 0x79, 0xd5,
	0xd3, 0xfc, 0x86, 0x75, 0x9a, 0x5b, 0xd9, 0x69, 0x16, 0x0f, 0x12, 0xa9, 0xc4, 0xdf, 0xfe, 0xc0,
	0x8c, 0x88, 0x11, 0x72, 0x40, 0x0a, 0xf3, 0xcf, 0x97, 0x4d, 0x42, 0xbe, 0xa2, 0x65, 0xa3, 0xe7,
	0x40, 0x9c, 0x47, 0xa3, 0xa8, 0x7b, 0xd7, 0x54, 0x40, 0x46, 0x62, 0x0d, 0xce, 0x18, 0x79, 0xa0,
	0x99, 0x77, 0x1b, 0x02, 0x56, 0x79, 0x91, 0x80, 0x55, 0x67, 0x04, 0xec, 0x7d, 0xb8, 0xa2, 0xed,
	0x9f, 0x3c, 0x0d, 0x63, 0xa9, 0xe5, 0x4c, 0x59, 0x37, 0x87, 0xad, 0x20, 0x75, 0xb1, 0xb4, 0xcd,
	0x88, 0xe4, 0xca, 0x1c, 0x91, 0xbc, 0x03, 0x5b, 0x8c, 0x24, 0x4e, 0x53, 0x19, 0x6b, 0xd4, 0x3a,
	0xa1, 0x6e, 0xaa, 0xae, 0x3d, 0xec, 0x61, 0xfc, 0xbb, 0xb0, 0x9d, 0xd9, 0x57, 0x8b, 0x0e, 0xa0,
	0x11, 0x5b, 0xda, 0xca, 0x9a, 0x84, 0xbc, 0x05, 0xda, 0xf8, 0x6a, 0xe4, 0x86, 0x32, 0xdd, 0x0c,
	0x65, 0xb4, 0xf7, 0xe1, 0x8a, 0x46, 0xb3, 0x68, 0x69, 0xaa, 0x1d, 0x72, 0x9f, 0x49, 0x4c, 0xf1,
	0x3e, 0xad, 0xcd, 0xe8, 0x18, 0xe7, 0x4d, 0x58, 0xcb, 0x19, 0x4b, 0x57, 0x43, 0x55, 0x48, 0x36,
	0x35, 0x6b, 0xe9, 0x7a, 0x7c, 0x0b, 0x56, 0x3b, 0x62, 0xda, 0xcb, 0xcb, 0x33, 0x6e, 0xb3, 0x1c,
	0x96, 0x48, 0x0e, 0xd7, 0x75, 0x54, 0xd2, 0x93, 0xa9, 0xa1, 0x4c, 0xbe, 0x0d, 0x90, 0xc3, 0x66,
	0xe2, 0x12, 0x23, 0xf0, 0x28, 0x5b, 0x41, 0xd3, 0xef, 0x96, 0x60, 0x9d, 0xec, 0x3e, 0x97, 0x58,
	0xbe, 0xa4, 0x45, 0xbf, 0xc8, 0xee, 0x7c, 0x15, 0x9b, 0xee, 0xca, 0x22, 0x39, 0xaf, 0x7a, 0x69,
	0xdf, 0xb4, 0x7c, 0xd6, 0x0d, 0xcd, 0x2c, 0x9c, 0xcc, 0xe0, 0x56, 0x07, 0x1a, 0x06, 0xd0, 0x32,
	0xe0, 0x25, 0xdb, 0x80, 0xbf, 0x0d, 0xeb, 0x7e, 0xd2, 0x7f, 0x3a, 0x19, 0x47, 0xfd, 0x81, 0x48,
	0xce, 0x7c, 0xbe, 0x94, 0x2b, 0xdd, 0x55, 0x3f, 0x79, 0x38, 0x19, 0x47, 0x07, 0x0a, 0xe8, 0x1e,
	0x43, 0x4b, 0x97, 0x56, 0xec, 0x4f, 0xb9, 0xec, 0xe6, 0x25, 0x8a, 0xfc, 0x76, 0x01, 0xc4, 0x68,
	0xa4, 0xdf, 0xff, 0xd5, 0xcc, 0x75, 0x31, 0x1a, 0xa9, 0x27, 0x3c, 0xf7, 0x9f, 0xca, 0x70, 0x6d,
	0xce, 0xb4, 0x2f, 0x2e, 0xdd, 0x30, 0x53, 0x3f, 0x65, 0xfb, 0x15, 0x8f, 0x5f, 0x74, 0x8a, 0x65,
	0x1b, 0x61, 0x5e, 0x2a, 0xfd, 0xf5, 0x94, 0x6d, 0xd8, 0x35, 0x06, 0x95, 0xac, 0x2e, 0xe3, 0x1a,
	0x65, 0xf8, 0x95, 0x2e, 0x62, 0xef, 0x57, 0x78, 0xde, 0x4c, 0xc5, 0x9d, 0xe1, 0x58, 0xf9, 0x85,
	0x3c, 0x22, 0xd8, 0x47, 0x56, 0x28, 0x9c, 0x6c, 0x14, 0x0b, 0x27, 0xdd, 0x7f, 0x2c, 0xc3, 0xb5,
	0x5e, 0x2a, 0x02, 0x4f, 0x79, 0xb1, 0x85, 0xe7, 0xd2, 0x0b, 0xf2, 0xa5, 0xb6, 0x67, 0xac, 0xea,
	0x97, 0x8c, 0x04, 0xde, 0x4c, 0xa9, 0x86, 0x55, 0xbc, 0xc4, 0x48, 0x37, 0xa1, 0x99, 0x9e, 0xf9,
	0xb1, 0xd7, 0xe7, 0x64, 0x1e, 0x87, 0x1f, 0x04, 0xeb, 0x50, 0x46, 0xef, 0x6d, 0x58, 0x57, 0x18,
	0x3c, 0x59, 0xa0, 0xeb, 0x1f, 0x56, 0x09, 0xac, 0xbc, 0x39, 0xeb, 0x81, 0xba, 0x6a, 0x15, 0xba,
	0x5c, 0x90, 0x11, 0xbc, 0x01, 0x0d, 0x5d, 0x99, 0xe2, 0x67, 0xa5, 0x0f, 0xc0, 0x55, 0x29, 0xc5,
	0x94, 0x61, 0x7d, 0xe6, 0xf5, 0x57, 0x47, 0x9a, 0x41, 0xc8, 0xee, 0x6c, 0x5e, 0xd7, 0xe4, 0xfe,
	0xb8, 0x64, 0x31, 0x55, 0xfb, 0x9c, 0xcc, 0xd4, 0x19, 0xd6, 0x94, 0xe6, 0xb0, 0xe6, 0x65, 0xeb,
	0x66, 0xe6, 0x30, 0x68, 0x69, 0x01, 0x83, 0x38, 0x99, 0xca, 0xee, 0xa7, 0x6a, 0xa9, 0x67, 0x20,
	0xfc, 0xea, 0x8f, 0x93, 0xa1, 0xb6, 0x7f, 0x0a, 0xf2, 0x28, 0x19, 0x5e, 0x54, 0xcc, 0xf9, 0x07,
	0x65, 0xd8, 0xb2, 0x36, 0xf9, 0x95, 0x65, 0xa6, 0x28, 0x0e, 0x4b, 0x33, 0xe2, 0xb0, 0x68, 0x17,
	0x97, 0x2a, 0x47, 0x35, 0x4b, 0xc5, 0x6b, 0xaf, 0x54, 0x2a, 0xbe, 0xcb, 0x95, 0x72, 0xea, 0xca,
	0xa9, 0x8b, 0xaa, 0x8a, 0x6c, 0xc9, 0x7f, 0xfe, 0x26, 0xac, 0x3d, 0x4c, 0xc2, 0xa0, 0x97, 0x66,
	0x7f, 0xd7, 0xb8, 0x0e, 0xf5, 0xa7, 0x49, 0x18, 0xf4, 0x4f, 0xa6, 0x5c, 0x59, 0xd7, 0xec, 0xae,
	0x20, 0x60, 0x7f, 0x9a, 0x4a, 0xf7, 0x0e, 0xac, 0x67, 0xe8, 0xcc, 0xc3, 0x0b, 0xf1, 0xfb, 0x70,
	0xd5, 0xe0, 0xfb, 0xc1, 0x28, 0x4c, 0xb2, 0x64, 0x58, 0xfe, 0xd2, 0x5b, 0x2a, 0x94, 0x41, 0x2f,
	0xd2, 0x80, 0x0e, 0x2c, 0x27, 0x46, 0x6d, 0x1c, 0x7e, 0xdf, 0xfd, 0xb7, 0x35, 0xa8, 0x71, 0x81,
	0xb2, 0x73, 0x1f, 0x9a, 0x26, 0x3f, 0x9c, 0xd6, 0x1c, 0x16, 0xd1, 0xda, 0xed, 0x6b, 0x0b, 0x99,
	0xe7, 0xbe, 0xe6, 0xfc, 0x0a, 0x19, 0xf2, 0xbc, 0xe6, 0xd0, 0xb9, 0xa6, 0xad, 0xd1, 0x4c, 0x1d,
	0x62, 0x5b, 0x25, 0x57, 0xe8, 0xed, 0x3d, 0x9f, 0xe0, 0xbb, 0xf4, 0x97, 0x15, 0x52, 0xfe, 0x4e,
	0x56, 0xa6, 0x66, 0xd6, 0x07, 0xb6, 0xb7, 0x0b, 0xd0, 0x6c, 0xe8, 0x31, 0x25, 0x43, 0x6d, 0xbb,
	0xe1, 0xec, 0x5a, 0xd8, 0x45, 0x33, 0xd5, 0x7e, 0x63, 0x51, 0x77, 0x36, 0xeb, 0x7d, 0x68, 0x9a,
	0x45, 0xdc, 0xcc, 0x98, 0x39, 0x75, 0xed, 0xed, 0x6b, 0x0b, 0x2b, 0xbe, 0x89, 0x31, 0x90, 0x17,
	0x3c, 0x39, 0xaa, 0x7e, 0x7e, 0xa6, 0xca, 0xac, 0x7d, 0x75, 0x06, 0x9e, 0x4d, 0xf0, 0xcb, 0xd0,
	0x34, 0x2b, 0x9a, 0x98, 0x8e, 0x39, 0x45, 0x4e, 0x0b, 0xf9, 0xaa, 0x2b, 0x8a, 0x98, 0xaf, 0x85,
	0x22, 0x25, 0xe6, 0x6b, 0xb1, 0xec, 0xc8, 0x7d, 0xcd, 0xf9, 0x25, 0xca, 0xfe, 0xaa, 0xd7, 0x44,
	0x27, 0xe3, 0xbe, 0x55, 0x86, 0xd2, 0xde, 0x29, 0x82, 0xb3, 0xd1, 0x1f, 0xc3, 0x9a, 0x5d, 0xa3,
	0xe3, 0xb4, 0xf3, 0x85, 0x8a, 0xe5, 0x44, 0xed, 0xeb, 0x73, 0xfb, 0x66, 0x76, 0xd1, 0x39, 0xf7,
	0xcc, 0x5d, 0xe4, 0x4f, 0x08, 0xe6, 0x2e, 0x8c, 0xd7, 0x18, 0xf7, 0x35, 0xe7, 0xe7, 0xa1, 0xaa,
	0x9e, 0x52, 0x1c, 0x55, 0xb5, 0x6c, 0xbd, 0xab, 0xcc, 0xe7, 0xd9, 0x1e, 0xac, 0xd9, 0xef, 0x23,
	0x4c, 0xfa, 0xdc, 0x47, 0x93, 0xf9, 0x53, 0xfc, 0x22, 0x34, 0x8c, 0x77, 0x0e, 0xe7, 0xaa, 0x35,
	0xfe, 0x45, 0xeb, 0x7f, 0x87, 0xae, 0xe7, 0x27, 0x7e, 0x92, 0x2e, 0xd8, 0x6c, 0x76, 0x41, 0xcc,
	0xb7, 0x0b, 0x5a, 0xd5, 0xc8, 0xd7, 0xcf, 0x1f, 0xba, 0x53, 0x48, 0xf4, 0x5b, 0x8c, 0x52, 0x5e,
	0x35, 0x33, 0xca, 0x72, 0xcc, 0xe7, 0x13, 0x7a, 0x1f, 0xd6, 0x0b, 0x49, 0x6e, 0xde, 0xe9, 0x6c,
	0xa6, 0xbc, 0xbd, 0xa0, 0x23, 0x71, 0x5f, 0x73, 0xde, 0x87, 0x15, 0x0d, 0x77, 0x36, 0x2c, 0x34,
	0x1c, 0x58, 0x84, 0xe0, 0x88, 0xf7, 0xa0, 0xc6, 0x69, 0x39, 0x67, 0x3d, 0x8b, 0x55, 0x55, 0x92,
	0x6e, 0x3e, 0xa5, 0x1f, 0xaa, 0x3c, 0x9e, 0x32, 0x97, 0x4e, 0x1e, 0xdf, 0xe6, 0xf9, 0xa2, 0xf9,
	0xe3, 0xbe, 0xaf, 0xf2, 0xea, 0x66, 0xaa, 0xc4, 0x99, 0x9b, 0xeb, 0x20, 0x52, 0x17, 0xf5, 0x24,
	0x24, 0xc2, 0x0d, 0x23, 0x42, 0x77, 0x8a, 0x21, 0x36, 0x8d, 0x9f, 0x03, 0x4c, 0xe8, 0x22, 0x36,
	0xcd, 0x40, 0x81, 0x4f, 0xb6, 0x10, 0xca, 0xb4, 0xe7, 0x41, 0x71, 0xf4, 0xf7, 0xa0, 0x61, 0x98,
	0x13, 0x5e, 0xd8, 0xb6, 0x5f, 0x6d, 0xad, 0x54, 0x66, 0xac, 0xbd, 0xfb, 0x9a, 0xf3, 0x10, 0x36,
	0x67, 0x7c, 0x9d, 0xf9, 0xb3, 0xbc, 0x31, 0x3b, 0x8b, 0xe9, 0x18, 0xcd, 0xcc, 0xc5, 0x7a, 0xe1,
	0xe5, 0xe6, 0x9a, 0xd1, 0x09, 0xf7, 0x61, 0xa3, 0x68, 0x26, 0x9d, 0xd7, 0x8b, 0xa3, 0x4c, 0xeb,
	0x39, 0xff, 0x84, 0x7f, 0x08, 0x5b, 0x73, 0xaa, 0xfe, 0x9d, 0x1b, 0x8c, 0xbb, 0xe8, 0x8f, 0x04,
	0xed, 0x9b, 0x8b, 0x11, 0xf4, 0xdc, 0x27, 0x55, 0xfa, 0x93, 0xe7, 0x07, 0xff, 0x1b, 0x00, 0x00,
	0xff, 0xff, 0xba, 0x66, 0x57, 0x69, 0x2f, 0x3a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PayInfoClient is the client API for PayInfo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PayInfoClient interface {
	//统一订单
	UnifiedOrder(ctx context.Context, in *UnifiedOrderRequest, opts ...grpc.CallOption) (*UnifiedOrderResponse, error)
	//支付异步回调
	PayAsynNotice(ctx context.Context, in *PayAsynNoticeRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// @Desc			    支付查询
	// <AUTHOR>
	// @Date		 		2020-06-24
	PayQuery(ctx context.Context, in *PayQueryRequest, opts ...grpc.CallOption) (*PayQueryResponse, error)
	PayQueryByOrderId(ctx context.Context, in *PayQueryByOrderIdRequest, opts ...grpc.CallOption) (*PayQueryByOrderIdResponse, error)
	PayInfoQuery(ctx context.Context, in *PayInfoQueryRequest, opts ...grpc.CallOption) (*PayInfoQueryResponse, error)
	// 标准终端绑定
	StdBinding(ctx context.Context, in *StdBindingRequest, opts ...grpc.CallOption) (*StdBindingResponse, error)
	// 标准终端解绑
	StdUnBinding(ctx context.Context, in *StdUnBindingRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 终端状态查询
	QuerySts(ctx context.Context, in *QueryStsRequest, opts ...grpc.CallOption) (*QueryStsResponse, error)
	// 发起支付B2C
	PayForB2C(ctx context.Context, in *PayForB2CRequest, opts ...grpc.CallOption) (*PayForB2CResponse, error)
	// 支付状态查询
	QueryPayStatus(ctx context.Context, in *QueryPayStatusRequest, opts ...grpc.CallOption) (*QueryPayStatusResponse, error)
	// 查询是否设置过支付密码
	QueryPwd(ctx context.Context, in *QueryPwdRequest, opts ...grpc.CallOption) (*QueryPwdResponse, error)
	// 设置支付密码-首次
	SetPwd(ctx context.Context, in *SetPwdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 通过手机验证码修改支付密码
	UpdateByMobile(ctx context.Context, in *UpdateByMobileRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 通过原密码修改支付密码
	UpdateByPwd(ctx context.Context, in *UpdateByPwdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取支付方式列表-小程序
	PayList(ctx context.Context, in *QueryPwdRequest, opts ...grpc.CallOption) (*PayListResponse, error)
	// 获取支付方式配置
	PayConfig(ctx context.Context, in *QueryPwdRequest, opts ...grpc.CallOption) (*PayConfigResponse, error)
	// 支付设置
	PaySet(ctx context.Context, in *PaySetRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取储蓄卡余额
	GetCardsBalance(ctx context.Context, in *GetCardsBalanceReq, opts ...grpc.CallOption) (*GetCardsBalanceRes, error)
	//获取储蓄卡列表
	GetCards(ctx context.Context, in *GetCardsReq, opts ...grpc.CallOption) (*GetCardsRes, error)
	//储蓄卡支付
	CardPay(ctx context.Context, in *CardPayReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//储蓄卡退款
	CardRefund(ctx context.Context, in *CardsRefundReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//储蓄卡订单交易记录
	CardOrderRecords(ctx context.Context, in *CardOrderRecordsReq, opts ...grpc.CallOption) (*CardOrderRecordsRes, error)
	//储蓄卡交易流水明细
	CardRecords(ctx context.Context, in *CardRecordsReq, opts ...grpc.CallOption) (*CardRecordsRes, error)
	//选定支付方式(默认储蓄卡支付)
	CheckPayType(ctx context.Context, in *CheckPayTypeReq, opts ...grpc.CallOption) (*CheckPayTypeRes, error)
	// 支付中心-标准支付统一入口
	StandardPay(ctx context.Context, in *JsonStrRequest, opts ...grpc.CallOption) (*StandardPayResponse, error)
	// 退款
	StandardPayRefund(ctx context.Context, in *JsonStrRequest, opts ...grpc.CallOption) (*StandardPayRefundResponse, error)
	// 状态查询
	StandardPayStatus(ctx context.Context, in *JsonStrRequest, opts ...grpc.CallOption) (*StandardPayStatusResponse, error)
	// 关闭支付
	StandardPayClose(ctx context.Context, in *StandardPayCloseRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 百度支付退款查询
	BaiduOrderRefundGet(ctx context.Context, in *BaiduOrderRefundGetRequest, opts ...grpc.CallOption) (*BaiduOrderRefundGetResponse, error)
}

type payInfoClient struct {
	cc *grpc.ClientConn
}

func NewPayInfoClient(cc *grpc.ClientConn) PayInfoClient {
	return &payInfoClient{cc}
}

func (c *payInfoClient) UnifiedOrder(ctx context.Context, in *UnifiedOrderRequest, opts ...grpc.CallOption) (*UnifiedOrderResponse, error) {
	out := new(UnifiedOrderResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/UnifiedOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) PayAsynNotice(ctx context.Context, in *PayAsynNoticeRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/PayAsynNotice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) PayQuery(ctx context.Context, in *PayQueryRequest, opts ...grpc.CallOption) (*PayQueryResponse, error) {
	out := new(PayQueryResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/PayQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) PayQueryByOrderId(ctx context.Context, in *PayQueryByOrderIdRequest, opts ...grpc.CallOption) (*PayQueryByOrderIdResponse, error) {
	out := new(PayQueryByOrderIdResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/PayQueryByOrderId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) PayInfoQuery(ctx context.Context, in *PayInfoQueryRequest, opts ...grpc.CallOption) (*PayInfoQueryResponse, error) {
	out := new(PayInfoQueryResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/PayInfoQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) StdBinding(ctx context.Context, in *StdBindingRequest, opts ...grpc.CallOption) (*StdBindingResponse, error) {
	out := new(StdBindingResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/StdBinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) StdUnBinding(ctx context.Context, in *StdUnBindingRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/StdUnBinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) QuerySts(ctx context.Context, in *QueryStsRequest, opts ...grpc.CallOption) (*QueryStsResponse, error) {
	out := new(QueryStsResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/QuerySts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) PayForB2C(ctx context.Context, in *PayForB2CRequest, opts ...grpc.CallOption) (*PayForB2CResponse, error) {
	out := new(PayForB2CResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/PayForB2C", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) QueryPayStatus(ctx context.Context, in *QueryPayStatusRequest, opts ...grpc.CallOption) (*QueryPayStatusResponse, error) {
	out := new(QueryPayStatusResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/QueryPayStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) QueryPwd(ctx context.Context, in *QueryPwdRequest, opts ...grpc.CallOption) (*QueryPwdResponse, error) {
	out := new(QueryPwdResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/QueryPwd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) SetPwd(ctx context.Context, in *SetPwdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/SetPwd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) UpdateByMobile(ctx context.Context, in *UpdateByMobileRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/UpdateByMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) UpdateByPwd(ctx context.Context, in *UpdateByPwdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/UpdateByPwd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) PayList(ctx context.Context, in *QueryPwdRequest, opts ...grpc.CallOption) (*PayListResponse, error) {
	out := new(PayListResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/PayList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) PayConfig(ctx context.Context, in *QueryPwdRequest, opts ...grpc.CallOption) (*PayConfigResponse, error) {
	out := new(PayConfigResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/PayConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) PaySet(ctx context.Context, in *PaySetRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/PaySet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) GetCardsBalance(ctx context.Context, in *GetCardsBalanceReq, opts ...grpc.CallOption) (*GetCardsBalanceRes, error) {
	out := new(GetCardsBalanceRes)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/GetCardsBalance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) GetCards(ctx context.Context, in *GetCardsReq, opts ...grpc.CallOption) (*GetCardsRes, error) {
	out := new(GetCardsRes)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/GetCards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) CardPay(ctx context.Context, in *CardPayReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/CardPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) CardRefund(ctx context.Context, in *CardsRefundReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/CardRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) CardOrderRecords(ctx context.Context, in *CardOrderRecordsReq, opts ...grpc.CallOption) (*CardOrderRecordsRes, error) {
	out := new(CardOrderRecordsRes)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/CardOrderRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) CardRecords(ctx context.Context, in *CardRecordsReq, opts ...grpc.CallOption) (*CardRecordsRes, error) {
	out := new(CardRecordsRes)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/CardRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) CheckPayType(ctx context.Context, in *CheckPayTypeReq, opts ...grpc.CallOption) (*CheckPayTypeRes, error) {
	out := new(CheckPayTypeRes)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/CheckPayType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) StandardPay(ctx context.Context, in *JsonStrRequest, opts ...grpc.CallOption) (*StandardPayResponse, error) {
	out := new(StandardPayResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/StandardPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) StandardPayRefund(ctx context.Context, in *JsonStrRequest, opts ...grpc.CallOption) (*StandardPayRefundResponse, error) {
	out := new(StandardPayRefundResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/StandardPayRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) StandardPayStatus(ctx context.Context, in *JsonStrRequest, opts ...grpc.CallOption) (*StandardPayStatusResponse, error) {
	out := new(StandardPayStatusResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/StandardPayStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) StandardPayClose(ctx context.Context, in *StandardPayCloseRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/StandardPayClose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payInfoClient) BaiduOrderRefundGet(ctx context.Context, in *BaiduOrderRefundGetRequest, opts ...grpc.CallOption) (*BaiduOrderRefundGetResponse, error) {
	out := new(BaiduOrderRefundGetResponse)
	err := c.cc.Invoke(ctx, "/pay.PayInfo/BaiduOrderRefundGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PayInfoServer is the server API for PayInfo service.
type PayInfoServer interface {
	//统一订单
	UnifiedOrder(context.Context, *UnifiedOrderRequest) (*UnifiedOrderResponse, error)
	//支付异步回调
	PayAsynNotice(context.Context, *PayAsynNoticeRequest) (*BaseResponse, error)
	// @Desc			    支付查询
	// <AUTHOR>
	// @Date		 		2020-06-24
	PayQuery(context.Context, *PayQueryRequest) (*PayQueryResponse, error)
	PayQueryByOrderId(context.Context, *PayQueryByOrderIdRequest) (*PayQueryByOrderIdResponse, error)
	PayInfoQuery(context.Context, *PayInfoQueryRequest) (*PayInfoQueryResponse, error)
	// 标准终端绑定
	StdBinding(context.Context, *StdBindingRequest) (*StdBindingResponse, error)
	// 标准终端解绑
	StdUnBinding(context.Context, *StdUnBindingRequest) (*BaseResponse, error)
	// 终端状态查询
	QuerySts(context.Context, *QueryStsRequest) (*QueryStsResponse, error)
	// 发起支付B2C
	PayForB2C(context.Context, *PayForB2CRequest) (*PayForB2CResponse, error)
	// 支付状态查询
	QueryPayStatus(context.Context, *QueryPayStatusRequest) (*QueryPayStatusResponse, error)
	// 查询是否设置过支付密码
	QueryPwd(context.Context, *QueryPwdRequest) (*QueryPwdResponse, error)
	// 设置支付密码-首次
	SetPwd(context.Context, *SetPwdRequest) (*BaseResponse, error)
	// 通过手机验证码修改支付密码
	UpdateByMobile(context.Context, *UpdateByMobileRequest) (*BaseResponse, error)
	// 通过原密码修改支付密码
	UpdateByPwd(context.Context, *UpdateByPwdRequest) (*BaseResponse, error)
	// 获取支付方式列表-小程序
	PayList(context.Context, *QueryPwdRequest) (*PayListResponse, error)
	// 获取支付方式配置
	PayConfig(context.Context, *QueryPwdRequest) (*PayConfigResponse, error)
	// 支付设置
	PaySet(context.Context, *PaySetRequest) (*BaseResponse, error)
	//获取储蓄卡余额
	GetCardsBalance(context.Context, *GetCardsBalanceReq) (*GetCardsBalanceRes, error)
	//获取储蓄卡列表
	GetCards(context.Context, *GetCardsReq) (*GetCardsRes, error)
	//储蓄卡支付
	CardPay(context.Context, *CardPayReq) (*BaseResponse, error)
	//储蓄卡退款
	CardRefund(context.Context, *CardsRefundReq) (*BaseResponse, error)
	//储蓄卡订单交易记录
	CardOrderRecords(context.Context, *CardOrderRecordsReq) (*CardOrderRecordsRes, error)
	//储蓄卡交易流水明细
	CardRecords(context.Context, *CardRecordsReq) (*CardRecordsRes, error)
	//选定支付方式(默认储蓄卡支付)
	CheckPayType(context.Context, *CheckPayTypeReq) (*CheckPayTypeRes, error)
	// 支付中心-标准支付统一入口
	StandardPay(context.Context, *JsonStrRequest) (*StandardPayResponse, error)
	// 退款
	StandardPayRefund(context.Context, *JsonStrRequest) (*StandardPayRefundResponse, error)
	// 状态查询
	StandardPayStatus(context.Context, *JsonStrRequest) (*StandardPayStatusResponse, error)
	// 关闭支付
	StandardPayClose(context.Context, *StandardPayCloseRequest) (*BaseResponse, error)
	// 百度支付退款查询
	BaiduOrderRefundGet(context.Context, *BaiduOrderRefundGetRequest) (*BaiduOrderRefundGetResponse, error)
}

// UnimplementedPayInfoServer can be embedded to have forward compatible implementations.
type UnimplementedPayInfoServer struct {
}

func (*UnimplementedPayInfoServer) UnifiedOrder(ctx context.Context, req *UnifiedOrderRequest) (*UnifiedOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnifiedOrder not implemented")
}
func (*UnimplementedPayInfoServer) PayAsynNotice(ctx context.Context, req *PayAsynNoticeRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayAsynNotice not implemented")
}
func (*UnimplementedPayInfoServer) PayQuery(ctx context.Context, req *PayQueryRequest) (*PayQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayQuery not implemented")
}
func (*UnimplementedPayInfoServer) PayQueryByOrderId(ctx context.Context, req *PayQueryByOrderIdRequest) (*PayQueryByOrderIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayQueryByOrderId not implemented")
}
func (*UnimplementedPayInfoServer) PayInfoQuery(ctx context.Context, req *PayInfoQueryRequest) (*PayInfoQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayInfoQuery not implemented")
}
func (*UnimplementedPayInfoServer) StdBinding(ctx context.Context, req *StdBindingRequest) (*StdBindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StdBinding not implemented")
}
func (*UnimplementedPayInfoServer) StdUnBinding(ctx context.Context, req *StdUnBindingRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StdUnBinding not implemented")
}
func (*UnimplementedPayInfoServer) QuerySts(ctx context.Context, req *QueryStsRequest) (*QueryStsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySts not implemented")
}
func (*UnimplementedPayInfoServer) PayForB2C(ctx context.Context, req *PayForB2CRequest) (*PayForB2CResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayForB2C not implemented")
}
func (*UnimplementedPayInfoServer) QueryPayStatus(ctx context.Context, req *QueryPayStatusRequest) (*QueryPayStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPayStatus not implemented")
}
func (*UnimplementedPayInfoServer) QueryPwd(ctx context.Context, req *QueryPwdRequest) (*QueryPwdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPwd not implemented")
}
func (*UnimplementedPayInfoServer) SetPwd(ctx context.Context, req *SetPwdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPwd not implemented")
}
func (*UnimplementedPayInfoServer) UpdateByMobile(ctx context.Context, req *UpdateByMobileRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateByMobile not implemented")
}
func (*UnimplementedPayInfoServer) UpdateByPwd(ctx context.Context, req *UpdateByPwdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateByPwd not implemented")
}
func (*UnimplementedPayInfoServer) PayList(ctx context.Context, req *QueryPwdRequest) (*PayListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayList not implemented")
}
func (*UnimplementedPayInfoServer) PayConfig(ctx context.Context, req *QueryPwdRequest) (*PayConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayConfig not implemented")
}
func (*UnimplementedPayInfoServer) PaySet(ctx context.Context, req *PaySetRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaySet not implemented")
}
func (*UnimplementedPayInfoServer) GetCardsBalance(ctx context.Context, req *GetCardsBalanceReq) (*GetCardsBalanceRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardsBalance not implemented")
}
func (*UnimplementedPayInfoServer) GetCards(ctx context.Context, req *GetCardsReq) (*GetCardsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCards not implemented")
}
func (*UnimplementedPayInfoServer) CardPay(ctx context.Context, req *CardPayReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CardPay not implemented")
}
func (*UnimplementedPayInfoServer) CardRefund(ctx context.Context, req *CardsRefundReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CardRefund not implemented")
}
func (*UnimplementedPayInfoServer) CardOrderRecords(ctx context.Context, req *CardOrderRecordsReq) (*CardOrderRecordsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CardOrderRecords not implemented")
}
func (*UnimplementedPayInfoServer) CardRecords(ctx context.Context, req *CardRecordsReq) (*CardRecordsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CardRecords not implemented")
}
func (*UnimplementedPayInfoServer) CheckPayType(ctx context.Context, req *CheckPayTypeReq) (*CheckPayTypeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPayType not implemented")
}
func (*UnimplementedPayInfoServer) StandardPay(ctx context.Context, req *JsonStrRequest) (*StandardPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StandardPay not implemented")
}
func (*UnimplementedPayInfoServer) StandardPayRefund(ctx context.Context, req *JsonStrRequest) (*StandardPayRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StandardPayRefund not implemented")
}
func (*UnimplementedPayInfoServer) StandardPayStatus(ctx context.Context, req *JsonStrRequest) (*StandardPayStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StandardPayStatus not implemented")
}
func (*UnimplementedPayInfoServer) StandardPayClose(ctx context.Context, req *StandardPayCloseRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StandardPayClose not implemented")
}
func (*UnimplementedPayInfoServer) BaiduOrderRefundGet(ctx context.Context, req *BaiduOrderRefundGetRequest) (*BaiduOrderRefundGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BaiduOrderRefundGet not implemented")
}

func RegisterPayInfoServer(s *grpc.Server, srv PayInfoServer) {
	s.RegisterService(&_PayInfo_serviceDesc, srv)
}

func _PayInfo_UnifiedOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnifiedOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).UnifiedOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/UnifiedOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).UnifiedOrder(ctx, req.(*UnifiedOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_PayAsynNotice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayAsynNoticeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).PayAsynNotice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/PayAsynNotice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).PayAsynNotice(ctx, req.(*PayAsynNoticeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_PayQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).PayQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/PayQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).PayQuery(ctx, req.(*PayQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_PayQueryByOrderId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayQueryByOrderIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).PayQueryByOrderId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/PayQueryByOrderId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).PayQueryByOrderId(ctx, req.(*PayQueryByOrderIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_PayInfoQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayInfoQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).PayInfoQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/PayInfoQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).PayInfoQuery(ctx, req.(*PayInfoQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_StdBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StdBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).StdBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/StdBinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).StdBinding(ctx, req.(*StdBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_StdUnBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StdUnBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).StdUnBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/StdUnBinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).StdUnBinding(ctx, req.(*StdUnBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_QuerySts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).QuerySts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/QuerySts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).QuerySts(ctx, req.(*QueryStsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_PayForB2C_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayForB2CRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).PayForB2C(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/PayForB2C",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).PayForB2C(ctx, req.(*PayForB2CRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_QueryPayStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPayStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).QueryPayStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/QueryPayStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).QueryPayStatus(ctx, req.(*QueryPayStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_QueryPwd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPwdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).QueryPwd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/QueryPwd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).QueryPwd(ctx, req.(*QueryPwdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_SetPwd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPwdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).SetPwd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/SetPwd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).SetPwd(ctx, req.(*SetPwdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_UpdateByMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateByMobileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).UpdateByMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/UpdateByMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).UpdateByMobile(ctx, req.(*UpdateByMobileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_UpdateByPwd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateByPwdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).UpdateByPwd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/UpdateByPwd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).UpdateByPwd(ctx, req.(*UpdateByPwdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_PayList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPwdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).PayList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/PayList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).PayList(ctx, req.(*QueryPwdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_PayConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPwdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).PayConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/PayConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).PayConfig(ctx, req.(*QueryPwdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_PaySet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PaySetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).PaySet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/PaySet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).PaySet(ctx, req.(*PaySetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_GetCardsBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardsBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).GetCardsBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/GetCardsBalance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).GetCardsBalance(ctx, req.(*GetCardsBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_GetCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).GetCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/GetCards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).GetCards(ctx, req.(*GetCardsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_CardPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardPayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).CardPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/CardPay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).CardPay(ctx, req.(*CardPayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_CardRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardsRefundReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).CardRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/CardRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).CardRefund(ctx, req.(*CardsRefundReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_CardOrderRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardOrderRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).CardOrderRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/CardOrderRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).CardOrderRecords(ctx, req.(*CardOrderRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_CardRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).CardRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/CardRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).CardRecords(ctx, req.(*CardRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_CheckPayType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPayTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).CheckPayType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/CheckPayType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).CheckPayType(ctx, req.(*CheckPayTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_StandardPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonStrRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).StandardPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/StandardPay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).StandardPay(ctx, req.(*JsonStrRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_StandardPayRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonStrRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).StandardPayRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/StandardPayRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).StandardPayRefund(ctx, req.(*JsonStrRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_StandardPayStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonStrRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).StandardPayStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/StandardPayStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).StandardPayStatus(ctx, req.(*JsonStrRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_StandardPayClose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StandardPayCloseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).StandardPayClose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/StandardPayClose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).StandardPayClose(ctx, req.(*StandardPayCloseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayInfo_BaiduOrderRefundGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaiduOrderRefundGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayInfoServer).BaiduOrderRefundGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pay.PayInfo/BaiduOrderRefundGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayInfoServer).BaiduOrderRefundGet(ctx, req.(*BaiduOrderRefundGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PayInfo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pay.PayInfo",
	HandlerType: (*PayInfoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UnifiedOrder",
			Handler:    _PayInfo_UnifiedOrder_Handler,
		},
		{
			MethodName: "PayAsynNotice",
			Handler:    _PayInfo_PayAsynNotice_Handler,
		},
		{
			MethodName: "PayQuery",
			Handler:    _PayInfo_PayQuery_Handler,
		},
		{
			MethodName: "PayQueryByOrderId",
			Handler:    _PayInfo_PayQueryByOrderId_Handler,
		},
		{
			MethodName: "PayInfoQuery",
			Handler:    _PayInfo_PayInfoQuery_Handler,
		},
		{
			MethodName: "StdBinding",
			Handler:    _PayInfo_StdBinding_Handler,
		},
		{
			MethodName: "StdUnBinding",
			Handler:    _PayInfo_StdUnBinding_Handler,
		},
		{
			MethodName: "QuerySts",
			Handler:    _PayInfo_QuerySts_Handler,
		},
		{
			MethodName: "PayForB2C",
			Handler:    _PayInfo_PayForB2C_Handler,
		},
		{
			MethodName: "QueryPayStatus",
			Handler:    _PayInfo_QueryPayStatus_Handler,
		},
		{
			MethodName: "QueryPwd",
			Handler:    _PayInfo_QueryPwd_Handler,
		},
		{
			MethodName: "SetPwd",
			Handler:    _PayInfo_SetPwd_Handler,
		},
		{
			MethodName: "UpdateByMobile",
			Handler:    _PayInfo_UpdateByMobile_Handler,
		},
		{
			MethodName: "UpdateByPwd",
			Handler:    _PayInfo_UpdateByPwd_Handler,
		},
		{
			MethodName: "PayList",
			Handler:    _PayInfo_PayList_Handler,
		},
		{
			MethodName: "PayConfig",
			Handler:    _PayInfo_PayConfig_Handler,
		},
		{
			MethodName: "PaySet",
			Handler:    _PayInfo_PaySet_Handler,
		},
		{
			MethodName: "GetCardsBalance",
			Handler:    _PayInfo_GetCardsBalance_Handler,
		},
		{
			MethodName: "GetCards",
			Handler:    _PayInfo_GetCards_Handler,
		},
		{
			MethodName: "CardPay",
			Handler:    _PayInfo_CardPay_Handler,
		},
		{
			MethodName: "CardRefund",
			Handler:    _PayInfo_CardRefund_Handler,
		},
		{
			MethodName: "CardOrderRecords",
			Handler:    _PayInfo_CardOrderRecords_Handler,
		},
		{
			MethodName: "CardRecords",
			Handler:    _PayInfo_CardRecords_Handler,
		},
		{
			MethodName: "CheckPayType",
			Handler:    _PayInfo_CheckPayType_Handler,
		},
		{
			MethodName: "StandardPay",
			Handler:    _PayInfo_StandardPay_Handler,
		},
		{
			MethodName: "StandardPayRefund",
			Handler:    _PayInfo_StandardPayRefund_Handler,
		},
		{
			MethodName: "StandardPayStatus",
			Handler:    _PayInfo_StandardPayStatus_Handler,
		},
		{
			MethodName: "StandardPayClose",
			Handler:    _PayInfo_StandardPayClose_Handler,
		},
		{
			MethodName: "BaiduOrderRefundGet",
			Handler:    _PayInfo_BaiduOrderRefundGet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pay/pay_info.proto",
}
