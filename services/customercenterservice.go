package services

import (
	"_/models"
	"_/pkg/http/zilong"
	"_/proto/ac"
	"_/proto/cc"
	"_/proto/mc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/encoding/gjson"
	"github.com/limitedlee/microservice/common/config"

	"github.com/go-xorm/xorm"
	"github.com/jinzhu/copier"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type CustomerCenterService struct {
}

// 会员等级列表
func (service *CustomerCenterService) UserLevelList(ctx context.Context, req *cc.UserLevelListReq) (*cc.UserLevelListRes, error) {
	resp := &cc.UserLevelListRes{Code: 400, List: make([]*cc.UserLevelList, 0)}

	err := DataCenterEngine.Table("user_level").Cols("id,level_id,level_name,level_icon,background,health_val,level_status").
		Limit(int(req.PageSize), int((req.PageIndex-1)*req.PageSize)).Find(&resp.List)
	if err != nil {
		resp.Message = "查询失败，" + err.Error()
		return resp, nil
	}
	total, _ := DataCenterEngine.Table("user_level").Count()

	// 查看会员等级变更状态
	levelLog := &models.UserLevelLog{}
	b, err := DataCenterEngine.Table("user_level_log").OrderBy("id desc").Limit(1).Get(levelLog)
	if err != nil {
		resp.Message = "查询会员等级操作日志失败，error：" + err.Error()
		return resp, nil
	}
	if b {
		// 会员等级刷新状态，1待刷新、2刷新中、3已结束、4异常
		if levelLog.LevelRefreshState == 1 || levelLog.LevelRefreshState == 4 {
			// 会员等级状态，0无状态，可以编辑会员等级、1更新了会员规则、2会员等级更新中
			resp.LevelState = 1
		} else if levelLog.LevelRefreshState == 2 || levelLog.MemberPriceState == 2 {
			resp.LevelState = 2
		} else {
			resp.LevelState = 0
		}
	}

	resp.Code = 200
	resp.Total = total
	return resp, nil
}

// 设置启用停用
func (service *CustomerCenterService) UserLevelSet(ctx context.Context, req *cc.UserLevelSetReq) (*cc.Response, error) {
	resp := &cc.Response{Code: 400}
	if req.Status != 0 && req.Status != 1 {
		resp.Message = "状态参数错误"
		return resp, nil
	}

	// 查看会员等级变更状态
	if err := checkUserLevelState(); err != nil {
		resp.Message = err.Error()
		return resp, nil
	}

	detail := &models.UserLevel{}
	b, err := DataCenterEngine.Table("user_level").Where("id=?", req.Id).Get(detail)
	if err != nil {
		resp.Message = "查询失败，" + err.Error()
		return resp, nil
	}
	if !b {
		resp.Message = "会员等级不存在"
		return resp, nil
	}
	// 状态未变更
	if detail.LevelStatus == req.Status {
		resp.Code = 200
		return resp, nil
	}

	session := DataCenterEngine.NewSession()
	session.Begin()

	_, err = session.Table("user_level").Where("id=?", req.Id).Update(map[string]interface{}{
		"level_status": req.Status,
	})
	if err != nil {
		session.Rollback()
		resp.Message = err.Error()
		return resp, nil
	}

	// 启用
	levelLog := &models.UserLevelLog{
		LevelId:           detail.LevelId,
		LevelRefreshState: 1,
	}
	if req.Status == 1 {
		levelLog.LogType = models.UserLevelLogTypeEnable
		levelLog.Content = fmt.Sprintf("启用会员等级V%d，level_id:%d", detail.LevelId, detail.LevelId)
		_, err = session.Insert(levelLog)
	} else {
		// 停用
		levelLog.LogType = models.UserLevelLogTypeDisanable
		levelLog.Content = fmt.Sprintf("停用会员等级V%d，level_id:%d", detail.LevelId, detail.LevelId)
		_, err = session.Insert(levelLog)
	}
	if err != nil {
		session.Rollback()
		resp.Message = err.Error()
		return resp, nil
	}
	session.Commit()

	resp.Code = 200
	return resp, nil
}

// 校验会员等级更新状态
func checkUserLevelState() error {
	userLevelLog := &models.UserLevelLog{}
	b, err := DataCenterEngine.Table("user_level_log").OrderBy("id desc").Limit(1).Get(userLevelLog)
	if err != nil {
		return err
	}
	if b && userLevelLog.LevelRefreshState == 2 {
		return errors.New("会员等级刷新中，请先等待刷新结束")
	}
	return nil
}

// 会员等级编辑权益项
func (service *CustomerCenterService) UserEditEquityList(ctx context.Context, req *cc.EmptyReq) (*cc.UserEditEquityListRes, error) {
	resp := &cc.UserEditEquityListRes{Code: 400, List: make([]*cc.UserEditEquity, 0)}

	err := DataCenterEngine.Table("user_equity").Cols("id,equity_type,equity_name,is_voucher").
		Where("is_display=1").Find(&resp.List)
	if err != nil {
		resp.Message = err.Error()
		return resp, nil
	}
	resp.Code = 200
	return resp, nil
}

// 编辑会员等级
func (service *CustomerCenterService) UserLevelEdit(ctx context.Context, req *cc.UserLevelEditReq) (*cc.Response, error) {
	resp := &cc.Response{Code: 400}
	// 查看会员等级变更状态
	if err := checkUserLevelState(); err != nil {
		resp.Message = err.Error()
		return resp, nil
	}
	// 判断优惠券、门店券、会员价的权益是否勾选，勾选了值可以存储，未勾选值不存储
	if err := checkVoucherAndMemberPrice(req); err != nil {
		resp.Message = err.Error()
		return resp, nil
	}

	detail := &models.UserLevel{}
	b, err := DataCenterEngine.Table("user_level").Where("id=?", req.Id).Get(detail)
	if err != nil {
		resp.Message = "查询失败，" + err.Error()
		return resp, nil
	}
	if !b {
		resp.Message = "会员等级不存在"
		return resp, nil
	}
	oldHealthVal := detail.HealthVal
	copier.Copy(detail, req)

	// 校验参数
	if err := checkUserLevelDetail(detail); err != nil {
		resp.Message = err.Error()
		return resp, nil
	}

	session := DataCenterEngine.NewSession()
	session.Begin()
	_, err = session.Table("user_level").AllCols().Where("id=?", detail.Id).Update(detail)
	if err != nil {
		session.Rollback()
		resp.Message = "保存失败"
		return resp, nil
	}

	// 编辑启用的会员等级才触发更新提醒
	if detail.LevelStatus == 1 && detail.LevelId > 0 {
		var refreshState int64
		if oldHealthVal != detail.HealthVal {
			// 调整了健康值需要刷新会员等级
			refreshState = 1
		}

		if refreshState == 1 {
			_, err = session.Insert(&models.UserLevelLog{
				LevelId:           detail.LevelId,
				LogType:           models.UserLevelLogTypeDisanable,
				Content:           fmt.Sprintf("编辑会员等级V%d，level_id:%d，oldHealthVal->newHealthVal：%d->%d", detail.LevelId, detail.LevelId, oldHealthVal, detail.HealthVal),
				LevelRefreshState: refreshState,
			})
			if err != nil {
				session.Rollback()
				resp.Message = err.Error()
				return resp, nil
			}
		}
	}
	session.Commit()

	resp.Code = 200
	return resp, nil
}

// 校验参数
func checkUserLevelDetail(detail *models.UserLevel) error {
	if detail.LevelId == 0 {
		detail.HealthVal = 0 // V0等级的健康值默认为0
	}
	// 校验健康值
	err := checkHealVal(detail)
	if err != nil {
		return err
	}
	// 判断商城升级券是否有效
	detail.GoodsUpgradeVouchers, _, err = checkGoodsVouchers(detail.GoodsUpgradeVouchers)
	if err != nil {
		return err
	}
	// 检查子龙优惠券
	detail.StoreUpgradeVouchers, _, err = checkZiLongVouchers(detail.StoreUpgradeVouchers)
	if err != nil {
		return err
	}
	// 检查子龙优惠券，周特权券
	detail.StoreWeekVouchers, _, err = checkZiLongVouchers(detail.StoreWeekVouchers)
	if err != nil {
		return err
	}
	if err := checkMemberPrice(detail.MemberPrice); err != nil {
		return err
	}
	return nil
}

// 判断优惠券、门店券、会员价的权益是否勾选，勾选了值可以存储，未勾选值不存储
func checkVoucherAndMemberPrice(req *cc.UserLevelEditReq) error {
	privilegeIds := utils.StringToArrrayInt(req.PrivilegeIds)
	equities := make([]*models.UserEquity, 0)
	err := DataCenterEngine.Table("user_equity").In("id", privilegeIds).Find(&equities)
	if err != nil {
		return err
	}
	// 判断商品优惠券、门店券、会员价的权益是否勾选，勾选了值可以存储，未勾选值不存储
	var hasGoodsVoucher, hasStoreVoucher, hasMemberPrice bool
	for _, v := range equities {
		if v.EquityName == "商品礼券" {
			hasGoodsVoucher = true
		}
		if v.EquityName == "到店礼券" {
			hasStoreVoucher = true
		}
		if v.EquityName == "会员价" {
			hasMemberPrice = true
		}
	}
	if !hasGoodsVoucher {
		req.GoodsUpgradeVouchers = ""
	}
	if !hasStoreVoucher {
		req.StoreUpgradeVouchers = ""
		req.StoreWeekVouchers = ""
	}
	if !hasMemberPrice {
		req.MemberPrice = "0.0"
	}

	if hasStoreVoucher {
		if req.StoreUpgradeVouchers == "" && req.StoreWeekVouchers == "" {
			return errors.New("到店礼券周特权券和升级券至少填一项")
		}
	}

	return nil
}

// 校验健康值
func checkHealVal(detail *models.UserLevel) error {
	userLevelList, err := getUserLevelList(false)
	if err != nil {
		return err
	}

	var prevLevel, nextLevel *models.UserLevel
	for _, v := range userLevelList {
		if v.LevelId < detail.LevelId {
			prevLevel = v
		} else if v.LevelId > detail.LevelId {
			nextLevel = v
			break
		}
	}

	// 健康值必须＞上一等级健康值，且必须小于下一等级健康值。如下一等级还未设置健康值，本等级最高可设置为10万
	if detail.LevelId > 0 && prevLevel != nil && detail.HealthVal <= prevLevel.HealthVal {
		return errors.New("健康值必须大于上一等级健康值")
	}
	if nextLevel != nil && detail.HealthVal >= nextLevel.HealthVal {
		return errors.New("健康值必须小于下一等级健康值")
	}
	if detail.HealthVal > 100000 {
		return errors.New("健康值不能大于100000")
	}
	return nil
}

func checkMemberPrice(memberPrice string) error {
	price := cast.ToFloat64(memberPrice)
	if price != 0 && (price < 1.0 || price > 9.9) {
		return errors.New("会员价折扣必须在1.0-9.9之间")
	}
	return nil
}

func getUserLevelList(isAll bool) ([]*models.UserLevel, error) {
	list := make([]*models.UserLevel, 0)
	var err error
	if isAll {
		err = DataCenterEngine.Table("user_level").OrderBy("level_id asc").Find(&list)
	} else {
		err = DataCenterEngine.Table("user_level").Where("level_status=1").OrderBy("level_id asc").Find(&list)
	}
	return list, err
}

func checkGoodsVouchers(goodsVoucherVal string) (string, []string, error) {
	if goodsVoucherVal == "" {
		return "", nil, nil
	}
	vouchers := strings.FieldsFunc(goodsVoucherVal, func(r rune) bool {
		return r == ',' || r == '，'
	})
	if len(vouchers) > 10 {
		return "", nil, errors.New("券id不能超过10个")
	}
	list := make([]*models.UpetVoucherTemplate, 0)
	err := BbcEngine.Table("upet_voucher_template").In("voucher_t_id", vouchers).Find(&list)
	if err != nil {
		glog.Error("CustomerCenterService.checkGoodsVouchers error: ", err)
		return "", nil, err
	}

	var invalidIds, expiredIds, nostoreIds, validIds, voucherList []string
	curUnix := time.Now().Unix()
	for _, v := range list {
		if v.VoucherTState != 1 {
			invalidIds = append(invalidIds, cast.ToString(v.VoucherTId))
			voucherList = append(voucherList, fmt.Sprintf("%v-%s:已失效", v.VoucherTId, v.VoucherTTitle))
		} else if v.VoucherTEndDate > 0 && v.VoucherTEndDate < curUnix {
			expiredIds = append(expiredIds, cast.ToString(v.VoucherTId))
			voucherList = append(voucherList, fmt.Sprintf("%v-%s:已过期", v.VoucherTId, v.VoucherTTitle))
		} else if v.VoucherTGiveout >= v.VoucherTTotal {
			nostoreIds = append(nostoreIds, cast.ToString(v.VoucherTId))
			voucherList = append(voucherList, fmt.Sprintf("%v-%s:无库存", v.VoucherTId, v.VoucherTTitle))
		} else {
			validIds = append(validIds, cast.ToString(v.VoucherTId))
			voucherList = append(voucherList, fmt.Sprintf("%v-%s", v.VoucherTId, v.VoucherTTitle))
		}
	}
	var errMsgs []string
	if len(invalidIds) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("%s的券已失效", strings.Join(invalidIds, ",")))
	}
	if len(expiredIds) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("%s的券已过期", strings.Join(expiredIds, ",")))
	}
	if len(nostoreIds) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("%s的券无库存", strings.Join(nostoreIds, ",")))
	}
	if diff := utils.ArrayStringDiff(vouchers, invalidIds, expiredIds, nostoreIds, validIds); len(diff) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("%s的券不存在", strings.Join(diff, ",")))
		voucherList = append(voucherList, fmt.Sprintf("%s的券不存在", strings.Join(diff, ",")))
	}
	if len(errMsgs) > 0 {
		return "", voucherList, errors.New(strings.Join(errMsgs, ";"))
	}
	return strings.Join(validIds, ","), voucherList, nil
}

func checkZiLongVouchers(vouchers string) (string, []string, error) {
	if vouchers == "" {
		return "", nil, nil
	}
	voucherIds := strings.FieldsFunc(vouchers, func(r rune) bool {
		return r == ',' || r == '，'
	})
	if len(voucherIds) > 10 {
		return "", nil, errors.New("券id不能超过10个")
	}
	var validIds, nostoreIds, invalidIds, existsIds, expiredIds, voucherList []string
	wg := sync.WaitGroup{}
	for _, v := range voucherIds {
		vId := cast.ToInt32(v)
		if vId > 0 && !utils.InArrayString(v, validIds) {
			validIds = append(validIds, v)

			wg.Add(1)
			go func(id int32, vv string) {
				defer wg.Done()
				_, template, err := zilong.CouponTemplateDetail(id)
				if err != nil {
					existsIds = append(existsIds, vv)
				} else if template == nil {
					existsIds = append(existsIds, vv)
				} else if template != nil && template.StatusValue == 50 { // 10审核中、20待提交、30待投放、40已投放、50已过期
					expiredIds = append(expiredIds, vv)
					voucherList = append(voucherList, fmt.Sprintf("%v-%s:已过期", template.TemplateID, template.TemplateName))
				} else if template != nil && template.Inventory < 1 {
					nostoreIds = append(nostoreIds, vv)
					voucherList = append(voucherList, fmt.Sprintf("%v-%s:无库存", template.TemplateID, template.TemplateName))
				} else if template != nil && template.StatusValue != 40 && template.StatusValue != 30 { //30待投放、40已投放 可以领取
					invalidIds = append(invalidIds, vv)
					voucherList = append(voucherList, fmt.Sprintf("%v-%s:未投放", template.TemplateID, template.TemplateName))
				} else {
					voucherList = append(voucherList, fmt.Sprintf("%v-%s", template.TemplateID, template.TemplateName))
				}
			}(vId, v)
		}
	}
	wg.Wait()

	var errMsgs []string
	if len(invalidIds) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("%s的门店券未投放", strings.Join(invalidIds, ",")))
	}
	if len(nostoreIds) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("%s的门店券无库存", strings.Join(nostoreIds, ",")))
	}
	if len(existsIds) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("%s的门店券不存在", strings.Join(existsIds, ",")))
		voucherList = append(voucherList, fmt.Sprintf("%s-门店券不存在", strings.Join(existsIds, ",")))
	}
	if len(expiredIds) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("%s的门店券已过期", strings.Join(expiredIds, ",")))
	}
	if len(errMsgs) > 0 {
		return "", voucherList, errors.New(strings.Join(errMsgs, ","))
	}
	return strings.Join(validIds, ","), voucherList, nil
}

// 获取会员等级详情
func (service *CustomerCenterService) UserLevelDetail(ctx context.Context, req *cc.UserLevelDetailReq) (*cc.UserLevelDetailRes, error) {
	resp := &cc.UserLevelDetailRes{Code: 400}
	userLevel := &models.UserLevel{}
	_, err := DataCenterEngine.Table("user_level").Where("level_id=?", req.UserLevelId).Get(userLevel)
	if err != nil {
		glog.Error("UserLevelDetail.error: ", err)
		return resp, err
	}
	copier.Copy(resp, userLevel)

	equityIds := strings.Split(userLevel.PrivilegeIds, ",")
	err = DataCenterEngine.Table("user_equity").Select("id,equity_type,equity_name").In("id", equityIds).Find(&resp.EquityList)
	if err != nil {
		return resp, err
	}
	resp.Code = 200
	return resp, nil
}

// 获取所有的会员等级关联权益
func (service *CustomerCenterService) AllUserLevelEquityList(ctx context.Context, req *cc.EmptyReq) (*cc.AllUserLevelEquityListRes, error) {
	resp := &cc.AllUserLevelEquityListRes{Code: 400}
	userLevelList := make([]*models.UserLevel, 0)
	err := DataCenterEngine.Table("user_level").Where("level_status=1").Find(&userLevelList)
	if err != nil {
		glog.Error("AllUserLevelEquityList.error: ", err)
		return resp, err
	}
	equityList := make([]*models.UserEquity, 0)
	err = DataCenterEngine.Table("user_equity").Find(&equityList)
	if err != nil {
		return resp, err
	}
	equityData := make(map[int64]*models.UserEquity)
	for _, v := range equityList {
		equityData[int64(v.Id)] = v
	}

	for _, v := range userLevelList {
		list := make([]*cc.UserEquity, 0)
		equityIds := strings.Split(v.PrivilegeIds, ",")
		for _, vv := range equityIds {
			id := cast.ToInt64(vv)
			eq, ok := equityData[id]
			if !ok {
				continue
			}
			icon := ""
			for _, ic := range eq.Icons {
				if ic.LevelId == v.LevelId {
					icon = ic.Icon
				}
			}
			list = append(list, &cc.UserEquity{
				Id:         id,
				EquityName: eq.EquityName,
				Icon:       icon,
				EquityInfo: eq.EquityInfo,
			})
		}
		resp.LevelEquityList = append(resp.LevelEquityList, &cc.UserLevelEquityList{
			LevelId:    v.LevelId,
			EquityList: list,
		})
	}

	resp.Code = 200
	return resp, nil
}

// 加减用户健康值
func (service *CustomerCenterService) AddUserHealthVal(ctx context.Context, req *cc.AddUserHealthValReq) (*cc.Response, error) {
	resp := &cc.Response{Code: 400}

	//org_id := 0
	//
	//_, err := DataCenterEngine.Table("dc_order.order_main").Where("order_sn=?", req.OrderSn).Select("org_id").Get(&org_id)
	//if err != nil {
	//	glog.Error("获取订单主体失败", err, req.OrderSn)
	//	resp.Message = "获取用户积分信息失败"
	//	return resp, nil
	//}
	//
	//if org_id != 1 {
	//	resp.Code = 200
	//	return resp, nil
	//}

	// 判断用户是否存在健康值记录，不存在新增
	memberIntegralInfo := &models.MemberIntegralInfo{}
	b, err := DataCenterEngine.Table("member_integral_info").Where("memberid=?", req.UserId).Get(memberIntegralInfo)
	if err != nil {
		glog.Error("handleMemberLevel member_integral_info.Get error", err, req.UserId)
		resp.Message = "获取用户积分信息失败"
		return resp, nil
	}
	if !b {
		// 没有积分记录新增
		memberIntegralInfo.MemberId = req.UserId
		if _, err := DataCenterEngine.Table("member_integral_info").Insert(memberIntegralInfo); err != nil {
			glog.Error("handleMemberLevel member_integral_info.insert error: ", err, kit.JsonEncode(memberIntegralInfo))
			resp.Message = "新增用户积分记录失败"
			return resp, nil
		}
	}

	// 退款处理
	if req.HealthType == 5 {
		return RefundHandleHealthVal(ctx, req)
	}

	healthDetail := &models.HealthDetail{}
	copier.Copy(healthDetail, req)

	if req.Type == 1 || req.Type == 3 {
		if has, err := DataCenterEngine.Table("health_detail").Where("user_id=? AND order_sn=?", req.UserId, req.OrderSn).Exist(); err != nil {
			resp.Message = err.Error()
			return resp, nil
		} else if has {
			glog.Info("该订单已增加过健康值", kit.JsonEncode(req))
			resp.Code = 200
			return resp, nil
		}
	}

	// type默认0,1=收入，2=支出 3=冻结
	if req.Type == 3 {
		if req.EffectTime == "" {
			resp.Message = "生效时间不能为空"
			return resp, nil
		}
		effectTime, _ := time.ParseInLocation("2006-01-02 15:04:05", req.EffectTime, time.Local)
		if effectTime.Before(time.Now()) {
			resp.Message = "生效时间不能小于当前时间"
			return resp, nil
		}
		healthDetail.EffectTime = effectTime
	}
	if len(req.PayTime) > 0 {
		healthDetail.PayTime, _ = time.ParseInLocation("2006-01-02 15:04:05", req.PayTime, time.Local)
	} else {
		healthDetail.PayTime = time.Now()
	}

	if healthDetail.PayAmount == "" {
		healthDetail.PayAmount = "0"
	}
	if healthDetail.RefundAmount == "" {
		healthDetail.RefundAmount = "0"
	}
	if req.Type == 1 || req.Type == 3 {
		healthDetail.HealthVal = utils.CalculateHealthVal(cast.ToFloat64(req.PayAmount))
		healthDetail.PrevHealthCount = int64(memberIntegralInfo.HealthVal)
		if req.Type == 1 {
			healthDetail.CurrHealthCount = int64(memberIntegralInfo.HealthVal) + healthDetail.HealthVal
		} else {
			healthDetail.CurrHealthCount = int64(memberIntegralInfo.HealthVal)
		}
	} else if req.Type == 2 {
		healthDetail.HealthVal = utils.CalculateHealthVal(cast.ToFloat64(req.RefundAmount))
		healthDetail.PrevHealthCount = int64(memberIntegralInfo.HealthVal)
		healthDetail.CurrHealthCount = int64(memberIntegralInfo.HealthVal) - healthDetail.HealthVal
	}

	session := DataCenterEngine.NewSession()
	defer session.Close()

	session.Begin()
	if _, err := session.Table("health_detail").Insert(healthDetail); err != nil {
		session.Rollback()
		glog.Error("health_detail.Insert error: ", err)
		resp.Message = err.Error()
		return resp, nil
	}

	// 默认0,1=收入，2=支出 3=冻结
	switch req.Type {
	case 1:
		_, err = session.Exec("UPDATE member_integral_info SET health_val=health_val+? WHERE memberid=?", healthDetail.HealthVal, req.UserId)
	case 2:
		_, err = session.Exec("UPDATE member_integral_info SET health_val=health_val-? WHERE memberid=?", healthDetail.HealthVal, req.UserId)
	case 3:
		_, err = session.Exec("UPDATE member_integral_info SET freeze_health_val=freeze_health_val+? WHERE memberid=?", healthDetail.HealthVal, req.UserId)
	default:
		resp.Message = "参数type错误"
		return resp, nil
	}
	if err != nil {
		session.Rollback()
		resp.Message = err.Error()
		return resp, nil
	}
	session.Commit()

	// 处理会员等级
	if req.Type == 1 || req.Type == 2 {
		go handleMemberLevel(req.UserId)
	}

	resp.Code = 200
	return resp, nil
}

// 退款，处理健康值
func RefundHandleHealthVal(ctx context.Context, req *cc.AddUserHealthValReq) (*cc.Response, error) {
	resp := &cc.Response{Code: 400}
	if req.OrderSn == "" {
		resp.Message = "订单编号不能为空"
		return resp, nil
	}
	healthDetail := &models.HealthDetail{}
	b, err := DataCenterEngine.Table("health_detail").Where("user_id=? AND order_sn=?", req.UserId, req.OrderSn).Get(healthDetail)
	if err != nil {
		glog.Error("health_detail.Get error: ", err)
		resp.Message = err.Error()
		return resp, nil
	}
	if !b {
		glog.Error("refoundHandleHealthVal health_detail not found, req: ", kit.JsonEncode(req))
		resp.Message = "未找到健康值记录"
		return resp, nil
	}

	// 已经过了冻结期，不处理退款扣减健康值
	if healthDetail.EffectTime.Before(time.Now()) && healthDetail.HealthType != 2 {
		glog.Info("已经过了冻结期，不处理退款扣减健康值", kit.JsonEncode(req), healthDetail.EffectTime)
		resp.Code = 200
		return resp, nil
	}
	if healthDetail.PayAmount == healthDetail.RefundAmount {
		glog.Info("已经退完健康值", kit.JsonEncode(req))
		resp.Code = 200
		return resp, nil
	}
	refundAmountTotal := cast.ToString(cast.ToFloat64(healthDetail.RefundAmount) + cast.ToFloat64(req.RefundAmount))

	healthVal := utils.CalculateHealthVal((cast.ToFloat64(healthDetail.PayAmount) - cast.ToFloat64(healthDetail.RefundAmount)) - cast.ToFloat64(req.RefundAmount))
	if healthVal < 0 {
		healthVal = 0
		refundAmountTotal = healthDetail.PayAmount
	}
	decHealthVal := healthDetail.HealthVal - healthVal // 需要扣减的冻结健康值

	session := DataCenterEngine.NewSession()
	defer session.Close()

	session.Begin()
	_, err = session.Table("health_detail").Where("user_id=? AND order_sn=?", req.UserId, req.OrderSn).Update(map[string]interface{}{
		"refund_amount": refundAmountTotal,
		"health_val":    healthVal,
	})
	if err != nil {
		session.Rollback()
		glog.Error("health_detail.Get Update: ", err)
		resp.Message = err.Error()
		return resp, nil
	}
	_, err = session.Exec("UPDATE member_integral_info SET freeze_health_val=IF(freeze_health_val<?,0,freeze_health_val-?) WHERE memberid=?", decHealthVal, decHealthVal, req.UserId)
	if err != nil {
		session.Rollback()
		glog.Error("health_detail.member_integral_info Update: ", err)
		resp.Message = err.Error()
		return resp, nil
	}
	session.Commit()

	resp.Code = 200
	return resp, nil
}

// 处理用户会员等级
func handleMemberLevel(scrmUserId string) error {
	memberIntegralInfo := &models.MemberIntegralInfo{}
	b, err := DataCenterEngine.Table("member_integral_info").Where("memberid=?", scrmUserId).Get(memberIntegralInfo)
	if err != nil {
		glog.Error("handleMemberLevel member_integral_info.Get error", err, scrmUserId)
		return err
	}
	if !b {
		return errors.New("用户积分记录未找到 " + scrmUserId)
	}
	member := &models.UpetMember{}
	b, err = BbcEngine.Table("upet_member").Cols("scrm_user_id,user_level_id,user_level_stime,weixin_mini_openid").Where("scrm_user_id=?", scrmUserId).Get(member)
	if err != nil {
		glog.Error("handleMemberLevel upet_member.Get error", err)
		return err
	}
	if !b {
		return errors.New("用户记录未找到 " + scrmUserId)
	}

	userLevelList := make([]*models.UserLevel, 0)
	err = DataCenterEngine.Table("user_level").Where("level_status=1").OrderBy("level_id asc").Find(&userLevelList)
	if err != nil {
		glog.Error("handleMemberLevel user_level.Find error", err)
		return err
	}

	// 新会员等级
	var newLevelId int64
	oriLevelName, curLevelName := "V0闻卡会员", ""
	for k, v := range userLevelList {
		if v.LevelId == member.UserLevelId {
			oriLevelName = fmt.Sprintf("V%d%s", v.LevelId, v.LevelName)
		}
		if int64(memberIntegralInfo.HealthVal) >= v.HealthVal &&
			(k == len(userLevelList)-1 || int64(memberIntegralInfo.HealthVal) < userLevelList[k+1].HealthVal) {
			curLevelName = fmt.Sprintf("V%d%s", v.LevelId, v.LevelName)
			newLevelId = v.LevelId
		}
	}

	if newLevelId != member.UserLevelId {
		updData := map[string]interface{}{
			"user_level_id":    newLevelId,
			"user_level_etime": time.Now().AddDate(1, 0, 0).Unix(),
		}
		if member.UserLevelStime == 0 {
			updData["user_level_stime"] = time.Now().Unix()
		}
		if _, err := BbcEngine.Table("upet_member").Where("scrm_user_id=?", member.ScrmUserId).Update(updData); err != nil {
			return err
		}

		if member.UserLevelId > newLevelId {
			// 降级只有会员到期，其他情况不降级
			glog.Warning(fmt.Sprintf("handleMemberLevel 降级 scrm_user_id:%s, user_level_id:%d, user_level_stime:%d, newLevelId:%d, HealthVal:%d",
				member.ScrmUserId, member.UserLevelId, member.UserLevelStime, newLevelId, memberIntegralInfo.HealthVal))
			return nil
		} else {
			currentLevelId := member.UserLevelId
			n := newLevelId - currentLevelId
			for i := int64(0); i < n; i++ {
				if _, err = BbcEngine.Table("upet_member_level_log").Insert(&models.UpetMemberLevelLog{
					ScrmUserId:     member.ScrmUserId,
					LiftType:       models.MemberLevelLiftTypeUp,
					OldUserLevelId: currentLevelId,
					NewUserLevelId: currentLevelId + 1,
					Content:        "更新健康值，用户会员等级变更",
				}); err != nil {
					return err
				}
				currentLevelId++
			}
		}

		//发送会员等级变更消息
		SendLevelChangeWxMessage(member, newLevelId, int64(memberIntegralInfo.HealthVal), oriLevelName, curLevelName)
	}

	return nil
}

// 发送微信订阅消息，会员等级变更
func SendLevelChangeWxMessage(member *models.UpetMember, newLevelId int64, newHealthVal int64, oriLevelName, curLevelName string) {
	subTemplate, err := getLevelChangeSubscribeMessageTemplate()
	if err != nil {
		glog.Error("SendLevelChangeWxMessage getLevelChangeSubscribeMessageTemplate error: ", err, member.ScrmUserId)
		return
	}

	if !CanSendWxMessage(member.ScrmUserId, "user_level", 1) {
		glog.Info("SendLevelChangeWxMessage CanSendWxMessage false", member.ScrmUserId, subTemplate.TemplateId)
		return
	}

	miniprogramState := ""
	if kit.IsDebug {
		miniprogramState = "trial"
	}
	// 字符串长度为20个字符
	msg := fmt.Sprintf(`您由%s变更为%s`, oriLevelName, curLevelName)
	subscribeMessageRequest := &mc.SubscribeMessageRequest{
		Touser:           member.WeixinMiniOpenid,
		TemplateId:       subTemplate.TemplateId,
		Page:             "/pages/tabs/member", // 小程序跳转页面
		MiniprogramState: miniprogramState,
		Data:             fmt.Sprintf(subTemplate.Content, oriLevelName, curLevelName, msg),
		IsJpush:          0,
	}
	msgClient := mc.GetMessageCenterClient()
	re, err := msgClient.RPC.SubscribeMessage(msgClient.Ctx, subscribeMessageRequest)
	if err != nil {
		glog.Error("SendLevelChangeWxMessage SubscribeMessage error: ", err, " ScrmUserId:", member.ScrmUserId, kit.JsonEncode(subscribeMessageRequest))
		return
	}
	if re.Code != 200 {
		glog.Error("SendLevelChangeWxMessage 发送会员等级变更消息失败，ScrmUserId:", member.ScrmUserId, kit.JsonEncode(re), kit.JsonEncode(subscribeMessageRequest))
		return
	}

	// 记录发送成功的记录
	msgRecord := &models.WechatSubscribeMessageRecord{
		ScrmUserId:    member.ScrmUserId,
		TemplateId:    subTemplate.TemplateId,
		MessageData:   subscribeMessageRequest.Data,
		MessageParams: "",
	}
	if _, err := DataCenterEngine.Table("wechat_subscribe_message_record").Insert(msgRecord); err != nil {
		glog.Error("SendLevelChangeWxMessage wechat_subscribe_message_record insert error: ", err, kit.JsonEncode(msgRecord))
		return
	}

	// 订阅数减一
	_, err = DataCenterEngine.Exec("UPDATE wechat_subscribe_message SET number=IF(number<1,0,number-1) WHERE scrm_user_id=? AND template_id=?", member.ScrmUserId, subTemplate.TemplateId)
	if err != nil {
		glog.Error("SendLevelChangeWxMessage 扣减用户订阅数失败，error: ", err, member.ScrmUserId)
		return
	}
	glog.Info("SendLevelChangeWxMessage 发送订阅消息成功: ", kit.JsonEncode(msgRecord))
}

// 等级变更订阅模板
var levelChangeSubscribeMessageTemplate *models.WechatSubscribeMessageTemplate

// 获取订阅消息模板
func getLevelChangeSubscribeMessageTemplate() (*models.WechatSubscribeMessageTemplate, error) {
	if levelChangeSubscribeMessageTemplate != nil {
		return levelChangeSubscribeMessageTemplate, nil
	}
	levelChangeSubscribeMessageTemplate = &models.WechatSubscribeMessageTemplate{}
	_, err := DataCenterEngine.Table("wechat_subscribe_message_template").Where("template_key=? AND store_id=1", "user-level-change").Get(levelChangeSubscribeMessageTemplate)
	if err != nil {
		return nil, err
	}
	return levelChangeSubscribeMessageTemplate, nil
}

// 是否可以发送微信订阅消息
func CanSendWxMessage(scrmUserId, subscribeType string, orgId int32) bool {
	notification := &models.WechatSubscribeNotification{}
	_, err := DataCenterEngine.Table("wechat_subscribe_notification").Where("scrm_user_id=? AND store_id=?", scrmUserId, orgId).Get(notification)
	if err != nil {
		glog.Error("CanSendWxMessage wechat_subscribe_notification error: ", err, scrmUserId, subscribeType, orgId)
		return false
	}
	if notification.All == 1 {
		return true
	}

	switch subscribeType {
	case "user_level":
		if notification.UserLevel == 1 {
			return true
		}
	case "integral":
		if notification.Integral == 1 {
			return true
		}
	case "voucher":
		if notification.Voucher == 1 {
			return true
		}
	case "register":
		if notification.Register == 1 {
			return true
		}
	case "queuing":
		if notification.Queuing == 1 {
			return true
		}
	case "service":
		if notification.Queuing == 1 {
			return true
		}
	case "vr_code_use":
		if notification.VrCodeUse == 1 {
			return true
		}
	case "vr_code_expire":
		if notification.VrCodeExpire == 1 {
			return true
		}
	case "vip_card_expire":
		if notification.VipCardExpire == 1 {
			return true
		}
	}

	return false
}

// 是否可以发送微信订阅消息
func (service *CustomerCenterService) CanSendWechatSubscribeMessage(ctx context.Context, req *cc.CanSendWechatSubscribeMessageReq) (*cc.Response, error) {
	resp := &cc.Response{Code: 400}
	if !CanSendWxMessage(req.ScrmUserId, req.SubscribeType, 1) {
		resp.Message = "不能推送微信订阅消息"
		return resp, nil
	}

	resp.Code = 200
	return resp, nil
}

// 用户微信订阅消息开启状态
func (service *CustomerCenterService) UserNotification(ctx context.Context, req *cc.UserNotificationReq) (*cc.UserNotificationRes, error) {
	resp := &cc.UserNotificationRes{Code: 400}

	notification := &models.WechatSubscribeNotification{}
	_, err := DataCenterEngine.Table("wechat_subscribe_notification").Where("scrm_user_id=? AND store_id=?", req.ScrmUserId, req.OrgId).Get(notification)
	if err != nil {
		resp.Message = err.Error()
		return resp, nil
	}

	resp.Code = 200
	resp.All = notification.All
	resp.UserLevel = notification.UserLevel
	resp.Integral = notification.Integral
	resp.Voucher = notification.Voucher
	resp.Register = notification.Register
	resp.Queuing = notification.Queuing
	resp.Service = notification.Service
	resp.VrCodeUse = notification.VrCodeUse
	resp.VrCodeExpire = notification.VrCodeExpire
	resp.VipCardExpire = notification.VipCardExpire
	return resp, nil
}

// 刷新用户会员等级
func (service *CustomerCenterService) RefreshUserLevel(ctx context.Context, req *cc.EmptyReq) (*cc.Response, error) {
	resp := &cc.Response{Code: 400}

	// 查看会员等级变更状态
	userLevelLog := &models.UserLevelLog{}
	b, _ := DataCenterEngine.Table("user_level_log").OrderBy("id desc").Limit(1).Get(userLevelLog)
	if !b {
		resp.Message = "没有会员等级更新记录"
		return resp, nil
	}
	if userLevelLog.LevelRefreshState == 2 {
		resp.Message = "会员等级刷新中，请耐心等待"
		return resp, nil
	}

	// 刷新用户会员等级
	if userLevelLog.LevelRefreshState == 1 || userLevelLog.LevelRefreshState == 4 {
		go handleUserLevel(userLevelLog)
	}

	resp.Code = 200
	return resp, nil
}

// 处理会员等级
func handleUserLevel(userLevelLog *models.UserLevelLog) error {
	userLevelList, err := getUserLevelList(false)
	if err != nil {
		glog.Error("getUserLevels error: ", err)
		return err
	}

	if _, err := DataCenterEngine.Table("user_level_log").Where("id=?", userLevelLog.Id).Update(map[string]interface{}{
		"level_refresh_state": 2, // 标记用户会员等级刷新中
	}); err != nil {
		return err
	}

	// 计算健康值大于1的数据
	prevLevel := &models.UserLevel{HealthVal: int64(1)}
	for k, v := range userLevelList {
		if v.LevelId == 0 {
			// v0健康值默认是0，这里不用处理
			continue
		}

		if err := refreshCurUserLevel(prevLevel, v); err != nil {
			setRefreshUserLevelError(userLevelLog.Id, err)
			return err
		}
		if k == len(userLevelList)-1 {
			if err := refreshMaxUserLevel(v); err != nil {
				setRefreshUserLevelError(userLevelLog.Id, err)
				return err
			}
		}
		prevLevel = v
	}

	_, err = DataCenterEngine.Table("user_level_log").Where("id=?", userLevelLog.Id).Update(map[string]interface{}{
		"level_refresh_state": 3, // 刷新结束
		"member_price_error":  "",
	})
	return err
}

// 更新当前会员等级
func refreshCurUserLevel(prevLevel, curLevel *models.UserLevel) error {
	page, size := 1, 200
	for {
		list := make([]*models.MemberIntegralInfo, 0)
		err := DataCenterEngine.Table("member_integral_info").Cols("memberid").Where("health_val>=? AND health_val<?",
			prevLevel.HealthVal, curLevel.HealthVal).Limit(size, (page-1)*size).OrderBy("lasttime asc").Find(&list)
		if err != nil {
			glog.Error("member_integral_info find error: ", err)
			return err
		}
		if len(list) == 0 {
			break
		}
		userIds := make([]string, 0)
		for _, v := range list {
			userIds = append(userIds, v.MemberId)
		}
		_, err = BbcEngine.Table("upet_member").In("scrm_user_id", userIds).Update(map[string]interface{}{
			"user_level_id": prevLevel.LevelId,
		})
		if err != nil {
			glog.Error("upet_member Update error: ", err)
			return err
		}
		page++
		time.Sleep(time.Millisecond * 200)
	}
	return nil
}

// 更新最高会员等级
func refreshMaxUserLevel(curLevel *models.UserLevel) error {
	page, size := 1, 200
	for {
		list := make([]*models.MemberIntegralInfo, 0)
		err := DataCenterEngine.Table("member_integral_info").Cols("memberid").Where("health_val>=?",
			curLevel.HealthVal).Limit(size, (page-1)*size).OrderBy("lasttime asc").Find(&list)
		if err != nil {
			glog.Error("member_integral_info find error: ", err)
			return err
		}
		if len(list) == 0 {
			break
		}
		userIds := make([]string, 0)
		for _, v := range list {
			userIds = append(userIds, v.MemberId)
		}
		_, err = BbcEngine.Table("upet_member").In("scrm_user_id", userIds).Update(map[string]interface{}{
			"user_level_id": curLevel.LevelId,
		})
		if err != nil {
			glog.Error("upet_member Update error: ", err)
			return err
		}
		page++
		time.Sleep(time.Millisecond * 200)
	}
	return nil
}

func setRefreshUserLevelError(levelLogId int64, err error) {
	DataCenterEngine.Table("user_level_log").Where("id=?", levelLogId).Update(map[string]interface{}{
		"level_refresh_state": 4, // 刷新出错
		"level_refresh_error": err.Error(),
	})
}

// 处理会员价
func handleGoodsMemberPrice(userLevelLog *models.UserLevelLog) error {
	userLevelList, err := getUserLevelList(true)
	if err != nil {
		glog.Error("getUserLevels error: ", err)
		return err
	}

	if _, err := DataCenterEngine.Table("user_level_log").Where("id=?", userLevelLog.Id).Update(map[string]interface{}{
		"member_price_state": 2,
	}); err != nil {
		return err
	}

	for _, v := range userLevelList {
		// 未启用的会员等级，会员价为0
		if v.LevelStatus == 0 {
			v.MemberPrice = "0"
		}

		// 会员等级0~8对应会员价member_price_0~member_price_8
		sql := fmt.Sprintf(`UPDATE upet_goods SET member_price_%d=goods_price*0.1*%s WHERE goods_state=? AND goods_verify=? AND store_id=1`, v.LevelId, v.MemberPrice)
		_, err := BbcEngine.Exec(sql, 1, 1)
		if err != nil {
			glog.Error("handleMemberPriceEnable error: ", err)
			DataCenterEngine.Table("user_level_log").Where("id=?", userLevelLog.Id).Update(map[string]interface{}{
				"member_price_state": 4, // 错误
				"member_price_error": err.Error(),
			})
			return err
		}
		time.Sleep(200 * time.Millisecond)
	}
	DataCenterEngine.Table("user_level_log").Where("id=?", userLevelLog.Id).Update(map[string]interface{}{
		"member_price_state": 3, // 更新完成
		"member_price_error": "",
	})
	return nil
}

func (service *CustomerCenterService) SetNotificationState(ctx context.Context, req *cc.SetNotificationStateReq) (*cc.Response, error) {
	resp := &cc.Response{Code: 400}

	notification := &models.WechatSubscribeNotification{}
	b, err := DataCenterEngine.Table("wechat_subscribe_notification").Where("scrm_user_id=? AND store_id=?", req.ScrmUserId, req.OrgId).Get(notification)
	if err != nil {
		resp.Message = err.Error()
		return resp, nil
	}

	// 新增订阅
	copier.Copy(notification, req)
	notification.StoreId = req.OrgId
	if !b {
		_, err = DataCenterEngine.Table("wechat_subscribe_notification").Insert(notification)
		if err != nil {
			resp.Message = err.Error()
			return resp, nil
		}
	} else {
		// 更新订阅，现在订阅入口分布在不同页面，所以这里要处理不同的订阅类型
		updData := map[string]interface{}{}
		if req.All == 2 || req.All == 1 {
			updData["all"] = req.All
		}
		if req.UserLevel == 2 || req.UserLevel == 1 {
			updData["user_level"] = req.UserLevel
		}
		if req.Integral == 2 || req.Integral == 1 {
			updData["integral"] = req.Integral
		}
		if req.Voucher == 2 || req.Voucher == 1 {
			updData["voucher"] = req.Voucher
		}
		if req.Register == 2 || req.Register == 1 {
			updData["register"] = req.Register
		}
		if req.Queuing == 2 || req.Queuing == 1 {
			updData["queuing"] = req.Queuing
		}
		if req.VrCodeUse == 2 || req.VrCodeUse == 1 {
			updData["vr_code_use"] = req.VrCodeUse
		}
		if req.VrCodeExpire == 2 || req.VrCodeExpire == 1 {
			updData["vr_code_expire"] = req.VrCodeExpire
		}
		if req.VrCodeExpire == 2 || req.VrCodeExpire == 1 {
			updData["vr_code_expire"] = req.VrCodeExpire
		}
		if req.VipCardExpire == 2 || req.VipCardExpire == 1 {
			updData["vip_card_expire"] = req.VipCardExpire
		}

		if len(updData) > 0 {
			_, err = DataCenterEngine.Table("wechat_subscribe_notification").Where("scrm_user_id=? AND store_id=?", req.ScrmUserId, req.OrgId).Update(updData)
		}
	}
	if err != nil {
		resp.Message = err.Error()
		return resp, nil
	}
	resp.Code = 200
	return resp, nil
}

func (service *CustomerCenterService) AddNotificationMessage(ctx context.Context, req *cc.AddNotificationMessageReq) (*cc.Response, error) {
	resp := &cc.Response{Code: 400}
	if len(req.Messages) == 0 {
		resp.Message = "参数错误"
		return resp, nil
	}
	for _, v := range req.Messages {
		msg := &models.WechatSubscribeMessage{}
		b, err := DataCenterEngine.Table("wechat_subscribe_message").Where("scrm_user_id=? AND template_id=?", req.ScrmUserId, v.TemplateId).Get(msg)
		if err != nil {
			resp.Message = err.Error()
			return resp, nil
		}
		// 取消订阅，订阅数不扣减
		if v.Type == 0 {
			continue
		}

		num := int64(1)
		if !b {
			// 新增
			msg.ScrmUserId = req.ScrmUserId
			msg.TemplateId = v.TemplateId
			msg.Number = num
			_, err = DataCenterEngine.Table("wechat_subscribe_message").Insert(msg)
		} else {
			_, err = DataCenterEngine.Exec("UPDATE wechat_subscribe_message SET number=number+? WHERE scrm_user_id=? AND template_id=?", num, req.ScrmUserId, v.TemplateId)
		}
		if err != nil {
			resp.Message = err.Error()
			return resp, nil
		}
	}

	resp.Code = 200
	return resp, nil
}

// SendSubscribeMessage 发送微信订阅消息
func (service *CustomerCenterService) SendSubscribeMessage(ctx context.Context, req *cc.SendSubscribeMessageReq) (*cc.Response, error) {
	resp := &cc.Response{Code: 400}
	subTemplate := &models.WechatSubscribeMessageTemplate{}
	_, err := DataCenterEngine.Table("wechat_subscribe_message_template").Where("template_key=? AND store_id=?", req.TemplateKey, req.OrgId).Get(subTemplate)
	if err != nil {
		glog.Error("SendSubscribeMessage subTemplate error: ", err)
		resp.Message = err.Error()
		return resp, nil
	}

	if req.SubscribeType == "" {
		req.SubscribeType = strings.ReplaceAll(req.TemplateKey, "-", "_")
	}

	if !CanSendWxMessage(req.ScrmUserId, req.SubscribeType, req.OrgId) {
		glog.Info("SendSubscribeMessage CanSendWxMessage false", req.ScrmUserId, subTemplate.TemplateId)
		resp.Message = "用户未开启微信消息订阅推送"
		return resp, nil
	}

	member := &models.UpetMember{}
	b, err := BbcEngine.Table("upet_member").Select("member_id,scrm_user_id,weixin_mini_openid").Where("scrm_user_id=?", req.ScrmUserId).Get(member)
	if err != nil || !b {
		glog.Error("SendSubscribeMessage error:", err, ",b:", b)
		resp.Message = "用户未找到"
		return resp, nil
	}

	miniprogramState := ""
	if kit.IsDebug {
		miniprogramState = "trial"
	}

	// 字符串长度为20个字符
	values := make([]interface{}, 0)
	for _, v := range req.Values {
		if v.Type == "int" {
			values = append(values, cast.ToInt64(v.Value))
		} else if v.Type == "float" {
			values = append(values, cast.ToFloat64(v.Value))
		} else {
			values = append(values, v.Value)
		}
	}

	if req.Page != "" {
		subTemplate.Page = req.Page
	} else if req.PageParams != "" { // 小程序页面跳转参数
		if req.TemplateKey == "register-queuing" { // 排队信息
			subTemplate.Page += "?hoscode=" + req.PageParams
		} else {
			subTemplate.Page += "?" + req.PageParams
		}
	}

	// 判断使用哪个小程序
	miniOpenid := member.WeixinMiniOpenid
	if req.OrgId == 2 {
		miniOpenid = member.WeixinMiniOpenid2
	} else if req.OrgId == 3 || req.OrgId == 4 {
		// 福码购小程序，需要从eshop.users表获取小程序openid
		miniOpenid, err = getEshopUser(member.MemberId, int(req.OrgId))
		if err != nil {
			glog.Error("SendSubscribeMessage 获取用户福码购小程序的openid异常: ", err, member.MemberId)
			return resp, err
		}
	}

	subscribeMessageRequest := &mc.SubscribeMessageRequest{
		Touser:           miniOpenid,
		TemplateId:       subTemplate.TemplateId,
		Page:             subTemplate.Page, // 小程序跳转页面
		MiniprogramState: miniprogramState,
		Data:             fmt.Sprintf(subTemplate.Content, values...),
		OrgId:            req.OrgId,
	}
	msgClient := mc.GetMessageCenterClient()
	re, err := msgClient.RPC.SubscribeMessage(msgClient.Ctx, subscribeMessageRequest)
	if err != nil {
		glog.Error("SendSubscribeMessage SubscribeMessage error: ", err, " ScrmUserId:", member.ScrmUserId, kit.JsonEncode(subscribeMessageRequest))
		resp.Message = err.Error()
		return resp, nil
	}
	if re.Code != 200 {
		glog.Error("SendSubscribeMessage 发送订阅消息失败，ScrmUserId:", member.ScrmUserId, kit.JsonEncode(re), kit.JsonEncode(subscribeMessageRequest))
		resp.Message = re.Error
		return resp, nil
	}

	// 记录发送成功的记录
	msgRecord := &models.WechatSubscribeMessageRecord{
		ScrmUserId:    req.ScrmUserId,
		TemplateId:    subTemplate.TemplateId,
		MessageData:   subscribeMessageRequest.Data,
		MessageParams: req.PageParams,
	}
	if _, err := DataCenterEngine.Table("wechat_subscribe_message_record").Insert(msgRecord); err != nil {
		glog.Error("SendSubscribeMessage wechat_subscribe_message_record insert error: ", err, kit.JsonEncode(msgRecord))
		resp.Message = err.Error()
		return resp, nil
	}

	// 订阅数减一
	_, err = DataCenterEngine.Exec("UPDATE wechat_subscribe_message SET number=IF(number<1,0,number-1) WHERE scrm_user_id=? AND template_id=?", member.ScrmUserId, subTemplate.TemplateId)
	if err != nil {
		glog.Error("SendSubscribeMessage 扣减用户订阅数失败，error: ", err, member.ScrmUserId)
		resp.Message = err.Error()
		return resp, nil
	}

	glog.Info("SendSubscribeMessage 发送订阅消息成功: ", kit.JsonEncode(msgRecord))
	resp.Code = 200
	return resp, nil
}

func getEshopUser(memberId int64, orgId int) (string, error) {
	var miniOpenId string
	_, err := Engine.SQL("SELECT weixin_mini_openid FROM eshop.users Where member_id=? AND org_id=?", memberId, orgId).Get(&miniOpenId)
	if err != nil {
		glog.Error("获取福码购小程序的用户openId失败")
		return "", err
	}
	return miniOpenId, nil
}

func (service *CustomerCenterService) TaskList(ctx context.Context, req *cc.TaskListReq) (*cc.TaskListRes, error) {
	out := new(cc.TaskListRes)

	var data []models.UserTaskManage
	db := service.getDbEngine()
	err := db.Table("datacenter.user_task_manage").Find(&data)
	if err != nil {
		out.Msg = "查询失败，" + err.Error()
		return out, err
	}

	if len(data) > 0 {
		for _, v := range data {
			out.Data = append(out.Data, &cc.TaskList{
				Id:       v.Id,
				TaskName: v.TaskName,
				TaskVal:  v.TaskVal,
				IsSelect: v.IsSelect,
			})
		}
	}

	return out, nil
}

func (service *CustomerCenterService) TaskSave(ctx context.Context, req *cc.TaskSaveReq) (*cc.BaseResponseNew, error) {
	out := new(cc.BaseResponseNew)
	if len(req.List) == 0 {
		out.Msg = "list参数不能为空"
		return out, errors.New("list参数不能为空")
	}

	db := service.getDbEngine()
	for k, v := range req.List {
		if v.Id == 0 || v.TaskName == "" || v.TaskVal == 0 {
			out.Msg = cast.ToString(k+1) + "行参数有误"
			return out, errors.New(cast.ToString(k+1) + "行参数有误")
		}

		_, err := db.Table("datacenter.user_task_manage").Where("id=?", v.Id).Cols("task_name, task_val, is_select").Update(v)
		if err != nil {
			glog.Error("更新任务失败：", err.Error())
		}
	}

	return out, nil
}

func (service *CustomerCenterService) MemberEquityList(ctx context.Context, in *cc.MemberEquityListReq) (*cc.MemberEquityListRes, error) {
	out := new(cc.MemberEquityListRes)
	session := Engine.Table("datacenter.user_equity").Where("1=1")

	if in.EquityName != "" {
		session.And("equity_name like ?", "%"+in.EquityName+"%")
	}

	if in.IsDisplay > 0 {
		if in.IsDisplay == 2 {
			in.IsDisplay = 0
		}
		session.And("is_display=?", in.IsDisplay)
	}

	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	countSession := *session

	count, _ := countSession.Count()
	out.Total = int32(count)

	if err := session.Select("id, equity_name, icon, is_display").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
		OrderBy("create_time Desc").Find(&out.Data); err != nil {
		return out, err
	}

	return out, nil
}

func (service *CustomerCenterService) MemberEquityDetail(ctx context.Context, req *cc.MemberEquityDetailReq) (*cc.MemberEquityDetailRes, error) {
	out := new(cc.MemberEquityDetailRes)

	data := models.UserEquity{}
	_, err := Engine.Table("datacenter.user_equity").Where("id=?", req.Id).Get(&data)

	if err != nil {
		out.Msg = err.Error()
		return out, err
	}

	out.Id = data.Id
	out.EquityName = data.EquityName
	out.EquityInfo = data.EquityInfo
	out.EquityRules = data.EquityRules
	out.IsDisplay = data.IsDisplay
	out.IsVoucher = data.IsVoucher
	out.Icon = data.Icon
	return out, nil
}

func (service *CustomerCenterService) MemberEquityEdit(ctx context.Context, req *cc.MemberEquityEditReq) (*cc.BaseResponseNew, error) {
	out := new(cc.BaseResponseNew)

	//当某个会员等级是启用中的，对应的等级图标要必须上传
	icons := []models.IconInfo{}
	err := json.Unmarshal([]byte(req.EquityIcon), &icons)
	if err != nil {
		glog.Error("编辑权益解析数据异常：", req.EquityIcon, err.Error())
		return out, err
	}
	iconMap := make(map[int64]string)
	for _, v := range icons {
		iconMap[v.LevelId] = v.Icon
	}

	//查当前已启用的级别
	userLevel := []models.UserLevel{}
	err = Engine.Table("datacenter.user_level").Where("level_status=1").Find(&userLevel)
	if err != nil {
		glog.Error("查询等级列表异常：", err.Error())
		return out, err
	}

	for _, v := range userLevel {
		url, ok := iconMap[v.LevelId]
		if !ok || url == "" {
			return out, errors.New(cast.ToString(v.LevelId) + "级别icon不能为空")
		}
	}

	data := models.UserEquity{
		EquityName:  req.EquityName,
		Icon:        req.EquityIcon,
		EquityInfo:  req.EquityInfo,
		EquityRules: req.EquityRules,
		IsVoucher:   req.IsVoucher,
	}
	_, err = Engine.Table("datacenter.user_equity").
		Cols("equity_name,icon,equity_info,equity_rules,is_voucher").Where("id=?", req.Id).Update(&data)

	if err != nil {
		out.Msg = err.Error()
		return out, err
	}
	return out, nil
}

type IconInfo struct {
	LevelId int64  `json:"level_id"`
	Icon    string `json:"icon"`
}

// 获取会员等级权益
func (service *CustomerCenterService) UserLevelEquities(ctx context.Context, req *cc.UserLevelEquitiesReq) (*cc.UserLevelEquitiesRes, error) {
	out := new(cc.UserLevelEquitiesRes)

	userLevel := models.UserLevel{}
	_, err := DataCenterEngine.Table("user_level").Where("level_id=?", req.LevelId).Get(&userLevel)
	if err != nil {
		return out, err
	}
	equityIds := strings.Split(userLevel.PrivilegeIds, ",")
	equityList := make([]*models.UserEquity, 0)
	err = DataCenterEngine.Table("user_equity").In("id", equityIds).Find(&equityList)
	if err != nil {
		return out, err
	}
	for _, v := range equityList {
		icon := ""
		for _, c := range v.Icons {
			if c.LevelId == req.LevelId {
				icon = c.Icon
			}
		}
		out.List = append(out.List, &cc.UserEquity{
			Id:         int64(v.Id),
			EquityName: v.EquityName,
			Icon:       icon,
			EquityInfo: v.EquityInfo,
		})
	}
	return out, nil
}

func (service *CustomerCenterService) MemberTaskList(ctx context.Context, in *cc.MemberTaskListReq) (*cc.MemberTaskListRes, error) {
	out := new(cc.MemberTaskListRes)
	out.Data = new(cc.MemberTaskListData)
	data := []models.UserTaskAndManage{}
	startTime := time.Now().Format("2006-01-02")
	endTime := startTime + " 23:59:59"

	sql := "SELECT distinct m.id, m.task_name, m.icon, m.task_val,IFNULL(u.status,0) as status FROM datacenter.user_task_manage as m " +
		"LEFT JOIN datacenter.user_task as u on m.id=u.task_id and u.user_id=? and u.create_time>=? and u.create_time<=? " +
		"where is_select=1"

	err := Engine.SQL(sql, in.ScrmUserid, startTime, endTime).Find(&data)
	if err != nil {
		out.Msg = err.Error()
		return out, err
	}

	if len(data) > 0 {
		for _, v := range data {
			temp := cc.MemberTaskList{
				TaskId:   v.Id,
				Status:   cast.ToInt32(v.Status),
				TaskName: v.TaskName,
				TaskVal:  v.TaskVal,
				Icon:     v.Icon,
			}
			out.Data.TaskList = append(out.Data.TaskList, &temp)
		}
	}

	//获取用户健康值
	model := models.MemberIntegralInfoNew{}
	_, err = Engine.Table("datacenter.member_integral_info").Where("memberid=?", in.ScrmUserid).Get(&model)
	if err != nil {
		out.Msg = err.Error()
		return out, err
	}
	out.Data.HealthVal = model.HealthVal
	//查询等级列表
	err = Engine.Table("datacenter.user_level").Select("level_id, level_name, health_val").
		Where("level_status=1").Find(&out.Data.LevelList)
	if err != nil {
		glog.Error("任务列表查询等级列表失败：", err.Error())
		return out, err
	}
	//查询用户当前级别
	member := models.UpetMember{}
	_, err = BbcEngine.Table("upet_member").Where("scrm_user_id=?", in.ScrmUserid).Get(&member)
	if err != nil {
		out.Msg = err.Error()
		return out, err
	}

	out.Data.LevelId = cast.ToInt32(member.UserLevelId)
	return out, nil
}

func (service *CustomerCenterService) MemberHealthVal(ctx context.Context, in *cc.MemberHealthValReq) (*cc.MemberHealthValRes, error) {
	out := new(cc.MemberHealthValRes)

	//获取用户当前健康值
	model := models.MemberIntegralInfoNew{}
	_, err := Engine.Table("datacenter.member_integral_info").Where("memberid=?", in.ScrmUserId).Get(&model)
	if err != nil {
		out.Msg = err.Error()
		return out, err
	}
	//查询用户当前级别
	member := models.UpetMember{}
	_, err = BbcEngine.Table("upet_member").Where("scrm_user_id=?", in.ScrmUserId).Get(&member)
	if err != nil {
		out.Msg = err.Error()
		return out, err
	}
	//查询下一级别需要多少健康值
	nextLevel := models.UserLevel{}
	_, err = Engine.Table("datacenter.user_level").Where("level_id=?", member.UserLevelId+1).Get(&nextLevel)
	if err != nil {
		out.Msg = err.Error()
		return out, err
	}
	//查询当前级别需要多少健康值
	currentLevel := models.UserLevel{}
	_, err = Engine.Table("datacenter.user_level").Where("level_id=?", member.UserLevelId).Get(&currentLevel)
	if err != nil {
		out.Msg = err.Error()
		return out, err
	}
	//计算距下一个级别还差多少健康值
	out.GapVal = cast.ToInt32(nextLevel.HealthVal) - model.HealthVal
	out.NextLevelVal = cast.ToInt32(nextLevel.HealthVal)
	out.CurrentLevel = cast.ToInt32(member.UserLevelId)
	out.CurrentLevelName = currentLevel.LevelName
	out.CurrentLevelVal = cast.ToInt32(currentLevel.HealthVal)
	out.HealthVal = model.HealthVal

	return out, nil
}

func (service *CustomerCenterService) MemberHealthDetail(ctx context.Context, in *cc.MemberHealthDetailReq) (*cc.MemberHealthDetailRes, error) {
	out := new(cc.MemberHealthDetailRes)
	session := DataCenterEngine.Table("health_detail").Where("user_id=? and shop_id!='2' AND shop_id!='3'", in.ScrmUserid)

	if in.Type > 0 {
		session.And("type=?", in.Type)
	}

	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	countSession := *session
	count, _ := countSession.Count()
	out.Total = int32(count)

	err := session.Select("id, type, title, pay_amount, refund_amount, order_sn, shop_name, content, health_val, health_type, effect_time, create_time,pay_time").
		Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("create_time Desc").Find(&out.Data)
	if err != nil {
		return out, err
	}
	// 处理创建时间格式问题
	if out.Total > 0 {
		for _, v := range out.Data {
			if len(v.CreateTime) > 0 {
				v.CreateTime = cast.ToTime(v.CreateTime).Format(kit.DATETIME_LAYOUT)
			}
			if len(v.EffectTime) > 0 {
				v.EffectTime = cast.ToTime(v.EffectTime).Format(kit.DATETIME_LAYOUT)
			}
			if len(v.PayTime) > 0 {
				v.PayTime = cast.ToTime(v.PayTime).Format(kit.DATETIME_LAYOUT)
			}
		}
	}

	return out, nil
}

func (service *CustomerCenterService) UserEquity(ctx context.Context, req *cc.UserEquityReq) (*cc.UserEquityRes, error) {
	out := new(cc.UserEquityRes)
	//查询特权列表
	err := Engine.Table("datacenter.user_equity").Select("id,equity_name,icon,equity_info,equity_rules,is_voucher").
		Where("is_display=1").Find(&out.Data)

	if err != nil {
		out.Msg = err.Error()
		return out, err
	}

	if len(out.Data) > 0 {
		for k, v := range out.Data {
			userLevel := []*models.UserLevel{}
			//查该权益适合哪些级别
			Engine.Table("datacenter.user_level").Where("privilege_ids like ?", "%"+cast.ToString(v.Id)+"%").Find(&userLevel)
			if len(userLevel) > 0 {
				for _, level := range userLevel {
					out.Data[k].EquityLevel += cast.ToString(level.LevelId) + ","
				}
			}

			if v.IsVoucher == 1 && req.ScrmUserid != "" {
				//门店券
				out.Data[k].StoreVouchers = getVoucherByUserid(req.ScrmUserid, 1)
				//商城券
				out.Data[k].CouponList = getVoucherByUserid(req.ScrmUserid, 2)
			}
		}
	}

	return out, nil
}

func (service *CustomerCenterService) MemberProductDiscount(ctx context.Context, req *cc.MemberProductDiscountRequest) (*cc.MemberProductDiscountResponse, error) {
	out := new(cc.MemberProductDiscountResponse)

	levelId := int64(-1)
	if len(req.UserId) > 0 {
		//查询用户当前级别
		member := models.UpetMember{}
		_, err := BbcEngine.Table("upet_member").Where("scrm_user_id=?", req.UserId).Get(&member)
		if err != nil {
			return nil, err
		}
		levelId = member.UserLevelId
	}
	var levelList []*models.UserLevel
	//查询特权列表
	err := DataCenterEngine.Table("user_level").Select("level_id,member_price").
		Where("level_status=1").OrderBy("id desc").Find(&levelList)
	if err != nil {
		return nil, err
	}
	var maxLevelDiscount string
	if len(levelList) > 0 {
		for _, level := range levelList {
			if levelId >= 0 && levelId == level.LevelId {
				maxLevelDiscount = level.MemberPrice
				break
			}
		}
	}
	out.Discount = maxLevelDiscount

	return out, nil
}

// 浏览会员中心：点击去完成，跳转到会员中心主页，当日不管还浏览了什么，只要返回任务中心，去完成按钮变为可领取，点击可领取，获得这一项任务的健康值。
// 健康值为后台配置的。领取成功，按钮变为灰色的已领取。
func (service *CustomerCenterService) TaskFinish(ctx context.Context, req *cc.TaskFinishReq) (*cc.BaseResponseNew, error) {
	out := new(cc.BaseResponseNew)
	//查询任务详情
	taskManage := models.UserTaskManage{}
	exists, _ := Engine.Table("datacenter.user_task_manage").Where("id=? and is_select=1", req.TaskId).Get(&taskManage)
	if !exists {
		out.Msg = "任务不存在"
		return out, errors.New("任务不存在")
	}

	//查询当天是否有任务记录
	startTime := time.Now().Format("2006-01-02")
	endTime := startTime + " 23:59:59"
	userTask := models.UserTask{}
	has, err := Engine.Table("datacenter.user_task").Where("task_id=?", req.TaskId).
		Where("user_id=? and create_time>=? and create_time<=?", req.ScrmUserid, startTime, endTime).Get(&userTask)

	if err != nil {
		out.Msg = err.Error()
		return out, err
	}

	if req.Type == 2 && !has {
		out.Msg = "该任务还未完成，请先完成任务"
		return out, errors.New("该任务还未完成，请先完成任务")
	}
	//完成任务
	if req.Type == 1 && !has {
		data := models.UserTask{
			TaskId: cast.ToInt8(req.TaskId),
			UserId: req.ScrmUserid,
			Status: 1,
		}

		_, err := Engine.Table("datacenter.user_task").Insert(&data)
		if err != nil {
			glog.Error("完成任务失败，参数，", kit.JsonEncode(req), err.Error())
			out.Msg = err.Error()
			return out, err
		}
	}
	//领取奖励
	if req.Type == 2 {
		session := Engine.NewSession()
		defer session.Close()
		//判断是否有领取过
		has, _ := session.Table("datacenter.user_task").Where("task_id=? and user_id=?", req.TaskId, req.ScrmUserid).
			Where("create_time>=? and create_time<=? and status=2", startTime, endTime).Exist()

		if has {
			out.Msg = "已领取过，明日再来哦"
			return out, errors.New("已领取过")
		}

		currentHealth := int64(0)
		memberInte := models.MemberIntegralInfo{}
		exist, _ := session.Table("datacenter.member_integral_info").Where("memberid=?", req.ScrmUserid).Get(&memberInte)
		if exist {
			currentHealth = int64(memberInte.HealthVal)
		}

		//开启事务
		session.Begin()
		update := models.UserTask{
			Status: 2,
		}
		_, err = session.Table("datacenter.user_task").Where("task_id=? and user_id=?", req.TaskId, req.ScrmUserid).
			Where("create_time>=? and create_time<=?", startTime, endTime).Update(&update)
		if err != nil {
			session.Rollback()
			glog.Error("领取奖券更新状态失败，参数，", req, err.Error())
			out.Msg = err.Error()
			return out, err
		}
		//加健康值
		_, err = session.Exec("INSERT INTO datacenter.member_integral_info (memberid,health_val) VALUES (?,?) ON DUPLICATE KEY UPDATE health_val = health_val+?", req.ScrmUserid, taskManage.TaskVal, taskManage.TaskVal)
		if err != nil {
			session.Rollback()
			glog.Error("完成任务增加健康值失败，参数，", kit.JsonEncode(req), err.Error())
			out.Msg = err.Error()
			return out, err
		}
		//加一条健康值明细记录
		healthDetail := models.HealthDetail{
			UserId:          req.ScrmUserid,
			Type:            1,
			Title:           "做升级任务",
			Content:         taskManage.TaskName,
			HealthVal:       cast.ToInt64(taskManage.TaskVal),
			PrevHealthCount: currentHealth,
			CurrHealthCount: currentHealth + int64(taskManage.TaskVal),
			HealthType:      3,
			PayAmount:       "0.00",
			RefundAmount:    "0.00",
			EffectTime:      time.Now(),
		}
		_, err = Engine.Table("datacenter.health_detail").Insert(&healthDetail)
		if err != nil {
			session.Rollback()
			glog.Error("完成任务增加健康值明细失败，参数，", kit.JsonEncode(req), err.Error())
			out.Msg = err.Error()
			return out, err
		}
		err = session.Commit()
		if err != nil {
			session.Rollback()
			glog.Error("完成任务失败，参数，", kit.JsonEncode(req), err.Error())
			out.Msg = err.Error()
			return out, err
		}
		// 检验是否触发会员等级变更
		go handleMemberLevel(req.ScrmUserid)
	}

	return out, nil
}

func (service *CustomerCenterService) EquityIndex(ctx context.Context, req *cc.EquityIndexReq) (*cc.EquityIndexRes, error) {
	out := new(cc.EquityIndexRes)
	//查会员等级列表
	var userLevel []*models.UserLevel
	err := Engine.Table("datacenter.user_level").Where("level_status=1").Find(&userLevel)
	if err != nil {
		glog.Error("查询等级列表失败：", err.Error())
		return out, err
	}
	//查询权益列表
	var equity []*models.UserEquity
	Engine.Table("datacenter.user_equity").Where("is_display=1").Find(&equity)

	for _, v := range userLevel {
		userLevelTmp := &cc.EquityIndex{
			LevelId:          cast.ToInt32(v.LevelId),
			LevelName:        v.LevelName,
			HealthVal:        cast.ToInt32(v.HealthVal),
			Background:       v.Background,
			CenterBackground: v.CenterBackground,
			LevelIcon:        v.LevelIcon,
		}
		//当前享受哪些权益
		var equityListTmp []*cc.EquityDataList
		privilegeIds := strings.Split(v.PrivilegeIds, ",")
		for _, eq := range equity {
			isSelect := 0
			//判断该等级是否有这个权益
			if utils.InArrayString(cast.ToString(eq.Id), privilegeIds) {
				isSelect = 1
			}

			equityTmp := &cc.EquityDataList{
				EquityName: eq.EquityName,
				Icon:       eq.Icon,
				IsSelect:   cast.ToInt32(isSelect),
				Id:         eq.Id,
			}

			equityListTmp = append(equityListTmp, equityTmp)
		}
		userLevelTmp.EquityList = equityListTmp
		out.Data = append(out.Data, userLevelTmp)
	}

	//判断是否登录
	if req.ScrmUserid != "" {
		//查询用户当前级别
		member := models.UpetMember{}
		_, err = BbcEngine.Table("upet_member").Where("scrm_user_id=?", req.ScrmUserid).Get(&member)
		if err != nil {
			out.Msg = err.Error()
			return out, err
		}
		//查询用户健康值
		model := models.MemberIntegralInfoNew{}
		_, err := Engine.Table("datacenter.member_integral_info").Where("memberid=?", req.ScrmUserid).Get(&model)
		if err != nil {
			out.Msg = err.Error()
			return out, err
		}
		out.LevelId = cast.ToInt32(member.UserLevelId)
		out.UserLevelEtime = cast.ToInt32(member.UserLevelEtime)
		out.HealthVal = model.HealthVal
		out.FreezeHealthVal = model.FreezeHealthVal

		//微信订阅消息开启状态
		notification := models.WechatSubscribeNotification{}
		_, err = Engine.Table("datacenter.wechat_subscribe_notification").Where("scrm_user_id=?", req.ScrmUserid).Get(&notification)
		if err != nil {
			out.Msg = err.Error()
			return out, err
		}
		out.NotificationState = int32(notification.UserLevel)
	}

	return out, nil
}

func (service *CustomerCenterService) getDbEngine() *xorm.Engine {
	return NewEngine()
}

// 查询标签
func (service *CustomerCenterService) QueryTags(ctx context.Context, req *cc.TagsQueryRequest) (*cc.TagsQueryResponse, error) {
	var response = &cc.TagsQueryResponse{Code: 200}

	var tags []models.Tags

	var query = service.getDbEngine().Table(&models.Tags{})

	if req.Groups > 0 {
		query = query.Where("`groups`=?", req.Groups)
	}

	// 内容中心只保留性别标签
	if req.From == "content" {
		query.Where("name = '性别'")
	}

	err := query.Asc("sort").Find(&tags)
	if err == nil {
		for _, tag := range tags {
			response.Data = append(response.Data, tag.ToTagsDto())
		}
	} else {
		response.Code = 500
		response.Message = err.Error()
	}

	return response, err
}

// 查询宠物标签信息
func (service *CustomerCenterService) QueryPetTag(ctx context.Context, req *cc.PetTagQueryRequest) (*cc.PetTagQueryResponse, error) {
	var userPetTagService = new(UserPetTagService)
	// 是否传递用户ID
	if len(req.UserId) > 0 {
		userPetTagService.GeneratePetTagByUserId(req.UserId)
	}

	res, err := userPetTagService.QueryPetTag(req.UserId, req.PetId)
	if len(res.Data) == 0 {
		if len(req.PetId) > 0 {
			userPetTagService.GeneratePetTagByPetId(req.PetId)
		}
		res, err = userPetTagService.QueryPetTag(req.UserId, req.PetId)
	}
	return res, err
}

// 生成宠物标签
func (service *CustomerCenterService) GeneratePetTag(ctx context.Context, req *cc.PetTagGenerateRequest) (*cc.BaseResponse, error) {
	var response = &cc.BaseResponse{Code: 200}
	var userPetTagService = new(UserPetTagService)
	if len(req.UserId) > 0 {
		userPetTagService.GeneratePetTagByUserId(req.UserId)
	}
	if len(req.PetId) > 0 {
		userPetTagService.GeneratePetTagByUserId(req.PetId)
	}
	return response, nil
}

// 查询用户的推荐商品--常购记录
func (service *CustomerCenterService) QueryUsuallyRecordProduct(ctx context.Context, in *cc.RecommendProductQueryRequest) (*cc.QueryUsuallyRecordProductResponse, error) {
	var response = &cc.QueryUsuallyRecordProductResponse{Code: 200}
	var list []models.UserBuy
	err := Engine.Table("user_buy").Where("user_id=?", in.UserId).Limit(int(in.PageSize), int((in.PageIndex-1)*in.PageSize)).OrderBy("number desc").Find(&list)
	if err != nil {
		response.Code = 400
		response.Message = err.Error()
		return response, err
	}
	for _, v := range list {
		var model cc.ProductInfo
		model.SkuId = int32(v.Sku)
		model.ChannelId = int32(v.ChannelId)
		model.SalesVolume = int32(v.Number)
		response.Data = append(response.Data, &model)
	}
	return response, nil
}

// todo 常购记录 手动执行
func (service *CustomerCenterService) ManualQueryUsuallyRecordProduct(ctx context.Context, in *cc.ManualQueryUsuallyRecordProductRequest) (*cc.ManualQueryUsuallyRecordProductResponse, error) {
	var o Order
	var out = new(cc.ManualQueryUsuallyRecordProductResponse)
	out.Code = 200
	if len(in.StartTime) <= 0 || len(in.EndTime) <= 0 {
		out.Code = 400
		out.Message = "请求参数不能为空"
		return out, nil
	}
	//实物订单
	o.CreateUserBuyOrders(in.StartTime, in.EndTime)
	//虚拟订单
	o.CreateUserBuyOrdersToFictitious(in.StartTime, in.EndTime)
	return out, nil
}

// 根据地址ID查询地址详情
func (service *CustomerCenterService) GetAddressInfo(ctx context.Context, in *cc.GetAddressInfoRequest) (*cc.GetAddressInfoResponse, error) {
	out := &cc.GetAddressInfoResponse{Code: 200}
	model := &models.UpetAddress{}
	_, err := BbcEngine.Table("upet_address").Where("address_id=?", in.AddressId).Get(model)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return out, err
	}

	out.Data = &cc.AddressInfoRequest{
		AddressId:   cast.ToString(model.AddressId),
		TrueName:    model.TrueName,
		AreaId:      cast.ToString(model.AreaId),
		CityId:      cast.ToString(model.CityId),
		AreaInfo:    model.AreaInfo,
		Address:     model.Address,
		TelPhone:    utils.MobileReplaceWithStar(model.TelPhone),
		MobPhone:    utils.MobileReplaceWithStar(model.MobPhone),
		IsDefault:   cast.ToString(model.IsDefault),
		DlypId:      cast.ToString(model.DlypId),
		TxLat:       float32(model.AreaTxlat),
		TxLng:       float32(model.AreaTxlng),
		AddressDesc: model.AddressDesc,
		HouseInfo:   model.HouseInfo,
		AreaAdcode:  cast.ToString(model.AreaAdcode),
		MapAddress:  model.MapAddress,
	}
	return out, nil
}

func (service *CustomerCenterService) AddAddress(ctx context.Context, in *cc.AddressInfoRequest) (*cc.ResResponse, error) {
	out := &cc.ResResponse{Code: 200}
	if len(in.ScrmUserId) == 0 {
		out.Code = 400
		out.Error = "用户ID不能为空"
		out.Message = "用户ID不能为空"
		return out, nil
	}

	member := &models.UpetMember{}
	if in.OrgId == SAASMainId {
		member.MemberId = cast.ToInt64(in.ScrmUserId)
	} else {
		has, err := BbcEngine.Table("upet_member").Select("member_id").Where("scrm_user_id=?", in.ScrmUserId).Get(member)
		if !has || err != nil {
			out.Code = 400
			out.Error = "会员不存在"
			out.Message = "未查询到会员信息"
			return out, err
		}
	}

	// 开启事物
	session := BbcEngine.NewSession()
	defer session.Close()
	session.Begin()

	if cast.ToInt32(in.IsDefault) == 1 {
		//默认地址只能有一个，其他地址is_default置为0
		session.Exec("update `upet_address` set is_default=0 where member_id = ?", member.MemberId)
	}
	model := models.UpetAddress{
		TrueName:    in.TrueName,
		AreaId:      cast.ToInt32(in.AreaId),
		CityId:      cast.ToInt32(in.CityId),
		AreaInfo:    in.AreaInfo,
		Address:     in.Address,
		MobPhone:    in.MobPhone,
		IsDefault:   cast.ToInt32(in.IsDefault),
		AreaTxlat:   float64(in.TxLat),
		AreaTxlng:   float64(in.TxLng),
		HouseInfo:   in.HouseInfo,
		MemberId:    cast.ToInt64(member.MemberId),
		MapAddress:  in.MapAddress,
		AreaAdcode:  cast.ToInt32(in.Adcode),
		AddressDesc: in.AddressDesc,
		StoreId:     in.OrgId,
	}

	_ = model.UpdateAreaInfo(BbcEngine)

	if in.TxLng > 0 && in.TxLat > 0 {
		model.Isdeal = 1
	}
	session.Insert(&model)
	err := session.Commit()
	if err != nil {
		session.Rollback()
		out.Code = 400
		out.Message = "保存数据失败"
		return out, nil
	}
	return out, nil
}

func (service *CustomerCenterService) UpdateAddress(ctx context.Context, in *cc.AddressInfoRequest) (*cc.ResResponse, error) {
	out := &cc.ResResponse{Code: 200}
	if len(in.ScrmUserId) == 0 {
		out.Code = 400
		out.Error = "用户ID不能为空"
		out.Message = "用户ID不能为空"
		return out, nil
	}
	member := &models.UpetMember{}
	if in.OrgId == SAASMainId {
		member.MemberId = cast.ToInt64(in.ScrmUserId)
	} else {
		has, err := BbcEngine.Table("upet_member").Select("member_id").Where("scrm_user_id=?", in.ScrmUserId).Get(member)
		if !has || err != nil {
			out.Code = 400
			out.Error = "会员不存在"
			out.Message = "未查询到会员信息"
			return out, err
		}
	}

	model := &models.UpetAddress{
		TrueName:    in.TrueName,
		AreaId:      cast.ToInt32(in.AreaId),
		CityId:      cast.ToInt32(in.CityId),
		AreaInfo:    in.AreaInfo,
		Address:     in.Address,
		MobPhone:    in.MobPhone,
		IsDefault:   cast.ToInt32(in.IsDefault),
		AreaTxlat:   float64(in.TxLat),
		AreaTxlng:   float64(in.TxLng),
		HouseInfo:   in.HouseInfo,
		AreaAdcode:  cast.ToInt32(in.Adcode),
		AddressDesc: in.AddressDesc,
		MapAddress:  in.MapAddress,
	}

	_ = model.UpdateAreaInfo(BbcEngine)

	if in.TxLng > 0 && in.TxLat > 0 {
		model.Isdeal = 1
	}
	// 开启事物
	session := BbcEngine.NewSession()
	defer session.Close()
	session.Begin()

	if cast.ToInt32(in.IsDefault) == 1 {
		//默认地址只能有一个，其他地址is_default置为0
		session.Exec("update `upet_address` set is_default=0 where member_id = ?", member.MemberId)
	}

	session.Table("upet_address").Where("address_id=?", in.AddressId).Update(model)
	err := session.Commit()
	if err != nil {
		session.Rollback()
		out.Code = 400
		out.Error = err.Error()
		out.Message = "保存数据失败"
		return out, nil
	}
	return out, nil
}

func (service *CustomerCenterService) DelAddress(ctx context.Context, in *cc.GetAddressInfoRequest) (*cc.ResResponse, error) {
	out := &cc.ResResponse{Code: 200}
	_, err := BbcEngine.Table("upet_address").Where("address_id=?", in.AddressId).Delete(&models.UpetAddress{})
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = "删除地址失败"
		return out, err
	}
	return out, nil
}

func (service *CustomerCenterService) GetAddressList(ctx context.Context, in *cc.GetAddressListRequest) (*cc.GetAddressListResponse, error) {
	out := &cc.GetAddressListResponse{Code: 200}
	member := &models.UpetMember{}
	if in.OrgId == SAASMainId {
		member.MemberId = cast.ToInt64(in.ScrmUserId)
	} else {
		has, err := BbcEngine.Table("upet_member").Select("member_id").Where("scrm_user_id=?", in.ScrmUserId).Get(member)
		if !has || err != nil {
			out.Code = 400
			out.Message = "未查询到会员信息"
			return out, err
		}
	}
	var model []*models.UpetAddress
	err := BbcEngine.Table("upet_address").Where("member_id=?", member.MemberId).Find(&model)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return out, err
	}

	for _, v := range model {
		address := &cc.AddressList{
			AddressId:   cast.ToString(v.AddressId),
			MemberId:    cast.ToString(v.MemberId),
			TrueName:    v.TrueName,
			AreaId:      cast.ToString(v.AreaId),
			CityId:      cast.ToString(v.CityId),
			AreaInfo:    v.AreaInfo,
			Address:     v.Address,
			TelPhone:    v.TelPhone,
			MobPhone:    v.MobPhone,
			IsDefault:   cast.ToString(v.IsDefault),
			DlypId:      cast.ToString(v.DlypId),
			AreaLat:     cast.ToString(v.AreaLat),
			AreaLng:     cast.ToString(v.AreaLng),
			AreaTxlng:   cast.ToString(v.AreaTxlng),
			AreaTxlat:   cast.ToString(v.AreaTxlat),
			Isdeal:      cast.ToString(v.Isdeal),
			AddressDesc: v.AddressDesc,
			HouseInfo:   v.HouseInfo,
			AreaAdcode:  cast.ToString(v.AreaAdcode),
			MapAddress:  v.MapAddress,
		}

		out.Data = append(out.Data, address)
	}

	return out, nil
}

// 内容标签查询
func (service *CustomerCenterService) QueryContentTag(ctx context.Context, in *cc.ContentTagRequest) (out *cc.ContentTagResponse, err error) {
	out = new(cc.ContentTagResponse)

	var tags []models.Tags
	db := service.getDbEngine()
	err = db.Where("`groups` = 2 and org_id = ?", in.OrgId).Find(&tags)
	if err != nil {
		out.Code = 400
		out.Message = "查询失败，" + err.Error()
		return
	}

	if len(tags) > 0 {
		for k, _ := range tags {
			out.Data = append(out.Data, &cc.TagQueryData{
				Name: tags[k].Name,
				Tags: tags[k].Value,
			})
		}
	}

	out.Code = 200
	return
}

// 内容标签增,删,改操作
func (service *CustomerCenterService) TagOperate(ctx context.Context, in *cc.TagOperateRequest) (out *cc.TagOperateResponse, err error) {
	out = new(cc.TagOperateResponse)
	out.Code = 400
	var tags models.Tags
	db := service.getDbEngine()
	db.Where("name = ? and org_id = ?", in.GroupName, in.OrgId).Get(&tags)

	switch in.Opt {
	case "delete":
		if tags.Id < 1 {
			out.Message = "删除失败，分组不存在！"
			return
		}
		// 检测标签或分组是否绑定了文章
		if articleIds, errMsg := service.isBindArticle(in.GroupName, in.Name); errMsg != "" || len(articleIds) > 0 {
			if len(articleIds) > 0 {
				errMsg = "已绑定文章，请解绑后再删除！"
			}
			out.Message = errMsg
			out.ArticleIds = articleIds
			return
		}
		if in.Name != "" { // 标签删除
			if !service.isExistTagName(tags.Value, in.Name) {
				out.Message = "标签名不存在或已删除！"
				return
			}
			tags.Value = service.getEditTagValueStr(tags.Value, in.Name, "")
			_, err = db.ID(tags.Id).Cols("value").Update(&tags)
		} else { // 分组删除
			_, err = db.ID(tags.Id).Delete(&tags)
		}
		if err != nil {
			out.Message = "删除失败，" + err.Error()
			return
		}

	case "update":
		// 标签名修改
		if tags.Id < 1 {
			out.Message = "标签对应的分组不存在，无法修改！"
			return
		}
		if !service.isExistTagName(tags.Value, in.OldName) {
			out.Message = "请检查原标签名是否正确！"
			return
		}
		if service.isExistTagName(tags.Value, in.Name) {
			out.Message = "标签名已存在！"
			return
		}
		// 检测标签或分组是否绑定了文章
		if articleIds, errMsg := service.isBindArticle(in.GroupName, in.OldName); errMsg != "" || len(articleIds) > 0 {
			if len(articleIds) > 0 {
				errMsg = "标签已绑定文章，请解绑后再更新！"
			}
			out.Message = errMsg
			out.ArticleIds = articleIds
			return
		}
		tags.Value = service.getEditTagValueStr(tags.Value, in.OldName, in.Name)
		_, err = db.ID(tags.Id).Cols("value").Update(tags)
		if err != nil {
			out.Message = "修改标签失败，" + err.Error()
			return
		}

	case "add":
		if in.Name != "" {
			// 新增标签名
			if tags.Id < 1 {
				out.Message = "标签新增失败，请检查分组是否存在！"
				return
			}
			if service.isExistTagName(tags.Value, in.Name) {
				out.Message = "标签名已存在！"
				return
			}
			arrValue := strings.Split(strings.Trim(tags.Value, ","), ",")
			if len(arrValue) >= 20 {
				out.Message = "标签最多20个"
				return
			}
			_, err = db.ID(tags.Id).Cols("value").Update(&models.Tags{
				Value: tags.Value + "," + in.Name,
			})
			if err != nil {
				out.Message = "新增标签失败，！" + err.Error()
				return
			}

		} else {
			// 新增分组名
			if tags.Id > 0 {
				out.Message = "分组名已存在！"
				return
			}
			// 标签最多20，标签组最多15
			tagCount, err := db.Where("`groups` = 2").Count(&models.Tags{})
			if tagCount >= 15 {
				out.Message = "标签组最多15个"
			}
			_, err = db.Insert(&models.Tags{
				Name:        in.GroupName,
				Value:       "",
				HasAsterisk: 0,
				Sort:        10,
				Groups:      2, // 内容标签
				OrgId:       cast.ToInt(in.OrgId),
			})
			if err != nil {
				out.Message = "新增分组失败，！" + err.Error()
			}
		}
	default:
		out.Message = "未知错误"
		return
	}

	out.Code = 200
	out.Message = "操作成功！"
	return
}

// 检测标签是否已经绑定文章
func (service *CustomerCenterService) isBindArticle(groupName string, name string) ([]int32, string) {
	dbContent := ContentCenterNewEngine()
	var err error
	errMsg := ""
	var articleIds []int32
	if name != "" {
		err = dbContent.SQL("select article_id from article_tag where content_tag like '%" + name + "%'").Find(&articleIds)
	} else {
		err = dbContent.SQL("select article_id from article_tag where content_tag like '%" + groupName + "%'").Find(&articleIds)
	}
	if err != nil {
		errMsg = "查询标签绑定的文章id失败，" + err.Error()
		return articleIds, errMsg
	}
	return articleIds, errMsg
}

// 检测标签是否存在
func (service *CustomerCenterService) isExistTagName(value string, findStr string) bool {
	if value != "" {
		arrStr := strings.Split(value, ",")
		for _, v := range arrStr {
			if v == findStr {
				return true
			}
		}
	}
	return false
}

// 获取拼接后的标签字符串，用于存储
func (service *CustomerCenterService) getEditTagValueStr(value string, oldName string, newName string) string {
	str := ""
	if value == "" {
		return str
	}
	arrStr := strings.Split(value, ",")
	for _, v := range arrStr {
		if v == oldName {
			v = newName
		}
		if v != "" {
			str = str + "," + v
		}
	}
	return strings.Trim(str, ",")
}

// 查用户获得的券
func getVoucherByUserid(scrmUserid string, couponType int32) []*cc.EquityCoupon {
	var out []*cc.EquityCoupon
	//查用户当前级别 和 对应级别配置的券ID(比如用户是7级，就要显示7级对应的券)
	userLevel := models.UserLevel{}
	exist, err := BbcEngine.Table("upet_member").Alias("m").
		Join("left", "datacenter.user_level as u", "m.user_level_id=u.level_id").
		Where("m.scrm_user_id=?", scrmUserid).Get(&userLevel)

	if err != nil {
		glog.Error("通过用户等级获取券失败：", err.Error(), "用户id:", scrmUserid)
		return out
	}

	if !exist || (userLevel.GoodsUpgradeVouchers == "" && userLevel.StoreUpgradeVouchers == "" && userLevel.StoreWeekVouchers == "") {
		return out
	}
	//查询本周是否有升级记录
	////查询最后一条等级变更记录
	memberLevelLog := models.UpetMemberLevelLog{}
	BbcEngine.Table("upet_member_level_log").Where("scrm_user_id=?", scrmUserid).OrderBy("create_time desc").Get(&memberLevelLog)
	startDate := memberLevelLog.CreateTime
	endDate := memberLevelLog.CreateTime.AddDate(0, 0, 7)

	if couponType == 1 {
		//门店券
		//判断等级是否有配置升级券和周券（升级券优先展示）
		if userLevel.StoreUpgradeVouchers == "" && userLevel.StoreWeekVouchers == "" {
			return out
		}
		//到店券，如果本周有升级记录，就显示升级券，没配就不显示
		var voucherTid []string

		isUpdate := 0 //0周期券  1升级券
		voucherTid = strings.Split(userLevel.StoreWeekVouchers, ",")
		if memberLevelLog.LiftType == 1 && memberLevelLog.CreateTime.AddDate(0, 0, 7).Unix() > time.Now().Unix() {
			if userLevel.StoreUpgradeVouchers == "" {
				return out
			}
			isUpdate = 1
			voucherTid = strings.Split(userLevel.StoreUpgradeVouchers, ",")
		}

		if len(voucherTid) == 0 {
			return out
		}
		type PeriodValidityInfo struct {
			Type           int32  `json:"type"`
			AfterDay       int64  `json:"after_day"`
			PeriodValidity int64  `json:"period_validity"`
			BeginTime      string `json:"begin_time,omitempty"`
			EndTime        string `json:"end_time,omitempty"`
		}

		//获取券详情
		for _, vid := range voucherTid {
			_, template, err := zilong.CouponTemplateDetail(cast.ToInt32(vid))
			if err != nil {
				glog.Error("获取门店券失败1：", err.Error(), "参数：", voucherTid, "用户id:", scrmUserid)
				return out
			} else if template == nil {
				glog.Error("获取门店券失败2：", kit.JsonEncode(template), "参数：", voucherTid, "用户id:", scrmUserid)
				return out
			}
			//有效期
			var periodValidityInfo PeriodValidityInfo
			err = json.Unmarshal([]byte(template.PeriodValidity), &periodValidityInfo)
			if err != nil {
				glog.Error("获取门店券失败3：", err.Error(), "参数：", voucherTid, "用户id:", scrmUserid)
				return out
			}
			if periodValidityInfo.Type == 1 {
				periodValidityInfo.PeriodValidity = 0
			} else {
				periodValidityInfo.BeginTime = ""
				periodValidityInfo.EndTime = ""
			}
			tmp := cc.EquityCoupon{
				CouponId:             cast.ToString(vid),
				CouponName:           template.TemplateName,
				VoucherTPrice:        cast.ToInt32(kit.YuanToFen(cast.ToFloat64(template.TemplateValue))),
				VoucherDays:          periodValidityInfo.PeriodValidity,
				VoucherStartDateText: periodValidityInfo.BeginTime,
				VoucherEndDateText:   periodValidityInfo.EndTime,
				Type:                 periodValidityInfo.Type,
			}
			//查券的状态
			has := false
			if isUpdate == 1 {
				has, _ = Engine.Table("datacenter.user_coupon").Where("user_id=?", scrmUserid).
					Where("coupon_id=? and coupon_type=1", vid).
					Where("create_time>=? and create_time<=?", startDate.Format("2006-01-02 15:04:05"), endDate.Format("2006-01-02 15:04:05")).Exist()
			} else {
				has, _ = Engine.Table("datacenter.user_coupon").Where("user_id=?", scrmUserid).Where("coupon_id=? and coupon_type=1", vid).
					Where("create_time>=? and create_time<=?", utils.GetFirstDateOfWeek()+" 00:00:00", utils.GetLastWeekDate()+" 23:59:59").Exist()
			}

			if !has {
				tmp.Status = 1
				//判断是否过期
				if template.Status == "已过期" {
					tmp.Status = 4
				}
				//判断是否已失效
				if template.Status == "待提交" || template.Status == "审核中" {
					tmp.Status = 3
				}
				//判断是否已抢光
				if template.Inventory <= 0 {
					tmp.Status = 5
				}
			} else {
				tmp.Status = 2
			}

			out = append(out, &tmp)
		}

	} else { //2是商城券(商城券没有周特权券,只有升级券)
		//有升级记录但升级券配置为空，则不展示券
		//判断最后一条等级变更记录是不是降级，是则不展示升级券
		//如果本周(升级时间一周内)没有升级记录，也不显示
		//超过一周不展示升级券
		if memberLevelLog.LiftType == 1 && memberLevelLog.CreateTime.AddDate(0, 0, 7).Unix() < time.Now().Unix() {
			return out
		}

		if memberLevelLog.Id == 0 || memberLevelLog.LiftType == 2 || userLevel.GoodsUpgradeVouchers == "" {
			return out
		}

		voucherTid := strings.Split(userLevel.GoodsUpgradeVouchers, ",")
		//查代金券信息
		for _, vid := range voucherTid {
			voucherTemp := models.UpetVoucherTemplate{}
			_, err := BbcEngine.Table("upet_voucher_template").Where("voucher_t_id=?", vid).Get(&voucherTemp)
			if err != nil {
				glog.Error("通过券ID查券模板信息失败：", err.Error(), "参数：", voucherTid, "用户id:", scrmUserid)
				return out
			}
			//ts, err := strconv.ParseInt(, 10, 64)
			startTime := time.Unix(voucherTemp.VoucherTStartDate, 0).Format("2006.01.02")
			endTime := time.Unix(voucherTemp.VoucherTEndDate, 0).Format("2006.01.02")
			tmp := cc.EquityCoupon{
				CouponId:             cast.ToString(vid),
				CouponName:           voucherTemp.VoucherTTitle,
				VoucherTPrice:        cast.ToInt32(voucherTemp.VoucherTPrice),
				VoucherTLimit:        cast.ToFloat32(voucherTemp.VoucherTLimit),
				VoucherStartDateText: startTime,
				VoucherEndDateText:   endTime,
				VoucherDays:          voucherTemp.VoucherDays,
			}
			//查券的状态
			voucherInfo := models.UpetVoucher{}
			has, err := BbcEngine.Table("upet_voucher").Alias("v").
				Join("left", "upet_member as m", "v.voucher_owner_id=m.member_id").
				Where("m.scrm_user_id=? and voucher_t_id=?", scrmUserid, vid).
				Where("v.voucher_active_date>=? and v.voucher_active_date<=?", startDate.Unix(), endDate.Unix()).Get(&voucherInfo)

			if err != nil {
				glog.Error("通过券ID查用户获得的券失败2：", err.Error(), "参数：", voucherTid, "用户id:", scrmUserid)
				return out
			}

			if !has {
				///tmp.ApplicableScope = cast.ToInt32(voucherInfo.VoucherTType)
				tmp.Status = 1
				//判断是否已失效
				if voucherTemp.VoucherTState == 2 {
					tmp.Status = 3
				}
				//判断是否已抢光
				if voucherTemp.VoucherTGiveout >= voucherTemp.VoucherTTotal {
					tmp.Status = 5
				}
			} else {
				tmp.Status = 2
				tmp.ApplicableScope = cast.ToInt32(voucherInfo.VoucherTType)
			}

			out = append(out, &tmp)
		}
	}

	return out
}

func (service *CustomerCenterService) MemberEquitySet(ctx context.Context, req *cc.MemberEquitySetReq) (*cc.BaseResponseNew, error) {
	out := new(cc.BaseResponseNew)

	db := service.getDbEngine()
	data := models.UserEquity{
		IsDisplay: req.IsDisplay,
	}
	_, err := db.Table("datacenter.user_equity").Where("id=?", req.Id).Cols("is_display").Update(&data)
	if err != nil {
		glog.Error("提交失败：", err.Error())
	}

	return out, nil
}

func (service *CustomerCenterService) EquityGetcoupon(ctx context.Context, req *cc.EquityGetcouponReq) (*cc.BaseResponseNew, error) {
	out := cc.BaseResponseNew{}
	member := models.UpetMember{}
	_, err := BbcEngine.Table("upet_member").Where("scrm_user_id=?", req.ScrmUserid).Get(&member)

	if err != nil {
		glog.Error("领券接口查询用户信息失败，参数：", kit.JsonEncode(req), err.Error())
		return &out, err
	}

	startDate := utils.GetFirstDateOfWeek() + " 00:00:00"
	endDate := utils.GetLastWeekDate() + " 23:59:59"
	//查询传过来的券是否在用户当前级别配置的券之中
	couponInfo := models.UserLevel{}
	coupon := "%" + cast.ToString(req.CouponId) + "%"
	if req.CouponType == 1 {
		exist, _ := Engine.Table("datacenter.user_level").
			Where("level_id=? and (store_upgrade_vouchers like ? or store_week_vouchers like ?)", member.UserLevelId, coupon, coupon).Get(&couponInfo)

		if !exist {
			out.Msg = "券ID不正确"
			return &out, nil
		}
	} else {
		exist, _ := Engine.Table("datacenter.user_level").
			Where("level_id=? and goods_upgrade_vouchers like ?", member.UserLevelId, coupon).Get(&couponInfo)

		if !exist {
			out.Msg = "券ID不正确!"
			return &out, nil
		}
	}

	//判断传过来的券是升级券还是周特权券
	voucherType := 2 //1是升级券  2是周特权券
	//有一个特殊情况，goods_upgrade_vouchers store_upgrade_vouchers store_week_vouchers 这三个字段都配置了同一个券ID
	isExt, _ := Engine.Table("datacenter.user_level").
		Where("level_id=? and store_upgrade_vouchers like ? and goods_upgrade_vouchers like ? and store_week_vouchers like ?", member.UserLevelId, coupon, coupon, coupon).Exist()

	if isExt {
		//查询有没有升级记录，有且没领则是升级券
		memberLevelLog := models.UpetMemberLevelLog{}
		upLog, _ := BbcEngine.Table("upet_member_level_log").Where("scrm_user_id=? and lift_type=1", req.ScrmUserid).OrderBy("create_time desc").Get(&memberLevelLog)
		if upLog {
			//查询有没有领这个升级券
			upCoupon, _ := Engine.Table("datacenter.user_coupon").Where("user_id=?", req.ScrmUserid).
				Where("coupon_id=? and type=1", req.CouponId).Exist()

			if !upCoupon {
				voucherType = 1
			}
		}
	} else {
		ext, _ := Engine.Table("datacenter.user_level").
			Where("level_id=? and (store_upgrade_vouchers like ? or goods_upgrade_vouchers like ?)", member.UserLevelId, coupon, coupon).Get(&couponInfo)
		if ext {
			voucherType = 1
		}
	}

	if req.CouponType == 1 {
		if voucherType == 1 { //到店券且是升级券
			//四选一只发一次
			has, _ := Engine.Table("datacenter.user_coupon").Where("user_id=?", req.ScrmUserid).
				Where("coupon_id=? and coupon_type=1 and level_id=?", req.CouponId, member.UserLevelId).Exist()
			if has {
				out.Msg = "已经领取过了!"
				return &out, nil
			}
		} else { //到店券且是周特权券
			//四选一每周发一次
			has, _ := Engine.Table("datacenter.user_coupon").Where("user_id=? and coupon_type=1", req.ScrmUserid).
				Where("coupon_id=?", req.CouponId).Where("create_time>=? and create_time<=?", startDate, endDate).Exist()
			if has {
				out.Msg = "已经领取过了，下周再来吧"
				return &out, nil
			}
		}
	} else {
		//商城券且是升级券（商城券没有周特权）
		if voucherType == 1 {
			//只发一次
			has, _ := Engine.Table("datacenter.user_coupon").Where("user_id=?", req.ScrmUserid).
				Where("coupon_id=? and coupon_type=2", req.CouponId).Exist()
			if has {
				out.Msg = "已经领取过了。"
				return &out, nil
			}
		} else {
			out.Msg = "商城券没有周特权券。"
			return &out, nil
		}
	}

	//调用发券的接口
	client := ac.GetActivityCenterClient()
	//商城券如果是限领多张，就调多次
	if req.CouponType == 2 {
		voucherTemp := models.UpetVoucherTemplate{}
		_, err := BbcEngine.Table("upet_voucher_template").Where("voucher_t_id=?", req.CouponId).Get(&voucherTemp)
		if err != nil {
			glog.Error("通过券ID查券模板信息异常：", err.Error(), "参数：", req.CouponId, "用户id:", req.ScrmUserid)
			return &out, err
		}

		for i := int64(0); i < voucherTemp.VoucherTEachlimit; i++ {
			res, err := client.RPC.HandOutCouponData(context.Background(), &ac.HandOutCouponRequest{
				CouponId: cast.ToString(req.CouponId),
				Type:     req.CouponType,
				Mobile:   member.MemberMobile,
				From:     9,
			})

			if err != nil {
				glog.Error("发券失败：", err.Error(), "参数：", kit.JsonEncode(req), "用户id:", req.ScrmUserid)
				out.Msg = err.Error()
				return &out, nil
			}
			//获取券的有效期开始时间和结束时间
			upetVoucher := models.UpetVoucher{}
			_, err = BbcEngine.Table("upet_voucher").Alias("v").
				Join("left", "upet_member as m", "v.voucher_owner_id=m.member_id").
				Where("v.voucher_t_id=? and v.voucher_from=9 and m.scrm_user_id=?", req.CouponId, req.ScrmUserid).
				OrderBy("v.voucher_id desc").Get(&upetVoucher)

			if err != nil {
				glog.Error("通过券ID查券有效期异常：", err.Error(), "参数：", req.CouponId, "用户id:", req.ScrmUserid)
				return &out, err
			}
			//插入一条领取记录
			data := models.UserCoupon{
				CouponType:        cast.ToInt8(req.CouponType),
				CouponId:          req.CouponId,
				UserId:            req.ScrmUserid,
				VoucherId:         res.CouponCode,
				Type:              cast.ToInt8(voucherType),
				LevelId:           cast.ToInt8(member.UserLevelId),
				VoucherTStartDate: cast.ToInt32(upetVoucher.VoucherStartDate),
				VoucherTEndDate:   cast.ToInt32(upetVoucher.VoucherEndDate),
			}

			_, err = Engine.Table("datacenter.user_coupon").Insert(&data)
			if err != nil {
				glog.Error("插入领券记录失败，参数：", kit.JsonEncode(req), err)
				return &out, err
			}
		}
	} else {
		params := zilong.CouponSendMultiReq{
			Number:        1,
			PhoneArr:      []string{member.MemberMobile},
			TemplateIdArr: []int32{req.CouponId},
		}
		_, result, err := zilong.CouponSendMulti(&params)
		glog.Error("发放门店券北京接口返回：", kit.JsonEncode(result), err)
		if err != nil {
			glog.Error("发券失败：", err.Error(), "参数：", kit.JsonEncode(req), "用户id:", req.ScrmUserid)
			out.Msg = err.Error()
			return &out, nil
		}

		//插入一条领取记录
		data := models.UserCoupon{
			CouponType: cast.ToInt8(req.CouponType),
			CouponId:   req.CouponId,
			UserId:     req.ScrmUserid,
			Type:       cast.ToInt8(voucherType),
			LevelId:    cast.ToInt8(member.UserLevelId),
		}

		if len(result) > 0 && len(result[0].CouponList) > 0 {
			data.CouponCode = result[0].CouponList[0].CouponCode
			//兼容北京接口返回的时间格式不定（有的带时分秒，有的不带）
			if len(result[0].PeriodValidityBeginTime) <= 10 {
				result[0].PeriodValidityBeginTime = result[0].PeriodValidityBeginTime + " 00:00:00"
			}

			if len(result[0].PeriodValidityEndTime) <= 10 {
				result[0].PeriodValidityEndTime = result[0].PeriodValidityEndTime + " 23:59:59"
			}

			voucherTStartDate, err := utils.StrToTime(result[0].PeriodValidityBeginTime)
			if err == nil {
				data.VoucherTStartDate = cast.ToInt32(voucherTStartDate.Unix())
			}
			voucherTEndDate, err := utils.StrToTime(result[0].PeriodValidityEndTime)
			if err == nil {
				data.VoucherTEndDate = cast.ToInt32(voucherTEndDate.Unix())
			}
		}

		_, err = Engine.Table("datacenter.user_coupon").Insert(&data)
		if err != nil {
			glog.Error("插入领券记录失败，参数：", kit.JsonEncode(req), err)
			return &out, err
		}
	}

	return &out, nil
}

func (service *CustomerCenterService) MessageInfo(ctx context.Context, req *cc.MessageInfoRequest) (*cc.MessageInfoResponse, error) {
	out := cc.MessageInfoResponse{}
	var templates []*models.WechatSubscribeMessageTemplate
	err := DataCenterEngine.Table("wechat_subscribe_message_template").Where("store_id = ?", req.OrgId).Find(&templates)

	if err != nil {
		glog.Error("查询订阅通知模板，参数：", kit.JsonEncode(req), err.Error())
		return &out, err
	}
	var templateIds []string
	for _, i := range templates {
		templateIds = append(templateIds, i.TemplateId)
	}
	var subscribes []models.WechatSubscribeMessage
	err = DataCenterEngine.Table("wechat_subscribe_message").Where("scrm_user_id = ?", req.UserId).In("template_id", templateIds).Find(&subscribes)
	if err != nil {
		glog.Error("查询用户订阅情况，参数：", kit.JsonEncode(req), err.Error())
		return &out, err
	}

	subscribeMessagMap := make(map[string]models.WechatSubscribeMessage)

	for _, s := range subscribes {
		subscribeMessagMap[s.TemplateId] = s
	}

	for _, j := range templates {
		messageInfo := &cc.MessageInfo{
			TemplateId:   j.TemplateId,
			MessageType:  j.TemplateType,
			TemplateKey:  j.TemplateKey,
			MessageTitle: utils.MessageTitle[j.TemplateKey],
			Number:       0,
		}
		if _, ok := subscribeMessagMap[j.TemplateId]; ok {
			messageInfo.Number = cast.ToInt32(subscribeMessagMap[j.TemplateId].Number)
		}
		switch j.TemplateType {
		case 1:
			out.StatusData = append(out.StatusData, messageInfo)
		case 2, 3, 7, 8, 9:
			out.PropertyData = append(out.PropertyData, messageInfo)
		case 4, 5:
			out.BusinessData = append(out.BusinessData, messageInfo)
		}
	}

	return &out, nil
}

func (service *CustomerCenterService) MemberCouponsMessage(ctx context.Context, req *cc.MessageInfoRequest) (*cc.MemberCouponsMessageResponse, error) {
	out := cc.MemberCouponsMessageResponse{}

	//会员权益后台配置为没有赠券时或隐藏时，不发通知
	goods, _ := BbcEngine.Table("datacenter.user_equity").Where("equity_name= '商品礼券' and is_voucher=1 and is_display=1").Exist()
	store, _ := BbcEngine.Table("datacenter.user_equity").Where("equity_name= '到店礼券' and is_voucher=1 and is_display=1").Exist()

	userCoupon := []*models.UserCoupon{}
	userLevel := models.UserLevel{}
	exist, err := BbcEngine.Table("upet_member").Alias("m").
		Join("left", "datacenter.user_level as u", "m.user_level_id=u.level_id").
		Where("m.scrm_user_id=?", req.UserId).Get(&userLevel)

	if err != nil {
		glog.Error("通过用户等级获取券失败：", err.Error(), "用户id:", req.UserId)
		return nil, err
	}

	if !exist || (userLevel.GoodsUpgradeVouchers == "" && userLevel.StoreUpgradeVouchers == "" && userLevel.StoreWeekVouchers == "") {
		return &out, nil
	}

	//查询最后一条等级变更记录
	memberLevelLog := models.UpetMemberLevelLog{}
	BbcEngine.Table("upet_member_level_log").Where("scrm_user_id=?", req.UserId).OrderBy("create_time desc").Get(&memberLevelLog)

	if memberLevelLog.LiftType == 1 && memberLevelLog.CreateTime.AddDate(0, 0, 7).Unix() > time.Now().Unix() {
		if len(userLevel.StoreUpgradeVouchers) > 0 && store {
			couponIds := strings.Split(userLevel.StoreUpgradeVouchers, ",")
			//查询是否已领取门店升级券
			Engine.Table("datacenter.user_coupon").Where("coupon_type=1 and type=1").
				Where("user_id=? and user_level_id=?", req.UserId, userLevel.LevelId).
				In("coupon_id", couponIds).Find(&userCoupon)

			if len(userCoupon) >= len(couponIds) {
				return &out, nil
			}

			out.Data = append(out.Data, &cc.MemberCouponsMessage{
				MessageType: 1,
				CouponType:  1,
				MessageBody: fmt.Sprintf("恭喜你升级到V%s，到店礼券已发放，快去领取吧！", cast.ToString(userLevel.LevelId)),
			})
		}
		if len(userLevel.GoodsUpgradeVouchers) > 0 && goods {
			couponIds := strings.Split(userLevel.GoodsUpgradeVouchers, ",")
			//查询是否已领取商城升级券
			Engine.Table("datacenter.user_coupon").Where("coupon_type=2 and type=1").
				Where("user_id=? and user_level_id=?", req.UserId, userLevel.LevelId).
				In("coupon_id", couponIds).Find(&userCoupon)

			if len(userCoupon) >= len(couponIds) {
				return &out, nil
			}
			out.Data = append(out.Data, &cc.MemberCouponsMessage{
				MessageType: 1,
				CouponType:  2,
				MessageBody: fmt.Sprintf("恭喜你升级到V%s，商品礼券已发放，快去领取吧！", cast.ToString(userLevel.LevelId)),
			})
		}
	} else {
		if len(userLevel.StoreWeekVouchers) > 0 && store {
			couponIds := strings.Split(userLevel.StoreWeekVouchers, ",")
			//查询是否已领取周特权券
			Engine.Table("datacenter.user_coupon").Where("coupon_type=1 and type=2").
				Where("user_id=? and user_level_id=?", req.UserId, userLevel.LevelId).
				In("coupon_id", couponIds).Find(&userCoupon)

			if len(userCoupon) >= len(couponIds) {
				return &out, nil
			}

			out.Data = append(out.Data, &cc.MemberCouponsMessage{
				MessageType: 2,
				CouponType:  1,
				MessageBody: fmt.Sprintf("V%s到店周特权券已到账，快去领取吧！", cast.ToString(userLevel.LevelId)),
			})
		}
	}

	return &out, nil
}

func (service *CustomerCenterService) MemberNewMessage(ctx context.Context, req *cc.MemberNewMessageRequest) (*cc.MemberNewMessageResponse, error) {
	out := cc.MemberNewMessageResponse{
		Data: &cc.MemberNewMessageResponseData{
			Integral:  &cc.MemberNewMessageResponseData_NewMessageData{},
			Health:    &cc.MemberNewMessageResponseData_NewMessageData{},
			MedRecord: &cc.MemberNewMessageResponseData_NewMessageData{},
		},
	}

	//健康值
	var healthDetail models.HealthDetail
	if hasHealthDetail, err := DataCenterEngine.Select("`type`, health_val, create_time").
		Where("`type` IN(1,2) AND user_id = ? AND create_time >= ?", req.UserId, time.Now().Format("2006-01-02")).
		OrderBy("create_time DESC").
		Get(&healthDetail); err != nil {
		glog.Error("获取用户最新消息失败：", err.Error(), "用户id:", req.UserId)
		//return nil, err
	} else if hasHealthDetail {
		out.Data.Health.Datetime = "今天 " + healthDetail.CreateTime.Format("15:04")
		symbol := "+"
		if cast.ToInt(healthDetail.Type) == 2 {
			symbol = "-"
		}
		out.Data.Health.Content = symbol + cast.ToString(healthDetail.HealthVal)
	}
	//积分
	var integral models.MemberIntegralRecord
	if hasIntegral, err := DataCenterEngine.Select("integraltype, integralcount, createdate").
		Where("org_id = ? and ischeck = 1 AND integralcount > 0 AND memberid = ? AND createdate >= ?", req.OrgId, req.UserId, time.Now().Format("2006-01-02")).
		OrderBy("createdate DESC").Get(&integral); err != nil {
		glog.Error("获取用户最新消息失败：", err.Error(), "用户id:", req.UserId)
		//return nil, err
	} else if hasIntegral {
		out.Data.Integral.Datetime = "今天 " + integral.Createdate.Format("15:04")
		symbol := "+"
		if integral.Integraltype%2 == 0 {
			symbol = "-"
		}
		out.Data.Integral.Content = symbol + cast.ToString(integral.Integralcount)
	}

	//病例
	engine := NewBeijingPetEngine()
	defer engine.Close()

	var data []*cc.PetInfoNew
	if err := engine.Table("t_scrm_pet_info").Select("pet_id").Where("user_id = ? and pet_status = 0", req.UserId).Find(&data); err != nil {
		glog.Error("获取用户最新消息失败：", err.Error(), "用户id:", req.UserId)
		return &out, nil
	}
	apiGateway := config.GetString("apiGateway")
	for _, v := range data {
		resData := new(models.NewMedRecordList)
		url := fmt.Sprintf("%s/user-api/appointment/med-record-list?pet_id=%s&page_index=%d&page_size=%d", apiGateway, v.PetId, 1, 1)
		body, err := utils.HttpGetZl(url, fmt.Sprintf("authorization|%s", req.Token), map[string]string{})
		if err != nil {
			glog.Error("获取用户最新消息失败, 参数："+url, string(body))
			continue
		}

		err = json.Unmarshal(body, &resData)
		if err != nil {
			glog.Error("获取用户最新消息失败, 返回结果错误：", string(body))
			continue
		}

		if resData.PageCount > 0 {
			stamp, _ := time.Parse("2006-01-02 15:04:05", resData.Data[0].CreateTime)
			if stamp.Format("2006-01-02") == cast.ToString(time.Now().Local().Format("2006-01-02")) {
				out.Data.MedRecord.Datetime = "今天 " + stamp.Format("15:04")
				out.Data.MedRecord.Content = resData.Data[0].MainSymptom
				break
			}
		}
		time.Sleep(time.Duration(500))
	}

	return &out, nil
}

func (service *CustomerCenterService) InfoUpdate(ctx context.Context, req *cc.InfoUpdateRequest) (*cc.BaseResponseNew, error) {
	out := new(cc.BaseResponseNew)

	url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-wx-url"), "/scrm-mini-api/user/updateByUserId")
	var criteria = make(map[string]interface{}, 0)
	criteria["userId"] = req.UserId
	criteria["userName"] = req.UserName
	criteria["userSex"] = req.UserSex
	criteria["userMobile"] = req.UserMobile
	criteria["userBirthday"] = req.UserBirthday
	criteria["userAvatar"] = req.UserAvatar
	criteria["province"] = req.Province
	criteria["city"] = req.City
	criteria["area"] = req.Area
	criteria["remoteTreatStatus"] = req.RemoteTreatStatus

	if req.FirstRaisesPet != "" {
		criteria["firstRaisesPet"] = req.FirstRaisesPet
	}
	ZlBaseMap := map[string]interface{}{
		"criteria": criteria,
	}
	/*ZlBaseMap := map[string]interface{}{
		"criteria": map[string]interface{}{
			"userId":       req.UserId,
			"userName":     req.UserName,
			"userSex":      req.UserSex,
			"userMobile":   req.UserMobile,
			"userBirthday": req.UserBirthday,
			"firstRaisesPet":    req.FirstRaisesPet,
			"userAvatar":        req.UserAvatar,
			"province":          req.Province,
			"city":              req.City,
			"area":              req.Area,
			"remoteTreatStatus": req.RemoteTreatStatus,
		},
	}*/

	dataJson, _ := json.MarshalIndent(ZlBaseMap, "", "    ")
	glog.Info("更新用户信息子龙接口(/scrm-mini-api/user/updateByUserId)请求参数(1)： ", string(dataJson), url)
	resData, err := utils.HttpPostZl(url, dataJson, fmt.Sprintf("token|%s&systemcode|scrm_mini_customer", req.Token))
	glog.Info("更新用户信息子龙接口(/scrm-mini-api/user/updateByUserId)返回结果：", string(resData), "接口参数：", string(dataJson))
	if err != nil {
		return nil, err
	}
	res := new(models.BaseZlInfoUpdateResponse)
	err = gjson.DecodeTo(resData, res)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != 200 {
		return nil, errors.New(res.Message)
	} else {
		//写日志
		_, _ = Engine.Insert(&models.UpdateLog{
			UserId:     req.UserId,
			UpdateType: 1,
			Nickname:   req.UserName,
			Avatar:     req.UserAvatar,
			CreateTime: time.Now(),
		})
	}

	return out, nil
}

// 更新宠物信息
func (service *CustomerCenterService) PetInfoUpdate(ctx context.Context, req *cc.PetInfoUpdateRequest) (*cc.BaseResponseNew, error) {
	out := new(cc.BaseResponseNew)

	url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-wx-url"), "/scrm-mini-api/scrm/updatePet")
	ZlBaseMap := map[string]interface{}{
		"criteria": map[string]interface{}{
			"id":               req.Id,
			"userId":           req.UserId,
			"petId":            req.PetId,
			"petAvatar":        req.PetAvatar,
			"petName":          req.PetName,
			"petBirthday":      req.PetBirthday,
			"petHomeday":       req.PetHomeDay,
			"petSex":           req.PetSex,
			"petKindof":        req.PetKindof,
			"petVariety":       req.PetVariety,
			"petLong":          req.PetLong,
			"petWeight":        req.PetWeight,
			"petNeutering":     req.PetNeutering,
			"petVaccinated":    req.PetVaccinated,
			"petDeworming":     req.PetDeworming,
			"petHeight":        req.PetHeight,
			"petSource":        req.PetSource,
			"petStatus":        req.PetStatus,
			"petRemark":        req.PetRemark,
			"petVarietyStr":    req.PetVarietyStr,
			"petKindofStr":     req.PetKindofStr,
			"dogLicenceCode":   req.DogLicenceCode,
			"dogVaccinateCode": req.DogVaccinateCode,
			"faceId":           req.FaceId,
			"petCode":          req.PetCode,
			"sendTime":         req.SendTime,
			"insuranceFaceId":  req.InsuranceFaceId,
			"ageStr":           req.AgeStr,
			"ageConversionStr": req.AgeConversionStr,
			"petFlower":        req.PetFlower,
			"flowerCode":       req.FlowerCode,
		},
	}
	dataJson, _ := json.MarshalIndent(ZlBaseMap, "", "    ")
	glog.Info("更新宠物信息子龙接口(/scrm-mini-api/scrm/updatePet)请求参数(1)： ", string(dataJson), url)
	resData, err := utils.HttpPostZl(url, dataJson, fmt.Sprintf("token|%s&systemcode|scrm_mini_customer", req.Token))
	glog.Info("更新宠物信息子龙接口(/scrm-mini-api/scrm/updatePet)返回结果：", string(resData), "接口参数：", string(dataJson))
	if err != nil {
		return nil, err
	}
	res := new(models.BaseZlResponse)
	err = gjson.DecodeTo(resData, res)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != 200 {
		return nil, errors.New(res.Message)
	} else {
		//写日志
		_, _ = Engine.Insert(&models.UpdateLog{
			UserId:     req.UserId,
			UpdateType: 2,
			Nickname:   req.PetName,
			Avatar:     req.PetAvatar,
			CreateTime: time.Now(),
		})
	}

	return out, nil
}

// 添加宠物信息
func (service *CustomerCenterService) PetInfoAdd(ctx context.Context, req *cc.PetInfoUpdateRequest) (*cc.PetInfoUpdateResponse, error) {
	out := new(cc.PetInfoUpdateResponse)

	url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-wx-url"), "/scrm-mini-api/scrm/insertPet")
	ZlBaseMap := map[string]interface{}{
		"criteria": map[string]interface{}{
			"userId":           req.UserId,
			"petAvatar":        req.PetAvatar,
			"petName":          req.PetName,
			"petBirthday":      req.PetBirthday,
			"petHomeday":       req.PetHomeDay,
			"petSex":           req.PetSex,
			"petKindof":        req.PetKindof,
			"petVariety":       req.PetVariety,
			"petLong":          req.PetLong,
			"petWeight":        req.PetWeight,
			"petNeutering":     req.PetNeutering,
			"petVaccinated":    req.PetVaccinated,
			"petDeworming":     req.PetDeworming,
			"petHeight":        req.PetHeight,
			"petSource":        req.PetSource,
			"petStatus":        req.PetStatus,
			"petRemark":        req.PetRemark,
			"petVarietyStr":    req.PetVarietyStr,
			"petKindofStr":     req.PetKindofStr,
			"dogLicenceCode":   req.DogLicenceCode,
			"dogVaccinateCode": req.DogVaccinateCode,
			"faceId":           req.FaceId,
			"petCode":          req.PetCode,
			"sendTime":         req.SendTime,
			"insuranceFaceId":  req.InsuranceFaceId,
			"ensure_card":      req.EnsureCard,
			"ageStr":           req.AgeStr,
			"ageConversionStr": req.AgeConversionStr,
			"petFlower":        req.PetFlower,
			"flowerCode":       req.FlowerCode,
		},
	}
	dataJson, _ := json.MarshalIndent(ZlBaseMap, "", "    ")
	data, _ := json.Marshal(req)
	glog.Info("添加宠物信息子龙接口(/scrm-mini-api/scrm/insertPet)请求参数(1)： ", string(data), url)
	glog.Info("添加宠物信息子龙接口(/scrm-mini-api/scrm/insertPet)请求参数(2)： ", string(dataJson), url)
	resData, err := utils.HttpPostZl(url, dataJson, fmt.Sprintf("token|%s&systemcode|scrm_mini_customer", req.Token))
	glog.Info("添加宠物信息子龙接口(/scrm-mini-api/scrm/insertPet)返回结果：", string(resData), "接口参数：", string(dataJson))
	if err != nil {
		return nil, err
	}
	res := new(models.BaseZlResponse)
	err = gjson.DecodeTo(resData, res)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != 200 {
		return nil, errors.New(res.Message)
	} else {
		//写日志
		_, _ = Engine.Insert(&models.UpdateLog{
			UserId:     req.UserId,
			UpdateType: 2,
			Nickname:   req.PetName,
			Avatar:     req.PetAvatar,
			CreateTime: time.Now(),
		})
	}
	out.Data = res.Result
	return out, nil
}
