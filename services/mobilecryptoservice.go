package services

import (
	"_/proto/cc"
	"_/utils"
	"context"

	"github.com/maybgit/glog"
)

// MobileCryptoService 手机号加密解密服务
type MobileCryptoService struct {
	cc.UnimplementedMobileCryptoServiceServer
}

// MobileDecrypt 手机号解密
func (s *MobileCryptoService) MobileDecrypt(ctx context.Context, req *cc.MobileDecryptRequest) (*cc.MobileDecryptResponse, error) {
	glog.Infof("MobileDecrypt request: ciphertext=%s", req.Ciphertext)
	
	// 参数验证
	if req.Ciphertext == "" {
		return &cc.MobileDecryptResponse{
			Mobile: "",
			Error:  "ciphertext cannot be empty",
		}, nil
	}

	// 调用解密方法
	mobile := utils.MobileDecrypt(req.Ciphertext)
	
	glog.Infof("MobileDecrypt response: mobile=%s", mobile)
	
	return &cc.MobileDecryptResponse{
		Mobile: mobile,
		Error:  "",
	}, nil
}

// MobileEncrypt 手机号加密
func (s *MobileCryptoService) MobileEncrypt(ctx context.Context, req *cc.MobileEncryptRequest) (*cc.MobileEncryptResponse, error) {
	glog.Infof("MobileEncrypt request: mobile=%s", req.Mobile)
	
	// 参数验证
	if req.Mobile == "" {
		return &cc.MobileEncryptResponse{
			Ciphertext: "",
			Error:      "mobile cannot be empty",
		}, nil
	}

	// 调用加密方法
	ciphertext := utils.MobileEncrypt(req.Mobile)
	
	glog.Infof("MobileEncrypt response: ciphertext=%s", ciphertext)
	
	return &cc.MobileEncryptResponse{
		Ciphertext: ciphertext,
		Error:      "",
	}, nil
}
