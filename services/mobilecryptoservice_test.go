package services

import (
	"_/proto/cc"
	"context"
	"testing"
)

func TestMobileCryptoService_MobileEncrypt(t *testing.T) {
	service := &MobileCryptoService{}
	
	tests := []struct {
		name     string
		mobile   string
		wantErr  bool
		errMsg   string
	}{
		{
			name:    "valid mobile number",
			mobile:  "13800138000",
			wantErr: false,
		},
		{
			name:    "empty mobile number",
			mobile:  "",
			wantErr: true,
			errMsg:  "mobile cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &cc.MobileEncryptRequest{
				Mobile: tt.mobile,
			}
			
			resp, err := service.MobileEncrypt(context.Background(), req)
			if err != nil {
				t.Errorf("MobileEncrypt() error = %v", err)
				return
			}
			
			if tt.wantErr {
				if resp.Error != tt.errMsg {
					t.Errorf("MobileEncrypt() error = %v, wantErr %v", resp.Error, tt.errMsg)
				}
			} else {
				if resp.Error != "" {
					t.Errorf("MobileEncrypt() unexpected error = %v", resp.Error)
				}
				if resp.Ciphertext == "" {
					t.Errorf("MobileEncrypt() ciphertext is empty")
				}
			}
		})
	}
}

func TestMobileCryptoService_MobileDecrypt(t *testing.T) {
	service := &MobileCryptoService{}
	
	// 先加密一个手机号用于测试解密
	encryptReq := &cc.MobileEncryptRequest{
		Mobile: "13800138000",
	}
	encryptResp, err := service.MobileEncrypt(context.Background(), encryptReq)
	if err != nil {
		t.Fatalf("Failed to encrypt mobile for test: %v", err)
	}
	
	tests := []struct {
		name       string
		ciphertext string
		wantMobile string
		wantErr    bool
		errMsg     string
	}{
		{
			name:       "valid ciphertext",
			ciphertext: encryptResp.Ciphertext,
			wantMobile: "13800138000",
			wantErr:    false,
		},
		{
			name:       "empty ciphertext",
			ciphertext: "",
			wantMobile: "",
			wantErr:    true,
			errMsg:     "ciphertext cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &cc.MobileDecryptRequest{
				Ciphertext: tt.ciphertext,
			}
			
			resp, err := service.MobileDecrypt(context.Background(), req)
			if err != nil {
				t.Errorf("MobileDecrypt() error = %v", err)
				return
			}
			
			if tt.wantErr {
				if resp.Error != tt.errMsg {
					t.Errorf("MobileDecrypt() error = %v, wantErr %v", resp.Error, tt.errMsg)
				}
			} else {
				if resp.Error != "" {
					t.Errorf("MobileDecrypt() unexpected error = %v", resp.Error)
				}
				if resp.Mobile != tt.wantMobile {
					t.Errorf("MobileDecrypt() mobile = %v, want %v", resp.Mobile, tt.wantMobile)
				}
			}
		})
	}
}

func TestMobileCryptoService_EncryptDecryptRoundTrip(t *testing.T) {
	service := &MobileCryptoService{}
	
	testMobiles := []string{
		"13800138000",
		"18888888888",
		"15999999999",
	}
	
	for _, mobile := range testMobiles {
		t.Run("roundtrip_"+mobile, func(t *testing.T) {
			// 加密
			encryptReq := &cc.MobileEncryptRequest{
				Mobile: mobile,
			}
			encryptResp, err := service.MobileEncrypt(context.Background(), encryptReq)
			if err != nil {
				t.Errorf("MobileEncrypt() error = %v", err)
				return
			}
			if encryptResp.Error != "" {
				t.Errorf("MobileEncrypt() error = %v", encryptResp.Error)
				return
			}
			
			// 解密
			decryptReq := &cc.MobileDecryptRequest{
				Ciphertext: encryptResp.Ciphertext,
			}
			decryptResp, err := service.MobileDecrypt(context.Background(), decryptReq)
			if err != nil {
				t.Errorf("MobileDecrypt() error = %v", err)
				return
			}
			if decryptResp.Error != "" {
				t.Errorf("MobileDecrypt() error = %v", decryptResp.Error)
				return
			}
			
			// 验证结果
			if decryptResp.Mobile != mobile {
				t.Errorf("Round trip failed: original=%v, decrypted=%v", mobile, decryptResp.Mobile)
			}
		})
	}
}
