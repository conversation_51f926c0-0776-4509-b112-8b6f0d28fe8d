package services

import (
	"_/proto/cc"
	"context"
	"testing"
)

func TestMobileCryptoService_MobileEncrypt(t *testing.T) {
	service := &MobileCryptoService{}
	
	tests := []struct {
		name     string
		mobile   string
		wantErr  bool
		errMsg   string
	}{
		{
			name:    "valid mobile number",
			mobile:  "13800138000",
			wantErr: false,
		},
		{
			name:    "empty mobile number",
			mobile:  "",
			wantErr: true,
			errMsg:  "mobile cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &cc.MobileEncryptRequest{
				Mobile: tt.mobile,
			}
			
			resp, err := service.MobileEncrypt(context.Background(), req)
			if err != nil {
				t.Errorf("MobileEncrypt() error = %v", err)
				return
			}
			
			if tt.wantErr {
				if resp.Error != tt.errMsg {
					t.Errorf("MobileEncrypt() error = %v, wantErr %v", resp.Error, tt.errMsg)
				}
			} else {
				if resp.Error != "" {
					t.Errorf("MobileEncrypt() unexpected error = %v", resp.Error)
				}
				if resp.Ciphertext == "" {
					t.Errorf("MobileEncrypt() ciphertext is empty")
				}
			}
		})
	}
}

func TestMobileCryptoService_MobileDecrypt(t *testing.T) {
	service := &MobileCryptoService{}
	
	tests := []struct {
		name       string
		ciphertext string
		wantErr    bool
		errMsg     string
	}{
		{
			name:       "empty ciphertext",
			ciphertext: "",
			wantErr:    true,
			errMsg:     "ciphertext cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &cc.MobileDecryptRequest{
				Ciphertext: tt.ciphertext,
			}
			
			resp, err := service.MobileDecrypt(context.Background(), req)
			if err != nil {
				t.Errorf("MobileDecrypt() error = %v", err)
				return
			}
			
			if tt.wantErr {
				if resp.Error != tt.errMsg {
					t.Errorf("MobileDecrypt() error = %v, wantErr %v", resp.Error, tt.errMsg)
				}
			} else {
				if resp.Error != "" {
					t.Errorf("MobileDecrypt() unexpected error = %v", resp.Error)
				}
			}
		})
	}
}
