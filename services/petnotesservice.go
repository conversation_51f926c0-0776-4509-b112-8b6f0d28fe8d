package services

import (
	"_/models"
	"_/proto/cc"
	"context"
	"fmt"
	"strings"
	"time"
)

//订单模块--常买记录
type PetNotesService struct {
	BaseService
}

func (p *PetNotesService) GetNotesByUserId(ctx context.Context, in *cc.GetNotesByUserIdRequest) (*cc.GetNotesByUserIdResponse, error) {
	out := new(cc.GetNotesByUserIdResponse)
	out.Code = 200
	if len(in.UserId) <= 0 {
		out.Code = 400
		out.Message = "用户id不能为空"
		out.Error = "用户id不能为空"
		return out, nil
	}
	err := Engine.Where("user_id=?", in.UserId).And("is_remind=?", 2).And("is_deleted=?", 1).And("  (HOUR(timediff(now(), remind_time)) < 24 or remind_time>now()) ").
		Cols("id,user_id,pet_id,title,content,is_remind,is_deleted,remind_time,create_time,create_id,update_time,update_id").Find(&out.List)
	if err != nil {
		fmt.Println(err)
	}
	return out, nil
}

//宠物便签——分页获取宠物便签列表
func (p *PetNotesService) GetPetNotesList(ctx context.Context, in *cc.GetPetNotesListRequest) (*cc.GetPetNotesListResponse, error) {
	out := new(cc.GetPetNotesListResponse)
	out.Code = 400
	session := Engine.Table("pet_notes").Where("1=1")
	if in.Id > 0 {
		session.And("`id` = ?", in.Id)
	}
	if in.UserId != "" {
		session.And("`user_id` = ?", in.UserId)
		//session.And("`name` like ?", "%"+in.Where.Name+"%")
	}
	if in.PetId != "" {
		session.And("`pet_id` = ?", in.PetId)
	}
	if in.IsRemind > 0 {
		session.And("`is_remind` = ?", in.IsRemind)
	}
	session.And("`is_deleted` = ?", 1)
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	countSession := *session

	count, _ := countSession.Count()
	out.Total = int32(count)

	if err := session.Select("id, user_id, pet_id, title, content, is_remind, remind_time, create_id, create_time, update_id,update_time").Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).OrderBy("create_time Desc").Find(&out.Details); err != nil {
		return out, err
	}

	out.Code = 200

	return out, nil
}

//宠物便签——获取宠物便签详情
func (p *PetNotesService) GetPetNotesDetail(ctx context.Context, in *cc.GetPetNotesDetailRequest) (*cc.GetPetNotesDetailResponse, error) {
	out := new(cc.GetPetNotesDetailResponse)
	out.Code = 200

	session := Engine.Table("pet_notes").Where("1=1")
	if len(in.Ids) > 0 {
		session.In("id", strings.Split(in.Ids, ","))
	}
	if len(in.UserIds) > 0 {
		session.In("user_id", strings.Split(in.UserIds, ","))
		//session.And("`name` like ?", "%"+in.Where.Name+"%")
	}
	if len(in.PetIds) > 0 {
		session.In("pet_id", strings.Split(in.PetIds, ","))
	}

	if err := session.Select("id, user_id, pet_id, title, content, is_remind, remind_time, create_id, create_time, update_id,update_time").OrderBy("create_time Desc").Find(&out.Details); err != nil {
		return out, err
	}

	return out, nil
}

//宠物便签——创建
func (p *PetNotesService) CreatePetNotes(ctx context.Context, in *cc.CreatePetNotesRequest) (*cc.CreatePetNotesResponse, error) {
	out := new(cc.CreatePetNotesResponse)
	out.Code = 200
	out.Message = "创建成功"

	var sql strings.Builder
	sql.WriteString("INSERT INTO `pet_notes` ")
	sql.WriteString(" (`user_id`, `pet_id`, `title`, `content`, `is_remind`, `is_deleted`, `remind_time`, `create_time`, `create_id`, `update_time`, `update_id`) ")
	if len(in.RemindTime) == 0 {
		sql.WriteString(" VALUES (?,?,?,?,?,?,NULL,?,?,?,?) ")
	} else {
		sql.WriteString(fmt.Sprintf(" VALUES (?,?,?,?,?,?,\"%s\",?,?,?,?) ", in.RemindTime))
	}

	_, err := Engine.Exec(sql.String(), in.UserId, in.PetId, in.Tittle, in.Content, int(in.IsRemind), 1, time.Now().Format("2006-01-02 15:04:05"), in.CreateId, time.Now().Format("2006-01-02 15:04:05"), in.CreateId)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
	}
	return out, err
}

//宠物便签——更新
func (p *PetNotesService) UpdatePetNotes(ctx context.Context, in *cc.UpdatePetNotesRequest) (*cc.UpdatePetNotesResponse, error) {
	out := new(cc.UpdatePetNotesResponse)
	out.Code = 200

	_, err := Engine.Id(int(in.Id)).Update(models.PetNotes{
		UserId:     in.UserId,
		PetId:      in.PetId,
		Title:      in.Tittle,
		Content:    in.Content,
		IsRemind:   int(in.IsRemind),
		RemindTime: in.RemindTime,
		UpdateTime: time.Now().Format("2006-01-02 15:04:05"),
		UpdateId:   in.UpdateId,
	})
	return out, err
}

//宠物便签——删除（软删除）
func (p *PetNotesService) DeletePetNotes(ctx context.Context, in *cc.DeletePetNotesRequest) (*cc.DeletePetNotesResponse, error) {
	out := new(cc.DeletePetNotesResponse)
	out.Code = 200

	_, err := Engine.Id(int(in.Id)).Update(models.PetNotes{
		IsDeleted: 2,
		UpdateId:  in.UpdateId,
	})

	return out, err
}
