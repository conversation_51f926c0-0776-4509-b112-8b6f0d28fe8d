package services

import (
	"_/models"
	"_/proto/cc"
	"context"
	"fmt"
	"testing"

	kit "github.com/tricobbler/rp-kit"
)

func TestPetService_GetPetList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		req *cc.PetListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *cc.PetListResponse
		wantErr bool
	}{
		{name: "获取宠物列表",
			args: args{
				ctx: context.Background(),
				req: &cc.PetListRequest{
					PetId:  "",
					UserId: "8165e27b9840402fa196349aa34645db",
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PetService{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.GetPetList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.<PERSON><PERSON><PERSON>("GetPetList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}

func TestPetService_VaccinateRecord(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		petId         string
		vaccinateType int32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *models.VaccinateRecordResponse
		wantErr bool
	}{
		{name: "查询疫苗驱虫记录"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PetService{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.VaccinateRecord("3ce9789fc97240f7bccee70e1df188f3", 2)
			if (err != nil) != tt.wantErr {
				t.Errorf("VaccinateRecord() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}

func TestPetService_GetPetDate(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		req *cc.PetListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *cc.PetRecordDateBaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				req: &cc.PetListRequest{
					//PetId: "6c813283758b43c686ceef3120a847a8",
					//PetId: "5a064d6fffa24accbcbf989ace06a8b7",
					PetId: "a576859493fb4cf191575139c5f70b5e", //艳青
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PetService{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.GetPetDate(tt.args.ctx, tt.args.req)
			t.Log(kit.JsonEncode(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPetDate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestPetService_MedRecordList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		req *cc.MedRecordListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *cc.MedRecordListRes
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test2",
			args: args{
				ctx: context.Background(),
				req: &cc.MedRecordListReq{
					PetId: "ed8c3d50107a4b92b08ab414f3d29f9b",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PetService{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.MedRecordList(tt.args.ctx, tt.args.req)
			t.Log(kit.JsonEncode(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("MedRecordList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
