package services

import (
	"_/models"
	"_/utils"
	"time"

	"github.com/limitedlee/microservice/common/config"
	logger "github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
)

//订单模块--常买记录
type Order struct {
	BaseService
}

//定时任务
func InitTask() {
	c := cron.New()
	// 每天凌晨1点拉取一次库存
	c.AddFunc("0 0 1 * * ? ", Add)
	c.Start()
}

func Add() {
	order_task := "task:order:task"
	//初始化redis
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()
	lockRes := redisConn.SetNX(order_task, time.Now().Unix(), time.Minute*30).Val()
	if !lockRes {
		return
	}
	defer func() {
		for {
			_, err := redisConn.Del(order_task).Result()
			if err != nil {
				logger.Error("SyncStock删除锁出错:" + err.Error())
				time.Sleep(time.Second * 5)
			} else {
				logger.Info("本次处理库存结束")
				break
			}

		}
	}()
	logger.Info("获取到锁准备处理数据,订单模块数据")
	service := Order{}
	all_quantity := config.GetString("all_quantity")
	start := ""
	end := ""
	if all_quantity != "true" {
		start = time.Now().AddDate(0, 0, -1).Format("2006-01-02 00:00:00")
		end = time.Now().Format("2006-01-02 00:00:00")
	}
	service.CreateUserBuyOrders(start, end)
	service.CreateUserBuyOrdersToFictitious(start, end)
}

//todo 常购记录 定时任务 增量  实物商品
func (o *Order) CreateUserBuyOrders(start, end string) {
	//todo 业务逻辑：把已支付过的订单数据全部写入到user_buy里面
	sql := "SELECT o.`channel_id`, o.`member_id` AS user_id,op.product_name,op.`product_id`,op.`sku`,op.`product_id`,op.`price`,op.`image`,op.number,o.`member_tel`  FROM `order` o " +
		" INNER JOIN `order_product` op ON o.order_sn=op.`order_sn`" +
		" WHERE (o.order_status=20 OR  o.order_status=30) AND o.`member_id`!=''  "
	if len(start) > 0 && len(end) > 0 {
		sql += " AND o.`create_time` BETWEEN ? AND ?"
	}
	var list []models.UserOrderDto
	if len(start) > 0 && len(end) > 0 {
		err := OrderEngine.SQL(sql, start, end).Find(&list)
		if err != nil {
			logger.Error("查询失败，err:", err)
		}
	} else {
		err := OrderEngine.SQL(sql).Find(&list)
		if err != nil {
			logger.Error("查询失败，err:", err)
		}
	}
	for _, v := range list {
		if v.ChannelId == 5 && len(v.MemberTel) > 0 {
			var member_model models.MemberInfo
			DataCenterEngine.Where("mobile=?", v.MemberTel).Cols("memberid").Get(&member_model)
			if len(member_model.Memberid) <= 0 {
				continue
			}
			v.UserId = member_model.Memberid
			var product models.SkuThird
			ProductCenterEngine.Where("third_sku_id=?", v.Sku).Get(&product)
			v.Sku = cast.ToString(product.SkuId)
		}
		var model models.UserBuy
		Engine.Where("user_id=?", v.UserId).And("sku=?", v.Sku).And("channel_id=?", v.ChannelId).Get(&model)
		if model.Id > 0 { //如果存在，则直接累加数量
			model.Number = model.Number + v.Number
			model.CreateTime = time.Now()
			Engine.Id(model.Id).Update(&model)
			continue
		} else { //如果不存在，则新增记录
			model.UserId = v.UserId
			model.ProductName = v.ProductName
			model.ProductId = v.ProductId
			model.Sku = cast.ToInt(v.Sku)
			model.Number = v.Number
			model.Price = v.Price
			model.Image = v.Image
			model.ChannelId = v.ChannelId
			model.CreateTime = time.Now()
			Engine.Insert(&model)
		}
	}
}

//todo 常购记录 定时任务 电商的虚拟商品
func (o *Order) CreateUserBuyOrdersToFictitious(start, end string) {
	sql := " SELECT o.`memberid` AS user_id, og.`name` AS product_name,0 AS product_id,og.`sku`,og.`quantity` AS number,0 AS price,og.`goodsimage` AS image, 5 AS Channel_id   " +
		" FROM  `order_info` o INNER JOIN `order_goods_detail`  og  ON o.`orderid`=og.`orderid` WHERE  o.`platformid`=1 AND o.`orderstate`=2 AND og.`sku`>0  "
	if len(start) > 0 && len(end) > 0 {
		sql += " AND o.`createtime` BETWEEN ? AND ?"
	} else {
		sql = "SELECT user_id, product_name, product_id,sku, number, price, image, channel_id  FROM `product_info`"
	}
	var list []models.UserOrderDto
	if len(start) > 0 && len(end) > 0 {
		err := DataCenterEngine.SQL(sql, start, end).Find(&list)
		if err != nil {
			logger.Error("查询失败，err:", err)
		}
	} else {
		err := Engine.SQL(sql).Find(&list)
		if err != nil {
			logger.Error("查询失败，err:", err)
		}
	}
	for _, v := range list {
		var model models.UserBuy
		Engine.Where("user_id=?", v.UserId).And("sku=?", v.Sku).And("channel_id=?", v.ChannelId).Get(&model)
		if model.Id > 0 { //如果存在，则直接累加数量
			model.Number = model.Number + v.Number
			model.CreateTime = time.Now()
			Engine.Id(model.Id).Update(&model)
			continue
		} else { //如果不存在，则新增记录
			model.UserId = v.UserId
			model.ProductName = v.ProductName
			model.ProductId = v.ProductId
			model.Sku = cast.ToInt(v.Sku)
			model.Number = v.Number
			model.Price = v.Price
			model.Image = v.Image
			model.ChannelId = v.ChannelId
			model.CreateTime = time.Now()
			Engine.Insert(&model)
		}
	}
}
