package services

import (
	"_/proto/cc"
	"context"
	"fmt"
	"reflect"
	"testing"
)

func TestUserService_MemberCounts(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.MemberCountsRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *cc.MemberCountsResponse
		wantErr bool
	}{
		{name: "会员统计",
			args: args{
				ctx: context.Background(),
				req: &cc.MemberCountsRequest{
					BeginTime: "2022-01-01 00:00:00",
					EndTime:   "2022-08-09 23:59:59",
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			userService := &UserService{}
			got, err := userService.MemberCounts(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("MemberCounts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}

func TestUserService_CheckBlackList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *cc.CheckBlackListReq
	}
	tests := []struct {
		name    string
		args    args
		wantOut *cc.BaseRes
		wantErr bool
	}{
		{
			args: args{in: &cc.CheckBlackListReq{
				Mobile:         "17704021685",
				ReceiverMobile: "",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			userService := &UserService{}
			gotOut, err := userService.CheckBlackList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckBlackList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("CheckBlackList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestUserService_QueryCustomerId(t *testing.T) {

	_ = ZlScrmNewEngine()

	type args struct {
		ctx context.Context
		in  *cc.QueryCustomerIdReq
	}
	tests := []struct {
		name    string
		args    args
		wantOut *cc.QueryCustomerIdRes
		wantErr bool
	}{
		{
			args: args{in: &cc.QueryCustomerIdReq{
				ScrmId: "02e2c06214aa4b7497199922af0e187c",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			userService := &UserService{}
			gotOut, err := userService.QueryCustomerId(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryCustomerId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("QueryCustomerId() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}
