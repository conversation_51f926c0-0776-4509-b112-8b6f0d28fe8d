package services

import (
	"_/models"
	"reflect"
	"testing"
)

func TestGetUserIntegral(t *testing.T) {
	type args struct {
		scrmUserId string
		orgId      int32
	}
	tests := []struct {
		name    string
		args    args
		want    *models.MemberIntegralInfo
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "测试", args: struct {
			scrmUserId string
			orgId      int32
		}{scrmUserId: "000015ff1f2e4fb1b0e59ea2c340b53f", orgId: 1}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUserIntegral(tt.args.scrmUserId, tt.args.orgId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserIntegral() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("GetUserIntegral() got = %v, want %v", got, tt.want)
			}
		})
	}
}
