package services

import (
	"_/proto/cc"
	"context"
	"reflect"
	"testing"
)

func TestVirtualCardService_CreateVirtualCard(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.CreateRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.VirtualBaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "创建任务"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VirtualCardService{
				BaseService: tt.fields.BaseService,
			}

			app := cc.CreateRequest{}
			app.OrgName = "测试机构"
			app.OrgId = 2
			app.CardCount = 10000
			app.CardPrice = 998
			app.UserId = "zhou"
			app.UserName = "周翔"
			app.TemplateId = 2
			app.SellType = 2
			gotOut, err := v.CreateVirtualCard(context.Background(), &app)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateVirtualCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("CreateVirtualCard() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestVirtualCardService_VirtualCardList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		req *cc.VirtualCardListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *cc.VirtualCardListRes
		wantErr bool
	}{
		{name: "获取虚拟卡信息列表",
			args: args{
				ctx: nil,
				req: &cc.VirtualCardListReq{
					BatchId:         "1",
					OrgId:           0,
					TemplateId:      0,
					UserId:          "",
					UserMobile:      "",
					UseTimeStart:    "",
					UseTimeEnd:      "",
					CreateTimeStart: "",
					CreateTimeEnd:   "",
					CardIds:         []int64{0},
					PageIndex:       0,
					PageSize:        0,
					UserNo:          "",
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VirtualCardService{
				BaseService: tt.fields.BaseService,
			}
			got, err := v.VirtualCardList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("VirtualCardList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("VirtualCardList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
