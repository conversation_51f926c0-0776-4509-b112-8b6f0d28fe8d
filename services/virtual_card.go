package services

import (
	"_/models"
	"_/proto/cc"
	"_/utils"
	"context"
	"fmt"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

type VirtualCardService struct {
	BaseService
}

func (v *VirtualCardService) VirtualCardList(ctx context.Context, req *cc.VirtualCardListReq) (*cc.VirtualCardListRes, error) {
	logFix := fmt.Sprintf("VirtualCardList ====== 参数：%s", utils.InterfaceToJSON(req))
	glog.Info(logFix)
	out := new(cc.VirtualCardListRes)
	if req.PageIndex == 0 {
		req.PageIndex = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	tx := DataCenterEngine.Table("vip_card_virtual")
	if req.OrgId != 0 {
		tx = tx.Where("org_id=?", req.OrgId)
	}
	if req.Status != -1 {
		tx = tx.Where("status=?", req.Status)
	}
	if len(req.BatchId) != 0 {
		tx = tx.Where("batch_id=?", req.BatchId)
	}
	if req.TemplateId != 0 {
		tx = tx.Where("template_id=?", req.TemplateId)
	}
	if len(req.UserId) != 0 {
		tx = tx.Where("user_id=?", req.UserId)
	}
	if len(req.UserMobile) != 0 {
		tx = tx.Where("en_user_mobile=?", utils.MobileEncrypt(req.UserMobile))
	}
	if req.CardId != 0 {
		tx = tx.Where("card_id=?", req.CardId)
	}
	if len(req.UseTimeStart) != 0 {
		tx = tx.Where("use_time >?", req.UseTimeStart)
	}
	if len(req.UseTimeEnd) != 0 {
		tx = tx.Where("use_time <= ?", req.UseTimeEnd)
	}
	if len(req.CreateTimeStart) != 0 {
		tx = tx.Where("create_time > ?", req.CreateTimeStart)
	}
	if len(req.CreateTimeEnd) != 0 {
		tx = tx.Where("create_time<=?", req.CreateTimeEnd)
	}

	//if len(req.CardIds) > 0 {
	//	if len(req.CardIds) == 1 && req.CardIds[0] != 0 {
	//		tx = tx.In("card_id", req.CardIds)
	//	}
	//}
	//todo 暂时屏蔽用户信息权限模块
	//if len(req.UserNo) > 0 {
	//	tx = tx.Where("user_id=?", req.UserNo)
	//}
	//统计条数
	txCount := tx.Clone()
	defer txCount.Close()

	outPageCount, err := txCount.Count()
	out.PageCount = outPageCount
	if err != nil {
		glog.Error(logFix, "VirtualCardList 查询会员卡虚拟卡销卡信息总条数报错：", err)
		return out, err
	}
	limit := int(req.PageSize)
	start := int((req.PageIndex - 1) * req.PageSize)
	if err = tx.Select("*").OrderBy("use_time DESC").Limit(limit, start).Find(&out.List); err != nil {
		glog.Error(logFix, "VirtualCardList 查询会员卡虚拟卡销卡信息表报错：", err)
		return out, err
	}
	for i, x := range out.List {

		statusName := "已卖出"
		switch x.Status {
		case 0:
			statusName = "已卖出"
			break
		case 1:
			statusName = "已激活"

			break
		case 2:
			statusName = "已注销"
			break
		case 3:
			statusName = "已失效"
			break
		}
		//if x.Status == 0 || time.Now().Format(utils.DATE_TIME_LAYOUT) > x.ExpireTime {
		//	statusName = "已失效"
		//	out.List[i].Status = 3
		//}
		out.List[i].StatusName = statusName

	}
	return out, nil
}

func (v *VirtualCardService) GetEquityList(ctx context.Context, request *cc.CreateRequest) (*cc.VirtualBaseResponse, error) {
	//TODO implement me
	panic("implement me")
}

// CancelVirtualCard 创建虚拟卡券注销功能
func (v *VirtualCardService) CancelVirtualCard(ctx context.Context, req *cc.CancelVirtualCardReq) (*empty.Empty, error) {
	logFix := fmt.Sprintf("CancelVirtualCard ====== 参数：%s", utils.InterfaceToJSON(req))
	glog.Info(logFix)
	out := new(empty.Empty)
	vipCardVirtualCancel := models.VipCardVirtualCancel{
		FileUrl:      req.FileUrl,
		CancelRemark: req.CancelRemark,
		OrgId:        req.OrgId,
		OrgName:      req.OrgName,
		UserId:       req.UserNo,
		UserName:     req.UserName,
		CancelStatus: 1,
	}
	_, err := DataCenterEngine.Insert(&vipCardVirtualCancel)
	if err != nil {
		glog.Error(logFix, "创建虚拟卡券注销信息失败：", err)
		return out, err
	}
	return out, nil
}

// VirtualCardCancelList 获取所有的虚拟卡券注销申请记录
func (v *VirtualCardService) VirtualCardCancelList(ctx context.Context, req *cc.VirtualCardCancelListReq) (*cc.VirtualCardCancelListRes, error) {
	logFix := fmt.Sprintf("VirtualCardCancelList ====== 参数：%s", utils.InterfaceToJSON(req))
	glog.Info(logFix)
	out := new(cc.VirtualCardCancelListRes)
	if req.PageIndex == 0 {
		req.PageIndex = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	tx := DataCenterEngine.Table("vip_card_virtual_cancel")
	//if len(req.UserNo) > 0 {
	//	tx.Where("user_id=?", req.UserNo)
	//}
	//统计条数
	txCount := tx.Clone()
	defer txCount.Close()

	if outPageCount, err := txCount.Count(); err != nil {
		glog.Error(logFix, "VirtualCardCancelList 查询会员卡虚拟卡销卡信息表总条数报错：", err)
		return out, err
	} else {
		out.PageCount = outPageCount
	}
	limit := int(req.PageSize)
	start := int((req.PageIndex - 1) * req.PageSize)
	if err := tx.Select("*").OrderBy("id DESC").Limit(limit, start).Find(&out.List); err != nil {
		glog.Error(logFix, "VirtualCardCancelList 查询会员卡虚拟卡销卡信息表报错：", err)
		return out, err
	}
	for i, x := range out.List {

		if x.UserId != req.UserNo {
			out.List[i].CancelResult = ""
		}
	}
	return out, nil
}

// CreateVirtualCard 生成虚拟卡添加任务
func (v *VirtualCardService) CreateVirtualCard(ctx context.Context, in *cc.CreateRequest) (out *cc.VirtualBaseResponse, e error) {
	out = &cc.VirtualBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("CreateVirtualCard 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	model := models.VipCardVirtualCreate{}

	model.OrgId = int(in.OrgId)
	model.OrgName = in.OrgName
	model.TemplateId = int(in.TemplateId)
	model.CardCount = int(in.CardCount)
	model.CardPrice = int(in.CardPrice)
	model.UserId = in.UserId
	model.UserName = in.UserName
	model.SellType = int(in.SellType)
	_, err := DataCenterEngine.Insert(&model)

	if err != nil {
		out.Message = "添加数据失败" + err.Error()
		return out, nil
	}
	out.Code = 200
	return out, nil
}
