package services

import (
	"_/proto/cc"
	"context"
	"reflect"
	"testing"
)

func TestFeedbackService_GetFeedbackList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx     context.Context
		request *cc.GetFeedbackListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *cc.GetFeedbackListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "获取反馈列表",
			args: args{
				ctx: context.Background(),
				request: &cc.GetFeedbackListRequest{
					PageIndex:  2,
					PageSize:   1,
					Phone:      "",
					Content:    "",
					UserId:     "",
					StartTime:  "2021-09-09",
					CreateTime: "2021-12-09 11:17:44 - 2021-12-09 11:21:15",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := FeedbackService{
				BaseService: tt.fields.BaseService,
			}
			got, err := f.GetFeedbackList(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.<PERSON>("GetFeedbackList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFeedbackList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
