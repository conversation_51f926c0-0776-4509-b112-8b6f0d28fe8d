package services

import (
	"_/enum"
	"_/jpush"
	"_/myerror"
	"_/utils"
	"encoding/json"
	"fmt"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

var (
	ahWenJPushAppId  = ""
	ahWenJPushSecret = ""
)

func init() {
	ahWenJPushAppId = config.GetString("ah-wen-jpush-app-id")
	ahWenJPushSecret = config.GetString("ah-wen-jpush-app-secret")
}

type Message interface {
	Push() error
}

//推送小程序消息通知结构体
type PushFeedbackCommentMessage struct {
	ScrmUserId   string
	UserMobile   string
	PushType     int32
	JpushContent string
	UniqKey      string
	NoticeExtras map[string]interface{}
}

//反馈阿闻回复推送
func NewPushFeedbackCommentMessage(ScrmUserId, UserMobile string, PushType, feedBackId, commentId int32) Message {
	UniqKey := fmt.Sprintf("%d:%d", feedBackId, commentId)
	noticeExtras := make(map[string]interface{}, 0)
	noticeExtras["feedBackId"] = feedBackId
	noticeExtras["type"] = enum.JPushMSGPushTypeMapType[PushType]

	return &PushFeedbackCommentMessage{
		ScrmUserId:   ScrmUserId,
		UserMobile:   UserMobile,
		PushType:     PushType,
		UniqKey:      UniqKey,
		NoticeExtras: noticeExtras,
	}
}

//极光推送
func (p *PushFeedbackCommentMessage) Push() error {
	glog.Info("Push 入参：", p.ScrmUserId, kit.JsonEncode(p))
	//发送消息一定要执行这个函数，判断
	if err := p.init(); err != nil {
		return myerror.New("Push 发送消息初始化出错", kit.JsonEncode(p), err.Error(), err.(*myerror.MyError).GetMsg())
	}

	redisKey := "jpush:feedback-comment:" + p.UniqKey
	redisConn := GetRedisConn()
	defer redisConn.Close()
	defer redisConn.Del(redisKey)
	if redisConn.SetNX(redisKey, time.Now().Unix(), 1*time.Minute).Val() {
		jPushClient := jpush.NewPushClient(ahWenJPushSecret, ahWenJPushAppId)
		glog.Info("Push-推送参数：", p.ScrmUserId, kit.JsonEncode(p.JpushContent))
		msgId, err := jPushClient.SendPushString(p.JpushContent)
		if err != nil {
			glog.Error("Push 发送极光推送失败，参数：", p.JpushContent, redisKey, ";错误信息：", err)
			return err
		}
		glog.Info("Push-推送结果：", p.ScrmUserId, msgId, err)
	} else {
		glog.Info("Push NewFeedBackPush.init 参数：", kit.JsonEncode(p), redisKey, ",已经推送过一次了")
	}
	return nil
}

//发送消息初始化 （发送消息之前一定要调用该方法）
func (p *PushFeedbackCommentMessage) init() error {
	if p.PushType <= 0 {
		return myerror.New("PushSubscribeMessage.init", kit.JsonEncode(p), "", "消息推送类型必须大于0")
	}

	jpushMsg, ok := enum.JPushMSGPushTypeMapText[p.PushType]
	if !ok {
		return myerror.New("PushSubscribeMessage.init", kit.JsonEncode(p), "", "无效的消息推送类型", 4)
	}
	// 处理极光推送的参数
	jPushPayLoad := jpush.NewPushPayLoad()
	jPushPayLoad.SetPlatform(&jpush.Platform{Os: "all"})

	audience := &jpush.Audience{Object: "alias"}
	audience.SetAlias([]string{fmt.Sprintf("%s%s", utils.GenerateSpecialStrByEnv(), p.UserMobile)})
	jPushPayLoad.SetAudience(audience)

	jPushPayLoad.Notification = jpush.Notice{
		Alert: jpushMsg,
		Android: &jpush.AndroidNotice{
			Alert:     jpushMsg,
			Title:     jpushMsg,
			BuilderId: 1,
			Extras:    p.NoticeExtras,
		},
		IOS: &jpush.IOSNotice{
			Alert:  jpushMsg,
			Sound:  "default",
			Badge:  "1",
			Extras: p.NoticeExtras,
		},
	}
	jPushPayLoadStr, _ := json.Marshal(jPushPayLoad)
	p.JpushContent = string(jPushPayLoadStr)
	return nil
}
