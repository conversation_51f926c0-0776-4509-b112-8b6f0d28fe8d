package services

import (
	"_/models"
	"_/proto/cc"
	"context"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"sync"
	"time"

	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

type UserService struct {
}

func (userService *UserService) UserIntegral(ctx context.Context, req *cc.UserIntegralRequest) (*cc.UserIntegralResponse, error) {
	response := cc.UserIntegralResponse{}

	userIntegral, err := GetUserIntegral(req.MemberId, req.OrgId)
	if err != nil {
		return &response, err
	}
	response.Integral = userIntegral.Integral
	response.LastTime = userIntegral.Lasttime.Format("2006-01-02 15:04:05")
	return &response, nil
}
func (userService *UserService) UserInfo(ctx context.Context, req *cc.UserInfoRequest) (*cc.UserInfoResponse, error) {
	var response *cc.UserInfoResponse

	userInfo, err := GetUserInfo(req.MemberId, req.Mobile)
	if err != nil {
		return response, err
	}
	response = userInfo.ToUserInfoResponse()
	return response, nil
}
func (userService *UserService) UserVipInfo(ctx context.Context, req *cc.UserInfoRequest) (*cc.UserVipInfoResponse, error) {
	response := cc.UserVipInfoResponse{}

	if len(req.MemberId) <= 0 {
		userInfo, err := GetUserInfo(req.MemberId, req.Mobile)
		if err != nil {
			return &response, err
		}
		req.MemberId = userInfo.UserId
	}

	userVipInfos, err := GetUserVipInfo(req.MemberId)
	if err != nil {
		return &response, err
	}

	for _, v := range userVipInfos {
		response.Data = append(response.Data, v.ToUserVipInfo())
	}
	return &response, nil
}

func (userService *UserService) SubscribeExpertRemind(ctx context.Context, req *cc.SubscribeExpertRemindRequest) (*cc.BaseReq, error) {
	out := new(cc.BaseReq)

	expertRemind := &models.ExpertRemind{
		UserId:     req.UserId,
		PetId:      req.PetId,
		TemplateId: req.TemplateId,
		Type:       req.Type,
		CreateTime: time.Now(),
	}
	_, err := Engine.Insert(expertRemind) // 返回自增id
	if err != nil {
		glog.Errorf("SubscribeExpertRemind异常，参数：%v，错误：%v", kit.JsonEncode(expertRemind), err.Error())
		return nil, errors.New("提交数据错误，请重试")
	}
	return out, nil
}

func (userService *UserService) GetExpertRemindNum(ctx context.Context, req *cc.GetExpertRemindNumRequest) (*cc.GetExpertRemindNumResponse, error) {
	out := new(cc.GetExpertRemindNumResponse)

	db := Engine.Table("expert_remind")

	if len(req.PetId) > 0 {
		db.And("pet_id = ?", req.PetId)
	}
	if len(req.TemplateId) > 0 {
		db.And("template_id = ?", req.TemplateId)
	}
	if len(req.UserId) > 0 {
		db.And("user_id = ?", req.UserId)
	}
	if req.Type > 0 {
		db.And("type = ?", req.Type)
	}
	total, err := db.Count()
	if err != nil {
		glog.Errorf("GetExpertRemindRequest异常，参数：%v，错误：%v", kit.JsonEncode(req), err.Error())
		return nil, errors.New("提交数据错误，请重试")
	}
	out.Num = total
	return out, nil
}

func (userService *UserService) MemberCounts(ctx context.Context, req *cc.MemberCountsRequest) (*cc.MemberCountsResponse, error) {
	out := new(cc.MemberCountsResponse)

	memberCounts := make(map[int32]*cc.MemberCounts)
	number, err := userTotalCounts()
	if err != nil {
		return nil, err
	}
	var (
		totalNumber           int32
		userUpgradeNumTotal   int32
		userDowngradeNumTotal int32
	)

	for i := int32(0); i < 9; i++ {
		memberCounts[i] = &cc.MemberCounts{
			Level: i,
		}
		for _, n := range number {
			if n.Level == i {
				memberCounts[i].UserTotal = n.Num
				memberCounts[i].UserNum = n.Num
				totalNumber += n.Num
			}
		}
	}

	wg := new(sync.WaitGroup)
	wg.Add(3)
	go func() error {
		defer wg.Done()
		err = payUserTotalCounts(req.BeginTime, req.EndTime, memberCounts)
		if err != nil {
			return err
		}
		return nil
	}()

	go func() error {
		defer wg.Done()
		err = couponUserTotalCounts(req.BeginTime, req.EndTime, memberCounts)
		if err != nil {
			return err
		}
		return nil
	}()
	go func() error {
		defer wg.Done()
		err = newUserTotalCounts(req.BeginTime, req.EndTime, memberCounts)
		if err != nil {
			return err
		}
		userUpgradeNumTotal, userDowngradeNumTotal, err = userNumCounts(req.BeginTime, req.EndTime, memberCounts)
		if err != nil {
			return err
		}
		return nil
	}()

	wg.Wait()

	for i, _ := range memberCounts {
		if userUpgradeNumTotal > 0 {
			memberCounts[i].UserUpgradeNumRate = cast.ToFloat32(fmt.Sprintf("%.2f", (cast.ToFloat32(memberCounts[i].UserUpgradeNum)/cast.ToFloat32(userUpgradeNumTotal))*100))
		}
		if userDowngradeNumTotal > 0 {
			memberCounts[i].UserDowngradeNumRate = cast.ToFloat32(fmt.Sprintf("%.2f", cast.ToFloat32(memberCounts[i].UserDowngradeNum)/cast.ToFloat32(userDowngradeNumTotal)*100))
		}
		if totalNumber > 0 {
			memberCounts[i].UserNumRate = cast.ToFloat32(fmt.Sprintf("%.2f", cast.ToFloat32(memberCounts[i].UserTotal)/cast.ToFloat32(totalNumber)*100))
		}
		if memberCounts[i].CouponUserTotal > 0 {
			memberCounts[i].CouponVerifyRate = cast.ToFloat32(fmt.Sprintf("%.2f", (memberCounts[i].CouponVerifyRate/cast.ToFloat32(memberCounts[i].MallCouponTotal+memberCounts[i].ShopCouponTotal))*100))
		}
		if memberCounts[i].MallCouponTotal > 0 {
			memberCounts[i].MallCouponVerifyRate = cast.ToFloat32(fmt.Sprintf("%.2f", (memberCounts[i].MallCouponVerifyRate/cast.ToFloat32(memberCounts[i].MallCouponTotal))*100))
		}
		if memberCounts[i].ShopCouponTotal > 0 {
			memberCounts[i].ShopCouponVerifyRate = cast.ToFloat32(fmt.Sprintf("%.2f", (memberCounts[i].ShopCouponVerifyRate/cast.ToFloat32(memberCounts[i].ShopCouponTotal))*100))
		}
		if memberCounts[i].PayUserTotal > 0 {
			memberCounts[i].UserOrderPrice = cast.ToFloat32(fmt.Sprintf("%.2f", memberCounts[i].UserPayAmount/cast.ToFloat32(memberCounts[i].PayUserTotal)))
		}
		out.Data = append(out.Data, memberCounts[i])
	}
	return out, nil

}

// CheckBlackList 检查黑名单
func (userService *UserService) CheckBlackList(ctx context.Context, in *cc.CheckBlackListReq) (out *cc.BaseRes, e error) {
	out = &cc.BaseRes{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("CheckBlackList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.Mobile) == 0 {
		out.Message = "Mobile不能为空"
		return
	}

	mobiles := []string{in.Mobile}
	if len(in.ReceiverMobile) > 0 {
		mobiles = append(mobiles, in.ReceiverMobile)
	}
	db := NewCustomerEngine()

	// 最后一次解封时间
	var unlockTime time.Time
	var unlockTimeString string
	if _, err := db.Table("dc_customer.risk_user").
		Where("status = 1 and unlock_time > ?", time.Now().Format(kit.DATETIME_LAYOUT)).
		In("mobile", mobiles).OrderBy("unlock_time desc").
		Select("unlock_time").Get(&unlockTimeString); err != nil {
		out.Message = err.Error()
		return
	} else if len(unlockTimeString) == 0 { // 没有黑名单
		out.Code = 200
		return
	} else {
		unlockTime, _ = time.ParseInLocation(kit.DATETIME_LAYOUT, unlockTimeString, time.Local)
	}

	// 封控了，再看看账号白名单
	if has, err := db.Table("dc_customer.risk_whitelist").Where("mobile = ? and status = 1", in.Mobile).Exist(); err != nil {
		out.Message = err.Error()
		return
	} else if has {
		out.Code = 200
		return
	}

	seconds := unlockTime.Sub(time.Now()).Seconds()
	notice := "账号存在异常，已被管控，本次交易无法完成。账号将在%d%s后解封。"

	// 超过天
	if seconds > (24 * 60 * 60) {
		out.Message = fmt.Sprintf(notice, int(seconds/(24*60*60))+1, "天")
	} else if seconds > (60 * 60) { // 超过小时
		out.Message = fmt.Sprintf(notice, int(seconds/(60*60))+1, "小时")
	} else if seconds > 60 { // 超过分钟
		out.Message = fmt.Sprintf(notice, int(seconds/60)+1, "分钟")
	} else {
		out.Message = fmt.Sprintf(notice, int(seconds), "秒")
	}

	return
}

// QueryCustomerId 通过scrmId 查子龙的customerId
func (userService *UserService) QueryCustomerId(ctx context.Context, in *cc.QueryCustomerIdReq) (out *cc.QueryCustomerIdRes, e error) {
	out = &cc.QueryCustomerIdRes{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("QueryCustomerId 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if err := ZlScrmEngine.Table("t_scrm_map").Select("target_id").Where("type= 1 and source=0 and status=0 and scrm_id = ?", in.ScrmId).
		Find(&out.CustomerId); err != nil {
		out.Message = err.Error()
		return
	} else if len(out.CustomerId) == 0 {
		out.Message = "未找到会员id"
		return
	}

	out.Code = 200
	return
}

func userTotalCounts() ([]models.Counts, error) {
	var counts []models.Counts
	err := BbcEngine.Table("upet_member").Select("user_level_id level,count(member_id) num").GroupBy("user_level_id").Find(&counts)
	if err != nil {
		return nil, err
	}
	if len(counts) <= 0 {
		return nil, errors.New("查不到数据")
	}
	return counts, nil
}

// 新增会员数：自然日新增新注册会员数
func newUserTotalCounts(dayStr, endStr string, out map[int32]*cc.MemberCounts) error {
	var counts []models.Counts

	err := BbcEngine.SQL("SELECT u.user_level_id level,count(u.scrm_user_id) num from (select max(id),um.scrm_user_id,um.user_level_id,if(ISNULL(ul.create_time),FROM_UNIXTIME(um.member_time),ul.create_time) create_time from `upet_member` AS `um` left JOIN upet_member_level_log ul ON um.scrm_user_id = ul.scrm_user_id and um.user_level_id = ul.new_user_level_id where um.scrm_user_id != '' and (um.user_level_id =0 and if(ISNULL(ul.create_time),FROM_UNIXTIME(um.member_time),ul.create_time) >= ? and if(um.user_level_id =0,FROM_UNIXTIME(um.member_time),ul.create_time) < ?) or (um.user_level_id>0 and ul.create_time >= ? and ul.create_time < ?) GROUP BY um.scrm_user_id) u GROUP BY u.user_level_id", dayStr, endStr, dayStr, endStr).Find(&counts)
	if err != nil {
		return err
	}
	if len(counts) > 0 {
		for _, i := range counts {
			out[i.Level].NewUserTotal = i.Num
		}
	}
	return nil
}

// 支付会员数 会员支付订单数 会员支付金额 会员客单价=会员支付金额/支付会员数
func payUserTotalCounts(starTime, endTime string, out map[int32]*cc.MemberCounts) error {
	var payCounts []models.PayCounts
	err := OrderEngine.SQL("select um.user_level_id level_id ,count(DISTINCT(u.member_id)) pay_user_total, count(u.member_id) user_order_total,sum(pay_amount) total_pay_amount from (SELECT u.member_id,u.pay_amount,order_sn FROM(SELECT member_id,pay_amount,order_sn FROM order_main WHERE is_pay = 1 AND parent_order_sn = '' AND create_time > ? AND create_time < ? AND member_id != '' AND pay_amount > 0 UNION ALL SELECT user_id member_id,pay_price pay_amount,pin_order_sn order_sn FROM pin_order_group WHERE STATUS IN ( 20, 30, 40 ) AND create_time > ? AND create_time < ? AND user_id != '' AND pay_price > 0 UNION ALL SELECT scrm_user_id member_id,actual_amount pay_amount,order_sn FROM datacenter.pos_orders WHERE is_push IN ( 0, 2 ) AND created_time > ? AND created_time < ? AND scrm_user_id != '' AND actual_amount > 0 AND payment_type != 5 ) u GROUP BY u.order_sn) u INNER JOIN upetmart.upet_member um on um.scrm_user_id = u.member_id GROUP BY um.user_level_id ", starTime, endTime, starTime, endTime, starTime, endTime).Find(&payCounts)
	if err != nil {
		return err
	}
	for _, k := range payCounts {
		if _, ok := out[k.LevelId]; ok {

			out[k.LevelId].UserPayAmount += cast.ToFloat32(kit.FenToYuan(k.TotalPayAmount))
			out[k.LevelId].UserOrderTotal += k.UserOrderTotal
			out[k.LevelId].PayUserTotal += k.PayUserTotal
		}
	}

	return nil
}

func couponUserTotalCounts(starTime, endTime string, out map[int32]*cc.MemberCounts) error {
	var couponUserCounts []models.CouponUserCounts
	err := DataCenterEngine.Table("user_coupon").Alias("u").
		Join("inner", "upetmart.upet_member um", "um.scrm_user_id = u.user_id").
		Select("um.user_level_id level_id,count(DISTINCT(u.user_id)) user_num").Where("u.create_time > ? and u.create_time < ? ", starTime, endTime).GroupBy("um.user_level_id").Find(&couponUserCounts)
	if err != nil {
		return err
	}
	if len(couponUserCounts) <= 0 {
		return nil
	}

	var couponCounts []models.CouponCounts
	err = DataCenterEngine.Table("user_coupon").Alias("u").
		Join("inner", "upetmart.upet_member um", "um.scrm_user_id = u.user_id").
		Select("um.user_level_id level_id,u.coupon_type,count(um.user_level_id) total_num").Where("u.create_time > ? and u.create_time < ? ", starTime, endTime).GroupBy("um.user_level_id,u.coupon_type").Find(&couponCounts)
	if err != nil {
		return err
	}

	//商城券核销人数
	var couponWriteOff []models.CouponWriteOff
	err = DataCenterEngine.Table("user_coupon").Alias("u").
		Join("inner", "upetmart.upet_member um", "um.scrm_user_id = u.user_id").
		Join("inner", "upetmart.upet_voucher uv", "uv.voucher_id = u.voucher_id and date(u.create_time) = date(uv.write_off_date) and uv.voucher_state = 2").
		Where("u.create_time > ? and u.create_time < ? and u.coupon_type = 2", starTime, endTime).Select("um.user_level_id level_id,count(DISTINCT(u.user_id)),count(um.user_level_id) total_num").GroupBy("um.user_level_id").Find(&couponWriteOff)

	if err != nil {
		return err
	}

	for _, k := range couponUserCounts {
		if _, ok := out[k.LevelId]; ok {
			out[k.LevelId].CouponUserTotal += k.UserNum
		}
	}
	for _, k := range couponCounts {
		if _, ok := out[k.LevelId]; ok {
			if k.CouponType == 1 {
				out[k.LevelId].ShopCouponTotal += k.TotalNum
			} else if k.CouponType == 2 {
				out[k.LevelId].MallCouponTotal += k.TotalNum
			}
		}
	}
	for _, k := range couponWriteOff {
		if _, ok := out[k.LevelId]; ok {
			out[k.LevelId].MallCouponVerifyRate += cast.ToFloat32(k.TotalNum)
			out[k.LevelId].CouponVerifyRate += cast.ToFloat32(k.TotalNum)
		}
	}

	pageIndex, pageSize := 1, 1000
	for {
		var (
			memberCoupons []models.ZlMemberCoupon
			zlCouponCode  []string
		)
		memberCouponsMap := make(map[string]models.ZlMemberCoupon)

		err = DataCenterEngine.Table("user_coupon").Alias("u").
			Join("inner", "upetmart.upet_member um", "um.scrm_user_id = u.user_id").
			Select("um.user_level_id level_id,u.create_time,u.coupon_code").Where("u.coupon_type = 1 and u.create_time > ? and u.create_time < ? ", starTime, endTime).Limit(pageSize, (pageIndex*pageSize)-pageSize).Find(&memberCoupons)
		if err != nil {
			return err
		}
		if len(memberCoupons) <= 0 {
			break
		}
		for _, i := range memberCoupons {
			zlCouponCode = append(zlCouponCode, i.CouponCode)
		}

		for index, _ := range memberCoupons {
			memberCouponsMap[memberCoupons[index].CouponCode] = memberCoupons[index]
		}

		var zlCouponWriteOffs []models.ZlCouponWriteOffDate
		err = ZlHospitalNewEngine().Table("pos_payment_promotion").Select("create_date write_off_date,promotion_number").In("promotion_number", zlCouponCode).Find(&zlCouponWriteOffs)
		if err != nil {
			return err
		}

		for _, k := range zlCouponWriteOffs {
			if memberCouponsMap[k.PromotionNumber].CreateTime.Year() == k.WriteOffDate.Year() &&
				memberCouponsMap[k.PromotionNumber].CreateTime.Month() == k.WriteOffDate.Month() &&
				memberCouponsMap[k.PromotionNumber].CreateTime.Day() == k.WriteOffDate.Day() {
				if _, ok := memberCouponsMap[k.PromotionNumber]; ok {
					if _, ok1 := out[memberCouponsMap[k.PromotionNumber].LevelId]; ok1 {
						out[memberCouponsMap[k.PromotionNumber].LevelId].CouponVerifyRate++
						out[memberCouponsMap[k.PromotionNumber].LevelId].ShopCouponVerifyRate++
					}
				}
			}
		}
		pageIndex++
	}

	return nil
}

func userNumCounts(starTime, endTime string, out map[int32]*cc.MemberCounts) (userUpgradeNumTotal, userDowngradeNumTotal int32, err error) {

	var levelCounts []models.Counts
	err = BbcEngine.Table("upet_member_level_log").Select("`old_user_level_id` level,count(scrm_user_id) num,`lift_type` type").Where("create_time > ? and create_time < ?", starTime, endTime).GroupBy("lift_type,`old_user_level_id`").Find(&levelCounts)
	if err != nil {
		return userUpgradeNumTotal, userDowngradeNumTotal, err
	}
	if len(levelCounts) > 0 {
		for _, i := range levelCounts {
			if i.Type == 1 {
				out[i.Level].UserUpgradeNum = i.Num
				userUpgradeNumTotal += i.Num
			} else if i.Type == 2 {
				out[i.Level].UserDowngradeNum = i.Num
				userDowngradeNumTotal += i.Num
			}
		}
	}
	return userUpgradeNumTotal, userDowngradeNumTotal, nil
}
