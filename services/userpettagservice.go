package services

import (
	"_/models"
	"_/proto/cc"
	"_/utils"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
)

// 物种缓存
var petVarietyData map[int]string = make(map[int]string)

type UserPetTagService struct {
}

func (service *UserPetTagService) getDbEngine() *xorm.Engine {
	return NewEngine()
}
func (service *UserPetTagService) getScrmDbEngine() *xorm.Engine {

	mySqlStr := config.GetString("mysql.scrm")
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	if Debug {
		engine.ShowSQL()
		engine.ShowExecTime()
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}
func (service *UserPetTagService) GetDbEngine() *xorm.Engine {
	return NewEngine()
}
func (service *UserPetTagService) GetScrmDbEngine() *xorm.Engine {

	mySqlStr := config.GetString("mysql.scrm")
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	if Debug {
		engine.ShowSQL()
		engine.ShowExecTime()
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

// 开始执行SQL查询逻辑
func (service *UserPetTagService) beginQuery(queryFunc func(*xorm.Engine) error) error {

	err := queryFunc(service.getDbEngine())
	if err != nil {
		glog.Error(err)
	}
	return err
}

// 开始事务处理逻辑
func (service *UserPetTagService) beginTran(callFunc func(*xorm.Session) error) error {

	// 开启事物
	session := service.getDbEngine().NewSession()
	defer session.Close()
	session.Begin()

	// 调用处理逻辑
	err := callFunc(session)

	// 如果没有逻辑错误
	if err == nil {
		// 提交保存
		session.Commit()
	} else {
		glog.Error(err)
		// 回滚
		session.Rollback()
	}

	return err
}

// 查询宠物标签数据
func (service *UserPetTagService) QueryPetTag(userId string, petId string) (*cc.PetTagQueryResponse, error) {
	var response = &cc.PetTagQueryResponse{Code: 200}
	var query = service.getDbEngine().Table(&models.UserPetTag{})
	if len(userId) > 0 {
		query = query.Where("user_id=?", userId)
	}
	if len(petId) > 0 {
		query = query.Where("pet_id=?", petId)
	}
	var models []*models.UserPetTag
	err := query.Limit(1000, 0).Find(&models)
	if err != nil {
		response.Code = 400
		response.Message = err.Error()
	} else {
		for _, model := range models {
			response.Data = append(response.Data, model.ToPetTagDto())
		}
	}
	return response, err
}

// 根据用户Id生成宠物的标签信息
func (service *UserPetTagService) GeneratePetTagByUserId(userId string) {
	// 查询宠物列表
	var petModels []*models.ScrmUserPet
	err := service.getScrmDbEngine().Where("user_id=?", userId).Find(&petModels)
	if err != nil {
		fmt.Println(err)
	} else {
		if len(petModels) > 0 {
			// 开启事务
			service.beginTran(func(session *xorm.Session) error {
				for _, pet := range petModels {
					_, err := session.Where("pet_id=?", pet.PetId).Delete(&models.UserPetTag{})
					if err != nil {
						return err
					}
					service.generatePetSexTag(pet, session)
					service.generatePetNeuteringTag(pet, session)
					service.generatePetAgeTag(pet, session)
					service.generatePetKindofTag(pet, session)
					service.generatePetVariety(pet, session)
					service.generatePetSpecialAgeTag(pet, session)
				}
				return nil
			})
		}
	}
}

// 根据用户Id生成宠物的标签信息
func (service *UserPetTagService) GeneratePetTagByPetId(petId string) {
	// 查询宠物列表
	var petModels []*models.ScrmUserPet
	err := service.getScrmDbEngine().Where("pet_id=?", petId).Find(&petModels)
	if err != nil {
		fmt.Println(err)
	} else {
		// 开启事务
		service.beginTran(func(session *xorm.Session) error {
			for _, pet := range petModels {
				_, err := session.Where("pet_id=?", pet.PetId).Delete(&models.UserPetTag{})
				if err != nil {
					return err
				}
				service.generatePetSexTag(pet, session)
				service.generatePetNeuteringTag(pet, session)
				service.generatePetAgeTag(pet, session)
				service.generatePetKindofTag(pet, session)
				service.generatePetVariety(pet, session)
				service.generatePetSpecialAgeTag(pet, session)
			}
			return nil
		})
	}
}

// 性别
func (service *UserPetTagService) generatePetSexTag(pet *models.ScrmUserPet, session *xorm.Session) {
	// 性别
	var petTag = new(models.UserPetTag)
	petTag.UserId = pet.UserId
	petTag.PetId = pet.PetId
	petTag.TagName = "性别"
	if pet.PetSex == 1 {
		petTag.TagValue = "公"
	} else if pet.PetSex == 2 {
		petTag.TagValue = "母"
	} else {
		petTag.TagValue = "不限"
	}
	session.Insert(petTag)
}

// 绝育
func (service *UserPetTagService) generatePetNeuteringTag(pet *models.ScrmUserPet, session *xorm.Session) {
	// 绝育
	var petTag = new(models.UserPetTag)
	petTag.UserId = pet.UserId
	petTag.PetId = pet.PetId
	petTag.TagName = "是否绝育"
	if pet.PetNeutering == 0 {
		petTag.TagValue = "未绝育"
	} else if pet.PetNeutering == 2 {
		petTag.TagValue = "已绝育"
	} else {
		petTag.TagValue = "不限"
	}
	session.Insert(petTag)
}

// 年龄
func (service *UserPetTagService) generatePetAgeTag(pet *models.ScrmUserPet, session *xorm.Session) {
	// 年龄
	var petTag = new(models.UserPetTag)
	petTag.UserId = pet.UserId
	petTag.PetId = pet.PetId
	petTag.TagName = "年龄"
	var diffYear = time.Now().Year() - pet.PetBirthday.Year()
	if diffYear == 0 {
		petTag.TagValue = "幼年"
	} else if diffYear >= 1 && diffYear < 10 {
		petTag.TagValue = "成年"
	} else if diffYear >= 10 && diffYear < 20 {
		petTag.TagValue = "老年"
	} else {
		petTag.TagValue = "不限"
	}
	session.Insert(petTag)
}

// 种类
func (service *UserPetTagService) generatePetKindofTag(pet *models.ScrmUserPet, session *xorm.Session) {
	// 种类
	var petTag = new(models.UserPetTag)
	petTag.UserId = pet.UserId
	petTag.PetId = pet.PetId
	petTag.TagName = "物种"
	if pet.PetKindof == 1000 {
		petTag.TagValue = "猫"
	} else if pet.PetKindof == 1001 {
		petTag.TagValue = "犬"
	} else {
		petTag.TagValue = "不限"
	}
	session.Insert(petTag)
}

// 品种
func (service *UserPetTagService) generatePetVariety(pet *models.ScrmUserPet, session *xorm.Session) {
	var petTag = new(models.UserPetTag)
	petTag.UserId = pet.UserId
	petTag.PetId = pet.PetId
	petTag.TagName = "品种"
	petTag.TagValue = "不限"
	if pet.PetVariety != -1 {
		if tavValue, ok := petVarietyData[pet.PetVariety]; ok {
			petTag.TagValue = tavValue
		} else {
			// 查询品种
			var resData = service.queryScrmPetVariety(strconv.Itoa(pet.PetVariety))
			if resData != nil && len(resData.Result) > 0 {
				petTag.TagValue = resData.Result[0].PetDictName
				petVarietyData[pet.PetVariety] = resData.Result[0].PetDictName
			}
		}
	}

	session.Insert(petTag)
}

// 特殊阶段
func (service *UserPetTagService) generatePetSpecialAgeTag(pet *models.ScrmUserPet, session *xorm.Session) {

	var diffhours = int(math.Floor(time.Now().Sub(pet.PetBirthday).Hours()))

	var dayHours = 24
	var monthHours = 30 * dayHours
	getPetTag := func(tagValue string) *models.UserPetTag {
		var petTag = new(models.UserPetTag)
		petTag.UserId = pet.UserId
		petTag.PetId = pet.PetId
		petTag.TagName = "特殊阶段"
		petTag.TagValue = tagValue
		return petTag
	}

	var specialAgeList []string
	if pet.PetKindof == 1000 { // 猫
		var isSpecialAge = false
		if diffhours >= 0 && diffhours < 30*dayHours {
			specialAgeList = append(specialAgeList, "哺乳期")
			isSpecialAge = true
		}
		if diffhours >= 30*dayHours && diffhours < 45*dayHours {
			specialAgeList = append(specialAgeList, "离乳期")
			isSpecialAge = true
		}
		if diffhours >= 3*monthHours && diffhours <= 7*monthHours {
			specialAgeList = append(specialAgeList, "换牙期")
			isSpecialAge = true
		}
		if diffhours >= 2*monthHours && diffhours <= 6*monthHours {
			specialAgeList = append(specialAgeList, "首次疫苗")
			isSpecialAge = true
		}
		if diffhours >= 5*monthHours && diffhours <= 6*monthHours {
			specialAgeList = append(specialAgeList, "最佳绝育期")
			isSpecialAge = true
		}
		if !isSpecialAge {
			specialAgeList = append(specialAgeList, "不限")
		}
	} else if pet.PetKindof == 1001 { // 狗
		var isSpecialAge = false
		if diffhours >= 0 && diffhours < 45*dayHours {
			specialAgeList = append(specialAgeList, "哺乳期")
			isSpecialAge = true
		}
		if diffhours >= 30*dayHours && diffhours < 45*dayHours {
			specialAgeList = append(specialAgeList, "离乳期")
			isSpecialAge = true
		}
		if diffhours >= 5*7*dayHours && diffhours <= 6*7*dayHours {
			specialAgeList = append(specialAgeList, "首次疫苗")
			isSpecialAge = true
		}
		if diffhours >= 3*monthHours && diffhours <= 7*monthHours {
			specialAgeList = append(specialAgeList, "换牙期")
			isSpecialAge = true
		}
		if diffhours >= 10*monthHours && diffhours <= 12*monthHours {
			specialAgeList = append(specialAgeList, "最佳绝育期")
			isSpecialAge = true
		}
		if !isSpecialAge {
			specialAgeList = append(specialAgeList, "不限")
		}
	} else {
		specialAgeList = append(specialAgeList, "不限")
	}
	// 保存一条记录
	session.Insert(getPetTag(strings.Join(specialAgeList, "/")))
}

// 查询宠物种类
func (service *UserPetTagService) queryScrmPetVariety(petId string) *models.ResData {
	var querySTr = `{"criteria": {}}`
	if len(petId) > 0 {
		querySTr = fmt.Sprintf(`{"criteria": {"petDictId":"%s"}}`, petId)
	}
	url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-url"), "/scrm-organization-api/petDict/query")
	resData, err := utils.HttpPost(url, []byte(querySTr), "")
	if resData != nil {
		var resStruct models.ResData
		err = json.Unmarshal(resData, &resStruct)
		if err != nil {
			glog.Error(err)
		}
		return &resStruct
	}
	return nil
}
