package services

import (
	"_/models"
	"_/proto/cc"
	"_/utils"
	"context"
	"reflect"
	"testing"

	kit "github.com/tricobbler/rp-kit"
)

func TestCustomerCenterService_QueryUsuallyRecordProduct(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *cc.RecommendProductQueryRequest
	}

	var params cc.RecommendProductQueryRequest
	params.UserId = "c924eec634f13d6a"

	tests := []struct {
		name    string
		args    args
		want    *cc.QueryUsuallyRecordProductResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "QueryUsuallyRecordProduct", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.QueryUsuallyRecordProduct(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryUsuallyRecordProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("QueryUsuallyRecordProduct() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomerCenterService_QueryTags(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.TagsQueryRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *cc.TagsQueryResponse
		wantErr bool
	}{
		{
			args: args{
				req: &cc.TagsQueryRequest{
					Groups: 1,
					From:   "content",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.QueryTags(tt.args.ctx, tt.args.req)
			t.Log(got, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryTags() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCustomerCenterService_TaskList(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.TaskListReq
	}
	tests := []struct {
		name    string
		args    args
		want    *cc.TaskListRes
		wantErr bool
	}{
		{
			name: "TestCustomerCenterService_TaskList",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.TaskList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("TaskList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TaskList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomerCenterService_MemberEquityList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *cc.MemberEquityListReq
	}
	tests := []struct {
		name    string
		args    args
		want    *cc.MemberEquityListRes
		wantErr bool
	}{
		{
			name: "TestCustomerCenterService_MemberEquityList",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.MemberEquityList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("MemberEquityList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MemberEquityList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomerCenterService_UserLevelList(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.UserLevelListReq
	}
	tests := []struct {
		name    string
		service *CustomerCenterService
		args    args
		want    *cc.UserLevelListRes
		wantErr bool
	}{
		{
			args: args{
				req: &cc.UserLevelListReq{
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.UserLevelList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CustomerCenterService.UserLevelList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CustomerCenterService.UserLevelList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomerCenterService_UserLevelEquities(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.UserLevelEquitiesReq
	}
	tests := []struct {
		name    string
		service *CustomerCenterService
		args    args
		want    *cc.UserLevelEquitiesRes
		wantErr bool
	}{
		{
			args: args{
				req: &cc.UserLevelEquitiesReq{
					LevelId: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.UserLevelEquities(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CustomerCenterService.UserLevelEquities() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(11, err, got)
		})
	}
}

func TestCustomerCenterService_UserEditEquityList(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.EmptyReq
	}
	tests := []struct {
		name    string
		service *CustomerCenterService
		args    args
		want    *cc.UserEditEquityListRes
		wantErr bool
	}{
		{
			args: args{
				req: &cc.EmptyReq{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.UserEditEquityList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CustomerCenterService.UserEditEquityList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Message != "" {
				t.Errorf("CustomerCenterService.UserEditEquityList() = %v, want %v", got, tt.want)
			} else {
				t.Log(11, got.List)
			}
		})
	}
}

func TestCustomerCenterService_AddUserHealthVal(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.AddUserHealthValReq
	}
	tests := []struct {
		name    string
		service *CustomerCenterService
		args    args
		want    *cc.Response
		wantErr bool
	}{
		{
			args: args{
				req: &cc.AddUserHealthValReq{
					UserId:     "697c53c494344f5eaa646b0e065f8914",
					HealthType: 1,
					Type:       1,
					Title:      "测试收入",
					Content:    "测试收入健康值",
					OrderSn:    "4100000025009128",
					PayAmount:  "100",
				},
			},
			want:    &cc.Response{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.AddUserHealthVal(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CustomerCenterService.AddUserHealthVal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CustomerCenterService.AddUserHealthVal() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_calculateHealthVal(t *testing.T) {
	type args struct {
		payAmount float64
		activeVal int64
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			args: args{
				payAmount: 10.52,
				activeVal: 100,
			},
			want: 29,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := utils.CalculateHealthVal(tt.args.payAmount); got != tt.want {
				t.Errorf("calculateHealthVal() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomerCenterService_RefreshUserLevel(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.EmptyReq
	}
	tests := []struct {
		name    string
		service *CustomerCenterService
		args    args
		want    *cc.Response
		wantErr bool
	}{
		{
			args: args{
				req: &cc.EmptyReq{},
			},
			want:    &cc.Response{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.RefreshUserLevel(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CustomerCenterService.RefreshUserLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CustomerCenterService.RefreshUserLevel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomerCenterService_SetNotificationState(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.SetNotificationStateReq
	}
	tests := []struct {
		name    string
		service *CustomerCenterService
		args    args
		want    *cc.Response
		wantErr bool
	}{
		{
			args: args{
				req: &cc.SetNotificationStateReq{
					ScrmUserId:    "2abaad6b463f4d7fab9d4a565cab958a",
					All:           1,
					UserLevel:     1,
					Integral:      1,
					Voucher:       1,
					VipCardExpire: 1,
				},
			},
			want:    &cc.Response{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.SetNotificationState(tt.args.ctx, tt.args.req)
			t.Log(kit.JsonEncode(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("CustomerCenterService.SetNotificationState() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

		})
	}
}

func TestCustomerCenterService_AddNotificationMessage(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.AddNotificationMessageReq
	}
	tests := []struct {
		name    string
		service *CustomerCenterService
		args    args
		want    *cc.Response
		wantErr bool
	}{
		{
			args: args{
				req: &cc.AddNotificationMessageReq{
					ScrmUserId: "2abaad6b463f4d7fab9d4a565cab958a",
					Messages: []*cc.NotificationMessage{
						//{TemplateId: "dVMdRlYE5obhuc1_1P663aRWCxSPYu95kx3oSNtxjzg", Type: 1},
						//{TemplateId: "WDraXT5RXhU3FeIkMJpQfhDhhZgj8b3FHhQyqRQZLkQ", Type: 1},
						//{TemplateId: "jAhxuRsGexaTGF2413jimfo3_dxPQY9kXb0BBAT2ffI", Type: 1},
						//{TemplateId: "-ZfVYMs9ZuZ2xkOfs1zWVAjLKiRDdFTQ6emZvlEo-q4", Type: 0},
						//{TemplateId: "r2znwrhqjtggyhwoo-qOaiNFflN_avv_8_LyS2Hc2pY", Type: 1},
						{TemplateId: "BzDJg4sqMyZS79D45ExfnRN2n_5J1zeWjzTDVvieZA0", Type: 1}, //会员权益过期通知
					},
				},
			},
			want:    &cc.Response{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.AddNotificationMessage(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CustomerCenterService.AddNotificationMessage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CustomerCenterService.AddNotificationMessage() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_handleMemberLevel(t *testing.T) {
	type args struct {
		scrmUserId string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			args:    args{scrmUserId: "6601ae33cb7b4393bfb98ac92810777b"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := handleMemberLevel(tt.args.scrmUserId); (err != nil) != tt.wantErr {
				t.Errorf("handleMemberLevel() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCustomerCenterService_EquityGetcoupon(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.EquityGetcouponReq
	}
	tests := []struct {
		name    string
		args    args
		want    *cc.BaseResponseNew
		wantErr bool
	}{
		{
			name: "EquityGetcoupon_TEST",
			args: args{
				ctx: context.Background(),
				req: &cc.EquityGetcouponReq{
					CouponId:   1164,
					CouponType: 2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.EquityGetcoupon(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("EquityGetcoupon() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(got)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EquityGetcoupon() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomerCenterService_SendSubscribeMessage(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.SendSubscribeMessageReq
	}
	tests := []struct {
		name    string
		service *CustomerCenterService
		args    args
		want    *cc.Response
		wantErr bool
	}{
		//{
		//	args: args{
		//		req: &cc.SendSubscribeMessageReq{
		//			ScrmUserId:    "14877d6e2f4f06e90d076cddbf53ba35",
		//			SubscribeType: "queuing",
		//			TemplateKey:   "register-queuing",
		//			Values: []*cc.MessageValue{
		//				{Type: "string", Value: "aaaa"},
		//				{Type: "string", Value: "bbbbb"},
		//				{Type: "string", Value: "cccccc"},
		//			},
		//			PageParams: "0c8186250c3e41d2b3488391285b73e2",
		//		},
		//	},
		//	want:    &cc.Response{Code: 200},
		//	wantErr: false,
		//},
		{
			args: args{
				req: &cc.SendSubscribeMessageReq{
					ScrmUserId:    "b341138ab8bf4493bdc3d943ed5ea189",
					SubscribeType: "",
					TemplateKey:   "vip_card_expire",
					Values: []*cc.MessageValue{
						{Type: "string", Value: "开卡礼包"},
						{Type: "string", Value: "2023-4-10 10:00:00"},
						{Type: "string", Value: "你的权益快到期了"},
					},
					//PageParams: "0c8186250c3e41d2b3488391285b73e2",
					OrgId: 1,
				},
			},
			want:    &cc.Response{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &CustomerCenterService{}
			got, err := service.SendSubscribeMessage(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CustomerCenterService.SendSubscribeMessage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Code != tt.want.Code {
				t.Errorf("CustomerCenterService.SendSubscribeMessage() = %v, want %v", got, tt.want)
			} else {
				t.Log(111, tt.want)
			}
		})
	}
}

func TestSendLevelChangeWxMessage(t *testing.T) {
	type args struct {
		member       *models.UpetMember
		newLevelId   int64
		newHealthVal int64
		oriLevelName string
		curLevelName string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			args: args{
				member:       &models.UpetMember{ScrmUserId: "14877d6e2f4f06e90d076cddbf53ba35"},
				newLevelId:   3,
				newHealthVal: 3000,
				oriLevelName: "v2",
				curLevelName: "v3",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SendLevelChangeWxMessage(tt.args.member, tt.args.newLevelId, tt.args.newHealthVal, tt.args.oriLevelName, tt.args.curLevelName)
		})
	}
}

func Test_getVoucherByUserid(t *testing.T) {
	type args struct {
		scrmUserid string
		couponType int32
	}
	tests := []struct {
		name string
		args args
		want []*cc.EquityCoupon
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				scrmUserid: "3928fb9aef33430ebcd022d996539c91",
				couponType: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getVoucherByUserid(tt.args.scrmUserid, tt.args.couponType); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getVoucherByUserid() = %v, want %v", got, tt.want)
			}
		})
	}
}
