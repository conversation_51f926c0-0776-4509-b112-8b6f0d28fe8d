package services

import (
	"time"

	"github.com/go-redis/redis"
	"github.com/spf13/cast"

	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
)

type BaseService struct {
}

var (
	Engine              *xorm.Engine
	OrderEngine         *xorm.Engine
	Debug               bool = true
	DataCenterEngine    *xorm.Engine
	ProductCenterEngine *xorm.Engine
	BbcEngine           *xorm.Engine
	AnalysisEngine      *xorm.Engine
	ContentCenterEngine *xorm.Engine
	ZlHospitalEngine    *xorm.Engine
	ZlScrmEngine        *xorm.Engine
)

func init() {
	Engine = NewEngine()

	if OrderEngine == nil {
		params := config.GetString("mysql.dc_order")
		//params = "s2b2c:9iIJth3tJzhmSk5w@(124.221.96.140:23306)/dc_order?charset=utf8mb4" //腾讯云测试
		//params = "root:XjIrQepuHn7u^E8D@(172.30.3.6:13306)/dc_order?charset=utf8mb4" // uat1

		engine, err := xorm.NewEngine("mysql", params)
		//engine.ShowSQL()
		//err = engine.Ping()
		if err != nil {
			panic(err)
		}

		//空闲关闭时间
		engine.SetConnMaxLifetime(60 * time.Second)
		//最大空闲连接
		engine.SetMaxIdleConns(10)
		//最大连接数
		engine.SetMaxOpenConns(1000)

		OrderEngine = engine
	}
	DataCenterEngine = DataNewEngine()
	ProductCenterEngine = ProductCenterNewEngine()
	BbcEngine = BbcNewEngine()
	AnalysisEngine = AnalysisNewEngine()
	ZlHospitalEngine = ZlHospitalNewEngine()
	ZlScrmEngine = ZlScrmNewEngine()
}

func NewEngine() *xorm.Engine {
	if Engine != nil {
		if err := Engine.DB().Ping(); err == nil {
			return Engine
		}
	}

	mySqlStr := config.GetString("mysql.dc_customer")
	//mySqlStr = "root:XjIrQepuHn7u^E8D@(39.106.30.60:13306)/dc_customer?charset=utf8mb4" // uat1
	//mySqlStr = "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/dc_customer?charset=utf8mb4" //sit
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	if Debug {
		engine.ShowSQL()
		engine.ShowExecTime()
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

func DataNewEngine() *xorm.Engine {
	if DataCenterEngine != nil {
		if err := DataCenterEngine.DB().Ping(); err == nil {
			return DataCenterEngine
		}
	}

	mySqlStr := config.GetString("mysql.datacenter")
	//mySqlStr = "root:root@(10.1.1.245:3306)/datacenter?charset=utf8mb4" //sit
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	if Debug {
		engine.ShowSQL()
		engine.ShowExecTime()
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

func ProductCenterNewEngine() *xorm.Engine {
	if ProductCenterEngine != nil {
		if err := ProductCenterEngine.DB().Ping(); err == nil {
			return DataCenterEngine
		}
	}

	mySqlStr := config.GetString("mysql.dc_product")
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	if Debug {
		engine.ShowSQL()
		engine.ShowExecTime()
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

func BbcNewEngine() *xorm.Engine {
	if BbcEngine != nil {
		if err := BbcEngine.DB().Ping(); err == nil {
			return BbcEngine
		}
	}

	mySqlStr := config.GetString("mysql.upetmart")
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}
	//mySqlStr = "root:XjIrQepuHn7u^E8D@(39.106.30.60:13306)/testupetmart?charset=utf8mb4" //uat1
	//mySqlStr = "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/testupetmart?charset=utf8mb4" //sit1
	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	if Debug {
		engine.ShowSQL()
		engine.ShowExecTime()
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

func AnalysisNewEngine() *xorm.Engine {
	if AnalysisEngine != nil {
		if err := AnalysisEngine.DB().Ping(); err == nil {
			return AnalysisEngine
		}
	}

	mySqlStr := config.GetString("mysql.dc_analysis")
	//mySqlStr = "readonly:dsax45677uDHR3gGGFJU-@(rm-wz995pj8775649i3f90170.mysql.rds.aliyuncs.com:5532)/dc_customer?charset=utf8mb4"
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	// if Debug {
	// 	engine.ShowSQL()
	// 	engine.ShowExecTime()
	// }

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(200)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

// redis短连接
func GetRedisConn() *redis.Client {
	var db = cast.ToInt(config.GetString("redis.DB"))
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")
	redisClient := redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     pwd,
		DB:           db,
		MinIdleConns: 28,
	})
	return redisClient
}

func ContentCenterNewEngine() *xorm.Engine {
	if ContentCenterEngine != nil {
		if err := ContentCenterEngine.DB().Ping(); err == nil {
			return ContentCenterEngine
		}
	}

	mySqlStr := config.GetString("mysql.dc_content")
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	if Debug {
		engine.ShowSQL()
		engine.ShowExecTime()
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}
func ZlHospitalNewEngine() *xorm.Engine {
	if ZlHospitalEngine != nil {
		if err := ZlHospitalEngine.DB().Ping(); err == nil {
			return ZlHospitalEngine
		}
	}

	mySqlStr := config.GetString("mysql.zl_hospital_db")
	//mySqlStr = "readonly:dsax45677uDHR3gGGFJU-@(rm-wz995pj8775649i3f90170.mysql.rds.aliyuncs.com:5532)/dc_customer?charset=utf8mb4"
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	// if Debug {
	// 	engine.ShowSQL()
	// 	engine.ShowExecTime()
	// }

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

func ZlScrmNewEngine() *xorm.Engine {
	if ZlScrmEngine != nil {
		if err := ZlScrmEngine.DB().Ping(); err == nil {
			return ZlScrmEngine
		}
	}

	mySqlStr := config.GetString("mysql.zl_scrm_third_db")
	//mySqlStr = "readonly:dsax45677uDHR3gGGFJU-@(rm-wz995pj8775649i3f90170.mysql.rds.aliyuncs.com:5532)/dc_customer?charset=utf8mb4"
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

func NewCustomerEngine() *xorm.Engine {

	mySqlStr := config.GetString("mysql.dc_customer")
	//mySqlStr = "root:XjIrQepuHn7u^E8D@(39.106.30.60:13306)/dc_customer?charset=utf8mb4" // uat1
	//mySqlStr = "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/dc_customer?charset=utf8mb4" //sit
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	//engine.ShowSQL(true)
	// if Debug {
	// 	engine.ShowSQL()
	// 	engine.ShowExecTime()
	// }

	//空闲关闭时间
	//engine.SetConnMaxLifetime(60 * time.Second)
	////最大空闲连接
	//engine.SetMaxIdleConns(10)
	////最大连接数
	//engine.SetMaxOpenConns(20)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

// 查询北京宠物数据信息
func NewBeijingPetEngine() *xorm.Engine {

	mySqlStr := config.GetString("mysql.scrm_organization_db_pet")
	//mySqlStr = "read_only:AAAsklapdadf@(172.28.24.210:13308)/scrm_third_db?charset=utf8"

	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	//engine.ShowSQL(true)
	// if Debug {
	// 	engine.ShowSQL()
	// 	engine.ShowExecTime()
	// }

	//空闲关闭时间
	//engine.SetConnMaxLifetime(60 * time.Second)
	////最大空闲连接
	//engine.SetMaxIdleConns(10)
	////最大连接数
	//engine.SetMaxOpenConns(20)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}
