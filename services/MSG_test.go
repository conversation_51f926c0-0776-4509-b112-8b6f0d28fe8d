package services

import "testing"

func TestNewFeedBackPush(t *testing.T) {
	type args struct {
		pushType   int32
		feedBackId int32
		commentId  int32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestNewFeedBackPush",
			args: args{
				pushType:   1,
				feedBackId: 49,
				commentId:  51,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			/*if err := NewFeedBackPush(tt.args.pushType, tt.args.feedBackId, tt.args.commentId); (err != nil) != tt.wantErr {
				t.Errorf("NewFeedBackPush() error = %v, wantErr %v", err, tt.wantErr)
			}*/
		})
	}
}
