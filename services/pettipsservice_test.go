package services

import (
	"_/proto/cc"
	"context"
	"testing"
)

func TestPetTipsService_GetTipsList(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.GetTipsListRequest
	}
	tests := []struct {
		name    string
		service *PetTipsService
		args    args
		want    *cc.GetTipsListResponse
		wantErr bool
	}{
		{
			args: args{
				req: &cc.GetTipsListRequest{
					TipId:     122,
					PageIndex: 1,
					PageSize:  10,
				},
			},
			want:    &cc.GetTipsListResponse{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &PetTipsService{}
			got, err := service.GetTipsList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.E<PERSON>rf("PetTipsService.GetTipsList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Code != tt.want.Code {
				t.Errorf("PetTipsService.GetTipsList() = %v, want %v", got, tt.want)
			} else {
				t.Log(got)
			}
		})
	}
}

func TestPetTipsService_QueryBySpecies(t *testing.T) {
	type args struct {
		ctx context.Context
		req *cc.QueryBySpeciesRequest
	}
	tests := []struct {
		name    string
		service *PetTipsService
		args    args
		want    *cc.PetTipsQueryResponse
		wantErr bool
	}{
		{
			args: args{
				req: &cc.QueryBySpeciesRequest{
					Species:   "猫",
					PageIndex: 1,
					PageSize:  10,
				},
			},
			want:    &cc.PetTipsQueryResponse{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &PetTipsService{}
			got, err := service.QueryBySpecies(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PetTipsService.QueryBySpecies() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Code != tt.want.Code {
				t.Errorf("PetTipsService.QueryBySpecies() = %v, want %v", got, tt.want)
			} else {
				t.Log(got)
			}
		})
	}
}
