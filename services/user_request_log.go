package services

import (
	"_/models"
	"_/proto/cc"
	"_/proto/common"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"regexp"
	"runtime"
	"strings"
	"time"

	"github.com/Shopify/sarama"
	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
)

var (
	regexSku        map[string]*regexp.Regexp //提到sku的正则
	regexTip        map[string]*regexp.Regexp //提取贴士的正则
	producerUserLog sarama.AsyncProducer
	regexNumber     = regexp.MustCompile(`\d+`)
	kafkaAddress    string
	kafkaAddressArr []string
)

func init() {
	//kafkaAddress = config.GetString("kafka.address")
	//kafkaAddressArr = strings.Split(kafkaAddress, ",")

	regexSku = make(map[string]*regexp.Regexp)

	//----曝光的商品
	//首页个性化推荐
	regexSku["/personalized-api/product/recommend-product"] = regexp.MustCompile(`"sku_id":\d+`)
	//首页搜素
	regexSku["/product-api/index/search"] = regexp.MustCompile(`"sku_id":\d+`)
	//店铺首页搜索
	regexSku["/product-api/product/shop/search"] = regexp.MustCompile(`"sku_id":\d+`)
	//店铺商品搜索
	regexSku["/product-api/product/search"] = regexp.MustCompile(`"sku_id":\d+`)
	//到家商品列表页
	regexSku["/product-api/category/list"] = regexp.MustCompile(`"sku_id":\d+`)
	//到家商品详情页
	regexSku["/product-api/product/detail"] = regexp.MustCompile(`"sku_id":\d+`)
	//周期购商品列表
	regexSku["/product-api/product/cycle-buy/list"] = regexp.MustCompile(`"sku_id":\d+`)
	//预售商品列表
	regexSku["/product-api/product/book-buy/list"] = regexp.MustCompile(`"skuId":\d+`)
	//秒杀商品列表
	regexSku["/product-api/product/seckill/product/list"] = regexp.MustCompile(`"sku_id":\d+`)

	//电商商品详情页
	regexSku["/index.php?act=goods&op=goods_detail"] = regexp.MustCompile(`"goods_id":\d+`)
	//电商商品列表
	regexSku["/index.php?act=goods&op=getGoodsList"] = regexp.MustCompile(`"goods_id":"\d+"`)
	//电商购物车
	regexSku["/index.php?act=member_cart&op=cart_list"] = regexp.MustCompile(`"goods_id":"\d+"`)
	//电商下单第一步
	regexSku["/mobile/index.php?act=member_buy&op=buy_step1"] = regexp.MustCompile(`"goods_id":"\d+"`)

	//----曝光的贴士
	regexTip = make(map[string]*regexp.Regexp)
	regexTip["/personalized-api/tips/recommend-tips"] = regexp.MustCompile(`"pet_tips_id":\d+`)
	regexTip["/product-api/tips/get"] = regexp.MustCompile(`"id":\d+`)

	//var err error
	//producerUserLog, err = sarama.NewAsyncProducer(kafkaAddressArr, nil)
	//if err != nil {
	//	panic(err)
	//}
	//
	//go consumerApiPageData()
}

func (service *CustomerCenterService) UploadUserRequestLog(ctx context.Context, in *cc.UserRequestLogRequest) (*cc.UserRequestLogResonse, error) {
	out := new(cc.UserRequestLogResonse)
	out.Code = 200

	var s userLog
	s.Init()
	s.redis = common.GetAnalysisRedisConn()
	defer s.redis.Close()

	if in.To == "globalrequest" {
		s.cacheGlobalRequestInfo(in.Msg)
	} else if in.To == "pagerequest" {
		s.pageUserLog(in.Msg)
	}
	return out, nil
}

type apiData struct {
	PageIndex          int
	Path               string
	Query              url.Values
	RequestBody        string
	ResponseBody       string
	Header             map[string]string
	HeaderStr          string
	DecodeRequestBody  string
	DecodeResponseBody string
}
type userLog struct {
	requestInit *models.RequestInit
	requestPage *models.RequestPage
	apiData     *apiData
	apis        []*models.RequestApi
	datas       []*models.RequestApiData
	db          *xorm.Engine
	redis       *redis.Client
}

func (s *userLog) Init() {
	s.db = AnalysisEngine
	if s.db == nil {
		s.db = AnalysisNewEngine()
	}
}

// 缓存全局请求上报的信息
func (s *userLog) cacheGlobalRequestInfo(msg string) {
	var m models.RequestInit
	// println(msg)
	if err := json.Unmarshal([]byte(msg), &m); err != nil {
		glog.Error(err)
		return
	}
	k := globalRequestIdKey(m.GlobalRequestId)
	has := s.redis.Get(k)
	if has.Val() != "" {
		return
	}
	mJson, err := json.Marshal(m)
	if err != nil {
		glog.Error(err)
		return
	}
	s.redis.Set(k, string(mJson), time.Hour*24)
	//保存入库
	if _, err := s.db.InsertOne(&m); err != nil {
		glog.Error(err)
	}
}

// 获取全局请求ID的缓存key
func globalRequestIdKey(id string) string {
	return fmt.Sprintf("app:userlog:global-request:%s", id)
}

// 从redis中加载缓存的全局请求信息数据
func (s *userLog) loadCacheRequestInit(id string) {
	k := globalRequestIdKey(id)
	cmd := s.redis.Get(k)
	s.requestInit = &models.RequestInit{}
	if cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), s.requestInit); err != nil {
			glog.Error(err)
		}
	} else {
		// redis没有，尝试从数据库读取，如果都没有，那就真没有了
		if _, err := s.db.Table("request_init").Where("global_request_id=?", id).Get(s.requestInit); err != nil {
			glog.Error(err)
		}
	}

	if s.requestInit == nil || s.requestInit.GlobalRequestId == "" {
		s.requestInit = nil
	}
}

func (s *userLog) pageUserLog(msg string) {
	var newP models.RequestPageNew
	if err := json.Unmarshal([]byte(msg), &newP); err != nil {
		glog.Error(err)
		return
	}
	p := models.RequestPage{
		Id:              newP.Id,
		GlobalRequestId: newP.GlobalRequestId,
		PageRequestId:   newP.PageRequestId,
		Path:            newP.Path,
		Args:            newP.Args,
		BeforePath:      newP.BeforePath,
		RequestTime:     newP.RequestTime,
		Type:            newP.Type,
		EventName:       newP.EventName,
	}
	pExtra, _ := json.Marshal(newP.Extra)
	p.Extra = string(pExtra)
	if p.GlobalRequestId == "" || p.PageRequestId == "" {
		return
	}

	s.requestPage = &p

	s.loadCacheRequestInit(p.GlobalRequestId)
	if s.requestInit == nil {
		return
	}
	// go s.saveRequestPage()
	go s.cacheRequestPage()
	// go s.savePageInfo()
	go s.saveAndPush()
}

func (s *userLog) saveRequestPage() {
	if _, err := s.db.Table("request_page").InsertOne(s.requestPage); err != nil {
		glog.Error(err)
	}
}

func (s *userLog) cacheRequestPage() {
	//缓存前端上传的页面信息，页面接口涉及分页，在接口分页大于等于第二页的时候用
	requestPageJson, err := json.Marshal(s.requestPage)
	if err != nil {
		glog.Error(err)
		return
	}
	s.redis = common.GetAnalysisRedisConn()
	defer s.redis.Close()
	s.redis.Set(fmt.Sprintf("app:userlog:pagedata:%s:%s", s.requestInit.UserId, s.requestPage.PageRequestId), requestPageJson, time.Minute*10)
}

// 保存页面的信息，每个页面只保存一份
func (s *userLog) savePageInfo() {
	m := &models.PageInfo{
		Path:      s.requestPage.Path,
		Type:      s.requestPage.Type,
		ChannelId: s.requestInit.ChannelId,
		UserAgent: s.requestInit.UserAgent,
	}

	if has, err := s.db.Table("page_info").Where("path=? and type=? and channel_id=? and user_agent=?", m.Path, m.Type, m.ChannelId, m.UserAgent).Exist(); err != nil {
		glog.Error(err)
	} else if !has {
		if _, err := s.db.Table("page_info").InsertOne(m); err != nil {
			glog.Error(err)
		}
	}
}

func (s *userLog) unmarshalApiData(v string) {
	s.apiData = &apiData{}
	if err := json.Unmarshal([]byte(v), &s.apiData); err != nil {
		glog.Info(err, v)
		return
	}
	req, err := base64.StdEncoding.DecodeString(s.apiData.RequestBody)
	if err != nil {
		glog.Infof("user_request_log_unmarshalApiData-RequestBody-base64-decode-failed:%v", err)
	}
	res, err := base64.StdEncoding.DecodeString(s.apiData.ResponseBody)
	if err != nil {
		glog.Infof("user_request_log_unmarshalApiData-ResponseBody-base64-decode-failed:%v", err)
	}

	hea, _ := json.Marshal(s.apiData.Header)

	s.apiData.HeaderStr = string(hea)
	s.apiData.DecodeRequestBody = string(req)
	s.apiData.DecodeResponseBody = string(res)
}

//从api data中查找字段信息（sku,tips,keywords）
func (s *userLog) findApiDataInfo(message *models.UserLogKfk) {
	find := func(reg *regexp.Regexp) []string {
		findResult := reg.FindAllString(s.apiData.DecodeResponseBody, -1)
		return regexNumber.FindAllString(strings.Join(findResult, ","), -1)
	}

	//从接口ResponseBody中获取曝光的sku数据
	if reg, has := regexSku[s.apiData.Path]; has {
		message.Sku = append(message.Sku, find(reg)...)
	}

	//从接口ResponseBody中获取曝光的贴士数据
	if reg, has := regexTip[s.apiData.Path]; has {
		message.Tips = append(message.Tips, find(reg)...)
	}

	//获取搜索词
	if s.apiData.Query.Get("key_words") != "" {
		message.KeyWords = s.apiData.Query.Get("key_words")
	}
	if s.apiData.Query.Get("keywords") != "" {
		message.KeyWords = s.apiData.Query.Get("keywords")
	}
}

func (s *userLog) saveApisDatas() {
	if _, err := s.db.Table("request_api").Insert(s.apis); err != nil {
		glog.Error(err)
	}
	if _, err := s.db.Table("request_api_data").Insert(s.datas); err != nil {
		glog.Error(err)
	}
}

func (s *userLog) appendToApisDatas() {
	s.apis = append(s.apis, &models.RequestApi{
		GlobalRequestId: s.requestInit.GlobalRequestId,
		PageRequestId:   s.requestPage.PageRequestId,
		Path:            s.apiData.Path,
		RawQuery:        s.apiData.Query.Encode(),
		RequestTime:     s.requestPage.RequestTime,
	})
	s.datas = append(s.datas, &models.RequestApiData{
		PageRequestId: s.requestPage.PageRequestId,
		Header:        s.apiData.HeaderStr,
		RequestBody:   s.apiData.DecodeRequestBody,
		ResponseBody:  s.apiData.DecodeResponseBody,
	})
}

func (s *userLog) getUserLogKfk() *models.UserLogKfk {
	return &models.UserLogKfk{
		UserId:      s.requestInit.UserId,
		Imei:        s.requestInit.Imei,
		ChannelId:   s.requestInit.ChannelId,
		UserAgent:   s.requestInit.UserAgent,
		City:        s.requestInit.City,
		LonLat:      s.requestInit.LonLat,
		Os:          s.requestInit.Os,
		Device:      s.requestInit.Device,
		Path:        s.requestPage.Path,
		Args:        s.requestPage.Args,
		BeforePath:  s.requestPage.BeforePath,
		Type:        s.requestPage.Type,
		PageIndex:   1,
		RequestTime: s.requestPage.RequestTime,
	}
}

// 保存接口数据+推送到kafka给北京部门消费
func (s *userLog) saveAndPush() {
	message := s.getUserLogKfk()

	k := fmt.Sprintf("app:userlog:apidata:%s:%s:%s", s.requestInit.UserId, s.requestInit.GlobalRequestId, s.requestPage.PageRequestId)
	s.redis = common.GetAnalysisRedisConn()
	defer s.redis.Close()
	ha := s.redis.HGetAll(k)
	result, _ := ha.Result()
	if len(result) > 0 {
		for _, v := range result {
			s.unmarshalApiData(v)
			s.findApiDataInfo(message)
			// s.appendToApisDatas()
		}

		pushToKafka(message)
		// s.saveApisDatas()
		s.redis.Del(k) //删除redis中缓存
	}

}

func pushToKafka(log *models.UserLogKfk) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("pushToKafka异常信息捕获：", err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER PWERROR] %v %s\n", err, stack[:length])

		}
	}()
	bt, _ := json.Marshal(log)
	if producerUserLog != nil {
		producerUserLog.Input() <- &sarama.ProducerMessage{
			Topic: "app-userlog-tobj",
			Key:   nil,
			Value: sarama.ByteEncoder(bt),
		}
	}

}

// 消费kafka 接口数据（第二页及以上的数据）
//func consumerApiPageData() {
//	consumer, err := sarama.NewConsumer(kafkaAddressArr, nil)
//	if err != nil {
//		panic(err)
//	}
//
//	defer func() {
//		if err := consumer.Close(); err != nil {
//			glog.Error(err)
//		}
//	}()
//	partitionConsumer, err := consumer.ConsumePartition("app-userlog-apipagedata", 0, sarama.OffsetNewest)
//	if err != nil {
//		panic(err)
//	}
//
//	defer func() {
//		if err := partitionConsumer.Close(); err != nil {
//			glog.Error(err)
//		}
//	}()
//
//	signals := make(chan os.Signal, 1)
//	signal.Notify(signals, os.Interrupt)
//
//ConsumerLoop:
//	for {
//		select {
//		case msg := <-partitionConsumer.Messages():
//			glog.Infof("收到kafka消息: %v", string(msg.Value))
//			go func(b []byte) {
//				var s userLog
//				s.Init()
//				s.redis = common.GetAnalysisRedisConn()
//				defer s.redis.Close()
//				s.unmarshalApiData(string(b))
//
//				//kafka能订阅到的数据，都是接口的分页大于1的数据，全局的数据与页面的数据从缓存获取
//				v := s.redis.Get(fmt.Sprintf("app:userlog:pagedata:%s:%s", s.apiData.Header["UserId"], s.apiData.Header["PageRequestId"])).Val()
//				if v != "" {
//					newReqPage := &models.RequestPageNew{}
//					if err := json.Unmarshal([]byte(v), newReqPage); err != nil {
//						glog.Info(err)
//					}
//					s.requestPage = &models.RequestPage{
//						Id:              newReqPage.Id,
//						GlobalRequestId: newReqPage.GlobalRequestId,
//						PageRequestId:   newReqPage.PageRequestId,
//						Path:            newReqPage.Path,
//						Args:            newReqPage.Args,
//						BeforePath:      newReqPage.BeforePath,
//						RequestTime:     newReqPage.RequestTime,
//						Type:            newReqPage.Type,
//						EventName:       newReqPage.EventName,
//					}
//					newReqPageExtra, _ := json.Marshal(newReqPage.Extra)
//					s.requestPage.Extra = string(newReqPageExtra)
//
//					s.loadCacheRequestInit(s.apiData.Header["GlobalRequestId"])
//					message := s.getUserLogKfk()
//					message.PageIndex = s.apiData.PageIndex
//					s.findApiDataInfo(message)
//					pushToKafka(message)
//					// s.appendToApisDatas()
//					// s.saveApisDatas()
//				}
//			}(msg.Value)
//		case <-signals:
//			break ConsumerLoop
//		}
//	}
//}
