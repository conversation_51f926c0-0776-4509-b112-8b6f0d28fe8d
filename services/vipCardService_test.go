package services

import (
	"_/proto/cc"
	"context"
	"reflect"
	"testing"

	kit "github.com/tricobbler/rp-kit"
)

func TestVipCardService_GetVipCardTemplateDetail(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.BaseIdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.VipCardTemplateDetailResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestVipCardService_GetVipCardTemplateDetail",
			args: args{
				ctx: context.Background(),
				in: &cc.BaseIdRequest{
					Id: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}

			gotOut, err := v.GetVipCardTemplateDetail(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.<PERSON>rf("GetVipCardTemplateDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_AddVipCardTemplate(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.VipCardTemplateAddRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "AddVipCardTemplate",
			args: args{
				ctx: context.Background(),
				in: &cc.VipCardTemplateAddRequest{
					CardName:        "月卡",
					CardType:        1,
					CardCycle:       3,
					DurationDate:    30,
					MemberDiscPrice: 12.123,
					MemberPrice:     22.889,
					Type:            1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.AddVipCardTemplate(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("AddVipCardTemplate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_UpdateVipCardTemplate(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.VipCardTemplateUpdateRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "UpdateVipCardTemplate",
			args: args{
				ctx: context.Background(),
				in: &cc.VipCardTemplateUpdateRequest{
					Id:              1,
					MemberDiscPrice: 299,
					MemberPrice:     288,
					DisRate:         0.01,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.UpdateVipCardTemplate(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateVipCardTemplate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_ListVipEquity(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.ListPageRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.ListVipEquityResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ListVipEquity",
			args: args{
				ctx: context.Background(),
				in: &cc.ListPageRequest{
					PageSize:  10,
					PageIndex: 1,
					OrId:      -1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.ListVipEquity(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("ListVipEquity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_CreateOrUpdateVipEquity(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.VipEquity
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "CreateOrUpdateVipEquity",
			args: args{
				ctx: context.Background(),
				in: &cc.VipEquity{
					Id:          6,
					EquityIcon:  "http://www.baidu.com",
					EquityName:  "会员专享价2",
					EquityCopy:  "test",
					EquityPrice: 200,
					EquityType:  3,
					MatchType:   123,
					Status:      1,
					OrIds:       "2,3,1",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.CreateOrUpdateVipEquity(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrUpdateVipEquity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_GetEquityConfig(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.GetEquityConfigRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.GetEquityConfigResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetEquityConfig",
			args: args{
				ctx: context.Background(),
				in: &cc.GetEquityConfigRequest{
					CardTid: 6,
					OrId:    -1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetEquityConfig(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEquityConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_SaveEquityConfig(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.CreateEquityConfigRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &cc.CreateEquityConfigRequest{
					CardTid: 6,
					OrId:    -1,
					Data: []*cc.EquityConfig{
						{
							EquityId:     5,
							CardTid:      6,
							PrivilegeIds: "95d7844ba756f419bae10,125d7844ba757d61dr0400",
							Status:       1,
							EquityType:   2,
							Refundable:   0,
							FreeValue: []*cc.EquityConfigValue{
								{
									PrivilegeId: "95d7844ba756f419bae10",
									FreeQuality: 1111,
								},
								{
									PrivilegeId: "95d7844ba756f419bae10",
									FreeQuality: 2222,
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.SaveEquityConfig(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("SaveEquityConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_ListEquityConfigs(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.ListPageRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.ListEquityConfigsResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ListEquityConfigs",
			args: args{
				ctx: context.Background(),
				in: &cc.ListPageRequest{
					PageIndex: 1,
					PageSize:  1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.ListEquityConfigs(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("ListEquityConfigs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_GetVipCardTemplateList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.VipCardTemplateListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.VipCardTemplateListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestVipCardService_GetVipCardTemplateList",
			args: args{
				ctx: context.Background(),
				in: &cc.VipCardTemplateListRequest{
					Type:      1,
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetVipCardTemplateList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVipCardTemplateList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("GetVipCardTemplateList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestVipCardService_GetGiftList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.GiftListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.GetGiftListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &cc.GiftListRequest{
					//Id:        1,
					//PackName:  "test2",
					//State:      1,
					//IsMain:     -1,
					//PageSize:   10,
					//PageIndex:  1,
					StockState: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetGiftList(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGiftList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_GetGift(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.BaseIdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.GetGiftResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetGift",
			args: args{
				ctx: context.Background(),
				in: &cc.BaseIdRequest{
					Id: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetGift(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGift() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_SaveGift(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.GiftData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.VcBaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "SaveGift",
			args: args{
				ctx: context.Background(),
				in: &cc.GiftData{
					//Id:        1,
					PackName:  "test133",
					PackDesc:  "desc123",
					PackImage: "image",
					PackPrice: 11.05,
					//PackSkuId: 1033690001,
					PackSkuId: 1000099002, //
					IsMain:    1,
					State:     1,
					UserNo:    "Rp0001",
					UserName:  "zyw",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.SaveGift(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaveGift() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("SaveGift() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestVipCardService_DeleteGift(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.BaseIdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.VcBaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "DeleteGift",
			args: args{
				ctx: context.Background(),
				in: &cc.BaseIdRequest{
					Id: 4,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.DeleteGift(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteGift() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_UpDownGift(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.BaseIdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.VcBaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "UpDownGift",
			args: args{
				ctx: context.Background(),
				in: &cc.BaseIdRequest{
					Id: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.UpDownGift(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("UpDownGift() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_CheckVouchers(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.VoucherRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.DataBaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "CheckVouchers",
			args: args{
				ctx: context.Background(),
				in: &cc.VoucherRequest{
					//Vid: "345,346,347",
					//Type: 1,
					Vid:  "345,346,347",
					Type: 2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.CheckVouchers(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckVouchers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_GetCardInfo(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.BaseCardRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.GetCardInfoResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &cc.BaseCardRequest{
					Id:      1,
					UserId:  "6601ae33cb7b4393bfb98ac92810777b",
					OrderSn: "****************",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetCardInfo(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCardInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_GetWelfareList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.BaseCardOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.GetWelfareResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetWelfareList",
			args: args{
				ctx: context.Background(),
				in: &cc.BaseCardOrderRequest{
					OrderSn: "****************",
					//UserId:  "83aa9d65c097481b953db6a90e0a16f5", //xucp
					//UserId: "6601ae33cb7b4393bfb98ac92810777b", //lvhb
					UserId: "6601ae33cb7b4393bfb98ac92810777b", //zhangsi
					//UserId: "2abaad6b463f4d7fab9d4a565cab958a", //zhuoyw
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetWelfareList(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWelfareList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

		})
	}
}

func TestVipCardService_GetGiftInfo(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.BaseCardOrderRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.GetGiftInfoResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetGiftInfo",
			args: args{
				ctx: context.Background(),
				in: &cc.BaseCardOrderRequest{
					OrderSn: "****************",
					UserId:  "2aaff26b63be4718b6eec7d176281ab6",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetGiftInfo(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGiftInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test_getRandUserName(t *testing.T) {
	getRandUserName()
}

func TestVipCardService_GetUserInfo(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.GetUserInfoRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.GetUserInfoResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &cc.GetUserInfoRequest{
					Mobile: "15118811943",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.GetUserInfo(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestBuyNoticesList(t *testing.T) {
	BuyNoticesList()
}

func TestVipCardService_CardValue(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.CardValueReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.CardValueRes
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &cc.CardValueReq{
					ScrmId: "57ccfc6ac6e84eb5a3ad04cbce2ef13e",
					CardId: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.CardValue(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("CardValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVipCardService_ListNoRefundableEquityConfig(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.ListPageRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.ListNoRefundableEquityConfigsResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ListNoRefundableEquityConfig",
			args: args{
				ctx: context.Background(),
				in: &cc.ListPageRequest{
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.ListNoRefundableEquityConfig(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ListNoRefundableEquityConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("ListNoRefundableEquityConfig() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestVipCardService_CardOrderValue(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.CardOrderValueReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *cc.CardOrderValueResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "CardOrderValue",
			args: args{
				ctx: context.Background(),
				in: &cc.CardOrderValueReq{
					OrderSn: []string{"****************"},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := &VipCardService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := v.CardOrderValue(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CardOrderValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("CardOrderValue() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}
