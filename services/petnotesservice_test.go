package services

import (
	"_/proto/cc"
	"context"
	"reflect"
	"testing"
)

func TestPetNotesService_GetNotesByUserId(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *cc.GetNotesByUserIdRequest
	}

	var params cc.GetNotesByUserIdRequest
	params.UserId = "e9a3352b5ce14fa291d8177cc04adb66"
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *cc.GetNotesByUserIdResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "GetNotesByUserId", args: args{ctx: nil, in: &params}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PetNotesService{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.GetNotesByUserId(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNotesByUserId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetNotesByUserId() got = %v, want %v", got, tt.want)
			}
		})
	}
}
