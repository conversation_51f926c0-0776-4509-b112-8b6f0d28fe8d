package services

import (
	"_/models"
	"_/proto/cc"
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"testing"
	"time"
)

func Test_getCacheGlobalRequestInfo(t *testing.T) {

}

func TestApiData(t *testing.T) {
	d := `{"code":200,"message":"","error":"","details":[{"pic":"https://oss.upetmart.com/www/shop/store/goods/1/2021/1_06808101180524123.jpg","name":"【新瑞鹏全国】vip黄金会员plus（仅限犬用） 黄金会员plus","product_id":106314,"sku_id":113016,"selling_point":"","total_count":0,"tags":"","market_price":29800,"goods_promotion_type":0,"pet_tips_id":0,"goods_promotion_price":29800,"data_type":2,"channel_category_id":0,"member_price_1":0,"member_price_2":0,"member_price_3":0},{"pic":"https://oss.upetmart.com/www/shop/store/goods/1/2021/1_06679346417436638.jpg","name":"【新瑞鹏全国】vip黄金会员（仅限犬用） 【首次购买】请点此项——198黄金会员 黄金会员","product_id":102147,"sku_id":108389,"selling_point":"","total_count":0,"tags":"","market_price":19800,"goods_promotion_type":0,"pet_tips_id":0,"goods_promotion_price":19800,"data_type":2,"channel_category_id":0,"member_price_1":0,"member_price_2":0,"member_price_3":0},{"pic":"https://oss.upetmart.com/www/shop/store/goods/1/2021/1_06779498213986220.png","name":"爱哒哒 原味豆腐猫砂 2.5kg","product_id":1001430,"sku_id":1000082002,"selling_point":"","total_count":0,"tags":"","market_price":1800,"goods_promotion_type":0,"pet_tips_id":0,"goods_promotion_price":1800,"data_type":2,"channel_category_id":0,"member_price_1":1710,"member_price_2":1710,"member_price_3":1710},{"pic":"https://file.vetscloud.com/e1ac584d443f04c145153d4b4bae2bad.jpg","name":"【3袋】皇家幼猫肠道全价处方粮 GIK35/1KG 1kg*3袋","product_id":1042178,"sku_id":1042178001,"selling_point":"","total_count":0,"tags":"","market_price":45900,"goods_promotion_type":2,"pet_tips_id":0,"goods_promotion_price":43600,"data_type":2,"channel_category_id":0,"member_price_1":44100,"member_price_2":44100,"member_price_3":44100},{"pic":"https://oss.upetmart.com/www/shop/store/goods/1/2020/1_06426877311017546.jpg","name":"皇家成猫全价配方粮FA33/2KG 2KG","product_id":102678,"sku_id":105296,"selling_point":"","total_count":0,"tags":"","market_price":18100,"goods_promotion_type":2,"pet_tips_id":0,"goods_promotion_price":16300,"data_type":2,"channel_category_id":0,"member_price_1":17200,"member_price_2":17200,"member_price_3":17200},{"pic":"http://file.vetscloud.com/b8dc8c1031a47bc1db9ba9e6153546c0.jpg","name":"K9猫Feline Natural天然无谷猫罐鸡肉 85g","product_id":1003286,"sku_id":1003286001,"selling_point":"","total_count":0,"tags":"","market_price":2200,"goods_promotion_type":2,"pet_tips_id":0,"goods_promotion_price":2100,"data_type":2,"channel_category_id":0,"member_price_1":2090,"member_price_2":2090,"member_price_3":2090},{"pic":"https://oss.upetmart.com/www/shop/store/goods/1/2020/1_06610828217259501.jpg","name":"爱宠健康保障卡--硕腾联名款-欣宠克（狗狗1-7岁）","product_id":107034,"sku_id":119158,"selling_point":"","total_count":0,"tags":"","market_price":169900,"goods_promotion_type":0,"pet_tips_id":0,"goods_promotion_price":169900,"data_type":2,"channel_category_id":0,"member_price_1":0,"member_price_2":0,"member_price_3":0},{"pic":"https://oss.upetmart.com/www/shop/store/goods/1/2020/1_06420924434337375.jpg","name":"麦富迪狗狗零食 牛肉火腿肠300g","product_id":103961,"sku_id":106949,"selling_point":"","total_count":0,"tags":"","market_price":1800,"goods_promotion_type":2,"pet_tips_id":0,"goods_promotion_price":1600,"data_type":2,"channel_category_id":0,"member_price_1":1700,"member_price_2":1700,"member_price_3":1700},{"pic":"http://file.vetscloud.com/786a054a1e96826c869c6095267a574d.png","name":"多格漫绑带背心-长颈鹿 迷你XS 1个","product_id":1006890,"sku_id":1006890001,"selling_point":"","total_count":0,"tags":"","market_price":7600,"goods_promotion_type":2,"pet_tips_id":0,"goods_promotion_price":6800,"data_type":2,"channel_category_id":0,"member_price_1":7200,"member_price_2":7200,"member_price_3":7200},{"pic":"https://file.vetscloud.com/d96c18cfbd0393eb85e76f92c586f551.jpg","name":"雪貂留香猫多爱系列柔顺丝滑香波 500ml","product_id":1040471,"sku_id":1040471001,"selling_point":"","total_count":0,"tags":"","market_price":3990,"goods_promotion_type":2,"pet_tips_id":0,"goods_promotion_price":3600,"data_type":2,"channel_category_id":0,"member_price_1":3790,"member_price_2":3790,"member_price_3":3790}]}`
	api := "/personalized-api/product/recommend-product"
	fmt.Println(findApiData(api, d))
}

func findApiData(api, data string) string {
	return strings.Join(regexp.MustCompile(`\d+`).FindAllString(strings.Join(regexSku[api].FindAllString(data, -1), ","), -1), ",")
}

func TestJson(t *testing.T) {
	// var m models.UserLogKfk
	// var m models.RequestPage
	var m models.RequestInit
	b, _ := json.Marshal(&m)
	fmt.Println(string(b))
}

func TestCustomerCenterService_UploadUserRequestLog(t *testing.T) {
	if Engine == nil {
		Engine = NewEngine()
	}

	Engine.Exec("TRUNCATE TABLE request_api;")
	Engine.Exec("TRUNCATE TABLE page_info;")
	Engine.Exec("TRUNCATE TABLE request_api_data;")
	Engine.Exec("TRUNCATE TABLE request_init;")
	Engine.Exec("TRUNCATE TABLE request_page;")

	type args struct {
		ctx context.Context
		in  *cc.UserRequestLogRequest
	}
	tests := []struct {
		name    string
		service *CustomerCenterService
		args    args
		want    *cc.UserRequestLogResonse
		wantErr bool
	}{
		{
			name: "埋点上报公共信息数据",
			args: args{
				ctx: context.Background(),
				in: &cc.UserRequestLogRequest{
					To:  "globalrequest",
					Msg: `{"GlobalRequestId": "18cfd86e-3b35-434f-aaa0-7bc164daa518", "UserId": "97ba7d77-f4eb-4fc5-a959-83d45750e0f8", "Imei": "1234567890", "ChannelId":1, "UserAgent":3, "City": "深圳市", "LonLat": "0.00，0.00", "Os": "Android 8.0", "Device": "小米 MIX 4", "RequestTime": "2021-10-25 00:00:00" }`,
				},
			},
		},
		{
			name: "埋点上报页面信息",
			args: args{
				in: &cc.UserRequestLogRequest{
					To:  "pagerequest",
					Msg: `{"GlobalRequestId": "18cfd86e-3b35-434f-aaa0-7bc164daa518", "PageRequestId": "17cedfc7-215b-44c5-981c-8fd2dc5085b2", "Path": "app/page/index", "Args": "a=123", "BeforePath": "", "RequestTime": "2021-10-25 00:00:00", "Type": 1 }`,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := tt.service.UploadUserRequestLog(tt.args.ctx, tt.args.in)
			if err != nil {
				t.Error(err)
			}
		})
	}

	time.Sleep(time.Second * 10)
}

func TestUnmarshal(t *testing.T) {
	type apiData2 struct {
		PageIndex          int
		Path               string
		Query              url.Values
		RequestBody        string
		ResponseBody       string
		Header             map[string]string
		HeaderStr          string
		DecodeRequestBody  string
		DecodeResponseBody string
	}
	str := `{"PageIndex":2,"Path":"/personalized-api/product/recommend-product","Query":{"adcode":["440304"],"finance_code":["RP0228"],"keywords":["aaa"],"page_index":["2"],"page_size":["10"],"pet_id":["362037a75d3c4157a565f94c9fe9e323"],"pet_type":["猫"],"user_id":["3bd32183118f4769bca6c44bc71127a6"]},"RequestBody":"","ResponseBody":"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","Header":{"GlobalRequestId":"18cfd86e-3b35-434f-aaa0-7bc164daa518","PageRequestId":"17cedfc7-215b-44c5-981c-8fd2dc5085b2","UserId":"97ba7d77-f4eb-4fc5-a959-83d45750e0f8"}}`
	var d apiData2
	if err := json.Unmarshal([]byte(str), &d); err != nil {
		t.Error(err)
	} else {
		t.Log(len(d.Header))
	}
}
