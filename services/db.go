package services

import (
	"_/models"
	"fmt"
)

//获取用户基本信息
func GetScrmUserInfo(scrmUserId string) (*models.ScrmUserInfo, error) {
	response := new(models.ScrmUserInfo)
	if scrmUserId == "" {
		return response, nil
	}
	db := NewEngine()
	sql := fmt.Sprintf("SELECT user_name,user_mobile FROM scrm_organization_db.t_scrm_user_info WHERE user_id = %q", scrmUserId)
	if has, err := db.SQL(sql).Get(response); err != nil {
		return response, err
	} else if !has {
		return response, nil
	}
	return response, nil
}

func GetUserIntegral(scrmUserId string, orgId int32) (*models.MemberIntegralInfo, error) {

	response := new(models.MemberIntegralInfo)
	if scrmUserId == "" {
		return response, nil
	}
	sql := fmt.Sprintf("select memberid,integral,lasttime from datacenter.member_integral_info where memberid= %q and org_id=?", scrmUserId)
	if has, err := DataCenterEngine.SQL(sql, orgId).Get(response); err != nil {
		return response, err
	} else if !has {
		return response, nil
	}
	return response, nil
}

//获取用户基本信息
func GetUserInfo(memberId, mobile string) (*models.TScrmUserInfo, error) {
	response := new(models.TScrmUserInfo)

	db := DataCenterEngine.Table("scrm_organization_db.t_scrm_user_info")
	if len(memberId) > 0 {
		db.And("user_id = ?", memberId)
	}
	if len(mobile) > 0 {
		db.And("user_mobile = ?", mobile)
	}
	if has, err := db.Get(response); err != nil {
		return response, err
	} else if !has {
		return response, nil
	}
	return response, nil
}

//获取用户基本信息
func GetUserVipInfo(memberId string) ([]*models.MemberCardRelation, error) {
	var response []*models.MemberCardRelation

	if err := DataCenterEngine.Table("member_card_relation").Where("userid = ?", memberId).Find(&response); err != nil {
		return response, err
	}
	return response, nil
}
