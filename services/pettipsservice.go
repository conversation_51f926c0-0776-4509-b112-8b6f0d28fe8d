package services

import (
	"_/models"
	"_/proto/cc"
	"_/utils"
	"context"
	"strings"

	"github.com/spf13/cast"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
)

type PetTipsService struct {
}

func (service *PetTipsService) getDbEngine() *xorm.Engine {
	return NewEngine()
}

// 开始执行SQL查询逻辑
func (service *PetTipsService) beginQuery(queryFunc func(*xorm.Engine) error) error {

	err := queryFunc(service.getDbEngine())
	if err != nil {
		glog.Error(err)
	}
	return err
}

// 开始事务处理逻辑
func (service *PetTipsService) beginTran(callFunc func(*xorm.Session) error) error {

	// 开启事物
	session := service.getDbEngine().NewSession()
	defer session.Close()
	session.Begin()

	// 调用处理逻辑
	err := callFunc(session)

	// 如果没有逻辑错误
	if err == nil {
		// 提交保存
		session.Commit()
	} else {
		glog.Error(err)
		// 回滚
		session.Rollback()
	}

	return err
}

//分页获取贴士信息(带上标签数据)
func (service *PetTipsService) QueryTipWithTags(ctx context.Context, req *cc.QueryTipWithTagsRequest) (*cc.PetTipsQueryResponse, error) {
	var response = &cc.PetTipsQueryResponse{Code: 200}

	err := service.beginQuery(func(engine *xorm.Engine) error {
		//获取贴士信息
		session := Engine.Table("pet_tips").Select("`id`, `title`, `icon`, `reading`, `is_deleted`, `create_at`, `create_by`, `update_at`, `update_by`")
		session.Where("pet_tips.`is_deleted` = ?", 0)
		if err := session.Limit(int(req.PageSize), int((req.PageIndex-1)*req.PageSize)).Find(&response.Data); err != nil {
			glog.Error(err)
			return err
		} else {
			//获取贴士id
			var PetTipsId []int32
			for _, v := range response.Data {
				PetTipsId = append(PetTipsId, v.Id)
			}
			//获取贴士标签
			var petTipsTag []models.PetTipsTag
			if err := session.Table("pet_tips_tag").In("pet_tips_id", PetTipsId).Find(&petTipsTag); err != nil {
				glog.Error(err)
				return err
			} else {
				for _, petTipsValue := range response.Data {
					for _, petTipsTagVales := range petTipsTag {
						if petTipsValue.Id == int32(petTipsTagVales.PetTipsId) {
							var tag cc.PetTipsTagDto
							tag.Name = petTipsTagVales.TagName
							tag.Value = petTipsTagVales.TagValue
							petTipsValue.Tags = append(petTipsValue.Tags, &tag)
						}
					}
				}
			}
		}
		return nil
	})
	return response, err
}

//通过贴士id([]int32)获取贴士信息
func (service *PetTipsService) QueryByIds(ctx context.Context, req *cc.QueryByIdRequest) (*cc.PetTipsQueryResponse, error) {
	var response = &cc.PetTipsQueryResponse{Code: 200}

	err := service.beginQuery(func(engine *xorm.Engine) error {
		//获取贴士信息
		session := Engine.Table("pet_tips").Select("`id`, `title`, `icon`, `reading`, `is_deleted`, `create_at`, `create_by`, `update_at`, `update_by`")
		session.Where("pet_tips.`is_deleted` = ?", 0)
		session.In("pet_tips.id", req.PetTipsId)
		if err := session.Find(&response.Data); err != nil {
			glog.Error(err)
			return err
		} else {
			//获取贴士标签
			var petTipsTag []models.PetTipsTag
			if err := session.Table("pet_tips_tag").In("pet_tips_id", req.PetTipsId).Find(&petTipsTag); err != nil {
				glog.Error(err)
				return err
			} else {
				for _, petTipsValue := range response.Data {
					for _, petTipsTagVales := range petTipsTag {
						if petTipsValue.Id == int32(petTipsTagVales.PetTipsId) {
							var tag cc.PetTipsTagDto
							tag.Name = petTipsTagVales.TagName
							tag.Value = petTipsTagVales.TagValue
							petTipsValue.Tags = append(petTipsValue.Tags, &tag)
						}
					}
				}
			}
		}
		return nil
	})
	return response, err
}

func (service *PetTipsService) Add(ctx context.Context, req *cc.PetTipsEditRequest) (*cc.PetTipsEditResponse, error) {
	var response = &cc.PetTipsEditResponse{Code: 200}
	// 开始事务
	err := service.beginTran(func(session *xorm.Session) error {
		// 贴士
		var petTips = new(models.PetTips)
		petTips.Title = req.Title
		petTips.CreateBy = req.UserNo
		petTips.Icon = req.Icon
		petTips.VirtualReading = int(utils.RandInt64(8000, 20000))
		_, err := session.Insert(petTips)
		if err == nil {
			// 贴士详情
			var petTipsContent = new(models.PetTipsContent)
			petTipsContent.PetTipsId = petTips.Id
			petTipsContent.Content = req.Content
			_, err = session.Insert(petTipsContent)
			if err == nil {
				// 贴士标签
				for _, tag := range req.Tags {
					var petTipsTag = new(models.PetTipsTag)
					petTipsTag.PetTipsId = petTips.Id
					petTipsTag.TagName = tag.Name
					petTipsTag.TagValue = tag.Value
					_, err = session.Insert(petTipsTag)
					if err != nil {
						break
					}
				}
			}
		}

		return err
	})
	return response, err
}

// 修改
func (service *PetTipsService) Update(ctx context.Context, req *cc.PetTipsEditRequest) (*cc.PetTipsEditResponse, error) {
	var response = &cc.PetTipsEditResponse{Code: 200}
	err := service.beginTran(func(session *xorm.Session) error {
		// 更新主记录
		var petTips models.PetTips
		petTips.Title = req.Title
		petTips.Icon = req.Icon
		petTips.UpdateBy = req.UserNo
		isUpdated, err := session.ID(req.Id).Cols("title,icon,update_by").Update(&petTips)
		if isUpdated == 0 {
			response.Code = 400
			response.Message = "更新记录不存在"
			return nil
		}

		// 明细
		if err == nil {
			// 内容
			var petTipsContent models.PetTipsContent
			petTipsContent.Content = req.Content
			_, err = session.Where("pet_tips_id=?", req.Id).Cols("content").Update(&petTipsContent)

		}

		// 标签
		if err == nil {
			//删除已有的
			_, err = session.Where("pet_tips_id=?", req.Id).Delete(&models.PetTipsTag{})

			// 贴士标签
			for _, tag := range req.Tags {
				var petTipsTag = new(models.PetTipsTag)
				petTipsTag.PetTipsId = int(req.Id)
				petTipsTag.TagName = tag.Name
				petTipsTag.TagValue = tag.Value
				_, err = session.Insert(petTipsTag)
				if err != nil {
					break
				}
			}
		}

		//是否有异常
		if err != nil {
			response.Code = 500
			response.Message = err.Error()
		}

		return nil
	})
	return response, err
}

// 修改阅读量
func (service *PetTipsService) UpdateReading(ctx context.Context, req *cc.PetTipsEditRequest) (*cc.PetTipsEditResponse, error) {
	var response = &cc.PetTipsEditResponse{Code: 200}

	err := service.beginTran(func(session *xorm.Session) error {
		var model models.PetTips
		isSuccess, err := session.ID(req.Id).Get(&model)

		if !isSuccess {
			response.Code = 400
			response.Message = "信息不存在"
			return nil
		}

		// 更新阅读量
		if err == nil {
			model.Reading = model.Reading + 1
			_, err = session.ID(req.Id).Cols("reading").Update(&model)
		}
		// 保存阅读历史记录
		if err == nil {
			var readingModel = new(models.PetTipsReading)
			readingModel.PetTipsId = model.Id
			readingModel.ReadingBy = req.UserNo
			_, err = session.Insert(readingModel)
		}

		if err != nil {
			response.Code = 400
			response.Message = err.Error()
		}
		return err
	})
	return response, err
}

// 删除
func (service *PetTipsService) Delete(ctx context.Context, req *cc.PetTipsDeleteRequest) (*cc.PetTipsEditResponse, error) {
	var response = &cc.PetTipsEditResponse{Code: 200}
	err := service.beginTran(func(session *xorm.Session) error {
		if req.Id > 0 {
			_, err := session.ID(req.Id).Cols("is_deleted").Update(&models.PetTips{IsDeleted: 1})
			if err != nil {
				response.Code = 500
				response.Message = err.Error()
				return err
			}
		}
		if len(req.Ids) > 0 {
			var ids = strings.Split(req.Ids, ",")
			for _, id := range ids {
				_, err := session.ID(id).Cols("is_deleted").Update(&models.PetTips{IsDeleted: 1})
				if err != nil {
					response.Code = 500
					response.Message = err.Error()
					return err
				}
			}
		}
		return nil
	})
	return response, err
}

// 查询
func (service *PetTipsService) Query(ctx context.Context, req *cc.PetTipsQueryRequest) (*cc.PetTipsQueryResponse, error) {
	var response = &cc.PetTipsQueryResponse{Code: 200}

	err := service.beginQuery(func(engine *xorm.Engine) error {
		var dbModels []models.PetTips
		var petTipsQuery = engine.Where("is_deleted=0")
		if req.Id != 0 {
			petTipsQuery = petTipsQuery.Where("id=?", req.Id)
		}
		if len(req.SearchKey) > 0 {
			petTipsQuery = petTipsQuery.Where("title like ?", "%"+req.SearchKey+"%")
		}
		total, err := petTipsQuery.Asc("id").Limit(int(req.PageSize), int((req.PageIndex-1)*req.PageSize)).FindAndCount(&dbModels)
		if err != nil {
			response.Code = 500
			response.Message = err.Error()
		} else {
			response.Total = int32(total)
			for _, dbModel := range dbModels {
				var dto = dbModel.ToPetTipsDto()
				response.Data = append(response.Data, dto)
			}
		}
		return err
	})
	return response, err
}

// 随机查询数据
func (service *PetTipsService) QueryRand(ctx context.Context, req *cc.PetTipsRandQueryRequest) (*cc.PetTipsQueryResponse, error) {
	var response = &cc.PetTipsQueryResponse{Code: 200}
	err := service.beginQuery(func(engine *xorm.Engine) error {
		var dbModels []models.PetTips
		var petTipsQuery = engine.Where("is_deleted=0")
		err := petTipsQuery.Limit(int(req.Size), 0).OrderBy("RAND()").Find(&dbModels)

		if err != nil {
			response.Code = 500
			response.Message = err.Error()
		} else {
			response.Total = int32(len(dbModels))
			for _, dbModel := range dbModels {
				var dto = dbModel.ToPetTipsDto()
				response.Data = append(response.Data, dto)
			}
		}

		return err
	})
	return response, err
}

// 查询scrm宠物品种
func (service *PetTipsService) QueryScrmPetVariety(ctx context.Context, req *cc.ScrmPetVarietyRequest) (*cc.ScrmPetVarietyResponse, error) {
	var response = &cc.ScrmPetVarietyResponse{Code: 200}
	var userpetTagService = new(UserPetTagService)
	var data = userpetTagService.queryScrmPetVariety(req.Id)
	if data != nil && len(data.Result) > 0 {
		for _, item := range data.Result {
			var dto = &cc.ScrmPetVarietyDto{Id: item.PetDictID, Name: item.PetDictName}
			if item.PetDictParentID == "1000" {
				dto.Extend = "猫"
			} else if item.PetDictParentID == "1001" {
				dto.Extend = "犬"
			} else {
				dto.Extend = "不限"
			}
			response.Data = append(response.Data, dto)
		}
	}
	return response, nil
}

// 查询单个
func (service *PetTipsService) Get(ctx context.Context, req *cc.PetTipsGetRequest) (*cc.PetTipsGetResponse, error) {
	var response = &cc.PetTipsGetResponse{Code: 200}
	// 开始查询
	err := service.beginQuery(func(engine *xorm.Engine) error {
		var petTips models.PetTips
		isSuccess, err := engine.ID(req.Id).Get(&petTips)
		if !isSuccess {
			response.Code = 400
			response.Message = "未找到信息"
		}

		if err == nil {

			var petTipsDto = petTips.ToPetTipsDto()

			// 详细内容
			var petTipContent models.PetTipsContent
			engine.Where("pet_tips_id=?", petTips.Id).Get(&petTipContent)
			petTipsDto.Content = petTipContent.Content

			// 标签
			var petTipsTags []models.PetTipsTag
			engine.Where("pet_tips_id=?", petTips.Id).Find(&petTipsTags)
			for _, petTipsTag := range petTipsTags {
				var tagDto = petTipsTag.ToPetTipsTagDto()
				petTipsDto.Tags = append(petTipsDto.Tags, tagDto)
			}

			response.Data = petTipsDto
		} else {
			response.Code = 500
			response.Message = err.Error()
		}

		return err
	})

	return response, err
}

// 判断是否特殊状态
func (service *PetTipsService) QueryByTipIds(ctx context.Context, req *cc.TipsForCustomizedRequest) (*cc.TipsForCustomizedResponse, error) {
	var response = &cc.TipsForCustomizedResponse{Code: 200}
	// 开始查询
	err := service.beginQuery(func(engine *xorm.Engine) error {
		err := engine.Table("pet_tips").Select("`id`, `title`, `icon`, `reading`, `is_deleted`, `create_at`, `create_by`, `update_at`, `update_by`").In(`id`, req.TipIds).Find(&response.Data)
		if err == nil {
			var petTipsTag []models.PetTipsTag
			var tipsIds []int
			for _, v := range response.Data {
				tipsIds = append(tipsIds, int(v.Id))
			}
			err = engine.In("pet_tips_id", tipsIds).Find(&petTipsTag)
			if err != nil {
				response.Code = 500
				response.Message = err.Error()
			}
			for _, petTipsRecommendValue := range response.Data {
				for _, petTipsTagVales := range petTipsTag {
					if petTipsRecommendValue.Id == int32(petTipsTagVales.PetTipsId) {
						var tag cc.TipsTag
						tag.TagName = petTipsTagVales.TagName
						tag.TagValue = petTipsTagVales.TagValue
						petTipsRecommendValue.Tag = append(petTipsRecommendValue.Tag, &tag)
					}
				}
			}

		} else {
			response.Code = 500
			response.Message = err.Error()
		}

		return err
	})

	return response, err
}

// 根据特定条件获取数据
func (service *PetTipsService) QueryByCondition(ctx context.Context, req *cc.QueryByConditionRequest) (*cc.QueryByConditionResponse, error) {
	var response = &cc.QueryByConditionResponse{Code: 200}
	// 开始查询
	err := service.beginQuery(func(engine *xorm.Engine) error {
		petType := "猫"
		if req.Species == petType {
			petType = "犬"
		}

		err := engine.SQL(`select id, title, icon, reading, is_deleted, create_at, create_by, update_at, update_by  from pet_tips WHERE pet_tips.id=( select pet_tips_id from(select pet_tips_tag.pet_tips_id from  pet_tips_tag INNER JOIN pet_tips_tag as pet_tips_tag2 on pet_tips_tag.pet_tips_id=pet_tips_tag2.pet_tips_id WHERE pet_tips_tag.tag_name="物种" and (pet_tips_tag.tag_value != ?) and (pet_tips_tag2.tag_name="年龄" and pet_tips_tag2.tag_value = ?)  ORDER BY RAND() LIMIT 1)temp_tipsid)`, petType, req.Age).Find(&response.Data)
		if err == nil {
			var petTipsTag []models.PetTipsTag
			var tipsIds []int
			for _, v := range response.Data {
				tipsIds = append(tipsIds, int(v.Id))
			}
			err = engine.In("pet_tips_id", tipsIds).Find(&petTipsTag)
			if err != nil {
				response.Code = 500
				response.Message = err.Error()
			}
			for _, petTipsRecommendValue := range response.Data {
				for _, petTipsTagVales := range petTipsTag {
					if petTipsRecommendValue.Id == int32(petTipsTagVales.PetTipsId) {
						var tag cc.TipsTag
						tag.TagName = petTipsTagVales.TagName
						tag.TagValue = petTipsTagVales.TagValue
						petTipsRecommendValue.Tag = append(petTipsRecommendValue.Tag, &tag)
					}
				}
			}

		} else {
			response.Code = 500
			response.Message = err.Error()
		}

		return err
	})

	return response, err
}

// 根据物种分页获取数据
func (service *PetTipsService) QueryBySpecies(ctx context.Context, req *cc.QueryBySpeciesRequest) (*cc.PetTipsQueryResponse, error) {
	var response = &cc.PetTipsQueryResponse{Code: 200}
	// 开始查询
	err := service.beginQuery(func(engine *xorm.Engine) error {
		var sql strings.Builder
		var sqlParams = make([]interface{}, 0)
		//容错处理
		if req.Species == "猫" || req.Species == "犬" {
			sql.WriteString(`SELECT id, title, icon, reading, is_deleted, create_at, create_by, update_at, update_by FROM pet_tips AS pt INNER JOIN ( SELECT pet_tips_id FROM pet_tips_tag WHERE ( tag_name = "物种" AND ( tag_value != ? ) ) ) AS tt ON pt.id = tt.pet_tips_id WHERE is_deleted = 0`)

			if req.Species == "猫" {
				sqlParams = append(sqlParams, "犬")
			} else {
				sqlParams = append(sqlParams, "猫")
			}
		} else if len(req.Species) > 0 {
			sql.WriteString(`select id, title, icon, reading, is_deleted, create_at, create_by, update_at, update_by from pet_tips WHERE is_deleted = 0 `)
		}

		if req.PageIndex > 0 && req.PageSize > 0 {
			sql.WriteString(" limit ? offset ?")
			sqlParams = append(sqlParams, req.PageSize)
			sqlParams = append(sqlParams, (req.PageIndex-1)*req.PageSize)
		}

		err := engine.SQL(sql.String(), sqlParams...).Find(&response.Data)
		if err == nil {
			var petTipsTag []models.PetTipsTag
			var tipsIds []int
			for _, v := range response.Data {
				tipsIds = append(tipsIds, int(v.Id))
			}
			err = engine.In("pet_tips_id", tipsIds).Find(&petTipsTag)
			if err != nil {
				response.Code = 500
				response.Message = err.Error()
			}
			for _, petTipsRecommendValue := range response.Data {
				for _, petTipsTagVales := range petTipsTag {
					if petTipsRecommendValue.Id == int32(petTipsTagVales.PetTipsId) {
						var tag cc.PetTipsTagDto
						tag.Name = petTipsTagVales.TagName
						tag.Value = petTipsTagVales.TagValue
						petTipsRecommendValue.Tags = append(petTipsRecommendValue.Tags, &tag)
					}
				}
			}

		} else {
			response.Code = 500
			response.Message = err.Error()
		}
		return err
	})

	return response, err
}

// 未登录时分页获取贴士信息
func (service *PetTipsService) GetTipsList(ctx context.Context, req *cc.GetTipsListRequest) (*cc.GetTipsListResponse, error) {
	var response = &cc.GetTipsListResponse{Code: 200}

	err := service.beginQuery(func(engine *xorm.Engine) error {
		engine.ShowSQL(true)
		var sql strings.Builder
		var sqlParams = make([]interface{}, 0)
		//sql.WriteString("select id, title, icon, reading, is_deleted, create_at, create_by, update_at, update_by from (")
		sql.WriteString("SELECT rights,seq,id, title, icon, reading, is_deleted, create_at, create_by, update_at, update_by from  (")
		sql.WriteString("(SELECT 2 rights,rank() over(order by create_at) seq, id, title, icon, reading, is_deleted, create_at, create_by, update_at, update_by FROM `pet_tips` WHERE (is_deleted=0) and (date(create_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)))")
		sql.WriteString("union all")
		sql.WriteString("(SELECT 1 rights, rank() over(order by reading) seq,id, title, icon, reading, is_deleted, create_at, create_by, update_at, update_by FROM `pet_tips` WHERE (is_deleted=0) and (date(create_at) < DATE_SUB(CURDATE(), INTERVAL 7 DAY)))")
		sql.WriteString(") res order by rights desc,seq desc ")
		//sql.WriteString(") final ")
		if req.PageIndex > 0 && req.PageSize > 0 {
			sql.WriteString(" limit ? offset ?")
			sqlParams = append(sqlParams, req.PageSize)
			sqlParams = append(sqlParams, (req.PageIndex-1)*req.PageSize)
		}

		err := engine.SQL(sql.String(), sqlParams...).Find(&response.Data)
		if err != nil {
			response.Code = 500
			response.Message = err.Error()
		}

		//求总数
		total, err := engine.Table("pet_tips").Where("is_deleted=0").Count()
		if err != nil {
			response.Code = 500
			response.Message = err.Error()
		}
		response.Total = int32(total)
		var petTipsTag []models.PetTipsTag
		var tipsIds []int
		for _, v := range response.Data {
			tipsIds = append(tipsIds, int(v.Id))
		}
		err = engine.In("pet_tips_id", tipsIds).Find(&petTipsTag)
		if err != nil {
			response.Code = 500
			response.Message = err.Error()
		}
		for _, petTipsRecommendValue := range response.Data {
			for _, petTipsTagVales := range petTipsTag {
				if petTipsRecommendValue.Id == int32(petTipsTagVales.PetTipsId) {
					var tag cc.TipsTag
					tag.TagName = petTipsTagVales.TagName
					tag.TagValue = petTipsTagVales.TagValue
					petTipsRecommendValue.Tag = append(petTipsRecommendValue.Tag, &tag)
				}
			}
		}

		return err
	})
	return response, err
}

//旺财小贴士
func (service *PetTipsService) RecommendTips(ctx context.Context, req *cc.RecommendTipsRequest) (*cc.RecommendTipsResponse, error) {
	var response = &cc.RecommendTipsResponse{Code: 200}
	// 开始查询
	err := service.beginQuery(func(engine *xorm.Engine) error {
		var sql strings.Builder
		var sqlParams = make([]interface{}, 0)
		//var petTipsRecommend []*cc.TipsForCustomized
		sql.WriteString(`select id, title, icon, reading, is_deleted, create_at, create_by, update_at, update_by, content from pet_tips as pt 
				INNER JOIN pet_tips_content as ptc on pt.id = ptc.pet_tips_id 
				INNER JOIN 
				(select pet_tips_id, COUNT(*) as num from (
				 select pet_tips_id from  pet_tips_tag where (tag_name="物种" and (tag_value like ? or tag_value like "%不限%"))
				) t1  GROUP BY pet_tips_id HAVING COUNT(*)=1)  tt ON pt.id = tt.pet_tips_id WHERE is_deleted = 0 ORDER BY create_at desc`)
		sqlParams = append(sqlParams, "%"+req.Species+"%")

		err := engine.SQL(sql.String(), sqlParams...).Find(&response.Data)
		if err == nil {
			var petTipsTag []models.PetTipsTag
			var tipsIds []int
			for _, v := range response.Data {
				tipsIds = append(tipsIds, int(v.Id))
			}
			err = engine.In("pet_tips_id", tipsIds).Find(&petTipsTag)
			if err != nil {
				response.Code = 500
				response.Message = err.Error()
			}
			for _, petTipsRecommendValue := range response.Data {
				for _, petTipsTagVales := range petTipsTag {
					if petTipsRecommendValue.Id == int32(petTipsTagVales.PetTipsId) {
						var tag cc.TipsTag
						tag.TagName = petTipsTagVales.TagName
						tag.TagValue = petTipsTagVales.TagValue
						petTipsRecommendValue.Tag = append(petTipsRecommendValue.Tag, &tag)
					}
				}
			}

		} else {
			response.Code = 500
			response.Message = err.Error()
		}

		return err
	})

	return response, err
}

//定时任务，每天定时增加虚拟阅读量(每天阅读量随机增加10-100)
func (service *PetTipsService) IncreaseVirtualReading() error {
	err := service.beginTran(func(session *xorm.Session) error {
		// 更新主记录
		_, err := session.Exec("update pet_tips set virtual_reading = virtual_reading + FLOOR(10 + RAND() * 91)")
		if err != nil {
			glog.Error("IncreaseVirtualReading err:", err)
			return err
		}
		return nil
	})
	return err
}

//首页小贴士推荐规则
func (service *PetTipsService) ArticleTips(ctx context.Context, in *cc.ArticleTipsRequest) (out *cc.ArticleTipsResponse, err error) {
	out = new(cc.ArticleTipsResponse)
	db := service.getDbEngine()

	// 排序规则-随机展示12个
	var tips []models.PetTips
	err = db.SQL("SELECT a1.id,a1.title FROM pet_tips a1 RIGHT JOIN (SELECT id FROM pet_tips WHERE is_deleted = ? ORDER BY rand() LIMIT ?) as a2 ON a1.id = a2.id", 0, 12).Find(&tips)
	if err != nil {
		glog.Error("文章操作，小贴士查询错误，err:", err.Error())
	}

	if len(tips) > 0 {
		for k, _ := range tips {
			out.Data = append(out.Data, &cc.ArticleTipsData{
				ArticleId: cast.ToInt64(tips[k].Id),
				Title:     tips[k].Title,
			})
		}
	}

	out.Code = 200
	return
}
