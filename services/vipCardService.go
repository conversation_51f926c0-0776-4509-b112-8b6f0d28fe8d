package services

import (
	"_/models"
	"_/pkg/http/zilong"
	"_/proto/cc"
	"_/proto/et"
	"_/proto/ic"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"os"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-xorm/xorm"

	"github.com/golang-module/carbon/v2"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type VipCardService struct {
	BaseService
}

type OrderCardMap struct {
	Id           int32
	OrderSn      string
	ExpiryDate   string
	CardId       int32
	OrId         int32
	UserId       string
	EnUserMobile string
	CreateTime   string
}

type UserOrder struct {
	OrderSn        string `json:"order_sn"`
	EquityId       int32  `json:"equity_id"`
	EquityType     int32  `json:"equity_type"`
	EquityName     string `json:"equity_name"`
	PrivilegeId    string `json:"privilege_id"`
	Status         int32  `json:"status"`
	CreateTime     string `json:"create_time"`
	OrderTime      string `json:"order_time"`
	CollectionType int    `json:"collection_type"`
	Source         int32  `json:"source"`
}

type EquityConfig struct {
	EquityId          int32  `json:"equity_id"`
	PrivilegeIds      string `json:"privilege_ids"`
	ReceiveNum        int32  `json:"receive_num"`
	EquityName        string `json:"equity_name"`
	EquityIcon        string `json:"equity_icon"`
	EquityReceiveIcon string `json:"equity_receive_icon"`
	EquityCopy        string `json:"equity_copy"`
	ExpiryDay         int    `json:"expiry_day"`
}

// PeriodValidityInfo 子龙门店券解析有效期
type PeriodValidityInfo struct {
	Type           int32  `json:"type"`
	AfterDay       int64  `json:"after_day"`
	PeriodValidity int64  `json:"period_validity"`
	BeginTime      string `json:"begin_time,omitempty"`
	EndTime        string `json:"end_time,omitempty"`
}

func (v *VipCardService) GetEquityList(ctx context.Context, empty *empty.Empty) (*cc.GetEquityListResponse, error) {
	//TODO implement me
	panic("implement me")
}

// CycleTypeMap 卡周期
var CycleTypeMap = map[int32]string{1: "年卡", 2: "季卡", 3: "月卡", 4: "周卡", 5: "日卡"}

// OrIds 大区id
var OrIds = []int32{-1, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13}
var OrIdMap = map[int64]string{
	-1: "新瑞鹏集团", 1: "大湾区", 2: "华南区", 3: "华北区", 4: "东北区", 5: "华西区", 6: "华中区", 8: "西北区",
	9: "上海区", 10: "南京区", 11: "苏皖区", 12: "浙闵一区", 13: "浙闵二区"}

// SalesTypeMap 卡来源对应销售方式 【卡来源：0-主动购买，1-分销购买，2-虚拟卡券兑换，3-门店开卡】 【销售方式：1-主动购买、分销购买、虚拟卡券兑换，2-充值赠送】
var SalesTypeMap = map[int32]int32{
	0: 1, 1: 1, 2: 1, 3: 2,
}

// GetVipCardTemplateList 获取会员卡模版列表
func (v *VipCardService) GetVipCardTemplateList(ctx context.Context, in *cc.VipCardTemplateListRequest) (out *cc.VipCardTemplateListResponse, e error) {
	out = &cc.VipCardTemplateListResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetVipCardTemplateList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	session := DataCenterEngine.Table("vip_card_template")
	if in.OrId != 0 {
		session.And("or_id = ?", in.OrId)
	}
	var onlyData []*models.VipCardTemplate
	total, err := session.Where("type =?", in.Type).FindAndCount(&onlyData)

	if err != nil {
		out.Message = "查询数据异常"
		return
	}
	out.Total = int32(total)
	for i := range onlyData {
		date := onlyData[i]
		cycleTypeName := CycleTypeMap[date.CardCycle]
		orName := OrIdMap[date.OrId]
		var resData = cc.VipCardTemplate{
			Id:              date.Id,
			OrId:            date.OrId,
			OrName:          orName,
			CardName:        date.CardName,
			CardType:        date.CardType,
			CardCycle:       date.CardCycle,
			DurationDate:    date.DurationDate,
			MemberPrice:     date.MemberPrice,
			MemberDiscPrice: date.MemberDiscPrice,
			CreateTime:      date.CreateTime.Format(utils.DATE_TIME_LAYOUT),
			CycleName:       cycleTypeName,
			TipTitle:        date.TipTitle,
			DisRate:         date.DisRate,
			WebId:           date.WebId,
			SkuId:           date.SkuId,
		}
		out.Data = append(out.Data, &resData)
	}
	out.Code = 200
	return
}

// GetVipCardTemplateDetail 获取指定会员卡模版详情
func (v *VipCardService) GetVipCardTemplateDetail(ctx context.Context, in *cc.BaseIdRequest) (out *cc.VipCardTemplateDetailResponse, e error) {
	out = &cc.VipCardTemplateDetailResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetVipCardTemplateDetail 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	var onlyData = models.VipCardTemplate{}
	if has, err := DataCenterEngine.SQL("select * from datacenter.vip_card_template where id =?;", in.Id).Get(&onlyData); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	} else if !has {
		out.Message = "查询数据不存在"
		return
	}
	orName := OrIdMap[onlyData.OrId]
	out.Data = &cc.VipCardTemplate{
		Id:              onlyData.Id,
		OrId:            onlyData.OrId,
		OrName:          orName,
		CardName:        onlyData.CardName,
		CardType:        onlyData.CardType,
		CardCycle:       onlyData.CardCycle,
		DurationDate:    onlyData.DurationDate,
		MemberPrice:     onlyData.MemberPrice,
		MemberDiscPrice: onlyData.MemberDiscPrice,
		CreateTime:      onlyData.CreateTime.Format(utils.DATE_TIME_LAYOUT),
		CycleName:       CycleTypeMap[onlyData.CardCycle],
		TipTitle:        onlyData.TipTitle,
		WebId:           onlyData.WebId,
		DisRate:         onlyData.DisRate,
		SkuId:           onlyData.SkuId,
	}
	out.Code = 200
	return
}

// AddVipCardTemplate 添加会员卡模版
func (v *VipCardService) AddVipCardTemplate(ctx context.Context, in *cc.VipCardTemplateAddRequest) (out *cc.VcBaseResponse, e error) {
	out = &cc.VcBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("AddVipCardTemplate 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.MemberPrice <= 0 || in.MemberDiscPrice <= 0 {
		out.Message = "价格填写有误"
		return
	}
	MemberPrice, _ := decimal.NewFromFloat(in.MemberPrice).Round(2).Float64()
	MemberDiscPrice, _ := decimal.NewFromFloat(in.MemberDiscPrice).Round(2).Float64()

	var onlyData = models.VipCardTemplate{}
	onlyData.CardName = in.CardName
	onlyData.CardType = in.CardType
	onlyData.CardCycle = in.CardCycle
	onlyData.MemberPrice = MemberPrice
	onlyData.MemberDiscPrice = MemberDiscPrice
	onlyData.DurationDate = in.DurationDate
	onlyData.Type = in.Type
	onlyData.TipTitle = in.TipTitle

	_, err := DataCenterEngine.Insert(&onlyData)

	if err != nil {
		out.Message = "添加数据失败" + err.Error()
		return
	}

	out.Code = 200
	return
}

// UpdateVipCardTemplate 更新会员卡模版
func (v *VipCardService) UpdateVipCardTemplate(ctx context.Context, in *cc.VipCardTemplateUpdateRequest) (out *cc.VcBaseResponse, e error) {
	out = &cc.VcBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("UpdateVipCardTemplate 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.MemberPrice <= 0 || in.MemberDiscPrice <= 0 {
		out.Message = "价格填写有误"
		return
	}
	if in.DisRate <= 0 || in.DisRate >= 100 {
		out.Message = "分销佣金请输入1-100之间"
		return
	}
	if len([]rune(in.TipTitle)) > 16 {
		out.Message = "宣传文案限制16个字数"
		return
	}
	memberPrice, _ := decimal.NewFromFloat(in.MemberPrice).Round(2).Float64()
	memberDiscPrice, _ := decimal.NewFromFloat(in.MemberDiscPrice).Round(2).Float64()
	var onlyData = models.VipCardTemplate{
		MemberPrice:     memberPrice,
		MemberDiscPrice: memberDiscPrice,
		DisRate:         in.DisRate,
		WebId:           in.WebId,
		TipTitle:        in.TipTitle,
	}

	_, err := DataCenterEngine.Id(in.Id).Update(&onlyData)

	if err != nil {
		out.Message = "更新数据失败" + err.Error()
		return
	}

	out.Code = 200
	return
}

// ListVipEquity 付费会员卡权益列表
func (v *VipCardService) ListVipEquity(ctx context.Context, in *cc.ListPageRequest) (out *cc.ListVipEquityResponse, e error) {
	out = &cc.ListVipEquityResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("ListVipEquity 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	session := DataCenterEngine.Table("vip_card_equity")

	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	if in.OrId != 0 {
		session.And("find_in_set(?,or_ids)", in.OrId)
	}

	var onlyData []*models.VipCardEquity
	total, err := session.OrderBy("create_time desc").
		FindAndCount(&onlyData)
	if err != nil {
		out.Message = "查询数据异常"
		return
	}
	out.Total = int32(total)
	for i := range onlyData {
		date := onlyData[i]
		var resData = cc.VipEquity{
			Id:              date.Id,
			OrIds:           date.OrIds,
			EquityIcon:      date.EquityIcon,
			EquityName:      date.EquityName,
			EquityShortName: date.EquityShortName,
			EquityCopy:      date.EquityCopy,
			EquityPrice:     date.EquityPrice,
			EquityType:      date.EquityType,
			MatchType:       date.MatchType,
			IssueType:       date.IssueType,
			CollectionIds:   date.CollectionIds,
			Status:          date.Status,
			ExpiryDay:       date.ExpiryDay,
			ReceiveType:     date.ReceiveType,
			EquityInfo:      date.EquityInfo,
			EquityRule:      date.EquityRule,
			EquityImg:       date.EquityImg,
			IsActive:        date.IsActive,
		}
		out.Data = append(out.Data, &resData)
	}
	out.Code = 200
	return
}

// GetVipEquity 付费会员卡权益详情
func (v *VipCardService) GetVipEquity(ctx context.Context, in *cc.BaseIdRequest) (out *cc.GetVipEquityResponse, e error) {
	out = &cc.GetVipEquityResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetVipEquity 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	var onlyData = models.VipCardEquity{}
	if has, err := DataCenterEngine.SQL("select * from datacenter.vip_card_equity where id =?;", in.Id).Get(&onlyData); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	} else if !has {
		out.Message = "查询数据不存在"
		return
	}

	out.Data = &cc.VipEquity{
		Id:                onlyData.Id,
		OrIds:             onlyData.OrIds,
		EquityIcon:        onlyData.EquityIcon,
		EquityName:        onlyData.EquityName,
		EquityCopy:        onlyData.EquityCopy,
		EquityPrice:       onlyData.EquityPrice,
		EquityType:        onlyData.EquityType,
		MatchType:         onlyData.MatchType,
		CollectionIds:     onlyData.CollectionIds,
		IssueType:         onlyData.IssueType,
		Status:            onlyData.Status,
		JumpUrl:           onlyData.JumpUrl,
		EquityInfo:        onlyData.EquityInfo,
		EquityRule:        onlyData.EquityRule,
		EquityImg:         onlyData.EquityImg,
		ExpiryDay:         onlyData.ExpiryDay,
		ReceiveType:       onlyData.ReceiveType,
		EquityShortName:   onlyData.EquityShortName,
		IsActive:          onlyData.IsActive,
		MainTitle:         onlyData.MainTitle,
		SubTitle:          onlyData.SubTitle,
		EquityReceiveIcon: onlyData.EquityReceiveIcon,
	}
	out.Code = 200
	return
}

// CreateOrUpdateVipEquity 付费会员卡权益新增/更新
func (v *VipCardService) CreateOrUpdateVipEquity(ctx context.Context, in *cc.VipEquity) (out *cc.VcBaseResponse, e error) {
	out = &cc.VcBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("CreateVipEquity 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len([]rune(in.EquityName)) > 8 {
		out.Message = "权益名称限制8个字数"
		return
	}
	if len([]rune(in.EquityCopy)) > 10 {
		out.Message = "宣传文案限制10个字数"
		return
	}
	if in.EquityPrice <= 0 {
		out.Message = "价格填写有误"
		return
	}
	types := []int{1, 2, 5, 6}
	idx := sort.SearchInts(types, int(in.EquityType))
	if idx < len(types) && types[idx] == int(in.EquityType) && in.CollectionIds == "" {
		out.Message = "领取类型不能为空"
		return
	}
	//宠物医保权益有效期判断
	if in.EquityType == 7 && (in.ExpiryDay < 1 || in.ExpiryDay > 12) {
		out.Message = "权益有效期参数有误"
		return
	}

	if in.EquityType == 8 && in.MatchType != 1 {
		out.Message = "子龙打折卡只能单项填写"
		return
	}

	//判断大区id
	if res := CheckOrgId(in.OrIds); res == false {
		out.Message = "大区id参数有误"
		return
	}

	if has, err := DataCenterEngine.SQL("select * from datacenter.vip_card_equity where equity_name =? and id != ?;", in.EquityName, in.Id).Get(&models.VipCardEquity{}); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	} else if has {
		out.Message = "权益名称已存在"
		return
	}
	var onlyData = models.VipCardEquity{}
	equityPrice, _ := decimal.NewFromFloat(in.EquityPrice).Round(2).Float64()

	onlyData.EquityIcon = in.EquityIcon
	onlyData.EquityName = in.EquityName
	onlyData.EquityCopy = in.EquityCopy
	onlyData.EquityPrice = equityPrice
	onlyData.EquityType = in.EquityType
	onlyData.MatchType = in.MatchType
	onlyData.IssueType = in.IssueType
	onlyData.CollectionIds = in.CollectionIds
	onlyData.Status = in.Status
	onlyData.JumpUrl = in.JumpUrl
	onlyData.EquityInfo = in.EquityInfo
	onlyData.EquityRule = in.EquityRule
	onlyData.EquityImg = in.EquityImg
	onlyData.ExpiryDay = in.ExpiryDay
	onlyData.OrIds = in.OrIds
	//onlyData.ReceiveType = in.ReceiveType
	onlyData.EquityShortName = in.EquityShortName
	onlyData.IsActive = in.IsActive
	onlyData.MainTitle = in.MainTitle
	onlyData.SubTitle = in.SubTitle
	onlyData.EquityReceiveIcon = in.EquityReceiveIcon

	if in.Id > 0 {
		_, err := DataCenterEngine.Id(in.Id).MustCols("status,issue_type,sub_title").Update(&onlyData)
		if err != nil {
			out.Message = "操作数据失败" + err.Error()
			return
		}
	} else {
		_, err := DataCenterEngine.Insert(&onlyData)
		if err != nil {
			out.Message = "操作数据失败" + err.Error()
			return
		}
	}
	out.Code = 200
	return
}

// ListEquityConfigs 权益配置列表
func (v *VipCardService) ListEquityConfigs(ctx context.Context, in *cc.ListPageRequest) (out *cc.ListEquityConfigsResponse, e error) {
	out = &cc.ListEquityConfigsResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("ListEquityConfigs 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	var onlyData []*models.EquityConfig
	total, err := DataCenterEngine.Table("vip_card_equity_config").Alias("vcec").
		Select("vcec.or_id,vcec.card_tid,vct.card_name,vct.card_cycle,group_concat(vce.equity_name) as equity_name,log.user_name as opter,log.create_time").
		Join("left", "vip_card_equity vce", "vcec.equity_id = vce.id").
		Join("left", "vip_card_template vct", "vcec.card_tid = vct.id").
		Join("left", "(SELECT s.from_id, s.user_no, s.user_name,s.create_time FROM store_operate_log s "+
			"INNER JOIN (SELECT from_id, MAX(create_time) AS max_time FROM store_operate_log "+
			"WHERE type = 2 GROUP BY from_id) t ON s.from_id = t.from_id AND s.create_time = t.max_time "+
			"WHERE s.type = 2) log", "vcec.card_tid = log.from_id").
		GroupBy("vcec.card_tid").
		FindAndCount(&onlyData)
	//DataCenterEngine.ShowSQL()
	if err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}
	out.Total = int32(total)
	for i := range onlyData {
		date := onlyData[i]
		cycleName := CycleTypeMap[date.CardCycle]
		orName := OrIdMap[date.OrId]
		var resData = cc.ListEquityConfig{
			CardTid:    date.CardTid,
			OrId:       date.OrId,
			CardName:   date.CardName,
			EquityName: date.EquityName,
			UserName:   date.Opter,
			UpdateTime: date.CreateTime.Format(utils.DATE_TIME_LAYOUT),
			CycleName:  cycleName,
			OrName:     orName,
		}
		out.Data = append(out.Data, &resData)
	}
	out.Code = 200
	return
}

// 1-商城优惠券，2-子龙门店券，3-积分翻倍，4-会员价商品，5-大牌礼包，6-家庭服务包 7-宠物医保链接(洗护报销、买药报销、看病报销)8-子龙打折卡 9-商品特权 10-健康服务金
// 商城优惠券-已核销;子龙门店券-已核销;大牌礼包-已领取;家庭医生服务包-已激活;宠物医保链接-已投保;子龙打折卡-已享受;会员特价商品-已享受;医保价商品-已享受;会员价商品-已享受
var EquityTypeRuleMap = map[int32]string{1: "已核销权益", 2: "已核销权益", 3: "已享受权益", 4: "已享受权益", 5: "已领取权益", 6: "已激活权益", 7: "已投保权益", 8: "已享受权益", 9: "已享受权益", 10: "已享受权益"}

// ListEquityConfigs 权益配置列表
func (v *VipCardService) ListNoRefundableEquityConfig(ctx context.Context, in *cc.ListPageRequest) (out *cc.ListNoRefundableEquityConfigsResponse, e error) {
	out = &cc.ListNoRefundableEquityConfigsResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("ListNoRefundableEquityConfig 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	var onlyData = make([]*models.NoRefundableEquityConfig, 0)
	total, err := DataCenterEngine.Table("vip_card_equity_config").Alias("vcec").
		Select("vcec.card_tid,vct.card_name,vct.card_cycle,vcec.equity_id,vce.equity_name,'存在' AS equity_rule,vce.equity_type").
		Join("left", "vip_card_equity vce", "vcec.equity_id = vce.id").
		Join("left", "vip_card_template vct", "vcec.card_tid = vct.id").
		Where("vcec.refundable=0").
		Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
		FindAndCount(&onlyData)

	if err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}

	// 处理“规则值”返回
	var retData = make([]*cc.ListNoRefundableEquityConfig, 0)
	for _, data := range onlyData {
		equityType := data.EquityType
		var ret = cc.ListNoRefundableEquityConfig{
			CardTid:    data.CardTid,
			CardName:   data.CardName,
			CardCycle:  data.CardCycle,
			EquityId:   data.EquityId,
			EquityName: data.EquityName,
			EquityRule: data.EquityRule,
			RuleValue:  EquityTypeRuleMap[equityType],
			EquityType: data.EquityType,
		}
		retData = append(retData, &ret)
	}

	out.Total = int32(total)
	out.Data = retData
	out.Code = 200
	return
}

// GetEquityConfig 权益配置详情
func (v *VipCardService) GetEquityConfig(ctx context.Context, in *cc.GetEquityConfigRequest) (out *cc.GetEquityConfigResponse, e error) {
	out = &cc.GetEquityConfigResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetVipEquity 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	var onlyData []*models.VipCardEquityConfigExtend

	session := DataCenterEngine.Table("vip_card_equity_config").Alias("c")
	if in.OrId == 0 {
		out.Message = "组织or_id参数有误"
		return
	}
	if in.CardTid == 0 {
		out.Message = "模板card_tid参数有误"
		return
	}

	err := session.Select("c.*,e.equity_short_name,e.equity_type").Join("left", "vip_card_equity as e", "c.equity_id = e.id").
		Where("c.card_tid = ? and c.or_id =?", in.CardTid, in.OrId).OrderBy("sort asc").Find(&onlyData)

	if err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}
	for i := range onlyData {
		date := onlyData[i]

		values, _ := getVipCardEquityValues(date.CardTid, date.OrId, date.EquityId, date.PrivilegeIds)

		var resData = cc.EquityConfig{
			CardTid:         date.CardTid,
			EquityId:        date.EquityId,
			Status:          date.Status,
			PrivilegeIds:    date.PrivilegeIds,
			ReceiveNum:      date.ReceiveNum,
			EquityShortName: date.EquityShortName,
			EquityType:      date.EquityType,
			Refundable:      date.Refundable,
			FreeValue:       values,
		}
		out.Data = append(out.Data, &resData)
	}
	out.Code = 200
	return
}

func getVipCardEquityValues(cardId int32, orId int64, equityId int32, privilegeIds string) (values []*cc.EquityConfigValue, err error) {
	session := DataCenterEngine.Table("vip_card_equity_value").Alias("vcev")
	err = session.Select("vcev.privilege_id,vcev.free_quality,vcev.sales_type").
		Where("vcev.card_tid = ? AND vcev.or_id =? AND vcev.equity_id=?", cardId, orId, equityId).Find(&values)

	if err != nil {
		return nil, err
	}

	if len(values) == 0 && len(privilegeIds) > 0 {
		privilegeIdArr := strings.Split(privilegeIds, ",")
		for _, privilegeId := range privilegeIdArr {
			values = append(values, &cc.EquityConfigValue{
				PrivilegeId: privilegeId,
			})
		}
	}

	return values, nil
}

// SaveEquityConfig 权益配置新增/更新
// 1-商城优惠券，2-子龙门店券，3-积分翻倍，4-会员价商品，5-大牌礼包，6-家庭服务包 7-医保卡商品(洗护报销、买药报销、看病报销) 8-子龙打折卡
func (v *VipCardService) SaveEquityConfig(ctx context.Context, in *cc.CreateEquityConfigRequest) (out *cc.VcBaseResponse, e error) {
	out = &cc.VcBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("SaveEquityConfig 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.CardTid <= 0 {
		out.Message = "付费周期选择不能为空"
		return
	}
	if len(in.Data) == 0 {
		out.Message = "至少选择一项权益"
		return
	}
	//判断大区id
	if res := CheckOrgId(cast.ToString(in.OrId)); res == false {
		out.Message = "大区id参数有误"
		return
	}

	//校验权益配置
	//1、权益不可重复
	//2、龙门店券、商城优惠券的id是否正常
	//3、礼包商品校验
	//4、输入值是否符合单项、多项要求
	var onlyData = make([]*models.VipCardEquityConfig, 0)
	var equityValue = make([]*models.VipCardEquityValue, 0)
	var typeKeyMap = make(map[int32]bool)
	var typeMap = make(map[int32]bool)      //判断全国子龙券唯一
	var typeNameMap = make(map[string]bool) //判断浙闽二区的医疗权益、医疗礼包唯一
	for k, v := range in.Data {
		if v.EquityType <= 0 {
			out.Message = "请选择权益关联类型"
			return
		}
		if typeKeyMap[v.EquityId] {
			out.Message = fmt.Sprintf("权益为%v增加重复", v.EquityId)
			return
		}
		typeKeyMap[v.EquityId] = true

		// 兼容有赠送价值，没有权益值的情况
		if len(v.PrivilegeIds) == 0 && len(v.FreeValue) > 0 {
			var privilegeIds = make([]string, 0)
			for _, value := range v.FreeValue {
				privilegeIds = append(privilegeIds, value.PrivilegeId)
			}
			v.PrivilegeIds = strings.Join(privilegeIds, ",")
		}

		switch v.EquityType {
		case 1: //商城优惠券 领取个数校验
			v.PrivilegeIds, _, e = checkGoodsVouchers(v.PrivilegeIds)
			if e != nil {
				out.Message = e.Error()
				return
			}
			vouchers := strings.Split(v.PrivilegeIds, ",")
			v.ReceiveNum = int32(len(vouchers))
		case 2: //子龙门店券 领取个数校验
			if in.OrId == -1 {
				if typeMap[v.EquityType] {
					out.Message = "子龙门店券只能添加一个"
					return
				}
				typeMap[v.EquityType] = true
			} else if in.OrId == 13 {
				var EquityShortName string
				if _, err := DataCenterEngine.Table("vip_card_equity").Select("equity_short_name").
					Where("id = ?", v.EquityId).Get(&EquityShortName); err != nil {
					out.Message = "查询权益短名称失败" + err.Error()
					return
				}
				if typeNameMap[EquityShortName] {
					out.Message = fmt.Sprintf("%s只能添加一个", EquityShortName)
					return
				}
				typeNameMap[EquityShortName] = true
			}

			v.PrivilegeIds, _, e = checkZiLongVouchers(v.PrivilegeIds)
			if e != nil {
				out.Message = e.Error()
				return
			}

			vouchers := strings.Split(v.PrivilegeIds, ",")
			v.ReceiveNum = int32(len(vouchers))
		case 5: //大牌礼包
			ids := strings.FieldsFunc(v.PrivilegeIds, func(r rune) bool {
				return r == ',' || r == '，'
			})
			if results, err := DataCenterEngine.Table("vip_card_gift").In("id", ids).
				QueryInterface(); err != nil {
				out.Message = "大牌礼包查询商品信息异常"
				return
			} else if len(results) == 0 {
				out.Message = "大牌礼包查询不到商品记录"
				return
			} else {
				var errMsgs []string
				if len(results) < len(ids) {
					var (
						oid []int64
						nid []int64
					)
					for _, r := range results {
						nid = append(nid, r["id"].(int64))
					}
					for _, r := range ids {
						oid = append(oid, cast.ToInt64(r))
					}
					for _, item := range oid {
						contains := false
						for _, a := range nid {
							if a == item {
								contains = true
								break
							}
						}
						if !contains {
							errMsgs = append(errMsgs, fmt.Sprintf("%v-礼包不存在", item))
						}
					}
				}
				if len(errMsgs) > 0 {
					out.Message = errors.New(strings.Join(errMsgs, ";")).Error()
					return
				}
			}
		case 6: //家庭服务包
			var cardData = models.VipCardTemplate{}
			if has, err := DataCenterEngine.SQL("select * from datacenter.vip_card_template where type =2 and id = ?;",
				v.PrivilegeIds).Get(&cardData); err != nil {
				out.Message = "查询家庭服务包数据异常" + err.Error()
				return
			} else if !has {
				out.Message = "家庭服务包不存在"
				return
			}
		case 8:
			vouchers := strings.FieldsFunc(v.PrivilegeIds, func(r rune) bool {
				return r == ',' || r == '，'
			})
			if int32(len(vouchers)) > 1 {
				out.Message = fmt.Sprintf("子龙打折卡只能单项配置")
				return
			}
		}

		var resData = models.VipCardEquityConfig{
			OrId:         in.OrId,
			CardTid:      in.CardTid,
			EquityId:     v.EquityId,
			Sort:         k,
			Status:       v.Status,
			PrivilegeIds: v.PrivilegeIds,
			ReceiveNum:   v.ReceiveNum,
			Refundable:   v.Refundable,
		}
		onlyData = append(onlyData, &resData)

		if len(v.FreeValue) > 0 {
			// 判断赠送价值和卡模板的价值
			var template = models.VipCardTemplate{}
			if has, err := DataCenterEngine.SQL("select * from datacenter.vip_card_template where id =?;", in.CardTid).Get(&template); err != nil {
				out.Message = "查询卡模板数据异常" + err.Error()
				return
			} else if !has {
				out.Message = "查询卡模板数据不存在"
				return
			}

			for _, value := range v.FreeValue {
				if kit.YuanToFen(template.MemberDiscPrice) < int(value.FreeQuality) {
					out.Message = fmt.Sprintf("存在赠送价值：大于卡模版售卖价格")
					return
				}

				var resValue = models.VipCardEquityValue{
					OrId:        in.OrId,
					CardTid:     in.CardTid,
					EquityId:    v.EquityId,
					PrivilegeId: value.PrivilegeId,
					FreeQuality: value.FreeQuality,
					SalesType:   value.SalesType,
				}
				equityValue = append(equityValue, &resValue)
			}
		}
	}

	db := DataCenterEngine
	session := db.NewSession()
	defer session.Close()
	_ = session.Begin()

	var equityModel []*models.VipCardEquityConfig
	if err := session.SQL("select * from datacenter.vip_card_equity_config where card_tid = ? and or_id = ? order by sort;", in.CardTid, in.OrId).
		Find(&equityModel); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}

	if len(equityModel) > 0 {
		//避免新增已有周期卡的情况，判断添加的数据小于已有的数据则提示异常
		if len(equityModel) > len(onlyData) {
			out.Message = "添加数据权益记录异常"
			return
		}
		// 新的数据权益需包含已有的权益，否则提示错误
		if isContains := ContainsData(equityModel, in.Data); !isContains {
			out.Message = "数据权益配置有误"
			return
		}
		_, err := session.Where("card_tid = ? and or_id = ?", in.CardTid, in.OrId).Delete(&models.VipCardEquityConfig{})
		if err != nil {
			_ = session.Rollback()
			out.Message = "删除数据异常" + err.Error()
			return
		}
	}

	_, err := session.Insert(&onlyData)
	if err != nil {
		_ = session.Rollback()
		out.Message = err.Error()
		return
	}

	// 处理赠送价值
	err = insertEquityValue(session, equityValue)
	if err != nil {
		_ = session.Rollback()
		out.Message = err.Error()
		return
	}

	//记录日志
	DataCenterEngine.Insert(models.StoreOperateLog{
		Type:       2,
		FromId:     in.CardTid,
		Desc:       "权益配置编辑",
		UserName:   in.UserName,
		UserNo:     in.UserId,
		BeforeJson: kit.JsonEncode(equityModel),
		AfterJson:  kit.JsonEncode(onlyData),
	})

	_ = session.Commit()
	out.Code = 200
	return
}

func insertEquityValue(session *xorm.Session, equityValue []*models.VipCardEquityValue) error {
	if len(equityValue) == 0 {
		return nil
	}

	value := equityValue[0]
	var values []*models.VipCardEquityValue
	if err := session.SQL("select * from datacenter.vip_card_equity_value where card_tid = ? and or_id = ?;", value.CardTid, value.OrId).
		Find(&values); err != nil {
		return nil
	}

	if len(values) > 0 {
		_, err := session.Where("card_tid = ? and or_id = ?", value.CardTid, value.OrId).Delete(&models.VipCardEquityValue{})
		if err != nil {
			_ = session.Rollback()
			return err
		}
	}

	_, err := session.Insert(&equityValue)
	if err != nil {
		return err
	}
	return nil
}

func ContainsData(EquityModel []*models.VipCardEquityConfig, Data []*cc.EquityConfig) bool {
	var (
		oid []int32
		nid []int32
	)
	isContains := true
	for _, v := range EquityModel {
		oid = append(oid, v.EquityId)
	}
	for _, v := range Data {
		nid = append(nid, v.EquityId)
	}
	for _, item := range oid {
		contains := false
		for _, a := range nid {
			if a == item {
				contains = true
				break
			}
		}
		if !contains {
			isContains = false
			break
		}
	}
	if isContains {
		return true
	} else {
		return false
	}
}

// GetGiftList 开卡礼包列表
func (v *VipCardService) GetGiftList(ctx context.Context, in *cc.GiftListRequest) (out *cc.GetGiftListResponse, e error) {
	out = &cc.GetGiftListResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetGiftList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	var onlyData []*models.VipCardGiftExtends
	session := DataCenterEngine.Table("vip_card_gift").Alias("vcg").Where("vcg.state != 3")
	if in.State > 0 {
		session.And("vcg.state = ?", in.State)
	}
	if in.Id > 0 {
		session.And("vcg.id = ?", in.Id)
	}
	if in.PackName != "" {
		session.And("vcg.pack_name like ?", "%"+in.PackName+"%")
	}
	if in.IsMain > 0 {
		session.And("vcg.is_main = ?", in.IsMain)
	}
	//只查询配置的礼包商品
	if in.StockState == 1 {
		var privilege_ids string
		if _, err := DataCenterEngine.SQL("select privilege_ids from vip_card_equity_config c left join vip_card_equity e on " +
			"c.equity_id = e.id where c.card_tid =1 and e.equity_type=5;").Get(&privilege_ids); err != nil {
			out.Message = "查询数据异常" + err.Error()
			return
		}
		if privilege_ids != "" {
			ids := strings.Split(privilege_ids, ",")
			session.In("vcg.id", ids)
		}
	}
	total, err := session.Select("vcg.*,log.user_no, log.user_name").
		Join("left", "(SELECT s.from_id, s.user_no, s.user_name FROM store_operate_log s "+
			"INNER JOIN (SELECT from_id, MAX(create_time) AS max_time FROM store_operate_log "+
			"WHERE type = 3 GROUP BY from_id) t ON s.from_id = t.from_id AND s.create_time = t.max_time "+
			"WHERE s.type = 3) log", "vcg.id = log.from_id").FindAndCount(&onlyData)

	if err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}

	if total > 0 {
		//判断库存
		var GoodsStockMap = make(map[int32]int32, len(onlyData))
		if in.StockState == 1 {
			req := ic.GetStockInfoRequest{}
			req.Source = 1
			req.IsNeedPull = 0
			for k := range onlyData {
				item := ic.ProductsInfo{
					SkuId:        cast.ToInt32(onlyData[k].PackSkuId),
					Type:         2,
					IsAllVirtual: 0,
				}
				req.ProductsInfo = append(req.ProductsInfo, &item)
			}
			client := ic.GetInventoryServiceClient()
			stockRes, err := client.RPC.GetStockInfo(client.Ctx, &req)
			if err != nil {
				out.Message = "调用GetStockInfo查询库存异常" + err.Error()
				return
			} else if stockRes.Code != 200 {
				out.Message = "调用GetStockInfo查询库存失败" + err.Error()
				return
			}

			for _, x := range stockRes.GoodsInfo.ProductsInfo {
				GoodsStockMap[x.SkuId] = x.Stock
			}
		}

		for i := range onlyData {
			date := onlyData[i]
			//无库存为下架
			stock := GoodsStockMap[date.PackSkuId]
			var resData = cc.GiftData{
				Id:         date.Id,
				PackName:   date.PackName,
				PackDesc:   date.PackDesc,
				PackPrice:  date.PackPrice,
				PackImage:  date.PackImage,
				PackType:   date.PackType,
				PackSkuId:  date.PackSkuId,
				State:      date.State,
				IsMain:     date.IsMain,
				UserNo:     date.UserNo,
				UserName:   date.UserName,
				UpdateTime: date.UpdateTime.Format(utils.DATE_TIME_LAYOUT),
				Stock:      stock,
			}
			out.Data = append(out.Data, &resData)
		}
	}
	out.Total = int32(total)
	out.Code = 200
	return
}

// GetGift 开卡礼包详情
func (v *VipCardService) GetGift(ctx context.Context, in *cc.BaseIdRequest) (out *cc.GetGiftResponse, e error) {
	out = &cc.GetGiftResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetVipEquity 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	var onlyData = models.VipCardGift{}
	_, err := DataCenterEngine.SQL("select * from datacenter.vip_card_gift where id = ?;", in.Id).Get(&onlyData)

	if err != nil {
		out.Message = "查询数据异常"
		return
	}

	out.Data = &cc.GiftData{
		Id:         onlyData.Id,
		PackName:   onlyData.PackName,
		PackDesc:   onlyData.PackDesc,
		PackPrice:  onlyData.PackPrice,
		PackImage:  onlyData.PackImage,
		PackType:   onlyData.PackType,
		PackSkuId:  onlyData.PackSkuId,
		State:      onlyData.State,
		IsMain:     onlyData.IsMain,
		UpdateTime: onlyData.UpdateTime.Format(utils.DATE_TIME_LAYOUT),
	}

	out.Code = 200
	return
}

// SaveGift 开卡礼包新增/更新
func (v *VipCardService) SaveGift(ctx context.Context, in *cc.GiftData) (out *cc.VcBaseResponse, e error) {
	out = &cc.VcBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("SaveGift 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len([]rune(in.PackName)) > 10 {
		out.Message = "礼包名称限制10个字数"
		return
	}
	if len([]rune(in.PackDesc)) > 50 {
		out.Message = "PackDesc限制50个字数"
		return
	}
	if in.PackPrice <= 0 {
		out.Message = "礼包价值输入有误"
		return
	}
	//判断礼包商品是否上架状态
	type goodsMap struct {
		GoodsId   int32 `json:"goods_id"`
		IsVirtual int32 `json:"is_virtual"`
		GoodsType int32 `json:"goods_type"`
	}
	var goodsMod goodsMap
	if has, err := BbcEngine.SQL("select g.goods_id,g.is_virtual,gc.goods_type from upet_goods g left join "+
		"upet_goods_common gc on g.goods_commonid = gc.goods_commonid where g.goods_id = ? AND g.store_id=? and gc.store_id = ?; ", in.PackSkuId, in.OrgId, in.OrgId).
		Get(&goodsMod); err != nil {
		out.Message = "查询商品信息异常"
		return
	} else if !has {
		out.Message = "礼包商品不存在或状态异常"
		return
	}
	if goodsMod.IsVirtual == 1 {
		out.Message = "礼包商品不能添加虚拟商品"
		return
	}
	if goodsMod.GoodsType > 0 {
		out.Message = "礼包商品不能添加组合商品"
		return
	}

	onlyData := models.VipCardGift{
		PackName:  in.PackName,
		PackDesc:  in.PackDesc,
		PackPrice: in.PackPrice,
		PackImage: in.PackImage,
		PackType:  in.PackType,
		PackSkuId: in.PackSkuId,
		State:     in.State,
		IsMain:    in.IsMain,
	}
	var (
		id   int32
		desc string
	)

	var giftData = models.VipCardGift{}
	_, err := DataCenterEngine.SQL("select * from datacenter.vip_card_gift where is_main = 1;").Get(&giftData)
	if err != nil {
		out.Message = "查询商品信息异常"
		return
	}
	if in.Id > 0 {
		if giftData.Id > 0 && in.IsMain == 1 && giftData.Id != in.Id {
			out.Message = "其他礼包标记了主推，保存不成功"
			return
		}
		_, err := DataCenterEngine.Id(in.Id).Update(&onlyData)
		if err != nil {
			out.Message = "操作数据失败" + err.Error()
			return
		}
		id = in.Id
		desc = "编辑开卡礼包"
	} else {
		if giftData.Id > 0 && in.IsMain == 1 {
			out.Message = "其他礼包标记了主推，保存不成功"
			return
		}
		//判断礼包商品是否已经存在状态1、2的记录
		var Id int32
		if _, err = DataCenterEngine.SQL("select id from vip_card_gift where state in (1,2) and pack_sku_id = ?;", in.PackSkuId).Get(&Id); err != nil {
			out.Message = "查询礼包商品信息异常"
			return
		}
		if Id > 0 {
			out.Message = "礼包商品已存在，添加不成功"
			return
		}
		_, err = DataCenterEngine.Insert(&onlyData)
		if err != nil {
			out.Message = "操作数据失败" + err.Error()
			return
		}
		id = onlyData.Id
		desc = "添加开卡礼包"
	}
	go func() {
		DataCenterEngine.Insert(models.StoreOperateLog{
			Type:     3,
			FromId:   id,
			Desc:     desc,
			UserName: in.UserName,
			UserNo:   in.UserNo,
		})
	}()

	out.Code = 200
	return
}

// DeleteGift 删除开卡礼包
func (v *VipCardService) DeleteGift(ctx context.Context, in *cc.BaseIdRequest) (out *cc.VcBaseResponse, e error) {
	out = &cc.VcBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("DeleteGift 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	//有绑定权益不能删除
	var Data = models.VipCardEquityConfig{}
	if has, err := DataCenterEngine.SQL("select vcec.* from vip_card_equity vce left join vip_card_equity_config "+
		"vcec on vce.id = vcec.equity_id where vce.equity_type = 5 and find_in_set(?,privilege_ids);", in.Id).Get(&Data); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	} else if has {
		out.Message = "已绑定权益，不能删除"
		return
	}

	if _, err := DataCenterEngine.Exec("update vip_card_gift set state=3 where id = ?", in.Id); err != nil {
		out.Message = "操作数据库异常"
		return
	}

	out.Code = 200
	return
}

// UpDownGift 礼包上下架操作
func (v *VipCardService) UpDownGift(ctx context.Context, in *cc.BaseIdRequest) (out *cc.VcBaseResponse, e error) {
	out = &cc.VcBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("UpDownGift 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	_, err := DataCenterEngine.Exec("UPDATE vip_card_gift SET state = (CASE WHEN state = 1 THEN 2 ELSE 1 END) where id = ?", in.Id)
	if err != nil {
		out.Message = "操作数据库异常"
		return
	}
	out.Code = 200
	return
}

// CheckVouchers 优惠券检查
func (v *VipCardService) CheckVouchers(ctx context.Context, in *cc.VoucherRequest) (out *cc.DataBaseResponse, e error) {
	out = &cc.DataBaseResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("CheckVouchers 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	switch in.Type {
	case 1: //商城优惠券
		if _, resList, err := checkGoodsVouchers(in.Vid); err != nil {
			out.Message = err.Error()
			out.Data = resList
		} else {
			out.Data = resList
		}
	case 2: //子龙门店券
		if _, resList, err := checkZiLongVouchers(in.Vid); err != nil {
			out.Message = err.Error()
			out.Data = resList
		} else {
			out.Data = resList
		}
	}
	out.Code = 200
	return
}

// GetCardInfo 付费会卡信息权益
func (v *VipCardService) GetCardInfo(ctx context.Context, in *cc.BaseCardRequest) (out *cc.GetCardInfoResponse, e error) {
	out = &cc.GetCardInfoResponse{Code: 400}
	logFix := fmt.Sprintf("GetCardInfo====入参：%s-%d", in.UserId, in.Id)
	glog.Info(logFix)
	defer func() {
		if out.Code != 200 {
			glog.Info("GetCardInfo 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	//获取用户开通状态
	var vipCardState int32
	wg := new(sync.WaitGroup)
	wg.Add(2)
	go func() {
		defer wg.Done()
		if in.UserId != "" {
			type memberMap struct {
				VipCardState int32 `json:"vip_card_state"`
			}
			var userData memberMap
			if has, err := BbcEngine.SQL("select vip_card_state from upet_member where scrm_user_id = ?;", in.UserId).Get(&userData); err != nil {
				glog.Error(logFix, "查询数据库错误vip_card_state：", err.Error())
				out.Message = "查询数据异常" + err.Error()
				return
			} else if !has {
				glog.Error(logFix, "查询vip_card_state数据不存在")
				out.Message = "查询数据不存在"
				return
			}
			glog.Info(logFix, "userData的值为：", utils.InterfaceToJSON(userData))
			vipCardState = userData.VipCardState
		}
	}()

	giftData := models.VipCardGift{}
	go func() {
		defer wg.Done()
		if _, err := DataCenterEngine.SQL("select * from datacenter.vip_card_gift where is_main = 1;").Get(&giftData); err != nil {
			out.Message = "查询开卡礼包数据异常"
			return
		}
	}()
	wg.Wait()

	//先判断是否登录,已登录则判断是否购买会员卡
	//1.已开通，则返回对应区域卡的信息
	//2.未开通，则判断是否传卡id，没传默认全国卡
	type orderMap struct {
		OrderSn    string
		ExpiryDate string
		CardId     int32
	}
	OrderData := orderMap{}
	if vipCardState == 1 && in.UserId != "" {
		if in.Type == 0 {
			// 兼容有传入orderSn的情况
			if len(in.OrderSn) > 0 {
				if _, err := DataCenterEngine.SQL("SELECT order_sn,expiry_date,card_id "+
					"FROM vip_card_order "+
					"WHERE state = 10 AND expiry_date > NOW() AND order_sn = ?", in.OrderSn).Get(&OrderData); err != nil {
					out.Message = "查询会员订单数据异常"
					glog.Error(logFix, "查询数据库失败vip_card_order：", err.Error())
					return
				}
			} else {
				if _, err := DataCenterEngine.SQL("SELECT order_sn,expiry_date,card_id "+
					"FROM vip_card_order "+
					"WHERE state = 10 AND expiry_date > NOW() AND user_id = ? ORDER BY id DESC;", in.UserId).Get(&OrderData); err != nil {
					out.Message = "查询会员订单数据异常"
					glog.Error(logFix, "查询数据库失败vip_card_order：", err.Error())
					return
				}
			}
			in.Id = OrderData.CardId
		}
	} else if in.Id == 0 {
		in.Id = -1
	}
	var cardData = models.VipCardTemplate{}
	if has, err := DataCenterEngine.SQL("select * from datacenter.vip_card_template where id = ?;", in.Id).Get(&cardData); err != nil {
		out.Message = "查询卡信息数据异常" + err.Error()
		return
	} else if !has {
		glog.Error(logFix, "查询数据不存在")
		out.Message = "查询数据不存在"
		return
	}

	//判断用户是否有全国卡，有则显示会员卡标识
	var (
		Id            int32
		showCardState int32
	)
	if _, err := DataCenterEngine.SQL("SELECT id FROM vip_card_order "+
		"WHERE state = 10 AND expiry_date > NOW() AND user_id = ? and card_id = 1;", in.UserId).Get(&Id); err != nil {
		out.Message = "查询全国健康卡数据异常"
		glog.Error(logFix, "查询全国健康卡失败vip_card_order：", err.Error())
		return
	} else if Id > 0 {
		showCardState = 1
	}

	//宠物体验券配置skuid
	redisKey := "awen:vip-pet-coupon"
	r := GetRedisConn()
	defer r.Close()
	vipCoupon := r.Get(redisKey).Val()

	out.Data = &cc.CardInfo{
		CardTid:         cardData.Id,
		CardName:        cardData.CardName,
		MemberPrice:     cardData.MemberPrice,
		MemberDiscPrice: cardData.MemberDiscPrice,
		TipTitle:        cardData.TipTitle,
		GiftName:        giftData.PackName,
		VipCardState:    vipCardState,
		OrderSn:         OrderData.OrderSn,
		ExpiryDate:      OrderData.ExpiryDate,
		VipPetCoupon:    vipCoupon,
		SkuId:           cardData.SkuId,
		ShowCardState:   showCardState,
	}
	//购买轮播记录
	if vipCardState == 0 {
		if noticesRes, err := BuyNoticesList(); err != nil {
			glog.Errorf("查询购买记录异常：%s", err.Error())
			out.Message = err.Error()
			return
		} else {
			out.Data.NoticesList = noticesRes
		}
	}

	var onlyData []*models.VipCardEquity
	if err := DataCenterEngine.SQL("select vce.id,vce.equity_icon,vce.equity_name,vce.equity_copy,vce.equity_price,"+
		"vce.equity_type,vce.status,vce.jump_url,vce.equity_info,vce.equity_img,vce.equity_rule,vce.main_title,vce.sub_title "+
		"from datacenter.vip_card_equity_config vcec left join vip_card_equity vce on vcec.equity_id = vce.id where vcec.card_tid = ? "+
		"order by sort asc;", in.Id).Find(&onlyData); err != nil {
		out.Message = "查询卡权益数据异常" + err.Error()
		return
	}
	var saveMoney float64
	for i := range onlyData {
		date := onlyData[i]
		saveMoney += date.EquityPrice
		var resData = cc.VipEquityInfo{
			Id:          date.Id,
			EquityName:  date.EquityName,
			EquityCopy:  date.EquityCopy,
			Status:      date.Status,
			EquityIcon:  date.EquityIcon,
			EquityPrice: date.EquityPrice,
			EquityType:  date.EquityType,
			EquityImg:   date.EquityImg,
			EquityRule:  date.EquityRule,
			EquityInfo:  date.EquityInfo,
			JumpUrl:     date.JumpUrl,
			MainTitle:   date.MainTitle,
			SubTitle:    date.SubTitle,
		}
		out.Data.Data = append(out.Data.Data, &resData)
	}
	out.Data.SaveMoney = saveMoney
	out.Code = 200
	return
}

func BuyNoticesList() (data []*cc.BuyNoticesList, e error) {
	type vipCardOrderMap struct {
		UserName string    `json:"user_name"`
		PayTime  time.Time `json:"create_time"`
	}
	var onlyData []*vipCardOrderMap
	if err := DataCenterEngine.Table("vip_card_order").Where("state=10 and user_name !=''").GroupBy("user_name").
		Limit(20).OrderBy("id desc").Find(&onlyData); err != nil {
		return nil, err
	}
	if len(onlyData) > 0 {
		for i := range onlyData {
			date := onlyData[i]
			maskedSurname := date.UserName
			chars := []rune(date.UserName)
			if len(chars) > 2 {
				maskedSurname = fmt.Sprintf("%c%s%c", chars[0], "*", chars[len(chars)-1])
			} else {
				maskedSurname = string(chars[0])
			}
			var resData = cc.BuyNoticesList{
				UserName: maskedSurname,
				LastTime: int32(getRandDuration()),
			}
			data = append(data, &resData)
		}
	}

	for len(data) < 20 {
		newUser := &cc.BuyNoticesList{
			UserName: getRandUserName(),
			LastTime: int32(getRandDuration()),
		}
		data = append(data, newUser)
	}

	return data, nil
}

// 获取随机时间间隔
func getRandDuration() int {
	rand.Seed(time.Now().UnixNano())
	time.Sleep(1)
	return rand.Intn(45) + 15
}

// 随机生成姓名
func getRandUserName() string {
	type OrderMain struct {
		MemberName string `json:"member_name"`
	}
	var memberName []*OrderMain
	var surnames []string
	keyName := "customer-center:RandUserName"
	r := GetRedisConn()
	defer r.Close()
	memberNameStr := r.Get(keyName).Val()
	if memberNameStr == "" {
		rand.Seed(time.Now().UnixNano())
		var ids []int
		for i := 1; i <= 50; i++ {
			num := rand.Intn(11460080) + 1
			ids = append(ids, num)
		}
		if err := NewBeijingPetEngine().Table("t_scrm_user_info").Where("user_name != ''").Select("user_name as member_name").
			In("id", ids).
			Find(&memberName); err != nil {
			glog.Error("随机查询订单用户名错误：" + err.Error())
			return err.Error()
		}
		if len(memberName) > 0 {
			for _, v := range memberName {
				surnames = append(surnames, v.MemberName)
			}
			mJson, _ := json.Marshal(surnames)
			r.Set(keyName, string(mJson), time.Hour*1)
		}
	} else {
		if err := json.Unmarshal([]byte(memberNameStr), &surnames); err != nil {
			glog.Error(err)
		}
	}

	rand.Seed(time.Now().UnixNano())
	index := rand.Intn(len(surnames))
	surname := surnames[index]
	chars := []rune(surname)
	var maskedSurname string
	if len(chars) > 2 {
		maskedSurname = fmt.Sprintf("%c%s%c", chars[0], "*", chars[len(chars)-1])
	} else {
		maskedSurname = string(chars[0])
	}
	return maskedSurname
}

// 我的福利
func (v *VipCardService) GetWelfareList(ctx context.Context, in *cc.BaseCardOrderRequest) (out *cc.GetWelfareResponse, e error) {
	out = &cc.GetWelfareResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetWelfareList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()
	if in.UserId == "" {
		out.Message = "用户Id不能为空"
		return
	}

	//免费会员福利 v0等级以上才显示
	member := models.UpetMember{}
	if has, err := BbcEngine.Table("upet_member").Where("scrm_user_id=?", in.UserId).Get(&member); err != nil {
		out.Message = "查询用户信息异常"
		return
	} else if !has {
		out.Message = "查询用户不存在"
		return
	}

	if member.UserLevelId > 0 {
		//查询用户等级是否有配置
		wg := new(sync.WaitGroup)
		wg.Add(2)
		go func() {
			defer wg.Done()
			var id int32
			if has, err := DataCenterEngine.SQL("select id from user_level where FIND_IN_SET('2', privilege_ids) and "+
				"level_id = ?", member.UserLevelId).Get(&id); err != nil {
				out.Message = "查询用户信息异常"
				return
			} else if has {
				var StoreVoucher = make([]*cc.CouponList, 0)
				var shopVoucher = getVoucherByUserid(in.UserId, 2)
				var state int32 = 1 //默认 去查看
				var unstate int
				for i := range shopVoucher {
					date := shopVoucher[i]
					if date.Status == 1 {
						state = 0
					} else if date.Status > 2 {
						unstate++
					}
					res := &cc.CouponList{
						CouponId:             date.CouponId,
						CouponName:           date.CouponName,
						Status:               date.Status, //券状态 1未领取 2已领取 3已失效 4已过期 5已抢光
						VoucherTPrice:        date.VoucherTPrice,
						VoucherTLimit:        date.VoucherTLimit,
						ApplicableScope:      date.ApplicableScope,
						VoucherDays:          date.VoucherDays,
						VoucherStartDateText: strings.ReplaceAll(date.VoucherStartDateText, "-", "."),
						VoucherEndDateText:   strings.ReplaceAll(date.VoucherEndDateText, "-", "."),
					}
					StoreVoucher = append(StoreVoucher, res)
				}
				if len(StoreVoucher) == unstate {
					state = 2
				}
				var res1 = &cc.WelfareInfo{
					Type:          1,
					Name:          "商品礼券",
					StoreVouchers: StoreVoucher,
					State:         state, //0-待领取 1-已领取 2-已过期
				}
				out.FreeData = append(out.FreeData, res1)
			}
		}()
		go func() {
			defer wg.Done()
			var pid int32
			if has, err := DataCenterEngine.SQL("select id from user_level where FIND_IN_SET('4', privilege_ids) and "+
				"level_id = ?", member.UserLevelId).Get(&pid); err != nil {
				out.Message = "查询用户信息异常"
				return
			} else if has {
				var couponList = getVoucherByUserid(in.UserId, 1)
				var CouponList = make([]*cc.CouponList, 0)
				var state int32
				var unstate int

				for i := range couponList {
					date := couponList[i]
					if date.Status == 2 {
						state = 1
					} else if date.Status > 2 {
						unstate++
					}
					if date.Type == 1 {
						date.VoucherDays = 0
					} else {
						date.VoucherStartDateText = ""
						date.VoucherEndDateText = ""
					}
					CouponListRes := &cc.CouponList{
						CouponId:             date.CouponId,
						CouponName:           date.CouponName,
						Status:               date.Status,
						VoucherTPrice:        date.VoucherTPrice,
						VoucherTLimit:        date.VoucherTLimit,
						VoucherDays:          date.VoucherDays,
						VoucherStartDateText: date.VoucherStartDateText,
						VoucherEndDateText:   date.VoucherEndDateText,
					}
					CouponList = append(CouponList, CouponListRes)
				}
				if len(CouponList) == unstate {
					state = 2
				}
				var res2 = &cc.WelfareInfo{
					Type:       2,
					Name:       "到店礼券",
					CouponList: CouponList,
					State:      state,
				}
				out.FreeData = append(out.FreeData, res2)
			}
		}()
		wg.Wait()
		var res = &cc.WelfareInfo{
			Name: "积分兑换",
			Type: 3,
		}
		out.FreeData = append(out.FreeData, res)
	}

	//付费会员福利，判断是否开通
	//权益类型：1-商城优惠券，2-子龙门店券，3-积分翻倍，4-会员价商品，5-大牌礼包，6-家庭服务包 7-宠物医保链接 8-子龙折扣卡 9-会员特价商品 10-医保价商品
	if member.VipCardState == 1 {
		//查询用户开通卡所属大区
		OrderData := OrderCardMap{}
		if _, err := DataCenterEngine.SQL("select o.id,o.order_sn,o.expiry_date,o.create_time,o.card_id,o.user_id,o.en_user_mobile,"+
			"vct.or_id from vip_card_order o left join vip_card_template vct on o.card_id = vct.id where o.order_sn=? and o.state = 10 and "+
			"o.expiry_date > now() and o.user_id = ?;", in.OrderSn, in.UserId).Get(&OrderData); err != nil {
			out.Message = "查询用户开卡信息异常"
			return
		}
		c := carbon.SetLocation(time.Local)
		YearTimeStr := c.Parse(OrderData.CreateTime).AddMonthsNoOverflow(11).ToDateTimeString()
		//查询卡相关券信息
		var orderInfo []UserOrder
		if err := DataCenterEngine.SQL(
			"SELECT vco.order_sn,"+
				"vce.equity_type,vce.equity_short_name as equity_name,"+
				"vuer.equity_id,vuer.privilege_id,vuer.status,vuer.create_time,vco.create_time order_time,vco.collection_type,vco.source "+
				"FROM vip_card_order vco "+
				"LEFT JOIN vip_user_equity_record vuer ON vco.order_sn = vuer.order_sn "+
				"LEFT JOIN vip_card_equity vce ON vuer.equity_id = vce.id "+
				"WHERE vco.order_sn=? AND vco.state=10 AND vco.expiry_date>NOW() AND vco.user_id=? AND vco.create_time<?", in.OrderSn, in.UserId, YearTimeStr).
			Find(&orderInfo); err != nil {
			out.Message = "查询订单权益信息异常" + err.Error()
			return
		}

		if len(orderInfo) > 0 {
			var (
				GiftState         int32 //开卡礼包状态
				ServiceState      int32 //服务包状态
				CouponState       int32 //宠物体验券状态
				shopState         int32 //门店专享状态
				DoctorGiftState   int32 //医疗礼包券状态
				DoctorState       int32 //医疗权益券状态
				PetInsuranceState int32 //宠物医保状态
				MonthState        int32 //月底领券状态
			)
			c := carbon.SetLocation(time.Local)
			//判断是否在当前领取周期内的数据
			//订单创建时间的所在月
			orderYear := c.Parse(OrderData.CreateTime).Year()
			orderMonth := c.Parse(OrderData.CreateTime).Month()
			var couponMap = make(map[string]bool, 0)
			// 转人orderInfo[0].CollectionType
			collectionType := strconv.FormatInt(int64(orderInfo[0].CollectionType), 10)
			for _, v := range orderInfo {

				switch v.EquityType {
				case 2:
					if v.EquityName == "月度返券" {
						//判断是否在当前领取周期内的数据
						//订单创建时间的所在月
						//当前所在月
						now := c.Now()
						nowYear := now.Year()
						nowMonth := now.Month()
						diffMonth := nowMonth - orderMonth + ((nowYear - orderYear) * 12)
						beginadd := 0
						endadd := 0
						addAfterTime := now.Parse(OrderData.CreateTime).AddMonthsNoOverflow(diffMonth)
						if now.ToStdTime().Before(addAfterTime.ToStdTime()) {
							beginadd = -1
						} else {
							endadd = 1
						}

						beTime := c.Parse(OrderData.CreateTime).AddMonthsNoOverflow(diffMonth + beginadd).ToDateTimeString()
						endTime := c.Parse(OrderData.CreateTime).AddMonthsNoOverflow(diffMonth + endadd).ToDateTimeString()
						if v.CreateTime < beTime || v.CreateTime >= endTime {
							//glog.Info("不存在 " + in.UserId + " order_sn:" + OrderData.OrderSn + " EquityId:" + cast.ToString(v.EquityId) + " PrivilegeId" + v.PrivilegeId)
							continue
						} else {
							glog.Info("存在 "+in.UserId, v)
							MonthState = 1
						}

					}

					CouponState = 1
					couponKey := fmt.Sprintf("%d%d%s", OrderData.Id, v.EquityId, v.PrivilegeId)
					couponMap[couponKey] = true
					if v.EquityName == "医疗礼包" {
						DoctorGiftState = 1
					} else if v.EquityName == "医疗权益" {
						DoctorState = 1
					}
				case 5:
					GiftState = 1
				case 6:
					ServiceState = 1
				case 8:
					couponMap[v.PrivilegeId] = true
				}
			}
			//** 针对浙闽二区显示：门店专享、医疗礼包、医疗权益
			if OrderData.OrId == 13 {
				wg := new(sync.WaitGroup)
				wg.Add(4)

				var res1 = &cc.WelfareInfo{}
				var res2 = &cc.WelfareInfo{}
				var res3 = &cc.WelfareInfo{}
				var res4 = &cc.WelfareInfo{}
				go func(shopState int32) {
					defer wg.Done()
					var equityConfig []*EquityConfig
					if err := DataCenterEngine.SQL("SELECT c.privilege_ids,c.receive_num,e.equity_name,e.equity_icon,e.equity_receive_icon,e.equity_copy "+
						"FROM vip_card_equity_config c "+
						"LEFT JOIN vip_card_equity e ON c.equity_id=e.id "+
						"WHERE e.equity_type=8 AND c.or_id=? AND FIND_IN_SET(?,e.collection_ids)", OrderData.OrId, collectionType).Find(&equityConfig); err != nil {
						out.Message = "查询门店专享券异常" + err.Error()
						return
					}
					if len(equityConfig) > 0 {
						var Coupon = make([]*cc.CouponList, 0)
						var hasCouponMap = make(map[string][]*EquityConfig, 0)

						for _, v := range equityConfig {
							hasCouponMap[v.PrivilegeIds] = append(hasCouponMap[v.PrivilegeIds], v)
						}
						var hasCount int
						for k, v := range hasCouponMap {
							var (
								equityName []string
								id         string
								State      int32
							)
							for _, val := range v {
								equityName = append(equityName, val.EquityName)
								id = val.PrivilegeIds
							}
							if couponMap[k] {
								hasCount++
								State = 1
							}
							CouponRes := &cc.CouponList{
								CouponId:   id,
								CouponName: strings.Join(equityName, ","),
								Status:     State,
							}
							Coupon = append(Coupon, CouponRes)
						}
						if len(hasCouponMap) == hasCount {
							shopState = 1
						}
						res1 = &cc.WelfareInfo{
							Name:              "门店专享",
							State:             shopState,
							Type:              7,
							CouponList:        Coupon,
							EquityIcon:        equityConfig[0].EquityIcon,
							EquityReceiveIcon: equityConfig[0].EquityReceiveIcon,
							EquityCopy:        equityConfig[0].EquityCopy,
						}
					}
				}(shopState)

				//医疗礼包
				go func(DoctorGiftState int32) {
					defer wg.Done()
					var equityConfig1 EquityConfig

					// 添加销售方式的筛选
					source := orderInfo[0].Source
					salesType := SalesTypeMap[source]
					if has, err := DataCenterEngine.SQL("SELECT GROUP_CONCAT(v.privilege_id) AS privilege_ids,c.receive_num,c.equity_id,e.equity_icon,e.equity_receive_icon,e.equity_copy "+
						"FROM vip_card_equity_config c "+
						"LEFT JOIN vip_card_equity e ON c.equity_id=e.id "+
						"LEFT JOIN vip_card_equity_value v ON v.card_tid=c.card_tid AND v.or_id=c.or_id AND v.equity_id=c.equity_id "+
						"WHERE e.equity_type=2 AND c.or_id=? AND FIND_IN_SET(?,e.collection_ids) AND e.equity_short_name='医疗礼包' AND v.sales_type=? "+
						"GROUP BY c.card_tid,c.or_id,c.equity_id", OrderData.OrId, collectionType, salesType).
						Get(&equityConfig1); err != nil {
						out.Message = "查询医疗礼包券异常"
						return
					} else if has {
						var Coupon1 = make([]*cc.CouponList, 0)
						if equityConfig1.PrivilegeIds != "" {
							DoctorGiftState, Coupon1 = GetZiLongCoupon(equityConfig1, OrderData.Id, DoctorGiftState, couponMap)
						}
						res2 = &cc.WelfareInfo{
							Name:              "医疗礼包",
							State:             DoctorGiftState,
							Type:              8,
							CouponList:        Coupon1,
							EquityIcon:        equityConfig1.EquityIcon,
							EquityReceiveIcon: equityConfig1.EquityReceiveIcon,
							EquityCopy:        equityConfig1.EquityCopy,
						}
					}
				}(DoctorGiftState)

				//医疗权益
				go func(DoctorState int32) {
					defer wg.Done()
					var equityConfig2 EquityConfig
					if has, err := DataCenterEngine.SQL("SELECT c.privilege_ids,c.receive_num,c.equity_id,e.equity_icon,e.equity_receive_icon,e.equity_copy "+
						"FROM vip_card_equity_config c "+
						"LEFT JOIN vip_card_equity e ON c.equity_id=e.id "+
						"WHERE e.equity_type=2 AND c.or_id=? AND FIND_IN_SET(?,e.collection_ids) AND e.equity_short_name='医疗权益'", OrderData.OrId, collectionType).
						Get(&equityConfig2); err != nil {
						out.Message = "查询医疗权益券异常"
						return
					} else if has {
						var Coupon2 = make([]*cc.CouponList, 0)
						if equityConfig2.PrivilegeIds != "" {
							DoctorState, Coupon2 = GetZiLongCoupon(equityConfig2, OrderData.Id, DoctorState, couponMap)
						}
						res3 = &cc.WelfareInfo{
							Name:              "医疗权益",
							State:             DoctorState,
							Type:              9,
							CouponList:        Coupon2,
							EquityIcon:        equityConfig2.EquityIcon,
							EquityReceiveIcon: equityConfig2.EquityReceiveIcon,
							EquityCopy:        equityConfig2.EquityCopy,
						}
					}
				}(DoctorState)

				//月度反券
				go func(MonthState int32) {
					defer wg.Done()
					var equityConfig3 EquityConfig
					if has, err := DataCenterEngine.SQL("SELECT c.privilege_ids,c.receive_num,c.equity_id,e.equity_icon,e.equity_receive_icon,e.equity_copy "+
						"FROM vip_card_equity_config c "+
						"LEFT JOIN vip_card_equity e ON c.equity_id=e.id "+
						"WHERE e.equity_type=2 AND c.or_id=? AND FIND_IN_SET(?,e.collection_ids) AND e.equity_short_name='月度返券'", OrderData.OrId, collectionType).
						Get(&equityConfig3); err != nil {
						out.Message = "查询月度返券异常"
						return
					} else if has {
						var Coupon2 = make([]*cc.CouponList, 0)
						if equityConfig3.PrivilegeIds != "" {
							MonthState, Coupon2 = GetZiLongCoupon(equityConfig3, OrderData.Id, MonthState, couponMap)
						}
						res4 = &cc.WelfareInfo{
							Name:              "月度返券",
							State:             MonthState,
							Type:              11,
							CouponList:        Coupon2,
							EquityIcon:        equityConfig3.EquityIcon,
							EquityReceiveIcon: equityConfig3.EquityReceiveIcon,
							EquityCopy:        equityConfig3.EquityCopy,
						}
					}
				}(MonthState)
				wg.Wait()

				if res1.Name != "" {
					out.PaidData = append(out.PaidData, res1)
				}
				if res2.Name != "" {
					out.PaidData = append(out.PaidData, res2)
				}
				if res3.Name != "" {
					out.PaidData = append(out.PaidData, res3)
				}
				if res4.Name != "" {
					out.PaidData = append(out.PaidData, res4)
				}
			} else {
				var equityConfig1 EquityConfig
				if has, err := DataCenterEngine.SQL("SELECT e.equity_icon,e.equity_receive_icon,e.equity_copy "+
					"FROM vip_card_equity_config c "+
					"LEFT JOIN vip_card_equity e ON c.equity_id=e.id "+
					"WHERE e.equity_type=5 and c.or_id=? AND FIND_IN_SET(?,e.collection_ids);", OrderData.OrId, collectionType).
					Get(&equityConfig1); err != nil {
					out.Message = "查询开卡礼包异常"
					return
				} else if has {
					out.PaidData = append(out.PaidData, &cc.WelfareInfo{
						Name:              "开卡礼包",
						State:             GiftState,
						Type:              4,
						EquityIcon:        equityConfig1.EquityIcon,
						EquityReceiveIcon: equityConfig1.EquityReceiveIcon,
						EquityCopy:        equityConfig1.EquityCopy,
					})
				}

				// vip-2.0.1 增加一个福利模块 -- 健康服务金
				var equityConfig2 EquityConfig
				if has, err := DataCenterEngine.SQL("SELECT e.equity_icon,e.equity_receive_icon,e.equity_copy "+
					"FROM vip_card_equity_config c "+
					"LEFT JOIN vip_card_equity e ON c.equity_id=e.id "+
					"WHERE e.equity_type=10 AND c.or_id=? AND FIND_IN_SET(?,e.collection_ids);", OrderData.OrId, collectionType).
					Get(&equityConfig2); err != nil {
					out.Message = "查询健康服务金异常"
					return
				} else if has {
					out.PaidData = append(out.PaidData, &cc.WelfareInfo{
						Name:              "健康服务金",
						State:             1,
						Type:              12,
						Url:               OrderData.OrderSn,
						EquityIcon:        equityConfig2.EquityIcon,
						EquityReceiveIcon: equityConfig2.EquityReceiveIcon,
						EquityCopy:        equityConfig2.EquityCopy,
					})
				}

				//宠物医保
				var equityConfig3 EquityConfig
				if has, err := DataCenterEngine.SQL("SELECT e.expiry_day,e.equity_icon,e.equity_receive_icon,e.equity_copy "+
					"FROM vip_card_equity_config c "+
					"LEFT JOIN vip_card_equity e ON c.equity_id=e.id "+
					"WHERE e.equity_type=7 and c.or_id=? AND FIND_IN_SET(?,e.collection_ids);", OrderData.OrId, collectionType).
					Get(&equityConfig3); err != nil {
					out.Message = "查询医疗礼包券异常"
					return
				} else if has {
					var (
						petInsuranceUrl string //保险链接：未投保返回链接，有保单号则返回
					)
					type InsuranceMap struct {
						InsurancePolicyNumber string //保单号
						Status                int32  //状态:1-投保中 2-投保成功 3-投保失败(重新投保) 4-已失效(过期不能投保)
					}
					var insuranceMap InsuranceMap
					if has, err = DataCenterEngine.SQL(`select pp.insurance_policy_number,r.status from member_pet_property pp 
    inner join vip_user_equity_record r on pp.insurance_order_number = r.gift_order_sn where r.order_sn =? order by pp.id desc limit 1;`, OrderData.OrderSn).Get(&insuranceMap); err != nil {
						out.Message = "查询保单信息异常"
						return
					} else if has {
						PetInsuranceState = insuranceMap.Status
						petInsuranceUrl = insuranceMap.InsurancePolicyNumber
					}
					if PetInsuranceState == 0 {
						location, _ := time.ParseInLocation(utils.DATE_TIME_LAYOUT, OrderData.CreateTime, time.Local)
						add := location.AddDate(0, equityConfig3.ExpiryDay, 0)
						now := time.Now()
						if now.After(add) {
							PetInsuranceState = 4
						}
					}

					if PetInsuranceState == 0 || PetInsuranceState == 3 {
						petInsuranceUrl = config.GetString("petInsurance_url")
						mobile := utils.MobileDecrypt(OrderData.EnUserMobile)
						// vip-2.0.1 增加sign字段进行加密验签操作。HmacSHA256   文档：https://zhuanlan.zhihu.com/p/627953393?utm_id=0
						//生产环境：f0p2I6MbZ5iwhdAzpOnGkHssE32Nb1W3
						//测试环境和uat环境：h70PzIrW9xtGgKP5RZ5iwhE32Nb1WdAz
						kkk := "f0p2I6MbZ5iwhdAzpOnGkHssE32Nb1W3"
						env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
						if env != "production" && env != "pro" {
							kkk = "h70PzIrW9xtGgKP5RZ5iwhE32Nb1WdAz"
						}
						glog.Info("加签密钥：", kkk, "env:", env)
						sign := utils.HmacSha256ToBase64(kkk, fmt.Sprintf("%s_%s", OrderData.UserId, OrderData.OrderSn))
						petInsuranceUrl = fmt.Sprintf("%s?channelCode=XRP20230531&productCode=AXK20230531&userPhone=%s&outUserId=%s_%s&sign=%s", petInsuranceUrl, mobile, OrderData.UserId, OrderData.OrderSn, sign)
					}
					var res4 = &cc.WelfareInfo{
						Name:              "宠物医保",
						State:             PetInsuranceState,
						Type:              10,
						Url:               petInsuranceUrl,
						EquityIcon:        equityConfig3.EquityIcon,
						EquityReceiveIcon: equityConfig3.EquityReceiveIcon,
						EquityCopy:        equityConfig3.EquityCopy,
					}
					out.PaidData = append(out.PaidData, res4)
				}

				// 服务包
				var equityConfig4 EquityConfig
				if has, err := DataCenterEngine.SQL("SELECT e.equity_type,e.equity_icon,e.equity_receive_icon,e.equity_copy "+
					"FROM vip_card_equity_config c "+
					"LEFT JOIN vip_card_equity e ON c.equity_id=e.id "+
					"WHERE e.equity_type=6 and c.or_id=? AND FIND_IN_SET(?,e.collection_ids);", OrderData.OrId, collectionType).
					Get(&equityConfig4); err != nil {
					out.Message = "查询家医服务异常"
					return
				} else if has {
					out.PaidData = append(out.PaidData, &cc.WelfareInfo{
						Name:              "家医服务",
						State:             ServiceState,
						Type:              6,
						EquityIcon:        equityConfig4.EquityIcon,
						EquityReceiveIcon: equityConfig4.EquityReceiveIcon,
						EquityCopy:        equityConfig4.EquityCopy,
					})
				}

				//宠物检验券
				var equityConfig5 EquityConfig
				if has, err := DataCenterEngine.SQL("SELECT c.privilege_ids,c.receive_num,c.equity_id,e.equity_icon,e.equity_receive_icon,e.equity_copy "+
					"FROM vip_card_equity_config c "+
					"LEFT JOIN vip_card_equity e ON c.equity_id=e.id "+
					"WHERE e.equity_type=2 AND c.or_id=? AND FIND_IN_SET(?,e.collection_ids)", OrderData.OrId, collectionType).
					Get(&equityConfig5); err != nil {
					out.Message = "查询宠物体检券异常"
					return
				} else if has {
					var Coupon = make([]*cc.CouponList, 0)
					if equityConfig5.PrivilegeIds != "" {
						CouponState, Coupon = GetZiLongCoupon(equityConfig5, OrderData.Id, CouponState, couponMap)
					}
					out.PaidData = append(out.PaidData, &cc.WelfareInfo{
						Name:              "宠物体检券",
						State:             CouponState,
						Type:              5,
						CouponList:        Coupon,
						EquityIcon:        equityConfig5.EquityIcon,
						EquityReceiveIcon: equityConfig5.EquityReceiveIcon,
						EquityCopy:        equityConfig5.EquityCopy,
					})
				}
			}
		}
	}
	out.Code = 200
	return
}

// GetZiLongCoupon 查询子龙门店券显示状态
func GetZiLongCoupon(EquityData EquityConfig, Oid, CouponState int32, couponMap map[string]bool) (couponState int32, CouponList []*cc.CouponList) {
	var (
		CouponData = make([]*cc.CouponList, 0)
		unState    int32 //过期数量
		hasState   int32 //领取个数统计
		//beforeState int32
		//nid         []string
	)

	ids := strings.Split(EquityData.PrivilegeIds, ",")

	//先判断是否已经领取个数是否已经领取完,领取完只返回已领取的券即可
	//for _, vid := range ids {
	//	couponKey := fmt.Sprintf("%d%d%s", Oid, EquityData.EquityId, vid)
	//	if couponMap[couponKey] {
	//		nid = append(nid, vid)
	//		beforeState++
	//	}
	//}
	//if beforeState >= EquityData.ReceiveNum {
	//	ids = nid
	//}

	for _, vid := range ids {
		_, template, err := zilong.CouponTemplateDetail(cast.ToInt32(vid))
		if err != nil {
			glog.Error("获取门店券接口异常" + err.Error())
			continue
		} else if template == nil {
			glog.Error("获取门店券无记录," + vid)
			continue
		}
		//有效期
		var periodValidityInfo PeriodValidityInfo
		err = json.Unmarshal([]byte(template.PeriodValidity), &periodValidityInfo)
		if err != nil {
			glog.Error("获取门店券有效期解析失败" + template.PeriodValidity)
			continue
		}
		if periodValidityInfo.Type == 1 {
			periodValidityInfo.PeriodValidity = 0
		} else {
			periodValidityInfo.BeginTime = ""
			periodValidityInfo.EndTime = ""
		}
		CouponRes := &cc.CouponList{
			CouponId:             cast.ToString(vid),
			CouponName:           template.TemplateName,
			VoucherTPrice:        cast.ToInt32(kit.YuanToFen(cast.ToFloat64(template.TemplateValue))),
			VoucherDays:          periodValidityInfo.PeriodValidity,
			VoucherStartDateText: strings.ReplaceAll(periodValidityInfo.BeginTime, "-", "."),
			VoucherEndDateText:   strings.ReplaceAll(periodValidityInfo.EndTime, "-", "."),
			Type:                 periodValidityInfo.Type,
			EquityId:             EquityData.EquityId,
		}
		//判断是否领取
		couponKey := fmt.Sprintf("%d%d%s", Oid, EquityData.EquityId, vid)
		if couponMap[couponKey] {
			CouponRes.Status = 2
			hasState++
		} else {
			if template.Status == "已过期" { //判断是否过期
				unState++
				CouponRes.Status = 4
			} else if template.Status == "待提交" || template.Status == "审核中" { //判断是否已失效
				unState++
				CouponRes.Status = 3
			} else if template.Inventory <= 0 { //判断是否已抢光
				unState++
				CouponRes.Status = 5
			} else { //未领取
				CouponRes.Status = 1
				CouponState = 0
			}
		}
		CouponData = append(CouponData, CouponRes)
	}
	//领取个数大于等于配置个数时，返回失效状态
	for _, v := range CouponData {
		if hasState == EquityData.ReceiveNum && v.Status == 1 {
			v.Status = 3
		}
	}
	//如果无效的券数 == 配置的券数，则返回无效状态
	if int32(len(ids)) == unState {
		CouponState = 2
	} else if hasState >= EquityData.ReceiveNum || (unState+hasState) == EquityData.ReceiveNum {
		//如果用户领取数量>=配置的领取个数 || 用户领取+无效的券数=配置的领取个数的情况，则返回查看状态
		CouponState = 1
	}
	return CouponState, CouponData
}

// GetGift 开卡已领取显示
func (v *VipCardService) GetGiftInfo(ctx context.Context, in *cc.BaseCardOrderRequest) (out *cc.GetGiftInfoResponse, e error) {
	out = &cc.GetGiftInfoResponse{Code: 400}
	defer func() {
		if out.Code != 200 {
			glog.Info("GetGiftInfo 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	var orderGiftInfo models.VipOrderGiftExtends
	if _, err := DataCenterEngine.SQL("select g.id,g.pack_desc,g.pack_sku_id,g.pack_name,g.pack_price,g.pack_image,vuer.gift_order_sn from vip_card_order vco "+
		"left join vip_user_equity_record vuer on vco.order_sn = vuer.order_sn "+
		"left join vip_card_equity vce on vuer.equity_id = vce.id "+
		"left join vip_card_gift g on vuer.privilege_id = g.pack_sku_id and g.state in (1,2) "+
		"where vco.order_sn=? AND vco.state =10 and vco.expiry_date > now() and vce.equity_type = 5 and vco.user_id = ?", in.OrderSn, in.UserId).
		Get(&orderGiftInfo); err != nil {
		out.Message = "查询信息异常"
		return
	}

	if orderGiftInfo.Id > 0 {

		orderSn := ""
		if _, err := OrderEngine.SQL(" SELECT order_sn FROM dc_order.order_main o WHERE o.old_order_sn=? AND o.parent_order_sn ='' ", orderGiftInfo.GiftOrderSn).
			Get(&orderSn); err != nil {
			out.Message = "查询信息异常"
			return
		}
		orderGiftInfo.OrderSn = orderSn

		if orderGiftInfo.OrderSn != "" {
			var orderSn string
			if _, err := OrderEngine.SQL("select order_sn from dc_order.order_main where parent_order_sn = ?", orderGiftInfo.OrderSn).Get(&orderSn); err != nil {
				out.Message = "查询礼包子订单信息异常"
				return
			}
			orderGiftInfo.OrderSn = orderSn
		}
		out.Data = &cc.GiftInfo{
			Id:        orderGiftInfo.Id,
			PackName:  orderGiftInfo.PackName,
			PackDesc:  orderGiftInfo.PackDesc,
			PackPrice: orderGiftInfo.PackPrice,
			PackImage: orderGiftInfo.PackImage,
			PackSkuId: orderGiftInfo.PackSkuId,
			OrderSn:   orderGiftInfo.OrderSn,
		}
	}

	out.Code = 200
	return
}

// GetUserInfo 提供接口给子龙
func (v *VipCardService) GetUserInfo(ctx context.Context, in *cc.GetUserInfoRequest) (out *cc.GetUserInfoResponse, e error) {
	out = &cc.GetUserInfoResponse{Code: 400}
	defer func() {
		glog.Info("GetUserInfo 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()
	var vipCardState int32
	if has, err := BbcEngine.SQL("select vip_card_state from upet_member where member_mobile = ?;", in.Mobile).Get(&vipCardState); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	} else if !has {
		out.Code = 200
		out.Message = "查询数据不存在"
		return
	}
	out.Code = 200
	out.VipCardState = vipCardState
	return
}

func CheckOrgId(OrId string) bool {
	//判断大区id
	numMap := make(map[int32]bool)
	for _, num := range OrIds {
		numMap[num] = true
	}
	targets := strings.FieldsFunc(OrId, func(r rune) bool {
		return r == ',' || r == '，'
	})
	for _, target := range targets {
		if _, ok := numMap[cast.ToInt32(target)]; !ok {
			return false
		}
	}
	return true
}

// 给子龙提供权益价值查询
func (v *VipCardService) CardValue(ctx context.Context, in *cc.CardValueReq) (out *cc.CardValueRes, e error) {
	out = &cc.CardValueRes{Code: 400}
	defer func() {
		glog.Info("CardValue 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	var CardValueData = make([]*cc.CardValueData, 0)
	if err := DataCenterEngine.SQL("select vce.equity_name,vcev.privilege_id as coupon_code,vcev.free_quality from vip_card_order vco "+
		"inner join vip_card_equity_value vcev on vcev.card_tid = vco.card_id left join vip_card_equity vce on vce.id = vcev.equity_id "+
		"where vco.state =10 and vco.expiry_date > now() and vco.user_id = ? and vco.card_id =? and vce.equity_type=2;", in.ScrmId, in.CardId).
		Find(&CardValueData); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return
	}
	out.Data = CardValueData
	out.Code = 200
	return
}

// CardOrderValue 查询会员卡下已使用子龙门店券赠送价值
func (v *VipCardService) CardOrderValue(ctx context.Context, in *cc.CardOrderValueReq) (out *cc.CardOrderValueResp, e error) {
	out = &cc.CardOrderValueResp{Code: 400}
	defer func() {
		glog.Info("CardOrderValue 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	var value = make([]*models.CardOrderValue, 0)

	sql := `SELECT vco.order_sn,vcev.equity_id,vcev.sales_type,vcev.free_quality,vuer.gift_order_sn,vce.equity_name
	FROM vip_card_order vco
    LEFT JOIN vip_card_equity_value vcev ON vcev.card_tid=vco.card_id
    LEFT JOIN vip_user_equity_record vuer ON vuer.order_sn=vco.order_sn AND vuer.equity_id=vcev.equity_id AND vuer.privilege_id=vcev.privilege_id
    LEFT JOIN vip_card_equity vce ON vce.id=vuer.equity_id
	WHERE vco.order_sn IN ('%s') AND vcev.or_id=13 AND vce.equity_type=2;`
	fullSql := fmt.Sprintf(sql, strings.Join(in.OrderSn, "','"))
	if err := DataCenterEngine.SQL(fullSql).Find(&value); err != nil {
		out.Message = "查询数据异常" + err.Error()
		return out, err
	}
	if len(value) == 0 {
		out.Message = "查询数据异常，没有找到对应的会员卡订单记录，order_sn=" + kit.JsonEncode(in.OrderSn)
		return out, errors.New("查询数据异常，没有找到对应的会员卡订单记录")
	}

	// 拼装券码数组，调用子龙接口，查询门店券使用情况
	var couponCodes []string
	if len(value) > 0 {
		for _, v := range value {
			couponCodes = append(couponCodes, v.GiftOrderSn)
		}
	}

	defer func() {
		glog.Info("GetCouponStatus 入参：", kit.JsonEncode(couponCodes))
	}()
	client := et.GetExternalClient()
	resp, err := client.ZiLong.GetCouponStatus(client.Ctx, &et.GetCouponStatusReq{
		UsercouponCodes: couponCodes,
	})
	if err != nil || len(resp.Data) == 0 {
		glog.Error("获取门店券使用状态数据异常：err=%v")
		return out, err
	}

	// 过滤掉未使用的门店券
	var data = make([]*cc.CardOrderValueData, 0)
	var usedCouponCodes []string
	for _, d := range resp.Data {
		if d.Status == 2 && d.IsRefund == 0 {
			usedCouponCodes = append(usedCouponCodes, d.CouponCode)
		}
	}
	if len(usedCouponCodes) == 0 {
		glog.Error("查询会员卡下已使用子龙门店券赠送价值，请求子龙门店券使用状态，没有找到已使用的券记录")
		return out, errors.New("没有已使用的门店券记录")
	}

	// 计算订单下的赠送价值总和
	couponCodeStr := strings.Join(usedCouponCodes, ",")
	var snEquityFreeMap = make(map[string]int32, 0)
	for _, v := range value {
		// 医疗礼包只计算充值赠送时的赠送价值
		if v.EquityId == 7 && v.SalesType != 2 {
			continue
		}

		if strings.Contains(couponCodeStr, v.GiftOrderSn) {
			if _, has := snEquityFreeMap[fmt.Sprintf("%s_%s", v.OrderSn, v.EquityName)]; has {
				snEquityFreeMap[fmt.Sprintf("%s_%s", v.OrderSn, v.EquityName)] += v.FreeQuality
			} else {
				snEquityFreeMap[fmt.Sprintf("%s_%s", v.OrderSn, v.EquityName)] = v.FreeQuality
			}
		}
	}

	if len(snEquityFreeMap) > 0 {
		index := 0
		var snMap = make(map[string]int, 0)
		for snEquity, freeQuality := range snEquityFreeMap {
			key := strings.Split(snEquity, "_")
			orderSn := key[0]
			equityName := key[1]

			var valueData *cc.CardOrderValueData
			if ind, has := snMap[orderSn]; has {
				valueData = data[ind]
			} else {
				valueData = &cc.CardOrderValueData{
					OrderSn: orderSn,
				}
				data = append(data, valueData)
				snMap[orderSn] = index
				index++
			}

			equity := new(cc.CardOrderEquity)
			equity.EquityName = equityName
			equity.FreeQuality = freeQuality
			valueData.Equity = append(valueData.Equity, equity)

		}
	}

	out.Data = data
	out.Code = 200
	return
}
