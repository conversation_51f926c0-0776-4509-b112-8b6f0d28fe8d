package services

import (
	"_/models"
	"_/proto/cc"
	"context"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
)

type SearchService struct {
}

func (service *SearchService) getDbEngine() *xorm.Engine {
	return NewEngine()
}

//搜索历史数据——创建
func (service *SearchService) CreateSearchHistory(ctx context.Context, req *cc.CreateSearchHistoryRequest) (*cc.CreateSearchHistoryResponse, error) {
	var response = &cc.CreateSearchHistoryResponse{
		Code:    200,
		Message: "搜索历史数据创建成功",
	}

	var engine = service.getDbEngine()

	searchHistoryModel := models.SearchHistory{}
	isExist, err := engine.Where("`user_id`=? and `key_word`=?", req.UserId, req.KeyWord).Get(&searchHistoryModel)
	if err != nil {
		glog.Error("CreateSearchHistory，get fail：", err)
		response.Code = 400
		response.Error = err.Error()
		response.Message = "搜索历史数据创建失败"
		return response, err
	}

	//存在即更新，不存在则创建
	if isExist {
		if _, err := engine.Exec(`update search_history set search_num = ? where id = ?`, searchHistoryModel.SearchNum+1, searchHistoryModel.Id); err != nil {
			glog.Error("CreateSearchHistory update fail:", err)
			response.Code = 400
			response.Error = err.Error()
			response.Message = "搜索历史数据创建失败"
			return response, err
		}
	} else {
		_, err := engine.Insert(&models.SearchHistory{
			UserId:  req.UserId,
			KeyWord: req.KeyWord,
		})
		if err != nil {
			glog.Error("CreateSearchHistory insert fail:", err)
			response.Code = 400
			response.Error = err.Error()
			response.Message = "搜索历史数据创建失败"
			return response, err
		}
	}

	return response, err
}
