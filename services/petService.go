package services

import (
	"_/models"
	"_/proto/cc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/encoding/gjson"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/panjf2000/ants"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// PetService 宠物模块
type PetService struct {
	BaseService
}

var AntsP *ants.Pool

func init() {
	AntsP, _ = ants.NewPool(50)
	//defer p.Release()
}

// GetPetList 获取宠物列表 预防医学项目废弃接口
func (p *PetService) GetPetList(ctx context.Context, req *cc.PetListRequest) (*cc.PetListResponse, error) {
	out := new(cc.PetListResponse)

	url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-url"), "/scrm-organization-api/pet/query")
	ZlBaseMap := map[string]interface{}{
		"criteria": map[string]interface{}{
			"userId": req.UserId,
		},
	}

	dataJson, _ := json.MarshalIndent(ZlBaseMap, "", "    ")

	glog.Info("查询用户宠物信息子龙接口(/scrm-organization-api/pet/query)请求参数(1)： ", string(dataJson))
	resData, err := utils.HttpPostZl(url, dataJson, "")
	glog.Info("查询用户宠物信息子龙接口(/scrm-organization-api/pet/query)返回结果：", string(resData), "接口参数：", string(dataJson))
	if err != nil {
		return nil, err
	}

	petList := new(models.PetListResponse)
	err = gjson.DecodeTo(resData, petList)
	if err != nil {
		return nil, err
	}

	if petList.StatusCode != 200 {
		return nil, errors.New("查询信息错误")
	}

	wg := sync.WaitGroup{}
	for _, v := range petList.Result {
		//if v.PetStatus !=0{
		//	continue
		//}
		wg.Add(1)
		v := v
		go func() {
			defer func() {
				wg.Done()
			}()

			format := "2006-01-02 15:04:05"
			birthdayTime, _ := time.Parse(format, v.PetBirthday)
			month, day, isWholeMonth := GetAge(time.Now(), birthdayTime)
			if v.PetKindof == 1000 {
				v.AgeStr, v.AgeConversionStr = catAgeConvert(month, day, isWholeMonth)
			} else if v.PetKindof == 1001 {
				v.AgeStr, v.AgeConversionStr = dogAgeConvert(month, day, isWholeMonth)
			} else {
				v.AgeStr, _ = dogAgeConvert(month, day, isWholeMonth)
			}

			if v.PetKindof == 1000 || v.PetKindof == 1001 {
				if v.PetNeutering != 1 {
					v.NeuteringRemind = 1
				}
				expellingParasiteRecord, err := p.VaccinateRecord(v.PetId, 2)
				if err == nil {
					v.ExpellingParasiteRemind = 1
					v.LastExpellingParasiteRecord = expellingParasiteRecord
					if v.LastExpellingParasiteRecord != nil && len(v.LastExpellingParasiteRecord.OperationDate) > 0 {
						if len(v.LastExpellingParasiteRecord.OperationDate) > 0 {
							if importantRemind(v.LastExpellingParasiteRecord.OperationDate, 0, 0, 60) == 1 {
								v.ExpellingParasiteRemind = 0
							}
						}
					}
				}

				immunityRecord, err := p.VaccinateRecord(v.PetId, 1)
				if err == nil {
					v.ImmunityRemind = 1
					v.LastImmunityRecord = immunityRecord
					if v.LastImmunityRecord != nil && len(v.LastImmunityRecord.OperationDate) > 0 {
						if len(v.LastImmunityRecord.OperationDate) > 0 {
							if importantRemind(v.LastImmunityRecord.OperationDate, 1, 0, 0) == 1 {
								v.ImmunityRemind = 0
							}
						}
					}
				}
			}

			//v.DiagnosisRemind
			lastDiagnosisRecord, err := p.GetLastDiagnosisRecord(v.PetId)
			if err == nil {
				v.DiagnosisRemind = 0
				v.LastDiagnosisRecord = lastDiagnosisRecord
				if len(lastDiagnosisRecord.StartTime) > 0 {
					check := importantRemindV2(lastDiagnosisRecord.StartTime, 0, 2, 0)
					if check == 1 {
						v.DiagnosisRemind = 1
					} else {
						v.DiagnosisRemind = 2
					}
				}
			}

			if ((v.PetKindof == 1000 || v.PetKindof == 1001) && (v.ImmunityRemind == 1 || v.ExpellingParasiteRemind == 1 || v.NeuteringRemind == 1)) || v.DiagnosisRemind == 2 {
				v.SortWeight = v.SortWeight + 3
			}
			if v.PetKindof == 1000 {
				v.SortWeight = v.SortWeight + 2
			} else if v.PetKindof == 1001 {
				v.SortWeight = v.SortWeight + 1
			}

			out.Data = append(out.Data, v)
		}()
	}
	wg.Wait()

	//排序
	sort.Slice(out.Data, func(i, j int) bool {
		//根据排序权重排
		if out.Data[i].SortWeight != out.Data[j].SortWeight {
			return out.Data[i].SortWeight > out.Data[j].SortWeight
		}
		format := "2006-01-02 15:04:05"
		onePetTime, _ := time.Parse(format, out.Data[i].CreateTime)
		twoPetTime, _ := time.Parse(format, out.Data[j].CreateTime)

		//再进行创建时间倒序排序
		return onePetTime.Unix() < twoPetTime.Unix()
	})

	//out.Data = petList.Result
	//fmt.Println(kit.JsonEncode(out))
	return out, nil
}

// GetAge 计算年龄 岁+月
func GetAge(t1, t2 time.Time) (month, day, isWholeMonth int) {
	y1 := t1.Year()
	y2 := t2.Year()
	m1 := int(t1.Month())
	m2 := int(t2.Month())
	d1 := t1.Day()
	d2 := t2.Day()

	// 0大于 1等于 2小于
	isWholeMonth = 0
	// 获取月数差值
	month = (y1-y2)*12 + (m1 - m2)
	day = d1 - d2

	if d1 < d2 {
		isWholeMonth = 2
		// 避免出现 1月-2天
		if month > 0 {
			month--
			// 生日当月天数
			day += time.Date(y2, t2.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, 1, -1).Day()
		}
	} else if d1 == d2 {
		isWholeMonth = 1
	}

	return
}
func catAgeConvert(month, day, isWholeMonth int) (ageStr, ageConvertStr string) {
	if month == 0 {
		ageStr = ageStr + cast.ToString(day) + "天"
	} else {
		if (month / 12) > 0 {
			ageStr = ageStr + cast.ToString(month/12) + "岁"
		}
		if (month % 12) > 0 {
			ageStr = ageStr + cast.ToString(month%12) + "个月"
		}
	}

	age := 0
	if month < 1 {
		age = 1

	} else if month >= 1 && month < 2 {
		age = 3
		if month == 1 && isWholeMonth == 1 {
			age = 1
		}
	} else if month >= 2 && month < 3 {
		age = 5
		if month == 2 && isWholeMonth == 1 {
			age = 3
		}
	} else if month >= 3 && month < 6 {
		age = 9
		if month == 3 && isWholeMonth == 1 {
			age = 5
		}
	} else if month >= 6 && month < 9 {
		age = 12
		if month == 6 && isWholeMonth == 1 {
			age = 9
		}
	} else if month >= 9 && month < 12 {
		age = 17
		if month == 6 && isWholeMonth == 1 {
			age = 12
		}
	} else if month >= 12 && month < 24 {
		age = 24
		if month == 12 && isWholeMonth == 1 {
			age = 17
		}
	} else if month >= 24 && month < 36 {
		age = 28
		if month == 24 && isWholeMonth == 1 {
			age = 24
		}
	} else if month >= 36 && month < 48 {
		age = 32
		if month == 36 && isWholeMonth == 1 {
			age = 28
		}
	} else if month >= 48 && month < 60 {
		age = 36
		if month == 48 && isWholeMonth == 1 {
			age = 32
		}
	} else if month >= 60 && month < 72 {
		age = 40
		if month == 60 && isWholeMonth == 1 {
			age = 36
		}
	} else if month >= 72 && month < 84 {
		age = 44
		if month == 72 && isWholeMonth == 1 {
			age = 40
		}
	} else if month >= 84 && month < 96 {
		age = 48
		if month == 84 && isWholeMonth == 1 {
			age = 44
		}
	} else if month >= 96 && month < 108 {
		age = 52
		if month == 96 && isWholeMonth == 1 {
			age = 48
		}
	} else if month >= 108 && month < 120 {
		age = 56
		if month == 108 && isWholeMonth == 1 {
			age = 52
		}
	} else if month >= 120 && month < 132 {
		age = 60
		if month == 120 && isWholeMonth == 1 {
			age = 56
		}
	} else if month >= 132 && month < 144 {
		age = 64
		if month == 132 && isWholeMonth == 1 {
			age = 60
		}
	} else if month >= 144 && month < 156 {
		age = 68
		if month == 144 && isWholeMonth == 1 {
			age = 64
		}
	} else if month >= 156 && month < 168 {
		age = 72
		if month == 156 && isWholeMonth == 1 {
			age = 68
		}
	} else if month >= 168 && month < 180 {
		age = 76
		if month == 168 && isWholeMonth == 1 {
			age = 72
		}
	} else if month >= 180 {
		age = 80
		if month == 180 && isWholeMonth == 1 {
			age = 76
		}
	}
	ageConvertStr = cast.ToString(age) + "岁"
	return
}
func dogAgeConvert(month, day, isWholeMonth int) (ageStr, ageConvertStr string) {
	if month == 0 {
		ageStr = ageStr + cast.ToString(day) + "天"
	} else {
		if (month / 12) > 0 {
			ageStr = ageStr + cast.ToString(month/12) + "岁"
		}
		if (month % 12) > 0 {
			ageStr = ageStr + cast.ToString(month%12) + "个月"
		}
	}

	age := 0
	if month < 2 {
		age = 2

	} else if month >= 2 && month < 4 {
		age = 6
		if month == 2 && isWholeMonth == 1 {
			age = 2
		}
	} else if month >= 4 && month < 6 {
		age = 10
		if month == 4 && isWholeMonth == 1 {
			age = 6
		}
	} else if month >= 6 && month < 8 {
		age = 12
		if month == 6 && isWholeMonth == 1 {
			age = 10
		}
	} else if month >= 8 && month < 10 {
		age = 14
		if month == 8 && isWholeMonth == 1 {
			age = 12
		}
	} else if month >= 10 && month < 12 {
		age = 16
		if month == 10 && isWholeMonth == 1 {
			age = 14
		}
	} else if month >= 12 && month < 18 {
		age = 20
		if month == 12 && isWholeMonth == 1 {
			age = 16
		}
	} else if month >= 18 && month < 24 {
		age = 24
		if month == 18 && isWholeMonth == 1 {
			age = 20
		}
	} else if month >= 24 && month < 36 {
		age = 29
		if month == 24 && isWholeMonth == 1 {
			age = 24
		}
	} else if month >= 36 && month < 48 {
		age = 34
		if month == 36 && isWholeMonth == 1 {
			age = 29
		}
	} else if month >= 48 && month < 60 {
		age = 37
		if month == 48 && isWholeMonth == 1 {
			age = 34
		}
	} else if month >= 60 && month < 72 {
		age = 42
		if month == 60 && isWholeMonth == 1 {
			age = 37
		}
	} else if month >= 72 && month < 84 {
		age = 47
		if month == 72 && isWholeMonth == 1 {
			age = 42
		}
	} else if month >= 84 && month < 96 {
		age = 51
		if month == 84 && isWholeMonth == 1 {
			age = 47
		}
	} else if month >= 96 && month < 108 {
		age = 56
		if month == 96 && isWholeMonth == 1 {
			age = 51
		}
	} else if month >= 108 && month < 120 {
		age = 60
		if month == 108 && isWholeMonth == 1 {
			age = 56
		}
	} else if month >= 120 && month < 132 {
		age = 65
		if month == 120 && isWholeMonth == 1 {
			age = 60
		}
	} else if month >= 132 && month < 144 {
		age = 69
		if month == 132 && isWholeMonth == 1 {
			age = 65
		}
	} else if month >= 144 && month < 156 {
		age = 74
		if month == 144 && isWholeMonth == 1 {
			age = 69
		}
	} else if month >= 156 && month < 168 {
		age = 78
		if month == 156 && isWholeMonth == 1 {
			age = 74
		}
	} else if month >= 168 && month < 180 {
		age = 83
		if month == 168 && isWholeMonth == 1 {
			age = 78
		}
	} else if month >= 180 {
		age = 87
		if month == 180 && isWholeMonth == 1 {
			age = 83
		}
	}
	ageConvertStr = cast.ToString(age) + "岁"
	return
}

func (p *PetService) VaccinateRecord(petId string, vaccinateType int32) (*cc.VaccinateRecord, error) {
	year := time.Now().Year()
	for i := 0; i < 2; i++ {
		url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-url"), "/scrm-mini-api/petRecord/vaccinate/query")
		ZlBaseMap := map[string]interface{}{
			"criteria": map[string]interface{}{
				"petId":         petId,
				"current":       1,
				"pageSize":      1,
				"type":          vaccinateType,
				"operationYear": year,
			},
		}
		dataJson, _ := json.MarshalIndent(ZlBaseMap, "", "    ")

		glog.Info("获取免疫驱虫记录子龙接口(/scrm-mini-api/petRecord/vaccinate/query)请求参数(1)： ", string(dataJson), url)
		resData, err := utils.HttpPostZl(url, dataJson, "")
		glog.Info("获取免疫驱虫记录子龙接口(/scrm-mini-api/petRecord/vaccinate/query)返回结果：", string(resData), "接口参数：", string(dataJson))
		if err != nil {
			return nil, err
		}
		res := new(models.VaccinateRecordResponse)
		err = gjson.DecodeTo(resData, res)
		if err != nil {
			return nil, err
		}
		if res.StatusCode != 200 {
			return nil, errors.New("查询信息错误")
		}
		if len(res.Result.Result) > 0 {
			return res.Result.Result[0], nil
		}
		year--
	}
	return nil, nil
}
func (p *PetService) GetLastDiagnosisRecord(petId string) (*cc.LastDiagnosis, error) {
	res, err := p.MedRecordList(context.Background(), &cc.MedRecordListReq{
		PetId:     petId,
		PageIndex: 1,
		PageSize:  1,
	})
	if err != nil {
		return nil, err
	}
	out := &cc.LastDiagnosis{}

	if len(res.Data) > 0 {
		out = &cc.LastDiagnosis{
			MainSymptom: res.Data[0].MainSymptom,
			StartTime:   res.Data[0].CreateTime,
			EndTime:     res.Data[0].EndTime,
			Orgid:       res.Data[0].Orgid,
			RegId:       res.Data[0].RegId,
			MedType:     res.Data[0].MedType,
		}
	}
	return out, nil
}

func importantRemind(operationDate string, year, months, day int) int32 {
	format := "2006-01-02 15:04:05"
	a, _ := time.Parse(format, operationDate+" 00:00:00")

	if a.AddDate(year, months, day).Unix() > time.Now().Unix() {
		return 1
	}

	return 0
}

func importantRemindV2(operationDate string, year, months, day int) int32 {
	format := "2006-01-02 15:04:05"
	a, _ := time.Parse(format, operationDate)

	if a.AddDate(year, months, day).Unix() > time.Now().Unix() {
		return 1
	}

	return 0
}

// https://yapi.rp-field.com/project/522/interface/api/42253
// 互联网医院-病例列表
func (p *PetService) MedRecordList(ctx context.Context, req *cc.MedRecordListReq) (*cc.MedRecordListRes, error) {
	out := new(cc.MedRecordListRes)

	resData := new(models.MedRecordList)
	returnErr := new(models.ZlReturnError)

	bjZLUrl := config.GetString("bj-his-url")
	url := fmt.Sprintf("%s/api/nethospital/med-record/med-record-list?pet_id=%s&page=%d&page_size=%d", bjZLUrl, req.PetId, req.PageIndex, req.PageSize)
	glog.Info("查询互联网医院-病例列表子龙接口(nethospital/med-record/med-record-list)请求参数： ", url)
	bjZLHeaders := utils.GetBJZLRequestHeader()
	body, err := utils.HttpGetZl(url, "", bjZLHeaders)
	glog.Info("查询互联网医院-病例列表子龙接口(nethospital/med-record/med-record-list)返回结果：", string(body), "接口参数：", url)
	if err != nil {
		glog.Error("调用互联网医院-病例列表方法错误, 参数："+url, string(body))
		return out, errors.New(err.Error())
	}
	err = json.Unmarshal(body, &resData)
	if err != nil {
		glog.Error("解析互联网医院-病例列表返回结果错误：", string(body))
		err = json.Unmarshal(body, &returnErr)
		if err == nil {
			return out, nil
		}
		return out, errors.New(returnErr.Msg)
	}

	if resData.Code != 0 {
		glog.Error("调用互联网医院-病例列表返回结果错误：", string(body))
		return out, errors.New(resData.Msg)
	}

	out.Total = cast.ToInt32(resData.Data.Total)
	out.PageSize = cast.ToInt32(resData.Data.PageSize)
	out.PageIndex = cast.ToInt32(resData.Data.Page)

	if resData.Data.Total > 0 {
		for _, v := range resData.Data.List {
			data := &cc.MedRecordList{
				Orgid:          cast.ToInt64(v.Orgid),
				RegId:          cast.ToInt64(v.RegID),
				MedType:        cast.ToInt32(v.MedType),
				PhysicianId:    cast.ToInt64(v.PhysicianID),
				PhysicianName:  v.PhysicianName,
				CusLogicid:     cast.ToInt64(v.CusLogicid),
				CusName:        v.CusName,
				PetLogicid:     cast.ToInt64(v.PetLogicid),
				PetName:        v.PetName,
				CreateTime:     v.CreateTime,
				EndTime:        v.EndTime,
				RecordType:     cast.ToInt32(v.RecordType),
				HospitalName:   v.HospitalName,
				RecordTypeText: v.RecordTypeText,
				MainSymptom:    v.MainSymptom,
			}

			out.Data = append(out.Data, data)
		}
	}

	return out, nil
}

// https://yapi.rp-field.com/project/522/interface/api/42307
// 互联网医院-病例详情
func (p *PetService) MedRecordInfo(ctx context.Context, req *cc.MedRecordInfoReq) (*cc.MedRecordInfoRes, error) {
	out := new(cc.MedRecordInfoRes)
	resData := new(models.MedRecordInfo)
	returnErr := new(models.ZlReturnError)

	bjZLUrl := config.GetString("bj-his-url")
	url := fmt.Sprintf("%s/api/nethospital/med-record/med-record-info?reg_id=%d&orgid=%d", bjZLUrl, req.RegId, req.Orgid)
	glog.Info("查询互联网医院-病例详情子龙接口(nethospital/med-record/med-record-info)请求参数： ", url)
	bjZLHeaders := utils.GetBJZLRequestHeader()
	body, err := utils.HttpGetZl(url, "", bjZLHeaders)
	glog.Info("查询互联网医院-病例详情子龙接口(nethospital/med-record/med-record-info)返回结果：", string(body), "接口参数：", url)
	if err != nil {
		glog.Error("调用互联网医院-病例详情方法错误, 参数："+url, string(body))
		return out, errors.New(err.Error())
	}
	err = json.Unmarshal(body, &resData)
	if err != nil {
		glog.Error("解析互联网医院-病例详情返回结果错误：", string(body))
		err = json.Unmarshal(body, &returnErr)
		if returnErr.Code == 0 && len(returnErr.Data) == 0 {
			return out, errors.New("未查询到宠物信息")
		}
		return out, errors.New(returnErr.Msg)
	}

	if resData.Code != 0 {
		glog.Error("调用互联网医院-病例详情返回结果错误：", string(body))
		return out, errors.New(resData.Msg)
	}

	out = &cc.MedRecordInfoRes{
		Orgid:                 cast.ToInt64(resData.Data.Orgid),
		MedType:               resData.Data.MedType,
		RegId:                 cast.ToInt64(resData.Data.RegID),
		PhysicianName:         resData.Data.PhysicianName,
		PhysicianId:           cast.ToInt64(resData.Data.PhysicianID),
		MainSymptom:           resData.Data.MainSymptom,
		CusName:               resData.Data.CusName,
		CusLogicid:            cast.ToInt64(resData.Data.CusLogicid),
		PetName:               resData.Data.PetName,
		PetLogicid:            cast.ToInt64(resData.Data.PetLogicid),
		CreateTime:            resData.Data.CreateTime,
		EndTime:               resData.Data.EndTime,
		ChiefComplaint:        resData.Data.ChiefComplaint,
		PastHistory:           resData.Data.PastHistory,
		PhysicalDesc:          resData.Data.PhysicalDesc,
		PhysicalDescription:   resData.Data.PhysicalDescription,
		TreatmentOpinion:      resData.Data.TreatmentOpinion,
		DoctorAdvice:          resData.Data.DoctorAdvice,
		HospitalName:          resData.Data.HospitalName,
		MedLevelText:          resData.Data.MedLevelText,
		DiseaseUrgencyText:    resData.Data.DiseaseUrgencyText,
		PhysicalLevelText:     resData.Data.PhysicalLevelText,
		MedResultText:         resData.Data.MedResultText,
		LeaveSymptom:          resData.Data.LeaveSymptom,
		MedTypeText:           resData.Data.MedTypeText,
		IllnessDesc:           resData.Data.IllnessDesc,
		InpatientDoctorAdvice: resData.Data.InpatientDoctorAdvice,
		PetInfo: &cc.MdPetInfo{
			PetName:          resData.Data.PetInfo.PetName,
			PetLogicid:       cast.ToInt64(resData.Data.PetInfo.PetLogicid),
			PetGenderText:    resData.Data.PetInfo.PetGenderText,
			PetKindofText:    resData.Data.PetInfo.PetKindofText,
			PetVarietyText:   resData.Data.PetInfo.PetVarietyText,
			ColorTxt:         resData.Data.PetInfo.ColorTxt,
			PetAge:           resData.Data.PetInfo.PetAge,
			PetWeight:        cast.ToFloat32(resData.Data.PetInfo.PetWeight),
			PetNeuteringText: resData.Data.PetInfo.PetNeuteringText,
			PetStatusText:    resData.Data.PetInfo.PetStatusText,
			IsVaccinatedTxt:  resData.Data.PetInfo.IsVaccinatedTxt,
			IsDewormingTxt:   resData.Data.PetInfo.IsDewormingTxt,
		},
	}
	//具体治疗
	if len(resData.Data.SpecificTreatments) > 0 {
		for _, v := range resData.Data.SpecificTreatments {
			specificTreatments := cc.SpecificTreatments{
				PhysicianId:       cast.ToInt64(v.PhysicianID),
				PhysicianName:     v.PhysicianName,
				ExecPhysicianId:   v.ExecPhysicianID,
				ExecPhysicianName: v.ExecPhysicianName,
				InsertTime:        v.InsertTime,
				Description:       v.Description,
			}
			out.SpecificTreatments = append(out.SpecificTreatments, &specificTreatments)
		}
	}

	return out, nil
}

// 编辑新增记录
func (p *PetService) CreateOrUpdateRecord(ctx context.Context, req *cc.CreateOrUpdateRecordReq) (*cc.PetBaseResponse, error) {
	glog.Info("CreateOrUpdateRecord入参： ", kit.JsonEncode(req))
	var res = &cc.PetBaseResponse{Code: 400}

	engine := NewCustomerEngine()
	defer engine.Close()

	location, _ := time.ParseInLocation(utils.DATE_LAYOUT, req.OperationDate, time.Local)
	if req.Id > 0 { // 更新
		data := models.TScrmPetVaccinateRecord{
			Id:            cast.ToInt(req.Id),
			PetId:         req.PetId,
			OperationYear: cast.ToInt(req.OperationYear),
			OperationDate: location,
			ShopName:      req.ShopName,
			ProductName:   req.ProductName,
			Type:          cast.ToInt(req.RecordType),
			RecordPhoto:   req.RecordPhoto,
		}
		_, err := engine.Where("id = ?", data.Id).Update(data)
		if err != nil {
			res.Msg = "更新宠物记录异常："
			glog.Error(res.Msg, " ", err.Error())
			return res, err
		}
	} else { // 新增
		data := models.TScrmPetVaccinateRecord{
			PetId:         req.PetId,
			OperationYear: cast.ToInt(req.OperationYear),
			OperationDate: location,
			ShopName:      req.ShopName,
			ProductName:   req.ProductName,
			Type:          cast.ToInt(req.RecordType),
			RecordPhoto:   req.RecordPhoto,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		}

		_, err := engine.Insert(&data)
		if err != nil {
			res.Msg = "插入宠物记录数据异常"
			glog.Error(res.Msg, " ", err.Error())
			return res, err
		}
	}
	res.Code = 200
	return res, nil
}

// 删除记录
func (p *PetService) DeleteRecord(ctx context.Context, req *cc.DeleteRecordReq) (*cc.PetBaseResponse, error) {

	glog.Info("DeleteRecord入参：", kit.JsonEncode(req))
	var res = &cc.PetBaseResponse{Code: 400}

	engine := NewCustomerEngine()
	defer engine.Close()

	_, err := engine.Where("id= ? ", req.Id).Delete(&models.TScrmPetVaccinateRecord{})
	if err != nil {
		glog.Error("删除记录异常：", err.Error())
		return res, err
	}

	res.Code = 200
	return res, nil
}

// 新版宠物列表获取，直接查询北京的数据库
func (p *PetService) GetPetListNew(ctx context.Context, req *cc.PetListRequest) (*cc.PetListNewRes, error) {

	glog.Info("GetPetListNew入参：", kit.JsonEncode(req))

	var res = &cc.PetListNewRes{Code: 400}

	engine := NewBeijingPetEngine()
	defer engine.Close()

	// 查询宠物是否有保障卡
	sqlCard := "select pet_id from scrm_organization_db.t_scrm_ensure_cards_batch tsecb where user_id = ? and batch_status =1 and run_out =0;"
	var listCard []string
	err := engine.SQL(sqlCard, req.UserId).Find(&listCard)
	if err != nil {
		res.Msg = "获取宠物保障卡信息异常"
		glog.Error(res.Msg, " ", err.Error())
		return res, err
	}
	var mapCard = make(map[string]struct{}, len(listCard))
	for i := range listCard {
		mapCard[listCard[i]] = struct{}{}
	}

	// 只展示正常的宠物信息pet_status = 0
	sql := `
		select a.id, a.pet_id , a.user_id ,a.pet_name,a.pet_sex, a.pet_kindof,a.pet_variety ,a.pet_neutering ,
		a.pet_vaccinated, a.pet_deworming ,a.pet_weight , a.pet_long ,a.pet_height ,a.pet_source ,a.pet_status ,
		a.pet_avatar ,a.pet_birthday ,a.pet_homeday ,a.pet_remark ,a.create_time ,a.update_time ,a.face_id ,
		a.pet_code ,a.insurance_face_id ,a.dog_licence_code ,a.dog_vaccinate_code,a.pet_flower,a.flower_code, tspd2.pet_dict_name  pet_variety_str
		from scrm_organization_db.t_scrm_pet_info a
		left join scrm_organization_db.t_scrm_pet_dict tspd2 on a.pet_kindof = tspd2.pet_dict_parent_id  and a.pet_variety = tspd2.pet_dict_id  
		where user_id = ? and pet_status = 0  group by user_id, pet_id  order by a.create_time desc ; `

	var data []*cc.PetInfoNew
	err = engine.SQL(sql, req.UserId).Find(&data)
	if err != nil {
		res.Msg = "查询宠物信息异常"
		glog.Error(res.Msg, " ", err.Error())
		return res, err
	}

	var faceIds []string
	for i := range data {
		if len(strings.TrimSpace(data[i].FaceId)) > 0 {
			faceIds = append(faceIds, data[i].FaceId)
		}
	}
	// 查询宠物的facePhoto
	if len(faceIds) > 0 {
		var photoData []*models.TScrmPetPhoto
		sqlFace := `select * from scrm_organization_db.t_scrm_pet_photo where face_id  in ('%s');`
		sprintf := fmt.Sprintf(sqlFace, strings.Join(faceIds, "','"))
		engine.SQL(sprintf).Find(&photoData)
		for i := range photoData {
			photo := photoData[i]
			for ti := range data {
				infoNew := data[ti]
				if photo.FaceId == infoNew.FaceId {
					infoNew.ScrmPetPhoto = append(infoNew.ScrmPetPhoto, photo.PetPhoto)
					format := photo.CreateTime.Format(utils.DATE_TIME_LAYOUT)
					infoNew.SendTime = format
				}
			}
		}
	}
	// 格式化宠物的分类和宠物保障卡信息
	p.FormatKinOfNameOrCard(data, mapCard)

	// 计算年龄信息
	p.FormatAgeStr(data)

	// 计算是否展示首页的红点信息,统一都将时间展示出来给前端，前端自己去计算
	//_ = p.FormatDateToShow(data)

	res.Data = data
	res.Code = 200
	return res, nil
}

// 添加宠物信息
func (service *PetService) GetPetFlower(ctx context.Context, req *cc.GetPetFlowerRequest) (*cc.GetPetFlowerRes, error) {
	out := new(cc.GetPetFlowerRes)

	url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-wx-url"), "/scrm-organization-api/dict/getPetFlower")

	glog.Info("添加宠物毛色信息子龙接口(/scrm-organization-api/dict/getPetFlower)请求参数(1)： ", req.Token, url)
	resData, err := utils.HttpPostZl(url, []byte{}, fmt.Sprintf("token|%s&systemcode|scrm_mini_customer", req.Token))
	glog.Info("添加宠物毛色信息子龙接口(/scrm-organization-api/dict/getPetFlower)返回结果：", string(resData))
	if err != nil {
		return nil, err
	}
	res := new(models.PetFlowerDictBjRes)
	err = gjson.DecodeTo(resData, res)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != 200 {
		return nil, errors.New(res.Message)
	}

	for _, v := range res.Result.Child {
		out.Data = append(out.Data, &cc.PetFlowerDict{
			Id:          v.Id,
			Code:        v.Code,
			Name:        v.Name,
			Status:      v.Status,
			Orders:      v.Orders,
			EnglishName: v.EnglishName,
			Path:        v.Path,
			ParentCode:  v.ParentCode,
		})
	}
	return out, nil
}

// 获取驱虫免疫口腔体检的最后一次时间
func (p *PetService) FormatDateToShow(data []*cc.PetInfoNew) error {

	for k := range data {
		infoNew := data[k]
		petId := infoNew.PetId
		// 获取每一个宠物的记录信息
		for i := 1; i < 5; i++ {
			vi := i
			list, err := p.PetRecordList(context.Background(), &cc.PetRecordListReq{
				PetId:      petId,
				RecordType: cast.ToInt64(vi),
			})
			if err != nil {
				glog.Error("首页获取驱虫免疫等记录信息异常", err.Error(), " pet_id: ", petId, " type: ", vi)
				continue
			}
			if len(list.Data) > 0 {
				if vi == 1 {
					infoNew.LastExpellingParasiteRecordTime = list.Data[0].OperationDate
				}
				if vi == 2 {
					infoNew.LastImmunityRecordTime = list.Data[0].OperationDate
				}
				if vi == 3 {
					infoNew.LastMouthRecordTime = list.Data[0].OperationDate
				}
				if vi == 4 {
					infoNew.LastExaminationRecordTime = list.Data[0].OperationDate
				}
			}
		}
	}
	return nil
}

/*
*
格式化年龄信息
*/
func (p *PetService) FormatAgeStr(data []*cc.PetInfoNew) {

	var wg sync.WaitGroup
	for i := range data {
		wg.Add(1)
		v := data[i]
		AntsP.Submit(func() {
			defer func() {
				wg.Done()
			}()
			birthdayTime, _ := time.Parse(utils.DATE_TIME_LAYOUT, v.PetBirthday)
			month, day, isWholeMonth := GetAge(time.Now(), birthdayTime)
			if v.PetKindof == 1000 {
				v.AgeStr, v.AgeConversionStr = catAgeConvert(month, day, isWholeMonth)
			} else if v.PetKindof == 1001 {
				v.AgeStr, v.AgeConversionStr = dogAgeConvert(month, day, isWholeMonth)
			} else {
				v.AgeStr, _ = dogAgeConvert(month, day, isWholeMonth)
			}
		})
	}
	wg.Wait()
}

// 查询宠物的分类
func (p *PetService) FormatKinOfNameOrCard(data []*cc.PetInfoNew, mapCard map[string]struct{}) {

	//engine := NewBeijingPetEngine()
	//defer engine.Close()

	var wg sync.WaitGroup

	var unKnown = "未知"
	for i := range data {
		wg.Add(1)
		infoNew := data[i]
		AntsP.Submit(func() {
			defer func() {
				wg.Done()
			}()
			if infoNew.PetKindof == 1000 {
				infoNew.PetKindofStr = "猫"
			} else if infoNew.PetKindof == 1001 {
				infoNew.PetKindofStr = "犬"
			} else if infoNew.PetKindof == 1002 {
				infoNew.PetKindofStr = "其他"
			} else {
				infoNew.PetKindofStr = unKnown
			}
			if len(infoNew.PetVarietyStr) <= 0 {
				infoNew.PetVarietyStr = unKnown
			}
			if _, ok := mapCard[infoNew.PetId]; ok {
				infoNew.EnsureCard = true
			}
		})
	}
	wg.Wait()
}

/*
*
获取记录类型  记录类型 1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重

	1疫苗记录 2驱虫记录的数据来源是两部分，自己的数据库自建类型 + his的数据(没有id)
	3口腔 4体检 目前都是我们的数据
*/
func (p *PetService) PetRecordList(ctx context.Context, req *cc.PetRecordListReq) (*cc.PetRecordListBaseResponse, error) {

	glog.Info("GetPetListNew入参：", kit.JsonEncode(req))

	var res = &cc.PetRecordListBaseResponse{
		Code: 400,
		Data: []*cc.PetRecordListRes{},
	}
	engine := NewCustomerEngine()
	defer engine.Close()

	sql := `select * from dc_customer.t_scrm_pet_vaccinate_record tspvr where pet_id =? and type = ?  order by operation_date desc ;`

	var data = []*models.TScrmPetVaccinateRecord{}
	err := engine.SQL(sql, req.PetId, req.RecordType).Find(&data)
	if err != nil {
		res.Msg = "查询记录异常"
		return res, err
	}

	if req.RecordType == 1 || req.RecordType == 2 { // 查询his的驱虫和疫苗记录
		hisData, err := p.GetHisData(req.PetId, cast.ToInt(req.RecordType))
		if err != nil {
			glog.Error("获取his记录失败：", err.Error(), " pet_id", req.PetId, " type : ", req.RecordType)
		}
		data = append(data, hisData...)
	}
	// 日期排序
	sort.Slice(data, func(i, j int) bool {
		return data[i].OperationDate.After(data[j].OperationDate)
	})

	// 格式化数据返回
	for i := range data {
		record := data[i]
		operationDateStr := record.OperationDate.Format(utils.DATE_LAYOUT)
		createTimeStr := record.CreateTime.Format(utils.DATE_TIME_LAYOUT)
		updateTimeStr := record.UpdateTime.Format(utils.DATE_TIME_LAYOUT)

		var resData = cc.PetRecordListRes{
			Id:            cast.ToInt64(record.Id),
			PetId:         record.PetId,
			OperationYear: cast.ToString(record.OperationYear),
			OperationDate: operationDateStr,
			ShopName:      record.ShopName,
			ProductName:   record.ProductName,
			RecordType:    cast.ToInt64(record.Type),
			RecordPhoto:   record.RecordPhoto,
			CreateTime:    createTimeStr,
			UpdateTime:    updateTimeStr,
		}
		res.Data = append(res.Data, &resData)
	}

	res.Code = 200
	return res, nil
}

// 获取his的驱虫免疫记录
func (p *PetService) GetHisData(petId string, recordType int) ([]*models.TScrmPetVaccinateRecord, error) {

	//  查询his记录
	url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-url"), "/api/zlcrm/openapi/vaccinate/get-list")
	//url := "https://erp-zuul-outside-test.rp-field.com/api/zlcrm/openapi/vaccinate/get-list"
	endTime := time.Now().Format(utils.DATE_TIME_LAYOUT)
	var data = make([]*models.TScrmPetVaccinateRecord, 0)

	ZlBaseMap := map[string]interface{}{
		"scrm_pet_id": petId,
		"page_size":   math.MaxInt,
		"page":        1,
		"search_type": recordType,
		"start_time":  "2010-01-01 00:00:00",
		"end_time":    endTime,
	}

	glog.Info("获取免疫驱虫记录his接口请求参数： ", url, " dataJson: ", kit.JsonEncode(ZlBaseMap))
	resData, err := utils.HttpPostZl(url, []byte(kit.JsonEncode(ZlBaseMap)), "")

	if string(resData) == `{"code":0,"msg":"ok","data":[]}` { //北京PHP团队对无数据应答的默认结构体
		glog.Info("获取免疫驱虫记录his接口请求参数： ", url, " dataJson: ", kit.JsonEncode(ZlBaseMap), "子龙无数据，默认应答")
		return data, err
	}

	glog.Info("获取免疫驱虫记录his接口返回结果： ", string(resData), " 接口参数：", kit.JsonEncode(ZlBaseMap), " err: ", err)
	if err != nil {
		glog.Error("获取免疫驱虫记录his接口异常：", err.Error(), " 接口参数：", kit.JsonEncode(ZlBaseMap))
		return data, err
	}
	var hisRecords = models.HisRecordRes{}
	err = json.Unmarshal(resData, &hisRecords)
	if err != nil {
		glog.Error("序列化免疫驱虫记录his接口异常：", err.Error(), " 返回值：", string(resData), " 接口参数：", kit.JsonEncode(ZlBaseMap), " err: ", err)
		return data, nil
	}

	// 加入his记录返回
	if hisRecords.Data != nil && len(hisRecords.Data.List) > 0 {
		for i := range hisRecords.Data.List {
			record := hisRecords.Data.List[i]
			if len(record.ActuallyDate) > 0 && len(record.HostName) > 0 && len(record.ItemName) > 0 { // 排除空数据
				ActuallyDateLocation, _ := time.ParseInLocation(utils.DATE_LAYOUT, record.ActuallyDate, time.Local)
				hisData := models.TScrmPetVaccinateRecord{
					PetId:         petId,
					OperationYear: cast.ToInt(record.Year),
					OperationDate: ActuallyDateLocation,
					ShopName:      record.HostName, // todo 医院名称
					ProductName:   record.ItemName,
					Type:          recordType,
					CreateTime:    time.Now(),
					UpdateTime:    time.Now(),
				}
				data = append(data, &hisData)
			}
		}
	}

	return data, nil
}

// 获取子龙驱虫、免疫的列表数据 feature-1.0
// recordType:类别（免疫：200000084，驱虫：200000085，绝育：200000086，洗牙：200000087）
// token 阿闻小程序宠主token
func (p *PetService) GetNewHisData(petId, recordType, token string) (*models.NewHisRecordRes, error) {
	//  查询his记录
	//https://erp-zuul-outside.rp-field.com这个网关 app才能用
	url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-url"), "/pethealthapi/awhealth/record_list")

	//url := fmt.Sprintf("%s%s", config.GetString("bj-zuul-wechat-url"), "/pethealthapi/awhealth/record_list")
	//url := "https://zuul-wechat-test.rp-field.com/pethealthapi/awhealth/record_list" 这个网关不对，
	logPrefix := fmt.Sprintf("获取新的驱虫免疫记录customer-center-GetNewHisData====,入参：petId:%s，recordType:%s,请求地址：%s", petId, recordType, url)

	var data = new(models.NewHisRecordRes)
	data.Data = make([]*models.NewHisRecord, 0)

	ZlBaseMap := map[string]interface{}{
		"pet_id": petId,
		"type":   recordType,
	}

	body, _ := json.Marshal(ZlBaseMap)

	// header := http.Header{
	// 	"companycode": []string{"RPX0001"},
	// 	"systemcode":  []string{"scrm_mini_customer"},
	// 	"token":       []string{token},
	// }
	header := fmt.Sprintf("companycode|%s&systemcode|%s&token|%s", "RPX0001", "scrm_mini_customer", token)
	glog.Info(logPrefix, "访问子龙请求参数:", kit.JsonEncode(ZlBaseMap))
	//resData, err := utils.Post(url, body, header)
	resData, err := utils.HttpPost(url, body, header)
	if err != nil {
		glog.Error(logPrefix, "访问子龙出错：", err.Error())
		return nil, err
	}
	glog.Info(logPrefix, "访问子龙返回结果:", string(resData))

	err = json.Unmarshal(resData, data)
	if err != nil {
		glog.Error(logPrefix, "解析数据出错：", err.Error())
		return nil, err
	}
	if data.Code != 0 {
		glog.Error(logPrefix, "数据异常")
		return nil, errors.New("子龙返回的数据异常")
	}

	return data, nil
}

/*
*
查询记录的时间
1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重 9绝育
注意： 疫苗记录和驱虫记录直接取子龙数据 feature-1.0
*/
func (p *PetService) GetNewPetDate(ctx context.Context, req *cc.PetListRequest) (*cc.NewPetRecordDateBaseResponse, error) {
	logPrefix := fmt.Sprintf("customer-center-GetNewPetDate====查询记录时间，入参：petId:%s,UserId:%s", req.PetId, req.UserId)
	glog.Info(logPrefix, "token:", req.Token)
	var res = &cc.NewPetRecordDateBaseResponse{
		Code: 400,
		Data: []*cc.NewPetRecordDateRes{},
	}

	engine := NewCustomerEngine()
	defer engine.Close()
	// 查询自建记录的时间
	sql := "select pet_id, `type` record_type, operation_date, record_photo, product_name, create_time from t_scrm_pet_vaccinate_record " +
		"as a where pet_id = ? and type not in (1,2) and not exists(select b.id from t_scrm_pet_vaccinate_record as b where b.pet_id = a.pet_id " +
		"and b.type = a.type and b.create_time > a.create_time);"

	var onlyData []*models.PetRecordDate
	err := engine.SQL(sql, req.PetId).Find(&onlyData)
	if err != nil {
		res.Msg = "查询自建记录的时间异常"
		glog.Error(res.Msg, " ", err.Error())
		return res, err
	}
	petEngine := NewBeijingPetEngine()
	defer petEngine.Close()
	sqlBirthDay := "select pet_birthday,pet_neutering,pet_kindof from scrm_organization_db.t_scrm_pet_info where pet_id =?;"
	var neuteringData models.ShowPetNeuteringData
	ok, _ := petEngine.SQL(sqlBirthDay, req.PetId).Get(&neuteringData)

	//记录存在的类型
	var hasTypeMap = make(map[int64]bool, len(onlyData))
	now := time.Now()
	for i := range onlyData {
		date := onlyData[i]
		var resData = cc.NewPetRecordDateRes{
			RecordPhoto:   date.RecordPhoto,
			ProductName:   date.ProductName,
			RecordType:    date.RecordType,
			CreateTime:    date.CreateTime,
			OperationDate: date.OperationDate,
		}
		hasTypeMap[date.RecordType] = true
		switch date.RecordType {
		//红点展示
		//case 1, 3, 4:
		case 3, 4:
			// if date.RecordType == 1 {
			// 	dataImmunity, err := p.GetHisData(req.PetId, 1)
			// 	if err != nil {
			// 		res.Msg = "查询his的免疫记录异常"
			// 		glog.Error(res.Msg, " ", err.Error())
			// 	}
			// 	if len(dataImmunity) > 0 {
			// 		sort.Slice(dataImmunity, func(i, j int) bool {
			// 			return dataImmunity[i].OperationDate.After(dataImmunity[j].OperationDate)
			// 		})
			// 		date := dataImmunity[0].OperationDate
			// 		format := date.Format(utils.DATE_LAYOUT)
			// 		if len(resData.OperationDate) <= 0 {
			// 			resData.OperationDate = format
			// 		} else {
			// 			if resData.OperationDate < format {
			// 				resData.OperationDate = format
			// 			}
			// 		}
			// 	}
			// }
			//未记录 + 最后一次记录离现在超过365自然天；（和之前的逻辑一样）
			if len(resData.OperationDate) > 0 {
				location, _ := time.ParseInLocation(utils.DATE_LAYOUT, resData.OperationDate, time.Local)
				add := location.AddDate(1, 0, 0)
				if now.After(add) {
					resData.Show = true
				}
				//提醒
				remindOneYearLater := add.AddDate(0, 0, -3)
				if now.After(remindOneYearLater) && now.Before(add) {
					resData.Remind = true
				}
				hour := time.Until(add).Hours() / 24
				resData.DayNum = int32(math.Ceil(hour))
			} else {
				resData.Show = true
			}
		// case 2:
		// 	dataDeworming, err := p.GetHisData(req.PetId, 2)
		// 	if err != nil {
		// 		res.Msg = "查询his的驱虫记录异常"
		// 		glog.Error(res.Msg, " ", err.Error())
		// 	}
		// 	if len(dataDeworming) > 0 {
		// 		sort.Slice(dataDeworming, func(i, j int) bool {
		// 			return dataDeworming[i].OperationDate.After(dataDeworming[j].OperationDate)
		// 		})

		// 		date := dataDeworming[0].OperationDate
		// 		format := date.Format(utils.DATE_LAYOUT)
		// 		if len(resData.OperationDate) <= 0 {
		// 			resData.OperationDate = format
		// 		} else {
		// 			if resData.OperationDate < format {
		// 				resData.OperationDate = format
		// 			}
		// 		}
		// 	}
		// 	// 驱虫：未记录 + 最后一次记录离现在超过90自然天；（和之前的逻辑一样）
		// 	if len(resData.OperationDate) > 0 { // 未记录 + 最后一次记录离现在超过60自然天
		// 		location, _ := time.ParseInLocation(utils.DATE_LAYOUT, resData.OperationDate, time.Local)
		// 		add := location.AddDate(0, 3, 0)
		// 		if now.After(add) {
		// 			resData.Show = true
		// 		}
		// 		//提醒
		// 		remindThreeMonthsLater := add.AddDate(0, 0, -3)
		// 		if now.After(remindThreeMonthsLater) && now.Before(add) {
		// 			resData.Remind = true
		// 		}
		// 		hour := add.Sub(time.Now()).Hours() / 24
		// 		resData.DayNum = int32(math.Ceil(hour))
		// 	} else {
		// 		resData.Show = true
		// 	}
		case 5:
			//洗护 猫咪建议3月1次、狗狗建议2周1次
			if ok {
				if len(resData.OperationDate) > 0 {
					location, _ := time.ParseInLocation(utils.DATE_LAYOUT, resData.OperationDate, time.Local)
					switch neuteringData.PetKindof {
					case 1000:
						add := location.AddDate(0, 3, 0)
						if now.After(add) {
							resData.Show = true
						}
						remindThreeMonthsLater := add.AddDate(0, 0, -3)
						if now.After(remindThreeMonthsLater) && now.Before(add) {
							resData.Remind = true
						}
						hour := time.Until(add).Hours() / 24
						resData.DayNum = int32(math.Ceil(hour))
					case 1001:
						add := location.Add(14 * 24 * time.Hour)
						if now.After(add) {
							resData.Show = true
						}
						remindTwoWeeksLater := add.AddDate(0, 0, -3)
						if now.After(remindTwoWeeksLater) && now.Before(add) {
							resData.Remind = true
						}
						hour := time.Until(add).Hours() / 24
						resData.DayNum = int32(math.Ceil(hour))
					}
				} else {
					resData.Show = true
				}
			}
		case 6, 7, 8:
			// 6体况评分 7三围 8体重：未记录 + 最后一次记录离现在超过一个月
			if len(resData.OperationDate) > 0 { // 未记录 + 最后一次记录离现在超过60自然天
				location, _ := time.ParseInLocation(utils.DATE_LAYOUT, resData.OperationDate, time.Local)
				add := location.AddDate(0, 1, 0)
				if now.After(add) {
					resData.Show = true
				}
				//提醒
				remindThreeMonthsLater := add.AddDate(0, 0, -3)
				if now.After(remindThreeMonthsLater) && now.Before(add) {
					resData.Remind = true
				}
				hour := time.Until(add).Hours() / 24
				resData.DayNum = int32(math.Ceil(hour))
			} else {
				resData.Show = true
			}
		}

		res.Data = append(res.Data, &resData)
	}
	//  从子龙取 最新一条 免疫记录和最新一条驱虫记录 feature-1.0
	//免疫：最后一次记录离现在超过365自然天；
	//驱虫：最后一次记录离现在超过90自然天
	recordType := [3]string{"", "200000084", "200000085"}
	for k, v := range recordType {
		if len(v) == 0 {
			continue
		}
		if NewHisData, err := p.GetNewHisData(req.PetId, v, req.Token); err != nil {
			glog.Error(logPrefix, "p.GetNewHisData失败：", err.Error(), "v:", v)
			res.Msg = "从子龙记录失败"
			return res, err
		} else if len(NewHisData.Data) > 0 {

			hasTypeMap[int64(k)] = true
			firstMianY := NewHisData.Data[0]
			resData := cc.NewPetRecordDateRes{
				RecordPhoto:   "",
				ProductName:   firstMianY.ProductName,
				RecordType:    int64(k),
				CreateTime:    firstMianY.CDate.Timestamp.Format(kit.DATETIME_LAYOUT),
				OperationDate: firstMianY.CDate.Timestamp.Format(kit.DATETIME_LAYOUT),
			}
			if len(resData.OperationDate) > 0 {
				location, _ := time.ParseInLocation(utils.DATE_TIME_LAYOUT, resData.OperationDate, time.Local)
				var add time.Time
				if v == "200000084" {
					add = location.AddDate(1, 0, 0)
				} else {
					add = location.AddDate(0, 3, 0)
				}

				if now.After(add) {
					resData.Show = true
				}
				//提醒
				remindOneYearLater := add.AddDate(0, 0, -3)
				if now.After(remindOneYearLater) && now.Before(add) {
					resData.Remind = true
				}
				hour := time.Until(add).Hours() / 24
				resData.DayNum = int32(math.Ceil(hour))
			} else {
				resData.Show = true
			}
			res.Data = append(res.Data, &resData)

		}
	}

	// 绝育展示
	var resDataMap = cc.NewPetRecordDateRes{}
	if ok {
		birthdayTime, _ := time.Parse(utils.DATE_TIME_LAYOUT, neuteringData.PetBirthday)
		month, day, _ := GetAge(time.Now(), birthdayTime)
		if month > 12 && neuteringData.PetNeutering == 0 {
			resDataMap.Show = true
		}
		if month == 12 && day > 0 && neuteringData.PetNeutering == 0 {
			resDataMap.Show = true
		}
	}
	res.Data = append(res.Data, &resDataMap)
	//没有记录的类型也返回
	for i := int64(1); i < 9; i++ {
		if !hasTypeMap[i] {
			res.Data = append(res.Data, &cc.NewPetRecordDateRes{
				RecordType: i,
				Show:       true,
				Remind:     true,
			})
		}
	}

	res.Code = 200
	return res, nil
}

/*
*
查询记录的时间
1疫苗记录 2驱虫记录 3口腔 4体检
*/
func (p *PetService) GetPetDate(ctx context.Context, req *cc.PetListRequest) (*cc.PetRecordDateBaseResponse, error) {
	var res = &cc.PetRecordDateBaseResponse{
		Code: 400,
		Data: &cc.PetRecordDateRes{},
	}

	engine := NewCustomerEngine()
	defer engine.Close()
	// 查询自建记录的时间
	sql := "select pet_id, `type` record_type, max(operation_date)  operation_date  from  dc_customer.t_scrm_pet_vaccinate_record tspvr where pet_id = ? group  by pet_id, `type` ;"

	var onlyData = []*models.PetRecordDate{}
	err := engine.SQL(sql, req.PetId).Find(&onlyData)
	if err != nil {
		res.Msg = "查询自建记录的时间异常"
		glog.Error(res.Msg, " ", err.Error())
		return res, err
	}

	for i := range onlyData {
		date := onlyData[i]
		if date.RecordType == 1 {
			res.Data.LastImmunityRecordTime = date.OperationDate
		}
		if date.RecordType == 2 {
			res.Data.LastExpellingParasiteRecordTime = date.OperationDate
		}
		if date.RecordType == 3 {
			res.Data.LastMouthRecordTime = date.OperationDate
		}
		if date.RecordType == 4 {
			res.Data.LastExaminationRecordTime = date.OperationDate
		}
	}

	var wg sync.WaitGroup
	// 查询his的驱虫和免疫记录
	wg.Add(1)
	go func() {
		defer func() {
			wg.Done()
		}()
		dataImmunity, err := p.GetHisData(req.PetId, 1)
		if err != nil {
			res.Msg = "查询his的免疫记录异常"
			glog.Error(res.Msg, " ", err.Error())
		}

		if len(dataImmunity) > 0 {
			sort.Slice(dataImmunity, func(i, j int) bool {
				return dataImmunity[i].OperationDate.After(dataImmunity[j].OperationDate)
			})
			date := dataImmunity[0].OperationDate
			format := date.Format(utils.DATE_LAYOUT)
			if len(res.Data.LastImmunityRecordTime) <= 0 {
				res.Data.LastImmunityRecordTime = format
			} else {
				if res.Data.LastImmunityRecordTime < format {
					res.Data.LastImmunityRecordTime = format
				}
			}
		}
	}()

	wg.Add(1)
	go func() {
		defer func() {
			wg.Done()
		}()
		// 驱虫
		dataDeworming, err := p.GetHisData(req.PetId, 2)
		if err != nil {
			res.Msg = "查询his的驱虫记录异常"
			glog.Error(res.Msg, " ", err.Error())
		}
		if len(dataDeworming) > 0 {
			sort.Slice(dataDeworming, func(i, j int) bool {
				return dataDeworming[i].OperationDate.After(dataDeworming[j].OperationDate)
			})

			date := dataDeworming[0].OperationDate
			format := date.Format(utils.DATE_LAYOUT)
			if len(res.Data.LastExpellingParasiteRecordTime) <= 0 {
				res.Data.LastExpellingParasiteRecordTime = format
			} else {
				if res.Data.LastExpellingParasiteRecordTime < format {
					res.Data.LastExpellingParasiteRecordTime = format
				}
			}
		}
	}()

	wg.Wait()

	// 红点展示
	now := time.Now()
	//  免疫：未记录 + 最后一次记录离现在超过365自然天；（和之前的逻辑一样）
	if len(res.Data.LastImmunityRecordTime) > 0 {
		location, _ := time.ParseInLocation(utils.DATE_LAYOUT, res.Data.LastImmunityRecordTime, time.Local)
		add := location.Add(365 * 24 * time.Hour)
		if now.After(add) {
			res.Data.ImmunityRecordTimeShow = true
		}
	} else {
		res.Data.ImmunityRecordTimeShow = true
	}
	// 驱虫：未记录 + 最后一次记录离现在超过90自然天；（和之前的逻辑一样）
	if len(res.Data.LastExpellingParasiteRecordTime) > 0 { // 未记录 + 最后一次记录离现在超过60自然天
		location, _ := time.ParseInLocation(utils.DATE_LAYOUT, res.Data.LastExpellingParasiteRecordTime, time.Local)
		add := location.Add(90 * 24 * time.Hour)
		if now.After(add) {
			res.Data.ExpellingParasiteRecordShow = true
		}
	} else {
		res.Data.ExpellingParasiteRecordShow = true
	}
	//口腔：未记录 + 最后一次记录离现在超过1自然天；
	if len(res.Data.LastMouthRecordTime) > 0 {
		location, _ := time.ParseInLocation(utils.DATE_LAYOUT, res.Data.LastMouthRecordTime, time.Local)
		add := location.Add(1 * 24 * time.Hour) //  未记录 + 最后一次记录离现在超过30自然天；
		if now.After(add) {
			res.Data.MouthRecordShow = true
		}
	} else {
		res.Data.MouthRecordShow = true
	}
	//体检：未记录 + 最后一次记录离现在超过365自然天；（和之前的逻辑一样
	if len(res.Data.LastExaminationRecordTime) > 0 {
		location, _ := time.ParseInLocation(utils.DATE_LAYOUT, res.Data.LastExaminationRecordTime, time.Local)
		add := location.Add(365 * 24 * time.Hour)
		if now.After(add) {
			res.Data.ExaminationRecordShow = true
		}
	} else {
		res.Data.ExaminationRecordShow = true
	}

	// 绝育展示
	petEngine := NewBeijingPetEngine()
	defer petEngine.Close()
	sqlBirthDay := "select pet_birthday,pet_neutering,pet_kindof  from scrm_organization_db.t_scrm_pet_info where pet_id =?;"
	var neuteringData models.ShowPetNeuteringData
	ok, _ := petEngine.SQL(sqlBirthDay, req.PetId).Get(&neuteringData)
	if ok {
		birthdayTime, _ := time.Parse(utils.DATE_TIME_LAYOUT, neuteringData.PetBirthday)
		month, day, _ := GetAge(time.Now(), birthdayTime)
		if month > 12 && neuteringData.PetNeutering == 0 {
			res.Data.SterilizationShow = true
		}
		if month == 12 && day > 0 && neuteringData.PetNeutering == 0 {
			res.Data.SterilizationShow = true
		}
	}
	res.Code = 200
	glog.Info("GetPetDate返回：", kit.JsonEncode(res))
	return res, nil
}
