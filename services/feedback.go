package services

import (
	"_/enum"
	"_/models"
	"_/proto/base"
	"_/proto/cc"
	"_/utils"
	"context"
	"encoding/json"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"net/http"
	"strings"
)

type FeedbackService struct {
	BaseService
}

//添加用户反馈
func (f FeedbackService) AddFeedback(ctx context.Context, request *cc.AddFeedbackRequest) (*cc.FeedBaseResponse, error) {
	glog.Info("AddFeedback 入参：" + kit.JsonEncode(request))
	out := new(cc.FeedBaseResponse)
	out.Code = http.StatusBadRequest

	Feedback := &models.Feedback{}

	db := NewEngine()
	session := db.NewSession()
	defer session.Close()

	_ = session.Begin()
	err := utils.MapTo(request, Feedback)
	if err != nil {
		glog.Error("AddFeedback 添加用户反馈数据处理出错:" + err.Error())
		out.Error = err.Error()
		return out, err
	}

	if Feedback.ClientInfo != "" {
		clientInfo := new(base.ClientInfo)
		err = json.Unmarshal([]byte(Feedback.ClientInfo), clientInfo)
		if err != nil {
			glog.Error("AddFeedback clientInfo json解码出错:" + err.Error())
		} else {
			Feedback.Version = clientInfo.Version
		}
	}
	//获取用户名
	if Feedback.UserName == "" {
		userInfo, err := GetScrmUserInfo(Feedback.UserId)
		if err != nil {
			glog.Error("AddFeedback 查询用户信息出错" + err.Error())
		}
		if userInfo != nil {
			Feedback.UserName = userInfo.UserName
		}
	}
	Feedback.StoreId = request.OrgId

	if _, err = session.Insert(Feedback); err != nil {
		_ = session.Rollback()
		glog.Error("AddFeedback 添加用户反馈出错:" + err.Error())
		out.Error = err.Error()
		return out, err
	}
	_ = session.Commit()

	out.Code = http.StatusOK
	out.Message = "Success"
	return out, nil
}

//获取反馈的详情
func (f FeedbackService) GetFeedback(ctx context.Context, request *cc.GetFeedbackRequest) (*cc.GetFeedbackResponse, error) {
	out := new(cc.GetFeedbackResponse)
	out.Code = http.StatusBadRequest

	Feedback := &models.Feedback{}

	db := NewEngine()
	session := db.NewSession()
	defer session.Close()

	session.Where("id = ?", request.Id)

	if request.UserId != "" {
		session.Where("user_id = ?", request.UserId)
	}
	has, err := session.Get(Feedback)

	if err != nil {
		glog.Error("GetFeedback 获取用户反馈信息出错:" + err.Error())
		out.Message = "获失败"
		out.Error = err.Error()
		return out, err
	}
	if !has {
		out.Message = "反馈信息不存在"
		return out, nil
	}

	FeedbackData := &cc.Feedback{}
	err = utils.MapTo(Feedback, FeedbackData)
	if err != nil {
		glog.Error("GetFeedback 获取用户反馈信息数据转换失败:" + err.Error())
		out.Message = "获取失败"
		out.Error = err.Error()
		return out, err
	}
	out.Code = http.StatusOK
	out.Message = "Success"
	out.Data = FeedbackData
	return out, nil
}

//获取用户反馈列表
func (f FeedbackService) GetFeedbackList(ctx context.Context, request *cc.GetFeedbackListRequest) (*cc.GetFeedbackListResponse, error) {
	//查询拼团商品列表
	out := new(cc.GetFeedbackListResponse)
	out.Code = http.StatusBadRequest

	var (
		queryResult []models.Feedback //查询结果集
		err         error
		total       int64 //总的记录条数
	)
	db := NewEngine()
	session := db.NewSession()
	defer session.Close()
	session.Table("feedback").Where("store_id=?", request.OrgId)

	//条件拼装
	if request.UserId != "" {
		session.And("user_id = ?", request.UserId)
	}
	if request.Phone != "" {
		session.And("phone like ?", "%"+request.Phone+"%")
	}
	if request.Content != "" {
		session.And("content like ?", "%"+request.Content+"%")
	}
	if request.StartTime != "" {
		session.And("create_time >= ?", request.StartTime)
	}
	if request.Type >= 0 {
		session.And("type = ?", request.Type)
	}
	if request.CreateTime != "" {
		duringTime := strings.Split(request.CreateTime, " - ")
		if len(duringTime) > 0 {
			session.And("create_time between ? AND ? ", duringTime[0]+" 00:00:00", duringTime[1]+" 23:59:59")
		}
	}
	//统计条数
	cloneSession := session.Clone()
	defer cloneSession.Close()

	if total, err = cloneSession.Count(); err != nil {
		glog.Error("GetFeedbackList 查询用户反馈列表失败：" + err.Error())
		out.Error = err.Error()
		out.Message = "查询失败"
		return out, err
	}
	if request.PageIndex == 0 {
		request.PageIndex = 1
	}
	if request.PageSize == 0 {
		request.PageSize = 15
	}
	limit := int(request.PageSize)
	start := int((request.PageIndex - 1) * request.PageSize)

	err = session.Select("*").OrderBy("id DESC").Limit(limit, start).Find(&queryResult)

	if err != nil {
		glog.Error("GetFeedbackList 查询用户反馈列表失败：" + err.Error())
		out.Error = err.Error()
		out.Message = "查询失败"
		return out, err
	}

	out.Code = http.StatusOK
	out.Message = "获取成功"
	out.Total = int32(total)
	if len(queryResult) == 0 {
		return out, nil
	}
	//输出结果
	err = utils.MapTo(queryResult, &out.Data)
	if err != nil {
		glog.Error("GetFeedbackList map to fail：" + err.Error())
		out.Error = err.Error()
		out.Message = "查询失败"
		return out, err
	}
	//查询活动的拼团数据
	return out, nil
}

//添加反馈回复
func (f FeedbackService) AddFeedbackComment(ctx context.Context, request *cc.AddFeedbackCommentRequest) (*cc.FeedBaseResponse, error) {
	glog.Info("AddFeedbackComment 入参：" + kit.JsonEncode(request))
	out := new(cc.FeedBaseResponse)
	out.Code = http.StatusBadRequest

	FeedbackComment := &models.FeedbackComments{}

	db := NewEngine()
	session := db.NewSession()
	defer session.Close()

	_ = session.Begin()
	err := utils.MapTo(request, FeedbackComment)
	if err != nil {
		glog.Error("AddFeedbackComment 添加反馈回复处理出错:" + err.Error())
		out.Error = err.Error()
		return out, err
	}
	//用户回复 获取用户名
	if FeedbackComment.IsOfficial == 0 && FeedbackComment.UserName == "" {
		userInfo, err := GetScrmUserInfo(FeedbackComment.UserId)
		if err != nil {
			glog.Error("AddFeedback 查询用户信息出错" + err.Error())
		}
		if userInfo != nil {
			FeedbackComment.UserName = userInfo.UserName
		}
	}

	if _, err = session.Insert(FeedbackComment); err != nil {
		_ = session.Rollback()
		glog.Error("AddFeedbackComment 添加反馈回复出错:" + err.Error())
		out.Error = err.Error()
		return out, err
	}
	_ = session.Commit()
	out.Code = http.StatusOK
	out.Message = "Success"
	//官方回复 需要给用户发通知
	if FeedbackComment.IsOfficial == 1 {
		//获取反馈用户的user_id
		var userScrmId string
		_, err = session.Table("feedback").Where("id = ? ", FeedbackComment.FeedbackId).Cols("user_id").Get(&userScrmId)
		if err != nil {
			glog.Error("AddFeedbackComment 查询反馈用户出错", FeedbackComment.Id)
		}
		if userScrmId == "" {
			glog.Error("AddFeedbackComment 查询反馈用户未查询到用户id", FeedbackComment.FeedbackId)
			return out, nil
		}
		//获取用户电话
		feedUserInfo, err := GetScrmUserInfo(userScrmId)
		if err != nil {
			glog.Error("AddFeedbackComment 查询反馈用户信息出错"+err.Error(), FeedbackComment.Id)
			return out, nil
		}
		if feedUserInfo.UserMobile == "" {
			glog.Error("AddFeedbackComment 未查询到反馈用户信息", FeedbackComment.Id)
			return out, nil
		}
		//构建极光推送消息结构体
		message := NewPushFeedbackCommentMessage(
			userScrmId,
			feedUserInfo.UserMobile,
			enum.JPushFeedbackCommentForUser,
			FeedbackComment.FeedbackId,
			FeedbackComment.Id,
		)
		//推送消息
		_ = message.Push()
	}
	return out, nil
}

//反馈回复列表
func (f FeedbackService) GetFeedbackCommentList(ctx context.Context, request *cc.GetFeedbackCommentListRequest) (*cc.GetFeedbackCommentListResponse, error) {
	//查询拼团商品列表
	out := new(cc.GetFeedbackCommentListResponse)
	out.Code = http.StatusBadRequest

	var (
		queryResult []models.FeedbackComments //查询结果集
		err         error
		total       int64 //总的记录条数
	)
	db := NewEngine()
	session := db.NewSession()
	defer session.Close()
	session.Table("feedback_comments").Where("feedback_id = ?", request.FeedbackId)

	//统计条数
	cloneSession := session.Clone()
	defer cloneSession.Close()

	if total, err = cloneSession.Count(); err != nil {
		glog.Error("GetFeedbackCommentList 查询用户反馈回复列表失败：" + err.Error())
		out.Error = err.Error()
		out.Message = "查询失败"
		return out, err
	}
	if request.PageIndex == 0 {
		request.PageIndex = 1
	}
	if request.PageSize == 0 {
		request.PageSize = 15
	}
	limit := int(request.PageSize)
	start := int((request.PageIndex - 1) * request.PageSize)
	//-1 返回全部
	if request.PageSize == -1 {
		err = session.Select("*").OrderBy("id ASC").Find(&queryResult)
	} else {
		err = session.Select("*").OrderBy("id ASC").Limit(limit, start).Find(&queryResult)
	}

	if err != nil {
		glog.Error("GetFeedbackCommentList 查询用户反馈回复列表失败：" + err.Error())
		out.Error = err.Error()
		out.Message = "查询回复数据失败"
		return out, err
	}

	out.Code = http.StatusOK
	out.Message = "获取成功"
	out.Total = int32(total)
	if len(queryResult) == 0 {
		return out, nil
	}
	//输出结果
	err = utils.MapTo(queryResult, &out.Data)
	if err != nil {
		glog.Error("GetFeedbackCommentList map to fail：" + err.Error())
		out.Error = err.Error()
		out.Message = "查询失败"
		return out, err
	}
	//查询活动的拼团数据
	return out, nil
}
