package common

import (
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
)

//小程序提审数据显示开关，true：显示全部数据,false：显示非药品数据
func IsShowMpData(c echo.Context) bool {
	// envVersion := c.Request().Header.Get("EnvVersion") //小程序环境变量  开发版：develop，体验版：trial，正式版：release
	// if envVersion == "trial" {
	ver := config.GetString("mp.switch")             //获取小程序配置中心配置的审核版本
	mpVersion := c.Request().Header.Get("MpVersion") //小程序版本
	//glog.Info("IsShowMpData", ver, mpVersion)
	if ver == mpVersion {
		return false
	}
	// }
	return true
}

// 开发版
func MpIsDevelop(c echo.Context) bool {
	return c.Request().Header.Get("EnvVersion") == "develop"
}

// 体验版
func MpIsTrial(c echo.Context) bool {
	return c.Request().Header.Get("EnvVersion") == "trial"
}

// 正式环境
func MpIsRelease(c echo.Context) bool {
	return c.Request().Header.Get("EnvVersion") == "release"
}

// 推荐切换兼容开关
func RecommendSwith(c echo.Context) bool {
	return false
	return MpIsRelease(c)
}
