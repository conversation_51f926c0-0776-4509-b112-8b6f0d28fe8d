package common

import (
	"fmt"
	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	"strconv"
)

func GetAnalysisRedisConn() *redis.Client {
	var redisHandle *redis.Client
	if redisHandle != nil {
		_, err := redisHandle.Ping().Result()
		//glog.Info("redis connections: ", redisHandle.PoolStats().TotalConns)
		if err == nil {
			return redisHandle
		}
	}

	var db = cast.ToInt(config.GetString("redis.Analysis.DB"))
	var addr = config.GetString("redis.Analysis.Addr")
	var pwd = config.GetString("redis.Analysis.Password")

	//glog.Info("redis connections:" + addr + ",paw:" + pwd)

	redisHandle = redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: pwd,
		DB:       db,
		//MinIdleConns: 6000,
		// IdleTimeout:  30,
		// PoolSize:     6000,
	})
	_, err := redisHandle.Ping().Result()
	if err != nil {
		panic(err)
	}

	return redisHandle
}

// 生产环境电商和阿闻redis是分开的
func GetRedisUpetConn() *redis.Client {
	var redisHandle *redis.Client
	if redisHandle != nil {
		_, err := redisHandle.Ping().Result()
		if err == nil {
			return redisHandle
		}
	}

	redisHandle = redis.NewClient(&redis.Options{
		Addr:     config.GetString("redis.Upet.Addr"),
		Password: config.GetString("redis.Upet.Password"),
		DB:       cast.ToInt(config.GetString("redis.Upet.DB")),
		//MinIdleConns: 6000,
		// IdleTimeout:  30,
		// PoolSize:     6000,
	})
	_, err := redisHandle.Ping().Result()
	if err != nil {
		panic(err)
	}

	return redisHandle
}

func GetRedisConn() *redis.Client {
	var redisHandle *redis.Client
	if redisHandle != nil {
		_, err := redisHandle.Ping().Result()
		//glog.Info("redis connections: ", redisHandle.PoolStats().TotalConns)
		if err == nil {
			return redisHandle
		}
	}

	var db = cast.ToInt(config.GetString("redis.DB"))
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")

	//glog.Info("redis connections:" + addr + ",paw:" + pwd)

	redisHandle = redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: pwd,
		DB:       db,
		//MinIdleConns: 6000,
		// IdleTimeout:  30,
		// PoolSize:     6000,
	})
	_, err := redisHandle.Ping().Result()
	if err != nil {
		panic(err)
	}

	return redisHandle
}

//获取支付的redis信息(废弃，不要调)
func GetPayRedisConn() *redis.Client {
	var db = cast.ToInt(config.GetString("redis.DB"))
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")

	redisClient := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: pwd,
		DB:       db,
		//MinIdleConns: 6000,
		//MinIdleConns: 28,
		//IdleTimeout:  30,
		//PoolSize:     512,
		//MaxConnAge:   30 * time.Second,
	})
	_, err := redisClient.Ping().Result()
	if err != nil {
		fmt.Println("redis connections:" + addr + ",paw:" + pwd + ",db:" + strconv.Itoa(db))
		panic("redis连接失败")
	}
	return redisClient
}
