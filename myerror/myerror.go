package myerror

import (
	"fmt"
	"runtime"
)

type MyError struct {
	funName string //记录当前函数名
	code    int    //记录当前函数 错误编号（可用于准确定位到发生错误的地方）
	what    string //记录任何你想记录的数据（可以是参数）
	errStr  string //打印到日志里的错误日志
	msg     string //展示给用户的提示语
	line    int    //发生行数
}

func (e *MyError) Error() string {
	return fmt.Sprintf("【【【（函数名）%s|（行数）%d|（code）%d|（记录what）%s|（错误描述）%s|（用户提示语）%s】】】", e.funName, e.line, e.code, e.what, e.errStr, e.msg)
}
func New(funcName string, what string, errStr, msg string, code ...int) *MyError {
	_, _, line, _ := runtime.Caller(1)
	c := 0
	if len(code) > 0 {
		c = code[0]
	}
	return &MyError{
		funName: funcName,
		code:    c,
		what:    what,
		errStr:  errStr,
		msg:     msg,
		line:    line,
	}
}

// 获取用户的提示语
func (e *MyError) GetMsg() string {
	return e.msg
}
func (e *MyError) GetCode() int {
	return e.code
}
