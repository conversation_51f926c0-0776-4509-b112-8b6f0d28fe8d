// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/insurance.proto

package mk

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type InsuranceChoiceSkuReq struct {
	// 当前页
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 每页数量
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	// 开始日期
	BeginDate string `protobuf:"bytes,6,opt,name=beginDate,proto3" json:"beginDate"`
	// 结束日期，留空表示长期
	EndDate string `protobuf:"bytes,7,opt,name=endDate,proto3" json:"endDate"`
	// 当前活动id，更新时
	PromotionId int32 `protobuf:"varint,8,opt,name=promotionId,proto3" json:"promotionId"`
	// skuId
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	// spuId
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,9,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsuranceChoiceSkuReq) Reset()         { *m = InsuranceChoiceSkuReq{} }
func (m *InsuranceChoiceSkuReq) String() string { return proto.CompactTextString(m) }
func (*InsuranceChoiceSkuReq) ProtoMessage()    {}
func (*InsuranceChoiceSkuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{0}
}

func (m *InsuranceChoiceSkuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceChoiceSkuReq.Unmarshal(m, b)
}
func (m *InsuranceChoiceSkuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceChoiceSkuReq.Marshal(b, m, deterministic)
}
func (m *InsuranceChoiceSkuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceChoiceSkuReq.Merge(m, src)
}
func (m *InsuranceChoiceSkuReq) XXX_Size() int {
	return xxx_messageInfo_InsuranceChoiceSkuReq.Size(m)
}
func (m *InsuranceChoiceSkuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceChoiceSkuReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceChoiceSkuReq proto.InternalMessageInfo

func (m *InsuranceChoiceSkuReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *InsuranceChoiceSkuReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *InsuranceChoiceSkuReq) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *InsuranceChoiceSkuReq) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *InsuranceChoiceSkuReq) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *InsuranceChoiceSkuReq) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *InsuranceChoiceSkuReq) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *InsuranceChoiceSkuReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *InsuranceChoiceSkuReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type InsuranceChoiceSkuData struct {
	// skuId
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	// spuId
	ProductId int32 `protobuf:"varint,2,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	// 状态，0活动时间冲突不可选 1可选
	State                int32    `protobuf:"varint,4,opt,name=state,proto3" json:"state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsuranceChoiceSkuData) Reset()         { *m = InsuranceChoiceSkuData{} }
func (m *InsuranceChoiceSkuData) String() string { return proto.CompactTextString(m) }
func (*InsuranceChoiceSkuData) ProtoMessage()    {}
func (*InsuranceChoiceSkuData) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{1}
}

func (m *InsuranceChoiceSkuData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceChoiceSkuData.Unmarshal(m, b)
}
func (m *InsuranceChoiceSkuData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceChoiceSkuData.Marshal(b, m, deterministic)
}
func (m *InsuranceChoiceSkuData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceChoiceSkuData.Merge(m, src)
}
func (m *InsuranceChoiceSkuData) XXX_Size() int {
	return xxx_messageInfo_InsuranceChoiceSkuData.Size(m)
}
func (m *InsuranceChoiceSkuData) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceChoiceSkuData.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceChoiceSkuData proto.InternalMessageInfo

func (m *InsuranceChoiceSkuData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *InsuranceChoiceSkuData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *InsuranceChoiceSkuData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *InsuranceChoiceSkuData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

type InsuranceChoiceSkuRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*InsuranceChoiceSkuData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsuranceChoiceSkuRes) Reset()         { *m = InsuranceChoiceSkuRes{} }
func (m *InsuranceChoiceSkuRes) String() string { return proto.CompactTextString(m) }
func (*InsuranceChoiceSkuRes) ProtoMessage()    {}
func (*InsuranceChoiceSkuRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{2}
}

func (m *InsuranceChoiceSkuRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceChoiceSkuRes.Unmarshal(m, b)
}
func (m *InsuranceChoiceSkuRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceChoiceSkuRes.Marshal(b, m, deterministic)
}
func (m *InsuranceChoiceSkuRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceChoiceSkuRes.Merge(m, src)
}
func (m *InsuranceChoiceSkuRes) XXX_Size() int {
	return xxx_messageInfo_InsuranceChoiceSkuRes.Size(m)
}
func (m *InsuranceChoiceSkuRes) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceChoiceSkuRes.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceChoiceSkuRes proto.InternalMessageInfo

func (m *InsuranceChoiceSkuRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *InsuranceChoiceSkuRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *InsuranceChoiceSkuRes) GetData() []*InsuranceChoiceSkuData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *InsuranceChoiceSkuRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 新增/更新
type InsuranceReq struct {
	//用户Id，即userno
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户Id，即登录人姓名
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName"`
	// 赠险数据
	Insurance            *Insurance `protobuf:"bytes,3,opt,name=insurance,proto3" json:"insurance"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *InsuranceReq) Reset()         { *m = InsuranceReq{} }
func (m *InsuranceReq) String() string { return proto.CompactTextString(m) }
func (*InsuranceReq) ProtoMessage()    {}
func (*InsuranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{3}
}

func (m *InsuranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceReq.Unmarshal(m, b)
}
func (m *InsuranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceReq.Marshal(b, m, deterministic)
}
func (m *InsuranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceReq.Merge(m, src)
}
func (m *InsuranceReq) XXX_Size() int {
	return xxx_messageInfo_InsuranceReq.Size(m)
}
func (m *InsuranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceReq proto.InternalMessageInfo

func (m *InsuranceReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *InsuranceReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *InsuranceReq) GetInsurance() *Insurance {
	if m != nil {
		return m.Insurance
	}
	return nil
}

type Insurance struct {
	// 活动id，新增不用传
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动名称
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title"`
	// 开始日期
	BeginDate string `protobuf:"bytes,4,opt,name=beginDate,proto3" json:"beginDate"`
	// 结束日期，留空表示长期
	EndDate string `protobuf:"bytes,5,opt,name=endDate,proto3" json:"endDate"`
	// 状态 10 未开始，20进行中 30 已结束 31 已撤销
	State int32 `protobuf:"varint,6,opt,name=state,proto3" json:"state"`
	// 协议微页面id
	AgreementWepageId string `protobuf:"bytes,8,opt,name=agreementWepageId,proto3" json:"agreementWepageId"`
	// 协议微页面标题
	AgreementWepageTitle string `protobuf:"bytes,9,opt,name=agreementWepageTitle,proto3" json:"agreementWepageTitle"`
	// 活动商品
	Skus                 []*InsuranceSku `protobuf:"bytes,7,rep,name=skus,proto3" json:"skus"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Insurance) Reset()         { *m = Insurance{} }
func (m *Insurance) String() string { return proto.CompactTextString(m) }
func (*Insurance) ProtoMessage()    {}
func (*Insurance) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{4}
}

func (m *Insurance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Insurance.Unmarshal(m, b)
}
func (m *Insurance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Insurance.Marshal(b, m, deterministic)
}
func (m *Insurance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Insurance.Merge(m, src)
}
func (m *Insurance) XXX_Size() int {
	return xxx_messageInfo_Insurance.Size(m)
}
func (m *Insurance) XXX_DiscardUnknown() {
	xxx_messageInfo_Insurance.DiscardUnknown(m)
}

var xxx_messageInfo_Insurance proto.InternalMessageInfo

func (m *Insurance) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Insurance) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Insurance) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *Insurance) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *Insurance) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *Insurance) GetAgreementWepageId() string {
	if m != nil {
		return m.AgreementWepageId
	}
	return ""
}

func (m *Insurance) GetAgreementWepageTitle() string {
	if m != nil {
		return m.AgreementWepageTitle
	}
	return ""
}

func (m *Insurance) GetSkus() []*InsuranceSku {
	if m != nil {
		return m.Skus
	}
	return nil
}

type InsuranceSkuLinkShop struct {
	// 财务编码
	FinanceCode string `protobuf:"bytes,1,opt,name=financeCode,proto3" json:"financeCode"`
	// 门店名称
	ShopName string `protobuf:"bytes,2,opt,name=shopName,proto3" json:"shopName"`
	// 层级
	Level                int32    `protobuf:"varint,3,opt,name=level,proto3" json:"level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsuranceSkuLinkShop) Reset()         { *m = InsuranceSkuLinkShop{} }
func (m *InsuranceSkuLinkShop) String() string { return proto.CompactTextString(m) }
func (*InsuranceSkuLinkShop) ProtoMessage()    {}
func (*InsuranceSkuLinkShop) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{5}
}

func (m *InsuranceSkuLinkShop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceSkuLinkShop.Unmarshal(m, b)
}
func (m *InsuranceSkuLinkShop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceSkuLinkShop.Marshal(b, m, deterministic)
}
func (m *InsuranceSkuLinkShop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceSkuLinkShop.Merge(m, src)
}
func (m *InsuranceSkuLinkShop) XXX_Size() int {
	return xxx_messageInfo_InsuranceSkuLinkShop.Size(m)
}
func (m *InsuranceSkuLinkShop) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceSkuLinkShop.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceSkuLinkShop proto.InternalMessageInfo

func (m *InsuranceSkuLinkShop) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *InsuranceSkuLinkShop) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *InsuranceSkuLinkShop) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 赠保险
type InsuranceSkuLink struct {
	// 链接
	Link string `protobuf:"bytes,1,opt,name=link,proto3" json:"link"`
	// 适用地区/门店
	Shops                []*InsuranceSkuLinkShop `protobuf:"bytes,3,rep,name=shops,proto3" json:"shops"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *InsuranceSkuLink) Reset()         { *m = InsuranceSkuLink{} }
func (m *InsuranceSkuLink) String() string { return proto.CompactTextString(m) }
func (*InsuranceSkuLink) ProtoMessage()    {}
func (*InsuranceSkuLink) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{6}
}

func (m *InsuranceSkuLink) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceSkuLink.Unmarshal(m, b)
}
func (m *InsuranceSkuLink) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceSkuLink.Marshal(b, m, deterministic)
}
func (m *InsuranceSkuLink) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceSkuLink.Merge(m, src)
}
func (m *InsuranceSkuLink) XXX_Size() int {
	return xxx_messageInfo_InsuranceSkuLink.Size(m)
}
func (m *InsuranceSkuLink) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceSkuLink.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceSkuLink proto.InternalMessageInfo

func (m *InsuranceSkuLink) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *InsuranceSkuLink) GetShops() []*InsuranceSkuLinkShop {
	if m != nil {
		return m.Shops
	}
	return nil
}

// 赠险商品
type InsuranceSku struct {
	// 商品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	// skuId
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	// 商品名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	// 赠品
	Links                []*InsuranceSkuLink `protobuf:"bytes,4,rep,name=links,proto3" json:"links"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *InsuranceSku) Reset()         { *m = InsuranceSku{} }
func (m *InsuranceSku) String() string { return proto.CompactTextString(m) }
func (*InsuranceSku) ProtoMessage()    {}
func (*InsuranceSku) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{7}
}

func (m *InsuranceSku) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceSku.Unmarshal(m, b)
}
func (m *InsuranceSku) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceSku.Marshal(b, m, deterministic)
}
func (m *InsuranceSku) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceSku.Merge(m, src)
}
func (m *InsuranceSku) XXX_Size() int {
	return xxx_messageInfo_InsuranceSku.Size(m)
}
func (m *InsuranceSku) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceSku.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceSku proto.InternalMessageInfo

func (m *InsuranceSku) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *InsuranceSku) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *InsuranceSku) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *InsuranceSku) GetLinks() []*InsuranceSkuLink {
	if m != nil {
		return m.Links
	}
	return nil
}

type InsuranceListReq struct {
	// 当前页
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 每页数量
	PageSize             int32    `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsuranceListReq) Reset()         { *m = InsuranceListReq{} }
func (m *InsuranceListReq) String() string { return proto.CompactTextString(m) }
func (*InsuranceListReq) ProtoMessage()    {}
func (*InsuranceListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{8}
}

func (m *InsuranceListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceListReq.Unmarshal(m, b)
}
func (m *InsuranceListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceListReq.Marshal(b, m, deterministic)
}
func (m *InsuranceListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceListReq.Merge(m, src)
}
func (m *InsuranceListReq) XXX_Size() int {
	return xxx_messageInfo_InsuranceListReq.Size(m)
}
func (m *InsuranceListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceListReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceListReq proto.InternalMessageInfo

func (m *InsuranceListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *InsuranceListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type InsuranceList struct {
	// 活动id，新增不用传
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动名称
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title"`
	// 开始日期
	BeginDate string `protobuf:"bytes,4,opt,name=beginDate,proto3" json:"beginDate"`
	// 结束日期，留空表示长期
	EndDate string `protobuf:"bytes,5,opt,name=endDate,proto3" json:"endDate"`
	// 状态 10 未开始，20进行中 30 已结束 31 已撤销
	State                int32    `protobuf:"varint,6,opt,name=state,proto3" json:"state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsuranceList) Reset()         { *m = InsuranceList{} }
func (m *InsuranceList) String() string { return proto.CompactTextString(m) }
func (*InsuranceList) ProtoMessage()    {}
func (*InsuranceList) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{9}
}

func (m *InsuranceList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceList.Unmarshal(m, b)
}
func (m *InsuranceList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceList.Marshal(b, m, deterministic)
}
func (m *InsuranceList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceList.Merge(m, src)
}
func (m *InsuranceList) XXX_Size() int {
	return xxx_messageInfo_InsuranceList.Size(m)
}
func (m *InsuranceList) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceList.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceList proto.InternalMessageInfo

func (m *InsuranceList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *InsuranceList) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *InsuranceList) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *InsuranceList) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *InsuranceList) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

type InsuranceListRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*InsuranceList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsuranceListRes) Reset()         { *m = InsuranceListRes{} }
func (m *InsuranceListRes) String() string { return proto.CompactTextString(m) }
func (*InsuranceListRes) ProtoMessage()    {}
func (*InsuranceListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{10}
}

func (m *InsuranceListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceListRes.Unmarshal(m, b)
}
func (m *InsuranceListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceListRes.Marshal(b, m, deterministic)
}
func (m *InsuranceListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceListRes.Merge(m, src)
}
func (m *InsuranceListRes) XXX_Size() int {
	return xxx_messageInfo_InsuranceListRes.Size(m)
}
func (m *InsuranceListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceListRes.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceListRes proto.InternalMessageInfo

func (m *InsuranceListRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *InsuranceListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *InsuranceListRes) GetData() []*InsuranceList {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *InsuranceListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type InsuranceDetailReq struct {
	// 活动id
	Id                   int32    `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsuranceDetailReq) Reset()         { *m = InsuranceDetailReq{} }
func (m *InsuranceDetailReq) String() string { return proto.CompactTextString(m) }
func (*InsuranceDetailReq) ProtoMessage()    {}
func (*InsuranceDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{11}
}

func (m *InsuranceDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceDetailReq.Unmarshal(m, b)
}
func (m *InsuranceDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceDetailReq.Marshal(b, m, deterministic)
}
func (m *InsuranceDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceDetailReq.Merge(m, src)
}
func (m *InsuranceDetailReq) XXX_Size() int {
	return xxx_messageInfo_InsuranceDetailReq.Size(m)
}
func (m *InsuranceDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceDetailReq proto.InternalMessageInfo

func (m *InsuranceDetailReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type InsuranceDetailRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message              string     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *Insurance `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *InsuranceDetailRes) Reset()         { *m = InsuranceDetailRes{} }
func (m *InsuranceDetailRes) String() string { return proto.CompactTextString(m) }
func (*InsuranceDetailRes) ProtoMessage()    {}
func (*InsuranceDetailRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{12}
}

func (m *InsuranceDetailRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceDetailRes.Unmarshal(m, b)
}
func (m *InsuranceDetailRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceDetailRes.Marshal(b, m, deterministic)
}
func (m *InsuranceDetailRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceDetailRes.Merge(m, src)
}
func (m *InsuranceDetailRes) XXX_Size() int {
	return xxx_messageInfo_InsuranceDetailRes.Size(m)
}
func (m *InsuranceDetailRes) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceDetailRes.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceDetailRes proto.InternalMessageInfo

func (m *InsuranceDetailRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *InsuranceDetailRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *InsuranceDetailRes) GetData() *Insurance {
	if m != nil {
		return m.Data
	}
	return nil
}

type InsuranceCancelReq struct {
	//用户Id，即userno
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户Id，即登录人姓名
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName"`
	// 活动id
	Id                   int32    `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsuranceCancelReq) Reset()         { *m = InsuranceCancelReq{} }
func (m *InsuranceCancelReq) String() string { return proto.CompactTextString(m) }
func (*InsuranceCancelReq) ProtoMessage()    {}
func (*InsuranceCancelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3250b8a707b9398a, []int{13}
}

func (m *InsuranceCancelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsuranceCancelReq.Unmarshal(m, b)
}
func (m *InsuranceCancelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsuranceCancelReq.Marshal(b, m, deterministic)
}
func (m *InsuranceCancelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsuranceCancelReq.Merge(m, src)
}
func (m *InsuranceCancelReq) XXX_Size() int {
	return xxx_messageInfo_InsuranceCancelReq.Size(m)
}
func (m *InsuranceCancelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsuranceCancelReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsuranceCancelReq proto.InternalMessageInfo

func (m *InsuranceCancelReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *InsuranceCancelReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *InsuranceCancelReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func init() {
	proto.RegisterType((*InsuranceChoiceSkuReq)(nil), "mk.InsuranceChoiceSkuReq")
	proto.RegisterType((*InsuranceChoiceSkuData)(nil), "mk.InsuranceChoiceSkuData")
	proto.RegisterType((*InsuranceChoiceSkuRes)(nil), "mk.InsuranceChoiceSkuRes")
	proto.RegisterType((*InsuranceReq)(nil), "mk.InsuranceReq")
	proto.RegisterType((*Insurance)(nil), "mk.Insurance")
	proto.RegisterType((*InsuranceSkuLinkShop)(nil), "mk.InsuranceSkuLinkShop")
	proto.RegisterType((*InsuranceSkuLink)(nil), "mk.InsuranceSkuLink")
	proto.RegisterType((*InsuranceSku)(nil), "mk.InsuranceSku")
	proto.RegisterType((*InsuranceListReq)(nil), "mk.InsuranceListReq")
	proto.RegisterType((*InsuranceList)(nil), "mk.InsuranceList")
	proto.RegisterType((*InsuranceListRes)(nil), "mk.InsuranceListRes")
	proto.RegisterType((*InsuranceDetailReq)(nil), "mk.InsuranceDetailReq")
	proto.RegisterType((*InsuranceDetailRes)(nil), "mk.InsuranceDetailRes")
	proto.RegisterType((*InsuranceCancelReq)(nil), "mk.InsuranceCancelReq")
}

func init() { proto.RegisterFile("mk/insurance.proto", fileDescriptor_3250b8a707b9398a) }

var fileDescriptor_3250b8a707b9398a = []byte{
	// 731 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x56, 0xcd, 0x6f, 0xd3, 0x4a,
	0x10, 0x97, 0x1d, 0xdb, 0xad, 0xa7, 0xaf, 0x4f, 0xed, 0x2a, 0xad, 0xfc, 0xa2, 0x77, 0x08, 0x56,
	0x91, 0x2a, 0x40, 0x41, 0x0a, 0x17, 0xae, 0xa8, 0xbd, 0x44, 0xaa, 0x38, 0x38, 0x08, 0xb8, 0x21,
	0x37, 0x1e, 0xd2, 0xc5, 0x1f, 0x9b, 0x7a, 0xd7, 0x15, 0x42, 0xe2, 0x02, 0x07, 0x24, 0xf8, 0x37,
	0xf8, 0x43, 0xd1, 0x8e, 0x3f, 0x62, 0x27, 0x6e, 0xa5, 0xc2, 0x81, 0x4b, 0xb4, 0x33, 0xb3, 0x3b,
	0xf3, 0x9b, 0xdf, 0x7c, 0x38, 0xc0, 0xd2, 0xf8, 0x29, 0xcf, 0x64, 0x91, 0x87, 0xd9, 0x02, 0x27,
	0xab, 0x5c, 0x28, 0xc1, 0xcc, 0x34, 0x1e, 0x69, 0xfd, 0x2a, 0x17, 0xa9, 0x50, 0x5c, 0x64, 0xa5,
	0xde, 0xff, 0x6e, 0xc2, 0xd1, 0xac, 0xbe, 0x7b, 0x76, 0x25, 0xf8, 0x02, 0xe7, 0x71, 0x11, 0xe0,
	0x35, 0xfb, 0x1f, 0xdc, 0x55, 0xb8, 0xc4, 0x59, 0x16, 0xe1, 0x47, 0xcf, 0x18, 0x1b, 0xa7, 0x76,
	0xb0, 0x56, 0xb0, 0x11, 0xec, 0x6a, 0x61, 0xce, 0x3f, 0xa1, 0x67, 0x92, 0xb1, 0x91, 0xf5, 0xcb,
	0x4b, 0x5c, 0xf2, 0xec, 0x3c, 0x54, 0xe8, 0x39, 0x63, 0xe3, 0xd4, 0x0d, 0xd6, 0x0a, 0xe6, 0xc1,
	0x0e, 0x66, 0x11, 0xd9, 0x76, 0xc8, 0x56, 0x8b, 0x6c, 0x0c, 0x7b, 0x0d, 0xbc, 0x59, 0xe4, 0xed,
	0x92, 0xdb, 0xb6, 0x8a, 0x0d, 0xc1, 0x96, 0x71, 0x31, 0x8b, 0xbc, 0x01, 0xd9, 0x4a, 0x81, 0x90,
	0xe6, 0x22, 0x2a, 0x16, 0x6a, 0x16, 0x79, 0x56, 0x85, 0xb4, 0x56, 0x30, 0x06, 0x56, 0x16, 0xa6,
	0xe8, 0xd9, 0x14, 0x8c, 0xce, 0xec, 0x08, 0x1c, 0x91, 0x2f, 0xdf, 0xf1, 0xc8, 0x73, 0x4b, 0x47,
	0x22, 0x5f, 0xce, 0x22, 0xff, 0x06, 0x8e, 0xb7, 0xb9, 0x38, 0x0f, 0x55, 0xb8, 0x0e, 0x6c, 0xdc,
	0x1a, 0xd8, 0xbc, 0x2d, 0xf0, 0xa0, 0x15, 0x58, 0xfb, 0x51, 0x3a, 0x75, 0xab, 0xf2, 0xa3, 0x05,
	0xff, 0x87, 0xd1, 0x5f, 0x04, 0xa9, 0x7d, 0x2c, 0x44, 0x84, 0x55, 0x58, 0x3a, 0x6b, 0x02, 0x53,
	0x94, 0x32, 0x5c, 0x96, 0xcc, 0xbb, 0x41, 0x2d, 0xb2, 0x09, 0x58, 0x51, 0xa8, 0x42, 0x6f, 0x30,
	0x1e, 0x9c, 0xee, 0x4d, 0x47, 0x93, 0x34, 0x9e, 0xf4, 0xe7, 0x13, 0xd0, 0x3d, 0x8d, 0x46, 0x09,
	0x15, 0x26, 0x35, 0x1a, 0x12, 0x7c, 0x01, 0xff, 0x34, 0xaf, 0x74, 0x23, 0x1c, 0x83, 0x53, 0x48,
	0xcc, 0xab, 0xe4, 0xdd, 0xa0, 0x92, 0x74, 0x0b, 0xe8, 0xd3, 0x4b, 0x9d, 0x63, 0x09, 0xa4, 0x91,
	0xd9, 0x63, 0x70, 0x9b, 0x0e, 0x24, 0x02, 0xf6, 0xa6, 0xfb, 0x1d, 0x38, 0xc1, 0xda, 0xee, 0x7f,
	0x33, 0xc1, 0x6d, 0x0c, 0xec, 0x5f, 0x30, 0x79, 0xcd, 0xb3, 0xc9, 0xa9, 0xe6, 0x8a, 0xab, 0xa4,
	0xe6, 0xb1, 0x14, 0xba, 0x3d, 0x66, 0xdd, 0xd1, 0x63, 0x76, 0xb7, 0xc7, 0x9a, 0x02, 0x38, 0xad,
	0x02, 0xb0, 0x27, 0x70, 0x18, 0x2e, 0x73, 0xc4, 0x14, 0x33, 0xf5, 0x06, 0xa9, 0xcb, 0xcb, 0xfe,
	0x73, 0x83, 0x6d, 0x03, 0x9b, 0xc2, 0x70, 0x43, 0xf9, 0x8a, 0x00, 0xba, 0xf4, 0xa0, 0xd7, 0xc6,
	0x4e, 0xc0, 0x92, 0x71, 0x21, 0xbd, 0x1d, 0x2a, 0xcd, 0x41, 0x87, 0x0b, 0x5d, 0x6b, 0xb2, 0xfa,
	0x1f, 0x60, 0xd8, 0xd6, 0x5e, 0xf0, 0x2c, 0x9e, 0x5f, 0x89, 0x95, 0x9e, 0x8c, 0xf7, 0x3c, 0xa3,
	0x32, 0xd6, 0xdd, 0xe0, 0x06, 0x6d, 0x95, 0x2e, 0x86, 0xbc, 0x12, 0xab, 0x76, 0x31, 0x6a, 0x59,
	0xe7, 0x9c, 0xe0, 0x0d, 0x26, 0xf5, 0xd4, 0x90, 0xe0, 0xbf, 0x86, 0x83, 0xcd, 0x58, 0xba, 0xdd,
	0x12, 0x9e, 0xc5, 0x55, 0x00, 0x3a, 0xb3, 0x09, 0xd8, 0xda, 0x93, 0xac, 0xba, 0xca, 0xdb, 0x84,
	0x5e, 0x83, 0x0c, 0xca, 0x6b, 0xfe, 0x17, 0xa3, 0xd5, 0x3f, 0xf3, 0xb8, 0xe8, 0x4e, 0x89, 0xbd,
	0x39, 0x25, 0xcd, 0x64, 0x99, 0xed, 0xc9, 0xea, 0x9b, 0x9d, 0x47, 0x60, 0x6b, 0x40, 0xd2, 0xb3,
	0x08, 0xc8, 0xb0, 0x0f, 0x48, 0x50, 0x5e, 0xf1, 0x2f, 0x5a, 0xc9, 0x5d, 0x70, 0xa9, 0xfe, 0x68,
	0xa1, 0xf9, 0x5f, 0x0d, 0xd8, 0xef, 0xb8, 0xfb, 0x1b, 0x4d, 0xea, 0x7f, 0xde, 0xca, 0xe9, 0xbe,
	0xfb, 0xe1, 0x61, 0x67, 0x3f, 0x1c, 0x76, 0x08, 0x24, 0x8f, 0x77, 0xad, 0x85, 0x13, 0x60, 0xcd,
	0xe5, 0x73, 0x54, 0x21, 0x4f, 0x34, 0xa9, 0x25, 0x11, 0x83, 0x9a, 0x08, 0x1f, 0x7b, 0x6e, 0xdd,
	0x17, 0xe6, 0x83, 0x06, 0x66, 0xcf, 0xde, 0x20, 0x93, 0xff, 0xb6, 0x15, 0xe6, 0x4c, 0xff, 0x24,
	0xbf, 0xbb, 0xa9, 0x36, 0x12, 0x98, 0xfe, 0x34, 0xdb, 0x73, 0x81, 0xf9, 0x0d, 0x5f, 0xe8, 0x75,
	0x66, 0xcf, 0x95, 0xc8, 0x91, 0x75, 0x07, 0x37, 0xc0, 0xeb, 0x11, 0x69, 0x2e, 0x43, 0x89, 0x01,
	0xca, 0x95, 0xc8, 0x24, 0xb2, 0x29, 0x58, 0xd4, 0x23, 0xc3, 0x6d, 0x7e, 0xf1, 0x7a, 0xd4, 0xa7,
	0x95, 0xec, 0x39, 0x38, 0x25, 0x5b, 0xec, 0xb8, 0x63, 0x6f, 0x88, 0x1e, 0xf5, 0xeb, 0x25, 0x9b,
	0x82, 0x53, 0x12, 0xb0, 0xf1, 0xb2, 0x61, 0xa5, 0x07, 0xe1, 0x0b, 0x70, 0x9b, 0xcf, 0x01, 0xfb,
	0xaf, 0xff, 0x33, 0xa1, 0x5f, 0xde, 0x6a, 0x92, 0x97, 0x0e, 0xfd, 0x7d, 0x78, 0xf6, 0x2b, 0x00,
	0x00, 0xff, 0xff, 0x42, 0xca, 0x1c, 0x5d, 0x6c, 0x08, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// InsuranceServiceClient is the client API for InsuranceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type InsuranceServiceClient interface {
	// 新增/编辑
	Store(ctx context.Context, in *InsuranceReq, opts ...grpc.CallOption) (*BaseResponse, error)
	// 列表
	List(ctx context.Context, in *InsuranceListReq, opts ...grpc.CallOption) (*InsuranceListRes, error)
	// 详情
	Detail(ctx context.Context, in *InsuranceDetailReq, opts ...grpc.CallOption) (*InsuranceDetailRes, error)
	// 撤销
	Cancel(ctx context.Context, in *InsuranceCancelReq, opts ...grpc.CallOption) (*BaseResponse, error)
	// 选择商品
	ChoiceSku(ctx context.Context, in *InsuranceChoiceSkuReq, opts ...grpc.CallOption) (*InsuranceChoiceSkuRes, error)
}

type insuranceServiceClient struct {
	cc *grpc.ClientConn
}

func NewInsuranceServiceClient(cc *grpc.ClientConn) InsuranceServiceClient {
	return &insuranceServiceClient{cc}
}

func (c *insuranceServiceClient) Store(ctx context.Context, in *InsuranceReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.InsuranceService/Store", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *insuranceServiceClient) List(ctx context.Context, in *InsuranceListReq, opts ...grpc.CallOption) (*InsuranceListRes, error) {
	out := new(InsuranceListRes)
	err := c.cc.Invoke(ctx, "/mk.InsuranceService/List", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *insuranceServiceClient) Detail(ctx context.Context, in *InsuranceDetailReq, opts ...grpc.CallOption) (*InsuranceDetailRes, error) {
	out := new(InsuranceDetailRes)
	err := c.cc.Invoke(ctx, "/mk.InsuranceService/Detail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *insuranceServiceClient) Cancel(ctx context.Context, in *InsuranceCancelReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.InsuranceService/Cancel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *insuranceServiceClient) ChoiceSku(ctx context.Context, in *InsuranceChoiceSkuReq, opts ...grpc.CallOption) (*InsuranceChoiceSkuRes, error) {
	out := new(InsuranceChoiceSkuRes)
	err := c.cc.Invoke(ctx, "/mk.InsuranceService/ChoiceSku", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InsuranceServiceServer is the server API for InsuranceService service.
type InsuranceServiceServer interface {
	// 新增/编辑
	Store(context.Context, *InsuranceReq) (*BaseResponse, error)
	// 列表
	List(context.Context, *InsuranceListReq) (*InsuranceListRes, error)
	// 详情
	Detail(context.Context, *InsuranceDetailReq) (*InsuranceDetailRes, error)
	// 撤销
	Cancel(context.Context, *InsuranceCancelReq) (*BaseResponse, error)
	// 选择商品
	ChoiceSku(context.Context, *InsuranceChoiceSkuReq) (*InsuranceChoiceSkuRes, error)
}

// UnimplementedInsuranceServiceServer can be embedded to have forward compatible implementations.
type UnimplementedInsuranceServiceServer struct {
}

func (*UnimplementedInsuranceServiceServer) Store(ctx context.Context, req *InsuranceReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Store not implemented")
}
func (*UnimplementedInsuranceServiceServer) List(ctx context.Context, req *InsuranceListReq) (*InsuranceListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (*UnimplementedInsuranceServiceServer) Detail(ctx context.Context, req *InsuranceDetailReq) (*InsuranceDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Detail not implemented")
}
func (*UnimplementedInsuranceServiceServer) Cancel(ctx context.Context, req *InsuranceCancelReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}
func (*UnimplementedInsuranceServiceServer) ChoiceSku(ctx context.Context, req *InsuranceChoiceSkuReq) (*InsuranceChoiceSkuRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChoiceSku not implemented")
}

func RegisterInsuranceServiceServer(s *grpc.Server, srv InsuranceServiceServer) {
	s.RegisterService(&_InsuranceService_serviceDesc, srv)
}

func _InsuranceService_Store_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsuranceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceServiceServer).Store(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.InsuranceService/Store",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceServiceServer).Store(ctx, req.(*InsuranceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InsuranceService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsuranceListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.InsuranceService/List",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceServiceServer).List(ctx, req.(*InsuranceListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InsuranceService_Detail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsuranceDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceServiceServer).Detail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.InsuranceService/Detail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceServiceServer).Detail(ctx, req.(*InsuranceDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InsuranceService_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsuranceCancelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceServiceServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.InsuranceService/Cancel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceServiceServer).Cancel(ctx, req.(*InsuranceCancelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InsuranceService_ChoiceSku_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsuranceChoiceSkuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceServiceServer).ChoiceSku(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.InsuranceService/ChoiceSku",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceServiceServer).ChoiceSku(ctx, req.(*InsuranceChoiceSkuReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _InsuranceService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mk.InsuranceService",
	HandlerType: (*InsuranceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Store",
			Handler:    _InsuranceService_Store_Handler,
		},
		{
			MethodName: "List",
			Handler:    _InsuranceService_List_Handler,
		},
		{
			MethodName: "Detail",
			Handler:    _InsuranceService_Detail_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _InsuranceService_Cancel_Handler,
		},
		{
			MethodName: "ChoiceSku",
			Handler:    _InsuranceService_ChoiceSku_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mk/insurance.proto",
}
