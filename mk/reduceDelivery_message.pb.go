// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/reduceDelivery_message.proto

package mk

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

/////////////////////////////////////////////////  Dto  ///////////////////////////////////////////////////////////////////////////////
// 查询满减运费数据Dto
type ReduceDeliveryShopDto struct {
	// 关联关系唯一Id
	ReduceDeliveryShopId int32 `protobuf:"varint,13,opt,name=reduceDeliveryShopId,proto3" json:"reduceDeliveryShopId"`
	// 店铺Id
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	// 店铺名称
	ShopName string `protobuf:"bytes,2,opt,name=shopName,proto3" json:"shopName"`
	// 促销活动名称
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title"`
	// 活动详情
	Summary string `protobuf:"bytes,4,opt,name=summary,proto3" json:"summary"`
	// 活动时间
	DateRange string `protobuf:"bytes,5,opt,name=dateRange,proto3" json:"dateRange"`
	// 周期循环
	WeekDay string `protobuf:"bytes,6,opt,name=weekDay,proto3" json:"weekDay"`
	// 生效时段
	TimeRange string `protobuf:"bytes,7,opt,name=timeRange,proto3" json:"timeRange"`
	// 状态
	State                PromotionState `protobuf:"varint,12,opt,name=state,proto3,enum=mk.PromotionState" json:"state"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ReduceDeliveryShopDto) Reset()         { *m = ReduceDeliveryShopDto{} }
func (m *ReduceDeliveryShopDto) String() string { return proto.CompactTextString(m) }
func (*ReduceDeliveryShopDto) ProtoMessage()    {}
func (*ReduceDeliveryShopDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f4a11cc54e7372f, []int{0}
}

func (m *ReduceDeliveryShopDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReduceDeliveryShopDto.Unmarshal(m, b)
}
func (m *ReduceDeliveryShopDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReduceDeliveryShopDto.Marshal(b, m, deterministic)
}
func (m *ReduceDeliveryShopDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReduceDeliveryShopDto.Merge(m, src)
}
func (m *ReduceDeliveryShopDto) XXX_Size() int {
	return xxx_messageInfo_ReduceDeliveryShopDto.Size(m)
}
func (m *ReduceDeliveryShopDto) XXX_DiscardUnknown() {
	xxx_messageInfo_ReduceDeliveryShopDto.DiscardUnknown(m)
}

var xxx_messageInfo_ReduceDeliveryShopDto proto.InternalMessageInfo

func (m *ReduceDeliveryShopDto) GetReduceDeliveryShopId() int32 {
	if m != nil {
		return m.ReduceDeliveryShopId
	}
	return 0
}

func (m *ReduceDeliveryShopDto) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *ReduceDeliveryShopDto) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *ReduceDeliveryShopDto) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ReduceDeliveryShopDto) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

func (m *ReduceDeliveryShopDto) GetDateRange() string {
	if m != nil {
		return m.DateRange
	}
	return ""
}

func (m *ReduceDeliveryShopDto) GetWeekDay() string {
	if m != nil {
		return m.WeekDay
	}
	return ""
}

func (m *ReduceDeliveryShopDto) GetTimeRange() string {
	if m != nil {
		return m.TimeRange
	}
	return ""
}

func (m *ReduceDeliveryShopDto) GetState() PromotionState {
	if m != nil {
		return m.State
	}
	return PromotionState_zero
}

// 根据条件查询满减活动与店铺关联关系Dto
type ReachReduceDeliveryPromotionShopDto struct {
	// 促销活动
	PromotionDto *PromotionListDto `protobuf:"bytes,1,opt,name=promotionDto,proto3" json:"promotionDto"`
	// 促销活动和店铺关联关系
	PromotionShopDto *PromotionShopDto `protobuf:"bytes,2,opt,name=promotionShopDto,proto3" json:"promotionShopDto"`
	// 活动时间
	TimeRanges []*PromotionTime `protobuf:"bytes,3,rep,name=timeRanges,proto3" json:"timeRanges"`
	//满减运费
	PromotionReduceDelivery []*PromotionReduceDeliveryDto `protobuf:"bytes,4,rep,name=promotionReduceDelivery,proto3" json:"promotionReduceDelivery"`
	XXX_NoUnkeyedLiteral    struct{}                      `json:"-"`
	XXX_unrecognized        []byte                        `json:"-"`
	XXX_sizecache           int32                         `json:"-"`
}

func (m *ReachReduceDeliveryPromotionShopDto) Reset()         { *m = ReachReduceDeliveryPromotionShopDto{} }
func (m *ReachReduceDeliveryPromotionShopDto) String() string { return proto.CompactTextString(m) }
func (*ReachReduceDeliveryPromotionShopDto) ProtoMessage()    {}
func (*ReachReduceDeliveryPromotionShopDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f4a11cc54e7372f, []int{1}
}

func (m *ReachReduceDeliveryPromotionShopDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReachReduceDeliveryPromotionShopDto.Unmarshal(m, b)
}
func (m *ReachReduceDeliveryPromotionShopDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReachReduceDeliveryPromotionShopDto.Marshal(b, m, deterministic)
}
func (m *ReachReduceDeliveryPromotionShopDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReachReduceDeliveryPromotionShopDto.Merge(m, src)
}
func (m *ReachReduceDeliveryPromotionShopDto) XXX_Size() int {
	return xxx_messageInfo_ReachReduceDeliveryPromotionShopDto.Size(m)
}
func (m *ReachReduceDeliveryPromotionShopDto) XXX_DiscardUnknown() {
	xxx_messageInfo_ReachReduceDeliveryPromotionShopDto.DiscardUnknown(m)
}

var xxx_messageInfo_ReachReduceDeliveryPromotionShopDto proto.InternalMessageInfo

func (m *ReachReduceDeliveryPromotionShopDto) GetPromotionDto() *PromotionListDto {
	if m != nil {
		return m.PromotionDto
	}
	return nil
}

func (m *ReachReduceDeliveryPromotionShopDto) GetPromotionShopDto() *PromotionShopDto {
	if m != nil {
		return m.PromotionShopDto
	}
	return nil
}

func (m *ReachReduceDeliveryPromotionShopDto) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *ReachReduceDeliveryPromotionShopDto) GetPromotionReduceDelivery() []*PromotionReduceDeliveryDto {
	if m != nil {
		return m.PromotionReduceDelivery
	}
	return nil
}

// 新增
type ReduceDeliveryAddRequest struct {
	//用户Id，即userno
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户Id，即登录人姓名
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName"`
	// 活动基本信息
	Promotion *PromotionDto `protobuf:"bytes,3,opt,name=promotion,proto3" json:"promotion"`
	// 时间
	TimeRanges []*PromotionTime `protobuf:"bytes,4,rep,name=timeRanges,proto3" json:"timeRanges"`
	// 应用店铺
	Shops []*PromotionShopDto `protobuf:"bytes,5,rep,name=shops,proto3" json:"shops"`
	// 满减配置
	ReduceDeliveries     []*PromotionReduceDeliveryDto `protobuf:"bytes,6,rep,name=reduceDeliveries,proto3" json:"reduceDeliveries"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *ReduceDeliveryAddRequest) Reset()         { *m = ReduceDeliveryAddRequest{} }
func (m *ReduceDeliveryAddRequest) String() string { return proto.CompactTextString(m) }
func (*ReduceDeliveryAddRequest) ProtoMessage()    {}
func (*ReduceDeliveryAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f4a11cc54e7372f, []int{2}
}

func (m *ReduceDeliveryAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReduceDeliveryAddRequest.Unmarshal(m, b)
}
func (m *ReduceDeliveryAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReduceDeliveryAddRequest.Marshal(b, m, deterministic)
}
func (m *ReduceDeliveryAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReduceDeliveryAddRequest.Merge(m, src)
}
func (m *ReduceDeliveryAddRequest) XXX_Size() int {
	return xxx_messageInfo_ReduceDeliveryAddRequest.Size(m)
}
func (m *ReduceDeliveryAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReduceDeliveryAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReduceDeliveryAddRequest proto.InternalMessageInfo

func (m *ReduceDeliveryAddRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ReduceDeliveryAddRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *ReduceDeliveryAddRequest) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *ReduceDeliveryAddRequest) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *ReduceDeliveryAddRequest) GetShops() []*PromotionShopDto {
	if m != nil {
		return m.Shops
	}
	return nil
}

func (m *ReduceDeliveryAddRequest) GetReduceDeliveries() []*PromotionReduceDeliveryDto {
	if m != nil {
		return m.ReduceDeliveries
	}
	return nil
}

// 修改
type ReduceDeliveryUpdateRequest struct {
	//用户Id，即userno
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户Id，即登录人姓名
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName"`
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,3,opt,name=promotionId,proto3" json:"promotionId"`
	// 活动基本信息
	Promotion *PromotionDto `protobuf:"bytes,4,opt,name=promotion,proto3" json:"promotion"`
	// 时间区间
	TimeRanges []*PromotionTime `protobuf:"bytes,5,rep,name=timeRanges,proto3" json:"timeRanges"`
	// 应用店铺
	Shops []*PromotionShopDto `protobuf:"bytes,6,rep,name=shops,proto3" json:"shops"`
	// 满减配置
	ReduceDeliveries     []*PromotionReduceDeliveryDto `protobuf:"bytes,7,rep,name=reduceDeliveries,proto3" json:"reduceDeliveries"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *ReduceDeliveryUpdateRequest) Reset()         { *m = ReduceDeliveryUpdateRequest{} }
func (m *ReduceDeliveryUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*ReduceDeliveryUpdateRequest) ProtoMessage()    {}
func (*ReduceDeliveryUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f4a11cc54e7372f, []int{3}
}

func (m *ReduceDeliveryUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReduceDeliveryUpdateRequest.Unmarshal(m, b)
}
func (m *ReduceDeliveryUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReduceDeliveryUpdateRequest.Marshal(b, m, deterministic)
}
func (m *ReduceDeliveryUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReduceDeliveryUpdateRequest.Merge(m, src)
}
func (m *ReduceDeliveryUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_ReduceDeliveryUpdateRequest.Size(m)
}
func (m *ReduceDeliveryUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReduceDeliveryUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReduceDeliveryUpdateRequest proto.InternalMessageInfo

func (m *ReduceDeliveryUpdateRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ReduceDeliveryUpdateRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *ReduceDeliveryUpdateRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *ReduceDeliveryUpdateRequest) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *ReduceDeliveryUpdateRequest) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *ReduceDeliveryUpdateRequest) GetShops() []*PromotionShopDto {
	if m != nil {
		return m.Shops
	}
	return nil
}

func (m *ReduceDeliveryUpdateRequest) GetReduceDeliveries() []*PromotionReduceDeliveryDto {
	if m != nil {
		return m.ReduceDeliveries
	}
	return nil
}

// 查询满减运费数据请求
type ReduceDeliveryShopRequest struct {
	// 页索引
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	// 店铺列表，逗号分割
	ShopIds string `protobuf:"bytes,3,opt,name=shopIds,proto3" json:"shopIds"`
	// 当前登录用户Id
	UserId string `protobuf:"bytes,4,opt,name=userId,proto3" json:"userId"`
	// 0 所有 1 进行中 2 待生效 3 已结束 4 冻结中
	State PromotionState `protobuf:"varint,5,opt,name=state,proto3,enum=mk.PromotionState" json:"state"`
	// 开始日期
	BeginDate string `protobuf:"bytes,6,opt,name=beginDate,proto3" json:"beginDate"`
	// 截止日期日期
	EndDate string `protobuf:"bytes,7,opt,name=endDate,proto3" json:"endDate"`
	// 多个活动id
	Ids                  string   `protobuf:"bytes,8,opt,name=Ids,proto3" json:"Ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReduceDeliveryShopRequest) Reset()         { *m = ReduceDeliveryShopRequest{} }
func (m *ReduceDeliveryShopRequest) String() string { return proto.CompactTextString(m) }
func (*ReduceDeliveryShopRequest) ProtoMessage()    {}
func (*ReduceDeliveryShopRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f4a11cc54e7372f, []int{4}
}

func (m *ReduceDeliveryShopRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReduceDeliveryShopRequest.Unmarshal(m, b)
}
func (m *ReduceDeliveryShopRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReduceDeliveryShopRequest.Marshal(b, m, deterministic)
}
func (m *ReduceDeliveryShopRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReduceDeliveryShopRequest.Merge(m, src)
}
func (m *ReduceDeliveryShopRequest) XXX_Size() int {
	return xxx_messageInfo_ReduceDeliveryShopRequest.Size(m)
}
func (m *ReduceDeliveryShopRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReduceDeliveryShopRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReduceDeliveryShopRequest proto.InternalMessageInfo

func (m *ReduceDeliveryShopRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ReduceDeliveryShopRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ReduceDeliveryShopRequest) GetShopIds() string {
	if m != nil {
		return m.ShopIds
	}
	return ""
}

func (m *ReduceDeliveryShopRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ReduceDeliveryShopRequest) GetState() PromotionState {
	if m != nil {
		return m.State
	}
	return PromotionState_zero
}

func (m *ReduceDeliveryShopRequest) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *ReduceDeliveryShopRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *ReduceDeliveryShopRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

/////////////////////////////////////////////////  Response  ///////////////////////////////////////////////////////////////////////////////
// 查询满减运费数据请求
type ReduceDeliveryShopResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总条数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 当前页数据
	Data                 []*ReachReduceDeliveryPromotionShopDto `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *ReduceDeliveryShopResponse) Reset()         { *m = ReduceDeliveryShopResponse{} }
func (m *ReduceDeliveryShopResponse) String() string { return proto.CompactTextString(m) }
func (*ReduceDeliveryShopResponse) ProtoMessage()    {}
func (*ReduceDeliveryShopResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f4a11cc54e7372f, []int{5}
}

func (m *ReduceDeliveryShopResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReduceDeliveryShopResponse.Unmarshal(m, b)
}
func (m *ReduceDeliveryShopResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReduceDeliveryShopResponse.Marshal(b, m, deterministic)
}
func (m *ReduceDeliveryShopResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReduceDeliveryShopResponse.Merge(m, src)
}
func (m *ReduceDeliveryShopResponse) XXX_Size() int {
	return xxx_messageInfo_ReduceDeliveryShopResponse.Size(m)
}
func (m *ReduceDeliveryShopResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReduceDeliveryShopResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReduceDeliveryShopResponse proto.InternalMessageInfo

func (m *ReduceDeliveryShopResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *ReduceDeliveryShopResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ReduceDeliveryShopResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ReduceDeliveryShopResponse) GetData() []*ReachReduceDeliveryPromotionShopDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// 限时促销 数据响应
type ReduceDeliveryByIdResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message     string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	PromotionId int32  `protobuf:"varint,3,opt,name=promotionId,proto3" json:"promotionId"`
	// 活动基本信息
	Promotion *PromotionDto `protobuf:"bytes,4,opt,name=promotion,proto3" json:"promotion"`
	// 时间限制
	TimeRanges []*PromotionTime `protobuf:"bytes,6,rep,name=timeRanges,proto3" json:"timeRanges"`
	// 应用店铺
	Shops []*PromotionShopDto `protobuf:"bytes,7,rep,name=shops,proto3" json:"shops"`
	// 满减优惠
	ReduceDeliveries     []*PromotionReduceDeliveryDto `protobuf:"bytes,8,rep,name=reduceDeliveries,proto3" json:"reduceDeliveries"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *ReduceDeliveryByIdResponse) Reset()         { *m = ReduceDeliveryByIdResponse{} }
func (m *ReduceDeliveryByIdResponse) String() string { return proto.CompactTextString(m) }
func (*ReduceDeliveryByIdResponse) ProtoMessage()    {}
func (*ReduceDeliveryByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f4a11cc54e7372f, []int{6}
}

func (m *ReduceDeliveryByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReduceDeliveryByIdResponse.Unmarshal(m, b)
}
func (m *ReduceDeliveryByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReduceDeliveryByIdResponse.Marshal(b, m, deterministic)
}
func (m *ReduceDeliveryByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReduceDeliveryByIdResponse.Merge(m, src)
}
func (m *ReduceDeliveryByIdResponse) XXX_Size() int {
	return xxx_messageInfo_ReduceDeliveryByIdResponse.Size(m)
}
func (m *ReduceDeliveryByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReduceDeliveryByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReduceDeliveryByIdResponse proto.InternalMessageInfo

func (m *ReduceDeliveryByIdResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *ReduceDeliveryByIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ReduceDeliveryByIdResponse) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *ReduceDeliveryByIdResponse) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *ReduceDeliveryByIdResponse) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *ReduceDeliveryByIdResponse) GetShops() []*PromotionShopDto {
	if m != nil {
		return m.Shops
	}
	return nil
}

func (m *ReduceDeliveryByIdResponse) GetReduceDeliveries() []*PromotionReduceDeliveryDto {
	if m != nil {
		return m.ReduceDeliveries
	}
	return nil
}

func init() {
	proto.RegisterType((*ReduceDeliveryShopDto)(nil), "mk.reduceDeliveryShopDto")
	proto.RegisterType((*ReachReduceDeliveryPromotionShopDto)(nil), "mk.reachReduceDeliveryPromotionShopDto")
	proto.RegisterType((*ReduceDeliveryAddRequest)(nil), "mk.reduceDeliveryAddRequest")
	proto.RegisterType((*ReduceDeliveryUpdateRequest)(nil), "mk.reduceDeliveryUpdateRequest")
	proto.RegisterType((*ReduceDeliveryShopRequest)(nil), "mk.reduceDeliveryShopRequest")
	proto.RegisterType((*ReduceDeliveryShopResponse)(nil), "mk.reduceDeliveryShopResponse")
	proto.RegisterType((*ReduceDeliveryByIdResponse)(nil), "mk.reduceDeliveryByIdResponse")
}

func init() { proto.RegisterFile("mk/reduceDelivery_message.proto", fileDescriptor_2f4a11cc54e7372f) }

var fileDescriptor_2f4a11cc54e7372f = []byte{
	// 644 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x55, 0x4f, 0x6f, 0x13, 0x3f,
	0x10, 0x55, 0x36, 0xbb, 0xf9, 0x33, 0xe9, 0xaf, 0xca, 0xcf, 0x2a, 0x60, 0x4a, 0x05, 0x51, 0x38,
	0x10, 0x71, 0x68, 0x45, 0xb8, 0x20, 0x71, 0x01, 0x94, 0x4b, 0x10, 0x42, 0xc8, 0x05, 0x89, 0x1b,
	0x72, 0xeb, 0x51, 0xba, 0x4a, 0x36, 0x0e, 0xb1, 0x03, 0x84, 0x2f, 0x84, 0xb8, 0xc0, 0x37, 0x41,
	0xe2, 0xf3, 0x70, 0x41, 0x63, 0x6f, 0x36, 0x71, 0x52, 0x44, 0x9b, 0x8a, 0x9b, 0xdf, 0xcc, 0x3c,
	0x7b, 0xe6, 0xe5, 0x65, 0x16, 0xee, 0x64, 0xc3, 0xa3, 0x29, 0xaa, 0xd9, 0x29, 0xf6, 0x70, 0x94,
	0x7e, 0xc0, 0xe9, 0xfc, 0x5d, 0x86, 0xc6, 0xc8, 0x01, 0x1e, 0x4e, 0xa6, 0xda, 0x6a, 0x16, 0x65,
	0xc3, 0xfd, 0xdd, 0x6c, 0x78, 0x94, 0x69, 0x85, 0x23, 0x1f, 0x6b, 0x7f, 0x8f, 0xe0, 0x5a, 0x48,
	0x3a, 0x3e, 0xd3, 0x93, 0x9e, 0xd5, 0xac, 0x0b, 0x7b, 0x9b, 0x89, 0xbe, 0xe2, 0xff, 0xb5, 0x4a,
	0x9d, 0x44, 0x9c, 0x9b, 0x63, 0xd7, 0xa1, 0x62, 0x7c, 0x55, 0xa9, 0x55, 0xea, 0xd4, 0x45, 0x8e,
	0xd8, 0x3e, 0xd4, 0xe8, 0xf4, 0x52, 0x66, 0xc8, 0x23, 0x97, 0x29, 0x30, 0xdb, 0x83, 0xc4, 0xa6,
	0x76, 0x84, 0xbc, 0xec, 0x12, 0x1e, 0x30, 0x0e, 0x55, 0x33, 0xcb, 0x32, 0x39, 0x9d, 0xf3, 0xd8,
	0xc5, 0x17, 0x90, 0x1d, 0x40, 0x5d, 0x49, 0x8b, 0x42, 0x8e, 0x07, 0xc8, 0x13, 0x97, 0x5b, 0x06,
	0x88, 0xf7, 0x11, 0x71, 0xd8, 0x93, 0x73, 0x5e, 0xf1, 0xbc, 0x1c, 0x12, 0xcf, 0xa6, 0x59, 0xce,
	0xab, 0x7a, 0x5e, 0x11, 0x60, 0x1d, 0x48, 0x8c, 0x95, 0x16, 0xf9, 0x4e, 0xab, 0xd4, 0xd9, 0xed,
	0xb2, 0xc3, 0x6c, 0x48, 0x0a, 0x65, 0xda, 0xa6, 0x7a, 0x7c, 0x4c, 0x19, 0xe1, 0x0b, 0xda, 0xdf,
	0x22, 0xb8, 0x3b, 0x45, 0x79, 0x7a, 0x26, 0x02, 0x05, 0x5e, 0x15, 0xc5, 0xb9, 0x7e, 0x8f, 0x60,
	0xa7, 0xb8, 0xa0, 0x67, 0xb5, 0x53, 0xa4, 0xd1, 0xdd, 0x0b, 0x2e, 0x7e, 0x91, 0x1a, 0xdb, 0xb3,
	0x5a, 0x04, 0x95, 0xec, 0x09, 0x34, 0x27, 0x6b, 0xb7, 0x39, 0xd5, 0xd6, 0xd9, 0x79, 0x4e, 0x6c,
	0x54, 0xb3, 0x07, 0x00, 0xc5, 0x68, 0x86, 0x97, 0x5b, 0xe5, 0x4e, 0xa3, 0xfb, 0x3f, 0x71, 0x8b,
	0x2e, 0x5f, 0x53, 0x7a, 0xa5, 0x88, 0xbd, 0x85, 0x1b, 0xc5, 0x35, 0xe1, 0x64, 0x3c, 0x76, 0xfc,
	0xdb, 0xc1, 0xdb, 0x61, 0x09, 0x75, 0xf1, 0x27, 0x7a, 0xfb, 0x6b, 0x04, 0x3c, 0x74, 0xcb, 0x53,
	0xa5, 0x04, 0xbe, 0x9f, 0xa1, 0xb1, 0xe4, 0x98, 0x99, 0xc1, 0xe9, 0xd2, 0x31, 0x1e, 0x91, 0x63,
	0xe8, 0xb4, 0xea, 0x98, 0x05, 0x66, 0x87, 0x50, 0x2f, 0xde, 0x72, 0xae, 0x69, 0x74, 0x9b, 0x41,
	0x73, 0xd4, 0xce, 0xb2, 0x64, 0x4d, 0x8d, 0xf8, 0x22, 0x6a, 0xdc, 0x87, 0x84, 0x0c, 0x6a, 0x78,
	0xe2, 0xaa, 0xcf, 0xd7, 0xdd, 0x97, 0xb0, 0xe7, 0xd0, 0x0c, 0xc6, 0x4b, 0xd1, 0xf0, 0xca, 0x85,
	0x24, 0xdb, 0xe0, 0xb5, 0x7f, 0x44, 0x70, 0x2b, 0xd4, 0xea, 0xcd, 0xc4, 0x99, 0xfb, 0x0a, 0x72,
	0xb5, 0xa0, 0x51, 0xf4, 0xd0, 0x57, 0x4e, 0xb0, 0x44, 0xac, 0x86, 0x42, 0x41, 0xe3, 0xcb, 0x0a,
	0x9a, 0x5c, 0x4a, 0xd0, 0xca, 0x76, 0x82, 0x56, 0xb7, 0x14, 0xf4, 0x57, 0x09, 0x6e, 0x6e, 0xae,
	0xaa, 0x85, 0x9c, 0x07, 0x50, 0x9f, 0xc8, 0x01, 0xf6, 0xc7, 0x0a, 0x3f, 0x39, 0x45, 0x13, 0xb1,
	0x0c, 0x90, 0xa8, 0x04, 0x8e, 0xd3, 0xcf, 0x5e, 0xd4, 0x44, 0x14, 0xd8, 0xed, 0x27, 0xb7, 0xdb,
	0x4c, 0xbe, 0xb7, 0x16, 0x70, 0xe5, 0x27, 0x8a, 0x83, 0x9f, 0xa8, 0xd8, 0x30, 0xc9, 0x5f, 0x36,
	0x0c, 0x75, 0x75, 0x82, 0x83, 0x74, 0xdc, 0xa3, 0x6a, 0xbf, 0xc5, 0x96, 0x01, 0x7a, 0x19, 0xc7,
	0xca, 0xe5, 0xfc, 0x16, 0x5b, 0x40, 0xd6, 0x84, 0x32, 0xf5, 0x53, 0x73, 0x51, 0x3a, 0xb6, 0xbf,
	0x94, 0x60, 0xff, 0xbc, 0xe9, 0xcd, 0x44, 0x8f, 0x0d, 0x3d, 0x14, 0x9f, 0x6a, 0x85, 0x6e, 0xf2,
	0xdd, 0x6e, 0x8d, 0x3a, 0x22, 0x2c, 0x5c, 0x94, 0x1e, 0xca, 0xbf, 0x1f, 0xb9, 0xa5, 0x16, 0xd0,
	0xad, 0x6c, 0x6d, 0xe5, 0x28, 0xf7, 0x92, 0x07, 0xec, 0x31, 0xc4, 0x4a, 0x5a, 0x99, 0xff, 0xc1,
	0xee, 0xd1, 0x6d, 0x17, 0xd8, 0x93, 0xc2, 0x91, 0xda, 0x3f, 0xa3, 0xf5, 0x4e, 0x9f, 0xcd, 0xfb,
	0xea, 0xca, 0x9d, 0xfe, 0x6b, 0xef, 0x57, 0x2e, 0xe5, 0xfd, 0xea, 0x76, 0xde, 0xaf, 0x6d, 0xe7,
	0xfd, 0x93, 0x8a, 0xfb, 0xc4, 0x3f, 0xfc, 0x1d, 0x00, 0x00, 0xff, 0xff, 0xbb, 0xe1, 0x95, 0x90,
	0x19, 0x08, 0x00, 0x00,
}
