// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/timeDiscount_message.proto

package mk

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type WeekDayDto struct {
	BeginTime            string   `protobuf:"bytes,2,opt,name=beginTime,proto3" json:"beginTime"`
	EndTime              string   `protobuf:"bytes,3,opt,name=EndTime,proto3" json:"EndTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeekDayDto) Reset()         { *m = WeekDayDto{} }
func (m *WeekDayDto) String() string { return proto.CompactTextString(m) }
func (*WeekDayDto) ProtoMessage()    {}
func (*WeekDayDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{0}
}

func (m *WeekDayDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeekDayDto.Unmarshal(m, b)
}
func (m *WeekDayDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeekDayDto.Marshal(b, m, deterministic)
}
func (m *WeekDayDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeekDayDto.Merge(m, src)
}
func (m *WeekDayDto) XXX_Size() int {
	return xxx_messageInfo_WeekDayDto.Size(m)
}
func (m *WeekDayDto) XXX_DiscardUnknown() {
	xxx_messageInfo_WeekDayDto.DiscardUnknown(m)
}

var xxx_messageInfo_WeekDayDto proto.InternalMessageInfo

func (m *WeekDayDto) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *WeekDayDto) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

// 限时促销与商品关联关系Dto
type TimeDiscountProductDto struct {
	// 关联关系Id
	TimeDiscountProductId int32 `protobuf:"varint,13,opt,name=timeDiscountProductId,proto3" json:"timeDiscountProductId"`
	//店铺Id
	ShopId string `protobuf:"bytes,14,opt,name=shopId,proto3" json:"shopId"`
	//店铺名称
	ShopName string `protobuf:"bytes,1,opt,name=shopName,proto3" json:"shopName"`
	//商品Id
	ProductSkuId string `protobuf:"bytes,15,opt,name=productSkuId,proto3" json:"productSkuId"`
	// 商品名称
	ProductName string `protobuf:"bytes,2,opt,name=productName,proto3" json:"productName"`
	// 原价
	MarketingPrice float64 `protobuf:"fixed64,3,opt,name=marketingPrice,proto3" json:"marketingPrice"`
	// 折扣类型
	DiscountType int32 `protobuf:"varint,16,opt,name=discountType,proto3" json:"discountType"`
	// 折扣
	Discount float64 `protobuf:"fixed64,4,opt,name=discount,proto3" json:"discount"`
	// 活动价
	SellingPrice float64 `protobuf:"fixed64,5,opt,name=sellingPrice,proto3" json:"sellingPrice"`
	// 每单限购
	LimitByOrder int32 `protobuf:"varint,6,opt,name=limitByOrder,proto3" json:"limitByOrder"`
	// 当日活动库存
	LimitByStock int32 `protobuf:"varint,7,opt,name=limitByStock,proto3" json:"limitByStock"`
	// 活动开始时间
	BeginDate string `protobuf:"bytes,8,opt,name=BeginDate,proto3" json:"BeginDate"`
	// 活动结束时间
	EndDate string `protobuf:"bytes,10,opt,name=EndDate,proto3" json:"EndDate"`
	// 周期循环
	TimeRanges []*PromotionTime `protobuf:"bytes,9,rep,name=timeRanges,proto3" json:"timeRanges"`
	Week       int32            `protobuf:"varint,18,opt,name=Week,proto3" json:"Week"`
	// 循环周期 0周日 1-6分别表示周一到周六
	WeekDays []int32 `protobuf:"varint,19,rep,packed,name=weekDays,proto3" json:"weekDays"`
	// 用户类型
	UserType int32 `protobuf:"varint,11,opt,name=userType,proto3" json:"userType"`
	// 状态
	State PromotionState `protobuf:"varint,12,opt,name=state,proto3,enum=mk.PromotionState" json:"state"`
	// 最后更新时间
	UpdateDate           string   `protobuf:"bytes,17,opt,name=updateDate,proto3" json:"updateDate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeDiscountProductDto) Reset()         { *m = TimeDiscountProductDto{} }
func (m *TimeDiscountProductDto) String() string { return proto.CompactTextString(m) }
func (*TimeDiscountProductDto) ProtoMessage()    {}
func (*TimeDiscountProductDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{1}
}

func (m *TimeDiscountProductDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeDiscountProductDto.Unmarshal(m, b)
}
func (m *TimeDiscountProductDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeDiscountProductDto.Marshal(b, m, deterministic)
}
func (m *TimeDiscountProductDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeDiscountProductDto.Merge(m, src)
}
func (m *TimeDiscountProductDto) XXX_Size() int {
	return xxx_messageInfo_TimeDiscountProductDto.Size(m)
}
func (m *TimeDiscountProductDto) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeDiscountProductDto.DiscardUnknown(m)
}

var xxx_messageInfo_TimeDiscountProductDto proto.InternalMessageInfo

func (m *TimeDiscountProductDto) GetTimeDiscountProductId() int32 {
	if m != nil {
		return m.TimeDiscountProductId
	}
	return 0
}

func (m *TimeDiscountProductDto) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *TimeDiscountProductDto) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *TimeDiscountProductDto) GetProductSkuId() string {
	if m != nil {
		return m.ProductSkuId
	}
	return ""
}

func (m *TimeDiscountProductDto) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *TimeDiscountProductDto) GetMarketingPrice() float64 {
	if m != nil {
		return m.MarketingPrice
	}
	return 0
}

func (m *TimeDiscountProductDto) GetDiscountType() int32 {
	if m != nil {
		return m.DiscountType
	}
	return 0
}

func (m *TimeDiscountProductDto) GetDiscount() float64 {
	if m != nil {
		return m.Discount
	}
	return 0
}

func (m *TimeDiscountProductDto) GetSellingPrice() float64 {
	if m != nil {
		return m.SellingPrice
	}
	return 0
}

func (m *TimeDiscountProductDto) GetLimitByOrder() int32 {
	if m != nil {
		return m.LimitByOrder
	}
	return 0
}

func (m *TimeDiscountProductDto) GetLimitByStock() int32 {
	if m != nil {
		return m.LimitByStock
	}
	return 0
}

func (m *TimeDiscountProductDto) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *TimeDiscountProductDto) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *TimeDiscountProductDto) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *TimeDiscountProductDto) GetWeek() int32 {
	if m != nil {
		return m.Week
	}
	return 0
}

func (m *TimeDiscountProductDto) GetWeekDays() []int32 {
	if m != nil {
		return m.WeekDays
	}
	return nil
}

func (m *TimeDiscountProductDto) GetUserType() int32 {
	if m != nil {
		return m.UserType
	}
	return 0
}

func (m *TimeDiscountProductDto) GetState() PromotionState {
	if m != nil {
		return m.State
	}
	return PromotionState_zero
}

func (m *TimeDiscountProductDto) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

type TimeDiscountUpdateRequestDto struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotionId,proto3" json:"promotionId"`
	// 活动基本信息
	Promotion *PromotionDto `protobuf:"bytes,2,opt,name=promotion,proto3" json:"promotion"`
	// 星期
	TimeRanges []*PromotionTime `protobuf:"bytes,3,rep,name=timeRanges,proto3" json:"timeRanges"`
	// 相关商品
	PromotionProduct *PromotionProductDto `protobuf:"bytes,5,opt,name=promotionProduct,proto3" json:"promotionProduct"`
	// 限时折扣配置
	PromotionTimeDiscount *PromotionTimeDiscountDto `protobuf:"bytes,6,opt,name=promotionTimeDiscount,proto3" json:"promotionTimeDiscount"`
	XXX_NoUnkeyedLiteral  struct{}                  `json:"-"`
	XXX_unrecognized      []byte                    `json:"-"`
	XXX_sizecache         int32                     `json:"-"`
}

func (m *TimeDiscountUpdateRequestDto) Reset()         { *m = TimeDiscountUpdateRequestDto{} }
func (m *TimeDiscountUpdateRequestDto) String() string { return proto.CompactTextString(m) }
func (*TimeDiscountUpdateRequestDto) ProtoMessage()    {}
func (*TimeDiscountUpdateRequestDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{2}
}

func (m *TimeDiscountUpdateRequestDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeDiscountUpdateRequestDto.Unmarshal(m, b)
}
func (m *TimeDiscountUpdateRequestDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeDiscountUpdateRequestDto.Marshal(b, m, deterministic)
}
func (m *TimeDiscountUpdateRequestDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeDiscountUpdateRequestDto.Merge(m, src)
}
func (m *TimeDiscountUpdateRequestDto) XXX_Size() int {
	return xxx_messageInfo_TimeDiscountUpdateRequestDto.Size(m)
}
func (m *TimeDiscountUpdateRequestDto) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeDiscountUpdateRequestDto.DiscardUnknown(m)
}

var xxx_messageInfo_TimeDiscountUpdateRequestDto proto.InternalMessageInfo

func (m *TimeDiscountUpdateRequestDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *TimeDiscountUpdateRequestDto) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *TimeDiscountUpdateRequestDto) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *TimeDiscountUpdateRequestDto) GetPromotionProduct() *PromotionProductDto {
	if m != nil {
		return m.PromotionProduct
	}
	return nil
}

func (m *TimeDiscountUpdateRequestDto) GetPromotionTimeDiscount() *PromotionTimeDiscountDto {
	if m != nil {
		return m.PromotionTimeDiscount
	}
	return nil
}

//限时折扣活动商品单日库存数量列表
type PromotionProductLimitCountDto struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotionId,proto3" json:"promotionId"`
	// 商品sku
	ProductSkuId string `protobuf:"bytes,2,opt,name=productSkuId,proto3" json:"productSkuId"`
	// 每单限购数量
	LimitCountByOrder int32 `protobuf:"varint,3,opt,name=limitCountByOrder,proto3" json:"limitCountByOrder"`
	// 每日库存
	LimitCountByStock    int32    `protobuf:"varint,4,opt,name=limitCountByStock,proto3" json:"limitCountByStock"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionProductLimitCountDto) Reset()         { *m = PromotionProductLimitCountDto{} }
func (m *PromotionProductLimitCountDto) String() string { return proto.CompactTextString(m) }
func (*PromotionProductLimitCountDto) ProtoMessage()    {}
func (*PromotionProductLimitCountDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{3}
}

func (m *PromotionProductLimitCountDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionProductLimitCountDto.Unmarshal(m, b)
}
func (m *PromotionProductLimitCountDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionProductLimitCountDto.Marshal(b, m, deterministic)
}
func (m *PromotionProductLimitCountDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionProductLimitCountDto.Merge(m, src)
}
func (m *PromotionProductLimitCountDto) XXX_Size() int {
	return xxx_messageInfo_PromotionProductLimitCountDto.Size(m)
}
func (m *PromotionProductLimitCountDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionProductLimitCountDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionProductLimitCountDto proto.InternalMessageInfo

func (m *PromotionProductLimitCountDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotionProductLimitCountDto) GetProductSkuId() string {
	if m != nil {
		return m.ProductSkuId
	}
	return ""
}

func (m *PromotionProductLimitCountDto) GetLimitCountByOrder() int32 {
	if m != nil {
		return m.LimitCountByOrder
	}
	return 0
}

func (m *PromotionProductLimitCountDto) GetLimitCountByStock() int32 {
	if m != nil {
		return m.LimitCountByStock
	}
	return 0
}

// 更新
type TimeDiscountUpdateRequest struct {
	//用户Id，即userno
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户Id，即登录人姓名
	UserName             string                          `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName"`
	UpdateParams         []*TimeDiscountUpdateRequestDto `protobuf:"bytes,3,rep,name=updateParams,proto3" json:"updateParams"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *TimeDiscountUpdateRequest) Reset()         { *m = TimeDiscountUpdateRequest{} }
func (m *TimeDiscountUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*TimeDiscountUpdateRequest) ProtoMessage()    {}
func (*TimeDiscountUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{4}
}

func (m *TimeDiscountUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeDiscountUpdateRequest.Unmarshal(m, b)
}
func (m *TimeDiscountUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeDiscountUpdateRequest.Marshal(b, m, deterministic)
}
func (m *TimeDiscountUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeDiscountUpdateRequest.Merge(m, src)
}
func (m *TimeDiscountUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_TimeDiscountUpdateRequest.Size(m)
}
func (m *TimeDiscountUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeDiscountUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TimeDiscountUpdateRequest proto.InternalMessageInfo

func (m *TimeDiscountUpdateRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *TimeDiscountUpdateRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *TimeDiscountUpdateRequest) GetUpdateParams() []*TimeDiscountUpdateRequestDto {
	if m != nil {
		return m.UpdateParams
	}
	return nil
}

// 获取列表
type TimeDiscountProductRequest struct {
	// 页索引
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	// 店铺列表，逗号分割
	ShopIds string `protobuf:"bytes,3,opt,name=shopIds,proto3" json:"shopIds"`
	// 当前登录用户Id
	UserId string `protobuf:"bytes,4,opt,name=userId,proto3" json:"userId"`
	// 0 所有 1 进行中 2 待生效 3 已结束 4 冻结中
	State PromotionState `protobuf:"varint,5,opt,name=state,proto3,enum=mk.PromotionState" json:"state"`
	// 开始日期
	BeginDate string `protobuf:"bytes,6,opt,name=beginDate,proto3" json:"beginDate"`
	// 截止日期日期
	EndDate string `protobuf:"bytes,7,opt,name=endDate,proto3" json:"endDate"`
	// 商品名称
	ProductName string `protobuf:"bytes,8,opt,name=productName,proto3" json:"productName"`
	// 每单限购 0 不限制, 非0  限制多少数量
	LimitCountByOrder int32  `protobuf:"varint,9,opt,name=LimitCountByOrder,proto3" json:"LimitCountByOrder"`
	UserName          string `protobuf:"bytes,10,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//活动id
	PromotionId int64 `protobuf:"varint,11,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	// 批量活动id，多个逗号分隔
	PromotionIds string `protobuf:"bytes,12,opt,name=promotion_ids,json=promotionIds,proto3" json:"promotion_ids"`
	// 批量活动id，多个逗号分隔
	SkuId                string   `protobuf:"bytes,13,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeDiscountProductRequest) Reset()         { *m = TimeDiscountProductRequest{} }
func (m *TimeDiscountProductRequest) String() string { return proto.CompactTextString(m) }
func (*TimeDiscountProductRequest) ProtoMessage()    {}
func (*TimeDiscountProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{5}
}

func (m *TimeDiscountProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeDiscountProductRequest.Unmarshal(m, b)
}
func (m *TimeDiscountProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeDiscountProductRequest.Marshal(b, m, deterministic)
}
func (m *TimeDiscountProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeDiscountProductRequest.Merge(m, src)
}
func (m *TimeDiscountProductRequest) XXX_Size() int {
	return xxx_messageInfo_TimeDiscountProductRequest.Size(m)
}
func (m *TimeDiscountProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeDiscountProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TimeDiscountProductRequest proto.InternalMessageInfo

func (m *TimeDiscountProductRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *TimeDiscountProductRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *TimeDiscountProductRequest) GetShopIds() string {
	if m != nil {
		return m.ShopIds
	}
	return ""
}

func (m *TimeDiscountProductRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *TimeDiscountProductRequest) GetState() PromotionState {
	if m != nil {
		return m.State
	}
	return PromotionState_zero
}

func (m *TimeDiscountProductRequest) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *TimeDiscountProductRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *TimeDiscountProductRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *TimeDiscountProductRequest) GetLimitCountByOrder() int32 {
	if m != nil {
		return m.LimitCountByOrder
	}
	return 0
}

func (m *TimeDiscountProductRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *TimeDiscountProductRequest) GetPromotionId() int64 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *TimeDiscountProductRequest) GetPromotionIds() string {
	if m != nil {
		return m.PromotionIds
	}
	return ""
}

func (m *TimeDiscountProductRequest) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

// 查询限时折扣活动的单日库存请求
type QueryLimitCountRequest struct {
	//门店id
	Shopid string `protobuf:"bytes,1,opt,name=shopid,proto3" json:"shopid"`
	//多个活动
	Promotionids string `protobuf:"bytes,2,opt,name=promotionids,proto3" json:"promotionids"`
	//多个商品skuid
	Skuids               string   `protobuf:"bytes,3,opt,name=skuids,proto3" json:"skuids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryLimitCountRequest) Reset()         { *m = QueryLimitCountRequest{} }
func (m *QueryLimitCountRequest) String() string { return proto.CompactTextString(m) }
func (*QueryLimitCountRequest) ProtoMessage()    {}
func (*QueryLimitCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{6}
}

func (m *QueryLimitCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryLimitCountRequest.Unmarshal(m, b)
}
func (m *QueryLimitCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryLimitCountRequest.Marshal(b, m, deterministic)
}
func (m *QueryLimitCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryLimitCountRequest.Merge(m, src)
}
func (m *QueryLimitCountRequest) XXX_Size() int {
	return xxx_messageInfo_QueryLimitCountRequest.Size(m)
}
func (m *QueryLimitCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryLimitCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryLimitCountRequest proto.InternalMessageInfo

func (m *QueryLimitCountRequest) GetShopid() string {
	if m != nil {
		return m.Shopid
	}
	return ""
}

func (m *QueryLimitCountRequest) GetPromotionids() string {
	if m != nil {
		return m.Promotionids
	}
	return ""
}

func (m *QueryLimitCountRequest) GetSkuids() string {
	if m != nil {
		return m.Skuids
	}
	return ""
}

/////////////////////////////////////////////////  Response  ///////////////////////////////////////////////////////////////////////////////
// 限时促销与商品关联关系响应
type TimeDiscountProductResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总条数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 分页数据
	Data                 []*TimeDiscountProductDto `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *TimeDiscountProductResponse) Reset()         { *m = TimeDiscountProductResponse{} }
func (m *TimeDiscountProductResponse) String() string { return proto.CompactTextString(m) }
func (*TimeDiscountProductResponse) ProtoMessage()    {}
func (*TimeDiscountProductResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{7}
}

func (m *TimeDiscountProductResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeDiscountProductResponse.Unmarshal(m, b)
}
func (m *TimeDiscountProductResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeDiscountProductResponse.Marshal(b, m, deterministic)
}
func (m *TimeDiscountProductResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeDiscountProductResponse.Merge(m, src)
}
func (m *TimeDiscountProductResponse) XXX_Size() int {
	return xxx_messageInfo_TimeDiscountProductResponse.Size(m)
}
func (m *TimeDiscountProductResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeDiscountProductResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TimeDiscountProductResponse proto.InternalMessageInfo

func (m *TimeDiscountProductResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *TimeDiscountProductResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *TimeDiscountProductResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *TimeDiscountProductResponse) GetData() []*TimeDiscountProductDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// 限时促销 数据响应
type TimeDiscountByIdResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message     string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	PromotionId int32  `protobuf:"varint,3,opt,name=promotionId,proto3" json:"promotionId"`
	// 活动基本信息
	Promotion *PromotionDto `protobuf:"bytes,4,opt,name=promotion,proto3" json:"promotion"`
	// 活动时间
	TimeRanges []*PromotionTime `protobuf:"bytes,6,rep,name=timeRanges,proto3" json:"timeRanges"`
	// 应用店铺
	Shops []*PromotionShopDto `protobuf:"bytes,7,rep,name=shops,proto3" json:"shops"`
	// 相关商品
	Products []*PromotionProductDto `protobuf:"bytes,8,rep,name=products,proto3" json:"products"`
	// 满减优惠
	TimeDiscounts        []*PromotionTimeDiscountDto `protobuf:"bytes,9,rep,name=timeDiscounts,proto3" json:"timeDiscounts"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *TimeDiscountByIdResponse) Reset()         { *m = TimeDiscountByIdResponse{} }
func (m *TimeDiscountByIdResponse) String() string { return proto.CompactTextString(m) }
func (*TimeDiscountByIdResponse) ProtoMessage()    {}
func (*TimeDiscountByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{8}
}

func (m *TimeDiscountByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeDiscountByIdResponse.Unmarshal(m, b)
}
func (m *TimeDiscountByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeDiscountByIdResponse.Marshal(b, m, deterministic)
}
func (m *TimeDiscountByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeDiscountByIdResponse.Merge(m, src)
}
func (m *TimeDiscountByIdResponse) XXX_Size() int {
	return xxx_messageInfo_TimeDiscountByIdResponse.Size(m)
}
func (m *TimeDiscountByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeDiscountByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TimeDiscountByIdResponse proto.InternalMessageInfo

func (m *TimeDiscountByIdResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *TimeDiscountByIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *TimeDiscountByIdResponse) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *TimeDiscountByIdResponse) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *TimeDiscountByIdResponse) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *TimeDiscountByIdResponse) GetShops() []*PromotionShopDto {
	if m != nil {
		return m.Shops
	}
	return nil
}

func (m *TimeDiscountByIdResponse) GetProducts() []*PromotionProductDto {
	if m != nil {
		return m.Products
	}
	return nil
}

func (m *TimeDiscountByIdResponse) GetTimeDiscounts() []*PromotionTimeDiscountDto {
	if m != nil {
		return m.TimeDiscounts
	}
	return nil
}

// 查询限时折扣活动的单日库存响应
type QueryLimitCountResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 应用店铺
	PromotionLimits      []*PromotionProductLimitCountDto `protobuf:"bytes,7,rep,name=promotionLimits,proto3" json:"promotionLimits"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *QueryLimitCountResponse) Reset()         { *m = QueryLimitCountResponse{} }
func (m *QueryLimitCountResponse) String() string { return proto.CompactTextString(m) }
func (*QueryLimitCountResponse) ProtoMessage()    {}
func (*QueryLimitCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{9}
}

func (m *QueryLimitCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryLimitCountResponse.Unmarshal(m, b)
}
func (m *QueryLimitCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryLimitCountResponse.Marshal(b, m, deterministic)
}
func (m *QueryLimitCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryLimitCountResponse.Merge(m, src)
}
func (m *QueryLimitCountResponse) XXX_Size() int {
	return xxx_messageInfo_QueryLimitCountResponse.Size(m)
}
func (m *QueryLimitCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryLimitCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryLimitCountResponse proto.InternalMessageInfo

func (m *QueryLimitCountResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryLimitCountResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryLimitCountResponse) GetPromotionLimits() []*PromotionProductLimitCountDto {
	if m != nil {
		return m.PromotionLimits
	}
	return nil
}

type PromotionTimeDiscountProductDto struct {
	Promotion             *PromotionDto             `protobuf:"bytes,1,opt,name=promotion,proto3" json:"promotion"`
	TimeRanges            []*PromotionTime          `protobuf:"bytes,2,rep,name=timeRanges,proto3" json:"timeRanges"`
	PromotionProduct      *PromotionProductDto      `protobuf:"bytes,3,opt,name=promotionProduct,proto3" json:"promotionProduct"`
	PromotionTimeDiscount *PromotionTimeDiscountDto `protobuf:"bytes,4,opt,name=promotionTimeDiscount,proto3" json:"promotionTimeDiscount"`
	XXX_NoUnkeyedLiteral  struct{}                  `json:"-"`
	XXX_unrecognized      []byte                    `json:"-"`
	XXX_sizecache         int32                     `json:"-"`
}

func (m *PromotionTimeDiscountProductDto) Reset()         { *m = PromotionTimeDiscountProductDto{} }
func (m *PromotionTimeDiscountProductDto) String() string { return proto.CompactTextString(m) }
func (*PromotionTimeDiscountProductDto) ProtoMessage()    {}
func (*PromotionTimeDiscountProductDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{10}
}

func (m *PromotionTimeDiscountProductDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTimeDiscountProductDto.Unmarshal(m, b)
}
func (m *PromotionTimeDiscountProductDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTimeDiscountProductDto.Marshal(b, m, deterministic)
}
func (m *PromotionTimeDiscountProductDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTimeDiscountProductDto.Merge(m, src)
}
func (m *PromotionTimeDiscountProductDto) XXX_Size() int {
	return xxx_messageInfo_PromotionTimeDiscountProductDto.Size(m)
}
func (m *PromotionTimeDiscountProductDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTimeDiscountProductDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTimeDiscountProductDto proto.InternalMessageInfo

func (m *PromotionTimeDiscountProductDto) GetPromotion() *PromotionDto {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *PromotionTimeDiscountProductDto) GetTimeRanges() []*PromotionTime {
	if m != nil {
		return m.TimeRanges
	}
	return nil
}

func (m *PromotionTimeDiscountProductDto) GetPromotionProduct() *PromotionProductDto {
	if m != nil {
		return m.PromotionProduct
	}
	return nil
}

func (m *PromotionTimeDiscountProductDto) GetPromotionTimeDiscount() *PromotionTimeDiscountDto {
	if m != nil {
		return m.PromotionTimeDiscount
	}
	return nil
}

//限时折扣-批量新增-Request
type PromotionTimeDiscountAddRequest struct {
	//用户Id，即userno
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户Id，即登录人姓名
	UserName             string                             `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName"`
	PromotionShop        []*PromotionShopDto                `protobuf:"bytes,3,rep,name=promotionShop,proto3" json:"promotionShop"`
	AddParams            []*PromotionTimeDiscountProductDto `protobuf:"bytes,4,rep,name=addParams,proto3" json:"addParams"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *PromotionTimeDiscountAddRequest) Reset()         { *m = PromotionTimeDiscountAddRequest{} }
func (m *PromotionTimeDiscountAddRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionTimeDiscountAddRequest) ProtoMessage()    {}
func (*PromotionTimeDiscountAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7707615ad5add816, []int{11}
}

func (m *PromotionTimeDiscountAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTimeDiscountAddRequest.Unmarshal(m, b)
}
func (m *PromotionTimeDiscountAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTimeDiscountAddRequest.Marshal(b, m, deterministic)
}
func (m *PromotionTimeDiscountAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTimeDiscountAddRequest.Merge(m, src)
}
func (m *PromotionTimeDiscountAddRequest) XXX_Size() int {
	return xxx_messageInfo_PromotionTimeDiscountAddRequest.Size(m)
}
func (m *PromotionTimeDiscountAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTimeDiscountAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTimeDiscountAddRequest proto.InternalMessageInfo

func (m *PromotionTimeDiscountAddRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PromotionTimeDiscountAddRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *PromotionTimeDiscountAddRequest) GetPromotionShop() []*PromotionShopDto {
	if m != nil {
		return m.PromotionShop
	}
	return nil
}

func (m *PromotionTimeDiscountAddRequest) GetAddParams() []*PromotionTimeDiscountProductDto {
	if m != nil {
		return m.AddParams
	}
	return nil
}

func init() {
	proto.RegisterType((*WeekDayDto)(nil), "mk.weekDayDto")
	proto.RegisterType((*TimeDiscountProductDto)(nil), "mk.timeDiscountProductDto")
	proto.RegisterType((*TimeDiscountUpdateRequestDto)(nil), "mk.timeDiscountUpdateRequestDto")
	proto.RegisterType((*PromotionProductLimitCountDto)(nil), "mk.promotionProductLimitCountDto")
	proto.RegisterType((*TimeDiscountUpdateRequest)(nil), "mk.timeDiscountUpdateRequest")
	proto.RegisterType((*TimeDiscountProductRequest)(nil), "mk.timeDiscountProductRequest")
	proto.RegisterType((*QueryLimitCountRequest)(nil), "mk.queryLimitCountRequest")
	proto.RegisterType((*TimeDiscountProductResponse)(nil), "mk.timeDiscountProductResponse")
	proto.RegisterType((*TimeDiscountByIdResponse)(nil), "mk.timeDiscountByIdResponse")
	proto.RegisterType((*QueryLimitCountResponse)(nil), "mk.queryLimitCountResponse")
	proto.RegisterType((*PromotionTimeDiscountProductDto)(nil), "mk.promotionTimeDiscountProductDto")
	proto.RegisterType((*PromotionTimeDiscountAddRequest)(nil), "mk.promotionTimeDiscountAddRequest")
}

func init() { proto.RegisterFile("mk/timeDiscount_message.proto", fileDescriptor_7707615ad5add816) }

var fileDescriptor_7707615ad5add816 = []byte{
	// 1020 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0x06, 0x25, 0x52, 0x16, 0x47, 0xb6, 0x62, 0x6f, 0x63, 0x67, 0xeb, 0x38, 0xad, 0xc2, 0x00,
	0x85, 0x50, 0x04, 0x0a, 0xaa, 0xf4, 0xd4, 0x5b, 0x1c, 0xf5, 0x20, 0x34, 0x68, 0x0d, 0x3a, 0x45,
	0x8f, 0x06, 0xa3, 0x5d, 0x28, 0x04, 0x45, 0x91, 0xe1, 0x92, 0x68, 0xd5, 0xe7, 0x68, 0x4f, 0x45,
	0x5e, 0xa3, 0xa7, 0x5e, 0xfa, 0x0e, 0x7d, 0x9c, 0x1e, 0x8a, 0x9d, 0x5d, 0x52, 0x4b, 0x8a, 0x8e,
	0x9d, 0xa4, 0x37, 0xcd, 0xcf, 0xce, 0xef, 0x37, 0x33, 0x14, 0x3c, 0x88, 0xa3, 0x27, 0x79, 0x18,
	0xf3, 0x59, 0x28, 0x16, 0x49, 0xb1, 0xce, 0xaf, 0x62, 0x2e, 0x44, 0xb0, 0xe4, 0x93, 0x34, 0x4b,
	0xf2, 0x84, 0x74, 0xe2, 0xe8, 0x74, 0x18, 0x47, 0x4f, 0xe2, 0x84, 0xf1, 0x95, 0xe2, 0x79, 0x33,
	0x80, 0x9f, 0x39, 0x8f, 0x66, 0xc1, 0x66, 0x96, 0x27, 0xe4, 0x0c, 0xdc, 0x57, 0x7c, 0x19, 0xae,
	0x5f, 0x86, 0x31, 0xa7, 0x9d, 0x91, 0x35, 0x76, 0xfd, 0x2d, 0x83, 0x50, 0xd8, 0xfb, 0x76, 0xcd,
	0x50, 0xd6, 0x45, 0x59, 0x49, 0x7a, 0x6f, 0x1d, 0x38, 0x31, 0x1d, 0x5f, 0x64, 0x09, 0x2b, 0x16,
	0xb9, 0x34, 0xf9, 0x35, 0x1c, 0xb7, 0x48, 0xe6, 0x8c, 0x1e, 0x8c, 0xac, 0xb1, 0xe3, 0xb7, 0x0b,
	0xc9, 0x09, 0xf4, 0xc4, 0xeb, 0x24, 0x9d, 0x33, 0x3a, 0x44, 0x4f, 0x9a, 0x22, 0xa7, 0xd0, 0x97,
	0xbf, 0xbe, 0x0f, 0x62, 0x4e, 0x2d, 0x94, 0x54, 0x34, 0xf1, 0x60, 0x3f, 0x55, 0x06, 0x2e, 0xa3,
	0x62, 0xce, 0xe8, 0x1d, 0x94, 0xd7, 0x78, 0x64, 0x04, 0x03, 0x4d, 0xa3, 0x09, 0x95, 0xa2, 0xc9,
	0x22, 0x5f, 0xc0, 0x30, 0x0e, 0xb2, 0x88, 0xe7, 0xe1, 0x7a, 0x79, 0x91, 0x85, 0x0b, 0x95, 0xab,
	0xe5, 0x37, 0xb8, 0xd2, 0x1b, 0xd3, 0x61, 0xbf, 0xdc, 0xa4, 0x9c, 0x1e, 0x62, 0x3a, 0x35, 0x9e,
	0x8c, 0xb6, 0xa4, 0xa9, 0x8d, 0x56, 0x2a, 0x5a, 0xbe, 0x17, 0x7c, 0xb5, 0xaa, 0xbc, 0x38, 0x28,
	0xaf, 0xf1, 0xa4, 0xce, 0x2a, 0x8c, 0xc3, 0xfc, 0x7c, 0xf3, 0x43, 0xc6, 0x78, 0x46, 0x7b, 0xca,
	0x87, 0xc9, 0x33, 0x74, 0x2e, 0xf3, 0x64, 0x11, 0xd1, 0xbd, 0x9a, 0x0e, 0xf2, 0x64, 0x5b, 0xcf,
	0x65, 0x17, 0x67, 0x41, 0xce, 0x69, 0x5f, 0xb5, 0xb5, 0x62, 0xe8, 0xb6, 0xa2, 0x0c, 0xaa, 0xb6,
	0xa2, 0xe4, 0x2b, 0x00, 0xd9, 0x1e, 0x3f, 0x58, 0x2f, 0xb9, 0xa0, 0xee, 0xa8, 0x3b, 0x1e, 0x4c,
	0x8f, 0x26, 0x71, 0x34, 0xb9, 0xc8, 0x92, 0x38, 0xc9, 0xc3, 0x04, 0x71, 0xe1, 0x1b, 0x4a, 0x84,
	0x80, 0xfd, 0x13, 0xe7, 0x11, 0x25, 0x18, 0x06, 0xfe, 0x96, 0x65, 0xd0, 0x18, 0x13, 0xf4, 0x93,
	0x51, 0x77, 0xec, 0xf8, 0x15, 0x2d, 0x65, 0x85, 0xe0, 0x19, 0x96, 0x70, 0x80, 0x6f, 0x2a, 0x9a,
	0x8c, 0xc1, 0x11, 0xb9, 0x0c, 0x6b, 0x7f, 0x64, 0x8d, 0x87, 0x53, 0x22, 0x3d, 0xa7, 0xa5, 0xe7,
	0x4b, 0x29, 0xf1, 0x95, 0x02, 0xf9, 0x0c, 0xa0, 0x48, 0x59, 0x90, 0x73, 0xcc, 0xe2, 0x08, 0xb3,
	0x30, 0x38, 0xde, 0x5f, 0x1d, 0x38, 0x33, 0x81, 0xf6, 0x23, 0x8a, 0x7c, 0xfe, 0xa6, 0xe0, 0x02,
	0x51, 0xaa, 0x70, 0xa1, 0x2c, 0xcf, 0x19, 0x42, 0xcb, 0xf1, 0x4d, 0x16, 0x99, 0x80, 0x5b, 0x91,
	0x88, 0x9b, 0xc1, 0xf4, 0xb0, 0x16, 0xd0, 0x2c, 0x4f, 0xfc, 0xad, 0x4a, 0xa3, 0x76, 0xdd, 0xdb,
	0xd4, 0xee, 0x39, 0x1c, 0x56, 0xef, 0xf5, 0x28, 0x20, 0x2c, 0x06, 0xd3, 0x7b, 0x35, 0x4f, 0xdb,
	0xe9, 0xf2, 0x77, 0x1e, 0x10, 0x1f, 0x8e, 0x53, 0xd3, 0x43, 0x99, 0x32, 0x82, 0x67, 0x30, 0x3d,
	0xab, 0x59, 0x32, 0x15, 0xa4, 0xb9, 0xf6, 0xa7, 0xde, 0xdf, 0x16, 0x3c, 0x68, 0x3a, 0x7a, 0x21,
	0x01, 0xf6, 0x5c, 0x3f, 0xbc, 0x45, 0xfd, 0x9a, 0xd3, 0xd9, 0x69, 0x99, 0xce, 0xc7, 0x70, 0xb4,
	0xaa, 0xcc, 0x96, 0xa0, 0xef, 0xa2, 0xad, 0x5d, 0x41, 0x53, 0x5b, 0xc1, 0xdf, 0xde, 0xd5, 0x46,
	0x81, 0xf7, 0xbb, 0x05, 0x9f, 0x5e, 0x0b, 0x01, 0xb9, 0x6f, 0x24, 0xec, 0x74, 0xe8, 0xae, 0xaf,
	0xa9, 0x12, 0x9e, 0xc6, 0xb2, 0xa8, 0x68, 0x32, 0x83, 0x7d, 0x05, 0xb1, 0x8b, 0x20, 0x0b, 0xe2,
	0xb2, 0xc7, 0x23, 0x59, 0xe0, 0x77, 0x61, 0xcd, 0xaf, 0xbd, 0xf2, 0xfe, 0xec, 0xc2, 0x69, 0xcb,
	0x0e, 0x2c, 0x03, 0x3b, 0x03, 0x37, 0x0d, 0x96, 0x7c, 0xbe, 0x66, 0xfc, 0x17, 0x5d, 0xd6, 0x2d,
	0x43, 0x86, 0x27, 0x89, 0xcb, 0xf0, 0x57, 0x15, 0x9e, 0xe3, 0x57, 0xb4, 0x1c, 0x6b, 0xb5, 0x34,
	0x45, 0xb9, 0xad, 0x35, 0x69, 0x24, 0x6b, 0xd7, 0x92, 0xad, 0xe6, 0xcd, 0xb9, 0x69, 0xde, 0xca,
	0x3b, 0x81, 0xe3, 0xd6, 0x33, 0xee, 0x44, 0xb9, 0x50, 0xb8, 0x5e, 0x28, 0x7b, 0xca, 0xb3, 0x26,
	0x9b, 0xeb, 0xb7, 0xbf, 0xbb, 0x7e, 0x1f, 0xc3, 0xd1, 0x8b, 0x1d, 0x08, 0xb8, 0xaa, 0xa9, 0x3b,
	0x02, 0x72, 0x1f, 0x5c, 0x19, 0xfb, 0xd5, 0x5a, 0x5a, 0x83, 0x46, 0x7f, 0x1e, 0x22, 0xe2, 0x54,
	0xf4, 0x57, 0x21, 0xc3, 0xf5, 0xd2, 0xad, 0x83, 0xf2, 0x11, 0x1c, 0x98, 0x2a, 0x02, 0x37, 0x8d,
	0x42, 0x65, 0xa9, 0x23, 0xc8, 0x31, 0xf4, 0x44, 0x54, 0x48, 0x0b, 0x07, 0x28, 0x75, 0x84, 0x04,
	0xab, 0xb7, 0x82, 0x93, 0x37, 0x05, 0xcf, 0x36, 0xdb, 0xa8, 0x0c, 0x30, 0xc9, 0x52, 0x87, 0x15,
	0x98, 0x14, 0xa5, 0x47, 0x40, 0x19, 0x96, 0xce, 0x3a, 0x0d, 0x67, 0xa1, 0xea, 0x8d, 0x88, 0x8a,
	0xb0, 0x6a, 0x9a, 0xa6, 0xbc, 0x3f, 0x2c, 0xb8, 0xdf, 0x0a, 0x13, 0x91, 0x26, 0x6b, 0x21, 0x3b,
	0x62, 0x2f, 0x12, 0xa6, 0x8e, 0xe2, 0x70, 0xda, 0x97, 0xad, 0x93, 0xb4, 0x8f, 0x5c, 0xd9, 0x11,
	0xfd, 0x29, 0xa0, 0x9d, 0x96, 0x24, 0xb9, 0x0b, 0x4e, 0x9e, 0xe4, 0xc1, 0x4a, 0x8f, 0x99, 0x22,
	0xc8, 0x04, 0x6c, 0x16, 0xe4, 0x01, 0xb5, 0x11, 0xd2, 0xa7, 0x4d, 0x48, 0x1b, 0x0b, 0x08, 0xf5,
	0xbc, 0x7f, 0x3b, 0x40, 0x4d, 0x85, 0xf3, 0xcd, 0x9c, 0x7d, 0x74, 0x68, 0x8d, 0x9d, 0xd2, 0xbd,
	0x61, 0x27, 0xdb, 0xef, 0xbb, 0x93, 0x7b, 0xb7, 0xd9, 0xc9, 0x5f, 0x82, 0x23, 0xbb, 0x27, 0xe8,
	0x1e, 0x6a, 0xdf, 0xad, 0xcf, 0xc4, 0xeb, 0x24, 0x95, 0x2e, 0x94, 0x0a, 0x79, 0x0a, 0x7d, 0x0d,
	0x65, 0x41, 0xfb, 0xa8, 0x7e, 0xed, 0xde, 0xae, 0x14, 0xc9, 0x39, 0x1c, 0x98, 0x95, 0x2b, 0xcf,
	0xec, 0xbb, 0xf7, 0x74, 0xfd, 0x89, 0xf7, 0x9b, 0x05, 0xf7, 0x76, 0xb0, 0xa8, 0xab, 0x4f, 0x8c,
	0xea, 0x3b, 0x37, 0xd6, 0xfc, 0x3b, 0xb8, 0x53, 0x39, 0x45, 0x63, 0x65, 0xe2, 0x0f, 0xdb, 0x32,
	0xa9, 0xdd, 0x00, 0xbf, 0xf9, 0xd2, 0x7b, 0xdb, 0x81, 0xcf, 0x5b, 0x53, 0x30, 0x3e, 0x0f, 0x6b,
	0x2d, 0xb4, 0xde, 0xb7, 0x85, 0x9d, 0x0f, 0x3d, 0xab, 0xdd, 0xff, 0xed, 0xac, 0xda, 0x1f, 0x7e,
	0x56, 0xff, 0xb1, 0xae, 0xa9, 0xcf, 0x33, 0xc6, 0x3e, 0xe6, 0x30, 0x7d, 0x63, 0x6c, 0x35, 0x09,
	0x51, 0x7d, 0x99, 0xda, 0xb1, 0x5b, 0x57, 0x25, 0xcf, 0xc0, 0x0d, 0x18, 0xd3, 0x17, 0x4d, 0x8d,
	0xff, 0xa3, 0x6b, 0x73, 0x33, 0x2a, 0xb6, 0x7d, 0xf5, 0xaa, 0x87, 0xff, 0x2c, 0x9e, 0xfe, 0x17,
	0x00, 0x00, 0xff, 0xff, 0x1d, 0x7a, 0xe3, 0x16, 0x8e, 0x0c, 0x00, 0x00,
}
