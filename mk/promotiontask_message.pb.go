// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/promotiontask_message.proto

package mk

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PromotionTaskQueryRequest struct {
	// 类型 0 全部 1 满减 2 限时折扣
	Types int32 `protobuf:"varint,1,opt,name=types,proto3" json:"types"`
	// 创建用户Id
	CreateUserId string `protobuf:"bytes,2,opt,name=createUserId,proto3" json:"createUserId"`
	// 页索引
	PageIndex int32 `protobuf:"varint,3,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize int32 `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize"`
	//主体Id
	OrgId                int64    `protobuf:"varint,5,opt,name=orgId,proto3" json:"orgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionTaskQueryRequest) Reset()         { *m = PromotionTaskQueryRequest{} }
func (m *PromotionTaskQueryRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionTaskQueryRequest) ProtoMessage()    {}
func (*PromotionTaskQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9dbd2b3c3aefcedc, []int{0}
}

func (m *PromotionTaskQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTaskQueryRequest.Unmarshal(m, b)
}
func (m *PromotionTaskQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTaskQueryRequest.Marshal(b, m, deterministic)
}
func (m *PromotionTaskQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTaskQueryRequest.Merge(m, src)
}
func (m *PromotionTaskQueryRequest) XXX_Size() int {
	return xxx_messageInfo_PromotionTaskQueryRequest.Size(m)
}
func (m *PromotionTaskQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTaskQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTaskQueryRequest proto.InternalMessageInfo

func (m *PromotionTaskQueryRequest) GetTypes() int32 {
	if m != nil {
		return m.Types
	}
	return 0
}

func (m *PromotionTaskQueryRequest) GetCreateUserId() string {
	if m != nil {
		return m.CreateUserId
	}
	return ""
}

func (m *PromotionTaskQueryRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *PromotionTaskQueryRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *PromotionTaskQueryRequest) GetOrgId() int64 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type PromotionTaskDetailQueryRequest struct {
	// 任务Id
	TaskId int32 `protobuf:"varint,1,opt,name=taskId,proto3" json:"taskId"`
	// 页索引
	PageIndex int32 `protobuf:"varint,3,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize             int32    `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionTaskDetailQueryRequest) Reset()         { *m = PromotionTaskDetailQueryRequest{} }
func (m *PromotionTaskDetailQueryRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionTaskDetailQueryRequest) ProtoMessage()    {}
func (*PromotionTaskDetailQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9dbd2b3c3aefcedc, []int{1}
}

func (m *PromotionTaskDetailQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTaskDetailQueryRequest.Unmarshal(m, b)
}
func (m *PromotionTaskDetailQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTaskDetailQueryRequest.Marshal(b, m, deterministic)
}
func (m *PromotionTaskDetailQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTaskDetailQueryRequest.Merge(m, src)
}
func (m *PromotionTaskDetailQueryRequest) XXX_Size() int {
	return xxx_messageInfo_PromotionTaskDetailQueryRequest.Size(m)
}
func (m *PromotionTaskDetailQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTaskDetailQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTaskDetailQueryRequest proto.InternalMessageInfo

func (m *PromotionTaskDetailQueryRequest) GetTaskId() int32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *PromotionTaskDetailQueryRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *PromotionTaskDetailQueryRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type PromotionTaskQueryResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 数据
	Data []*PromotionTaskDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总条数
	Total                int64    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionTaskQueryResponse) Reset()         { *m = PromotionTaskQueryResponse{} }
func (m *PromotionTaskQueryResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionTaskQueryResponse) ProtoMessage()    {}
func (*PromotionTaskQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9dbd2b3c3aefcedc, []int{2}
}

func (m *PromotionTaskQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTaskQueryResponse.Unmarshal(m, b)
}
func (m *PromotionTaskQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTaskQueryResponse.Marshal(b, m, deterministic)
}
func (m *PromotionTaskQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTaskQueryResponse.Merge(m, src)
}
func (m *PromotionTaskQueryResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionTaskQueryResponse.Size(m)
}
func (m *PromotionTaskQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTaskQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTaskQueryResponse proto.InternalMessageInfo

func (m *PromotionTaskQueryResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *PromotionTaskQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionTaskQueryResponse) GetData() []*PromotionTaskDto {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PromotionTaskQueryResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 根据Id查询任务信息
type PromotionTaskByIdQueryResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 数据
	Data                 *PromotionTaskDto `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PromotionTaskByIdQueryResponse) Reset()         { *m = PromotionTaskByIdQueryResponse{} }
func (m *PromotionTaskByIdQueryResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionTaskByIdQueryResponse) ProtoMessage()    {}
func (*PromotionTaskByIdQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9dbd2b3c3aefcedc, []int{3}
}

func (m *PromotionTaskByIdQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTaskByIdQueryResponse.Unmarshal(m, b)
}
func (m *PromotionTaskByIdQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTaskByIdQueryResponse.Marshal(b, m, deterministic)
}
func (m *PromotionTaskByIdQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTaskByIdQueryResponse.Merge(m, src)
}
func (m *PromotionTaskByIdQueryResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionTaskByIdQueryResponse.Size(m)
}
func (m *PromotionTaskByIdQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTaskByIdQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTaskByIdQueryResponse proto.InternalMessageInfo

func (m *PromotionTaskByIdQueryResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *PromotionTaskByIdQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionTaskByIdQueryResponse) GetData() *PromotionTaskDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// 查询明细
type PromotionTaskDetailQueryResponse struct {
	// 响应代码 0 成功 非 0 失败查看 message
	Code Code `protobuf:"varint,1,opt,name=code,proto3,enum=mk.Code" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总记录条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	// 任务列表
	Data                 []*PromotionTaskDetailDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *PromotionTaskDetailQueryResponse) Reset()         { *m = PromotionTaskDetailQueryResponse{} }
func (m *PromotionTaskDetailQueryResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionTaskDetailQueryResponse) ProtoMessage()    {}
func (*PromotionTaskDetailQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9dbd2b3c3aefcedc, []int{4}
}

func (m *PromotionTaskDetailQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionTaskDetailQueryResponse.Unmarshal(m, b)
}
func (m *PromotionTaskDetailQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionTaskDetailQueryResponse.Marshal(b, m, deterministic)
}
func (m *PromotionTaskDetailQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionTaskDetailQueryResponse.Merge(m, src)
}
func (m *PromotionTaskDetailQueryResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionTaskDetailQueryResponse.Size(m)
}
func (m *PromotionTaskDetailQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionTaskDetailQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionTaskDetailQueryResponse proto.InternalMessageInfo

func (m *PromotionTaskDetailQueryResponse) GetCode() Code {
	if m != nil {
		return m.Code
	}
	return Code_default
}

func (m *PromotionTaskDetailQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionTaskDetailQueryResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *PromotionTaskDetailQueryResponse) GetData() []*PromotionTaskDetailDto {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*PromotionTaskQueryRequest)(nil), "mk.promotionTaskQueryRequest")
	proto.RegisterType((*PromotionTaskDetailQueryRequest)(nil), "mk.promotionTaskDetailQueryRequest")
	proto.RegisterType((*PromotionTaskQueryResponse)(nil), "mk.promotionTaskQueryResponse")
	proto.RegisterType((*PromotionTaskByIdQueryResponse)(nil), "mk.promotionTaskByIdQueryResponse")
	proto.RegisterType((*PromotionTaskDetailQueryResponse)(nil), "mk.promotionTaskDetailQueryResponse")
}

func init() { proto.RegisterFile("mk/promotiontask_message.proto", fileDescriptor_9dbd2b3c3aefcedc) }

var fileDescriptor_9dbd2b3c3aefcedc = []byte{
	// 331 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x92, 0xbf, 0x4e, 0xc3, 0x30,
	0x10, 0xc6, 0xe5, 0xa6, 0x29, 0xed, 0x81, 0x3a, 0x58, 0x15, 0x32, 0x51, 0x55, 0xa2, 0x4c, 0x99,
	0x52, 0xa9, 0xbc, 0x01, 0x62, 0xc9, 0x88, 0x81, 0x19, 0x99, 0xfa, 0x54, 0x55, 0xa9, 0xeb, 0x10,
	0xbb, 0x12, 0x65, 0xe4, 0x19, 0x78, 0x00, 0x16, 0xde, 0x13, 0xc5, 0x09, 0x41, 0x16, 0x7f, 0x06,
	0x60, 0xfc, 0x5d, 0x72, 0xba, 0xdf, 0xf7, 0x25, 0x30, 0x53, 0xc5, 0xbc, 0xac, 0xb4, 0xd2, 0x76,
	0xad, 0xb7, 0x56, 0x98, 0xe2, 0x56, 0xa1, 0x31, 0x62, 0x85, 0x59, 0x59, 0x69, 0xab, 0x69, 0x4f,
	0x15, 0xd1, 0x58, 0x15, 0x73, 0xa5, 0x25, 0x6e, 0x9a, 0x59, 0xf2, 0x4a, 0xe0, 0xa4, 0xdb, 0xb9,
	0x16, 0xa6, 0xb8, 0xdc, 0x61, 0xb5, 0xe7, 0x78, 0xbf, 0x43, 0x63, 0xe9, 0x04, 0x42, 0xbb, 0x2f,
	0xd1, 0x30, 0x12, 0x93, 0x34, 0xe4, 0x0d, 0xd0, 0x04, 0x8e, 0x96, 0x15, 0x0a, 0x8b, 0x37, 0x06,
	0xab, 0x5c, 0xb2, 0x5e, 0x4c, 0xd2, 0x11, 0xf7, 0x66, 0x74, 0x0a, 0xa3, 0x52, 0xac, 0x30, 0xdf,
	0x4a, 0x7c, 0x60, 0x81, 0xdb, 0xfe, 0x18, 0xd0, 0x08, 0x86, 0x35, 0x5c, 0xad, 0x1f, 0x91, 0xf5,
	0xdd, 0xc3, 0x8e, 0xeb, 0x9b, 0xba, 0x5a, 0xe5, 0x92, 0x85, 0x31, 0x49, 0x03, 0xde, 0x40, 0x62,
	0xe0, 0xd4, 0xd3, 0xbc, 0x40, 0x2b, 0xd6, 0x1b, 0x4f, 0xf6, 0x18, 0x06, 0x75, 0xe8, 0x5c, 0xb6,
	0xb6, 0x2d, 0xfd, 0x5e, 0x25, 0x79, 0x26, 0x10, 0x7d, 0x55, 0x8e, 0x29, 0xf5, 0xd6, 0x20, 0x9d,
	0x42, 0x7f, 0xa9, 0x25, 0xba, 0x73, 0xe3, 0xc5, 0x30, 0x53, 0x45, 0x56, 0x33, 0x77, 0x53, 0xca,
	0xe0, 0xa0, 0xad, 0xbf, 0x2d, 0xe8, 0x1d, 0x69, 0x0a, 0x7d, 0x29, 0xac, 0x60, 0x41, 0x1c, 0xa4,
	0x87, 0x8b, 0x49, 0xbd, 0xe7, 0x67, 0xb3, 0x9a, 0xbb, 0x37, 0x5c, 0xff, 0xda, 0x8a, 0x8d, 0x33,
	0x0b, 0x78, 0x03, 0xc9, 0x13, 0x81, 0x99, 0xb7, 0x70, 0xbe, 0xcf, 0xe5, 0x7f, 0xab, 0x91, 0x9f,
	0xd5, 0x92, 0x17, 0x02, 0xf1, 0xf7, 0x5f, 0xe4, 0x8f, 0x1a, 0x5e, 0xee, 0xb0, 0xcd, 0x4d, 0x33,
	0xaf, 0xb7, 0xe8, 0xb3, 0x9c, 0x33, 0xe8, 0x14, 0xef, 0x06, 0xee, 0x17, 0x3f, 0x7b, 0x0b, 0x00,
	0x00, 0xff, 0xff, 0xf7, 0x7f, 0x26, 0x70, 0x18, 0x03, 0x00, 0x00,
}
