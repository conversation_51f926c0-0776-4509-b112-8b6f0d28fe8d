// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/market.proto

package mk

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 活动列表请求
type ActivityIdsRequest struct {
	// 编辑使用查询ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 搜索SKU
	Sku int32 `protobuf:"varint,2,opt,name=sku,proto3" json:"sku"`
	// 搜索活动名
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	// 搜索活动状态 -3待提交，-2待审核 1未开始 2进行中 3已结束 4已禁用
	State                int32    `protobuf:"varint,4,opt,name=state,proto3" json:"state"`
	PageIndex            int32    `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityIdsRequest) Reset()         { *m = ActivityIdsRequest{} }
func (m *ActivityIdsRequest) String() string { return proto.CompactTextString(m) }
func (*ActivityIdsRequest) ProtoMessage()    {}
func (*ActivityIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{0}
}

func (m *ActivityIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityIdsRequest.Unmarshal(m, b)
}
func (m *ActivityIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityIdsRequest.Marshal(b, m, deterministic)
}
func (m *ActivityIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityIdsRequest.Merge(m, src)
}
func (m *ActivityIdsRequest) XXX_Size() int {
	return xxx_messageInfo_ActivityIdsRequest.Size(m)
}
func (m *ActivityIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityIdsRequest proto.InternalMessageInfo

func (m *ActivityIdsRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ActivityIdsRequest) GetSku() int32 {
	if m != nil {
		return m.Sku
	}
	return 0
}

func (m *ActivityIdsRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ActivityIdsRequest) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *ActivityIdsRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ActivityIdsRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 活动删除请求
type IdRequest struct {
	// 活动ID
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IdRequest) Reset()         { *m = IdRequest{} }
func (m *IdRequest) String() string { return proto.CompactTextString(m) }
func (*IdRequest) ProtoMessage()    {}
func (*IdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{1}
}

func (m *IdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IdRequest.Unmarshal(m, b)
}
func (m *IdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IdRequest.Marshal(b, m, deterministic)
}
func (m *IdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IdRequest.Merge(m, src)
}
func (m *IdRequest) XXX_Size() int {
	return xxx_messageInfo_IdRequest.Size(m)
}
func (m *IdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IdRequest proto.InternalMessageInfo

func (m *IdRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

// 活动状态更新请求
type StatusFreshActivityRequest struct {
	// 活动ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动状态 1-启用 2-禁用
	State int32 `protobuf:"varint,2,opt,name=state,proto3" json:"state"`
	// 操作人
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StatusFreshActivityRequest) Reset()         { *m = StatusFreshActivityRequest{} }
func (m *StatusFreshActivityRequest) String() string { return proto.CompactTextString(m) }
func (*StatusFreshActivityRequest) ProtoMessage()    {}
func (*StatusFreshActivityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{2}
}

func (m *StatusFreshActivityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StatusFreshActivityRequest.Unmarshal(m, b)
}
func (m *StatusFreshActivityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StatusFreshActivityRequest.Marshal(b, m, deterministic)
}
func (m *StatusFreshActivityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatusFreshActivityRequest.Merge(m, src)
}
func (m *StatusFreshActivityRequest) XXX_Size() int {
	return xxx_messageInfo_StatusFreshActivityRequest.Size(m)
}
func (m *StatusFreshActivityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StatusFreshActivityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StatusFreshActivityRequest proto.InternalMessageInfo

func (m *StatusFreshActivityRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StatusFreshActivityRequest) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *StatusFreshActivityRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 活动新增修改请求
type NewActivityRequest struct {
	// 活动ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动名称
	MaName string `protobuf:"bytes,2,opt,name=ma_name,json=maName,proto3" json:"ma_name"`
	// 活动开始时间
	MaStartDate string `protobuf:"bytes,3,opt,name=ma_start_date,json=maStartDate,proto3" json:"ma_start_date"`
	// 活动结束时间
	MaEndDate string `protobuf:"bytes,4,opt,name=ma_end_date,json=maEndDate,proto3" json:"ma_end_date"`
	// 分享开关：1-开 2-关
	MaShareStatus int32 `protobuf:"varint,5,opt,name=ma_share_status,json=maShareStatus,proto3" json:"ma_share_status"`
	// 分享标题
	MaShareTitle string `protobuf:"bytes,6,opt,name=ma_share_title,json=maShareTitle,proto3" json:"ma_share_title"`
	// 分享图
	MaShareImg string `protobuf:"bytes,7,opt,name=ma_share_img,json=maShareImg,proto3" json:"ma_share_img"`
	// 操作人
	MaCreater string `protobuf:"bytes,8,opt,name=ma_creater,json=maCreater,proto3" json:"ma_creater"`
	// 活动商品
	MaProduct []*MaProduct `protobuf:"bytes,9,rep,name=ma_product,json=maProduct,proto3" json:"ma_product"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,10,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewActivityRequest) Reset()         { *m = NewActivityRequest{} }
func (m *NewActivityRequest) String() string { return proto.CompactTextString(m) }
func (*NewActivityRequest) ProtoMessage()    {}
func (*NewActivityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{3}
}

func (m *NewActivityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewActivityRequest.Unmarshal(m, b)
}
func (m *NewActivityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewActivityRequest.Marshal(b, m, deterministic)
}
func (m *NewActivityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewActivityRequest.Merge(m, src)
}
func (m *NewActivityRequest) XXX_Size() int {
	return xxx_messageInfo_NewActivityRequest.Size(m)
}
func (m *NewActivityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewActivityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewActivityRequest proto.InternalMessageInfo

func (m *NewActivityRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NewActivityRequest) GetMaName() string {
	if m != nil {
		return m.MaName
	}
	return ""
}

func (m *NewActivityRequest) GetMaStartDate() string {
	if m != nil {
		return m.MaStartDate
	}
	return ""
}

func (m *NewActivityRequest) GetMaEndDate() string {
	if m != nil {
		return m.MaEndDate
	}
	return ""
}

func (m *NewActivityRequest) GetMaShareStatus() int32 {
	if m != nil {
		return m.MaShareStatus
	}
	return 0
}

func (m *NewActivityRequest) GetMaShareTitle() string {
	if m != nil {
		return m.MaShareTitle
	}
	return ""
}

func (m *NewActivityRequest) GetMaShareImg() string {
	if m != nil {
		return m.MaShareImg
	}
	return ""
}

func (m *NewActivityRequest) GetMaCreater() string {
	if m != nil {
		return m.MaCreater
	}
	return ""
}

func (m *NewActivityRequest) GetMaProduct() []*MaProduct {
	if m != nil {
		return m.MaProduct
	}
	return nil
}

func (m *NewActivityRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type MaProduct struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 序号
	MaProductGroup int32 `protobuf:"varint,2,opt,name=ma_product_group,json=maProductGroup,proto3" json:"ma_product_group"`
	// 商品skuid
	MaProductSku string `protobuf:"bytes,3,opt,name=ma_product_sku,json=maProductSku,proto3" json:"ma_product_sku"`
	// 助力价格、元
	MaProductPrice string `protobuf:"bytes,4,opt,name=ma_product_price,json=maProductPrice,proto3" json:"ma_product_price"`
	// 市场价、元
	MarketPrice string `protobuf:"bytes,8,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	// 采购价，元
	PurchasePrice string `protobuf:"bytes,21,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price"`
	// 助力达标人数
	MaSuccessNum int32 `protobuf:"varint,5,opt,name=ma_success_num,json=maSuccessNum,proto3" json:"ma_success_num"`
	// 助力有效时间(小时) 0-不限 1-限制
	MaValidHourLimit int32 `protobuf:"varint,6,opt,name=ma_valid_hour_limit,json=maValidHourLimit,proto3" json:"ma_valid_hour_limit"`
	// 助力有效时间(小时)
	MaValidHour int32 `protobuf:"varint,7,opt,name=ma_valid_hour,json=maValidHour,proto3" json:"ma_valid_hour"`
	// 折扣率
	DiscountRate string `protobuf:"bytes,22,opt,name=discount_rate,json=discountRate,proto3" json:"discount_rate"`
	// 是否异常 1:异常 0：正常
	IsException int32 `protobuf:"varint,19,opt,name=is_exception,json=isException,proto3" json:"is_exception"`
	// 标记状态 0不显示按钮、1显示标记非异常、2显示取消标记
	MarkState            int32    `protobuf:"varint,20,opt,name=mark_state,json=markState,proto3" json:"mark_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MaProduct) Reset()         { *m = MaProduct{} }
func (m *MaProduct) String() string { return proto.CompactTextString(m) }
func (*MaProduct) ProtoMessage()    {}
func (*MaProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{4}
}

func (m *MaProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaProduct.Unmarshal(m, b)
}
func (m *MaProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaProduct.Marshal(b, m, deterministic)
}
func (m *MaProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaProduct.Merge(m, src)
}
func (m *MaProduct) XXX_Size() int {
	return xxx_messageInfo_MaProduct.Size(m)
}
func (m *MaProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_MaProduct.DiscardUnknown(m)
}

var xxx_messageInfo_MaProduct proto.InternalMessageInfo

func (m *MaProduct) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MaProduct) GetMaProductGroup() int32 {
	if m != nil {
		return m.MaProductGroup
	}
	return 0
}

func (m *MaProduct) GetMaProductSku() string {
	if m != nil {
		return m.MaProductSku
	}
	return ""
}

func (m *MaProduct) GetMaProductPrice() string {
	if m != nil {
		return m.MaProductPrice
	}
	return ""
}

func (m *MaProduct) GetMarketPrice() string {
	if m != nil {
		return m.MarketPrice
	}
	return ""
}

func (m *MaProduct) GetPurchasePrice() string {
	if m != nil {
		return m.PurchasePrice
	}
	return ""
}

func (m *MaProduct) GetMaSuccessNum() int32 {
	if m != nil {
		return m.MaSuccessNum
	}
	return 0
}

func (m *MaProduct) GetMaValidHourLimit() int32 {
	if m != nil {
		return m.MaValidHourLimit
	}
	return 0
}

func (m *MaProduct) GetMaValidHour() int32 {
	if m != nil {
		return m.MaValidHour
	}
	return 0
}

func (m *MaProduct) GetDiscountRate() string {
	if m != nil {
		return m.DiscountRate
	}
	return ""
}

func (m *MaProduct) GetIsException() int32 {
	if m != nil {
		return m.IsException
	}
	return 0
}

func (m *MaProduct) GetMarkState() int32 {
	if m != nil {
		return m.MarkState
	}
	return 0
}

// 活动配置查询
type QuerySettingActivityRequest struct {
	MaSettingKey         string   `protobuf:"bytes,1,opt,name=ma_setting_key,json=maSettingKey,proto3" json:"ma_setting_key"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuerySettingActivityRequest) Reset()         { *m = QuerySettingActivityRequest{} }
func (m *QuerySettingActivityRequest) String() string { return proto.CompactTextString(m) }
func (*QuerySettingActivityRequest) ProtoMessage()    {}
func (*QuerySettingActivityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{5}
}

func (m *QuerySettingActivityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySettingActivityRequest.Unmarshal(m, b)
}
func (m *QuerySettingActivityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySettingActivityRequest.Marshal(b, m, deterministic)
}
func (m *QuerySettingActivityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySettingActivityRequest.Merge(m, src)
}
func (m *QuerySettingActivityRequest) XXX_Size() int {
	return xxx_messageInfo_QuerySettingActivityRequest.Size(m)
}
func (m *QuerySettingActivityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySettingActivityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySettingActivityRequest proto.InternalMessageInfo

func (m *QuerySettingActivityRequest) GetMaSettingKey() string {
	if m != nil {
		return m.MaSettingKey
	}
	return ""
}

// 活动配置修改
type SettingActivityRequest struct {
	//单人每日助力最大上限
	MaDayMax             int32    `protobuf:"varint,1,opt,name=ma_day_max,json=maDayMax,proto3" json:"ma_day_max"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettingActivityRequest) Reset()         { *m = SettingActivityRequest{} }
func (m *SettingActivityRequest) String() string { return proto.CompactTextString(m) }
func (*SettingActivityRequest) ProtoMessage()    {}
func (*SettingActivityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{6}
}

func (m *SettingActivityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettingActivityRequest.Unmarshal(m, b)
}
func (m *SettingActivityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettingActivityRequest.Marshal(b, m, deterministic)
}
func (m *SettingActivityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettingActivityRequest.Merge(m, src)
}
func (m *SettingActivityRequest) XXX_Size() int {
	return xxx_messageInfo_SettingActivityRequest.Size(m)
}
func (m *SettingActivityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SettingActivityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SettingActivityRequest proto.InternalMessageInfo

func (m *SettingActivityRequest) GetMaDayMax() int32 {
	if m != nil {
		return m.MaDayMax
	}
	return 0
}

// 活动列表返回
type ActivityIdsResponse struct {
	Code    int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string      `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details []*Activity `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//返回总数，用于分页
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityIdsResponse) Reset()         { *m = ActivityIdsResponse{} }
func (m *ActivityIdsResponse) String() string { return proto.CompactTextString(m) }
func (*ActivityIdsResponse) ProtoMessage()    {}
func (*ActivityIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{7}
}

func (m *ActivityIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityIdsResponse.Unmarshal(m, b)
}
func (m *ActivityIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityIdsResponse.Marshal(b, m, deterministic)
}
func (m *ActivityIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityIdsResponse.Merge(m, src)
}
func (m *ActivityIdsResponse) XXX_Size() int {
	return xxx_messageInfo_ActivityIdsResponse.Size(m)
}
func (m *ActivityIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityIdsResponse proto.InternalMessageInfo

func (m *ActivityIdsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ActivityIdsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ActivityIdsResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *ActivityIdsResponse) GetDetails() []*Activity {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *ActivityIdsResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// 活动列表数据集
type Activity struct {
	// 活动ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动名称
	MaName string `protobuf:"bytes,2,opt,name=ma_name,json=maName,proto3" json:"ma_name"`
	// 活动开始时间
	MaStartDate string `protobuf:"bytes,3,opt,name=ma_start_date,json=maStartDate,proto3" json:"ma_start_date"`
	// 活动结束时间
	MaEndDate string `protobuf:"bytes,4,opt,name=ma_end_date,json=maEndDate,proto3" json:"ma_end_date"`
	// 执行人
	MaCreater string `protobuf:"bytes,5,opt,name=ma_creater,json=maCreater,proto3" json:"ma_creater"`
	// 启用状态：-3待提交，-2待审核 1未开始 2进行中 3已结束 4已禁用
	MaStatus int32 `protobuf:"varint,6,opt,name=ma_status,json=maStatus,proto3" json:"ma_status"`
	// 活动创建时间
	MaCreateDate string `protobuf:"bytes,7,opt,name=ma_create_date,json=maCreateDate,proto3" json:"ma_create_date"`
	// 分享开关：1-开 2-关
	MaShareStatus int32 `protobuf:"varint,8,opt,name=ma_share_status,json=maShareStatus,proto3" json:"ma_share_status"`
	// 分享标题
	MaShareTitle string `protobuf:"bytes,9,opt,name=ma_share_title,json=maShareTitle,proto3" json:"ma_share_title"`
	// 分享图
	MaShareImg string `protobuf:"bytes,10,opt,name=ma_share_img,json=maShareImg,proto3" json:"ma_share_img"`
	// 是否过期：1-未过期 2-已过期
	MaExpire int32 `protobuf:"varint,11,opt,name=ma_expire,json=maExpire,proto3" json:"ma_expire"`
	// 异常商品计数
	ExceptionCount int32 `protobuf:"varint,14,opt,name=exception_count,json=exceptionCount,proto3" json:"exception_count"`
	// 总商品数量
	TotalCount int32 `protobuf:"varint,15,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	// 审核原因
	CheckReason string `protobuf:"bytes,16,opt,name=check_reason,json=checkReason,proto3" json:"check_reason"`
	// 活动商品SKU拼接
	MaProductSku string `protobuf:"bytes,12,opt,name=ma_product_sku,json=maProductSku,proto3" json:"ma_product_sku"`
	// 活动详情活动商品
	MaProduct            []*MaProduct `protobuf:"bytes,13,rep,name=ma_product,json=maProduct,proto3" json:"ma_product"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *Activity) Reset()         { *m = Activity{} }
func (m *Activity) String() string { return proto.CompactTextString(m) }
func (*Activity) ProtoMessage()    {}
func (*Activity) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{8}
}

func (m *Activity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Activity.Unmarshal(m, b)
}
func (m *Activity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Activity.Marshal(b, m, deterministic)
}
func (m *Activity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Activity.Merge(m, src)
}
func (m *Activity) XXX_Size() int {
	return xxx_messageInfo_Activity.Size(m)
}
func (m *Activity) XXX_DiscardUnknown() {
	xxx_messageInfo_Activity.DiscardUnknown(m)
}

var xxx_messageInfo_Activity proto.InternalMessageInfo

func (m *Activity) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Activity) GetMaName() string {
	if m != nil {
		return m.MaName
	}
	return ""
}

func (m *Activity) GetMaStartDate() string {
	if m != nil {
		return m.MaStartDate
	}
	return ""
}

func (m *Activity) GetMaEndDate() string {
	if m != nil {
		return m.MaEndDate
	}
	return ""
}

func (m *Activity) GetMaCreater() string {
	if m != nil {
		return m.MaCreater
	}
	return ""
}

func (m *Activity) GetMaStatus() int32 {
	if m != nil {
		return m.MaStatus
	}
	return 0
}

func (m *Activity) GetMaCreateDate() string {
	if m != nil {
		return m.MaCreateDate
	}
	return ""
}

func (m *Activity) GetMaShareStatus() int32 {
	if m != nil {
		return m.MaShareStatus
	}
	return 0
}

func (m *Activity) GetMaShareTitle() string {
	if m != nil {
		return m.MaShareTitle
	}
	return ""
}

func (m *Activity) GetMaShareImg() string {
	if m != nil {
		return m.MaShareImg
	}
	return ""
}

func (m *Activity) GetMaExpire() int32 {
	if m != nil {
		return m.MaExpire
	}
	return 0
}

func (m *Activity) GetExceptionCount() int32 {
	if m != nil {
		return m.ExceptionCount
	}
	return 0
}

func (m *Activity) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *Activity) GetCheckReason() string {
	if m != nil {
		return m.CheckReason
	}
	return ""
}

func (m *Activity) GetMaProductSku() string {
	if m != nil {
		return m.MaProductSku
	}
	return ""
}

func (m *Activity) GetMaProduct() []*MaProduct {
	if m != nil {
		return m.MaProduct
	}
	return nil
}

// 活动配置返回
type SettingActivityResponse struct {
	Code                 int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string             `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details              []*ActivitySetting `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SettingActivityResponse) Reset()         { *m = SettingActivityResponse{} }
func (m *SettingActivityResponse) String() string { return proto.CompactTextString(m) }
func (*SettingActivityResponse) ProtoMessage()    {}
func (*SettingActivityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{9}
}

func (m *SettingActivityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettingActivityResponse.Unmarshal(m, b)
}
func (m *SettingActivityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettingActivityResponse.Marshal(b, m, deterministic)
}
func (m *SettingActivityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettingActivityResponse.Merge(m, src)
}
func (m *SettingActivityResponse) XXX_Size() int {
	return xxx_messageInfo_SettingActivityResponse.Size(m)
}
func (m *SettingActivityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SettingActivityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SettingActivityResponse proto.InternalMessageInfo

func (m *SettingActivityResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SettingActivityResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SettingActivityResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *SettingActivityResponse) GetDetails() []*ActivitySetting {
	if m != nil {
		return m.Details
	}
	return nil
}

type ActivitySetting struct {
	MaDayMax             int32    `protobuf:"varint,1,opt,name=ma_day_max,json=maDayMax,proto3" json:"ma_day_max"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivitySetting) Reset()         { *m = ActivitySetting{} }
func (m *ActivitySetting) String() string { return proto.CompactTextString(m) }
func (*ActivitySetting) ProtoMessage()    {}
func (*ActivitySetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{10}
}

func (m *ActivitySetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivitySetting.Unmarshal(m, b)
}
func (m *ActivitySetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivitySetting.Marshal(b, m, deterministic)
}
func (m *ActivitySetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivitySetting.Merge(m, src)
}
func (m *ActivitySetting) XXX_Size() int {
	return xxx_messageInfo_ActivitySetting.Size(m)
}
func (m *ActivitySetting) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivitySetting.DiscardUnknown(m)
}

var xxx_messageInfo_ActivitySetting proto.InternalMessageInfo

func (m *ActivitySetting) GetMaDayMax() int32 {
	if m != nil {
		return m.MaDayMax
	}
	return 0
}

// 优惠券新增修改请求
type CouponActivityNewRequest struct {
	// 活动ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 业务类型：1-预约挂号
	BusinessType int32 `protobuf:"varint,2,opt,name=business_type,json=businessType,proto3" json:"business_type"`
	//发券场景：1-预约挂号成功,2-商城支付成功,3-会员卡续费成功
	CouponScene int32 `protobuf:"varint,3,opt,name=coupon_scene,json=couponScene,proto3" json:"coupon_scene"`
	//优惠券类型：1-商城券,2-门店券,3-本地生活券,4-阿闻平台券
	CouponType int32 `protobuf:"varint,4,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type"`
	//优惠券ID
	CouponId string `protobuf:"bytes,5,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	// 活动开始时间
	StartDate string `protobuf:"bytes,6,opt,name=start_date,json=startDate,proto3" json:"start_date"`
	// 活动结束时间
	EndDate string `protobuf:"bytes,7,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	// 适用渠道
	Channel string `protobuf:"bytes,8,opt,name=channel,proto3" json:"channel"`
	// 操作人
	CaCreater string `protobuf:"bytes,9,opt,name=ca_creater,json=caCreater,proto3" json:"ca_creater"`
	// 启用状态：0-未开始 1-已开始 2-已结束 3-已终止
	CaStatus int32 `protobuf:"varint,10,opt,name=ca_status,json=caStatus,proto3" json:"ca_status"`
	// 弹窗背景图
	CaImgUrl string `protobuf:"bytes,11,opt,name=ca_img_url,json=caImgUrl,proto3" json:"ca_img_url"`
	// 微页面路径
	CaPath               string   `protobuf:"bytes,12,opt,name=ca_path,json=caPath,proto3" json:"ca_path"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponActivityNewRequest) Reset()         { *m = CouponActivityNewRequest{} }
func (m *CouponActivityNewRequest) String() string { return proto.CompactTextString(m) }
func (*CouponActivityNewRequest) ProtoMessage()    {}
func (*CouponActivityNewRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{11}
}

func (m *CouponActivityNewRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponActivityNewRequest.Unmarshal(m, b)
}
func (m *CouponActivityNewRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponActivityNewRequest.Marshal(b, m, deterministic)
}
func (m *CouponActivityNewRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponActivityNewRequest.Merge(m, src)
}
func (m *CouponActivityNewRequest) XXX_Size() int {
	return xxx_messageInfo_CouponActivityNewRequest.Size(m)
}
func (m *CouponActivityNewRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponActivityNewRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CouponActivityNewRequest proto.InternalMessageInfo

func (m *CouponActivityNewRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CouponActivityNewRequest) GetBusinessType() int32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *CouponActivityNewRequest) GetCouponScene() int32 {
	if m != nil {
		return m.CouponScene
	}
	return 0
}

func (m *CouponActivityNewRequest) GetCouponType() int32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

func (m *CouponActivityNewRequest) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *CouponActivityNewRequest) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *CouponActivityNewRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *CouponActivityNewRequest) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *CouponActivityNewRequest) GetCaCreater() string {
	if m != nil {
		return m.CaCreater
	}
	return ""
}

func (m *CouponActivityNewRequest) GetCaStatus() int32 {
	if m != nil {
		return m.CaStatus
	}
	return 0
}

func (m *CouponActivityNewRequest) GetCaImgUrl() string {
	if m != nil {
		return m.CaImgUrl
	}
	return ""
}

func (m *CouponActivityNewRequest) GetCaPath() string {
	if m != nil {
		return m.CaPath
	}
	return ""
}

// 优惠券列表请求
type CouponListRequest struct {
	//优惠券ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 优惠券类型
	CouponType int32 `protobuf:"varint,2,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type"`
	// 发券场景
	CouponScene int32 `protobuf:"varint,3,opt,name=coupon_scene,json=couponScene,proto3" json:"coupon_scene"`
	//业务类型
	BusinessType int32 `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type"`
	// 启用状态：1-未开始 2-已开始 3-已结束 4-已终止
	State int32 `protobuf:"varint,5,opt,name=state,proto3" json:"state"`
	//开始时间
	Stime string `protobuf:"bytes,6,opt,name=stime,proto3" json:"stime"`
	//结束时间
	Etime string `protobuf:"bytes,7,opt,name=etime,proto3" json:"etime"`
	//适用渠道
	Channel              string   `protobuf:"bytes,8,opt,name=channel,proto3" json:"channel"`
	PageIndex            int32    `protobuf:"varint,9,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,10,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponListRequest) Reset()         { *m = CouponListRequest{} }
func (m *CouponListRequest) String() string { return proto.CompactTextString(m) }
func (*CouponListRequest) ProtoMessage()    {}
func (*CouponListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{12}
}

func (m *CouponListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponListRequest.Unmarshal(m, b)
}
func (m *CouponListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponListRequest.Marshal(b, m, deterministic)
}
func (m *CouponListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponListRequest.Merge(m, src)
}
func (m *CouponListRequest) XXX_Size() int {
	return xxx_messageInfo_CouponListRequest.Size(m)
}
func (m *CouponListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CouponListRequest proto.InternalMessageInfo

func (m *CouponListRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CouponListRequest) GetCouponType() int32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

func (m *CouponListRequest) GetCouponScene() int32 {
	if m != nil {
		return m.CouponScene
	}
	return 0
}

func (m *CouponListRequest) GetBusinessType() int32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *CouponListRequest) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *CouponListRequest) GetStime() string {
	if m != nil {
		return m.Stime
	}
	return ""
}

func (m *CouponListRequest) GetEtime() string {
	if m != nil {
		return m.Etime
	}
	return ""
}

func (m *CouponListRequest) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *CouponListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CouponListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 优惠券列表返回
type CouponListResponse struct {
	Code    int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string        `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details []*CouponData `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//返回总数，用于分页
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponListResponse) Reset()         { *m = CouponListResponse{} }
func (m *CouponListResponse) String() string { return proto.CompactTextString(m) }
func (*CouponListResponse) ProtoMessage()    {}
func (*CouponListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{13}
}

func (m *CouponListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponListResponse.Unmarshal(m, b)
}
func (m *CouponListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponListResponse.Marshal(b, m, deterministic)
}
func (m *CouponListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponListResponse.Merge(m, src)
}
func (m *CouponListResponse) XXX_Size() int {
	return xxx_messageInfo_CouponListResponse.Size(m)
}
func (m *CouponListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CouponListResponse proto.InternalMessageInfo

func (m *CouponListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CouponListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CouponListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CouponListResponse) GetDetails() []*CouponData {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *CouponListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// 优惠券列表数据集
type CouponData struct {
	// 索引ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 业务类型
	CaBusinessType string `protobuf:"bytes,2,opt,name=ca_business_type,json=caBusinessType,proto3" json:"ca_business_type"`
	//业务类型文字说明
	BusinessTypeText string `protobuf:"bytes,3,opt,name=business_type_text,json=businessTypeText,proto3" json:"business_type_text"`
	// 发券场景
	CaCouponScene string `protobuf:"bytes,4,opt,name=ca_coupon_scene,json=caCouponScene,proto3" json:"ca_coupon_scene"`
	// 发券场景文字说明
	CouponSceneText string `protobuf:"bytes,5,opt,name=coupon_scene_text,json=couponSceneText,proto3" json:"coupon_scene_text"`
	// 优惠券类型
	CaCouponType string `protobuf:"bytes,6,opt,name=ca_coupon_type,json=caCouponType,proto3" json:"ca_coupon_type"`
	// 优惠券类型文字说明
	CouponTypeText string `protobuf:"bytes,7,opt,name=coupon_type_text,json=couponTypeText,proto3" json:"coupon_type_text"`
	// 优惠券ID
	CaCouponId string `protobuf:"bytes,8,opt,name=ca_coupon_id,json=caCouponId,proto3" json:"ca_coupon_id"`
	// 活动开始时间
	CaStartDate string `protobuf:"bytes,9,opt,name=ca_start_date,json=caStartDate,proto3" json:"ca_start_date"`
	// 活动结束时间
	CaEndDate string `protobuf:"bytes,10,opt,name=ca_end_date,json=caEndDate,proto3" json:"ca_end_date"`
	// 活动创建时间
	CaCreateDate string `protobuf:"bytes,11,opt,name=ca_create_date,json=caCreateDate,proto3" json:"ca_create_date"`
	// 适用渠道
	CaChannel string `protobuf:"bytes,12,opt,name=ca_channel,json=caChannel,proto3" json:"ca_channel"`
	// 创建者
	CaCreater string `protobuf:"bytes,13,opt,name=ca_creater,json=caCreater,proto3" json:"ca_creater"`
	// 启用状态：1-未开始 2-已开始 3-已结束 4-已终止
	CaStatus int32 `protobuf:"varint,14,opt,name=ca_status,json=caStatus,proto3" json:"ca_status"`
	// 发放数量
	CaNumbers int32 `protobuf:"varint,15,opt,name=ca_numbers,json=caNumbers,proto3" json:"ca_numbers"`
	// 弹窗背景图
	CaImgUrl string `protobuf:"bytes,16,opt,name=ca_img_url,json=caImgUrl,proto3" json:"ca_img_url"`
	// 微页面路径
	CaPath               string   `protobuf:"bytes,17,opt,name=ca_path,json=caPath,proto3" json:"ca_path"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponData) Reset()         { *m = CouponData{} }
func (m *CouponData) String() string { return proto.CompactTextString(m) }
func (*CouponData) ProtoMessage()    {}
func (*CouponData) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{14}
}

func (m *CouponData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponData.Unmarshal(m, b)
}
func (m *CouponData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponData.Marshal(b, m, deterministic)
}
func (m *CouponData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponData.Merge(m, src)
}
func (m *CouponData) XXX_Size() int {
	return xxx_messageInfo_CouponData.Size(m)
}
func (m *CouponData) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponData.DiscardUnknown(m)
}

var xxx_messageInfo_CouponData proto.InternalMessageInfo

func (m *CouponData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CouponData) GetCaBusinessType() string {
	if m != nil {
		return m.CaBusinessType
	}
	return ""
}

func (m *CouponData) GetBusinessTypeText() string {
	if m != nil {
		return m.BusinessTypeText
	}
	return ""
}

func (m *CouponData) GetCaCouponScene() string {
	if m != nil {
		return m.CaCouponScene
	}
	return ""
}

func (m *CouponData) GetCouponSceneText() string {
	if m != nil {
		return m.CouponSceneText
	}
	return ""
}

func (m *CouponData) GetCaCouponType() string {
	if m != nil {
		return m.CaCouponType
	}
	return ""
}

func (m *CouponData) GetCouponTypeText() string {
	if m != nil {
		return m.CouponTypeText
	}
	return ""
}

func (m *CouponData) GetCaCouponId() string {
	if m != nil {
		return m.CaCouponId
	}
	return ""
}

func (m *CouponData) GetCaStartDate() string {
	if m != nil {
		return m.CaStartDate
	}
	return ""
}

func (m *CouponData) GetCaEndDate() string {
	if m != nil {
		return m.CaEndDate
	}
	return ""
}

func (m *CouponData) GetCaCreateDate() string {
	if m != nil {
		return m.CaCreateDate
	}
	return ""
}

func (m *CouponData) GetCaChannel() string {
	if m != nil {
		return m.CaChannel
	}
	return ""
}

func (m *CouponData) GetCaCreater() string {
	if m != nil {
		return m.CaCreater
	}
	return ""
}

func (m *CouponData) GetCaStatus() int32 {
	if m != nil {
		return m.CaStatus
	}
	return 0
}

func (m *CouponData) GetCaNumbers() int32 {
	if m != nil {
		return m.CaNumbers
	}
	return 0
}

func (m *CouponData) GetCaImgUrl() string {
	if m != nil {
		return m.CaImgUrl
	}
	return ""
}

func (m *CouponData) GetCaPath() string {
	if m != nil {
		return m.CaPath
	}
	return ""
}

//优惠列表字段
type VoucherList struct {
	//优惠券id
	VoucherId int32 `protobuf:"varint,1,opt,name=VoucherId,proto3" json:"VoucherId"`
	//开始时间
	VoucherStartDate int32 `protobuf:"varint,2,opt,name=VoucherStartDate,proto3" json:"VoucherStartDate"`
	//结束时间
	VoucherEndDate       int32    `protobuf:"varint,3,opt,name=VoucherEndDate,proto3" json:"VoucherEndDate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoucherList) Reset()         { *m = VoucherList{} }
func (m *VoucherList) String() string { return proto.CompactTextString(m) }
func (*VoucherList) ProtoMessage()    {}
func (*VoucherList) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{15}
}

func (m *VoucherList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoucherList.Unmarshal(m, b)
}
func (m *VoucherList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoucherList.Marshal(b, m, deterministic)
}
func (m *VoucherList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoucherList.Merge(m, src)
}
func (m *VoucherList) XXX_Size() int {
	return xxx_messageInfo_VoucherList.Size(m)
}
func (m *VoucherList) XXX_DiscardUnknown() {
	xxx_messageInfo_VoucherList.DiscardUnknown(m)
}

var xxx_messageInfo_VoucherList proto.InternalMessageInfo

func (m *VoucherList) GetVoucherId() int32 {
	if m != nil {
		return m.VoucherId
	}
	return 0
}

func (m *VoucherList) GetVoucherStartDate() int32 {
	if m != nil {
		return m.VoucherStartDate
	}
	return 0
}

func (m *VoucherList) GetVoucherEndDate() int32 {
	if m != nil {
		return m.VoucherEndDate
	}
	return 0
}

// 领取优惠券列表请求
type VoucherListRequest struct {
	//用户名
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
	// 券类型
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	//开始时间
	Stime string `protobuf:"bytes,3,opt,name=stime,proto3" json:"stime"`
	//结束时间
	Etime string `protobuf:"bytes,4,opt,name=etime,proto3" json:"etime"`
	// 手机号
	Mobile string `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile"`
	// 优惠券ID
	Couponid             string   `protobuf:"bytes,6,opt,name=couponid,proto3" json:"couponid"`
	PageIndex            int32    `protobuf:"varint,7,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoucherListRequest) Reset()         { *m = VoucherListRequest{} }
func (m *VoucherListRequest) String() string { return proto.CompactTextString(m) }
func (*VoucherListRequest) ProtoMessage()    {}
func (*VoucherListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{16}
}

func (m *VoucherListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoucherListRequest.Unmarshal(m, b)
}
func (m *VoucherListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoucherListRequest.Marshal(b, m, deterministic)
}
func (m *VoucherListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoucherListRequest.Merge(m, src)
}
func (m *VoucherListRequest) XXX_Size() int {
	return xxx_messageInfo_VoucherListRequest.Size(m)
}
func (m *VoucherListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VoucherListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VoucherListRequest proto.InternalMessageInfo

func (m *VoucherListRequest) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *VoucherListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *VoucherListRequest) GetStime() string {
	if m != nil {
		return m.Stime
	}
	return ""
}

func (m *VoucherListRequest) GetEtime() string {
	if m != nil {
		return m.Etime
	}
	return ""
}

func (m *VoucherListRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *VoucherListRequest) GetCouponid() string {
	if m != nil {
		return m.Couponid
	}
	return ""
}

func (m *VoucherListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *VoucherListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 领取优惠券列表返回
type VoucherListResponse struct {
	Code    int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string         `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details []*VoucherData `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//返回总数，用于分页
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoucherListResponse) Reset()         { *m = VoucherListResponse{} }
func (m *VoucherListResponse) String() string { return proto.CompactTextString(m) }
func (*VoucherListResponse) ProtoMessage()    {}
func (*VoucherListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{17}
}

func (m *VoucherListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoucherListResponse.Unmarshal(m, b)
}
func (m *VoucherListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoucherListResponse.Marshal(b, m, deterministic)
}
func (m *VoucherListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoucherListResponse.Merge(m, src)
}
func (m *VoucherListResponse) XXX_Size() int {
	return xxx_messageInfo_VoucherListResponse.Size(m)
}
func (m *VoucherListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_VoucherListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_VoucherListResponse proto.InternalMessageInfo

func (m *VoucherListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VoucherListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *VoucherListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *VoucherListResponse) GetDetails() []*VoucherData {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *VoucherListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// 领取优惠券列表数据集
type VoucherData struct {
	// 索引ID
	VoucherId int32 `protobuf:"varint,1,opt,name=VoucherId,proto3" json:"VoucherId"`
	// 用户名
	MemberName string `protobuf:"bytes,2,opt,name=MemberName,proto3" json:"MemberName"`
	// 手机号
	MemberMobile string `protobuf:"bytes,3,opt,name=MemberMobile,proto3" json:"MemberMobile"`
	// 领取时间
	VoucherActiveDate string `protobuf:"bytes,4,opt,name=VoucherActiveDate,proto3" json:"VoucherActiveDate"`
	// 券类型
	VoucherFrom string `protobuf:"bytes,5,opt,name=VoucherFrom,proto3" json:"VoucherFrom"`
	// 券ID
	VoucherTId           int32    `protobuf:"varint,6,opt,name=VoucherTId,proto3" json:"VoucherTId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoucherData) Reset()         { *m = VoucherData{} }
func (m *VoucherData) String() string { return proto.CompactTextString(m) }
func (*VoucherData) ProtoMessage()    {}
func (*VoucherData) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{18}
}

func (m *VoucherData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoucherData.Unmarshal(m, b)
}
func (m *VoucherData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoucherData.Marshal(b, m, deterministic)
}
func (m *VoucherData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoucherData.Merge(m, src)
}
func (m *VoucherData) XXX_Size() int {
	return xxx_messageInfo_VoucherData.Size(m)
}
func (m *VoucherData) XXX_DiscardUnknown() {
	xxx_messageInfo_VoucherData.DiscardUnknown(m)
}

var xxx_messageInfo_VoucherData proto.InternalMessageInfo

func (m *VoucherData) GetVoucherId() int32 {
	if m != nil {
		return m.VoucherId
	}
	return 0
}

func (m *VoucherData) GetMemberName() string {
	if m != nil {
		return m.MemberName
	}
	return ""
}

func (m *VoucherData) GetMemberMobile() string {
	if m != nil {
		return m.MemberMobile
	}
	return ""
}

func (m *VoucherData) GetVoucherActiveDate() string {
	if m != nil {
		return m.VoucherActiveDate
	}
	return ""
}

func (m *VoucherData) GetVoucherFrom() string {
	if m != nil {
		return m.VoucherFrom
	}
	return ""
}

func (m *VoucherData) GetVoucherTId() int32 {
	if m != nil {
		return m.VoucherTId
	}
	return 0
}

//流浪救助用户列表
type UserListRequest struct {
	// 手机号
	MemberPhone string `protobuf:"bytes,1,opt,name=member_phone,json=memberPhone,proto3" json:"member_phone"`
	// 用户uuid
	MemberId int32 `protobuf:"varint,2,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	// 查询状态：0-黑名单 1-全部
	MemberState int32 `protobuf:"varint,3,opt,name=member_state,json=memberState,proto3" json:"member_state"`
	// 分页参数
	PageIndex            int32    `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserListRequest) Reset()         { *m = UserListRequest{} }
func (m *UserListRequest) String() string { return proto.CompactTextString(m) }
func (*UserListRequest) ProtoMessage()    {}
func (*UserListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{19}
}

func (m *UserListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserListRequest.Unmarshal(m, b)
}
func (m *UserListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserListRequest.Marshal(b, m, deterministic)
}
func (m *UserListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserListRequest.Merge(m, src)
}
func (m *UserListRequest) XXX_Size() int {
	return xxx_messageInfo_UserListRequest.Size(m)
}
func (m *UserListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserListRequest proto.InternalMessageInfo

func (m *UserListRequest) GetMemberPhone() string {
	if m != nil {
		return m.MemberPhone
	}
	return ""
}

func (m *UserListRequest) GetMemberId() int32 {
	if m != nil {
		return m.MemberId
	}
	return 0
}

func (m *UserListRequest) GetMemberState() int32 {
	if m != nil {
		return m.MemberState
	}
	return 0
}

func (m *UserListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *UserListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 流浪救助用户列表
type UserListResponse struct {
	Code    int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string      `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details []*UserData `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//返回总数，用于分页
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserListResponse) Reset()         { *m = UserListResponse{} }
func (m *UserListResponse) String() string { return proto.CompactTextString(m) }
func (*UserListResponse) ProtoMessage()    {}
func (*UserListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{20}
}

func (m *UserListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserListResponse.Unmarshal(m, b)
}
func (m *UserListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserListResponse.Marshal(b, m, deterministic)
}
func (m *UserListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserListResponse.Merge(m, src)
}
func (m *UserListResponse) XXX_Size() int {
	return xxx_messageInfo_UserListResponse.Size(m)
}
func (m *UserListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserListResponse proto.InternalMessageInfo

func (m *UserListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *UserListResponse) GetDetails() []*UserData {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *UserListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

//流浪救助用户列表
type UserData struct {
	MemberId             int32    `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	NickName             string   `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	MemberMobile         string   `protobuf:"bytes,3,opt,name=member_mobile,json=memberMobile,proto3" json:"member_mobile"`
	DonatedWeight        string   `protobuf:"bytes,4,opt,name=donated_weight,json=donatedWeight,proto3" json:"donated_weight"`
	FreeDonated          int32    `protobuf:"varint,5,opt,name=free_donated,json=freeDonated,proto3" json:"free_donated"`
	State                int32    `protobuf:"varint,6,opt,name=state,proto3" json:"state"`
	StateText            string   `protobuf:"bytes,7,opt,name=state_text,json=stateText,proto3" json:"state_text"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserData) Reset()         { *m = UserData{} }
func (m *UserData) String() string { return proto.CompactTextString(m) }
func (*UserData) ProtoMessage()    {}
func (*UserData) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{21}
}

func (m *UserData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserData.Unmarshal(m, b)
}
func (m *UserData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserData.Marshal(b, m, deterministic)
}
func (m *UserData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserData.Merge(m, src)
}
func (m *UserData) XXX_Size() int {
	return xxx_messageInfo_UserData.Size(m)
}
func (m *UserData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserData.DiscardUnknown(m)
}

var xxx_messageInfo_UserData proto.InternalMessageInfo

func (m *UserData) GetMemberId() int32 {
	if m != nil {
		return m.MemberId
	}
	return 0
}

func (m *UserData) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *UserData) GetMemberMobile() string {
	if m != nil {
		return m.MemberMobile
	}
	return ""
}

func (m *UserData) GetDonatedWeight() string {
	if m != nil {
		return m.DonatedWeight
	}
	return ""
}

func (m *UserData) GetFreeDonated() int32 {
	if m != nil {
		return m.FreeDonated
	}
	return 0
}

func (m *UserData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *UserData) GetStateText() string {
	if m != nil {
		return m.StateText
	}
	return ""
}

//流浪救助用户修改
type UserRequest struct {
	//id
	MemberId int32 `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	// 状态
	MemberState          int32    `protobuf:"varint,2,opt,name=member_state,json=memberState,proto3" json:"member_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRequest) Reset()         { *m = UserRequest{} }
func (m *UserRequest) String() string { return proto.CompactTextString(m) }
func (*UserRequest) ProtoMessage()    {}
func (*UserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{22}
}

func (m *UserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRequest.Unmarshal(m, b)
}
func (m *UserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRequest.Marshal(b, m, deterministic)
}
func (m *UserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRequest.Merge(m, src)
}
func (m *UserRequest) XXX_Size() int {
	return xxx_messageInfo_UserRequest.Size(m)
}
func (m *UserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserRequest proto.InternalMessageInfo

func (m *UserRequest) GetMemberId() int32 {
	if m != nil {
		return m.MemberId
	}
	return 0
}

func (m *UserRequest) GetMemberState() int32 {
	if m != nil {
		return m.MemberState
	}
	return 0
}

// 流浪救助用户修改
type UserResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserResponse) Reset()         { *m = UserResponse{} }
func (m *UserResponse) String() string { return proto.CompactTextString(m) }
func (*UserResponse) ProtoMessage()    {}
func (*UserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{23}
}

func (m *UserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserResponse.Unmarshal(m, b)
}
func (m *UserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserResponse.Marshal(b, m, deterministic)
}
func (m *UserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserResponse.Merge(m, src)
}
func (m *UserResponse) XXX_Size() int {
	return xxx_messageInfo_UserResponse.Size(m)
}
func (m *UserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserResponse proto.InternalMessageInfo

func (m *UserResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

//二维码列表
type QrcodeRequest struct {
	PageIndex            int32    `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Id                   int32    `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QrcodeRequest) Reset()         { *m = QrcodeRequest{} }
func (m *QrcodeRequest) String() string { return proto.CompactTextString(m) }
func (*QrcodeRequest) ProtoMessage()    {}
func (*QrcodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{24}
}

func (m *QrcodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeRequest.Unmarshal(m, b)
}
func (m *QrcodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeRequest.Marshal(b, m, deterministic)
}
func (m *QrcodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeRequest.Merge(m, src)
}
func (m *QrcodeRequest) XXX_Size() int {
	return xxx_messageInfo_QrcodeRequest.Size(m)
}
func (m *QrcodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeRequest proto.InternalMessageInfo

func (m *QrcodeRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *QrcodeRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *QrcodeRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 二维码
type QrcodeResponse struct {
	Code    int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string        `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details []*QrcodeData `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//返回总数，用于分页
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	QrcodeName           string   `protobuf:"bytes,6,opt,name=qrcode_name,json=qrcodeName,proto3" json:"qrcode_name"`
	QrId                 int32    `protobuf:"varint,7,opt,name=qr_id,json=qrId,proto3" json:"qr_id"`
	StartName            string   `protobuf:"bytes,8,opt,name=start_name,json=startName,proto3" json:"start_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QrcodeResponse) Reset()         { *m = QrcodeResponse{} }
func (m *QrcodeResponse) String() string { return proto.CompactTextString(m) }
func (*QrcodeResponse) ProtoMessage()    {}
func (*QrcodeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{25}
}

func (m *QrcodeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeResponse.Unmarshal(m, b)
}
func (m *QrcodeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeResponse.Marshal(b, m, deterministic)
}
func (m *QrcodeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeResponse.Merge(m, src)
}
func (m *QrcodeResponse) XXX_Size() int {
	return xxx_messageInfo_QrcodeResponse.Size(m)
}
func (m *QrcodeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeResponse proto.InternalMessageInfo

func (m *QrcodeResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QrcodeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QrcodeResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *QrcodeResponse) GetDetails() []*QrcodeData {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *QrcodeResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *QrcodeResponse) GetQrcodeName() string {
	if m != nil {
		return m.QrcodeName
	}
	return ""
}

func (m *QrcodeResponse) GetQrId() int32 {
	if m != nil {
		return m.QrId
	}
	return 0
}

func (m *QrcodeResponse) GetStartName() string {
	if m != nil {
		return m.StartName
	}
	return ""
}

//二维码
type QrcodeData struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Qrcode               string   `protobuf:"bytes,3,opt,name=qrcode,proto3" json:"qrcode"`
	AddTime              string   `protobuf:"bytes,4,opt,name=add_time,json=addTime,proto3" json:"add_time"`
	DonatePath           string   `protobuf:"bytes,5,opt,name=donate_path,json=donatePath,proto3" json:"donate_path"`
	DonateMiniQrcode     string   `protobuf:"bytes,6,opt,name=donate_mini_qrcode,json=donateMiniQrcode,proto3" json:"donate_mini_qrcode"`
	State                int32    `protobuf:"varint,7,opt,name=state,proto3" json:"state"`
	StateText            string   `protobuf:"bytes,8,opt,name=state_text,json=stateText,proto3" json:"state_text"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QrcodeData) Reset()         { *m = QrcodeData{} }
func (m *QrcodeData) String() string { return proto.CompactTextString(m) }
func (*QrcodeData) ProtoMessage()    {}
func (*QrcodeData) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{26}
}

func (m *QrcodeData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeData.Unmarshal(m, b)
}
func (m *QrcodeData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeData.Marshal(b, m, deterministic)
}
func (m *QrcodeData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeData.Merge(m, src)
}
func (m *QrcodeData) XXX_Size() int {
	return xxx_messageInfo_QrcodeData.Size(m)
}
func (m *QrcodeData) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeData.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeData proto.InternalMessageInfo

func (m *QrcodeData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *QrcodeData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *QrcodeData) GetQrcode() string {
	if m != nil {
		return m.Qrcode
	}
	return ""
}

func (m *QrcodeData) GetAddTime() string {
	if m != nil {
		return m.AddTime
	}
	return ""
}

func (m *QrcodeData) GetDonatePath() string {
	if m != nil {
		return m.DonatePath
	}
	return ""
}

func (m *QrcodeData) GetDonateMiniQrcode() string {
	if m != nil {
		return m.DonateMiniQrcode
	}
	return ""
}

func (m *QrcodeData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *QrcodeData) GetStateText() string {
	if m != nil {
		return m.StateText
	}
	return ""
}

//二维码新增,编辑
type QrcodeAddRequest struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Qrcode               string   `protobuf:"bytes,2,opt,name=qrcode,proto3" json:"qrcode"`
	DonatePath           string   `protobuf:"bytes,3,opt,name=donate_path,json=donatePath,proto3" json:"donate_path"`
	Id                   int32    `protobuf:"varint,4,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QrcodeAddRequest) Reset()         { *m = QrcodeAddRequest{} }
func (m *QrcodeAddRequest) String() string { return proto.CompactTextString(m) }
func (*QrcodeAddRequest) ProtoMessage()    {}
func (*QrcodeAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{27}
}

func (m *QrcodeAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeAddRequest.Unmarshal(m, b)
}
func (m *QrcodeAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeAddRequest.Marshal(b, m, deterministic)
}
func (m *QrcodeAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeAddRequest.Merge(m, src)
}
func (m *QrcodeAddRequest) XXX_Size() int {
	return xxx_messageInfo_QrcodeAddRequest.Size(m)
}
func (m *QrcodeAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeAddRequest proto.InternalMessageInfo

func (m *QrcodeAddRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *QrcodeAddRequest) GetQrcode() string {
	if m != nil {
		return m.Qrcode
	}
	return ""
}

func (m *QrcodeAddRequest) GetDonatePath() string {
	if m != nil {
		return m.DonatePath
	}
	return ""
}

func (m *QrcodeAddRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type QrcodeAddResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QrcodeAddResponse) Reset()         { *m = QrcodeAddResponse{} }
func (m *QrcodeAddResponse) String() string { return proto.CompactTextString(m) }
func (*QrcodeAddResponse) ProtoMessage()    {}
func (*QrcodeAddResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{28}
}

func (m *QrcodeAddResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeAddResponse.Unmarshal(m, b)
}
func (m *QrcodeAddResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeAddResponse.Marshal(b, m, deterministic)
}
func (m *QrcodeAddResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeAddResponse.Merge(m, src)
}
func (m *QrcodeAddResponse) XXX_Size() int {
	return xxx_messageInfo_QrcodeAddResponse.Size(m)
}
func (m *QrcodeAddResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeAddResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeAddResponse proto.InternalMessageInfo

func (m *QrcodeAddResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QrcodeAddResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QrcodeAddResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//二维码启用
type QrcodeSetRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QrcodeSetRequest) Reset()         { *m = QrcodeSetRequest{} }
func (m *QrcodeSetRequest) String() string { return proto.CompactTextString(m) }
func (*QrcodeSetRequest) ProtoMessage()    {}
func (*QrcodeSetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{29}
}

func (m *QrcodeSetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeSetRequest.Unmarshal(m, b)
}
func (m *QrcodeSetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeSetRequest.Marshal(b, m, deterministic)
}
func (m *QrcodeSetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeSetRequest.Merge(m, src)
}
func (m *QrcodeSetRequest) XXX_Size() int {
	return xxx_messageInfo_QrcodeSetRequest.Size(m)
}
func (m *QrcodeSetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeSetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeSetRequest proto.InternalMessageInfo

func (m *QrcodeSetRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 二维码启用
type QrcodeSetResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QrcodeSetResponse) Reset()         { *m = QrcodeSetResponse{} }
func (m *QrcodeSetResponse) String() string { return proto.CompactTextString(m) }
func (*QrcodeSetResponse) ProtoMessage()    {}
func (*QrcodeSetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{30}
}

func (m *QrcodeSetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeSetResponse.Unmarshal(m, b)
}
func (m *QrcodeSetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeSetResponse.Marshal(b, m, deterministic)
}
func (m *QrcodeSetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeSetResponse.Merge(m, src)
}
func (m *QrcodeSetResponse) XXX_Size() int {
	return xxx_messageInfo_QrcodeSetResponse.Size(m)
}
func (m *QrcodeSetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeSetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeSetResponse proto.InternalMessageInfo

func (m *QrcodeSetResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QrcodeSetResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QrcodeSetResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//救助站列表
type SalvationRequest struct {
	// 分页参数
	PageIndex            int32    `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Id                   int32    `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationRequest) Reset()         { *m = SalvationRequest{} }
func (m *SalvationRequest) String() string { return proto.CompactTextString(m) }
func (*SalvationRequest) ProtoMessage()    {}
func (*SalvationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{31}
}

func (m *SalvationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationRequest.Unmarshal(m, b)
}
func (m *SalvationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationRequest.Marshal(b, m, deterministic)
}
func (m *SalvationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationRequest.Merge(m, src)
}
func (m *SalvationRequest) XXX_Size() int {
	return xxx_messageInfo_SalvationRequest.Size(m)
}
func (m *SalvationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationRequest proto.InternalMessageInfo

func (m *SalvationRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *SalvationRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *SalvationRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type SalvationResponse struct {
	Code    int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string           `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details []*SalvationData `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//返回总数，用于分页
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationResponse) Reset()         { *m = SalvationResponse{} }
func (m *SalvationResponse) String() string { return proto.CompactTextString(m) }
func (*SalvationResponse) ProtoMessage()    {}
func (*SalvationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{32}
}

func (m *SalvationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationResponse.Unmarshal(m, b)
}
func (m *SalvationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationResponse.Marshal(b, m, deterministic)
}
func (m *SalvationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationResponse.Merge(m, src)
}
func (m *SalvationResponse) XXX_Size() int {
	return xxx_messageInfo_SalvationResponse.Size(m)
}
func (m *SalvationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationResponse proto.InternalMessageInfo

func (m *SalvationResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SalvationResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SalvationResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *SalvationResponse) GetDetails() []*SalvationData {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *SalvationResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type SalvationData struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Weight               string   `protobuf:"bytes,3,opt,name=weight,proto3" json:"weight"`
	CurrentWeight        string   `protobuf:"bytes,4,opt,name=current_weight,json=currentWeight,proto3" json:"current_weight"`
	AddTime              string   `protobuf:"bytes,5,opt,name=add_time,json=addTime,proto3" json:"add_time"`
	FinishTime           string   `protobuf:"bytes,6,opt,name=finish_time,json=finishTime,proto3" json:"finish_time"`
	StopTime             string   `protobuf:"bytes,7,opt,name=stop_time,json=stopTime,proto3" json:"stop_time"`
	State                int32    `protobuf:"varint,8,opt,name=state,proto3" json:"state"`
	Percent              string   `protobuf:"bytes,9,opt,name=percent,proto3" json:"percent"`
	StateText            string   `protobuf:"bytes,10,opt,name=state_text,json=stateText,proto3" json:"state_text"`
	Percentage           string   `protobuf:"bytes,11,opt,name=percentage,proto3" json:"percentage"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationData) Reset()         { *m = SalvationData{} }
func (m *SalvationData) String() string { return proto.CompactTextString(m) }
func (*SalvationData) ProtoMessage()    {}
func (*SalvationData) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{33}
}

func (m *SalvationData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationData.Unmarshal(m, b)
}
func (m *SalvationData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationData.Marshal(b, m, deterministic)
}
func (m *SalvationData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationData.Merge(m, src)
}
func (m *SalvationData) XXX_Size() int {
	return xxx_messageInfo_SalvationData.Size(m)
}
func (m *SalvationData) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationData.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationData proto.InternalMessageInfo

func (m *SalvationData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SalvationData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SalvationData) GetWeight() string {
	if m != nil {
		return m.Weight
	}
	return ""
}

func (m *SalvationData) GetCurrentWeight() string {
	if m != nil {
		return m.CurrentWeight
	}
	return ""
}

func (m *SalvationData) GetAddTime() string {
	if m != nil {
		return m.AddTime
	}
	return ""
}

func (m *SalvationData) GetFinishTime() string {
	if m != nil {
		return m.FinishTime
	}
	return ""
}

func (m *SalvationData) GetStopTime() string {
	if m != nil {
		return m.StopTime
	}
	return ""
}

func (m *SalvationData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *SalvationData) GetPercent() string {
	if m != nil {
		return m.Percent
	}
	return ""
}

func (m *SalvationData) GetStateText() string {
	if m != nil {
		return m.StateText
	}
	return ""
}

func (m *SalvationData) GetPercentage() string {
	if m != nil {
		return m.Percentage
	}
	return ""
}

//救助站新增、编辑
type SalvationAddRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Weight               string   `protobuf:"bytes,3,opt,name=weight,proto3" json:"weight"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationAddRequest) Reset()         { *m = SalvationAddRequest{} }
func (m *SalvationAddRequest) String() string { return proto.CompactTextString(m) }
func (*SalvationAddRequest) ProtoMessage()    {}
func (*SalvationAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{34}
}

func (m *SalvationAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationAddRequest.Unmarshal(m, b)
}
func (m *SalvationAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationAddRequest.Marshal(b, m, deterministic)
}
func (m *SalvationAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationAddRequest.Merge(m, src)
}
func (m *SalvationAddRequest) XXX_Size() int {
	return xxx_messageInfo_SalvationAddRequest.Size(m)
}
func (m *SalvationAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationAddRequest proto.InternalMessageInfo

func (m *SalvationAddRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SalvationAddRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SalvationAddRequest) GetWeight() string {
	if m != nil {
		return m.Weight
	}
	return ""
}

type SalvationAddResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationAddResponse) Reset()         { *m = SalvationAddResponse{} }
func (m *SalvationAddResponse) String() string { return proto.CompactTextString(m) }
func (*SalvationAddResponse) ProtoMessage()    {}
func (*SalvationAddResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{35}
}

func (m *SalvationAddResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationAddResponse.Unmarshal(m, b)
}
func (m *SalvationAddResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationAddResponse.Marshal(b, m, deterministic)
}
func (m *SalvationAddResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationAddResponse.Merge(m, src)
}
func (m *SalvationAddResponse) XXX_Size() int {
	return xxx_messageInfo_SalvationAddResponse.Size(m)
}
func (m *SalvationAddResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationAddResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationAddResponse proto.InternalMessageInfo

func (m *SalvationAddResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SalvationAddResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SalvationAddResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//设置救助站停止募集
type SalvationSetRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Status               int32    `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationSetRequest) Reset()         { *m = SalvationSetRequest{} }
func (m *SalvationSetRequest) String() string { return proto.CompactTextString(m) }
func (*SalvationSetRequest) ProtoMessage()    {}
func (*SalvationSetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{36}
}

func (m *SalvationSetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationSetRequest.Unmarshal(m, b)
}
func (m *SalvationSetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationSetRequest.Marshal(b, m, deterministic)
}
func (m *SalvationSetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationSetRequest.Merge(m, src)
}
func (m *SalvationSetRequest) XXX_Size() int {
	return xxx_messageInfo_SalvationSetRequest.Size(m)
}
func (m *SalvationSetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationSetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationSetRequest proto.InternalMessageInfo

func (m *SalvationSetRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SalvationSetRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type SalvationSetResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationSetResponse) Reset()         { *m = SalvationSetResponse{} }
func (m *SalvationSetResponse) String() string { return proto.CompactTextString(m) }
func (*SalvationSetResponse) ProtoMessage()    {}
func (*SalvationSetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{37}
}

func (m *SalvationSetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationSetResponse.Unmarshal(m, b)
}
func (m *SalvationSetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationSetResponse.Marshal(b, m, deterministic)
}
func (m *SalvationSetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationSetResponse.Merge(m, src)
}
func (m *SalvationSetResponse) XXX_Size() int {
	return xxx_messageInfo_SalvationSetResponse.Size(m)
}
func (m *SalvationSetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationSetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationSetResponse proto.InternalMessageInfo

func (m *SalvationSetResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SalvationSetResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SalvationSetResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//查看收货凭证
type SalvationReceivingRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationReceivingRequest) Reset()         { *m = SalvationReceivingRequest{} }
func (m *SalvationReceivingRequest) String() string { return proto.CompactTextString(m) }
func (*SalvationReceivingRequest) ProtoMessage()    {}
func (*SalvationReceivingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{38}
}

func (m *SalvationReceivingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationReceivingRequest.Unmarshal(m, b)
}
func (m *SalvationReceivingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationReceivingRequest.Marshal(b, m, deterministic)
}
func (m *SalvationReceivingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationReceivingRequest.Merge(m, src)
}
func (m *SalvationReceivingRequest) XXX_Size() int {
	return xxx_messageInfo_SalvationReceivingRequest.Size(m)
}
func (m *SalvationReceivingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationReceivingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationReceivingRequest proto.InternalMessageInfo

func (m *SalvationReceivingRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type SalvationReceivingResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	ReceiptCerts         string   `protobuf:"bytes,4,opt,name=receipt_certs,json=receiptCerts,proto3" json:"receipt_certs"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationReceivingResponse) Reset()         { *m = SalvationReceivingResponse{} }
func (m *SalvationReceivingResponse) String() string { return proto.CompactTextString(m) }
func (*SalvationReceivingResponse) ProtoMessage()    {}
func (*SalvationReceivingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{39}
}

func (m *SalvationReceivingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationReceivingResponse.Unmarshal(m, b)
}
func (m *SalvationReceivingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationReceivingResponse.Marshal(b, m, deterministic)
}
func (m *SalvationReceivingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationReceivingResponse.Merge(m, src)
}
func (m *SalvationReceivingResponse) XXX_Size() int {
	return xxx_messageInfo_SalvationReceivingResponse.Size(m)
}
func (m *SalvationReceivingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationReceivingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationReceivingResponse proto.InternalMessageInfo

func (m *SalvationReceivingResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SalvationReceivingResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SalvationReceivingResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *SalvationReceivingResponse) GetReceiptCerts() string {
	if m != nil {
		return m.ReceiptCerts
	}
	return ""
}

//救助站上传、编辑收货凭证
type SalvationReceivingAddRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	ReceiptCerts         string   `protobuf:"bytes,2,opt,name=receipt_certs,json=receiptCerts,proto3" json:"receipt_certs"`
	Type                 string   `protobuf:"bytes,3,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationReceivingAddRequest) Reset()         { *m = SalvationReceivingAddRequest{} }
func (m *SalvationReceivingAddRequest) String() string { return proto.CompactTextString(m) }
func (*SalvationReceivingAddRequest) ProtoMessage()    {}
func (*SalvationReceivingAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{40}
}

func (m *SalvationReceivingAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationReceivingAddRequest.Unmarshal(m, b)
}
func (m *SalvationReceivingAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationReceivingAddRequest.Marshal(b, m, deterministic)
}
func (m *SalvationReceivingAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationReceivingAddRequest.Merge(m, src)
}
func (m *SalvationReceivingAddRequest) XXX_Size() int {
	return xxx_messageInfo_SalvationReceivingAddRequest.Size(m)
}
func (m *SalvationReceivingAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationReceivingAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationReceivingAddRequest proto.InternalMessageInfo

func (m *SalvationReceivingAddRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SalvationReceivingAddRequest) GetReceiptCerts() string {
	if m != nil {
		return m.ReceiptCerts
	}
	return ""
}

func (m *SalvationReceivingAddRequest) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

type SalvationReceivingAddResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	ReceiptCerts         string   `protobuf:"bytes,4,opt,name=receipt_certs,json=receiptCerts,proto3" json:"receipt_certs"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalvationReceivingAddResponse) Reset()         { *m = SalvationReceivingAddResponse{} }
func (m *SalvationReceivingAddResponse) String() string { return proto.CompactTextString(m) }
func (*SalvationReceivingAddResponse) ProtoMessage()    {}
func (*SalvationReceivingAddResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{41}
}

func (m *SalvationReceivingAddResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalvationReceivingAddResponse.Unmarshal(m, b)
}
func (m *SalvationReceivingAddResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalvationReceivingAddResponse.Marshal(b, m, deterministic)
}
func (m *SalvationReceivingAddResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalvationReceivingAddResponse.Merge(m, src)
}
func (m *SalvationReceivingAddResponse) XXX_Size() int {
	return xxx_messageInfo_SalvationReceivingAddResponse.Size(m)
}
func (m *SalvationReceivingAddResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SalvationReceivingAddResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SalvationReceivingAddResponse proto.InternalMessageInfo

func (m *SalvationReceivingAddResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SalvationReceivingAddResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SalvationReceivingAddResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *SalvationReceivingAddResponse) GetReceiptCerts() string {
	if m != nil {
		return m.ReceiptCerts
	}
	return ""
}

//设置用户捐赠限制
type DonateSetRequest struct {
	FreeDonateWeight     string   `protobuf:"bytes,1,opt,name=free_donate_weight,json=freeDonateWeight,proto3" json:"free_donate_weight"`
	DonateRate           string   `protobuf:"bytes,2,opt,name=donate_rate,json=donateRate,proto3" json:"donate_rate"`
	Type                 int32    `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	GoodsId              string   `protobuf:"bytes,4,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	ActivityStartTime    string   `protobuf:"bytes,5,opt,name=activity_start_time,json=activityStartTime,proto3" json:"activity_start_time"`
	ActivityPauseTime    string   `protobuf:"bytes,6,opt,name=activity_pause_time,json=activityPauseTime,proto3" json:"activity_pause_time"`
	ActivityEndTime      string   `protobuf:"bytes,7,opt,name=activity_end_time,json=activityEndTime,proto3" json:"activity_end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DonateSetRequest) Reset()         { *m = DonateSetRequest{} }
func (m *DonateSetRequest) String() string { return proto.CompactTextString(m) }
func (*DonateSetRequest) ProtoMessage()    {}
func (*DonateSetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{42}
}

func (m *DonateSetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DonateSetRequest.Unmarshal(m, b)
}
func (m *DonateSetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DonateSetRequest.Marshal(b, m, deterministic)
}
func (m *DonateSetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DonateSetRequest.Merge(m, src)
}
func (m *DonateSetRequest) XXX_Size() int {
	return xxx_messageInfo_DonateSetRequest.Size(m)
}
func (m *DonateSetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DonateSetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DonateSetRequest proto.InternalMessageInfo

func (m *DonateSetRequest) GetFreeDonateWeight() string {
	if m != nil {
		return m.FreeDonateWeight
	}
	return ""
}

func (m *DonateSetRequest) GetDonateRate() string {
	if m != nil {
		return m.DonateRate
	}
	return ""
}

func (m *DonateSetRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *DonateSetRequest) GetGoodsId() string {
	if m != nil {
		return m.GoodsId
	}
	return ""
}

func (m *DonateSetRequest) GetActivityStartTime() string {
	if m != nil {
		return m.ActivityStartTime
	}
	return ""
}

func (m *DonateSetRequest) GetActivityPauseTime() string {
	if m != nil {
		return m.ActivityPauseTime
	}
	return ""
}

func (m *DonateSetRequest) GetActivityEndTime() string {
	if m != nil {
		return m.ActivityEndTime
	}
	return ""
}

type DonateSetResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	FreeDonateWeight     string   `protobuf:"bytes,4,opt,name=free_donate_weight,json=freeDonateWeight,proto3" json:"free_donate_weight"`
	DonateRate           string   `protobuf:"bytes,5,opt,name=donate_rate,json=donateRate,proto3" json:"donate_rate"`
	GoodsId              string   `protobuf:"bytes,6,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	ActivityStartTime    string   `protobuf:"bytes,7,opt,name=activity_start_time,json=activityStartTime,proto3" json:"activity_start_time"`
	ActivityPauseTime    string   `protobuf:"bytes,8,opt,name=activity_pause_time,json=activityPauseTime,proto3" json:"activity_pause_time"`
	ActivityEndTime      string   `protobuf:"bytes,9,opt,name=activity_end_time,json=activityEndTime,proto3" json:"activity_end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DonateSetResponse) Reset()         { *m = DonateSetResponse{} }
func (m *DonateSetResponse) String() string { return proto.CompactTextString(m) }
func (*DonateSetResponse) ProtoMessage()    {}
func (*DonateSetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{43}
}

func (m *DonateSetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DonateSetResponse.Unmarshal(m, b)
}
func (m *DonateSetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DonateSetResponse.Marshal(b, m, deterministic)
}
func (m *DonateSetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DonateSetResponse.Merge(m, src)
}
func (m *DonateSetResponse) XXX_Size() int {
	return xxx_messageInfo_DonateSetResponse.Size(m)
}
func (m *DonateSetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DonateSetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DonateSetResponse proto.InternalMessageInfo

func (m *DonateSetResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DonateSetResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DonateSetResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *DonateSetResponse) GetFreeDonateWeight() string {
	if m != nil {
		return m.FreeDonateWeight
	}
	return ""
}

func (m *DonateSetResponse) GetDonateRate() string {
	if m != nil {
		return m.DonateRate
	}
	return ""
}

func (m *DonateSetResponse) GetGoodsId() string {
	if m != nil {
		return m.GoodsId
	}
	return ""
}

func (m *DonateSetResponse) GetActivityStartTime() string {
	if m != nil {
		return m.ActivityStartTime
	}
	return ""
}

func (m *DonateSetResponse) GetActivityPauseTime() string {
	if m != nil {
		return m.ActivityPauseTime
	}
	return ""
}

func (m *DonateSetResponse) GetActivityEndTime() string {
	if m != nil {
		return m.ActivityEndTime
	}
	return ""
}

//访问限制
type QrcodeLimitRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QrcodeLimitRequest) Reset()         { *m = QrcodeLimitRequest{} }
func (m *QrcodeLimitRequest) String() string { return proto.CompactTextString(m) }
func (*QrcodeLimitRequest) ProtoMessage()    {}
func (*QrcodeLimitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{44}
}

func (m *QrcodeLimitRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeLimitRequest.Unmarshal(m, b)
}
func (m *QrcodeLimitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeLimitRequest.Marshal(b, m, deterministic)
}
func (m *QrcodeLimitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeLimitRequest.Merge(m, src)
}
func (m *QrcodeLimitRequest) XXX_Size() int {
	return xxx_messageInfo_QrcodeLimitRequest.Size(m)
}
func (m *QrcodeLimitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeLimitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeLimitRequest proto.InternalMessageInfo

func (m *QrcodeLimitRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type QrcodeLimitResponse struct {
	Code                 int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string             `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details              []*QrcodeLimitData `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	Id                   int32              `protobuf:"varint,5,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *QrcodeLimitResponse) Reset()         { *m = QrcodeLimitResponse{} }
func (m *QrcodeLimitResponse) String() string { return proto.CompactTextString(m) }
func (*QrcodeLimitResponse) ProtoMessage()    {}
func (*QrcodeLimitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{45}
}

func (m *QrcodeLimitResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeLimitResponse.Unmarshal(m, b)
}
func (m *QrcodeLimitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeLimitResponse.Marshal(b, m, deterministic)
}
func (m *QrcodeLimitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeLimitResponse.Merge(m, src)
}
func (m *QrcodeLimitResponse) XXX_Size() int {
	return xxx_messageInfo_QrcodeLimitResponse.Size(m)
}
func (m *QrcodeLimitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeLimitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeLimitResponse proto.InternalMessageInfo

func (m *QrcodeLimitResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QrcodeLimitResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QrcodeLimitResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *QrcodeLimitResponse) GetDetails() []*QrcodeLimitData {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *QrcodeLimitResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type QrcodeLimitData struct {
	Id                     int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	DayShowChatQrcodeLimit int32    `protobuf:"varint,2,opt,name=day_show_chat_qrcode_limit,json=dayShowChatQrcodeLimit,proto3" json:"day_show_chat_qrcode_limit"`
	ShowChatQrcodeLimit    int32    `protobuf:"varint,3,opt,name=show_chat_qrcode_limit,json=showChatQrcodeLimit,proto3" json:"show_chat_qrcode_limit"`
	Type                   int32    `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	TypeText               string   `protobuf:"bytes,5,opt,name=type_text,json=typeText,proto3" json:"type_text"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *QrcodeLimitData) Reset()         { *m = QrcodeLimitData{} }
func (m *QrcodeLimitData) String() string { return proto.CompactTextString(m) }
func (*QrcodeLimitData) ProtoMessage()    {}
func (*QrcodeLimitData) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{46}
}

func (m *QrcodeLimitData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeLimitData.Unmarshal(m, b)
}
func (m *QrcodeLimitData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeLimitData.Marshal(b, m, deterministic)
}
func (m *QrcodeLimitData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeLimitData.Merge(m, src)
}
func (m *QrcodeLimitData) XXX_Size() int {
	return xxx_messageInfo_QrcodeLimitData.Size(m)
}
func (m *QrcodeLimitData) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeLimitData.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeLimitData proto.InternalMessageInfo

func (m *QrcodeLimitData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *QrcodeLimitData) GetDayShowChatQrcodeLimit() int32 {
	if m != nil {
		return m.DayShowChatQrcodeLimit
	}
	return 0
}

func (m *QrcodeLimitData) GetShowChatQrcodeLimit() int32 {
	if m != nil {
		return m.ShowChatQrcodeLimit
	}
	return 0
}

func (m *QrcodeLimitData) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *QrcodeLimitData) GetTypeText() string {
	if m != nil {
		return m.TypeText
	}
	return ""
}

//设置用户访问二维码个数限制
type QrcodeLimitEditRequest struct {
	DayShowChatQrcodeLimit int32    `protobuf:"varint,1,opt,name=day_show_chat_qrcode_limit,json=dayShowChatQrcodeLimit,proto3" json:"day_show_chat_qrcode_limit"`
	ShowChatQrcodeLimit    int32    `protobuf:"varint,2,opt,name=show_chat_qrcode_limit,json=showChatQrcodeLimit,proto3" json:"show_chat_qrcode_limit"`
	Id                     int32    `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	Status                 int32    `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *QrcodeLimitEditRequest) Reset()         { *m = QrcodeLimitEditRequest{} }
func (m *QrcodeLimitEditRequest) String() string { return proto.CompactTextString(m) }
func (*QrcodeLimitEditRequest) ProtoMessage()    {}
func (*QrcodeLimitEditRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{47}
}

func (m *QrcodeLimitEditRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeLimitEditRequest.Unmarshal(m, b)
}
func (m *QrcodeLimitEditRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeLimitEditRequest.Marshal(b, m, deterministic)
}
func (m *QrcodeLimitEditRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeLimitEditRequest.Merge(m, src)
}
func (m *QrcodeLimitEditRequest) XXX_Size() int {
	return xxx_messageInfo_QrcodeLimitEditRequest.Size(m)
}
func (m *QrcodeLimitEditRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeLimitEditRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeLimitEditRequest proto.InternalMessageInfo

func (m *QrcodeLimitEditRequest) GetDayShowChatQrcodeLimit() int32 {
	if m != nil {
		return m.DayShowChatQrcodeLimit
	}
	return 0
}

func (m *QrcodeLimitEditRequest) GetShowChatQrcodeLimit() int32 {
	if m != nil {
		return m.ShowChatQrcodeLimit
	}
	return 0
}

func (m *QrcodeLimitEditRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *QrcodeLimitEditRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type QrcodeLimitEditResponse struct {
	Code                   int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message                string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                  string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	DayShowChatQrcodeLimit int32    `protobuf:"varint,4,opt,name=day_show_chat_qrcode_limit,json=dayShowChatQrcodeLimit,proto3" json:"day_show_chat_qrcode_limit"`
	ShowChatQrcodeLimit    int32    `protobuf:"varint,5,opt,name=show_chat_qrcode_limit,json=showChatQrcodeLimit,proto3" json:"show_chat_qrcode_limit"`
	Id                     int32    `protobuf:"varint,6,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *QrcodeLimitEditResponse) Reset()         { *m = QrcodeLimitEditResponse{} }
func (m *QrcodeLimitEditResponse) String() string { return proto.CompactTextString(m) }
func (*QrcodeLimitEditResponse) ProtoMessage()    {}
func (*QrcodeLimitEditResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_946f3840559e11bb, []int{48}
}

func (m *QrcodeLimitEditResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QrcodeLimitEditResponse.Unmarshal(m, b)
}
func (m *QrcodeLimitEditResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QrcodeLimitEditResponse.Marshal(b, m, deterministic)
}
func (m *QrcodeLimitEditResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QrcodeLimitEditResponse.Merge(m, src)
}
func (m *QrcodeLimitEditResponse) XXX_Size() int {
	return xxx_messageInfo_QrcodeLimitEditResponse.Size(m)
}
func (m *QrcodeLimitEditResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QrcodeLimitEditResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QrcodeLimitEditResponse proto.InternalMessageInfo

func (m *QrcodeLimitEditResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QrcodeLimitEditResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QrcodeLimitEditResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *QrcodeLimitEditResponse) GetDayShowChatQrcodeLimit() int32 {
	if m != nil {
		return m.DayShowChatQrcodeLimit
	}
	return 0
}

func (m *QrcodeLimitEditResponse) GetShowChatQrcodeLimit() int32 {
	if m != nil {
		return m.ShowChatQrcodeLimit
	}
	return 0
}

func (m *QrcodeLimitEditResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func init() {
	proto.RegisterType((*ActivityIdsRequest)(nil), "mk.activityIdsRequest")
	proto.RegisterType((*IdRequest)(nil), "mk.IdRequest")
	proto.RegisterType((*StatusFreshActivityRequest)(nil), "mk.statusFreshActivityRequest")
	proto.RegisterType((*NewActivityRequest)(nil), "mk.newActivityRequest")
	proto.RegisterType((*MaProduct)(nil), "mk.maProduct")
	proto.RegisterType((*QuerySettingActivityRequest)(nil), "mk.querySettingActivityRequest")
	proto.RegisterType((*SettingActivityRequest)(nil), "mk.settingActivityRequest")
	proto.RegisterType((*ActivityIdsResponse)(nil), "mk.activityIdsResponse")
	proto.RegisterType((*Activity)(nil), "mk.Activity")
	proto.RegisterType((*SettingActivityResponse)(nil), "mk.settingActivityResponse")
	proto.RegisterType((*ActivitySetting)(nil), "mk.ActivitySetting")
	proto.RegisterType((*CouponActivityNewRequest)(nil), "mk.CouponActivityNewRequest")
	proto.RegisterType((*CouponListRequest)(nil), "mk.couponListRequest")
	proto.RegisterType((*CouponListResponse)(nil), "mk.couponListResponse")
	proto.RegisterType((*CouponData)(nil), "mk.CouponData")
	proto.RegisterType((*VoucherList)(nil), "mk.VoucherList")
	proto.RegisterType((*VoucherListRequest)(nil), "mk.VoucherListRequest")
	proto.RegisterType((*VoucherListResponse)(nil), "mk.VoucherListResponse")
	proto.RegisterType((*VoucherData)(nil), "mk.VoucherData")
	proto.RegisterType((*UserListRequest)(nil), "mk.userListRequest")
	proto.RegisterType((*UserListResponse)(nil), "mk.userListResponse")
	proto.RegisterType((*UserData)(nil), "mk.userData")
	proto.RegisterType((*UserRequest)(nil), "mk.userRequest")
	proto.RegisterType((*UserResponse)(nil), "mk.userResponse")
	proto.RegisterType((*QrcodeRequest)(nil), "mk.qrcodeRequest")
	proto.RegisterType((*QrcodeResponse)(nil), "mk.qrcodeResponse")
	proto.RegisterType((*QrcodeData)(nil), "mk.qrcodeData")
	proto.RegisterType((*QrcodeAddRequest)(nil), "mk.qrcodeAddRequest")
	proto.RegisterType((*QrcodeAddResponse)(nil), "mk.qrcodeAddResponse")
	proto.RegisterType((*QrcodeSetRequest)(nil), "mk.qrcodeSetRequest")
	proto.RegisterType((*QrcodeSetResponse)(nil), "mk.qrcodeSetResponse")
	proto.RegisterType((*SalvationRequest)(nil), "mk.salvationRequest")
	proto.RegisterType((*SalvationResponse)(nil), "mk.salvationResponse")
	proto.RegisterType((*SalvationData)(nil), "mk.salvationData")
	proto.RegisterType((*SalvationAddRequest)(nil), "mk.salvationAddRequest")
	proto.RegisterType((*SalvationAddResponse)(nil), "mk.salvationAddResponse")
	proto.RegisterType((*SalvationSetRequest)(nil), "mk.salvationSetRequest")
	proto.RegisterType((*SalvationSetResponse)(nil), "mk.salvationSetResponse")
	proto.RegisterType((*SalvationReceivingRequest)(nil), "mk.salvationReceivingRequest")
	proto.RegisterType((*SalvationReceivingResponse)(nil), "mk.salvationReceivingResponse")
	proto.RegisterType((*SalvationReceivingAddRequest)(nil), "mk.salvationReceivingAddRequest")
	proto.RegisterType((*SalvationReceivingAddResponse)(nil), "mk.salvationReceivingAddResponse")
	proto.RegisterType((*DonateSetRequest)(nil), "mk.donateSetRequest")
	proto.RegisterType((*DonateSetResponse)(nil), "mk.donateSetResponse")
	proto.RegisterType((*QrcodeLimitRequest)(nil), "mk.qrcodeLimitRequest")
	proto.RegisterType((*QrcodeLimitResponse)(nil), "mk.qrcodeLimitResponse")
	proto.RegisterType((*QrcodeLimitData)(nil), "mk.qrcodeLimitData")
	proto.RegisterType((*QrcodeLimitEditRequest)(nil), "mk.qrcodeLimitEditRequest")
	proto.RegisterType((*QrcodeLimitEditResponse)(nil), "mk.qrcodeLimitEditResponse")
}

func init() { proto.RegisterFile("mk/market.proto", fileDescriptor_946f3840559e11bb) }

var fileDescriptor_946f3840559e11bb = []byte{
	// 2401 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xcd, 0x8f, 0x1c, 0x47,
	0x15, 0x57, 0xcf, 0xce, 0x57, 0xbf, 0xf9, 0xd8, 0xd9, 0x5e, 0x67, 0x3d, 0xf6, 0x3a, 0xc9, 0xa6,
	0xed, 0x18, 0xe3, 0x38, 0x8e, 0x44, 0x24, 0x0e, 0x08, 0x0e, 0x61, 0xed, 0xc0, 0x88, 0xd8, 0xb2,
	0x77, 0x97, 0x44, 0x02, 0x29, 0xad, 0x72, 0x77, 0x65, 0xa6, 0xb4, 0xdb, 0xdd, 0xe3, 0xfe, 0xb0,
	0x77, 0x73, 0xe1, 0x84, 0xc4, 0x81, 0x3f, 0x80, 0x43, 0x24, 0x24, 0x20, 0xe2, 0x0a, 0x57, 0x2e,
	0x1c, 0x38, 0x73, 0xcc, 0x15, 0xb8, 0xc1, 0x1f, 0x80, 0xc4, 0x15, 0x55, 0xbd, 0x57, 0xdd, 0x35,
	0x3d, 0x1f, 0xde, 0x75, 0xc6, 0xb9, 0x4d, 0xfd, 0xfa, 0xd5, 0xab, 0x57, 0xaf, 0x7e, 0xbf, 0xf7,
	0xaa, 0x7b, 0x17, 0x36, 0xc3, 0xe3, 0xf7, 0x42, 0x96, 0x1c, 0xf3, 0xec, 0xee, 0x34, 0x89, 0xb3,
	0xd8, 0xa9, 0x85, 0xc7, 0xee, 0x17, 0x16, 0x38, 0xcc, 0xcf, 0xc4, 0x33, 0x91, 0x9d, 0x8d, 0x82,
	0xf4, 0x80, 0x3f, 0xcd, 0x79, 0x9a, 0x39, 0x7d, 0xa8, 0x89, 0x60, 0x68, 0xed, 0x59, 0xb7, 0x1a,
	0x07, 0x35, 0x11, 0x38, 0x03, 0xd8, 0x48, 0x8f, 0xf3, 0x61, 0x4d, 0x01, 0xf2, 0xa7, 0xe3, 0x40,
	0x3d, 0x62, 0x21, 0x1f, 0x6e, 0xec, 0x59, 0xb7, 0xec, 0x03, 0xf5, 0xdb, 0xb9, 0x04, 0x8d, 0x34,
	0x63, 0x19, 0x1f, 0xd6, 0x95, 0x1d, 0x0e, 0x9c, 0xd7, 0x01, 0xa6, 0x6c, 0xcc, 0x3d, 0x11, 0x05,
	0xfc, 0x74, 0xd8, 0x50, 0x8f, 0x6c, 0x89, 0x8c, 0x24, 0xe0, 0xec, 0x82, 0x1a, 0x78, 0xa9, 0xf8,
	0x9c, 0x0f, 0x9b, 0xea, 0x69, 0x5b, 0x02, 0x87, 0xe2, 0x73, 0xee, 0xee, 0x82, 0x3d, 0x0a, 0xe6,
	0x83, 0xb2, 0x65, 0x50, 0xee, 0xa7, 0x70, 0x55, 0xae, 0x90, 0xa7, 0x1f, 0x26, 0x3c, 0x9d, 0x7c,
	0x40, 0xbb, 0x58, 0xb6, 0x85, 0x22, 0xb8, 0x9a, 0x19, 0xdc, 0x55, 0x68, 0xc7, 0x53, 0x9e, 0xb0,
	0x2c, 0x4e, 0x68, 0x2b, 0xc5, 0xd8, 0xfd, 0xaa, 0x06, 0x4e, 0xc4, 0x9f, 0xbf, 0xc8, 0xf1, 0x65,
	0x68, 0x85, 0xcc, 0x53, 0xc9, 0xa8, 0x29, 0x0f, 0xcd, 0x90, 0x3d, 0x94, 0xe9, 0x70, 0xa1, 0x17,
	0x32, 0x2f, 0xcd, 0x58, 0x92, 0x79, 0x81, 0x5c, 0x19, 0x17, 0xe8, 0x84, 0xec, 0x50, 0x62, 0xf7,
	0xe4, 0xfa, 0x6f, 0x40, 0x27, 0x64, 0x1e, 0x8f, 0x02, 0xb4, 0xa8, 0x2b, 0x0b, 0x3b, 0x64, 0xf7,
	0xa3, 0x40, 0x3d, 0xbf, 0x09, 0x9b, 0xd2, 0xc7, 0x84, 0x25, 0xdc, 0xc3, 0xcd, 0x52, 0x06, 0x7b,
	0x21, 0x3b, 0x94, 0xe8, 0xa1, 0x02, 0x9d, 0x1b, 0xd0, 0x2f, 0xec, 0x32, 0x91, 0x9d, 0x60, 0x2a,
	0xed, 0x83, 0x2e, 0x99, 0x1d, 0x49, 0xcc, 0xd9, 0x83, 0x6e, 0x61, 0x25, 0xc2, 0xf1, 0xb0, 0xa5,
	0x6c, 0x80, 0x6c, 0x46, 0xe1, 0x58, 0x1e, 0x56, 0xc8, 0x3c, 0x3f, 0xe1, 0x2c, 0xe3, 0xc9, 0xb0,
	0xad, 0xc3, 0xd9, 0x47, 0xc0, 0xb9, 0xa3, 0x1e, 0x4f, 0x93, 0x38, 0xc8, 0xfd, 0x6c, 0x68, 0xef,
	0x6d, 0xdc, 0xea, 0x7c, 0xa7, 0x77, 0x37, 0x3c, 0xbe, 0x1b, 0xb2, 0x47, 0x08, 0x4a, 0x6b, 0xfa,
	0xe9, 0xbc, 0x06, 0xcd, 0x38, 0x19, 0x7b, 0x22, 0x18, 0x02, 0xe6, 0x3c, 0x4e, 0xc6, 0xa3, 0xc0,
	0xfd, 0xeb, 0x06, 0x18, 0x46, 0xd5, 0x74, 0xde, 0x82, 0x41, 0xb9, 0x84, 0x37, 0x4e, 0xe2, 0x7c,
	0x4a, 0x47, 0xd6, 0x2f, 0x26, 0xfd, 0x48, 0xa2, 0xb4, 0x67, 0x6d, 0x29, 0xf9, 0xb9, 0xa1, 0xf7,
	0x4c, 0x76, 0x87, 0xc7, 0x79, 0xc5, 0xdf, 0x34, 0x11, 0xbe, 0x4e, 0x73, 0xe9, 0xef, 0x91, 0x44,
	0x9d, 0xb7, 0x64, 0x76, 0xa4, 0x3e, 0xc8, 0xaa, 0xad, 0x8f, 0x4b, 0x62, 0x68, 0xf2, 0x36, 0xf4,
	0xa7, 0x79, 0xe2, 0x4f, 0x58, 0xca, 0xc9, 0xe8, 0x35, 0x65, 0xd4, 0xd3, 0x28, 0x9a, 0xd1, 0x69,
	0xe4, 0xbe, 0xcf, 0xd3, 0xd4, 0x8b, 0xf2, 0x90, 0x0e, 0x4d, 0x9e, 0x06, 0x82, 0x0f, 0xf3, 0xd0,
	0x79, 0x17, 0xb6, 0x43, 0xe6, 0x3d, 0x63, 0x27, 0x22, 0xf0, 0x26, 0x71, 0x9e, 0x78, 0x27, 0x22,
	0x14, 0x19, 0x69, 0x60, 0x10, 0xb2, 0x8f, 0xe5, 0x93, 0x1f, 0xc7, 0x79, 0xf2, 0x91, 0xc4, 0x89,
	0x4e, 0xa5, 0xb9, 0x3a, 0xbd, 0x86, 0x8c, 0xaf, 0x30, 0x74, 0xae, 0x43, 0x2f, 0x10, 0xa9, 0x1f,
	0xe7, 0x51, 0xe6, 0x25, 0x92, 0x50, 0x3b, 0x98, 0x11, 0x0d, 0x1e, 0x48, 0x4e, 0xbd, 0x05, 0x5d,
	0x91, 0x7a, 0xfc, 0xd4, 0xe7, 0xd3, 0x4c, 0xc4, 0xd1, 0x70, 0x1b, 0xfd, 0x88, 0xf4, 0xbe, 0x86,
	0x90, 0x06, 0xc9, 0xb1, 0x87, 0x8a, 0xb9, 0x84, 0x9a, 0x95, 0x88, 0xa4, 0x1b, 0x77, 0xf7, 0x61,
	0xf7, 0x69, 0xce, 0x93, 0xb3, 0x43, 0x9e, 0x65, 0x22, 0x1a, 0x57, 0x15, 0x42, 0xdb, 0xc7, 0x87,
	0xde, 0x31, 0x3f, 0x23, 0xd1, 0xca, 0xed, 0x23, 0xf8, 0x13, 0x7e, 0xe6, 0x7e, 0x17, 0x76, 0xd2,
	0xc5, 0xf3, 0xaf, 0x29, 0x96, 0x05, 0xec, 0xcc, 0x0b, 0xd9, 0x29, 0x51, 0xa3, 0x1d, 0xb2, 0x7b,
	0xec, 0xec, 0x01, 0x3b, 0x75, 0x7f, 0x67, 0xc1, 0xf6, 0x4c, 0xc9, 0x4a, 0xa7, 0x71, 0x94, 0x72,
	0x59, 0x91, 0xfc, 0x38, 0xe0, 0x64, 0xaf, 0x7e, 0x3b, 0x43, 0x68, 0x85, 0x3c, 0x4d, 0xd9, 0x58,
	0x6b, 0x53, 0x0f, 0x65, 0x39, 0xe0, 0x49, 0x52, 0xa8, 0x1e, 0x07, 0xce, 0x4d, 0x68, 0x05, 0x3c,
	0x63, 0xe2, 0x24, 0x1d, 0xd6, 0x15, 0xb9, 0xbb, 0x92, 0xdc, 0x45, 0x7c, 0xfa, 0xa1, 0xf3, 0x26,
	0x74, 0xb2, 0x38, 0x63, 0x27, 0x9e, 0xca, 0x2a, 0x9d, 0x2e, 0x28, 0x68, 0x5f, 0x22, 0xee, 0x1f,
	0xeb, 0xd0, 0xd6, 0xd3, 0xbe, 0xd9, 0x8a, 0x31, 0xab, 0xe0, 0x46, 0x55, 0xc1, 0xbb, 0x52, 0x7b,
	0xba, 0x94, 0x34, 0x75, 0x6a, 0x67, 0xaa, 0x08, 0xce, 0x45, 0xf7, 0x2d, 0x7d, 0x70, 0x38, 0x7f,
	0x59, 0x4d, 0x6a, 0x9f, 0xaf, 0x26, 0xd9, 0xe7, 0xa8, 0x49, 0x30, 0x57, 0x93, 0x30, 0x64, 0x7e,
	0x3a, 0x15, 0x09, 0x1f, 0x76, 0x74, 0xc8, 0xf7, 0xd5, 0xd8, 0xf9, 0x16, 0x6c, 0x16, 0x4c, 0xa6,
	0xd3, 0xe8, 0x63, 0xb5, 0x28, 0x60, 0x75, 0x22, 0xd5, 0x23, 0xdb, 0xac, 0x1e, 0x99, 0x94, 0x85,
	0x3f, 0xe1, 0xfe, 0xb1, 0x97, 0x70, 0x96, 0xc6, 0xd1, 0x70, 0x80, 0xb9, 0x57, 0xd8, 0x81, 0x82,
	0x16, 0x54, 0x9c, 0xee, 0x82, 0x8a, 0x33, 0x5b, 0x24, 0x7b, 0xab, 0x8b, 0xa4, 0xfb, 0x6b, 0x0b,
	0x2e, 0xcf, 0xe9, 0x60, 0x8d, 0x94, 0x7e, 0xb7, 0x4a, 0xe9, 0x6d, 0x93, 0xd2, 0xa4, 0xc7, 0x82,
	0xd9, 0xee, 0x7b, 0xb0, 0x59, 0x79, 0xf6, 0x02, 0x39, 0xfe, 0xaf, 0x06, 0xc3, 0xfd, 0x38, 0x9f,
	0xc6, 0x91, 0x9e, 0xf7, 0x90, 0x3f, 0x5f, 0xd6, 0x2b, 0xaf, 0x43, 0xef, 0x49, 0x9e, 0x8a, 0x48,
	0x96, 0xc5, 0xec, 0x6c, 0xaa, 0x9b, 0x71, 0x57, 0x83, 0x47, 0x67, 0x53, 0x55, 0x9f, 0x7c, 0xe5,
	0xd0, 0x4b, 0x7d, 0x1e, 0xa1, 0x08, 0x1a, 0x07, 0x1d, 0xc4, 0x0e, 0x25, 0x24, 0x0f, 0x93, 0x4c,
	0x94, 0x17, 0xbc, 0x6f, 0x00, 0x42, 0xca, 0xc7, 0x2e, 0xd8, 0x64, 0x20, 0x02, 0x12, 0x41, 0x1b,
	0x81, 0x51, 0x20, 0x25, 0x62, 0x68, 0x0c, 0x1b, 0xa5, 0x9d, 0x16, 0x0a, 0xbb, 0x02, 0xed, 0x42,
	0x5e, 0xc8, 0xff, 0x16, 0x27, 0x71, 0x0d, 0xa1, 0xe5, 0x4f, 0x58, 0x14, 0xf1, 0x13, 0xea, 0x0e,
	0x7a, 0x28, 0x7d, 0xfa, 0xa5, 0xec, 0x90, 0xe8, 0xb6, 0x6f, 0xca, 0xce, 0x2f, 0x64, 0x87, 0xdd,
	0xb0, 0xed, 0x6b, 0xd9, 0x5d, 0x53, 0x73, 0x45, 0x38, 0xf6, 0xf2, 0xe4, 0x44, 0x31, 0x5c, 0x46,
	0xcb, 0x46, 0xe1, 0xf8, 0xa7, 0xc9, 0x89, 0xac, 0x16, 0x3e, 0xf3, 0xa6, 0x2c, 0x9b, 0x10, 0xdb,
	0x9a, 0x3e, 0x7b, 0xc4, 0xb2, 0x89, 0xfb, 0x87, 0x1a, 0x6c, 0xe1, 0x9e, 0x3e, 0x12, 0x69, 0xb6,
	0x2c, 0xe5, 0x95, 0x54, 0xd5, 0xe6, 0x52, 0x75, 0x8e, 0x74, 0xcf, 0x1d, 0x5b, 0x7d, 0xc1, 0xb1,
	0x15, 0x17, 0xac, 0x86, 0x79, 0xc1, 0x52, 0xa8, 0x08, 0x75, 0x9a, 0x71, 0xa0, 0xa8, 0xaa, 0xd0,
	0x16, 0x51, 0x55, 0xa1, 0x2b, 0xb3, 0x6b, 0xdc, 0x21, 0xed, 0x95, 0x77, 0x48, 0xa8, 0xdc, 0x21,
	0x7f, 0x6f, 0x81, 0x63, 0xa6, 0x69, 0x8d, 0xda, 0xba, 0x55, 0xd5, 0x56, 0x5f, 0x6a, 0x0b, 0xd5,
	0x70, 0x8f, 0x65, 0xec, 0x02, 0x0d, 0xe3, 0xab, 0x3a, 0x40, 0x39, 0x71, 0xd1, 0xad, 0xc8, 0x67,
	0xde, 0xbc, 0x76, 0xec, 0x83, 0xbe, 0xcf, 0x7e, 0x68, 0x1e, 0xc3, 0x1d, 0x70, 0x66, 0xcc, 0xbc,
	0x8c, 0x9f, 0x66, 0x14, 0xf6, 0xc0, 0x3c, 0xb0, 0x23, 0x7e, 0x9a, 0xc9, 0x5a, 0x2e, 0x69, 0x6b,
	0x9e, 0x3f, 0x76, 0x94, 0x9e, 0xcf, 0xf6, 0x0d, 0x06, 0xdc, 0xd6, 0x54, 0x43, 0x23, 0x74, 0x8a,
	0xba, 0xda, 0x34, 0x98, 0xa2, 0x7c, 0xde, 0x80, 0x7e, 0xe9, 0x53, 0x45, 0x4a, 0x77, 0x51, 0xed,
	0x52, 0xc5, 0x29, 0x77, 0x54, 0x9a, 0xa0, 0xc3, 0x16, 0xed, 0xa8, 0xb0, 0x52, 0xfe, 0xf6, 0xa0,
	0x5b, 0xfa, 0x13, 0x01, 0x71, 0x03, 0xb4, 0xb7, 0x51, 0x20, 0xfb, 0xa6, 0x3f, 0xd3, 0x37, 0x6d,
	0xaa, 0xdd, 0xb3, 0x7d, 0xd3, 0x37, 0xfa, 0x26, 0x68, 0x85, 0xea, 0xbe, 0x49, 0x51, 0x1b, 0xbd,
	0xaf, 0x53, 0x44, 0x5d, 0xf6, 0x3e, 0x92, 0x39, 0xb1, 0xb4, 0x5b, 0xc8, 0x7c, 0x61, 0x15, 0xe8,
	0xad, 0xac, 0x02, 0xfd, 0x4a, 0x15, 0xc0, 0xb9, 0x51, 0x1e, 0x3e, 0xe1, 0x49, 0x4a, 0xfd, 0xc9,
	0xf6, 0xd9, 0x43, 0x04, 0x2a, 0x45, 0x62, 0xb0, 0xbc, 0x48, 0x6c, 0xcd, 0x14, 0x89, 0x5f, 0x40,
	0xe7, 0xe3, 0x38, 0xf7, 0x27, 0x3c, 0x91, 0xec, 0x77, 0xae, 0x81, 0x4d, 0xc3, 0x91, 0xa6, 0x57,
	0x09, 0x38, 0xb7, 0x61, 0x40, 0x83, 0x22, 0x6f, 0x54, 0x30, 0xe6, 0x70, 0xe7, 0x26, 0xf4, 0x09,
	0xa3, 0x0c, 0x52, 0xe1, 0xa8, 0xa0, 0xee, 0xbf, 0x2c, 0x70, 0x8c, 0x08, 0x74, 0x99, 0xba, 0x0a,
	0xed, 0x3c, 0xe5, 0x89, 0xba, 0x04, 0xe1, 0xed, 0xb0, 0x18, 0x4b, 0x69, 0x1a, 0xb5, 0x4a, 0xfd,
	0x2e, 0xeb, 0xc8, 0xc6, 0xc2, 0x3a, 0x52, 0x37, 0xeb, 0xc8, 0x0e, 0x34, 0xc3, 0xf8, 0x89, 0x38,
	0xe1, 0xc4, 0x50, 0x1a, 0xc9, 0x35, 0x91, 0x45, 0x22, 0x20, 0x4a, 0x16, 0xe3, 0x4a, 0x85, 0x69,
	0xad, 0xac, 0x30, 0xed, 0x4a, 0x85, 0xf9, 0xd2, 0x82, 0xed, 0x99, 0x2d, 0xae, 0xb1, 0xc4, 0x7c,
	0xbb, 0x5a, 0x62, 0x36, 0x65, 0x89, 0xa1, 0xd5, 0x2e, 0x58, 0x63, 0xfe, 0x69, 0x15, 0x64, 0x50,
	0x45, 0x66, 0x35, 0x19, 0xde, 0x00, 0x78, 0xc0, 0x25, 0xf7, 0x1e, 0x96, 0x17, 0x55, 0x03, 0x71,
	0x5c, 0xe8, 0xe2, 0xe8, 0x01, 0xe6, 0x9a, 0x5e, 0xbe, 0x4c, 0xcc, 0xb9, 0x03, 0x5b, 0xe4, 0x50,
	0x5d, 0x0e, 0x94, 0x86, 0xe8, 0xac, 0xe6, 0x1f, 0x38, 0x7b, 0x45, 0x78, 0x1f, 0x26, 0x71, 0x48,
	0x87, 0x67, 0x42, 0x32, 0x26, 0x1a, 0x1e, 0x8d, 0x02, 0xba, 0xbe, 0x1a, 0x88, 0xfb, 0x27, 0x0b,
	0x36, 0x25, 0x8d, 0x4c, 0xa6, 0xc9, 0xd7, 0x3a, 0x15, 0x93, 0x37, 0x9d, 0xc4, 0x91, 0x66, 0x5b,
	0x07, 0xb1, 0x47, 0x12, 0x52, 0x37, 0x4c, 0x34, 0x11, 0x01, 0xb1, 0xae, 0x8d, 0xc0, 0x28, 0x30,
	0xe6, 0x63, 0x7b, 0xa3, 0xfe, 0x88, 0xd8, 0xe1, 0x82, 0x4f, 0x1c, 0xf5, 0x95, 0xe4, 0x69, 0x54,
	0xc8, 0xf3, 0x5b, 0x0b, 0x06, 0x65, 0xc8, 0xaf, 0xfc, 0x5d, 0x46, 0x2e, 0x75, 0x41, 0xda, 0xfc,
	0xc7, 0x42, 0xad, 0x2a, 0xce, 0xcc, 0xa4, 0xca, 0xaa, 0xa4, 0x6a, 0x17, 0xec, 0x48, 0xf8, 0xc7,
	0xe6, 0xab, 0x4d, 0x5b, 0x02, 0x8a, 0x2f, 0xd7, 0xa1, 0x47, 0x33, 0xc3, 0x19, 0xc2, 0x84, 0x26,
	0x61, 0xde, 0x86, 0x7e, 0x10, 0x47, 0x2c, 0xe3, 0x81, 0xf7, 0x9c, 0x8b, 0xf1, 0x24, 0xd3, 0xed,
	0x88, 0xd0, 0x4f, 0x14, 0x28, 0xcf, 0xe4, 0xb3, 0x84, 0x73, 0x8f, 0x50, 0x0a, 0xba, 0x23, 0xb1,
	0x7b, 0x08, 0x95, 0xd7, 0x91, 0x66, 0xe5, 0x63, 0x94, 0xfa, 0x61, 0xf6, 0x1b, 0x5b, 0x21, 0xb2,
	0xd5, 0xb8, 0x0f, 0xa0, 0x23, 0x77, 0xaa, 0xa9, 0xb3, 0x72, 0xb3, 0x55, 0x5e, 0xd4, 0xe6, 0x78,
	0xe1, 0x7e, 0x1f, 0xba, 0xe8, 0xee, 0x65, 0x8e, 0xd5, 0xfd, 0x39, 0xf4, 0x9e, 0x26, 0xd2, 0x46,
	0x87, 0x33, 0x4b, 0x33, 0x6b, 0x25, 0xcd, 0x6a, 0xb3, 0x34, 0xa3, 0x0b, 0xc5, 0x86, 0xbe, 0x50,
	0xb8, 0xff, 0xb5, 0xa0, 0xaf, 0xbd, 0xbf, 0xf2, 0x1b, 0x11, 0x2e, 0x74, 0x31, 0xda, 0x49, 0x03,
	0x9c, 0x87, 0x74, 0xc2, 0x82, 0x0d, 0x08, 0x29, 0x42, 0x6d, 0x43, 0xe3, 0xa9, 0x3a, 0x19, 0xac,
	0xd6, 0xf5, 0xa7, 0x89, 0x79, 0xb7, 0x57, 0x93, 0xda, 0xc6, 0xdd, 0x5e, 0xce, 0x71, 0xff, 0x6d,
	0x01, 0x94, 0xd1, 0xcc, 0x5d, 0xb3, 0xf4, 0x57, 0xcd, 0x9a, 0xf1, 0x55, 0x73, 0x07, 0x9a, 0x38,
	0x83, 0x76, 0x4a, 0x23, 0xf9, 0x9a, 0xc0, 0x82, 0xc0, 0x33, 0xda, 0x4f, 0x8b, 0x05, 0xc1, 0x91,
	0x6c, 0x40, 0x6f, 0x42, 0x07, 0x99, 0x89, 0x1d, 0x19, 0x0b, 0x19, 0x20, 0x24, 0xbb, 0xb2, 0xbc,
	0xa4, 0x91, 0x41, 0x28, 0x22, 0xe1, 0x91, 0x7f, 0xdc, 0xe2, 0x00, 0x9f, 0x3c, 0x10, 0x91, 0x78,
	0x8c, 0x2b, 0x15, 0x54, 0x6e, 0x2d, 0xa7, 0x72, 0xbb, 0x4a, 0xe5, 0x18, 0x06, 0xe8, 0xf6, 0x83,
	0xa0, 0xf8, 0x82, 0xaa, 0xb7, 0x67, 0x2d, 0xdc, 0x5e, 0x6d, 0x66, 0x7b, 0x95, 0x3d, 0x6c, 0xcc,
	0xed, 0x01, 0x73, 0x57, 0x2f, 0x18, 0xf5, 0x09, 0x6c, 0x19, 0x0b, 0xae, 0x8f, 0x53, 0xae, 0xab,
	0x77, 0x72, 0xc8, 0x97, 0xbd, 0xe5, 0x94, 0x8b, 0x2b, 0x9b, 0x35, 0x2e, 0xfe, 0x29, 0x0c, 0x52,
	0x76, 0xf2, 0x8c, 0x65, 0x22, 0x8e, 0x5e, 0x85, 0x0e, 0xbf, 0xb4, 0x60, 0xcb, 0x58, 0x60, 0x8d,
	0x52, 0x7c, 0xa7, 0x2a, 0xc5, 0x2d, 0x29, 0xc5, 0x62, 0xad, 0x0b, 0x36, 0x81, 0xbf, 0xd4, 0xa0,
	0x37, 0x33, 0xf7, 0xbc, 0xda, 0xa1, 0x32, 0x4e, 0xda, 0xc1, 0x91, 0x2c, 0xf3, 0x7e, 0x9e, 0x24,
	0x3c, 0xca, 0x2a, 0x65, 0x9e, 0x50, 0x2a, 0xf3, 0xa6, 0xc4, 0x1a, 0x73, 0x12, 0xfb, 0x4c, 0x44,
	0x22, 0x9d, 0x78, 0xc6, 0xdb, 0x25, 0x20, 0xa4, 0x0c, 0x76, 0xc1, 0x4e, 0xb3, 0x78, 0xea, 0x19,
	0xaf, 0x99, 0x6d, 0x09, 0x1c, 0x09, 0xf3, 0x2f, 0x15, 0x6d, 0x53, 0x51, 0x43, 0x68, 0x4d, 0x79,
	0xe2, 0xf3, 0x28, 0xa3, 0x17, 0x08, 0x3d, 0xac, 0x68, 0x0d, 0x2a, 0x5a, 0x93, 0xd7, 0x12, 0xb2,
	0x94, 0xa7, 0x83, 0xef, 0x0d, 0x06, 0xe2, 0x3e, 0x86, 0xed, 0x22, 0x77, 0x86, 0x1c, 0xbf, 0x46,
	0x06, 0xdd, 0x9f, 0xc1, 0xa5, 0x59, 0x97, 0x6b, 0xe4, 0xfc, 0x0f, 0x8c, 0x70, 0x97, 0x6b, 0x4e,
	0x86, 0x46, 0xaf, 0x32, 0x48, 0x72, 0x1a, 0xcd, 0x84, 0xb6, 0x6e, 0x39, 0xbe, 0x03, 0x57, 0x0c,
	0xb5, 0xf8, 0x5c, 0x3c, 0x13, 0xd1, 0x78, 0x59, 0x51, 0xf8, 0xa5, 0x05, 0x57, 0x17, 0x59, 0xaf,
	0x51, 0x64, 0xd7, 0xa1, 0x97, 0x48, 0xc7, 0xd3, 0xcc, 0xf3, 0x79, 0x92, 0xa5, 0xc4, 0xe3, 0x2e,
	0x81, 0xfb, 0x12, 0x73, 0xc7, 0x70, 0x6d, 0x3e, 0x8c, 0x15, 0x3c, 0x98, 0x73, 0x5a, 0x9b, 0x77,
	0x5a, 0xbc, 0x24, 0xd1, 0x1f, 0xe0, 0xe4, 0x6f, 0xf7, 0x57, 0x16, 0xbc, 0xbe, 0x64, 0xa5, 0x6f,
	0x7a, 0xcf, 0x5f, 0xd4, 0x80, 0x1a, 0x99, 0xc1, 0xa0, 0x3b, 0xe0, 0x18, 0xd7, 0x36, 0x2d, 0x7d,
	0xec, 0x46, 0x83, 0xf2, 0xf2, 0x46, 0xea, 0x2f, 0x3b, 0x50, 0xa2, 0xef, 0x57, 0x45, 0x07, 0x52,
	0x7f, 0xc8, 0x30, 0x53, 0xa0, 0xdf, 0x13, 0xaf, 0x40, 0x7b, 0x1c, 0xc7, 0x41, 0xea, 0x51, 0x6f,
	0xb2, 0x0f, 0x5a, 0x6a, 0x3c, 0x0a, 0x9c, 0xbb, 0xe5, 0xdf, 0x0d, 0xe8, 0x5b, 0x81, 0x51, 0x58,
	0xb6, 0xf4, 0x23, 0xf5, 0x86, 0xab, 0x8a, 0x84, 0x69, 0x3f, 0x65, 0x79, 0xca, 0xcd, 0x52, 0x53,
	0xd8, 0x3f, 0x92, 0x4f, 0x94, 0xfd, 0x6d, 0x28, 0x40, 0xf5, 0x9d, 0xc1, 0xa8, 0x3c, 0x9b, 0xfa,
	0xc1, 0xfd, 0x48, 0x95, 0x2f, 0xf7, 0xef, 0x35, 0xd8, 0x32, 0xd2, 0xb3, 0xc6, 0xd3, 0x59, 0x9c,
	0xe3, 0xfa, 0xf9, 0x72, 0xdc, 0x98, 0xcb, 0xb1, 0x99, 0xcf, 0xe6, 0xb9, 0xf2, 0xd9, 0xba, 0x60,
	0x3e, 0xdb, 0x17, 0xca, 0xa7, 0xbd, 0x38, 0x9f, 0x37, 0xc0, 0xc1, 0xfe, 0xaf, 0xfe, 0x56, 0xb6,
	0xac, 0x20, 0xfc, 0xc6, 0x82, 0xed, 0x19, 0xb3, 0x57, 0xfe, 0x9d, 0xdd, 0x58, 0x6d, 0xb6, 0xe1,
	0x62, 0x68, 0x8d, 0x22, 0xb4, 0xbf, 0x59, 0xb0, 0x59, 0x31, 0x9e, 0xab, 0x0b, 0xdf, 0x83, 0xab,
	0x01, 0x3b, 0xf3, 0xd2, 0x49, 0xfc, 0xdc, 0xf3, 0x27, 0x2c, 0xa3, 0x7b, 0x23, 0xfd, 0xdd, 0x10,
	0x8b, 0xf0, 0x4e, 0xc0, 0xce, 0x0e, 0x27, 0xf1, 0xf3, 0xfd, 0x09, 0xcb, 0x1e, 0x97, 0xfe, 0x9c,
	0xf7, 0x61, 0x67, 0xc9, 0x3c, 0x54, 0xcf, 0x76, 0xba, 0x60, 0x92, 0x16, 0x58, 0xdd, 0x10, 0xd8,
	0x2e, 0xd8, 0xe5, 0x07, 0x3b, 0xfa, 0xb2, 0x9e, 0xd1, 0xa7, 0x3a, 0xf7, 0xcf, 0x16, 0xec, 0x18,
	0xbb, 0xb8, 0x1f, 0x94, 0x67, 0xb1, 0x3a, 0x78, 0xeb, 0x25, 0x83, 0xaf, 0x2d, 0x0f, 0xbe, 0x72,
	0xd3, 0x32, 0xda, 0x55, 0x7d, 0xa6, 0x5d, 0xfd, 0xc3, 0x82, 0xcb, 0x73, 0x31, 0xaf, 0x91, 0x18,
	0xab, 0x37, 0x5e, 0x7f, 0xc9, 0x8d, 0x37, 0x5e, 0xb4, 0xf1, 0xa6, 0xde, 0xf8, 0x93, 0xa6, 0xfa,
	0x77, 0x8f, 0xf7, 0xff, 0x1f, 0x00, 0x00, 0xff, 0xff, 0xd8, 0x76, 0xb5, 0x69, 0x01, 0x22, 0x00,
	0x00,
}
