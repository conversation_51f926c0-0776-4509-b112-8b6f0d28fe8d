// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/promotion_configuration.proto

package mk

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//活动全局配置模板
type PromotionConfiguration struct {
	//id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//门店id
	Shopid string `protobuf:"bytes,2,opt,name=shopid,proto3" json:"shopid"`
	//类型 1 满减活动 2 限时折扣 3 满减运费
	Promotiontype int32 `protobuf:"varint,3,opt,name=promotiontype,proto3" json:"promotiontype"`
	//类型 1 不限制 2 自定义
	Buytype int32 `protobuf:"varint,4,opt,name=buytype,proto3" json:"buytype"`
	//购买总数
	Buycount int32 `protobuf:"varint,5,opt,name=buycount,proto3" json:"buycount"`
	//状态:1:正常;2:冻结;
	Status int32 `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	//修改人id
	Modifyid string `protobuf:"bytes,7,opt,name=modifyid,proto3" json:"modifyid"`
	//修改时间
	Modifytime string `protobuf:"bytes,8,opt,name=modifytime,proto3" json:"modifytime"`
	//创建人id
	Createid string `protobuf:"bytes,9,opt,name=createid,proto3" json:"createid"`
	//创建时间
	Createtime           string   `protobuf:"bytes,10,opt,name=createtime,proto3" json:"createtime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionConfiguration) Reset()         { *m = PromotionConfiguration{} }
func (m *PromotionConfiguration) String() string { return proto.CompactTextString(m) }
func (*PromotionConfiguration) ProtoMessage()    {}
func (*PromotionConfiguration) Descriptor() ([]byte, []int) {
	return fileDescriptor_961815f9b7328f28, []int{0}
}

func (m *PromotionConfiguration) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionConfiguration.Unmarshal(m, b)
}
func (m *PromotionConfiguration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionConfiguration.Marshal(b, m, deterministic)
}
func (m *PromotionConfiguration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionConfiguration.Merge(m, src)
}
func (m *PromotionConfiguration) XXX_Size() int {
	return xxx_messageInfo_PromotionConfiguration.Size(m)
}
func (m *PromotionConfiguration) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionConfiguration.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionConfiguration proto.InternalMessageInfo

func (m *PromotionConfiguration) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromotionConfiguration) GetShopid() string {
	if m != nil {
		return m.Shopid
	}
	return ""
}

func (m *PromotionConfiguration) GetPromotiontype() int32 {
	if m != nil {
		return m.Promotiontype
	}
	return 0
}

func (m *PromotionConfiguration) GetBuytype() int32 {
	if m != nil {
		return m.Buytype
	}
	return 0
}

func (m *PromotionConfiguration) GetBuycount() int32 {
	if m != nil {
		return m.Buycount
	}
	return 0
}

func (m *PromotionConfiguration) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PromotionConfiguration) GetModifyid() string {
	if m != nil {
		return m.Modifyid
	}
	return ""
}

func (m *PromotionConfiguration) GetModifytime() string {
	if m != nil {
		return m.Modifytime
	}
	return ""
}

func (m *PromotionConfiguration) GetCreateid() string {
	if m != nil {
		return m.Createid
	}
	return ""
}

func (m *PromotionConfiguration) GetCreatetime() string {
	if m != nil {
		return m.Createtime
	}
	return ""
}

//获取活动全局配置详情 => Request
type GetPromotionConfigurationDetailRequest struct {
	//ids
	Ids string `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids"`
	//门店ids
	ShopIds string `protobuf:"bytes,2,opt,name=shopIds,proto3" json:"shopIds"`
	//类型 1 满减活动 2 限时折扣 3 满减运费
	PromotionType int32 `protobuf:"varint,3,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	//排序类型：createTimeDesc:根据创建时间倒序
	Sort string `protobuf:"bytes,4,opt,name=sort,proto3" json:"sort"`
	//状态:1:正常;2:冻结;
	Status               int32    `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPromotionConfigurationDetailRequest) Reset() {
	*m = GetPromotionConfigurationDetailRequest{}
}
func (m *GetPromotionConfigurationDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetPromotionConfigurationDetailRequest) ProtoMessage()    {}
func (*GetPromotionConfigurationDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_961815f9b7328f28, []int{1}
}

func (m *GetPromotionConfigurationDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPromotionConfigurationDetailRequest.Unmarshal(m, b)
}
func (m *GetPromotionConfigurationDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPromotionConfigurationDetailRequest.Marshal(b, m, deterministic)
}
func (m *GetPromotionConfigurationDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPromotionConfigurationDetailRequest.Merge(m, src)
}
func (m *GetPromotionConfigurationDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetPromotionConfigurationDetailRequest.Size(m)
}
func (m *GetPromotionConfigurationDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPromotionConfigurationDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPromotionConfigurationDetailRequest proto.InternalMessageInfo

func (m *GetPromotionConfigurationDetailRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

func (m *GetPromotionConfigurationDetailRequest) GetShopIds() string {
	if m != nil {
		return m.ShopIds
	}
	return ""
}

func (m *GetPromotionConfigurationDetailRequest) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *GetPromotionConfigurationDetailRequest) GetSort() string {
	if m != nil {
		return m.Sort
	}
	return ""
}

func (m *GetPromotionConfigurationDetailRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

//获取活动全局配置详情 => Response
type GetPromotionConfigurationDetailResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//响应信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//结果
	PromotionConfigurationList []*PromotionConfiguration `protobuf:"bytes,4,rep,name=promotionConfiguration_list,json=promotionConfigurationList,proto3" json:"promotionConfiguration_list"`
	XXX_NoUnkeyedLiteral       struct{}                  `json:"-"`
	XXX_unrecognized           []byte                    `json:"-"`
	XXX_sizecache              int32                     `json:"-"`
}

func (m *GetPromotionConfigurationDetailResponse) Reset() {
	*m = GetPromotionConfigurationDetailResponse{}
}
func (m *GetPromotionConfigurationDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetPromotionConfigurationDetailResponse) ProtoMessage()    {}
func (*GetPromotionConfigurationDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_961815f9b7328f28, []int{2}
}

func (m *GetPromotionConfigurationDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPromotionConfigurationDetailResponse.Unmarshal(m, b)
}
func (m *GetPromotionConfigurationDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPromotionConfigurationDetailResponse.Marshal(b, m, deterministic)
}
func (m *GetPromotionConfigurationDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPromotionConfigurationDetailResponse.Merge(m, src)
}
func (m *GetPromotionConfigurationDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetPromotionConfigurationDetailResponse.Size(m)
}
func (m *GetPromotionConfigurationDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPromotionConfigurationDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPromotionConfigurationDetailResponse proto.InternalMessageInfo

func (m *GetPromotionConfigurationDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPromotionConfigurationDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPromotionConfigurationDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetPromotionConfigurationDetailResponse) GetPromotionConfigurationList() []*PromotionConfiguration {
	if m != nil {
		return m.PromotionConfigurationList
	}
	return nil
}

//创建活动全局配置 => Request
type PromotionConfigurationCreateInfoRequest struct {
	//类型 1 满减活动 2 限时折扣 3 满减运费
	PromotionType int32 `protobuf:"varint,1,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	//类型 1 不限制 2 自定义
	BuyType int32 `protobuf:"varint,2,opt,name=buy_type,json=buyType,proto3" json:"buy_type"`
	//购买总数
	BuyCount int32 `protobuf:"varint,3,opt,name=buy_count,json=buyCount,proto3" json:"buy_count"`
	//修改人id
	ModifyId string `protobuf:"bytes,4,opt,name=modify_id,json=modifyId,proto3" json:"modify_id"`
	//创建人id
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	//门店id
	ShopIds              string   `protobuf:"bytes,6,opt,name=shopIds,proto3" json:"shopIds"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionConfigurationCreateInfoRequest) Reset() {
	*m = PromotionConfigurationCreateInfoRequest{}
}
func (m *PromotionConfigurationCreateInfoRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionConfigurationCreateInfoRequest) ProtoMessage()    {}
func (*PromotionConfigurationCreateInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_961815f9b7328f28, []int{3}
}

func (m *PromotionConfigurationCreateInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionConfigurationCreateInfoRequest.Unmarshal(m, b)
}
func (m *PromotionConfigurationCreateInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionConfigurationCreateInfoRequest.Marshal(b, m, deterministic)
}
func (m *PromotionConfigurationCreateInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionConfigurationCreateInfoRequest.Merge(m, src)
}
func (m *PromotionConfigurationCreateInfoRequest) XXX_Size() int {
	return xxx_messageInfo_PromotionConfigurationCreateInfoRequest.Size(m)
}
func (m *PromotionConfigurationCreateInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionConfigurationCreateInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionConfigurationCreateInfoRequest proto.InternalMessageInfo

func (m *PromotionConfigurationCreateInfoRequest) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *PromotionConfigurationCreateInfoRequest) GetBuyType() int32 {
	if m != nil {
		return m.BuyType
	}
	return 0
}

func (m *PromotionConfigurationCreateInfoRequest) GetBuyCount() int32 {
	if m != nil {
		return m.BuyCount
	}
	return 0
}

func (m *PromotionConfigurationCreateInfoRequest) GetModifyId() string {
	if m != nil {
		return m.ModifyId
	}
	return ""
}

func (m *PromotionConfigurationCreateInfoRequest) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *PromotionConfigurationCreateInfoRequest) GetShopIds() string {
	if m != nil {
		return m.ShopIds
	}
	return ""
}

//创建活动全局配置 => Response
type PromotionConfigurationCreateInfoResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//响应信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionConfigurationCreateInfoResponse) Reset() {
	*m = PromotionConfigurationCreateInfoResponse{}
}
func (m *PromotionConfigurationCreateInfoResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionConfigurationCreateInfoResponse) ProtoMessage()    {}
func (*PromotionConfigurationCreateInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_961815f9b7328f28, []int{4}
}

func (m *PromotionConfigurationCreateInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionConfigurationCreateInfoResponse.Unmarshal(m, b)
}
func (m *PromotionConfigurationCreateInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionConfigurationCreateInfoResponse.Marshal(b, m, deterministic)
}
func (m *PromotionConfigurationCreateInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionConfigurationCreateInfoResponse.Merge(m, src)
}
func (m *PromotionConfigurationCreateInfoResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionConfigurationCreateInfoResponse.Size(m)
}
func (m *PromotionConfigurationCreateInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionConfigurationCreateInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionConfigurationCreateInfoResponse proto.InternalMessageInfo

func (m *PromotionConfigurationCreateInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PromotionConfigurationCreateInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionConfigurationCreateInfoResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//更新活动全局配置信息 => Request
type PromotionConfigurationUpdateInfoRequest struct {
	//id
	Ids string `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids"`
	//门店id
	ShopId string `protobuf:"bytes,2,opt,name=shopId,proto3" json:"shopId"`
	//类型 1 满减活动 2 限时折扣 3 满减运费
	PromotionType int32 `protobuf:"varint,3,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	//类型 1 不限制 2 自定义
	BuyType int32 `protobuf:"varint,4,opt,name=buy_type,json=buyType,proto3" json:"buy_type"`
	//购买总数
	BuyCount int32 `protobuf:"varint,5,opt,name=buy_count,json=buyCount,proto3" json:"buy_count"`
	//修改人id
	ModifyId             string   `protobuf:"bytes,6,opt,name=modify_id,json=modifyId,proto3" json:"modify_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionConfigurationUpdateInfoRequest) Reset() {
	*m = PromotionConfigurationUpdateInfoRequest{}
}
func (m *PromotionConfigurationUpdateInfoRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionConfigurationUpdateInfoRequest) ProtoMessage()    {}
func (*PromotionConfigurationUpdateInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_961815f9b7328f28, []int{5}
}

func (m *PromotionConfigurationUpdateInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionConfigurationUpdateInfoRequest.Unmarshal(m, b)
}
func (m *PromotionConfigurationUpdateInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionConfigurationUpdateInfoRequest.Marshal(b, m, deterministic)
}
func (m *PromotionConfigurationUpdateInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionConfigurationUpdateInfoRequest.Merge(m, src)
}
func (m *PromotionConfigurationUpdateInfoRequest) XXX_Size() int {
	return xxx_messageInfo_PromotionConfigurationUpdateInfoRequest.Size(m)
}
func (m *PromotionConfigurationUpdateInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionConfigurationUpdateInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionConfigurationUpdateInfoRequest proto.InternalMessageInfo

func (m *PromotionConfigurationUpdateInfoRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

func (m *PromotionConfigurationUpdateInfoRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *PromotionConfigurationUpdateInfoRequest) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *PromotionConfigurationUpdateInfoRequest) GetBuyType() int32 {
	if m != nil {
		return m.BuyType
	}
	return 0
}

func (m *PromotionConfigurationUpdateInfoRequest) GetBuyCount() int32 {
	if m != nil {
		return m.BuyCount
	}
	return 0
}

func (m *PromotionConfigurationUpdateInfoRequest) GetModifyId() string {
	if m != nil {
		return m.ModifyId
	}
	return ""
}

//更新活动全局配置信息 => Response
type PromotionConfigurationUpdateInfoResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//响应信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionConfigurationUpdateInfoResponse) Reset() {
	*m = PromotionConfigurationUpdateInfoResponse{}
}
func (m *PromotionConfigurationUpdateInfoResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionConfigurationUpdateInfoResponse) ProtoMessage()    {}
func (*PromotionConfigurationUpdateInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_961815f9b7328f28, []int{6}
}

func (m *PromotionConfigurationUpdateInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionConfigurationUpdateInfoResponse.Unmarshal(m, b)
}
func (m *PromotionConfigurationUpdateInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionConfigurationUpdateInfoResponse.Marshal(b, m, deterministic)
}
func (m *PromotionConfigurationUpdateInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionConfigurationUpdateInfoResponse.Merge(m, src)
}
func (m *PromotionConfigurationUpdateInfoResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionConfigurationUpdateInfoResponse.Size(m)
}
func (m *PromotionConfigurationUpdateInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionConfigurationUpdateInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionConfigurationUpdateInfoResponse proto.InternalMessageInfo

func (m *PromotionConfigurationUpdateInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PromotionConfigurationUpdateInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionConfigurationUpdateInfoResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//replace活动全局配置信息 => Request
type PromotionConfigurationReplaceInfoRequest struct {
	//门店ids
	ShopIds string `protobuf:"bytes,1,opt,name=shopIds,proto3" json:"shopIds"`
	//类型 1 满减活动 2 限时折扣 3 满减运费
	PromotionType int32 `protobuf:"varint,2,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	//类型 1 不限制 2 自定义
	BuyType int32 `protobuf:"varint,3,opt,name=buy_type,json=buyType,proto3" json:"buy_type"`
	//购买总数
	BuyCount int32 `protobuf:"varint,4,opt,name=buy_count,json=buyCount,proto3" json:"buy_count"`
	//修改人id
	ModifyId             string   `protobuf:"bytes,5,opt,name=modify_id,json=modifyId,proto3" json:"modify_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionConfigurationReplaceInfoRequest) Reset() {
	*m = PromotionConfigurationReplaceInfoRequest{}
}
func (m *PromotionConfigurationReplaceInfoRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionConfigurationReplaceInfoRequest) ProtoMessage()    {}
func (*PromotionConfigurationReplaceInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_961815f9b7328f28, []int{7}
}

func (m *PromotionConfigurationReplaceInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionConfigurationReplaceInfoRequest.Unmarshal(m, b)
}
func (m *PromotionConfigurationReplaceInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionConfigurationReplaceInfoRequest.Marshal(b, m, deterministic)
}
func (m *PromotionConfigurationReplaceInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionConfigurationReplaceInfoRequest.Merge(m, src)
}
func (m *PromotionConfigurationReplaceInfoRequest) XXX_Size() int {
	return xxx_messageInfo_PromotionConfigurationReplaceInfoRequest.Size(m)
}
func (m *PromotionConfigurationReplaceInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionConfigurationReplaceInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionConfigurationReplaceInfoRequest proto.InternalMessageInfo

func (m *PromotionConfigurationReplaceInfoRequest) GetShopIds() string {
	if m != nil {
		return m.ShopIds
	}
	return ""
}

func (m *PromotionConfigurationReplaceInfoRequest) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *PromotionConfigurationReplaceInfoRequest) GetBuyType() int32 {
	if m != nil {
		return m.BuyType
	}
	return 0
}

func (m *PromotionConfigurationReplaceInfoRequest) GetBuyCount() int32 {
	if m != nil {
		return m.BuyCount
	}
	return 0
}

func (m *PromotionConfigurationReplaceInfoRequest) GetModifyId() string {
	if m != nil {
		return m.ModifyId
	}
	return ""
}

//replace活动全局配置信息 => Response
type PromotionConfigurationReplaceInfoResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//响应信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionConfigurationReplaceInfoResponse) Reset() {
	*m = PromotionConfigurationReplaceInfoResponse{}
}
func (m *PromotionConfigurationReplaceInfoResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionConfigurationReplaceInfoResponse) ProtoMessage()    {}
func (*PromotionConfigurationReplaceInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_961815f9b7328f28, []int{8}
}

func (m *PromotionConfigurationReplaceInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionConfigurationReplaceInfoResponse.Unmarshal(m, b)
}
func (m *PromotionConfigurationReplaceInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionConfigurationReplaceInfoResponse.Marshal(b, m, deterministic)
}
func (m *PromotionConfigurationReplaceInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionConfigurationReplaceInfoResponse.Merge(m, src)
}
func (m *PromotionConfigurationReplaceInfoResponse) XXX_Size() int {
	return xxx_messageInfo_PromotionConfigurationReplaceInfoResponse.Size(m)
}
func (m *PromotionConfigurationReplaceInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionConfigurationReplaceInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionConfigurationReplaceInfoResponse proto.InternalMessageInfo

func (m *PromotionConfigurationReplaceInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PromotionConfigurationReplaceInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PromotionConfigurationReplaceInfoResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func init() {
	proto.RegisterType((*PromotionConfiguration)(nil), "mk.PromotionConfiguration")
	proto.RegisterType((*GetPromotionConfigurationDetailRequest)(nil), "mk.GetPromotionConfigurationDetailRequest")
	proto.RegisterType((*GetPromotionConfigurationDetailResponse)(nil), "mk.GetPromotionConfigurationDetailResponse")
	proto.RegisterType((*PromotionConfigurationCreateInfoRequest)(nil), "mk.PromotionConfigurationCreateInfoRequest")
	proto.RegisterType((*PromotionConfigurationCreateInfoResponse)(nil), "mk.PromotionConfigurationCreateInfoResponse")
	proto.RegisterType((*PromotionConfigurationUpdateInfoRequest)(nil), "mk.PromotionConfigurationUpdateInfoRequest")
	proto.RegisterType((*PromotionConfigurationUpdateInfoResponse)(nil), "mk.PromotionConfigurationUpdateInfoResponse")
	proto.RegisterType((*PromotionConfigurationReplaceInfoRequest)(nil), "mk.PromotionConfigurationReplaceInfoRequest")
	proto.RegisterType((*PromotionConfigurationReplaceInfoResponse)(nil), "mk.PromotionConfigurationReplaceInfoResponse")
}

func init() { proto.RegisterFile("mk/promotion_configuration.proto", fileDescriptor_961815f9b7328f28) }

var fileDescriptor_961815f9b7328f28 = []byte{
	// 532 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x95, 0xcd, 0x8e, 0xd3, 0x30,
	0x10, 0xc7, 0x95, 0xa4, 0xc9, 0x6e, 0x06, 0xb1, 0x42, 0x16, 0x5a, 0x85, 0x56, 0x42, 0x55, 0x04,
	0x6c, 0xb9, 0x14, 0x09, 0x1e, 0xa1, 0x48, 0x28, 0x12, 0x07, 0x14, 0xc1, 0x89, 0x43, 0x95, 0xc6,
	0xee, 0x62, 0xb5, 0x89, 0x43, 0xec, 0x1c, 0xfa, 0x44, 0x5c, 0x78, 0x08, 0xce, 0x5c, 0x78, 0x01,
	0x1e, 0x66, 0xe5, 0xc9, 0xc7, 0x3a, 0xbb, 0xe9, 0xc7, 0xa1, 0xb7, 0xf9, 0x7b, 0x3c, 0xf5, 0xf8,
	0x37, 0xff, 0x3a, 0x30, 0xcd, 0x36, 0xef, 0x8a, 0x52, 0x64, 0x42, 0x71, 0x91, 0x2f, 0x53, 0x91,
	0xaf, 0xf9, 0x6d, 0x55, 0x26, 0x5a, 0xcd, 0x8b, 0x52, 0x28, 0x41, 0xec, 0x6c, 0x13, 0xfe, 0xb6,
	0xe1, 0xfa, 0x4b, 0xbb, 0x6b, 0x61, 0x6e, 0x22, 0x57, 0x60, 0x73, 0x1a, 0x58, 0x53, 0x6b, 0xe6,
	0xc6, 0x36, 0xa7, 0xe4, 0x1a, 0x3c, 0xf9, 0x43, 0x14, 0x9c, 0x06, 0xf6, 0xd4, 0x9a, 0xf9, 0x71,
	0xa3, 0xc8, 0x2b, 0x78, 0xda, 0x9d, 0xa3, 0x76, 0x05, 0x0b, 0x1c, 0x2c, 0xe9, 0x2f, 0x92, 0x00,
	0x2e, 0x56, 0xd5, 0x0e, 0xf3, 0x23, 0xcc, 0xb7, 0x92, 0x8c, 0xe1, 0x72, 0x55, 0xed, 0x52, 0x51,
	0xe5, 0x2a, 0x70, 0x31, 0xd5, 0x69, 0x3c, 0x53, 0x25, 0xaa, 0x92, 0x81, 0x87, 0x99, 0x46, 0xe9,
	0x9a, 0x4c, 0x50, 0xbe, 0xde, 0x71, 0x1a, 0x5c, 0x60, 0x37, 0x9d, 0x26, 0x2f, 0x01, 0xea, 0x58,
	0xf1, 0x8c, 0x05, 0x97, 0x98, 0x35, 0x56, 0x74, 0x6d, 0x5a, 0xb2, 0x44, 0x31, 0x4e, 0x03, 0xbf,
	0xae, 0x6d, 0xb5, 0xae, 0xad, 0x63, 0xac, 0x85, 0xba, 0xf6, 0x7e, 0x25, 0xfc, 0x65, 0xc1, 0x9b,
	0x4f, 0x4c, 0x0d, 0x13, 0xfb, 0xc8, 0x54, 0xc2, 0xb7, 0x31, 0xfb, 0x59, 0x31, 0xa9, 0xc8, 0x33,
	0x70, 0x38, 0x95, 0xc8, 0xcf, 0x8f, 0x75, 0xa8, 0x11, 0x68, 0x64, 0x11, 0x95, 0x0d, 0xc1, 0x56,
	0x92, 0xd7, 0x70, 0x75, 0x3f, 0xaa, 0x41, 0x86, 0x5f, 0x35, 0x29, 0x02, 0x23, 0x29, 0x4a, 0x85,
	0x00, 0xfd, 0x18, 0x63, 0x83, 0x90, 0x6b, 0x12, 0x0a, 0xff, 0x5a, 0x70, 0x73, 0xb4, 0x53, 0x59,
	0x88, 0x5c, 0xe2, 0xef, 0xa6, 0x82, 0xb2, 0x66, 0xd6, 0x18, 0xeb, 0x66, 0x33, 0x26, 0x65, 0x72,
	0xcb, 0xda, 0x66, 0x1b, 0x49, 0x9e, 0x83, 0xcb, 0xca, 0x52, 0x94, 0xd8, 0xa3, 0x1f, 0xd7, 0x82,
	0x7c, 0x87, 0x49, 0x31, 0x78, 0xd6, 0x72, 0xcb, 0xa5, 0x6e, 0xd9, 0x99, 0x3d, 0x79, 0x3f, 0x9e,
	0x67, 0x9b, 0xf9, 0x70, 0x4b, 0xf1, 0x78, 0xb8, 0xfc, 0x33, 0x97, 0x2a, 0xfc, 0x6f, 0xc1, 0xcd,
	0x70, 0xd9, 0x02, 0x67, 0x13, 0xe5, 0x6b, 0xd1, 0x72, 0x7f, 0xcc, 0xd2, 0x1a, 0x62, 0xf9, 0x02,
	0x5d, 0x57, 0x6f, 0xb0, 0x3b, 0x43, 0x62, 0x6a, 0x02, 0xbe, 0x4e, 0xd5, 0x8e, 0x74, 0x3a, 0x47,
	0x2e, 0xd0, 0x91, 0x13, 0xf0, 0x6b, 0x2f, 0x2d, 0x39, 0x6d, 0x06, 0xd1, 0x58, 0x2f, 0xa2, 0x3a,
	0x59, 0x9b, 0x45, 0x27, 0x5d, 0xd3, 0x5b, 0x11, 0x35, 0xc7, 0xef, 0xf5, 0xc6, 0x1f, 0xe6, 0x30,
	0x3b, 0x7e, 0xbb, 0xf3, 0xcd, 0x2a, 0xfc, 0xb7, 0x17, 0xe7, 0xb7, 0x82, 0x3e, 0xc0, 0xf9, 0xd8,
	0xc6, 0xcd, 0x3b, 0x10, 0xf5, 0xde, 0x81, 0x88, 0x9e, 0x6a, 0x62, 0x13, 0xfc, 0xe8, 0x00, 0x78,
	0xf7, 0x10, 0x78, 0xaf, 0x0f, 0x7e, 0x3f, 0x41, 0xf3, 0x42, 0x67, 0x24, 0xf8, 0xc7, 0xda, 0x77,
	0x60, 0xcc, 0x8a, 0x6d, 0x92, 0xf6, 0x10, 0x1a, 0x83, 0xb7, 0x8e, 0xfd, 0xef, 0xed, 0x63, 0xc8,
	0x9c, 0x03, 0xc8, 0x46, 0x87, 0x90, 0xb9, 0x0f, 0x90, 0x09, 0x78, 0x7b, 0xc2, 0x0d, 0xce, 0xc7,
	0x6c, 0xe5, 0xe1, 0x57, 0xe7, 0xc3, 0x5d, 0x00, 0x00, 0x00, 0xff, 0xff, 0xcd, 0x9c, 0x37, 0x2a,
	0x99, 0x06, 0x00, 0x00,
}
