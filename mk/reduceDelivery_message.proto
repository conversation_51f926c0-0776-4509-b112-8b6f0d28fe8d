syntax = "proto3";

package mk;


import "mk/model.proto";


/////////////////////////////////////////////////  Dto  ///////////////////////////////////////////////////////////////////////////////
// 查询满减运费数据Dto
message reduceDeliveryShopDto {
    // 关联关系唯一Id
    int32 reduceDeliveryShopId=13;
    // 店铺Id
    string shopId=1;
    // 店铺名称
    string shopName=2;
    // 促销活动名称
    string title=3;
    // 活动详情
    string summary=4;
    // 活动时间
    string dateRange=5;
    // 周期循环
    string weekDay=6;
    // 生效时段
    string timeRange=7;
    // 状态
    promotionState state=12;
}

// 根据条件查询满减活动与店铺关联关系Dto
message reachReduceDeliveryPromotionShopDto {
     // 促销活动
     promotionListDto promotionDto=1;
     // 促销活动和店铺关联关系
     promotionShopDto promotionShopDto=2;
     // 活动时间
     repeated PromotionTime timeRanges=3;
     //满减运费
     repeated promotionReduceDeliveryDto promotionReduceDelivery=4;
}

/////////////////////////////////////////////////  Request  ///////////////////////////////////////////////////////////////////////////////

// 新增
message reduceDeliveryAddRequest {
     //用户Id，即userno
     string userId=1;
     //用户Id，即登录人姓名
     string userName=2;
    // 活动基本信息
    promotionDto promotion=3;
    // 时间
    repeated PromotionTime timeRanges=4;
    // 应用店铺
    repeated promotionShopDto shops=5;
    // 满减配置
    repeated promotionReduceDeliveryDto reduceDeliveries=6;
}

// 修改
message reduceDeliveryUpdateRequest {
     //用户Id，即userno
     string userId=1;
     //用户Id，即登录人姓名
     string userName=2;
    // 促销活动Id
    int32 promotionId=3;
    // 活动基本信息
    promotionDto promotion=4;
   // 时间区间
   repeated PromotionTime timeRanges=5;
   // 应用店铺
   repeated promotionShopDto shops=6;
   // 满减配置
   repeated promotionReduceDeliveryDto reduceDeliveries=7;
}

// 查询满减运费数据请求
message reduceDeliveryShopRequest {
     // 页索引
     int32 pageIndex=1;
     // 页大小
     int32 pageSize=2;
     // 店铺列表，逗号分割
     string shopIds=3;
     // 当前登录用户Id
     string userId =4;
     // 0 所有 1 进行中 2 待生效 3 已结束 4 冻结中
     promotionState state=5;
     // 开始日期
     string beginDate=6;
     // 截止日期日期
     string endDate=7;
     // 多个活动id
     string Ids = 8;
}

/////////////////////////////////////////////////  Response  ///////////////////////////////////////////////////////////////////////////////
// 查询满减运费数据请求
message reduceDeliveryShopResponse {
     // 响应代码 0 成功 非 0 失败查看 message
     code code = 1;
     // 不成功的错误信息
     string message = 2;
     // 总条数
     int32 total=3;
     // 当前页数据
     repeated reachReduceDeliveryPromotionShopDto data=4;
}


// 限时促销 数据响应
message reduceDeliveryByIdResponse {
     // 响应代码 0 成功 非 0 失败查看 message
     code code = 1;
     // 不成功的错误信息
     string message=2;
     int32 promotionId =3;
     // 活动基本信息
     promotionDto promotion=4;
     // 时间限制
     repeated PromotionTime timeRanges=6;
     // 应用店铺
     repeated promotionShopDto shops=7;
     // 满减优惠
     repeated promotionReduceDeliveryDto reduceDeliveries=8;
 }
