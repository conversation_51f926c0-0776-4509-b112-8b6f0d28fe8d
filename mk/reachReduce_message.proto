syntax = "proto3";

package mk;


import "mk/model.proto";


//////////////////////////////////////////////// Dto    /////////////////////////////////////////////////////////////////////////////////
// 根据条件查询满减活动与店铺关联关系Dto
message reachReducePromotionShopDto {
     // 促销活动
     promotionListDto promotionDto=1;
     // 促销活动和店铺关联关系
     promotionShopDto promotionShopDto=2;
     // 时间区间
     repeated PromotionTime timeRanges=3;
     //促销活动
     repeated promotionReduceDto promotionReduce=4;
     // 创建日期
     string createTime=5;
}


/////////////////////////////////////////////////  Request  ///////////////////////////////////////////////////////////////////////////////
// 新增
message reachReduceAddRequest {
    //用户Id，即userno
   string userId=1;
   //用户Id，即登录人姓名
   string userName=2;
    // 活动基本信息
    promotionDto promotion=3;
    // 时间区间
    repeated PromotionTime timeRanges=5;
    // 应用店铺
    repeated promotionShopDto promotionShop=6;
    // 排除的商品skuid
    repeated int32 excludeSkuIds=7;
    // 满减优惠
    repeated promotionReduceDto promotionReduce=8;
}

// 更新
message reachReduceUpdateRequest {
     //用户Id，即userno
     string userId=1;
     //用户Id，即登录人姓名
     string userName=2;
     // 需要更新活动ID
     int32 promotionId=3;
     // 活动基本信息
     promotionDto promotion=4;
    // 时间区间
    repeated PromotionTime timeRanges=5;
     // 删除相关商品
     repeated int32 deletePromotionProductId=6;
     // 添加相关商品
     repeated promotionProductDto addPromotionProduct=7;
     // 满减优惠
     repeated promotionReduceDto promotionReduce=8;     
}

// 根据条件查询满减活动与店铺关联关系请求
message reachReducePromotionShopQuery {
    // 店铺列表，逗号分割
    string shopIds=1;
    // 当前登录用户Id
    string userId =2;
    // 用户名称
    string userName=11;
    // 开始日期
    string beginDateStart=3;
    string beginDateEnd=4;
    // 截止日期
    string endDateStart=5;
    string endDateEnd=6;
    // 0 所有 1 进行中 2 待生效 3 已结束 4 冻结中
    promotionState state=7;
    // 指定活动id
    repeated int32 promotion_ids = 10;
    // 页索引
    int32 pageIndex=8;
    // 页大小
    int32 pageSize=9;
}

// 根据Id 删除活动与店铺关联关系
message deletePromotonShopByIds {
    repeated int32 promotionShopId = 1;
    //当前登录用户
    string userId=2;
    // 当前登录用户名
    string userName=3;
}

message reachReduceProductQueryRequest {
    int32 promotionId=1;
    // spu
    string productSpuId=2;
    // sku
    string productSkuId=3;
    // 商品名称
    string productName=4;
    // 页索引
    int32 pageIndex=8;
    // 页大小
    int32 pageSize=9;
}

/////////////////////////////////////////////////  Response  ///////////////////////////////////////////////////////////////////////////////
// 根据条件查询满减活动与店铺关联关系响应
message reachReducePromotionShopResponse {
    // 响应代码 0 成功 非 0 失败查看 message
    code code = 1;
    // 不成功的错误信息
    string message = 2;
    // 总条数
    int32 total=3;
    // 当前页数据
    repeated reachReducePromotionShopDto data=4;
}


//根据Id查询活动信息
message reachReduceByIdResponse{
    // 响应代码 0 成功 非 0 失败查看 message
    code code = 1;
    // 不成功的错误信息
    string message=2;    

   int32 promotionId =3;
   // 活动基本信息
   promotionDto promotion=4;
   // 时间区间
   repeated PromotionTime timeRanges=6;
   // 应用店铺
   repeated promotionShopDto promotionShop=7;
   // 相关商品
   repeated promotionProductDto promotionProduct=8;
   // 满减优惠
   repeated promotionReduceDto promotionReduce=9;
}

// 根据门店Id查询满减信息
message reachReduceQueryByShopIdResponse {
   // 响应代码 0 成功 非 0 失败查看 message
   code code = 1;
   // 不成功的错误信息
   string message=2;   
    repeated promotionReduceDto data=3;
}

message reachReduceProductQueryResponse {
    // 响应代码 0 成功 非 0 失败查看 message
    code code = 1;
    // 不成功的错误信息
    string message=2;   
    // 总记录条数
    int32 total=3;
    // 当前页数据
    repeated promotionProductDto data=8;
}

