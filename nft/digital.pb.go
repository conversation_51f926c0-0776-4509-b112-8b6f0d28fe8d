// Code generated by protoc-gen-go. DO NOT EDIT.
// source: nft/digital.proto

package nft

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 用户实名注册入参
type RealRegisterRequest struct {
	PersonName           string   `protobuf:"bytes,1,opt,name=person_name,json=personName,proto3" json:"person_name"`
	Mobile               string   `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	IdCard               string   `protobuf:"bytes,3,opt,name=id_card,json=idCard,proto3" json:"id_card"`
	Openid               string   `protobuf:"bytes,4,opt,name=openid,proto3" json:"openid"`
	ScrmUserId           string   `protobuf:"bytes,5,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	MiniOpenid           string   `protobuf:"bytes,6,opt,name=mini_openid,json=miniOpenid,proto3" json:"mini_openid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RealRegisterRequest) Reset()         { *m = RealRegisterRequest{} }
func (m *RealRegisterRequest) String() string { return proto.CompactTextString(m) }
func (*RealRegisterRequest) ProtoMessage()    {}
func (*RealRegisterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{0}
}

func (m *RealRegisterRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RealRegisterRequest.Unmarshal(m, b)
}
func (m *RealRegisterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RealRegisterRequest.Marshal(b, m, deterministic)
}
func (m *RealRegisterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RealRegisterRequest.Merge(m, src)
}
func (m *RealRegisterRequest) XXX_Size() int {
	return xxx_messageInfo_RealRegisterRequest.Size(m)
}
func (m *RealRegisterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RealRegisterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RealRegisterRequest proto.InternalMessageInfo

func (m *RealRegisterRequest) GetPersonName() string {
	if m != nil {
		return m.PersonName
	}
	return ""
}

func (m *RealRegisterRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *RealRegisterRequest) GetIdCard() string {
	if m != nil {
		return m.IdCard
	}
	return ""
}

func (m *RealRegisterRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *RealRegisterRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *RealRegisterRequest) GetMiniOpenid() string {
	if m != nil {
		return m.MiniOpenid
	}
	return ""
}

// 用户实名注册返回值
type RealRegisterResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	UserIdentification   string   `protobuf:"bytes,3,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RealRegisterResponse) Reset()         { *m = RealRegisterResponse{} }
func (m *RealRegisterResponse) String() string { return proto.CompactTextString(m) }
func (*RealRegisterResponse) ProtoMessage()    {}
func (*RealRegisterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{1}
}

func (m *RealRegisterResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RealRegisterResponse.Unmarshal(m, b)
}
func (m *RealRegisterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RealRegisterResponse.Marshal(b, m, deterministic)
}
func (m *RealRegisterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RealRegisterResponse.Merge(m, src)
}
func (m *RealRegisterResponse) XXX_Size() int {
	return xxx_messageInfo_RealRegisterResponse.Size(m)
}
func (m *RealRegisterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RealRegisterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RealRegisterResponse proto.InternalMessageInfo

func (m *RealRegisterResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RealRegisterResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RealRegisterResponse) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

// 素材上传参数
type DigitalImageUploadRequest struct {
	FilePath             string   `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path"`
	UserIdentification   string   `protobuf:"bytes,2,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DigitalImageUploadRequest) Reset()         { *m = DigitalImageUploadRequest{} }
func (m *DigitalImageUploadRequest) String() string { return proto.CompactTextString(m) }
func (*DigitalImageUploadRequest) ProtoMessage()    {}
func (*DigitalImageUploadRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{2}
}

func (m *DigitalImageUploadRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalImageUploadRequest.Unmarshal(m, b)
}
func (m *DigitalImageUploadRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalImageUploadRequest.Marshal(b, m, deterministic)
}
func (m *DigitalImageUploadRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalImageUploadRequest.Merge(m, src)
}
func (m *DigitalImageUploadRequest) XXX_Size() int {
	return xxx_messageInfo_DigitalImageUploadRequest.Size(m)
}
func (m *DigitalImageUploadRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalImageUploadRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalImageUploadRequest proto.InternalMessageInfo

func (m *DigitalImageUploadRequest) GetFilePath() string {
	if m != nil {
		return m.FilePath
	}
	return ""
}

func (m *DigitalImageUploadRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

// 素材上传返回值
type DigitalImageUploadResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	ImageUrl             string   `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DigitalImageUploadResponse) Reset()         { *m = DigitalImageUploadResponse{} }
func (m *DigitalImageUploadResponse) String() string { return proto.CompactTextString(m) }
func (*DigitalImageUploadResponse) ProtoMessage()    {}
func (*DigitalImageUploadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{3}
}

func (m *DigitalImageUploadResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalImageUploadResponse.Unmarshal(m, b)
}
func (m *DigitalImageUploadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalImageUploadResponse.Marshal(b, m, deterministic)
}
func (m *DigitalImageUploadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalImageUploadResponse.Merge(m, src)
}
func (m *DigitalImageUploadResponse) XXX_Size() int {
	return xxx_messageInfo_DigitalImageUploadResponse.Size(m)
}
func (m *DigitalImageUploadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalImageUploadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalImageUploadResponse proto.InternalMessageInfo

func (m *DigitalImageUploadResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DigitalImageUploadResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DigitalImageUploadResponse) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

// 发行数字藏品参数
type NftPublishDigitalRequest struct {
	Author               string   `protobuf:"bytes,1,opt,name=author,proto3" json:"author"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url"`
	DisplayUrl           string   `protobuf:"bytes,4,opt,name=display_url,json=displayUrl,proto3" json:"display_url"`
	Desc                 string   `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc"`
	Flag                 string   `protobuf:"bytes,6,opt,name=flag,proto3" json:"flag"`
	UserIdentification   string   `protobuf:"bytes,7,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	PublishCount         int32    `protobuf:"varint,8,opt,name=publish_count,json=publishCount,proto3" json:"publish_count"`
	SeriesId             string   `protobuf:"bytes,9,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	SeriesBeginIndex     int32    `protobuf:"varint,10,opt,name=series_begin_index,json=seriesBeginIndex,proto3" json:"series_begin_index"`
	SellStatus           int32    `protobuf:"varint,11,opt,name=sell_status,json=sellStatus,proto3" json:"sell_status"`
	SellCount            int32    `protobuf:"varint,12,opt,name=sell_count,json=sellCount,proto3" json:"sell_count"`
	PackageType          string   `protobuf:"bytes,13,opt,name=package_type,json=packageType,proto3" json:"package_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPublishDigitalRequest) Reset()         { *m = NftPublishDigitalRequest{} }
func (m *NftPublishDigitalRequest) String() string { return proto.CompactTextString(m) }
func (*NftPublishDigitalRequest) ProtoMessage()    {}
func (*NftPublishDigitalRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{4}
}

func (m *NftPublishDigitalRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPublishDigitalRequest.Unmarshal(m, b)
}
func (m *NftPublishDigitalRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPublishDigitalRequest.Marshal(b, m, deterministic)
}
func (m *NftPublishDigitalRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPublishDigitalRequest.Merge(m, src)
}
func (m *NftPublishDigitalRequest) XXX_Size() int {
	return xxx_messageInfo_NftPublishDigitalRequest.Size(m)
}
func (m *NftPublishDigitalRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPublishDigitalRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftPublishDigitalRequest proto.InternalMessageInfo

func (m *NftPublishDigitalRequest) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetDisplayUrl() string {
	if m != nil {
		return m.DisplayUrl
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetFlag() string {
	if m != nil {
		return m.Flag
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetPublishCount() int32 {
	if m != nil {
		return m.PublishCount
	}
	return 0
}

func (m *NftPublishDigitalRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftPublishDigitalRequest) GetSeriesBeginIndex() int32 {
	if m != nil {
		return m.SeriesBeginIndex
	}
	return 0
}

func (m *NftPublishDigitalRequest) GetSellStatus() int32 {
	if m != nil {
		return m.SellStatus
	}
	return 0
}

func (m *NftPublishDigitalRequest) GetSellCount() int32 {
	if m != nil {
		return m.SellCount
	}
	return 0
}

func (m *NftPublishDigitalRequest) GetPackageType() string {
	if m != nil {
		return m.PackageType
	}
	return ""
}

//发行数字藏品返回值
type NftPublishDigitalResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPublishDigitalResponse) Reset()         { *m = NftPublishDigitalResponse{} }
func (m *NftPublishDigitalResponse) String() string { return proto.CompactTextString(m) }
func (*NftPublishDigitalResponse) ProtoMessage()    {}
func (*NftPublishDigitalResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{5}
}

func (m *NftPublishDigitalResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPublishDigitalResponse.Unmarshal(m, b)
}
func (m *NftPublishDigitalResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPublishDigitalResponse.Marshal(b, m, deterministic)
}
func (m *NftPublishDigitalResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPublishDigitalResponse.Merge(m, src)
}
func (m *NftPublishDigitalResponse) XXX_Size() int {
	return xxx_messageInfo_NftPublishDigitalResponse.Size(m)
}
func (m *NftPublishDigitalResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPublishDigitalResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftPublishDigitalResponse proto.InternalMessageInfo

func (m *NftPublishDigitalResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftPublishDigitalResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftPublishDigitalResponse) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

//声明系列入参
type NftSeriesClaimRequest struct {
	SeriesName           string   `protobuf:"bytes,1,opt,name=series_name,json=seriesName,proto3" json:"series_name"`
	TotalCount           int32    `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	CoverUrl             string   `protobuf:"bytes,3,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	UserIdentification   string   `protobuf:"bytes,5,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSeriesClaimRequest) Reset()         { *m = NftSeriesClaimRequest{} }
func (m *NftSeriesClaimRequest) String() string { return proto.CompactTextString(m) }
func (*NftSeriesClaimRequest) ProtoMessage()    {}
func (*NftSeriesClaimRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{6}
}

func (m *NftSeriesClaimRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSeriesClaimRequest.Unmarshal(m, b)
}
func (m *NftSeriesClaimRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSeriesClaimRequest.Marshal(b, m, deterministic)
}
func (m *NftSeriesClaimRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSeriesClaimRequest.Merge(m, src)
}
func (m *NftSeriesClaimRequest) XXX_Size() int {
	return xxx_messageInfo_NftSeriesClaimRequest.Size(m)
}
func (m *NftSeriesClaimRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSeriesClaimRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftSeriesClaimRequest proto.InternalMessageInfo

func (m *NftSeriesClaimRequest) GetSeriesName() string {
	if m != nil {
		return m.SeriesName
	}
	return ""
}

func (m *NftSeriesClaimRequest) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *NftSeriesClaimRequest) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *NftSeriesClaimRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftSeriesClaimRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

//声明系列返回值
type NftSeriesClaimResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSeriesClaimResponse) Reset()         { *m = NftSeriesClaimResponse{} }
func (m *NftSeriesClaimResponse) String() string { return proto.CompactTextString(m) }
func (*NftSeriesClaimResponse) ProtoMessage()    {}
func (*NftSeriesClaimResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{7}
}

func (m *NftSeriesClaimResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSeriesClaimResponse.Unmarshal(m, b)
}
func (m *NftSeriesClaimResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSeriesClaimResponse.Marshal(b, m, deterministic)
}
func (m *NftSeriesClaimResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSeriesClaimResponse.Merge(m, src)
}
func (m *NftSeriesClaimResponse) XXX_Size() int {
	return xxx_messageInfo_NftSeriesClaimResponse.Size(m)
}
func (m *NftSeriesClaimResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSeriesClaimResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftSeriesClaimResponse proto.InternalMessageInfo

func (m *NftSeriesClaimResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftSeriesClaimResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftSeriesClaimResponse) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

//nft数字藏品列表藏品入参
type NftSeriesListRequest struct {
	Offset               int64    `protobuf:"varint,1,opt,name=offset,proto3" json:"offset"`
	Limit                int64    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit"`
	SeriesId             string   `protobuf:"bytes,3,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	UserIdentification   string   `protobuf:"bytes,4,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSeriesListRequest) Reset()         { *m = NftSeriesListRequest{} }
func (m *NftSeriesListRequest) String() string { return proto.CompactTextString(m) }
func (*NftSeriesListRequest) ProtoMessage()    {}
func (*NftSeriesListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{8}
}

func (m *NftSeriesListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSeriesListRequest.Unmarshal(m, b)
}
func (m *NftSeriesListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSeriesListRequest.Marshal(b, m, deterministic)
}
func (m *NftSeriesListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSeriesListRequest.Merge(m, src)
}
func (m *NftSeriesListRequest) XXX_Size() int {
	return xxx_messageInfo_NftSeriesListRequest.Size(m)
}
func (m *NftSeriesListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSeriesListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftSeriesListRequest proto.InternalMessageInfo

func (m *NftSeriesListRequest) GetOffset() int64 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *NftSeriesListRequest) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *NftSeriesListRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftSeriesListRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

type NftInfo struct {
	NftId                string   `protobuf:"bytes,1,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	Flag                 string   `protobuf:"bytes,2,opt,name=flag,proto3" json:"flag"`
	Author               string   `protobuf:"bytes,3,opt,name=author,proto3" json:"author"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	Url                  string   `protobuf:"bytes,5,opt,name=url,proto3" json:"url"`
	DisplayUrl           string   `protobuf:"bytes,6,opt,name=display_url,json=displayUrl,proto3" json:"display_url"`
	Desc                 string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc"`
	SeriesName           string   `protobuf:"bytes,8,opt,name=series_name,json=seriesName,proto3" json:"series_name"`
	SeriesId             string   `protobuf:"bytes,9,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	SellStatus           int32    `protobuf:"varint,10,opt,name=sell_status,json=sellStatus,proto3" json:"sell_status"`
	SeriesTotalNum       int32    `protobuf:"varint,11,opt,name=series_total_num,json=seriesTotalNum,proto3" json:"series_total_num"`
	SeriesIndexId        int32    `protobuf:"varint,12,opt,name=series_index_id,json=seriesIndexId,proto3" json:"series_index_id"`
	SellCount            int32    `protobuf:"varint,13,opt,name=sell_count,json=sellCount,proto3" json:"sell_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftInfo) Reset()         { *m = NftInfo{} }
func (m *NftInfo) String() string { return proto.CompactTextString(m) }
func (*NftInfo) ProtoMessage()    {}
func (*NftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{9}
}

func (m *NftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftInfo.Unmarshal(m, b)
}
func (m *NftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftInfo.Marshal(b, m, deterministic)
}
func (m *NftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftInfo.Merge(m, src)
}
func (m *NftInfo) XXX_Size() int {
	return xxx_messageInfo_NftInfo.Size(m)
}
func (m *NftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NftInfo proto.InternalMessageInfo

func (m *NftInfo) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

func (m *NftInfo) GetFlag() string {
	if m != nil {
		return m.Flag
	}
	return ""
}

func (m *NftInfo) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *NftInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *NftInfo) GetDisplayUrl() string {
	if m != nil {
		return m.DisplayUrl
	}
	return ""
}

func (m *NftInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftInfo) GetSeriesName() string {
	if m != nil {
		return m.SeriesName
	}
	return ""
}

func (m *NftInfo) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftInfo) GetSellStatus() int32 {
	if m != nil {
		return m.SellStatus
	}
	return 0
}

func (m *NftInfo) GetSeriesTotalNum() int32 {
	if m != nil {
		return m.SeriesTotalNum
	}
	return 0
}

func (m *NftInfo) GetSeriesIndexId() int32 {
	if m != nil {
		return m.SeriesIndexId
	}
	return 0
}

func (m *NftInfo) GetSellCount() int32 {
	if m != nil {
		return m.SellCount
	}
	return 0
}

//nft数字藏品列表返回值
type NftSeriesListResponse struct {
	Code                 int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32      `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	NftInfo              []*NftInfo `protobuf:"bytes,4,rep,name=nft_info,json=nftInfo,proto3" json:"nft_info"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *NftSeriesListResponse) Reset()         { *m = NftSeriesListResponse{} }
func (m *NftSeriesListResponse) String() string { return proto.CompactTextString(m) }
func (*NftSeriesListResponse) ProtoMessage()    {}
func (*NftSeriesListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{10}
}

func (m *NftSeriesListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSeriesListResponse.Unmarshal(m, b)
}
func (m *NftSeriesListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSeriesListResponse.Marshal(b, m, deterministic)
}
func (m *NftSeriesListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSeriesListResponse.Merge(m, src)
}
func (m *NftSeriesListResponse) XXX_Size() int {
	return xxx_messageInfo_NftSeriesListResponse.Size(m)
}
func (m *NftSeriesListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSeriesListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftSeriesListResponse proto.InternalMessageInfo

func (m *NftSeriesListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftSeriesListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftSeriesListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *NftSeriesListResponse) GetNftInfo() []*NftInfo {
	if m != nil {
		return m.NftInfo
	}
	return nil
}

//查询各类结果入参
type NftSearchResultRequest struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Method               string   `protobuf:"bytes,2,opt,name=method,proto3" json:"method"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSearchResultRequest) Reset()         { *m = NftSearchResultRequest{} }
func (m *NftSearchResultRequest) String() string { return proto.CompactTextString(m) }
func (*NftSearchResultRequest) ProtoMessage()    {}
func (*NftSearchResultRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{11}
}

func (m *NftSearchResultRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSearchResultRequest.Unmarshal(m, b)
}
func (m *NftSearchResultRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSearchResultRequest.Marshal(b, m, deterministic)
}
func (m *NftSearchResultRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSearchResultRequest.Merge(m, src)
}
func (m *NftSearchResultRequest) XXX_Size() int {
	return xxx_messageInfo_NftSearchResultRequest.Size(m)
}
func (m *NftSearchResultRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSearchResultRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftSearchResultRequest proto.InternalMessageInfo

func (m *NftSearchResultRequest) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *NftSearchResultRequest) GetMethod() string {
	if m != nil {
		return m.Method
	}
	return ""
}

//查询各类结果返回值
type NftSearchResultResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSearchResultResponse) Reset()         { *m = NftSearchResultResponse{} }
func (m *NftSearchResultResponse) String() string { return proto.CompactTextString(m) }
func (*NftSearchResultResponse) ProtoMessage()    {}
func (*NftSearchResultResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{12}
}

func (m *NftSearchResultResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSearchResultResponse.Unmarshal(m, b)
}
func (m *NftSearchResultResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSearchResultResponse.Marshal(b, m, deterministic)
}
func (m *NftSearchResultResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSearchResultResponse.Merge(m, src)
}
func (m *NftSearchResultResponse) XXX_Size() int {
	return xxx_messageInfo_NftSearchResultResponse.Size(m)
}
func (m *NftSearchResultResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSearchResultResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftSearchResultResponse proto.InternalMessageInfo

func (m *NftSearchResultResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftSearchResultResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftSearchResultResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

//积分查询入参
type NftPointQueryRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPointQueryRequest) Reset()         { *m = NftPointQueryRequest{} }
func (m *NftPointQueryRequest) String() string { return proto.CompactTextString(m) }
func (*NftPointQueryRequest) ProtoMessage()    {}
func (*NftPointQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{13}
}

func (m *NftPointQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPointQueryRequest.Unmarshal(m, b)
}
func (m *NftPointQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPointQueryRequest.Marshal(b, m, deterministic)
}
func (m *NftPointQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPointQueryRequest.Merge(m, src)
}
func (m *NftPointQueryRequest) XXX_Size() int {
	return xxx_messageInfo_NftPointQueryRequest.Size(m)
}
func (m *NftPointQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPointQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftPointQueryRequest proto.InternalMessageInfo

func (m *NftPointQueryRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

//积分查询返回值
type NftPointQueryResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Count                int32    `protobuf:"varint,3,opt,name=count,proto3" json:"count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPointQueryResponse) Reset()         { *m = NftPointQueryResponse{} }
func (m *NftPointQueryResponse) String() string { return proto.CompactTextString(m) }
func (*NftPointQueryResponse) ProtoMessage()    {}
func (*NftPointQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{14}
}

func (m *NftPointQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPointQueryResponse.Unmarshal(m, b)
}
func (m *NftPointQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPointQueryResponse.Marshal(b, m, deterministic)
}
func (m *NftPointQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPointQueryResponse.Merge(m, src)
}
func (m *NftPointQueryResponse) XXX_Size() int {
	return xxx_messageInfo_NftPointQueryResponse.Size(m)
}
func (m *NftPointQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPointQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftPointQueryResponse proto.InternalMessageInfo

func (m *NftPointQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftPointQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftPointQueryResponse) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

//数字藏品详细信息参数
type NftInfoRequest struct {
	NftId                string   `protobuf:"bytes,1,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftInfoRequest) Reset()         { *m = NftInfoRequest{} }
func (m *NftInfoRequest) String() string { return proto.CompactTextString(m) }
func (*NftInfoRequest) ProtoMessage()    {}
func (*NftInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{15}
}

func (m *NftInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftInfoRequest.Unmarshal(m, b)
}
func (m *NftInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftInfoRequest.Marshal(b, m, deterministic)
}
func (m *NftInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftInfoRequest.Merge(m, src)
}
func (m *NftInfoRequest) XXX_Size() int {
	return xxx_messageInfo_NftInfoRequest.Size(m)
}
func (m *NftInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftInfoRequest proto.InternalMessageInfo

func (m *NftInfoRequest) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

//数字藏品详细信息参数返回值
type NftInfoResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	NftInfo              *NftInfo `protobuf:"bytes,3,opt,name=nft_info,json=nftInfo,proto3" json:"nft_info"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftInfoResponse) Reset()         { *m = NftInfoResponse{} }
func (m *NftInfoResponse) String() string { return proto.CompactTextString(m) }
func (*NftInfoResponse) ProtoMessage()    {}
func (*NftInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{16}
}

func (m *NftInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftInfoResponse.Unmarshal(m, b)
}
func (m *NftInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftInfoResponse.Marshal(b, m, deterministic)
}
func (m *NftInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftInfoResponse.Merge(m, src)
}
func (m *NftInfoResponse) XXX_Size() int {
	return xxx_messageInfo_NftInfoResponse.Size(m)
}
func (m *NftInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftInfoResponse proto.InternalMessageInfo

func (m *NftInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftInfoResponse) GetNftInfo() *NftInfo {
	if m != nil {
		return m.NftInfo
	}
	return nil
}

//nft购买参数
type NftBuyRequest struct {
	NftId                string   `protobuf:"bytes,1,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	UserIdentification   string   `protobuf:"bytes,2,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	ApplyScore           int32    `protobuf:"varint,3,opt,name=apply_score,json=applyScore,proto3" json:"apply_score"`
	OfferCount           int32    `protobuf:"varint,4,opt,name=offer_count,json=offerCount,proto3" json:"offer_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftBuyRequest) Reset()         { *m = NftBuyRequest{} }
func (m *NftBuyRequest) String() string { return proto.CompactTextString(m) }
func (*NftBuyRequest) ProtoMessage()    {}
func (*NftBuyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{17}
}

func (m *NftBuyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftBuyRequest.Unmarshal(m, b)
}
func (m *NftBuyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftBuyRequest.Marshal(b, m, deterministic)
}
func (m *NftBuyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftBuyRequest.Merge(m, src)
}
func (m *NftBuyRequest) XXX_Size() int {
	return xxx_messageInfo_NftBuyRequest.Size(m)
}
func (m *NftBuyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftBuyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftBuyRequest proto.InternalMessageInfo

func (m *NftBuyRequest) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

func (m *NftBuyRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftBuyRequest) GetApplyScore() int32 {
	if m != nil {
		return m.ApplyScore
	}
	return 0
}

func (m *NftBuyRequest) GetOfferCount() int32 {
	if m != nil {
		return m.OfferCount
	}
	return 0
}

//数字藏品购买返回值
type NftBuyResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftBuyResponse) Reset()         { *m = NftBuyResponse{} }
func (m *NftBuyResponse) String() string { return proto.CompactTextString(m) }
func (*NftBuyResponse) ProtoMessage()    {}
func (*NftBuyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{18}
}

func (m *NftBuyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftBuyResponse.Unmarshal(m, b)
}
func (m *NftBuyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftBuyResponse.Marshal(b, m, deterministic)
}
func (m *NftBuyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftBuyResponse.Merge(m, src)
}
func (m *NftBuyResponse) XXX_Size() int {
	return xxx_messageInfo_NftBuyResponse.Size(m)
}
func (m *NftBuyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftBuyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftBuyResponse proto.InternalMessageInfo

func (m *NftBuyResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftBuyResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftBuyResponse) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

//nft销售状态变更参数
type NftStatusRequest struct {
	NftId                string   `protobuf:"bytes,1,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	UserIdentification   string   `protobuf:"bytes,2,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	TransStatus          int32    `protobuf:"varint,3,opt,name=trans_status,json=transStatus,proto3" json:"trans_status"`
	TransPrice           int32    `protobuf:"varint,4,opt,name=trans_price,json=transPrice,proto3" json:"trans_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftStatusRequest) Reset()         { *m = NftStatusRequest{} }
func (m *NftStatusRequest) String() string { return proto.CompactTextString(m) }
func (*NftStatusRequest) ProtoMessage()    {}
func (*NftStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{19}
}

func (m *NftStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftStatusRequest.Unmarshal(m, b)
}
func (m *NftStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftStatusRequest.Marshal(b, m, deterministic)
}
func (m *NftStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftStatusRequest.Merge(m, src)
}
func (m *NftStatusRequest) XXX_Size() int {
	return xxx_messageInfo_NftStatusRequest.Size(m)
}
func (m *NftStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftStatusRequest proto.InternalMessageInfo

func (m *NftStatusRequest) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

func (m *NftStatusRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftStatusRequest) GetTransStatus() int32 {
	if m != nil {
		return m.TransStatus
	}
	return 0
}

func (m *NftStatusRequest) GetTransPrice() int32 {
	if m != nil {
		return m.TransPrice
	}
	return 0
}

//nft销售状态变更返回值
type NftStatusResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	TaskId               string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftStatusResponse) Reset()         { *m = NftStatusResponse{} }
func (m *NftStatusResponse) String() string { return proto.CompactTextString(m) }
func (*NftStatusResponse) ProtoMessage()    {}
func (*NftStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{20}
}

func (m *NftStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftStatusResponse.Unmarshal(m, b)
}
func (m *NftStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftStatusResponse.Marshal(b, m, deterministic)
}
func (m *NftStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftStatusResponse.Merge(m, src)
}
func (m *NftStatusResponse) XXX_Size() int {
	return xxx_messageInfo_NftStatusResponse.Size(m)
}
func (m *NftStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftStatusResponse proto.InternalMessageInfo

func (m *NftStatusResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftStatusResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftStatusResponse) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

//判断获取openid
type UserOpenidRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOpenidRequest) Reset()         { *m = UserOpenidRequest{} }
func (m *UserOpenidRequest) String() string { return proto.CompactTextString(m) }
func (*UserOpenidRequest) ProtoMessage()    {}
func (*UserOpenidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{21}
}

func (m *UserOpenidRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOpenidRequest.Unmarshal(m, b)
}
func (m *UserOpenidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOpenidRequest.Marshal(b, m, deterministic)
}
func (m *UserOpenidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOpenidRequest.Merge(m, src)
}
func (m *UserOpenidRequest) XXX_Size() int {
	return xxx_messageInfo_UserOpenidRequest.Size(m)
}
func (m *UserOpenidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOpenidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserOpenidRequest proto.InternalMessageInfo

func (m *UserOpenidRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

type UserOpenidResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOpenidResponse) Reset()         { *m = UserOpenidResponse{} }
func (m *UserOpenidResponse) String() string { return proto.CompactTextString(m) }
func (*UserOpenidResponse) ProtoMessage()    {}
func (*UserOpenidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{22}
}

func (m *UserOpenidResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOpenidResponse.Unmarshal(m, b)
}
func (m *UserOpenidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOpenidResponse.Marshal(b, m, deterministic)
}
func (m *UserOpenidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOpenidResponse.Merge(m, src)
}
func (m *UserOpenidResponse) XXX_Size() int {
	return xxx_messageInfo_UserOpenidResponse.Size(m)
}
func (m *UserOpenidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOpenidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserOpenidResponse proto.InternalMessageInfo

func (m *UserOpenidResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserOpenidResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserOpenidResponse) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

//创建平台数字藏品参数
type NftCreateRequest struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	ImageUrl             string   `protobuf:"bytes,2,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	UserIdentification   string   `protobuf:"bytes,4,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	Price                float32  `protobuf:"fixed32,5,opt,name=price,proto3" json:"price"`
	SeriesId             string   `protobuf:"bytes,6,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Detail               string   `protobuf:"bytes,7,opt,name=detail,proto3" json:"detail"`
	DigitalType          int32    `protobuf:"varint,8,opt,name=digital_type,json=digitalType,proto3" json:"digital_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftCreateRequest) Reset()         { *m = NftCreateRequest{} }
func (m *NftCreateRequest) String() string { return proto.CompactTextString(m) }
func (*NftCreateRequest) ProtoMessage()    {}
func (*NftCreateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{23}
}

func (m *NftCreateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftCreateRequest.Unmarshal(m, b)
}
func (m *NftCreateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftCreateRequest.Marshal(b, m, deterministic)
}
func (m *NftCreateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftCreateRequest.Merge(m, src)
}
func (m *NftCreateRequest) XXX_Size() int {
	return xxx_messageInfo_NftCreateRequest.Size(m)
}
func (m *NftCreateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftCreateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftCreateRequest proto.InternalMessageInfo

func (m *NftCreateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftCreateRequest) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *NftCreateRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftCreateRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftCreateRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *NftCreateRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftCreateRequest) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *NftCreateRequest) GetDigitalType() int32 {
	if m != nil {
		return m.DigitalType
	}
	return 0
}

//平台数字藏品返回值
type NftResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftResponse) Reset()         { *m = NftResponse{} }
func (m *NftResponse) String() string { return proto.CompactTextString(m) }
func (*NftResponse) ProtoMessage()    {}
func (*NftResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{24}
}

func (m *NftResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftResponse.Unmarshal(m, b)
}
func (m *NftResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftResponse.Marshal(b, m, deterministic)
}
func (m *NftResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftResponse.Merge(m, src)
}
func (m *NftResponse) XXX_Size() int {
	return xxx_messageInfo_NftResponse.Size(m)
}
func (m *NftResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftResponse proto.InternalMessageInfo

func (m *NftResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

//更新数字藏品信息
type NftUpdateRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	ImageUrl             string   `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	UserIdentification   string   `protobuf:"bytes,5,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	Price                float32  `protobuf:"fixed32,6,opt,name=price,proto3" json:"price"`
	Status               int32    `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	SeriesId             string   `protobuf:"bytes,8,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Detail               string   `protobuf:"bytes,9,opt,name=detail,proto3" json:"detail"`
	DigitalType          int32    `protobuf:"varint,10,opt,name=digital_type,json=digitalType,proto3" json:"digital_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftUpdateRequest) Reset()         { *m = NftUpdateRequest{} }
func (m *NftUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*NftUpdateRequest) ProtoMessage()    {}
func (*NftUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{25}
}

func (m *NftUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftUpdateRequest.Unmarshal(m, b)
}
func (m *NftUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftUpdateRequest.Marshal(b, m, deterministic)
}
func (m *NftUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftUpdateRequest.Merge(m, src)
}
func (m *NftUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_NftUpdateRequest.Size(m)
}
func (m *NftUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftUpdateRequest proto.InternalMessageInfo

func (m *NftUpdateRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NftUpdateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftUpdateRequest) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *NftUpdateRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftUpdateRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftUpdateRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *NftUpdateRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *NftUpdateRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftUpdateRequest) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *NftUpdateRequest) GetDigitalType() int32 {
	if m != nil {
		return m.DigitalType
	}
	return 0
}

type NftDeleteRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftDeleteRequest) Reset()         { *m = NftDeleteRequest{} }
func (m *NftDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*NftDeleteRequest) ProtoMessage()    {}
func (*NftDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{26}
}

func (m *NftDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftDeleteRequest.Unmarshal(m, b)
}
func (m *NftDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftDeleteRequest.Marshal(b, m, deterministic)
}
func (m *NftDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftDeleteRequest.Merge(m, src)
}
func (m *NftDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_NftDeleteRequest.Size(m)
}
func (m *NftDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftDeleteRequest proto.InternalMessageInfo

func (m *NftDeleteRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type NftRuiPengListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftRuiPengListRequest) Reset()         { *m = NftRuiPengListRequest{} }
func (m *NftRuiPengListRequest) String() string { return proto.CompactTextString(m) }
func (*NftRuiPengListRequest) ProtoMessage()    {}
func (*NftRuiPengListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{27}
}

func (m *NftRuiPengListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftRuiPengListRequest.Unmarshal(m, b)
}
func (m *NftRuiPengListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftRuiPengListRequest.Marshal(b, m, deterministic)
}
func (m *NftRuiPengListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftRuiPengListRequest.Merge(m, src)
}
func (m *NftRuiPengListRequest) XXX_Size() int {
	return xxx_messageInfo_NftRuiPengListRequest.Size(m)
}
func (m *NftRuiPengListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftRuiPengListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftRuiPengListRequest proto.InternalMessageInfo

//瑞鹏数据藏品返回值
type NftRuiPengListResponse struct {
	Code                 int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*RuiPengList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *NftRuiPengListResponse) Reset()         { *m = NftRuiPengListResponse{} }
func (m *NftRuiPengListResponse) String() string { return proto.CompactTextString(m) }
func (*NftRuiPengListResponse) ProtoMessage()    {}
func (*NftRuiPengListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{28}
}

func (m *NftRuiPengListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftRuiPengListResponse.Unmarshal(m, b)
}
func (m *NftRuiPengListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftRuiPengListResponse.Marshal(b, m, deterministic)
}
func (m *NftRuiPengListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftRuiPengListResponse.Merge(m, src)
}
func (m *NftRuiPengListResponse) XXX_Size() int {
	return xxx_messageInfo_NftRuiPengListResponse.Size(m)
}
func (m *NftRuiPengListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftRuiPengListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftRuiPengListResponse proto.InternalMessageInfo

func (m *NftRuiPengListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftRuiPengListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftRuiPengListResponse) GetData() []*RuiPengList {
	if m != nil {
		return m.Data
	}
	return nil
}

//瑞鹏数据藏品列表数据展示
type RuiPengList struct {
	Name                  string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	ImageUrl              string   `protobuf:"bytes,2,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	Desc                  string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	Price                 float32  `protobuf:"fixed32,4,opt,name=price,proto3" json:"price"`
	SeriesId              string   `protobuf:"bytes,5,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	PublishIdentification string   `protobuf:"bytes,6,opt,name=publish_identification,json=publishIdentification,proto3" json:"publish_identification"`
	Detail                string   `protobuf:"bytes,7,opt,name=detail,proto3" json:"detail"`
	DigitalType           int32    `protobuf:"varint,8,opt,name=digital_type,json=digitalType,proto3" json:"digital_type"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *RuiPengList) Reset()         { *m = RuiPengList{} }
func (m *RuiPengList) String() string { return proto.CompactTextString(m) }
func (*RuiPengList) ProtoMessage()    {}
func (*RuiPengList) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{29}
}

func (m *RuiPengList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RuiPengList.Unmarshal(m, b)
}
func (m *RuiPengList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RuiPengList.Marshal(b, m, deterministic)
}
func (m *RuiPengList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuiPengList.Merge(m, src)
}
func (m *RuiPengList) XXX_Size() int {
	return xxx_messageInfo_RuiPengList.Size(m)
}
func (m *RuiPengList) XXX_DiscardUnknown() {
	xxx_messageInfo_RuiPengList.DiscardUnknown(m)
}

var xxx_messageInfo_RuiPengList proto.InternalMessageInfo

func (m *RuiPengList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RuiPengList) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *RuiPengList) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *RuiPengList) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *RuiPengList) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *RuiPengList) GetPublishIdentification() string {
	if m != nil {
		return m.PublishIdentification
	}
	return ""
}

func (m *RuiPengList) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *RuiPengList) GetDigitalType() int32 {
	if m != nil {
		return m.DigitalType
	}
	return 0
}

//购买nft入惨
type UserBuyNftRequest struct {
	UserIdentification    string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	PublishIdentification string   `protobuf:"bytes,2,opt,name=publish_identification,json=publishIdentification,proto3" json:"publish_identification"`
	SeriesId              string   `protobuf:"bytes,3,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Price                 float32  `protobuf:"fixed32,4,opt,name=price,proto3" json:"price"`
	SellType              int32    `protobuf:"varint,5,opt,name=sell_type,json=sellType,proto3" json:"sell_type"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *UserBuyNftRequest) Reset()         { *m = UserBuyNftRequest{} }
func (m *UserBuyNftRequest) String() string { return proto.CompactTextString(m) }
func (*UserBuyNftRequest) ProtoMessage()    {}
func (*UserBuyNftRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{30}
}

func (m *UserBuyNftRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBuyNftRequest.Unmarshal(m, b)
}
func (m *UserBuyNftRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBuyNftRequest.Marshal(b, m, deterministic)
}
func (m *UserBuyNftRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBuyNftRequest.Merge(m, src)
}
func (m *UserBuyNftRequest) XXX_Size() int {
	return xxx_messageInfo_UserBuyNftRequest.Size(m)
}
func (m *UserBuyNftRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBuyNftRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserBuyNftRequest proto.InternalMessageInfo

func (m *UserBuyNftRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *UserBuyNftRequest) GetPublishIdentification() string {
	if m != nil {
		return m.PublishIdentification
	}
	return ""
}

func (m *UserBuyNftRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *UserBuyNftRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *UserBuyNftRequest) GetSellType() int32 {
	if m != nil {
		return m.SellType
	}
	return 0
}

//购买nft返回值
type UserBuyNftResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	OrderSn              string   `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBuyNftResponse) Reset()         { *m = UserBuyNftResponse{} }
func (m *UserBuyNftResponse) String() string { return proto.CompactTextString(m) }
func (*UserBuyNftResponse) ProtoMessage()    {}
func (*UserBuyNftResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{31}
}

func (m *UserBuyNftResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBuyNftResponse.Unmarshal(m, b)
}
func (m *UserBuyNftResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBuyNftResponse.Marshal(b, m, deterministic)
}
func (m *UserBuyNftResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBuyNftResponse.Merge(m, src)
}
func (m *UserBuyNftResponse) XXX_Size() int {
	return xxx_messageInfo_UserBuyNftResponse.Size(m)
}
func (m *UserBuyNftResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBuyNftResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserBuyNftResponse proto.InternalMessageInfo

func (m *UserBuyNftResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserBuyNftResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserBuyNftResponse) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//瑞鹏平台详情页参数
type NftRuiPengDetailRequest struct {
	SeriesId             string   `protobuf:"bytes,1,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	UserIdentification   string   `protobuf:"bytes,2,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftRuiPengDetailRequest) Reset()         { *m = NftRuiPengDetailRequest{} }
func (m *NftRuiPengDetailRequest) String() string { return proto.CompactTextString(m) }
func (*NftRuiPengDetailRequest) ProtoMessage()    {}
func (*NftRuiPengDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{32}
}

func (m *NftRuiPengDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftRuiPengDetailRequest.Unmarshal(m, b)
}
func (m *NftRuiPengDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftRuiPengDetailRequest.Marshal(b, m, deterministic)
}
func (m *NftRuiPengDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftRuiPengDetailRequest.Merge(m, src)
}
func (m *NftRuiPengDetailRequest) XXX_Size() int {
	return xxx_messageInfo_NftRuiPengDetailRequest.Size(m)
}
func (m *NftRuiPengDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftRuiPengDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftRuiPengDetailRequest proto.InternalMessageInfo

func (m *NftRuiPengDetailRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftRuiPengDetailRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

type NftDetail struct {
	Detail                string   `protobuf:"bytes,1,opt,name=detail,proto3" json:"detail"`
	Price                 float32  `protobuf:"fixed32,2,opt,name=price,proto3" json:"price"`
	PublishIdentification string   `protobuf:"bytes,3,opt,name=publish_identification,json=publishIdentification,proto3" json:"publish_identification"`
	SeriesId              string   `protobuf:"bytes,4,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Flag                  int32    `protobuf:"varint,5,opt,name=flag,proto3" json:"flag"`
	Name                  string   `protobuf:"bytes,6,opt,name=name,proto3" json:"name"`
	Desc                  string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc"`
	DigitalType           int32    `protobuf:"varint,8,opt,name=digital_type,json=digitalType,proto3" json:"digital_type"`
	ImageUrl              string   `protobuf:"bytes,9,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *NftDetail) Reset()         { *m = NftDetail{} }
func (m *NftDetail) String() string { return proto.CompactTextString(m) }
func (*NftDetail) ProtoMessage()    {}
func (*NftDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{33}
}

func (m *NftDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftDetail.Unmarshal(m, b)
}
func (m *NftDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftDetail.Marshal(b, m, deterministic)
}
func (m *NftDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftDetail.Merge(m, src)
}
func (m *NftDetail) XXX_Size() int {
	return xxx_messageInfo_NftDetail.Size(m)
}
func (m *NftDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_NftDetail.DiscardUnknown(m)
}

var xxx_messageInfo_NftDetail proto.InternalMessageInfo

func (m *NftDetail) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *NftDetail) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *NftDetail) GetPublishIdentification() string {
	if m != nil {
		return m.PublishIdentification
	}
	return ""
}

func (m *NftDetail) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftDetail) GetFlag() int32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

func (m *NftDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftDetail) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftDetail) GetDigitalType() int32 {
	if m != nil {
		return m.DigitalType
	}
	return 0
}

func (m *NftDetail) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

//瑞鹏平台详情页返回值
type NftRuiPengDetailResponse struct {
	Code                 int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *NftDetail `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *NftRuiPengDetailResponse) Reset()         { *m = NftRuiPengDetailResponse{} }
func (m *NftRuiPengDetailResponse) String() string { return proto.CompactTextString(m) }
func (*NftRuiPengDetailResponse) ProtoMessage()    {}
func (*NftRuiPengDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{34}
}

func (m *NftRuiPengDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftRuiPengDetailResponse.Unmarshal(m, b)
}
func (m *NftRuiPengDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftRuiPengDetailResponse.Marshal(b, m, deterministic)
}
func (m *NftRuiPengDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftRuiPengDetailResponse.Merge(m, src)
}
func (m *NftRuiPengDetailResponse) XXX_Size() int {
	return xxx_messageInfo_NftRuiPengDetailResponse.Size(m)
}
func (m *NftRuiPengDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftRuiPengDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftRuiPengDetailResponse proto.InternalMessageInfo

func (m *NftRuiPengDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftRuiPengDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftRuiPengDetailResponse) GetData() *NftDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

//数字藏品订单支付参数
type UserNftPayRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	OrderSn              string   `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserNftPayRequest) Reset()         { *m = UserNftPayRequest{} }
func (m *UserNftPayRequest) String() string { return proto.CompactTextString(m) }
func (*UserNftPayRequest) ProtoMessage()    {}
func (*UserNftPayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{35}
}

func (m *UserNftPayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNftPayRequest.Unmarshal(m, b)
}
func (m *UserNftPayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNftPayRequest.Marshal(b, m, deterministic)
}
func (m *UserNftPayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNftPayRequest.Merge(m, src)
}
func (m *UserNftPayRequest) XXX_Size() int {
	return xxx_messageInfo_UserNftPayRequest.Size(m)
}
func (m *UserNftPayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNftPayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserNftPayRequest proto.InternalMessageInfo

func (m *UserNftPayRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *UserNftPayRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//数字藏品订单支付返回值
type UserNftPayResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserNftPayResponse) Reset()         { *m = UserNftPayResponse{} }
func (m *UserNftPayResponse) String() string { return proto.CompactTextString(m) }
func (*UserNftPayResponse) ProtoMessage()    {}
func (*UserNftPayResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{36}
}

func (m *UserNftPayResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNftPayResponse.Unmarshal(m, b)
}
func (m *UserNftPayResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNftPayResponse.Marshal(b, m, deterministic)
}
func (m *UserNftPayResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNftPayResponse.Merge(m, src)
}
func (m *UserNftPayResponse) XXX_Size() int {
	return xxx_messageInfo_UserNftPayResponse.Size(m)
}
func (m *UserNftPayResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNftPayResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserNftPayResponse proto.InternalMessageInfo

func (m *UserNftPayResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserNftPayResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserNftPayResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

//数字藏品购买领取
type NftPayResultPickRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	NftId                string   `protobuf:"bytes,2,opt,name=nft_id,json=nftId,proto3" json:"nft_id"`
	Price                float32  `protobuf:"fixed32,3,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftPayResultPickRequest) Reset()         { *m = NftPayResultPickRequest{} }
func (m *NftPayResultPickRequest) String() string { return proto.CompactTextString(m) }
func (*NftPayResultPickRequest) ProtoMessage()    {}
func (*NftPayResultPickRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{37}
}

func (m *NftPayResultPickRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftPayResultPickRequest.Unmarshal(m, b)
}
func (m *NftPayResultPickRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftPayResultPickRequest.Marshal(b, m, deterministic)
}
func (m *NftPayResultPickRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftPayResultPickRequest.Merge(m, src)
}
func (m *NftPayResultPickRequest) XXX_Size() int {
	return xxx_messageInfo_NftPayResultPickRequest.Size(m)
}
func (m *NftPayResultPickRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftPayResultPickRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftPayResultPickRequest proto.InternalMessageInfo

func (m *NftPayResultPickRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftPayResultPickRequest) GetNftId() string {
	if m != nil {
		return m.NftId
	}
	return ""
}

func (m *NftPayResultPickRequest) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

//获取auth openid
type UserAuthOpenidRequest struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserAuthOpenidRequest) Reset()         { *m = UserAuthOpenidRequest{} }
func (m *UserAuthOpenidRequest) String() string { return proto.CompactTextString(m) }
func (*UserAuthOpenidRequest) ProtoMessage()    {}
func (*UserAuthOpenidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{38}
}

func (m *UserAuthOpenidRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAuthOpenidRequest.Unmarshal(m, b)
}
func (m *UserAuthOpenidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAuthOpenidRequest.Marshal(b, m, deterministic)
}
func (m *UserAuthOpenidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAuthOpenidRequest.Merge(m, src)
}
func (m *UserAuthOpenidRequest) XXX_Size() int {
	return xxx_messageInfo_UserAuthOpenidRequest.Size(m)
}
func (m *UserAuthOpenidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAuthOpenidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserAuthOpenidRequest proto.InternalMessageInfo

func (m *UserAuthOpenidRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

//数字藏品订单支付返回值
type UserAuthOpenidResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Openid               string   `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserAuthOpenidResponse) Reset()         { *m = UserAuthOpenidResponse{} }
func (m *UserAuthOpenidResponse) String() string { return proto.CompactTextString(m) }
func (*UserAuthOpenidResponse) ProtoMessage()    {}
func (*UserAuthOpenidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{39}
}

func (m *UserAuthOpenidResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAuthOpenidResponse.Unmarshal(m, b)
}
func (m *UserAuthOpenidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAuthOpenidResponse.Marshal(b, m, deterministic)
}
func (m *UserAuthOpenidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAuthOpenidResponse.Merge(m, src)
}
func (m *UserAuthOpenidResponse) XXX_Size() int {
	return xxx_messageInfo_UserAuthOpenidResponse.Size(m)
}
func (m *UserAuthOpenidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAuthOpenidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserAuthOpenidResponse proto.InternalMessageInfo

func (m *UserAuthOpenidResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserAuthOpenidResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserAuthOpenidResponse) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

//用户转换参数
type ChangeUserIdRequest struct {
	ScrmUserId           string   `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	MiniOpenid           string   `protobuf:"bytes,2,opt,name=mini_openid,json=miniOpenid,proto3" json:"mini_openid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeUserIdRequest) Reset()         { *m = ChangeUserIdRequest{} }
func (m *ChangeUserIdRequest) String() string { return proto.CompactTextString(m) }
func (*ChangeUserIdRequest) ProtoMessage()    {}
func (*ChangeUserIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{40}
}

func (m *ChangeUserIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeUserIdRequest.Unmarshal(m, b)
}
func (m *ChangeUserIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeUserIdRequest.Marshal(b, m, deterministic)
}
func (m *ChangeUserIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeUserIdRequest.Merge(m, src)
}
func (m *ChangeUserIdRequest) XXX_Size() int {
	return xxx_messageInfo_ChangeUserIdRequest.Size(m)
}
func (m *ChangeUserIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeUserIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeUserIdRequest proto.InternalMessageInfo

func (m *ChangeUserIdRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *ChangeUserIdRequest) GetMiniOpenid() string {
	if m != nil {
		return m.MiniOpenid
	}
	return ""
}

//用户转换返回值
type ChangeUserIdResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	UserIdentification   string   `protobuf:"bytes,3,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	ScrmUserId           string   `protobuf:"bytes,4,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeUserIdResponse) Reset()         { *m = ChangeUserIdResponse{} }
func (m *ChangeUserIdResponse) String() string { return proto.CompactTextString(m) }
func (*ChangeUserIdResponse) ProtoMessage()    {}
func (*ChangeUserIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{41}
}

func (m *ChangeUserIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeUserIdResponse.Unmarshal(m, b)
}
func (m *ChangeUserIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeUserIdResponse.Marshal(b, m, deterministic)
}
func (m *ChangeUserIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeUserIdResponse.Merge(m, src)
}
func (m *ChangeUserIdResponse) XXX_Size() int {
	return xxx_messageInfo_ChangeUserIdResponse.Size(m)
}
func (m *ChangeUserIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeUserIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeUserIdResponse proto.InternalMessageInfo

func (m *ChangeUserIdResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChangeUserIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ChangeUserIdResponse) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *ChangeUserIdResponse) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

type NftSelectRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	SeriesId             string   `protobuf:"bytes,2,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSelectRequest) Reset()         { *m = NftSelectRequest{} }
func (m *NftSelectRequest) String() string { return proto.CompactTextString(m) }
func (*NftSelectRequest) ProtoMessage()    {}
func (*NftSelectRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{42}
}

func (m *NftSelectRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSelectRequest.Unmarshal(m, b)
}
func (m *NftSelectRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSelectRequest.Marshal(b, m, deterministic)
}
func (m *NftSelectRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSelectRequest.Merge(m, src)
}
func (m *NftSelectRequest) XXX_Size() int {
	return xxx_messageInfo_NftSelectRequest.Size(m)
}
func (m *NftSelectRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSelectRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftSelectRequest proto.InternalMessageInfo

func (m *NftSelectRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftSelectRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

type NftSelectPickRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	SeriesId             string   `protobuf:"bytes,2,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	ScrmUserId           string   `protobuf:"bytes,3,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	VenueId              int32    `protobuf:"varint,4,opt,name=venue_id,json=venueId,proto3" json:"venue_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSelectPickRequest) Reset()         { *m = NftSelectPickRequest{} }
func (m *NftSelectPickRequest) String() string { return proto.CompactTextString(m) }
func (*NftSelectPickRequest) ProtoMessage()    {}
func (*NftSelectPickRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{43}
}

func (m *NftSelectPickRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSelectPickRequest.Unmarshal(m, b)
}
func (m *NftSelectPickRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSelectPickRequest.Marshal(b, m, deterministic)
}
func (m *NftSelectPickRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSelectPickRequest.Merge(m, src)
}
func (m *NftSelectPickRequest) XXX_Size() int {
	return xxx_messageInfo_NftSelectPickRequest.Size(m)
}
func (m *NftSelectPickRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSelectPickRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftSelectPickRequest proto.InternalMessageInfo

func (m *NftSelectPickRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftSelectPickRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftSelectPickRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *NftSelectPickRequest) GetVenueId() int32 {
	if m != nil {
		return m.VenueId
	}
	return 0
}

//消费获取nft列表入参
type NftCostSelectRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	ScrmUserId           string   `protobuf:"bytes,2,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	Type                 int32    `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftCostSelectRequest) Reset()         { *m = NftCostSelectRequest{} }
func (m *NftCostSelectRequest) String() string { return proto.CompactTextString(m) }
func (*NftCostSelectRequest) ProtoMessage()    {}
func (*NftCostSelectRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{44}
}

func (m *NftCostSelectRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftCostSelectRequest.Unmarshal(m, b)
}
func (m *NftCostSelectRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftCostSelectRequest.Marshal(b, m, deterministic)
}
func (m *NftCostSelectRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftCostSelectRequest.Merge(m, src)
}
func (m *NftCostSelectRequest) XXX_Size() int {
	return xxx_messageInfo_NftCostSelectRequest.Size(m)
}
func (m *NftCostSelectRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftCostSelectRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftCostSelectRequest proto.InternalMessageInfo

func (m *NftCostSelectRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

func (m *NftCostSelectRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *NftCostSelectRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

//nft和coupon返回值
type NftCoupon struct {
	Key                  int32      `protobuf:"varint,1,opt,name=key,proto3" json:"key"`
	Title                string     `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	Desc                 string     `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	Coupon               *Coupon    `protobuf:"bytes,4,opt,name=coupon,proto3" json:"coupon"`
	Nft                  *NftDetail `protobuf:"bytes,5,opt,name=nft,proto3" json:"nft"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *NftCoupon) Reset()         { *m = NftCoupon{} }
func (m *NftCoupon) String() string { return proto.CompactTextString(m) }
func (*NftCoupon) ProtoMessage()    {}
func (*NftCoupon) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{45}
}

func (m *NftCoupon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftCoupon.Unmarshal(m, b)
}
func (m *NftCoupon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftCoupon.Marshal(b, m, deterministic)
}
func (m *NftCoupon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftCoupon.Merge(m, src)
}
func (m *NftCoupon) XXX_Size() int {
	return xxx_messageInfo_NftCoupon.Size(m)
}
func (m *NftCoupon) XXX_DiscardUnknown() {
	xxx_messageInfo_NftCoupon.DiscardUnknown(m)
}

var xxx_messageInfo_NftCoupon proto.InternalMessageInfo

func (m *NftCoupon) GetKey() int32 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *NftCoupon) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NftCoupon) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftCoupon) GetCoupon() *Coupon {
	if m != nil {
		return m.Coupon
	}
	return nil
}

func (m *NftCoupon) GetNft() *NftDetail {
	if m != nil {
		return m.Nft
	}
	return nil
}

type Coupon struct {
	CouponId             int32    `protobuf:"varint,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	CouponName           string   `protobuf:"bytes,2,opt,name=couponName,proto3" json:"couponName"`
	CouponDesc           string   `protobuf:"bytes,3,opt,name=couponDesc,proto3" json:"couponDesc"`
	CouponTime           string   `protobuf:"bytes,4,opt,name=CouponTime,proto3" json:"CouponTime"`
	CouponPrice          int32    `protobuf:"varint,5,opt,name=CouponPrice,proto3" json:"CouponPrice"`
	Flag                 int32    `protobuf:"varint,6,opt,name=flag,proto3" json:"flag"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Coupon) Reset()         { *m = Coupon{} }
func (m *Coupon) String() string { return proto.CompactTextString(m) }
func (*Coupon) ProtoMessage()    {}
func (*Coupon) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{46}
}

func (m *Coupon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Coupon.Unmarshal(m, b)
}
func (m *Coupon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Coupon.Marshal(b, m, deterministic)
}
func (m *Coupon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Coupon.Merge(m, src)
}
func (m *Coupon) XXX_Size() int {
	return xxx_messageInfo_Coupon.Size(m)
}
func (m *Coupon) XXX_DiscardUnknown() {
	xxx_messageInfo_Coupon.DiscardUnknown(m)
}

var xxx_messageInfo_Coupon proto.InternalMessageInfo

func (m *Coupon) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *Coupon) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *Coupon) GetCouponDesc() string {
	if m != nil {
		return m.CouponDesc
	}
	return ""
}

func (m *Coupon) GetCouponTime() string {
	if m != nil {
		return m.CouponTime
	}
	return ""
}

func (m *Coupon) GetCouponPrice() int32 {
	if m != nil {
		return m.CouponPrice
	}
	return 0
}

func (m *Coupon) GetFlag() int32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

type NftCostSelectResponse struct {
	Code                 int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*NftCoupon `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *NftCostSelectResponse) Reset()         { *m = NftCostSelectResponse{} }
func (m *NftCostSelectResponse) String() string { return proto.CompactTextString(m) }
func (*NftCostSelectResponse) ProtoMessage()    {}
func (*NftCostSelectResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{47}
}

func (m *NftCostSelectResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftCostSelectResponse.Unmarshal(m, b)
}
func (m *NftCostSelectResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftCostSelectResponse.Marshal(b, m, deterministic)
}
func (m *NftCostSelectResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftCostSelectResponse.Merge(m, src)
}
func (m *NftCostSelectResponse) XXX_Size() int {
	return xxx_messageInfo_NftCostSelectResponse.Size(m)
}
func (m *NftCostSelectResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftCostSelectResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftCostSelectResponse proto.InternalMessageInfo

func (m *NftCostSelectResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftCostSelectResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftCostSelectResponse) GetData() []*NftCoupon {
	if m != nil {
		return m.Data
	}
	return nil
}

//支付成功页计算支付金额
type CalcOrderCostRequest struct {
	ScrmUserId           string   `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	OrderSn              string   `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CalcOrderCostRequest) Reset()         { *m = CalcOrderCostRequest{} }
func (m *CalcOrderCostRequest) String() string { return proto.CompactTextString(m) }
func (*CalcOrderCostRequest) ProtoMessage()    {}
func (*CalcOrderCostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{48}
}

func (m *CalcOrderCostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalcOrderCostRequest.Unmarshal(m, b)
}
func (m *CalcOrderCostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalcOrderCostRequest.Marshal(b, m, deterministic)
}
func (m *CalcOrderCostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalcOrderCostRequest.Merge(m, src)
}
func (m *CalcOrderCostRequest) XXX_Size() int {
	return xxx_messageInfo_CalcOrderCostRequest.Size(m)
}
func (m *CalcOrderCostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CalcOrderCostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CalcOrderCostRequest proto.InternalMessageInfo

func (m *CalcOrderCostRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *CalcOrderCostRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type Cost struct {
	Flag                 int32    `protobuf:"varint,1,opt,name=flag,proto3" json:"flag"`
	Type                 int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Cost) Reset()         { *m = Cost{} }
func (m *Cost) String() string { return proto.CompactTextString(m) }
func (*Cost) ProtoMessage()    {}
func (*Cost) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{49}
}

func (m *Cost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Cost.Unmarshal(m, b)
}
func (m *Cost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Cost.Marshal(b, m, deterministic)
}
func (m *Cost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Cost.Merge(m, src)
}
func (m *Cost) XXX_Size() int {
	return xxx_messageInfo_Cost.Size(m)
}
func (m *Cost) XXX_DiscardUnknown() {
	xxx_messageInfo_Cost.DiscardUnknown(m)
}

var xxx_messageInfo_Cost proto.InternalMessageInfo

func (m *Cost) GetFlag() int32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

func (m *Cost) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type CalcOrderCostResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *Cost    `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CalcOrderCostResponse) Reset()         { *m = CalcOrderCostResponse{} }
func (m *CalcOrderCostResponse) String() string { return proto.CompactTextString(m) }
func (*CalcOrderCostResponse) ProtoMessage()    {}
func (*CalcOrderCostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{50}
}

func (m *CalcOrderCostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalcOrderCostResponse.Unmarshal(m, b)
}
func (m *CalcOrderCostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalcOrderCostResponse.Marshal(b, m, deterministic)
}
func (m *CalcOrderCostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalcOrderCostResponse.Merge(m, src)
}
func (m *CalcOrderCostResponse) XXX_Size() int {
	return xxx_messageInfo_CalcOrderCostResponse.Size(m)
}
func (m *CalcOrderCostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CalcOrderCostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CalcOrderCostResponse proto.InternalMessageInfo

func (m *CalcOrderCostResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CalcOrderCostResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CalcOrderCostResponse) GetData() *Cost {
	if m != nil {
		return m.Data
	}
	return nil
}

type VenueListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VenueListRequest) Reset()         { *m = VenueListRequest{} }
func (m *VenueListRequest) String() string { return proto.CompactTextString(m) }
func (*VenueListRequest) ProtoMessage()    {}
func (*VenueListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{51}
}

func (m *VenueListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VenueListRequest.Unmarshal(m, b)
}
func (m *VenueListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VenueListRequest.Marshal(b, m, deterministic)
}
func (m *VenueListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VenueListRequest.Merge(m, src)
}
func (m *VenueListRequest) XXX_Size() int {
	return xxx_messageInfo_VenueListRequest.Size(m)
}
func (m *VenueListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VenueListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VenueListRequest proto.InternalMessageInfo

type VenueInfo struct {
	VenueId              int32    `protobuf:"varint,1,opt,name=venue_id,json=venueId,proto3" json:"venue_id"`
	VenueName            string   `protobuf:"bytes,3,opt,name=venue_name,json=venueName,proto3" json:"venue_name"`
	VenueImage           string   `protobuf:"bytes,4,opt,name=venue_image,json=venueImage,proto3" json:"venue_image"`
	OpenTime             string   `protobuf:"bytes,5,opt,name=open_time,json=openTime,proto3" json:"open_time"`
	CloseTime            string   `protobuf:"bytes,6,opt,name=close_time,json=closeTime,proto3" json:"close_time"`
	State                int32    `protobuf:"varint,7,opt,name=state,proto3" json:"state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VenueInfo) Reset()         { *m = VenueInfo{} }
func (m *VenueInfo) String() string { return proto.CompactTextString(m) }
func (*VenueInfo) ProtoMessage()    {}
func (*VenueInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{52}
}

func (m *VenueInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VenueInfo.Unmarshal(m, b)
}
func (m *VenueInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VenueInfo.Marshal(b, m, deterministic)
}
func (m *VenueInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VenueInfo.Merge(m, src)
}
func (m *VenueInfo) XXX_Size() int {
	return xxx_messageInfo_VenueInfo.Size(m)
}
func (m *VenueInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VenueInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VenueInfo proto.InternalMessageInfo

func (m *VenueInfo) GetVenueId() int32 {
	if m != nil {
		return m.VenueId
	}
	return 0
}

func (m *VenueInfo) GetVenueName() string {
	if m != nil {
		return m.VenueName
	}
	return ""
}

func (m *VenueInfo) GetVenueImage() string {
	if m != nil {
		return m.VenueImage
	}
	return ""
}

func (m *VenueInfo) GetOpenTime() string {
	if m != nil {
		return m.OpenTime
	}
	return ""
}

func (m *VenueInfo) GetCloseTime() string {
	if m != nil {
		return m.CloseTime
	}
	return ""
}

func (m *VenueInfo) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

type VenueListResponse struct {
	Code                 int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*VenueInfo `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *VenueListResponse) Reset()         { *m = VenueListResponse{} }
func (m *VenueListResponse) String() string { return proto.CompactTextString(m) }
func (*VenueListResponse) ProtoMessage()    {}
func (*VenueListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{53}
}

func (m *VenueListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VenueListResponse.Unmarshal(m, b)
}
func (m *VenueListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VenueListResponse.Marshal(b, m, deterministic)
}
func (m *VenueListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VenueListResponse.Merge(m, src)
}
func (m *VenueListResponse) XXX_Size() int {
	return xxx_messageInfo_VenueListResponse.Size(m)
}
func (m *VenueListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_VenueListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_VenueListResponse proto.InternalMessageInfo

func (m *VenueListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VenueListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *VenueListResponse) GetData() []*VenueInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type VenueDetailRequest struct {
	VenueId              int32    `protobuf:"varint,1,opt,name=venue_id,json=venueId,proto3" json:"venue_id"`
	ScrmUserId           string   `protobuf:"bytes,2,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VenueDetailRequest) Reset()         { *m = VenueDetailRequest{} }
func (m *VenueDetailRequest) String() string { return proto.CompactTextString(m) }
func (*VenueDetailRequest) ProtoMessage()    {}
func (*VenueDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{54}
}

func (m *VenueDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VenueDetailRequest.Unmarshal(m, b)
}
func (m *VenueDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VenueDetailRequest.Marshal(b, m, deterministic)
}
func (m *VenueDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VenueDetailRequest.Merge(m, src)
}
func (m *VenueDetailRequest) XXX_Size() int {
	return xxx_messageInfo_VenueDetailRequest.Size(m)
}
func (m *VenueDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VenueDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VenueDetailRequest proto.InternalMessageInfo

func (m *VenueDetailRequest) GetVenueId() int32 {
	if m != nil {
		return m.VenueId
	}
	return 0
}

func (m *VenueDetailRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

type GoodsSku struct {
	SkuId                int32    `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	SkuName              string   `protobuf:"bytes,2,opt,name=sku_name,json=skuName,proto3" json:"sku_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsSku) Reset()         { *m = GoodsSku{} }
func (m *GoodsSku) String() string { return proto.CompactTextString(m) }
func (*GoodsSku) ProtoMessage()    {}
func (*GoodsSku) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{55}
}

func (m *GoodsSku) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsSku.Unmarshal(m, b)
}
func (m *GoodsSku) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsSku.Marshal(b, m, deterministic)
}
func (m *GoodsSku) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsSku.Merge(m, src)
}
func (m *GoodsSku) XXX_Size() int {
	return xxx_messageInfo_GoodsSku.Size(m)
}
func (m *GoodsSku) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsSku.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsSku proto.InternalMessageInfo

func (m *GoodsSku) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GoodsSku) GetSkuName() string {
	if m != nil {
		return m.SkuName
	}
	return ""
}

type Goods struct {
	GoodsId              int32       `protobuf:"varint,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	GoodsName            string      `protobuf:"bytes,2,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	GoodsImage           string      `protobuf:"bytes,3,opt,name=goods_image,json=goodsImage,proto3" json:"goods_image"`
	GoodsDesc            string      `protobuf:"bytes,4,opt,name=goods_desc,json=goodsDesc,proto3" json:"goods_desc"`
	State                int32       `protobuf:"varint,5,opt,name=state,proto3" json:"state"`
	ShareCount           int32       `protobuf:"varint,6,opt,name=share_count,json=shareCount,proto3" json:"share_count"`
	GoodsSku             []*GoodsSku `protobuf:"bytes,7,rep,name=goods_sku,json=goodsSku,proto3" json:"goods_sku"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Goods) Reset()         { *m = Goods{} }
func (m *Goods) String() string { return proto.CompactTextString(m) }
func (*Goods) ProtoMessage()    {}
func (*Goods) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{56}
}

func (m *Goods) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Goods.Unmarshal(m, b)
}
func (m *Goods) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Goods.Marshal(b, m, deterministic)
}
func (m *Goods) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Goods.Merge(m, src)
}
func (m *Goods) XXX_Size() int {
	return xxx_messageInfo_Goods.Size(m)
}
func (m *Goods) XXX_DiscardUnknown() {
	xxx_messageInfo_Goods.DiscardUnknown(m)
}

var xxx_messageInfo_Goods proto.InternalMessageInfo

func (m *Goods) GetGoodsId() int32 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

func (m *Goods) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *Goods) GetGoodsImage() string {
	if m != nil {
		return m.GoodsImage
	}
	return ""
}

func (m *Goods) GetGoodsDesc() string {
	if m != nil {
		return m.GoodsDesc
	}
	return ""
}

func (m *Goods) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *Goods) GetShareCount() int32 {
	if m != nil {
		return m.ShareCount
	}
	return 0
}

func (m *Goods) GetGoodsSku() []*GoodsSku {
	if m != nil {
		return m.GoodsSku
	}
	return nil
}

type VenueDetailResponse struct {
	Code                 int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Nft                  *NftDetail `protobuf:"bytes,3,opt,name=nft,proto3" json:"nft"`
	Goods                []*Goods   `protobuf:"bytes,4,rep,name=goods,proto3" json:"goods"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *VenueDetailResponse) Reset()         { *m = VenueDetailResponse{} }
func (m *VenueDetailResponse) String() string { return proto.CompactTextString(m) }
func (*VenueDetailResponse) ProtoMessage()    {}
func (*VenueDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{57}
}

func (m *VenueDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VenueDetailResponse.Unmarshal(m, b)
}
func (m *VenueDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VenueDetailResponse.Marshal(b, m, deterministic)
}
func (m *VenueDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VenueDetailResponse.Merge(m, src)
}
func (m *VenueDetailResponse) XXX_Size() int {
	return xxx_messageInfo_VenueDetailResponse.Size(m)
}
func (m *VenueDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_VenueDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_VenueDetailResponse proto.InternalMessageInfo

func (m *VenueDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VenueDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *VenueDetailResponse) GetNft() *NftDetail {
	if m != nil {
		return m.Nft
	}
	return nil
}

func (m *VenueDetailResponse) GetGoods() []*Goods {
	if m != nil {
		return m.Goods
	}
	return nil
}

//分享用户收集
type ShareUserCollectRequest struct {
	VenueId              int32    `protobuf:"varint,1,opt,name=venue_id,json=venueId,proto3" json:"venue_id"`
	ScrmUserId           string   `protobuf:"bytes,2,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	GoodsId              int32    `protobuf:"varint,3,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShareUserCollectRequest) Reset()         { *m = ShareUserCollectRequest{} }
func (m *ShareUserCollectRequest) String() string { return proto.CompactTextString(m) }
func (*ShareUserCollectRequest) ProtoMessage()    {}
func (*ShareUserCollectRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{58}
}

func (m *ShareUserCollectRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShareUserCollectRequest.Unmarshal(m, b)
}
func (m *ShareUserCollectRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShareUserCollectRequest.Marshal(b, m, deterministic)
}
func (m *ShareUserCollectRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShareUserCollectRequest.Merge(m, src)
}
func (m *ShareUserCollectRequest) XXX_Size() int {
	return xxx_messageInfo_ShareUserCollectRequest.Size(m)
}
func (m *ShareUserCollectRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShareUserCollectRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShareUserCollectRequest proto.InternalMessageInfo

func (m *ShareUserCollectRequest) GetVenueId() int32 {
	if m != nil {
		return m.VenueId
	}
	return 0
}

func (m *ShareUserCollectRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *ShareUserCollectRequest) GetGoodsId() int32 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

type VenueMessageSubscribeRequest struct {
	VenueId              int32    `protobuf:"varint,1,opt,name=venue_id,json=venueId,proto3" json:"venue_id"`
	ScrmUserId           string   `protobuf:"bytes,2,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VenueMessageSubscribeRequest) Reset()         { *m = VenueMessageSubscribeRequest{} }
func (m *VenueMessageSubscribeRequest) String() string { return proto.CompactTextString(m) }
func (*VenueMessageSubscribeRequest) ProtoMessage()    {}
func (*VenueMessageSubscribeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{59}
}

func (m *VenueMessageSubscribeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VenueMessageSubscribeRequest.Unmarshal(m, b)
}
func (m *VenueMessageSubscribeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VenueMessageSubscribeRequest.Marshal(b, m, deterministic)
}
func (m *VenueMessageSubscribeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VenueMessageSubscribeRequest.Merge(m, src)
}
func (m *VenueMessageSubscribeRequest) XXX_Size() int {
	return xxx_messageInfo_VenueMessageSubscribeRequest.Size(m)
}
func (m *VenueMessageSubscribeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VenueMessageSubscribeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VenueMessageSubscribeRequest proto.InternalMessageInfo

func (m *VenueMessageSubscribeRequest) GetVenueId() int32 {
	if m != nil {
		return m.VenueId
	}
	return 0
}

func (m *VenueMessageSubscribeRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

type SendSubcribeMessageRequest struct {
	VenueId              int32    `protobuf:"varint,1,opt,name=venue_id,json=venueId,proto3" json:"venue_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendSubcribeMessageRequest) Reset()         { *m = SendSubcribeMessageRequest{} }
func (m *SendSubcribeMessageRequest) String() string { return proto.CompactTextString(m) }
func (*SendSubcribeMessageRequest) ProtoMessage()    {}
func (*SendSubcribeMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{60}
}

func (m *SendSubcribeMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendSubcribeMessageRequest.Unmarshal(m, b)
}
func (m *SendSubcribeMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendSubcribeMessageRequest.Marshal(b, m, deterministic)
}
func (m *SendSubcribeMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendSubcribeMessageRequest.Merge(m, src)
}
func (m *SendSubcribeMessageRequest) XXX_Size() int {
	return xxx_messageInfo_SendSubcribeMessageRequest.Size(m)
}
func (m *SendSubcribeMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendSubcribeMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendSubcribeMessageRequest proto.InternalMessageInfo

func (m *SendSubcribeMessageRequest) GetVenueId() int32 {
	if m != nil {
		return m.VenueId
	}
	return 0
}

//优惠券领取参数
type CouponPickRequest struct {
	CouponPickId         int32    `protobuf:"varint,1,opt,name=coupon_pick_id,json=couponPickId,proto3" json:"coupon_pick_id"`
	ScrmUserId           string   `protobuf:"bytes,2,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	Token                string   `protobuf:"bytes,3,opt,name=token,proto3" json:"token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponPickRequest) Reset()         { *m = CouponPickRequest{} }
func (m *CouponPickRequest) String() string { return proto.CompactTextString(m) }
func (*CouponPickRequest) ProtoMessage()    {}
func (*CouponPickRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{61}
}

func (m *CouponPickRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponPickRequest.Unmarshal(m, b)
}
func (m *CouponPickRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponPickRequest.Marshal(b, m, deterministic)
}
func (m *CouponPickRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponPickRequest.Merge(m, src)
}
func (m *CouponPickRequest) XXX_Size() int {
	return xxx_messageInfo_CouponPickRequest.Size(m)
}
func (m *CouponPickRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponPickRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CouponPickRequest proto.InternalMessageInfo

func (m *CouponPickRequest) GetCouponPickId() int32 {
	if m != nil {
		return m.CouponPickId
	}
	return 0
}

func (m *CouponPickRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *CouponPickRequest) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type FindDigitalUserNftRequest struct {
	UserIdentification   string   `protobuf:"bytes,1,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FindDigitalUserNftRequest) Reset()         { *m = FindDigitalUserNftRequest{} }
func (m *FindDigitalUserNftRequest) String() string { return proto.CompactTextString(m) }
func (*FindDigitalUserNftRequest) ProtoMessage()    {}
func (*FindDigitalUserNftRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{62}
}

func (m *FindDigitalUserNftRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FindDigitalUserNftRequest.Unmarshal(m, b)
}
func (m *FindDigitalUserNftRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FindDigitalUserNftRequest.Marshal(b, m, deterministic)
}
func (m *FindDigitalUserNftRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindDigitalUserNftRequest.Merge(m, src)
}
func (m *FindDigitalUserNftRequest) XXX_Size() int {
	return xxx_messageInfo_FindDigitalUserNftRequest.Size(m)
}
func (m *FindDigitalUserNftRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FindDigitalUserNftRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FindDigitalUserNftRequest proto.InternalMessageInfo

func (m *FindDigitalUserNftRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

type NftSetStockRequest struct {
	SeriesId             string   `protobuf:"bytes,1,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Stock                int32    `protobuf:"varint,2,opt,name=stock,proto3" json:"stock"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftSetStockRequest) Reset()         { *m = NftSetStockRequest{} }
func (m *NftSetStockRequest) String() string { return proto.CompactTextString(m) }
func (*NftSetStockRequest) ProtoMessage()    {}
func (*NftSetStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{63}
}

func (m *NftSetStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftSetStockRequest.Unmarshal(m, b)
}
func (m *NftSetStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftSetStockRequest.Marshal(b, m, deterministic)
}
func (m *NftSetStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftSetStockRequest.Merge(m, src)
}
func (m *NftSetStockRequest) XXX_Size() int {
	return xxx_messageInfo_NftSetStockRequest.Size(m)
}
func (m *NftSetStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftSetStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftSetStockRequest proto.InternalMessageInfo

func (m *NftSetStockRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftSetStockRequest) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

type NftMetaSelectRequest struct {
	SeriesIds            string   `protobuf:"bytes,1,opt,name=series_ids,json=seriesIds,proto3" json:"series_ids"`
	UserIdentification   string   `protobuf:"bytes,2,opt,name=user_identification,json=userIdentification,proto3" json:"user_identification"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftMetaSelectRequest) Reset()         { *m = NftMetaSelectRequest{} }
func (m *NftMetaSelectRequest) String() string { return proto.CompactTextString(m) }
func (*NftMetaSelectRequest) ProtoMessage()    {}
func (*NftMetaSelectRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{64}
}

func (m *NftMetaSelectRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftMetaSelectRequest.Unmarshal(m, b)
}
func (m *NftMetaSelectRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftMetaSelectRequest.Marshal(b, m, deterministic)
}
func (m *NftMetaSelectRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftMetaSelectRequest.Merge(m, src)
}
func (m *NftMetaSelectRequest) XXX_Size() int {
	return xxx_messageInfo_NftMetaSelectRequest.Size(m)
}
func (m *NftMetaSelectRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NftMetaSelectRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NftMetaSelectRequest proto.InternalMessageInfo

func (m *NftMetaSelectRequest) GetSeriesIds() string {
	if m != nil {
		return m.SeriesIds
	}
	return ""
}

func (m *NftMetaSelectRequest) GetUserIdentification() string {
	if m != nil {
		return m.UserIdentification
	}
	return ""
}

type NftImage struct {
	SeriesId             string   `protobuf:"bytes,1,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	Flag                 int32    `protobuf:"varint,2,opt,name=flag,proto3" json:"flag"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	ImageUrl             string   `protobuf:"bytes,5,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NftImage) Reset()         { *m = NftImage{} }
func (m *NftImage) String() string { return proto.CompactTextString(m) }
func (*NftImage) ProtoMessage()    {}
func (*NftImage) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{65}
}

func (m *NftImage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftImage.Unmarshal(m, b)
}
func (m *NftImage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftImage.Marshal(b, m, deterministic)
}
func (m *NftImage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftImage.Merge(m, src)
}
func (m *NftImage) XXX_Size() int {
	return xxx_messageInfo_NftImage.Size(m)
}
func (m *NftImage) XXX_DiscardUnknown() {
	xxx_messageInfo_NftImage.DiscardUnknown(m)
}

var xxx_messageInfo_NftImage proto.InternalMessageInfo

func (m *NftImage) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *NftImage) GetFlag() int32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

func (m *NftImage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NftImage) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *NftImage) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

type NftMetaSelectResponse struct {
	Code                 int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*NftImage `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *NftMetaSelectResponse) Reset()         { *m = NftMetaSelectResponse{} }
func (m *NftMetaSelectResponse) String() string { return proto.CompactTextString(m) }
func (*NftMetaSelectResponse) ProtoMessage()    {}
func (*NftMetaSelectResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cde888c79920ab7f, []int{66}
}

func (m *NftMetaSelectResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NftMetaSelectResponse.Unmarshal(m, b)
}
func (m *NftMetaSelectResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NftMetaSelectResponse.Marshal(b, m, deterministic)
}
func (m *NftMetaSelectResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NftMetaSelectResponse.Merge(m, src)
}
func (m *NftMetaSelectResponse) XXX_Size() int {
	return xxx_messageInfo_NftMetaSelectResponse.Size(m)
}
func (m *NftMetaSelectResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NftMetaSelectResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NftMetaSelectResponse proto.InternalMessageInfo

func (m *NftMetaSelectResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NftMetaSelectResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NftMetaSelectResponse) GetData() []*NftImage {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*RealRegisterRequest)(nil), "nft.RealRegisterRequest")
	proto.RegisterType((*RealRegisterResponse)(nil), "nft.RealRegisterResponse")
	proto.RegisterType((*DigitalImageUploadRequest)(nil), "nft.DigitalImageUploadRequest")
	proto.RegisterType((*DigitalImageUploadResponse)(nil), "nft.DigitalImageUploadResponse")
	proto.RegisterType((*NftPublishDigitalRequest)(nil), "nft.NftPublishDigitalRequest")
	proto.RegisterType((*NftPublishDigitalResponse)(nil), "nft.NftPublishDigitalResponse")
	proto.RegisterType((*NftSeriesClaimRequest)(nil), "nft.NftSeriesClaimRequest")
	proto.RegisterType((*NftSeriesClaimResponse)(nil), "nft.NftSeriesClaimResponse")
	proto.RegisterType((*NftSeriesListRequest)(nil), "nft.NftSeriesListRequest")
	proto.RegisterType((*NftInfo)(nil), "nft.NftInfo")
	proto.RegisterType((*NftSeriesListResponse)(nil), "nft.NftSeriesListResponse")
	proto.RegisterType((*NftSearchResultRequest)(nil), "nft.NftSearchResultRequest")
	proto.RegisterType((*NftSearchResultResponse)(nil), "nft.NftSearchResultResponse")
	proto.RegisterType((*NftPointQueryRequest)(nil), "nft.NftPointQueryRequest")
	proto.RegisterType((*NftPointQueryResponse)(nil), "nft.NftPointQueryResponse")
	proto.RegisterType((*NftInfoRequest)(nil), "nft.NftInfoRequest")
	proto.RegisterType((*NftInfoResponse)(nil), "nft.NftInfoResponse")
	proto.RegisterType((*NftBuyRequest)(nil), "nft.NftBuyRequest")
	proto.RegisterType((*NftBuyResponse)(nil), "nft.NftBuyResponse")
	proto.RegisterType((*NftStatusRequest)(nil), "nft.NftStatusRequest")
	proto.RegisterType((*NftStatusResponse)(nil), "nft.NftStatusResponse")
	proto.RegisterType((*UserOpenidRequest)(nil), "nft.UserOpenidRequest")
	proto.RegisterType((*UserOpenidResponse)(nil), "nft.UserOpenidResponse")
	proto.RegisterType((*NftCreateRequest)(nil), "nft.NftCreateRequest")
	proto.RegisterType((*NftResponse)(nil), "nft.NftResponse")
	proto.RegisterType((*NftUpdateRequest)(nil), "nft.NftUpdateRequest")
	proto.RegisterType((*NftDeleteRequest)(nil), "nft.NftDeleteRequest")
	proto.RegisterType((*NftRuiPengListRequest)(nil), "nft.NftRuiPengListRequest")
	proto.RegisterType((*NftRuiPengListResponse)(nil), "nft.NftRuiPengListResponse")
	proto.RegisterType((*RuiPengList)(nil), "nft.RuiPengList")
	proto.RegisterType((*UserBuyNftRequest)(nil), "nft.UserBuyNftRequest")
	proto.RegisterType((*UserBuyNftResponse)(nil), "nft.UserBuyNftResponse")
	proto.RegisterType((*NftRuiPengDetailRequest)(nil), "nft.NftRuiPengDetailRequest")
	proto.RegisterType((*NftDetail)(nil), "nft.NftDetail")
	proto.RegisterType((*NftRuiPengDetailResponse)(nil), "nft.NftRuiPengDetailResponse")
	proto.RegisterType((*UserNftPayRequest)(nil), "nft.UserNftPayRequest")
	proto.RegisterType((*UserNftPayResponse)(nil), "nft.UserNftPayResponse")
	proto.RegisterType((*NftPayResultPickRequest)(nil), "nft.NftPayResultPickRequest")
	proto.RegisterType((*UserAuthOpenidRequest)(nil), "nft.UserAuthOpenidRequest")
	proto.RegisterType((*UserAuthOpenidResponse)(nil), "nft.UserAuthOpenidResponse")
	proto.RegisterType((*ChangeUserIdRequest)(nil), "nft.ChangeUserIdRequest")
	proto.RegisterType((*ChangeUserIdResponse)(nil), "nft.ChangeUserIdResponse")
	proto.RegisterType((*NftSelectRequest)(nil), "nft.NftSelectRequest")
	proto.RegisterType((*NftSelectPickRequest)(nil), "nft.NftSelectPickRequest")
	proto.RegisterType((*NftCostSelectRequest)(nil), "nft.NftCostSelectRequest")
	proto.RegisterType((*NftCoupon)(nil), "nft.NftCoupon")
	proto.RegisterType((*Coupon)(nil), "nft.Coupon")
	proto.RegisterType((*NftCostSelectResponse)(nil), "nft.NftCostSelectResponse")
	proto.RegisterType((*CalcOrderCostRequest)(nil), "nft.CalcOrderCostRequest")
	proto.RegisterType((*Cost)(nil), "nft.Cost")
	proto.RegisterType((*CalcOrderCostResponse)(nil), "nft.CalcOrderCostResponse")
	proto.RegisterType((*VenueListRequest)(nil), "nft.VenueListRequest")
	proto.RegisterType((*VenueInfo)(nil), "nft.VenueInfo")
	proto.RegisterType((*VenueListResponse)(nil), "nft.VenueListResponse")
	proto.RegisterType((*VenueDetailRequest)(nil), "nft.VenueDetailRequest")
	proto.RegisterType((*GoodsSku)(nil), "nft.GoodsSku")
	proto.RegisterType((*Goods)(nil), "nft.Goods")
	proto.RegisterType((*VenueDetailResponse)(nil), "nft.VenueDetailResponse")
	proto.RegisterType((*ShareUserCollectRequest)(nil), "nft.ShareUserCollectRequest")
	proto.RegisterType((*VenueMessageSubscribeRequest)(nil), "nft.VenueMessageSubscribeRequest")
	proto.RegisterType((*SendSubcribeMessageRequest)(nil), "nft.SendSubcribeMessageRequest")
	proto.RegisterType((*CouponPickRequest)(nil), "nft.CouponPickRequest")
	proto.RegisterType((*FindDigitalUserNftRequest)(nil), "nft.FindDigitalUserNftRequest")
	proto.RegisterType((*NftSetStockRequest)(nil), "nft.NftSetStockRequest")
	proto.RegisterType((*NftMetaSelectRequest)(nil), "nft.NftMetaSelectRequest")
	proto.RegisterType((*NftImage)(nil), "nft.NftImage")
	proto.RegisterType((*NftMetaSelectResponse)(nil), "nft.NftMetaSelectResponse")
}

func init() { proto.RegisterFile("nft/digital.proto", fileDescriptor_cde888c79920ab7f) }

var fileDescriptor_cde888c79920ab7f = []byte{
	// 2765 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xcd, 0x6f, 0x1c, 0x49,
	0x15, 0x57, 0xcf, 0xf7, 0xbc, 0xb1, 0xbd, 0x4e, 0xfb, 0x6b, 0x3c, 0x89, 0x13, 0xa7, 0x77, 0xc5,
	0x46, 0x80, 0x12, 0x11, 0x3e, 0xb5, 0xec, 0x22, 0x36, 0xb6, 0x12, 0x06, 0x65, 0x1d, 0xef, 0x4c,
	0xbc, 0xa0, 0x8d, 0x60, 0x68, 0x4f, 0x57, 0x7b, 0x5a, 0xd3, 0xd3, 0x3d, 0xe9, 0x8f, 0x80, 0x0f,
	0x48, 0x1c, 0x90, 0x10, 0x17, 0x0e, 0x5c, 0x10, 0x97, 0x95, 0xf6, 0xc0, 0xff, 0x80, 0xb4, 0x37,
	0xae, 0xfc, 0x1b, 0xfc, 0x0d, 0xec, 0x15, 0xd5, 0xab, 0xd7, 0xdd, 0xd5, 0x5f, 0xe3, 0xa4, 0x63,
	0x71, 0x9b, 0x7a, 0xd5, 0xf5, 0xea, 0x7d, 0xd7, 0xaf, 0x5e, 0x0d, 0xdc, 0x70, 0xcc, 0xe0, 0x81,
	0x61, 0x5d, 0x58, 0x81, 0x6e, 0xdf, 0x5f, 0x7a, 0x6e, 0xe0, 0xaa, 0x75, 0xc7, 0x0c, 0xb4, 0x7f,
	0x29, 0xb0, 0x35, 0x62, 0xba, 0x3d, 0x62, 0x17, 0x96, 0x1f, 0x30, 0x6f, 0xc4, 0x5e, 0x86, 0xcc,
	0x0f, 0xd4, 0x3b, 0xd0, 0x5b, 0x32, 0xcf, 0x77, 0x9d, 0x89, 0xa3, 0x2f, 0x58, 0x5f, 0x39, 0x54,
	0xee, 0x75, 0x47, 0x20, 0x48, 0x27, 0xfa, 0x82, 0xa9, 0xbb, 0xd0, 0x5a, 0xb8, 0xe7, 0x96, 0xcd,
	0xfa, 0x35, 0x9c, 0xa3, 0x91, 0xba, 0x07, 0x6d, 0xcb, 0x98, 0x4c, 0x75, 0xcf, 0xe8, 0xd7, 0xc5,
	0x84, 0x65, 0x1c, 0xe9, 0x9e, 0xc1, 0x17, 0xb8, 0x4b, 0xe6, 0x58, 0x46, 0xbf, 0x21, 0xe8, 0x62,
	0xa4, 0x1e, 0xc2, 0x9a, 0x3f, 0xf5, 0x16, 0x93, 0xd0, 0x67, 0xde, 0xc4, 0x32, 0xfa, 0x4d, 0xb1,
	0x15, 0xa7, 0x9d, 0xf9, 0xcc, 0x1b, 0x1a, 0x5c, 0x96, 0x85, 0xe5, 0x58, 0x13, 0x5a, 0xde, 0x12,
	0x1f, 0x70, 0xd2, 0x33, 0xa4, 0x68, 0x21, 0x6c, 0xa7, 0x75, 0xf0, 0x97, 0xae, 0xe3, 0x33, 0x55,
	0x85, 0xc6, 0xd4, 0x35, 0x84, 0xf4, 0xcd, 0x11, 0xfe, 0x56, 0xfb, 0xd0, 0x5e, 0x30, 0xdf, 0xd7,
	0x2f, 0x22, 0xc1, 0xa3, 0xa1, 0xfa, 0x00, 0xb6, 0x48, 0x06, 0xe6, 0x04, 0x96, 0x69, 0x4d, 0xf5,
	0xc0, 0x72, 0x1d, 0xd2, 0x42, 0x0d, 0x51, 0x16, 0x79, 0x46, 0xb3, 0x60, 0xff, 0x58, 0x58, 0x74,
	0xb8, 0xd0, 0x2f, 0xd8, 0xd9, 0xd2, 0x76, 0x75, 0x23, 0x32, 0xe0, 0x4d, 0xe8, 0x9a, 0x96, 0xcd,
	0x26, 0x4b, 0x3d, 0x98, 0x91, 0xf9, 0x3a, 0x9c, 0x70, 0xaa, 0x07, 0xb3, 0xb2, 0xad, 0x6a, 0xa5,
	0x5b, 0x5d, 0xc0, 0xa0, 0x68, 0xab, 0x4a, 0x7a, 0xde, 0x84, 0xae, 0xc5, 0x99, 0x4c, 0x42, 0xcf,
	0x26, 0xed, 0x3a, 0x48, 0x38, 0xf3, 0x6c, 0xed, 0x1f, 0x75, 0xe8, 0x9f, 0x98, 0xc1, 0x69, 0x78,
	0x6e, 0x5b, 0xfe, 0x8c, 0xf6, 0x8c, 0x74, 0xda, 0x85, 0x96, 0x1e, 0x06, 0x33, 0xd7, 0x23, 0x85,
	0x68, 0xc4, 0xf7, 0xc7, 0x28, 0x11, 0x1b, 0xe1, 0x6f, 0x75, 0x13, 0xea, 0x09, 0x7f, 0xfe, 0x93,
	0xbb, 0xd1, 0xb0, 0xfc, 0xa5, 0xad, 0x5f, 0xe2, 0xce, 0x22, 0x0a, 0x80, 0x48, 0x67, 0x9e, 0xcd,
	0xd9, 0x18, 0xcc, 0x9f, 0x52, 0x04, 0xe0, 0x6f, 0x4e, 0x33, 0x6d, 0xfd, 0x82, 0x9c, 0x8e, 0xbf,
	0xcb, 0xac, 0xd7, 0x2e, 0xb3, 0x9e, 0xfa, 0x2e, 0xac, 0x2f, 0x85, 0x42, 0x93, 0xa9, 0x1b, 0x3a,
	0x41, 0xbf, 0x83, 0x86, 0x5a, 0x23, 0xe2, 0x11, 0xa7, 0x71, 0xb3, 0xf8, 0xcc, 0xb3, 0x98, 0xcf,
	0x83, 0xb0, 0x2b, 0xcc, 0x22, 0x08, 0x43, 0x43, 0xfd, 0x36, 0xa8, 0x34, 0x79, 0xce, 0x2e, 0x2c,
	0x67, 0x62, 0x39, 0x06, 0xfb, 0x5d, 0x1f, 0x90, 0xcd, 0xa6, 0x98, 0x79, 0xc4, 0x27, 0x86, 0x9c,
	0xce, 0x35, 0xf5, 0x99, 0x6d, 0x4f, 0xfc, 0x40, 0x0f, 0x42, 0xbf, 0xdf, 0xc3, 0xcf, 0x80, 0x93,
	0xc6, 0x48, 0x51, 0x0f, 0x00, 0x47, 0x24, 0xcd, 0x1a, 0xce, 0x77, 0x39, 0x45, 0x88, 0x72, 0x17,
	0xd6, 0x96, 0xfa, 0x74, 0xce, 0x7d, 0x14, 0x5c, 0x2e, 0x59, 0x7f, 0x1d, 0xa5, 0xe9, 0x11, 0xed,
	0xf9, 0xe5, 0x92, 0x69, 0xe7, 0xb0, 0x5f, 0xe0, 0xa6, 0x4a, 0xf1, 0xb0, 0x07, 0xed, 0x40, 0xf7,
	0xe7, 0x5c, 0x6d, 0xca, 0x58, 0x3e, 0x1c, 0x1a, 0xda, 0x57, 0x0a, 0xec, 0x9c, 0x98, 0xc1, 0x18,
	0xd5, 0x3b, 0xb2, 0x75, 0x6b, 0x21, 0x55, 0x07, 0x32, 0x87, 0x5c, 0x1d, 0x04, 0x09, 0xab, 0xc3,
	0x1d, 0xe8, 0x05, 0x6e, 0xa0, 0x47, 0x1a, 0xd6, 0x84, 0x05, 0x90, 0x14, 0x5b, 0x7b, 0xea, 0xbe,
	0x62, 0x9e, 0x1c, 0x84, 0x48, 0x90, 0x03, 0xa1, 0x21, 0x05, 0x42, 0x89, 0xd3, 0x9b, 0xa5, 0x29,
	0x33, 0x81, 0xdd, 0xac, 0xf0, 0xd7, 0x6b, 0x9e, 0xbf, 0x2a, 0xb0, 0x1d, 0xef, 0xf0, 0xd4, 0xf2,
	0x03, 0x29, 0x4d, 0x5c, 0xd3, 0xf4, 0x59, 0x80, 0x3b, 0xd4, 0x47, 0x34, 0x52, 0xb7, 0xa1, 0x69,
	0x5b, 0x0b, 0x4b, 0x98, 0xa3, 0x3e, 0x12, 0x83, 0x74, 0xdc, 0xd5, 0x33, 0x71, 0x57, 0xa2, 0x75,
	0xa3, 0x54, 0xeb, 0xaf, 0x6b, 0xd0, 0x3e, 0x31, 0x83, 0xa1, 0x63, 0xba, 0xea, 0x0e, 0xb4, 0x1c,
	0x33, 0xe0, 0x6c, 0x85, 0x83, 0x9a, 0x8e, 0x19, 0x0c, 0x8d, 0x38, 0xa5, 0x6a, 0x52, 0x4a, 0x25,
	0x99, 0x5d, 0x2f, 0xcc, 0xec, 0x46, 0x3e, 0xb3, 0x9b, 0xa5, 0x99, 0xdd, 0x2a, 0xcd, 0xec, 0xb6,
	0xe4, 0xd0, 0x4c, 0x0c, 0x75, 0x72, 0x31, 0xb4, 0x32, 0x21, 0x33, 0x29, 0x06, 0xb9, 0x14, 0xbb,
	0x07, 0x94, 0x97, 0x13, 0x11, 0x88, 0x4e, 0xb8, 0xa0, 0x44, 0xdc, 0x10, 0xf4, 0xe7, 0x9c, 0x7c,
	0x12, 0x2e, 0xd4, 0x6f, 0xc0, 0x3b, 0xd1, 0x3e, 0x3c, 0x7b, 0xf9, 0x6e, 0x22, 0x23, 0xd7, 0x69,
	0x37, 0x4e, 0x1d, 0x1a, 0x99, 0xa4, 0x5d, 0xcf, 0x24, 0xad, 0xf6, 0x47, 0x39, 0x5b, 0x44, 0x38,
	0x54, 0x8a, 0xb7, 0x6d, 0x68, 0xa2, 0xc4, 0xe8, 0x89, 0xe6, 0x48, 0x0c, 0xd4, 0xf7, 0xa1, 0x83,
	0xbe, 0x74, 0x4c, 0xb7, 0xdf, 0x38, 0xac, 0xdf, 0xeb, 0x3d, 0x5c, 0xbb, 0xef, 0x98, 0xc1, 0x7d,
	0xf2, 0xf5, 0xa8, 0xed, 0x88, 0x1f, 0xda, 0x90, 0xc2, 0x5e, 0xf7, 0xa6, 0xb3, 0x11, 0xf3, 0x43,
	0x3b, 0x0e, 0x4b, 0x29, 0x90, 0x15, 0x39, 0x90, 0xf1, 0x28, 0x67, 0xc1, 0xcc, 0x35, 0xe2, 0xa3,
	0x1c, 0x47, 0xda, 0x0b, 0xd8, 0xcb, 0xb1, 0xaa, 0xa4, 0x12, 0x77, 0xbf, 0x1e, 0xe8, 0x14, 0x5b,
	0xf8, 0x5b, 0x7b, 0x82, 0xc9, 0x73, 0xea, 0x5a, 0x4e, 0xf0, 0x69, 0xc8, 0xbc, 0xcb, 0x48, 0xca,
	0x92, 0x88, 0x57, 0x4a, 0x23, 0xfe, 0x05, 0x9a, 0x5d, 0x66, 0x54, 0xd5, 0xec, 0xc2, 0xb1, 0x64,
	0x76, 0x1c, 0x68, 0xef, 0xc3, 0x46, 0x64, 0x61, 0x92, 0xaf, 0x38, 0xa9, 0xb4, 0x19, 0xbc, 0x13,
	0x7f, 0x58, 0x69, 0x7f, 0xd9, 0xc1, 0x5c, 0x84, 0x52, 0x07, 0xff, 0x5d, 0x81, 0xf5, 0x13, 0x33,
	0x78, 0x14, 0x5e, 0xae, 0x16, 0xe9, 0x8d, 0x41, 0x06, 0xcf, 0x29, 0x7d, 0xb9, 0xb4, 0x2f, 0x27,
	0xfe, 0xd4, 0xf5, 0x18, 0x19, 0x02, 0x90, 0x34, 0xe6, 0x14, 0xfe, 0x81, 0x6b, 0x9a, 0xcc, 0xa3,
	0x14, 0x68, 0x88, 0x0f, 0x90, 0x24, 0x72, 0xe0, 0x17, 0x68, 0x2e, 0x14, 0xed, 0x7a, 0x6b, 0xed,
	0x17, 0x0a, 0x6c, 0xf2, 0x58, 0xc4, 0xdc, 0xbe, 0x6e, 0xbd, 0xef, 0xc2, 0x5a, 0xe0, 0xe9, 0x8e,
	0x1f, 0x15, 0x13, 0xa1, 0x78, 0x0f, 0x69, 0x54, 0x4d, 0xf8, 0x79, 0x86, 0x9f, 0x2c, 0x3d, 0x6b,
	0xca, 0x22, 0xcd, 0x91, 0x74, 0xca, 0x29, 0xda, 0xe7, 0x70, 0x43, 0x92, 0xef, 0x7a, 0x95, 0x3f,
	0x86, 0x1b, 0x1c, 0x09, 0x0b, 0xb0, 0x5b, 0x39, 0x4f, 0x9e, 0x83, 0x2a, 0x73, 0xa9, 0x24, 0x62,
	0x0e, 0xd4, 0x69, 0xff, 0x15, 0x8e, 0x39, 0xf2, 0x98, 0x1e, 0xb0, 0x48, 0xb6, 0xe8, 0xd4, 0x50,
	0xa4, 0x53, 0x23, 0x85, 0x3a, 0x6b, 0x69, 0xd4, 0x19, 0x9f, 0x0f, 0xf5, 0xab, 0x0f, 0xfc, 0xd2,
	0xa3, 0x8f, 0x67, 0xb0, 0xf0, 0x0e, 0x3f, 0x99, 0x6a, 0x23, 0x31, 0x48, 0x9f, 0x22, 0xad, 0xcc,
	0x29, 0xb2, 0x0b, 0x2d, 0x83, 0x05, 0xba, 0x65, 0xd3, 0xc9, 0x44, 0x23, 0x1e, 0x11, 0x74, 0x57,
	0x12, 0x00, 0x4c, 0xe0, 0xc5, 0x1e, 0xd1, 0x10, 0x80, 0xfd, 0x18, 0x7a, 0x27, 0x66, 0xc5, 0x82,
	0xa8, 0x7d, 0x51, 0x43, 0xab, 0x9d, 0x2d, 0x0d, 0xc9, 0x6a, 0x1b, 0x50, 0xa3, 0x50, 0x6e, 0x8e,
	0x6a, 0x96, 0x51, 0x88, 0xaa, 0x57, 0x61, 0xf7, 0x6b, 0x81, 0x4d, 0x89, 0x15, 0x5b, 0xb2, 0x15,
	0x77, 0xa1, 0x45, 0xc9, 0xd1, 0x46, 0xf9, 0x68, 0x94, 0xb6, 0x6e, 0xa7, 0xd4, 0xba, 0xdd, 0x95,
	0xd6, 0x85, 0xbc, 0x75, 0x35, 0xb4, 0xcf, 0x31, 0xb3, 0x59, 0xa9, 0x7d, 0xb4, 0x3d, 0x2c, 0xfc,
	0xa3, 0xd0, 0x3a, 0x65, 0xce, 0x85, 0x84, 0xbf, 0x34, 0x1b, 0x8f, 0xc0, 0xd4, 0x44, 0xa5, 0x68,
	0x7f, 0x2f, 0x3e, 0xb6, 0xf8, 0x79, 0xbb, 0x89, 0xe5, 0x58, 0xe6, 0x2a, 0x0e, 0xb2, 0xaf, 0x15,
	0xe8, 0x49, 0xd4, 0xeb, 0x09, 0xfe, 0xd8, 0x0b, 0x8d, 0xd2, 0x58, 0x6e, 0x66, 0xac, 0xfd, 0x7d,
	0xd8, 0x8d, 0x2e, 0x39, 0x19, 0x67, 0x8b, 0xa8, 0xdf, 0xa1, 0xd9, 0x8c, 0xbf, 0xdf, 0x22, 0x05,
	0xfe, 0xad, 0x88, 0xc2, 0xf4, 0x28, 0xbc, 0xc4, 0x54, 0xa8, 0x56, 0x98, 0x56, 0x08, 0x5e, 0x5b,
	0x25, 0xf8, 0x4a, 0xdc, 0xbc, 0xc2, 0x7e, 0x36, 0x29, 0xd4, 0x44, 0x85, 0x3a, 0x9c, 0x80, 0xda,
	0xfc, 0x4a, 0xd4, 0xc7, 0x48, 0x99, 0x4a, 0x11, 0xb3, 0x0f, 0x1d, 0xd7, 0x33, 0x98, 0x37, 0xf1,
	0xa3, 0xbe, 0x41, 0x1b, 0xc7, 0x63, 0x7e, 0x83, 0xdf, 0x4b, 0x82, 0xf2, 0x18, 0x6d, 0x2c, 0xb5,
	0x0a, 0x12, 0x4d, 0x94, 0xd7, 0xbb, 0x01, 0x94, 0xb7, 0x0a, 0xfe, 0x5c, 0x83, 0x2e, 0xe6, 0x0e,
	0xba, 0x31, 0x71, 0xaf, 0x92, 0x72, 0x6f, 0x6c, 0xa0, 0x9a, 0x6c, 0xa0, 0x72, 0x57, 0xd4, 0x5f,
	0xdb, 0x15, 0x8d, 0x8c, 0x02, 0xd1, 0x75, 0x43, 0xd8, 0x5b, 0x5c, 0x37, 0xa2, 0x1c, 0x69, 0x49,
	0x39, 0x52, 0x74, 0x47, 0xb8, 0x3a, 0x08, 0xd3, 0xa9, 0xd5, 0xcd, 0x74, 0x33, 0x6c, 0x6c, 0x66,
	0x64, 0x8c, 0x5e, 0xc9, 0xb3, 0x9a, 0x04, 0x61, 0x7b, 0x0f, 0x37, 0x22, 0x68, 0x46, 0x3c, 0x45,
	0x25, 0x98, 0x88, 0x74, 0xe0, 0x68, 0x54, 0xaf, 0x8c, 0x67, 0x53, 0x31, 0x54, 0x4b, 0xc7, 0xd0,
	0x67, 0x22, 0x44, 0xa3, 0x0d, 0xae, 0x0d, 0x8b, 0xff, 0x16, 0x63, 0x53, 0xf0, 0x0c, 0xed, 0xe0,
	0xd4, 0x9a, 0xce, 0x2b, 0x8b, 0x9f, 0x80, 0xb2, 0x9a, 0x0c, 0xca, 0xe2, 0x78, 0xab, 0x4b, 0xf1,
	0xa6, 0x7d, 0x0b, 0x76, 0xb8, 0x42, 0x1f, 0x87, 0xc1, 0x2c, 0x8d, 0x6e, 0x64, 0x9d, 0xba, 0x42,
	0x27, 0xed, 0xd7, 0xb0, 0x9b, 0xfd, 0xb8, 0x92, 0x05, 0x92, 0x46, 0x64, 0x5d, 0x6e, 0x44, 0x6a,
	0xbf, 0x84, 0xad, 0xa3, 0x99, 0xee, 0x5c, 0x30, 0xd1, 0x76, 0x8c, 0x44, 0xc9, 0xf6, 0x27, 0x95,
	0xab, 0xfa, 0x93, 0xb5, 0x5c, 0x7f, 0xf2, 0x6f, 0x0a, 0x6c, 0xa7, 0x59, 0xff, 0x5f, 0x1a, 0x94,
	0x39, 0xd1, 0x1b, 0x59, 0xd1, 0xb5, 0xdf, 0x08, 0x58, 0xcd, 0x6c, 0x36, 0xad, 0x5e, 0xc0, 0x53,
	0xe9, 0x5f, 0x4b, 0xa7, 0xbf, 0xf6, 0x65, 0xd4, 0x25, 0xe1, 0x5b, 0xbc, 0x55, 0x64, 0xad, 0xda,
	0x26, 0xa7, 0x6a, 0x3d, 0xe7, 0xa5, 0x7d, 0xe8, 0xbc, 0x62, 0x4e, 0xc8, 0x22, 0x43, 0x34, 0x47,
	0x6d, 0x1c, 0x0f, 0x0d, 0xed, 0xf7, 0x28, 0xe2, 0x91, 0xeb, 0xbf, 0xad, 0x25, 0xb2, 0x52, 0xd4,
	0x72, 0x52, 0xa8, 0xd0, 0xc0, 0x4a, 0x26, 0xee, 0x18, 0xf8, 0x5b, 0xfb, 0x8b, 0x82, 0x15, 0xfb,
	0xc8, 0x0d, 0x97, 0xae, 0xc3, 0x31, 0xf6, 0x9c, 0x5d, 0x52, 0x48, 0xf0, 0x9f, 0xd8, 0x11, 0xb0,
	0x82, 0xb8, 0xd3, 0x2e, 0x06, 0x85, 0xb0, 0xe1, 0x5d, 0x68, 0x4d, 0x91, 0x0b, 0x6a, 0xd8, 0x7b,
	0xd8, 0xc3, 0x3a, 0x25, 0x18, 0x8f, 0x68, 0x4a, 0x3d, 0x84, 0xba, 0x63, 0x06, 0x58, 0x8f, 0xf3,
	0x95, 0x0c, 0x1f, 0x05, 0xbe, 0x52, 0xa0, 0x45, 0xd2, 0x60, 0x9f, 0x8e, 0xff, 0x9a, 0xc4, 0xd8,
	0xab, 0x23, 0x08, 0x43, 0x43, 0xbd, 0x0d, 0x20, 0x7e, 0x9f, 0x24, 0x38, 0x55, 0xa2, 0x24, 0xf3,
	0xc7, 0x89, 0xa0, 0x12, 0x85, 0xcf, 0x8b, 0x6d, 0x9e, 0x5b, 0x71, 0x8f, 0x49, 0xa2, 0xa8, 0x87,
	0xd0, 0x13, 0xa3, 0xd3, 0x18, 0xd7, 0x37, 0x47, 0x32, 0x29, 0xd5, 0x1e, 0xa6, 0xc3, 0x45, 0xb3,
	0x10, 0x17, 0xca, 0xde, 0x7c, 0xcb, 0x8a, 0x5f, 0x97, 0xed, 0x44, 0xc6, 0x14, 0x85, 0x73, 0x0c,
	0xdb, 0x47, 0xba, 0x3d, 0x7d, 0xc6, 0xeb, 0x33, 0xdf, 0xf0, 0xf5, 0x6b, 0xc6, 0x8a, 0x2a, 0x7f,
	0x1f, 0x1a, 0x9c, 0x57, 0xac, 0x9b, 0x92, 0x3e, 0x38, 0x31, 0x7c, 0x6a, 0x52, 0xf8, 0x18, 0xb0,
	0x93, 0x11, 0xa2, 0x92, 0xbe, 0x07, 0xa9, 0x13, 0xae, 0x4b, 0x91, 0x13, 0xc3, 0x5c, 0x15, 0x36,
	0x3f, 0xe3, 0xe9, 0x22, 0x03, 0xed, 0x7f, 0x2a, 0xd0, 0x45, 0x22, 0xb6, 0x1b, 0xe5, 0x04, 0x53,
	0x52, 0x09, 0xa6, 0x1e, 0x00, 0x88, 0x29, 0x3c, 0xf5, 0x45, 0x20, 0x74, 0x91, 0x12, 0x75, 0x8b,
	0x69, 0x25, 0x3f, 0xb8, 0xa3, 0x40, 0x10, 0x8b, 0x17, 0xf4, 0x64, 0xc1, 0x8b, 0xeb, 0x24, 0xe0,
	0x71, 0x42, 0xc0, 0x97, 0x13, 0x30, 0x4a, 0x0e, 0x00, 0xa6, 0xb6, 0xeb, 0x33, 0x31, 0x2b, 0x20,
	0x45, 0x17, 0x29, 0x38, 0xbd, 0x0d, 0x4d, 0x7e, 0x59, 0x61, 0x74, 0x73, 0x11, 0x03, 0x8d, 0xc1,
	0x0d, 0x49, 0x9d, 0x6b, 0x0b, 0x90, 0xd8, 0x1a, 0x64, 0xb5, 0x4f, 0x41, 0x45, 0x52, 0x1a, 0xf0,
	0xad, 0xb0, 0xd4, 0x95, 0x15, 0x44, 0xfb, 0x10, 0x3a, 0x4f, 0x5c, 0xd7, 0xf0, 0xc7, 0xf3, 0x90,
	0x1f, 0xb6, 0xfe, 0x3c, 0x4c, 0xd8, 0x34, 0xfd, 0x79, 0x28, 0x82, 0x8b, 0x93, 0xa5, 0xdb, 0x63,
	0xdb, 0x9f, 0x87, 0xdc, 0xd4, 0xda, 0x7f, 0x14, 0x68, 0xe2, 0x72, 0xfe, 0xd1, 0x05, 0xff, 0x21,
	0x09, 0x81, 0x63, 0xe1, 0x2e, 0x31, 0x25, 0x71, 0xe8, 0x22, 0x25, 0x72, 0x17, 0xad, 0x44, 0x77,
	0x51, 0x5e, 0x8b, 0xc5, 0x0b, 0x11, 0x4a, 0xb4, 0x5e, 0xba, 0x8e, 0x8a, 0xf5, 0xc7, 0x74, 0xb9,
	0x11, 0x1e, 0x69, 0x4a, 0x1e, 0xc1, 0x8e, 0xee, 0x4c, 0xf7, 0x18, 0x35, 0x97, 0x5a, 0xd4, 0xd1,
	0xe5, 0x24, 0xf1, 0x64, 0xf0, 0x4d, 0x10, 0x3c, 0x26, 0xfe, 0x3c, 0xec, 0xb7, 0xd1, 0xe8, 0xeb,
	0x68, 0xf4, 0xc8, 0x1c, 0x23, 0xa1, 0xd0, 0x78, 0x1e, 0x6a, 0x7f, 0x52, 0x60, 0x2b, 0x65, 0xf8,
	0x4a, 0x1e, 0xa6, 0x4a, 0x59, 0x2f, 0xad, 0x94, 0xea, 0x21, 0x34, 0x71, 0x4f, 0xea, 0xc9, 0x42,
	0x22, 0xcf, 0x48, 0x4c, 0x68, 0x2f, 0x61, 0x6f, 0xcc, 0x75, 0xe0, 0xde, 0x3b, 0x72, 0x6d, 0xf9,
	0x78, 0x79, 0x9b, 0x30, 0x48, 0xb9, 0xaf, 0x9e, 0x72, 0x9f, 0xf6, 0x02, 0x6e, 0xa1, 0xee, 0x9f,
	0x08, 0x35, 0xc6, 0xe1, 0xb9, 0x3f, 0xf5, 0xac, 0x73, 0x76, 0x2d, 0xe1, 0xf7, 0x43, 0x18, 0x8c,
	0x99, 0x63, 0x8c, 0xc3, 0x73, 0xe4, 0x49, 0x7b, 0x5c, 0xcd, 0x5a, 0x7b, 0x09, 0x37, 0xa8, 0x72,
	0x4b, 0x20, 0xe0, 0x3d, 0xd8, 0xa0, 0xe3, 0x65, 0x69, 0x4d, 0xe7, 0xc9, 0xaa, 0xb5, 0x69, 0xfc,
	0xe9, 0x6b, 0x59, 0x03, 0x9b, 0xe6, 0x73, 0x16, 0x81, 0x21, 0x31, 0xd0, 0x9e, 0xc2, 0xfe, 0x63,
	0xcb, 0x31, 0xe8, 0x79, 0x8c, 0xa0, 0x73, 0xe5, 0x06, 0xda, 0x13, 0x50, 0x11, 0xc8, 0x04, 0xe3,
	0xc0, 0x4d, 0x34, 0x58, 0x79, 0x79, 0xc3, 0x48, 0x77, 0xa7, 0x73, 0xaa, 0xd7, 0x62, 0xa0, 0x99,
	0x08, 0x37, 0x3e, 0x61, 0x81, 0x9e, 0x86, 0x1b, 0xf8, 0xc0, 0x40, 0xac, 0x7c, 0xe2, 0xd5, 0x8d,
	0x78, 0xf9, 0x6f, 0x7e, 0x13, 0xfc, 0x83, 0x02, 0x9d, 0x13, 0x33, 0x88, 0x4b, 0x68, 0xb9, 0x9c,
	0xf2, 0x93, 0x50, 0xf6, 0x8e, 0x56, 0x2f, 0xb8, 0xa3, 0xc9, 0x1d, 0xa6, 0xd4, 0x05, 0xac, 0x99,
	0xb9, 0x80, 0xcd, 0xf0, 0x2c, 0x96, 0x55, 0xad, 0x94, 0x88, 0x77, 0x53, 0xa5, 0x76, 0x3d, 0x6e,
	0x8c, 0xf3, 0x6d, 0x44, 0xa5, 0x7d, 0xf8, 0xe5, 0x36, 0x6c, 0x90, 0xa3, 0xc7, 0xcc, 0x7b, 0xc5,
	0xc1, 0xc1, 0x11, 0xac, 0xc9, 0x7f, 0x0b, 0x50, 0xfb, 0xa2, 0x83, 0x93, 0xff, 0xb7, 0xc3, 0x60,
	0xbf, 0x60, 0x86, 0x04, 0x3d, 0x03, 0x35, 0xff, 0xf2, 0xae, 0xde, 0xc6, 0x05, 0xa5, 0xaf, 0xff,
	0x83, 0x3b, 0xa5, 0xf3, 0xc4, 0x76, 0x84, 0xfd, 0xe2, 0xf4, 0xfb, 0xad, 0x7a, 0x10, 0x29, 0x56,
	0xf8, 0xfc, 0x3e, 0xb8, 0x5d, 0x36, 0x4d, 0x3c, 0x87, 0xd8, 0x7d, 0x97, 0x5e, 0x3c, 0xd5, 0x41,
	0xb4, 0x22, 0xff, 0x86, 0x3b, 0xb8, 0x59, 0x38, 0x47, 0xac, 0x1e, 0xe3, 0x1b, 0x43, 0xf2, 0x96,
	0xa5, 0xee, 0xa7, 0xbf, 0x96, 0x50, 0xc0, 0x60, 0x50, 0x34, 0x45, 0x7c, 0xbe, 0x97, 0xbc, 0x46,
	0x6e, 0xa5, 0x9e, 0x33, 0x68, 0xed, 0x76, 0x9a, 0x48, 0xab, 0x9e, 0xe2, 0x63, 0x8a, 0xfc, 0xf0,
	0xa4, 0x4a, 0xd2, 0xe6, 0x5e, 0xb6, 0x06, 0xb7, 0x8a, 0x27, 0x53, 0xba, 0x24, 0x0f, 0x44, 0x89,
	0x2e, 0xb9, 0xd7, 0xa7, 0x44, 0x97, 0x82, 0xf7, 0xa4, 0xef, 0x40, 0x4b, 0x3c, 0x6e, 0xa8, 0x6a,
	0xf4, 0x55, 0xf2, 0x08, 0x33, 0xd8, 0x4a, 0xd1, 0x68, 0xc9, 0x07, 0x08, 0xec, 0xe9, 0x0d, 0x61,
	0x27, 0x96, 0x52, 0x7e, 0xc5, 0x18, 0xec, 0x66, 0xc9, 0xb1, 0xe9, 0xba, 0x71, 0x63, 0x3d, 0x59,
	0x9b, 0x6a, 0xb4, 0x0f, 0x36, 0x23, 0x72, 0x66, 0x95, 0x68, 0x2c, 0x27, 0xab, 0x52, 0x8d, 0xe6,
	0xd2, 0x55, 0xa2, 0xdd, 0x9a, 0xac, 0x4a, 0xb5, 0x5f, 0x0b, 0x56, 0x89, 0x78, 0x93, 0x7b, 0x9f,
	0xb1, 0xf9, 0xf2, 0x5d, 0xd9, 0x24, 0xde, 0x8a, 0x1a, 0xb3, 0xcf, 0xf0, 0x1e, 0x9a, 0x6a, 0xd4,
	0xa8, 0xb7, 0x32, 0x0b, 0x52, 0x18, 0x6a, 0x70, 0x50, 0x32, 0x4b, 0x0c, 0x3f, 0x02, 0x48, 0x5e,
	0x3b, 0x54, 0x61, 0xe3, 0xdc, 0x23, 0xca, 0x60, 0x2f, 0x47, 0x4f, 0x54, 0x4b, 0xf7, 0x1a, 0x48,
	0xb5, 0xc2, 0x6e, 0x05, 0xa9, 0x56, 0xd2, 0x9c, 0x20, 0x49, 0x44, 0x5f, 0x51, 0x92, 0x24, 0xd5,
	0x35, 0x95, 0x24, 0xc9, 0x34, 0x20, 0x69, 0xb9, 0xe8, 0xcf, 0x48, 0xcb, 0x53, 0x5d, 0x26, 0x69,
	0x79, 0xa6, 0x39, 0xf4, 0x08, 0x0d, 0x9b, 0x6a, 0xed, 0x24, 0x86, 0x2d, 0xea, 0xf8, 0x14, 0xf8,
	0xf9, 0x08, 0xd6, 0xe4, 0xee, 0x05, 0xd5, 0xd1, 0x82, 0x5e, 0x09, 0xd5, 0xd1, 0xc2, 0x56, 0xc7,
	0xc7, 0x22, 0x15, 0xf0, 0x14, 0x90, 0x52, 0x41, 0x3e, 0x00, 0xaf, 0xf2, 0xa9, 0x48, 0xe4, 0xe4,
	0x62, 0x97, 0x24, 0x72, 0xee, 0xea, 0x9e, 0x24, 0x72, 0xc1, 0x3d, 0xf0, 0x43, 0x2a, 0x6e, 0x51,
	0x47, 0x42, 0x2e, 0x6e, 0x99, 0x2e, 0x45, 0x81, 0x35, 0x7e, 0x8a, 0x95, 0x3b, 0x61, 0xfb, 0xe6,
	0x1c, 0x1e, 0xc3, 0x7a, 0xea, 0xc2, 0x46, 0xab, 0x8b, 0x6e, 0x92, 0xa4, 0x47, 0xf1, 0xfd, 0xee,
	0x03, 0xba, 0x7d, 0x61, 0xea, 0xed, 0x24, 0xf7, 0x0f, 0x39, 0xeb, 0x76, 0xb3, 0xe4, 0x58, 0x8b,
	0x9e, 0x84, 0x8f, 0xd5, 0xbd, 0xe4, 0xb3, 0x74, 0x9a, 0xf5, 0xf3, 0x13, 0x49, 0x64, 0x65, 0x81,
	0x2d, 0x45, 0x56, 0x09, 0xde, 0x2d, 0xb0, 0xc4, 0x53, 0xd8, 0x29, 0x44, 0xaa, 0xea, 0xdd, 0x64,
	0xdb, 0x12, 0x14, 0x5b, 0xc0, 0xed, 0x27, 0xf0, 0x0e, 0xae, 0xa8, 0xea, 0x97, 0x9f, 0xc1, 0x56,
	0x01, 0xb4, 0x55, 0xc5, 0x59, 0x5e, 0x0e, 0x7a, 0x0b, 0x38, 0xfd, 0x20, 0x6a, 0x6c, 0xa0, 0x10,
	0xbb, 0x52, 0x17, 0x66, 0xb5, 0x04, 0xa7, 0xa0, 0xe6, 0x01, 0x2b, 0x81, 0x8d, 0x52, 0x24, 0xbb,
	0xf2, 0x00, 0xfe, 0x11, 0x3e, 0x53, 0x46, 0xa0, 0x95, 0xfc, 0x9c, 0x87, 0xb1, 0xc5, 0x51, 0x9a,
	0x82, 0x6e, 0x89, 0x2d, 0x73, 0xc8, 0x35, 0x91, 0xa0, 0x00, 0xe9, 0x7d, 0x04, 0x1b, 0x09, 0xf5,
	0xcd, 0x9d, 0xf2, 0x73, 0xc2, 0x02, 0x5c, 0x33, 0xc2, 0x02, 0x2b, 0xce, 0xef, 0x95, 0x48, 0xe0,
	0x51, 0xfb, 0xf3, 0xe6, 0xfd, 0x07, 0x8e, 0x19, 0x9c, 0xb7, 0xf0, 0x1f, 0xb0, 0xdf, 0xfd, 0x5f,
	0x00, 0x00, 0x00, 0xff, 0xff, 0xc7, 0x6d, 0x0d, 0x00, 0x16, 0x2b, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DigitalServiceClient is the client API for DigitalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DigitalServiceClient interface {
	//用户实名注册
	RealRegister(ctx context.Context, in *RealRegisterRequest, opts ...grpc.CallOption) (*RealRegisterResponse, error)
	//素材上传致信链
	DigitalImageUpload(ctx context.Context, in *DigitalImageUploadRequest, opts ...grpc.CallOption) (*DigitalImageUploadResponse, error)
	//发行数字藏品
	NftPublishDigital(ctx context.Context, in *NftPublishDigitalRequest, opts ...grpc.CallOption) (*NftPublishDigitalResponse, error)
	//发行数字藏品系列
	NftSeriesClaim(ctx context.Context, in *NftSeriesClaimRequest, opts ...grpc.CallOption) (*NftSeriesClaimResponse, error)
	//查询数字藏品列表(系列查询)
	NftSeriesList(ctx context.Context, in *NftSeriesListRequest, opts ...grpc.CallOption) (*NftSeriesListResponse, error)
	//查询数字藏品详细信息
	NftInfo(ctx context.Context, in *NftInfoRequest, opts ...grpc.CallOption) (*NftInfoResponse, error)
	//查询各类结果接口
	NftSearchResult(ctx context.Context, in *NftSearchResultRequest, opts ...grpc.CallOption) (*NftSearchResultResponse, error)
	//nft积分查询接口
	NftPointQuery(ctx context.Context, in *NftPointQueryRequest, opts ...grpc.CallOption) (*NftPointQueryResponse, error)
	//nft购买
	NftBuy(ctx context.Context, in *NftBuyRequest, opts ...grpc.CallOption) (*NftBuyResponse, error)
	//nft销售状态变更
	NftStatus(ctx context.Context, in *NftStatusRequest, opts ...grpc.CallOption) (*NftStatusResponse, error)
	//新增数字藏品到瑞鹏平台数据
	NftCreate(ctx context.Context, in *NftCreateRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//更新瑞鹏平台数据藏品
	NftUpdate(ctx context.Context, in *NftUpdateRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//删除瑞鹏平台数据藏品
	NftDelete(ctx context.Context, in *NftDeleteRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//展示瑞鹏平台数据藏品
	NftRuiPengList(ctx context.Context, in *NftRuiPengListRequest, opts ...grpc.CallOption) (*NftRuiPengListResponse, error)
	//瑞鹏平台数据藏品详情数据
	NftRuiPengDetail(ctx context.Context, in *NftRuiPengDetailRequest, opts ...grpc.CallOption) (*NftRuiPengDetailResponse, error)
	//判断用户是否获取openid
	UserOpenid(ctx context.Context, in *UserOpenidRequest, opts ...grpc.CallOption) (*UserOpenidResponse, error)
	//获取openid
	UserAuthOpenid(ctx context.Context, in *UserAuthOpenidRequest, opts ...grpc.CallOption) (*UserAuthOpenidResponse, error)
	//用户购买或免费领取数字藏品
	UserBuyNft(ctx context.Context, in *UserBuyNftRequest, opts ...grpc.CallOption) (*UserBuyNftResponse, error)
	//数字藏品订单支付
	UserNftPay(ctx context.Context, in *UserNftPayRequest, opts ...grpc.CallOption) (*UserNftPayResponse, error)
	//支付结果领取nft
	NftPayResultPick(ctx context.Context, in *NftPayResultPickRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//scrmUserId换userIdentification
	ChangeUserId(ctx context.Context, in *ChangeUserIdRequest, opts ...grpc.CallOption) (*ChangeUserIdResponse, error)
	//随机展示一款数字藏品
	NftSelect(ctx context.Context, in *NftSelectRequest, opts ...grpc.CallOption) (*NftRuiPengDetailResponse, error)
	//消费获取nft列表展示
	NftCostSelect(ctx context.Context, in *NftCostSelectRequest, opts ...grpc.CallOption) (*NftCostSelectResponse, error)
	//活动1领取nft
	NftSelectPick(ctx context.Context, in *NftSelectPickRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//消费活动领取nft列表
	NftCostSelectPick(ctx context.Context, in *NftSelectPickRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//支付成功页计算支付消费金额接口
	CalcOrderCost(ctx context.Context, in *CalcOrderCostRequest, opts ...grpc.CallOption) (*CalcOrderCostResponse, error)
	//场馆列表
	VenueList(ctx context.Context, in *VenueListRequest, opts ...grpc.CallOption) (*VenueListResponse, error)
	//场馆内容
	VenueDetail(ctx context.Context, in *VenueDetailRequest, opts ...grpc.CallOption) (*VenueDetailResponse, error)
	//分享用户收集
	ShareUserCollect(ctx context.Context, in *ShareUserCollectRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//抢nft资格消息订阅
	VenueMessageSubscribe(ctx context.Context, in *VenueMessageSubscribeRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//场馆活动领取nft
	VenueSelectPick(ctx context.Context, in *NftSelectPickRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//发送订阅消息
	SendSubcribeMessage(ctx context.Context, in *SendSubcribeMessageRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//领取优惠券接口
	CouponPick(ctx context.Context, in *CouponPickRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//测试假数据
	FindDigitalUserNft(ctx context.Context, in *FindDigitalUserNftRequest, opts ...grpc.CallOption) (*NftSeriesListResponse, error)
	//根据系列设置库存
	NftSetStock(ctx context.Context, in *NftSetStockRequest, opts ...grpc.CallOption) (*NftResponse, error)
	//元宇宙领取nft列表
	NftMetaSelect(ctx context.Context, in *NftMetaSelectRequest, opts ...grpc.CallOption) (*NftMetaSelectResponse, error)
	//元宇宙活动领取nft
	MetaSelectPick(ctx context.Context, in *NftSelectPickRequest, opts ...grpc.CallOption) (*NftResponse, error)
	// 查询该账户资产归属的系列列表
	NftSeriesResult(ctx context.Context, in *NftPointQueryRequest, opts ...grpc.CallOption) (*NftSearchResultResponse, error)
}

type digitalServiceClient struct {
	cc *grpc.ClientConn
}

func NewDigitalServiceClient(cc *grpc.ClientConn) DigitalServiceClient {
	return &digitalServiceClient{cc}
}

func (c *digitalServiceClient) RealRegister(ctx context.Context, in *RealRegisterRequest, opts ...grpc.CallOption) (*RealRegisterResponse, error) {
	out := new(RealRegisterResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/RealRegister", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) DigitalImageUpload(ctx context.Context, in *DigitalImageUploadRequest, opts ...grpc.CallOption) (*DigitalImageUploadResponse, error) {
	out := new(DigitalImageUploadResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/DigitalImageUpload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftPublishDigital(ctx context.Context, in *NftPublishDigitalRequest, opts ...grpc.CallOption) (*NftPublishDigitalResponse, error) {
	out := new(NftPublishDigitalResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftPublishDigital", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSeriesClaim(ctx context.Context, in *NftSeriesClaimRequest, opts ...grpc.CallOption) (*NftSeriesClaimResponse, error) {
	out := new(NftSeriesClaimResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftSeriesClaim", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSeriesList(ctx context.Context, in *NftSeriesListRequest, opts ...grpc.CallOption) (*NftSeriesListResponse, error) {
	out := new(NftSeriesListResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftSeriesList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftInfo(ctx context.Context, in *NftInfoRequest, opts ...grpc.CallOption) (*NftInfoResponse, error) {
	out := new(NftInfoResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSearchResult(ctx context.Context, in *NftSearchResultRequest, opts ...grpc.CallOption) (*NftSearchResultResponse, error) {
	out := new(NftSearchResultResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftSearchResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftPointQuery(ctx context.Context, in *NftPointQueryRequest, opts ...grpc.CallOption) (*NftPointQueryResponse, error) {
	out := new(NftPointQueryResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftPointQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftBuy(ctx context.Context, in *NftBuyRequest, opts ...grpc.CallOption) (*NftBuyResponse, error) {
	out := new(NftBuyResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftStatus(ctx context.Context, in *NftStatusRequest, opts ...grpc.CallOption) (*NftStatusResponse, error) {
	out := new(NftStatusResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftCreate(ctx context.Context, in *NftCreateRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftUpdate(ctx context.Context, in *NftUpdateRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftDelete(ctx context.Context, in *NftDeleteRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftRuiPengList(ctx context.Context, in *NftRuiPengListRequest, opts ...grpc.CallOption) (*NftRuiPengListResponse, error) {
	out := new(NftRuiPengListResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftRuiPengList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftRuiPengDetail(ctx context.Context, in *NftRuiPengDetailRequest, opts ...grpc.CallOption) (*NftRuiPengDetailResponse, error) {
	out := new(NftRuiPengDetailResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftRuiPengDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) UserOpenid(ctx context.Context, in *UserOpenidRequest, opts ...grpc.CallOption) (*UserOpenidResponse, error) {
	out := new(UserOpenidResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/UserOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) UserAuthOpenid(ctx context.Context, in *UserAuthOpenidRequest, opts ...grpc.CallOption) (*UserAuthOpenidResponse, error) {
	out := new(UserAuthOpenidResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/UserAuthOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) UserBuyNft(ctx context.Context, in *UserBuyNftRequest, opts ...grpc.CallOption) (*UserBuyNftResponse, error) {
	out := new(UserBuyNftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/UserBuyNft", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) UserNftPay(ctx context.Context, in *UserNftPayRequest, opts ...grpc.CallOption) (*UserNftPayResponse, error) {
	out := new(UserNftPayResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/UserNftPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftPayResultPick(ctx context.Context, in *NftPayResultPickRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftPayResultPick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) ChangeUserId(ctx context.Context, in *ChangeUserIdRequest, opts ...grpc.CallOption) (*ChangeUserIdResponse, error) {
	out := new(ChangeUserIdResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/ChangeUserId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSelect(ctx context.Context, in *NftSelectRequest, opts ...grpc.CallOption) (*NftRuiPengDetailResponse, error) {
	out := new(NftRuiPengDetailResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftSelect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftCostSelect(ctx context.Context, in *NftCostSelectRequest, opts ...grpc.CallOption) (*NftCostSelectResponse, error) {
	out := new(NftCostSelectResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftCostSelect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSelectPick(ctx context.Context, in *NftSelectPickRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftSelectPick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftCostSelectPick(ctx context.Context, in *NftSelectPickRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftCostSelectPick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) CalcOrderCost(ctx context.Context, in *CalcOrderCostRequest, opts ...grpc.CallOption) (*CalcOrderCostResponse, error) {
	out := new(CalcOrderCostResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/CalcOrderCost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) VenueList(ctx context.Context, in *VenueListRequest, opts ...grpc.CallOption) (*VenueListResponse, error) {
	out := new(VenueListResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/VenueList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) VenueDetail(ctx context.Context, in *VenueDetailRequest, opts ...grpc.CallOption) (*VenueDetailResponse, error) {
	out := new(VenueDetailResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/VenueDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) ShareUserCollect(ctx context.Context, in *ShareUserCollectRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/ShareUserCollect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) VenueMessageSubscribe(ctx context.Context, in *VenueMessageSubscribeRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/VenueMessageSubscribe", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) VenueSelectPick(ctx context.Context, in *NftSelectPickRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/VenueSelectPick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) SendSubcribeMessage(ctx context.Context, in *SendSubcribeMessageRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/SendSubcribeMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) CouponPick(ctx context.Context, in *CouponPickRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/CouponPick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) FindDigitalUserNft(ctx context.Context, in *FindDigitalUserNftRequest, opts ...grpc.CallOption) (*NftSeriesListResponse, error) {
	out := new(NftSeriesListResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/FindDigitalUserNft", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSetStock(ctx context.Context, in *NftSetStockRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftSetStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftMetaSelect(ctx context.Context, in *NftMetaSelectRequest, opts ...grpc.CallOption) (*NftMetaSelectResponse, error) {
	out := new(NftMetaSelectResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftMetaSelect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) MetaSelectPick(ctx context.Context, in *NftSelectPickRequest, opts ...grpc.CallOption) (*NftResponse, error) {
	out := new(NftResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/MetaSelectPick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *digitalServiceClient) NftSeriesResult(ctx context.Context, in *NftPointQueryRequest, opts ...grpc.CallOption) (*NftSearchResultResponse, error) {
	out := new(NftSearchResultResponse)
	err := c.cc.Invoke(ctx, "/nft.DigitalService/NftSeriesResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DigitalServiceServer is the server API for DigitalService service.
type DigitalServiceServer interface {
	//用户实名注册
	RealRegister(context.Context, *RealRegisterRequest) (*RealRegisterResponse, error)
	//素材上传致信链
	DigitalImageUpload(context.Context, *DigitalImageUploadRequest) (*DigitalImageUploadResponse, error)
	//发行数字藏品
	NftPublishDigital(context.Context, *NftPublishDigitalRequest) (*NftPublishDigitalResponse, error)
	//发行数字藏品系列
	NftSeriesClaim(context.Context, *NftSeriesClaimRequest) (*NftSeriesClaimResponse, error)
	//查询数字藏品列表(系列查询)
	NftSeriesList(context.Context, *NftSeriesListRequest) (*NftSeriesListResponse, error)
	//查询数字藏品详细信息
	NftInfo(context.Context, *NftInfoRequest) (*NftInfoResponse, error)
	//查询各类结果接口
	NftSearchResult(context.Context, *NftSearchResultRequest) (*NftSearchResultResponse, error)
	//nft积分查询接口
	NftPointQuery(context.Context, *NftPointQueryRequest) (*NftPointQueryResponse, error)
	//nft购买
	NftBuy(context.Context, *NftBuyRequest) (*NftBuyResponse, error)
	//nft销售状态变更
	NftStatus(context.Context, *NftStatusRequest) (*NftStatusResponse, error)
	//新增数字藏品到瑞鹏平台数据
	NftCreate(context.Context, *NftCreateRequest) (*NftResponse, error)
	//更新瑞鹏平台数据藏品
	NftUpdate(context.Context, *NftUpdateRequest) (*NftResponse, error)
	//删除瑞鹏平台数据藏品
	NftDelete(context.Context, *NftDeleteRequest) (*NftResponse, error)
	//展示瑞鹏平台数据藏品
	NftRuiPengList(context.Context, *NftRuiPengListRequest) (*NftRuiPengListResponse, error)
	//瑞鹏平台数据藏品详情数据
	NftRuiPengDetail(context.Context, *NftRuiPengDetailRequest) (*NftRuiPengDetailResponse, error)
	//判断用户是否获取openid
	UserOpenid(context.Context, *UserOpenidRequest) (*UserOpenidResponse, error)
	//获取openid
	UserAuthOpenid(context.Context, *UserAuthOpenidRequest) (*UserAuthOpenidResponse, error)
	//用户购买或免费领取数字藏品
	UserBuyNft(context.Context, *UserBuyNftRequest) (*UserBuyNftResponse, error)
	//数字藏品订单支付
	UserNftPay(context.Context, *UserNftPayRequest) (*UserNftPayResponse, error)
	//支付结果领取nft
	NftPayResultPick(context.Context, *NftPayResultPickRequest) (*NftResponse, error)
	//scrmUserId换userIdentification
	ChangeUserId(context.Context, *ChangeUserIdRequest) (*ChangeUserIdResponse, error)
	//随机展示一款数字藏品
	NftSelect(context.Context, *NftSelectRequest) (*NftRuiPengDetailResponse, error)
	//消费获取nft列表展示
	NftCostSelect(context.Context, *NftCostSelectRequest) (*NftCostSelectResponse, error)
	//活动1领取nft
	NftSelectPick(context.Context, *NftSelectPickRequest) (*NftResponse, error)
	//消费活动领取nft列表
	NftCostSelectPick(context.Context, *NftSelectPickRequest) (*NftResponse, error)
	//支付成功页计算支付消费金额接口
	CalcOrderCost(context.Context, *CalcOrderCostRequest) (*CalcOrderCostResponse, error)
	//场馆列表
	VenueList(context.Context, *VenueListRequest) (*VenueListResponse, error)
	//场馆内容
	VenueDetail(context.Context, *VenueDetailRequest) (*VenueDetailResponse, error)
	//分享用户收集
	ShareUserCollect(context.Context, *ShareUserCollectRequest) (*NftResponse, error)
	//抢nft资格消息订阅
	VenueMessageSubscribe(context.Context, *VenueMessageSubscribeRequest) (*NftResponse, error)
	//场馆活动领取nft
	VenueSelectPick(context.Context, *NftSelectPickRequest) (*NftResponse, error)
	//发送订阅消息
	SendSubcribeMessage(context.Context, *SendSubcribeMessageRequest) (*NftResponse, error)
	//领取优惠券接口
	CouponPick(context.Context, *CouponPickRequest) (*NftResponse, error)
	//测试假数据
	FindDigitalUserNft(context.Context, *FindDigitalUserNftRequest) (*NftSeriesListResponse, error)
	//根据系列设置库存
	NftSetStock(context.Context, *NftSetStockRequest) (*NftResponse, error)
	//元宇宙领取nft列表
	NftMetaSelect(context.Context, *NftMetaSelectRequest) (*NftMetaSelectResponse, error)
	//元宇宙活动领取nft
	MetaSelectPick(context.Context, *NftSelectPickRequest) (*NftResponse, error)
	// 查询该账户资产归属的系列列表
	NftSeriesResult(context.Context, *NftPointQueryRequest) (*NftSearchResultResponse, error)
}

// UnimplementedDigitalServiceServer can be embedded to have forward compatible implementations.
type UnimplementedDigitalServiceServer struct {
}

func (*UnimplementedDigitalServiceServer) RealRegister(ctx context.Context, req *RealRegisterRequest) (*RealRegisterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RealRegister not implemented")
}
func (*UnimplementedDigitalServiceServer) DigitalImageUpload(ctx context.Context, req *DigitalImageUploadRequest) (*DigitalImageUploadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DigitalImageUpload not implemented")
}
func (*UnimplementedDigitalServiceServer) NftPublishDigital(ctx context.Context, req *NftPublishDigitalRequest) (*NftPublishDigitalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftPublishDigital not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSeriesClaim(ctx context.Context, req *NftSeriesClaimRequest) (*NftSeriesClaimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSeriesClaim not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSeriesList(ctx context.Context, req *NftSeriesListRequest) (*NftSeriesListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSeriesList not implemented")
}
func (*UnimplementedDigitalServiceServer) NftInfo(ctx context.Context, req *NftInfoRequest) (*NftInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftInfo not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSearchResult(ctx context.Context, req *NftSearchResultRequest) (*NftSearchResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSearchResult not implemented")
}
func (*UnimplementedDigitalServiceServer) NftPointQuery(ctx context.Context, req *NftPointQueryRequest) (*NftPointQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftPointQuery not implemented")
}
func (*UnimplementedDigitalServiceServer) NftBuy(ctx context.Context, req *NftBuyRequest) (*NftBuyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftBuy not implemented")
}
func (*UnimplementedDigitalServiceServer) NftStatus(ctx context.Context, req *NftStatusRequest) (*NftStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftStatus not implemented")
}
func (*UnimplementedDigitalServiceServer) NftCreate(ctx context.Context, req *NftCreateRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftCreate not implemented")
}
func (*UnimplementedDigitalServiceServer) NftUpdate(ctx context.Context, req *NftUpdateRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftUpdate not implemented")
}
func (*UnimplementedDigitalServiceServer) NftDelete(ctx context.Context, req *NftDeleteRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftDelete not implemented")
}
func (*UnimplementedDigitalServiceServer) NftRuiPengList(ctx context.Context, req *NftRuiPengListRequest) (*NftRuiPengListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftRuiPengList not implemented")
}
func (*UnimplementedDigitalServiceServer) NftRuiPengDetail(ctx context.Context, req *NftRuiPengDetailRequest) (*NftRuiPengDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftRuiPengDetail not implemented")
}
func (*UnimplementedDigitalServiceServer) UserOpenid(ctx context.Context, req *UserOpenidRequest) (*UserOpenidResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserOpenid not implemented")
}
func (*UnimplementedDigitalServiceServer) UserAuthOpenid(ctx context.Context, req *UserAuthOpenidRequest) (*UserAuthOpenidResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserAuthOpenid not implemented")
}
func (*UnimplementedDigitalServiceServer) UserBuyNft(ctx context.Context, req *UserBuyNftRequest) (*UserBuyNftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserBuyNft not implemented")
}
func (*UnimplementedDigitalServiceServer) UserNftPay(ctx context.Context, req *UserNftPayRequest) (*UserNftPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserNftPay not implemented")
}
func (*UnimplementedDigitalServiceServer) NftPayResultPick(ctx context.Context, req *NftPayResultPickRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftPayResultPick not implemented")
}
func (*UnimplementedDigitalServiceServer) ChangeUserId(ctx context.Context, req *ChangeUserIdRequest) (*ChangeUserIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeUserId not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSelect(ctx context.Context, req *NftSelectRequest) (*NftRuiPengDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSelect not implemented")
}
func (*UnimplementedDigitalServiceServer) NftCostSelect(ctx context.Context, req *NftCostSelectRequest) (*NftCostSelectResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftCostSelect not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSelectPick(ctx context.Context, req *NftSelectPickRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSelectPick not implemented")
}
func (*UnimplementedDigitalServiceServer) NftCostSelectPick(ctx context.Context, req *NftSelectPickRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftCostSelectPick not implemented")
}
func (*UnimplementedDigitalServiceServer) CalcOrderCost(ctx context.Context, req *CalcOrderCostRequest) (*CalcOrderCostResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalcOrderCost not implemented")
}
func (*UnimplementedDigitalServiceServer) VenueList(ctx context.Context, req *VenueListRequest) (*VenueListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VenueList not implemented")
}
func (*UnimplementedDigitalServiceServer) VenueDetail(ctx context.Context, req *VenueDetailRequest) (*VenueDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VenueDetail not implemented")
}
func (*UnimplementedDigitalServiceServer) ShareUserCollect(ctx context.Context, req *ShareUserCollectRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShareUserCollect not implemented")
}
func (*UnimplementedDigitalServiceServer) VenueMessageSubscribe(ctx context.Context, req *VenueMessageSubscribeRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VenueMessageSubscribe not implemented")
}
func (*UnimplementedDigitalServiceServer) VenueSelectPick(ctx context.Context, req *NftSelectPickRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VenueSelectPick not implemented")
}
func (*UnimplementedDigitalServiceServer) SendSubcribeMessage(ctx context.Context, req *SendSubcribeMessageRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSubcribeMessage not implemented")
}
func (*UnimplementedDigitalServiceServer) CouponPick(ctx context.Context, req *CouponPickRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CouponPick not implemented")
}
func (*UnimplementedDigitalServiceServer) FindDigitalUserNft(ctx context.Context, req *FindDigitalUserNftRequest) (*NftSeriesListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindDigitalUserNft not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSetStock(ctx context.Context, req *NftSetStockRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSetStock not implemented")
}
func (*UnimplementedDigitalServiceServer) NftMetaSelect(ctx context.Context, req *NftMetaSelectRequest) (*NftMetaSelectResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftMetaSelect not implemented")
}
func (*UnimplementedDigitalServiceServer) MetaSelectPick(ctx context.Context, req *NftSelectPickRequest) (*NftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MetaSelectPick not implemented")
}
func (*UnimplementedDigitalServiceServer) NftSeriesResult(ctx context.Context, req *NftPointQueryRequest) (*NftSearchResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NftSeriesResult not implemented")
}

func RegisterDigitalServiceServer(s *grpc.Server, srv DigitalServiceServer) {
	s.RegisterService(&_DigitalService_serviceDesc, srv)
}

func _DigitalService_RealRegister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RealRegisterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).RealRegister(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/RealRegister",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).RealRegister(ctx, req.(*RealRegisterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_DigitalImageUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DigitalImageUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).DigitalImageUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/DigitalImageUpload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).DigitalImageUpload(ctx, req.(*DigitalImageUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftPublishDigital_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftPublishDigitalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftPublishDigital(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftPublishDigital",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftPublishDigital(ctx, req.(*NftPublishDigitalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSeriesClaim_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSeriesClaimRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSeriesClaim(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftSeriesClaim",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSeriesClaim(ctx, req.(*NftSeriesClaimRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSeriesList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSeriesListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSeriesList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftSeriesList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSeriesList(ctx, req.(*NftSeriesListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftInfo(ctx, req.(*NftInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSearchResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSearchResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSearchResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftSearchResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSearchResult(ctx, req.(*NftSearchResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftPointQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftPointQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftPointQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftPointQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftPointQuery(ctx, req.(*NftPointQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftBuy(ctx, req.(*NftBuyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftStatus(ctx, req.(*NftStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftCreate(ctx, req.(*NftCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftUpdate(ctx, req.(*NftUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftDelete(ctx, req.(*NftDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftRuiPengList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftRuiPengListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftRuiPengList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftRuiPengList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftRuiPengList(ctx, req.(*NftRuiPengListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftRuiPengDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftRuiPengDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftRuiPengDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftRuiPengDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftRuiPengDetail(ctx, req.(*NftRuiPengDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_UserOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserOpenidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).UserOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/UserOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).UserOpenid(ctx, req.(*UserOpenidRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_UserAuthOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAuthOpenidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).UserAuthOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/UserAuthOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).UserAuthOpenid(ctx, req.(*UserAuthOpenidRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_UserBuyNft_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserBuyNftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).UserBuyNft(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/UserBuyNft",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).UserBuyNft(ctx, req.(*UserBuyNftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_UserNftPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserNftPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).UserNftPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/UserNftPay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).UserNftPay(ctx, req.(*UserNftPayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftPayResultPick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftPayResultPickRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftPayResultPick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftPayResultPick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftPayResultPick(ctx, req.(*NftPayResultPickRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_ChangeUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).ChangeUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/ChangeUserId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).ChangeUserId(ctx, req.(*ChangeUserIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSelect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSelectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSelect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftSelect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSelect(ctx, req.(*NftSelectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftCostSelect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftCostSelectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftCostSelect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftCostSelect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftCostSelect(ctx, req.(*NftCostSelectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSelectPick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSelectPickRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSelectPick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftSelectPick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSelectPick(ctx, req.(*NftSelectPickRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftCostSelectPick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSelectPickRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftCostSelectPick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftCostSelectPick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftCostSelectPick(ctx, req.(*NftSelectPickRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_CalcOrderCost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalcOrderCostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).CalcOrderCost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/CalcOrderCost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).CalcOrderCost(ctx, req.(*CalcOrderCostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_VenueList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VenueListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).VenueList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/VenueList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).VenueList(ctx, req.(*VenueListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_VenueDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VenueDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).VenueDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/VenueDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).VenueDetail(ctx, req.(*VenueDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_ShareUserCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShareUserCollectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).ShareUserCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/ShareUserCollect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).ShareUserCollect(ctx, req.(*ShareUserCollectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_VenueMessageSubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VenueMessageSubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).VenueMessageSubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/VenueMessageSubscribe",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).VenueMessageSubscribe(ctx, req.(*VenueMessageSubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_VenueSelectPick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSelectPickRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).VenueSelectPick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/VenueSelectPick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).VenueSelectPick(ctx, req.(*NftSelectPickRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_SendSubcribeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSubcribeMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).SendSubcribeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/SendSubcribeMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).SendSubcribeMessage(ctx, req.(*SendSubcribeMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_CouponPick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CouponPickRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).CouponPick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/CouponPick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).CouponPick(ctx, req.(*CouponPickRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_FindDigitalUserNft_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindDigitalUserNftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).FindDigitalUserNft(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/FindDigitalUserNft",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).FindDigitalUserNft(ctx, req.(*FindDigitalUserNftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSetStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSetStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSetStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftSetStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSetStock(ctx, req.(*NftSetStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftMetaSelect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftMetaSelectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftMetaSelect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftMetaSelect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftMetaSelect(ctx, req.(*NftMetaSelectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_MetaSelectPick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftSelectPickRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).MetaSelectPick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/MetaSelectPick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).MetaSelectPick(ctx, req.(*NftSelectPickRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DigitalService_NftSeriesResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NftPointQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DigitalServiceServer).NftSeriesResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/nft.DigitalService/NftSeriesResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DigitalServiceServer).NftSeriesResult(ctx, req.(*NftPointQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DigitalService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "nft.DigitalService",
	HandlerType: (*DigitalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RealRegister",
			Handler:    _DigitalService_RealRegister_Handler,
		},
		{
			MethodName: "DigitalImageUpload",
			Handler:    _DigitalService_DigitalImageUpload_Handler,
		},
		{
			MethodName: "NftPublishDigital",
			Handler:    _DigitalService_NftPublishDigital_Handler,
		},
		{
			MethodName: "NftSeriesClaim",
			Handler:    _DigitalService_NftSeriesClaim_Handler,
		},
		{
			MethodName: "NftSeriesList",
			Handler:    _DigitalService_NftSeriesList_Handler,
		},
		{
			MethodName: "NftInfo",
			Handler:    _DigitalService_NftInfo_Handler,
		},
		{
			MethodName: "NftSearchResult",
			Handler:    _DigitalService_NftSearchResult_Handler,
		},
		{
			MethodName: "NftPointQuery",
			Handler:    _DigitalService_NftPointQuery_Handler,
		},
		{
			MethodName: "NftBuy",
			Handler:    _DigitalService_NftBuy_Handler,
		},
		{
			MethodName: "NftStatus",
			Handler:    _DigitalService_NftStatus_Handler,
		},
		{
			MethodName: "NftCreate",
			Handler:    _DigitalService_NftCreate_Handler,
		},
		{
			MethodName: "NftUpdate",
			Handler:    _DigitalService_NftUpdate_Handler,
		},
		{
			MethodName: "NftDelete",
			Handler:    _DigitalService_NftDelete_Handler,
		},
		{
			MethodName: "NftRuiPengList",
			Handler:    _DigitalService_NftRuiPengList_Handler,
		},
		{
			MethodName: "NftRuiPengDetail",
			Handler:    _DigitalService_NftRuiPengDetail_Handler,
		},
		{
			MethodName: "UserOpenid",
			Handler:    _DigitalService_UserOpenid_Handler,
		},
		{
			MethodName: "UserAuthOpenid",
			Handler:    _DigitalService_UserAuthOpenid_Handler,
		},
		{
			MethodName: "UserBuyNft",
			Handler:    _DigitalService_UserBuyNft_Handler,
		},
		{
			MethodName: "UserNftPay",
			Handler:    _DigitalService_UserNftPay_Handler,
		},
		{
			MethodName: "NftPayResultPick",
			Handler:    _DigitalService_NftPayResultPick_Handler,
		},
		{
			MethodName: "ChangeUserId",
			Handler:    _DigitalService_ChangeUserId_Handler,
		},
		{
			MethodName: "NftSelect",
			Handler:    _DigitalService_NftSelect_Handler,
		},
		{
			MethodName: "NftCostSelect",
			Handler:    _DigitalService_NftCostSelect_Handler,
		},
		{
			MethodName: "NftSelectPick",
			Handler:    _DigitalService_NftSelectPick_Handler,
		},
		{
			MethodName: "NftCostSelectPick",
			Handler:    _DigitalService_NftCostSelectPick_Handler,
		},
		{
			MethodName: "CalcOrderCost",
			Handler:    _DigitalService_CalcOrderCost_Handler,
		},
		{
			MethodName: "VenueList",
			Handler:    _DigitalService_VenueList_Handler,
		},
		{
			MethodName: "VenueDetail",
			Handler:    _DigitalService_VenueDetail_Handler,
		},
		{
			MethodName: "ShareUserCollect",
			Handler:    _DigitalService_ShareUserCollect_Handler,
		},
		{
			MethodName: "VenueMessageSubscribe",
			Handler:    _DigitalService_VenueMessageSubscribe_Handler,
		},
		{
			MethodName: "VenueSelectPick",
			Handler:    _DigitalService_VenueSelectPick_Handler,
		},
		{
			MethodName: "SendSubcribeMessage",
			Handler:    _DigitalService_SendSubcribeMessage_Handler,
		},
		{
			MethodName: "CouponPick",
			Handler:    _DigitalService_CouponPick_Handler,
		},
		{
			MethodName: "FindDigitalUserNft",
			Handler:    _DigitalService_FindDigitalUserNft_Handler,
		},
		{
			MethodName: "NftSetStock",
			Handler:    _DigitalService_NftSetStock_Handler,
		},
		{
			MethodName: "NftMetaSelect",
			Handler:    _DigitalService_NftMetaSelect_Handler,
		},
		{
			MethodName: "MetaSelectPick",
			Handler:    _DigitalService_MetaSelectPick_Handler,
		},
		{
			MethodName: "NftSeriesResult",
			Handler:    _DigitalService_NftSeriesResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "nft/digital.proto",
}
