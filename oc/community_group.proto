syntax = "proto3";
package oc;

// @Desc 社区团购
service CommunityGroupService {
  //  团列表-可参加团的列表
  rpc ActivityList(CommunityGroupActivityListRequest) returns (CommunityGroupActivityListResponse);
  //  团列表-可参加团的列表
  rpc ParticipantList(CommunityGroupParticipantListRequest) returns (CommunityGroupParticipantListResponse);
  //  团详情
  rpc Detail(CommunityGroupDetailRequest) returns (CommunityGroupDetailResponse);
  // 是否是团长
  rpc IsGroup(IsGroupReq) returns(IsGroupRes);
  // 我的团-佣金数据
  rpc MyGroup(MyGroupReq) returns(MyGroupRes);
  // 我的团-列表数据
  rpc MyGroupList(MyGroupListReq) returns(MyGroupListRes);
  // 团员的订单
  rpc MerberOrder(MerberOrderReq) returns(MerberOrderRes);
  // 我的团-确认收货
  rpc GroupCompleted(GroupCompletedReq) returns(BaseResponseNew);
  // 团员订单-确认完成
  rpc MemberCompleted(MemberCompletedReq) returns(BaseResponseNew);
  // 开团
  rpc CommunityGroupOpen(CommunityGroupOpenRequest) returns(CommunityGroupOpenResponse);
  //消息订阅
  rpc MessageSubscribe(CommunityGroupMessageSubscribeRequest) returns (BaseResponseNew);
  //活动结束更新开团状态
  rpc UpdateOrderGroup(UpdateOrderGroupRequest) returns (BaseResponseNew);
  // 团订单列表
  rpc OrderList(CommunityGroupOrderListRequest) returns (CommunityGroupOrderListResponse);
}

message CommunityGroupOrderListRequest {
  int32 page_size = 1;
  int32 page_index = 2;
  //团状态 0拼团中 1拼团成功 2拼团失败
  string group_status = 3;
  // 搜索类型 1团长手机 2团编码 3财务编码 4团长单位名
  string search_type = 4;
  //搜索关键词
  string keyword = 5;
  // 团长代收状态 0不代收 1代收
  string group_take_type = 6;
  // 时间搜索类型 1开团时间 2拼成时间 3失败时间
  string time_type = 7;
  // 开始时间
  string start_time = 8;
  // 结束时间
  string end_time = 9;
  //登录用户所有权限的门店id。前端不用传
  repeated string shop_ids = 10;
  //用户编号，前端不用传
  string user_no = 11;
  //ip地址
  string ip = 12;
  //ip所属地
  string ip_location = 13;
  // 请求来源，默认0-订单列表，1-实物订单，2-虚拟订单
  int32 request_from = 14;
}

message CommunityGroupOrderListResponse {
  //状态码
  int32  code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单
  repeated Details details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
  message Details {
    //团id
    int64 id = 1;
    //店铺财务编码
    string finance_code = 2;
    // 店铺名称
    string store_name = 3;
    //是否分销员 0否 1是
    int32 is_dis = 4;
    //团状态 0开团 1拼团成功 2拼团失败
    int32 status = 5;
    //团长代收状态 0不代收 1代收
    int32 take_type = 6;
    //成团最小金额 分
    int32 min_amount = 7;
    //已支付金额 分
    int32 pay_amount = 8;
    //待结算金额 分
    int32 wait_amount = 9;
    //已结算金额 分
    int32 paid_amount = 10;
    //团开始时间'
    string start_time = 11;
    //团结束时间'
    string end_time = 12;
    //预计送达时间
    string expected_time_text = 13;
    //创建时间
    string created_at = 14;
    //拼团成功者失败时间
    string group_at = 15;
    // 开团会员id
    string member_id = 16;
    // 开团会员名称
    string member_name = 17;
    // 开团会员头像
    string member_profile = 18;
    // 收件人
    string receiver_name = 19;
    // 收件手机
    string receiver_mobile = 20;
    // 收件地址
    string receiver_address = 21;
    //财务编码
    string dis_chain_finance_code = 22;
    //所属单位
    string dis_chain_name = 23;
    // 渠道id
    int32 channel_id = 24;
    // 渠道来源
    int32 user_agent = 25;
    // 用于前端统一，虚拟订单不显示预计送达时间
    int32 delivery_type = 26;
    // 密文收件手机号
    string en_receiver_mobile = 27;
  }
}

message BaseResponseNew {
  string msg = 1;
}

message GroupCompletedReq {
  //主键ID
  int32 id = 1;
}

message MemberCompletedReq {
  //订单号
  string order_sn = 1;
}

message MerberOrderReq {
  //my-group-list 返回的ID
  int32 id = 1;
  string key = 2;
  int32 page_index = 3;
  int32 page_size = 4;
}

message MerberOrderRes {
  string msg = 1;
  repeated MemberOrderData data = 2;
  int32 total = 3;
}

message MemberOrderData {
  //团员信息
  string mbmber = 1;
  //订单状态  1已接单 2退款中 3已取消  4已完成
  int32 order_status = 2;
  //参团时间
  string created_at = 3;
  //订单号
  string order_sn = 4;
  //订单商品列表
  repeated OrderProduct goods_list = 5;
  // 团员收货地址
  string group_address = 6;
}

message OrderProduct {
  //商品图
  string goods_img = 1;
  //商品名称
  string goods_name = 2;
  //商品数量
  int32 goods_num = 3;
  //商品规格
  string specs = 4;
}

message IsGroupReq {
  //会员ID
  string member_id = 1;
}

message IsGroupRes {
  string msg = 1;
  message Data {
    //是否是团长 1是 0否
    int32 is_group = 2;
    //是否分销员 0不是分销员 1内部分销员 2外部分销员
    int32 dis_type = 3;
    //是否显示table  1是  0否
    int32 has_commission = 4;
    // 是否显示开团按钮，临时需求，分销员+开关开启才显示
    int32 is_show_open_btn = 5;
  }
  Data data = 2;
}

message MyGroupReq {
  //年
  string year = 1;
  //月
  string month = 2;
  //会员ID（前端不用传）
  string member_id = 3;
}

message MyGroupRes {
  string msg = 1;
  //成团实付总金额(分)
  int32 pay_amount = 2;
  //待结算金额  分
  int32 wait_amount = 3;
  //已结算金额  分
  int32 paid_amount = 4;
  //当前佣金
  string commissions = 5;
}

message MyGroupListReq {
  //是否为分销员 1是 0否
  int32 is_dis = 1;
  //会员ID（前端不用传）
  string member_id = 2;
  int32 page_index = 3;
  int32 page_size = 4;
  //年
  string year = 5;
  //月
  string month = 6;
}

message MyGroupListRes {
  string msg = 1;
  //团列表
  repeated GroupActivity data = 2;
  int32 total = 3;
}

message GroupActivity {
  //团状态  0拼团中 1拼团成功 2拼团失败
  int32 status = 1;
  //店铺名称
  string store_name = 2;
  //代收地址
  string receiver_address = 3;
  //门店头像
  string store_img = 4;
  //已凑金额  分
  int32 pay_monery = 5;
  //贷物状态
  int32 transportation = 6;
  //主键ID
  int32 id = 7;
  //活动剩余时间 秒
  int32 surplus_time = 8;
  //成团金额
  int32 min_amount = 9;
  //团长代收状态 0不代收 1代收
  int32 final_take_type = 10;
  //是否确认收货 1是  0否
  int32 is_receive = 11;
}

message CommunityGroupDetailRequest {
  //团id
  int32 id = 1;
  //前端不用传
  string scrm_id = 2;
  // 是否返回参团列表信息 0否 1返回6条数据 2返回全部数据
  int32 return_detail = 3;
}

message CommunityGroupDetailResponse {
  // 状态 200成功 非200失败
  int32 code = 1;
  //错误信息
  string message = 2;
  message Data  {
    int32 id = 1;
    //是否团长 0否 1是
    int32 is_grouper = 2;
    //开团人名称
    string member_name = 3;
    //开团人头像
    string member_profile = 4;
    //开团发起时间
    string created_at = 5;
    //剩余多少（秒）
    int32 time = 6;
    //结束时间
    string end_time = 7;
    //成团金额
    int32 min_amount = 8;
    //已支付金额
    int32 pay_amount = 9;
    //是否团长代收 0不代收 1代收
    int32 final_take_type = 10;
    //团长代收时的名称
    string receiver_name = 11;
    //团长代收时的地址
    string receiver_address = 12;
    //团长代收时的手机号码
    string receiver_mobile = 13;
    //团状态 0进行中 1成功 2失败
    int32 status = 14;
    //拼团用户列表
    message GroupDetails  {
      // 头像
      string profile = 1;
      // 昵称
      string nick_name = 2;
      //实付金额
      int32 pay_amount = 3;
      //创建时间
      string created_at = 4;
    }
    repeated GroupDetails group_details = 15;
    // 预计送达时间描述，如成团当日
    string expected_desc = 16;
    //进度百分比不带单位 2位小数 最大100.00
    float progress = 17;
    //是否订阅信息 0否1是
    int32 is_subscribe = 18;

    message MemberOrders {
      //订单id
      int32 order_id = 1;
      //订单编号
      string order_sn = 2;
    }
    // 当前用户订单号 目前最多返回2条记录
    repeated MemberOrders orders = 19;
    // 店铺财务编码
    string finance_code = 20;
    //经度
    string lng = 21;
    //纬度
    string lat = 22;
    // 会员id
    string member_id = 23;
    // 预计N天送达
    int32 deliver_days = 24;
  }
  Data data = 15;
}
message CommunityGroupActivityListRequest {
  //门店编码
  string finance_code = 1;
  //需要显示的记录数量 0获取所以 大于0获取指定数量
  int32 page_size = 3;
  //排序值 0:默认排序
  int32 sort_type = 4;
  //前端不用传
  string scrm_id = 5;
}

message CommunityGroupActivityListResponse{
  // 状态 200成功 非200失败
  int32 code = 1;
  // 错误信息
  string message = 2;
  //总记录数量
  int32 total = 3;
  message List {
    //团id
    int32 id = 1;
    //开团人名称
    string nick_name = 2;
    //开团人头像
    string profile = 3;
    //剩余时间(秒)
    int32 time = 4;
    //还差N金额成团
    int32 amount = 6;
    //团长代收状态 0不代收 1代收
    int32 take_type = 7;
  }
  repeated List data = 4;
}


message CommunityGroupParticipantListRequest{
  //门店编码
  string finance_code = 1;
  //需要显示的记录数量 0获取所有 大于0获取指定数量
  int32 page_size = 3;
}

message CommunityGroupParticipantListResponse{
  // 状态 200成功 非200失败
  int32 code = 1;
  // 错误信息
  string message = 2;
  message List {
    //名称
    string nick_name = 2;
    //头像
    string profile = 3;
    // 开团或参团时间
    string time = 4;
    // 类型 0开团 1参团
    int32 type = 5;
  }
  repeated List data = 4;
}

message CommunityGroupOpenRequest {
  // 店铺财务编码
  string store_finance_code = 1;
  // 团长代收状态 0不代收 1代收
  int32 final_take_type = 2;
  // 收件人
  string receiver_name = 3;
  // 收件手机
  string receiver_mobile = 4;
  // 收件省
  string receiver_state = 5;
  // 收件市
  string receiver_city = 6;
  // 收件区
  string receiver_district = 7;
  // 收件地址
  string receiver_address = 8;
  // 会员id，后端通过token获取
  string member_id = 9;
  // 代收地址，经度
  string lng = 10;
  // 代收地址，纬度
  string lat = 11;
  // 昵称
  string nick_name = 12;
  // 头像完整地址
  string avatar_url = 13;
  // 开团渠道id
  int32 channel_id = 14;
  // 开团渠道来源
  int32 user_agent = 15;
  // 海报带的分销id
  int32 dis_member_id = 16;
}

message CommunityGroupOpenResponse {
  // 提示信息
  string msg = 2;
  // 错误信息
  string error = 3;
  CommunityGroupOpenData data = 4;
}
message CommunityGroupOpenData {
  // 团id
  int64 group_id = 4;
  // 模板id
  string template_id = 5;
}

message CommunityGroupBaseResponse {
  // 提示信息
  string msg = 2;
  // 错误信息
  string error = 3;
}

message JsonStrRequest {
  bytes json_str = 1;
}

message CommunityGroupMessageSubscribeRequest{
  //订阅消息类型 0团长开团 1团员订单
  int32 type = 1;
  //类型是0时传团id 1订单编号
  string number = 2;
  //前端不用传
  string scrm_id=3;
}

message UpdateOrderGroupRequest{
  //活动ID
  int32 promotion_group_activity_id = 1;
}