syntax = "proto3";
package oc;

import "google/protobuf/any.proto";

service VipCardOrderService {
  //会员卡订单
  rpc GetOrderList(GetOrderListRequest) returns (GetOrderListResponse);
  rpc GetVipCardOrderEquity(GetVipCardOrderEquityRequest) returns (GetVipCardOrderEquityResponse);
  rpc GetVipVrRefundList(GetVrRefundListRequest) returns (GetVrRefundListResponse);
  rpc VipVrRefundDetail(VipVrRefundDetailReq) returns (VipVrRefundDetailResp);
  rpc CreateVipVrRefund(CreateVrRefundRequest) returns (CreateVrRefundResponse);
  rpc DelVipOrderCard(DelVipOrderCardRequest) returns (VcBaseResponse);
  rpc GetOrderOperateLogList(GetOrderOperateLogListRequest) returns (GetOrderOperateLogListResponse);
  //会员卡实体卡订单列表
  rpc GetPhysicalVipCardOrderList(GetPhysicalVipCardOrderListRequest) returns(GetPhysicalVipCardOrderListResponse);
  //会员卡实体卡订单详情
  rpc GetPhysicalVipCardOrderDetail(GetPhysicalVipCardOrderDetailRequest) returns(GetPhysicalVipCardOrderDetailResponse);
  //会员卡实体卡导出
  rpc GetPhysicalVipCardOrderExport(GetPhysicalVipCardOrderListRequest) returns(GetPhysicalVipCardOrderExportResponse);
  //健康会员卡实体卡订单导出结果列表
  rpc PhysicalVipCardOrderExportList(PhysicalVipCardOrderExportListRequest)returns(PhysicalVipCardOrderExportListResponse);
  // 会员卡实体卡 批量导入物流模板
  rpc PVCExpressImportTemplate(PVCExpressImportTemplateRequest) returns(PVCExpressImportTemplateResponse);
  // 会员卡实体卡更新物流信息
  rpc PVCExpressEdit(PVCExpressEditRequest) returns(PVCExpressEditResponse);
  // 会员卡实体卡会员卡号编辑
  rpc PVCEdit(PVCEditRequest) returns(PVCEditResponse);
  // 会员卡实体卡 批量导入物流
  rpc PVCExpressImport(PVCExpressImportRequest) returns(PVCExpressImportResponse);
  // 会员卡实体卡 批量导入物流历史
  rpc PVCExpressImportList(PVCExpressImportListRequest) returns(PVCExpressImportListResponse);
}
message PVCEditRequest{
  // 订单号
  string order_sn = 1;
  //会员卡号
  string virtual_card = 2;
}
message PVCEditResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
}
message PVCExpressEditRequest{
  // 订单号
  string order_sn = 1;
  // 物流单号
  string shipping_code = 2;
  // 物流公司编码
  string shipping_ecode = 3;
}
message PVCExpressEditResponse{
   // 状态码，200正常，>=400出错
   int32 code = 1;
   // 消息
   string message = 2;
}
message GetPhysicalVipCardOrderDetailRequest{
  string order_sn = 1;
}
message GetPhysicalVipCardOrderDetailResponse{
  int32 code = 1;
  string message = 2;
  PhysicalVipCardOrderDetail data = 3;
}

message PhysicalVipCardOrderDetail{
  //订单编号
  string order_sn = 1;
  // 支付时间
  string payment_time =2;
  //订单来源：'1:PC端,2:移动端,3:APP端,4:ERP,5:智慧门店,6:有赞,7:阿闻宠物,8:阿闻商城
  int32 order_from = 3;
  //订单来源文本 
  string order_from_text = 4;
  //卡号
  int64 virtual_card_id = 5;
  //卡号
  string virtual_card_id_text = 6;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已发货;40:已收货;50部分发货
  int32 order_state = 7;
  //订单状态文本
  string order_state_text=8;
  //支付方式：
  string payment_code=9;
  string payment_code_text = 10;  
  //订单备注
  string order_message = 11;
  //会员编号
  string scrm_user_id = 12;
  //会员手机
  string buyer_phone = 13;
  //会员加密手机号
  string encrypt_mobile = 14;
  //会员账号
  string buy_name = 15;
  Address1 address = 16;
  repeated Goods goods = 17;
  DeliveryInfo delivery = 18;
  repeated UpetOrderLog steps = 19;
}
message DeliveryInfo{
  //物流单号
  string shipping_code =1;
  //物流公司名称
  string e_name = 2;
  //配送时间
  string shipping_time = 3;

}
message UpetOrderLog{
  int64 order_id = 1;
  //处理时间
  string log_time = 2;
  //订单状态：0(已取消)10:未付款;20:已付款;30:已发货;40:已收货;
  string log_orderstate_text = 3;
}
message PVCExpressImportListRequest{
  // 页码，不传默认为1
  int32 page_index = 1;
  // 每页数量，不传默认10
  int32 page_size = 2;
}

message PVCExpressImportListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  message List {
    // 操作时间
    string created_at = 1;
    // 结果url
    string url = 2;
    // 导入结果文本
    string result = 3;
  }
  repeated List data = 3;
  // 总数
  int64 total = 4;
}
message PhysicalVipCardOrderExportListRequest{
  // 页码，不传默认为1
  int32 page_index = 1;
  // 每页数量，不传默认10
  int32 page_size = 2;
}
message PhysicalVipCardOrderExportListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  message List {
    // 操作时间
    string created_at = 1;
    // 状态 0处理中、1成功、2失败
    int32 state = 2;
    // 状态文本
    string state_text = 3;
    // 当state = 1时返回链接
    string url = 4;
    // 当state = 2返回失败原因
    string result = 5;
  }
  repeated List data = 3;
  // 总数
  int64 total = 4;
}
message PVCExpressImportRequest {
  // 文件字节流
  bytes file = 1;
}
message PVCExpressImportResponse{
  int32 code=1;
  string message =2;
}
message PVCExpressImportTemplateRequest{}
message PVCExpressImportTemplateResponse{
    // 状态码，200正常，>=400出错
    int32 code = 1;
    // 消息
    string message = 2;
    // 文件字节流
    bytes template = 3;
}

message GetPhysicalVipCardOrderExportResponse{
  int32 code = 1;
  string message =2;
}
message GetPhysicalVipCardOrderListRequest{
  int32 page_index = 1;
  int32 page_size = 2;
  //订单编号
  string order_sn = 3;
  //用户手机号
  string member_mobile = 4;
  //购买时间
  string payment_time_start = 5;
  string payment_time_end = 6;
  //订单状态：-1(全部);0(已取消);10(默认):未付款;20:已付款;30:已发货;40:已收货;50部分发货,
  int32 order_state = 7;

}
message GetPhysicalVipCardOrderListResponse{
  int32 code = 1;
  string message = 2;
  int32 total =3;
  repeated PhysicalOrderData data = 4;
}
message PhysicalOrderData{
  //订单编号
  string order_sn = 1;
  // 支付时间
  string payment_time =2;
  //订单来源：'1:PC端,2:移动端,3:APP端,4:ERP,5:智慧门店,6:有赞,7:阿闻宠物,8:阿闻商城
  int32 order_from = 3;
  //订单来源文本 
  string order_from_text = 4;
  //卡号
  int64 virtual_card_id = 5;
  //卡号
  string virtual_card_id_text = 6;
  //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已发货;40:已收货;50部分发货
  int32 order_state = 7;
  //订单状态文本
  string order_state_text=8;
  
  //快递单号
  string shipping_code = 9;
  int32 shipping_express_id = 10;
  string e_code_kdniao = 11;
   Address1 address = 12;
   repeated Goods goods = 13;
  //是否可以修改卡号
  int32 is_can_up = 14;

}
message Address1 {
  //顾 客
  string reciver_name = 1;
  //电 话
  string mob_phone = 2;
  //加密电话
  string encrypt_mobile=3;
  //收货地址
  string reciver_address = 4;
  
 

}
message Goods {
  //商品id(SKU)
  int64 goods_id=1; 
  //商品主图
  string goods_image=2; 
  //商品名称（+规格名称）
  string goods_name=3; 
  // 商品价格
  float goods_price=4;
  //商品数量
  int32 goods_num = 5;

}
message OrderData {
  int64 id = 1;
  //订单号
  string order_sn = 2;
  //订单金额(分)
  int32 order_amount = 3;
  //用户id
  string user_id = 4;
  //用户等级
  int32 user_level_id = 5;
  //卡名称
  string card_name = 6;
  //卡类型
  int32 card_type = 7;
  //付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
  int32 card_cycle = 8;
  //支付时间
  string pay_time = 9;
  //状态 10-已支付 20-已退款
  int32 state = 10;
  //会员卡有效期
  string expiry_date = 11;
  //退款金额(分)
  int32 refund_amount = 12;
  //退款时间
  string refund_time = 13;
  string create_time = 14;
  string update_time = 15;
  //周期名称
  string cycle_name = 16;
  //卡类型名称
  string card_type_name =17;
  // 手机号
  string user_mobile = 18;
  //来源 0主动购买 1分销购买，2虚拟卡券兑换
  int32 source=19;
  //卡密
  string card_pass=20;
  //大区
  string region=21;
  //省
  string province=22;
  //市
  string city=23;
  //分销人名称
  string dis_member_name=24;
  //分销人id
  string dis_member_id=25;
  //分销佣金，分
  int32 dis_commission=26;
  //卡号
  string card_id=27;
  //虚拟卡号
  int64 virtual_card_id=28;
        //加密手机号
  string en_user_mobile = 29;
  // 子龙门店id
  int32 store_id = 30;
  // 门店名称
  string store_name = 31;
  // 大区组织id
  int32  org_id = 32;
  // 支付方式
  string  payment_code = 33;
  // 卡模板id
  string t_id = 34;
  // 虚拟订单状态：0-已取消；10-未付款；20-已付款；40-已完成
  int32 order_state = 35;
  // 会员卡是否过期：0-否，1-是
  int32 is_expiry = 36;
  //实体卡订单号
  string entity_order_sn = 37;
}

message GetOrderListResponse {
  int32 code = 1;
  string message = 2;
  int32 total =3;
  repeated OrderData data = 4;
}

message GetOrderListRequest {
  // 当前页
  int32 pageIndex=1;
  // 每页数量
  int32 pageSize=2;
  //10-购卡 20-退卡
  int32 type =3;
  //用户id
  string user_id=4;
  //订单号
  string order_sn =5;
  // 是否导出 1-购卡导出 2-退卡导出
  int32 export = 6;
  //导出ids
  string ids = 7;
  // 手机号
  string user_mobile = 8;
  //卡状态
  int32 state = 9;
  //卡名称
  string card_name = 10;
  //来源 0主动购买 1分销购买，2虚拟卡券兑换，3门店开卡
  int32 source = 11;
  //购买时间-开始时间
  string pay_time_start = 12;
  //购买时间-结束时间
  string pay_time_end = 13;
  //排序规则：asc-正序，desc-倒序
  string order = 14;
  //是否过期：0-全部，1-未过期的，2-过期的
  int32 expiry = 15;
  //实体卡订单号
  string entity_order_sn = 16;
  //卡号
  int64 virtual_card_id = 17; 
}
message GetVipCardOrderEquityRequest{
  //订单编号
  string order_sn = 1; 
}
message GetVipCardOrderEquityResponse{
    int32 code = 1;
    string message = 2;
    repeated EquityData data = 3;
}

message GetVrRefundListRequest {
  // 订单号ID
  string order_sn = 1;
  // erp订单号
  string erp_order_sn = 2;
  // 用户手机号
  string mobile = 3;
  // 申请时间-开始时间
  string start_time = 4;
  // 申请时间-结束时间
  string end_time = 5;
  // 状态：0-全部（默认）；1-待审核；2-审批拒绝；3-退款成功；4-退款失败；5-注销成功
  int32 admin_state = 6;
  // 是否导出：0-列表查询（默认）；1-导出列表
  int32 export = 7;
  //当前多少页 从1开始 必传且必须大于0
  int32 page_index = 8;
  //每页多少条数据 必传且必须大于0
  int32 page_size = 9;
  //卡名称
  string card_name = 10;
}

message GetVrRefundListResponse {
  int32 code = 1;
  string message = 2;
  int32 total =3;
  repeated GetVrRefundListData data = 4;

}

message GetVrRefundListData {
  // 申请批次
  int32 refund_id = 1;
  // 用户id
  string member_id = 2;
  // 用户手机号
  string mobile = 3;
  // 订单号
  string order_sn = 4;
  // 申请类型：1-仅注销身份和权益；2-退款+注销身份和权益
  int32 apply_type = 5;
  // 申请人
  string apply_user = 6;
  // 申请原因
  string buyer_message = 7;
  // 申请时间
  string add_time = 8;
  // 状态
  int32 admin_state = 9;
  // 审核人
  string admin_user = 10;
  // 审核时间
  string admin_time = 11;
  // 审核原因
  string admin_message = 12;
  // 电银退款状态：0-默认；1-退款中；2-成功；3-失败；4-接口异常
  int32 dy_state = 13;
  // 加密手机号
  string encrypt_mobile = 14;
  // erp订单号
  string erp_order_sn = 15;
  // 卡名称
  string card_name = 16;
}

message CreateVrRefundRequest {
  // 用户手机号
  string mobile = 1;
  // 购买会员的订单号
  string order_sn = 2;
  // 申请原因
  string buyer_message = 3;
  // 申请类型：1-仅注销身份和权益；2-退款+注销身份和权益
  int32 apply_type = 4;
  // 申请人
  string apply_user = 5;
}

message CreateVrRefundResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message DelVipOrderCardRequest {
  // 会员卡订单id
  int32 id = 1;
  // 用户编号，前端不用传
  string user_no = 2;
  // 用户昵称，前端不用传
  string user_name = 3;
}

message VcBaseResponse {
  // 状态码
  int32 code = 1;
  // 消息
  string message =2;
}

message GetOrderOperateLogListRequest {
  // 当前页
  int32 pageIndex=1;
  // 每页数量
  int32 pageSize=2;
  // 开始时间
  string start_time = 3;
  // 结束时间
  string end_time = 4;
}

message GetOrderOperateLogListResponse {
  int32 code = 1;
  string message = 2;
  int32 total =3;
  repeated OrderOperateLogData data = 4;
}

message OrderOperateLogData {
  // 操作日志id
  int32 id = 1;
  // 操作人
  string user_name = 2;
  // 操作时间
  string create_time = 3;
  // 订单号
  string order_sn = 4;
  // 卡名称
  string card_name = 5;
  // 购买时间
  string pay_time = 6;
}

message VipVrRefundDetailReq {
  //会员卡订单号
  int32 refund_id = 1;
  //用户id(前端不用传)
  string user_id = 2;
}

message VipVrRefundDetailResp {
  int32 code = 1;
  string message = 2;
  VipVrRefundDetailData data = 3;
}

message VipVrRefundDetailData {
  // 申请批次
  int32 refund_id = 1;
  // 用户手机号
  string mobile = 2;
  // 订单号
  string order_sn = 3;
  // erp订单号
  string erp_order_sn = 4;
  // 申请类型：0-未知，1-仅注销身份和权益；2-退款+注销身份和权益
  int32 apply_type = 5;
  // 会员购买记录
  repeated VipCardOrderData order = 6;
  // 会员优惠
  repeated EquityData equity = 7;
  // 系统推荐：0-可退款；1-拒绝
  int32 advise = 8;
  // 用户id
  string scrm_user_id = 9;
}

message VipCardOrderData {
  // 大区id:-1-全国、13-浙闵二区
  int32 or_id = 1;
  // 卡模板id
  int32 card_id = 2;
  // 卡名称
  string card_name = 3;
  // 购买金额
  int32 order_amount = 4;
  // 开卡时间
  string pay_time = 5;
  // 过期时间
  string expiry_date = 6;
  // 订单状态：0-已取消，10(默认)-未付款，20-已付款，40-已完成
  int32 order_state = 7;
  // 用户id
  string user_id = 8;
  // 会员id
  int32 member_id = 9;
}

message EquityData {
  // 权益名称
  string equity_name = 1;
  // 权益类型
  int32 equity_type = 2;
  // 是否已使用：0-否，1-是
  int32 used_state = 3;
  // 权益相关详细数据，json字符串
  string equity_content = 4;
  // 礼包电商父订单号
  string gift_order_sn = 5;
}