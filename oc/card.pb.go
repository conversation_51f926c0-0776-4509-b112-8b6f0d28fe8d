// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/card.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type RefundExamineReq struct {
	// 退款id
	RefundId int32 `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id"`
	//备注或者拒绝原因
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason"`
	// 退款金额
	RefundAmount int32 `protobuf:"varint,3,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	//审核状态:1为待审核,2为同意,3为不同意
	State int32 `protobuf:"varint,4,opt,name=state,proto3" json:"state"`
	// 分销id
	UserName string `protobuf:"bytes,5,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 分销类型 1-链接 2-扫码
	UserNo string `protobuf:"bytes,6,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	//审核类型：0-默认人工 1-系统自动
	AdminType            int32    `protobuf:"varint,7,opt,name=admin_type,json=adminType,proto3" json:"admin_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundExamineReq) Reset()         { *m = RefundExamineReq{} }
func (m *RefundExamineReq) String() string { return proto.CompactTextString(m) }
func (*RefundExamineReq) ProtoMessage()    {}
func (*RefundExamineReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{0}
}

func (m *RefundExamineReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundExamineReq.Unmarshal(m, b)
}
func (m *RefundExamineReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundExamineReq.Marshal(b, m, deterministic)
}
func (m *RefundExamineReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundExamineReq.Merge(m, src)
}
func (m *RefundExamineReq) XXX_Size() int {
	return xxx_messageInfo_RefundExamineReq.Size(m)
}
func (m *RefundExamineReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundExamineReq.DiscardUnknown(m)
}

var xxx_messageInfo_RefundExamineReq proto.InternalMessageInfo

func (m *RefundExamineReq) GetRefundId() int32 {
	if m != nil {
		return m.RefundId
	}
	return 0
}

func (m *RefundExamineReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *RefundExamineReq) GetRefundAmount() int32 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *RefundExamineReq) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *RefundExamineReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *RefundExamineReq) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *RefundExamineReq) GetAdminType() int32 {
	if m != nil {
		return m.AdminType
	}
	return 0
}

type CardBaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardBaseResponse) Reset()         { *m = CardBaseResponse{} }
func (m *CardBaseResponse) String() string { return proto.CompactTextString(m) }
func (*CardBaseResponse) ProtoMessage()    {}
func (*CardBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{1}
}

func (m *CardBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardBaseResponse.Unmarshal(m, b)
}
func (m *CardBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardBaseResponse.Marshal(b, m, deterministic)
}
func (m *CardBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardBaseResponse.Merge(m, src)
}
func (m *CardBaseResponse) XXX_Size() int {
	return xxx_messageInfo_CardBaseResponse.Size(m)
}
func (m *CardBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CardBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CardBaseResponse proto.InternalMessageInfo

func (m *CardBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CardBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type CardNewReq struct {
	// 卡id
	CardId int32 `protobuf:"varint,2,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	// 前端不用传，用户id
	ScrmId string `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 前端不用传，用户名
	UserName string `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 前端不用传，从请求头解析
	UserAgent int32 `protobuf:"varint,5,opt,name=user_agent,json=userAgent,proto3" json:"user_agent"`
	// 分销id
	DisId int32 `protobuf:"varint,6,opt,name=dis_id,json=disId,proto3" json:"dis_id"`
	// 分销类型 1-链接 2-扫码
	DisType int32 `protobuf:"varint,7,opt,name=dis_type,json=disType,proto3" json:"dis_type"`
	// 来源，0默认，1兑换码激活，2门店开卡
	Source               int32    `protobuf:"varint,8,opt,name=source,proto3" json:"source"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardNewReq) Reset()         { *m = CardNewReq{} }
func (m *CardNewReq) String() string { return proto.CompactTextString(m) }
func (*CardNewReq) ProtoMessage()    {}
func (*CardNewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{2}
}

func (m *CardNewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardNewReq.Unmarshal(m, b)
}
func (m *CardNewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardNewReq.Marshal(b, m, deterministic)
}
func (m *CardNewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardNewReq.Merge(m, src)
}
func (m *CardNewReq) XXX_Size() int {
	return xxx_messageInfo_CardNewReq.Size(m)
}
func (m *CardNewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardNewReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardNewReq proto.InternalMessageInfo

func (m *CardNewReq) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *CardNewReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *CardNewReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *CardNewReq) GetUserAgent() int32 {
	if m != nil {
		return m.UserAgent
	}
	return 0
}

func (m *CardNewReq) GetDisId() int32 {
	if m != nil {
		return m.DisId
	}
	return 0
}

func (m *CardNewReq) GetDisType() int32 {
	if m != nil {
		return m.DisType
	}
	return 0
}

func (m *CardNewReq) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type CardNewRes struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 订单号
	OrderSn string `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 支付截止时间戳
	Deadline             int32    `protobuf:"varint,4,opt,name=deadline,proto3" json:"deadline"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardNewRes) Reset()         { *m = CardNewRes{} }
func (m *CardNewRes) String() string { return proto.CompactTextString(m) }
func (*CardNewRes) ProtoMessage()    {}
func (*CardNewRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{3}
}

func (m *CardNewRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardNewRes.Unmarshal(m, b)
}
func (m *CardNewRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardNewRes.Marshal(b, m, deterministic)
}
func (m *CardNewRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardNewRes.Merge(m, src)
}
func (m *CardNewRes) XXX_Size() int {
	return xxx_messageInfo_CardNewRes.Size(m)
}
func (m *CardNewRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CardNewRes.DiscardUnknown(m)
}

var xxx_messageInfo_CardNewRes proto.InternalMessageInfo

func (m *CardNewRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CardNewRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CardNewRes) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CardNewRes) GetDeadline() int32 {
	if m != nil {
		return m.Deadline
	}
	return 0
}

type CardNewByCodeReq struct {
	// 兑换码
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	// 前端不用传，用户id
	ScrmId string `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 前端不用传，用户名
	UserName string `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 前端不用传，从请求头解析
	UserAgent            int32    `protobuf:"varint,5,opt,name=user_agent,json=userAgent,proto3" json:"user_agent"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardNewByCodeReq) Reset()         { *m = CardNewByCodeReq{} }
func (m *CardNewByCodeReq) String() string { return proto.CompactTextString(m) }
func (*CardNewByCodeReq) ProtoMessage()    {}
func (*CardNewByCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{4}
}

func (m *CardNewByCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardNewByCodeReq.Unmarshal(m, b)
}
func (m *CardNewByCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardNewByCodeReq.Marshal(b, m, deterministic)
}
func (m *CardNewByCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardNewByCodeReq.Merge(m, src)
}
func (m *CardNewByCodeReq) XXX_Size() int {
	return xxx_messageInfo_CardNewByCodeReq.Size(m)
}
func (m *CardNewByCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardNewByCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardNewByCodeReq proto.InternalMessageInfo

func (m *CardNewByCodeReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *CardNewByCodeReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *CardNewByCodeReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *CardNewByCodeReq) GetUserAgent() int32 {
	if m != nil {
		return m.UserAgent
	}
	return 0
}

type CardNewByStoreReq struct {
	// 子龙门店id
	StoreId int32 `protobuf:"varint,1,opt,name=store_id,json=storeId,proto3" json:"store_id"`
	// 用户id
	ScrmId string `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 卡模板id
	CardId               int32    `protobuf:"varint,4,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardNewByStoreReq) Reset()         { *m = CardNewByStoreReq{} }
func (m *CardNewByStoreReq) String() string { return proto.CompactTextString(m) }
func (*CardNewByStoreReq) ProtoMessage()    {}
func (*CardNewByStoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{5}
}

func (m *CardNewByStoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardNewByStoreReq.Unmarshal(m, b)
}
func (m *CardNewByStoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardNewByStoreReq.Marshal(b, m, deterministic)
}
func (m *CardNewByStoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardNewByStoreReq.Merge(m, src)
}
func (m *CardNewByStoreReq) XXX_Size() int {
	return xxx_messageInfo_CardNewByStoreReq.Size(m)
}
func (m *CardNewByStoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardNewByStoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardNewByStoreReq proto.InternalMessageInfo

func (m *CardNewByStoreReq) GetStoreId() int32 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *CardNewByStoreReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *CardNewByStoreReq) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type CardNewByStoreRes struct {
	// 状态码，200成功，400失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string                  `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *CardNewByStoreRes_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *CardNewByStoreRes) Reset()         { *m = CardNewByStoreRes{} }
func (m *CardNewByStoreRes) String() string { return proto.CompactTextString(m) }
func (*CardNewByStoreRes) ProtoMessage()    {}
func (*CardNewByStoreRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{6}
}

func (m *CardNewByStoreRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardNewByStoreRes.Unmarshal(m, b)
}
func (m *CardNewByStoreRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardNewByStoreRes.Marshal(b, m, deterministic)
}
func (m *CardNewByStoreRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardNewByStoreRes.Merge(m, src)
}
func (m *CardNewByStoreRes) XXX_Size() int {
	return xxx_messageInfo_CardNewByStoreRes.Size(m)
}
func (m *CardNewByStoreRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CardNewByStoreRes.DiscardUnknown(m)
}

var xxx_messageInfo_CardNewByStoreRes proto.InternalMessageInfo

func (m *CardNewByStoreRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CardNewByStoreRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CardNewByStoreRes) GetData() *CardNewByStoreRes_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type CardNewByStoreRes_Data struct {
	// 卡订单号
	OrderSn              string   `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardNewByStoreRes_Data) Reset()         { *m = CardNewByStoreRes_Data{} }
func (m *CardNewByStoreRes_Data) String() string { return proto.CompactTextString(m) }
func (*CardNewByStoreRes_Data) ProtoMessage()    {}
func (*CardNewByStoreRes_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{6, 0}
}

func (m *CardNewByStoreRes_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardNewByStoreRes_Data.Unmarshal(m, b)
}
func (m *CardNewByStoreRes_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardNewByStoreRes_Data.Marshal(b, m, deterministic)
}
func (m *CardNewByStoreRes_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardNewByStoreRes_Data.Merge(m, src)
}
func (m *CardNewByStoreRes_Data) XXX_Size() int {
	return xxx_messageInfo_CardNewByStoreRes_Data.Size(m)
}
func (m *CardNewByStoreRes_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_CardNewByStoreRes_Data.DiscardUnknown(m)
}

var xxx_messageInfo_CardNewByStoreRes_Data proto.InternalMessageInfo

func (m *CardNewByStoreRes_Data) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type CardPayNotifyReq struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 来源，0默认，1兑换码激活，2门店开卡
	Source               int32    `protobuf:"varint,2,opt,name=source,proto3" json:"source"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardPayNotifyReq) Reset()         { *m = CardPayNotifyReq{} }
func (m *CardPayNotifyReq) String() string { return proto.CompactTextString(m) }
func (*CardPayNotifyReq) ProtoMessage()    {}
func (*CardPayNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{7}
}

func (m *CardPayNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardPayNotifyReq.Unmarshal(m, b)
}
func (m *CardPayNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardPayNotifyReq.Marshal(b, m, deterministic)
}
func (m *CardPayNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardPayNotifyReq.Merge(m, src)
}
func (m *CardPayNotifyReq) XXX_Size() int {
	return xxx_messageInfo_CardPayNotifyReq.Size(m)
}
func (m *CardPayNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardPayNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardPayNotifyReq proto.InternalMessageInfo

func (m *CardPayNotifyReq) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CardPayNotifyReq) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type CardServicePackCreateReq struct {
	AwOrderSn            string   `protobuf:"bytes,1,opt,name=aw_order_sn,json=awOrderSn,proto3" json:"aw_order_sn"`
	MemberId             string   `protobuf:"bytes,2,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	MemberMobile         string   `protobuf:"bytes,3,opt,name=member_mobile,json=memberMobile,proto3" json:"member_mobile"`
	MemberName           string   `protobuf:"bytes,4,opt,name=member_name,json=memberName,proto3" json:"member_name"`
	OrderSource          int32    `protobuf:"varint,5,opt,name=order_source,json=orderSource,proto3" json:"order_source"`
	OrderSourceChannel   int32    `protobuf:"varint,6,opt,name=order_source_channel,json=orderSourceChannel,proto3" json:"order_source_channel"`
	OriginalPrice        int32    `protobuf:"varint,7,opt,name=original_price,json=originalPrice,proto3" json:"original_price"`
	PackBuyWay           int32    `protobuf:"varint,8,opt,name=pack_buy_way,json=packBuyWay,proto3" json:"pack_buy_way"`
	PayPrice             int32    `protobuf:"varint,9,opt,name=pay_price,json=payPrice,proto3" json:"pay_price"`
	SkuId                int32    `protobuf:"varint,10,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardServicePackCreateReq) Reset()         { *m = CardServicePackCreateReq{} }
func (m *CardServicePackCreateReq) String() string { return proto.CompactTextString(m) }
func (*CardServicePackCreateReq) ProtoMessage()    {}
func (*CardServicePackCreateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{8}
}

func (m *CardServicePackCreateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardServicePackCreateReq.Unmarshal(m, b)
}
func (m *CardServicePackCreateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardServicePackCreateReq.Marshal(b, m, deterministic)
}
func (m *CardServicePackCreateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardServicePackCreateReq.Merge(m, src)
}
func (m *CardServicePackCreateReq) XXX_Size() int {
	return xxx_messageInfo_CardServicePackCreateReq.Size(m)
}
func (m *CardServicePackCreateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardServicePackCreateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardServicePackCreateReq proto.InternalMessageInfo

func (m *CardServicePackCreateReq) GetAwOrderSn() string {
	if m != nil {
		return m.AwOrderSn
	}
	return ""
}

func (m *CardServicePackCreateReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *CardServicePackCreateReq) GetMemberMobile() string {
	if m != nil {
		return m.MemberMobile
	}
	return ""
}

func (m *CardServicePackCreateReq) GetMemberName() string {
	if m != nil {
		return m.MemberName
	}
	return ""
}

func (m *CardServicePackCreateReq) GetOrderSource() int32 {
	if m != nil {
		return m.OrderSource
	}
	return 0
}

func (m *CardServicePackCreateReq) GetOrderSourceChannel() int32 {
	if m != nil {
		return m.OrderSourceChannel
	}
	return 0
}

func (m *CardServicePackCreateReq) GetOriginalPrice() int32 {
	if m != nil {
		return m.OriginalPrice
	}
	return 0
}

func (m *CardServicePackCreateReq) GetPackBuyWay() int32 {
	if m != nil {
		return m.PackBuyWay
	}
	return 0
}

func (m *CardServicePackCreateReq) GetPayPrice() int32 {
	if m != nil {
		return m.PayPrice
	}
	return 0
}

func (m *CardServicePackCreateReq) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

type CardServicePackCreateRes struct {
	Code                 int32                          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Data                 *CardServicePackCreateRes_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	Message              string                         `protobuf:"bytes,3,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *CardServicePackCreateRes) Reset()         { *m = CardServicePackCreateRes{} }
func (m *CardServicePackCreateRes) String() string { return proto.CompactTextString(m) }
func (*CardServicePackCreateRes) ProtoMessage()    {}
func (*CardServicePackCreateRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{9}
}

func (m *CardServicePackCreateRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardServicePackCreateRes.Unmarshal(m, b)
}
func (m *CardServicePackCreateRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardServicePackCreateRes.Marshal(b, m, deterministic)
}
func (m *CardServicePackCreateRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardServicePackCreateRes.Merge(m, src)
}
func (m *CardServicePackCreateRes) XXX_Size() int {
	return xxx_messageInfo_CardServicePackCreateRes.Size(m)
}
func (m *CardServicePackCreateRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CardServicePackCreateRes.DiscardUnknown(m)
}

var xxx_messageInfo_CardServicePackCreateRes proto.InternalMessageInfo

func (m *CardServicePackCreateRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CardServicePackCreateRes) GetData() *CardServicePackCreateRes_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *CardServicePackCreateRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type CardServicePackCreateRes_Data struct {
	SignId               int32    `protobuf:"varint,1,opt,name=sign_id,json=signId,proto3" json:"sign_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardServicePackCreateRes_Data) Reset()         { *m = CardServicePackCreateRes_Data{} }
func (m *CardServicePackCreateRes_Data) String() string { return proto.CompactTextString(m) }
func (*CardServicePackCreateRes_Data) ProtoMessage()    {}
func (*CardServicePackCreateRes_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{9, 0}
}

func (m *CardServicePackCreateRes_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardServicePackCreateRes_Data.Unmarshal(m, b)
}
func (m *CardServicePackCreateRes_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardServicePackCreateRes_Data.Marshal(b, m, deterministic)
}
func (m *CardServicePackCreateRes_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardServicePackCreateRes_Data.Merge(m, src)
}
func (m *CardServicePackCreateRes_Data) XXX_Size() int {
	return xxx_messageInfo_CardServicePackCreateRes_Data.Size(m)
}
func (m *CardServicePackCreateRes_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_CardServicePackCreateRes_Data.DiscardUnknown(m)
}

var xxx_messageInfo_CardServicePackCreateRes_Data proto.InternalMessageInfo

func (m *CardServicePackCreateRes_Data) GetSignId() int32 {
	if m != nil {
		return m.SignId
	}
	return 0
}

type CardServicePackActivityReq struct {
	// 前端不用传，用户id
	MemberId string `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	// 宠物年龄，记录宠物当时的年龄
	PetAge string `protobuf:"bytes,2,opt,name=pet_age,json=petAge,proto3" json:"pet_age"`
	// 宠物头像
	PetAvatar string `protobuf:"bytes,3,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar"`
	// 生日例如：2021-10-01 00:00:00
	PetBirthday string `protobuf:"bytes,4,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	// 宠物ID
	PetId string `protobuf:"bytes,5,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	// 宠物种类大分类
	PetKindOf string `protobuf:"bytes,6,opt,name=pet_kind_of,json=petKindOf,proto3" json:"pet_kind_of"`
	// 宠物名字
	PetName string `protobuf:"bytes,7,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	// 性别：1GG,2MM
	PetSex int32 `protobuf:"varint,8,opt,name=pet_sex,json=petSex,proto3" json:"pet_sex"`
	// 种类
	PetVariety string `protobuf:"bytes,9,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	// 签约id
	SignId               int32    `protobuf:"varint,10,opt,name=sign_id,json=signId,proto3" json:"sign_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardServicePackActivityReq) Reset()         { *m = CardServicePackActivityReq{} }
func (m *CardServicePackActivityReq) String() string { return proto.CompactTextString(m) }
func (*CardServicePackActivityReq) ProtoMessage()    {}
func (*CardServicePackActivityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{10}
}

func (m *CardServicePackActivityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardServicePackActivityReq.Unmarshal(m, b)
}
func (m *CardServicePackActivityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardServicePackActivityReq.Marshal(b, m, deterministic)
}
func (m *CardServicePackActivityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardServicePackActivityReq.Merge(m, src)
}
func (m *CardServicePackActivityReq) XXX_Size() int {
	return xxx_messageInfo_CardServicePackActivityReq.Size(m)
}
func (m *CardServicePackActivityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardServicePackActivityReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardServicePackActivityReq proto.InternalMessageInfo

func (m *CardServicePackActivityReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *CardServicePackActivityReq) GetPetAge() string {
	if m != nil {
		return m.PetAge
	}
	return ""
}

func (m *CardServicePackActivityReq) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *CardServicePackActivityReq) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *CardServicePackActivityReq) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *CardServicePackActivityReq) GetPetKindOf() string {
	if m != nil {
		return m.PetKindOf
	}
	return ""
}

func (m *CardServicePackActivityReq) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *CardServicePackActivityReq) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *CardServicePackActivityReq) GetPetVariety() string {
	if m != nil {
		return m.PetVariety
	}
	return ""
}

func (m *CardServicePackActivityReq) GetSignId() int32 {
	if m != nil {
		return m.SignId
	}
	return 0
}

type CardRefundReq struct {
	// 退款单号
	RefundSn             string   `protobuf:"bytes,1,opt,name=refund_sn,json=refundSn,proto3" json:"refund_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardRefundReq) Reset()         { *m = CardRefundReq{} }
func (m *CardRefundReq) String() string { return proto.CompactTextString(m) }
func (*CardRefundReq) ProtoMessage()    {}
func (*CardRefundReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{11}
}

func (m *CardRefundReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardRefundReq.Unmarshal(m, b)
}
func (m *CardRefundReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardRefundReq.Marshal(b, m, deterministic)
}
func (m *CardRefundReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardRefundReq.Merge(m, src)
}
func (m *CardRefundReq) XXX_Size() int {
	return xxx_messageInfo_CardRefundReq.Size(m)
}
func (m *CardRefundReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardRefundReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardRefundReq proto.InternalMessageInfo

func (m *CardRefundReq) GetRefundSn() string {
	if m != nil {
		return m.RefundSn
	}
	return ""
}

type CardEquityReceiveReq struct {
	// 家庭医生服务包 签约id
	SignId int32 `protobuf:"varint,1,opt,name=sign_id,json=signId,proto3" json:"sign_id"`
	// 会员卡权益领取 卡订单号
	OrderSn string `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 权益类型：1商城优惠券，2子龙门店券，8打折卡，与type、equity_id二选一
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	// 权益id，type、equity_id二选一
	EquityId int32 `protobuf:"varint,6,opt,name=equity_id,json=equityId,proto3" json:"equity_id"`
	// 权益值 优惠券id
	Id string `protobuf:"bytes,4,opt,name=id,proto3" json:"id"`
	// 前端不用传，用户id
	ScrmId string `protobuf:"bytes,5,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 0开卡发券， 1月度领券
	IsMonth int32 `protobuf:"varint,7,opt,name=is_month,json=isMonth,proto3" json:"is_month"`
	// 创建时间
	CreateTime           string   `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardEquityReceiveReq) Reset()         { *m = CardEquityReceiveReq{} }
func (m *CardEquityReceiveReq) String() string { return proto.CompactTextString(m) }
func (*CardEquityReceiveReq) ProtoMessage()    {}
func (*CardEquityReceiveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{12}
}

func (m *CardEquityReceiveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardEquityReceiveReq.Unmarshal(m, b)
}
func (m *CardEquityReceiveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardEquityReceiveReq.Marshal(b, m, deterministic)
}
func (m *CardEquityReceiveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardEquityReceiveReq.Merge(m, src)
}
func (m *CardEquityReceiveReq) XXX_Size() int {
	return xxx_messageInfo_CardEquityReceiveReq.Size(m)
}
func (m *CardEquityReceiveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardEquityReceiveReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardEquityReceiveReq proto.InternalMessageInfo

func (m *CardEquityReceiveReq) GetSignId() int32 {
	if m != nil {
		return m.SignId
	}
	return 0
}

func (m *CardEquityReceiveReq) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CardEquityReceiveReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CardEquityReceiveReq) GetEquityId() int32 {
	if m != nil {
		return m.EquityId
	}
	return 0
}

func (m *CardEquityReceiveReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CardEquityReceiveReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *CardEquityReceiveReq) GetIsMonth() int32 {
	if m != nil {
		return m.IsMonth
	}
	return 0
}

func (m *CardEquityReceiveReq) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

type QuerySignIdReq struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 前端不用传，用户id
	ScrmId               string   `protobuf:"bytes,5,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuerySignIdReq) Reset()         { *m = QuerySignIdReq{} }
func (m *QuerySignIdReq) String() string { return proto.CompactTextString(m) }
func (*QuerySignIdReq) ProtoMessage()    {}
func (*QuerySignIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{13}
}

func (m *QuerySignIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySignIdReq.Unmarshal(m, b)
}
func (m *QuerySignIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySignIdReq.Marshal(b, m, deterministic)
}
func (m *QuerySignIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySignIdReq.Merge(m, src)
}
func (m *QuerySignIdReq) XXX_Size() int {
	return xxx_messageInfo_QuerySignIdReq.Size(m)
}
func (m *QuerySignIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySignIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySignIdReq proto.InternalMessageInfo

func (m *QuerySignIdReq) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *QuerySignIdReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type QuerySignIdRes struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 签约id
	SignId               int32    `protobuf:"varint,3,opt,name=sign_id,json=signId,proto3" json:"sign_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuerySignIdRes) Reset()         { *m = QuerySignIdRes{} }
func (m *QuerySignIdRes) String() string { return proto.CompactTextString(m) }
func (*QuerySignIdRes) ProtoMessage()    {}
func (*QuerySignIdRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{14}
}

func (m *QuerySignIdRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySignIdRes.Unmarshal(m, b)
}
func (m *QuerySignIdRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySignIdRes.Marshal(b, m, deterministic)
}
func (m *QuerySignIdRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySignIdRes.Merge(m, src)
}
func (m *QuerySignIdRes) XXX_Size() int {
	return xxx_messageInfo_QuerySignIdRes.Size(m)
}
func (m *QuerySignIdRes) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySignIdRes.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySignIdRes proto.InternalMessageInfo

func (m *QuerySignIdRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QuerySignIdRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QuerySignIdRes) GetSignId() int32 {
	if m != nil {
		return m.SignId
	}
	return 0
}

type CheckCardIdReq struct {
	// 卡id
	CardId               int32    `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCardIdReq) Reset()         { *m = CheckCardIdReq{} }
func (m *CheckCardIdReq) String() string { return proto.CompactTextString(m) }
func (*CheckCardIdReq) ProtoMessage()    {}
func (*CheckCardIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{15}
}

func (m *CheckCardIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCardIdReq.Unmarshal(m, b)
}
func (m *CheckCardIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCardIdReq.Marshal(b, m, deterministic)
}
func (m *CheckCardIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCardIdReq.Merge(m, src)
}
func (m *CheckCardIdReq) XXX_Size() int {
	return xxx_messageInfo_CheckCardIdReq.Size(m)
}
func (m *CheckCardIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCardIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCardIdReq proto.InternalMessageInfo

func (m *CheckCardIdReq) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type CheckCardIdRes struct {
	// 状态码，200成功，400失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message              string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *CheckCardIdRes_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CheckCardIdRes) Reset()         { *m = CheckCardIdRes{} }
func (m *CheckCardIdRes) String() string { return proto.CompactTextString(m) }
func (*CheckCardIdRes) ProtoMessage()    {}
func (*CheckCardIdRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{16}
}

func (m *CheckCardIdRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCardIdRes.Unmarshal(m, b)
}
func (m *CheckCardIdRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCardIdRes.Marshal(b, m, deterministic)
}
func (m *CheckCardIdRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCardIdRes.Merge(m, src)
}
func (m *CheckCardIdRes) XXX_Size() int {
	return xxx_messageInfo_CheckCardIdRes.Size(m)
}
func (m *CheckCardIdRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCardIdRes.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCardIdRes proto.InternalMessageInfo

func (m *CheckCardIdRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CheckCardIdRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CheckCardIdRes) GetData() *CheckCardIdRes_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type CheckCardIdRes_Data struct {
	// true 有效，false无效
	Result bool `protobuf:"varint,3,opt,name=result,proto3" json:"result"`
	// 有效时返回 卡名称
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCardIdRes_Data) Reset()         { *m = CheckCardIdRes_Data{} }
func (m *CheckCardIdRes_Data) String() string { return proto.CompactTextString(m) }
func (*CheckCardIdRes_Data) ProtoMessage()    {}
func (*CheckCardIdRes_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_52d46bfacf3fd838, []int{16, 0}
}

func (m *CheckCardIdRes_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCardIdRes_Data.Unmarshal(m, b)
}
func (m *CheckCardIdRes_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCardIdRes_Data.Marshal(b, m, deterministic)
}
func (m *CheckCardIdRes_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCardIdRes_Data.Merge(m, src)
}
func (m *CheckCardIdRes_Data) XXX_Size() int {
	return xxx_messageInfo_CheckCardIdRes_Data.Size(m)
}
func (m *CheckCardIdRes_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCardIdRes_Data.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCardIdRes_Data proto.InternalMessageInfo

func (m *CheckCardIdRes_Data) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

func (m *CheckCardIdRes_Data) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func init() {
	proto.RegisterType((*RefundExamineReq)(nil), "oc.RefundExamineReq")
	proto.RegisterType((*CardBaseResponse)(nil), "oc.CardBaseResponse")
	proto.RegisterType((*CardNewReq)(nil), "oc.CardNewReq")
	proto.RegisterType((*CardNewRes)(nil), "oc.CardNewRes")
	proto.RegisterType((*CardNewByCodeReq)(nil), "oc.CardNewByCodeReq")
	proto.RegisterType((*CardNewByStoreReq)(nil), "oc.CardNewByStoreReq")
	proto.RegisterType((*CardNewByStoreRes)(nil), "oc.CardNewByStoreRes")
	proto.RegisterType((*CardNewByStoreRes_Data)(nil), "oc.CardNewByStoreRes.Data")
	proto.RegisterType((*CardPayNotifyReq)(nil), "oc.CardPayNotifyReq")
	proto.RegisterType((*CardServicePackCreateReq)(nil), "oc.CardServicePackCreateReq")
	proto.RegisterType((*CardServicePackCreateRes)(nil), "oc.CardServicePackCreateRes")
	proto.RegisterType((*CardServicePackCreateRes_Data)(nil), "oc.CardServicePackCreateRes.Data")
	proto.RegisterType((*CardServicePackActivityReq)(nil), "oc.CardServicePackActivityReq")
	proto.RegisterType((*CardRefundReq)(nil), "oc.CardRefundReq")
	proto.RegisterType((*CardEquityReceiveReq)(nil), "oc.CardEquityReceiveReq")
	proto.RegisterType((*QuerySignIdReq)(nil), "oc.QuerySignIdReq")
	proto.RegisterType((*QuerySignIdRes)(nil), "oc.QuerySignIdRes")
	proto.RegisterType((*CheckCardIdReq)(nil), "oc.CheckCardIdReq")
	proto.RegisterType((*CheckCardIdRes)(nil), "oc.CheckCardIdRes")
	proto.RegisterType((*CheckCardIdRes_Data)(nil), "oc.CheckCardIdRes.Data")
}

func init() { proto.RegisterFile("oc/card.proto", fileDescriptor_52d46bfacf3fd838) }

var fileDescriptor_52d46bfacf3fd838 = []byte{
	// 1212 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x57, 0xdd, 0x6e, 0x1b, 0x45,
	0x14, 0xd6, 0x3a, 0xfe, 0xdb, 0xe3, 0x38, 0x6a, 0x86, 0x94, 0xb8, 0x2e, 0xfd, 0x5b, 0x54, 0xa9,
	0x08, 0x14, 0xa0, 0xa8, 0x42, 0x82, 0x0b, 0x48, 0xdc, 0x5e, 0x58, 0x55, 0xd3, 0xb2, 0xae, 0xe8,
	0xe5, 0x32, 0xd9, 0x99, 0x24, 0x23, 0x67, 0x7f, 0xbc, 0x33, 0x1b, 0x77, 0xc5, 0x6b, 0x70, 0x0f,
	0xaf, 0xc1, 0x3d, 0xef, 0xc0, 0x6d, 0xdf, 0x80, 0x57, 0x40, 0x67, 0x66, 0xd6, 0xde, 0x75, 0xec,
	0xa0, 0x5c, 0x70, 0xb7, 0xe7, 0x3b, 0x33, 0xe7, 0xcc, 0xf9, 0xfd, 0x6c, 0xe8, 0x27, 0xe1, 0x97,
	0x21, 0xcd, 0xd8, 0x41, 0x9a, 0x25, 0x2a, 0x21, 0x8d, 0x24, 0xf4, 0xfe, 0x76, 0xe0, 0x96, 0xcf,
	0x4f, 0xf3, 0x98, 0xbd, 0x78, 0x4f, 0x23, 0x11, 0x73, 0x9f, 0xcf, 0xc8, 0x5d, 0x70, 0x33, 0x8d,
	0x05, 0x82, 0x0d, 0x9c, 0x87, 0xce, 0x93, 0x96, 0xdf, 0x35, 0xc0, 0x98, 0x91, 0x8f, 0xa1, 0x9d,
	0x71, 0x2a, 0x93, 0x78, 0xd0, 0x78, 0xe8, 0x3c, 0x71, 0x7d, 0x2b, 0x91, 0x4f, 0xa1, 0x6f, 0x2f,
	0xd1, 0x28, 0xc9, 0x63, 0x35, 0xd8, 0xd2, 0x17, 0xb7, 0x0d, 0x78, 0xa8, 0x31, 0xb2, 0x07, 0x2d,
	0xa9, 0xa8, 0xe2, 0x83, 0xa6, 0x56, 0x1a, 0x01, 0xfd, 0xe5, 0x92, 0x67, 0x41, 0x4c, 0x23, 0x3e,
	0x68, 0x69, 0xab, 0x5d, 0x04, 0x8e, 0x69, 0xc4, 0xc9, 0x3e, 0x74, 0x8c, 0x32, 0x19, 0xb4, 0x8d,
	0x43, 0xad, 0x4a, 0xc8, 0x3d, 0x00, 0xca, 0x22, 0x11, 0x07, 0xaa, 0x48, 0xf9, 0xa0, 0xa3, 0x0d,
	0xba, 0x1a, 0x79, 0x5b, 0xa4, 0xdc, 0xfb, 0x11, 0x6e, 0x8d, 0x68, 0xc6, 0x8e, 0xa8, 0xe4, 0x3e,
	0x97, 0x69, 0x12, 0x4b, 0x4e, 0x08, 0x34, 0xc3, 0x84, 0x71, 0x1b, 0x93, 0xfe, 0x26, 0x03, 0xe8,
	0x44, 0x5c, 0x4a, 0x7a, 0xc6, 0x6d, 0x40, 0xa5, 0xe8, 0xfd, 0xe5, 0x00, 0xa0, 0x89, 0x63, 0x3e,
	0xc7, 0xac, 0xec, 0x43, 0x07, 0x93, 0x87, 0x39, 0x69, 0xe8, 0xfb, 0x6d, 0x14, 0xc7, 0x0c, 0x15,
	0x32, 0xcc, 0x22, 0x54, 0x6c, 0x99, 0x17, 0xa2, 0x38, 0x66, 0xf5, 0xb8, 0x9a, 0x2b, 0x71, 0xdd,
	0x03, 0xd0, 0x4a, 0x7a, 0xc6, 0x63, 0xa5, 0xa3, 0x6e, 0xf9, 0xfa, 0xf8, 0x21, 0x02, 0xe4, 0x36,
	0xb4, 0x99, 0x90, 0x68, 0xb3, 0x6d, 0x52, 0xc5, 0x84, 0x1c, 0x33, 0x72, 0x07, 0xba, 0x08, 0x57,
	0x42, 0xee, 0x30, 0x21, 0x31, 0x60, 0x2c, 0x8c, 0x4c, 0xf2, 0x2c, 0xe4, 0x83, 0xae, 0x79, 0x9e,
	0x91, 0xbc, 0x59, 0x25, 0x0a, 0x79, 0xb3, 0x14, 0xa0, 0xbb, 0x24, 0x63, 0x3c, 0x0b, 0x64, 0x6c,
	0x63, 0xeb, 0x68, 0x79, 0x12, 0x93, 0x21, 0x74, 0x19, 0xa7, 0xec, 0x42, 0xc4, 0x65, 0x35, 0x17,
	0xb2, 0xf7, 0xab, 0xc9, 0xfd, 0x31, 0x9f, 0x1f, 0x15, 0xa3, 0x84, 0xe9, 0xa6, 0xaa, 0x3a, 0x76,
	0xad, 0xe3, 0xff, 0x23, 0x73, 0xde, 0x2f, 0xb0, 0xbb, 0x70, 0x3e, 0x51, 0x49, 0xa6, 0xbd, 0xdf,
	0x81, 0xae, 0xc4, 0xef, 0x65, 0x47, 0x77, 0xb4, 0x7c, 0x5d, 0xf9, 0x2a, 0x05, 0x6f, 0x56, 0x0b,
	0xee, 0xfd, 0xe6, 0x5c, 0x75, 0x71, 0xd3, 0xcc, 0x1e, 0x40, 0x93, 0x51, 0x45, 0xb5, 0xcb, 0xde,
	0xd3, 0xe1, 0x41, 0x12, 0x1e, 0x5c, 0x31, 0x79, 0xf0, 0x9c, 0x2a, 0xea, 0xeb, 0x73, 0xc3, 0x47,
	0xd0, 0x44, 0xe9, 0x9a, 0x8a, 0x78, 0x2f, 0x4c, 0xd6, 0xdf, 0xd0, 0xe2, 0x38, 0x51, 0xe2, 0xb4,
	0xb0, 0x71, 0x2f, 0x8e, 0x3b, 0xf5, 0x02, 0x2e, 0xfb, 0xa5, 0x51, 0xeb, 0x97, 0x7f, 0x1a, 0x30,
	0x40, 0x3b, 0x13, 0x9e, 0x5d, 0x8a, 0x90, 0xbf, 0xa1, 0xe1, 0x74, 0x94, 0x71, 0xaa, 0x74, 0x1e,
	0xef, 0x43, 0x8f, 0xce, 0x83, 0x15, 0x93, 0x2e, 0x9d, 0xbf, 0xb6, 0x46, 0xef, 0x82, 0x1b, 0xf1,
	0xe8, 0x84, 0x67, 0xe5, 0x98, 0xb8, 0x7e, 0xd7, 0x00, 0x63, 0x86, 0x2b, 0xc2, 0x2a, 0xa3, 0xe4,
	0x44, 0x5c, 0x70, 0x1b, 0xc0, 0xb6, 0x01, 0x5f, 0x69, 0x8c, 0x3c, 0x80, 0x9e, 0x3d, 0x54, 0x29,
	0x3e, 0x18, 0x48, 0x97, 0xff, 0x11, 0x6c, 0x5b, 0xff, 0xe6, 0xf5, 0xa6, 0x01, 0x7a, 0x26, 0x2c,
	0x0d, 0x91, 0xaf, 0x60, 0xaf, 0x7a, 0x24, 0x08, 0xcf, 0x69, 0x1c, 0xf3, 0x0b, 0x3b, 0x4a, 0xa4,
	0x72, 0x74, 0x64, 0x34, 0xe4, 0x31, 0xec, 0x24, 0x99, 0x38, 0x13, 0x31, 0xbd, 0x08, 0xd2, 0x4c,
	0x84, 0xe5, 0x74, 0xf5, 0x4b, 0xf4, 0x0d, 0x82, 0xe4, 0x21, 0x6c, 0xa7, 0x34, 0x9c, 0x06, 0x27,
	0x79, 0x11, 0xcc, 0x69, 0x61, 0x27, 0x0d, 0x10, 0x3b, 0xca, 0x8b, 0x77, 0xb4, 0xc0, 0x04, 0xa4,
	0xb4, 0xb0, 0x36, 0x5c, 0x33, 0x17, 0x29, 0x2d, 0xcc, 0xf5, 0xdb, 0xd0, 0x96, 0xd3, 0x1c, 0x53,
	0x03, 0x76, 0xff, 0x4d, 0xf3, 0x31, 0xf3, 0xfe, 0x70, 0x36, 0x66, 0x7c, 0x7d, 0x5b, 0x3d, 0xb3,
	0xcd, 0xd3, 0xd0, 0xcd, 0xf3, 0xa8, 0x6c, 0x9e, 0x75, 0xf7, 0x2b, 0x3d, 0x54, 0xed, 0xc6, 0xad,
	0x5a, 0x37, 0x0e, 0x1f, 0xd8, 0xee, 0xc2, 0x59, 0x10, 0x67, 0xf1, 0x72, 0x4a, 0xda, 0x28, 0x8e,
	0x99, 0xf7, 0x67, 0x03, 0x86, 0x2b, 0x2e, 0x0e, 0x43, 0x25, 0x2e, 0x85, 0x2a, 0x2c, 0x63, 0x2c,
	0xcb, 0xee, 0xac, 0x94, 0x7d, 0x1f, 0x3a, 0x29, 0x57, 0xc1, 0x72, 0x08, 0xda, 0x29, 0x57, 0x87,
	0x67, 0x7a, 0x90, 0xb5, 0xe2, 0x92, 0x2a, 0x9a, 0xd9, 0x27, 0xb9, 0xa8, 0xd3, 0x00, 0x16, 0x1a,
	0xd5, 0x27, 0x22, 0x53, 0xe7, 0x8c, 0x16, 0xb6, 0x15, 0x7a, 0x29, 0x57, 0x47, 0x16, 0xc2, 0x84,
	0xe2, 0x11, 0xc1, 0x2c, 0x6d, 0xb4, 0x52, 0xae, 0xc6, 0x0c, 0xbb, 0x14, 0xe1, 0xa9, 0x88, 0x59,
	0x90, 0x9c, 0x5a, 0xde, 0x40, 0xcb, 0x2f, 0x45, 0xcc, 0x5e, 0x9f, 0xe2, 0x54, 0xa0, 0x5e, 0x37,
	0x58, 0xc7, 0x64, 0x22, 0xe5, 0xaa, 0xa4, 0x1b, 0x54, 0x49, 0xfe, 0xbe, 0x5c, 0xa3, 0x29, 0x57,
	0x13, 0xfe, 0x1e, 0xfb, 0x12, 0x15, 0x97, 0x34, 0x13, 0x5c, 0x15, 0xba, 0xb4, 0xae, 0x8f, 0xef,
	0xff, 0xd9, 0x20, 0xd5, 0xdc, 0x41, 0x2d, 0x77, 0x5f, 0x40, 0x1f, 0x53, 0x67, 0x68, 0xb6, 0xce,
	0xaf, 0x8b, 0x11, 0xb2, 0xfc, 0x3a, 0x89, 0xbd, 0x0f, 0x0e, 0xec, 0xe1, 0xf1, 0x17, 0xb3, 0x5c,
	0x27, 0x37, 0xe4, 0xe2, 0x92, 0x5b, 0xfe, 0x59, 0x5b, 0x9b, 0xda, 0x8c, 0x37, 0xea, 0x33, 0x4e,
	0xa0, 0xa9, 0xa9, 0xc2, 0x70, 0xb1, 0xfe, 0x46, 0xef, 0x5c, 0xdb, 0x5e, 0x92, 0x4b, 0xd7, 0x00,
	0x63, 0x46, 0x76, 0xa0, 0x61, 0xd7, 0x9d, 0xeb, 0x37, 0x44, 0x6d, 0x39, 0xb6, 0x6a, 0xcb, 0xf1,
	0x0e, 0x74, 0x85, 0x0c, 0xa2, 0x24, 0x56, 0xe7, 0x25, 0x11, 0x09, 0xf9, 0x0a, 0x45, 0xcc, 0x54,
	0xa8, 0xdb, 0x2f, 0x50, 0x22, 0x32, 0x6c, 0xe4, 0xfa, 0x60, 0xa0, 0xb7, 0x22, 0xe2, 0xde, 0x73,
	0xd8, 0xf9, 0x29, 0xe7, 0x59, 0x31, 0xd1, 0xef, 0xff, 0x8f, 0x35, 0xb5, 0xe9, 0x05, 0xde, 0xbb,
	0x15, 0x2b, 0x37, 0xdd, 0xc0, 0x95, 0x7c, 0x6e, 0xd5, 0xea, 0xf5, 0x19, 0xec, 0x8c, 0xce, 0x79,
	0x38, 0x1d, 0xe9, 0x6d, 0xbf, 0x42, 0xfd, 0x4e, 0x8d, 0x09, 0x7e, 0x77, 0x56, 0xce, 0xde, 0xf4,
	0x11, 0x9f, 0xd7, 0x68, 0x60, 0x5f, 0x4f, 0x72, 0xcd, 0x5e, 0x95, 0x03, 0x9e, 0xda, 0x29, 0xd5,
	0x3f, 0xc1, 0x64, 0x7e, 0x61, 0x7e, 0x63, 0x75, 0x7d, 0x2b, 0xa1, 0xeb, 0xca, 0xce, 0xd4, 0xdf,
	0x4f, 0x3f, 0xb4, 0xa0, 0x57, 0x19, 0x5c, 0xf2, 0x18, 0xb6, 0x8e, 0xf9, 0x9c, 0xec, 0x54, 0x08,
	0xc7, 0xe7, 0xb3, 0x61, 0x5d, 0x96, 0xe4, 0x5b, 0x70, 0x17, 0xec, 0x4d, 0xf6, 0x6a, 0xec, 0x64,
	0x09, 0x7d, 0xb8, 0x40, 0x6b, 0x3f, 0xb1, 0xbe, 0x03, 0x58, 0x72, 0x18, 0xb9, 0xbd, 0x8e, 0xd7,
	0x66, 0xc3, 0xb5, 0xb0, 0x76, 0xba, 0x20, 0xaf, 0xa5, 0xd3, 0x2a, 0x9f, 0x6d, 0x70, 0xfa, 0x1a,
	0x76, 0xaf, 0xec, 0x3e, 0xf2, 0xc9, 0x35, 0x6b, 0x71, 0x36, 0xbc, 0x4e, 0x2b, 0xc9, 0x4b, 0xf8,
	0x68, 0xcd, 0xa6, 0x23, 0xf7, 0xd7, 0x5c, 0xaa, 0xac, 0xc1, 0x0d, 0xaf, 0xfb, 0x1a, 0xda, 0x66,
	0xf6, 0xc9, 0x6e, 0xa9, 0x5f, 0xec, 0x82, 0x0d, 0x57, 0x7e, 0x80, 0x7e, 0x6d, 0xfe, 0xc9, 0xa0,
	0x3c, 0xb6, 0xba, 0x16, 0x36, 0x18, 0x78, 0x06, 0xbd, 0xca, 0x70, 0x10, 0x82, 0x87, 0xea, 0x33,
	0x37, 0xbc, 0x8a, 0x49, 0xbc, 0x56, 0x69, 0x3f, 0x73, 0xad, 0x3e, 0x0b, 0xc3, 0xab, 0x98, 0x24,
	0xdf, 0x43, 0xbf, 0xf6, 0x27, 0xc2, 0x14, 0x6f, 0xf5, 0x7f, 0xc5, 0xc6, 0x58, 0x77, 0x4d, 0x4a,
	0x54, 0x9e, 0xc5, 0x65, 0xe3, 0xdc, 0xa0, 0xfa, 0x27, 0x6d, 0xfd, 0x77, 0xe6, 0x9b, 0x7f, 0x03,
	0x00, 0x00, 0xff, 0xff, 0x85, 0x8f, 0x53, 0x94, 0xdf, 0x0c, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CardServiceClient is the client API for CardService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CardServiceClient interface {
	// 开卡
	New(ctx context.Context, in *CardNewReq, opts ...grpc.CallOption) (*CardNewRes, error)
	// 通过激活码开卡
	NewByCode(ctx context.Context, in *CardNewByCodeReq, opts ...grpc.CallOption) (*CardBaseResponse, error)
	// 通过门店开卡（充储值卡送会员卡）
	NewByStore(ctx context.Context, in *CardNewByStoreReq, opts ...grpc.CallOption) (*CardNewByStoreRes, error)
	// 支付通知
	PayNotify(ctx context.Context, in *CardPayNotifyReq, opts ...grpc.CallOption) (*CardBaseResponse, error)
	// 服务包创建
	ServicePackCreate(ctx context.Context, in *CardServicePackCreateReq, opts ...grpc.CallOption) (*CardServicePackCreateRes, error)
	// 服务包激活
	ServicePackActivity(ctx context.Context, in *CardServicePackActivityReq, opts ...grpc.CallOption) (*CardBaseResponse, error)
	// 卡退款处理
	Refund(ctx context.Context, in *CardRefundReq, opts ...grpc.CallOption) (*CardBaseResponse, error)
	// 权益领取
	EquityReceive(ctx context.Context, in *CardEquityReceiveReq, opts ...grpc.CallOption) (*CardBaseResponse, error)
	// 查询签约id
	QuerySignId(ctx context.Context, in *QuerySignIdReq, opts ...grpc.CallOption) (*QuerySignIdRes, error)
	// 检测卡id是否存在
	CheckCardId(ctx context.Context, in *CheckCardIdReq, opts ...grpc.CallOption) (*CheckCardIdRes, error)
	// VIP卡退款审核
	RefundExamine(ctx context.Context, in *RefundExamineReq, opts ...grpc.CallOption) (*CardBaseResponse, error)
	// 子龙储值卡退款通知
	CardReturnByStore(ctx context.Context, in *CardPayNotifyReq, opts ...grpc.CallOption) (*CardBaseResponse, error)
}

type cardServiceClient struct {
	cc *grpc.ClientConn
}

func NewCardServiceClient(cc *grpc.ClientConn) CardServiceClient {
	return &cardServiceClient{cc}
}

func (c *cardServiceClient) New(ctx context.Context, in *CardNewReq, opts ...grpc.CallOption) (*CardNewRes, error) {
	out := new(CardNewRes)
	err := c.cc.Invoke(ctx, "/oc.CardService/New", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) NewByCode(ctx context.Context, in *CardNewByCodeReq, opts ...grpc.CallOption) (*CardBaseResponse, error) {
	out := new(CardBaseResponse)
	err := c.cc.Invoke(ctx, "/oc.CardService/NewByCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) NewByStore(ctx context.Context, in *CardNewByStoreReq, opts ...grpc.CallOption) (*CardNewByStoreRes, error) {
	out := new(CardNewByStoreRes)
	err := c.cc.Invoke(ctx, "/oc.CardService/NewByStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) PayNotify(ctx context.Context, in *CardPayNotifyReq, opts ...grpc.CallOption) (*CardBaseResponse, error) {
	out := new(CardBaseResponse)
	err := c.cc.Invoke(ctx, "/oc.CardService/PayNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) ServicePackCreate(ctx context.Context, in *CardServicePackCreateReq, opts ...grpc.CallOption) (*CardServicePackCreateRes, error) {
	out := new(CardServicePackCreateRes)
	err := c.cc.Invoke(ctx, "/oc.CardService/ServicePackCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) ServicePackActivity(ctx context.Context, in *CardServicePackActivityReq, opts ...grpc.CallOption) (*CardBaseResponse, error) {
	out := new(CardBaseResponse)
	err := c.cc.Invoke(ctx, "/oc.CardService/ServicePackActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) Refund(ctx context.Context, in *CardRefundReq, opts ...grpc.CallOption) (*CardBaseResponse, error) {
	out := new(CardBaseResponse)
	err := c.cc.Invoke(ctx, "/oc.CardService/Refund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) EquityReceive(ctx context.Context, in *CardEquityReceiveReq, opts ...grpc.CallOption) (*CardBaseResponse, error) {
	out := new(CardBaseResponse)
	err := c.cc.Invoke(ctx, "/oc.CardService/EquityReceive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) QuerySignId(ctx context.Context, in *QuerySignIdReq, opts ...grpc.CallOption) (*QuerySignIdRes, error) {
	out := new(QuerySignIdRes)
	err := c.cc.Invoke(ctx, "/oc.CardService/QuerySignId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) CheckCardId(ctx context.Context, in *CheckCardIdReq, opts ...grpc.CallOption) (*CheckCardIdRes, error) {
	out := new(CheckCardIdRes)
	err := c.cc.Invoke(ctx, "/oc.CardService/CheckCardId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) RefundExamine(ctx context.Context, in *RefundExamineReq, opts ...grpc.CallOption) (*CardBaseResponse, error) {
	out := new(CardBaseResponse)
	err := c.cc.Invoke(ctx, "/oc.CardService/RefundExamine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardServiceClient) CardReturnByStore(ctx context.Context, in *CardPayNotifyReq, opts ...grpc.CallOption) (*CardBaseResponse, error) {
	out := new(CardBaseResponse)
	err := c.cc.Invoke(ctx, "/oc.CardService/CardReturnByStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CardServiceServer is the server API for CardService service.
type CardServiceServer interface {
	// 开卡
	New(context.Context, *CardNewReq) (*CardNewRes, error)
	// 通过激活码开卡
	NewByCode(context.Context, *CardNewByCodeReq) (*CardBaseResponse, error)
	// 通过门店开卡（充储值卡送会员卡）
	NewByStore(context.Context, *CardNewByStoreReq) (*CardNewByStoreRes, error)
	// 支付通知
	PayNotify(context.Context, *CardPayNotifyReq) (*CardBaseResponse, error)
	// 服务包创建
	ServicePackCreate(context.Context, *CardServicePackCreateReq) (*CardServicePackCreateRes, error)
	// 服务包激活
	ServicePackActivity(context.Context, *CardServicePackActivityReq) (*CardBaseResponse, error)
	// 卡退款处理
	Refund(context.Context, *CardRefundReq) (*CardBaseResponse, error)
	// 权益领取
	EquityReceive(context.Context, *CardEquityReceiveReq) (*CardBaseResponse, error)
	// 查询签约id
	QuerySignId(context.Context, *QuerySignIdReq) (*QuerySignIdRes, error)
	// 检测卡id是否存在
	CheckCardId(context.Context, *CheckCardIdReq) (*CheckCardIdRes, error)
	// VIP卡退款审核
	RefundExamine(context.Context, *RefundExamineReq) (*CardBaseResponse, error)
	// 子龙储值卡退款通知
	CardReturnByStore(context.Context, *CardPayNotifyReq) (*CardBaseResponse, error)
}

// UnimplementedCardServiceServer can be embedded to have forward compatible implementations.
type UnimplementedCardServiceServer struct {
}

func (*UnimplementedCardServiceServer) New(ctx context.Context, req *CardNewReq) (*CardNewRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method New not implemented")
}
func (*UnimplementedCardServiceServer) NewByCode(ctx context.Context, req *CardNewByCodeReq) (*CardBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewByCode not implemented")
}
func (*UnimplementedCardServiceServer) NewByStore(ctx context.Context, req *CardNewByStoreReq) (*CardNewByStoreRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewByStore not implemented")
}
func (*UnimplementedCardServiceServer) PayNotify(ctx context.Context, req *CardPayNotifyReq) (*CardBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayNotify not implemented")
}
func (*UnimplementedCardServiceServer) ServicePackCreate(ctx context.Context, req *CardServicePackCreateReq) (*CardServicePackCreateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServicePackCreate not implemented")
}
func (*UnimplementedCardServiceServer) ServicePackActivity(ctx context.Context, req *CardServicePackActivityReq) (*CardBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServicePackActivity not implemented")
}
func (*UnimplementedCardServiceServer) Refund(ctx context.Context, req *CardRefundReq) (*CardBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (*UnimplementedCardServiceServer) EquityReceive(ctx context.Context, req *CardEquityReceiveReq) (*CardBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EquityReceive not implemented")
}
func (*UnimplementedCardServiceServer) QuerySignId(ctx context.Context, req *QuerySignIdReq) (*QuerySignIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySignId not implemented")
}
func (*UnimplementedCardServiceServer) CheckCardId(ctx context.Context, req *CheckCardIdReq) (*CheckCardIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckCardId not implemented")
}
func (*UnimplementedCardServiceServer) RefundExamine(ctx context.Context, req *RefundExamineReq) (*CardBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundExamine not implemented")
}
func (*UnimplementedCardServiceServer) CardReturnByStore(ctx context.Context, req *CardPayNotifyReq) (*CardBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CardReturnByStore not implemented")
}

func RegisterCardServiceServer(s *grpc.Server, srv CardServiceServer) {
	s.RegisterService(&_CardService_serviceDesc, srv)
}

func _CardService_New_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardNewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).New(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/New",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).New(ctx, req.(*CardNewReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_NewByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardNewByCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).NewByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/NewByCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).NewByCode(ctx, req.(*CardNewByCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_NewByStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardNewByStoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).NewByStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/NewByStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).NewByStore(ctx, req.(*CardNewByStoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_PayNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardPayNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).PayNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/PayNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).PayNotify(ctx, req.(*CardPayNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_ServicePackCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardServicePackCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).ServicePackCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/ServicePackCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).ServicePackCreate(ctx, req.(*CardServicePackCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_ServicePackActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardServicePackActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).ServicePackActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/ServicePackActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).ServicePackActivity(ctx, req.(*CardServicePackActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardRefundReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/Refund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).Refund(ctx, req.(*CardRefundReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_EquityReceive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardEquityReceiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).EquityReceive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/EquityReceive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).EquityReceive(ctx, req.(*CardEquityReceiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_QuerySignId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySignIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).QuerySignId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/QuerySignId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).QuerySignId(ctx, req.(*QuerySignIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_CheckCardId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCardIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).CheckCardId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/CheckCardId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).CheckCardId(ctx, req.(*CheckCardIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_RefundExamine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundExamineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).RefundExamine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/RefundExamine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).RefundExamine(ctx, req.(*RefundExamineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardService_CardReturnByStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardPayNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServiceServer).CardReturnByStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CardService/CardReturnByStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServiceServer).CardReturnByStore(ctx, req.(*CardPayNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _CardService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.CardService",
	HandlerType: (*CardServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "New",
			Handler:    _CardService_New_Handler,
		},
		{
			MethodName: "NewByCode",
			Handler:    _CardService_NewByCode_Handler,
		},
		{
			MethodName: "NewByStore",
			Handler:    _CardService_NewByStore_Handler,
		},
		{
			MethodName: "PayNotify",
			Handler:    _CardService_PayNotify_Handler,
		},
		{
			MethodName: "ServicePackCreate",
			Handler:    _CardService_ServicePackCreate_Handler,
		},
		{
			MethodName: "ServicePackActivity",
			Handler:    _CardService_ServicePackActivity_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _CardService_Refund_Handler,
		},
		{
			MethodName: "EquityReceive",
			Handler:    _CardService_EquityReceive_Handler,
		},
		{
			MethodName: "QuerySignId",
			Handler:    _CardService_QuerySignId_Handler,
		},
		{
			MethodName: "CheckCardId",
			Handler:    _CardService_CheckCardId_Handler,
		},
		{
			MethodName: "RefundExamine",
			Handler:    _CardService_RefundExamine_Handler,
		},
		{
			MethodName: "CardReturnByStore",
			Handler:    _CardService_CardReturnByStore_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/card.proto",
}
