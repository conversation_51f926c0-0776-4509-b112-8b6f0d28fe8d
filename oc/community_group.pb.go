// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/community_group.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type CommunityGroupOrderListRequest struct {
	PageSize  int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageIndex int32 `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//团状态 0拼团中 1拼团成功 2拼团失败
	GroupStatus string `protobuf:"bytes,3,opt,name=group_status,json=groupStatus,proto3" json:"group_status"`
	// 搜索类型 1团长手机 2团编码 3财务编码 4团长单位名
	SearchType string `protobuf:"bytes,4,opt,name=search_type,json=searchType,proto3" json:"search_type"`
	//搜索关键词
	Keyword string `protobuf:"bytes,5,opt,name=keyword,proto3" json:"keyword"`
	// 团长代收状态 0不代收 1代收
	GroupTakeType string `protobuf:"bytes,6,opt,name=group_take_type,json=groupTakeType,proto3" json:"group_take_type"`
	// 时间搜索类型 1开团时间 2拼成时间 3失败时间
	TimeType string `protobuf:"bytes,7,opt,name=time_type,json=timeType,proto3" json:"time_type"`
	// 开始时间
	StartTime string `protobuf:"bytes,8,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 结束时间
	EndTime string `protobuf:"bytes,9,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//登录用户所有权限的门店id。前端不用传
	ShopIds []string `protobuf:"bytes,10,rep,name=shop_ids,json=shopIds,proto3" json:"shop_ids"`
	//用户编号，前端不用传
	UserNo string `protobuf:"bytes,11,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	//ip地址
	Ip string `protobuf:"bytes,12,opt,name=ip,proto3" json:"ip"`
	//ip所属地
	IpLocation string `protobuf:"bytes,13,opt,name=ip_location,json=ipLocation,proto3" json:"ip_location"`
	// 请求来源，默认0-订单列表，1-实物订单，2-虚拟订单
	RequestFrom          int32    `protobuf:"varint,14,opt,name=request_from,json=requestFrom,proto3" json:"request_from"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupOrderListRequest) Reset()         { *m = CommunityGroupOrderListRequest{} }
func (m *CommunityGroupOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupOrderListRequest) ProtoMessage()    {}
func (*CommunityGroupOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{0}
}

func (m *CommunityGroupOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupOrderListRequest.Unmarshal(m, b)
}
func (m *CommunityGroupOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupOrderListRequest.Marshal(b, m, deterministic)
}
func (m *CommunityGroupOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupOrderListRequest.Merge(m, src)
}
func (m *CommunityGroupOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupOrderListRequest.Size(m)
}
func (m *CommunityGroupOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupOrderListRequest proto.InternalMessageInfo

func (m *CommunityGroupOrderListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CommunityGroupOrderListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CommunityGroupOrderListRequest) GetGroupStatus() string {
	if m != nil {
		return m.GroupStatus
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetSearchType() string {
	if m != nil {
		return m.SearchType
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetGroupTakeType() string {
	if m != nil {
		return m.GroupTakeType
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetTimeType() string {
	if m != nil {
		return m.TimeType
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetShopIds() []string {
	if m != nil {
		return m.ShopIds
	}
	return nil
}

func (m *CommunityGroupOrderListRequest) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetIpLocation() string {
	if m != nil {
		return m.IpLocation
	}
	return ""
}

func (m *CommunityGroupOrderListRequest) GetRequestFrom() int32 {
	if m != nil {
		return m.RequestFrom
	}
	return 0
}

type CommunityGroupOrderListResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//订单
	Details []*CommunityGroupOrderListResponse_Details `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//返回总数，用于分页
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupOrderListResponse) Reset()         { *m = CommunityGroupOrderListResponse{} }
func (m *CommunityGroupOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupOrderListResponse) ProtoMessage()    {}
func (*CommunityGroupOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{1}
}

func (m *CommunityGroupOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupOrderListResponse.Unmarshal(m, b)
}
func (m *CommunityGroupOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupOrderListResponse.Marshal(b, m, deterministic)
}
func (m *CommunityGroupOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupOrderListResponse.Merge(m, src)
}
func (m *CommunityGroupOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupOrderListResponse.Size(m)
}
func (m *CommunityGroupOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupOrderListResponse proto.InternalMessageInfo

func (m *CommunityGroupOrderListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommunityGroupOrderListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CommunityGroupOrderListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CommunityGroupOrderListResponse) GetDetails() []*CommunityGroupOrderListResponse_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *CommunityGroupOrderListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type CommunityGroupOrderListResponse_Details struct {
	//团id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//店铺财务编码
	FinanceCode string `protobuf:"bytes,2,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 店铺名称
	StoreName string `protobuf:"bytes,3,opt,name=store_name,json=storeName,proto3" json:"store_name"`
	//是否分销员 0否 1是
	IsDis int32 `protobuf:"varint,4,opt,name=is_dis,json=isDis,proto3" json:"is_dis"`
	//团状态 0开团 1拼团成功 2拼团失败
	Status int32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	//团长代收状态 0不代收 1代收
	TakeType int32 `protobuf:"varint,6,opt,name=take_type,json=takeType,proto3" json:"take_type"`
	//成团最小金额 分
	MinAmount int32 `protobuf:"varint,7,opt,name=min_amount,json=minAmount,proto3" json:"min_amount"`
	//已支付金额 分
	PayAmount int32 `protobuf:"varint,8,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//待结算金额 分
	WaitAmount int32 `protobuf:"varint,9,opt,name=wait_amount,json=waitAmount,proto3" json:"wait_amount"`
	//已结算金额 分
	PaidAmount int32 `protobuf:"varint,10,opt,name=paid_amount,json=paidAmount,proto3" json:"paid_amount"`
	//团开始时间'
	StartTime string `protobuf:"bytes,11,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//团结束时间'
	EndTime string `protobuf:"bytes,12,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//预计送达时间
	ExpectedTimeText string `protobuf:"bytes,13,opt,name=expected_time_text,json=expectedTimeText,proto3" json:"expected_time_text"`
	//创建时间
	CreatedAt string `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	//拼团成功者失败时间
	GroupAt string `protobuf:"bytes,15,opt,name=group_at,json=groupAt,proto3" json:"group_at"`
	// 开团会员id
	MemberId string `protobuf:"bytes,16,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	// 开团会员名称
	MemberName string `protobuf:"bytes,17,opt,name=member_name,json=memberName,proto3" json:"member_name"`
	// 开团会员头像
	MemberProfile string `protobuf:"bytes,18,opt,name=member_profile,json=memberProfile,proto3" json:"member_profile"`
	// 收件人
	ReceiverName string `protobuf:"bytes,19,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name"`
	// 收件手机
	ReceiverMobile string `protobuf:"bytes,20,opt,name=receiver_mobile,json=receiverMobile,proto3" json:"receiver_mobile"`
	// 收件地址
	ReceiverAddress string `protobuf:"bytes,21,opt,name=receiver_address,json=receiverAddress,proto3" json:"receiver_address"`
	//财务编码
	DisChainFinanceCode string `protobuf:"bytes,22,opt,name=dis_chain_finance_code,json=disChainFinanceCode,proto3" json:"dis_chain_finance_code"`
	//所属单位
	DisChainName string `protobuf:"bytes,23,opt,name=dis_chain_name,json=disChainName,proto3" json:"dis_chain_name"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,24,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 渠道来源
	UserAgent int32 `protobuf:"varint,25,opt,name=user_agent,json=userAgent,proto3" json:"user_agent"`
	// 用于前端统一，虚拟订单不显示预计送达时间
	DeliveryType int32 `protobuf:"varint,26,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type"`
	// 密文收件手机号
	EnReceiverMobile     string   `protobuf:"bytes,27,opt,name=en_receiver_mobile,json=enReceiverMobile,proto3" json:"en_receiver_mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupOrderListResponse_Details) Reset() {
	*m = CommunityGroupOrderListResponse_Details{}
}
func (m *CommunityGroupOrderListResponse_Details) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupOrderListResponse_Details) ProtoMessage()    {}
func (*CommunityGroupOrderListResponse_Details) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{1, 0}
}

func (m *CommunityGroupOrderListResponse_Details) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupOrderListResponse_Details.Unmarshal(m, b)
}
func (m *CommunityGroupOrderListResponse_Details) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupOrderListResponse_Details.Marshal(b, m, deterministic)
}
func (m *CommunityGroupOrderListResponse_Details) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupOrderListResponse_Details.Merge(m, src)
}
func (m *CommunityGroupOrderListResponse_Details) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupOrderListResponse_Details.Size(m)
}
func (m *CommunityGroupOrderListResponse_Details) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupOrderListResponse_Details.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupOrderListResponse_Details proto.InternalMessageInfo

func (m *CommunityGroupOrderListResponse_Details) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetStoreName() string {
	if m != nil {
		return m.StoreName
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetIsDis() int32 {
	if m != nil {
		return m.IsDis
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetTakeType() int32 {
	if m != nil {
		return m.TakeType
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetMinAmount() int32 {
	if m != nil {
		return m.MinAmount
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetWaitAmount() int32 {
	if m != nil {
		return m.WaitAmount
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetPaidAmount() int32 {
	if m != nil {
		return m.PaidAmount
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetExpectedTimeText() string {
	if m != nil {
		return m.ExpectedTimeText
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetGroupAt() string {
	if m != nil {
		return m.GroupAt
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetMemberName() string {
	if m != nil {
		return m.MemberName
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetMemberProfile() string {
	if m != nil {
		return m.MemberProfile
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetReceiverName() string {
	if m != nil {
		return m.ReceiverName
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetReceiverMobile() string {
	if m != nil {
		return m.ReceiverMobile
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetReceiverAddress() string {
	if m != nil {
		return m.ReceiverAddress
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetDisChainFinanceCode() string {
	if m != nil {
		return m.DisChainFinanceCode
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetDisChainName() string {
	if m != nil {
		return m.DisChainName
	}
	return ""
}

func (m *CommunityGroupOrderListResponse_Details) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetUserAgent() int32 {
	if m != nil {
		return m.UserAgent
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetDeliveryType() int32 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

func (m *CommunityGroupOrderListResponse_Details) GetEnReceiverMobile() string {
	if m != nil {
		return m.EnReceiverMobile
	}
	return ""
}

type BaseResponseNew struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponseNew) Reset()         { *m = BaseResponseNew{} }
func (m *BaseResponseNew) String() string { return proto.CompactTextString(m) }
func (*BaseResponseNew) ProtoMessage()    {}
func (*BaseResponseNew) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{2}
}

func (m *BaseResponseNew) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponseNew.Unmarshal(m, b)
}
func (m *BaseResponseNew) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponseNew.Marshal(b, m, deterministic)
}
func (m *BaseResponseNew) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponseNew.Merge(m, src)
}
func (m *BaseResponseNew) XXX_Size() int {
	return xxx_messageInfo_BaseResponseNew.Size(m)
}
func (m *BaseResponseNew) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponseNew.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponseNew proto.InternalMessageInfo

func (m *BaseResponseNew) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GroupCompletedReq struct {
	//主键ID
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupCompletedReq) Reset()         { *m = GroupCompletedReq{} }
func (m *GroupCompletedReq) String() string { return proto.CompactTextString(m) }
func (*GroupCompletedReq) ProtoMessage()    {}
func (*GroupCompletedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{3}
}

func (m *GroupCompletedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupCompletedReq.Unmarshal(m, b)
}
func (m *GroupCompletedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupCompletedReq.Marshal(b, m, deterministic)
}
func (m *GroupCompletedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupCompletedReq.Merge(m, src)
}
func (m *GroupCompletedReq) XXX_Size() int {
	return xxx_messageInfo_GroupCompletedReq.Size(m)
}
func (m *GroupCompletedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupCompletedReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupCompletedReq proto.InternalMessageInfo

func (m *GroupCompletedReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type MemberCompletedReq struct {
	//订单号
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberCompletedReq) Reset()         { *m = MemberCompletedReq{} }
func (m *MemberCompletedReq) String() string { return proto.CompactTextString(m) }
func (*MemberCompletedReq) ProtoMessage()    {}
func (*MemberCompletedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{4}
}

func (m *MemberCompletedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCompletedReq.Unmarshal(m, b)
}
func (m *MemberCompletedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCompletedReq.Marshal(b, m, deterministic)
}
func (m *MemberCompletedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCompletedReq.Merge(m, src)
}
func (m *MemberCompletedReq) XXX_Size() int {
	return xxx_messageInfo_MemberCompletedReq.Size(m)
}
func (m *MemberCompletedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCompletedReq.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCompletedReq proto.InternalMessageInfo

func (m *MemberCompletedReq) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type MerberOrderReq struct {
	//my-group-list 返回的ID
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key"`
	PageIndex            int32    `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MerberOrderReq) Reset()         { *m = MerberOrderReq{} }
func (m *MerberOrderReq) String() string { return proto.CompactTextString(m) }
func (*MerberOrderReq) ProtoMessage()    {}
func (*MerberOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{5}
}

func (m *MerberOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MerberOrderReq.Unmarshal(m, b)
}
func (m *MerberOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MerberOrderReq.Marshal(b, m, deterministic)
}
func (m *MerberOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MerberOrderReq.Merge(m, src)
}
func (m *MerberOrderReq) XXX_Size() int {
	return xxx_messageInfo_MerberOrderReq.Size(m)
}
func (m *MerberOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MerberOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_MerberOrderReq proto.InternalMessageInfo

func (m *MerberOrderReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MerberOrderReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *MerberOrderReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *MerberOrderReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type MerberOrderRes struct {
	Msg                  string             `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 []*MemberOrderData `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	Total                int32              `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MerberOrderRes) Reset()         { *m = MerberOrderRes{} }
func (m *MerberOrderRes) String() string { return proto.CompactTextString(m) }
func (*MerberOrderRes) ProtoMessage()    {}
func (*MerberOrderRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{6}
}

func (m *MerberOrderRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MerberOrderRes.Unmarshal(m, b)
}
func (m *MerberOrderRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MerberOrderRes.Marshal(b, m, deterministic)
}
func (m *MerberOrderRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MerberOrderRes.Merge(m, src)
}
func (m *MerberOrderRes) XXX_Size() int {
	return xxx_messageInfo_MerberOrderRes.Size(m)
}
func (m *MerberOrderRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MerberOrderRes.DiscardUnknown(m)
}

var xxx_messageInfo_MerberOrderRes proto.InternalMessageInfo

func (m *MerberOrderRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MerberOrderRes) GetData() []*MemberOrderData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *MerberOrderRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type MemberOrderData struct {
	//团员信息
	Mbmber string `protobuf:"bytes,1,opt,name=mbmber,proto3" json:"mbmber"`
	//订单状态  1已接单 2退款中 3已取消  4已完成
	OrderStatus int32 `protobuf:"varint,2,opt,name=order_status,json=orderStatus,proto3" json:"order_status"`
	//参团时间
	CreatedAt string `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	//订单号
	OrderSn string `protobuf:"bytes,4,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//订单商品列表
	GoodsList []*OrderProduct `protobuf:"bytes,5,rep,name=goods_list,json=goodsList,proto3" json:"goods_list"`
	// 团员收货地址
	GroupAddress         string   `protobuf:"bytes,6,opt,name=group_address,json=groupAddress,proto3" json:"group_address"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberOrderData) Reset()         { *m = MemberOrderData{} }
func (m *MemberOrderData) String() string { return proto.CompactTextString(m) }
func (*MemberOrderData) ProtoMessage()    {}
func (*MemberOrderData) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{7}
}

func (m *MemberOrderData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberOrderData.Unmarshal(m, b)
}
func (m *MemberOrderData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberOrderData.Marshal(b, m, deterministic)
}
func (m *MemberOrderData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberOrderData.Merge(m, src)
}
func (m *MemberOrderData) XXX_Size() int {
	return xxx_messageInfo_MemberOrderData.Size(m)
}
func (m *MemberOrderData) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberOrderData.DiscardUnknown(m)
}

var xxx_messageInfo_MemberOrderData proto.InternalMessageInfo

func (m *MemberOrderData) GetMbmber() string {
	if m != nil {
		return m.Mbmber
	}
	return ""
}

func (m *MemberOrderData) GetOrderStatus() int32 {
	if m != nil {
		return m.OrderStatus
	}
	return 0
}

func (m *MemberOrderData) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *MemberOrderData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *MemberOrderData) GetGoodsList() []*OrderProduct {
	if m != nil {
		return m.GoodsList
	}
	return nil
}

func (m *MemberOrderData) GetGroupAddress() string {
	if m != nil {
		return m.GroupAddress
	}
	return ""
}

type OrderProduct struct {
	//商品图
	GoodsImg string `protobuf:"bytes,1,opt,name=goods_img,json=goodsImg,proto3" json:"goods_img"`
	//商品名称
	GoodsName string `protobuf:"bytes,2,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//商品数量
	GoodsNum int32 `protobuf:"varint,3,opt,name=goods_num,json=goodsNum,proto3" json:"goods_num"`
	//商品规格
	Specs                string   `protobuf:"bytes,4,opt,name=specs,proto3" json:"specs"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderProduct) Reset()         { *m = OrderProduct{} }
func (m *OrderProduct) String() string { return proto.CompactTextString(m) }
func (*OrderProduct) ProtoMessage()    {}
func (*OrderProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{8}
}

func (m *OrderProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderProduct.Unmarshal(m, b)
}
func (m *OrderProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderProduct.Marshal(b, m, deterministic)
}
func (m *OrderProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderProduct.Merge(m, src)
}
func (m *OrderProduct) XXX_Size() int {
	return xxx_messageInfo_OrderProduct.Size(m)
}
func (m *OrderProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderProduct.DiscardUnknown(m)
}

var xxx_messageInfo_OrderProduct proto.InternalMessageInfo

func (m *OrderProduct) GetGoodsImg() string {
	if m != nil {
		return m.GoodsImg
	}
	return ""
}

func (m *OrderProduct) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *OrderProduct) GetGoodsNum() int32 {
	if m != nil {
		return m.GoodsNum
	}
	return 0
}

func (m *OrderProduct) GetSpecs() string {
	if m != nil {
		return m.Specs
	}
	return ""
}

type IsGroupReq struct {
	//会员ID
	MemberId             string   `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsGroupReq) Reset()         { *m = IsGroupReq{} }
func (m *IsGroupReq) String() string { return proto.CompactTextString(m) }
func (*IsGroupReq) ProtoMessage()    {}
func (*IsGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{9}
}

func (m *IsGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsGroupReq.Unmarshal(m, b)
}
func (m *IsGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsGroupReq.Marshal(b, m, deterministic)
}
func (m *IsGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsGroupReq.Merge(m, src)
}
func (m *IsGroupReq) XXX_Size() int {
	return xxx_messageInfo_IsGroupReq.Size(m)
}
func (m *IsGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsGroupReq proto.InternalMessageInfo

func (m *IsGroupReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

type IsGroupRes struct {
	Msg                  string           `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 *IsGroupRes_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *IsGroupRes) Reset()         { *m = IsGroupRes{} }
func (m *IsGroupRes) String() string { return proto.CompactTextString(m) }
func (*IsGroupRes) ProtoMessage()    {}
func (*IsGroupRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{10}
}

func (m *IsGroupRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsGroupRes.Unmarshal(m, b)
}
func (m *IsGroupRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsGroupRes.Marshal(b, m, deterministic)
}
func (m *IsGroupRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsGroupRes.Merge(m, src)
}
func (m *IsGroupRes) XXX_Size() int {
	return xxx_messageInfo_IsGroupRes.Size(m)
}
func (m *IsGroupRes) XXX_DiscardUnknown() {
	xxx_messageInfo_IsGroupRes.DiscardUnknown(m)
}

var xxx_messageInfo_IsGroupRes proto.InternalMessageInfo

func (m *IsGroupRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *IsGroupRes) GetData() *IsGroupRes_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type IsGroupRes_Data struct {
	//是否是团长 1是 0否
	IsGroup int32 `protobuf:"varint,2,opt,name=is_group,json=isGroup,proto3" json:"is_group"`
	//是否分销员 0不是分销员 1内部分销员 2外部分销员
	DisType int32 `protobuf:"varint,3,opt,name=dis_type,json=disType,proto3" json:"dis_type"`
	//是否显示table  1是  0否
	HasCommission int32 `protobuf:"varint,4,opt,name=has_commission,json=hasCommission,proto3" json:"has_commission"`
	// 是否显示开团按钮，临时需求，分销员+开关开启才显示
	IsShowOpenBtn        int32    `protobuf:"varint,5,opt,name=is_show_open_btn,json=isShowOpenBtn,proto3" json:"is_show_open_btn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsGroupRes_Data) Reset()         { *m = IsGroupRes_Data{} }
func (m *IsGroupRes_Data) String() string { return proto.CompactTextString(m) }
func (*IsGroupRes_Data) ProtoMessage()    {}
func (*IsGroupRes_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{10, 0}
}

func (m *IsGroupRes_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsGroupRes_Data.Unmarshal(m, b)
}
func (m *IsGroupRes_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsGroupRes_Data.Marshal(b, m, deterministic)
}
func (m *IsGroupRes_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsGroupRes_Data.Merge(m, src)
}
func (m *IsGroupRes_Data) XXX_Size() int {
	return xxx_messageInfo_IsGroupRes_Data.Size(m)
}
func (m *IsGroupRes_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_IsGroupRes_Data.DiscardUnknown(m)
}

var xxx_messageInfo_IsGroupRes_Data proto.InternalMessageInfo

func (m *IsGroupRes_Data) GetIsGroup() int32 {
	if m != nil {
		return m.IsGroup
	}
	return 0
}

func (m *IsGroupRes_Data) GetDisType() int32 {
	if m != nil {
		return m.DisType
	}
	return 0
}

func (m *IsGroupRes_Data) GetHasCommission() int32 {
	if m != nil {
		return m.HasCommission
	}
	return 0
}

func (m *IsGroupRes_Data) GetIsShowOpenBtn() int32 {
	if m != nil {
		return m.IsShowOpenBtn
	}
	return 0
}

type MyGroupReq struct {
	//年
	Year string `protobuf:"bytes,1,opt,name=year,proto3" json:"year"`
	//月
	Month string `protobuf:"bytes,2,opt,name=month,proto3" json:"month"`
	//会员ID（前端不用传）
	MemberId             string   `protobuf:"bytes,3,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyGroupReq) Reset()         { *m = MyGroupReq{} }
func (m *MyGroupReq) String() string { return proto.CompactTextString(m) }
func (*MyGroupReq) ProtoMessage()    {}
func (*MyGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{11}
}

func (m *MyGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyGroupReq.Unmarshal(m, b)
}
func (m *MyGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyGroupReq.Marshal(b, m, deterministic)
}
func (m *MyGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyGroupReq.Merge(m, src)
}
func (m *MyGroupReq) XXX_Size() int {
	return xxx_messageInfo_MyGroupReq.Size(m)
}
func (m *MyGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MyGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_MyGroupReq proto.InternalMessageInfo

func (m *MyGroupReq) GetYear() string {
	if m != nil {
		return m.Year
	}
	return ""
}

func (m *MyGroupReq) GetMonth() string {
	if m != nil {
		return m.Month
	}
	return ""
}

func (m *MyGroupReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

type MyGroupRes struct {
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	//成团实付总金额(分)
	PayAmount int32 `protobuf:"varint,2,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//待结算金额  分
	WaitAmount int32 `protobuf:"varint,3,opt,name=wait_amount,json=waitAmount,proto3" json:"wait_amount"`
	//已结算金额  分
	PaidAmount int32 `protobuf:"varint,4,opt,name=paid_amount,json=paidAmount,proto3" json:"paid_amount"`
	//当前佣金
	Commissions          string   `protobuf:"bytes,5,opt,name=commissions,proto3" json:"commissions"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyGroupRes) Reset()         { *m = MyGroupRes{} }
func (m *MyGroupRes) String() string { return proto.CompactTextString(m) }
func (*MyGroupRes) ProtoMessage()    {}
func (*MyGroupRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{12}
}

func (m *MyGroupRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyGroupRes.Unmarshal(m, b)
}
func (m *MyGroupRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyGroupRes.Marshal(b, m, deterministic)
}
func (m *MyGroupRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyGroupRes.Merge(m, src)
}
func (m *MyGroupRes) XXX_Size() int {
	return xxx_messageInfo_MyGroupRes.Size(m)
}
func (m *MyGroupRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MyGroupRes.DiscardUnknown(m)
}

var xxx_messageInfo_MyGroupRes proto.InternalMessageInfo

func (m *MyGroupRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MyGroupRes) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *MyGroupRes) GetWaitAmount() int32 {
	if m != nil {
		return m.WaitAmount
	}
	return 0
}

func (m *MyGroupRes) GetPaidAmount() int32 {
	if m != nil {
		return m.PaidAmount
	}
	return 0
}

func (m *MyGroupRes) GetCommissions() string {
	if m != nil {
		return m.Commissions
	}
	return ""
}

type MyGroupListReq struct {
	//是否为分销员 1是 0否
	IsDis int32 `protobuf:"varint,1,opt,name=is_dis,json=isDis,proto3" json:"is_dis"`
	//会员ID（前端不用传）
	MemberId  string `protobuf:"bytes,2,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	PageIndex int32  `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize  int32  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//年
	Year string `protobuf:"bytes,5,opt,name=year,proto3" json:"year"`
	//月
	Month                string   `protobuf:"bytes,6,opt,name=month,proto3" json:"month"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyGroupListReq) Reset()         { *m = MyGroupListReq{} }
func (m *MyGroupListReq) String() string { return proto.CompactTextString(m) }
func (*MyGroupListReq) ProtoMessage()    {}
func (*MyGroupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{13}
}

func (m *MyGroupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyGroupListReq.Unmarshal(m, b)
}
func (m *MyGroupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyGroupListReq.Marshal(b, m, deterministic)
}
func (m *MyGroupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyGroupListReq.Merge(m, src)
}
func (m *MyGroupListReq) XXX_Size() int {
	return xxx_messageInfo_MyGroupListReq.Size(m)
}
func (m *MyGroupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MyGroupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_MyGroupListReq proto.InternalMessageInfo

func (m *MyGroupListReq) GetIsDis() int32 {
	if m != nil {
		return m.IsDis
	}
	return 0
}

func (m *MyGroupListReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *MyGroupListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *MyGroupListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *MyGroupListReq) GetYear() string {
	if m != nil {
		return m.Year
	}
	return ""
}

func (m *MyGroupListReq) GetMonth() string {
	if m != nil {
		return m.Month
	}
	return ""
}

type MyGroupListRes struct {
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	//团列表
	Data                 []*GroupActivity `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	Total                int32            `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MyGroupListRes) Reset()         { *m = MyGroupListRes{} }
func (m *MyGroupListRes) String() string { return proto.CompactTextString(m) }
func (*MyGroupListRes) ProtoMessage()    {}
func (*MyGroupListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{14}
}

func (m *MyGroupListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyGroupListRes.Unmarshal(m, b)
}
func (m *MyGroupListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyGroupListRes.Marshal(b, m, deterministic)
}
func (m *MyGroupListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyGroupListRes.Merge(m, src)
}
func (m *MyGroupListRes) XXX_Size() int {
	return xxx_messageInfo_MyGroupListRes.Size(m)
}
func (m *MyGroupListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MyGroupListRes.DiscardUnknown(m)
}

var xxx_messageInfo_MyGroupListRes proto.InternalMessageInfo

func (m *MyGroupListRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MyGroupListRes) GetData() []*GroupActivity {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *MyGroupListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GroupActivity struct {
	//团状态  0拼团中 1拼团成功 2拼团失败
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	//店铺名称
	StoreName string `protobuf:"bytes,2,opt,name=store_name,json=storeName,proto3" json:"store_name"`
	//代收地址
	ReceiverAddress string `protobuf:"bytes,3,opt,name=receiver_address,json=receiverAddress,proto3" json:"receiver_address"`
	//门店头像
	StoreImg string `protobuf:"bytes,4,opt,name=store_img,json=storeImg,proto3" json:"store_img"`
	//已凑金额  分
	PayMonery int32 `protobuf:"varint,5,opt,name=pay_monery,json=payMonery,proto3" json:"pay_monery"`
	//贷物状态
	Transportation int32 `protobuf:"varint,6,opt,name=transportation,proto3" json:"transportation"`
	//主键ID
	Id int32 `protobuf:"varint,7,opt,name=id,proto3" json:"id"`
	//活动剩余时间 秒
	SurplusTime int32 `protobuf:"varint,8,opt,name=surplus_time,json=surplusTime,proto3" json:"surplus_time"`
	//成团金额
	MinAmount int32 `protobuf:"varint,9,opt,name=min_amount,json=minAmount,proto3" json:"min_amount"`
	//团长代收状态 0不代收 1代收
	FinalTakeType int32 `protobuf:"varint,10,opt,name=final_take_type,json=finalTakeType,proto3" json:"final_take_type"`
	//是否确认收货 1是  0否
	IsReceive            int32    `protobuf:"varint,11,opt,name=is_receive,json=isReceive,proto3" json:"is_receive"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupActivity) Reset()         { *m = GroupActivity{} }
func (m *GroupActivity) String() string { return proto.CompactTextString(m) }
func (*GroupActivity) ProtoMessage()    {}
func (*GroupActivity) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{15}
}

func (m *GroupActivity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupActivity.Unmarshal(m, b)
}
func (m *GroupActivity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupActivity.Marshal(b, m, deterministic)
}
func (m *GroupActivity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupActivity.Merge(m, src)
}
func (m *GroupActivity) XXX_Size() int {
	return xxx_messageInfo_GroupActivity.Size(m)
}
func (m *GroupActivity) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupActivity.DiscardUnknown(m)
}

var xxx_messageInfo_GroupActivity proto.InternalMessageInfo

func (m *GroupActivity) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupActivity) GetStoreName() string {
	if m != nil {
		return m.StoreName
	}
	return ""
}

func (m *GroupActivity) GetReceiverAddress() string {
	if m != nil {
		return m.ReceiverAddress
	}
	return ""
}

func (m *GroupActivity) GetStoreImg() string {
	if m != nil {
		return m.StoreImg
	}
	return ""
}

func (m *GroupActivity) GetPayMonery() int32 {
	if m != nil {
		return m.PayMonery
	}
	return 0
}

func (m *GroupActivity) GetTransportation() int32 {
	if m != nil {
		return m.Transportation
	}
	return 0
}

func (m *GroupActivity) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupActivity) GetSurplusTime() int32 {
	if m != nil {
		return m.SurplusTime
	}
	return 0
}

func (m *GroupActivity) GetMinAmount() int32 {
	if m != nil {
		return m.MinAmount
	}
	return 0
}

func (m *GroupActivity) GetFinalTakeType() int32 {
	if m != nil {
		return m.FinalTakeType
	}
	return 0
}

func (m *GroupActivity) GetIsReceive() int32 {
	if m != nil {
		return m.IsReceive
	}
	return 0
}

type CommunityGroupDetailRequest struct {
	//团id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//前端不用传
	ScrmId string `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 是否返回参团列表信息 0否 1返回6条数据 2返回全部数据
	ReturnDetail         int32    `protobuf:"varint,3,opt,name=return_detail,json=returnDetail,proto3" json:"return_detail"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupDetailRequest) Reset()         { *m = CommunityGroupDetailRequest{} }
func (m *CommunityGroupDetailRequest) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupDetailRequest) ProtoMessage()    {}
func (*CommunityGroupDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{16}
}

func (m *CommunityGroupDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupDetailRequest.Unmarshal(m, b)
}
func (m *CommunityGroupDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupDetailRequest.Marshal(b, m, deterministic)
}
func (m *CommunityGroupDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupDetailRequest.Merge(m, src)
}
func (m *CommunityGroupDetailRequest) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupDetailRequest.Size(m)
}
func (m *CommunityGroupDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupDetailRequest proto.InternalMessageInfo

func (m *CommunityGroupDetailRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommunityGroupDetailRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *CommunityGroupDetailRequest) GetReturnDetail() int32 {
	if m != nil {
		return m.ReturnDetail
	}
	return 0
}

type CommunityGroupDetailResponse struct {
	// 状态 200成功 非200失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message              string                             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *CommunityGroupDetailResponse_Data `protobuf:"bytes,15,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *CommunityGroupDetailResponse) Reset()         { *m = CommunityGroupDetailResponse{} }
func (m *CommunityGroupDetailResponse) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupDetailResponse) ProtoMessage()    {}
func (*CommunityGroupDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{17}
}

func (m *CommunityGroupDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupDetailResponse.Unmarshal(m, b)
}
func (m *CommunityGroupDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupDetailResponse.Marshal(b, m, deterministic)
}
func (m *CommunityGroupDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupDetailResponse.Merge(m, src)
}
func (m *CommunityGroupDetailResponse) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupDetailResponse.Size(m)
}
func (m *CommunityGroupDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupDetailResponse proto.InternalMessageInfo

func (m *CommunityGroupDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommunityGroupDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CommunityGroupDetailResponse) GetData() *CommunityGroupDetailResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type CommunityGroupDetailResponse_Data struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//是否团长 0否 1是
	IsGrouper int32 `protobuf:"varint,2,opt,name=is_grouper,json=isGrouper,proto3" json:"is_grouper"`
	//开团人名称
	MemberName string `protobuf:"bytes,3,opt,name=member_name,json=memberName,proto3" json:"member_name"`
	//开团人头像
	MemberProfile string `protobuf:"bytes,4,opt,name=member_profile,json=memberProfile,proto3" json:"member_profile"`
	//开团发起时间
	CreatedAt string `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	//剩余多少（秒）
	Time int32 `protobuf:"varint,6,opt,name=time,proto3" json:"time"`
	//结束时间
	EndTime string `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//成团金额
	MinAmount int32 `protobuf:"varint,8,opt,name=min_amount,json=minAmount,proto3" json:"min_amount"`
	//已支付金额
	PayAmount int32 `protobuf:"varint,9,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//是否团长代收 0不代收 1代收
	FinalTakeType int32 `protobuf:"varint,10,opt,name=final_take_type,json=finalTakeType,proto3" json:"final_take_type"`
	//团长代收时的名称
	ReceiverName string `protobuf:"bytes,11,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name"`
	//团长代收时的地址
	ReceiverAddress string `protobuf:"bytes,12,opt,name=receiver_address,json=receiverAddress,proto3" json:"receiver_address"`
	//团长代收时的手机号码
	ReceiverMobile string `protobuf:"bytes,13,opt,name=receiver_mobile,json=receiverMobile,proto3" json:"receiver_mobile"`
	//团状态 0进行中 1成功 2失败
	Status       int32                                             `protobuf:"varint,14,opt,name=status,proto3" json:"status"`
	GroupDetails []*CommunityGroupDetailResponse_Data_GroupDetails `protobuf:"bytes,15,rep,name=group_details,json=groupDetails,proto3" json:"group_details"`
	// 预计送达时间描述，如成团当日
	ExpectedDesc string `protobuf:"bytes,16,opt,name=expected_desc,json=expectedDesc,proto3" json:"expected_desc"`
	//进度百分比不带单位 2位小数 最大100.00
	Progress float32 `protobuf:"fixed32,17,opt,name=progress,proto3" json:"progress"`
	//是否订阅信息 0否1是
	IsSubscribe int32 `protobuf:"varint,18,opt,name=is_subscribe,json=isSubscribe,proto3" json:"is_subscribe"`
	// 当前用户订单号 目前最多返回2条记录
	Orders []*CommunityGroupDetailResponse_Data_MemberOrders `protobuf:"bytes,19,rep,name=orders,proto3" json:"orders"`
	// 店铺财务编码
	FinanceCode string `protobuf:"bytes,20,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//经度
	Lng string `protobuf:"bytes,21,opt,name=lng,proto3" json:"lng"`
	//纬度
	Lat string `protobuf:"bytes,22,opt,name=lat,proto3" json:"lat"`
	// 会员id
	MemberId string `protobuf:"bytes,23,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	// 预计N天送达
	DeliverDays          int32    `protobuf:"varint,24,opt,name=deliver_days,json=deliverDays,proto3" json:"deliver_days"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupDetailResponse_Data) Reset()         { *m = CommunityGroupDetailResponse_Data{} }
func (m *CommunityGroupDetailResponse_Data) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupDetailResponse_Data) ProtoMessage()    {}
func (*CommunityGroupDetailResponse_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{17, 0}
}

func (m *CommunityGroupDetailResponse_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupDetailResponse_Data.Unmarshal(m, b)
}
func (m *CommunityGroupDetailResponse_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupDetailResponse_Data.Marshal(b, m, deterministic)
}
func (m *CommunityGroupDetailResponse_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupDetailResponse_Data.Merge(m, src)
}
func (m *CommunityGroupDetailResponse_Data) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupDetailResponse_Data.Size(m)
}
func (m *CommunityGroupDetailResponse_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupDetailResponse_Data.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupDetailResponse_Data proto.InternalMessageInfo

func (m *CommunityGroupDetailResponse_Data) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data) GetIsGrouper() int32 {
	if m != nil {
		return m.IsGrouper
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data) GetMemberName() string {
	if m != nil {
		return m.MemberName
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetMemberProfile() string {
	if m != nil {
		return m.MemberProfile
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetTime() int32 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetMinAmount() int32 {
	if m != nil {
		return m.MinAmount
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data) GetFinalTakeType() int32 {
	if m != nil {
		return m.FinalTakeType
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data) GetReceiverName() string {
	if m != nil {
		return m.ReceiverName
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetReceiverAddress() string {
	if m != nil {
		return m.ReceiverAddress
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetReceiverMobile() string {
	if m != nil {
		return m.ReceiverMobile
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data) GetGroupDetails() []*CommunityGroupDetailResponse_Data_GroupDetails {
	if m != nil {
		return m.GroupDetails
	}
	return nil
}

func (m *CommunityGroupDetailResponse_Data) GetExpectedDesc() string {
	if m != nil {
		return m.ExpectedDesc
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetProgress() float32 {
	if m != nil {
		return m.Progress
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data) GetIsSubscribe() int32 {
	if m != nil {
		return m.IsSubscribe
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data) GetOrders() []*CommunityGroupDetailResponse_Data_MemberOrders {
	if m != nil {
		return m.Orders
	}
	return nil
}

func (m *CommunityGroupDetailResponse_Data) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetLng() string {
	if m != nil {
		return m.Lng
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetLat() string {
	if m != nil {
		return m.Lat
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data) GetDeliverDays() int32 {
	if m != nil {
		return m.DeliverDays
	}
	return 0
}

//拼团用户列表
type CommunityGroupDetailResponse_Data_GroupDetails struct {
	// 头像
	Profile string `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile"`
	// 昵称
	NickName string `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	//实付金额
	PayAmount int32 `protobuf:"varint,3,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//创建时间
	CreatedAt            string   `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupDetailResponse_Data_GroupDetails) Reset() {
	*m = CommunityGroupDetailResponse_Data_GroupDetails{}
}
func (m *CommunityGroupDetailResponse_Data_GroupDetails) String() string {
	return proto.CompactTextString(m)
}
func (*CommunityGroupDetailResponse_Data_GroupDetails) ProtoMessage() {}
func (*CommunityGroupDetailResponse_Data_GroupDetails) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{17, 0, 0}
}

func (m *CommunityGroupDetailResponse_Data_GroupDetails) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupDetailResponse_Data_GroupDetails.Unmarshal(m, b)
}
func (m *CommunityGroupDetailResponse_Data_GroupDetails) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupDetailResponse_Data_GroupDetails.Marshal(b, m, deterministic)
}
func (m *CommunityGroupDetailResponse_Data_GroupDetails) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupDetailResponse_Data_GroupDetails.Merge(m, src)
}
func (m *CommunityGroupDetailResponse_Data_GroupDetails) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupDetailResponse_Data_GroupDetails.Size(m)
}
func (m *CommunityGroupDetailResponse_Data_GroupDetails) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupDetailResponse_Data_GroupDetails.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupDetailResponse_Data_GroupDetails proto.InternalMessageInfo

func (m *CommunityGroupDetailResponse_Data_GroupDetails) GetProfile() string {
	if m != nil {
		return m.Profile
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data_GroupDetails) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *CommunityGroupDetailResponse_Data_GroupDetails) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data_GroupDetails) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

type CommunityGroupDetailResponse_Data_MemberOrders struct {
	//订单id
	OrderId int32 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//订单编号
	OrderSn              string   `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupDetailResponse_Data_MemberOrders) Reset() {
	*m = CommunityGroupDetailResponse_Data_MemberOrders{}
}
func (m *CommunityGroupDetailResponse_Data_MemberOrders) String() string {
	return proto.CompactTextString(m)
}
func (*CommunityGroupDetailResponse_Data_MemberOrders) ProtoMessage() {}
func (*CommunityGroupDetailResponse_Data_MemberOrders) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{17, 0, 1}
}

func (m *CommunityGroupDetailResponse_Data_MemberOrders) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupDetailResponse_Data_MemberOrders.Unmarshal(m, b)
}
func (m *CommunityGroupDetailResponse_Data_MemberOrders) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupDetailResponse_Data_MemberOrders.Marshal(b, m, deterministic)
}
func (m *CommunityGroupDetailResponse_Data_MemberOrders) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupDetailResponse_Data_MemberOrders.Merge(m, src)
}
func (m *CommunityGroupDetailResponse_Data_MemberOrders) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupDetailResponse_Data_MemberOrders.Size(m)
}
func (m *CommunityGroupDetailResponse_Data_MemberOrders) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupDetailResponse_Data_MemberOrders.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupDetailResponse_Data_MemberOrders proto.InternalMessageInfo

func (m *CommunityGroupDetailResponse_Data_MemberOrders) GetOrderId() int32 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *CommunityGroupDetailResponse_Data_MemberOrders) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type CommunityGroupActivityListRequest struct {
	//门店编码
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//需要显示的记录数量 0获取所以 大于0获取指定数量
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//排序值 0:默认排序
	SortType int32 `protobuf:"varint,4,opt,name=sort_type,json=sortType,proto3" json:"sort_type"`
	//前端不用传
	ScrmId               string   `protobuf:"bytes,5,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupActivityListRequest) Reset()         { *m = CommunityGroupActivityListRequest{} }
func (m *CommunityGroupActivityListRequest) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupActivityListRequest) ProtoMessage()    {}
func (*CommunityGroupActivityListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{18}
}

func (m *CommunityGroupActivityListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupActivityListRequest.Unmarshal(m, b)
}
func (m *CommunityGroupActivityListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupActivityListRequest.Marshal(b, m, deterministic)
}
func (m *CommunityGroupActivityListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupActivityListRequest.Merge(m, src)
}
func (m *CommunityGroupActivityListRequest) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupActivityListRequest.Size(m)
}
func (m *CommunityGroupActivityListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupActivityListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupActivityListRequest proto.InternalMessageInfo

func (m *CommunityGroupActivityListRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *CommunityGroupActivityListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CommunityGroupActivityListRequest) GetSortType() int32 {
	if m != nil {
		return m.SortType
	}
	return 0
}

func (m *CommunityGroupActivityListRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type CommunityGroupActivityListResponse struct {
	// 状态 200成功 非200失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//总记录数量
	Total                int32                                      `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*CommunityGroupActivityListResponse_List `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *CommunityGroupActivityListResponse) Reset()         { *m = CommunityGroupActivityListResponse{} }
func (m *CommunityGroupActivityListResponse) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupActivityListResponse) ProtoMessage()    {}
func (*CommunityGroupActivityListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{19}
}

func (m *CommunityGroupActivityListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupActivityListResponse.Unmarshal(m, b)
}
func (m *CommunityGroupActivityListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupActivityListResponse.Marshal(b, m, deterministic)
}
func (m *CommunityGroupActivityListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupActivityListResponse.Merge(m, src)
}
func (m *CommunityGroupActivityListResponse) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupActivityListResponse.Size(m)
}
func (m *CommunityGroupActivityListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupActivityListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupActivityListResponse proto.InternalMessageInfo

func (m *CommunityGroupActivityListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommunityGroupActivityListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CommunityGroupActivityListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *CommunityGroupActivityListResponse) GetData() []*CommunityGroupActivityListResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

type CommunityGroupActivityListResponse_List struct {
	//团id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//开团人名称
	NickName string `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	//开团人头像
	Profile string `protobuf:"bytes,3,opt,name=profile,proto3" json:"profile"`
	//剩余时间(秒)
	Time int32 `protobuf:"varint,4,opt,name=time,proto3" json:"time"`
	//还差N金额成团
	Amount int32 `protobuf:"varint,6,opt,name=amount,proto3" json:"amount"`
	//团长代收状态 0不代收 1代收
	TakeType             int32    `protobuf:"varint,7,opt,name=take_type,json=takeType,proto3" json:"take_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupActivityListResponse_List) Reset() {
	*m = CommunityGroupActivityListResponse_List{}
}
func (m *CommunityGroupActivityListResponse_List) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupActivityListResponse_List) ProtoMessage()    {}
func (*CommunityGroupActivityListResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{19, 0}
}

func (m *CommunityGroupActivityListResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupActivityListResponse_List.Unmarshal(m, b)
}
func (m *CommunityGroupActivityListResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupActivityListResponse_List.Marshal(b, m, deterministic)
}
func (m *CommunityGroupActivityListResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupActivityListResponse_List.Merge(m, src)
}
func (m *CommunityGroupActivityListResponse_List) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupActivityListResponse_List.Size(m)
}
func (m *CommunityGroupActivityListResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupActivityListResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupActivityListResponse_List proto.InternalMessageInfo

func (m *CommunityGroupActivityListResponse_List) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommunityGroupActivityListResponse_List) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *CommunityGroupActivityListResponse_List) GetProfile() string {
	if m != nil {
		return m.Profile
	}
	return ""
}

func (m *CommunityGroupActivityListResponse_List) GetTime() int32 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *CommunityGroupActivityListResponse_List) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *CommunityGroupActivityListResponse_List) GetTakeType() int32 {
	if m != nil {
		return m.TakeType
	}
	return 0
}

type CommunityGroupParticipantListRequest struct {
	//门店编码
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//需要显示的记录数量 0获取所有 大于0获取指定数量
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupParticipantListRequest) Reset()         { *m = CommunityGroupParticipantListRequest{} }
func (m *CommunityGroupParticipantListRequest) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupParticipantListRequest) ProtoMessage()    {}
func (*CommunityGroupParticipantListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{20}
}

func (m *CommunityGroupParticipantListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupParticipantListRequest.Unmarshal(m, b)
}
func (m *CommunityGroupParticipantListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupParticipantListRequest.Marshal(b, m, deterministic)
}
func (m *CommunityGroupParticipantListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupParticipantListRequest.Merge(m, src)
}
func (m *CommunityGroupParticipantListRequest) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupParticipantListRequest.Size(m)
}
func (m *CommunityGroupParticipantListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupParticipantListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupParticipantListRequest proto.InternalMessageInfo

func (m *CommunityGroupParticipantListRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *CommunityGroupParticipantListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type CommunityGroupParticipantListResponse struct {
	// 状态 200成功 非200失败
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误信息
	Message              string                                        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*CommunityGroupParticipantListResponse_List `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *CommunityGroupParticipantListResponse) Reset()         { *m = CommunityGroupParticipantListResponse{} }
func (m *CommunityGroupParticipantListResponse) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupParticipantListResponse) ProtoMessage()    {}
func (*CommunityGroupParticipantListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{21}
}

func (m *CommunityGroupParticipantListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupParticipantListResponse.Unmarshal(m, b)
}
func (m *CommunityGroupParticipantListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupParticipantListResponse.Marshal(b, m, deterministic)
}
func (m *CommunityGroupParticipantListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupParticipantListResponse.Merge(m, src)
}
func (m *CommunityGroupParticipantListResponse) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupParticipantListResponse.Size(m)
}
func (m *CommunityGroupParticipantListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupParticipantListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupParticipantListResponse proto.InternalMessageInfo

func (m *CommunityGroupParticipantListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CommunityGroupParticipantListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CommunityGroupParticipantListResponse) GetData() []*CommunityGroupParticipantListResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

type CommunityGroupParticipantListResponse_List struct {
	//名称
	NickName string `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	//头像
	Profile string `protobuf:"bytes,3,opt,name=profile,proto3" json:"profile"`
	// 开团或参团时间
	Time string `protobuf:"bytes,4,opt,name=time,proto3" json:"time"`
	// 类型 0开团 1参团
	Type                 int32    `protobuf:"varint,5,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupParticipantListResponse_List) Reset() {
	*m = CommunityGroupParticipantListResponse_List{}
}
func (m *CommunityGroupParticipantListResponse_List) String() string {
	return proto.CompactTextString(m)
}
func (*CommunityGroupParticipantListResponse_List) ProtoMessage() {}
func (*CommunityGroupParticipantListResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{21, 0}
}

func (m *CommunityGroupParticipantListResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupParticipantListResponse_List.Unmarshal(m, b)
}
func (m *CommunityGroupParticipantListResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupParticipantListResponse_List.Marshal(b, m, deterministic)
}
func (m *CommunityGroupParticipantListResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupParticipantListResponse_List.Merge(m, src)
}
func (m *CommunityGroupParticipantListResponse_List) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupParticipantListResponse_List.Size(m)
}
func (m *CommunityGroupParticipantListResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupParticipantListResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupParticipantListResponse_List proto.InternalMessageInfo

func (m *CommunityGroupParticipantListResponse_List) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *CommunityGroupParticipantListResponse_List) GetProfile() string {
	if m != nil {
		return m.Profile
	}
	return ""
}

func (m *CommunityGroupParticipantListResponse_List) GetTime() string {
	if m != nil {
		return m.Time
	}
	return ""
}

func (m *CommunityGroupParticipantListResponse_List) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type CommunityGroupOpenRequest struct {
	// 店铺财务编码
	StoreFinanceCode string `protobuf:"bytes,1,opt,name=store_finance_code,json=storeFinanceCode,proto3" json:"store_finance_code"`
	// 团长代收状态 0不代收 1代收
	FinalTakeType int32 `protobuf:"varint,2,opt,name=final_take_type,json=finalTakeType,proto3" json:"final_take_type"`
	// 收件人
	ReceiverName string `protobuf:"bytes,3,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name"`
	// 收件手机
	ReceiverMobile string `protobuf:"bytes,4,opt,name=receiver_mobile,json=receiverMobile,proto3" json:"receiver_mobile"`
	// 收件省
	ReceiverState string `protobuf:"bytes,5,opt,name=receiver_state,json=receiverState,proto3" json:"receiver_state"`
	// 收件市
	ReceiverCity string `protobuf:"bytes,6,opt,name=receiver_city,json=receiverCity,proto3" json:"receiver_city"`
	// 收件区
	ReceiverDistrict string `protobuf:"bytes,7,opt,name=receiver_district,json=receiverDistrict,proto3" json:"receiver_district"`
	// 收件地址
	ReceiverAddress string `protobuf:"bytes,8,opt,name=receiver_address,json=receiverAddress,proto3" json:"receiver_address"`
	// 会员id，后端通过token获取
	MemberId string `protobuf:"bytes,9,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	// 代收地址，经度
	Lng string `protobuf:"bytes,10,opt,name=lng,proto3" json:"lng"`
	// 代收地址，纬度
	Lat string `protobuf:"bytes,11,opt,name=lat,proto3" json:"lat"`
	// 昵称
	NickName string `protobuf:"bytes,12,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	// 头像完整地址
	AvatarUrl string `protobuf:"bytes,13,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url"`
	// 开团渠道id
	ChannelId int32 `protobuf:"varint,14,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 开团渠道来源
	UserAgent int32 `protobuf:"varint,15,opt,name=user_agent,json=userAgent,proto3" json:"user_agent"`
	// 海报带的分销id
	DisMemberId          int32    `protobuf:"varint,16,opt,name=dis_member_id,json=disMemberId,proto3" json:"dis_member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupOpenRequest) Reset()         { *m = CommunityGroupOpenRequest{} }
func (m *CommunityGroupOpenRequest) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupOpenRequest) ProtoMessage()    {}
func (*CommunityGroupOpenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{22}
}

func (m *CommunityGroupOpenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupOpenRequest.Unmarshal(m, b)
}
func (m *CommunityGroupOpenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupOpenRequest.Marshal(b, m, deterministic)
}
func (m *CommunityGroupOpenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupOpenRequest.Merge(m, src)
}
func (m *CommunityGroupOpenRequest) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupOpenRequest.Size(m)
}
func (m *CommunityGroupOpenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupOpenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupOpenRequest proto.InternalMessageInfo

func (m *CommunityGroupOpenRequest) GetStoreFinanceCode() string {
	if m != nil {
		return m.StoreFinanceCode
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetFinalTakeType() int32 {
	if m != nil {
		return m.FinalTakeType
	}
	return 0
}

func (m *CommunityGroupOpenRequest) GetReceiverName() string {
	if m != nil {
		return m.ReceiverName
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetReceiverMobile() string {
	if m != nil {
		return m.ReceiverMobile
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetReceiverState() string {
	if m != nil {
		return m.ReceiverState
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetReceiverCity() string {
	if m != nil {
		return m.ReceiverCity
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetReceiverDistrict() string {
	if m != nil {
		return m.ReceiverDistrict
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetReceiverAddress() string {
	if m != nil {
		return m.ReceiverAddress
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetLng() string {
	if m != nil {
		return m.Lng
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetLat() string {
	if m != nil {
		return m.Lat
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetAvatarUrl() string {
	if m != nil {
		return m.AvatarUrl
	}
	return ""
}

func (m *CommunityGroupOpenRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CommunityGroupOpenRequest) GetUserAgent() int32 {
	if m != nil {
		return m.UserAgent
	}
	return 0
}

func (m *CommunityGroupOpenRequest) GetDisMemberId() int32 {
	if m != nil {
		return m.DisMemberId
	}
	return 0
}

type CommunityGroupOpenResponse struct {
	// 提示信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 错误信息
	Error                string                  `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *CommunityGroupOpenData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *CommunityGroupOpenResponse) Reset()         { *m = CommunityGroupOpenResponse{} }
func (m *CommunityGroupOpenResponse) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupOpenResponse) ProtoMessage()    {}
func (*CommunityGroupOpenResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{23}
}

func (m *CommunityGroupOpenResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupOpenResponse.Unmarshal(m, b)
}
func (m *CommunityGroupOpenResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupOpenResponse.Marshal(b, m, deterministic)
}
func (m *CommunityGroupOpenResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupOpenResponse.Merge(m, src)
}
func (m *CommunityGroupOpenResponse) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupOpenResponse.Size(m)
}
func (m *CommunityGroupOpenResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupOpenResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupOpenResponse proto.InternalMessageInfo

func (m *CommunityGroupOpenResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *CommunityGroupOpenResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CommunityGroupOpenResponse) GetData() *CommunityGroupOpenData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CommunityGroupOpenData struct {
	// 团id
	GroupId int64 `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	// 模板id
	TemplateId           string   `protobuf:"bytes,5,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupOpenData) Reset()         { *m = CommunityGroupOpenData{} }
func (m *CommunityGroupOpenData) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupOpenData) ProtoMessage()    {}
func (*CommunityGroupOpenData) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{24}
}

func (m *CommunityGroupOpenData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupOpenData.Unmarshal(m, b)
}
func (m *CommunityGroupOpenData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupOpenData.Marshal(b, m, deterministic)
}
func (m *CommunityGroupOpenData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupOpenData.Merge(m, src)
}
func (m *CommunityGroupOpenData) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupOpenData.Size(m)
}
func (m *CommunityGroupOpenData) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupOpenData.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupOpenData proto.InternalMessageInfo

func (m *CommunityGroupOpenData) GetGroupId() int64 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *CommunityGroupOpenData) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

type CommunityGroupBaseResponse struct {
	// 提示信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupBaseResponse) Reset()         { *m = CommunityGroupBaseResponse{} }
func (m *CommunityGroupBaseResponse) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupBaseResponse) ProtoMessage()    {}
func (*CommunityGroupBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{25}
}

func (m *CommunityGroupBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupBaseResponse.Unmarshal(m, b)
}
func (m *CommunityGroupBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupBaseResponse.Marshal(b, m, deterministic)
}
func (m *CommunityGroupBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupBaseResponse.Merge(m, src)
}
func (m *CommunityGroupBaseResponse) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupBaseResponse.Size(m)
}
func (m *CommunityGroupBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupBaseResponse proto.InternalMessageInfo

func (m *CommunityGroupBaseResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *CommunityGroupBaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type JsonStrRequest struct {
	JsonStr              []byte   `protobuf:"bytes,1,opt,name=json_str,json=jsonStr,proto3" json:"json_str"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JsonStrRequest) Reset()         { *m = JsonStrRequest{} }
func (m *JsonStrRequest) String() string { return proto.CompactTextString(m) }
func (*JsonStrRequest) ProtoMessage()    {}
func (*JsonStrRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{26}
}

func (m *JsonStrRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JsonStrRequest.Unmarshal(m, b)
}
func (m *JsonStrRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JsonStrRequest.Marshal(b, m, deterministic)
}
func (m *JsonStrRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JsonStrRequest.Merge(m, src)
}
func (m *JsonStrRequest) XXX_Size() int {
	return xxx_messageInfo_JsonStrRequest.Size(m)
}
func (m *JsonStrRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JsonStrRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JsonStrRequest proto.InternalMessageInfo

func (m *JsonStrRequest) GetJsonStr() []byte {
	if m != nil {
		return m.JsonStr
	}
	return nil
}

type CommunityGroupMessageSubscribeRequest struct {
	//订阅消息类型 0团长开团 1团员订单
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	//类型是0时传团id 1订单编号
	Number string `protobuf:"bytes,2,opt,name=number,proto3" json:"number"`
	//前端不用传
	ScrmId               string   `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommunityGroupMessageSubscribeRequest) Reset()         { *m = CommunityGroupMessageSubscribeRequest{} }
func (m *CommunityGroupMessageSubscribeRequest) String() string { return proto.CompactTextString(m) }
func (*CommunityGroupMessageSubscribeRequest) ProtoMessage()    {}
func (*CommunityGroupMessageSubscribeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{27}
}

func (m *CommunityGroupMessageSubscribeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommunityGroupMessageSubscribeRequest.Unmarshal(m, b)
}
func (m *CommunityGroupMessageSubscribeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommunityGroupMessageSubscribeRequest.Marshal(b, m, deterministic)
}
func (m *CommunityGroupMessageSubscribeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommunityGroupMessageSubscribeRequest.Merge(m, src)
}
func (m *CommunityGroupMessageSubscribeRequest) XXX_Size() int {
	return xxx_messageInfo_CommunityGroupMessageSubscribeRequest.Size(m)
}
func (m *CommunityGroupMessageSubscribeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommunityGroupMessageSubscribeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommunityGroupMessageSubscribeRequest proto.InternalMessageInfo

func (m *CommunityGroupMessageSubscribeRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CommunityGroupMessageSubscribeRequest) GetNumber() string {
	if m != nil {
		return m.Number
	}
	return ""
}

func (m *CommunityGroupMessageSubscribeRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type UpdateOrderGroupRequest struct {
	//活动ID
	PromotionGroupActivityId int32    `protobuf:"varint,1,opt,name=promotion_group_activity_id,json=promotionGroupActivityId,proto3" json:"promotion_group_activity_id"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *UpdateOrderGroupRequest) Reset()         { *m = UpdateOrderGroupRequest{} }
func (m *UpdateOrderGroupRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateOrderGroupRequest) ProtoMessage()    {}
func (*UpdateOrderGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2ffac58e9e43b62d, []int{28}
}

func (m *UpdateOrderGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateOrderGroupRequest.Unmarshal(m, b)
}
func (m *UpdateOrderGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateOrderGroupRequest.Marshal(b, m, deterministic)
}
func (m *UpdateOrderGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateOrderGroupRequest.Merge(m, src)
}
func (m *UpdateOrderGroupRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateOrderGroupRequest.Size(m)
}
func (m *UpdateOrderGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateOrderGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateOrderGroupRequest proto.InternalMessageInfo

func (m *UpdateOrderGroupRequest) GetPromotionGroupActivityId() int32 {
	if m != nil {
		return m.PromotionGroupActivityId
	}
	return 0
}

func init() {
	proto.RegisterType((*CommunityGroupOrderListRequest)(nil), "oc.CommunityGroupOrderListRequest")
	proto.RegisterType((*CommunityGroupOrderListResponse)(nil), "oc.CommunityGroupOrderListResponse")
	proto.RegisterType((*CommunityGroupOrderListResponse_Details)(nil), "oc.CommunityGroupOrderListResponse.Details")
	proto.RegisterType((*BaseResponseNew)(nil), "oc.BaseResponseNew")
	proto.RegisterType((*GroupCompletedReq)(nil), "oc.GroupCompletedReq")
	proto.RegisterType((*MemberCompletedReq)(nil), "oc.MemberCompletedReq")
	proto.RegisterType((*MerberOrderReq)(nil), "oc.MerberOrderReq")
	proto.RegisterType((*MerberOrderRes)(nil), "oc.MerberOrderRes")
	proto.RegisterType((*MemberOrderData)(nil), "oc.MemberOrderData")
	proto.RegisterType((*OrderProduct)(nil), "oc.OrderProduct")
	proto.RegisterType((*IsGroupReq)(nil), "oc.IsGroupReq")
	proto.RegisterType((*IsGroupRes)(nil), "oc.IsGroupRes")
	proto.RegisterType((*IsGroupRes_Data)(nil), "oc.IsGroupRes.Data")
	proto.RegisterType((*MyGroupReq)(nil), "oc.MyGroupReq")
	proto.RegisterType((*MyGroupRes)(nil), "oc.MyGroupRes")
	proto.RegisterType((*MyGroupListReq)(nil), "oc.MyGroupListReq")
	proto.RegisterType((*MyGroupListRes)(nil), "oc.MyGroupListRes")
	proto.RegisterType((*GroupActivity)(nil), "oc.GroupActivity")
	proto.RegisterType((*CommunityGroupDetailRequest)(nil), "oc.CommunityGroupDetailRequest")
	proto.RegisterType((*CommunityGroupDetailResponse)(nil), "oc.CommunityGroupDetailResponse")
	proto.RegisterType((*CommunityGroupDetailResponse_Data)(nil), "oc.CommunityGroupDetailResponse.Data")
	proto.RegisterType((*CommunityGroupDetailResponse_Data_GroupDetails)(nil), "oc.CommunityGroupDetailResponse.Data.GroupDetails")
	proto.RegisterType((*CommunityGroupDetailResponse_Data_MemberOrders)(nil), "oc.CommunityGroupDetailResponse.Data.MemberOrders")
	proto.RegisterType((*CommunityGroupActivityListRequest)(nil), "oc.CommunityGroupActivityListRequest")
	proto.RegisterType((*CommunityGroupActivityListResponse)(nil), "oc.CommunityGroupActivityListResponse")
	proto.RegisterType((*CommunityGroupActivityListResponse_List)(nil), "oc.CommunityGroupActivityListResponse.List")
	proto.RegisterType((*CommunityGroupParticipantListRequest)(nil), "oc.CommunityGroupParticipantListRequest")
	proto.RegisterType((*CommunityGroupParticipantListResponse)(nil), "oc.CommunityGroupParticipantListResponse")
	proto.RegisterType((*CommunityGroupParticipantListResponse_List)(nil), "oc.CommunityGroupParticipantListResponse.List")
	proto.RegisterType((*CommunityGroupOpenRequest)(nil), "oc.CommunityGroupOpenRequest")
	proto.RegisterType((*CommunityGroupOpenResponse)(nil), "oc.CommunityGroupOpenResponse")
	proto.RegisterType((*CommunityGroupOpenData)(nil), "oc.CommunityGroupOpenData")
	proto.RegisterType((*CommunityGroupBaseResponse)(nil), "oc.CommunityGroupBaseResponse")
	proto.RegisterType((*JsonStrRequest)(nil), "oc.JsonStrRequest")
	proto.RegisterType((*CommunityGroupMessageSubscribeRequest)(nil), "oc.CommunityGroupMessageSubscribeRequest")
	proto.RegisterType((*UpdateOrderGroupRequest)(nil), "oc.UpdateOrderGroupRequest")
}

func init() { proto.RegisterFile("oc/community_group.proto", fileDescriptor_2ffac58e9e43b62d) }

var fileDescriptor_2ffac58e9e43b62d = []byte{
	// 2486 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x59, 0xcb, 0x6f, 0x1b, 0xb9,
	0x19, 0x87, 0x5e, 0x96, 0xf4, 0x49, 0x96, 0x1d, 0x26, 0x71, 0xc6, 0x72, 0xb3, 0x71, 0x26, 0xeb,
	0x3c, 0x90, 0x85, 0x03, 0x64, 0xd1, 0x43, 0x81, 0x3e, 0xe0, 0xd8, 0xdd, 0xc2, 0xdb, 0x75, 0x36,
	0x18, 0x7b, 0xd1, 0x5e, 0x8a, 0xc1, 0x78, 0x86, 0xb1, 0xb9, 0xd6, 0x3c, 0x32, 0xa4, 0x92, 0x68,
	0x81, 0x02, 0x05, 0x7a, 0xed, 0xad, 0x40, 0x81, 0x5e, 0x0a, 0xf4, 0x56, 0xf4, 0xd8, 0xff, 0xa0,
	0xfb, 0x47, 0xf4, 0xd4, 0x5b, 0xef, 0xfd, 0x1b, 0x0a, 0x7e, 0x24, 0x35, 0xc3, 0xd1, 0xd8, 0x56,
	0xb3, 0x7b, 0x13, 0x7f, 0xfc, 0x48, 0x0e, 0xbf, 0xe7, 0x8f, 0x9f, 0xc0, 0x49, 0xc3, 0x67, 0x61,
	0x1a, 0xc7, 0xd3, 0x84, 0x89, 0x99, 0x7f, 0x96, 0xa7, 0xd3, 0x6c, 0x37, 0xcb, 0x53, 0x91, 0x92,
	0x66, 0x1a, 0xba, 0xff, 0x6c, 0xc1, 0x47, 0xfb, 0x66, 0xf6, 0x17, 0x72, 0xf2, 0xcb, 0x3c, 0xa2,
	0xf9, 0x17, 0x8c, 0x0b, 0x8f, 0xbe, 0x99, 0x52, 0x2e, 0xc8, 0x16, 0xf4, 0xb3, 0xe0, 0x8c, 0xfa,
	0x9c, 0x7d, 0x43, 0x9d, 0xc6, 0x76, 0xe3, 0x71, 0xc7, 0xeb, 0x49, 0xe0, 0x98, 0x7d, 0x43, 0xc9,
	0x5d, 0x00, 0x9c, 0x64, 0x49, 0x44, 0xdf, 0x3b, 0x4d, 0x9c, 0x45, 0xf1, 0x43, 0x09, 0x90, 0xfb,
	0x30, 0xc4, 0x13, 0x7d, 0x2e, 0x02, 0x31, 0xe5, 0x4e, 0x6b, 0xbb, 0xf1, 0xb8, 0xef, 0x0d, 0x10,
	0x3b, 0x46, 0x88, 0xdc, 0x83, 0x01, 0xa7, 0x41, 0x1e, 0x9e, 0xfb, 0x62, 0x96, 0x51, 0xa7, 0x8d,
	0x12, 0xa0, 0xa0, 0x93, 0x59, 0x46, 0x89, 0x03, 0xdd, 0x0b, 0x3a, 0x7b, 0x97, 0xe6, 0x91, 0xd3,
	0xc1, 0x49, 0x33, 0x24, 0x0f, 0x61, 0x4d, 0xed, 0x2e, 0x82, 0x0b, 0xaa, 0x96, 0xaf, 0xa0, 0xc4,
	0x2a, 0xc2, 0x27, 0xc1, 0x05, 0xc5, 0x1d, 0xb6, 0xa0, 0x2f, 0x58, 0xac, 0x25, 0xba, 0x28, 0xd1,
	0x93, 0x00, 0x4e, 0xde, 0x05, 0xe0, 0x22, 0xc8, 0x85, 0x2f, 0x11, 0xa7, 0x87, 0xb3, 0x7d, 0x44,
	0x4e, 0x58, 0x4c, 0xc9, 0x26, 0xf4, 0x68, 0x12, 0xa9, 0xc9, 0xbe, 0x3a, 0x9e, 0x26, 0x91, 0x99,
	0xe2, 0xe7, 0x69, 0xe6, 0xb3, 0x88, 0x3b, 0xb0, 0xdd, 0x92, 0x53, 0x72, 0x7c, 0x18, 0x71, 0x72,
	0x07, 0xba, 0x53, 0x4e, 0x73, 0x3f, 0x49, 0x9d, 0x01, 0x2e, 0x5a, 0x91, 0xc3, 0x97, 0x29, 0x19,
	0x41, 0x93, 0x65, 0xce, 0x10, 0xb1, 0x26, 0xcb, 0xe4, 0xed, 0x59, 0xe6, 0x4f, 0xd2, 0x30, 0x10,
	0x2c, 0x4d, 0x9c, 0x55, 0x75, 0x7b, 0x96, 0x7d, 0xa1, 0x11, 0xa9, 0xc1, 0x5c, 0x19, 0xc2, 0x7f,
	0x9d, 0xa7, 0xb1, 0x33, 0x42, 0x15, 0x0f, 0x34, 0xf6, 0x59, 0x9e, 0xc6, 0xee, 0x7f, 0x7a, 0x70,
	0xef, 0x52, 0x1b, 0xf2, 0x2c, 0x4d, 0x38, 0x25, 0x04, 0xda, 0x61, 0x1a, 0x19, 0xfb, 0xe1, 0x6f,
	0xa9, 0xd8, 0x98, 0x72, 0x1e, 0x9c, 0x51, 0x34, 0x5c, 0xdf, 0x33, 0x43, 0x72, 0x0b, 0x3a, 0x34,
	0xcf, 0xd3, 0x5c, 0xdb, 0x4b, 0x0d, 0xc8, 0xcf, 0xa1, 0x1b, 0x51, 0x11, 0xb0, 0x09, 0x77, 0xda,
	0xdb, 0xad, 0xc7, 0x83, 0xe7, 0x4f, 0x77, 0xd3, 0x70, 0xf7, 0x9a, 0x93, 0x77, 0x0f, 0xd4, 0x12,
	0xcf, 0xac, 0x95, 0x57, 0x16, 0xa9, 0x08, 0x26, 0x7e, 0x98, 0x4e, 0x13, 0x81, 0x36, 0xed, 0x78,
	0x80, 0xd0, 0xbe, 0x44, 0xc6, 0x7f, 0xec, 0x42, 0x57, 0xaf, 0x42, 0x7d, 0x45, 0xf8, 0xd5, 0x2d,
	0xaf, 0xc9, 0x22, 0xa9, 0x8e, 0xd7, 0x2c, 0x09, 0x92, 0x90, 0xfa, 0x78, 0x1f, 0xf5, 0xe1, 0x03,
	0x8d, 0xed, 0xcb, 0x6b, 0xa1, 0x41, 0xd3, 0x9c, 0xfa, 0x49, 0x10, 0x53, 0x7d, 0x83, 0x3e, 0x22,
	0x2f, 0x83, 0x98, 0x92, 0xdb, 0xb0, 0xc2, 0xb8, 0x1f, 0x31, 0x8e, 0xae, 0xd6, 0xf1, 0x3a, 0x8c,
	0x1f, 0x30, 0x4e, 0x36, 0x60, 0x45, 0xfb, 0xa8, 0xfa, 0x20, 0x3d, 0x42, 0xdf, 0xb1, 0xbc, 0xab,
	0xe3, 0xf5, 0x84, 0x71, 0xac, 0xbb, 0x00, 0x31, 0x4b, 0xfc, 0x20, 0xc6, 0x9b, 0x74, 0x95, 0xf7,
	0xc7, 0x2c, 0xd9, 0x43, 0x40, 0x05, 0xc7, 0xcc, 0x4c, 0xf7, 0x4c, 0x70, 0xcc, 0xf4, 0xf4, 0x3d,
	0x18, 0xbc, 0x0b, 0x98, 0x30, 0xf3, 0x7d, 0xa5, 0x08, 0x09, 0x15, 0x02, 0x59, 0xc0, 0x22, 0x23,
	0x00, 0x4a, 0x40, 0x42, 0xc5, 0x01, 0x25, 0xdf, 0x1d, 0x5c, 0xe5, 0xbb, 0x43, 0xdb, 0x77, 0x3f,
	0x01, 0x42, 0xdf, 0x67, 0x34, 0x14, 0x54, 0xcd, 0xfb, 0x82, 0xbe, 0x17, 0xda, 0xfd, 0xd6, 0xcd,
	0x8c, 0x94, 0x3c, 0xa1, 0xef, 0xf1, 0x9c, 0x30, 0xa7, 0x81, 0x14, 0x0e, 0x04, 0xba, 0x60, 0xdf,
	0xeb, 0x6b, 0x64, 0x4f, 0xc8, 0x73, 0x54, 0x1c, 0x06, 0xc2, 0x59, 0x53, 0xe7, 0xe0, 0x78, 0x0f,
	0x93, 0x47, 0x4c, 0xe3, 0x53, 0x9a, 0xfb, 0x2c, 0x72, 0xd6, 0x55, 0xe8, 0x29, 0xe0, 0x30, 0x92,
	0xf7, 0xd3, 0x93, 0x68, 0xaa, 0x1b, 0xca, 0xf9, 0x15, 0x84, 0xb6, 0xda, 0x81, 0x91, 0x16, 0xc8,
	0xf2, 0xf4, 0x35, 0x9b, 0x50, 0x87, 0xa8, 0xf8, 0x56, 0xe8, 0x2b, 0x05, 0x92, 0x07, 0xb0, 0x9a,
	0xd3, 0x90, 0xb2, 0xb7, 0x66, 0xa7, 0x9b, 0x28, 0x35, 0x34, 0x20, 0xee, 0xf5, 0x08, 0xd6, 0xe6,
	0x42, 0x71, 0x7a, 0x2a, 0x37, 0xbb, 0x85, 0x62, 0x23, 0x03, 0x1f, 0x21, 0x4a, 0x9e, 0xc0, 0xfa,
	0x5c, 0x30, 0x88, 0xa2, 0x9c, 0x72, 0xee, 0xdc, 0x46, 0xc9, 0xf9, 0x06, 0x7b, 0x0a, 0x26, 0x9f,
	0xc2, 0x46, 0xc4, 0xb8, 0x1f, 0x9e, 0x07, 0x2c, 0xf1, 0x2d, 0xbf, 0xdc, 0xc0, 0x05, 0x37, 0x23,
	0xc6, 0xf7, 0xe5, 0xe4, 0x67, 0x25, 0xff, 0xfc, 0x18, 0x46, 0xc5, 0x22, 0xfc, 0xdc, 0x3b, 0xea,
	0x73, 0x8d, 0x30, 0x7e, 0xae, 0x54, 0xf9, 0x79, 0x90, 0x24, 0x74, 0x22, 0x35, 0xe7, 0x28, 0xdf,
	0xd1, 0xc8, 0x61, 0x24, 0xa7, 0x31, 0xc1, 0x04, 0x67, 0x34, 0x11, 0xce, 0xa6, 0x9a, 0x96, 0xc8,
	0x9e, 0x04, 0xa4, 0x46, 0x22, 0x3a, 0x91, 0x9f, 0x3a, 0x53, 0x9e, 0x3b, 0x46, 0x89, 0xa1, 0x01,
	0xd1, 0x7b, 0xa5, 0x0f, 0x24, 0x7e, 0x55, 0x29, 0x5b, 0xda, 0x07, 0x12, 0xcf, 0x52, 0x8b, 0xfb,
	0x00, 0xd6, 0x5e, 0x04, 0x9c, 0x9a, 0xb8, 0x7e, 0x49, 0xdf, 0x91, 0x75, 0x68, 0xc5, 0xfc, 0x0c,
	0xa3, 0xb3, 0xef, 0xc9, 0x9f, 0xee, 0x03, 0xb8, 0x81, 0x69, 0x60, 0x3f, 0x8d, 0xb3, 0x09, 0x15,
	0x34, 0xf2, 0xe8, 0x9b, 0x52, 0x0c, 0x77, 0x64, 0x0c, 0xbb, 0xcf, 0x80, 0x1c, 0xa1, 0xfd, 0x2c,
	0xa9, 0x4d, 0xe8, 0xa5, 0x32, 0x79, 0xf8, 0x3c, 0xd1, 0x3b, 0x76, 0x71, 0x7c, 0x9c, 0xb8, 0x09,
	0x8c, 0x8e, 0x68, 0x7e, 0x4a, 0x73, 0xcc, 0x2e, 0x35, 0x5b, 0xca, 0x2f, 0xb9, 0xa0, 0x33, 0x9d,
	0x0d, 0xe4, 0xcf, 0x4a, 0x61, 0x6a, 0x55, 0x0b, 0x93, 0x55, 0xd4, 0xda, 0x76, 0x51, 0x73, 0x83,
	0xca, 0x79, 0x7c, 0xf1, 0xa6, 0xe4, 0x11, 0xb4, 0xa3, 0x40, 0x04, 0x4e, 0x13, 0x33, 0xe1, 0x4d,
	0x99, 0x09, 0xd5, 0xa5, 0x70, 0xcd, 0x41, 0x20, 0x02, 0x0f, 0x05, 0x64, 0x2e, 0xc5, 0xdc, 0xa6,
	0xbf, 0x41, 0x0d, 0xdc, 0x7f, 0x37, 0x60, 0xad, 0x22, 0x2f, 0x53, 0x50, 0x7c, 0x2a, 0x21, 0x7d,
	0x8e, 0x1e, 0xc9, 0x9c, 0xa7, 0x35, 0xa3, 0x12, 0x94, 0xaa, 0xb2, 0x03, 0xa5, 0x1d, 0x95, 0xa5,
	0xec, 0x00, 0x6d, 0xd5, 0x04, 0xe8, 0x5c, 0xb7, 0x6d, 0x4b, 0xb7, 0xe4, 0x19, 0xc0, 0x59, 0x9a,
	0x46, 0xdc, 0x9f, 0x30, 0x2e, 0x93, 0xb1, 0xbc, 0xcd, 0xba, 0xbc, 0x0d, 0x7e, 0xd7, 0xab, 0x3c,
	0x8d, 0xa6, 0xa1, 0xf0, 0xfa, 0x28, 0x23, 0xf3, 0xba, 0x74, 0x2d, 0x1d, 0xec, 0x3a, 0x36, 0x54,
	0xc9, 0x55, 0x75, 0x5e, 0x07, 0x86, 0xfb, 0x5b, 0x18, 0x96, 0xd7, 0x4b, 0x75, 0xab, 0x53, 0x58,
	0x6c, 0xb4, 0xd8, 0x43, 0xe0, 0x30, 0x3e, 0x93, 0x1f, 0xaf, 0x26, 0x31, 0x18, 0x94, 0x0d, 0x95,
	0x38, 0x46, 0xc2, 0x7c, 0x6d, 0x32, 0x8d, 0xb5, 0x12, 0xd5, 0xda, 0x97, 0xd3, 0x58, 0x6a, 0x97,
	0x67, 0x34, 0xe4, 0xfa, 0x5a, 0x6a, 0xe0, 0x3e, 0x01, 0x38, 0xe4, 0xe8, 0x88, 0xd2, 0x59, 0xac,
	0x1c, 0xd4, 0xb0, 0x73, 0x90, 0xfb, 0xaf, 0x46, 0x49, 0xf6, 0x6a, 0x43, 0x37, 0x8c, 0xa1, 0x0b,
	0xf9, 0xdd, 0xc2, 0xd0, 0xe3, 0x3f, 0x34, 0xa0, 0x8d, 0x76, 0xdc, 0x84, 0x1e, 0xe3, 0x8a, 0x69,
	0x69, 0x5b, 0x75, 0x99, 0x5a, 0x21, 0xa7, 0x64, 0xec, 0x63, 0x48, 0xaa, 0xab, 0x74, 0x23, 0xc6,
	0x31, 0x1a, 0x77, 0x60, 0x74, 0x1e, 0x70, 0x5f, 0x52, 0x35, 0xc6, 0xb9, 0x24, 0x03, 0xca, 0x2d,
	0x57, 0xcf, 0x03, 0xbe, 0x3f, 0x07, 0xc9, 0x23, 0x58, 0x67, 0xdc, 0xe7, 0xe7, 0xe9, 0x3b, 0x3f,
	0xcd, 0x68, 0xe2, 0x9f, 0x8a, 0x44, 0x57, 0xac, 0x55, 0xc6, 0x8f, 0xcf, 0xd3, 0x77, 0x5f, 0x66,
	0x34, 0x79, 0x21, 0x12, 0xf7, 0x18, 0xe0, 0x68, 0x36, 0xd7, 0x01, 0x81, 0xf6, 0x8c, 0x06, 0xc6,
	0xb3, 0xf0, 0xb7, 0xd4, 0x5d, 0x9c, 0x26, 0xe2, 0x5c, 0xab, 0x5c, 0x0d, 0x6c, 0x6d, 0xb5, 0x2a,
	0xda, 0xfa, 0x6b, 0xa3, 0xb4, 0x6b, 0x9d, 0xb6, 0xec, 0x92, 0xd7, 0xbc, 0xa6, 0xe4, 0xb5, 0xae,
	0x2b, 0x79, 0xed, 0x85, 0x92, 0xb7, 0x0d, 0x83, 0x42, 0x45, 0x5c, 0x33, 0xc2, 0x32, 0xe4, 0xfe,
	0xbd, 0x01, 0x23, 0xfd, 0x8d, 0x9a, 0xc6, 0x96, 0x6a, 0x7e, 0xa3, 0x5c, 0xf3, 0xad, 0xab, 0x36,
	0x2b, 0xc5, 0xe9, 0x3b, 0x24, 0x90, 0xb9, 0xb6, 0x3b, 0x75, 0xda, 0x5e, 0x29, 0x69, 0xdb, 0xf5,
	0x2b, 0xdf, 0x5a, 0xa7, 0xd3, 0x1d, 0x2b, 0xd5, 0xdc, 0x90, 0x1e, 0x88, 0x2b, 0xf6, 0x42, 0xc1,
	0xde, 0x32, 0x31, 0xbb, 0x32, 0xd1, 0xfc, 0xb7, 0x09, 0xab, 0x96, 0x74, 0x89, 0xe9, 0x34, 0x2c,
	0xa6, 0x63, 0xf3, 0xa6, 0x66, 0x95, 0x37, 0xd5, 0x95, 0xc5, 0x56, 0x7d, 0x59, 0xdc, 0x02, 0xb5,
	0x0e, 0xa3, 0x5d, 0x05, 0x66, 0x0f, 0x01, 0x1d, 0xed, 0xd2, 0x43, 0xe2, 0x34, 0xa1, 0xf9, 0x4c,
	0xbb, 0xae, 0xf4, 0x90, 0x23, 0x04, 0xc8, 0x43, 0x18, 0x89, 0x3c, 0x48, 0x78, 0x96, 0xe6, 0x42,
	0x71, 0x62, 0x45, 0xba, 0x2a, 0xa8, 0xae, 0x00, 0xdd, 0x79, 0x05, 0xb8, 0x0f, 0x43, 0x3e, 0xcd,
	0xb3, 0xc9, 0x94, 0x17, 0x44, 0xbe, 0xe3, 0x0d, 0x34, 0x86, 0x9c, 0xc7, 0x66, 0x6b, 0xfd, 0x2a,
	0x5b, 0x7b, 0x08, 0x6b, 0xb2, 0x84, 0x4f, 0x4a, 0xaf, 0x09, 0xc5, 0xb8, 0x56, 0x11, 0x3e, 0x29,
	0x91, 0x3e, 0xc6, 0x4d, 0xd9, 0x44, 0xd2, 0xd5, 0xf1, 0xfa, 0x8c, 0xeb, 0x72, 0xe9, 0x5e, 0xc0,
	0x96, 0x4d, 0x89, 0x15, 0x95, 0x35, 0xaf, 0xa9, 0x6a, 0xe5, 0xba, 0x03, 0x5d, 0x1e, 0xe6, 0x71,
	0xe1, 0x81, 0x2b, 0x72, 0x78, 0x18, 0x29, 0x52, 0x23, 0xa6, 0x79, 0xe2, 0x2b, 0xe2, 0xac, 0xcd,
	0x3a, 0x54, 0xa0, 0xda, 0xd4, 0xfd, 0x5b, 0x1f, 0x7e, 0x50, 0x7f, 0xda, 0x07, 0xf1, 0xfe, 0x1f,
	0x69, 0x4f, 0x5b, 0xc3, 0x5c, 0xb7, 0xb3, 0x48, 0xef, 0xed, 0xdd, 0xcb, 0xd9, 0xef, 0x2f, 0x3d,
	0x9d, 0xfd, 0xaa, 0x17, 0x54, 0xea, 0xc2, 0x6c, 0x48, 0x73, 0x93, 0x11, 0x74, 0x3e, 0xa4, 0x79,
	0x95, 0x03, 0xb6, 0x96, 0xe0, 0x80, 0xed, 0x3a, 0x0e, 0x68, 0x57, 0xc0, 0x4e, 0xb5, 0x02, 0x12,
	0x68, 0xa3, 0x5b, 0x28, 0x67, 0xc2, 0xdf, 0x16, 0x3d, 0xee, 0xda, 0xf4, 0xd8, 0x76, 0x95, 0xde,
	0xd5, 0xc4, 0xbe, 0x5f, 0xcd, 0x72, 0xcb, 0x7a, 0xd2, 0x02, 0x6f, 0x1d, 0xd4, 0xf0, 0xd6, 0xba,
	0xb8, 0x1b, 0xd6, 0xc7, 0x5d, 0x0d, 0xc5, 0x5d, 0xad, 0xa5, 0xb8, 0x45, 0x0a, 0x18, 0x59, 0x29,
	0xe0, 0x57, 0xa6, 0xb6, 0x9b, 0x77, 0xde, 0x1a, 0xa6, 0x9c, 0xe7, 0x4b, 0x39, 0xc2, 0x6e, 0x69,
	0x82, 0x6b, 0x3e, 0x60, 0x9e, 0x71, 0x0f, 0x60, 0x75, 0xfe, 0xdc, 0x88, 0x28, 0x0f, 0xf5, 0x53,
	0x60, 0x68, 0xc0, 0x03, 0xca, 0x43, 0x32, 0x86, 0x5e, 0x96, 0xa7, 0x67, 0x78, 0x43, 0xf9, 0x16,
	0x68, 0x7a, 0xf3, 0xb1, 0x0c, 0x6f, 0x59, 0xf6, 0xa6, 0xa7, 0x3c, 0xcc, 0xd9, 0xa9, 0x7a, 0x07,
	0x74, 0xbc, 0x01, 0xe3, 0xc7, 0x06, 0x22, 0x9f, 0xc3, 0x0a, 0x92, 0x1a, 0xee, 0xdc, 0xfc, 0x7f,
	0xbe, 0xba, 0xc4, 0xc2, 0xb8, 0xa7, 0x77, 0x58, 0x78, 0x66, 0xde, 0x5a, 0x7c, 0x66, 0xae, 0x43,
	0x6b, 0x92, 0x9c, 0xe9, 0x97, 0x81, 0xfc, 0x89, 0x48, 0x20, 0x34, 0xf5, 0x97, 0x3f, 0xed, 0x02,
	0x73, 0xa7, 0x52, 0x60, 0xee, 0x83, 0xa1, 0xe3, 0x7e, 0x14, 0xcc, 0xb8, 0xe6, 0xf8, 0x03, 0x8d,
	0x1d, 0x04, 0x33, 0x3e, 0xfe, 0x7d, 0x03, 0x86, 0x65, 0xad, 0xca, 0xd0, 0x35, 0x51, 0xa0, 0x39,
	0xb2, 0x1e, 0xca, 0xa3, 0x12, 0x16, 0x5e, 0x94, 0x93, 0x77, 0x4f, 0x02, 0xe6, 0x31, 0x51, 0xf2,
	0xd7, 0x56, 0xd5, 0x5f, 0xed, 0xd8, 0x69, 0x57, 0x62, 0x67, 0x7c, 0x00, 0xc3, 0xb2, 0x92, 0x0a,
	0x36, 0x39, 0x8f, 0x73, 0xc5, 0x26, 0x0f, 0x23, 0x8b, 0x68, 0x36, 0x6d, 0x12, 0xff, 0xe7, 0x06,
	0xdc, 0xb7, 0xad, 0x61, 0x2a, 0x52, 0xb9, 0xd9, 0x54, 0x55, 0x7c, 0x63, 0x51, 0xf1, 0x56, 0xe5,
	0x6d, 0x55, 0x2a, 0xaf, 0x2c, 0x3d, 0xa9, 0x7c, 0x10, 0x9b, 0x5e, 0x52, 0xc7, 0xeb, 0x49, 0x00,
	0xe3, 0xad, 0x94, 0x6b, 0x3b, 0xe5, 0x5c, 0xeb, 0x7e, 0xdb, 0x04, 0xf7, 0xaa, 0x6f, 0xfb, 0xd0,
	0x26, 0xca, 0x62, 0x3d, 0x26, 0x3f, 0xd3, 0x29, 0xf6, 0xd2, 0x0e, 0x4a, 0xdd, 0xc9, 0xbb, 0x38,
	0x50, 0x89, 0xf6, 0x4f, 0x0d, 0x68, 0x23, 0x11, 0xaf, 0x26, 0xda, 0x2b, 0x3d, 0xa0, 0xe4, 0x38,
	0x2d, 0xdb, 0x71, 0x4c, 0x66, 0x6c, 0x97, 0x32, 0xe3, 0x06, 0xac, 0x68, 0x5f, 0x51, 0xf9, 0x52,
	0x8f, 0xec, 0x66, 0x48, 0xd7, 0x6e, 0x86, 0xb8, 0xaf, 0xe1, 0x63, 0xfb, 0x26, 0xaf, 0x82, 0x5c,
	0xb0, 0x90, 0x65, 0x41, 0x22, 0xbe, 0x47, 0x13, 0xbb, 0xbf, 0x6b, 0xc2, 0xce, 0x35, 0x07, 0x7d,
	0x90, 0xbd, 0x5e, 0x58, 0x96, 0xd9, 0x5d, 0xb4, 0xcc, 0x25, 0xc7, 0x94, 0x8d, 0x43, 0xb5, 0x6d,
	0xbe, 0x07, 0x5b, 0xf4, 0xb5, 0x2d, 0x24, 0x26, 0xd5, 0xdd, 0xd1, 0xf6, 0x91, 0xaa, 0xfe, 0xb6,
	0x0d, 0x9b, 0x95, 0xbe, 0x5b, 0x26, 0x1f, 0xec, 0x4a, 0xc1, 0x9f, 0x00, 0x51, 0xf4, 0xab, 0x46,
	0xcd, 0xeb, 0x38, 0x53, 0x6e, 0x47, 0xd4, 0x14, 0xab, 0xe6, 0x52, 0xc5, 0xaa, 0xb5, 0x5c, 0x93,
	0xa5, 0x5d, 0x5b, 0x81, 0x76, 0x60, 0x8e, 0xe0, 0xb3, 0x96, 0xea, 0x88, 0x9c, 0x9f, 0x21, 0x1f,
	0xb6, 0xf6, 0xa1, 0x21, 0x13, 0x33, 0xf3, 0xd8, 0x34, 0xe0, 0xbe, 0x24, 0xb4, 0x4f, 0xe1, 0xc6,
	0x5c, 0x28, 0x62, 0x5c, 0xe4, 0x2c, 0x14, 0xba, 0xa0, 0xcf, 0x4b, 0xe7, 0x81, 0xc6, 0x6b, 0xcb,
	0x69, 0xef, 0x52, 0x1a, 0x5b, 0x64, 0xef, 0x7e, 0x25, 0x7b, 0xeb, 0xf4, 0x0f, 0x0b, 0xe9, 0x7f,
	0x60, 0xa5, 0xff, 0xc2, 0x0b, 0x86, 0x8b, 0x39, 0x39, 0x78, 0x1b, 0x88, 0x20, 0xf7, 0xa7, 0xf9,
	0x44, 0xd7, 0xe9, 0xbe, 0x42, 0xbe, 0xca, 0x27, 0x95, 0xfe, 0xcf, 0xe8, 0xea, 0xfe, 0xcf, 0x5a,
	0xb5, 0xff, 0xe3, 0xc2, 0xaa, 0x7c, 0x67, 0xda, 0xad, 0x37, 0x59, 0x5c, 0x18, 0x3f, 0x32, 0x6f,
	0x39, 0x01, 0xe3, 0x3a, 0x1f, 0xd2, 0xb1, 0xa3, 0x9f, 0x21, 0xcd, 0xe2, 0x19, 0x52, 0xdf, 0x14,
	0xde, 0x9d, 0x47, 0x8d, 0xa4, 0x8c, 0xe3, 0x9a, 0x8e, 0x70, 0x46, 0x93, 0x82, 0x27, 0xba, 0x27,
	0xb0, 0x51, 0x3f, 0x5f, 0x74, 0x11, 0x59, 0x84, 0xbb, 0xb5, 0x74, 0x17, 0x51, 0x35, 0x0a, 0x05,
	0x8d, 0xb3, 0x49, 0x20, 0x68, 0x91, 0xbc, 0xc1, 0x40, 0x87, 0x91, 0x7b, 0x50, 0xbd, 0x4b, 0xb9,
	0x55, 0xb5, 0xec, 0x5d, 0xdc, 0xa7, 0x30, 0xfa, 0x9c, 0xa7, 0xc9, 0xb1, 0xc8, 0x4d, 0x28, 0x6d,
	0x42, 0xef, 0x6b, 0x9e, 0x26, 0x3e, 0x17, 0xea, 0xe9, 0x3c, 0xf4, 0xba, 0x5f, 0x2b, 0x09, 0x77,
	0x52, 0xcd, 0x42, 0x47, 0x2a, 0x8f, 0xcc, 0x09, 0x89, 0xd9, 0xc3, 0x04, 0x70, 0xa3, 0x08, 0x60,
	0x99, 0x60, 0x93, 0x29, 0xb6, 0x7a, 0x34, 0xe9, 0x57, 0xa3, 0x72, 0x85, 0x6a, 0x59, 0x15, 0xea,
	0xd7, 0x70, 0xe7, 0xab, 0x2c, 0x0a, 0x04, 0xc5, 0x1a, 0x6c, 0x9e, 0xf5, 0xb8, 0xff, 0x4f, 0x60,
	0x2b, 0xcb, 0xd3, 0x38, 0x95, 0xcf, 0x22, 0x5f, 0xb7, 0x66, 0x74, 0x0d, 0x29, 0x2a, 0xb4, 0x33,
	0x17, 0xb1, 0x8a, 0xcc, 0x61, 0xf4, 0xfc, 0x1f, 0x5d, 0xb8, 0x6d, 0x5f, 0xe4, 0x98, 0xe6, 0x6f,
	0x59, 0x48, 0xc9, 0x6f, 0x60, 0x58, 0x2e, 0x46, 0x64, 0xe7, 0xba, 0x62, 0x85, 0xdf, 0x33, 0x7e,
	0xb8, 0x5c, 0x4d, 0x23, 0xaf, 0x61, 0xad, 0x92, 0x51, 0xc9, 0xe3, 0x25, 0x92, 0xae, 0x3a, 0xe4,
	0xc9, 0xd2, 0xe9, 0x99, 0xfc, 0x12, 0x56, 0x14, 0x7d, 0x22, 0xf7, 0x2e, 0x67, 0x84, 0x6a, 0xd7,
	0xed, 0xeb, 0x28, 0x23, 0x79, 0x02, 0x5d, 0xdd, 0xfd, 0x21, 0x23, 0xab, 0x15, 0xf4, 0x66, 0x6c,
	0x8f, 0xb9, 0x14, 0xd5, 0x4f, 0x7b, 0x25, 0x5a, 0x74, 0x63, 0xc6, 0xf6, 0x98, 0x93, 0x1f, 0xc2,
	0xa0, 0xd4, 0x05, 0x20, 0xa4, 0x34, 0xad, 0x2f, 0x3d, 0x5e, 0xc4, 0xd4, 0xb2, 0xa2, 0x4f, 0xa9,
	0x97, 0x59, 0x8d, 0xd2, 0xf1, 0x22, 0xc6, 0xc9, 0x8f, 0x61, 0x64, 0x37, 0x69, 0xc9, 0xed, 0x79,
	0x4f, 0xa1, 0xdc, 0x92, 0x1d, 0x63, 0xb3, 0xab, 0xda, 0xf4, 0xfd, 0xa9, 0x69, 0x5c, 0x16, 0xcb,
	0x37, 0x8a, 0xee, 0xe7, 0xf5, 0xeb, 0x8f, 0x81, 0x2c, 0x26, 0x00, 0x72, 0xb7, 0x3e, 0x71, 0x18,
	0xc3, 0x7c, 0x74, 0xd9, 0xb4, 0x36, 0x8b, 0x07, 0xeb, 0xd5, 0xf0, 0x23, 0x35, 0x2e, 0x72, 0x49,
	0x88, 0xd6, 0x7f, 0xe8, 0x01, 0xac, 0x57, 0x43, 0x8e, 0x6c, 0x49, 0xc1, 0x4b, 0x02, 0xb1, 0x7e,
	0x97, 0x13, 0xe8, 0xcf, 0xff, 0x13, 0x23, 0xee, 0x95, 0x7f, 0x98, 0xa9, 0x5d, 0x1e, 0x2c, 0xf1,
	0xa7, 0xda, 0xe9, 0x0a, 0xfe, 0x83, 0xfb, 0xe9, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0x60, 0x16,
	0x31, 0x23, 0xdd, 0x1d, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CommunityGroupServiceClient is the client API for CommunityGroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CommunityGroupServiceClient interface {
	//  团列表-可参加团的列表
	ActivityList(ctx context.Context, in *CommunityGroupActivityListRequest, opts ...grpc.CallOption) (*CommunityGroupActivityListResponse, error)
	//  团列表-可参加团的列表
	ParticipantList(ctx context.Context, in *CommunityGroupParticipantListRequest, opts ...grpc.CallOption) (*CommunityGroupParticipantListResponse, error)
	//  团详情
	Detail(ctx context.Context, in *CommunityGroupDetailRequest, opts ...grpc.CallOption) (*CommunityGroupDetailResponse, error)
	// 是否是团长
	IsGroup(ctx context.Context, in *IsGroupReq, opts ...grpc.CallOption) (*IsGroupRes, error)
	// 我的团-佣金数据
	MyGroup(ctx context.Context, in *MyGroupReq, opts ...grpc.CallOption) (*MyGroupRes, error)
	// 我的团-列表数据
	MyGroupList(ctx context.Context, in *MyGroupListReq, opts ...grpc.CallOption) (*MyGroupListRes, error)
	// 团员的订单
	MerberOrder(ctx context.Context, in *MerberOrderReq, opts ...grpc.CallOption) (*MerberOrderRes, error)
	// 我的团-确认收货
	GroupCompleted(ctx context.Context, in *GroupCompletedReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 团员订单-确认完成
	MemberCompleted(ctx context.Context, in *MemberCompletedReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 开团
	CommunityGroupOpen(ctx context.Context, in *CommunityGroupOpenRequest, opts ...grpc.CallOption) (*CommunityGroupOpenResponse, error)
	//消息订阅
	MessageSubscribe(ctx context.Context, in *CommunityGroupMessageSubscribeRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//活动结束更新开团状态
	UpdateOrderGroup(ctx context.Context, in *UpdateOrderGroupRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 团订单列表
	OrderList(ctx context.Context, in *CommunityGroupOrderListRequest, opts ...grpc.CallOption) (*CommunityGroupOrderListResponse, error)
}

type communityGroupServiceClient struct {
	cc *grpc.ClientConn
}

func NewCommunityGroupServiceClient(cc *grpc.ClientConn) CommunityGroupServiceClient {
	return &communityGroupServiceClient{cc}
}

func (c *communityGroupServiceClient) ActivityList(ctx context.Context, in *CommunityGroupActivityListRequest, opts ...grpc.CallOption) (*CommunityGroupActivityListResponse, error) {
	out := new(CommunityGroupActivityListResponse)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/ActivityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) ParticipantList(ctx context.Context, in *CommunityGroupParticipantListRequest, opts ...grpc.CallOption) (*CommunityGroupParticipantListResponse, error) {
	out := new(CommunityGroupParticipantListResponse)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/ParticipantList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) Detail(ctx context.Context, in *CommunityGroupDetailRequest, opts ...grpc.CallOption) (*CommunityGroupDetailResponse, error) {
	out := new(CommunityGroupDetailResponse)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/Detail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) IsGroup(ctx context.Context, in *IsGroupReq, opts ...grpc.CallOption) (*IsGroupRes, error) {
	out := new(IsGroupRes)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/IsGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) MyGroup(ctx context.Context, in *MyGroupReq, opts ...grpc.CallOption) (*MyGroupRes, error) {
	out := new(MyGroupRes)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/MyGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) MyGroupList(ctx context.Context, in *MyGroupListReq, opts ...grpc.CallOption) (*MyGroupListRes, error) {
	out := new(MyGroupListRes)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/MyGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) MerberOrder(ctx context.Context, in *MerberOrderReq, opts ...grpc.CallOption) (*MerberOrderRes, error) {
	out := new(MerberOrderRes)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/MerberOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) GroupCompleted(ctx context.Context, in *GroupCompletedReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/GroupCompleted", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) MemberCompleted(ctx context.Context, in *MemberCompletedReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/MemberCompleted", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) CommunityGroupOpen(ctx context.Context, in *CommunityGroupOpenRequest, opts ...grpc.CallOption) (*CommunityGroupOpenResponse, error) {
	out := new(CommunityGroupOpenResponse)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/CommunityGroupOpen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) MessageSubscribe(ctx context.Context, in *CommunityGroupMessageSubscribeRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/MessageSubscribe", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) UpdateOrderGroup(ctx context.Context, in *UpdateOrderGroupRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/UpdateOrderGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *communityGroupServiceClient) OrderList(ctx context.Context, in *CommunityGroupOrderListRequest, opts ...grpc.CallOption) (*CommunityGroupOrderListResponse, error) {
	out := new(CommunityGroupOrderListResponse)
	err := c.cc.Invoke(ctx, "/oc.CommunityGroupService/OrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CommunityGroupServiceServer is the server API for CommunityGroupService service.
type CommunityGroupServiceServer interface {
	//  团列表-可参加团的列表
	ActivityList(context.Context, *CommunityGroupActivityListRequest) (*CommunityGroupActivityListResponse, error)
	//  团列表-可参加团的列表
	ParticipantList(context.Context, *CommunityGroupParticipantListRequest) (*CommunityGroupParticipantListResponse, error)
	//  团详情
	Detail(context.Context, *CommunityGroupDetailRequest) (*CommunityGroupDetailResponse, error)
	// 是否是团长
	IsGroup(context.Context, *IsGroupReq) (*IsGroupRes, error)
	// 我的团-佣金数据
	MyGroup(context.Context, *MyGroupReq) (*MyGroupRes, error)
	// 我的团-列表数据
	MyGroupList(context.Context, *MyGroupListReq) (*MyGroupListRes, error)
	// 团员的订单
	MerberOrder(context.Context, *MerberOrderReq) (*MerberOrderRes, error)
	// 我的团-确认收货
	GroupCompleted(context.Context, *GroupCompletedReq) (*BaseResponseNew, error)
	// 团员订单-确认完成
	MemberCompleted(context.Context, *MemberCompletedReq) (*BaseResponseNew, error)
	// 开团
	CommunityGroupOpen(context.Context, *CommunityGroupOpenRequest) (*CommunityGroupOpenResponse, error)
	//消息订阅
	MessageSubscribe(context.Context, *CommunityGroupMessageSubscribeRequest) (*BaseResponseNew, error)
	//活动结束更新开团状态
	UpdateOrderGroup(context.Context, *UpdateOrderGroupRequest) (*BaseResponseNew, error)
	// 团订单列表
	OrderList(context.Context, *CommunityGroupOrderListRequest) (*CommunityGroupOrderListResponse, error)
}

// UnimplementedCommunityGroupServiceServer can be embedded to have forward compatible implementations.
type UnimplementedCommunityGroupServiceServer struct {
}

func (*UnimplementedCommunityGroupServiceServer) ActivityList(ctx context.Context, req *CommunityGroupActivityListRequest) (*CommunityGroupActivityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityList not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) ParticipantList(ctx context.Context, req *CommunityGroupParticipantListRequest) (*CommunityGroupParticipantListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParticipantList not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) Detail(ctx context.Context, req *CommunityGroupDetailRequest) (*CommunityGroupDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Detail not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) IsGroup(ctx context.Context, req *IsGroupReq) (*IsGroupRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsGroup not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) MyGroup(ctx context.Context, req *MyGroupReq) (*MyGroupRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MyGroup not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) MyGroupList(ctx context.Context, req *MyGroupListReq) (*MyGroupListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MyGroupList not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) MerberOrder(ctx context.Context, req *MerberOrderReq) (*MerberOrderRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MerberOrder not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) GroupCompleted(ctx context.Context, req *GroupCompletedReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupCompleted not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) MemberCompleted(ctx context.Context, req *MemberCompletedReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberCompleted not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) CommunityGroupOpen(ctx context.Context, req *CommunityGroupOpenRequest) (*CommunityGroupOpenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommunityGroupOpen not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) MessageSubscribe(ctx context.Context, req *CommunityGroupMessageSubscribeRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MessageSubscribe not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) UpdateOrderGroup(ctx context.Context, req *UpdateOrderGroupRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrderGroup not implemented")
}
func (*UnimplementedCommunityGroupServiceServer) OrderList(ctx context.Context, req *CommunityGroupOrderListRequest) (*CommunityGroupOrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderList not implemented")
}

func RegisterCommunityGroupServiceServer(s *grpc.Server, srv CommunityGroupServiceServer) {
	s.RegisterService(&_CommunityGroupService_serviceDesc, srv)
}

func _CommunityGroupService_ActivityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityGroupActivityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).ActivityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/ActivityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).ActivityList(ctx, req.(*CommunityGroupActivityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_ParticipantList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityGroupParticipantListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).ParticipantList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/ParticipantList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).ParticipantList(ctx, req.(*CommunityGroupParticipantListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_Detail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityGroupDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).Detail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/Detail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).Detail(ctx, req.(*CommunityGroupDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_IsGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).IsGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/IsGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).IsGroup(ctx, req.(*IsGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_MyGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MyGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).MyGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/MyGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).MyGroup(ctx, req.(*MyGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_MyGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MyGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).MyGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/MyGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).MyGroupList(ctx, req.(*MyGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_MerberOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MerberOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).MerberOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/MerberOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).MerberOrder(ctx, req.(*MerberOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_GroupCompleted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupCompletedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).GroupCompleted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/GroupCompleted",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).GroupCompleted(ctx, req.(*GroupCompletedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_MemberCompleted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberCompletedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).MemberCompleted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/MemberCompleted",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).MemberCompleted(ctx, req.(*MemberCompletedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_CommunityGroupOpen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityGroupOpenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).CommunityGroupOpen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/CommunityGroupOpen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).CommunityGroupOpen(ctx, req.(*CommunityGroupOpenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_MessageSubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityGroupMessageSubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).MessageSubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/MessageSubscribe",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).MessageSubscribe(ctx, req.(*CommunityGroupMessageSubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_UpdateOrderGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrderGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).UpdateOrderGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/UpdateOrderGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).UpdateOrderGroup(ctx, req.(*UpdateOrderGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommunityGroupService_OrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityGroupOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommunityGroupServiceServer).OrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.CommunityGroupService/OrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommunityGroupServiceServer).OrderList(ctx, req.(*CommunityGroupOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CommunityGroupService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.CommunityGroupService",
	HandlerType: (*CommunityGroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ActivityList",
			Handler:    _CommunityGroupService_ActivityList_Handler,
		},
		{
			MethodName: "ParticipantList",
			Handler:    _CommunityGroupService_ParticipantList_Handler,
		},
		{
			MethodName: "Detail",
			Handler:    _CommunityGroupService_Detail_Handler,
		},
		{
			MethodName: "IsGroup",
			Handler:    _CommunityGroupService_IsGroup_Handler,
		},
		{
			MethodName: "MyGroup",
			Handler:    _CommunityGroupService_MyGroup_Handler,
		},
		{
			MethodName: "MyGroupList",
			Handler:    _CommunityGroupService_MyGroupList_Handler,
		},
		{
			MethodName: "MerberOrder",
			Handler:    _CommunityGroupService_MerberOrder_Handler,
		},
		{
			MethodName: "GroupCompleted",
			Handler:    _CommunityGroupService_GroupCompleted_Handler,
		},
		{
			MethodName: "MemberCompleted",
			Handler:    _CommunityGroupService_MemberCompleted_Handler,
		},
		{
			MethodName: "CommunityGroupOpen",
			Handler:    _CommunityGroupService_CommunityGroupOpen_Handler,
		},
		{
			MethodName: "MessageSubscribe",
			Handler:    _CommunityGroupService_MessageSubscribe_Handler,
		},
		{
			MethodName: "UpdateOrderGroup",
			Handler:    _CommunityGroupService_UpdateOrderGroup_Handler,
		},
		{
			MethodName: "OrderList",
			Handler:    _CommunityGroupService_OrderList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/community_group.proto",
}
