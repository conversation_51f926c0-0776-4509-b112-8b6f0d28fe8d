package oc

import (
	"context"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	lock      sync.Mutex
	Conn      *grpc.ClientConn
	Ctx       context.Context
	RPC       OrderServiceClient
	OES       OrderExceptionServiceClient
	ROC       RefundOrderServiceClient
	AfterSale AfterSaleServiceClient
	Cart      CartServiceClient
	Integral  OrderIntegralServiceClient
	UpetDJ    UpetDjServiceClient
	SM        SubscribeMessageServiceClient
	MP        AdvertisementMpServiceClient
	IV        InvoiceServiceClient
	IS        IntegralServiceClient
	CG        CommunityGroupServiceClient
	DOrder    DigitalOrderServiceClient
	VC        VipCardOrderServiceClient
	Card      CardServiceClient
}

var grpcClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}
}

type PlatformChannel struct {
	ChannelId  int
	UserAgent  int
	AppChannel int
}

type GrpcContext struct {
	Channel PlatformChannel
}

func GetOrderServiceClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 5*time.Minute)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return NewClient()
}

func NewClient(customUrl ...string) *Client {
	var (
		err error
		url string
	)

	if len(customUrl) > 0 {
		url = customUrl[0]
	} else {
		url = config.GetString("grpc.order-center")
	}

	//url = "127.0.0.1:11005"
	if url == "" {
		url = "127.0.0.1:11005"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("order-center，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = NewOrderServiceClient(grpcClient.Conn)
		grpcClient.OES = NewOrderExceptionServiceClient(grpcClient.Conn)
		grpcClient.ROC = NewRefundOrderServiceClient(grpcClient.Conn)
		grpcClient.AfterSale = NewAfterSaleServiceClient(grpcClient.Conn)
		grpcClient.Cart = NewCartServiceClient(grpcClient.Conn)
		grpcClient.Integral = NewOrderIntegralServiceClient(grpcClient.Conn)
		grpcClient.UpetDJ = NewUpetDjServiceClient(grpcClient.Conn)
		grpcClient.SM = NewSubscribeMessageServiceClient(grpcClient.Conn)
		grpcClient.MP = NewAdvertisementMpServiceClient(grpcClient.Conn)
		grpcClient.IV = NewInvoiceServiceClient(grpcClient.Conn)
		grpcClient.IS = NewIntegralServiceClient(grpcClient.Conn)
		grpcClient.CG = NewCommunityGroupServiceClient(grpcClient.Conn)
		grpcClient.DOrder = NewDigitalOrderServiceClient(grpcClient.Conn)
		grpcClient.VC = NewVipCardOrderServiceClient(grpcClient.Conn)
		grpcClient.Card = NewCardServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
}
