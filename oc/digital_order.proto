syntax = "proto3";

package oc;

option go_package ="./oc";

// 数字藏品订单服务
service DigitalOrderService {
  //创建订单
  rpc CreateDigitalOrder(CreateDigitalOrderRequest) returns(DigitalOrderResponse);
  //更新订单信息
  rpc UpdateDigitalOrder(UpdateDigitalOrderRequest) returns(DigitalOrderResponse);
  //订单支付
  rpc PayDigitalOrder(PayDigitalOrderRequest) returns(PayDigitalOrderResponse);
  //订单支付回调
  rpc PayDigitalOrderNotify(PayDigitalOrderNotifyRequest) returns(DigitalOrderResponse);
  //测试支付
  rpc OrderPayTest(OrderPayTestRequest) returns(PayDigitalOrderResponse);
  //计算支付金额
//  rpc CalcOrderPayment(CalcOrderPaymentRequest) returns(CalcOrderPaymentResponse);
//  //校验是否购买商品
//  rpc FindOrderProduct(FindOrderProductRequest) returns(FindOrderProductResponse);
}

//创建订单入参
message CreateDigitalOrderRequest {
  string buy_user_id = 1;
  string sell_user_id = 2;
  string nft_id = 3;
  float price = 4;
  int32 status = 5;
  int32 sell_type = 6;
}

//返回值
message DigitalOrderResponse {
  int32 code = 1;
  string message = 2;
  string order_sn = 3;
}

//更新订单参数
message UpdateDigitalOrderRequest {
  string order_sn = 1;
  int32 status = 2;
  string pay_sn = 3;
  float pay_amount = 4;
}

//支付数字藏品订单参数
message PayDigitalOrderRequest {
  string openid = 1;
  string order_sn = 2;
  string user_id = 3;
}

//支付数字藏品订单返回值
message PayDigitalOrderResponse {
  int32 code = 1;
  string message = 2;
  string data = 3;
}

//支付回调参数
message PayDigitalOrderNotifyRequest {
  string order_sn = 1;
  int32 status = 2;
  string pay_sn = 3;
  float pay_amount = 4;
}

message OrderPayTestRequest {
  string openid = 1;
}

//计算支付金额参数
message CalcOrderPaymentRequest {
  string scrm_user_id = 1;
  string start = 2;
  string end = 3;
  float price = 4;
}

//计算支付金额返回值
message CalcOrderPaymentResponse {
  int32 code = 1;
  string message = 2;
  int32 flag = 3;
}

message FindOrderProductRequest {
  string sku_str = 1;
  string scrm_user_id = 2;
  string start = 3;
  string end = 4;
}

message FindOrderProductResponse {
  int32 code = 1;
  string message = 2;
  int32 flag = 3;
}