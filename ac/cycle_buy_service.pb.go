// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/cycle_buy_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//周期购列表请求
type CycleBuyListRequest struct {
	//活动状态：-5待添加商品审核，-4审核失败，-3:待提交，-2:待审核, -1 删除 0未开始 1进行中 2已结束 3已终止,如果要获取全部状态的活动，该参数传99
	Status    int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	Title     string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	PageIndex int32  `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize  int32  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//排序
	OrderBy              string   `protobuf:"bytes,6,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyListRequest) Reset()         { *m = CycleBuyListRequest{} }
func (m *CycleBuyListRequest) String() string { return proto.CompactTextString(m) }
func (*CycleBuyListRequest) ProtoMessage()    {}
func (*CycleBuyListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{0}
}

func (m *CycleBuyListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyListRequest.Unmarshal(m, b)
}
func (m *CycleBuyListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyListRequest.Marshal(b, m, deterministic)
}
func (m *CycleBuyListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyListRequest.Merge(m, src)
}
func (m *CycleBuyListRequest) XXX_Size() int {
	return xxx_messageInfo_CycleBuyListRequest.Size(m)
}
func (m *CycleBuyListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyListRequest proto.InternalMessageInfo

func (m *CycleBuyListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CycleBuyListRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CycleBuyListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CycleBuyListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CycleBuyListRequest) GetOrderBy() string {
	if m != nil {
		return m.OrderBy
	}
	return ""
}

//创建周期购活动请求
type CycleBuyRequest struct {
	// 活动id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动名称
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 活动开始时间
	BeginDate string `protobuf:"bytes,3,opt,name=begin_date,json=beginDate,proto3" json:"begin_date"`
	// 活动结束时间
	EndDate string `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	// 活动状态：-3:待提交，-2:待审核, -1 删除 0未开始 1进行中 2已结束 3已终止
	Status int32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	//活动是否免邮费
	IsShippingFree int32 `protobuf:"varint,6,opt,name=is_shipping_free,json=isShippingFree,proto3" json:"is_shipping_free"`
	//配送周期类型 1=3期 2=6期 3=12期
	CycleIds string `protobuf:"bytes,7,opt,name=cycle_ids,json=cycleIds,proto3" json:"cycle_ids"`
	//渠道id
	ChannelId int32 `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//创建时间
	CreateTime string `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//更新时间
	UpdateTime string `protobuf:"bytes,10,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	// 参加活动的商品数量
	ProductCount int32 `protobuf:"varint,11,opt,name=product_count,json=productCount,proto3" json:"product_count"`
	// 创建和编辑活动时，记录操作人
	//用户Id，即userno
	UserId string `protobuf:"bytes,12,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//userName,即登录人姓名
	UserName string `protobuf:"bytes,13,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//异常数量统计
	ExceptionCount int32 `protobuf:"varint,14,opt,name=exception_count,json=exceptionCount,proto3" json:"exception_count"`
	//商品总数
	GoodsTotal int32 `protobuf:"varint,15,opt,name=goods_total,json=goodsTotal,proto3" json:"goods_total"`
	// 审核原因
	CheckReason          string   `protobuf:"bytes,16,opt,name=check_reason,json=checkReason,proto3" json:"check_reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyRequest) Reset()         { *m = CycleBuyRequest{} }
func (m *CycleBuyRequest) String() string { return proto.CompactTextString(m) }
func (*CycleBuyRequest) ProtoMessage()    {}
func (*CycleBuyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{1}
}

func (m *CycleBuyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyRequest.Unmarshal(m, b)
}
func (m *CycleBuyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyRequest.Marshal(b, m, deterministic)
}
func (m *CycleBuyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyRequest.Merge(m, src)
}
func (m *CycleBuyRequest) XXX_Size() int {
	return xxx_messageInfo_CycleBuyRequest.Size(m)
}
func (m *CycleBuyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyRequest proto.InternalMessageInfo

func (m *CycleBuyRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CycleBuyRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CycleBuyRequest) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *CycleBuyRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *CycleBuyRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CycleBuyRequest) GetIsShippingFree() int32 {
	if m != nil {
		return m.IsShippingFree
	}
	return 0
}

func (m *CycleBuyRequest) GetCycleIds() string {
	if m != nil {
		return m.CycleIds
	}
	return ""
}

func (m *CycleBuyRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CycleBuyRequest) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *CycleBuyRequest) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *CycleBuyRequest) GetProductCount() int32 {
	if m != nil {
		return m.ProductCount
	}
	return 0
}

func (m *CycleBuyRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CycleBuyRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *CycleBuyRequest) GetExceptionCount() int32 {
	if m != nil {
		return m.ExceptionCount
	}
	return 0
}

func (m *CycleBuyRequest) GetGoodsTotal() int32 {
	if m != nil {
		return m.GoodsTotal
	}
	return 0
}

func (m *CycleBuyRequest) GetCheckReason() string {
	if m != nil {
		return m.CheckReason
	}
	return ""
}

//周期购列表响应
type CycleBuyListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//列表
	List []*CycleBuyRequest `protobuf:"bytes,4,rep,name=list,proto3" json:"list"`
	//总条数
	Total                int32    `protobuf:"varint,5,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyListResponse) Reset()         { *m = CycleBuyListResponse{} }
func (m *CycleBuyListResponse) String() string { return proto.CompactTextString(m) }
func (*CycleBuyListResponse) ProtoMessage()    {}
func (*CycleBuyListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{2}
}

func (m *CycleBuyListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyListResponse.Unmarshal(m, b)
}
func (m *CycleBuyListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyListResponse.Marshal(b, m, deterministic)
}
func (m *CycleBuyListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyListResponse.Merge(m, src)
}
func (m *CycleBuyListResponse) XXX_Size() int {
	return xxx_messageInfo_CycleBuyListResponse.Size(m)
}
func (m *CycleBuyListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyListResponse proto.InternalMessageInfo

func (m *CycleBuyListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CycleBuyListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CycleBuyListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CycleBuyListResponse) GetList() []*CycleBuyRequest {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *CycleBuyListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

//周期购ID
type CycleBuyIdRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	UserId               string   `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	UserName             string   `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyIdRequest) Reset()         { *m = CycleBuyIdRequest{} }
func (m *CycleBuyIdRequest) String() string { return proto.CompactTextString(m) }
func (*CycleBuyIdRequest) ProtoMessage()    {}
func (*CycleBuyIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{3}
}

func (m *CycleBuyIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyIdRequest.Unmarshal(m, b)
}
func (m *CycleBuyIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyIdRequest.Marshal(b, m, deterministic)
}
func (m *CycleBuyIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyIdRequest.Merge(m, src)
}
func (m *CycleBuyIdRequest) XXX_Size() int {
	return xxx_messageInfo_CycleBuyIdRequest.Size(m)
}
func (m *CycleBuyIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyIdRequest proto.InternalMessageInfo

func (m *CycleBuyIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CycleBuyIdRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CycleBuyIdRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

//周期购详情相应
type CycleBuyDetailResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string           `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Detail               *CycleBuyRequest `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *CycleBuyDetailResponse) Reset()         { *m = CycleBuyDetailResponse{} }
func (m *CycleBuyDetailResponse) String() string { return proto.CompactTextString(m) }
func (*CycleBuyDetailResponse) ProtoMessage()    {}
func (*CycleBuyDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{4}
}

func (m *CycleBuyDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyDetailResponse.Unmarshal(m, b)
}
func (m *CycleBuyDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyDetailResponse.Marshal(b, m, deterministic)
}
func (m *CycleBuyDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyDetailResponse.Merge(m, src)
}
func (m *CycleBuyDetailResponse) XXX_Size() int {
	return xxx_messageInfo_CycleBuyDetailResponse.Size(m)
}
func (m *CycleBuyDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyDetailResponse proto.InternalMessageInfo

func (m *CycleBuyDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CycleBuyDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CycleBuyDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CycleBuyDetailResponse) GetDetail() *CycleBuyRequest {
	if m != nil {
		return m.Detail
	}
	return nil
}

//统计周期购活动每个月未发货商品数 请求数据
type CycleBuyUnshipStatisticsRequest struct {
	PageIndex   int32  `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize    int32  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	ProductName string `protobuf:"bytes,3,opt,name=product_name,json=productName,proto3" json:"product_name"`
	//周期购活动id
	Cid                  int32    `protobuf:"varint,4,opt,name=cid,proto3" json:"cid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyUnshipStatisticsRequest) Reset()         { *m = CycleBuyUnshipStatisticsRequest{} }
func (m *CycleBuyUnshipStatisticsRequest) String() string { return proto.CompactTextString(m) }
func (*CycleBuyUnshipStatisticsRequest) ProtoMessage()    {}
func (*CycleBuyUnshipStatisticsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{5}
}

func (m *CycleBuyUnshipStatisticsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyUnshipStatisticsRequest.Unmarshal(m, b)
}
func (m *CycleBuyUnshipStatisticsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyUnshipStatisticsRequest.Marshal(b, m, deterministic)
}
func (m *CycleBuyUnshipStatisticsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyUnshipStatisticsRequest.Merge(m, src)
}
func (m *CycleBuyUnshipStatisticsRequest) XXX_Size() int {
	return xxx_messageInfo_CycleBuyUnshipStatisticsRequest.Size(m)
}
func (m *CycleBuyUnshipStatisticsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyUnshipStatisticsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyUnshipStatisticsRequest proto.InternalMessageInfo

func (m *CycleBuyUnshipStatisticsRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CycleBuyUnshipStatisticsRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CycleBuyUnshipStatisticsRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CycleBuyUnshipStatisticsRequest) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

//统计周期购活动每个月未发货商品数 响应数据
type CycleBuyUnshipStatisticsListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string               `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Total                int32                `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	List                 []*CycleProductTotal `protobuf:"bytes,5,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CycleBuyUnshipStatisticsListResponse) Reset()         { *m = CycleBuyUnshipStatisticsListResponse{} }
func (m *CycleBuyUnshipStatisticsListResponse) String() string { return proto.CompactTextString(m) }
func (*CycleBuyUnshipStatisticsListResponse) ProtoMessage()    {}
func (*CycleBuyUnshipStatisticsListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{6}
}

func (m *CycleBuyUnshipStatisticsListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyUnshipStatisticsListResponse.Unmarshal(m, b)
}
func (m *CycleBuyUnshipStatisticsListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyUnshipStatisticsListResponse.Marshal(b, m, deterministic)
}
func (m *CycleBuyUnshipStatisticsListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyUnshipStatisticsListResponse.Merge(m, src)
}
func (m *CycleBuyUnshipStatisticsListResponse) XXX_Size() int {
	return xxx_messageInfo_CycleBuyUnshipStatisticsListResponse.Size(m)
}
func (m *CycleBuyUnshipStatisticsListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyUnshipStatisticsListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyUnshipStatisticsListResponse proto.InternalMessageInfo

func (m *CycleBuyUnshipStatisticsListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CycleBuyUnshipStatisticsListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CycleBuyUnshipStatisticsListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CycleBuyUnshipStatisticsListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *CycleBuyUnshipStatisticsListResponse) GetList() []*CycleProductTotal {
	if m != nil {
		return m.List
	}
	return nil
}

// 统计 周期购活动 未发货 商品总数
type CycleProductTotal struct {
	SkuId                int32    `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	ProductName          string   `protobuf:"bytes,2,opt,name=product_name,json=productName,proto3" json:"product_name"`
	Jan                  int32    `protobuf:"varint,3,opt,name=jan,proto3" json:"jan"`
	Feb                  int32    `protobuf:"varint,4,opt,name=feb,proto3" json:"feb"`
	Mar                  int32    `protobuf:"varint,5,opt,name=mar,proto3" json:"mar"`
	Apr                  int32    `protobuf:"varint,6,opt,name=apr,proto3" json:"apr"`
	May                  int32    `protobuf:"varint,7,opt,name=may,proto3" json:"may"`
	Jun                  int32    `protobuf:"varint,8,opt,name=jun,proto3" json:"jun"`
	Jul                  int32    `protobuf:"varint,9,opt,name=jul,proto3" json:"jul"`
	Aug                  int32    `protobuf:"varint,10,opt,name=aug,proto3" json:"aug"`
	Sep                  int32    `protobuf:"varint,11,opt,name=sep,proto3" json:"sep"`
	Oct                  int32    `protobuf:"varint,12,opt,name=oct,proto3" json:"oct"`
	Nov                  int32    `protobuf:"varint,13,opt,name=nov,proto3" json:"nov"`
	Dec                  int32    `protobuf:"varint,14,opt,name=dec,proto3" json:"dec"`
	Cid                  int32    `protobuf:"varint,15,opt,name=cid,proto3" json:"cid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleProductTotal) Reset()         { *m = CycleProductTotal{} }
func (m *CycleProductTotal) String() string { return proto.CompactTextString(m) }
func (*CycleProductTotal) ProtoMessage()    {}
func (*CycleProductTotal) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{7}
}

func (m *CycleProductTotal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleProductTotal.Unmarshal(m, b)
}
func (m *CycleProductTotal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleProductTotal.Marshal(b, m, deterministic)
}
func (m *CycleProductTotal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleProductTotal.Merge(m, src)
}
func (m *CycleProductTotal) XXX_Size() int {
	return xxx_messageInfo_CycleProductTotal.Size(m)
}
func (m *CycleProductTotal) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleProductTotal.DiscardUnknown(m)
}

var xxx_messageInfo_CycleProductTotal proto.InternalMessageInfo

func (m *CycleProductTotal) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CycleProductTotal) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CycleProductTotal) GetJan() int32 {
	if m != nil {
		return m.Jan
	}
	return 0
}

func (m *CycleProductTotal) GetFeb() int32 {
	if m != nil {
		return m.Feb
	}
	return 0
}

func (m *CycleProductTotal) GetMar() int32 {
	if m != nil {
		return m.Mar
	}
	return 0
}

func (m *CycleProductTotal) GetApr() int32 {
	if m != nil {
		return m.Apr
	}
	return 0
}

func (m *CycleProductTotal) GetMay() int32 {
	if m != nil {
		return m.May
	}
	return 0
}

func (m *CycleProductTotal) GetJun() int32 {
	if m != nil {
		return m.Jun
	}
	return 0
}

func (m *CycleProductTotal) GetJul() int32 {
	if m != nil {
		return m.Jul
	}
	return 0
}

func (m *CycleProductTotal) GetAug() int32 {
	if m != nil {
		return m.Aug
	}
	return 0
}

func (m *CycleProductTotal) GetSep() int32 {
	if m != nil {
		return m.Sep
	}
	return 0
}

func (m *CycleProductTotal) GetOct() int32 {
	if m != nil {
		return m.Oct
	}
	return 0
}

func (m *CycleProductTotal) GetNov() int32 {
	if m != nil {
		return m.Nov
	}
	return 0
}

func (m *CycleProductTotal) GetDec() int32 {
	if m != nil {
		return m.Dec
	}
	return 0
}

func (m *CycleProductTotal) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

//周期购商品列表的请求数据
type GetCycleBuyProductListRequest struct {
	//周期购活动id
	Cid int32 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid"`
	//产品名称
	ProductName string `protobuf:"bytes,2,opt,name=productName,proto3" json:"productName"`
	//商品sku id
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	//商品的产品id
	ChannelId int32 `protobuf:"varint,5,opt,name=channelId,proto3" json:"channelId"`
	//商品状态 -1删除 0默认
	Status int32 `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	//排序 0 按商品排序设置排序，1；按成团真实订单数排序 默认为0
	OrderBy int32 `protobuf:"varint,7,opt,name=orderBy,proto3" json:"orderBy"`
	//分页参数
	Pagination *PaginationParam `protobuf:"bytes,8,opt,name=pagination,proto3" json:"pagination"`
	//是否导出 1导出
	Export int32 `protobuf:"varint,9,opt,name=export,proto3" json:"export"`
	// 1异常商品
	Type int32 `protobuf:"varint,10,opt,name=type,proto3" json:"type"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,11,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCycleBuyProductListRequest) Reset()         { *m = GetCycleBuyProductListRequest{} }
func (m *GetCycleBuyProductListRequest) String() string { return proto.CompactTextString(m) }
func (*GetCycleBuyProductListRequest) ProtoMessage()    {}
func (*GetCycleBuyProductListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{8}
}

func (m *GetCycleBuyProductListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCycleBuyProductListRequest.Unmarshal(m, b)
}
func (m *GetCycleBuyProductListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCycleBuyProductListRequest.Marshal(b, m, deterministic)
}
func (m *GetCycleBuyProductListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCycleBuyProductListRequest.Merge(m, src)
}
func (m *GetCycleBuyProductListRequest) XXX_Size() int {
	return xxx_messageInfo_GetCycleBuyProductListRequest.Size(m)
}
func (m *GetCycleBuyProductListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCycleBuyProductListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCycleBuyProductListRequest proto.InternalMessageInfo

func (m *GetCycleBuyProductListRequest) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetCycleBuyProductListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetCycleBuyProductListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetCycleBuyProductListRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetCycleBuyProductListRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetCycleBuyProductListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetCycleBuyProductListRequest) GetOrderBy() int32 {
	if m != nil {
		return m.OrderBy
	}
	return 0
}

func (m *GetCycleBuyProductListRequest) GetPagination() *PaginationParam {
	if m != nil {
		return m.Pagination
	}
	return nil
}

func (m *GetCycleBuyProductListRequest) GetExport() int32 {
	if m != nil {
		return m.Export
	}
	return 0
}

func (m *GetCycleBuyProductListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetCycleBuyProductListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//周期购商品列表
type GetCycleBuyProductListResponse struct {
	//响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//周期购商品信息
	Data []*CycleBuyProductData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	//异常商品数
	NonormalNum int32 `protobuf:"varint,6,opt,name=nonormal_num,json=nonormalNum,proto3" json:"nonormal_num"`
	//正常商品数
	NormalNum            int32    `protobuf:"varint,7,opt,name=normal_num,json=normalNum,proto3" json:"normal_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCycleBuyProductListResponse) Reset()         { *m = GetCycleBuyProductListResponse{} }
func (m *GetCycleBuyProductListResponse) String() string { return proto.CompactTextString(m) }
func (*GetCycleBuyProductListResponse) ProtoMessage()    {}
func (*GetCycleBuyProductListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{9}
}

func (m *GetCycleBuyProductListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCycleBuyProductListResponse.Unmarshal(m, b)
}
func (m *GetCycleBuyProductListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCycleBuyProductListResponse.Marshal(b, m, deterministic)
}
func (m *GetCycleBuyProductListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCycleBuyProductListResponse.Merge(m, src)
}
func (m *GetCycleBuyProductListResponse) XXX_Size() int {
	return xxx_messageInfo_GetCycleBuyProductListResponse.Size(m)
}
func (m *GetCycleBuyProductListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCycleBuyProductListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCycleBuyProductListResponse proto.InternalMessageInfo

func (m *GetCycleBuyProductListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCycleBuyProductListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetCycleBuyProductListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetCycleBuyProductListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetCycleBuyProductListResponse) GetData() []*CycleBuyProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GetCycleBuyProductListResponse) GetNonormalNum() int32 {
	if m != nil {
		return m.NonormalNum
	}
	return 0
}

func (m *GetCycleBuyProductListResponse) GetNormalNum() int32 {
	if m != nil {
		return m.NormalNum
	}
	return 0
}

//新增周期购活动商品
type CreateCycleBuyProductRequest struct {
	//商品sku id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,2,opt,name=productId,proto3" json:"productId"`
	//主体信息
	SaveData *SaveCycleBuyProductData `protobuf:"bytes,3,opt,name=saveData,proto3" json:"saveData"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateCycleBuyProductRequest) Reset()         { *m = CreateCycleBuyProductRequest{} }
func (m *CreateCycleBuyProductRequest) String() string { return proto.CompactTextString(m) }
func (*CreateCycleBuyProductRequest) ProtoMessage()    {}
func (*CreateCycleBuyProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{10}
}

func (m *CreateCycleBuyProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCycleBuyProductRequest.Unmarshal(m, b)
}
func (m *CreateCycleBuyProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCycleBuyProductRequest.Marshal(b, m, deterministic)
}
func (m *CreateCycleBuyProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCycleBuyProductRequest.Merge(m, src)
}
func (m *CreateCycleBuyProductRequest) XXX_Size() int {
	return xxx_messageInfo_CreateCycleBuyProductRequest.Size(m)
}
func (m *CreateCycleBuyProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCycleBuyProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCycleBuyProductRequest proto.InternalMessageInfo

func (m *CreateCycleBuyProductRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CreateCycleBuyProductRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CreateCycleBuyProductRequest) GetSaveData() *SaveCycleBuyProductData {
	if m != nil {
		return m.SaveData
	}
	return nil
}

func (m *CreateCycleBuyProductRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 更新周期购商品
type UpdateCycleBuyProductRequest struct {
	//需要更新的记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品sku id
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,3,opt,name=productId,proto3" json:"productId"`
	//主体信息
	SaveData *SaveCycleBuyProductData `protobuf:"bytes,4,opt,name=saveData,proto3" json:"saveData"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCycleBuyProductRequest) Reset()         { *m = UpdateCycleBuyProductRequest{} }
func (m *UpdateCycleBuyProductRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateCycleBuyProductRequest) ProtoMessage()    {}
func (*UpdateCycleBuyProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{11}
}

func (m *UpdateCycleBuyProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCycleBuyProductRequest.Unmarshal(m, b)
}
func (m *UpdateCycleBuyProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCycleBuyProductRequest.Marshal(b, m, deterministic)
}
func (m *UpdateCycleBuyProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCycleBuyProductRequest.Merge(m, src)
}
func (m *UpdateCycleBuyProductRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateCycleBuyProductRequest.Size(m)
}
func (m *UpdateCycleBuyProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCycleBuyProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCycleBuyProductRequest proto.InternalMessageInfo

func (m *UpdateCycleBuyProductRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateCycleBuyProductRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *UpdateCycleBuyProductRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UpdateCycleBuyProductRequest) GetSaveData() *SaveCycleBuyProductData {
	if m != nil {
		return m.SaveData
	}
	return nil
}

func (m *UpdateCycleBuyProductRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 周期购商品只需要id的请求
type CycleBuyProductDetailRequest struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//所属活动id
	Cid int32 `protobuf:"varint,2,opt,name=cid,proto3" json:"cid"`
	//商品skuId
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	//渠道 1电商 5商城
	ChannelId int32 `protobuf:"varint,5,opt,name=channelId,proto3" json:"channelId"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyProductDetailRequest) Reset()         { *m = CycleBuyProductDetailRequest{} }
func (m *CycleBuyProductDetailRequest) String() string { return proto.CompactTextString(m) }
func (*CycleBuyProductDetailRequest) ProtoMessage()    {}
func (*CycleBuyProductDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{12}
}

func (m *CycleBuyProductDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyProductDetailRequest.Unmarshal(m, b)
}
func (m *CycleBuyProductDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyProductDetailRequest.Marshal(b, m, deterministic)
}
func (m *CycleBuyProductDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyProductDetailRequest.Merge(m, src)
}
func (m *CycleBuyProductDetailRequest) XXX_Size() int {
	return xxx_messageInfo_CycleBuyProductDetailRequest.Size(m)
}
func (m *CycleBuyProductDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyProductDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyProductDetailRequest proto.InternalMessageInfo

func (m *CycleBuyProductDetailRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CycleBuyProductDetailRequest) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CycleBuyProductDetailRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CycleBuyProductDetailRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CycleBuyProductDetailRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CycleBuyProductDetailRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//获取周期购商品信息
type GetCycleBuyProductDetailResponse struct {
	//响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//周期购商品信息
	Data                 *CycleBuyProductDetailData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetCycleBuyProductDetailResponse) Reset()         { *m = GetCycleBuyProductDetailResponse{} }
func (m *GetCycleBuyProductDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetCycleBuyProductDetailResponse) ProtoMessage()    {}
func (*GetCycleBuyProductDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{13}
}

func (m *GetCycleBuyProductDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCycleBuyProductDetailResponse.Unmarshal(m, b)
}
func (m *GetCycleBuyProductDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCycleBuyProductDetailResponse.Marshal(b, m, deterministic)
}
func (m *GetCycleBuyProductDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCycleBuyProductDetailResponse.Merge(m, src)
}
func (m *GetCycleBuyProductDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetCycleBuyProductDetailResponse.Size(m)
}
func (m *GetCycleBuyProductDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCycleBuyProductDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCycleBuyProductDetailResponse proto.InternalMessageInfo

func (m *GetCycleBuyProductDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCycleBuyProductDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetCycleBuyProductDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetCycleBuyProductDetailResponse) GetData() *CycleBuyProductDetailData {
	if m != nil {
		return m.Data
	}
	return nil
}

//周期购商品只需要id的请求
type CycleBuyProductIdRequest struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyProductIdRequest) Reset()         { *m = CycleBuyProductIdRequest{} }
func (m *CycleBuyProductIdRequest) String() string { return proto.CompactTextString(m) }
func (*CycleBuyProductIdRequest) ProtoMessage()    {}
func (*CycleBuyProductIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{14}
}

func (m *CycleBuyProductIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyProductIdRequest.Unmarshal(m, b)
}
func (m *CycleBuyProductIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyProductIdRequest.Marshal(b, m, deterministic)
}
func (m *CycleBuyProductIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyProductIdRequest.Merge(m, src)
}
func (m *CycleBuyProductIdRequest) XXX_Size() int {
	return xxx_messageInfo_CycleBuyProductIdRequest.Size(m)
}
func (m *CycleBuyProductIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyProductIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyProductIdRequest proto.InternalMessageInfo

func (m *CycleBuyProductIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CycleBuyProductIdRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//分页参数
type PaginationParam struct {
	//当前多少页 从1开始 必传且必须大于0
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	//每页多少条数据 必传且必须大于0
	PageSize             int32    `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PaginationParam) Reset()         { *m = PaginationParam{} }
func (m *PaginationParam) String() string { return proto.CompactTextString(m) }
func (*PaginationParam) ProtoMessage()    {}
func (*PaginationParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{15}
}

func (m *PaginationParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PaginationParam.Unmarshal(m, b)
}
func (m *PaginationParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PaginationParam.Marshal(b, m, deterministic)
}
func (m *PaginationParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PaginationParam.Merge(m, src)
}
func (m *PaginationParam) XXX_Size() int {
	return xxx_messageInfo_PaginationParam.Size(m)
}
func (m *PaginationParam) XXX_DiscardUnknown() {
	xxx_messageInfo_PaginationParam.DiscardUnknown(m)
}

var xxx_messageInfo_PaginationParam proto.InternalMessageInfo

func (m *PaginationParam) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *PaginationParam) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

//周期购商品详细数据包含部分周期购信息
type CycleBuyProductDetailData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//所属活动id
	Cid int32 `protobuf:"varint,2,opt,name=cid,proto3" json:"cid"`
	//渠道ID
	ChannelId int32 `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	//商品sku id
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	//商品名称
	ProductName string `protobuf:"bytes,6,opt,name=productName,proto3" json:"productName"`
	//单买价 单位分
	SinglePrice int32 `protobuf:"varint,7,opt,name=singlePrice,proto3" json:"singlePrice"`
	//3期价 单位分
	ThreePrice int32 `protobuf:"varint,8,opt,name=threePrice,proto3" json:"threePrice"`
	//6期价 单位分
	SixPrice int32 `protobuf:"varint,9,opt,name=sixPrice,proto3" json:"sixPrice"`
	//12期价 单位分
	TwelvePrice int32 `protobuf:"varint,10,opt,name=twelvePrice,proto3" json:"twelvePrice"`
	//状态 -1删除 0默认
	Status int32 `protobuf:"varint,11,opt,name=status,proto3" json:"status"`
	//创建时间
	CreateTime string `protobuf:"bytes,12,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime string `protobuf:"bytes,13,opt,name=updateTime,proto3" json:"updateTime"`
	//周期购信息
	//开始时间
	BeginDate string `protobuf:"bytes,14,opt,name=beginDate,proto3" json:"beginDate"`
	//结束时间
	EndDate string `protobuf:"bytes,15,opt,name=endDate,proto3" json:"endDate"`
	//配送周期类型 1=3期 2=6期 3=12期
	CycleIds string `protobuf:"bytes,16,opt,name=cycleIds,proto3" json:"cycleIds"`
	//是否免邮费 0否1是
	IsShippingFree int32 `protobuf:"varint,17,opt,name=isShippingFree,proto3" json:"isShippingFree"`
	// 是否是虚拟商品 1是 0 否
	IsVirtual int32 `protobuf:"varint,18,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType            int32    `protobuf:"varint,19,opt,name=goodsType,proto3" json:"goodsType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyProductDetailData) Reset()         { *m = CycleBuyProductDetailData{} }
func (m *CycleBuyProductDetailData) String() string { return proto.CompactTextString(m) }
func (*CycleBuyProductDetailData) ProtoMessage()    {}
func (*CycleBuyProductDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{16}
}

func (m *CycleBuyProductDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyProductDetailData.Unmarshal(m, b)
}
func (m *CycleBuyProductDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyProductDetailData.Marshal(b, m, deterministic)
}
func (m *CycleBuyProductDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyProductDetailData.Merge(m, src)
}
func (m *CycleBuyProductDetailData) XXX_Size() int {
	return xxx_messageInfo_CycleBuyProductDetailData.Size(m)
}
func (m *CycleBuyProductDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyProductDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyProductDetailData proto.InternalMessageInfo

func (m *CycleBuyProductDetailData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CycleBuyProductDetailData) GetSinglePrice() int32 {
	if m != nil {
		return m.SinglePrice
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetThreePrice() int32 {
	if m != nil {
		return m.ThreePrice
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetSixPrice() int32 {
	if m != nil {
		return m.SixPrice
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetTwelvePrice() int32 {
	if m != nil {
		return m.TwelvePrice
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *CycleBuyProductDetailData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *CycleBuyProductDetailData) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *CycleBuyProductDetailData) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *CycleBuyProductDetailData) GetCycleIds() string {
	if m != nil {
		return m.CycleIds
	}
	return ""
}

func (m *CycleBuyProductDetailData) GetIsShippingFree() int32 {
	if m != nil {
		return m.IsShippingFree
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *CycleBuyProductDetailData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

//周期购商品数据
type CycleBuyProductData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//所属活动id
	Cid int32 `protobuf:"varint,2,opt,name=cid,proto3" json:"cid"`
	//渠道ID
	ChannelId int32 `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	//商品sku id
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	//商品名称
	ProductName string `protobuf:"bytes,6,opt,name=productName,proto3" json:"productName"`
	//单买价 单位分
	SinglePrice int32 `protobuf:"varint,7,opt,name=singlePrice,proto3" json:"singlePrice"`
	//3期价 单位分
	ThreePrice int32 `protobuf:"varint,8,opt,name=threePrice,proto3" json:"threePrice"`
	//6期价 单位分
	SixPrice int32 `protobuf:"varint,9,opt,name=sixPrice,proto3" json:"sixPrice"`
	//12期价 单位分
	TwelvePrice int32 `protobuf:"varint,10,opt,name=twelvePrice,proto3" json:"twelvePrice"`
	//状态 -1删除 0默认
	Status int32 `protobuf:"varint,11,opt,name=status,proto3" json:"status"`
	// 是否可被编辑 0 不可编辑 1 可编辑 用于boss后台
	CanBeEdited int32 `protobuf:"varint,12,opt,name=canBeEdited,proto3" json:"canBeEdited"`
	// 是否可被删除 0 不可删除 1 可删除 用于boss后台
	CanBeDeleted int32 `protobuf:"varint,13,opt,name=canBeDeleted,proto3" json:"canBeDeleted"`
	// 商品图片
	Pic string `protobuf:"bytes,14,opt,name=pic,proto3" json:"pic"`
	// 商品库存
	Stock int32 `protobuf:"varint,15,opt,name=stock,proto3" json:"stock"`
	// 是否是虚拟商品 1是 0 否
	IsVirtual int32 `protobuf:"varint,16,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,17,opt,name=goodsType,proto3" json:"goodsType"`
	//创建时间
	CreateTime string `protobuf:"bytes,18,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime string `protobuf:"bytes,19,opt,name=updateTime,proto3" json:"updateTime"`
	//商品3期折扣率
	ThreePriceRatio float64 `protobuf:"fixed64,34,opt,name=threePriceRatio,proto3" json:"threePriceRatio"`
	//商品6期折扣率
	SixPriceRatio float64 `protobuf:"fixed64,35,opt,name=sixPriceRatio,proto3" json:"sixPriceRatio"`
	//商品12期折扣率
	TwelvePriceRatio float64 `protobuf:"fixed64,36,opt,name=twelvePriceRatio,proto3" json:"twelvePriceRatio"`
	// 单价是否异常 1:异常 0：正常
	IsNormal int32 `protobuf:"varint,37,opt,name=isNormal,proto3" json:"isNormal"`
	// 3期是否异常 1:异常 0：正常
	IsNormalThree int32 `protobuf:"varint,38,opt,name=isNormalThree,proto3" json:"isNormalThree"`
	// 6期是否异常 1:异常 0：正常
	IsNormalSix int32 `protobuf:"varint,39,opt,name=isNormalSix,proto3" json:"isNormalSix"`
	// 12期是否异常 1:异常 0：正常
	IsNormalTwelve int32 `protobuf:"varint,40,opt,name=isNormalTwelve,proto3" json:"isNormalTwelve"`
	//R1集采价
	R1PurchasePrice int32 `protobuf:"varint,41,opt,name=R1PurchasePrice,proto3" json:"R1PurchasePrice"`
	// 是否标记 1:是 0：否
	IsMark int32 `protobuf:"varint,42,opt,name=is_mark,json=isMark,proto3" json:"is_mark"`
	// 标记为正常的异常折扣
	MarkDiscount float64 `protobuf:"fixed64,43,opt,name=MarkDiscount,proto3" json:"MarkDiscount"`
	// 标记为正常的采购价(分)
	MarkPurchasePrice    int32    `protobuf:"varint,44,opt,name=MarkPurchasePrice,proto3" json:"MarkPurchasePrice"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyProductData) Reset()         { *m = CycleBuyProductData{} }
func (m *CycleBuyProductData) String() string { return proto.CompactTextString(m) }
func (*CycleBuyProductData) ProtoMessage()    {}
func (*CycleBuyProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{17}
}

func (m *CycleBuyProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyProductData.Unmarshal(m, b)
}
func (m *CycleBuyProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyProductData.Marshal(b, m, deterministic)
}
func (m *CycleBuyProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyProductData.Merge(m, src)
}
func (m *CycleBuyProductData) XXX_Size() int {
	return xxx_messageInfo_CycleBuyProductData.Size(m)
}
func (m *CycleBuyProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyProductData.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyProductData proto.InternalMessageInfo

func (m *CycleBuyProductData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CycleBuyProductData) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CycleBuyProductData) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CycleBuyProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CycleBuyProductData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CycleBuyProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CycleBuyProductData) GetSinglePrice() int32 {
	if m != nil {
		return m.SinglePrice
	}
	return 0
}

func (m *CycleBuyProductData) GetThreePrice() int32 {
	if m != nil {
		return m.ThreePrice
	}
	return 0
}

func (m *CycleBuyProductData) GetSixPrice() int32 {
	if m != nil {
		return m.SixPrice
	}
	return 0
}

func (m *CycleBuyProductData) GetTwelvePrice() int32 {
	if m != nil {
		return m.TwelvePrice
	}
	return 0
}

func (m *CycleBuyProductData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CycleBuyProductData) GetCanBeEdited() int32 {
	if m != nil {
		return m.CanBeEdited
	}
	return 0
}

func (m *CycleBuyProductData) GetCanBeDeleted() int32 {
	if m != nil {
		return m.CanBeDeleted
	}
	return 0
}

func (m *CycleBuyProductData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *CycleBuyProductData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *CycleBuyProductData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *CycleBuyProductData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *CycleBuyProductData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *CycleBuyProductData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *CycleBuyProductData) GetThreePriceRatio() float64 {
	if m != nil {
		return m.ThreePriceRatio
	}
	return 0
}

func (m *CycleBuyProductData) GetSixPriceRatio() float64 {
	if m != nil {
		return m.SixPriceRatio
	}
	return 0
}

func (m *CycleBuyProductData) GetTwelvePriceRatio() float64 {
	if m != nil {
		return m.TwelvePriceRatio
	}
	return 0
}

func (m *CycleBuyProductData) GetIsNormal() int32 {
	if m != nil {
		return m.IsNormal
	}
	return 0
}

func (m *CycleBuyProductData) GetIsNormalThree() int32 {
	if m != nil {
		return m.IsNormalThree
	}
	return 0
}

func (m *CycleBuyProductData) GetIsNormalSix() int32 {
	if m != nil {
		return m.IsNormalSix
	}
	return 0
}

func (m *CycleBuyProductData) GetIsNormalTwelve() int32 {
	if m != nil {
		return m.IsNormalTwelve
	}
	return 0
}

func (m *CycleBuyProductData) GetR1PurchasePrice() int32 {
	if m != nil {
		return m.R1PurchasePrice
	}
	return 0
}

func (m *CycleBuyProductData) GetIsMark() int32 {
	if m != nil {
		return m.IsMark
	}
	return 0
}

func (m *CycleBuyProductData) GetMarkDiscount() float64 {
	if m != nil {
		return m.MarkDiscount
	}
	return 0
}

func (m *CycleBuyProductData) GetMarkPurchasePrice() int32 {
	if m != nil {
		return m.MarkPurchasePrice
	}
	return 0
}

//添加/编辑周期购商品
type SaveCycleBuyProductData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//所属活动id
	Cid int32 `protobuf:"varint,2,opt,name=cid,proto3" json:"cid"`
	//商品名称
	ProductName string `protobuf:"bytes,6,opt,name=productName,proto3" json:"productName"`
	//单买价 单位分
	SinglePrice int32 `protobuf:"varint,7,opt,name=singlePrice,proto3" json:"singlePrice"`
	//3期价 单位分
	ThreePrice int32 `protobuf:"varint,8,opt,name=threePrice,proto3" json:"threePrice"`
	//6期价 单位分
	SixPrice int32 `protobuf:"varint,9,opt,name=sixPrice,proto3" json:"sixPrice"`
	//12期价 单位分
	TwelvePrice          int32    `protobuf:"varint,10,opt,name=twelvePrice,proto3" json:"twelvePrice"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveCycleBuyProductData) Reset()         { *m = SaveCycleBuyProductData{} }
func (m *SaveCycleBuyProductData) String() string { return proto.CompactTextString(m) }
func (*SaveCycleBuyProductData) ProtoMessage()    {}
func (*SaveCycleBuyProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{18}
}

func (m *SaveCycleBuyProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveCycleBuyProductData.Unmarshal(m, b)
}
func (m *SaveCycleBuyProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveCycleBuyProductData.Marshal(b, m, deterministic)
}
func (m *SaveCycleBuyProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveCycleBuyProductData.Merge(m, src)
}
func (m *SaveCycleBuyProductData) XXX_Size() int {
	return xxx_messageInfo_SaveCycleBuyProductData.Size(m)
}
func (m *SaveCycleBuyProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveCycleBuyProductData.DiscardUnknown(m)
}

var xxx_messageInfo_SaveCycleBuyProductData proto.InternalMessageInfo

func (m *SaveCycleBuyProductData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SaveCycleBuyProductData) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SaveCycleBuyProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *SaveCycleBuyProductData) GetSinglePrice() int32 {
	if m != nil {
		return m.SinglePrice
	}
	return 0
}

func (m *SaveCycleBuyProductData) GetThreePrice() int32 {
	if m != nil {
		return m.ThreePrice
	}
	return 0
}

func (m *SaveCycleBuyProductData) GetSixPrice() int32 {
	if m != nil {
		return m.SixPrice
	}
	return 0
}

func (m *SaveCycleBuyProductData) GetTwelvePrice() int32 {
	if m != nil {
		return m.TwelvePrice
	}
	return 0
}

//阿闻电商参加周期购活动的商品
type GetCycleBuyUPetProductSelectListRequest struct {
	//活动id
	Cid int32 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid"`
	//商品sku_id 对应商城的goods_id
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	// 商品的产品id 对应商城的goods_commonid
	ProductId int32 `protobuf:"varint,3,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	ProductName string `protobuf:"bytes,4,opt,name=productName,proto3" json:"productName"`
	//分页参数
	Pagination *PaginationParam `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCycleBuyUPetProductSelectListRequest) Reset() {
	*m = GetCycleBuyUPetProductSelectListRequest{}
}
func (m *GetCycleBuyUPetProductSelectListRequest) String() string { return proto.CompactTextString(m) }
func (*GetCycleBuyUPetProductSelectListRequest) ProtoMessage()    {}
func (*GetCycleBuyUPetProductSelectListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{19}
}

func (m *GetCycleBuyUPetProductSelectListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCycleBuyUPetProductSelectListRequest.Unmarshal(m, b)
}
func (m *GetCycleBuyUPetProductSelectListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCycleBuyUPetProductSelectListRequest.Marshal(b, m, deterministic)
}
func (m *GetCycleBuyUPetProductSelectListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCycleBuyUPetProductSelectListRequest.Merge(m, src)
}
func (m *GetCycleBuyUPetProductSelectListRequest) XXX_Size() int {
	return xxx_messageInfo_GetCycleBuyUPetProductSelectListRequest.Size(m)
}
func (m *GetCycleBuyUPetProductSelectListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCycleBuyUPetProductSelectListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCycleBuyUPetProductSelectListRequest proto.InternalMessageInfo

func (m *GetCycleBuyUPetProductSelectListRequest) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetCycleBuyUPetProductSelectListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetCycleBuyUPetProductSelectListRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetCycleBuyUPetProductSelectListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetCycleBuyUPetProductSelectListRequest) GetPagination() *PaginationParam {
	if m != nil {
		return m.Pagination
	}
	return nil
}

func (m *GetCycleBuyUPetProductSelectListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//阿闻电商参加周期购活动的商品
type GetCycleBuyUPetProductSelectListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//周期购商品信息
	Data                 []*CycleBuySelectUPetProductData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetCycleBuyUPetProductSelectListResponse) Reset() {
	*m = GetCycleBuyUPetProductSelectListResponse{}
}
func (m *GetCycleBuyUPetProductSelectListResponse) String() string { return proto.CompactTextString(m) }
func (*GetCycleBuyUPetProductSelectListResponse) ProtoMessage()    {}
func (*GetCycleBuyUPetProductSelectListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{20}
}

func (m *GetCycleBuyUPetProductSelectListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCycleBuyUPetProductSelectListResponse.Unmarshal(m, b)
}
func (m *GetCycleBuyUPetProductSelectListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCycleBuyUPetProductSelectListResponse.Marshal(b, m, deterministic)
}
func (m *GetCycleBuyUPetProductSelectListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCycleBuyUPetProductSelectListResponse.Merge(m, src)
}
func (m *GetCycleBuyUPetProductSelectListResponse) XXX_Size() int {
	return xxx_messageInfo_GetCycleBuyUPetProductSelectListResponse.Size(m)
}
func (m *GetCycleBuyUPetProductSelectListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCycleBuyUPetProductSelectListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCycleBuyUPetProductSelectListResponse proto.InternalMessageInfo

func (m *GetCycleBuyUPetProductSelectListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCycleBuyUPetProductSelectListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetCycleBuyUPetProductSelectListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetCycleBuyUPetProductSelectListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetCycleBuyUPetProductSelectListResponse) GetData() []*CycleBuySelectUPetProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CycleBuySelectUPetProductData struct {
	//商品sku_id 对应商城的goods_id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	// 商品的产品id 对应商城的goods_commonid
	ProductId int32 `protobuf:"varint,2,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	ProductName string `protobuf:"bytes,3,opt,name=productName,proto3" json:"productName"`
	// 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
	TimeConflict int32 `protobuf:"varint,4,opt,name=timeConflict,proto3" json:"timeConflict"`
	//商品图片
	Pic string `protobuf:"bytes,5,opt,name=pic,proto3" json:"pic"`
	//库存
	Stock int32 `protobuf:"varint,6,opt,name=stock,proto3" json:"stock"`
	//价格 单位分
	MarketPrice int32 `protobuf:"varint,7,opt,name=marketPrice,proto3" json:"marketPrice"`
	//是否时虚拟产品 1是 0 否
	IsVirtual int32 `protobuf:"varint,8,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,9,opt,name=goodsType,proto3" json:"goodsType"`
	//组合商品的子商品信息
	ChildSkuIds          []*Child `protobuf:"bytes,10,rep,name=childSkuIds,proto3" json:"childSkuIds"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuySelectUPetProductData) Reset()         { *m = CycleBuySelectUPetProductData{} }
func (m *CycleBuySelectUPetProductData) String() string { return proto.CompactTextString(m) }
func (*CycleBuySelectUPetProductData) ProtoMessage()    {}
func (*CycleBuySelectUPetProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{21}
}

func (m *CycleBuySelectUPetProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuySelectUPetProductData.Unmarshal(m, b)
}
func (m *CycleBuySelectUPetProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuySelectUPetProductData.Marshal(b, m, deterministic)
}
func (m *CycleBuySelectUPetProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuySelectUPetProductData.Merge(m, src)
}
func (m *CycleBuySelectUPetProductData) XXX_Size() int {
	return xxx_messageInfo_CycleBuySelectUPetProductData.Size(m)
}
func (m *CycleBuySelectUPetProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuySelectUPetProductData.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuySelectUPetProductData proto.InternalMessageInfo

func (m *CycleBuySelectUPetProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CycleBuySelectUPetProductData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CycleBuySelectUPetProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CycleBuySelectUPetProductData) GetTimeConflict() int32 {
	if m != nil {
		return m.TimeConflict
	}
	return 0
}

func (m *CycleBuySelectUPetProductData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *CycleBuySelectUPetProductData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *CycleBuySelectUPetProductData) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *CycleBuySelectUPetProductData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *CycleBuySelectUPetProductData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *CycleBuySelectUPetProductData) GetChildSkuIds() []*Child {
	if m != nil {
		return m.ChildSkuIds
	}
	return nil
}

//组合商品子商品讯息
type Child struct {
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//规则
	RuleNum int32 `protobuf:"varint,2,opt,name=ruleNum,proto3" json:"ruleNum"`
	//是否为虚拟 0:不是 1：是虚拟
	IsVirtual int32 `protobuf:"varint,3,opt,name=isVirtual,proto3" json:"isVirtual"`
	//0不是药品仓 1药品仓
	StockWarehouse       int32    `protobuf:"varint,7,opt,name=stockWarehouse,proto3" json:"stockWarehouse"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Child) Reset()         { *m = Child{} }
func (m *Child) String() string { return proto.CompactTextString(m) }
func (*Child) ProtoMessage()    {}
func (*Child) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{22}
}

func (m *Child) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Child.Unmarshal(m, b)
}
func (m *Child) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Child.Marshal(b, m, deterministic)
}
func (m *Child) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Child.Merge(m, src)
}
func (m *Child) XXX_Size() int {
	return xxx_messageInfo_Child.Size(m)
}
func (m *Child) XXX_DiscardUnknown() {
	xxx_messageInfo_Child.DiscardUnknown(m)
}

var xxx_messageInfo_Child proto.InternalMessageInfo

func (m *Child) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *Child) GetRuleNum() int32 {
	if m != nil {
		return m.RuleNum
	}
	return 0
}

func (m *Child) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *Child) GetStockWarehouse() int32 {
	if m != nil {
		return m.StockWarehouse
	}
	return 0
}

//周期购活动 商品列表请求数据 product-api用
type CycleBuyProductListRequest struct {
	// 页码
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页显示条数
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 渠道ID
	ChannelId int32 `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//活动id 多个用逗号隔开
	Cids string `protobuf:"bytes,4,opt,name=cids,proto3" json:"cids"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyProductListRequest) Reset()         { *m = CycleBuyProductListRequest{} }
func (m *CycleBuyProductListRequest) String() string { return proto.CompactTextString(m) }
func (*CycleBuyProductListRequest) ProtoMessage()    {}
func (*CycleBuyProductListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{23}
}

func (m *CycleBuyProductListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyProductListRequest.Unmarshal(m, b)
}
func (m *CycleBuyProductListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyProductListRequest.Marshal(b, m, deterministic)
}
func (m *CycleBuyProductListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyProductListRequest.Merge(m, src)
}
func (m *CycleBuyProductListRequest) XXX_Size() int {
	return xxx_messageInfo_CycleBuyProductListRequest.Size(m)
}
func (m *CycleBuyProductListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyProductListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyProductListRequest proto.InternalMessageInfo

func (m *CycleBuyProductListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CycleBuyProductListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CycleBuyProductListRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CycleBuyProductListRequest) GetCids() string {
	if m != nil {
		return m.Cids
	}
	return ""
}

func (m *CycleBuyProductListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//周期购活动 商品列表响应数据 product-api用
type CycleBuyProductListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total                int32              `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	Data                 []*CycleBuyProduct `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CycleBuyProductListResponse) Reset()         { *m = CycleBuyProductListResponse{} }
func (m *CycleBuyProductListResponse) String() string { return proto.CompactTextString(m) }
func (*CycleBuyProductListResponse) ProtoMessage()    {}
func (*CycleBuyProductListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{24}
}

func (m *CycleBuyProductListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyProductListResponse.Unmarshal(m, b)
}
func (m *CycleBuyProductListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyProductListResponse.Marshal(b, m, deterministic)
}
func (m *CycleBuyProductListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyProductListResponse.Merge(m, src)
}
func (m *CycleBuyProductListResponse) XXX_Size() int {
	return xxx_messageInfo_CycleBuyProductListResponse.Size(m)
}
func (m *CycleBuyProductListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyProductListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyProductListResponse proto.InternalMessageInfo

func (m *CycleBuyProductListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CycleBuyProductListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CycleBuyProductListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CycleBuyProductListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *CycleBuyProductListResponse) GetData() []*CycleBuyProduct {
	if m != nil {
		return m.Data
	}
	return nil
}

//周期购活动商品信息
type CycleBuyProduct struct {
	//活动id
	Cid int32 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid"`
	//渠道id
	ChannelId int32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//商品skuID
	SkuId int32 `protobuf:"varint,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//产品id
	ProductId int32 `protobuf:"varint,4,opt,name=product_id,json=productId,proto3" json:"product_id"`
	//产品名称
	ProductName string `protobuf:"bytes,5,opt,name=product_name,json=productName,proto3" json:"product_name"`
	//商品图片
	ProductImg string `protobuf:"bytes,6,opt,name=product_img,json=productImg,proto3" json:"product_img"`
	// 单买价 单位分
	SinglePrice int32 `protobuf:"varint,7,opt,name=SinglePrice,proto3" json:"SinglePrice"`
	//3期价 单位分
	ThreePrice int32 `protobuf:"varint,8,opt,name=ThreePrice,proto3" json:"ThreePrice"`
	//6期价 单位分
	SixPrice int32 `protobuf:"varint,9,opt,name=SixPrice,proto3" json:"SixPrice"`
	//12期价 单位分
	TwelvePrice int32 `protobuf:"varint,10,opt,name=TwelvePrice,proto3" json:"TwelvePrice"`
	//R1集采价 单位分
	R1PurchasePrice int32 `protobuf:"varint,11,opt,name=R1PurchasePrice,proto3" json:"R1PurchasePrice"`
	//商品折扣率
	PriceRatio           float64  `protobuf:"fixed64,12,opt,name=priceRatio,proto3" json:"priceRatio"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyProduct) Reset()         { *m = CycleBuyProduct{} }
func (m *CycleBuyProduct) String() string { return proto.CompactTextString(m) }
func (*CycleBuyProduct) ProtoMessage()    {}
func (*CycleBuyProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{25}
}

func (m *CycleBuyProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyProduct.Unmarshal(m, b)
}
func (m *CycleBuyProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyProduct.Marshal(b, m, deterministic)
}
func (m *CycleBuyProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyProduct.Merge(m, src)
}
func (m *CycleBuyProduct) XXX_Size() int {
	return xxx_messageInfo_CycleBuyProduct.Size(m)
}
func (m *CycleBuyProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyProduct.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyProduct proto.InternalMessageInfo

func (m *CycleBuyProduct) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CycleBuyProduct) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CycleBuyProduct) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CycleBuyProduct) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CycleBuyProduct) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CycleBuyProduct) GetProductImg() string {
	if m != nil {
		return m.ProductImg
	}
	return ""
}

func (m *CycleBuyProduct) GetSinglePrice() int32 {
	if m != nil {
		return m.SinglePrice
	}
	return 0
}

func (m *CycleBuyProduct) GetThreePrice() int32 {
	if m != nil {
		return m.ThreePrice
	}
	return 0
}

func (m *CycleBuyProduct) GetSixPrice() int32 {
	if m != nil {
		return m.SixPrice
	}
	return 0
}

func (m *CycleBuyProduct) GetTwelvePrice() int32 {
	if m != nil {
		return m.TwelvePrice
	}
	return 0
}

func (m *CycleBuyProduct) GetR1PurchasePrice() int32 {
	if m != nil {
		return m.R1PurchasePrice
	}
	return 0
}

func (m *CycleBuyProduct) GetPriceRatio() float64 {
	if m != nil {
		return m.PriceRatio
	}
	return 0
}

//收藏 周期购活动商品 请求参数
type AddCycleBuyCollectRequest struct {
	//商品skuID
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//用户scrm_id
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCycleBuyCollectRequest) Reset()         { *m = AddCycleBuyCollectRequest{} }
func (m *AddCycleBuyCollectRequest) String() string { return proto.CompactTextString(m) }
func (*AddCycleBuyCollectRequest) ProtoMessage()    {}
func (*AddCycleBuyCollectRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{26}
}

func (m *AddCycleBuyCollectRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCycleBuyCollectRequest.Unmarshal(m, b)
}
func (m *AddCycleBuyCollectRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCycleBuyCollectRequest.Marshal(b, m, deterministic)
}
func (m *AddCycleBuyCollectRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCycleBuyCollectRequest.Merge(m, src)
}
func (m *AddCycleBuyCollectRequest) XXX_Size() int {
	return xxx_messageInfo_AddCycleBuyCollectRequest.Size(m)
}
func (m *AddCycleBuyCollectRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCycleBuyCollectRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddCycleBuyCollectRequest proto.InternalMessageInfo

func (m *AddCycleBuyCollectRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *AddCycleBuyCollectRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *AddCycleBuyCollectRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 周期购活动 收藏列表
type CycleBuyCollectListRequest struct {
	// 页码
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页显示条数
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 排序 创建时间倒叙
	OrderBy string `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	// 用户id
	UserId string `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyCollectListRequest) Reset()         { *m = CycleBuyCollectListRequest{} }
func (m *CycleBuyCollectListRequest) String() string { return proto.CompactTextString(m) }
func (*CycleBuyCollectListRequest) ProtoMessage()    {}
func (*CycleBuyCollectListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{27}
}

func (m *CycleBuyCollectListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyCollectListRequest.Unmarshal(m, b)
}
func (m *CycleBuyCollectListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyCollectListRequest.Marshal(b, m, deterministic)
}
func (m *CycleBuyCollectListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyCollectListRequest.Merge(m, src)
}
func (m *CycleBuyCollectListRequest) XXX_Size() int {
	return xxx_messageInfo_CycleBuyCollectListRequest.Size(m)
}
func (m *CycleBuyCollectListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyCollectListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyCollectListRequest proto.InternalMessageInfo

func (m *CycleBuyCollectListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CycleBuyCollectListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CycleBuyCollectListRequest) GetOrderBy() string {
	if m != nil {
		return m.OrderBy
	}
	return ""
}

func (m *CycleBuyCollectListRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CycleBuyCollectListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type CycleBuyCollectListResponse struct {
	Code                 int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string             `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Total                int32              `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	Data                 []*CycleBuyCollect `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CycleBuyCollectListResponse) Reset()         { *m = CycleBuyCollectListResponse{} }
func (m *CycleBuyCollectListResponse) String() string { return proto.CompactTextString(m) }
func (*CycleBuyCollectListResponse) ProtoMessage()    {}
func (*CycleBuyCollectListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{28}
}

func (m *CycleBuyCollectListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyCollectListResponse.Unmarshal(m, b)
}
func (m *CycleBuyCollectListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyCollectListResponse.Marshal(b, m, deterministic)
}
func (m *CycleBuyCollectListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyCollectListResponse.Merge(m, src)
}
func (m *CycleBuyCollectListResponse) XXX_Size() int {
	return xxx_messageInfo_CycleBuyCollectListResponse.Size(m)
}
func (m *CycleBuyCollectListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyCollectListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyCollectListResponse proto.InternalMessageInfo

func (m *CycleBuyCollectListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CycleBuyCollectListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CycleBuyCollectListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CycleBuyCollectListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *CycleBuyCollectListResponse) GetData() []*CycleBuyCollect {
	if m != nil {
		return m.Data
	}
	return nil
}

type CycleBuyCollectDetailResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//收藏状态：0未收藏，1已收藏
	Flag                 int32    `protobuf:"varint,4,opt,name=flag,proto3" json:"flag"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyCollectDetailResponse) Reset()         { *m = CycleBuyCollectDetailResponse{} }
func (m *CycleBuyCollectDetailResponse) String() string { return proto.CompactTextString(m) }
func (*CycleBuyCollectDetailResponse) ProtoMessage()    {}
func (*CycleBuyCollectDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{29}
}

func (m *CycleBuyCollectDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyCollectDetailResponse.Unmarshal(m, b)
}
func (m *CycleBuyCollectDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyCollectDetailResponse.Marshal(b, m, deterministic)
}
func (m *CycleBuyCollectDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyCollectDetailResponse.Merge(m, src)
}
func (m *CycleBuyCollectDetailResponse) XXX_Size() int {
	return xxx_messageInfo_CycleBuyCollectDetailResponse.Size(m)
}
func (m *CycleBuyCollectDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyCollectDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyCollectDetailResponse proto.InternalMessageInfo

func (m *CycleBuyCollectDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CycleBuyCollectDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CycleBuyCollectDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CycleBuyCollectDetailResponse) GetFlag() int32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

type CycleBuyCollect struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品skuID
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//用户scrm_id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//产品名称
	ProductName string `protobuf:"bytes,4,opt,name=product_name,json=productName,proto3" json:"product_name"`
	//商品图片
	ProductImg string `protobuf:"bytes,5,opt,name=product_img,json=productImg,proto3" json:"product_img"`
	// 价格 单位分
	Price int32 `protobuf:"varint,6,opt,name=price,proto3" json:"price"`
	//状态 0失效 1正常 2删除
	Status int32 `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	//创建时间
	CreateTime string `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 规格
	GoodsSpec string `protobuf:"bytes,9,opt,name=goods_spec,json=goodsSpec,proto3" json:"goods_spec"`
	// 类型
	GoodsType int32 `protobuf:"varint,10,opt,name=goods_type,json=goodsType,proto3" json:"goods_type"`
	// 是否是会员价
	IsMemberPrice int32 `protobuf:"varint,11,opt,name=is_member_price,json=isMemberPrice,proto3" json:"is_member_price"`
	// 会员价
	MemberPrice1 int32 `protobuf:"varint,12,opt,name=member_price1,json=memberPrice1,proto3" json:"member_price1"`
	// 促销类型：2-限时折扣, 3-秒杀,闪购 5-拼团 6-周期购 7-新人专享 8-预售 9-新秒杀
	GoodsPromotionType   int32    `protobuf:"varint,13,opt,name=goods_promotion_type,json=goodsPromotionType,proto3" json:"goods_promotion_type"`
	GoodsPromotionPrice  int32    `protobuf:"varint,14,opt,name=goods_promotion_price,json=goodsPromotionPrice,proto3" json:"goods_promotion_price"`
	NewPeoplePrice       int32    `protobuf:"varint,15,opt,name=new_people_price,json=newPeoplePrice,proto3" json:"new_people_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyCollect) Reset()         { *m = CycleBuyCollect{} }
func (m *CycleBuyCollect) String() string { return proto.CompactTextString(m) }
func (*CycleBuyCollect) ProtoMessage()    {}
func (*CycleBuyCollect) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{30}
}

func (m *CycleBuyCollect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyCollect.Unmarshal(m, b)
}
func (m *CycleBuyCollect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyCollect.Marshal(b, m, deterministic)
}
func (m *CycleBuyCollect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyCollect.Merge(m, src)
}
func (m *CycleBuyCollect) XXX_Size() int {
	return xxx_messageInfo_CycleBuyCollect.Size(m)
}
func (m *CycleBuyCollect) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyCollect.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyCollect proto.InternalMessageInfo

func (m *CycleBuyCollect) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CycleBuyCollect) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CycleBuyCollect) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CycleBuyCollect) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *CycleBuyCollect) GetProductImg() string {
	if m != nil {
		return m.ProductImg
	}
	return ""
}

func (m *CycleBuyCollect) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *CycleBuyCollect) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CycleBuyCollect) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *CycleBuyCollect) GetGoodsSpec() string {
	if m != nil {
		return m.GoodsSpec
	}
	return ""
}

func (m *CycleBuyCollect) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *CycleBuyCollect) GetIsMemberPrice() int32 {
	if m != nil {
		return m.IsMemberPrice
	}
	return 0
}

func (m *CycleBuyCollect) GetMemberPrice1() int32 {
	if m != nil {
		return m.MemberPrice1
	}
	return 0
}

func (m *CycleBuyCollect) GetGoodsPromotionType() int32 {
	if m != nil {
		return m.GoodsPromotionType
	}
	return 0
}

func (m *CycleBuyCollect) GetGoodsPromotionPrice() int32 {
	if m != nil {
		return m.GoodsPromotionPrice
	}
	return 0
}

func (m *CycleBuyCollect) GetNewPeoplePrice() int32 {
	if m != nil {
		return m.NewPeoplePrice
	}
	return 0
}

// 周期购活动商品  取消收藏
type DelCycleBuyCollectRequest struct {
	SkuId                int32    `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	UserId               string   `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCycleBuyCollectRequest) Reset()         { *m = DelCycleBuyCollectRequest{} }
func (m *DelCycleBuyCollectRequest) String() string { return proto.CompactTextString(m) }
func (*DelCycleBuyCollectRequest) ProtoMessage()    {}
func (*DelCycleBuyCollectRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{31}
}

func (m *DelCycleBuyCollectRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCycleBuyCollectRequest.Unmarshal(m, b)
}
func (m *DelCycleBuyCollectRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCycleBuyCollectRequest.Marshal(b, m, deterministic)
}
func (m *DelCycleBuyCollectRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCycleBuyCollectRequest.Merge(m, src)
}
func (m *DelCycleBuyCollectRequest) XXX_Size() int {
	return xxx_messageInfo_DelCycleBuyCollectRequest.Size(m)
}
func (m *DelCycleBuyCollectRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCycleBuyCollectRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelCycleBuyCollectRequest proto.InternalMessageInfo

func (m *DelCycleBuyCollectRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *DelCycleBuyCollectRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *DelCycleBuyCollectRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 获取周期购订单列表请求参数
type CycleOrderListRequest struct {
	PageIndex            int32    `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	UserId               string   `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	OpenId               string   `protobuf:"bytes,4,opt,name=open_id,json=openId,proto3" json:"open_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleOrderListRequest) Reset()         { *m = CycleOrderListRequest{} }
func (m *CycleOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*CycleOrderListRequest) ProtoMessage()    {}
func (*CycleOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{32}
}

func (m *CycleOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleOrderListRequest.Unmarshal(m, b)
}
func (m *CycleOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleOrderListRequest.Marshal(b, m, deterministic)
}
func (m *CycleOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleOrderListRequest.Merge(m, src)
}
func (m *CycleOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_CycleOrderListRequest.Size(m)
}
func (m *CycleOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleOrderListRequest proto.InternalMessageInfo

func (m *CycleOrderListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CycleOrderListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CycleOrderListRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CycleOrderListRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

// 获取周期购订单列表 响应数据
type CycleOrderListResponse struct {
	Code                 int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string            `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string            `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 []*CycleOrderInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CycleOrderListResponse) Reset()         { *m = CycleOrderListResponse{} }
func (m *CycleOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*CycleOrderListResponse) ProtoMessage()    {}
func (*CycleOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{33}
}

func (m *CycleOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleOrderListResponse.Unmarshal(m, b)
}
func (m *CycleOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleOrderListResponse.Marshal(b, m, deterministic)
}
func (m *CycleOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleOrderListResponse.Merge(m, src)
}
func (m *CycleOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_CycleOrderListResponse.Size(m)
}
func (m *CycleOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CycleOrderListResponse proto.InternalMessageInfo

func (m *CycleOrderListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CycleOrderListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CycleOrderListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CycleOrderListResponse) GetData() []*CycleOrderInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

// 我的周期购 订单列表项 数据
type CycleOrderInfo struct {
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//商品名称
	GoodsName string `protobuf:"bytes,2,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//商品图片
	GoodsImage string `protobuf:"bytes,3,opt,name=goods_image,json=goodsImage,proto3" json:"goods_image"`
	//商品单价 单位分
	GoodsPrice int32 `protobuf:"varint,4,opt,name=goods_price,json=goodsPrice,proto3" json:"goods_price"`
	//商品数量
	GoodsNum int32 `protobuf:"varint,5,opt,name=goods_num,json=goodsNum,proto3" json:"goods_num"`
	//订单状态
	OrderState int32 `protobuf:"varint,6,opt,name=order_state,json=orderState,proto3" json:"order_state"`
	//订单状态描述
	OrderStateDesc string `protobuf:"bytes,7,opt,name=order_state_desc,json=orderStateDesc,proto3" json:"order_state_desc"`
	//订单生成时间
	OrderAddTime int64 `protobuf:"varint,8,opt,name=order_add_time,json=orderAddTime,proto3" json:"order_add_time"`
	//周期购期数
	CycleNum int32 `protobuf:"varint,9,opt,name=cycle_num,json=cycleNum,proto3" json:"cycle_num"`
	//erp订单号
	ErpOrderSn string `protobuf:"bytes,10,opt,name=erp_order_sn,json=erpOrderSn,proto3" json:"erp_order_sn"`
	//支付单号
	PaySn string `protobuf:"bytes,11,opt,name=pay_sn,json=paySn,proto3" json:"pay_sn"`
	//未支付订单 支付倒计时
	OrderEndPayDatetime string `protobuf:"bytes,12,opt,name=order_end_pay_datetime,json=orderEndPayDatetime,proto3" json:"order_end_pay_datetime"`
	//服务器时间
	ServerDateTime string `protobuf:"bytes,13,opt,name=server_date_time,json=serverDateTime,proto3" json:"server_date_time"`
	//商品规格
	GoodsSpec string `protobuf:"bytes,14,opt,name=goods_spec,json=goodsSpec,proto3" json:"goods_spec"`
	//订单id
	OrderId int64 `protobuf:"varint,15,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 订单实付金额
	OrderAmount int64 `protobuf:"varint,16,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount"`
	// 周期购价格，单位分 该订单买的是几期购， 就对应几期购的价格
	CyclePrice int32 `protobuf:"varint,17,opt,name=cycle_price,json=cyclePrice,proto3" json:"cycle_price"`
	// 主订单编号
	ParentOrderSn        string   `protobuf:"bytes,18,opt,name=parent_order_sn,json=parentOrderSn,proto3" json:"parent_order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleOrderInfo) Reset()         { *m = CycleOrderInfo{} }
func (m *CycleOrderInfo) String() string { return proto.CompactTextString(m) }
func (*CycleOrderInfo) ProtoMessage()    {}
func (*CycleOrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{34}
}

func (m *CycleOrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleOrderInfo.Unmarshal(m, b)
}
func (m *CycleOrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleOrderInfo.Marshal(b, m, deterministic)
}
func (m *CycleOrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleOrderInfo.Merge(m, src)
}
func (m *CycleOrderInfo) XXX_Size() int {
	return xxx_messageInfo_CycleOrderInfo.Size(m)
}
func (m *CycleOrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleOrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CycleOrderInfo proto.InternalMessageInfo

func (m *CycleOrderInfo) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CycleOrderInfo) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *CycleOrderInfo) GetGoodsImage() string {
	if m != nil {
		return m.GoodsImage
	}
	return ""
}

func (m *CycleOrderInfo) GetGoodsPrice() int32 {
	if m != nil {
		return m.GoodsPrice
	}
	return 0
}

func (m *CycleOrderInfo) GetGoodsNum() int32 {
	if m != nil {
		return m.GoodsNum
	}
	return 0
}

func (m *CycleOrderInfo) GetOrderState() int32 {
	if m != nil {
		return m.OrderState
	}
	return 0
}

func (m *CycleOrderInfo) GetOrderStateDesc() string {
	if m != nil {
		return m.OrderStateDesc
	}
	return ""
}

func (m *CycleOrderInfo) GetOrderAddTime() int64 {
	if m != nil {
		return m.OrderAddTime
	}
	return 0
}

func (m *CycleOrderInfo) GetCycleNum() int32 {
	if m != nil {
		return m.CycleNum
	}
	return 0
}

func (m *CycleOrderInfo) GetErpOrderSn() string {
	if m != nil {
		return m.ErpOrderSn
	}
	return ""
}

func (m *CycleOrderInfo) GetPaySn() string {
	if m != nil {
		return m.PaySn
	}
	return ""
}

func (m *CycleOrderInfo) GetOrderEndPayDatetime() string {
	if m != nil {
		return m.OrderEndPayDatetime
	}
	return ""
}

func (m *CycleOrderInfo) GetServerDateTime() string {
	if m != nil {
		return m.ServerDateTime
	}
	return ""
}

func (m *CycleOrderInfo) GetGoodsSpec() string {
	if m != nil {
		return m.GoodsSpec
	}
	return ""
}

func (m *CycleOrderInfo) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *CycleOrderInfo) GetOrderAmount() int64 {
	if m != nil {
		return m.OrderAmount
	}
	return 0
}

func (m *CycleOrderInfo) GetCyclePrice() int32 {
	if m != nil {
		return m.CyclePrice
	}
	return 0
}

func (m *CycleOrderInfo) GetParentOrderSn() string {
	if m != nil {
		return m.ParentOrderSn
	}
	return ""
}

//周期购订单回调
type CycleBuyOrderStaticRequest struct {
	// required 回调场景 1 创建订单
	SyncType int32 `protobuf:"varint,1,opt,name=syncType,proto3" json:"syncType"`
	//周期购活动的产品记录id 关联dc.activity中的group_buy_product表
	ProductRecordId int32 `protobuf:"varint,2,opt,name=productRecordId,proto3" json:"productRecordId"`
	// required 周期购id
	Cid int32 `protobuf:"varint,3,opt,name=cid,proto3" json:"cid"`
	// required skuId
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//  商品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	//required 渠道id 1:阿闻本地 5阿闻电商 当前只有电商渠道
	ChannelId            int32    `protobuf:"varint,6,opt,name=channelId,proto3" json:"channelId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CycleBuyOrderStaticRequest) Reset()         { *m = CycleBuyOrderStaticRequest{} }
func (m *CycleBuyOrderStaticRequest) String() string { return proto.CompactTextString(m) }
func (*CycleBuyOrderStaticRequest) ProtoMessage()    {}
func (*CycleBuyOrderStaticRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e323e944545c78f2, []int{35}
}

func (m *CycleBuyOrderStaticRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CycleBuyOrderStaticRequest.Unmarshal(m, b)
}
func (m *CycleBuyOrderStaticRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CycleBuyOrderStaticRequest.Marshal(b, m, deterministic)
}
func (m *CycleBuyOrderStaticRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CycleBuyOrderStaticRequest.Merge(m, src)
}
func (m *CycleBuyOrderStaticRequest) XXX_Size() int {
	return xxx_messageInfo_CycleBuyOrderStaticRequest.Size(m)
}
func (m *CycleBuyOrderStaticRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CycleBuyOrderStaticRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CycleBuyOrderStaticRequest proto.InternalMessageInfo

func (m *CycleBuyOrderStaticRequest) GetSyncType() int32 {
	if m != nil {
		return m.SyncType
	}
	return 0
}

func (m *CycleBuyOrderStaticRequest) GetProductRecordId() int32 {
	if m != nil {
		return m.ProductRecordId
	}
	return 0
}

func (m *CycleBuyOrderStaticRequest) GetCid() int32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CycleBuyOrderStaticRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CycleBuyOrderStaticRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CycleBuyOrderStaticRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func init() {
	proto.RegisterType((*CycleBuyListRequest)(nil), "ac.CycleBuyListRequest")
	proto.RegisterType((*CycleBuyRequest)(nil), "ac.CycleBuyRequest")
	proto.RegisterType((*CycleBuyListResponse)(nil), "ac.CycleBuyListResponse")
	proto.RegisterType((*CycleBuyIdRequest)(nil), "ac.CycleBuyIdRequest")
	proto.RegisterType((*CycleBuyDetailResponse)(nil), "ac.CycleBuyDetailResponse")
	proto.RegisterType((*CycleBuyUnshipStatisticsRequest)(nil), "ac.CycleBuyUnshipStatisticsRequest")
	proto.RegisterType((*CycleBuyUnshipStatisticsListResponse)(nil), "ac.CycleBuyUnshipStatisticsListResponse")
	proto.RegisterType((*CycleProductTotal)(nil), "ac.CycleProductTotal")
	proto.RegisterType((*GetCycleBuyProductListRequest)(nil), "ac.GetCycleBuyProductListRequest")
	proto.RegisterType((*GetCycleBuyProductListResponse)(nil), "ac.GetCycleBuyProductListResponse")
	proto.RegisterType((*CreateCycleBuyProductRequest)(nil), "ac.CreateCycleBuyProductRequest")
	proto.RegisterType((*UpdateCycleBuyProductRequest)(nil), "ac.UpdateCycleBuyProductRequest")
	proto.RegisterType((*CycleBuyProductDetailRequest)(nil), "ac.CycleBuyProductDetailRequest")
	proto.RegisterType((*GetCycleBuyProductDetailResponse)(nil), "ac.GetCycleBuyProductDetailResponse")
	proto.RegisterType((*CycleBuyProductIdRequest)(nil), "ac.CycleBuyProductIdRequest")
	proto.RegisterType((*PaginationParam)(nil), "ac.PaginationParam")
	proto.RegisterType((*CycleBuyProductDetailData)(nil), "ac.CycleBuyProductDetailData")
	proto.RegisterType((*CycleBuyProductData)(nil), "ac.CycleBuyProductData")
	proto.RegisterType((*SaveCycleBuyProductData)(nil), "ac.SaveCycleBuyProductData")
	proto.RegisterType((*GetCycleBuyUPetProductSelectListRequest)(nil), "ac.GetCycleBuyUPetProductSelectListRequest")
	proto.RegisterType((*GetCycleBuyUPetProductSelectListResponse)(nil), "ac.GetCycleBuyUPetProductSelectListResponse")
	proto.RegisterType((*CycleBuySelectUPetProductData)(nil), "ac.CycleBuySelectUPetProductData")
	proto.RegisterType((*Child)(nil), "ac.Child")
	proto.RegisterType((*CycleBuyProductListRequest)(nil), "ac.CycleBuyProductListRequest")
	proto.RegisterType((*CycleBuyProductListResponse)(nil), "ac.CycleBuyProductListResponse")
	proto.RegisterType((*CycleBuyProduct)(nil), "ac.CycleBuyProduct")
	proto.RegisterType((*AddCycleBuyCollectRequest)(nil), "ac.AddCycleBuyCollectRequest")
	proto.RegisterType((*CycleBuyCollectListRequest)(nil), "ac.CycleBuyCollectListRequest")
	proto.RegisterType((*CycleBuyCollectListResponse)(nil), "ac.CycleBuyCollectListResponse")
	proto.RegisterType((*CycleBuyCollectDetailResponse)(nil), "ac.CycleBuyCollectDetailResponse")
	proto.RegisterType((*CycleBuyCollect)(nil), "ac.CycleBuyCollect")
	proto.RegisterType((*DelCycleBuyCollectRequest)(nil), "ac.DelCycleBuyCollectRequest")
	proto.RegisterType((*CycleOrderListRequest)(nil), "ac.CycleOrderListRequest")
	proto.RegisterType((*CycleOrderListResponse)(nil), "ac.CycleOrderListResponse")
	proto.RegisterType((*CycleOrderInfo)(nil), "ac.CycleOrderInfo")
	proto.RegisterType((*CycleBuyOrderStaticRequest)(nil), "ac.CycleBuyOrderStaticRequest")
}

func init() { proto.RegisterFile("ac/cycle_buy_service.proto", fileDescriptor_e323e944545c78f2) }

var fileDescriptor_e323e944545c78f2 = []byte{
	// 2753 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0xbd, 0x6f, 0xdc, 0xc8,
	0x15, 0xc7, 0x7e, 0xe9, 0xe3, 0xad, 0xbe, 0x4c, 0x59, 0x36, 0xbd, 0x96, 0x65, 0x79, 0xed, 0xc8,
	0xba, 0xb3, 0xe1, 0x8b, 0x6d, 0x04, 0xae, 0x6d, 0xc9, 0x77, 0xd8, 0x38, 0x67, 0x0b, 0xbb, 0xf2,
	0x05, 0x57, 0x11, 0x23, 0x72, 0xb4, 0x9a, 0x68, 0xf9, 0x11, 0x92, 0x2b, 0x7b, 0x0f, 0xb8, 0x2e,
	0x09, 0x90, 0xf6, 0xba, 0x4b, 0x15, 0x24, 0x55, 0x90, 0x2e, 0x4d, 0x8a, 0x54, 0x69, 0x52, 0x04,
	0x01, 0xf2, 0x17, 0xe4, 0x0f, 0x48, 0x99, 0x32, 0x55, 0x82, 0xf7, 0x66, 0x48, 0x0e, 0xb9, 0xe4,
	0xda, 0xe7, 0xc8, 0xb8, 0x14, 0x57, 0x2d, 0xe7, 0x37, 0x6f, 0x66, 0xde, 0xbc, 0xaf, 0x79, 0x6f,
	0x66, 0xa1, 0xc3, 0xec, 0x8f, 0xec, 0x89, 0x3d, 0xe2, 0xd6, 0xd1, 0x78, 0x62, 0x45, 0x3c, 0x3c,
	0x13, 0x36, 0xbf, 0x17, 0x84, 0x7e, 0xec, 0x1b, 0x75, 0x66, 0x77, 0x2e, 0x33, 0xfb, 0x23, 0x66,
	0xc7, 0xe2, 0x4c, 0xc4, 0x13, 0xcb, 0xf5, 0x1d, 0x3e, 0x92, 0x9d, 0xdd, 0x5f, 0xd5, 0x60, 0x7d,
	0x0f, 0x07, 0x3e, 0x19, 0x4f, 0x7e, 0x24, 0xa2, 0xb8, 0xcf, 0x7f, 0x3a, 0xe6, 0x51, 0x6c, 0x5c,
	0x82, 0xb9, 0x28, 0x66, 0xf1, 0x38, 0x32, 0x6b, 0xdb, 0xb5, 0xdd, 0x56, 0x5f, 0xb5, 0x8c, 0x8b,
	0xd0, 0x8a, 0x45, 0x3c, 0xe2, 0x66, 0x7d, 0xbb, 0xb6, 0xbb, 0xd8, 0x97, 0x0d, 0xe3, 0x1a, 0x40,
	0xc0, 0x86, 0xdc, 0x12, 0x9e, 0xc3, 0x5f, 0x9b, 0x0d, 0x1a, 0xb1, 0x88, 0x48, 0x0f, 0x01, 0xe3,
	0x2a, 0x50, 0xc3, 0x8a, 0xc4, 0x17, 0xdc, 0x6c, 0x52, 0xef, 0x02, 0x02, 0x03, 0xf1, 0x05, 0x37,
	0xae, 0xc0, 0x82, 0x1f, 0x3a, 0x3c, 0xb4, 0x8e, 0x26, 0xe6, 0x1c, 0x4d, 0x3a, 0x4f, 0xed, 0x27,
	0x93, 0xee, 0xbf, 0x1b, 0xb0, 0x9a, 0x30, 0x97, 0x30, 0xb6, 0x02, 0x75, 0xe1, 0x28, 0xa6, 0xea,
	0xc2, 0xa9, 0x66, 0xe8, 0x88, 0x0f, 0x85, 0x67, 0x39, 0x2c, 0xe6, 0xc4, 0xd0, 0x62, 0x7f, 0x91,
	0x90, 0x7d, 0x16, 0xd3, 0x9a, 0xdc, 0x73, 0x64, 0x67, 0x53, 0xae, 0xc9, 0x3d, 0x87, 0xba, 0xb2,
	0x8d, 0xb7, 0x72, 0x1b, 0xdf, 0x85, 0x35, 0x11, 0x59, 0xd1, 0x89, 0x08, 0x02, 0xe1, 0x0d, 0xad,
	0xe3, 0x90, 0x73, 0x62, 0xb7, 0xd5, 0x5f, 0x11, 0xd1, 0x40, 0xc1, 0x1f, 0x87, 0x9c, 0xe3, 0x6e,
	0xa5, 0x2a, 0x84, 0x13, 0x99, 0xf3, 0x34, 0xfb, 0x02, 0x01, 0x3d, 0x27, 0x42, 0xc6, 0xec, 0x13,
	0xe6, 0x79, 0x7c, 0x64, 0x09, 0xc7, 0x5c, 0x90, 0x92, 0x52, 0x48, 0xcf, 0x31, 0xae, 0x43, 0xdb,
	0x0e, 0x39, 0x8b, 0xb9, 0x15, 0x0b, 0x97, 0x9b, 0x8b, 0x34, 0x1a, 0x24, 0x74, 0x28, 0x5c, 0x8e,
	0x04, 0xe3, 0xc0, 0x49, 0x09, 0x40, 0x12, 0x48, 0x88, 0x08, 0x6e, 0xc2, 0x72, 0x10, 0xfa, 0xce,
	0xd8, 0x8e, 0x2d, 0xdb, 0x1f, 0x7b, 0xb1, 0xd9, 0xa6, 0x35, 0x96, 0x14, 0xb8, 0x87, 0x98, 0x71,
	0x19, 0xe6, 0xc7, 0x11, 0x0f, 0x91, 0x85, 0x25, 0x9a, 0x61, 0x0e, 0x9b, 0x3d, 0x07, 0x79, 0xa7,
	0x0e, 0x8f, 0xb9, 0xdc, 0x5c, 0x96, 0xbc, 0x23, 0xf0, 0x9c, 0xb9, 0xdc, 0xb8, 0x0d, 0xab, 0xfc,
	0xb5, 0xcd, 0x83, 0x58, 0xf8, 0x9e, 0x9a, 0x7c, 0x45, 0x4a, 0x20, 0x85, 0xe5, 0xf4, 0xd7, 0xa1,
	0x3d, 0xf4, 0x7d, 0x27, 0xb2, 0x62, 0x3f, 0x66, 0x23, 0x73, 0x95, 0x88, 0x80, 0xa0, 0x43, 0x44,
	0x8c, 0x1b, 0xb0, 0x64, 0x9f, 0x70, 0xfb, 0xd4, 0x0a, 0x39, 0x8b, 0x7c, 0xcf, 0x5c, 0xa3, 0x95,
	0xda, 0x84, 0xf5, 0x09, 0xea, 0x7e, 0x5d, 0x83, 0x8b, 0x79, 0xc3, 0x8c, 0x02, 0xdf, 0x8b, 0xb8,
	0x61, 0x40, 0xd3, 0xf6, 0x1d, 0xae, 0x4c, 0x80, 0xbe, 0x0d, 0x13, 0xe6, 0x5d, 0x1e, 0x45, 0x6c,
	0x98, 0x98, 0x41, 0xd2, 0x44, 0xf3, 0xe0, 0x61, 0xe8, 0x87, 0xca, 0x06, 0x64, 0xc3, 0xb8, 0x0d,
	0xcd, 0x91, 0x88, 0x62, 0xb3, 0xb9, 0xdd, 0xd8, 0x6d, 0x3f, 0x58, 0xbf, 0xc7, 0xec, 0x7b, 0x05,
	0x3b, 0xeb, 0x13, 0x01, 0x59, 0x17, 0xed, 0x41, 0x1a, 0x83, 0x6c, 0x74, 0x3f, 0x87, 0x0b, 0x09,
	0x79, 0xcf, 0xa9, 0x32, 0x4c, 0x4d, 0xc6, 0xf5, 0x6a, 0x19, 0x37, 0xf2, 0x32, 0xee, 0xfe, 0xb2,
	0x06, 0x97, 0x92, 0xb9, 0xf7, 0x79, 0xcc, 0xc4, 0xe8, 0x5c, 0x37, 0x7e, 0x07, 0xe6, 0x1c, 0x9a,
	0x95, 0xcc, 0xbe, 0x62, 0xeb, 0x8a, 0xa4, 0xfb, 0x55, 0x0d, 0xae, 0x27, 0x7d, 0x2f, 0x3d, 0x34,
	0xfd, 0x41, 0xcc, 0x62, 0x11, 0xc5, 0xc2, 0x8e, 0x92, 0x5d, 0xe7, 0x3d, 0xbf, 0x36, 0xd3, 0xf3,
	0xeb, 0x05, 0xcf, 0xbf, 0x01, 0x89, 0x55, 0xea, 0xb2, 0x68, 0x2b, 0x8c, 0x4c, 0x6e, 0x0d, 0x1a,
	0xb6, 0x70, 0x54, 0xcc, 0xc0, 0xcf, 0xee, 0xef, 0x6b, 0x70, 0xab, 0x8a, 0xa9, 0x73, 0xb7, 0x93,
	0x54, 0xfd, 0x4d, 0x4d, 0xfd, 0xc6, 0x07, 0xca, 0x7a, 0x5a, 0x64, 0x3d, 0x1b, 0xa9, 0x08, 0x0f,
	0x24, 0xe3, 0x64, 0xe2, 0xd2, 0x7e, 0xba, 0x7f, 0xac, 0x2b, 0x53, 0xd1, 0xfb, 0x8c, 0x0d, 0x98,
	0x8b, 0x4e, 0xc7, 0x56, 0x6a, 0x2e, 0xad, 0xe8, 0x74, 0xdc, 0x73, 0xa6, 0xe4, 0x51, 0x2f, 0x95,
	0xc7, 0x4f, 0x98, 0xa7, 0x22, 0x2c, 0x7e, 0x22, 0x72, 0xcc, 0x8f, 0x12, 0x09, 0x1d, 0xf3, 0x23,
	0x44, 0x5c, 0x16, 0x2a, 0x8b, 0xc5, 0x4f, 0x44, 0x58, 0x10, 0xaa, 0x70, 0x85, 0x9f, 0x92, 0x66,
	0x42, 0xd1, 0x89, 0x68, 0x26, 0x34, 0xf3, 0xd8, 0x53, 0x11, 0x09, 0x3f, 0x25, 0x32, 0xa2, 0x18,
	0x44, 0xc8, 0x88, 0xe6, 0x19, 0x0f, 0x29, 0xe8, 0xe0, 0x3c, 0xe3, 0x21, 0x22, 0x11, 0x0f, 0x54,
	0x8c, 0xc1, 0x4f, 0x44, 0x7c, 0x3b, 0xa6, 0xb0, 0xd2, 0xea, 0xe3, 0x27, 0x22, 0x9e, 0x7f, 0x46,
	0xd1, 0xa4, 0xd5, 0xc7, 0x4f, 0x44, 0x1c, 0x6e, 0xab, 0xe0, 0x81, 0x9f, 0x89, 0x9e, 0x57, 0x33,
	0x3d, 0xff, 0xbd, 0x0e, 0xd7, 0x3e, 0xe1, 0x71, 0xa2, 0x6a, 0x25, 0x3f, 0xfd, 0x88, 0x52, 0x63,
	0x6a, 0xe9, 0x18, 0x63, 0x1b, 0x74, 0x61, 0x95, 0xc9, 0xef, 0x22, 0x48, 0x59, 0x2b, 0x09, 0x2a,
	0xc1, 0x6f, 0xc2, 0xa2, 0x22, 0xea, 0x25, 0xb6, 0x96, 0x01, 0xd8, 0x9b, 0x06, 0x68, 0x25, 0x55,
	0x2d, 0x62, 0x67, 0xe7, 0xc5, 0x5c, 0xee, 0xbc, 0x30, 0x21, 0x39, 0xc6, 0x94, 0x94, 0x93, 0xa6,
	0xf1, 0x90, 0x5c, 0x46, 0x78, 0x0c, 0x03, 0x26, 0x09, 0x5c, 0xf9, 0xe1, 0x41, 0x8a, 0x1e, 0xb0,
	0x90, 0xb9, 0x7d, 0x8d, 0x0c, 0x97, 0xe1, 0xaf, 0x03, 0x3f, 0x8c, 0x95, 0x3e, 0x54, 0x0b, 0xad,
	0x3c, 0x9e, 0x04, 0x5c, 0xe9, 0x84, 0xbe, 0xd1, 0xbc, 0xfc, 0x70, 0x88, 0xe6, 0x25, 0xf5, 0xd2,
	0xf2, 0xc3, 0x61, 0xcf, 0xe9, 0xfe, 0xb3, 0x06, 0x5b, 0x55, 0x12, 0x7d, 0xef, 0x3e, 0x73, 0x07,
	0x9a, 0x0e, 0x8b, 0x99, 0xf2, 0x99, 0xcb, 0x7a, 0xd8, 0x51, 0x8c, 0xec, 0xb3, 0x98, 0xf5, 0x89,
	0x08, 0x1d, 0xc1, 0xf3, 0x3d, 0x3f, 0x74, 0xd9, 0xc8, 0xf2, 0xc6, 0xae, 0x92, 0x6c, 0x3b, 0xc1,
	0x9e, 0x8f, 0x5d, 0x8c, 0x3b, 0x1a, 0x81, 0x94, 0xf0, 0x62, 0xda, 0xdd, 0xfd, 0x4d, 0x0d, 0x36,
	0xf7, 0xe8, 0xd4, 0x2c, 0xac, 0x92, 0x18, 0x4f, 0x6a, 0x08, 0xb5, 0x4a, 0x43, 0xa8, 0x17, 0x0d,
	0xe1, 0x11, 0x2c, 0x44, 0xec, 0x8c, 0x23, 0xa3, 0xb4, 0xe5, 0xf6, 0x83, 0xab, 0xb8, 0x8f, 0x01,
	0x3b, 0xe3, 0x65, 0x7b, 0x49, 0x89, 0x35, 0x85, 0x34, 0x75, 0x85, 0xfc, 0xa1, 0x06, 0x9b, 0x2f,
	0xe9, 0xe4, 0xae, 0x60, 0xb2, 0x24, 0xd7, 0x91, 0x4c, 0xd7, 0x2b, 0x99, 0x6e, 0xcc, 0x62, 0xba,
	0xf9, 0x6e, 0x4c, 0xb7, 0x74, 0xa6, 0x7f, 0x87, 0x92, 0x2d, 0x0c, 0x54, 0xe7, 0x54, 0x39, 0xd3,
	0xca, 0x4d, 0xeb, 0x99, 0x9b, 0x9e, 0xbf, 0x13, 0x66, 0xbc, 0xce, 0xe9, 0xbc, 0x7e, 0x5d, 0x83,
	0xed, 0x69, 0x8b, 0x7f, 0x0f, 0xc7, 0xea, 0x7d, 0x65, 0xdd, 0x52, 0xc0, 0xd7, 0xca, 0xac, 0x9b,
	0x16, 0xcd, 0x6c, 0xbc, 0xfb, 0x18, 0xcc, 0x02, 0x49, 0x75, 0x2a, 0x91, 0x6d, 0xaf, 0xae, 0x6f,
	0xef, 0x19, 0xac, 0x16, 0x42, 0x06, 0x09, 0x31, 0x39, 0x7c, 0xa7, 0x4f, 0xe3, 0x0e, 0xa4, 0x87,
	0x6f, 0xf1, 0x30, 0xee, 0xfe, 0xb9, 0x09, 0x57, 0x2a, 0x79, 0x7e, 0x0b, 0xa5, 0xe6, 0x14, 0xd4,
	0x28, 0x2a, 0x28, 0x55, 0x79, 0xb3, 0x52, 0xe5, 0xad, 0xa2, 0xca, 0x0b, 0xd1, 0x7c, 0x6e, 0x3a,
	0x9a, 0x6f, 0x43, 0x3b, 0x12, 0xde, 0x10, 0x4f, 0x57, 0x61, 0x73, 0x15, 0x05, 0x74, 0xc8, 0xd8,
	0x02, 0x88, 0x4f, 0x42, 0xae, 0x08, 0xe4, 0xe1, 0xa6, 0x21, 0x28, 0x91, 0x48, 0xbc, 0x96, 0xbd,
	0x32, 0xb0, 0xa6, 0x6d, 0x9c, 0x3d, 0x7e, 0xc5, 0x47, 0x67, 0x6a, 0xb0, 0x8c, 0xb0, 0x3a, 0xa4,
	0xc5, 0xfe, 0x76, 0x2e, 0xf6, 0x6f, 0x81, 0x96, 0xb2, 0xab, 0x0c, 0x5b, 0x4f, 0xe2, 0xb7, 0x40,
	0xcb, 0xd8, 0x55, 0x9a, 0xad, 0xe7, 0xf0, 0x9b, 0x90, 0xd5, 0x2a, 0x74, 0x4a, 0xe6, 0x8a, 0x17,
	0x13, 0x92, 0x62, 0x85, 0xce, 0x4b, 0xad, 0x76, 0xe9, 0x40, 0x5a, 0x68, 0xa8, 0x94, 0x3a, 0x2b,
	0x3c, 0x76, 0xa0, 0x50, 0xa7, 0x98, 0x17, 0x4a, 0xab, 0x97, 0x4d, 0x58, 0x14, 0xd1, 0x67, 0x22,
	0x8c, 0xc7, 0x6c, 0x64, 0x1a, 0x52, 0x27, 0x29, 0x80, 0xbd, 0x32, 0x8d, 0xc7, 0x33, 0x67, 0x5d,
	0xf6, 0xa6, 0x40, 0xf7, 0xaf, 0xf3, 0x59, 0x31, 0xa9, 0x05, 0x95, 0xef, 0xac, 0xe7, 0x1b, 0x59,
	0xcf, 0x36, 0xb4, 0x6d, 0xe6, 0x3d, 0xe1, 0x4f, 0x1d, 0x11, 0x73, 0x47, 0x65, 0x52, 0x3a, 0x64,
	0x74, 0x61, 0x89, 0x9a, 0xfb, 0x7c, 0xc4, 0x91, 0x44, 0xa6, 0x56, 0x39, 0x0c, 0x65, 0x1c, 0x08,
	0x5b, 0x59, 0x0f, 0x7e, 0x92, 0x14, 0x63, 0xdf, 0x3e, 0x55, 0x59, 0x96, 0x6c, 0xe4, 0xf5, 0xbd,
	0x36, 0x53, 0xdf, 0x17, 0x0a, 0xfa, 0x2e, 0xd8, 0xb9, 0xf1, 0x06, 0x3b, 0x5f, 0x9f, 0xb2, 0xf3,
	0x5d, 0x58, 0xcd, 0xa4, 0xd9, 0xc7, 0x30, 0x66, 0x76, 0xb7, 0x6b, 0xbb, 0xb5, 0x7e, 0x11, 0x36,
	0x6e, 0xc1, 0x72, 0x22, 0x59, 0x49, 0x77, 0x93, 0xe8, 0xf2, 0xa0, 0xf1, 0x21, 0xac, 0x69, 0x02,
	0x96, 0x84, 0xb7, 0x88, 0x70, 0x0a, 0x47, 0xdd, 0x89, 0xe8, 0x39, 0x25, 0x0c, 0xe6, 0xf7, 0xa4,
	0xee, 0x92, 0x36, 0xae, 0x96, 0x7c, 0x1f, 0x22, 0x23, 0xe6, 0x0e, 0x11, 0xe4, 0x41, 0xd4, 0x53,
	0x02, 0x0c, 0xc4, 0x6b, 0xf3, 0xb6, 0xd4, 0x93, 0x06, 0x49, 0x9f, 0x53, 0x43, 0x68, 0x7d, 0x73,
	0x37, 0xf1, 0x39, 0x1d, 0x45, 0x39, 0xf4, 0xef, 0x1f, 0x8c, 0x43, 0xfb, 0x84, 0x45, 0xca, 0x5e,
	0x3e, 0x20, 0xc2, 0x22, 0x8c, 0x45, 0xa5, 0x88, 0x2c, 0x97, 0x85, 0xa7, 0xe6, 0x87, 0xd2, 0x68,
	0x44, 0xf4, 0x29, 0x0b, 0x4f, 0xd1, 0x24, 0xf0, 0x77, 0x5f, 0x44, 0xb2, 0x30, 0xbf, 0x43, 0xdb,
	0xce, 0x61, 0xc6, 0x5d, 0xb8, 0x80, 0xed, 0xfc, 0x42, 0x77, 0x69, 0x9a, 0xe9, 0x8e, 0xee, 0x3f,
	0x6a, 0x70, 0xb9, 0x22, 0x4b, 0x78, 0x0b, 0x87, 0xfe, 0xbf, 0x77, 0x3f, 0xdc, 0xdf, 0x6d, 0x2d,
	0x39, 0x78, 0x79, 0xc0, 0x63, 0xb5, 0xc5, 0x01, 0x1f, 0xf1, 0x37, 0x95, 0x1a, 0xef, 0x92, 0x8a,
	0x15, 0x64, 0xd2, 0x9c, 0x96, 0x49, 0xbe, 0x34, 0x68, 0xbd, 0x5d, 0x69, 0x50, 0x91, 0xfc, 0xfc,
	0xa9, 0x06, 0xbb, 0x6f, 0xde, 0xdf, 0x7b, 0x4f, 0xfc, 0x7f, 0x90, 0x4b, 0xfc, 0x6f, 0xe8, 0xa9,
	0x91, 0xe4, 0x43, 0x63, 0x4c, 0x4b, 0x8f, 0xfe, 0x56, 0x87, 0x6b, 0x33, 0xe9, 0xde, 0x29, 0x83,
	0x2f, 0x68, 0xa0, 0xe4, 0xc2, 0xa1, 0x0b, 0x4b, 0xb1, 0x70, 0xf9, 0x9e, 0xef, 0x1d, 0x8f, 0x84,
	0x1d, 0xab, 0xbd, 0xe4, 0xb0, 0x24, 0xb4, 0xb6, 0x4a, 0x42, 0xeb, 0x9c, 0x1e, 0x5a, 0xb7, 0xa1,
	0x8d, 0x9e, 0x8a, 0x6c, 0x6b, 0x16, 0xae, 0x41, 0xf9, 0xe0, 0xbb, 0x30, 0x33, 0xf8, 0x2e, 0x16,
	0x83, 0xef, 0x1d, 0x68, 0xdb, 0x27, 0x62, 0xe4, 0x0c, 0x70, 0xdf, 0x91, 0x09, 0x24, 0xdf, 0x45,
	0x92, 0x2f, 0xc2, 0x7d, 0xbd, 0xb7, 0xfb, 0x25, 0xb4, 0x08, 0xad, 0x90, 0x9a, 0x09, 0xf3, 0xe1,
	0x78, 0xc4, 0x9f, 0x8f, 0x5d, 0x25, 0xb3, 0xa4, 0x99, 0xe7, 0xb0, 0x51, 0xe4, 0x70, 0x07, 0x56,
	0x68, 0xab, 0x3f, 0x66, 0x21, 0x3f, 0xf1, 0xc7, 0x51, 0xb2, 0xc9, 0x02, 0x8a, 0xe5, 0x58, 0x67,
	0x46, 0x25, 0xff, 0xbf, 0x5c, 0x22, 0xe5, 0x2f, 0x54, 0xa7, 0x52, 0x07, 0x34, 0x6c, 0xe1, 0x44,
	0xca, 0xd9, 0xe8, 0xbb, 0xaa, 0xb2, 0xf9, 0x75, 0x0d, 0xae, 0x7e, 0x3b, 0xc5, 0xf1, 0xed, 0x9c,
	0x8f, 0xac, 0x97, 0x94, 0x0f, 0xca, 0x2b, 0x7e, 0xa1, 0x5d, 0x88, 0xab, 0x9e, 0x92, 0xd8, 0x94,
	0x17, 0x49, 0xbd, 0xa4, 0x58, 0x52, 0xb7, 0x4f, 0xb9, 0xfa, 0x0b, 0x95, 0xa0, 0x6e, 0x9f, 0x44,
	0x49, 0x01, 0x56, 0xbc, 0x9c, 0x6a, 0x4d, 0xfb, 0xce, 0xf5, 0xd4, 0xbb, 0x2c, 0xe1, 0x0e, 0x55,
	0xcc, 0x4f, 0x26, 0xed, 0xb9, 0x43, 0x74, 0x88, 0xc1, 0x74, 0xc8, 0x1f, 0xe4, 0x43, 0xfe, 0xe1,
	0x54, 0xc8, 0x3f, 0xcc, 0x85, 0xfc, 0x41, 0x21, 0xe4, 0x0f, 0xb4, 0x90, 0x7f, 0x38, 0x1d, 0xf2,
	0x35, 0xa8, 0xec, 0x9c, 0x6d, 0x97, 0x9f, 0xb3, 0x5b, 0x28, 0x8c, 0x34, 0x87, 0x58, 0xa2, 0xc3,
	0x54, 0x43, 0xba, 0x47, 0x70, 0xe5, 0xb1, 0xe3, 0x24, 0xaa, 0xd8, 0xf3, 0x47, 0x18, 0xa1, 0x12,
	0x73, 0xae, 0xb8, 0xde, 0xab, 0xbc, 0x10, 0xce, 0xec, 0xb1, 0xa1, 0xdb, 0xe3, 0x6f, 0x35, 0xa7,
	0x51, 0x2b, 0x9c, 0x97, 0xd3, 0xe8, 0x6f, 0x2e, 0x8d, 0xdc, 0x9b, 0x8b, 0xce, 0x65, 0xb3, 0x82,
	0xcb, 0x4a, 0xaf, 0xc9, 0x71, 0xf9, 0x6d, 0x7a, 0x4d, 0xa2, 0x10, 0xe9, 0x35, 0xaf, 0xb2, 0xa3,
	0x44, 0x75, 0xbc, 0x87, 0x2b, 0x00, 0x03, 0x9a, 0xc7, 0x23, 0x36, 0x54, 0x2c, 0xd2, 0x77, 0xf7,
	0x3f, 0x9a, 0xbb, 0xaa, 0x95, 0xcb, 0x6a, 0x7b, 0x65, 0x2c, 0xf5, 0x0a, 0x63, 0x69, 0xe4, 0xd4,
	0x50, 0xf4, 0xc3, 0xe6, 0x1b, 0xfd, 0xb0, 0x35, 0xe5, 0x87, 0x17, 0xa1, 0x45, 0xb6, 0x9c, 0x1c,
	0x57, 0x41, 0xa1, 0x1e, 0x99, 0xcf, 0xd5, 0x23, 0x85, 0x37, 0xa9, 0x85, 0xa9, 0x34, 0xff, 0x1a,
	0xc8, 0xb7, 0x1d, 0x2b, 0x0a, 0xb8, 0xad, 0xde, 0xac, 0xe4, 0x41, 0x35, 0x08, 0xb8, 0x9d, 0x75,
	0x6b, 0x17, 0x95, 0xda, 0x39, 0xb6, 0x03, 0xab, 0x98, 0xd2, 0x72, 0xf7, 0x88, 0x87, 0x56, 0xa0,
	0x39, 0xe5, 0xb2, 0x88, 0x3e, 0x25, 0x54, 0xba, 0xe4, 0x4d, 0x58, 0xd6, 0x89, 0xee, 0xab, 0xc2,
	0x68, 0xc9, 0xcd, 0x68, 0xee, 0x1b, 0xdf, 0x87, 0x8b, 0x72, 0xad, 0x20, 0xf4, 0x5d, 0x9f, 0x1e,
	0xaa, 0x68, 0x55, 0x59, 0x21, 0x19, 0xd4, 0x77, 0x90, 0x74, 0xd1, 0xf2, 0x0f, 0x60, 0xa3, 0x38,
	0x42, 0x32, 0x21, 0x6f, 0xa7, 0xd7, 0xf3, 0x43, 0x92, 0x38, 0xb2, 0xe6, 0xf1, 0x57, 0x56, 0xc0,
	0xfd, 0x60, 0xc4, 0x15, 0xb9, 0x2c, 0xaa, 0x56, 0x3c, 0xfe, 0xea, 0x80, 0x60, 0x99, 0x64, 0x1e,
	0xc1, 0x95, 0x7d, 0x3e, 0x7a, 0xbf, 0x71, 0xe2, 0xe7, 0x35, 0xd8, 0xa0, 0x15, 0x5e, 0xa0, 0x0b,
	0x9f, 0x57, 0x88, 0xa8, 0x34, 0xc0, 0xcb, 0x30, 0xef, 0x07, 0xdc, 0xd3, 0x02, 0x04, 0x36, 0x7b,
	0x4e, 0xf7, 0x67, 0xc9, 0xd3, 0x95, 0xc6, 0xc7, 0x39, 0x3a, 0xd8, 0x4e, 0x7a, 0xc7, 0x86, 0xee,
	0x6e, 0xa4, 0xee, 0x4e, 0xab, 0xf5, 0xbc, 0x63, 0x5f, 0x79, 0xfb, 0xbf, 0x9a, 0xb0, 0x92, 0xef,
	0xa8, 0x12, 0x74, 0x6a, 0x98, 0xda, 0x6b, 0x8b, 0x34, 0xcc, 0xc4, 0x8d, 0x64, 0xb7, 0x70, 0x91,
	0x49, 0xc9, 0x8c, 0x1c, 0xd1, 0x43, 0x24, 0x23, 0x90, 0x16, 0xd0, 0xd4, 0x9e, 0x39, 0xa5, 0x9d,
	0x5c, 0x55, 0x09, 0x1c, 0xdd, 0x51, 0xcb, 0xa8, 0xb9, 0x20, 0xe7, 0x1f, 0xbb, 0x38, 0x5a, 0xc6,
	0x60, 0x74, 0xb3, 0xc4, 0x15, 0x81, 0xa0, 0x01, 0x22, 0x68, 0x65, 0x1a, 0x81, 0xe5, 0xf0, 0xc8,
	0x56, 0xcf, 0xc9, 0x2b, 0x19, 0xd5, 0x3e, 0x8f, 0x6c, 0xe3, 0x16, 0x48, 0xc4, 0x62, 0x8e, 0x93,
	0x39, 0x69, 0xa3, 0xbf, 0x44, 0xe8, 0x63, 0xc7, 0x21, 0x37, 0x4d, 0xdf, 0xa5, 0x91, 0x1b, 0x75,
	0x78, 0x12, 0x80, 0xdc, 0x6c, 0xc3, 0x12, 0x0f, 0x03, 0x4b, 0x2d, 0xe8, 0x25, 0x0f, 0xcb, 0x3c,
	0x0c, 0x48, 0x8c, 0x03, 0x2a, 0x33, 0x02, 0x36, 0xc1, 0xbe, 0xb6, 0x54, 0x4b, 0xc0, 0x26, 0x03,
	0xcf, 0x78, 0x08, 0x97, 0xe4, 0x20, 0xee, 0x39, 0x16, 0x12, 0x60, 0x75, 0x1f, 0x67, 0xf7, 0x5e,
	0xeb, 0xd4, 0xfb, 0xd4, 0x73, 0x0e, 0xd8, 0x64, 0x5f, 0x75, 0xe1, 0xd6, 0x22, 0x1e, 0x9e, 0xf1,
	0xd0, 0xca, 0x9e, 0xb2, 0xe5, 0x35, 0xd8, 0x8a, 0xc4, 0xf7, 0xcb, 0x63, 0xcb, 0x4a, 0x31, 0xb6,
	0xa4, 0x07, 0x99, 0x7a, 0x3c, 0x6a, 0xa8, 0x83, 0x4c, 0x06, 0x4a, 0x25, 0x14, 0x97, 0x2a, 0xe2,
	0x35, 0xea, 0x96, 0x32, 0x7f, 0xec, 0x26, 0xef, 0xd4, 0x52, 0x22, 0x52, 0x81, 0xf2, 0x7e, 0x03,
	0x6c, 0xf9, 0x5e, 0x87, 0x0a, 0xdc, 0x81, 0xd5, 0x80, 0x85, 0xdc, 0x8b, 0x33, 0xc1, 0xc8, 0x5b,
	0x8e, 0x65, 0x09, 0x2b, 0xd9, 0x74, 0xff, 0xa2, 0x1d, 0xd5, 0x2f, 0x12, 0xdd, 0x08, 0x3b, 0xf1,
	0x43, 0x2c, 0x54, 0x27, 0x9e, 0x4d, 0x79, 0x7c, 0x4d, 0x15, 0xaa, 0xaa, 0x8d, 0x39, 0x49, 0x90,
	0xdc, 0xfa, 0xdb, 0x7e, 0xe8, 0xa4, 0x65, 0x4b, 0x11, 0x4e, 0x12, 0xbd, 0x46, 0x49, 0x11, 0xfa,
	0x0d, 0xee, 0xc5, 0x72, 0x37, 0x6d, 0x73, 0x85, 0xdc, 0xf0, 0xc1, 0x57, 0x4b, 0xd9, 0x89, 0x35,
	0x90, 0xff, 0x22, 0x31, 0xf6, 0x61, 0x55, 0xab, 0x23, 0xd1, 0xaf, 0x8d, 0xdc, 0xfb, 0x8d, 0x16,
	0x71, 0x3a, 0xe6, 0x74, 0x87, 0x0a, 0x01, 0x8f, 0x60, 0x25, 0xff, 0x20, 0x63, 0x94, 0xbd, 0x3d,
	0x77, 0xd6, 0x10, 0x3c, 0x62, 0x11, 0xd7, 0x07, 0xe6, 0x1f, 0x49, 0xde, 0x76, 0xe0, 0xc7, 0x70,
	0x41, 0xe3, 0x5b, 0x1e, 0xf9, 0xc6, 0x86, 0x3e, 0x36, 0xbd, 0x71, 0xef, 0x74, 0x74, 0xb8, 0x90,
	0x1d, 0x3c, 0x82, 0xa5, 0x41, 0xec, 0x07, 0xe9, 0xf2, 0x15, 0x53, 0x4c, 0x33, 0x20, 0xb2, 0x2b,
	0xfe, 0xe2, 0x4b, 0xb5, 0x71, 0x53, 0x9f, 0xa4, 0xe2, 0x71, 0xbd, 0xb3, 0x3b, 0x8b, 0x28, 0x27,
	0x5d, 0x0b, 0x2e, 0x95, 0x3f, 0xed, 0x19, 0x54, 0x71, 0xcf, 0x7c, 0x48, 0xed, 0x74, 0x67, 0x91,
	0xa8, 0x05, 0x9e, 0xc1, 0x46, 0xe9, 0x7b, 0x9a, 0xb1, 0x4d, 0x3c, 0xce, 0x78, 0x6a, 0x2b, 0x11,
	0xcc, 0x33, 0xd8, 0x28, 0x7d, 0xf7, 0x92, 0x93, 0xcd, 0x7a, 0x12, 0x2b, 0x99, 0xec, 0x08, 0xcc,
	0xaa, 0x37, 0x1e, 0xc5, 0xdc, 0x8c, 0xd7, 0xaa, 0xce, 0xad, 0xf2, 0xbd, 0x17, 0x4c, 0xe0, 0x13,
	0xd8, 0x90, 0xf7, 0xaa, 0x45, 0x86, 0x37, 0x4b, 0x16, 0x98, 0x65, 0x12, 0x5f, 0xe6, 0x1e, 0xa4,
	0x4a, 0xef, 0x64, 0x8c, 0x3b, 0x05, 0x96, 0x66, 0xdd, 0x4c, 0x75, 0xee, 0xbe, 0x1d, 0xb1, 0x5a,
	0xfe, 0x45, 0x96, 0xab, 0x6b, 0x61, 0x6a, 0x8f, 0x8d, 0x46, 0x4f, 0x98, 0x7d, 0x6a, 0x6c, 0xe9,
	0xbb, 0x99, 0x8e, 0x63, 0x25, 0xfb, 0xf9, 0x6c, 0xea, 0xc2, 0x9f, 0xb6, 0xb0, 0x55, 0x22, 0x16,
	0x9d, 0xeb, 0xeb, 0x95, 0xfd, 0x6a, 0xde, 0xa7, 0x60, 0x4c, 0xd7, 0x57, 0x06, 0x3d, 0xac, 0x55,
	0xd6, 0x5d, 0xb3, 0xd9, 0xd3, 0x6a, 0x93, 0x3c, 0x7b, 0xd3, 0xa5, 0x55, 0x9e, 0xbd, 0xb2, 0xa2,
	0xe6, 0x29, 0x18, 0xd3, 0x69, 0x9d, 0x64, 0xaf, 0x32, 0xdd, 0x2b, 0x61, 0xef, 0x73, 0x95, 0xb8,
	0x15, 0x0b, 0x93, 0x37, 0xcd, 0x74, 0xa3, 0x84, 0xbf, 0x82, 0xc5, 0xfe, 0x30, 0x0b, 0x7e, 0x69,
	0x3a, 0x66, 0x5c, 0xc9, 0x27, 0x4d, 0xfa, 0x96, 0x3b, 0x65, 0x5d, 0x72, 0xae, 0xa3, 0x39, 0xfa,
	0xab, 0xe0, 0xc3, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0x2a, 0x71, 0x31, 0xf6, 0x65, 0x28, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CycleBuyServiceClient is the client API for CycleBuyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CycleBuyServiceClient interface {
	//周期购活动列表 boss
	GetCycleBuyList(ctx context.Context, in *CycleBuyListRequest, opts ...grpc.CallOption) (*CycleBuyListResponse, error)
	//创建周期购活动
	CreateCycleBuy(ctx context.Context, in *CycleBuyRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//更新周期购活动
	UpdateCycleBuy(ctx context.Context, in *CycleBuyRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//周期购活动详情
	GetCycleBuyDetail(ctx context.Context, in *CycleBuyIdRequest, opts ...grpc.CallOption) (*CycleBuyDetailResponse, error)
	//终止周期购活动
	StopCycleBuy(ctx context.Context, in *CycleBuyIdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//统计周期购活动每个月未发货商品数
	CycleBuyUnshipStatistics(ctx context.Context, in *CycleBuyUnshipStatisticsRequest, opts ...grpc.CallOption) (*CycleBuyUnshipStatisticsListResponse, error)
	//获取周期购商品列表 boss
	GetCycleBuyProductList(ctx context.Context, in *GetCycleBuyProductListRequest, opts ...grpc.CallOption) (*GetCycleBuyProductListResponse, error)
	//创建周期购商品
	CreateCycleBuyProduct(ctx context.Context, in *CreateCycleBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//更新周期购商品
	UpdateCycleBuyProduct(ctx context.Context, in *UpdateCycleBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取周期购商品详情
	GetCycleBuyProductDetail(ctx context.Context, in *CycleBuyProductDetailRequest, opts ...grpc.CallOption) (*GetCycleBuyProductDetailResponse, error)
	//删除周期购商品
	DeleteCycleBuyProduct(ctx context.Context, in *CycleBuyProductIdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取可以参加周期购活动的阿闻电商渠道的商品
	GetCycleBuyUPetProductSelectList(ctx context.Context, in *GetCycleBuyUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetCycleBuyUPetProductSelectListResponse, error)
	// 周期购下单回调
	CycleBuyOrderStaticCallBack(ctx context.Context, in *CycleBuyOrderStaticRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 周期购活动商品列表 product-api
	CycleBuyProductList(ctx context.Context, in *CycleBuyProductListRequest, opts ...grpc.CallOption) (*CycleBuyProductListResponse, error)
	// 收藏 周期购活动商品
	AddCycleBuyCollect(ctx context.Context, in *AddCycleBuyCollectRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 周期购活动商品 收藏列表
	CycleBuyCollectList(ctx context.Context, in *CycleBuyCollectListRequest, opts ...grpc.CallOption) (*CycleBuyCollectListResponse, error)
	// 周期购活动商品 删除收藏
	DelCycleBuyCollect(ctx context.Context, in *DelCycleBuyCollectRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 周期购活动 商品 收藏状态
	CycleBuyCollectDetail(ctx context.Context, in *DelCycleBuyCollectRequest, opts ...grpc.CallOption) (*CycleBuyCollectDetailResponse, error)
	// 我的周期购主订单列表 mall用
	GetCycleOrderList(ctx context.Context, in *CycleOrderListRequest, opts ...grpc.CallOption) (*CycleOrderListResponse, error)
}

type cycleBuyServiceClient struct {
	cc *grpc.ClientConn
}

func NewCycleBuyServiceClient(cc *grpc.ClientConn) CycleBuyServiceClient {
	return &cycleBuyServiceClient{cc}
}

func (c *cycleBuyServiceClient) GetCycleBuyList(ctx context.Context, in *CycleBuyListRequest, opts ...grpc.CallOption) (*CycleBuyListResponse, error) {
	out := new(CycleBuyListResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/GetCycleBuyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) CreateCycleBuy(ctx context.Context, in *CycleBuyRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/CreateCycleBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) UpdateCycleBuy(ctx context.Context, in *CycleBuyRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/UpdateCycleBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) GetCycleBuyDetail(ctx context.Context, in *CycleBuyIdRequest, opts ...grpc.CallOption) (*CycleBuyDetailResponse, error) {
	out := new(CycleBuyDetailResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/GetCycleBuyDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) StopCycleBuy(ctx context.Context, in *CycleBuyIdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/StopCycleBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) CycleBuyUnshipStatistics(ctx context.Context, in *CycleBuyUnshipStatisticsRequest, opts ...grpc.CallOption) (*CycleBuyUnshipStatisticsListResponse, error) {
	out := new(CycleBuyUnshipStatisticsListResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/CycleBuyUnshipStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) GetCycleBuyProductList(ctx context.Context, in *GetCycleBuyProductListRequest, opts ...grpc.CallOption) (*GetCycleBuyProductListResponse, error) {
	out := new(GetCycleBuyProductListResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/GetCycleBuyProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) CreateCycleBuyProduct(ctx context.Context, in *CreateCycleBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/CreateCycleBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) UpdateCycleBuyProduct(ctx context.Context, in *UpdateCycleBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/UpdateCycleBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) GetCycleBuyProductDetail(ctx context.Context, in *CycleBuyProductDetailRequest, opts ...grpc.CallOption) (*GetCycleBuyProductDetailResponse, error) {
	out := new(GetCycleBuyProductDetailResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/GetCycleBuyProductDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) DeleteCycleBuyProduct(ctx context.Context, in *CycleBuyProductIdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/DeleteCycleBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) GetCycleBuyUPetProductSelectList(ctx context.Context, in *GetCycleBuyUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetCycleBuyUPetProductSelectListResponse, error) {
	out := new(GetCycleBuyUPetProductSelectListResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/GetCycleBuyUPetProductSelectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) CycleBuyOrderStaticCallBack(ctx context.Context, in *CycleBuyOrderStaticRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/CycleBuyOrderStaticCallBack", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) CycleBuyProductList(ctx context.Context, in *CycleBuyProductListRequest, opts ...grpc.CallOption) (*CycleBuyProductListResponse, error) {
	out := new(CycleBuyProductListResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/CycleBuyProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) AddCycleBuyCollect(ctx context.Context, in *AddCycleBuyCollectRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/AddCycleBuyCollect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) CycleBuyCollectList(ctx context.Context, in *CycleBuyCollectListRequest, opts ...grpc.CallOption) (*CycleBuyCollectListResponse, error) {
	out := new(CycleBuyCollectListResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/CycleBuyCollectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) DelCycleBuyCollect(ctx context.Context, in *DelCycleBuyCollectRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/DelCycleBuyCollect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) CycleBuyCollectDetail(ctx context.Context, in *DelCycleBuyCollectRequest, opts ...grpc.CallOption) (*CycleBuyCollectDetailResponse, error) {
	out := new(CycleBuyCollectDetailResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/CycleBuyCollectDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cycleBuyServiceClient) GetCycleOrderList(ctx context.Context, in *CycleOrderListRequest, opts ...grpc.CallOption) (*CycleOrderListResponse, error) {
	out := new(CycleOrderListResponse)
	err := c.cc.Invoke(ctx, "/ac.CycleBuyService/GetCycleOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CycleBuyServiceServer is the server API for CycleBuyService service.
type CycleBuyServiceServer interface {
	//周期购活动列表 boss
	GetCycleBuyList(context.Context, *CycleBuyListRequest) (*CycleBuyListResponse, error)
	//创建周期购活动
	CreateCycleBuy(context.Context, *CycleBuyRequest) (*BaseResponse, error)
	//更新周期购活动
	UpdateCycleBuy(context.Context, *CycleBuyRequest) (*BaseResponse, error)
	//周期购活动详情
	GetCycleBuyDetail(context.Context, *CycleBuyIdRequest) (*CycleBuyDetailResponse, error)
	//终止周期购活动
	StopCycleBuy(context.Context, *CycleBuyIdRequest) (*BaseResponse, error)
	//统计周期购活动每个月未发货商品数
	CycleBuyUnshipStatistics(context.Context, *CycleBuyUnshipStatisticsRequest) (*CycleBuyUnshipStatisticsListResponse, error)
	//获取周期购商品列表 boss
	GetCycleBuyProductList(context.Context, *GetCycleBuyProductListRequest) (*GetCycleBuyProductListResponse, error)
	//创建周期购商品
	CreateCycleBuyProduct(context.Context, *CreateCycleBuyProductRequest) (*BaseResponse, error)
	//更新周期购商品
	UpdateCycleBuyProduct(context.Context, *UpdateCycleBuyProductRequest) (*BaseResponse, error)
	//获取周期购商品详情
	GetCycleBuyProductDetail(context.Context, *CycleBuyProductDetailRequest) (*GetCycleBuyProductDetailResponse, error)
	//删除周期购商品
	DeleteCycleBuyProduct(context.Context, *CycleBuyProductIdRequest) (*BaseResponse, error)
	// 获取可以参加周期购活动的阿闻电商渠道的商品
	GetCycleBuyUPetProductSelectList(context.Context, *GetCycleBuyUPetProductSelectListRequest) (*GetCycleBuyUPetProductSelectListResponse, error)
	// 周期购下单回调
	CycleBuyOrderStaticCallBack(context.Context, *CycleBuyOrderStaticRequest) (*BaseResponse, error)
	// 周期购活动商品列表 product-api
	CycleBuyProductList(context.Context, *CycleBuyProductListRequest) (*CycleBuyProductListResponse, error)
	// 收藏 周期购活动商品
	AddCycleBuyCollect(context.Context, *AddCycleBuyCollectRequest) (*BaseResponse, error)
	// 周期购活动商品 收藏列表
	CycleBuyCollectList(context.Context, *CycleBuyCollectListRequest) (*CycleBuyCollectListResponse, error)
	// 周期购活动商品 删除收藏
	DelCycleBuyCollect(context.Context, *DelCycleBuyCollectRequest) (*BaseResponse, error)
	// 周期购活动 商品 收藏状态
	CycleBuyCollectDetail(context.Context, *DelCycleBuyCollectRequest) (*CycleBuyCollectDetailResponse, error)
	// 我的周期购主订单列表 mall用
	GetCycleOrderList(context.Context, *CycleOrderListRequest) (*CycleOrderListResponse, error)
}

// UnimplementedCycleBuyServiceServer can be embedded to have forward compatible implementations.
type UnimplementedCycleBuyServiceServer struct {
}

func (*UnimplementedCycleBuyServiceServer) GetCycleBuyList(ctx context.Context, req *CycleBuyListRequest) (*CycleBuyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCycleBuyList not implemented")
}
func (*UnimplementedCycleBuyServiceServer) CreateCycleBuy(ctx context.Context, req *CycleBuyRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCycleBuy not implemented")
}
func (*UnimplementedCycleBuyServiceServer) UpdateCycleBuy(ctx context.Context, req *CycleBuyRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCycleBuy not implemented")
}
func (*UnimplementedCycleBuyServiceServer) GetCycleBuyDetail(ctx context.Context, req *CycleBuyIdRequest) (*CycleBuyDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCycleBuyDetail not implemented")
}
func (*UnimplementedCycleBuyServiceServer) StopCycleBuy(ctx context.Context, req *CycleBuyIdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopCycleBuy not implemented")
}
func (*UnimplementedCycleBuyServiceServer) CycleBuyUnshipStatistics(ctx context.Context, req *CycleBuyUnshipStatisticsRequest) (*CycleBuyUnshipStatisticsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CycleBuyUnshipStatistics not implemented")
}
func (*UnimplementedCycleBuyServiceServer) GetCycleBuyProductList(ctx context.Context, req *GetCycleBuyProductListRequest) (*GetCycleBuyProductListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCycleBuyProductList not implemented")
}
func (*UnimplementedCycleBuyServiceServer) CreateCycleBuyProduct(ctx context.Context, req *CreateCycleBuyProductRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCycleBuyProduct not implemented")
}
func (*UnimplementedCycleBuyServiceServer) UpdateCycleBuyProduct(ctx context.Context, req *UpdateCycleBuyProductRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCycleBuyProduct not implemented")
}
func (*UnimplementedCycleBuyServiceServer) GetCycleBuyProductDetail(ctx context.Context, req *CycleBuyProductDetailRequest) (*GetCycleBuyProductDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCycleBuyProductDetail not implemented")
}
func (*UnimplementedCycleBuyServiceServer) DeleteCycleBuyProduct(ctx context.Context, req *CycleBuyProductIdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCycleBuyProduct not implemented")
}
func (*UnimplementedCycleBuyServiceServer) GetCycleBuyUPetProductSelectList(ctx context.Context, req *GetCycleBuyUPetProductSelectListRequest) (*GetCycleBuyUPetProductSelectListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCycleBuyUPetProductSelectList not implemented")
}
func (*UnimplementedCycleBuyServiceServer) CycleBuyOrderStaticCallBack(ctx context.Context, req *CycleBuyOrderStaticRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CycleBuyOrderStaticCallBack not implemented")
}
func (*UnimplementedCycleBuyServiceServer) CycleBuyProductList(ctx context.Context, req *CycleBuyProductListRequest) (*CycleBuyProductListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CycleBuyProductList not implemented")
}
func (*UnimplementedCycleBuyServiceServer) AddCycleBuyCollect(ctx context.Context, req *AddCycleBuyCollectRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCycleBuyCollect not implemented")
}
func (*UnimplementedCycleBuyServiceServer) CycleBuyCollectList(ctx context.Context, req *CycleBuyCollectListRequest) (*CycleBuyCollectListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CycleBuyCollectList not implemented")
}
func (*UnimplementedCycleBuyServiceServer) DelCycleBuyCollect(ctx context.Context, req *DelCycleBuyCollectRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelCycleBuyCollect not implemented")
}
func (*UnimplementedCycleBuyServiceServer) CycleBuyCollectDetail(ctx context.Context, req *DelCycleBuyCollectRequest) (*CycleBuyCollectDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CycleBuyCollectDetail not implemented")
}
func (*UnimplementedCycleBuyServiceServer) GetCycleOrderList(ctx context.Context, req *CycleOrderListRequest) (*CycleOrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCycleOrderList not implemented")
}

func RegisterCycleBuyServiceServer(s *grpc.Server, srv CycleBuyServiceServer) {
	s.RegisterService(&_CycleBuyService_serviceDesc, srv)
}

func _CycleBuyService_GetCycleBuyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).GetCycleBuyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/GetCycleBuyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).GetCycleBuyList(ctx, req.(*CycleBuyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_CreateCycleBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).CreateCycleBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/CreateCycleBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).CreateCycleBuy(ctx, req.(*CycleBuyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_UpdateCycleBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).UpdateCycleBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/UpdateCycleBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).UpdateCycleBuy(ctx, req.(*CycleBuyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_GetCycleBuyDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).GetCycleBuyDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/GetCycleBuyDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).GetCycleBuyDetail(ctx, req.(*CycleBuyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_StopCycleBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).StopCycleBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/StopCycleBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).StopCycleBuy(ctx, req.(*CycleBuyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_CycleBuyUnshipStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyUnshipStatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).CycleBuyUnshipStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/CycleBuyUnshipStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).CycleBuyUnshipStatistics(ctx, req.(*CycleBuyUnshipStatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_GetCycleBuyProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCycleBuyProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).GetCycleBuyProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/GetCycleBuyProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).GetCycleBuyProductList(ctx, req.(*GetCycleBuyProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_CreateCycleBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCycleBuyProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).CreateCycleBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/CreateCycleBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).CreateCycleBuyProduct(ctx, req.(*CreateCycleBuyProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_UpdateCycleBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCycleBuyProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).UpdateCycleBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/UpdateCycleBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).UpdateCycleBuyProduct(ctx, req.(*UpdateCycleBuyProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_GetCycleBuyProductDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyProductDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).GetCycleBuyProductDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/GetCycleBuyProductDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).GetCycleBuyProductDetail(ctx, req.(*CycleBuyProductDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_DeleteCycleBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyProductIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).DeleteCycleBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/DeleteCycleBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).DeleteCycleBuyProduct(ctx, req.(*CycleBuyProductIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_GetCycleBuyUPetProductSelectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCycleBuyUPetProductSelectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).GetCycleBuyUPetProductSelectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/GetCycleBuyUPetProductSelectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).GetCycleBuyUPetProductSelectList(ctx, req.(*GetCycleBuyUPetProductSelectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_CycleBuyOrderStaticCallBack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyOrderStaticRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).CycleBuyOrderStaticCallBack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/CycleBuyOrderStaticCallBack",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).CycleBuyOrderStaticCallBack(ctx, req.(*CycleBuyOrderStaticRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_CycleBuyProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).CycleBuyProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/CycleBuyProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).CycleBuyProductList(ctx, req.(*CycleBuyProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_AddCycleBuyCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCycleBuyCollectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).AddCycleBuyCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/AddCycleBuyCollect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).AddCycleBuyCollect(ctx, req.(*AddCycleBuyCollectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_CycleBuyCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleBuyCollectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).CycleBuyCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/CycleBuyCollectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).CycleBuyCollectList(ctx, req.(*CycleBuyCollectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_DelCycleBuyCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCycleBuyCollectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).DelCycleBuyCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/DelCycleBuyCollect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).DelCycleBuyCollect(ctx, req.(*DelCycleBuyCollectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_CycleBuyCollectDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCycleBuyCollectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).CycleBuyCollectDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/CycleBuyCollectDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).CycleBuyCollectDetail(ctx, req.(*DelCycleBuyCollectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CycleBuyService_GetCycleOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CycleOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CycleBuyServiceServer).GetCycleOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.CycleBuyService/GetCycleOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CycleBuyServiceServer).GetCycleOrderList(ctx, req.(*CycleOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CycleBuyService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.CycleBuyService",
	HandlerType: (*CycleBuyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCycleBuyList",
			Handler:    _CycleBuyService_GetCycleBuyList_Handler,
		},
		{
			MethodName: "CreateCycleBuy",
			Handler:    _CycleBuyService_CreateCycleBuy_Handler,
		},
		{
			MethodName: "UpdateCycleBuy",
			Handler:    _CycleBuyService_UpdateCycleBuy_Handler,
		},
		{
			MethodName: "GetCycleBuyDetail",
			Handler:    _CycleBuyService_GetCycleBuyDetail_Handler,
		},
		{
			MethodName: "StopCycleBuy",
			Handler:    _CycleBuyService_StopCycleBuy_Handler,
		},
		{
			MethodName: "CycleBuyUnshipStatistics",
			Handler:    _CycleBuyService_CycleBuyUnshipStatistics_Handler,
		},
		{
			MethodName: "GetCycleBuyProductList",
			Handler:    _CycleBuyService_GetCycleBuyProductList_Handler,
		},
		{
			MethodName: "CreateCycleBuyProduct",
			Handler:    _CycleBuyService_CreateCycleBuyProduct_Handler,
		},
		{
			MethodName: "UpdateCycleBuyProduct",
			Handler:    _CycleBuyService_UpdateCycleBuyProduct_Handler,
		},
		{
			MethodName: "GetCycleBuyProductDetail",
			Handler:    _CycleBuyService_GetCycleBuyProductDetail_Handler,
		},
		{
			MethodName: "DeleteCycleBuyProduct",
			Handler:    _CycleBuyService_DeleteCycleBuyProduct_Handler,
		},
		{
			MethodName: "GetCycleBuyUPetProductSelectList",
			Handler:    _CycleBuyService_GetCycleBuyUPetProductSelectList_Handler,
		},
		{
			MethodName: "CycleBuyOrderStaticCallBack",
			Handler:    _CycleBuyService_CycleBuyOrderStaticCallBack_Handler,
		},
		{
			MethodName: "CycleBuyProductList",
			Handler:    _CycleBuyService_CycleBuyProductList_Handler,
		},
		{
			MethodName: "AddCycleBuyCollect",
			Handler:    _CycleBuyService_AddCycleBuyCollect_Handler,
		},
		{
			MethodName: "CycleBuyCollectList",
			Handler:    _CycleBuyService_CycleBuyCollectList_Handler,
		},
		{
			MethodName: "DelCycleBuyCollect",
			Handler:    _CycleBuyService_DelCycleBuyCollect_Handler,
		},
		{
			MethodName: "CycleBuyCollectDetail",
			Handler:    _CycleBuyService_CycleBuyCollectDetail_Handler,
		},
		{
			MethodName: "GetCycleOrderList",
			Handler:    _CycleBuyService_GetCycleOrderList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/cycle_buy_service.proto",
}
