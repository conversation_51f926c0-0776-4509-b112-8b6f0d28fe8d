// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/user_coin_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GiveCoinRequest struct {
	// Scrm用户ID
	ScrmId string `protobuf:"bytes,1,opt,name=ScrmId,proto3" json:"ScrmId"`
	// 用户手机号
	Mobile string `protobuf:"bytes,2,opt,name=Mobile,proto3" json:"Mobile"`
	// 赠送积分数量
	Num int32 `protobuf:"varint,3,opt,name=Num,proto3" json:"Num"`
	// 备注信息，填写赠送爱心币缘由，必填
	Remark               string   `protobuf:"bytes,4,opt,name=Remark,proto3" json:"Remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveCoinRequest) Reset()         { *m = GiveCoinRequest{} }
func (m *GiveCoinRequest) String() string { return proto.CompactTextString(m) }
func (*GiveCoinRequest) ProtoMessage()    {}
func (*GiveCoinRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae2548c29ae33027, []int{0}
}

func (m *GiveCoinRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveCoinRequest.Unmarshal(m, b)
}
func (m *GiveCoinRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveCoinRequest.Marshal(b, m, deterministic)
}
func (m *GiveCoinRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveCoinRequest.Merge(m, src)
}
func (m *GiveCoinRequest) XXX_Size() int {
	return xxx_messageInfo_GiveCoinRequest.Size(m)
}
func (m *GiveCoinRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveCoinRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GiveCoinRequest proto.InternalMessageInfo

func (m *GiveCoinRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *GiveCoinRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *GiveCoinRequest) GetNum() int32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *GiveCoinRequest) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func init() {
	proto.RegisterType((*GiveCoinRequest)(nil), "ac.GiveCoinRequest")
}

func init() { proto.RegisterFile("ac/user_coin_service.proto", fileDescriptor_ae2548c29ae33027) }

var fileDescriptor_ae2548c29ae33027 = []byte{
	// 204 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x4a, 0x4c, 0xd6, 0x2f,
	0x2d, 0x4e, 0x2d, 0x8a, 0x4f, 0xce, 0xcf, 0xcc, 0x8b, 0x2f, 0x4e, 0x2d, 0x2a, 0xcb, 0x4c, 0x4e,
	0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x4a, 0x4c, 0x96, 0x92, 0x4e, 0xcf, 0xcf, 0x4f,
	0xcf, 0x49, 0xd5, 0x07, 0x8b, 0x24, 0x95, 0xa6, 0xe9, 0xa7, 0xe6, 0x16, 0x94, 0x54, 0x42, 0x14,
	0x28, 0x65, 0x73, 0xf1, 0xbb, 0x67, 0x96, 0xa5, 0x3a, 0xe7, 0x67, 0xe6, 0x05, 0xa5, 0x16, 0x96,
	0xa6, 0x16, 0x97, 0x08, 0x89, 0x71, 0xb1, 0x05, 0x27, 0x17, 0xe5, 0x7a, 0xa6, 0x48, 0x30, 0x2a,
	0x30, 0x6a, 0x70, 0x06, 0x41, 0x79, 0x20, 0x71, 0xdf, 0xfc, 0xa4, 0xcc, 0x9c, 0x54, 0x09, 0x26,
	0x88, 0x38, 0x84, 0x27, 0x24, 0xc0, 0xc5, 0xec, 0x57, 0x9a, 0x2b, 0xc1, 0xac, 0xc0, 0xa8, 0xc1,
	0x1a, 0x04, 0x62, 0x82, 0x54, 0x06, 0xa5, 0xe6, 0x26, 0x16, 0x65, 0x4b, 0xb0, 0x40, 0x54, 0x42,
	0x78, 0x46, 0x5e, 0x5c, 0xfc, 0xa1, 0xc5, 0xa9, 0x45, 0x20, 0xcb, 0x82, 0x21, 0xce, 0x14, 0x32,
	0xe7, 0xe2, 0x80, 0xd9, 0x2f, 0x24, 0xac, 0x97, 0x98, 0xac, 0x87, 0xe6, 0x1a, 0x29, 0x31, 0x3d,
	0x88, 0xf3, 0xf5, 0x60, 0xce, 0xd7, 0x73, 0x05, 0x39, 0x3f, 0x89, 0x0d, 0xcc, 0x37, 0x06, 0x04,
	0x00, 0x00, 0xff, 0xff, 0x5e, 0x41, 0xe2, 0x4b, 0xfe, 0x00, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserCoinServiceClient is the client API for UserCoinService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserCoinServiceClient interface {
	// 赠送爱心币
	GiveCoin(ctx context.Context, in *GiveCoinRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type userCoinServiceClient struct {
	cc *grpc.ClientConn
}

func NewUserCoinServiceClient(cc *grpc.ClientConn) UserCoinServiceClient {
	return &userCoinServiceClient{cc}
}

func (c *userCoinServiceClient) GiveCoin(ctx context.Context, in *GiveCoinRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/ac.UserCoinService/GiveCoin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserCoinServiceServer is the server API for UserCoinService service.
type UserCoinServiceServer interface {
	// 赠送爱心币
	GiveCoin(context.Context, *GiveCoinRequest) (*empty.Empty, error)
}

// UnimplementedUserCoinServiceServer can be embedded to have forward compatible implementations.
type UnimplementedUserCoinServiceServer struct {
}

func (*UnimplementedUserCoinServiceServer) GiveCoin(ctx context.Context, req *GiveCoinRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiveCoin not implemented")
}

func RegisterUserCoinServiceServer(s *grpc.Server, srv UserCoinServiceServer) {
	s.RegisterService(&_UserCoinService_serviceDesc, srv)
}

func _UserCoinService_GiveCoin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveCoinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCoinServiceServer).GiveCoin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.UserCoinService/GiveCoin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCoinServiceServer).GiveCoin(ctx, req.(*GiveCoinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserCoinService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.UserCoinService",
	HandlerType: (*UserCoinServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GiveCoin",
			Handler:    _UserCoinService_GiveCoin_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/user_coin_service.proto",
}
