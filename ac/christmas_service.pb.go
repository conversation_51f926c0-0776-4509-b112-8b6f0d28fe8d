// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/christmas_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ChristmasEmptyRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChristmasEmptyRequest) Reset()         { *m = ChristmasEmptyRequest{} }
func (m *ChristmasEmptyRequest) String() string { return proto.CompactTextString(m) }
func (*ChristmasEmptyRequest) ProtoMessage()    {}
func (*ChristmasEmptyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{0}
}

func (m *ChristmasEmptyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasEmptyRequest.Unmarshal(m, b)
}
func (m *ChristmasEmptyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasEmptyRequest.Marshal(b, m, deterministic)
}
func (m *ChristmasEmptyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasEmptyRequest.Merge(m, src)
}
func (m *ChristmasEmptyRequest) XXX_Size() int {
	return xxx_messageInfo_ChristmasEmptyRequest.Size(m)
}
func (m *ChristmasEmptyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasEmptyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasEmptyRequest proto.InternalMessageInfo

type ChristmasResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChristmasResponse) Reset()         { *m = ChristmasResponse{} }
func (m *ChristmasResponse) String() string { return proto.CompactTextString(m) }
func (*ChristmasResponse) ProtoMessage()    {}
func (*ChristmasResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{1}
}

func (m *ChristmasResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasResponse.Unmarshal(m, b)
}
func (m *ChristmasResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasResponse.Marshal(b, m, deterministic)
}
func (m *ChristmasResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasResponse.Merge(m, src)
}
func (m *ChristmasResponse) XXX_Size() int {
	return xxx_messageInfo_ChristmasResponse.Size(m)
}
func (m *ChristmasResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasResponse proto.InternalMessageInfo

func (m *ChristmasResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChristmasResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 参加抽奖
type ChristmasJoinRequest struct {
	// 1摇一摇、2许愿
	Batch int32 `protobuf:"varint,1,opt,name=batch,proto3" json:"batch"`
	// 前端不需要传，仅用于rpc标记用户
	ScrmId               string   `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChristmasJoinRequest) Reset()         { *m = ChristmasJoinRequest{} }
func (m *ChristmasJoinRequest) String() string { return proto.CompactTextString(m) }
func (*ChristmasJoinRequest) ProtoMessage()    {}
func (*ChristmasJoinRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{2}
}

func (m *ChristmasJoinRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasJoinRequest.Unmarshal(m, b)
}
func (m *ChristmasJoinRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasJoinRequest.Marshal(b, m, deterministic)
}
func (m *ChristmasJoinRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasJoinRequest.Merge(m, src)
}
func (m *ChristmasJoinRequest) XXX_Size() int {
	return xxx_messageInfo_ChristmasJoinRequest.Size(m)
}
func (m *ChristmasJoinRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasJoinRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasJoinRequest proto.InternalMessageInfo

func (m *ChristmasJoinRequest) GetBatch() int32 {
	if m != nil {
		return m.Batch
	}
	return 0
}

func (m *ChristmasJoinRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type ChristmasJoinAwardData struct {
	// 奖品id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 奖品名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 奖品类型 1商城券、2门店券、3实物
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	// 奖品订单Id
	OrderId              int32    `protobuf:"varint,4,opt,name=orderId,proto3" json:"orderId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChristmasJoinAwardData) Reset()         { *m = ChristmasJoinAwardData{} }
func (m *ChristmasJoinAwardData) String() string { return proto.CompactTextString(m) }
func (*ChristmasJoinAwardData) ProtoMessage()    {}
func (*ChristmasJoinAwardData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{3}
}

func (m *ChristmasJoinAwardData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasJoinAwardData.Unmarshal(m, b)
}
func (m *ChristmasJoinAwardData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasJoinAwardData.Marshal(b, m, deterministic)
}
func (m *ChristmasJoinAwardData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasJoinAwardData.Merge(m, src)
}
func (m *ChristmasJoinAwardData) XXX_Size() int {
	return xxx_messageInfo_ChristmasJoinAwardData.Size(m)
}
func (m *ChristmasJoinAwardData) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasJoinAwardData.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasJoinAwardData proto.InternalMessageInfo

func (m *ChristmasJoinAwardData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChristmasJoinAwardData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChristmasJoinAwardData) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ChristmasJoinAwardData) GetOrderId() int32 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

// 参加抽奖返回信息
type ChristmasJoinResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 奖品信息
	Data                 *ChristmasJoinAwardData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ChristmasJoinResponse) Reset()         { *m = ChristmasJoinResponse{} }
func (m *ChristmasJoinResponse) String() string { return proto.CompactTextString(m) }
func (*ChristmasJoinResponse) ProtoMessage()    {}
func (*ChristmasJoinResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{4}
}

func (m *ChristmasJoinResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasJoinResponse.Unmarshal(m, b)
}
func (m *ChristmasJoinResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasJoinResponse.Marshal(b, m, deterministic)
}
func (m *ChristmasJoinResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasJoinResponse.Merge(m, src)
}
func (m *ChristmasJoinResponse) XXX_Size() int {
	return xxx_messageInfo_ChristmasJoinResponse.Size(m)
}
func (m *ChristmasJoinResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasJoinResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasJoinResponse proto.InternalMessageInfo

func (m *ChristmasJoinResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChristmasJoinResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ChristmasJoinResponse) GetData() *ChristmasJoinAwardData {
	if m != nil {
		return m.Data
	}
	return nil
}

type ChristmasOrdersRequest struct {
	// 前端不需要传，仅用于rpc标记用户
	ScrmId               string   `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChristmasOrdersRequest) Reset()         { *m = ChristmasOrdersRequest{} }
func (m *ChristmasOrdersRequest) String() string { return proto.CompactTextString(m) }
func (*ChristmasOrdersRequest) ProtoMessage()    {}
func (*ChristmasOrdersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{5}
}

func (m *ChristmasOrdersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasOrdersRequest.Unmarshal(m, b)
}
func (m *ChristmasOrdersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasOrdersRequest.Marshal(b, m, deterministic)
}
func (m *ChristmasOrdersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasOrdersRequest.Merge(m, src)
}
func (m *ChristmasOrdersRequest) XXX_Size() int {
	return xxx_messageInfo_ChristmasOrdersRequest.Size(m)
}
func (m *ChristmasOrdersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasOrdersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasOrdersRequest proto.InternalMessageInfo

func (m *ChristmasOrdersRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type ChristmasOrdersResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 订单详情
	Data                 []*ChristmasOrderData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ChristmasOrdersResponse) Reset()         { *m = ChristmasOrdersResponse{} }
func (m *ChristmasOrdersResponse) String() string { return proto.CompactTextString(m) }
func (*ChristmasOrdersResponse) ProtoMessage()    {}
func (*ChristmasOrdersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{6}
}

func (m *ChristmasOrdersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasOrdersResponse.Unmarshal(m, b)
}
func (m *ChristmasOrdersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasOrdersResponse.Marshal(b, m, deterministic)
}
func (m *ChristmasOrdersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasOrdersResponse.Merge(m, src)
}
func (m *ChristmasOrdersResponse) XXX_Size() int {
	return xxx_messageInfo_ChristmasOrdersResponse.Size(m)
}
func (m *ChristmasOrdersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasOrdersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasOrdersResponse proto.InternalMessageInfo

func (m *ChristmasOrdersResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChristmasOrdersResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ChristmasOrdersResponse) GetData() []*ChristmasOrderData {
	if m != nil {
		return m.Data
	}
	return nil
}

type ChristmasOrderData struct {
	// 订单id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 收件人姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 收件地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	// 收件号码
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	// 宠物类型 1猫  2狗
	PetType int32 `protobuf:"varint,5,opt,name=pet_type,json=petType,proto3" json:"pet_type"`
	// 奖品id
	AwardId int32 `protobuf:"varint,6,opt,name=award_id,json=awardId,proto3" json:"award_id"`
	// 奖品类型 1商城券、2门店券、3实物
	AwardType int32 `protobuf:"varint,7,opt,name=award_type,json=awardType,proto3" json:"award_type"`
	// 奖品名称
	AwardName string `protobuf:"bytes,8,opt,name=award_name,json=awardName,proto3" json:"award_name"`
	// 获取时间
	CreateTime           string   `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChristmasOrderData) Reset()         { *m = ChristmasOrderData{} }
func (m *ChristmasOrderData) String() string { return proto.CompactTextString(m) }
func (*ChristmasOrderData) ProtoMessage()    {}
func (*ChristmasOrderData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{7}
}

func (m *ChristmasOrderData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasOrderData.Unmarshal(m, b)
}
func (m *ChristmasOrderData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasOrderData.Marshal(b, m, deterministic)
}
func (m *ChristmasOrderData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasOrderData.Merge(m, src)
}
func (m *ChristmasOrderData) XXX_Size() int {
	return xxx_messageInfo_ChristmasOrderData.Size(m)
}
func (m *ChristmasOrderData) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasOrderData.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasOrderData proto.InternalMessageInfo

func (m *ChristmasOrderData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChristmasOrderData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChristmasOrderData) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *ChristmasOrderData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *ChristmasOrderData) GetPetType() int32 {
	if m != nil {
		return m.PetType
	}
	return 0
}

func (m *ChristmasOrderData) GetAwardId() int32 {
	if m != nil {
		return m.AwardId
	}
	return 0
}

func (m *ChristmasOrderData) GetAwardType() int32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *ChristmasOrderData) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *ChristmasOrderData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

type ChristmasRecentAwardData struct {
	// 手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	// 礼品名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 数量
	Qty                  int32    `protobuf:"varint,3,opt,name=qty,proto3" json:"qty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChristmasRecentAwardData) Reset()         { *m = ChristmasRecentAwardData{} }
func (m *ChristmasRecentAwardData) String() string { return proto.CompactTextString(m) }
func (*ChristmasRecentAwardData) ProtoMessage()    {}
func (*ChristmasRecentAwardData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{8}
}

func (m *ChristmasRecentAwardData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasRecentAwardData.Unmarshal(m, b)
}
func (m *ChristmasRecentAwardData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasRecentAwardData.Marshal(b, m, deterministic)
}
func (m *ChristmasRecentAwardData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasRecentAwardData.Merge(m, src)
}
func (m *ChristmasRecentAwardData) XXX_Size() int {
	return xxx_messageInfo_ChristmasRecentAwardData.Size(m)
}
func (m *ChristmasRecentAwardData) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasRecentAwardData.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasRecentAwardData proto.InternalMessageInfo

func (m *ChristmasRecentAwardData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *ChristmasRecentAwardData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChristmasRecentAwardData) GetQty() int32 {
	if m != nil {
		return m.Qty
	}
	return 0
}

type ChristmasRecentAwardsResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 最近获的礼品信息
	Data                 []*ChristmasRecentAwardData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *ChristmasRecentAwardsResponse) Reset()         { *m = ChristmasRecentAwardsResponse{} }
func (m *ChristmasRecentAwardsResponse) String() string { return proto.CompactTextString(m) }
func (*ChristmasRecentAwardsResponse) ProtoMessage()    {}
func (*ChristmasRecentAwardsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0db8c263a35bf7e0, []int{9}
}

func (m *ChristmasRecentAwardsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasRecentAwardsResponse.Unmarshal(m, b)
}
func (m *ChristmasRecentAwardsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasRecentAwardsResponse.Marshal(b, m, deterministic)
}
func (m *ChristmasRecentAwardsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasRecentAwardsResponse.Merge(m, src)
}
func (m *ChristmasRecentAwardsResponse) XXX_Size() int {
	return xxx_messageInfo_ChristmasRecentAwardsResponse.Size(m)
}
func (m *ChristmasRecentAwardsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasRecentAwardsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasRecentAwardsResponse proto.InternalMessageInfo

func (m *ChristmasRecentAwardsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChristmasRecentAwardsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ChristmasRecentAwardsResponse) GetData() []*ChristmasRecentAwardData {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*ChristmasEmptyRequest)(nil), "ac.ChristmasEmptyRequest")
	proto.RegisterType((*ChristmasResponse)(nil), "ac.ChristmasResponse")
	proto.RegisterType((*ChristmasJoinRequest)(nil), "ac.ChristmasJoinRequest")
	proto.RegisterType((*ChristmasJoinAwardData)(nil), "ac.ChristmasJoinAwardData")
	proto.RegisterType((*ChristmasJoinResponse)(nil), "ac.ChristmasJoinResponse")
	proto.RegisterType((*ChristmasOrdersRequest)(nil), "ac.ChristmasOrdersRequest")
	proto.RegisterType((*ChristmasOrdersResponse)(nil), "ac.ChristmasOrdersResponse")
	proto.RegisterType((*ChristmasOrderData)(nil), "ac.ChristmasOrderData")
	proto.RegisterType((*ChristmasRecentAwardData)(nil), "ac.ChristmasRecentAwardData")
	proto.RegisterType((*ChristmasRecentAwardsResponse)(nil), "ac.ChristmasRecentAwardsResponse")
}

func init() { proto.RegisterFile("ac/christmas_service.proto", fileDescriptor_0db8c263a35bf7e0) }

var fileDescriptor_0db8c263a35bf7e0 = []byte{
	// 507 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x54, 0x4d, 0x8f, 0xd3, 0x30,
	0x10, 0x55, 0xfa, 0x9d, 0x29, 0x42, 0x8b, 0xb5, 0xb4, 0x69, 0x60, 0xc5, 0x92, 0x53, 0xc5, 0xa1,
	0x40, 0x39, 0x72, 0xaa, 0x60, 0x0f, 0x45, 0x08, 0xa4, 0xb0, 0x07, 0x6e, 0x95, 0x6b, 0x8f, 0xd8,
	0x20, 0xd2, 0x64, 0x63, 0x2f, 0xa8, 0x27, 0xfe, 0x2a, 0xff, 0x04, 0xe4, 0xb1, 0x93, 0x4d, 0x68,
	0x56, 0x5a, 0xf5, 0xe6, 0xf1, 0x8b, 0x9f, 0xdf, 0xcc, 0x7b, 0x31, 0x84, 0x5c, 0xbc, 0x14, 0x57,
	0x45, 0xa2, 0x74, 0xca, 0xd5, 0x46, 0x61, 0xf1, 0x33, 0x11, 0xb8, 0xc8, 0x8b, 0x4c, 0x67, 0xac,
	0xc3, 0x45, 0x34, 0x85, 0xc7, 0xef, 0x4a, 0xf8, 0x22, 0xcd, 0xf5, 0x3e, 0xc6, 0xeb, 0x1b, 0x54,
	0x3a, 0x5a, 0xc1, 0xa3, 0x0a, 0x88, 0x51, 0xe5, 0xd9, 0x4e, 0x21, 0x63, 0xd0, 0x13, 0x99, 0xc4,
	0xc0, 0x3b, 0xf7, 0xe6, 0xfd, 0x98, 0xd6, 0x2c, 0x80, 0x61, 0x8a, 0x4a, 0xf1, 0x6f, 0x18, 0x74,
	0xce, 0xbd, 0xb9, 0x1f, 0x97, 0x65, 0x74, 0x01, 0xa7, 0x15, 0xc5, 0x87, 0x2c, 0xd9, 0x39, 0x6a,
	0x76, 0x0a, 0xfd, 0x2d, 0xd7, 0xe2, 0xca, 0xd1, 0xd8, 0x82, 0x4d, 0x61, 0xa8, 0x44, 0x91, 0x6e,
	0x12, 0xe9, 0x78, 0x06, 0xa6, 0x5c, 0xcb, 0xe8, 0x3b, 0x4c, 0x1a, 0x34, 0xab, 0x5f, 0xbc, 0x90,
	0xef, 0xb9, 0xe6, 0xec, 0x21, 0x74, 0x12, 0xe9, 0x58, 0x3a, 0x89, 0x34, 0xf2, 0x76, 0x3c, 0x2d,
	0x75, 0xd0, 0xda, 0xec, 0xe9, 0x7d, 0x8e, 0x41, 0xd7, 0x4a, 0x36, 0x6b, 0x23, 0x39, 0x2b, 0x24,
	0x16, 0x6b, 0x19, 0xf4, 0x68, 0xbb, 0x2c, 0xa3, 0x9b, 0xda, 0x38, 0xac, 0xe4, 0x63, 0x3a, 0x67,
	0x0b, 0xe8, 0x49, 0xae, 0x39, 0x5d, 0x3a, 0x5e, 0x86, 0x0b, 0x2e, 0x16, 0xed, 0x2d, 0xc4, 0xf4,
	0x5d, 0xf4, 0xba, 0xd6, 0xe2, 0x67, 0x23, 0x45, 0x95, 0xb3, 0xba, 0x73, 0x2a, 0x0a, 0xa6, 0x07,
	0x47, 0x8e, 0xd2, 0xfa, 0xa2, 0xd2, 0xda, 0x9d, 0x8f, 0x97, 0x93, 0x86, 0x56, 0x22, 0xae, 0xe9,
	0xfc, 0xeb, 0x01, 0x3b, 0x04, 0xef, 0xe5, 0x43, 0x00, 0x43, 0x2e, 0x65, 0x81, 0x4a, 0xd1, 0x54,
	0xfc, 0xb8, 0x2c, 0xd9, 0x04, 0x06, 0x69, 0xb6, 0x4d, 0x7e, 0x20, 0x99, 0xe1, 0xc7, 0xae, 0x62,
	0x33, 0x18, 0xe5, 0xa8, 0x37, 0xe4, 0x5e, 0xdf, 0xda, 0x94, 0xa3, 0xbe, 0x34, 0x06, 0xce, 0x60,
	0xc4, 0xcd, 0x08, 0xcd, 0x58, 0x06, 0x16, 0xa2, 0x7a, 0x2d, 0xd9, 0x19, 0x80, 0x85, 0xe8, 0xdc,
	0x90, 0x40, 0x9f, 0x76, 0xe8, 0x64, 0x05, 0x93, 0xc0, 0x11, 0x5d, 0x68, 0xe1, 0x4f, 0x46, 0xe5,
	0x33, 0x18, 0x8b, 0x02, 0xb9, 0xc6, 0x8d, 0x4e, 0x52, 0x0c, 0x7c, 0xc2, 0xc1, 0x6e, 0x5d, 0x26,
	0x29, 0x46, 0x5f, 0x21, 0xa8, 0xfd, 0x16, 0x02, 0x77, 0xfa, 0x36, 0x8e, 0xb7, 0x8d, 0x78, 0x8d,
	0x46, 0xda, 0xc6, 0x71, 0x02, 0xdd, 0x6b, 0xbd, 0x77, 0xa9, 0x34, 0xcb, 0xe8, 0x37, 0x9c, 0xb5,
	0x31, 0x1f, 0x6b, 0xeb, 0xab, 0x86, 0xad, 0x4f, 0x1b, 0xb6, 0xfe, 0x27, 0xdc, 0x9a, 0xbb, 0xfc,
	0xe3, 0xc1, 0x49, 0xf5, 0xc9, 0x17, 0xfb, 0x52, 0xb0, 0xb7, 0xd0, 0x33, 0x81, 0x65, 0xc1, 0x41,
	0x86, 0x5d, 0x42, 0xc3, 0x59, 0x0b, 0xe2, 0x14, 0xaf, 0x60, 0x60, 0xa3, 0xc9, 0xc2, 0xc3, 0x58,
	0x95, 0x11, 0x0f, 0x9f, 0xb4, 0x62, 0x8e, 0xe2, 0x23, 0x3c, 0xa8, 0x0f, 0x83, 0x35, 0x6f, 0xab,
	0xbf, 0x58, 0xe1, 0xf3, 0xbb, 0x7a, 0xac, 0xd8, 0xb6, 0x03, 0x7a, 0xf8, 0xde, 0xfc, 0x0b, 0x00,
	0x00, 0xff, 0xff, 0x6f, 0xb3, 0x25, 0x83, 0x16, 0x05, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChristmasServiceClient is the client API for ChristmasService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChristmasServiceClient interface {
	// 参加抽奖
	Join(ctx context.Context, in *ChristmasJoinRequest, opts ...grpc.CallOption) (*ChristmasJoinResponse, error)
	// 订单列表
	Orders(ctx context.Context, in *ChristmasOrdersRequest, opts ...grpc.CallOption) (*ChristmasOrdersResponse, error)
	// 最近礼品列表
	RecentAwards(ctx context.Context, in *ChristmasEmptyRequest, opts ...grpc.CallOption) (*ChristmasRecentAwardsResponse, error)
}

type christmasServiceClient struct {
	cc *grpc.ClientConn
}

func NewChristmasServiceClient(cc *grpc.ClientConn) ChristmasServiceClient {
	return &christmasServiceClient{cc}
}

func (c *christmasServiceClient) Join(ctx context.Context, in *ChristmasJoinRequest, opts ...grpc.CallOption) (*ChristmasJoinResponse, error) {
	out := new(ChristmasJoinResponse)
	err := c.cc.Invoke(ctx, "/ac.ChristmasService/Join", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *christmasServiceClient) Orders(ctx context.Context, in *ChristmasOrdersRequest, opts ...grpc.CallOption) (*ChristmasOrdersResponse, error) {
	out := new(ChristmasOrdersResponse)
	err := c.cc.Invoke(ctx, "/ac.ChristmasService/Orders", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *christmasServiceClient) RecentAwards(ctx context.Context, in *ChristmasEmptyRequest, opts ...grpc.CallOption) (*ChristmasRecentAwardsResponse, error) {
	out := new(ChristmasRecentAwardsResponse)
	err := c.cc.Invoke(ctx, "/ac.ChristmasService/RecentAwards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChristmasServiceServer is the server API for ChristmasService service.
type ChristmasServiceServer interface {
	// 参加抽奖
	Join(context.Context, *ChristmasJoinRequest) (*ChristmasJoinResponse, error)
	// 订单列表
	Orders(context.Context, *ChristmasOrdersRequest) (*ChristmasOrdersResponse, error)
	// 最近礼品列表
	RecentAwards(context.Context, *ChristmasEmptyRequest) (*ChristmasRecentAwardsResponse, error)
}

// UnimplementedChristmasServiceServer can be embedded to have forward compatible implementations.
type UnimplementedChristmasServiceServer struct {
}

func (*UnimplementedChristmasServiceServer) Join(ctx context.Context, req *ChristmasJoinRequest) (*ChristmasJoinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Join not implemented")
}
func (*UnimplementedChristmasServiceServer) Orders(ctx context.Context, req *ChristmasOrdersRequest) (*ChristmasOrdersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Orders not implemented")
}
func (*UnimplementedChristmasServiceServer) RecentAwards(ctx context.Context, req *ChristmasEmptyRequest) (*ChristmasRecentAwardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecentAwards not implemented")
}

func RegisterChristmasServiceServer(s *grpc.Server, srv ChristmasServiceServer) {
	s.RegisterService(&_ChristmasService_serviceDesc, srv)
}

func _ChristmasService_Join_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChristmasJoinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChristmasServiceServer).Join(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ChristmasService/Join",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChristmasServiceServer).Join(ctx, req.(*ChristmasJoinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChristmasService_Orders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChristmasOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChristmasServiceServer).Orders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ChristmasService/Orders",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChristmasServiceServer).Orders(ctx, req.(*ChristmasOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChristmasService_RecentAwards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChristmasEmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChristmasServiceServer).RecentAwards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.ChristmasService/RecentAwards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChristmasServiceServer).RecentAwards(ctx, req.(*ChristmasEmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChristmasService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.ChristmasService",
	HandlerType: (*ChristmasServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Join",
			Handler:    _ChristmasService_Join_Handler,
		},
		{
			MethodName: "Orders",
			Handler:    _ChristmasService_Orders_Handler,
		},
		{
			MethodName: "RecentAwards",
			Handler:    _ChristmasService_RecentAwards_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/christmas_service.proto",
}
