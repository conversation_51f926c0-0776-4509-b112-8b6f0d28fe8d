syntax = "proto3";

package ac;

import "ac/activity_model.proto";
import "ac/cycle_buy_service.proto";

service BookBuyService {
  //获取预售商品商品列表 boss
  rpc GetBookBuyProductList (GetBookBuyProductListRequest) returns (GetBookBuyProductListResponse);
  //创建预售商品商品
  rpc CreateBookBuyProduct (CreateBookBuyProductRequest) returns (baseResponse);
  //更新预售商品商品
  rpc UpdateBookBuyProduct (UpdateBookBuyProductRequest) returns (baseResponse);
  //获取预售商品商品详情
  rpc GetBookBuyProductDetail(BookBuyProductDetailRequest) returns (GetBookBuyProductDetailResponse);
  //删除预售商品商品
  rpc DeleteBookBuyProduct(BookBuyProductIdRequest) returns (baseResponse);
  // 获取可以参加预售商品活动的阿闻电商渠道的商品
  rpc GetBookBuyUPetProductSelectList(GetBookBuyUPetProductSelectListRequest) returns (GetBookBuyUPetProductSelectListResponse);
  // 获取预售商品列表 小程序
  rpc GetBookBuyProductListForCustom (GetBookBuyProductListRequest) returns (BookBuyProductCustomListResponse);
}

//预售商品商品列表的请求数据
message GetBookBuyProductListRequest {
  //产品名称
  string productName = 2;
  //商品sku id
  int32 skuId = 3;
  //商品的产品id
  int32 productId = 4;
  //商品的产品id
  int32 ChannelId = 5;
  //商品状态 -1全部 -4审核失败，-3:待提交，-2:待审核/下架，1=>进行中，2=>已结束...
  int32 Status = 6;
  //排序 0 按商品排序设置排序，1；按真实订单数排序 默认为0
  int32 orderBy = 7;
  //分页参数
  PaginationParam pagination = 8;
  //是否导出 1导出
  int32 export =9;
  //主体：1-阿闻，2-极宠家
  int32 org_id =10;
}

//预售商品商品列表
message GetBookBuyProductListResponse {
  //响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  //不成功的错误信息
  string message = 2;
  //错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //预售商品商品信息
  repeated BookBuyProductData data = 5;
}

//预售商品商品数据
message BookBuyProductData {
  //活动商品信息记录id
  int32 id = 1;
  //渠道ID
  int32 channelId = 2;
  //商品sku id
  int32 skuId = 3;
  //商品的产品id
  int32 productId = 4;
  //商品名称
  string productName = 5;
  //单买价 单位分
  int32 Price = 6;
  //预售价 单位分
  int32 bookPrice = 7;
  //定金 单位分
  int32 deposit = 8;
  //定金开始时间
  string depositBeginDate = 9;
  //定金结束时间
  string depositEndDate = 10;
  //尾款开始时间
  string restBeginDate = 11;
  //尾款结束时间
  string restEndDate = 12;
  //状态 -1删除 0默认
  int32 status = 13;
  // 是否可被编辑 0 不可编辑 1 可编辑 用于boss后台
  int32 canBeEdited = 14;
  // 是否可被删除 0 不可删除 1 可删除 用于boss后台
  int32 canBeDeleted = 15;
  // 商品图片
  string pic = 16;
  // 商品库存
  int32 stock = 17;
  // 是否是虚拟商品 1是 0 否
  int32 isVirtual = 18;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 19;
  //创建时间
  string createTime = 20;
  //更新时间
  string updateTime = 21;
  //是否免邮费 0否1是
  int32 is_shipping_free = 22;
  // 虚拟库存，如果没开启会返回-，否则就是具体的库存
  string virtual_storage = 23;
  //商品折扣率
  double priceRatio = 24;
  // 是否异常 1:异常 0：正常
  int32 is_normal = 25;
  // 是否标记 1:是 0：否
  int32 is_mark = 26;
  // 采购价格
  int32 purchase_price = 27;
  // 标记为正常的异常折扣
  double MarkDiscount = 28;
  // 标记为正常的采购价(分)
  int32 MarkPurchasePrice = 29;
  // 审核原因
  string check_reason = 30;
}
//新增预售商品活动商品
message CreateBookBuyProductRequest {
  //商品sku id
  int32 skuId = 1;
  //商品的产品id
  int32 productId = 2;
  //用户Id，即userno
  string userId = 3;
  //用户名称，即登录人姓名
  string userName = 4;
  //主体信息
  SaveBookBuyProductData saveData = 5;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 6;
}

//添加/编辑预售商品商品
message SaveBookBuyProductData {
  //活动商品信息记录id
  int32 id = 1;
  //预售价 单位分
  int32 bookPrice = 7;
  //定金 单位分
  int32 deposit = 8;
  //定金开始时间
  string depositBeginDate = 9;
  //定金结束时间
  string depositEndDate = 10;
  //尾款开始时间
  string restBeginDate = 11;
  //尾款结束时间
  string restEndDate = 12;
  // 是否包邮
  int32 isShippingFree = 13;
}

// 更新预售商品商品
message UpdateBookBuyProductRequest {
  //需要更新的记录id
  int32 id = 1 ;
  //商品sku id
  int32 skuId = 2;
  //商品的产品id
  int32 productId = 3;
  //用户Id，即userno
  string userId = 4;
  //用户名称，即登录人姓名
  string userName = 5;
  //主体信息
  SaveBookBuyProductData saveData = 6;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 7;
}

// 预售商品商品只需要id的请求
message BookBuyProductDetailRequest {
  //活动商品信息记录id
  int32 id = 1;
  //商品skuId
  int32 skuId = 3;
  //商品的产品id
  int32 productId = 4;
  //渠道 1电商 5商城
  int32 channelId = 5;
  //主体：1-阿闻，2-极宠家
  int32 org_id = 6;
}

//获取预售商品商品信息
message GetBookBuyProductDetailResponse {
  //响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  //不成功的错误信息
  string message = 2;
  //错误信息
  string error = 3;
  //预售商品商品信息
  BookBuyProductDetailData data = 4;
}

//预售商品商品详细数据包含部分预售商品信息
message BookBuyProductDetailData {
  //活动商品信息记录id
  int32 id = 1;
  //渠道ID
  int32 channelId = 3;
  //商品sku id
  int32 skuId = 4;
  //商品的产品id
  int32 productId = 5;
  //商品名称
  string productName = 6;
  //预售价 单位分
  int32 bookPrice = 7;
  //定金 单位分
  int32 deposit = 8;
  //定金开始时间
  string depositBeginDate = 9;
  //定金结束时间
  string depositEndDate = 10;
  //尾款开始时间
  string restBeginDate = 11;
  //尾款结束时间
  string restEndDate = 12;
  //状态 -1删除 0默认
  int32 status = 13;
  //创建时间
  string createTime = 14;
  //更新时间
  string updateTime = 15;
  // 是否是虚拟商品 1是 0 否
  int32 isVirtual = 16;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 17;
  //R1集采价
  int32 R1PurchasePrice = 18;
  // 折扣率
  double priceRatio = 19;
}
//预售商品商品只需要id的请求
message BookBuyProductIdRequest {
  //活动商品信息记录id
  int32 id = 1;
  //用户Id，即userno
  string userId = 2;
  //用户名称，即登录人姓名
  string userName = 3;
    //主体：1-阿闻，2-极宠家
    int32 org_id = 4;
}
//阿闻电商参加预售商品活动的商品
message GetBookBuyUPetProductSelectListRequest {
  //商品sku_id 对应商城的goods_id
  int32 skuId = 2;
  // 商品的产品id 对应商城的goods_commonid
  int32 productId = 3;
  // 商品名称
  string productName = 4;
  //分页参数
  PaginationParam pagination = 5;
  	 //主体：1-阿闻，2-极宠家
     int32 org_id = 6;
}
//阿闻电商参加预售商品活动的商品
message GetBookBuyUPetProductSelectListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //预售商品商品信息
  repeated BookBuySelectUPetProductData data = 5;
}

message BookBuySelectUPetProductData {
  //商品sku_id 对应商城的goods_id
  int32 skuId = 1;
  // 商品的产品id 对应商城的goods_commonid
  int32 productId = 2;
  // 商品名称
  string productName = 3;
  // 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
  int32 timeConflict = 4;
  //商品图片
  string pic = 5;
  //库存
  int32 stock = 6;
  //价格 单位分
  int32 marketPrice = 7;
  // 采购价、分
  int32 purchase_price = 11;
  //是否时虚拟产品 1是 0 否
  int32 isVirtual = 8;
  //商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
  int32 goodsType = 9;
  //组合商品的子商品信息
  repeated Child childSkuIds = 10;
}

//团购商品列表
message BookBuyProductCustomListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //总的条数
  int32 total = 4;
  //预售商品信息
  repeated BookBuyProductCustomList data = 5;
}

//预售商品列表客户端返回数据
message BookBuyProductCustomList {
  //活动商品信息记录id
  int32 id = 1;
  // 预售价 单位分
  int32 bookPrice = 3;
  //商品sku id
  int32 skuId = 4;
  //商品的产品id
  int32 productId = 5;
  // 渠道id
  int32 channelId = 6;
  // 市场价格 单位分
  int32 marketPrice = 7;
  // 商品图片
  string pic = 8;
  //商品名称
  string productName = 9;
}