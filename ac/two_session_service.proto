syntax = "proto3";

package ac;
import "ac/activity_model.proto";

service TwoSessoinService{
  // 支持人大代表
  rpc UpRepresentative(UpRepresentativeReq) returns (BaseResponseNew);
  // 分享人大代表
  rpc ShareRepresentative(UpRepresentativeReq) returns (BaseResponseNew);
  // 获取人大代表支持人数
  rpc RepresentativeNumber(UpRepresentativeReq) returns (RepresentativeNumberRes);
  // 获取抽奖状态
  rpc LotteryStatus(UpRepresentativeReq) returns (LotteryStatusRes);
  // 获取结果上报
  rpc LotteryReporte(LotteryReporteReq) returns (BaseResponseNew);

  // 提交建议
  rpc SubmitProposal(SubmitProposalRequest) returns (SubmitProposalResponse);
  // 支持建议
  rpc SupportProposal(BaseProposalRequest) returns (BaseResponseNew);
  // 获取建议列表
  rpc GetProposalList(GetProposalListRequest) returns (GetProposalListResponse);
  // 获取单条建议
  rpc GetProposal(BaseProposalRequest) returns (Proposal);

}

message UpRepresentativeReq {
  //人大代表ID
  int32 id = 1;
  string scrm_id = 2;
}

message RepresentativeNumberRes {
  //人大代表支持人数
  int32 number = 1;
}

message LotteryStatusRes {
  int32 is_up = 1;
  int32 is_share = 2;
  int32 lottery_num = 3;
}

message LotteryReporteReq {
  //人大代表ID
  int32 representative_id = 1;
  string scrm_id = 2;
  int32 lottery_id = 3;
}