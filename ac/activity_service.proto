syntax = "proto3";

package ac;

import "ac/activity_model.proto";

service ActivityService {
    //打开宝箱
    rpc OpenTreasure (petTeaseRequest) returns (PetTeaseResponse);
    //获取奖励列表
    rpc GetPetTeaseList (baseRequest) returns (petTeaseListResponse);
    //调用电商发送优惠卷
    rpc SendCoupon(SendCouponReq) returns (SendCouponRes);
    //520爱猫日-获取题目列表
    rpc GetExamineList (baseRequest) returns (examineListResponse);
    //保存助力分享信息
    rpc SaveShare(SaveShareReq) returns(baseResponse);
    //520爱猫日-提交答案
    rpc SaveExamineAnswer (SaveExamineAnswerReq) returns (SaveExamineAnswerRes);

    //获取用户520爱猫日所有相关信息
    rpc GetMemberShare(GetMemberShareReq) returns(GetMemberShareRes);

    //获取优惠卷模板列表
    rpc GetCouponTemplate(GetCouponTemplateReq) returns(GetCouponTemplateRes);

    //保存门店优惠卷
    rpc SaveMemberCoupon(SaveMemberCouponReq)returns(baseResponse);
    //获取门店优惠卷信息
    rpc GetMemberCoupon(GetMemberCouponReq)returns(GetMemberCouponRes);

    //保存订阅消息
    rpc SaveSubscribeMessage(SaveSubscribeMessageReq)returns(baseResponse);

    //取消订阅消息
    rpc CancelSubscribeMessage(CancelSubscribeMessageReq)returns(baseResponse);

    //获取优惠券订阅消息
    rpc GetCouponMessage(baseRequest)returns(CouponMessageRes);

    //获取爱猫月活动信息
    rpc GetCatMonthInfo(baseRequest)returns(GetCatMonthInfoRes);

    //保存爱猫月活动信息
    rpc SaveCatMonthInfo(SaveCatMonthInfoReq)returns(baseResponse);

    //添加或编辑水印
    rpc SaveWatermark(SaveWatermarkReq)returns(SaveWatermarkRes);

    //获取水印列表
    rpc WatermarkList(WatermarkListReq)returns(WatermarkListRes);

    //获取水印详情
    rpc GetWatermark(GetWatermarkReq)returns(GetWatermarkRes);

    //获取水印关联的商品列表
    rpc WatermarkGoodsList(GoodsListReq)returns(GoodsListRes);

    //删除水印商品
    rpc WatermarkGoodsDelete(GoodsDeleteReq)returns(baseResponse);

    //添加水印商品
    rpc WatermarkGoodsAdd(GoodsAddReq)returns(baseResponse);

    //添加水印商品时弹窗中的商品列表
    rpc SearchGoods(SearchGoodsReq)returns(SearchGoodsRes);

    //修改水印商品价格
    rpc WatermarkGoodsPrice(GoodsPriceReq)returns(baseResponse);

    //终止水印
    rpc StopWatermark(StopWatermarkReq)returns(baseResponse);

    //批量导入水印商品
    rpc WatermarkGoodsImport(GoodsImportReq)returns(GoodsImportRes);

    //双旦活动添加修改收件地址
    rpc ChristmasAddAddress(ChristmasAddAddressReq)returns(BaseResponseNew);

    //双旦活动渠道统计
    rpc ChannelStatistics(ChannelStatisticsReq)returns(BaseResponseNew);

    rpc HandOutCoupon(HandOutCouponRequest)returns(BaseResponseNew);

    rpc HandOutCouponData(HandOutCouponRequest)returns(HandOutCouponResponse);

    // 宠物集市活动-获取用户领取的优惠券
    rpc GetPetMarketUserCoupon(GetPetMarketUserCouponReq)returns(GetPetMarketUserCouponResponse);
    // 宠物集市活动-新增用户完成任务
    rpc GetPetMarketTasks(GetPetMarketTasksReq)returns(GetPetMarketTasksResponse);
    // 宠物集市活动-新增用户完成任务
    rpc AddPetMarketUserTask(AddPetMarketUserTaskReq)returns(baseResponse);
    // 宠物集市活动-获取用户完成的任务
    rpc GetPetMarketUserTasks(GetPetMarketUserTasksReq)returns(GetPetMarketUserTasksResponse);
    // 宠物集市活动-领取奖励
    rpc AddPetMarketReward(AddPetMarketRewardReq)returns(baseResponse);
    // 宠物集市活动-查询领取记录
    rpc GetPetMarketUserReward(GetPetMarketUserRewardReq)returns(GetPetMarketUserRewardResponse);
    // 统计奖品领取数
    rpc CountPetMarketReward(CountPetMarketRewardReq)returns(CountPetMarketRewardReqResponse);
    // 统计用户某时间段内是否购买过指定商品
    rpc CountUserOrderWithSkuids(CountUserOrderWithSkuidsRequest) returns (CountUserOrderWithSkuidsResponse);
}