// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/activity_model.proto

package ac

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 基础请求
type BaseRequest struct {
	//手机号
	Mobile               string   `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRequest) Reset()         { *m = BaseRequest{} }
func (m *BaseRequest) String() string { return proto.CompactTextString(m) }
func (*BaseRequest) ProtoMessage()    {}
func (*BaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{0}
}

func (m *BaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRequest.Unmarshal(m, b)
}
func (m *BaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRequest.Marshal(b, m, deterministic)
}
func (m *BaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRequest.Merge(m, src)
}
func (m *BaseRequest) XXX_Size() int {
	return xxx_messageInfo_BaseRequest.Size(m)
}
func (m *BaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRequest proto.InternalMessageInfo

func (m *BaseRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

type BaseResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{1}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type PetTeaseRequest struct {
	//开启人手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//打开的宝箱类型 1：铜 2：银 3：金 4：--
	TreasureLevel        int32    `protobuf:"varint,2,opt,name=treasure_level,json=treasureLevel,proto3" json:"treasure_level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTeaseRequest) Reset()         { *m = PetTeaseRequest{} }
func (m *PetTeaseRequest) String() string { return proto.CompactTextString(m) }
func (*PetTeaseRequest) ProtoMessage()    {}
func (*PetTeaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{2}
}

func (m *PetTeaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTeaseRequest.Unmarshal(m, b)
}
func (m *PetTeaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTeaseRequest.Marshal(b, m, deterministic)
}
func (m *PetTeaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTeaseRequest.Merge(m, src)
}
func (m *PetTeaseRequest) XXX_Size() int {
	return xxx_messageInfo_PetTeaseRequest.Size(m)
}
func (m *PetTeaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTeaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetTeaseRequest proto.InternalMessageInfo

func (m *PetTeaseRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *PetTeaseRequest) GetTreasureLevel() int32 {
	if m != nil {
		return m.TreasureLevel
	}
	return 0
}

type PetTeaseResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//是否已经打开过 1：已经打开过 0：没打开过
	IsOpened int32 `protobuf:"varint,4,opt,name=is_opened,json=isOpened,proto3" json:"is_opened"`
	// 内容
	Data                 *PetTease `protobuf:"bytes,5,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *PetTeaseResponse) Reset()         { *m = PetTeaseResponse{} }
func (m *PetTeaseResponse) String() string { return proto.CompactTextString(m) }
func (*PetTeaseResponse) ProtoMessage()    {}
func (*PetTeaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{3}
}

func (m *PetTeaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTeaseResponse.Unmarshal(m, b)
}
func (m *PetTeaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTeaseResponse.Marshal(b, m, deterministic)
}
func (m *PetTeaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTeaseResponse.Merge(m, src)
}
func (m *PetTeaseResponse) XXX_Size() int {
	return xxx_messageInfo_PetTeaseResponse.Size(m)
}
func (m *PetTeaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTeaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetTeaseResponse proto.InternalMessageInfo

func (m *PetTeaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetTeaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PetTeaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *PetTeaseResponse) GetIsOpened() int32 {
	if m != nil {
		return m.IsOpened
	}
	return 0
}

func (m *PetTeaseResponse) GetData() *PetTease {
	if m != nil {
		return m.Data
	}
	return nil
}

type PetTeaseListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	// 内容
	Data                 []*PetTease `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *PetTeaseListResponse) Reset()         { *m = PetTeaseListResponse{} }
func (m *PetTeaseListResponse) String() string { return proto.CompactTextString(m) }
func (*PetTeaseListResponse) ProtoMessage()    {}
func (*PetTeaseListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{4}
}

func (m *PetTeaseListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTeaseListResponse.Unmarshal(m, b)
}
func (m *PetTeaseListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTeaseListResponse.Marshal(b, m, deterministic)
}
func (m *PetTeaseListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTeaseListResponse.Merge(m, src)
}
func (m *PetTeaseListResponse) XXX_Size() int {
	return xxx_messageInfo_PetTeaseListResponse.Size(m)
}
func (m *PetTeaseListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTeaseListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetTeaseListResponse proto.InternalMessageInfo

func (m *PetTeaseListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetTeaseListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PetTeaseListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *PetTeaseListResponse) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *PetTeaseListResponse) GetData() []*PetTease {
	if m != nil {
		return m.Data
	}
	return nil
}

type PetTease struct {
	//手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//宝箱层级
	TreasureLevel int32 `protobuf:"varint,2,opt,name=treasure_level,json=treasureLevel,proto3" json:"treasure_level"`
	//生效时间
	BeginTime string `protobuf:"bytes,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	//结束时间
	EndTime string `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//创建时间
	CreateTime string `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//满足金额
	ReachMoney string `protobuf:"bytes,6,opt,name=reach_money,json=reachMoney,proto3" json:"reach_money"`
	//减免金额
	ReduceMoney string `protobuf:"bytes,7,opt,name=reduce_money,json=reduceMoney,proto3" json:"reduce_money"`
	//优惠券名称
	CouponName string `protobuf:"bytes,8,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name"`
	//是否抢到 1是 0 否
	IsCatch int32 `protobuf:"varint,9,opt,name=is_catch,json=isCatch,proto3" json:"is_catch"`
	//核销码
	CouponNo             string   `protobuf:"bytes,10,opt,name=coupon_no,json=couponNo,proto3" json:"coupon_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTease) Reset()         { *m = PetTease{} }
func (m *PetTease) String() string { return proto.CompactTextString(m) }
func (*PetTease) ProtoMessage()    {}
func (*PetTease) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{5}
}

func (m *PetTease) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTease.Unmarshal(m, b)
}
func (m *PetTease) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTease.Marshal(b, m, deterministic)
}
func (m *PetTease) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTease.Merge(m, src)
}
func (m *PetTease) XXX_Size() int {
	return xxx_messageInfo_PetTease.Size(m)
}
func (m *PetTease) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTease.DiscardUnknown(m)
}

var xxx_messageInfo_PetTease proto.InternalMessageInfo

func (m *PetTease) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *PetTease) GetTreasureLevel() int32 {
	if m != nil {
		return m.TreasureLevel
	}
	return 0
}

func (m *PetTease) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *PetTease) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *PetTease) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *PetTease) GetReachMoney() string {
	if m != nil {
		return m.ReachMoney
	}
	return ""
}

func (m *PetTease) GetReduceMoney() string {
	if m != nil {
		return m.ReduceMoney
	}
	return ""
}

func (m *PetTease) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *PetTease) GetIsCatch() int32 {
	if m != nil {
		return m.IsCatch
	}
	return 0
}

func (m *PetTease) GetCouponNo() string {
	if m != nil {
		return m.CouponNo
	}
	return ""
}

//////////////////////////520爱猫日活动///////////////////////////
type ExamineListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string     `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 []*Examine `protobuf:"bytes,4,rep,name=Data,proto3" json:"Data"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ExamineListResponse) Reset()         { *m = ExamineListResponse{} }
func (m *ExamineListResponse) String() string { return proto.CompactTextString(m) }
func (*ExamineListResponse) ProtoMessage()    {}
func (*ExamineListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{6}
}

func (m *ExamineListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineListResponse.Unmarshal(m, b)
}
func (m *ExamineListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineListResponse.Marshal(b, m, deterministic)
}
func (m *ExamineListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineListResponse.Merge(m, src)
}
func (m *ExamineListResponse) XXX_Size() int {
	return xxx_messageInfo_ExamineListResponse.Size(m)
}
func (m *ExamineListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineListResponse proto.InternalMessageInfo

func (m *ExamineListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ExamineListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ExamineListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *ExamineListResponse) GetData() []*Examine {
	if m != nil {
		return m.Data
	}
	return nil
}

type Examine struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Topic                string   `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic"`
	AnswerList           []string `protobuf:"bytes,3,rep,name=answer_list,json=answerList,proto3" json:"answer_list"`
	Answer               string   `protobuf:"bytes,4,opt,name=answer,proto3" json:"answer"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Examine) Reset()         { *m = Examine{} }
func (m *Examine) String() string { return proto.CompactTextString(m) }
func (*Examine) ProtoMessage()    {}
func (*Examine) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{7}
}

func (m *Examine) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Examine.Unmarshal(m, b)
}
func (m *Examine) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Examine.Marshal(b, m, deterministic)
}
func (m *Examine) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Examine.Merge(m, src)
}
func (m *Examine) XXX_Size() int {
	return xxx_messageInfo_Examine.Size(m)
}
func (m *Examine) XXX_DiscardUnknown() {
	xxx_messageInfo_Examine.DiscardUnknown(m)
}

var xxx_messageInfo_Examine proto.InternalMessageInfo

func (m *Examine) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Examine) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *Examine) GetAnswerList() []string {
	if m != nil {
		return m.AnswerList
	}
	return nil
}

func (m *Examine) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

type SendCouponReq struct {
	//手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//优惠卷类型Id
	CouponId             int32    `protobuf:"varint,2,opt,name=couponId,proto3" json:"couponId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendCouponReq) Reset()         { *m = SendCouponReq{} }
func (m *SendCouponReq) String() string { return proto.CompactTextString(m) }
func (*SendCouponReq) ProtoMessage()    {}
func (*SendCouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{8}
}

func (m *SendCouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCouponReq.Unmarshal(m, b)
}
func (m *SendCouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCouponReq.Marshal(b, m, deterministic)
}
func (m *SendCouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCouponReq.Merge(m, src)
}
func (m *SendCouponReq) XXX_Size() int {
	return xxx_messageInfo_SendCouponReq.Size(m)
}
func (m *SendCouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendCouponReq proto.InternalMessageInfo

func (m *SendCouponReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *SendCouponReq) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

type SendCouponRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 string   `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendCouponRes) Reset()         { *m = SendCouponRes{} }
func (m *SendCouponRes) String() string { return proto.CompactTextString(m) }
func (*SendCouponRes) ProtoMessage()    {}
func (*SendCouponRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{9}
}

func (m *SendCouponRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCouponRes.Unmarshal(m, b)
}
func (m *SendCouponRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCouponRes.Marshal(b, m, deterministic)
}
func (m *SendCouponRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCouponRes.Merge(m, src)
}
func (m *SendCouponRes) XXX_Size() int {
	return xxx_messageInfo_SendCouponRes.Size(m)
}
func (m *SendCouponRes) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCouponRes.DiscardUnknown(m)
}

var xxx_messageInfo_SendCouponRes proto.InternalMessageInfo

func (m *SendCouponRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SendCouponRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SendCouponRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *SendCouponRes) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type SaveShareReq struct {
	SharerMobile         string   `protobuf:"bytes,1,opt,name=sharer_mobile,json=sharerMobile,proto3" json:"sharer_mobile"`
	HelpMobile           string   `protobuf:"bytes,2,opt,name=help_mobile,json=helpMobile,proto3" json:"help_mobile"`
	Avatar               string   `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveShareReq) Reset()         { *m = SaveShareReq{} }
func (m *SaveShareReq) String() string { return proto.CompactTextString(m) }
func (*SaveShareReq) ProtoMessage()    {}
func (*SaveShareReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{10}
}

func (m *SaveShareReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveShareReq.Unmarshal(m, b)
}
func (m *SaveShareReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveShareReq.Marshal(b, m, deterministic)
}
func (m *SaveShareReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveShareReq.Merge(m, src)
}
func (m *SaveShareReq) XXX_Size() int {
	return xxx_messageInfo_SaveShareReq.Size(m)
}
func (m *SaveShareReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveShareReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveShareReq proto.InternalMessageInfo

func (m *SaveShareReq) GetSharerMobile() string {
	if m != nil {
		return m.SharerMobile
	}
	return ""
}

func (m *SaveShareReq) GetHelpMobile() string {
	if m != nil {
		return m.HelpMobile
	}
	return ""
}

func (m *SaveShareReq) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

type GetMemberShareReq struct {
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//0：活动首页，1：海报首页
	Use                  int32    `protobuf:"varint,2,opt,name=use,proto3" json:"use"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMemberShareReq) Reset()         { *m = GetMemberShareReq{} }
func (m *GetMemberShareReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberShareReq) ProtoMessage()    {}
func (*GetMemberShareReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{11}
}

func (m *GetMemberShareReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberShareReq.Unmarshal(m, b)
}
func (m *GetMemberShareReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberShareReq.Marshal(b, m, deterministic)
}
func (m *GetMemberShareReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberShareReq.Merge(m, src)
}
func (m *GetMemberShareReq) XXX_Size() int {
	return xxx_messageInfo_GetMemberShareReq.Size(m)
}
func (m *GetMemberShareReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberShareReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberShareReq proto.InternalMessageInfo

func (m *GetMemberShareReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *GetMemberShareReq) GetUse() int32 {
	if m != nil {
		return m.Use
	}
	return 0
}

type GetMemberShareRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string           `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *MemberShareData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetMemberShareRes) Reset()         { *m = GetMemberShareRes{} }
func (m *GetMemberShareRes) String() string { return proto.CompactTextString(m) }
func (*GetMemberShareRes) ProtoMessage()    {}
func (*GetMemberShareRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{12}
}

func (m *GetMemberShareRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberShareRes.Unmarshal(m, b)
}
func (m *GetMemberShareRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberShareRes.Marshal(b, m, deterministic)
}
func (m *GetMemberShareRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberShareRes.Merge(m, src)
}
func (m *GetMemberShareRes) XXX_Size() int {
	return xxx_messageInfo_GetMemberShareRes.Size(m)
}
func (m *GetMemberShareRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberShareRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberShareRes proto.InternalMessageInfo

func (m *GetMemberShareRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetMemberShareRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetMemberShareRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetMemberShareRes) GetData() *MemberShareData {
	if m != nil {
		return m.Data
	}
	return nil
}

type MemberShareData struct {
	//
	Mobile        string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	BindingNumber int32  `protobuf:"varint,2,opt,name=binding_number,json=bindingNumber,proto3" json:"binding_number"`
	//  //爱猫指数
	CatIndex int32 `protobuf:"varint,3,opt,name=cat_index,json=catIndex,proto3" json:"cat_index"`
	// 宠主类型
	MasterType int32 `protobuf:"varint,4,opt,name=master_type,json=masterType,proto3" json:"master_type"`
	//宠主称号
	MasterTitle string `protobuf:"bytes,5,opt,name=master_title,json=masterTitle,proto3" json:"master_title"`
	//宠主性格类型
	Character string `protobuf:"bytes,6,opt,name=character,proto3" json:"character"`
	//在猫眼里的印象
	Impress string `protobuf:"bytes,7,opt,name=impress,proto3" json:"impress"`
	//爱猫宣言
	Manifesto             string                   `protobuf:"bytes,8,opt,name=manifesto,proto3" json:"manifesto"`
	Avatar                string                   `protobuf:"bytes,9,opt,name=avatar,proto3" json:"avatar"`
	ShareRelationshipData []*ShareRelationshipData `protobuf:"bytes,10,rep,name=share_relationship_data,json=shareRelationshipData,proto3" json:"share_relationship_data"`
	CouponData            []*CouponData            `protobuf:"bytes,11,rep,name=coupon_data,json=couponData,proto3" json:"coupon_data"`
	//昵称
	Nick                 string   `protobuf:"bytes,12,opt,name=nick,proto3" json:"nick"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberShareData) Reset()         { *m = MemberShareData{} }
func (m *MemberShareData) String() string { return proto.CompactTextString(m) }
func (*MemberShareData) ProtoMessage()    {}
func (*MemberShareData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{13}
}

func (m *MemberShareData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberShareData.Unmarshal(m, b)
}
func (m *MemberShareData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberShareData.Marshal(b, m, deterministic)
}
func (m *MemberShareData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberShareData.Merge(m, src)
}
func (m *MemberShareData) XXX_Size() int {
	return xxx_messageInfo_MemberShareData.Size(m)
}
func (m *MemberShareData) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberShareData.DiscardUnknown(m)
}

var xxx_messageInfo_MemberShareData proto.InternalMessageInfo

func (m *MemberShareData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *MemberShareData) GetBindingNumber() int32 {
	if m != nil {
		return m.BindingNumber
	}
	return 0
}

func (m *MemberShareData) GetCatIndex() int32 {
	if m != nil {
		return m.CatIndex
	}
	return 0
}

func (m *MemberShareData) GetMasterType() int32 {
	if m != nil {
		return m.MasterType
	}
	return 0
}

func (m *MemberShareData) GetMasterTitle() string {
	if m != nil {
		return m.MasterTitle
	}
	return ""
}

func (m *MemberShareData) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *MemberShareData) GetImpress() string {
	if m != nil {
		return m.Impress
	}
	return ""
}

func (m *MemberShareData) GetManifesto() string {
	if m != nil {
		return m.Manifesto
	}
	return ""
}

func (m *MemberShareData) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *MemberShareData) GetShareRelationshipData() []*ShareRelationshipData {
	if m != nil {
		return m.ShareRelationshipData
	}
	return nil
}

func (m *MemberShareData) GetCouponData() []*CouponData {
	if m != nil {
		return m.CouponData
	}
	return nil
}

func (m *MemberShareData) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

type ShareRelationshipData struct {
	//助力人手机号码
	HelpMobile string `protobuf:"bytes,1,opt,name=help_mobile,json=helpMobile,proto3" json:"help_mobile"`
	//助力人头像
	Avatar               string   `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShareRelationshipData) Reset()         { *m = ShareRelationshipData{} }
func (m *ShareRelationshipData) String() string { return proto.CompactTextString(m) }
func (*ShareRelationshipData) ProtoMessage()    {}
func (*ShareRelationshipData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{14}
}

func (m *ShareRelationshipData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShareRelationshipData.Unmarshal(m, b)
}
func (m *ShareRelationshipData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShareRelationshipData.Marshal(b, m, deterministic)
}
func (m *ShareRelationshipData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShareRelationshipData.Merge(m, src)
}
func (m *ShareRelationshipData) XXX_Size() int {
	return xxx_messageInfo_ShareRelationshipData.Size(m)
}
func (m *ShareRelationshipData) XXX_DiscardUnknown() {
	xxx_messageInfo_ShareRelationshipData.DiscardUnknown(m)
}

var xxx_messageInfo_ShareRelationshipData proto.InternalMessageInfo

func (m *ShareRelationshipData) GetHelpMobile() string {
	if m != nil {
		return m.HelpMobile
	}
	return ""
}

func (m *ShareRelationshipData) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

type CouponData struct {
	//金额(单位分)
	CouponAmount int32 `protobuf:"varint,1,opt,name=coupon_amount,json=couponAmount,proto3" json:"coupon_amount"`
	//优惠券ID
	CouponId int32 `protobuf:"varint,2,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	//0:未读,1已读
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	//优惠卷名称
	CouponName           string   `protobuf:"bytes,4,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponData) Reset()         { *m = CouponData{} }
func (m *CouponData) String() string { return proto.CompactTextString(m) }
func (*CouponData) ProtoMessage()    {}
func (*CouponData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{15}
}

func (m *CouponData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponData.Unmarshal(m, b)
}
func (m *CouponData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponData.Marshal(b, m, deterministic)
}
func (m *CouponData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponData.Merge(m, src)
}
func (m *CouponData) XXX_Size() int {
	return xxx_messageInfo_CouponData.Size(m)
}
func (m *CouponData) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponData.DiscardUnknown(m)
}

var xxx_messageInfo_CouponData proto.InternalMessageInfo

func (m *CouponData) GetCouponAmount() int32 {
	if m != nil {
		return m.CouponAmount
	}
	return 0
}

func (m *CouponData) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *CouponData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CouponData) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

type SaveExamineAnswerReq struct {
	//手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	// 答案
	Data []*Answer `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	//头像
	Avatar string `protobuf:"bytes,3,opt,name=Avatar,proto3" json:"Avatar"`
	//微信昵称
	Nick                 string   `protobuf:"bytes,4,opt,name=Nick,proto3" json:"Nick"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveExamineAnswerReq) Reset()         { *m = SaveExamineAnswerReq{} }
func (m *SaveExamineAnswerReq) String() string { return proto.CompactTextString(m) }
func (*SaveExamineAnswerReq) ProtoMessage()    {}
func (*SaveExamineAnswerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{16}
}

func (m *SaveExamineAnswerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveExamineAnswerReq.Unmarshal(m, b)
}
func (m *SaveExamineAnswerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveExamineAnswerReq.Marshal(b, m, deterministic)
}
func (m *SaveExamineAnswerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveExamineAnswerReq.Merge(m, src)
}
func (m *SaveExamineAnswerReq) XXX_Size() int {
	return xxx_messageInfo_SaveExamineAnswerReq.Size(m)
}
func (m *SaveExamineAnswerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveExamineAnswerReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveExamineAnswerReq proto.InternalMessageInfo

func (m *SaveExamineAnswerReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *SaveExamineAnswerReq) GetData() []*Answer {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *SaveExamineAnswerReq) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *SaveExamineAnswerReq) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

type Answer struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Answer               string   `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Answer) Reset()         { *m = Answer{} }
func (m *Answer) String() string { return proto.CompactTextString(m) }
func (*Answer) ProtoMessage()    {}
func (*Answer) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{17}
}

func (m *Answer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Answer.Unmarshal(m, b)
}
func (m *Answer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Answer.Marshal(b, m, deterministic)
}
func (m *Answer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Answer.Merge(m, src)
}
func (m *Answer) XXX_Size() int {
	return xxx_messageInfo_Answer.Size(m)
}
func (m *Answer) XXX_DiscardUnknown() {
	xxx_messageInfo_Answer.DiscardUnknown(m)
}

var xxx_messageInfo_Answer proto.InternalMessageInfo

func (m *Answer) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Answer) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

type SaveExamineAnswerRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string        `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *MemberAnswer `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SaveExamineAnswerRes) Reset()         { *m = SaveExamineAnswerRes{} }
func (m *SaveExamineAnswerRes) String() string { return proto.CompactTextString(m) }
func (*SaveExamineAnswerRes) ProtoMessage()    {}
func (*SaveExamineAnswerRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{18}
}

func (m *SaveExamineAnswerRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveExamineAnswerRes.Unmarshal(m, b)
}
func (m *SaveExamineAnswerRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveExamineAnswerRes.Marshal(b, m, deterministic)
}
func (m *SaveExamineAnswerRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveExamineAnswerRes.Merge(m, src)
}
func (m *SaveExamineAnswerRes) XXX_Size() int {
	return xxx_messageInfo_SaveExamineAnswerRes.Size(m)
}
func (m *SaveExamineAnswerRes) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveExamineAnswerRes.DiscardUnknown(m)
}

var xxx_messageInfo_SaveExamineAnswerRes proto.InternalMessageInfo

func (m *SaveExamineAnswerRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SaveExamineAnswerRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SaveExamineAnswerRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *SaveExamineAnswerRes) GetData() *MemberAnswer {
	if m != nil {
		return m.Data
	}
	return nil
}

type MemberAnswer struct {
	//爱猫指数
	CatIndex int32 `protobuf:"varint,1,opt,name=cat_index,json=catIndex,proto3" json:"cat_index"`
	// 宠主类型
	MasterType string `protobuf:"bytes,2,opt,name=master_type,json=masterType,proto3" json:"master_type"`
	//宠主称号
	MasterTitle string `protobuf:"bytes,3,opt,name=master_title,json=masterTitle,proto3" json:"master_title"`
	//宠主性格类型
	Character string `protobuf:"bytes,4,opt,name=character,proto3" json:"character"`
	//在猫眼里的印象
	Impress string `protobuf:"bytes,5,opt,name=impress,proto3" json:"impress"`
	//爱猫宣言
	Manifesto            string   `protobuf:"bytes,6,opt,name=manifesto,proto3" json:"manifesto"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberAnswer) Reset()         { *m = MemberAnswer{} }
func (m *MemberAnswer) String() string { return proto.CompactTextString(m) }
func (*MemberAnswer) ProtoMessage()    {}
func (*MemberAnswer) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{19}
}

func (m *MemberAnswer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberAnswer.Unmarshal(m, b)
}
func (m *MemberAnswer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberAnswer.Marshal(b, m, deterministic)
}
func (m *MemberAnswer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberAnswer.Merge(m, src)
}
func (m *MemberAnswer) XXX_Size() int {
	return xxx_messageInfo_MemberAnswer.Size(m)
}
func (m *MemberAnswer) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberAnswer.DiscardUnknown(m)
}

var xxx_messageInfo_MemberAnswer proto.InternalMessageInfo

func (m *MemberAnswer) GetCatIndex() int32 {
	if m != nil {
		return m.CatIndex
	}
	return 0
}

func (m *MemberAnswer) GetMasterType() string {
	if m != nil {
		return m.MasterType
	}
	return ""
}

func (m *MemberAnswer) GetMasterTitle() string {
	if m != nil {
		return m.MasterTitle
	}
	return ""
}

func (m *MemberAnswer) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *MemberAnswer) GetImpress() string {
	if m != nil {
		return m.Impress
	}
	return ""
}

func (m *MemberAnswer) GetManifesto() string {
	if m != nil {
		return m.Manifesto
	}
	return ""
}

type GetCouponTemplateReq struct {
	Tid                  int32    `protobuf:"varint,1,opt,name=tid,proto3" json:"tid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponTemplateReq) Reset()         { *m = GetCouponTemplateReq{} }
func (m *GetCouponTemplateReq) String() string { return proto.CompactTextString(m) }
func (*GetCouponTemplateReq) ProtoMessage()    {}
func (*GetCouponTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{20}
}

func (m *GetCouponTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponTemplateReq.Unmarshal(m, b)
}
func (m *GetCouponTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponTemplateReq.Marshal(b, m, deterministic)
}
func (m *GetCouponTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponTemplateReq.Merge(m, src)
}
func (m *GetCouponTemplateReq) XXX_Size() int {
	return xxx_messageInfo_GetCouponTemplateReq.Size(m)
}
func (m *GetCouponTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponTemplateReq proto.InternalMessageInfo

func (m *GetCouponTemplateReq) GetTid() int32 {
	if m != nil {
		return m.Tid
	}
	return 0
}

type GetCouponTemplateRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string                `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 []*CouponTemplateData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetCouponTemplateRes) Reset()         { *m = GetCouponTemplateRes{} }
func (m *GetCouponTemplateRes) String() string { return proto.CompactTextString(m) }
func (*GetCouponTemplateRes) ProtoMessage()    {}
func (*GetCouponTemplateRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{21}
}

func (m *GetCouponTemplateRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponTemplateRes.Unmarshal(m, b)
}
func (m *GetCouponTemplateRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponTemplateRes.Marshal(b, m, deterministic)
}
func (m *GetCouponTemplateRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponTemplateRes.Merge(m, src)
}
func (m *GetCouponTemplateRes) XXX_Size() int {
	return xxx_messageInfo_GetCouponTemplateRes.Size(m)
}
func (m *GetCouponTemplateRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponTemplateRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponTemplateRes proto.InternalMessageInfo

func (m *GetCouponTemplateRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCouponTemplateRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetCouponTemplateRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetCouponTemplateRes) GetData() []*CouponTemplateData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CouponTemplateData struct {
	//优惠券模板ID
	VoucherTId string `protobuf:"bytes,1,opt,name=voucher_t_id,json=voucherTId,proto3" json:"voucher_t_id"`
	//标题
	VoucherTTitle string `protobuf:"bytes,2,opt,name=voucher_t_title,json=voucherTTitle,proto3" json:"voucher_t_title"`
	//描述信息
	VoucherTDesc string `protobuf:"bytes,3,opt,name=voucher_t_desc,json=voucherTDesc,proto3" json:"voucher_t_desc"`
	//优惠券模版面额
	VoucherTPrice string `protobuf:"bytes,4,opt,name=voucher_t_price,json=voucherTPrice,proto3" json:"voucher_t_price"`
	//状态(1-有效,2-失效)
	VoucherTState string `protobuf:"bytes,5,opt,name=voucher_t_state,json=voucherTState,proto3" json:"voucher_t_state"`
	//状态文字说明
	VoucherTStateText    string   `protobuf:"bytes,6,opt,name=voucher_t_state_text,json=voucherTStateText,proto3" json:"voucher_t_state_text"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponTemplateData) Reset()         { *m = CouponTemplateData{} }
func (m *CouponTemplateData) String() string { return proto.CompactTextString(m) }
func (*CouponTemplateData) ProtoMessage()    {}
func (*CouponTemplateData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{22}
}

func (m *CouponTemplateData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponTemplateData.Unmarshal(m, b)
}
func (m *CouponTemplateData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponTemplateData.Marshal(b, m, deterministic)
}
func (m *CouponTemplateData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponTemplateData.Merge(m, src)
}
func (m *CouponTemplateData) XXX_Size() int {
	return xxx_messageInfo_CouponTemplateData.Size(m)
}
func (m *CouponTemplateData) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponTemplateData.DiscardUnknown(m)
}

var xxx_messageInfo_CouponTemplateData proto.InternalMessageInfo

func (m *CouponTemplateData) GetVoucherTId() string {
	if m != nil {
		return m.VoucherTId
	}
	return ""
}

func (m *CouponTemplateData) GetVoucherTTitle() string {
	if m != nil {
		return m.VoucherTTitle
	}
	return ""
}

func (m *CouponTemplateData) GetVoucherTDesc() string {
	if m != nil {
		return m.VoucherTDesc
	}
	return ""
}

func (m *CouponTemplateData) GetVoucherTPrice() string {
	if m != nil {
		return m.VoucherTPrice
	}
	return ""
}

func (m *CouponTemplateData) GetVoucherTState() string {
	if m != nil {
		return m.VoucherTState
	}
	return ""
}

func (m *CouponTemplateData) GetVoucherTStateText() string {
	if m != nil {
		return m.VoucherTStateText
	}
	return ""
}

type SaveMemberCouponReq struct {
	//优惠券ID
	CouponId int32 `protobuf:"varint,2,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	//手机号码
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//1:电商优惠卷,2:门店优惠卷,3:门店体检优惠卷
	CouponType           int32    `protobuf:"varint,3,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveMemberCouponReq) Reset()         { *m = SaveMemberCouponReq{} }
func (m *SaveMemberCouponReq) String() string { return proto.CompactTextString(m) }
func (*SaveMemberCouponReq) ProtoMessage()    {}
func (*SaveMemberCouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{23}
}

func (m *SaveMemberCouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveMemberCouponReq.Unmarshal(m, b)
}
func (m *SaveMemberCouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveMemberCouponReq.Marshal(b, m, deterministic)
}
func (m *SaveMemberCouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveMemberCouponReq.Merge(m, src)
}
func (m *SaveMemberCouponReq) XXX_Size() int {
	return xxx_messageInfo_SaveMemberCouponReq.Size(m)
}
func (m *SaveMemberCouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveMemberCouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveMemberCouponReq proto.InternalMessageInfo

func (m *SaveMemberCouponReq) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *SaveMemberCouponReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *SaveMemberCouponReq) GetCouponType() int32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

type GetMemberCouponReq struct {
	//手机号码
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//1:电商优惠卷,2:门店优惠卷,3:门店体检优惠卷
	CouponType           int32    `protobuf:"varint,2,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMemberCouponReq) Reset()         { *m = GetMemberCouponReq{} }
func (m *GetMemberCouponReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberCouponReq) ProtoMessage()    {}
func (*GetMemberCouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{24}
}

func (m *GetMemberCouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberCouponReq.Unmarshal(m, b)
}
func (m *GetMemberCouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberCouponReq.Marshal(b, m, deterministic)
}
func (m *GetMemberCouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberCouponReq.Merge(m, src)
}
func (m *GetMemberCouponReq) XXX_Size() int {
	return xxx_messageInfo_GetMemberCouponReq.Size(m)
}
func (m *GetMemberCouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberCouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberCouponReq proto.InternalMessageInfo

func (m *GetMemberCouponReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *GetMemberCouponReq) GetCouponType() int32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

type GetMemberCouponRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string            `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *MemberCouponData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetMemberCouponRes) Reset()         { *m = GetMemberCouponRes{} }
func (m *GetMemberCouponRes) String() string { return proto.CompactTextString(m) }
func (*GetMemberCouponRes) ProtoMessage()    {}
func (*GetMemberCouponRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{25}
}

func (m *GetMemberCouponRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberCouponRes.Unmarshal(m, b)
}
func (m *GetMemberCouponRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberCouponRes.Marshal(b, m, deterministic)
}
func (m *GetMemberCouponRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberCouponRes.Merge(m, src)
}
func (m *GetMemberCouponRes) XXX_Size() int {
	return xxx_messageInfo_GetMemberCouponRes.Size(m)
}
func (m *GetMemberCouponRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberCouponRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberCouponRes proto.InternalMessageInfo

func (m *GetMemberCouponRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetMemberCouponRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetMemberCouponRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetMemberCouponRes) GetData() *MemberCouponData {
	if m != nil {
		return m.Data
	}
	return nil
}

type MemberCouponData struct {
	//优惠券ID
	CouponId             int32    `protobuf:"varint,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberCouponData) Reset()         { *m = MemberCouponData{} }
func (m *MemberCouponData) String() string { return proto.CompactTextString(m) }
func (*MemberCouponData) ProtoMessage()    {}
func (*MemberCouponData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{26}
}

func (m *MemberCouponData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCouponData.Unmarshal(m, b)
}
func (m *MemberCouponData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCouponData.Marshal(b, m, deterministic)
}
func (m *MemberCouponData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCouponData.Merge(m, src)
}
func (m *MemberCouponData) XXX_Size() int {
	return xxx_messageInfo_MemberCouponData.Size(m)
}
func (m *MemberCouponData) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCouponData.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCouponData proto.InternalMessageInfo

func (m *MemberCouponData) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

type SaveSubscribeMessageReq struct {
	//手机号码
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//OpenId
	OpenId               string          `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id"`
	Data                 []*TemplateData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SaveSubscribeMessageReq) Reset()         { *m = SaveSubscribeMessageReq{} }
func (m *SaveSubscribeMessageReq) String() string { return proto.CompactTextString(m) }
func (*SaveSubscribeMessageReq) ProtoMessage()    {}
func (*SaveSubscribeMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{27}
}

func (m *SaveSubscribeMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveSubscribeMessageReq.Unmarshal(m, b)
}
func (m *SaveSubscribeMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveSubscribeMessageReq.Marshal(b, m, deterministic)
}
func (m *SaveSubscribeMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveSubscribeMessageReq.Merge(m, src)
}
func (m *SaveSubscribeMessageReq) XXX_Size() int {
	return xxx_messageInfo_SaveSubscribeMessageReq.Size(m)
}
func (m *SaveSubscribeMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveSubscribeMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveSubscribeMessageReq proto.InternalMessageInfo

func (m *SaveSubscribeMessageReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *SaveSubscribeMessageReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *SaveSubscribeMessageReq) GetData() []*TemplateData {
	if m != nil {
		return m.Data
	}
	return nil
}

type TemplateData struct {
	//模板Id
	TemplateId string `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	//跳转地址
	PageUrl string `protobuf:"bytes,2,opt,name=page_url,json=pageUrl,proto3" json:"page_url"`
	//是否定时
	MessageType          int32    `protobuf:"varint,3,opt,name=message_type,json=messageType,proto3" json:"message_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TemplateData) Reset()         { *m = TemplateData{} }
func (m *TemplateData) String() string { return proto.CompactTextString(m) }
func (*TemplateData) ProtoMessage()    {}
func (*TemplateData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{28}
}

func (m *TemplateData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TemplateData.Unmarshal(m, b)
}
func (m *TemplateData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TemplateData.Marshal(b, m, deterministic)
}
func (m *TemplateData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TemplateData.Merge(m, src)
}
func (m *TemplateData) XXX_Size() int {
	return xxx_messageInfo_TemplateData.Size(m)
}
func (m *TemplateData) XXX_DiscardUnknown() {
	xxx_messageInfo_TemplateData.DiscardUnknown(m)
}

var xxx_messageInfo_TemplateData proto.InternalMessageInfo

func (m *TemplateData) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *TemplateData) GetPageUrl() string {
	if m != nil {
		return m.PageUrl
	}
	return ""
}

func (m *TemplateData) GetMessageType() int32 {
	if m != nil {
		return m.MessageType
	}
	return 0
}

type CancelSubscribeMessageReq struct {
	//手机号码
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//OpenId
	OpenId               string   `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelSubscribeMessageReq) Reset()         { *m = CancelSubscribeMessageReq{} }
func (m *CancelSubscribeMessageReq) String() string { return proto.CompactTextString(m) }
func (*CancelSubscribeMessageReq) ProtoMessage()    {}
func (*CancelSubscribeMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{29}
}

func (m *CancelSubscribeMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelSubscribeMessageReq.Unmarshal(m, b)
}
func (m *CancelSubscribeMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelSubscribeMessageReq.Marshal(b, m, deterministic)
}
func (m *CancelSubscribeMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelSubscribeMessageReq.Merge(m, src)
}
func (m *CancelSubscribeMessageReq) XXX_Size() int {
	return xxx_messageInfo_CancelSubscribeMessageReq.Size(m)
}
func (m *CancelSubscribeMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelSubscribeMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelSubscribeMessageReq proto.InternalMessageInfo

func (m *CancelSubscribeMessageReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CancelSubscribeMessageReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

type CouponMessageRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string               `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 []*CouponMessageData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CouponMessageRes) Reset()         { *m = CouponMessageRes{} }
func (m *CouponMessageRes) String() string { return proto.CompactTextString(m) }
func (*CouponMessageRes) ProtoMessage()    {}
func (*CouponMessageRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{30}
}

func (m *CouponMessageRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponMessageRes.Unmarshal(m, b)
}
func (m *CouponMessageRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponMessageRes.Marshal(b, m, deterministic)
}
func (m *CouponMessageRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponMessageRes.Merge(m, src)
}
func (m *CouponMessageRes) XXX_Size() int {
	return xxx_messageInfo_CouponMessageRes.Size(m)
}
func (m *CouponMessageRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponMessageRes.DiscardUnknown(m)
}

var xxx_messageInfo_CouponMessageRes proto.InternalMessageInfo

func (m *CouponMessageRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CouponMessageRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CouponMessageRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CouponMessageRes) GetData() []*CouponMessageData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CouponMessageData struct {
	OpenId               string   `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id"`
	TemplateId           string   `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	Mobile               string   `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	PageUrl              string   `protobuf:"bytes,4,opt,name=page_url,json=pageUrl,proto3" json:"page_url"`
	MessageType          int32    `protobuf:"varint,5,opt,name=message_type,json=messageType,proto3" json:"message_type"`
	MessageStatus        int32    `protobuf:"varint,6,opt,name=message_status,json=messageStatus,proto3" json:"message_status"`
	CouponAmount         int64    `protobuf:"varint,7,opt,name=coupon_amount,json=couponAmount,proto3" json:"coupon_amount"`
	CouponId             int64    `protobuf:"varint,8,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	CouponMobile         string   `protobuf:"bytes,9,opt,name=coupon_mobile,json=couponMobile,proto3" json:"coupon_mobile"`
	Status               int32    `protobuf:"varint,10,opt,name=status,proto3" json:"status"`
	CouponName           string   `protobuf:"bytes,11,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name"`
	CouponType           int32    `protobuf:"varint,12,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type"`
	EffectiveData        int64    `protobuf:"varint,13,opt,name=effective_data,json=effectiveData,proto3" json:"effective_data"`
	UseRule              string   `protobuf:"bytes,14,opt,name=use_rule,json=useRule,proto3" json:"use_rule"`
	Remarks              string   `protobuf:"bytes,15,opt,name=remarks,proto3" json:"remarks"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponMessageData) Reset()         { *m = CouponMessageData{} }
func (m *CouponMessageData) String() string { return proto.CompactTextString(m) }
func (*CouponMessageData) ProtoMessage()    {}
func (*CouponMessageData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{31}
}

func (m *CouponMessageData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponMessageData.Unmarshal(m, b)
}
func (m *CouponMessageData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponMessageData.Marshal(b, m, deterministic)
}
func (m *CouponMessageData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponMessageData.Merge(m, src)
}
func (m *CouponMessageData) XXX_Size() int {
	return xxx_messageInfo_CouponMessageData.Size(m)
}
func (m *CouponMessageData) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponMessageData.DiscardUnknown(m)
}

var xxx_messageInfo_CouponMessageData proto.InternalMessageInfo

func (m *CouponMessageData) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *CouponMessageData) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *CouponMessageData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CouponMessageData) GetPageUrl() string {
	if m != nil {
		return m.PageUrl
	}
	return ""
}

func (m *CouponMessageData) GetMessageType() int32 {
	if m != nil {
		return m.MessageType
	}
	return 0
}

func (m *CouponMessageData) GetMessageStatus() int32 {
	if m != nil {
		return m.MessageStatus
	}
	return 0
}

func (m *CouponMessageData) GetCouponAmount() int64 {
	if m != nil {
		return m.CouponAmount
	}
	return 0
}

func (m *CouponMessageData) GetCouponId() int64 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *CouponMessageData) GetCouponMobile() string {
	if m != nil {
		return m.CouponMobile
	}
	return ""
}

func (m *CouponMessageData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CouponMessageData) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *CouponMessageData) GetCouponType() int32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

func (m *CouponMessageData) GetEffectiveData() int64 {
	if m != nil {
		return m.EffectiveData
	}
	return 0
}

func (m *CouponMessageData) GetUseRule() string {
	if m != nil {
		return m.UseRule
	}
	return ""
}

func (m *CouponMessageData) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

type GetCatMonthInfoRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error                string        `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *CatMonthData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCatMonthInfoRes) Reset()         { *m = GetCatMonthInfoRes{} }
func (m *GetCatMonthInfoRes) String() string { return proto.CompactTextString(m) }
func (*GetCatMonthInfoRes) ProtoMessage()    {}
func (*GetCatMonthInfoRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{32}
}

func (m *GetCatMonthInfoRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatMonthInfoRes.Unmarshal(m, b)
}
func (m *GetCatMonthInfoRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatMonthInfoRes.Marshal(b, m, deterministic)
}
func (m *GetCatMonthInfoRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatMonthInfoRes.Merge(m, src)
}
func (m *GetCatMonthInfoRes) XXX_Size() int {
	return xxx_messageInfo_GetCatMonthInfoRes.Size(m)
}
func (m *GetCatMonthInfoRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatMonthInfoRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatMonthInfoRes proto.InternalMessageInfo

func (m *GetCatMonthInfoRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCatMonthInfoRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetCatMonthInfoRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetCatMonthInfoRes) GetData() *CatMonthData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CatMonthData struct {
	// 是否参与过 1是 0否
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	// 图片地址
	ImgUrl string `protobuf:"bytes,2,opt,name=img_url,json=imgUrl,proto3" json:"img_url"`
	// 颜值分数
	ActivityScore float32 `protobuf:"fixed32,3,opt,name=activity_score,json=activityScore,proto3" json:"activity_score"`
	// 红包金额
	CouponAmount int64 `protobuf:"varint,4,opt,name=coupon_amount,json=couponAmount,proto3" json:"coupon_amount"`
	// 评语
	CatRemark            string   `protobuf:"bytes,5,opt,name=cat_remark,json=catRemark,proto3" json:"cat_remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CatMonthData) Reset()         { *m = CatMonthData{} }
func (m *CatMonthData) String() string { return proto.CompactTextString(m) }
func (*CatMonthData) ProtoMessage()    {}
func (*CatMonthData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{33}
}

func (m *CatMonthData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatMonthData.Unmarshal(m, b)
}
func (m *CatMonthData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatMonthData.Marshal(b, m, deterministic)
}
func (m *CatMonthData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatMonthData.Merge(m, src)
}
func (m *CatMonthData) XXX_Size() int {
	return xxx_messageInfo_CatMonthData.Size(m)
}
func (m *CatMonthData) XXX_DiscardUnknown() {
	xxx_messageInfo_CatMonthData.DiscardUnknown(m)
}

var xxx_messageInfo_CatMonthData proto.InternalMessageInfo

func (m *CatMonthData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CatMonthData) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *CatMonthData) GetActivityScore() float32 {
	if m != nil {
		return m.ActivityScore
	}
	return 0
}

func (m *CatMonthData) GetCouponAmount() int64 {
	if m != nil {
		return m.CouponAmount
	}
	return 0
}

func (m *CatMonthData) GetCatRemark() string {
	if m != nil {
		return m.CatRemark
	}
	return ""
}

type SaveCatMonthInfoReq struct {
	Mobile               string   `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	ImgUrl               string   `protobuf:"bytes,2,opt,name=img_url,json=imgUrl,proto3" json:"img_url"`
	ActivityScore        float32  `protobuf:"fixed32,3,opt,name=activity_score,json=activityScore,proto3" json:"activity_score"`
	IsCat                int32    `protobuf:"varint,4,opt,name=is_cat,json=isCat,proto3" json:"is_cat"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveCatMonthInfoReq) Reset()         { *m = SaveCatMonthInfoReq{} }
func (m *SaveCatMonthInfoReq) String() string { return proto.CompactTextString(m) }
func (*SaveCatMonthInfoReq) ProtoMessage()    {}
func (*SaveCatMonthInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{34}
}

func (m *SaveCatMonthInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveCatMonthInfoReq.Unmarshal(m, b)
}
func (m *SaveCatMonthInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveCatMonthInfoReq.Marshal(b, m, deterministic)
}
func (m *SaveCatMonthInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveCatMonthInfoReq.Merge(m, src)
}
func (m *SaveCatMonthInfoReq) XXX_Size() int {
	return xxx_messageInfo_SaveCatMonthInfoReq.Size(m)
}
func (m *SaveCatMonthInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveCatMonthInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveCatMonthInfoReq proto.InternalMessageInfo

func (m *SaveCatMonthInfoReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *SaveCatMonthInfoReq) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *SaveCatMonthInfoReq) GetActivityScore() float32 {
	if m != nil {
		return m.ActivityScore
	}
	return 0
}

func (m *SaveCatMonthInfoReq) GetIsCat() int32 {
	if m != nil {
		return m.IsCat
	}
	return 0
}

type SaveWatermarkReq struct {
	//水印ID，新增水印传0
	WatermarkId int32 `protobuf:"varint,1,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	//水印名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//开始时间
	StartTime int64 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime int64 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//水印图片地址
	WatermarkImg string `protobuf:"bytes,5,opt,name=watermark_img,json=watermarkImg,proto3" json:"watermark_img"`
	//是否展示活动价
	IsDisplayActivityPrice bool `protobuf:"varint,6,opt,name=is_display_activity_price,json=isDisplayActivityPrice,proto3" json:"is_display_activity_price"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveWatermarkReq) Reset()         { *m = SaveWatermarkReq{} }
func (m *SaveWatermarkReq) String() string { return proto.CompactTextString(m) }
func (*SaveWatermarkReq) ProtoMessage()    {}
func (*SaveWatermarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{35}
}

func (m *SaveWatermarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWatermarkReq.Unmarshal(m, b)
}
func (m *SaveWatermarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWatermarkReq.Marshal(b, m, deterministic)
}
func (m *SaveWatermarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWatermarkReq.Merge(m, src)
}
func (m *SaveWatermarkReq) XXX_Size() int {
	return xxx_messageInfo_SaveWatermarkReq.Size(m)
}
func (m *SaveWatermarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWatermarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWatermarkReq proto.InternalMessageInfo

func (m *SaveWatermarkReq) GetWatermarkId() int32 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

func (m *SaveWatermarkReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SaveWatermarkReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *SaveWatermarkReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SaveWatermarkReq) GetWatermarkImg() string {
	if m != nil {
		return m.WatermarkImg
	}
	return ""
}

func (m *SaveWatermarkReq) GetIsDisplayActivityPrice() bool {
	if m != nil {
		return m.IsDisplayActivityPrice
	}
	return false
}

func (m *SaveWatermarkReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type SaveWatermarkRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//水印ID
	WatermarkId          int64    `protobuf:"varint,3,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveWatermarkRes) Reset()         { *m = SaveWatermarkRes{} }
func (m *SaveWatermarkRes) String() string { return proto.CompactTextString(m) }
func (*SaveWatermarkRes) ProtoMessage()    {}
func (*SaveWatermarkRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{36}
}

func (m *SaveWatermarkRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWatermarkRes.Unmarshal(m, b)
}
func (m *SaveWatermarkRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWatermarkRes.Marshal(b, m, deterministic)
}
func (m *SaveWatermarkRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWatermarkRes.Merge(m, src)
}
func (m *SaveWatermarkRes) XXX_Size() int {
	return xxx_messageInfo_SaveWatermarkRes.Size(m)
}
func (m *SaveWatermarkRes) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWatermarkRes.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWatermarkRes proto.InternalMessageInfo

func (m *SaveWatermarkRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SaveWatermarkRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SaveWatermarkRes) GetWatermarkId() int64 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

type WatermarkListReq struct {
	//水印名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	//水印状态
	Status    int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	PageSize  int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WatermarkListReq) Reset()         { *m = WatermarkListReq{} }
func (m *WatermarkListReq) String() string { return proto.CompactTextString(m) }
func (*WatermarkListReq) ProtoMessage()    {}
func (*WatermarkListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{37}
}

func (m *WatermarkListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WatermarkListReq.Unmarshal(m, b)
}
func (m *WatermarkListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WatermarkListReq.Marshal(b, m, deterministic)
}
func (m *WatermarkListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WatermarkListReq.Merge(m, src)
}
func (m *WatermarkListReq) XXX_Size() int {
	return xxx_messageInfo_WatermarkListReq.Size(m)
}
func (m *WatermarkListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_WatermarkListReq.DiscardUnknown(m)
}

var xxx_messageInfo_WatermarkListReq proto.InternalMessageInfo

func (m *WatermarkListReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WatermarkListReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WatermarkListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *WatermarkListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *WatermarkListReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type WatermarkListRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//水印列表
	Data []*WatermarkData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	//总条数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WatermarkListRes) Reset()         { *m = WatermarkListRes{} }
func (m *WatermarkListRes) String() string { return proto.CompactTextString(m) }
func (*WatermarkListRes) ProtoMessage()    {}
func (*WatermarkListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{38}
}

func (m *WatermarkListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WatermarkListRes.Unmarshal(m, b)
}
func (m *WatermarkListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WatermarkListRes.Marshal(b, m, deterministic)
}
func (m *WatermarkListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WatermarkListRes.Merge(m, src)
}
func (m *WatermarkListRes) XXX_Size() int {
	return xxx_messageInfo_WatermarkListRes.Size(m)
}
func (m *WatermarkListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_WatermarkListRes.DiscardUnknown(m)
}

var xxx_messageInfo_WatermarkListRes proto.InternalMessageInfo

func (m *WatermarkListRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *WatermarkListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *WatermarkListRes) GetData() []*WatermarkData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *WatermarkListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type WatermarkData struct {
	//水印ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//水印名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//水印图片地址
	WatermarkImg string `protobuf:"bytes,3,opt,name=watermark_img,json=watermarkImg,proto3" json:"watermark_img"`
	//商品数量
	GoodsNum int32 `protobuf:"varint,4,opt,name=goods_num,json=goodsNum,proto3" json:"goods_num"`
	//开始时间
	StartTime int64 `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime int64 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//活动状态 1未开始  2进行中 3已终止  4已结束
	Status int32 `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	//是否展示活动价
	IsDisplayActivityPrice bool     `protobuf:"varint,8,opt,name=is_display_activity_price,json=isDisplayActivityPrice,proto3" json:"is_display_activity_price"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *WatermarkData) Reset()         { *m = WatermarkData{} }
func (m *WatermarkData) String() string { return proto.CompactTextString(m) }
func (*WatermarkData) ProtoMessage()    {}
func (*WatermarkData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{39}
}

func (m *WatermarkData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WatermarkData.Unmarshal(m, b)
}
func (m *WatermarkData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WatermarkData.Marshal(b, m, deterministic)
}
func (m *WatermarkData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WatermarkData.Merge(m, src)
}
func (m *WatermarkData) XXX_Size() int {
	return xxx_messageInfo_WatermarkData.Size(m)
}
func (m *WatermarkData) XXX_DiscardUnknown() {
	xxx_messageInfo_WatermarkData.DiscardUnknown(m)
}

var xxx_messageInfo_WatermarkData proto.InternalMessageInfo

func (m *WatermarkData) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WatermarkData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WatermarkData) GetWatermarkImg() string {
	if m != nil {
		return m.WatermarkImg
	}
	return ""
}

func (m *WatermarkData) GetGoodsNum() int32 {
	if m != nil {
		return m.GoodsNum
	}
	return 0
}

func (m *WatermarkData) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *WatermarkData) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *WatermarkData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WatermarkData) GetIsDisplayActivityPrice() bool {
	if m != nil {
		return m.IsDisplayActivityPrice
	}
	return false
}

type GetWatermarkReq struct {
	//水印ID
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWatermarkReq) Reset()         { *m = GetWatermarkReq{} }
func (m *GetWatermarkReq) String() string { return proto.CompactTextString(m) }
func (*GetWatermarkReq) ProtoMessage()    {}
func (*GetWatermarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{40}
}

func (m *GetWatermarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWatermarkReq.Unmarshal(m, b)
}
func (m *GetWatermarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWatermarkReq.Marshal(b, m, deterministic)
}
func (m *GetWatermarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWatermarkReq.Merge(m, src)
}
func (m *GetWatermarkReq) XXX_Size() int {
	return xxx_messageInfo_GetWatermarkReq.Size(m)
}
func (m *GetWatermarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWatermarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWatermarkReq proto.InternalMessageInfo

func (m *GetWatermarkReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetWatermarkRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//水印ID
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	//水印名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	//水印图片地址
	WatermarkImg string `protobuf:"bytes,5,opt,name=watermark_img,json=watermarkImg,proto3" json:"watermark_img"`
	//商品数量
	GoodsNum int32 `protobuf:"varint,6,opt,name=goods_num,json=goodsNum,proto3" json:"goods_num"`
	//开始时间
	StartTime int64 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime int64 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//活动状态 1未开始  2进行中 3已终止  4已结束
	Status int32 `protobuf:"varint,9,opt,name=status,proto3" json:"status"`
	//是否展示活动价
	IsDisplayActivityPrice bool     `protobuf:"varint,10,opt,name=is_display_activity_price,json=isDisplayActivityPrice,proto3" json:"is_display_activity_price"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GetWatermarkRes) Reset()         { *m = GetWatermarkRes{} }
func (m *GetWatermarkRes) String() string { return proto.CompactTextString(m) }
func (*GetWatermarkRes) ProtoMessage()    {}
func (*GetWatermarkRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{41}
}

func (m *GetWatermarkRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWatermarkRes.Unmarshal(m, b)
}
func (m *GetWatermarkRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWatermarkRes.Marshal(b, m, deterministic)
}
func (m *GetWatermarkRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWatermarkRes.Merge(m, src)
}
func (m *GetWatermarkRes) XXX_Size() int {
	return xxx_messageInfo_GetWatermarkRes.Size(m)
}
func (m *GetWatermarkRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWatermarkRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetWatermarkRes proto.InternalMessageInfo

func (m *GetWatermarkRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetWatermarkRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetWatermarkRes) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetWatermarkRes) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetWatermarkRes) GetWatermarkImg() string {
	if m != nil {
		return m.WatermarkImg
	}
	return ""
}

func (m *GetWatermarkRes) GetGoodsNum() int32 {
	if m != nil {
		return m.GoodsNum
	}
	return 0
}

func (m *GetWatermarkRes) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetWatermarkRes) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetWatermarkRes) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetWatermarkRes) GetIsDisplayActivityPrice() bool {
	if m != nil {
		return m.IsDisplayActivityPrice
	}
	return false
}

type GoodsListReq struct {
	//水印ID
	WatermarkId int64 `protobuf:"varint,1,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	//sku id
	Sku int32 `protobuf:"varint,2,opt,name=sku,proto3" json:"sku"`
	//商品名称
	GoodsName string `protobuf:"bytes,3,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//水印名称
	WatermarkName        string   `protobuf:"bytes,4,opt,name=watermark_name,json=watermarkName,proto3" json:"watermark_name"`
	PageSize             int32    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageIndex            int32    `protobuf:"varint,6,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsListReq) Reset()         { *m = GoodsListReq{} }
func (m *GoodsListReq) String() string { return proto.CompactTextString(m) }
func (*GoodsListReq) ProtoMessage()    {}
func (*GoodsListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{42}
}

func (m *GoodsListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsListReq.Unmarshal(m, b)
}
func (m *GoodsListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsListReq.Marshal(b, m, deterministic)
}
func (m *GoodsListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsListReq.Merge(m, src)
}
func (m *GoodsListReq) XXX_Size() int {
	return xxx_messageInfo_GoodsListReq.Size(m)
}
func (m *GoodsListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsListReq proto.InternalMessageInfo

func (m *GoodsListReq) GetWatermarkId() int64 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

func (m *GoodsListReq) GetSku() int32 {
	if m != nil {
		return m.Sku
	}
	return 0
}

func (m *GoodsListReq) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *GoodsListReq) GetWatermarkName() string {
	if m != nil {
		return m.WatermarkName
	}
	return ""
}

func (m *GoodsListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GoodsListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type GoodsListRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//总条数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	//数据列表
	Data                 []*GoodsListData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GoodsListRes) Reset()         { *m = GoodsListRes{} }
func (m *GoodsListRes) String() string { return proto.CompactTextString(m) }
func (*GoodsListRes) ProtoMessage()    {}
func (*GoodsListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{43}
}

func (m *GoodsListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsListRes.Unmarshal(m, b)
}
func (m *GoodsListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsListRes.Marshal(b, m, deterministic)
}
func (m *GoodsListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsListRes.Merge(m, src)
}
func (m *GoodsListRes) XXX_Size() int {
	return xxx_messageInfo_GoodsListRes.Size(m)
}
func (m *GoodsListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsListRes.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsListRes proto.InternalMessageInfo

func (m *GoodsListRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GoodsListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GoodsListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GoodsListRes) GetData() []*GoodsListData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GoodsListData struct {
	//主键ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品ID
	GoodsId int64 `protobuf:"varint,2,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	//商品名称
	GoodsName string `protobuf:"bytes,3,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//商品图片
	WatermarkImg string `protobuf:"bytes,4,opt,name=watermark_img,json=watermarkImg,proto3" json:"watermark_img"`
	//商品价格
	GoodsPrice float32 `protobuf:"fixed32,5,opt,name=goods_price,json=goodsPrice,proto3" json:"goods_price"`
	//活动价格
	ActivityPrice float32 `protobuf:"fixed32,6,opt,name=activity_price,json=activityPrice,proto3" json:"activity_price"`
	//水印名称
	WatermarkName string `protobuf:"bytes,7,opt,name=watermark_name,json=watermarkName,proto3" json:"watermark_name"`
	//开始时间
	StartTime int64 `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime int64 `protobuf:"varint,9,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//水印ID
	WatermarkId int64 `protobuf:"varint,10,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	//商品图片
	GoodsImage string `protobuf:"bytes,11,opt,name=goods_image,json=goodsImage,proto3" json:"goods_image"`
	// 色值
	Color string `protobuf:"bytes,12,opt,name=color,proto3" json:"color"`
	//是否展示活动价
	IsDisplayActivityPrice bool     `protobuf:"varint,13,opt,name=is_display_activity_price,json=isDisplayActivityPrice,proto3" json:"is_display_activity_price"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GoodsListData) Reset()         { *m = GoodsListData{} }
func (m *GoodsListData) String() string { return proto.CompactTextString(m) }
func (*GoodsListData) ProtoMessage()    {}
func (*GoodsListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{44}
}

func (m *GoodsListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsListData.Unmarshal(m, b)
}
func (m *GoodsListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsListData.Marshal(b, m, deterministic)
}
func (m *GoodsListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsListData.Merge(m, src)
}
func (m *GoodsListData) XXX_Size() int {
	return xxx_messageInfo_GoodsListData.Size(m)
}
func (m *GoodsListData) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsListData.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsListData proto.InternalMessageInfo

func (m *GoodsListData) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GoodsListData) GetGoodsId() int64 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

func (m *GoodsListData) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *GoodsListData) GetWatermarkImg() string {
	if m != nil {
		return m.WatermarkImg
	}
	return ""
}

func (m *GoodsListData) GetGoodsPrice() float32 {
	if m != nil {
		return m.GoodsPrice
	}
	return 0
}

func (m *GoodsListData) GetActivityPrice() float32 {
	if m != nil {
		return m.ActivityPrice
	}
	return 0
}

func (m *GoodsListData) GetWatermarkName() string {
	if m != nil {
		return m.WatermarkName
	}
	return ""
}

func (m *GoodsListData) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GoodsListData) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GoodsListData) GetWatermarkId() int64 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

func (m *GoodsListData) GetGoodsImage() string {
	if m != nil {
		return m.GoodsImage
	}
	return ""
}

func (m *GoodsListData) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func (m *GoodsListData) GetIsDisplayActivityPrice() bool {
	if m != nil {
		return m.IsDisplayActivityPrice
	}
	return false
}

type GoodsDeleteReq struct {
	//水印ID
	WatermarkId int64 `protobuf:"varint,1,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	//数据ID, 可批量删除，传数组
	Id                   []int64  `protobuf:"varint,2,rep,packed,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsDeleteReq) Reset()         { *m = GoodsDeleteReq{} }
func (m *GoodsDeleteReq) String() string { return proto.CompactTextString(m) }
func (*GoodsDeleteReq) ProtoMessage()    {}
func (*GoodsDeleteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{45}
}

func (m *GoodsDeleteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsDeleteReq.Unmarshal(m, b)
}
func (m *GoodsDeleteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsDeleteReq.Marshal(b, m, deterministic)
}
func (m *GoodsDeleteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsDeleteReq.Merge(m, src)
}
func (m *GoodsDeleteReq) XXX_Size() int {
	return xxx_messageInfo_GoodsDeleteReq.Size(m)
}
func (m *GoodsDeleteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsDeleteReq.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsDeleteReq proto.InternalMessageInfo

func (m *GoodsDeleteReq) GetWatermarkId() int64 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

func (m *GoodsDeleteReq) GetId() []int64 {
	if m != nil {
		return m.Id
	}
	return nil
}

type GoodsAddReq struct {
	//水印ID
	WatermarkId int64 `protobuf:"varint,1,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	//商品ID(sku_id)
	GoodsId int64 `protobuf:"varint,2,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	//活动价格
	Price float32 `protobuf:"fixed32,3,opt,name=price,proto3" json:"price"`
	// 色值
	Color string `protobuf:"bytes,4,opt,name=color,proto3" json:"color"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsAddReq) Reset()         { *m = GoodsAddReq{} }
func (m *GoodsAddReq) String() string { return proto.CompactTextString(m) }
func (*GoodsAddReq) ProtoMessage()    {}
func (*GoodsAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{46}
}

func (m *GoodsAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsAddReq.Unmarshal(m, b)
}
func (m *GoodsAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsAddReq.Marshal(b, m, deterministic)
}
func (m *GoodsAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsAddReq.Merge(m, src)
}
func (m *GoodsAddReq) XXX_Size() int {
	return xxx_messageInfo_GoodsAddReq.Size(m)
}
func (m *GoodsAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsAddReq proto.InternalMessageInfo

func (m *GoodsAddReq) GetWatermarkId() int64 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

func (m *GoodsAddReq) GetGoodsId() int64 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

func (m *GoodsAddReq) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GoodsAddReq) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func (m *GoodsAddReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type SearchGoodsReq struct {
	//SKUID
	SkuId int64 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//SPUID
	SpuId int64 `protobuf:"varint,2,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	//商品名称
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	PageSize  int32  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageIndex int32  `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//水印ID
	WatermarkId int64 `protobuf:"varint,6,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchGoodsReq) Reset()         { *m = SearchGoodsReq{} }
func (m *SearchGoodsReq) String() string { return proto.CompactTextString(m) }
func (*SearchGoodsReq) ProtoMessage()    {}
func (*SearchGoodsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{47}
}

func (m *SearchGoodsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGoodsReq.Unmarshal(m, b)
}
func (m *SearchGoodsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGoodsReq.Marshal(b, m, deterministic)
}
func (m *SearchGoodsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGoodsReq.Merge(m, src)
}
func (m *SearchGoodsReq) XXX_Size() int {
	return xxx_messageInfo_SearchGoodsReq.Size(m)
}
func (m *SearchGoodsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGoodsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGoodsReq proto.InternalMessageInfo

func (m *SearchGoodsReq) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SearchGoodsReq) GetSpuId() int64 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *SearchGoodsReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchGoodsReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *SearchGoodsReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *SearchGoodsReq) GetWatermarkId() int64 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

func (m *SearchGoodsReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type SearchGoodsRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//总条数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	//商品列表
	Data                 []*SearchGoodsData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SearchGoodsRes) Reset()         { *m = SearchGoodsRes{} }
func (m *SearchGoodsRes) String() string { return proto.CompactTextString(m) }
func (*SearchGoodsRes) ProtoMessage()    {}
func (*SearchGoodsRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{48}
}

func (m *SearchGoodsRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGoodsRes.Unmarshal(m, b)
}
func (m *SearchGoodsRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGoodsRes.Marshal(b, m, deterministic)
}
func (m *SearchGoodsRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGoodsRes.Merge(m, src)
}
func (m *SearchGoodsRes) XXX_Size() int {
	return xxx_messageInfo_SearchGoodsRes.Size(m)
}
func (m *SearchGoodsRes) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGoodsRes.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGoodsRes proto.InternalMessageInfo

func (m *SearchGoodsRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SearchGoodsRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SearchGoodsRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *SearchGoodsRes) GetData() []*SearchGoodsData {
	if m != nil {
		return m.Data
	}
	return nil
}

type SearchGoodsData struct {
	//SKUID
	SkuId int64 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//SPUID
	SpuId int64 `protobuf:"varint,2,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	//商品名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	//商品图片
	GoodsImage string `protobuf:"bytes,4,opt,name=goods_image,json=goodsImage,proto3" json:"goods_image"`
	//商品价格
	GoodsPrice float32 `protobuf:"fixed32,5,opt,name=goods_price,json=goodsPrice,proto3" json:"goods_price"`
	//是否添加了iiqg
	IsWatermark int32 `protobuf:"varint,6,opt,name=is_watermark,json=isWatermark,proto3" json:"is_watermark"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchGoodsData) Reset()         { *m = SearchGoodsData{} }
func (m *SearchGoodsData) String() string { return proto.CompactTextString(m) }
func (*SearchGoodsData) ProtoMessage()    {}
func (*SearchGoodsData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{49}
}

func (m *SearchGoodsData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGoodsData.Unmarshal(m, b)
}
func (m *SearchGoodsData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGoodsData.Marshal(b, m, deterministic)
}
func (m *SearchGoodsData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGoodsData.Merge(m, src)
}
func (m *SearchGoodsData) XXX_Size() int {
	return xxx_messageInfo_SearchGoodsData.Size(m)
}
func (m *SearchGoodsData) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGoodsData.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGoodsData proto.InternalMessageInfo

func (m *SearchGoodsData) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SearchGoodsData) GetSpuId() int64 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *SearchGoodsData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchGoodsData) GetGoodsImage() string {
	if m != nil {
		return m.GoodsImage
	}
	return ""
}

func (m *SearchGoodsData) GetGoodsPrice() float32 {
	if m != nil {
		return m.GoodsPrice
	}
	return 0
}

func (m *SearchGoodsData) GetIsWatermark() int32 {
	if m != nil {
		return m.IsWatermark
	}
	return 0
}

func (m *SearchGoodsData) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GoodsPriceReq struct {
	//数据ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//水印ID
	WatermarkId int64 `protobuf:"varint,2,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	//价格
	Price float32 `protobuf:"fixed32,3,opt,name=price,proto3" json:"price"`
	// 色值
	Color                string   `protobuf:"bytes,4,opt,name=color,proto3" json:"color"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsPriceReq) Reset()         { *m = GoodsPriceReq{} }
func (m *GoodsPriceReq) String() string { return proto.CompactTextString(m) }
func (*GoodsPriceReq) ProtoMessage()    {}
func (*GoodsPriceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{50}
}

func (m *GoodsPriceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsPriceReq.Unmarshal(m, b)
}
func (m *GoodsPriceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsPriceReq.Marshal(b, m, deterministic)
}
func (m *GoodsPriceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsPriceReq.Merge(m, src)
}
func (m *GoodsPriceReq) XXX_Size() int {
	return xxx_messageInfo_GoodsPriceReq.Size(m)
}
func (m *GoodsPriceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsPriceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsPriceReq proto.InternalMessageInfo

func (m *GoodsPriceReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GoodsPriceReq) GetWatermarkId() int64 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

func (m *GoodsPriceReq) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GoodsPriceReq) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

type StopWatermarkReq struct {
	//水印ID
	WatermarkId          int32    `protobuf:"varint,1,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopWatermarkReq) Reset()         { *m = StopWatermarkReq{} }
func (m *StopWatermarkReq) String() string { return proto.CompactTextString(m) }
func (*StopWatermarkReq) ProtoMessage()    {}
func (*StopWatermarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{51}
}

func (m *StopWatermarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopWatermarkReq.Unmarshal(m, b)
}
func (m *StopWatermarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopWatermarkReq.Marshal(b, m, deterministic)
}
func (m *StopWatermarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopWatermarkReq.Merge(m, src)
}
func (m *StopWatermarkReq) XXX_Size() int {
	return xxx_messageInfo_StopWatermarkReq.Size(m)
}
func (m *StopWatermarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopWatermarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopWatermarkReq proto.InternalMessageInfo

func (m *StopWatermarkReq) GetWatermarkId() int32 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

type GoodsImportReq struct {
	//水印ID
	WatermarkId int64 `protobuf:"varint,1,opt,name=watermark_id,json=watermarkId,proto3" json:"watermark_id"`
	//文件URL路径
	FilePath string `protobuf:"bytes,2,opt,name=file_path,json=filePath,proto3" json:"file_path"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsImportReq) Reset()         { *m = GoodsImportReq{} }
func (m *GoodsImportReq) String() string { return proto.CompactTextString(m) }
func (*GoodsImportReq) ProtoMessage()    {}
func (*GoodsImportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{52}
}

func (m *GoodsImportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsImportReq.Unmarshal(m, b)
}
func (m *GoodsImportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsImportReq.Marshal(b, m, deterministic)
}
func (m *GoodsImportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsImportReq.Merge(m, src)
}
func (m *GoodsImportReq) XXX_Size() int {
	return xxx_messageInfo_GoodsImportReq.Size(m)
}
func (m *GoodsImportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsImportReq.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsImportReq proto.InternalMessageInfo

func (m *GoodsImportReq) GetWatermarkId() int64 {
	if m != nil {
		return m.WatermarkId
	}
	return 0
}

func (m *GoodsImportReq) GetFilePath() string {
	if m != nil {
		return m.FilePath
	}
	return ""
}

func (m *GoodsImportReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GoodsImportRes struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//导入失败商品列表
	Data                 []*ImportFailGoods `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GoodsImportRes) Reset()         { *m = GoodsImportRes{} }
func (m *GoodsImportRes) String() string { return proto.CompactTextString(m) }
func (*GoodsImportRes) ProtoMessage()    {}
func (*GoodsImportRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{53}
}

func (m *GoodsImportRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsImportRes.Unmarshal(m, b)
}
func (m *GoodsImportRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsImportRes.Marshal(b, m, deterministic)
}
func (m *GoodsImportRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsImportRes.Merge(m, src)
}
func (m *GoodsImportRes) XXX_Size() int {
	return xxx_messageInfo_GoodsImportRes.Size(m)
}
func (m *GoodsImportRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsImportRes.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsImportRes proto.InternalMessageInfo

func (m *GoodsImportRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GoodsImportRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GoodsImportRes) GetData() []*ImportFailGoods {
	if m != nil {
		return m.Data
	}
	return nil
}

type ImportFailGoods struct {
	//SKUID
	SkuId int64 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//价格
	Price float32 `protobuf:"fixed32,2,opt,name=price,proto3" json:"price"`
	//导入失败原因
	Remark               string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImportFailGoods) Reset()         { *m = ImportFailGoods{} }
func (m *ImportFailGoods) String() string { return proto.CompactTextString(m) }
func (*ImportFailGoods) ProtoMessage()    {}
func (*ImportFailGoods) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{54}
}

func (m *ImportFailGoods) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImportFailGoods.Unmarshal(m, b)
}
func (m *ImportFailGoods) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImportFailGoods.Marshal(b, m, deterministic)
}
func (m *ImportFailGoods) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportFailGoods.Merge(m, src)
}
func (m *ImportFailGoods) XXX_Size() int {
	return xxx_messageInfo_ImportFailGoods.Size(m)
}
func (m *ImportFailGoods) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportFailGoods.DiscardUnknown(m)
}

var xxx_messageInfo_ImportFailGoods proto.InternalMessageInfo

func (m *ImportFailGoods) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ImportFailGoods) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ImportFailGoods) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type ChristmasAddAddressReq struct {
	//奖品列表ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//收件人姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//收件地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	//手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	//宠物类型 1猫 2狗
	PetType int32 `protobuf:"varint,5,opt,name=pet_type,json=petType,proto3" json:"pet_type"`
	//scrm_userid
	ScrmUserid           string   `protobuf:"bytes,6,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChristmasAddAddressReq) Reset()         { *m = ChristmasAddAddressReq{} }
func (m *ChristmasAddAddressReq) String() string { return proto.CompactTextString(m) }
func (*ChristmasAddAddressReq) ProtoMessage()    {}
func (*ChristmasAddAddressReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{55}
}

func (m *ChristmasAddAddressReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChristmasAddAddressReq.Unmarshal(m, b)
}
func (m *ChristmasAddAddressReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChristmasAddAddressReq.Marshal(b, m, deterministic)
}
func (m *ChristmasAddAddressReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChristmasAddAddressReq.Merge(m, src)
}
func (m *ChristmasAddAddressReq) XXX_Size() int {
	return xxx_messageInfo_ChristmasAddAddressReq.Size(m)
}
func (m *ChristmasAddAddressReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChristmasAddAddressReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChristmasAddAddressReq proto.InternalMessageInfo

func (m *ChristmasAddAddressReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChristmasAddAddressReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChristmasAddAddressReq) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *ChristmasAddAddressReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *ChristmasAddAddressReq) GetPetType() int32 {
	if m != nil {
		return m.PetType
	}
	return 0
}

func (m *ChristmasAddAddressReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

type BaseResponseNew struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponseNew) Reset()         { *m = BaseResponseNew{} }
func (m *BaseResponseNew) String() string { return proto.CompactTextString(m) }
func (*BaseResponseNew) ProtoMessage()    {}
func (*BaseResponseNew) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{56}
}

func (m *BaseResponseNew) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponseNew.Unmarshal(m, b)
}
func (m *BaseResponseNew) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponseNew.Marshal(b, m, deterministic)
}
func (m *BaseResponseNew) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponseNew.Merge(m, src)
}
func (m *BaseResponseNew) XXX_Size() int {
	return xxx_messageInfo_BaseResponseNew.Size(m)
}
func (m *BaseResponseNew) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponseNew.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponseNew proto.InternalMessageInfo

func (m *BaseResponseNew) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type ChannelStatisticsReq struct {
	//渠道名称
	ChannelName string `protobuf:"bytes,1,opt,name=channel_name,json=channelName,proto3" json:"channel_name"`
	//渠道参数
	Parameter string `protobuf:"bytes,2,opt,name=parameter,proto3" json:"parameter"`
	//网址
	Url string `protobuf:"bytes,3,opt,name=url,proto3" json:"url"`
	//ip地址（前端不需要传）
	Ip                   string   `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelStatisticsReq) Reset()         { *m = ChannelStatisticsReq{} }
func (m *ChannelStatisticsReq) String() string { return proto.CompactTextString(m) }
func (*ChannelStatisticsReq) ProtoMessage()    {}
func (*ChannelStatisticsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{57}
}

func (m *ChannelStatisticsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelStatisticsReq.Unmarshal(m, b)
}
func (m *ChannelStatisticsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelStatisticsReq.Marshal(b, m, deterministic)
}
func (m *ChannelStatisticsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelStatisticsReq.Merge(m, src)
}
func (m *ChannelStatisticsReq) XXX_Size() int {
	return xxx_messageInfo_ChannelStatisticsReq.Size(m)
}
func (m *ChannelStatisticsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelStatisticsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelStatisticsReq proto.InternalMessageInfo

func (m *ChannelStatisticsReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelStatisticsReq) GetParameter() string {
	if m != nil {
		return m.Parameter
	}
	return ""
}

func (m *ChannelStatisticsReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ChannelStatisticsReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type HandOutCouponRequest struct {
	//优惠券ID
	CouponId string `protobuf:"bytes,1,opt,name=couponId,proto3" json:"couponId"`
	//1门店券 2优惠券
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	//手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	// 来源
	From int32 `protobuf:"varint,4,opt,name=from,proto3" json:"from"`
	// scrm_user_id
	ScrmUserId           string   `protobuf:"bytes,5,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandOutCouponRequest) Reset()         { *m = HandOutCouponRequest{} }
func (m *HandOutCouponRequest) String() string { return proto.CompactTextString(m) }
func (*HandOutCouponRequest) ProtoMessage()    {}
func (*HandOutCouponRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{58}
}

func (m *HandOutCouponRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandOutCouponRequest.Unmarshal(m, b)
}
func (m *HandOutCouponRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandOutCouponRequest.Marshal(b, m, deterministic)
}
func (m *HandOutCouponRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandOutCouponRequest.Merge(m, src)
}
func (m *HandOutCouponRequest) XXX_Size() int {
	return xxx_messageInfo_HandOutCouponRequest.Size(m)
}
func (m *HandOutCouponRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandOutCouponRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandOutCouponRequest proto.InternalMessageInfo

func (m *HandOutCouponRequest) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *HandOutCouponRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *HandOutCouponRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *HandOutCouponRequest) GetFrom() int32 {
	if m != nil {
		return m.From
	}
	return 0
}

func (m *HandOutCouponRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

type HandOutCouponResponse struct {
	//商城券返回的是派发的券ID，门店券返回的是券code
	CouponCode           string   `protobuf:"bytes,1,opt,name=CouponCode,proto3" json:"CouponCode"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandOutCouponResponse) Reset()         { *m = HandOutCouponResponse{} }
func (m *HandOutCouponResponse) String() string { return proto.CompactTextString(m) }
func (*HandOutCouponResponse) ProtoMessage()    {}
func (*HandOutCouponResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{59}
}

func (m *HandOutCouponResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandOutCouponResponse.Unmarshal(m, b)
}
func (m *HandOutCouponResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandOutCouponResponse.Marshal(b, m, deterministic)
}
func (m *HandOutCouponResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandOutCouponResponse.Merge(m, src)
}
func (m *HandOutCouponResponse) XXX_Size() int {
	return xxx_messageInfo_HandOutCouponResponse.Size(m)
}
func (m *HandOutCouponResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandOutCouponResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandOutCouponResponse proto.InternalMessageInfo

func (m *HandOutCouponResponse) GetCouponCode() string {
	if m != nil {
		return m.CouponCode
	}
	return ""
}

type BaseProposalRequest struct {
	//用户Id
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//建议Id
	ProposalId int64 `protobuf:"varint,2,opt,name=proposalId,proto3" json:"proposalId"`
	//支持的用户id
	SupportUserId        string   `protobuf:"bytes,3,opt,name=supportUserId,proto3" json:"supportUserId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseProposalRequest) Reset()         { *m = BaseProposalRequest{} }
func (m *BaseProposalRequest) String() string { return proto.CompactTextString(m) }
func (*BaseProposalRequest) ProtoMessage()    {}
func (*BaseProposalRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{60}
}

func (m *BaseProposalRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseProposalRequest.Unmarshal(m, b)
}
func (m *BaseProposalRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseProposalRequest.Marshal(b, m, deterministic)
}
func (m *BaseProposalRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseProposalRequest.Merge(m, src)
}
func (m *BaseProposalRequest) XXX_Size() int {
	return xxx_messageInfo_BaseProposalRequest.Size(m)
}
func (m *BaseProposalRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseProposalRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseProposalRequest proto.InternalMessageInfo

func (m *BaseProposalRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *BaseProposalRequest) GetProposalId() int64 {
	if m != nil {
		return m.ProposalId
	}
	return 0
}

func (m *BaseProposalRequest) GetSupportUserId() string {
	if m != nil {
		return m.SupportUserId
	}
	return ""
}

type SubmitProposalRequest struct {
	//用户id
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//建议内容
	ProposalBody string `protobuf:"bytes,2,opt,name=proposalBody,proto3" json:"proposalBody"`
	//头像
	Profile string `protobuf:"bytes,3,opt,name=profile,proto3" json:"profile"`
	//手机
	Mobile               string   `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitProposalRequest) Reset()         { *m = SubmitProposalRequest{} }
func (m *SubmitProposalRequest) String() string { return proto.CompactTextString(m) }
func (*SubmitProposalRequest) ProtoMessage()    {}
func (*SubmitProposalRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{61}
}

func (m *SubmitProposalRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitProposalRequest.Unmarshal(m, b)
}
func (m *SubmitProposalRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitProposalRequest.Marshal(b, m, deterministic)
}
func (m *SubmitProposalRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitProposalRequest.Merge(m, src)
}
func (m *SubmitProposalRequest) XXX_Size() int {
	return xxx_messageInfo_SubmitProposalRequest.Size(m)
}
func (m *SubmitProposalRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitProposalRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitProposalRequest proto.InternalMessageInfo

func (m *SubmitProposalRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *SubmitProposalRequest) GetProposalBody() string {
	if m != nil {
		return m.ProposalBody
	}
	return ""
}

func (m *SubmitProposalRequest) GetProfile() string {
	if m != nil {
		return m.Profile
	}
	return ""
}

func (m *SubmitProposalRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

type SubmitProposalResponse struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitProposalResponse) Reset()         { *m = SubmitProposalResponse{} }
func (m *SubmitProposalResponse) String() string { return proto.CompactTextString(m) }
func (*SubmitProposalResponse) ProtoMessage()    {}
func (*SubmitProposalResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{62}
}

func (m *SubmitProposalResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitProposalResponse.Unmarshal(m, b)
}
func (m *SubmitProposalResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitProposalResponse.Marshal(b, m, deterministic)
}
func (m *SubmitProposalResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitProposalResponse.Merge(m, src)
}
func (m *SubmitProposalResponse) XXX_Size() int {
	return xxx_messageInfo_SubmitProposalResponse.Size(m)
}
func (m *SubmitProposalResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitProposalResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitProposalResponse proto.InternalMessageInfo

func (m *SubmitProposalResponse) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetProposalListRequest struct {
	//获取前面多少名，默认3名
	Size                 int32    `protobuf:"varint,1,opt,name=size,proto3" json:"size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProposalListRequest) Reset()         { *m = GetProposalListRequest{} }
func (m *GetProposalListRequest) String() string { return proto.CompactTextString(m) }
func (*GetProposalListRequest) ProtoMessage()    {}
func (*GetProposalListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{63}
}

func (m *GetProposalListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposalListRequest.Unmarshal(m, b)
}
func (m *GetProposalListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposalListRequest.Marshal(b, m, deterministic)
}
func (m *GetProposalListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposalListRequest.Merge(m, src)
}
func (m *GetProposalListRequest) XXX_Size() int {
	return xxx_messageInfo_GetProposalListRequest.Size(m)
}
func (m *GetProposalListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposalListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposalListRequest proto.InternalMessageInfo

func (m *GetProposalListRequest) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type GetProposalListResponse struct {
	//排名建议
	List                 []*Proposal `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetProposalListResponse) Reset()         { *m = GetProposalListResponse{} }
func (m *GetProposalListResponse) String() string { return proto.CompactTextString(m) }
func (*GetProposalListResponse) ProtoMessage()    {}
func (*GetProposalListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{64}
}

func (m *GetProposalListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposalListResponse.Unmarshal(m, b)
}
func (m *GetProposalListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposalListResponse.Marshal(b, m, deterministic)
}
func (m *GetProposalListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposalListResponse.Merge(m, src)
}
func (m *GetProposalListResponse) XXX_Size() int {
	return xxx_messageInfo_GetProposalListResponse.Size(m)
}
func (m *GetProposalListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposalListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposalListResponse proto.InternalMessageInfo

func (m *GetProposalListResponse) GetList() []*Proposal {
	if m != nil {
		return m.List
	}
	return nil
}

type Proposal struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//用户id
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId"`
	//建议内容
	ProposalBody string `protobuf:"bytes,3,opt,name=proposalBody,proto3" json:"proposalBody"`
	//头像
	Profile string `protobuf:"bytes,4,opt,name=profile,proto3" json:"profile"`
	//用户名
	UserName string `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName"`
	//支持数
	SupportNum int64 `protobuf:"varint,6,opt,name=supportNum,proto3" json:"supportNum"`
	//是否支持过 0 否 1 是
	IsSupport            int32    `protobuf:"varint,7,opt,name=isSupport,proto3" json:"isSupport"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Proposal) Reset()         { *m = Proposal{} }
func (m *Proposal) String() string { return proto.CompactTextString(m) }
func (*Proposal) ProtoMessage()    {}
func (*Proposal) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{65}
}

func (m *Proposal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Proposal.Unmarshal(m, b)
}
func (m *Proposal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Proposal.Marshal(b, m, deterministic)
}
func (m *Proposal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Proposal.Merge(m, src)
}
func (m *Proposal) XXX_Size() int {
	return xxx_messageInfo_Proposal.Size(m)
}
func (m *Proposal) XXX_DiscardUnknown() {
	xxx_messageInfo_Proposal.DiscardUnknown(m)
}

var xxx_messageInfo_Proposal proto.InternalMessageInfo

func (m *Proposal) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Proposal) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *Proposal) GetProposalBody() string {
	if m != nil {
		return m.ProposalBody
	}
	return ""
}

func (m *Proposal) GetProfile() string {
	if m != nil {
		return m.Profile
	}
	return ""
}

func (m *Proposal) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *Proposal) GetSupportNum() int64 {
	if m != nil {
		return m.SupportNum
	}
	return 0
}

func (m *Proposal) GetIsSupport() int32 {
	if m != nil {
		return m.IsSupport
	}
	return 0
}

type GetPetMarketUserCouponReq struct {
	// 用户id
	ScrmUserId string `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	// 优惠券模板id
	VoucherTIds []int64 `protobuf:"varint,2,rep,packed,name=voucher_t_ids,json=voucherTIds,proto3" json:"voucher_t_ids"`
	// 领取开始时间戳
	StartTime int64 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 领取结束时间戳
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetMarketUserCouponReq) Reset()         { *m = GetPetMarketUserCouponReq{} }
func (m *GetPetMarketUserCouponReq) String() string { return proto.CompactTextString(m) }
func (*GetPetMarketUserCouponReq) ProtoMessage()    {}
func (*GetPetMarketUserCouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{66}
}

func (m *GetPetMarketUserCouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetMarketUserCouponReq.Unmarshal(m, b)
}
func (m *GetPetMarketUserCouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetMarketUserCouponReq.Marshal(b, m, deterministic)
}
func (m *GetPetMarketUserCouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetMarketUserCouponReq.Merge(m, src)
}
func (m *GetPetMarketUserCouponReq) XXX_Size() int {
	return xxx_messageInfo_GetPetMarketUserCouponReq.Size(m)
}
func (m *GetPetMarketUserCouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetMarketUserCouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetMarketUserCouponReq proto.InternalMessageInfo

func (m *GetPetMarketUserCouponReq) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *GetPetMarketUserCouponReq) GetVoucherTIds() []int64 {
	if m != nil {
		return m.VoucherTIds
	}
	return nil
}

func (m *GetPetMarketUserCouponReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetPetMarketUserCouponReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type UserVoucher struct {
	VoucherId      int64 `protobuf:"varint,1,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id"`
	VoucherTId     int64 `protobuf:"varint,2,opt,name=voucher_t_id,json=voucherTId,proto3" json:"voucher_t_id"`
	VoucherOwnerId int64 `protobuf:"varint,3,opt,name=voucher_owner_id,json=voucherOwnerId,proto3" json:"voucher_owner_id"`
	// 代金券状态(1-未用,2-已用,3-过期,4-收回)
	VoucherState         int64    `protobuf:"varint,4,opt,name=voucher_state,json=voucherState,proto3" json:"voucher_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserVoucher) Reset()         { *m = UserVoucher{} }
func (m *UserVoucher) String() string { return proto.CompactTextString(m) }
func (*UserVoucher) ProtoMessage()    {}
func (*UserVoucher) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{67}
}

func (m *UserVoucher) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserVoucher.Unmarshal(m, b)
}
func (m *UserVoucher) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserVoucher.Marshal(b, m, deterministic)
}
func (m *UserVoucher) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserVoucher.Merge(m, src)
}
func (m *UserVoucher) XXX_Size() int {
	return xxx_messageInfo_UserVoucher.Size(m)
}
func (m *UserVoucher) XXX_DiscardUnknown() {
	xxx_messageInfo_UserVoucher.DiscardUnknown(m)
}

var xxx_messageInfo_UserVoucher proto.InternalMessageInfo

func (m *UserVoucher) GetVoucherId() int64 {
	if m != nil {
		return m.VoucherId
	}
	return 0
}

func (m *UserVoucher) GetVoucherTId() int64 {
	if m != nil {
		return m.VoucherTId
	}
	return 0
}

func (m *UserVoucher) GetVoucherOwnerId() int64 {
	if m != nil {
		return m.VoucherOwnerId
	}
	return 0
}

func (m *UserVoucher) GetVoucherState() int64 {
	if m != nil {
		return m.VoucherState
	}
	return 0
}

type GetPetMarketUserCouponResponse struct {
	Code                 int64          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	List                 []*UserVoucher `protobuf:"bytes,3,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetPetMarketUserCouponResponse) Reset()         { *m = GetPetMarketUserCouponResponse{} }
func (m *GetPetMarketUserCouponResponse) String() string { return proto.CompactTextString(m) }
func (*GetPetMarketUserCouponResponse) ProtoMessage()    {}
func (*GetPetMarketUserCouponResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{68}
}

func (m *GetPetMarketUserCouponResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetMarketUserCouponResponse.Unmarshal(m, b)
}
func (m *GetPetMarketUserCouponResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetMarketUserCouponResponse.Marshal(b, m, deterministic)
}
func (m *GetPetMarketUserCouponResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetMarketUserCouponResponse.Merge(m, src)
}
func (m *GetPetMarketUserCouponResponse) XXX_Size() int {
	return xxx_messageInfo_GetPetMarketUserCouponResponse.Size(m)
}
func (m *GetPetMarketUserCouponResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetMarketUserCouponResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetMarketUserCouponResponse proto.InternalMessageInfo

func (m *GetPetMarketUserCouponResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPetMarketUserCouponResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPetMarketUserCouponResponse) GetList() []*UserVoucher {
	if m != nil {
		return m.List
	}
	return nil
}

type GetPetMarketTasksReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetMarketTasksReq) Reset()         { *m = GetPetMarketTasksReq{} }
func (m *GetPetMarketTasksReq) String() string { return proto.CompactTextString(m) }
func (*GetPetMarketTasksReq) ProtoMessage()    {}
func (*GetPetMarketTasksReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{69}
}

func (m *GetPetMarketTasksReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetMarketTasksReq.Unmarshal(m, b)
}
func (m *GetPetMarketTasksReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetMarketTasksReq.Marshal(b, m, deterministic)
}
func (m *GetPetMarketTasksReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetMarketTasksReq.Merge(m, src)
}
func (m *GetPetMarketTasksReq) XXX_Size() int {
	return xxx_messageInfo_GetPetMarketTasksReq.Size(m)
}
func (m *GetPetMarketTasksReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetMarketTasksReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetMarketTasksReq proto.InternalMessageInfo

type PetMarketTask struct {
	Id                   int64    `protobuf:"varint,1,opt,name=Id,proto3" json:"Id"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title"`
	ObjValue             string   `protobuf:"bytes,4,opt,name=obj_value,json=objValue,proto3" json:"obj_value"`
	RewardValue          string   `protobuf:"bytes,5,opt,name=reward_value,json=rewardValue,proto3" json:"reward_value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetMarketTask) Reset()         { *m = PetMarketTask{} }
func (m *PetMarketTask) String() string { return proto.CompactTextString(m) }
func (*PetMarketTask) ProtoMessage()    {}
func (*PetMarketTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{70}
}

func (m *PetMarketTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetMarketTask.Unmarshal(m, b)
}
func (m *PetMarketTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetMarketTask.Marshal(b, m, deterministic)
}
func (m *PetMarketTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetMarketTask.Merge(m, src)
}
func (m *PetMarketTask) XXX_Size() int {
	return xxx_messageInfo_PetMarketTask.Size(m)
}
func (m *PetMarketTask) XXX_DiscardUnknown() {
	xxx_messageInfo_PetMarketTask.DiscardUnknown(m)
}

var xxx_messageInfo_PetMarketTask proto.InternalMessageInfo

func (m *PetMarketTask) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetMarketTask) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PetMarketTask) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *PetMarketTask) GetObjValue() string {
	if m != nil {
		return m.ObjValue
	}
	return ""
}

func (m *PetMarketTask) GetRewardValue() string {
	if m != nil {
		return m.RewardValue
	}
	return ""
}

type GetPetMarketTasksResponse struct {
	Code                 int64            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	List                 []*PetMarketTask `protobuf:"bytes,3,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPetMarketTasksResponse) Reset()         { *m = GetPetMarketTasksResponse{} }
func (m *GetPetMarketTasksResponse) String() string { return proto.CompactTextString(m) }
func (*GetPetMarketTasksResponse) ProtoMessage()    {}
func (*GetPetMarketTasksResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{71}
}

func (m *GetPetMarketTasksResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetMarketTasksResponse.Unmarshal(m, b)
}
func (m *GetPetMarketTasksResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetMarketTasksResponse.Marshal(b, m, deterministic)
}
func (m *GetPetMarketTasksResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetMarketTasksResponse.Merge(m, src)
}
func (m *GetPetMarketTasksResponse) XXX_Size() int {
	return xxx_messageInfo_GetPetMarketTasksResponse.Size(m)
}
func (m *GetPetMarketTasksResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetMarketTasksResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetMarketTasksResponse proto.InternalMessageInfo

func (m *GetPetMarketTasksResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPetMarketTasksResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPetMarketTasksResponse) GetList() []*PetMarketTask {
	if m != nil {
		return m.List
	}
	return nil
}

type AddPetMarketUserTaskReq struct {
	ScrmUserid           string   `protobuf:"bytes,1,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	TaskId               int64    `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPetMarketUserTaskReq) Reset()         { *m = AddPetMarketUserTaskReq{} }
func (m *AddPetMarketUserTaskReq) String() string { return proto.CompactTextString(m) }
func (*AddPetMarketUserTaskReq) ProtoMessage()    {}
func (*AddPetMarketUserTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{72}
}

func (m *AddPetMarketUserTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPetMarketUserTaskReq.Unmarshal(m, b)
}
func (m *AddPetMarketUserTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPetMarketUserTaskReq.Marshal(b, m, deterministic)
}
func (m *AddPetMarketUserTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPetMarketUserTaskReq.Merge(m, src)
}
func (m *AddPetMarketUserTaskReq) XXX_Size() int {
	return xxx_messageInfo_AddPetMarketUserTaskReq.Size(m)
}
func (m *AddPetMarketUserTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPetMarketUserTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPetMarketUserTaskReq proto.InternalMessageInfo

func (m *AddPetMarketUserTaskReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

func (m *AddPetMarketUserTaskReq) GetTaskId() int64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

type GetPetMarketUserTasksReq struct {
	ScrmUserid           string   `protobuf:"bytes,1,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	TaskId               int64    `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetMarketUserTasksReq) Reset()         { *m = GetPetMarketUserTasksReq{} }
func (m *GetPetMarketUserTasksReq) String() string { return proto.CompactTextString(m) }
func (*GetPetMarketUserTasksReq) ProtoMessage()    {}
func (*GetPetMarketUserTasksReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{73}
}

func (m *GetPetMarketUserTasksReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetMarketUserTasksReq.Unmarshal(m, b)
}
func (m *GetPetMarketUserTasksReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetMarketUserTasksReq.Marshal(b, m, deterministic)
}
func (m *GetPetMarketUserTasksReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetMarketUserTasksReq.Merge(m, src)
}
func (m *GetPetMarketUserTasksReq) XXX_Size() int {
	return xxx_messageInfo_GetPetMarketUserTasksReq.Size(m)
}
func (m *GetPetMarketUserTasksReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetMarketUserTasksReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetMarketUserTasksReq proto.InternalMessageInfo

func (m *GetPetMarketUserTasksReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

func (m *GetPetMarketUserTasksReq) GetTaskId() int64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

type PetMarketUserTask struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	ScrmUserid           string   `protobuf:"bytes,2,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	TaskId               int64    `protobuf:"varint,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetMarketUserTask) Reset()         { *m = PetMarketUserTask{} }
func (m *PetMarketUserTask) String() string { return proto.CompactTextString(m) }
func (*PetMarketUserTask) ProtoMessage()    {}
func (*PetMarketUserTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{74}
}

func (m *PetMarketUserTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetMarketUserTask.Unmarshal(m, b)
}
func (m *PetMarketUserTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetMarketUserTask.Marshal(b, m, deterministic)
}
func (m *PetMarketUserTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetMarketUserTask.Merge(m, src)
}
func (m *PetMarketUserTask) XXX_Size() int {
	return xxx_messageInfo_PetMarketUserTask.Size(m)
}
func (m *PetMarketUserTask) XXX_DiscardUnknown() {
	xxx_messageInfo_PetMarketUserTask.DiscardUnknown(m)
}

var xxx_messageInfo_PetMarketUserTask proto.InternalMessageInfo

func (m *PetMarketUserTask) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetMarketUserTask) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

func (m *PetMarketUserTask) GetTaskId() int64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

type GetPetMarketUserTasksResponse struct {
	Code                 int64                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	List                 []*PetMarketUserTask `protobuf:"bytes,3,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPetMarketUserTasksResponse) Reset()         { *m = GetPetMarketUserTasksResponse{} }
func (m *GetPetMarketUserTasksResponse) String() string { return proto.CompactTextString(m) }
func (*GetPetMarketUserTasksResponse) ProtoMessage()    {}
func (*GetPetMarketUserTasksResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{75}
}

func (m *GetPetMarketUserTasksResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetMarketUserTasksResponse.Unmarshal(m, b)
}
func (m *GetPetMarketUserTasksResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetMarketUserTasksResponse.Marshal(b, m, deterministic)
}
func (m *GetPetMarketUserTasksResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetMarketUserTasksResponse.Merge(m, src)
}
func (m *GetPetMarketUserTasksResponse) XXX_Size() int {
	return xxx_messageInfo_GetPetMarketUserTasksResponse.Size(m)
}
func (m *GetPetMarketUserTasksResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetMarketUserTasksResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetMarketUserTasksResponse proto.InternalMessageInfo

func (m *GetPetMarketUserTasksResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPetMarketUserTasksResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPetMarketUserTasksResponse) GetList() []*PetMarketUserTask {
	if m != nil {
		return m.List
	}
	return nil
}

type AddPetMarketRewardReq struct {
	ScrmUserid           string   `protobuf:"bytes,1,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	PrizeType            int64    `protobuf:"varint,2,opt,name=prize_type,json=prizeType,proto3" json:"prize_type"`
	Receiver             string   `protobuf:"bytes,3,opt,name=receiver,proto3" json:"receiver"`
	Mobile               string   `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	Address              string   `protobuf:"bytes,5,opt,name=address,proto3" json:"address"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPetMarketRewardReq) Reset()         { *m = AddPetMarketRewardReq{} }
func (m *AddPetMarketRewardReq) String() string { return proto.CompactTextString(m) }
func (*AddPetMarketRewardReq) ProtoMessage()    {}
func (*AddPetMarketRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{76}
}

func (m *AddPetMarketRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPetMarketRewardReq.Unmarshal(m, b)
}
func (m *AddPetMarketRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPetMarketRewardReq.Marshal(b, m, deterministic)
}
func (m *AddPetMarketRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPetMarketRewardReq.Merge(m, src)
}
func (m *AddPetMarketRewardReq) XXX_Size() int {
	return xxx_messageInfo_AddPetMarketRewardReq.Size(m)
}
func (m *AddPetMarketRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPetMarketRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPetMarketRewardReq proto.InternalMessageInfo

func (m *AddPetMarketRewardReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

func (m *AddPetMarketRewardReq) GetPrizeType() int64 {
	if m != nil {
		return m.PrizeType
	}
	return 0
}

func (m *AddPetMarketRewardReq) GetReceiver() string {
	if m != nil {
		return m.Receiver
	}
	return ""
}

func (m *AddPetMarketRewardReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *AddPetMarketRewardReq) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

type GetPetMarketUserRewardReq struct {
	ScrmUserid           string   `protobuf:"bytes,1,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetMarketUserRewardReq) Reset()         { *m = GetPetMarketUserRewardReq{} }
func (m *GetPetMarketUserRewardReq) String() string { return proto.CompactTextString(m) }
func (*GetPetMarketUserRewardReq) ProtoMessage()    {}
func (*GetPetMarketUserRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{77}
}

func (m *GetPetMarketUserRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetMarketUserRewardReq.Unmarshal(m, b)
}
func (m *GetPetMarketUserRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetMarketUserRewardReq.Marshal(b, m, deterministic)
}
func (m *GetPetMarketUserRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetMarketUserRewardReq.Merge(m, src)
}
func (m *GetPetMarketUserRewardReq) XXX_Size() int {
	return xxx_messageInfo_GetPetMarketUserRewardReq.Size(m)
}
func (m *GetPetMarketUserRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetMarketUserRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetMarketUserRewardReq proto.InternalMessageInfo

func (m *GetPetMarketUserRewardReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

type PetMarketUserReward struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	ScrmUserid           string   `protobuf:"bytes,2,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	PrizeType            int64    `protobuf:"varint,3,opt,name=prize_type,json=prizeType,proto3" json:"prize_type"`
	Receiver             string   `protobuf:"bytes,4,opt,name=receiver,proto3" json:"receiver"`
	Mobile               string   `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile"`
	Address              string   `protobuf:"bytes,6,opt,name=address,proto3" json:"address"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetMarketUserReward) Reset()         { *m = PetMarketUserReward{} }
func (m *PetMarketUserReward) String() string { return proto.CompactTextString(m) }
func (*PetMarketUserReward) ProtoMessage()    {}
func (*PetMarketUserReward) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{78}
}

func (m *PetMarketUserReward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetMarketUserReward.Unmarshal(m, b)
}
func (m *PetMarketUserReward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetMarketUserReward.Marshal(b, m, deterministic)
}
func (m *PetMarketUserReward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetMarketUserReward.Merge(m, src)
}
func (m *PetMarketUserReward) XXX_Size() int {
	return xxx_messageInfo_PetMarketUserReward.Size(m)
}
func (m *PetMarketUserReward) XXX_DiscardUnknown() {
	xxx_messageInfo_PetMarketUserReward.DiscardUnknown(m)
}

var xxx_messageInfo_PetMarketUserReward proto.InternalMessageInfo

func (m *PetMarketUserReward) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetMarketUserReward) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

func (m *PetMarketUserReward) GetPrizeType() int64 {
	if m != nil {
		return m.PrizeType
	}
	return 0
}

func (m *PetMarketUserReward) GetReceiver() string {
	if m != nil {
		return m.Receiver
	}
	return ""
}

func (m *PetMarketUserReward) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *PetMarketUserReward) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

type GetPetMarketUserRewardResponse struct {
	Code                 int64                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Detail               *PetMarketUserReward `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPetMarketUserRewardResponse) Reset()         { *m = GetPetMarketUserRewardResponse{} }
func (m *GetPetMarketUserRewardResponse) String() string { return proto.CompactTextString(m) }
func (*GetPetMarketUserRewardResponse) ProtoMessage()    {}
func (*GetPetMarketUserRewardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{79}
}

func (m *GetPetMarketUserRewardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetMarketUserRewardResponse.Unmarshal(m, b)
}
func (m *GetPetMarketUserRewardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetMarketUserRewardResponse.Marshal(b, m, deterministic)
}
func (m *GetPetMarketUserRewardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetMarketUserRewardResponse.Merge(m, src)
}
func (m *GetPetMarketUserRewardResponse) XXX_Size() int {
	return xxx_messageInfo_GetPetMarketUserRewardResponse.Size(m)
}
func (m *GetPetMarketUserRewardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetMarketUserRewardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetMarketUserRewardResponse proto.InternalMessageInfo

func (m *GetPetMarketUserRewardResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPetMarketUserRewardResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPetMarketUserRewardResponse) GetDetail() *PetMarketUserReward {
	if m != nil {
		return m.Detail
	}
	return nil
}

type CountPetMarketRewardReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountPetMarketRewardReq) Reset()         { *m = CountPetMarketRewardReq{} }
func (m *CountPetMarketRewardReq) String() string { return proto.CompactTextString(m) }
func (*CountPetMarketRewardReq) ProtoMessage()    {}
func (*CountPetMarketRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{80}
}

func (m *CountPetMarketRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountPetMarketRewardReq.Unmarshal(m, b)
}
func (m *CountPetMarketRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountPetMarketRewardReq.Marshal(b, m, deterministic)
}
func (m *CountPetMarketRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountPetMarketRewardReq.Merge(m, src)
}
func (m *CountPetMarketRewardReq) XXX_Size() int {
	return xxx_messageInfo_CountPetMarketRewardReq.Size(m)
}
func (m *CountPetMarketRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CountPetMarketRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_CountPetMarketRewardReq proto.InternalMessageInfo

type CountPetMarketRewardReqResponse struct {
	Code                 int64    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int64    `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountPetMarketRewardReqResponse) Reset()         { *m = CountPetMarketRewardReqResponse{} }
func (m *CountPetMarketRewardReqResponse) String() string { return proto.CompactTextString(m) }
func (*CountPetMarketRewardReqResponse) ProtoMessage()    {}
func (*CountPetMarketRewardReqResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{81}
}

func (m *CountPetMarketRewardReqResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountPetMarketRewardReqResponse.Unmarshal(m, b)
}
func (m *CountPetMarketRewardReqResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountPetMarketRewardReqResponse.Marshal(b, m, deterministic)
}
func (m *CountPetMarketRewardReqResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountPetMarketRewardReqResponse.Merge(m, src)
}
func (m *CountPetMarketRewardReqResponse) XXX_Size() int {
	return xxx_messageInfo_CountPetMarketRewardReqResponse.Size(m)
}
func (m *CountPetMarketRewardReqResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CountPetMarketRewardReqResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CountPetMarketRewardReqResponse proto.InternalMessageInfo

func (m *CountPetMarketRewardReqResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CountPetMarketRewardReqResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CountPetMarketRewardReqResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CountUserOrderWithSkuidsRequest struct {
	MemberId             string   `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	Skuids               []int64  `protobuf:"varint,2,rep,packed,name=skuids,proto3" json:"skuids"`
	StartTime            string   `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              string   `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountUserOrderWithSkuidsRequest) Reset()         { *m = CountUserOrderWithSkuidsRequest{} }
func (m *CountUserOrderWithSkuidsRequest) String() string { return proto.CompactTextString(m) }
func (*CountUserOrderWithSkuidsRequest) ProtoMessage()    {}
func (*CountUserOrderWithSkuidsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{82}
}

func (m *CountUserOrderWithSkuidsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountUserOrderWithSkuidsRequest.Unmarshal(m, b)
}
func (m *CountUserOrderWithSkuidsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountUserOrderWithSkuidsRequest.Marshal(b, m, deterministic)
}
func (m *CountUserOrderWithSkuidsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountUserOrderWithSkuidsRequest.Merge(m, src)
}
func (m *CountUserOrderWithSkuidsRequest) XXX_Size() int {
	return xxx_messageInfo_CountUserOrderWithSkuidsRequest.Size(m)
}
func (m *CountUserOrderWithSkuidsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CountUserOrderWithSkuidsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CountUserOrderWithSkuidsRequest proto.InternalMessageInfo

func (m *CountUserOrderWithSkuidsRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *CountUserOrderWithSkuidsRequest) GetSkuids() []int64 {
	if m != nil {
		return m.Skuids
	}
	return nil
}

func (m *CountUserOrderWithSkuidsRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *CountUserOrderWithSkuidsRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type CountUserOrderWithSkuidsResponse struct {
	Code                 int64    `protobuf:"varint,1,opt,name=Code,proto3" json:"Code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int64    `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountUserOrderWithSkuidsResponse) Reset()         { *m = CountUserOrderWithSkuidsResponse{} }
func (m *CountUserOrderWithSkuidsResponse) String() string { return proto.CompactTextString(m) }
func (*CountUserOrderWithSkuidsResponse) ProtoMessage()    {}
func (*CountUserOrderWithSkuidsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{83}
}

func (m *CountUserOrderWithSkuidsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountUserOrderWithSkuidsResponse.Unmarshal(m, b)
}
func (m *CountUserOrderWithSkuidsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountUserOrderWithSkuidsResponse.Marshal(b, m, deterministic)
}
func (m *CountUserOrderWithSkuidsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountUserOrderWithSkuidsResponse.Merge(m, src)
}
func (m *CountUserOrderWithSkuidsResponse) XXX_Size() int {
	return xxx_messageInfo_CountUserOrderWithSkuidsResponse.Size(m)
}
func (m *CountUserOrderWithSkuidsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CountUserOrderWithSkuidsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CountUserOrderWithSkuidsResponse proto.InternalMessageInfo

func (m *CountUserOrderWithSkuidsResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CountUserOrderWithSkuidsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CountUserOrderWithSkuidsResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CouponSendMultiReq struct {
	//优惠券模版id
	TemplateIdArr []int32 `protobuf:"varint,1,rep,packed,name=template_id_arr,json=templateIdArr,proto3" json:"template_id_arr"`
	//每个手机号的发放数量
	Number int32 `protobuf:"varint,2,opt,name=number,proto3" json:"number"`
	//渠道
	Channel int32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel"`
	//要发放的手机号数组
	PhoneArr             []string `protobuf:"bytes,4,rep,name=phone_arr,json=phoneArr,proto3" json:"phone_arr"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponSendMultiReq) Reset()         { *m = CouponSendMultiReq{} }
func (m *CouponSendMultiReq) String() string { return proto.CompactTextString(m) }
func (*CouponSendMultiReq) ProtoMessage()    {}
func (*CouponSendMultiReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{84}
}

func (m *CouponSendMultiReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponSendMultiReq.Unmarshal(m, b)
}
func (m *CouponSendMultiReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponSendMultiReq.Marshal(b, m, deterministic)
}
func (m *CouponSendMultiReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponSendMultiReq.Merge(m, src)
}
func (m *CouponSendMultiReq) XXX_Size() int {
	return xxx_messageInfo_CouponSendMultiReq.Size(m)
}
func (m *CouponSendMultiReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponSendMultiReq.DiscardUnknown(m)
}

var xxx_messageInfo_CouponSendMultiReq proto.InternalMessageInfo

func (m *CouponSendMultiReq) GetTemplateIdArr() []int32 {
	if m != nil {
		return m.TemplateIdArr
	}
	return nil
}

func (m *CouponSendMultiReq) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *CouponSendMultiReq) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *CouponSendMultiReq) GetPhoneArr() []string {
	if m != nil {
		return m.PhoneArr
	}
	return nil
}

type CouponSendMultiRes struct {
	Code                 int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg                  string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 []*CouponSendMultiData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	Status               int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *CouponSendMultiRes) Reset()         { *m = CouponSendMultiRes{} }
func (m *CouponSendMultiRes) String() string { return proto.CompactTextString(m) }
func (*CouponSendMultiRes) ProtoMessage()    {}
func (*CouponSendMultiRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{85}
}

func (m *CouponSendMultiRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponSendMultiRes.Unmarshal(m, b)
}
func (m *CouponSendMultiRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponSendMultiRes.Marshal(b, m, deterministic)
}
func (m *CouponSendMultiRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponSendMultiRes.Merge(m, src)
}
func (m *CouponSendMultiRes) XXX_Size() int {
	return xxx_messageInfo_CouponSendMultiRes.Size(m)
}
func (m *CouponSendMultiRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponSendMultiRes.DiscardUnknown(m)
}

var xxx_messageInfo_CouponSendMultiRes proto.InternalMessageInfo

func (m *CouponSendMultiRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CouponSendMultiRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *CouponSendMultiRes) GetData() []*CouponSendMultiData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *CouponSendMultiRes) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type CouponSendMultiData struct {
	CouponTemplateId     string        `protobuf:"bytes,1,opt,name=coupon_template_id,json=couponTemplateId,proto3" json:"coupon_template_id"`
	CouponName           string        `protobuf:"bytes,2,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name"`
	CouponList           []*CouponList `protobuf:"bytes,3,rep,name=coupon_list,json=couponList,proto3" json:"coupon_list"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CouponSendMultiData) Reset()         { *m = CouponSendMultiData{} }
func (m *CouponSendMultiData) String() string { return proto.CompactTextString(m) }
func (*CouponSendMultiData) ProtoMessage()    {}
func (*CouponSendMultiData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{86}
}

func (m *CouponSendMultiData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponSendMultiData.Unmarshal(m, b)
}
func (m *CouponSendMultiData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponSendMultiData.Marshal(b, m, deterministic)
}
func (m *CouponSendMultiData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponSendMultiData.Merge(m, src)
}
func (m *CouponSendMultiData) XXX_Size() int {
	return xxx_messageInfo_CouponSendMultiData.Size(m)
}
func (m *CouponSendMultiData) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponSendMultiData.DiscardUnknown(m)
}

var xxx_messageInfo_CouponSendMultiData proto.InternalMessageInfo

func (m *CouponSendMultiData) GetCouponTemplateId() string {
	if m != nil {
		return m.CouponTemplateId
	}
	return ""
}

func (m *CouponSendMultiData) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *CouponSendMultiData) GetCouponList() []*CouponList {
	if m != nil {
		return m.CouponList
	}
	return nil
}

type CouponList struct {
	CouponId             int32    `protobuf:"varint,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	CouponCode           string   `protobuf:"bytes,2,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code"`
	UserPhone            string   `protobuf:"bytes,3,opt,name=user_phone,json=userPhone,proto3" json:"user_phone"`
	Uuid                 string   `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponList) Reset()         { *m = CouponList{} }
func (m *CouponList) String() string { return proto.CompactTextString(m) }
func (*CouponList) ProtoMessage()    {}
func (*CouponList) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{87}
}

func (m *CouponList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponList.Unmarshal(m, b)
}
func (m *CouponList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponList.Marshal(b, m, deterministic)
}
func (m *CouponList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponList.Merge(m, src)
}
func (m *CouponList) XXX_Size() int {
	return xxx_messageInfo_CouponList.Size(m)
}
func (m *CouponList) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponList.DiscardUnknown(m)
}

var xxx_messageInfo_CouponList proto.InternalMessageInfo

func (m *CouponList) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *CouponList) GetCouponCode() string {
	if m != nil {
		return m.CouponCode
	}
	return ""
}

func (m *CouponList) GetUserPhone() string {
	if m != nil {
		return m.UserPhone
	}
	return ""
}

func (m *CouponList) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

type LuckyDrawActivityRecommendReq struct {
	// 1 推荐  2取消
	DownOrUp int32 `protobuf:"varint,1,opt,name=down_or_up,json=downOrUp,proto3" json:"down_or_up"`
	// id
	Id                   int32    `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyDrawActivityRecommendReq) Reset()         { *m = LuckyDrawActivityRecommendReq{} }
func (m *LuckyDrawActivityRecommendReq) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivityRecommendReq) ProtoMessage()    {}
func (*LuckyDrawActivityRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{88}
}

func (m *LuckyDrawActivityRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivityRecommendReq.Unmarshal(m, b)
}
func (m *LuckyDrawActivityRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivityRecommendReq.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivityRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivityRecommendReq.Merge(m, src)
}
func (m *LuckyDrawActivityRecommendReq) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivityRecommendReq.Size(m)
}
func (m *LuckyDrawActivityRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivityRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivityRecommendReq proto.InternalMessageInfo

func (m *LuckyDrawActivityRecommendReq) GetDownOrUp() int32 {
	if m != nil {
		return m.DownOrUp
	}
	return 0
}

func (m *LuckyDrawActivityRecommendReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type LuckyDrawActivityRecommendResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// id
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyDrawActivityRecommendResponse) Reset()         { *m = LuckyDrawActivityRecommendResponse{} }
func (m *LuckyDrawActivityRecommendResponse) String() string { return proto.CompactTextString(m) }
func (*LuckyDrawActivityRecommendResponse) ProtoMessage()    {}
func (*LuckyDrawActivityRecommendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f9ac9eda565a13e8, []int{89}
}

func (m *LuckyDrawActivityRecommendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyDrawActivityRecommendResponse.Unmarshal(m, b)
}
func (m *LuckyDrawActivityRecommendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyDrawActivityRecommendResponse.Marshal(b, m, deterministic)
}
func (m *LuckyDrawActivityRecommendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyDrawActivityRecommendResponse.Merge(m, src)
}
func (m *LuckyDrawActivityRecommendResponse) XXX_Size() int {
	return xxx_messageInfo_LuckyDrawActivityRecommendResponse.Size(m)
}
func (m *LuckyDrawActivityRecommendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyDrawActivityRecommendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyDrawActivityRecommendResponse proto.InternalMessageInfo

func (m *LuckyDrawActivityRecommendResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *LuckyDrawActivityRecommendResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func init() {
	proto.RegisterType((*BaseRequest)(nil), "ac.baseRequest")
	proto.RegisterType((*BaseResponse)(nil), "ac.baseResponse")
	proto.RegisterType((*PetTeaseRequest)(nil), "ac.petTeaseRequest")
	proto.RegisterType((*PetTeaseResponse)(nil), "ac.PetTeaseResponse")
	proto.RegisterType((*PetTeaseListResponse)(nil), "ac.petTeaseListResponse")
	proto.RegisterType((*PetTease)(nil), "ac.petTease")
	proto.RegisterType((*ExamineListResponse)(nil), "ac.examineListResponse")
	proto.RegisterType((*Examine)(nil), "ac.examine")
	proto.RegisterType((*SendCouponReq)(nil), "ac.SendCouponReq")
	proto.RegisterType((*SendCouponRes)(nil), "ac.SendCouponRes")
	proto.RegisterType((*SaveShareReq)(nil), "ac.SaveShareReq")
	proto.RegisterType((*GetMemberShareReq)(nil), "ac.GetMemberShareReq")
	proto.RegisterType((*GetMemberShareRes)(nil), "ac.GetMemberShareRes")
	proto.RegisterType((*MemberShareData)(nil), "ac.MemberShareData")
	proto.RegisterType((*ShareRelationshipData)(nil), "ac.ShareRelationshipData")
	proto.RegisterType((*CouponData)(nil), "ac.CouponData")
	proto.RegisterType((*SaveExamineAnswerReq)(nil), "ac.SaveExamineAnswerReq")
	proto.RegisterType((*Answer)(nil), "ac.Answer")
	proto.RegisterType((*SaveExamineAnswerRes)(nil), "ac.SaveExamineAnswerRes")
	proto.RegisterType((*MemberAnswer)(nil), "ac.MemberAnswer")
	proto.RegisterType((*GetCouponTemplateReq)(nil), "ac.GetCouponTemplateReq")
	proto.RegisterType((*GetCouponTemplateRes)(nil), "ac.GetCouponTemplateRes")
	proto.RegisterType((*CouponTemplateData)(nil), "ac.CouponTemplateData")
	proto.RegisterType((*SaveMemberCouponReq)(nil), "ac.SaveMemberCouponReq")
	proto.RegisterType((*GetMemberCouponReq)(nil), "ac.GetMemberCouponReq")
	proto.RegisterType((*GetMemberCouponRes)(nil), "ac.GetMemberCouponRes")
	proto.RegisterType((*MemberCouponData)(nil), "ac.MemberCouponData")
	proto.RegisterType((*SaveSubscribeMessageReq)(nil), "ac.SaveSubscribeMessageReq")
	proto.RegisterType((*TemplateData)(nil), "ac.TemplateData")
	proto.RegisterType((*CancelSubscribeMessageReq)(nil), "ac.CancelSubscribeMessageReq")
	proto.RegisterType((*CouponMessageRes)(nil), "ac.CouponMessageRes")
	proto.RegisterType((*CouponMessageData)(nil), "ac.CouponMessageData")
	proto.RegisterType((*GetCatMonthInfoRes)(nil), "ac.GetCatMonthInfoRes")
	proto.RegisterType((*CatMonthData)(nil), "ac.CatMonthData")
	proto.RegisterType((*SaveCatMonthInfoReq)(nil), "ac.SaveCatMonthInfoReq")
	proto.RegisterType((*SaveWatermarkReq)(nil), "ac.SaveWatermarkReq")
	proto.RegisterType((*SaveWatermarkRes)(nil), "ac.SaveWatermarkRes")
	proto.RegisterType((*WatermarkListReq)(nil), "ac.WatermarkListReq")
	proto.RegisterType((*WatermarkListRes)(nil), "ac.WatermarkListRes")
	proto.RegisterType((*WatermarkData)(nil), "ac.WatermarkData")
	proto.RegisterType((*GetWatermarkReq)(nil), "ac.GetWatermarkReq")
	proto.RegisterType((*GetWatermarkRes)(nil), "ac.GetWatermarkRes")
	proto.RegisterType((*GoodsListReq)(nil), "ac.GoodsListReq")
	proto.RegisterType((*GoodsListRes)(nil), "ac.GoodsListRes")
	proto.RegisterType((*GoodsListData)(nil), "ac.GoodsListData")
	proto.RegisterType((*GoodsDeleteReq)(nil), "ac.GoodsDeleteReq")
	proto.RegisterType((*GoodsAddReq)(nil), "ac.GoodsAddReq")
	proto.RegisterType((*SearchGoodsReq)(nil), "ac.SearchGoodsReq")
	proto.RegisterType((*SearchGoodsRes)(nil), "ac.SearchGoodsRes")
	proto.RegisterType((*SearchGoodsData)(nil), "ac.SearchGoodsData")
	proto.RegisterType((*GoodsPriceReq)(nil), "ac.GoodsPriceReq")
	proto.RegisterType((*StopWatermarkReq)(nil), "ac.StopWatermarkReq")
	proto.RegisterType((*GoodsImportReq)(nil), "ac.GoodsImportReq")
	proto.RegisterType((*GoodsImportRes)(nil), "ac.GoodsImportRes")
	proto.RegisterType((*ImportFailGoods)(nil), "ac.ImportFailGoods")
	proto.RegisterType((*ChristmasAddAddressReq)(nil), "ac.ChristmasAddAddressReq")
	proto.RegisterType((*BaseResponseNew)(nil), "ac.BaseResponseNew")
	proto.RegisterType((*ChannelStatisticsReq)(nil), "ac.ChannelStatisticsReq")
	proto.RegisterType((*HandOutCouponRequest)(nil), "ac.HandOutCouponRequest")
	proto.RegisterType((*HandOutCouponResponse)(nil), "ac.HandOutCouponResponse")
	proto.RegisterType((*BaseProposalRequest)(nil), "ac.BaseProposalRequest")
	proto.RegisterType((*SubmitProposalRequest)(nil), "ac.SubmitProposalRequest")
	proto.RegisterType((*SubmitProposalResponse)(nil), "ac.SubmitProposalResponse")
	proto.RegisterType((*GetProposalListRequest)(nil), "ac.GetProposalListRequest")
	proto.RegisterType((*GetProposalListResponse)(nil), "ac.GetProposalListResponse")
	proto.RegisterType((*Proposal)(nil), "ac.Proposal")
	proto.RegisterType((*GetPetMarketUserCouponReq)(nil), "ac.GetPetMarketUserCouponReq")
	proto.RegisterType((*UserVoucher)(nil), "ac.UserVoucher")
	proto.RegisterType((*GetPetMarketUserCouponResponse)(nil), "ac.GetPetMarketUserCouponResponse")
	proto.RegisterType((*GetPetMarketTasksReq)(nil), "ac.GetPetMarketTasksReq")
	proto.RegisterType((*PetMarketTask)(nil), "ac.PetMarketTask")
	proto.RegisterType((*GetPetMarketTasksResponse)(nil), "ac.GetPetMarketTasksResponse")
	proto.RegisterType((*AddPetMarketUserTaskReq)(nil), "ac.AddPetMarketUserTaskReq")
	proto.RegisterType((*GetPetMarketUserTasksReq)(nil), "ac.GetPetMarketUserTasksReq")
	proto.RegisterType((*PetMarketUserTask)(nil), "ac.PetMarketUserTask")
	proto.RegisterType((*GetPetMarketUserTasksResponse)(nil), "ac.GetPetMarketUserTasksResponse")
	proto.RegisterType((*AddPetMarketRewardReq)(nil), "ac.AddPetMarketRewardReq")
	proto.RegisterType((*GetPetMarketUserRewardReq)(nil), "ac.GetPetMarketUserRewardReq")
	proto.RegisterType((*PetMarketUserReward)(nil), "ac.PetMarketUserReward")
	proto.RegisterType((*GetPetMarketUserRewardResponse)(nil), "ac.GetPetMarketUserRewardResponse")
	proto.RegisterType((*CountPetMarketRewardReq)(nil), "ac.CountPetMarketRewardReq")
	proto.RegisterType((*CountPetMarketRewardReqResponse)(nil), "ac.CountPetMarketRewardReqResponse")
	proto.RegisterType((*CountUserOrderWithSkuidsRequest)(nil), "ac.CountUserOrderWithSkuidsRequest")
	proto.RegisterType((*CountUserOrderWithSkuidsResponse)(nil), "ac.CountUserOrderWithSkuidsResponse")
	proto.RegisterType((*CouponSendMultiReq)(nil), "ac.CouponSendMultiReq")
	proto.RegisterType((*CouponSendMultiRes)(nil), "ac.CouponSendMultiRes")
	proto.RegisterType((*CouponSendMultiData)(nil), "ac.CouponSendMultiData")
	proto.RegisterType((*CouponList)(nil), "ac.CouponList")
	proto.RegisterType((*LuckyDrawActivityRecommendReq)(nil), "ac.LuckyDrawActivityRecommendReq")
	proto.RegisterType((*LuckyDrawActivityRecommendResponse)(nil), "ac.LuckyDrawActivityRecommendResponse")
}

func init() { proto.RegisterFile("ac/activity_model.proto", fileDescriptor_f9ac9eda565a13e8) }

var fileDescriptor_f9ac9eda565a13e8 = []byte{
	// 3361 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x5a, 0xcd, 0x6f, 0x1c, 0xc7,
	0x95, 0x47, 0x4f, 0xcf, 0x0c, 0x67, 0xde, 0xcc, 0x90, 0x54, 0x8b, 0x1f, 0x43, 0xcb, 0xb2, 0xa8,
	0x92, 0x65, 0xcb, 0xbb, 0x86, 0xb5, 0xf0, 0x62, 0xb1, 0x58, 0x6c, 0x72, 0xa0, 0xa9, 0x44, 0x99,
	0x40, 0x94, 0x94, 0x19, 0xda, 0x3e, 0x05, 0x83, 0x62, 0x77, 0x91, 0xd3, 0xe6, 0xf4, 0x87, 0xba,
	0xaa, 0x29, 0x51, 0x80, 0x63, 0xc7, 0x88, 0x93, 0x43, 0x80, 0x20, 0x09, 0xe2, 0x53, 0x90, 0x63,
	0x2e, 0x39, 0x26, 0x87, 0x5c, 0x72, 0xc9, 0x21, 0x40, 0x90, 0x4b, 0xfe, 0x8c, 0xfc, 0x0f, 0x39,
	0x05, 0xf5, 0xd5, 0x5d, 0xdd, 0x3d, 0xd3, 0x94, 0x64, 0xe6, 0xd6, 0xf5, 0xba, 0xfa, 0xd5, 0xab,
	0xdf, 0xfb, 0xbd, 0x57, 0xaf, 0xaa, 0x1a, 0xb6, 0xb1, 0x7b, 0x17, 0xbb, 0xcc, 0x3f, 0xf3, 0xd9,
	0xf9, 0x34, 0x88, 0x3c, 0x32, 0x7f, 0x2f, 0x4e, 0x22, 0x16, 0x39, 0x0d, 0xec, 0xa2, 0xdb, 0xd0,
	0x3b, 0xc2, 0x94, 0x8c, 0xc9, 0x93, 0x94, 0x50, 0xe6, 0x6c, 0x41, 0x3b, 0x88, 0x8e, 0xfc, 0x39,
	0x19, 0x5a, 0xbb, 0xd6, 0x9d, 0xee, 0x58, 0xb5, 0xd0, 0x18, 0xfa, 0xb2, 0x1b, 0x8d, 0xa3, 0x90,
	0x12, 0xc7, 0x81, 0xa6, 0x1b, 0x79, 0xb2, 0x57, 0x6b, 0x2c, 0x9e, 0x9d, 0x21, 0xac, 0x04, 0x84,
	0x52, 0x7c, 0x42, 0x86, 0x0d, 0xf1, 0xb1, 0x6e, 0x3a, 0x1b, 0xd0, 0x22, 0x49, 0x12, 0x25, 0x43,
	0x5b, 0xc8, 0x65, 0x03, 0x3d, 0x86, 0xb5, 0x98, 0xb0, 0x43, 0x72, 0xf1, 0xf0, 0xce, 0x6d, 0x58,
	0x65, 0x09, 0xc1, 0x34, 0x4d, 0xc8, 0x74, 0x4e, 0xce, 0xc8, 0x5c, 0x8c, 0xd0, 0x1a, 0x0f, 0xb4,
	0xf4, 0x01, 0x17, 0xa2, 0xaf, 0x2c, 0x58, 0x7f, 0x9c, 0xa9, 0xbc, 0x3c, 0x53, 0x9d, 0x6b, 0xd0,
	0xf5, 0xe9, 0x34, 0x8a, 0x49, 0x48, 0xbc, 0x61, 0x53, 0x28, 0xea, 0xf8, 0xf4, 0x91, 0x68, 0x3b,
	0xbb, 0xd0, 0xf4, 0x30, 0xc3, 0xc3, 0xd6, 0xae, 0x75, 0xa7, 0xf7, 0x7e, 0xff, 0x3d, 0xec, 0xbe,
	0x97, 0xcd, 0x4b, 0xbc, 0x41, 0xbf, 0xb2, 0x60, 0x43, 0x8b, 0x1e, 0xf8, 0x94, 0x5d, 0xaa, 0x6d,
	0x39, 0x66, 0xcd, 0x02, 0x66, 0xb9, 0x59, 0xf6, 0x12, 0xb3, 0xfe, 0xdc, 0x80, 0x8e, 0x16, 0x7d,
	0x4d, 0xe8, 0x9d, 0xeb, 0x00, 0x47, 0xe4, 0xc4, 0x0f, 0xa7, 0xcc, 0x0f, 0x88, 0x32, 0xb0, 0x2b,
	0x24, 0x87, 0x7e, 0x40, 0x9c, 0x1d, 0xe8, 0x90, 0xd0, 0x93, 0x2f, 0xa5, 0x99, 0x2b, 0x24, 0xf4,
	0xc4, 0xab, 0x1b, 0xd0, 0x73, 0x13, 0x82, 0x19, 0x91, 0x6f, 0x5b, 0xe2, 0x2d, 0x48, 0x91, 0xee,
	0x90, 0x10, 0xec, 0xce, 0xa6, 0x41, 0x14, 0x92, 0xf3, 0x61, 0x5b, 0x76, 0x10, 0xa2, 0x03, 0x2e,
	0x71, 0x6e, 0x42, 0x3f, 0x21, 0x5e, 0xea, 0x12, 0xd5, 0x63, 0x45, 0xf4, 0xe8, 0x49, 0x99, 0xec,
	0xc2, 0x07, 0x89, 0xd2, 0x38, 0x0a, 0xa7, 0x21, 0x0e, 0xc8, 0xb0, 0xa3, 0x06, 0x11, 0xa2, 0x87,
	0x58, 0x1a, 0xe8, 0xd3, 0xa9, 0x8b, 0x99, 0x3b, 0x1b, 0x76, 0xc5, 0x04, 0x57, 0x7c, 0xba, 0xcf,
	0x9b, 0xdc, 0xf9, 0xfa, 0xdb, 0x68, 0x08, 0xe2, 0xcb, 0x8e, 0xfa, 0x32, 0x42, 0xcf, 0xe1, 0x2a,
	0x79, 0x86, 0x03, 0x3f, 0xbc, 0x7c, 0xc7, 0xde, 0x80, 0xe6, 0x3d, 0xee, 0xc0, 0xa6, 0x70, 0x60,
	0x8f, 0x3b, 0x50, 0x0d, 0x35, 0x16, 0x2f, 0xd0, 0x0c, 0x56, 0x94, 0xc0, 0x59, 0x85, 0x86, 0xef,
	0xa9, 0xd1, 0x1a, 0xbe, 0xc7, 0x35, 0xb2, 0x28, 0xf6, 0x5d, 0x35, 0x92, 0x6c, 0x70, 0x14, 0x70,
	0x48, 0x9f, 0x92, 0x64, 0x3a, 0xf7, 0x29, 0x1b, 0xda, 0xbb, 0x36, 0x47, 0x41, 0x8a, 0xb8, 0xf9,
	0x9c, 0x04, 0xb2, 0xa5, 0xb9, 0x24, 0x5b, 0x68, 0x1f, 0x06, 0x13, 0x12, 0x7a, 0xfb, 0x62, 0xd6,
	0x63, 0xf2, 0x64, 0x29, 0x5b, 0x5e, 0x03, 0x05, 0xcd, 0xc8, 0x53, 0x3c, 0xc9, 0xda, 0xe8, 0xa4,
	0xa8, 0x84, 0x5e, 0x0a, 0x48, 0x8e, 0x62, 0xb9, 0xb4, 0x57, 0xf2, 0x7a, 0x0e, 0xfd, 0x09, 0x3e,
	0x23, 0x93, 0x19, 0x4e, 0x78, 0x66, 0x71, 0x6e, 0xc1, 0x80, 0xf2, 0xe7, 0x64, 0x5a, 0xb0, 0xb9,
	0x2f, 0x85, 0x07, 0xd2, 0xf2, 0x1b, 0xd0, 0x9b, 0x91, 0x79, 0xac, 0xbb, 0xc8, 0xc1, 0x81, 0x8b,
	0x54, 0x07, 0x8e, 0xcd, 0x19, 0x66, 0x58, 0x1b, 0xa0, 0x5a, 0xe8, 0x9b, 0x70, 0xe5, 0x3e, 0x61,
	0x07, 0x24, 0x38, 0x22, 0x49, 0x36, 0xe4, 0x32, 0x7c, 0xd6, 0xc1, 0x4e, 0x29, 0x51, 0xd0, 0xf0,
	0x47, 0xf4, 0xb9, 0x55, 0xfd, 0xfe, 0x72, 0xa0, 0x79, 0xdb, 0x80, 0xa6, 0xf7, 0xfe, 0x55, 0xce,
	0x1f, 0x63, 0x14, 0xce, 0x20, 0x85, 0xd7, 0x1f, 0x6d, 0x58, 0x2b, 0xbd, 0xa9, 0x4b, 0x07, 0x47,
	0x7e, 0xe8, 0xf9, 0xe1, 0xc9, 0x34, 0x4c, 0xf9, 0x27, 0x3a, 0x1d, 0x28, 0xe9, 0x43, 0x21, 0x14,
	0x31, 0x83, 0xd9, 0xd4, 0x0f, 0x3d, 0xf2, 0x4c, 0x58, 0xc5, 0x89, 0x80, 0xd9, 0x88, 0xb7, 0x39,
	0xd4, 0x01, 0xa6, 0x8c, 0x24, 0x53, 0x76, 0x1e, 0x13, 0x95, 0x4f, 0x41, 0x8a, 0x0e, 0xcf, 0x63,
	0xc2, 0x03, 0x5a, 0x77, 0xf0, 0xd9, 0x5c, 0xe7, 0x04, 0xf5, 0xd1, 0x21, 0x17, 0x39, 0xaf, 0x43,
	0xd7, 0x9d, 0xe1, 0x04, 0xbb, 0x8c, 0x24, 0x2a, 0x25, 0xe4, 0x02, 0x0e, 0x95, 0x1f, 0xc4, 0x09,
	0xa1, 0x54, 0x25, 0x03, 0xdd, 0xe4, 0xdf, 0x05, 0x38, 0xf4, 0x8f, 0x09, 0x65, 0x91, 0x4a, 0x03,
	0xb9, 0xc0, 0xf0, 0x71, 0xd7, 0xf4, 0xb1, 0xf3, 0x3d, 0xd8, 0x16, 0x64, 0x99, 0x26, 0x64, 0x8e,
	0x99, 0x1f, 0x85, 0x74, 0xe6, 0xc7, 0x53, 0x81, 0x2e, 0x88, 0xe8, 0xdc, 0xe1, 0xe8, 0x2a, 0xef,
	0xe5, 0x3d, 0x04, 0xc6, 0x9b, 0x74, 0x91, 0xd8, 0xb9, 0x9b, 0x65, 0x24, 0xa1, 0xa6, 0x27, 0xd4,
	0xac, 0x72, 0x35, 0x32, 0x40, 0xc4, 0xb7, 0x2a, 0x43, 0x89, 0x0f, 0x1c, 0x68, 0x86, 0xbe, 0x7b,
	0x3a, 0xec, 0x4b, 0xa6, 0xf3, 0x67, 0xf4, 0x18, 0x36, 0x17, 0x0e, 0x5a, 0x66, 0xb3, 0x55, 0xc3,
	0xe6, 0x46, 0x81, 0xcd, 0x3f, 0xb1, 0x00, 0x72, 0x03, 0x78, 0xe8, 0x28, 0x2b, 0x71, 0x10, 0xa5,
	0x21, 0x53, 0x84, 0xec, 0x4b, 0xe1, 0x9e, 0x90, 0x19, 0x09, 0xd2, 0xaf, 0x44, 0x3d, 0x1f, 0x88,
	0x32, 0xcc, 0x52, 0xaa, 0x68, 0xa0, 0x5a, 0xe5, 0x8c, 0xdc, 0x2c, 0x67, 0x64, 0xf4, 0x1c, 0x36,
	0x78, 0x14, 0x7f, 0x4b, 0x66, 0xb8, 0x3d, 0x91, 0x88, 0xea, 0x42, 0xeb, 0x0d, 0x45, 0xf7, 0x86,
	0x40, 0x12, 0x38, 0x92, 0xea, 0x23, 0x21, 0xe7, 0xdf, 0xed, 0x15, 0xe2, 0x57, 0xb6, 0x38, 0xae,
	0x0f, 0x39, 0xae, 0x2a, 0x83, 0xf0, 0x67, 0xf4, 0x5f, 0xd0, 0x96, 0xdf, 0x56, 0x12, 0x6b, 0x9e,
	0x21, 0x1b, 0x85, 0x0c, 0xf9, 0xb9, 0xb5, 0xd0, 0xdc, 0xcb, 0x89, 0xe4, 0x37, 0x0b, 0x91, 0xbc,
	0x9e, 0x47, 0xb2, 0x39, 0x41, 0xf4, 0x17, 0x0b, 0xfa, 0xa6, 0xb8, 0x18, 0x84, 0x56, 0x7d, 0x10,
	0xaa, 0x7c, 0x57, 0x13, 0x84, 0xf6, 0x05, 0x41, 0xd8, 0xac, 0x09, 0xc2, 0x56, 0x4d, 0x10, 0xb6,
	0x4b, 0x41, 0x88, 0xee, 0xc0, 0xc6, 0x7d, 0xc2, 0x24, 0x09, 0x0f, 0x49, 0x10, 0xcf, 0x31, 0x13,
	0x39, 0x75, 0x1d, 0x6c, 0x96, 0xf9, 0x82, 0x3f, 0xa2, 0x1f, 0x5b, 0x0b, 0xbb, 0x5e, 0x0e, 0xe8,
	0xff, 0x91, 0x81, 0xce, 0xf9, 0xb4, 0x95, 0x47, 0xa6, 0x1e, 0xc8, 0xc8, 0xa0, 0x5f, 0x36, 0xc0,
	0xa9, 0xbe, 0x74, 0x76, 0xa1, 0x7f, 0x16, 0xa5, 0xee, 0x8c, 0x63, 0x38, 0x55, 0xa6, 0x77, 0xc7,
	0xa0, 0x64, 0x87, 0x23, 0xcf, 0x79, 0x0b, 0xd6, 0xf2, 0x1e, 0x12, 0x67, 0x69, 0xdc, 0x40, 0x77,
	0x92, 0x48, 0xbf, 0x09, 0xab, 0x79, 0x3f, 0x8f, 0x50, 0x57, 0xd9, 0xaa, 0xf5, 0x1f, 0xde, 0x23,
	0xd4, 0x2d, 0x6a, 0x8b, 0x13, 0xdf, 0xd5, 0x71, 0x95, 0x69, 0x7b, 0xcc, 0x85, 0xc5, 0x7e, 0x3c,
	0x1e, 0x75, 0x8a, 0xcd, 0xfa, 0x4d, 0xb8, 0xd0, 0xb9, 0x0b, 0x1b, 0xa5, 0x7e, 0x53, 0x46, 0x9e,
	0x31, 0xe5, 0xb2, 0x2b, 0x85, 0xce, 0x87, 0xe4, 0x19, 0x43, 0xa7, 0x70, 0x95, 0x07, 0x81, 0x64,
	0x61, 0x5e, 0x2d, 0x5c, 0x94, 0x20, 0x16, 0xc6, 0x73, 0x9e, 0x20, 0x04, 0x41, 0x65, 0xf6, 0x50,
	0x09, 0x82, 0x13, 0x14, 0x1d, 0x80, 0x93, 0x2d, 0x9c, 0x17, 0x57, 0x26, 0x25, 0x75, 0x8d, 0x8a,
	0xba, 0x2f, 0xac, 0x05, 0xfa, 0x2e, 0x87, 0x4a, 0x77, 0x0a, 0xf1, 0xbb, 0x91, 0xc7, 0xaf, 0x91,
	0xea, 0x25, 0x91, 0xee, 0xc2, 0x7a, 0xf9, 0x4d, 0x11, 0x3d, 0xab, 0x54, 0x54, 0xc5, 0xb0, 0x2d,
	0x6a, 0x9d, 0xf4, 0x88, 0xba, 0x89, 0x7f, 0x44, 0x0e, 0xa4, 0x21, 0x75, 0x48, 0x6c, 0xc3, 0x0a,
	0xdf, 0xc9, 0x68, 0x5f, 0x74, 0xc7, 0x6d, 0xde, 0x1c, 0x79, 0x59, 0x9a, 0xb1, 0x05, 0xe3, 0x45,
	0x9a, 0x59, 0xc0, 0xf5, 0x00, 0xfa, 0x05, 0x92, 0xdf, 0x80, 0x1e, 0x53, 0x6d, 0x83, 0xe3, 0x5a,
	0x34, 0xf2, 0x78, 0x69, 0x1d, 0xe3, 0x13, 0x32, 0x4d, 0x93, 0xb9, 0x86, 0x8b, 0xb7, 0x3f, 0x4c,
	0xe6, 0x22, 0xc7, 0x48, 0x83, 0x4d, 0x27, 0xf7, 0x94, 0x4c, 0xb8, 0xe5, 0x01, 0xec, 0xec, 0xe3,
	0xd0, 0x25, 0xf3, 0xcb, 0x98, 0x22, 0xfa, 0xa1, 0x05, 0xeb, 0x12, 0xda, 0x4c, 0xcb, 0xe5, 0xb8,
	0xf8, 0x9d, 0x42, 0xb6, 0xd8, 0xcc, 0xb3, 0x85, 0x1a, 0xc7, 0x00, 0xf0, 0x1f, 0x36, 0x5c, 0xa9,
	0xbc, 0x33, 0x4d, 0xb6, 0x0a, 0x5e, 0x29, 0xe1, 0xdb, 0xa8, 0xe0, 0x9b, 0x83, 0x60, 0x17, 0x40,
	0x30, 0x71, 0x6f, 0xd6, 0xe3, 0xde, 0xaa, 0xe0, 0xce, 0x0b, 0x3d, 0xdd, 0x45, 0xad, 0xdf, 0x6d,
	0x59, 0xe8, 0x29, 0xe9, 0x44, 0x2e, 0xe3, 0x95, 0x02, 0x81, 0xd7, 0x5b, 0x76, 0x5d, 0x81, 0xd0,
	0x11, 0x1d, 0xf2, 0xf8, 0xcf, 0x35, 0xa8, 0x59, 0xc8, 0xd2, 0x4b, 0x69, 0xc8, 0xcb, 0x15, 0x65,
	0x05, 0xd4, 0x55, 0x11, 0xbd, 0xca, 0xbe, 0xae, 0x14, 0xf6, 0xfd, 0x72, 0xd8, 0xf3, 0x79, 0x92,
	0xe3, 0x63, 0xe2, 0x32, 0xff, 0x8c, 0xc8, 0x52, 0x6c, 0x20, 0x0c, 0x1c, 0x64, 0x52, 0xe1, 0x9e,
	0x1d, 0xe8, 0xa4, 0x94, 0x4c, 0x93, 0x74, 0x4e, 0x86, 0xab, 0x12, 0xcc, 0x94, 0x92, 0x71, 0x3a,
	0x17, 0x54, 0x49, 0x48, 0x80, 0x93, 0x53, 0x3a, 0x5c, 0x93, 0x6f, 0x54, 0x13, 0xfd, 0x40, 0x64,
	0x94, 0x7d, 0xcc, 0x0e, 0xa2, 0x90, 0xcd, 0x46, 0xe1, 0x71, 0xf4, 0x6f, 0xac, 0x08, 0xf4, 0x30,
	0x06, 0xd3, 0x7e, 0x67, 0x41, 0xdf, 0x14, 0x1b, 0x30, 0x5a, 0x05, 0x18, 0xb7, 0xf9, 0x52, 0x7d,
	0x62, 0x44, 0x68, 0xdb, 0x0f, 0x4e, 0x38, 0x51, 0x6e, 0xc3, 0x6a, 0x76, 0x74, 0x44, 0xdd, 0x28,
	0x91, 0x1c, 0x6b, 0x8c, 0x07, 0x5a, 0x3a, 0xe1, 0xc2, 0x2a, 0x0b, 0x9a, 0x0b, 0x58, 0x70, 0x1d,
	0x80, 0x97, 0x23, 0x12, 0x1c, 0xb5, 0xe0, 0xf0, 0x02, 0x65, 0x2c, 0x04, 0xe8, 0x47, 0x96, 0x5c,
	0x3c, 0x8a, 0x70, 0xd5, 0xc6, 0xf8, 0xd7, 0xb2, 0x79, 0x13, 0xda, 0x72, 0xc7, 0xaf, 0x36, 0x20,
	0x2d, 0xb1, 0xdf, 0x47, 0xff, 0xb4, 0x60, 0x9d, 0x9b, 0xf1, 0x31, 0x66, 0x24, 0xe1, 0x86, 0x71,
	0x1b, 0x6e, 0x42, 0xff, 0xa9, 0x6e, 0xe7, 0x59, 0xb8, 0x97, 0xc9, 0x46, 0x9e, 0x28, 0xcf, 0x39,
	0x05, 0x1b, 0xaa, 0x3c, 0xe7, 0xe4, 0xbb, 0x0e, 0x40, 0x19, 0x4e, 0x58, 0x7e, 0x28, 0x62, 0x8f,
	0xbb, 0x42, 0xb2, 0xf0, 0x50, 0xc4, 0xce, 0x0f, 0x45, 0x6e, 0xc1, 0xc0, 0x18, 0x30, 0x38, 0x51,
	0x70, 0xe5, 0x56, 0x8c, 0x82, 0x13, 0xe7, 0xff, 0x60, 0xc7, 0xa7, 0x53, 0xcf, 0xa7, 0xf1, 0x1c,
	0x9f, 0x4f, 0xb3, 0x39, 0xcb, 0x85, 0x9f, 0x47, 0x6b, 0x67, 0xbc, 0xe5, 0xd3, 0x7b, 0xf2, 0xfd,
	0x9e, 0x7a, 0x2d, 0x2b, 0x80, 0x4d, 0x68, 0x47, 0xc9, 0x09, 0x9f, 0xca, 0x8a, 0x9c, 0x7c, 0x94,
	0x9c, 0x8c, 0x3c, 0xe4, 0x56, 0xe6, 0xfe, 0xb2, 0x74, 0x2d, 0x23, 0x25, 0x27, 0x6d, 0x22, 0x85,
	0x7e, 0x61, 0xc1, 0x7a, 0x36, 0x82, 0x3c, 0x35, 0x79, 0x92, 0xc1, 0x67, 0x19, 0xf0, 0xe5, 0x6c,
	0x6d, 0x14, 0xd8, 0x7a, 0x0d, 0xba, 0x22, 0xb1, 0x51, 0xff, 0xb9, 0x5e, 0x32, 0x44, 0xa6, 0x9b,
	0xf8, 0xcf, 0x05, 0xe6, 0xe2, 0xa5, 0xac, 0x7a, 0xa5, 0x6b, 0x45, 0x77, 0x59, 0xf6, 0xe6, 0x13,
	0x6f, 0x99, 0x13, 0xff, 0xb4, 0x62, 0xd2, 0xcb, 0x4e, 0xfc, 0x76, 0x61, 0xf1, 0xbc, 0xc2, 0x23,
	0x32, 0xd3, 0x98, 0x87, 0xa4, 0x3c, 0x98, 0x61, 0x78, 0xae, 0x49, 0x27, 0x1a, 0xe8, 0x8b, 0x06,
	0x0c, 0x0a, 0xbd, 0x8d, 0x7d, 0x87, 0x2d, 0xf6, 0x1d, 0x8b, 0xe8, 0x55, 0x21, 0x89, 0xbd, 0x80,
	0x24, 0xd7, 0xa0, 0x7b, 0x12, 0x45, 0x1e, 0xe5, 0xdb, 0x75, 0x7d, 0x74, 0x29, 0x04, 0x0f, 0xd3,
	0xa0, 0x44, 0xd0, 0x56, 0x1d, 0x41, 0xdb, 0x45, 0x82, 0xe6, 0xbe, 0x59, 0x29, 0xf8, 0xa6, 0x96,
	0x93, 0x9d, 0x3a, 0x4e, 0xa2, 0x9b, 0xb0, 0x76, 0x9f, 0xb0, 0x42, 0xdc, 0x95, 0x50, 0x40, 0xbf,
	0x6f, 0x94, 0xfb, 0xbc, 0xac, 0x9b, 0xa4, 0x46, 0xbb, 0x82, 0x6b, 0xb3, 0x0e, 0xd7, 0xd6, 0x45,
	0xb8, 0xb6, 0x6b, 0x71, 0x5d, 0xa9, 0xc3, 0xb5, 0xb3, 0x0c, 0xd7, 0xee, 0x8b, 0xe3, 0x0a, 0xb5,
	0xb8, 0xf2, 0x7d, 0xe1, 0x7d, 0x6e, 0x99, 0x8e, 0xb5, 0x45, 0xd9, 0xac, 0x18, 0xa3, 0x7c, 0xaf,
	0x45, 0x4f, 0x53, 0x7d, 0x4e, 0x45, 0x4f, 0x53, 0x3e, 0x25, 0x35, 0x5f, 0x9c, 0x1f, 0xf0, 0xca,
	0x09, 0x73, 0xcc, 0x6e, 0xc3, 0x6a, 0xae, 0xd3, 0x40, 0x34, 0x47, 0x52, 0x74, 0x2b, 0x84, 0x6e,
	0xab, 0x36, 0x74, 0xdb, 0xa5, 0xd0, 0x45, 0xe7, 0x85, 0x69, 0xbc, 0xc2, 0x3a, 0x2a, 0x03, 0xcf,
	0x36, 0x02, 0x2f, 0x8b, 0xda, 0x66, 0x1e, 0xb5, 0xd9, 0x18, 0xc6, 0x42, 0xfa, 0x27, 0x1b, 0x06,
	0x05, 0x79, 0x25, 0x3e, 0x77, 0x40, 0x7a, 0x5f, 0x97, 0x68, 0xf6, 0x78, 0x45, 0xb4, 0x47, 0xde,
	0x45, 0xc8, 0x55, 0xd8, 0xd6, 0x5c, 0xc0, 0xb6, 0x1b, 0xd0, 0x93, 0x3a, 0xa4, 0xc3, 0x5b, 0x62,
	0x41, 0x93, 0x6a, 0x65, 0x42, 0x37, 0x17, 0xbd, 0x7c, 0x01, 0x30, 0x16, 0xbd, 0xac, 0x5b, 0xc9,
	0x4d, 0x2b, 0x8b, 0xdc, 0x54, 0xe4, 0x6f, 0xa7, 0x8e, 0xbf, 0xdd, 0x22, 0x7f, 0xcb, 0xdc, 0x82,
	0x2a, 0xb7, 0xb2, 0xb9, 0xf8, 0x01, 0xf7, 0x93, 0xaa, 0xd9, 0x24, 0x5a, 0x81, 0x72, 0x95, 0x1b,
	0xcd, 0xa3, 0x44, 0x1d, 0x75, 0xc9, 0x46, 0x7d, 0x04, 0x0c, 0x6a, 0x23, 0x60, 0x1f, 0x56, 0x85,
	0xf7, 0xee, 0x91, 0x39, 0x91, 0x67, 0x09, 0x2f, 0x10, 0x02, 0xd2, 0xc3, 0x8d, 0x5d, 0x5b, 0xe5,
	0x9e, 0x9f, 0x59, 0xd0, 0x13, 0x5a, 0xf6, 0x3c, 0xef, 0x05, 0x55, 0xd4, 0x90, 0x62, 0x03, 0x5a,
	0xd2, 0x72, 0x59, 0x9b, 0xc8, 0x46, 0x3e, 0xf3, 0xa6, 0x39, 0xf3, 0x25, 0x6b, 0xd6, 0x5f, 0x2d,
	0x58, 0x9d, 0x10, 0x9c, 0xb8, 0x33, 0x61, 0x16, 0xb7, 0x69, 0x13, 0xda, 0xf4, 0x34, 0xcd, 0xad,
	0x69, 0xd1, 0xd3, 0x74, 0xe4, 0x09, 0x71, 0x9c, 0xe6, 0x56, 0xb4, 0x68, 0x9c, 0x1a, 0x25, 0x8b,
	0x6d, 0xe4, 0xbe, 0x42, 0x80, 0x36, 0x6b, 0x03, 0xb4, 0x55, 0x5e, 0x5b, 0xcb, 0x88, 0xb4, 0xab,
	0x88, 0x2c, 0xa9, 0x3b, 0x3e, 0x2d, 0xcd, 0xe4, 0x72, 0x82, 0xfb, 0xed, 0x42, 0x70, 0x8b, 0x03,
	0x70, 0x63, 0x14, 0x23, 0xbc, 0xff, 0x6e, 0xc1, 0x5a, 0xe9, 0xcd, 0x25, 0x40, 0x59, 0xe2, 0x79,
	0xb3, 0xc2, 0xf3, 0x0b, 0x83, 0xfa, 0x26, 0xf4, 0x7d, 0x3a, 0xcd, 0xf0, 0x53, 0x29, 0xb1, 0xe7,
	0xd3, 0x6c, 0x01, 0x5c, 0x06, 0x68, 0xa8, 0xf2, 0x95, 0xd0, 0xb3, 0x60, 0x25, 0xad, 0xf8, 0xaa,
	0x51, 0xf5, 0xd5, 0x4b, 0x50, 0x14, 0xfd, 0x0f, 0xac, 0x4f, 0x58, 0x14, 0xbf, 0x64, 0xd1, 0x8c,
	0x4e, 0x54, 0x60, 0x8e, 0x82, 0x38, 0x4a, 0x5e, 0x74, 0x6d, 0xba, 0x06, 0xdd, 0x63, 0x7f, 0x4e,
	0xa6, 0x31, 0x66, 0x33, 0x45, 0x84, 0x0e, 0x17, 0x3c, 0xc6, 0x6c, 0x66, 0xe0, 0x61, 0x9b, 0x78,
	0x94, 0x07, 0x7a, 0x59, 0x82, 0xbd, 0x5d, 0xa8, 0xee, 0x04, 0x95, 0xa4, 0xaa, 0x6f, 0x63, 0x7f,
	0x2e, 0x49, 0x2b, 0xa9, 0xf4, 0x11, 0xac, 0x95, 0x5e, 0x2c, 0x63, 0x52, 0x06, 0x6f, 0xc3, 0x84,
	0x77, 0x0b, 0xda, 0x6a, 0x83, 0xa4, 0x36, 0xf3, 0xb2, 0xc5, 0xb7, 0x72, 0x5b, 0xfb, 0xb3, 0xc4,
	0xa7, 0x2c, 0xc0, 0x3c, 0x03, 0xed, 0x79, 0x5e, 0x42, 0x28, 0x2d, 0xba, 0xb6, 0xb5, 0xb4, 0x54,
	0x1c, 0xc2, 0x0a, 0x96, 0x5f, 0x28, 0xbd, 0xba, 0xb9, 0xf4, 0xfa, 0x78, 0x47, 0xdc, 0x0d, 0x9b,
	0xc7, 0x03, 0x2b, 0x31, 0x61, 0x62, 0xcb, 0x7c, 0x03, 0x7a, 0xd4, 0x4d, 0x82, 0x69, 0x4a, 0x49,
	0xa2, 0xc2, 0xbc, 0x3b, 0x06, 0x2e, 0xfa, 0x50, 0x48, 0xd0, 0x2d, 0x58, 0xfb, 0xc0, 0xb8, 0x82,
	0x7f, 0x48, 0x9e, 0xf2, 0x82, 0x22, 0xa0, 0x27, 0xaa, 0xbc, 0xe7, 0x8f, 0xe8, 0x1c, 0x36, 0xf6,
	0x67, 0x38, 0x0c, 0xc9, 0x7c, 0xc2, 0x30, 0xf3, 0x29, 0xf3, 0x5d, 0xaa, 0x18, 0xe0, 0x4a, 0xf9,
	0xd4, 0xd8, 0x11, 0xf4, 0x94, 0x4c, 0x2c, 0x4f, 0xaf, 0xf3, 0x24, 0x95, 0xe0, 0x80, 0xb0, 0xec,
	0x1c, 0x3e, 0x17, 0x88, 0x3b, 0xb6, 0x64, 0xae, 0xe6, 0xc9, 0x1f, 0x05, 0x42, 0xb1, 0x9a, 0x5f,
	0xc3, 0x8f, 0xc5, 0x7d, 0xfc, 0x77, 0x70, 0xe8, 0x3d, 0x4a, 0x59, 0x76, 0x70, 0x28, 0xfe, 0x3f,
	0x30, 0xaf, 0x2f, 0x2d, 0xf3, 0xa6, 0x57, 0x86, 0xb8, 0x71, 0x72, 0x28, 0x9e, 0x97, 0x1e, 0xbd,
	0x38, 0xd0, 0x3c, 0x4e, 0x22, 0x5d, 0x6f, 0x8b, 0x67, 0x67, 0x17, 0xfa, 0x19, 0x6a, 0x3a, 0x97,
	0x1b, 0xb0, 0x8d, 0x3c, 0xf4, 0xbf, 0xb0, 0x59, 0xb2, 0x4a, 0xdd, 0x26, 0xbf, 0xa1, 0xef, 0x64,
	0xf6, 0x35, 0x63, 0xbb, 0x63, 0x43, 0x82, 0x28, 0x5c, 0xe5, 0x78, 0x3f, 0x4e, 0xa2, 0x38, 0xa2,
	0x78, 0x6e, 0xfc, 0x4d, 0x91, 0x0a, 0xcd, 0x7a, 0xe7, 0x2c, 0x5b, 0x5c, 0x5d, 0xac, 0xba, 0x8e,
	0x74, 0xe4, 0x1b, 0x12, 0xe7, 0x4d, 0x18, 0xd0, 0x34, 0xe6, 0x24, 0x96, 0x86, 0xa9, 0xc9, 0x15,
	0x85, 0xe8, 0x4b, 0x0b, 0x36, 0x27, 0xe9, 0x51, 0xe0, 0xb3, 0x17, 0x1d, 0x17, 0x41, 0x5f, 0x8f,
	0xf2, 0x41, 0xe4, 0x9d, 0x2b, 0xcf, 0x15, 0x64, 0x9c, 0xa8, 0x71, 0x12, 0x1d, 0xe7, 0x90, 0xea,
	0xe6, 0x32, 0xa2, 0xa2, 0x3b, 0xb0, 0x55, 0x36, 0x43, 0xc1, 0x56, 0xde, 0x3d, 0xbc, 0x0b, 0x5b,
	0xf7, 0x49, 0xd6, 0x4d, 0x55, 0xc3, 0xc2, 0x62, 0x07, 0x9a, 0x62, 0xc1, 0x53, 0xc9, 0x80, 0x3f,
	0xa3, 0xff, 0x87, 0xed, 0x4a, 0x6f, 0xa5, 0x78, 0x17, 0x9a, 0xe2, 0x02, 0xdd, 0xca, 0x7f, 0xad,
	0xc8, 0x06, 0x17, 0x6f, 0xd0, 0xdf, 0x2c, 0xe8, 0x68, 0x51, 0x25, 0xf7, 0xe6, 0xf8, 0x34, 0x6a,
	0xf1, 0xb1, 0xeb, 0xf1, 0x69, 0x16, 0xf1, 0x79, 0x4d, 0x9c, 0x50, 0x25, 0x3c, 0x40, 0x14, 0xb7,
	0xb2, 0x36, 0xf7, 0xb8, 0x72, 0xde, 0x43, 0xb5, 0x5b, 0xb1, 0xc7, 0x86, 0x84, 0x07, 0x94, 0x4f,
	0x27, 0xb2, 0xad, 0x16, 0x92, 0x5c, 0x80, 0x7e, 0x6d, 0xc1, 0x0e, 0x87, 0x82, 0xb0, 0x03, 0x9c,
	0x9c, 0x12, 0x41, 0x80, 0xfc, 0xc0, 0xbd, 0xcc, 0x6b, 0xab, 0xcc, 0x6b, 0x07, 0xc1, 0xc0, 0xbc,
	0x06, 0xa1, 0xaa, 0xa8, 0xea, 0xe5, 0xf7, 0x20, 0xf4, 0xd5, 0x8f, 0x4a, 0xd0, 0x6f, 0x2c, 0xe8,
	0xf1, 0x81, 0x3e, 0x92, 0xda, 0xb8, 0x26, 0x3d, 0x5a, 0x86, 0x7a, 0x57, 0x49, 0x46, 0x5e, 0xe5,
	0x4e, 0x46, 0xd1, 0xdf, 0xb8, 0x93, 0xb9, 0x03, 0xeb, 0xba, 0x47, 0xf4, 0x34, 0x94, 0x6a, 0xa4,
	0x41, 0xfa, 0x0e, 0xe6, 0x11, 0x17, 0xcb, 0xa3, 0x4b, 0xdd, 0x53, 0xde, 0xa2, 0xa8, 0x63, 0x2f,
	0x25, 0x14, 0xf7, 0x22, 0x88, 0xc2, 0x1b, 0xcb, 0xc0, 0x5b, 0xf0, 0xb3, 0x88, 0x7d, 0xe1, 0x52,
	0x74, 0x4b, 0x91, 0x4f, 0x2e, 0x45, 0x6b, 0x9c, 0x7c, 0xc6, 0xf4, 0x15, 0xff, 0xb6, 0xc4, 0xc5,
	0x58, 0x36, 0xe8, 0x21, 0xa6, 0xa7, 0x3c, 0xb9, 0xa2, 0x5f, 0x5a, 0x30, 0x28, 0x48, 0x39, 0x39,
	0x47, 0x19, 0x39, 0xe5, 0xb2, 0x64, 0xde, 0x43, 0xc9, 0x06, 0x5f, 0x73, 0x69, 0x7a, 0x54, 0xb8,
	0x09, 0xec, 0xd0, 0xf4, 0xe8, 0x50, 0xbf, 0x8c, 0x8e, 0x3e, 0x99, 0x9e, 0xe1, 0x79, 0xaa, 0x59,
	0xd9, 0x89, 0x8e, 0x3e, 0xf9, 0x88, 0xb7, 0xe5, 0xcf, 0x39, 0x4f, 0x71, 0xe2, 0xa9, 0xf7, 0x2d,
	0xfd, 0x73, 0x0e, 0x97, 0x89, 0x2e, 0x28, 0x2e, 0xd2, 0x4b, 0x19, 0xfb, 0x4a, 0xe0, 0xdc, 0x2e,
	0x80, 0x23, 0xf6, 0x73, 0x05, 0xbd, 0x0a, 0x9e, 0x09, 0x6c, 0xef, 0x79, 0x5e, 0xc1, 0x27, 0xe2,
	0x2d, 0x79, 0x52, 0x5e, 0xdc, 0xac, 0xf2, 0xe2, 0xe6, 0x6c, 0xc3, 0x0a, 0xc3, 0xd4, 0x28, 0x9a,
	0xda, 0xbc, 0x39, 0xf2, 0xd0, 0x21, 0x0c, 0xcb, 0x8e, 0xd6, 0xb8, 0x7f, 0x0d, 0xad, 0xdf, 0x87,
	0x2b, 0x15, 0x95, 0x95, 0x8c, 0x52, 0x52, 0xdf, 0xa8, 0x53, 0x6f, 0x17, 0xd4, 0x3f, 0x83, 0xeb,
	0x4b, 0x8c, 0x7e, 0x25, 0xfc, 0xdf, 0x29, 0xe0, 0xbf, 0x59, 0xc0, 0x3f, 0x43, 0x59, 0xfa, 0xe0,
	0xb7, 0x16, 0x6c, 0x9a, 0x4e, 0x18, 0x0b, 0x46, 0xbc, 0x10, 0x58, 0x7c, 0x1f, 0x92, 0xf8, 0xcf,
	0x49, 0x7e, 0x95, 0x67, 0x8f, 0xbb, 0x42, 0x22, 0xea, 0x93, 0xd7, 0xa0, 0x93, 0x10, 0x97, 0xf8,
	0x67, 0x44, 0x9f, 0x9a, 0x67, 0xed, 0xa5, 0xe5, 0x8e, 0x51, 0x20, 0xb5, 0x0a, 0x05, 0x12, 0xfa,
	0x46, 0x35, 0xf9, 0xbd, 0xb8, 0xa9, 0xe8, 0x0f, 0x16, 0x5c, 0x5d, 0xf0, 0xed, 0xcb, 0x7b, 0xb0,
	0x38, 0x67, 0xbb, 0x6e, 0xce, 0xcd, 0xa5, 0x73, 0x6e, 0x2d, 0x9b, 0x73, 0xbb, 0x38, 0xe7, 0xcf,
	0xaa, 0x39, 0x4b, 0xcf, 0xf9, 0x95, 0x68, 0x71, 0x17, 0xda, 0x1e, 0x61, 0xd8, 0x97, 0x55, 0x59,
	0xef, 0xfd, 0xed, 0x0a, 0x31, 0x94, 0x7a, 0xd5, 0x0d, 0xed, 0xc0, 0xf6, 0x7e, 0x94, 0x86, 0xac,
	0xca, 0x0e, 0x44, 0xe0, 0xc6, 0x92, 0x57, 0xaf, 0x68, 0x5c, 0x61, 0xf3, 0x68, 0xeb, 0x23, 0xd9,
	0x9f, 0x5b, 0x6a, 0x1c, 0x6e, 0xdd, 0xa3, 0xc4, 0x23, 0xc9, 0xc7, 0x3e, 0x9b, 0x4d, 0x4e, 0x53,
	0x5f, 0x6c, 0xb5, 0x45, 0xd9, 0x70, 0x0d, 0xba, 0x81, 0xb8, 0xad, 0xcd, 0xd7, 0xbd, 0x8e, 0x14,
	0xa8, 0x1f, 0x5f, 0x44, 0x6f, 0xb5, 0xdc, 0xa9, 0xd6, 0x82, 0x95, 0xae, 0x5b, 0xb7, 0xd2, 0xe5,
	0x7f, 0x4a, 0xa2, 0x63, 0xd8, 0x5d, 0x6e, 0x51, 0x3e, 0xf5, 0x7d, 0x63, 0xea, 0xfb, 0xaf, 0x32,
	0xf5, 0x9f, 0x5a, 0xfa, 0x6f, 0x86, 0x09, 0x09, 0xbd, 0x83, 0x74, 0xce, 0x7c, 0xce, 0xf5, 0xb7,
	0x60, 0xcd, 0xb8, 0x88, 0x9c, 0xe2, 0x24, 0x11, 0x05, 0x50, 0x6b, 0x3c, 0xc8, 0x2f, 0x23, 0xf7,
	0x12, 0x41, 0xb7, 0xc2, 0xaf, 0x61, 0xaa, 0xc5, 0xcd, 0x50, 0x45, 0xbc, 0xda, 0x9b, 0xe9, 0xa6,
	0x38, 0x74, 0x98, 0x45, 0x21, 0x11, 0x3a, 0x9b, 0xe2, 0xaf, 0xc4, 0x8e, 0x10, 0xec, 0x25, 0x09,
	0xfa, 0x6c, 0x81, 0x31, 0x8b, 0xb7, 0x6f, 0x6a, 0x8f, 0xd1, 0xc8, 0xf6, 0x18, 0xce, 0x7f, 0x16,
	0xb6, 0x6d, 0xdb, 0xf9, 0xad, 0x6c, 0xa6, 0xcb, 0x38, 0x9a, 0xcf, 0x8f, 0x5e, 0x9b, 0xe6, 0xd1,
	0x2b, 0xfa, 0xca, 0x82, 0xab, 0x0b, 0xbe, 0x72, 0xde, 0x05, 0x47, 0x5f, 0x2d, 0x56, 0xee, 0xbf,
	0xd7, 0xdd, 0xc2, 0xdf, 0x20, 0xf2, 0xd4, 0xcb, 0xbc, 0xa9, 0x6c, 0x54, 0x6e, 0x2a, 0xf3, 0x1f,
	0xc2, 0x8c, 0x0c, 0x6a, 0xfc, 0x10, 0x26, 0x2a, 0x50, 0xf5, 0x01, 0x7f, 0x46, 0x9f, 0xea, 0x5d,
	0x81, 0xf8, 0x75, 0xb3, 0xee, 0x2f, 0x01, 0x63, 0x70, 0x01, 0x5a, 0x61, 0x70, 0x41, 0x91, 0xeb,
	0x00, 0xa2, 0x7e, 0x13, 0xa8, 0x6b, 0x52, 0x72, 0xc9, 0x63, 0x2e, 0xe0, 0x68, 0xa7, 0xa9, 0xef,
	0xe9, 0x53, 0x72, 0xfe, 0x8c, 0x0e, 0xe0, 0xfa, 0x83, 0xd4, 0x3d, 0x3d, 0xbf, 0x97, 0xe0, 0xa7,
	0xfa, 0xb8, 0x6d, 0x4c, 0xdc, 0x28, 0x08, 0x48, 0x28, 0x72, 0xe3, 0xeb, 0x00, 0x5e, 0xf4, 0x34,
	0x9c, 0x46, 0xc9, 0x34, 0x8d, 0xb5, 0x49, 0x5c, 0xf2, 0x28, 0xf9, 0x30, 0xce, 0x8e, 0xd7, 0xd4,
	0xae, 0x15, 0x7d, 0x17, 0x50, 0x9d, 0xba, 0x9a, 0xff, 0x6a, 0x2b, 0x6e, 0x3f, 0x6a, 0x8b, 0xff,
	0xdb, 0xff, 0xfb, 0x5f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x73, 0x0e, 0xfe, 0x36, 0xfa, 0x2e, 0x00,
	0x00,
}
