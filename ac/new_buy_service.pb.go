// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/new_buy_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//新人专享商品列表的请求数据
type GetNewBuyProductListRequest struct {
	//新人专享活动id
	Nid int32 `protobuf:"varint,1,opt,name=nid,proto3" json:"nid"`
	//产品名称
	ProductName string `protobuf:"bytes,2,opt,name=productName,proto3" json:"productName"`
	//商品sku id
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	//商品的产品id
	ChannelId int32 `protobuf:"varint,5,opt,name=ChannelId,proto3" json:"ChannelId"`
	//商品状态 -1删除 0默认
	Status int32 `protobuf:"varint,6,opt,name=Status,proto3" json:"Status"`
	//排序 0 按商品排序设置排序，1；按成团真实订单数排序 默认为0
	OrderBy int32 `protobuf:"varint,7,opt,name=orderBy,proto3" json:"orderBy"`
	//分页参数
	Pagination *Page `protobuf:"bytes,8,opt,name=pagination,proto3" json:"pagination"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,9,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewBuyProductListRequest) Reset()         { *m = GetNewBuyProductListRequest{} }
func (m *GetNewBuyProductListRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewBuyProductListRequest) ProtoMessage()    {}
func (*GetNewBuyProductListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{0}
}

func (m *GetNewBuyProductListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewBuyProductListRequest.Unmarshal(m, b)
}
func (m *GetNewBuyProductListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewBuyProductListRequest.Marshal(b, m, deterministic)
}
func (m *GetNewBuyProductListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewBuyProductListRequest.Merge(m, src)
}
func (m *GetNewBuyProductListRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewBuyProductListRequest.Size(m)
}
func (m *GetNewBuyProductListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewBuyProductListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewBuyProductListRequest proto.InternalMessageInfo

func (m *GetNewBuyProductListRequest) GetNid() int32 {
	if m != nil {
		return m.Nid
	}
	return 0
}

func (m *GetNewBuyProductListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetNewBuyProductListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetNewBuyProductListRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetNewBuyProductListRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetNewBuyProductListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetNewBuyProductListRequest) GetOrderBy() int32 {
	if m != nil {
		return m.OrderBy
	}
	return 0
}

func (m *GetNewBuyProductListRequest) GetPagination() *Page {
	if m != nil {
		return m.Pagination
	}
	return nil
}

func (m *GetNewBuyProductListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//新人专享商品列表
type GetNewBuyProductListResponse struct {
	//响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//新人专享商品信息
	Data                 []*NewBuyProductData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetNewBuyProductListResponse) Reset()         { *m = GetNewBuyProductListResponse{} }
func (m *GetNewBuyProductListResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewBuyProductListResponse) ProtoMessage()    {}
func (*GetNewBuyProductListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{1}
}

func (m *GetNewBuyProductListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewBuyProductListResponse.Unmarshal(m, b)
}
func (m *GetNewBuyProductListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewBuyProductListResponse.Marshal(b, m, deterministic)
}
func (m *GetNewBuyProductListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewBuyProductListResponse.Merge(m, src)
}
func (m *GetNewBuyProductListResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewBuyProductListResponse.Size(m)
}
func (m *GetNewBuyProductListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewBuyProductListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewBuyProductListResponse proto.InternalMessageInfo

func (m *GetNewBuyProductListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetNewBuyProductListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetNewBuyProductListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetNewBuyProductListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetNewBuyProductListResponse) GetData() []*NewBuyProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

//新人专享商品数据
type NewBuyProductData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//所属活动id
	Nid int32 `protobuf:"varint,2,opt,name=nid,proto3" json:"nid"`
	//渠道ID
	ChannelId int32 `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	//商品sku id
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	//商品名称
	ProductName string `protobuf:"bytes,6,opt,name=productName,proto3" json:"productName"`
	//单买价 单位分
	Price int32 `protobuf:"varint,7,opt,name=price,proto3" json:"price"`
	//新人价 单位分
	NewPrice int32 `protobuf:"varint,8,opt,name=newPrice,proto3" json:"newPrice"`
	//短标题
	ShortTitle string `protobuf:"bytes,9,opt,name=shortTitle,proto3" json:"shortTitle"`
	//长标题
	LongTitle string `protobuf:"bytes,10,opt,name=longTitle,proto3" json:"longTitle"`
	//排序
	Sort int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort"`
	//状态 -1删除 0默认
	Status int32 `protobuf:"varint,12,opt,name=status,proto3" json:"status"`
	// 是否可被编辑 0 不可编辑 1 可编辑 用于boss后台
	CanBeEdited int32 `protobuf:"varint,13,opt,name=canBeEdited,proto3" json:"canBeEdited"`
	// 是否可被删除 0 不可删除 1 可删除 用于boss后台
	CanBeDeleted int32 `protobuf:"varint,14,opt,name=canBeDeleted,proto3" json:"canBeDeleted"`
	// 商品图片
	Pic string `protobuf:"bytes,15,opt,name=pic,proto3" json:"pic"`
	// 商品库存
	Stock int32 `protobuf:"varint,16,opt,name=stock,proto3" json:"stock"`
	// 是否是虚拟商品 1是 0 否
	IsVirtual int32 `protobuf:"varint,17,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,18,opt,name=goodsType,proto3" json:"goodsType"`
	//创建时间
	CreateTime string `protobuf:"bytes,19,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime           string   `protobuf:"bytes,20,opt,name=updateTime,proto3" json:"updateTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewBuyProductData) Reset()         { *m = NewBuyProductData{} }
func (m *NewBuyProductData) String() string { return proto.CompactTextString(m) }
func (*NewBuyProductData) ProtoMessage()    {}
func (*NewBuyProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{2}
}

func (m *NewBuyProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyProductData.Unmarshal(m, b)
}
func (m *NewBuyProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyProductData.Marshal(b, m, deterministic)
}
func (m *NewBuyProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyProductData.Merge(m, src)
}
func (m *NewBuyProductData) XXX_Size() int {
	return xxx_messageInfo_NewBuyProductData.Size(m)
}
func (m *NewBuyProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyProductData.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyProductData proto.InternalMessageInfo

func (m *NewBuyProductData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NewBuyProductData) GetNid() int32 {
	if m != nil {
		return m.Nid
	}
	return 0
}

func (m *NewBuyProductData) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NewBuyProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *NewBuyProductData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *NewBuyProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *NewBuyProductData) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *NewBuyProductData) GetNewPrice() int32 {
	if m != nil {
		return m.NewPrice
	}
	return 0
}

func (m *NewBuyProductData) GetShortTitle() string {
	if m != nil {
		return m.ShortTitle
	}
	return ""
}

func (m *NewBuyProductData) GetLongTitle() string {
	if m != nil {
		return m.LongTitle
	}
	return ""
}

func (m *NewBuyProductData) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *NewBuyProductData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *NewBuyProductData) GetCanBeEdited() int32 {
	if m != nil {
		return m.CanBeEdited
	}
	return 0
}

func (m *NewBuyProductData) GetCanBeDeleted() int32 {
	if m != nil {
		return m.CanBeDeleted
	}
	return 0
}

func (m *NewBuyProductData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *NewBuyProductData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *NewBuyProductData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *NewBuyProductData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *NewBuyProductData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *NewBuyProductData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

//新增新人专享活动商品
type CreateNewBuyProductRequest struct {
	//商品sku id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,2,opt,name=productId,proto3" json:"productId"`
	//主体信息
	SaveData *SaveNewBuyProductData `protobuf:"bytes,3,opt,name=saveData,proto3" json:"saveData"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateNewBuyProductRequest) Reset()         { *m = CreateNewBuyProductRequest{} }
func (m *CreateNewBuyProductRequest) String() string { return proto.CompactTextString(m) }
func (*CreateNewBuyProductRequest) ProtoMessage()    {}
func (*CreateNewBuyProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{3}
}

func (m *CreateNewBuyProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateNewBuyProductRequest.Unmarshal(m, b)
}
func (m *CreateNewBuyProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateNewBuyProductRequest.Marshal(b, m, deterministic)
}
func (m *CreateNewBuyProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateNewBuyProductRequest.Merge(m, src)
}
func (m *CreateNewBuyProductRequest) XXX_Size() int {
	return xxx_messageInfo_CreateNewBuyProductRequest.Size(m)
}
func (m *CreateNewBuyProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateNewBuyProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateNewBuyProductRequest proto.InternalMessageInfo

func (m *CreateNewBuyProductRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CreateNewBuyProductRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CreateNewBuyProductRequest) GetSaveData() *SaveNewBuyProductData {
	if m != nil {
		return m.SaveData
	}
	return nil
}

func (m *CreateNewBuyProductRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//添加/编辑新人专享商品
type SaveNewBuyProductData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//所属活动id
	Nid int32 `protobuf:"varint,2,opt,name=nid,proto3" json:"nid"`
	//商品名称
	ProductName string `protobuf:"bytes,6,opt,name=productName,proto3" json:"productName"`
	//单买价 单位分
	NewPrice int32 `protobuf:"varint,7,opt,name=newPrice,proto3" json:"newPrice"`
	//3期价 单位分
	Sort int32 `protobuf:"varint,8,opt,name=sort,proto3" json:"sort"`
	//短标题
	ShortTitle string `protobuf:"bytes,9,opt,name=shortTitle,proto3" json:"shortTitle"`
	//长标题
	LongTitle            string   `protobuf:"bytes,10,opt,name=longTitle,proto3" json:"longTitle"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveNewBuyProductData) Reset()         { *m = SaveNewBuyProductData{} }
func (m *SaveNewBuyProductData) String() string { return proto.CompactTextString(m) }
func (*SaveNewBuyProductData) ProtoMessage()    {}
func (*SaveNewBuyProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{4}
}

func (m *SaveNewBuyProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveNewBuyProductData.Unmarshal(m, b)
}
func (m *SaveNewBuyProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveNewBuyProductData.Marshal(b, m, deterministic)
}
func (m *SaveNewBuyProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveNewBuyProductData.Merge(m, src)
}
func (m *SaveNewBuyProductData) XXX_Size() int {
	return xxx_messageInfo_SaveNewBuyProductData.Size(m)
}
func (m *SaveNewBuyProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveNewBuyProductData.DiscardUnknown(m)
}

var xxx_messageInfo_SaveNewBuyProductData proto.InternalMessageInfo

func (m *SaveNewBuyProductData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SaveNewBuyProductData) GetNid() int32 {
	if m != nil {
		return m.Nid
	}
	return 0
}

func (m *SaveNewBuyProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *SaveNewBuyProductData) GetNewPrice() int32 {
	if m != nil {
		return m.NewPrice
	}
	return 0
}

func (m *SaveNewBuyProductData) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *SaveNewBuyProductData) GetShortTitle() string {
	if m != nil {
		return m.ShortTitle
	}
	return ""
}

func (m *SaveNewBuyProductData) GetLongTitle() string {
	if m != nil {
		return m.LongTitle
	}
	return ""
}

// 更新新人专享商品
type UpdateNewBuyProductRequest struct {
	//需要更新的记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品sku id
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,3,opt,name=productId,proto3" json:"productId"`
	//主体信息
	SaveData *SaveNewBuyProductData `protobuf:"bytes,4,opt,name=saveData,proto3" json:"saveData"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNewBuyProductRequest) Reset()         { *m = UpdateNewBuyProductRequest{} }
func (m *UpdateNewBuyProductRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateNewBuyProductRequest) ProtoMessage()    {}
func (*UpdateNewBuyProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{5}
}

func (m *UpdateNewBuyProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNewBuyProductRequest.Unmarshal(m, b)
}
func (m *UpdateNewBuyProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNewBuyProductRequest.Marshal(b, m, deterministic)
}
func (m *UpdateNewBuyProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNewBuyProductRequest.Merge(m, src)
}
func (m *UpdateNewBuyProductRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateNewBuyProductRequest.Size(m)
}
func (m *UpdateNewBuyProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNewBuyProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNewBuyProductRequest proto.InternalMessageInfo

func (m *UpdateNewBuyProductRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateNewBuyProductRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *UpdateNewBuyProductRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UpdateNewBuyProductRequest) GetSaveData() *SaveNewBuyProductData {
	if m != nil {
		return m.SaveData
	}
	return nil
}

func (m *UpdateNewBuyProductRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 新人专享商品只需要id的请求
type NewBuyProductDetailRequest struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//所属活动id
	Nid int32 `protobuf:"varint,2,opt,name=nid,proto3" json:"nid"`
	//商品skuId
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	//渠道 1电商 5商城
	ChannelId int32 `protobuf:"varint,5,opt,name=channelId,proto3" json:"channelId"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewBuyProductDetailRequest) Reset()         { *m = NewBuyProductDetailRequest{} }
func (m *NewBuyProductDetailRequest) String() string { return proto.CompactTextString(m) }
func (*NewBuyProductDetailRequest) ProtoMessage()    {}
func (*NewBuyProductDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{6}
}

func (m *NewBuyProductDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyProductDetailRequest.Unmarshal(m, b)
}
func (m *NewBuyProductDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyProductDetailRequest.Marshal(b, m, deterministic)
}
func (m *NewBuyProductDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyProductDetailRequest.Merge(m, src)
}
func (m *NewBuyProductDetailRequest) XXX_Size() int {
	return xxx_messageInfo_NewBuyProductDetailRequest.Size(m)
}
func (m *NewBuyProductDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyProductDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyProductDetailRequest proto.InternalMessageInfo

func (m *NewBuyProductDetailRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NewBuyProductDetailRequest) GetNid() int32 {
	if m != nil {
		return m.Nid
	}
	return 0
}

func (m *NewBuyProductDetailRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *NewBuyProductDetailRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *NewBuyProductDetailRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NewBuyProductDetailRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//获取新人专享商品信息
type GetNewBuyProductDetailResponse struct {
	//响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//新人专享商品信息
	Data                 *NewBuyProductDetailData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetNewBuyProductDetailResponse) Reset()         { *m = GetNewBuyProductDetailResponse{} }
func (m *GetNewBuyProductDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewBuyProductDetailResponse) ProtoMessage()    {}
func (*GetNewBuyProductDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{7}
}

func (m *GetNewBuyProductDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewBuyProductDetailResponse.Unmarshal(m, b)
}
func (m *GetNewBuyProductDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewBuyProductDetailResponse.Marshal(b, m, deterministic)
}
func (m *GetNewBuyProductDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewBuyProductDetailResponse.Merge(m, src)
}
func (m *GetNewBuyProductDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewBuyProductDetailResponse.Size(m)
}
func (m *GetNewBuyProductDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewBuyProductDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewBuyProductDetailResponse proto.InternalMessageInfo

func (m *GetNewBuyProductDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetNewBuyProductDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetNewBuyProductDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetNewBuyProductDetailResponse) GetData() *NewBuyProductDetailData {
	if m != nil {
		return m.Data
	}
	return nil
}

//新人专享商品详细数据包含部分新人专享信息
type NewBuyProductDetailData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//所属活动id
	Nid int32 `protobuf:"varint,2,opt,name=nid,proto3" json:"nid"`
	//渠道ID
	ChannelId int32 `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	//商品sku id
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	//商品名称
	ProductName string `protobuf:"bytes,6,opt,name=productName,proto3" json:"productName"`
	//单买价 单位分
	Price int32 `protobuf:"varint,7,opt,name=price,proto3" json:"price"`
	//新人价 单位分
	NewPrice int32 `protobuf:"varint,8,opt,name=newPrice,proto3" json:"newPrice"`
	//短标题
	ShortTitle string `protobuf:"bytes,9,opt,name=shortTitle,proto3" json:"shortTitle"`
	//长标题
	LongTitle string `protobuf:"bytes,10,opt,name=longTitle,proto3" json:"longTitle"`
	//排序
	Sort int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort"`
	//状态 -1删除 0默认
	Status int32 `protobuf:"varint,12,opt,name=status,proto3" json:"status"`
	//创建时间
	CreateTime string `protobuf:"bytes,13,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime string `protobuf:"bytes,14,opt,name=updateTime,proto3" json:"updateTime"`
	//新人专享信息
	//开始时间
	BeginDate string `protobuf:"bytes,15,opt,name=beginDate,proto3" json:"beginDate"`
	//结束时间
	EndDate string `protobuf:"bytes,16,opt,name=endDate,proto3" json:"endDate"`
	// 是否是虚拟商品 1是 0 否
	IsVirtual int32 `protobuf:"varint,17,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType            int32    `protobuf:"varint,18,opt,name=goodsType,proto3" json:"goodsType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewBuyProductDetailData) Reset()         { *m = NewBuyProductDetailData{} }
func (m *NewBuyProductDetailData) String() string { return proto.CompactTextString(m) }
func (*NewBuyProductDetailData) ProtoMessage()    {}
func (*NewBuyProductDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{8}
}

func (m *NewBuyProductDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyProductDetailData.Unmarshal(m, b)
}
func (m *NewBuyProductDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyProductDetailData.Marshal(b, m, deterministic)
}
func (m *NewBuyProductDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyProductDetailData.Merge(m, src)
}
func (m *NewBuyProductDetailData) XXX_Size() int {
	return xxx_messageInfo_NewBuyProductDetailData.Size(m)
}
func (m *NewBuyProductDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyProductDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyProductDetailData proto.InternalMessageInfo

func (m *NewBuyProductDetailData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NewBuyProductDetailData) GetNid() int32 {
	if m != nil {
		return m.Nid
	}
	return 0
}

func (m *NewBuyProductDetailData) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NewBuyProductDetailData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *NewBuyProductDetailData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *NewBuyProductDetailData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *NewBuyProductDetailData) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *NewBuyProductDetailData) GetNewPrice() int32 {
	if m != nil {
		return m.NewPrice
	}
	return 0
}

func (m *NewBuyProductDetailData) GetShortTitle() string {
	if m != nil {
		return m.ShortTitle
	}
	return ""
}

func (m *NewBuyProductDetailData) GetLongTitle() string {
	if m != nil {
		return m.LongTitle
	}
	return ""
}

func (m *NewBuyProductDetailData) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *NewBuyProductDetailData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *NewBuyProductDetailData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *NewBuyProductDetailData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *NewBuyProductDetailData) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *NewBuyProductDetailData) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *NewBuyProductDetailData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *NewBuyProductDetailData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

//新人专享商品只需要id的请求
type NewBuyProductIdRequest struct {
	//活动商品信息记录id
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewBuyProductIdRequest) Reset()         { *m = NewBuyProductIdRequest{} }
func (m *NewBuyProductIdRequest) String() string { return proto.CompactTextString(m) }
func (*NewBuyProductIdRequest) ProtoMessage()    {}
func (*NewBuyProductIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{9}
}

func (m *NewBuyProductIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyProductIdRequest.Unmarshal(m, b)
}
func (m *NewBuyProductIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyProductIdRequest.Marshal(b, m, deterministic)
}
func (m *NewBuyProductIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyProductIdRequest.Merge(m, src)
}
func (m *NewBuyProductIdRequest) XXX_Size() int {
	return xxx_messageInfo_NewBuyProductIdRequest.Size(m)
}
func (m *NewBuyProductIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyProductIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyProductIdRequest proto.InternalMessageInfo

func (m *NewBuyProductIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

//阿闻电商参加新人专享活动的商品
type GetNewBuyUPetProductSelectListRequest struct {
	//活动id
	Nid int32 `protobuf:"varint,1,opt,name=nid,proto3" json:"nid"`
	//商品sku_id 对应商城的goods_id
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	// 商品的产品id 对应商城的goods_commonid
	ProductId int32 `protobuf:"varint,3,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	ProductName string `protobuf:"bytes,4,opt,name=productName,proto3" json:"productName"`
	//分页参数
	Pagination *Page `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewBuyUPetProductSelectListRequest) Reset()         { *m = GetNewBuyUPetProductSelectListRequest{} }
func (m *GetNewBuyUPetProductSelectListRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewBuyUPetProductSelectListRequest) ProtoMessage()    {}
func (*GetNewBuyUPetProductSelectListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{10}
}

func (m *GetNewBuyUPetProductSelectListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewBuyUPetProductSelectListRequest.Unmarshal(m, b)
}
func (m *GetNewBuyUPetProductSelectListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewBuyUPetProductSelectListRequest.Marshal(b, m, deterministic)
}
func (m *GetNewBuyUPetProductSelectListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewBuyUPetProductSelectListRequest.Merge(m, src)
}
func (m *GetNewBuyUPetProductSelectListRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewBuyUPetProductSelectListRequest.Size(m)
}
func (m *GetNewBuyUPetProductSelectListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewBuyUPetProductSelectListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewBuyUPetProductSelectListRequest proto.InternalMessageInfo

func (m *GetNewBuyUPetProductSelectListRequest) GetNid() int32 {
	if m != nil {
		return m.Nid
	}
	return 0
}

func (m *GetNewBuyUPetProductSelectListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetNewBuyUPetProductSelectListRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetNewBuyUPetProductSelectListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetNewBuyUPetProductSelectListRequest) GetPagination() *Page {
	if m != nil {
		return m.Pagination
	}
	return nil
}

func (m *GetNewBuyUPetProductSelectListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//阿闻电商参加新人专享活动的商品
type GetNewBuyUPetProductSelectListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//新人专享商品信息
	Data                 []*NewBuySelectUPetProductData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetNewBuyUPetProductSelectListResponse) Reset() {
	*m = GetNewBuyUPetProductSelectListResponse{}
}
func (m *GetNewBuyUPetProductSelectListResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewBuyUPetProductSelectListResponse) ProtoMessage()    {}
func (*GetNewBuyUPetProductSelectListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{11}
}

func (m *GetNewBuyUPetProductSelectListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewBuyUPetProductSelectListResponse.Unmarshal(m, b)
}
func (m *GetNewBuyUPetProductSelectListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewBuyUPetProductSelectListResponse.Marshal(b, m, deterministic)
}
func (m *GetNewBuyUPetProductSelectListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewBuyUPetProductSelectListResponse.Merge(m, src)
}
func (m *GetNewBuyUPetProductSelectListResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewBuyUPetProductSelectListResponse.Size(m)
}
func (m *GetNewBuyUPetProductSelectListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewBuyUPetProductSelectListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewBuyUPetProductSelectListResponse proto.InternalMessageInfo

func (m *GetNewBuyUPetProductSelectListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetNewBuyUPetProductSelectListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetNewBuyUPetProductSelectListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetNewBuyUPetProductSelectListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetNewBuyUPetProductSelectListResponse) GetData() []*NewBuySelectUPetProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

type NewBuySelectUPetProductData struct {
	//商品sku_id 对应商城的goods_id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	// 商品的产品id 对应商城的goods_commonid
	ProductId int32 `protobuf:"varint,2,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	ProductName string `protobuf:"bytes,3,opt,name=productName,proto3" json:"productName"`
	// 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
	TimeConflict int32 `protobuf:"varint,4,opt,name=timeConflict,proto3" json:"timeConflict"`
	//商品图片
	Pic string `protobuf:"bytes,5,opt,name=pic,proto3" json:"pic"`
	//库存
	Stock int32 `protobuf:"varint,6,opt,name=stock,proto3" json:"stock"`
	//价格 单位分
	MarketPrice int32 `protobuf:"varint,7,opt,name=marketPrice,proto3" json:"marketPrice"`
	//是否时虚拟产品 1是 0 否
	IsVirtual int32 `protobuf:"varint,8,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,9,opt,name=goodsType,proto3" json:"goodsType"`
	//组合商品的子商品信息
	ChildSkuIds          []*ChildInfo `protobuf:"bytes,10,rep,name=childSkuIds,proto3" json:"childSkuIds"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *NewBuySelectUPetProductData) Reset()         { *m = NewBuySelectUPetProductData{} }
func (m *NewBuySelectUPetProductData) String() string { return proto.CompactTextString(m) }
func (*NewBuySelectUPetProductData) ProtoMessage()    {}
func (*NewBuySelectUPetProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{12}
}

func (m *NewBuySelectUPetProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuySelectUPetProductData.Unmarshal(m, b)
}
func (m *NewBuySelectUPetProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuySelectUPetProductData.Marshal(b, m, deterministic)
}
func (m *NewBuySelectUPetProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuySelectUPetProductData.Merge(m, src)
}
func (m *NewBuySelectUPetProductData) XXX_Size() int {
	return xxx_messageInfo_NewBuySelectUPetProductData.Size(m)
}
func (m *NewBuySelectUPetProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuySelectUPetProductData.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuySelectUPetProductData proto.InternalMessageInfo

func (m *NewBuySelectUPetProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *NewBuySelectUPetProductData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *NewBuySelectUPetProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *NewBuySelectUPetProductData) GetTimeConflict() int32 {
	if m != nil {
		return m.TimeConflict
	}
	return 0
}

func (m *NewBuySelectUPetProductData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *NewBuySelectUPetProductData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *NewBuySelectUPetProductData) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *NewBuySelectUPetProductData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *NewBuySelectUPetProductData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *NewBuySelectUPetProductData) GetChildSkuIds() []*ChildInfo {
	if m != nil {
		return m.ChildSkuIds
	}
	return nil
}

//组合商品子商品讯息
type ChildInfo struct {
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//规则
	RuleNum int32 `protobuf:"varint,2,opt,name=ruleNum,proto3" json:"ruleNum"`
	//是否为虚拟 0:不是 1：是虚拟
	IsVirtual int32 `protobuf:"varint,3,opt,name=isVirtual,proto3" json:"isVirtual"`
	//0不是药品仓 1药品仓
	StockWarehouse       int32    `protobuf:"varint,7,opt,name=stockWarehouse,proto3" json:"stockWarehouse"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChildInfo) Reset()         { *m = ChildInfo{} }
func (m *ChildInfo) String() string { return proto.CompactTextString(m) }
func (*ChildInfo) ProtoMessage()    {}
func (*ChildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{13}
}

func (m *ChildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChildInfo.Unmarshal(m, b)
}
func (m *ChildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChildInfo.Marshal(b, m, deterministic)
}
func (m *ChildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChildInfo.Merge(m, src)
}
func (m *ChildInfo) XXX_Size() int {
	return xxx_messageInfo_ChildInfo.Size(m)
}
func (m *ChildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChildInfo proto.InternalMessageInfo

func (m *ChildInfo) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ChildInfo) GetRuleNum() int32 {
	if m != nil {
		return m.RuleNum
	}
	return 0
}

func (m *ChildInfo) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *ChildInfo) GetStockWarehouse() int32 {
	if m != nil {
		return m.StockWarehouse
	}
	return 0
}

//分页参数
type Page struct {
	//当前多少页 从1开始 必传且必须大于0
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	//每页多少条数据 必传且必须大于0
	PageSize             int32    `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Page) Reset()         { *m = Page{} }
func (m *Page) String() string { return proto.CompactTextString(m) }
func (*Page) ProtoMessage()    {}
func (*Page) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{14}
}

func (m *Page) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Page.Unmarshal(m, b)
}
func (m *Page) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Page.Marshal(b, m, deterministic)
}
func (m *Page) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Page.Merge(m, src)
}
func (m *Page) XXX_Size() int {
	return xxx_messageInfo_Page.Size(m)
}
func (m *Page) XXX_DiscardUnknown() {
	xxx_messageInfo_Page.DiscardUnknown(m)
}

var xxx_messageInfo_Page proto.InternalMessageInfo

func (m *Page) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *Page) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type NewBuyIdRequest struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//分页参数
	Pagination           *Page    `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewBuyIdRequest) Reset()         { *m = NewBuyIdRequest{} }
func (m *NewBuyIdRequest) String() string { return proto.CompactTextString(m) }
func (*NewBuyIdRequest) ProtoMessage()    {}
func (*NewBuyIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{15}
}

func (m *NewBuyIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyIdRequest.Unmarshal(m, b)
}
func (m *NewBuyIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyIdRequest.Marshal(b, m, deterministic)
}
func (m *NewBuyIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyIdRequest.Merge(m, src)
}
func (m *NewBuyIdRequest) XXX_Size() int {
	return xxx_messageInfo_NewBuyIdRequest.Size(m)
}
func (m *NewBuyIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyIdRequest proto.InternalMessageInfo

func (m *NewBuyIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NewBuyIdRequest) GetPagination() *Page {
	if m != nil {
		return m.Pagination
	}
	return nil
}

//活动商品导入数据响应
type NewBuyProductImportData struct {
	//导入时间
	CreateTime string `protobuf:"bytes,1,opt,name=createTime,proto3" json:"createTime"`
	//导入结果（0=>全部成功，1=>部分成功）
	Result int32 `protobuf:"varint,2,opt,name=result,proto3" json:"result"`
	//操作结果文件路径
	ResultFileUrl        string   `protobuf:"bytes,3,opt,name=resultFileUrl,proto3" json:"resultFileUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewBuyProductImportData) Reset()         { *m = NewBuyProductImportData{} }
func (m *NewBuyProductImportData) String() string { return proto.CompactTextString(m) }
func (*NewBuyProductImportData) ProtoMessage()    {}
func (*NewBuyProductImportData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{16}
}

func (m *NewBuyProductImportData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyProductImportData.Unmarshal(m, b)
}
func (m *NewBuyProductImportData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyProductImportData.Marshal(b, m, deterministic)
}
func (m *NewBuyProductImportData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyProductImportData.Merge(m, src)
}
func (m *NewBuyProductImportData) XXX_Size() int {
	return xxx_messageInfo_NewBuyProductImportData.Size(m)
}
func (m *NewBuyProductImportData) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyProductImportData.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyProductImportData proto.InternalMessageInfo

func (m *NewBuyProductImportData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *NewBuyProductImportData) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *NewBuyProductImportData) GetResultFileUrl() string {
	if m != nil {
		return m.ResultFileUrl
	}
	return ""
}

//活动商品导入列表响应
type GetNewBuyProductImportListResponse struct {
	//响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//新人专享商品信息
	Data                 []*NewBuyProductImportData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetNewBuyProductImportListResponse) Reset()         { *m = GetNewBuyProductImportListResponse{} }
func (m *GetNewBuyProductImportListResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewBuyProductImportListResponse) ProtoMessage()    {}
func (*GetNewBuyProductImportListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{17}
}

func (m *GetNewBuyProductImportListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewBuyProductImportListResponse.Unmarshal(m, b)
}
func (m *GetNewBuyProductImportListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewBuyProductImportListResponse.Marshal(b, m, deterministic)
}
func (m *GetNewBuyProductImportListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewBuyProductImportListResponse.Merge(m, src)
}
func (m *GetNewBuyProductImportListResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewBuyProductImportListResponse.Size(m)
}
func (m *GetNewBuyProductImportListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewBuyProductImportListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewBuyProductImportListResponse proto.InternalMessageInfo

func (m *GetNewBuyProductImportListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetNewBuyProductImportListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetNewBuyProductImportListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetNewBuyProductImportListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetNewBuyProductImportListResponse) GetData() []*NewBuyProductImportData {
	if m != nil {
		return m.Data
	}
	return nil
}

//活动商品导入请求
type ImportNewBuyProductRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImportNewBuyProductRequest) Reset()         { *m = ImportNewBuyProductRequest{} }
func (m *ImportNewBuyProductRequest) String() string { return proto.CompactTextString(m) }
func (*ImportNewBuyProductRequest) ProtoMessage()    {}
func (*ImportNewBuyProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{18}
}

func (m *ImportNewBuyProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImportNewBuyProductRequest.Unmarshal(m, b)
}
func (m *ImportNewBuyProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImportNewBuyProductRequest.Marshal(b, m, deterministic)
}
func (m *ImportNewBuyProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportNewBuyProductRequest.Merge(m, src)
}
func (m *ImportNewBuyProductRequest) XXX_Size() int {
	return xxx_messageInfo_ImportNewBuyProductRequest.Size(m)
}
func (m *ImportNewBuyProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportNewBuyProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ImportNewBuyProductRequest proto.InternalMessageInfo

// 异步任务创建
type CreateTaskRequest struct {
	// 活动ID
	Nid int32 `protobuf:"varint,1,opt,name=nid,proto3" json:"nid"`
	// 任务类型 3 导出新人专享数据
	TaskContent int32 `protobuf:"varint,2,opt,name=taskContent,proto3" json:"taskContent"`
	// 操作文件路径或者参数
	OperationFileUrl string `protobuf:"bytes,3,opt,name=operationFileUrl,proto3" json:"operationFileUrl"`
	// 操作请求的token值，类似userinfo
	RequestHeader string `protobuf:"bytes,4,opt,name=requestHeader,proto3" json:"requestHeader"`
	// 创建人id
	CreateId string `protobuf:"bytes,5,opt,name=createId,proto3" json:"createId"`
	// 主体id
	OrgId                int32    `protobuf:"varint,6,opt,name=OrgId,proto3" json:"OrgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTaskRequest) Reset()         { *m = CreateTaskRequest{} }
func (m *CreateTaskRequest) String() string { return proto.CompactTextString(m) }
func (*CreateTaskRequest) ProtoMessage()    {}
func (*CreateTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{19}
}

func (m *CreateTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTaskRequest.Unmarshal(m, b)
}
func (m *CreateTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTaskRequest.Marshal(b, m, deterministic)
}
func (m *CreateTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTaskRequest.Merge(m, src)
}
func (m *CreateTaskRequest) XXX_Size() int {
	return xxx_messageInfo_CreateTaskRequest.Size(m)
}
func (m *CreateTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTaskRequest proto.InternalMessageInfo

func (m *CreateTaskRequest) GetNid() int32 {
	if m != nil {
		return m.Nid
	}
	return 0
}

func (m *CreateTaskRequest) GetTaskContent() int32 {
	if m != nil {
		return m.TaskContent
	}
	return 0
}

func (m *CreateTaskRequest) GetOperationFileUrl() string {
	if m != nil {
		return m.OperationFileUrl
	}
	return ""
}

func (m *CreateTaskRequest) GetRequestHeader() string {
	if m != nil {
		return m.RequestHeader
	}
	return ""
}

func (m *CreateTaskRequest) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *CreateTaskRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 任务列表请求参数
type TaskListRequest struct {
	//活动ID
	Nid int32 `protobuf:"varint,1,opt,name=nid,proto3" json:"nid"`
	// 任务: 1 导出新人专享数据
	TaskContent int32 `protobuf:"varint,2,opt,name=taskContent,proto3" json:"taskContent"`
	// 查询人id
	UserNo string `protobuf:"bytes,3,opt,name=userNo,proto3" json:"userNo"`
	//分页参数
	Pagination *Page `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination"`
	// 任务状态
	TaskStatus           int32    `protobuf:"varint,5,opt,name=taskStatus,proto3" json:"taskStatus"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskListRequest) Reset()         { *m = TaskListRequest{} }
func (m *TaskListRequest) String() string { return proto.CompactTextString(m) }
func (*TaskListRequest) ProtoMessage()    {}
func (*TaskListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{20}
}

func (m *TaskListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskListRequest.Unmarshal(m, b)
}
func (m *TaskListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskListRequest.Marshal(b, m, deterministic)
}
func (m *TaskListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskListRequest.Merge(m, src)
}
func (m *TaskListRequest) XXX_Size() int {
	return xxx_messageInfo_TaskListRequest.Size(m)
}
func (m *TaskListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TaskListRequest proto.InternalMessageInfo

func (m *TaskListRequest) GetNid() int32 {
	if m != nil {
		return m.Nid
	}
	return 0
}

func (m *TaskListRequest) GetTaskContent() int32 {
	if m != nil {
		return m.TaskContent
	}
	return 0
}

func (m *TaskListRequest) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *TaskListRequest) GetPagination() *Page {
	if m != nil {
		return m.Pagination
	}
	return nil
}

func (m *TaskListRequest) GetTaskStatus() int32 {
	if m != nil {
		return m.TaskStatus
	}
	return 0
}

// 任务列表响应参数
type TaskListResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 总数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 数据
	Data                 []*TaskData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *TaskListResponse) Reset()         { *m = TaskListResponse{} }
func (m *TaskListResponse) String() string { return proto.CompactTextString(m) }
func (*TaskListResponse) ProtoMessage()    {}
func (*TaskListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{21}
}

func (m *TaskListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskListResponse.Unmarshal(m, b)
}
func (m *TaskListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskListResponse.Marshal(b, m, deterministic)
}
func (m *TaskListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskListResponse.Merge(m, src)
}
func (m *TaskListResponse) XXX_Size() int {
	return xxx_messageInfo_TaskListResponse.Size(m)
}
func (m *TaskListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TaskListResponse proto.InternalMessageInfo

func (m *TaskListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TaskListResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *TaskListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *TaskListResponse) GetData() []*TaskData {
	if m != nil {
		return m.Data
	}
	return nil
}

// 任务数据
type TaskData struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//活动ID
	Nid int32 `protobuf:"varint,2,opt,name=nid,proto3" json:"nid"`
	// 任务类型 1 导入新人专享商品数据
	TaskContent int32 `protobuf:"varint,3,opt,name=taskContent,proto3" json:"taskContent"`
	// 任务状态 1 进行中 2 已完成 3 失败
	TaskStatus int32 `protobuf:"varint,4,opt,name=taskStatus,proto3" json:"taskStatus"`
	// 操作文件路径或者参数
	OperationFileUrl string `protobuf:"bytes,5,opt,name=operationFileUrl,proto3" json:"operationFileUrl"`
	// 操作请求的token值，类似userinfo
	RequestHeader string `protobuf:"bytes,6,opt,name=requestHeader,proto3" json:"requestHeader"`
	// 操作结果文件路径
	ResulteFileUrl string `protobuf:"bytes,7,opt,name=resulteFileUrl,proto3" json:"resulteFileUrl"`
	// 创建时间
	CreateTime string `protobuf:"bytes,8,opt,name=createTime,proto3" json:"createTime"`
	// 更新时间
	ModifyTime string `protobuf:"bytes,9,opt,name=modifyTime,proto3" json:"modifyTime"`
	// 创建人id
	CreateId string `protobuf:"bytes,10,opt,name=createId,proto3" json:"createId"`
	// 任务详情
	TaskDetail           string   `protobuf:"bytes,11,opt,name=taskDetail,proto3" json:"taskDetail"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskData) Reset()         { *m = TaskData{} }
func (m *TaskData) String() string { return proto.CompactTextString(m) }
func (*TaskData) ProtoMessage()    {}
func (*TaskData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{22}
}

func (m *TaskData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskData.Unmarshal(m, b)
}
func (m *TaskData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskData.Marshal(b, m, deterministic)
}
func (m *TaskData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskData.Merge(m, src)
}
func (m *TaskData) XXX_Size() int {
	return xxx_messageInfo_TaskData.Size(m)
}
func (m *TaskData) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskData.DiscardUnknown(m)
}

var xxx_messageInfo_TaskData proto.InternalMessageInfo

func (m *TaskData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TaskData) GetNid() int32 {
	if m != nil {
		return m.Nid
	}
	return 0
}

func (m *TaskData) GetTaskContent() int32 {
	if m != nil {
		return m.TaskContent
	}
	return 0
}

func (m *TaskData) GetTaskStatus() int32 {
	if m != nil {
		return m.TaskStatus
	}
	return 0
}

func (m *TaskData) GetOperationFileUrl() string {
	if m != nil {
		return m.OperationFileUrl
	}
	return ""
}

func (m *TaskData) GetRequestHeader() string {
	if m != nil {
		return m.RequestHeader
	}
	return ""
}

func (m *TaskData) GetResulteFileUrl() string {
	if m != nil {
		return m.ResulteFileUrl
	}
	return ""
}

func (m *TaskData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *TaskData) GetModifyTime() string {
	if m != nil {
		return m.ModifyTime
	}
	return ""
}

func (m *TaskData) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *TaskData) GetTaskDetail() string {
	if m != nil {
		return m.TaskDetail
	}
	return ""
}

// 新人专享列表
type NewPeopleListRequest struct {
	From     string `protobuf:"bytes,1,opt,name=from,proto3" json:"from"`
	Page     int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	PageSize int32  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleListRequest) Reset()         { *m = NewPeopleListRequest{} }
func (m *NewPeopleListRequest) String() string { return proto.CompactTextString(m) }
func (*NewPeopleListRequest) ProtoMessage()    {}
func (*NewPeopleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{23}
}

func (m *NewPeopleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleListRequest.Unmarshal(m, b)
}
func (m *NewPeopleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleListRequest.Marshal(b, m, deterministic)
}
func (m *NewPeopleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleListRequest.Merge(m, src)
}
func (m *NewPeopleListRequest) XXX_Size() int {
	return xxx_messageInfo_NewPeopleListRequest.Size(m)
}
func (m *NewPeopleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleListRequest proto.InternalMessageInfo

func (m *NewPeopleListRequest) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

func (m *NewPeopleListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *NewPeopleListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *NewPeopleListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type NewPeopleListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	// 新人专享分页数据
	Data    []*NewPeopleData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	HasMore int32            `protobuf:"varint,5,opt,name=has_more,json=hasMore,proto3" json:"has_more"`
	// 当前页和条数
	Page                 int32    `protobuf:"varint,6,opt,name=page,proto3" json:"page"`
	PageSize             int32    `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleListResponse) Reset()         { *m = NewPeopleListResponse{} }
func (m *NewPeopleListResponse) String() string { return proto.CompactTextString(m) }
func (*NewPeopleListResponse) ProtoMessage()    {}
func (*NewPeopleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{24}
}

func (m *NewPeopleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleListResponse.Unmarshal(m, b)
}
func (m *NewPeopleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleListResponse.Marshal(b, m, deterministic)
}
func (m *NewPeopleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleListResponse.Merge(m, src)
}
func (m *NewPeopleListResponse) XXX_Size() int {
	return xxx_messageInfo_NewPeopleListResponse.Size(m)
}
func (m *NewPeopleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleListResponse proto.InternalMessageInfo

func (m *NewPeopleListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewPeopleListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NewPeopleListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *NewPeopleListResponse) GetData() []*NewPeopleData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *NewPeopleListResponse) GetHasMore() int32 {
	if m != nil {
		return m.HasMore
	}
	return 0
}

func (m *NewPeopleListResponse) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *NewPeopleListResponse) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type NewPeopleData struct {
	//商品图片链接
	GoodsImageUrl string `protobuf:"bytes,1,opt,name=goods_image_url,json=goodsImageUrl,proto3" json:"goods_image_url"`
	//商品名称
	GoodsName string `protobuf:"bytes,2,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//商品简称
	GoodsShortName string `protobuf:"bytes,7,opt,name=goods_short_name,json=goodsShortName,proto3" json:"goods_short_name"`
	//新人价
	GoodsPrice float32 `protobuf:"fixed32,3,opt,name=goods_price,json=goodsPrice,proto3" json:"goods_price"`
	//市场价
	GoodsNormalPrice float32 `protobuf:"fixed32,4,opt,name=goods_normal_price,json=goodsNormalPrice,proto3" json:"goods_normal_price"`
	//skuid
	GoodsId int32 `protobuf:"varint,5,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	//是否虚拟商品 1 是 0 否
	IsVirtual int32 `protobuf:"varint,6,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	//是否购物车显示 1 是 0否
	IsShowCart           int32    `protobuf:"varint,8,opt,name=is_show_cart,json=isShowCart,proto3" json:"is_show_cart"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleData) Reset()         { *m = NewPeopleData{} }
func (m *NewPeopleData) String() string { return proto.CompactTextString(m) }
func (*NewPeopleData) ProtoMessage()    {}
func (*NewPeopleData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{25}
}

func (m *NewPeopleData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleData.Unmarshal(m, b)
}
func (m *NewPeopleData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleData.Marshal(b, m, deterministic)
}
func (m *NewPeopleData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleData.Merge(m, src)
}
func (m *NewPeopleData) XXX_Size() int {
	return xxx_messageInfo_NewPeopleData.Size(m)
}
func (m *NewPeopleData) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleData.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleData proto.InternalMessageInfo

func (m *NewPeopleData) GetGoodsImageUrl() string {
	if m != nil {
		return m.GoodsImageUrl
	}
	return ""
}

func (m *NewPeopleData) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *NewPeopleData) GetGoodsShortName() string {
	if m != nil {
		return m.GoodsShortName
	}
	return ""
}

func (m *NewPeopleData) GetGoodsPrice() float32 {
	if m != nil {
		return m.GoodsPrice
	}
	return 0
}

func (m *NewPeopleData) GetGoodsNormalPrice() float32 {
	if m != nil {
		return m.GoodsNormalPrice
	}
	return 0
}

func (m *NewPeopleData) GetGoodsId() int32 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

func (m *NewPeopleData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *NewPeopleData) GetIsShowCart() int32 {
	if m != nil {
		return m.IsShowCart
	}
	return 0
}

// 新人专享券
type NewPeopleVoucherRequest struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleVoucherRequest) Reset()         { *m = NewPeopleVoucherRequest{} }
func (m *NewPeopleVoucherRequest) String() string { return proto.CompactTextString(m) }
func (*NewPeopleVoucherRequest) ProtoMessage()    {}
func (*NewPeopleVoucherRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{26}
}

func (m *NewPeopleVoucherRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleVoucherRequest.Unmarshal(m, b)
}
func (m *NewPeopleVoucherRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleVoucherRequest.Marshal(b, m, deterministic)
}
func (m *NewPeopleVoucherRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleVoucherRequest.Merge(m, src)
}
func (m *NewPeopleVoucherRequest) XXX_Size() int {
	return xxx_messageInfo_NewPeopleVoucherRequest.Size(m)
}
func (m *NewPeopleVoucherRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleVoucherRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleVoucherRequest proto.InternalMessageInfo

func (m *NewPeopleVoucherRequest) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type NewPeopleVoucherResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	// 已领取总数
	HasGetTotal          int32                   `protobuf:"varint,4,opt,name=has_get_total,json=hasGetTotal,proto3" json:"has_get_total"`
	Data                 []*NewPeopleVoucherData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *NewPeopleVoucherResponse) Reset()         { *m = NewPeopleVoucherResponse{} }
func (m *NewPeopleVoucherResponse) String() string { return proto.CompactTextString(m) }
func (*NewPeopleVoucherResponse) ProtoMessage()    {}
func (*NewPeopleVoucherResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{27}
}

func (m *NewPeopleVoucherResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleVoucherResponse.Unmarshal(m, b)
}
func (m *NewPeopleVoucherResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleVoucherResponse.Marshal(b, m, deterministic)
}
func (m *NewPeopleVoucherResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleVoucherResponse.Merge(m, src)
}
func (m *NewPeopleVoucherResponse) XXX_Size() int {
	return xxx_messageInfo_NewPeopleVoucherResponse.Size(m)
}
func (m *NewPeopleVoucherResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleVoucherResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleVoucherResponse proto.InternalMessageInfo

func (m *NewPeopleVoucherResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewPeopleVoucherResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NewPeopleVoucherResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *NewPeopleVoucherResponse) GetHasGetTotal() int32 {
	if m != nil {
		return m.HasGetTotal
	}
	return 0
}

func (m *NewPeopleVoucherResponse) GetData() []*NewPeopleVoucherData {
	if m != nil {
		return m.Data
	}
	return nil
}

type NewPeopleVoucherData struct {
	//代金券模版编号
	VoucherTId int32 `protobuf:"varint,1,opt,name=voucher_t_id,json=voucherTId,proto3" json:"voucher_t_id"`
	//代金券使用时的订单限额
	LimitPrice string `protobuf:"bytes,2,opt,name=limit_price,json=limitPrice,proto3" json:"limit_price"`
	//代金券模版面额
	VoucherPrice string `protobuf:"bytes,3,opt,name=voucher_price,json=voucherPrice,proto3" json:"voucher_price"`
	//0-未领取，1-已领取，2-已抢光，3-已过期
	HasGet               int32    `protobuf:"varint,4,opt,name=has_get,json=hasGet,proto3" json:"has_get"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleVoucherData) Reset()         { *m = NewPeopleVoucherData{} }
func (m *NewPeopleVoucherData) String() string { return proto.CompactTextString(m) }
func (*NewPeopleVoucherData) ProtoMessage()    {}
func (*NewPeopleVoucherData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{28}
}

func (m *NewPeopleVoucherData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleVoucherData.Unmarshal(m, b)
}
func (m *NewPeopleVoucherData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleVoucherData.Marshal(b, m, deterministic)
}
func (m *NewPeopleVoucherData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleVoucherData.Merge(m, src)
}
func (m *NewPeopleVoucherData) XXX_Size() int {
	return xxx_messageInfo_NewPeopleVoucherData.Size(m)
}
func (m *NewPeopleVoucherData) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleVoucherData.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleVoucherData proto.InternalMessageInfo

func (m *NewPeopleVoucherData) GetVoucherTId() int32 {
	if m != nil {
		return m.VoucherTId
	}
	return 0
}

func (m *NewPeopleVoucherData) GetLimitPrice() string {
	if m != nil {
		return m.LimitPrice
	}
	return ""
}

func (m *NewPeopleVoucherData) GetVoucherPrice() string {
	if m != nil {
		return m.VoucherPrice
	}
	return ""
}

func (m *NewPeopleVoucherData) GetHasGet() int32 {
	if m != nil {
		return m.HasGet
	}
	return 0
}

// 新人券领取
type NewPeopleVoucherGetRequest struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	VoucherTIds          string   `protobuf:"bytes,2,opt,name=voucher_t_ids,json=voucherTIds,proto3" json:"voucher_t_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleVoucherGetRequest) Reset()         { *m = NewPeopleVoucherGetRequest{} }
func (m *NewPeopleVoucherGetRequest) String() string { return proto.CompactTextString(m) }
func (*NewPeopleVoucherGetRequest) ProtoMessage()    {}
func (*NewPeopleVoucherGetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{29}
}

func (m *NewPeopleVoucherGetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleVoucherGetRequest.Unmarshal(m, b)
}
func (m *NewPeopleVoucherGetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleVoucherGetRequest.Marshal(b, m, deterministic)
}
func (m *NewPeopleVoucherGetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleVoucherGetRequest.Merge(m, src)
}
func (m *NewPeopleVoucherGetRequest) XXX_Size() int {
	return xxx_messageInfo_NewPeopleVoucherGetRequest.Size(m)
}
func (m *NewPeopleVoucherGetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleVoucherGetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleVoucherGetRequest proto.InternalMessageInfo

func (m *NewPeopleVoucherGetRequest) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *NewPeopleVoucherGetRequest) GetVoucherTIds() string {
	if m != nil {
		return m.VoucherTIds
	}
	return ""
}

// 新人专享券设置
type NewPeopleVoucherAddRequest struct {
	VoucherTIds          string   `protobuf:"bytes,1,opt,name=voucher_t_ids,json=voucherTIds,proto3" json:"voucher_t_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleVoucherAddRequest) Reset()         { *m = NewPeopleVoucherAddRequest{} }
func (m *NewPeopleVoucherAddRequest) String() string { return proto.CompactTextString(m) }
func (*NewPeopleVoucherAddRequest) ProtoMessage()    {}
func (*NewPeopleVoucherAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{30}
}

func (m *NewPeopleVoucherAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleVoucherAddRequest.Unmarshal(m, b)
}
func (m *NewPeopleVoucherAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleVoucherAddRequest.Marshal(b, m, deterministic)
}
func (m *NewPeopleVoucherAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleVoucherAddRequest.Merge(m, src)
}
func (m *NewPeopleVoucherAddRequest) XXX_Size() int {
	return xxx_messageInfo_NewPeopleVoucherAddRequest.Size(m)
}
func (m *NewPeopleVoucherAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleVoucherAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleVoucherAddRequest proto.InternalMessageInfo

func (m *NewPeopleVoucherAddRequest) GetVoucherTIds() string {
	if m != nil {
		return m.VoucherTIds
	}
	return ""
}

type NewPeopleVoucherAddResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	// 错误id对应的信息
	ErrIds               []*NewPeopleVoucherAddErrData `protobuf:"bytes,4,rep,name=err_ids,json=errIds,proto3" json:"err_ids"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *NewPeopleVoucherAddResponse) Reset()         { *m = NewPeopleVoucherAddResponse{} }
func (m *NewPeopleVoucherAddResponse) String() string { return proto.CompactTextString(m) }
func (*NewPeopleVoucherAddResponse) ProtoMessage()    {}
func (*NewPeopleVoucherAddResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{31}
}

func (m *NewPeopleVoucherAddResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleVoucherAddResponse.Unmarshal(m, b)
}
func (m *NewPeopleVoucherAddResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleVoucherAddResponse.Marshal(b, m, deterministic)
}
func (m *NewPeopleVoucherAddResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleVoucherAddResponse.Merge(m, src)
}
func (m *NewPeopleVoucherAddResponse) XXX_Size() int {
	return xxx_messageInfo_NewPeopleVoucherAddResponse.Size(m)
}
func (m *NewPeopleVoucherAddResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleVoucherAddResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleVoucherAddResponse proto.InternalMessageInfo

func (m *NewPeopleVoucherAddResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewPeopleVoucherAddResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NewPeopleVoucherAddResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *NewPeopleVoucherAddResponse) GetErrIds() []*NewPeopleVoucherAddErrData {
	if m != nil {
		return m.ErrIds
	}
	return nil
}

type NewPeopleVoucherAddErrData struct {
	//代金券模版编号
	VoucherTId int32 `protobuf:"varint,1,opt,name=voucher_t_id,json=voucherTId,proto3" json:"voucher_t_id"`
	//错误信息
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleVoucherAddErrData) Reset()         { *m = NewPeopleVoucherAddErrData{} }
func (m *NewPeopleVoucherAddErrData) String() string { return proto.CompactTextString(m) }
func (*NewPeopleVoucherAddErrData) ProtoMessage()    {}
func (*NewPeopleVoucherAddErrData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{32}
}

func (m *NewPeopleVoucherAddErrData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleVoucherAddErrData.Unmarshal(m, b)
}
func (m *NewPeopleVoucherAddErrData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleVoucherAddErrData.Marshal(b, m, deterministic)
}
func (m *NewPeopleVoucherAddErrData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleVoucherAddErrData.Merge(m, src)
}
func (m *NewPeopleVoucherAddErrData) XXX_Size() int {
	return xxx_messageInfo_NewPeopleVoucherAddErrData.Size(m)
}
func (m *NewPeopleVoucherAddErrData) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleVoucherAddErrData.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleVoucherAddErrData proto.InternalMessageInfo

func (m *NewPeopleVoucherAddErrData) GetVoucherTId() int32 {
	if m != nil {
		return m.VoucherTId
	}
	return 0
}

func (m *NewPeopleVoucherAddErrData) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

//新人活动详情请求
type NewBuyInfoRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewBuyInfoRequest) Reset()         { *m = NewBuyInfoRequest{} }
func (m *NewBuyInfoRequest) String() string { return proto.CompactTextString(m) }
func (*NewBuyInfoRequest) ProtoMessage()    {}
func (*NewBuyInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{33}
}

func (m *NewBuyInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyInfoRequest.Unmarshal(m, b)
}
func (m *NewBuyInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyInfoRequest.Marshal(b, m, deterministic)
}
func (m *NewBuyInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyInfoRequest.Merge(m, src)
}
func (m *NewBuyInfoRequest) XXX_Size() int {
	return xxx_messageInfo_NewBuyInfoRequest.Size(m)
}
func (m *NewBuyInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyInfoRequest proto.InternalMessageInfo

func (m *NewBuyInfoRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

//创建新人专享活动请求
type NewBuyRequest struct {
	// 活动id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动名称
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	//渠道id
	ChannelId int32 `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 活动开始时间
	BeginDate string `protobuf:"bytes,4,opt,name=begin_date,json=beginDate,proto3" json:"begin_date"`
	// 活动结束时间
	EndDate string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	//分享封面
	Cover string `protobuf:"bytes,6,opt,name=cover,proto3" json:"cover"`
	//头图
	HeadImg string `protobuf:"bytes,7,opt,name=head_img,json=headImg,proto3" json:"head_img"`
	//活动状态
	Status int32 `protobuf:"varint,8,opt,name=status,proto3" json:"status"`
	// 参加活动的商品数量
	ProductCount int32 `protobuf:"varint,9,opt,name=product_count,json=productCount,proto3" json:"product_count"`
	// 是否免邮费 0否1是
	IsShippingFree int32 `protobuf:"varint,10,opt,name=is_shipping_free,json=isShippingFree,proto3" json:"is_shipping_free"`
	//创建时间
	CreateTime string `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//更新时间
	UpdateTime           string   `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewBuyRequest) Reset()         { *m = NewBuyRequest{} }
func (m *NewBuyRequest) String() string { return proto.CompactTextString(m) }
func (*NewBuyRequest) ProtoMessage()    {}
func (*NewBuyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{34}
}

func (m *NewBuyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyRequest.Unmarshal(m, b)
}
func (m *NewBuyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyRequest.Marshal(b, m, deterministic)
}
func (m *NewBuyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyRequest.Merge(m, src)
}
func (m *NewBuyRequest) XXX_Size() int {
	return xxx_messageInfo_NewBuyRequest.Size(m)
}
func (m *NewBuyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyRequest proto.InternalMessageInfo

func (m *NewBuyRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NewBuyRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NewBuyRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NewBuyRequest) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *NewBuyRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *NewBuyRequest) GetCover() string {
	if m != nil {
		return m.Cover
	}
	return ""
}

func (m *NewBuyRequest) GetHeadImg() string {
	if m != nil {
		return m.HeadImg
	}
	return ""
}

func (m *NewBuyRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *NewBuyRequest) GetProductCount() int32 {
	if m != nil {
		return m.ProductCount
	}
	return 0
}

func (m *NewBuyRequest) GetIsShippingFree() int32 {
	if m != nil {
		return m.IsShippingFree
	}
	return 0
}

func (m *NewBuyRequest) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *NewBuyRequest) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

//新人专享活动详情响应
type NewBuyInfoResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//列表
	Detail               *NewBuyRequest `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *NewBuyInfoResponse) Reset()         { *m = NewBuyInfoResponse{} }
func (m *NewBuyInfoResponse) String() string { return proto.CompactTextString(m) }
func (*NewBuyInfoResponse) ProtoMessage()    {}
func (*NewBuyInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{35}
}

func (m *NewBuyInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyInfoResponse.Unmarshal(m, b)
}
func (m *NewBuyInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyInfoResponse.Marshal(b, m, deterministic)
}
func (m *NewBuyInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyInfoResponse.Merge(m, src)
}
func (m *NewBuyInfoResponse) XXX_Size() int {
	return xxx_messageInfo_NewBuyInfoResponse.Size(m)
}
func (m *NewBuyInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyInfoResponse proto.InternalMessageInfo

func (m *NewBuyInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewBuyInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NewBuyInfoResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *NewBuyInfoResponse) GetDetail() *NewBuyRequest {
	if m != nil {
		return m.Detail
	}
	return nil
}

// 小程序接口获取参加新人专享活动商品
type GetNewPeopleProductRequest struct {
	// sku_id集合，空表示所有
	SkuIds               string   `protobuf:"bytes,1,opt,name=sku_ids,json=skuIds,proto3" json:"sku_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewPeopleProductRequest) Reset()         { *m = GetNewPeopleProductRequest{} }
func (m *GetNewPeopleProductRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewPeopleProductRequest) ProtoMessage()    {}
func (*GetNewPeopleProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{36}
}

func (m *GetNewPeopleProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewPeopleProductRequest.Unmarshal(m, b)
}
func (m *GetNewPeopleProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewPeopleProductRequest.Marshal(b, m, deterministic)
}
func (m *GetNewPeopleProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewPeopleProductRequest.Merge(m, src)
}
func (m *GetNewPeopleProductRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewPeopleProductRequest.Size(m)
}
func (m *GetNewPeopleProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewPeopleProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewPeopleProductRequest proto.InternalMessageInfo

func (m *GetNewPeopleProductRequest) GetSkuIds() string {
	if m != nil {
		return m.SkuIds
	}
	return ""
}

// 新人专享券列表
type NewBuyVoucherListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//列表
	Data                 []*NewBuyVoucherData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *NewBuyVoucherListResponse) Reset()         { *m = NewBuyVoucherListResponse{} }
func (m *NewBuyVoucherListResponse) String() string { return proto.CompactTextString(m) }
func (*NewBuyVoucherListResponse) ProtoMessage()    {}
func (*NewBuyVoucherListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{37}
}

func (m *NewBuyVoucherListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyVoucherListResponse.Unmarshal(m, b)
}
func (m *NewBuyVoucherListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyVoucherListResponse.Marshal(b, m, deterministic)
}
func (m *NewBuyVoucherListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyVoucherListResponse.Merge(m, src)
}
func (m *NewBuyVoucherListResponse) XXX_Size() int {
	return xxx_messageInfo_NewBuyVoucherListResponse.Size(m)
}
func (m *NewBuyVoucherListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyVoucherListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyVoucherListResponse proto.InternalMessageInfo

func (m *NewBuyVoucherListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewBuyVoucherListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NewBuyVoucherListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *NewBuyVoucherListResponse) GetData() []*NewBuyVoucherData {
	if m != nil {
		return m.Data
	}
	return nil
}

type NewBuyVoucherData struct {
	//商城优惠券id
	VoucherId            int32    `protobuf:"varint,1,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewBuyVoucherData) Reset()         { *m = NewBuyVoucherData{} }
func (m *NewBuyVoucherData) String() string { return proto.CompactTextString(m) }
func (*NewBuyVoucherData) ProtoMessage()    {}
func (*NewBuyVoucherData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{38}
}

func (m *NewBuyVoucherData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewBuyVoucherData.Unmarshal(m, b)
}
func (m *NewBuyVoucherData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewBuyVoucherData.Marshal(b, m, deterministic)
}
func (m *NewBuyVoucherData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewBuyVoucherData.Merge(m, src)
}
func (m *NewBuyVoucherData) XXX_Size() int {
	return xxx_messageInfo_NewBuyVoucherData.Size(m)
}
func (m *NewBuyVoucherData) XXX_DiscardUnknown() {
	xxx_messageInfo_NewBuyVoucherData.DiscardUnknown(m)
}

var xxx_messageInfo_NewBuyVoucherData proto.InternalMessageInfo

func (m *NewBuyVoucherData) GetVoucherId() int32 {
	if m != nil {
		return m.VoucherId
	}
	return 0
}

// 新人专享banner
type NewPeopleBannerRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleBannerRequest) Reset()         { *m = NewPeopleBannerRequest{} }
func (m *NewPeopleBannerRequest) String() string { return proto.CompactTextString(m) }
func (*NewPeopleBannerRequest) ProtoMessage()    {}
func (*NewPeopleBannerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{39}
}

func (m *NewPeopleBannerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleBannerRequest.Unmarshal(m, b)
}
func (m *NewPeopleBannerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleBannerRequest.Marshal(b, m, deterministic)
}
func (m *NewPeopleBannerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleBannerRequest.Merge(m, src)
}
func (m *NewPeopleBannerRequest) XXX_Size() int {
	return xxx_messageInfo_NewPeopleBannerRequest.Size(m)
}
func (m *NewPeopleBannerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleBannerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleBannerRequest proto.InternalMessageInfo

type NewPeopleBannerResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//列表
	Data                 *NewPeopleBannerData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *NewPeopleBannerResponse) Reset()         { *m = NewPeopleBannerResponse{} }
func (m *NewPeopleBannerResponse) String() string { return proto.CompactTextString(m) }
func (*NewPeopleBannerResponse) ProtoMessage()    {}
func (*NewPeopleBannerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{40}
}

func (m *NewPeopleBannerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleBannerResponse.Unmarshal(m, b)
}
func (m *NewPeopleBannerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleBannerResponse.Marshal(b, m, deterministic)
}
func (m *NewPeopleBannerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleBannerResponse.Merge(m, src)
}
func (m *NewPeopleBannerResponse) XXX_Size() int {
	return xxx_messageInfo_NewPeopleBannerResponse.Size(m)
}
func (m *NewPeopleBannerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleBannerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleBannerResponse proto.InternalMessageInfo

func (m *NewPeopleBannerResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *NewPeopleBannerResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *NewPeopleBannerResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *NewPeopleBannerResponse) GetData() *NewPeopleBannerData {
	if m != nil {
		return m.Data
	}
	return nil
}

type NewPeopleBannerData struct {
	//分享标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	//开始时间
	BeginDate string `protobuf:"bytes,2,opt,name=begin_date,json=beginDate,proto3" json:"begin_date"`
	//结束时间
	EndDate string `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	//头图
	Cover string `protobuf:"bytes,4,opt,name=cover,proto3" json:"cover"`
	//分享封面
	Pic string `protobuf:"bytes,5,opt,name=pic,proto3" json:"pic"`
	//当前时间
	SysTime              string   `protobuf:"bytes,6,opt,name=sys_time,json=sysTime,proto3" json:"sys_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewPeopleBannerData) Reset()         { *m = NewPeopleBannerData{} }
func (m *NewPeopleBannerData) String() string { return proto.CompactTextString(m) }
func (*NewPeopleBannerData) ProtoMessage()    {}
func (*NewPeopleBannerData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{41}
}

func (m *NewPeopleBannerData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPeopleBannerData.Unmarshal(m, b)
}
func (m *NewPeopleBannerData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPeopleBannerData.Marshal(b, m, deterministic)
}
func (m *NewPeopleBannerData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPeopleBannerData.Merge(m, src)
}
func (m *NewPeopleBannerData) XXX_Size() int {
	return xxx_messageInfo_NewPeopleBannerData.Size(m)
}
func (m *NewPeopleBannerData) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPeopleBannerData.DiscardUnknown(m)
}

var xxx_messageInfo_NewPeopleBannerData proto.InternalMessageInfo

func (m *NewPeopleBannerData) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NewPeopleBannerData) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

func (m *NewPeopleBannerData) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *NewPeopleBannerData) GetCover() string {
	if m != nil {
		return m.Cover
	}
	return ""
}

func (m *NewPeopleBannerData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *NewPeopleBannerData) GetSysTime() string {
	if m != nil {
		return m.SysTime
	}
	return ""
}

//更新活动响应
type UpdateNewBuyResponse struct {
	//响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//冲突商品信息
	Data                 []*ConflictProductData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *UpdateNewBuyResponse) Reset()         { *m = UpdateNewBuyResponse{} }
func (m *UpdateNewBuyResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateNewBuyResponse) ProtoMessage()    {}
func (*UpdateNewBuyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{42}
}

func (m *UpdateNewBuyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNewBuyResponse.Unmarshal(m, b)
}
func (m *UpdateNewBuyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNewBuyResponse.Marshal(b, m, deterministic)
}
func (m *UpdateNewBuyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNewBuyResponse.Merge(m, src)
}
func (m *UpdateNewBuyResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateNewBuyResponse.Size(m)
}
func (m *UpdateNewBuyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNewBuyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNewBuyResponse proto.InternalMessageInfo

func (m *UpdateNewBuyResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdateNewBuyResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UpdateNewBuyResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *UpdateNewBuyResponse) GetData() []*ConflictProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

type ConflictProductData struct {
	//商品sku_id 对应商城的goods_id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	// 商品名称
	ProductName          string   `protobuf:"bytes,3,opt,name=productName,proto3" json:"productName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConflictProductData) Reset()         { *m = ConflictProductData{} }
func (m *ConflictProductData) String() string { return proto.CompactTextString(m) }
func (*ConflictProductData) ProtoMessage()    {}
func (*ConflictProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5955276a4323d6b4, []int{43}
}

func (m *ConflictProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConflictProductData.Unmarshal(m, b)
}
func (m *ConflictProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConflictProductData.Marshal(b, m, deterministic)
}
func (m *ConflictProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConflictProductData.Merge(m, src)
}
func (m *ConflictProductData) XXX_Size() int {
	return xxx_messageInfo_ConflictProductData.Size(m)
}
func (m *ConflictProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_ConflictProductData.DiscardUnknown(m)
}

var xxx_messageInfo_ConflictProductData proto.InternalMessageInfo

func (m *ConflictProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ConflictProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func init() {
	proto.RegisterType((*GetNewBuyProductListRequest)(nil), "ac.GetNewBuyProductListRequest")
	proto.RegisterType((*GetNewBuyProductListResponse)(nil), "ac.GetNewBuyProductListResponse")
	proto.RegisterType((*NewBuyProductData)(nil), "ac.NewBuyProductData")
	proto.RegisterType((*CreateNewBuyProductRequest)(nil), "ac.CreateNewBuyProductRequest")
	proto.RegisterType((*SaveNewBuyProductData)(nil), "ac.SaveNewBuyProductData")
	proto.RegisterType((*UpdateNewBuyProductRequest)(nil), "ac.UpdateNewBuyProductRequest")
	proto.RegisterType((*NewBuyProductDetailRequest)(nil), "ac.NewBuyProductDetailRequest")
	proto.RegisterType((*GetNewBuyProductDetailResponse)(nil), "ac.GetNewBuyProductDetailResponse")
	proto.RegisterType((*NewBuyProductDetailData)(nil), "ac.NewBuyProductDetailData")
	proto.RegisterType((*NewBuyProductIdRequest)(nil), "ac.NewBuyProductIdRequest")
	proto.RegisterType((*GetNewBuyUPetProductSelectListRequest)(nil), "ac.GetNewBuyUPetProductSelectListRequest")
	proto.RegisterType((*GetNewBuyUPetProductSelectListResponse)(nil), "ac.GetNewBuyUPetProductSelectListResponse")
	proto.RegisterType((*NewBuySelectUPetProductData)(nil), "ac.NewBuySelectUPetProductData")
	proto.RegisterType((*ChildInfo)(nil), "ac.ChildInfo")
	proto.RegisterType((*Page)(nil), "ac.Page")
	proto.RegisterType((*NewBuyIdRequest)(nil), "ac.NewBuyIdRequest")
	proto.RegisterType((*NewBuyProductImportData)(nil), "ac.NewBuyProductImportData")
	proto.RegisterType((*GetNewBuyProductImportListResponse)(nil), "ac.GetNewBuyProductImportListResponse")
	proto.RegisterType((*ImportNewBuyProductRequest)(nil), "ac.ImportNewBuyProductRequest")
	proto.RegisterType((*CreateTaskRequest)(nil), "ac.CreateTaskRequest")
	proto.RegisterType((*TaskListRequest)(nil), "ac.TaskListRequest")
	proto.RegisterType((*TaskListResponse)(nil), "ac.TaskListResponse")
	proto.RegisterType((*TaskData)(nil), "ac.TaskData")
	proto.RegisterType((*NewPeopleListRequest)(nil), "ac.NewPeopleListRequest")
	proto.RegisterType((*NewPeopleListResponse)(nil), "ac.NewPeopleListResponse")
	proto.RegisterType((*NewPeopleData)(nil), "ac.NewPeopleData")
	proto.RegisterType((*NewPeopleVoucherRequest)(nil), "ac.NewPeopleVoucherRequest")
	proto.RegisterType((*NewPeopleVoucherResponse)(nil), "ac.NewPeopleVoucherResponse")
	proto.RegisterType((*NewPeopleVoucherData)(nil), "ac.NewPeopleVoucherData")
	proto.RegisterType((*NewPeopleVoucherGetRequest)(nil), "ac.NewPeopleVoucherGetRequest")
	proto.RegisterType((*NewPeopleVoucherAddRequest)(nil), "ac.NewPeopleVoucherAddRequest")
	proto.RegisterType((*NewPeopleVoucherAddResponse)(nil), "ac.NewPeopleVoucherAddResponse")
	proto.RegisterType((*NewPeopleVoucherAddErrData)(nil), "ac.NewPeopleVoucherAddErrData")
	proto.RegisterType((*NewBuyInfoRequest)(nil), "ac.NewBuyInfoRequest")
	proto.RegisterType((*NewBuyRequest)(nil), "ac.NewBuyRequest")
	proto.RegisterType((*NewBuyInfoResponse)(nil), "ac.NewBuyInfoResponse")
	proto.RegisterType((*GetNewPeopleProductRequest)(nil), "ac.GetNewPeopleProductRequest")
	proto.RegisterType((*NewBuyVoucherListResponse)(nil), "ac.NewBuyVoucherListResponse")
	proto.RegisterType((*NewBuyVoucherData)(nil), "ac.NewBuyVoucherData")
	proto.RegisterType((*NewPeopleBannerRequest)(nil), "ac.NewPeopleBannerRequest")
	proto.RegisterType((*NewPeopleBannerResponse)(nil), "ac.NewPeopleBannerResponse")
	proto.RegisterType((*NewPeopleBannerData)(nil), "ac.NewPeopleBannerData")
	proto.RegisterType((*UpdateNewBuyResponse)(nil), "ac.UpdateNewBuyResponse")
	proto.RegisterType((*ConflictProductData)(nil), "ac.ConflictProductData")
}

func init() { proto.RegisterFile("ac/new_buy_service.proto", fileDescriptor_5955276a4323d6b4) }

var fileDescriptor_5955276a4323d6b4 = []byte{
	// 2361 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0xcd, 0x6f, 0x1c, 0x49,
	0x15, 0x57, 0xcf, 0xf7, 0xbc, 0xf1, 0xd8, 0x4e, 0xdb, 0xb1, 0xdb, 0x63, 0xc7, 0xb1, 0x3a, 0x6c,
	0xe4, 0xec, 0xae, 0x62, 0xc9, 0x51, 0x04, 0x12, 0x12, 0x4a, 0xec, 0xec, 0x86, 0x01, 0xe2, 0x35,
	0x63, 0x27, 0x2b, 0x24, 0xa4, 0x51, 0xa5, 0xbb, 0x3c, 0x6e, 0xb9, 0xa7, 0x7b, 0xa8, 0xee, 0xb1,
	0x19, 0x6e, 0x88, 0x03, 0x12, 0x77, 0x40, 0xe2, 0xc0, 0x85, 0x03, 0x48, 0x48, 0xec, 0xde, 0xf8,
	0x1b, 0x10, 0xd2, 0x8a, 0x3f, 0x82, 0x03, 0xff, 0x05, 0xa8, 0xea, 0x55, 0x77, 0x57, 0x7f, 0x8d,
	0xbd, 0x59, 0x2f, 0xa7, 0xbd, 0x4d, 0xbd, 0x57, 0x5d, 0xfd, 0x3e, 0x7e, 0xef, 0xa3, 0x5e, 0x0f,
	0x18, 0xc4, 0xda, 0xf3, 0xe8, 0xd5, 0xf0, 0xed, 0x74, 0x36, 0x0c, 0x28, 0xbb, 0x74, 0x2c, 0xfa,
	0x78, 0xc2, 0xfc, 0xd0, 0xd7, 0x2b, 0xc4, 0xea, 0xad, 0x13, 0x6b, 0x8f, 0x58, 0xa1, 0x73, 0xe9,
	0x84, 0xb3, 0xe1, 0xd8, 0xb7, 0xa9, 0x8b, 0x4c, 0xf3, 0x77, 0x15, 0xd8, 0x7c, 0x49, 0xc3, 0x23,
	0x7a, 0x75, 0x30, 0x9d, 0x1d, 0x33, 0xdf, 0x9e, 0x5a, 0xe1, 0x8f, 0x9c, 0x20, 0x1c, 0xd0, 0x9f,
	0x4d, 0x69, 0x10, 0xea, 0xcb, 0x50, 0xf5, 0x1c, 0xdb, 0xd0, 0x76, 0xb4, 0xdd, 0xfa, 0x80, 0xff,
	0xd4, 0x77, 0xa0, 0x33, 0xc1, 0x7d, 0x47, 0x64, 0x4c, 0x8d, 0xca, 0x8e, 0xb6, 0xdb, 0x1e, 0xa8,
	0x24, 0x7d, 0x15, 0xea, 0xc1, 0xc5, 0xb4, 0x6f, 0x1b, 0x55, 0xf1, 0x14, 0x2e, 0xf4, 0x2d, 0x68,
	0xcb, 0x4d, 0x7d, 0xdb, 0xa8, 0x09, 0x4e, 0x42, 0xe0, 0xdc, 0xc3, 0x73, 0xe2, 0x79, 0xd4, 0xed,
	0xdb, 0x46, 0x1d, 0xb9, 0x31, 0x41, 0x5f, 0x83, 0xc6, 0x49, 0x48, 0xc2, 0x69, 0x60, 0x34, 0x04,
	0x4b, 0xae, 0x74, 0x03, 0x9a, 0x3e, 0xb3, 0x29, 0x3b, 0x98, 0x19, 0x4d, 0xc1, 0x88, 0x96, 0xfa,
	0x2e, 0xc0, 0x84, 0x8c, 0x1c, 0x8f, 0x84, 0x8e, 0xef, 0x19, 0xad, 0x1d, 0x6d, 0xb7, 0xb3, 0xdf,
	0x7a, 0x4c, 0xac, 0xc7, 0xc7, 0x64, 0x44, 0x07, 0x0a, 0x4f, 0xbf, 0x0b, 0x0d, 0x9f, 0x8d, 0x86,
	0x8e, 0x6d, 0xb4, 0x51, 0x5c, 0x9f, 0x8d, 0xfa, 0xb6, 0xf9, 0x27, 0x0d, 0xb6, 0x8a, 0x0d, 0x13,
	0x4c, 0x7c, 0x2f, 0xa0, 0xba, 0x0e, 0x35, 0xcb, 0xb7, 0xa9, 0x34, 0x8d, 0xf8, 0xcd, 0xe5, 0x19,
	0xd3, 0x20, 0x20, 0xa3, 0xc8, 0x2e, 0xd1, 0x92, 0xdb, 0x84, 0x32, 0xe6, 0x33, 0x61, 0x93, 0xf6,
	0x00, 0x17, 0x9c, 0x1a, 0xfa, 0x21, 0x71, 0xa5, 0x3d, 0x70, 0xa1, 0x3f, 0x82, 0x9a, 0x4d, 0x42,
	0x62, 0xd4, 0x77, 0xaa, 0xbb, 0x9d, 0xfd, 0xbb, 0x5c, 0xea, 0x94, 0x18, 0x2f, 0x48, 0x48, 0x06,
	0x62, 0x8b, 0xf9, 0x59, 0x0d, 0xee, 0xe4, 0x78, 0xfa, 0x22, 0x54, 0x62, 0x9f, 0x55, 0x1c, 0x3b,
	0x72, 0x62, 0x25, 0x71, 0xe2, 0x16, 0xb4, 0xad, 0xd8, 0xdc, 0xe8, 0xa6, 0x84, 0x90, 0x38, 0xb0,
	0x56, 0xea, 0xc0, 0x7a, 0xd6, 0x81, 0x19, 0x58, 0x34, 0x0a, 0x61, 0x31, 0x61, 0x8e, 0x45, 0xa5,
	0xab, 0x70, 0xa1, 0xf7, 0xa0, 0xe5, 0xd1, 0xab, 0x63, 0xc1, 0x68, 0x09, 0x46, 0xbc, 0xd6, 0xb7,
	0x01, 0x82, 0x73, 0x9f, 0x85, 0xa7, 0x4e, 0xe8, 0x52, 0xe1, 0x9e, 0xf6, 0x40, 0xa1, 0x70, 0x89,
	0x5c, 0xdf, 0x1b, 0x21, 0x1b, 0x04, 0x3b, 0x21, 0x70, 0x07, 0x05, 0x3e, 0x0b, 0x8d, 0x0e, 0x3a,
	0x88, 0xff, 0xe6, 0x40, 0x0a, 0x10, 0x48, 0x0b, 0x08, 0x24, 0x5c, 0x71, 0xe9, 0x2d, 0xe2, 0x1d,
	0xd0, 0x8f, 0x6c, 0x27, 0xa4, 0xb6, 0xd1, 0x15, 0x4c, 0x95, 0xa4, 0x9b, 0xb0, 0x20, 0x96, 0x2f,
	0xa8, 0x4b, 0xf9, 0x96, 0x45, 0xb1, 0x25, 0x45, 0xe3, 0x76, 0x9e, 0x38, 0x96, 0xb1, 0x24, 0x24,
	0xe1, 0x3f, 0x85, 0x25, 0x43, 0xdf, 0xba, 0x30, 0x96, 0xa5, 0x25, 0xf9, 0x82, 0xcb, 0xed, 0x04,
	0x6f, 0x1c, 0x16, 0x4e, 0x89, 0x6b, 0xdc, 0x41, 0x4b, 0xc6, 0x04, 0xce, 0x1d, 0xf9, 0xbe, 0x1d,
	0x9c, 0xce, 0x26, 0xd4, 0xd0, 0x91, 0x1b, 0x13, 0xb8, 0x4d, 0x2c, 0x46, 0x49, 0x48, 0x4f, 0x9d,
	0x31, 0x35, 0x56, 0xd0, 0x26, 0x09, 0x85, 0xf3, 0xa7, 0x13, 0x3b, 0xe2, 0xaf, 0x22, 0x3f, 0xa1,
	0x98, 0x7f, 0xd4, 0xa0, 0x77, 0x28, 0xb6, 0xa7, 0x70, 0x13, 0xc5, 0x7b, 0xec, 0x7a, 0xad, 0xd4,
	0xf5, 0x95, 0xac, 0xeb, 0x9f, 0x42, 0x2b, 0x20, 0x97, 0x94, 0x43, 0x4f, 0x60, 0xa9, 0xb3, 0xbf,
	0xc1, 0x31, 0x7b, 0x42, 0x2e, 0x69, 0x1e, 0xb7, 0xf1, 0x56, 0x25, 0xf0, 0x6a, 0x6a, 0xe0, 0xfd,
	0x43, 0x83, 0xbb, 0x85, 0x8f, 0xde, 0x00, 0xd6, 0xd7, 0x83, 0x50, 0x85, 0x5b, 0x33, 0x03, 0xb7,
	0x08, 0x30, 0x2d, 0x05, 0x30, 0x5f, 0x09, 0x82, 0xe6, 0xdf, 0x34, 0xe8, 0xbd, 0x16, 0xb6, 0x2f,
	0x34, 0x76, 0x56, 0xa1, 0xd8, 0xf8, 0x95, 0x52, 0xe3, 0x57, 0xe7, 0x19, 0xbf, 0xf6, 0x2e, 0xc6,
	0xaf, 0xab, 0xc6, 0xff, 0xb3, 0x06, 0xbd, 0xf4, 0x63, 0x34, 0x24, 0x8e, 0x5b, 0x26, 0x70, 0xde,
	0x03, 0xef, 0x98, 0xfb, 0xad, 0x6c, 0xee, 0x4f, 0x92, 0x51, 0x22, 0x69, 0x43, 0x95, 0xf4, 0xb7,
	0x1a, 0x6c, 0x67, 0xf3, 0x73, 0x24, 0xec, 0x2d, 0x66, 0xe8, 0x3d, 0x99, 0x8b, 0xd1, 0xb4, 0x9b,
	0xf9, 0x5c, 0x2c, 0x5e, 0xa9, 0x64, 0xe4, 0xff, 0x56, 0x61, 0xbd, 0x64, 0xc7, 0x37, 0x79, 0xf9,
	0x4b, 0xe7, 0xe5, 0x74, 0xb6, 0xeb, 0x5e, 0x93, 0xed, 0x16, 0xb3, 0xd9, 0x8e, 0x4b, 0xf2, 0x96,
	0x8e, 0x1c, 0xef, 0x05, 0x09, 0xa9, 0xcc, 0xcb, 0x09, 0x81, 0x83, 0x81, 0x7a, 0xb6, 0xe0, 0x2d,
	0x23, 0x18, 0xe4, 0xf2, 0xab, 0x64, 0x68, 0x73, 0x17, 0xd6, 0x52, 0x00, 0xe8, 0xdb, 0x25, 0xe1,
	0x63, 0xfe, 0x4b, 0x83, 0xf7, 0x62, 0x0c, 0xbf, 0x3e, 0xa6, 0xa1, 0x7c, 0xe2, 0x84, 0xba, 0xf4,
	0xba, 0x36, 0xec, 0x5d, 0x72, 0x45, 0x06, 0x0b, 0xb5, 0x3c, 0x16, 0xd2, 0x6d, 0x53, 0xfd, 0x46,
	0x6d, 0x53, 0x2a, 0x2c, 0xff, 0xae, 0xc1, 0xc3, 0xeb, 0x54, 0xfa, 0xda, 0x1b, 0xa8, 0x27, 0xa9,
	0x06, 0xea, 0x7e, 0x12, 0xb4, 0x28, 0x85, 0x22, 0x96, 0x12, 0xb8, 0x5f, 0x54, 0x60, 0x73, 0xce,
	0xae, 0x77, 0xaa, 0x8c, 0x19, 0x83, 0x57, 0xf3, 0x06, 0x37, 0x61, 0x21, 0x74, 0xc6, 0xf4, 0xd0,
	0xf7, 0xce, 0x5c, 0xc7, 0x0a, 0xa5, 0x1e, 0x29, 0x5a, 0xd4, 0x56, 0xd4, 0x0b, 0xda, 0x8a, 0x86,
	0xda, 0x56, 0xec, 0x40, 0x67, 0x4c, 0xd8, 0x05, 0x17, 0x3b, 0x09, 0x67, 0x95, 0x94, 0x86, 0x75,
	0x6b, 0x2e, 0xac, 0xdb, 0xd9, 0xc6, 0x63, 0x0f, 0x3a, 0xd6, 0xb9, 0xe3, 0xda, 0x27, 0x5c, 0xef,
	0xc0, 0x00, 0x61, 0xdb, 0x2e, 0xb7, 0xed, 0x21, 0x27, 0xf7, 0xbd, 0x33, 0x7f, 0xa0, 0xee, 0x30,
	0x7f, 0xa9, 0xf1, 0x9e, 0x5e, 0xb2, 0x4a, 0xcc, 0x67, 0x40, 0x93, 0x4d, 0x5d, 0x7a, 0x34, 0x1d,
	0x4b, 0xe3, 0x45, 0xcb, 0xb4, 0xa8, 0xd5, 0xac, 0xa8, 0x0f, 0x61, 0x51, 0xe8, 0xfc, 0x29, 0x61,
	0xf4, 0xdc, 0x9f, 0x06, 0x91, 0xb6, 0x19, 0xaa, 0xf9, 0x0c, 0x6a, 0x1c, 0xb9, 0xc2, 0x4d, 0x64,
	0x44, 0xfb, 0x9e, 0x4d, 0x7f, 0x2e, 0x25, 0x48, 0x08, 0x3c, 0xd7, 0xf1, 0xc5, 0x89, 0xf3, 0x0b,
	0x2a, 0xc5, 0x88, 0xd7, 0xe6, 0x0f, 0x61, 0x09, 0x51, 0x51, 0x1a, 0xc6, 0x37, 0x0f, 0x1a, 0xf3,
	0x2a, 0x53, 0x1b, 0xfa, 0xe3, 0x89, 0xcf, 0x10, 0x5e, 0xe9, 0x4c, 0xa7, 0xe5, 0x32, 0xdd, 0x1a,
	0x34, 0x18, 0x0d, 0xa6, 0x6e, 0x28, 0x25, 0x94, 0x2b, 0xfd, 0x5b, 0xd0, 0xc5, 0x5f, 0x1f, 0x3b,
	0x2e, 0x7d, 0xcd, 0x5c, 0x09, 0xb2, 0x34, 0xd1, 0xfc, 0x4c, 0x03, 0x33, 0x5b, 0x2d, 0xf1, 0xe5,
	0xff, 0xa7, 0x90, 0xdc, 0x4b, 0x85, 0x64, 0xbe, 0x8e, 0x26, 0x96, 0x90, 0xe1, 0xb8, 0x05, 0x3d,
	0xa4, 0x15, 0x75, 0x4e, 0xe6, 0x3f, 0x35, 0xb8, 0x83, 0x5d, 0xec, 0x29, 0x09, 0x2e, 0xe6, 0x5e,
	0x56, 0x43, 0x12, 0x5c, 0x1c, 0xfa, 0x5e, 0x48, 0xbd, 0xc8, 0x74, 0x2a, 0x49, 0x7f, 0x1f, 0x96,
	0xfd, 0x09, 0x65, 0xc2, 0x3f, 0x69, 0x13, 0xe6, 0xe8, 0x68, 0x6b, 0xf1, 0xaa, 0xef, 0x53, 0x62,
	0x53, 0x26, 0x33, 0x68, 0x9a, 0xc8, 0xd1, 0x84, 0x7e, 0x93, 0xe5, 0xb8, 0x3d, 0x88, 0xd7, 0xdc,
	0x38, 0x9f, 0xf0, 0x3c, 0x19, 0x05, 0xee, 0x27, 0x51, 0xd7, 0xb5, 0xc4, 0xf5, 0xb8, 0xf6, 0xe2,
	0x7d, 0x8d, 0x2e, 0x6b, 0xd0, 0x98, 0x06, 0x94, 0x1d, 0xf9, 0x52, 0x03, 0xb9, 0xca, 0x00, 0xb4,
	0x36, 0x27, 0xab, 0x6f, 0x03, 0xf0, 0x03, 0xe5, 0x65, 0x1b, 0x9b, 0x09, 0x85, 0x62, 0x4e, 0x60,
	0x39, 0x11, 0x74, 0x0e, 0x68, 0x96, 0xa1, 0x3a, 0x0e, 0x46, 0x12, 0x30, 0xfc, 0x67, 0x02, 0x8b,
	0xaa, 0x0a, 0x8b, 0x9d, 0xb8, 0xbd, 0xe2, 0xb0, 0x58, 0xe0, 0x32, 0xf1, 0xf3, 0x15, 0x1c, 0xfc,
	0xbb, 0x02, 0xad, 0x88, 0x74, 0xb3, 0x1b, 0x80, 0x6a, 0xa4, 0x6a, 0xde, 0x48, 0x69, 0x15, 0x6b,
	0x59, 0x15, 0x0b, 0x01, 0x51, 0xbf, 0x29, 0x20, 0x1a, 0x45, 0x80, 0x78, 0x08, 0x8b, 0x18, 0x8d,
	0x34, 0x3a, 0xaf, 0x29, 0xb6, 0x65, 0xa8, 0x99, 0x14, 0xd0, 0x2a, 0x6a, 0x76, 0xc6, 0xbe, 0xed,
	0x9c, 0xcd, 0x04, 0x5f, 0xb6, 0x5d, 0x09, 0x25, 0x05, 0x3c, 0xc8, 0x00, 0x4f, 0x6a, 0x8d, 0xcd,
	0xa8, 0x68, 0xbd, 0xda, 0x03, 0x85, 0x62, 0x32, 0x58, 0x3d, 0xa2, 0x57, 0xc7, 0xd4, 0x9f, 0xb8,
	0x54, 0x85, 0xa1, 0x0e, 0xb5, 0x33, 0xe6, 0x8f, 0x65, 0x42, 0x12, 0xbf, 0x39, 0x6d, 0x12, 0xa5,
	0x83, 0xfa, 0x40, 0xfc, 0xd6, 0x37, 0x31, 0xc1, 0x0e, 0x03, 0x9e, 0x43, 0xab, 0xe9, 0x1c, 0x5a,
	0x76, 0xd3, 0xfb, 0x42, 0x83, 0xbb, 0x99, 0x97, 0xde, 0x62, 0x1e, 0x7a, 0x2f, 0x05, 0xad, 0x3b,
	0x32, 0xe3, 0xe0, 0xcb, 0x12, 0x7c, 0xe9, 0x1b, 0xd0, 0x3a, 0x27, 0xc1, 0x70, 0xec, 0x33, 0x2a,
	0xf1, 0xde, 0x3c, 0x27, 0xc1, 0x2b, 0x9f, 0xd1, 0x58, 0xcf, 0x46, 0x99, 0x9e, 0xcd, 0x4c, 0xad,
	0xf8, 0x6b, 0x05, 0xba, 0xa9, 0x77, 0xe8, 0x0f, 0x61, 0x49, 0x54, 0xd0, 0xa1, 0x33, 0xe6, 0x4f,
	0x4d, 0x99, 0x2b, 0x2d, 0xd9, 0x15, 0xe4, 0x3e, 0xa7, 0x72, 0xd7, 0xdf, 0x03, 0xc0, 0x7d, 0x5e,
	0x32, 0x53, 0xc3, 0xda, 0x2b, 0xdb, 0xb2, 0x65, 0x64, 0x8b, 0x26, 0x1b, 0x37, 0x49, 0x0c, 0x09,
	0xfa, 0x09, 0x27, 0x8b, 0x9d, 0xf7, 0xa1, 0x83, 0x3b, 0xb1, 0xa5, 0xe7, 0x16, 0xa9, 0x0c, 0xf0,
	0x6c, 0x6c, 0x01, 0x3e, 0x04, 0x5d, 0xbe, 0xc9, 0x67, 0x63, 0xe2, 0xca, 0x7d, 0x35, 0xb1, 0x0f,
	0x5f, 0x72, 0x24, 0x18, 0xb8, 0x7b, 0x03, 0x5a, 0x52, 0xfe, 0xe8, 0x6a, 0xd1, 0x44, 0xc1, 0x6d,
	0x2e, 0xb2, 0x13, 0x0c, 0x2f, 0x65, 0x85, 0x6e, 0x64, 0x2b, 0xf4, 0x0e, 0x2c, 0x38, 0x42, 0xde,
	0xab, 0xa1, 0x45, 0xe2, 0x4b, 0x35, 0x38, 0x5c, 0xd6, 0xab, 0x43, 0xc2, 0x42, 0xf3, 0x03, 0x51,
	0x0c, 0xd1, 0x58, 0x6f, 0xfc, 0xa9, 0x75, 0x4e, 0x99, 0x92, 0xfc, 0xa6, 0x32, 0xd0, 0xdb, 0x03,
	0xfe, 0xd3, 0xfc, 0x5c, 0x03, 0x23, 0xbf, 0xfb, 0x16, 0xe1, 0x62, 0x42, 0x97, 0xe3, 0x60, 0x44,
	0xc3, 0xa1, 0x5a, 0xbe, 0x3a, 0xe7, 0x24, 0x78, 0x49, 0xc3, 0x53, 0x91, 0xad, 0x3e, 0x4c, 0x15,
	0x31, 0x23, 0x05, 0x29, 0x29, 0x93, 0x92, 0xb9, 0x7e, 0xaf, 0x29, 0x31, 0xa5, 0xb0, 0xb9, 0x69,
	0x2e, 0x71, 0x39, 0x0c, 0x87, 0x71, 0x3e, 0x03, 0x49, 0x3b, 0xed, 0xdb, 0xdc, 0x8b, 0xae, 0x33,
	0x76, 0x42, 0xe9, 0x1d, 0x54, 0x00, 0x04, 0x09, 0xfd, 0xf2, 0x00, 0xba, 0xd1, 0x11, 0x89, 0xa3,
	0xdb, 0x83, 0xe8, 0x5c, 0xdc, 0xb4, 0x0e, 0x4d, 0xa9, 0x92, 0x54, 0xa6, 0x81, 0xca, 0x98, 0x03,
	0x71, 0xc9, 0x4f, 0x09, 0xf6, 0x92, 0x86, 0xa5, 0xc6, 0xe7, 0xb6, 0x51, 0x05, 0x0e, 0xa2, 0xa1,
	0x6f, 0x22, 0x71, 0x60, 0x3e, 0xcb, 0x9f, 0xf9, 0xdc, 0x8e, 0x5b, 0xa6, 0xdc, 0x09, 0x5a, 0xfe,
	0x84, 0x3f, 0x68, 0xa2, 0x01, 0xcf, 0x1f, 0x71, 0x8b, 0x5e, 0xfe, 0x36, 0x34, 0x29, 0x63, 0x42,
	0x02, 0xcc, 0x0b, 0xdb, 0x45, 0x4e, 0x7c, 0x6e, 0xdb, 0x1f, 0x31, 0x74, 0x65, 0x83, 0x32, 0xc6,
	0x85, 0x3b, 0x2e, 0x54, 0x4f, 0xee, 0xba, 0x81, 0x47, 0x73, 0x05, 0xd1, 0x7c, 0x10, 0x4d, 0x6e,
	0x45, 0xe7, 0x5c, 0x72, 0x43, 0xfc, 0x0f, 0x66, 0x94, 0x83, 0xe9, 0x6c, 0xce, 0xcc, 0x28, 0x14,
	0xf7, 0x6c, 0x3c, 0x1a, 0x17, 0x3c, 0x38, 0xe5, 0xd8, 0x80, 0x8b, 0x93, 0x1b, 0x24, 0xdc, 0x03,
	0x10, 0xb7, 0xe0, 0x21, 0xbf, 0x28, 0xcb, 0x2e, 0x46, 0xb9, 0x17, 0x6f, 0x40, 0x8b, 0x7a, 0x36,
	0x32, 0xeb, 0xe9, 0x8b, 0xf1, 0x2a, 0xd4, 0x2d, 0xff, 0x32, 0xae, 0x74, 0xb8, 0x10, 0x49, 0x94,
	0x12, 0x7b, 0xe8, 0x8c, 0x47, 0x32, 0x2f, 0x35, 0xf9, 0xba, 0x3f, 0x1e, 0x29, 0x37, 0xfb, 0x56,
	0xea, 0x66, 0xff, 0x00, 0xba, 0xf2, 0x1e, 0x34, 0xb4, 0xfc, 0xa9, 0x17, 0xca, 0x0b, 0xc7, 0x82,
	0x24, 0x1e, 0x72, 0x1a, 0xcf, 0x7b, 0x22, 0x89, 0x38, 0x93, 0x89, 0xe3, 0x8d, 0x86, 0x67, 0x8c,
	0xe2, 0x3c, 0xa1, 0x3e, 0x58, 0xe4, 0x89, 0x04, 0xc9, 0x1f, 0x33, 0x2a, 0xf2, 0x1e, 0xd6, 0xba,
	0x21, 0xbf, 0x3a, 0x45, 0x05, 0x4e, 0x29, 0x9e, 0xf7, 0xa1, 0x83, 0x73, 0x01, 0xdc, 0xb0, 0x90,
	0x1b, 0x8c, 0xfe, 0x4a, 0x03, 0x5d, 0x75, 0xc8, 0x2d, 0xa2, 0xee, 0x11, 0x34, 0x6c, 0x2c, 0xbc,
	0xd8, 0x7b, 0xdd, 0x49, 0xda, 0x5f, 0xe9, 0xd6, 0x81, 0xdc, 0x60, 0x3e, 0x85, 0x1e, 0xf6, 0xe9,
	0x08, 0xb5, 0xcc, 0xc0, 0x70, 0x1d, 0x9a, 0xc1, 0xc5, 0x54, 0x09, 0xa0, 0x46, 0x80, 0x77, 0xad,
	0xdf, 0x68, 0xb0, 0x81, 0x07, 0x4a, 0x70, 0xde, 0x7a, 0x39, 0x7d, 0x94, 0x2a, 0xa7, 0xca, 0x47,
	0x89, 0x7c, 0xe2, 0xdb, 0x8f, 0x90, 0xad, 0x26, 0xbd, 0x7b, 0x10, 0x85, 0x43, 0x12, 0x20, 0x6d,
	0x49, 0xe9, 0xdb, 0xa6, 0x21, 0x86, 0x26, 0xa8, 0xf4, 0x01, 0x87, 0x69, 0x54, 0x0b, 0xb8, 0x6a,
	0xeb, 0x39, 0xd6, 0x2d, 0x2a, 0xf6, 0x41, 0x6a, 0xc2, 0xb7, 0x9e, 0xca, 0x07, 0xf8, 0x3a, 0x45,
	0xb5, 0xbf, 0x68, 0xb0, 0x52, 0xc0, 0x4d, 0xa2, 0x50, 0xcb, 0x44, 0xa1, 0x12, 0x66, 0x95, 0x79,
	0x61, 0x56, 0x2d, 0x09, 0xb3, 0x9a, 0x1a, 0x66, 0xf9, 0x41, 0xc0, 0x06, 0xb4, 0x82, 0x59, 0x80,
	0x90, 0xc6, 0x88, 0x6c, 0x06, 0xb3, 0x40, 0xe0, 0xf9, 0xd7, 0x1a, 0xac, 0xaa, 0xb3, 0xe7, 0xaf,
	0xc9, 0x68, 0xd5, 0xc8, 0x68, 0xd1, 0xb8, 0x22, 0x3f, 0x59, 0x79, 0x05, 0x2b, 0x05, 0xcc, 0x92,
	0x89, 0xc0, 0xb5, 0x23, 0x93, 0xfd, 0xcf, 0x3b, 0x51, 0x4e, 0x3c, 0xc1, 0xef, 0x9c, 0xfa, 0x4f,
	0x60, 0xb5, 0xe8, 0x53, 0x9d, 0x2e, 0x26, 0x3f, 0x73, 0xbe, 0x6e, 0xf6, 0x76, 0xca, 0x37, 0x48,
	0x63, 0xbd, 0x84, 0x95, 0x82, 0xaf, 0x25, 0xba, 0x28, 0x1b, 0xe5, 0x9f, 0x51, 0x7a, 0xcb, 0x9c,
	0xff, 0x96, 0x04, 0x54, 0x3d, 0xa8, 0xe0, 0x4b, 0x00, 0x1e, 0x54, 0xfe, 0x89, 0xa0, 0xe0, 0xa0,
	0x9f, 0xc2, 0x5a, 0xf1, 0xdc, 0x5b, 0xdf, 0x2e, 0x99, 0x4e, 0x47, 0x67, 0x99, 0x45, 0xda, 0x66,
	0x66, 0xe6, 0x87, 0xb0, 0x82, 0x5f, 0xb3, 0xd2, 0x62, 0xf6, 0xf2, 0x17, 0x76, 0xbb, 0x5c, 0xc4,
	0x2b, 0x65, 0x34, 0x5f, 0x38, 0x03, 0xd4, 0x1f, 0xa5, 0x44, 0x99, 0x37, 0xfa, 0xec, 0xbd, 0x7f,
	0x93, 0xad, 0xf2, 0xc5, 0x4f, 0x01, 0x92, 0xa9, 0x80, 0x7e, 0x37, 0x71, 0x92, 0x32, 0x25, 0x28,
	0x90, 0xf7, 0x3b, 0xd0, 0xe1, 0x3d, 0x9e, 0xbc, 0xd8, 0xea, 0x2b, 0xd1, 0x35, 0x54, 0x15, 0x63,
	0x35, 0x4d, 0x94, 0x4f, 0xbe, 0xca, 0x3b, 0xe3, 0x60, 0xf6, 0x8a, 0xb8, 0xae, 0x9e, 0xee, 0x0e,
	0xd5, 0x93, 0x36, 0x0a, 0x38, 0xf2, 0xb8, 0x1f, 0x2b, 0x40, 0x56, 0x12, 0xb9, 0xbe, 0x59, 0xd4,
	0xa5, 0x44, 0xe7, 0x6d, 0x15, 0x33, 0x13, 0xdc, 0x15, 0xf4, 0x7a, 0x7a, 0x61, 0xdf, 0x93, 0x34,
	0x81, 0x05, 0x46, 0x7a, 0x93, 0x3f, 0xe8, 0xb9, 0x6d, 0xeb, 0x65, 0x0d, 0x54, 0x74, 0xd0, 0xfd,
	0x52, 0xbe, 0x3c, 0xb7, 0x0f, 0xeb, 0x6a, 0xc5, 0x53, 0xd5, 0x5e, 0x4a, 0x84, 0xc0, 0xc3, 0xee,
	0xe5, 0xca, 0x4e, 0xca, 0x7c, 0x9f, 0xc2, 0x4a, 0x41, 0xf1, 0x44, 0x11, 0xcb, 0xab, 0xea, 0x0d,
	0xb2, 0xc0, 0xf7, 0xa0, 0x1b, 0xf3, 0xc5, 0x34, 0x53, 0xa9, 0x7f, 0x4a, 0xfb, 0xd6, 0x5b, 0xcb,
	0x92, 0xe5, 0xf3, 0xcf, 0x60, 0x29, 0x7e, 0x5e, 0x06, 0xeb, 0x97, 0x3c, 0xe1, 0x09, 0x2c, 0xa8,
	0xe9, 0x46, 0xcf, 0xb7, 0x10, 0x05, 0x2e, 0xfb, 0x2e, 0x2c, 0xa8, 0xa9, 0xa5, 0xe8, 0x21, 0x23,
	0x9b, 0x7f, 0xe2, 0x87, 0x7f, 0x20, 0x06, 0x9f, 0x6a, 0xa5, 0x8b, 0xb3, 0x40, 0x41, 0x99, 0xee,
	0x6d, 0x16, 0xf2, 0xf0, 0xac, 0xb7, 0x0d, 0xf1, 0x67, 0x93, 0x27, 0xff, 0x0b, 0x00, 0x00, 0xff,
	0xff, 0xd4, 0xce, 0xbb, 0x54, 0xa5, 0x22, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// NewBuyServiceClient is the client API for NewBuyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type NewBuyServiceClient interface {
	//获取新人专享商品列表 boss
	GetNewBuyProductList(ctx context.Context, in *GetNewBuyProductListRequest, opts ...grpc.CallOption) (*GetNewBuyProductListResponse, error)
	//创建新人专享商品
	CreateNewBuyProduct(ctx context.Context, in *CreateNewBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//更新新人专享商品
	UpdateNewBuyProduct(ctx context.Context, in *UpdateNewBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取新人专享商品详情
	GetNewBuyProductDetail(ctx context.Context, in *NewBuyProductDetailRequest, opts ...grpc.CallOption) (*GetNewBuyProductDetailResponse, error)
	//删除新人专享商品
	DeleteNewBuyProduct(ctx context.Context, in *NewBuyProductIdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取可以参加新人专享活动的阿闻电商渠道的商品
	GetNewBuyUPetProductSelectList(ctx context.Context, in *GetNewBuyUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetNewBuyUPetProductSelectListResponse, error)
	//创建异步导出任务
	CreateTask(ctx context.Context, in *CreateTaskRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//查询异步任务列表
	GetTaskList(ctx context.Context, in *TaskListRequest, opts ...grpc.CallOption) (*TaskListResponse, error)
	// 新人专享列表
	GetNewBuyProductByMall(ctx context.Context, in *NewPeopleListRequest, opts ...grpc.CallOption) (*NewPeopleListResponse, error)
	// 新人专享券列表
	GetNewBuyVoucherList(ctx context.Context, in *NewPeopleVoucherRequest, opts ...grpc.CallOption) (*NewPeopleVoucherResponse, error)
	// 新人专享券领取
	NewPeopleVoucherGet(ctx context.Context, in *NewPeopleVoucherGetRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 新人专享券设置
	NewPeopleVoucherAdd(ctx context.Context, in *NewPeopleVoucherAddRequest, opts ...grpc.CallOption) (*NewPeopleVoucherAddResponse, error)
	// 获取新人专享券列表
	GetNewPeopleVoucherList(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*NewBuyVoucherListResponse, error)
	// 获取参与新人专享活动商品（进行中）
	GetNewPeopleProduct(ctx context.Context, in *GetNewPeopleProductRequest, opts ...grpc.CallOption) (*GetNewBuyProductListResponse, error)
	//新人专享活动信息
	GetNewBuyInfo(ctx context.Context, in *NewBuyInfoRequest, opts ...grpc.CallOption) (*NewBuyInfoResponse, error)
	//新人专享活动信息
	GetNewBuyDetail(ctx context.Context, in *NewBuyInfoRequest, opts ...grpc.CallOption) (*NewBuyInfoResponse, error)
	//创建新人专享活动
	CreateNewBuy(ctx context.Context, in *NewBuyRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//更新新人专享活动
	UpdateNewBuy(ctx context.Context, in *NewBuyRequest, opts ...grpc.CallOption) (*UpdateNewBuyResponse, error)
	// 新人专享banner
	NewPeopleBanner(ctx context.Context, in *NewPeopleBannerRequest, opts ...grpc.CallOption) (*NewPeopleBannerResponse, error)
}

type newBuyServiceClient struct {
	cc *grpc.ClientConn
}

func NewNewBuyServiceClient(cc *grpc.ClientConn) NewBuyServiceClient {
	return &newBuyServiceClient{cc}
}

func (c *newBuyServiceClient) GetNewBuyProductList(ctx context.Context, in *GetNewBuyProductListRequest, opts ...grpc.CallOption) (*GetNewBuyProductListResponse, error) {
	out := new(GetNewBuyProductListResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetNewBuyProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) CreateNewBuyProduct(ctx context.Context, in *CreateNewBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/CreateNewBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) UpdateNewBuyProduct(ctx context.Context, in *UpdateNewBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/UpdateNewBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) GetNewBuyProductDetail(ctx context.Context, in *NewBuyProductDetailRequest, opts ...grpc.CallOption) (*GetNewBuyProductDetailResponse, error) {
	out := new(GetNewBuyProductDetailResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetNewBuyProductDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) DeleteNewBuyProduct(ctx context.Context, in *NewBuyProductIdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/DeleteNewBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) GetNewBuyUPetProductSelectList(ctx context.Context, in *GetNewBuyUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetNewBuyUPetProductSelectListResponse, error) {
	out := new(GetNewBuyUPetProductSelectListResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetNewBuyUPetProductSelectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) CreateTask(ctx context.Context, in *CreateTaskRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/CreateTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) GetTaskList(ctx context.Context, in *TaskListRequest, opts ...grpc.CallOption) (*TaskListResponse, error) {
	out := new(TaskListResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) GetNewBuyProductByMall(ctx context.Context, in *NewPeopleListRequest, opts ...grpc.CallOption) (*NewPeopleListResponse, error) {
	out := new(NewPeopleListResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetNewBuyProductByMall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) GetNewBuyVoucherList(ctx context.Context, in *NewPeopleVoucherRequest, opts ...grpc.CallOption) (*NewPeopleVoucherResponse, error) {
	out := new(NewPeopleVoucherResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetNewBuyVoucherList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) NewPeopleVoucherGet(ctx context.Context, in *NewPeopleVoucherGetRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/NewPeopleVoucherGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) NewPeopleVoucherAdd(ctx context.Context, in *NewPeopleVoucherAddRequest, opts ...grpc.CallOption) (*NewPeopleVoucherAddResponse, error) {
	out := new(NewPeopleVoucherAddResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/NewPeopleVoucherAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) GetNewPeopleVoucherList(ctx context.Context, in *BaseRequest, opts ...grpc.CallOption) (*NewBuyVoucherListResponse, error) {
	out := new(NewBuyVoucherListResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetNewPeopleVoucherList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) GetNewPeopleProduct(ctx context.Context, in *GetNewPeopleProductRequest, opts ...grpc.CallOption) (*GetNewBuyProductListResponse, error) {
	out := new(GetNewBuyProductListResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetNewPeopleProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) GetNewBuyInfo(ctx context.Context, in *NewBuyInfoRequest, opts ...grpc.CallOption) (*NewBuyInfoResponse, error) {
	out := new(NewBuyInfoResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetNewBuyInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) GetNewBuyDetail(ctx context.Context, in *NewBuyInfoRequest, opts ...grpc.CallOption) (*NewBuyInfoResponse, error) {
	out := new(NewBuyInfoResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/GetNewBuyDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) CreateNewBuy(ctx context.Context, in *NewBuyRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/CreateNewBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) UpdateNewBuy(ctx context.Context, in *NewBuyRequest, opts ...grpc.CallOption) (*UpdateNewBuyResponse, error) {
	out := new(UpdateNewBuyResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/UpdateNewBuy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newBuyServiceClient) NewPeopleBanner(ctx context.Context, in *NewPeopleBannerRequest, opts ...grpc.CallOption) (*NewPeopleBannerResponse, error) {
	out := new(NewPeopleBannerResponse)
	err := c.cc.Invoke(ctx, "/ac.NewBuyService/NewPeopleBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NewBuyServiceServer is the server API for NewBuyService service.
type NewBuyServiceServer interface {
	//获取新人专享商品列表 boss
	GetNewBuyProductList(context.Context, *GetNewBuyProductListRequest) (*GetNewBuyProductListResponse, error)
	//创建新人专享商品
	CreateNewBuyProduct(context.Context, *CreateNewBuyProductRequest) (*BaseResponse, error)
	//更新新人专享商品
	UpdateNewBuyProduct(context.Context, *UpdateNewBuyProductRequest) (*BaseResponse, error)
	//获取新人专享商品详情
	GetNewBuyProductDetail(context.Context, *NewBuyProductDetailRequest) (*GetNewBuyProductDetailResponse, error)
	//删除新人专享商品
	DeleteNewBuyProduct(context.Context, *NewBuyProductIdRequest) (*BaseResponse, error)
	//获取可以参加新人专享活动的阿闻电商渠道的商品
	GetNewBuyUPetProductSelectList(context.Context, *GetNewBuyUPetProductSelectListRequest) (*GetNewBuyUPetProductSelectListResponse, error)
	//创建异步导出任务
	CreateTask(context.Context, *CreateTaskRequest) (*BaseResponse, error)
	//查询异步任务列表
	GetTaskList(context.Context, *TaskListRequest) (*TaskListResponse, error)
	// 新人专享列表
	GetNewBuyProductByMall(context.Context, *NewPeopleListRequest) (*NewPeopleListResponse, error)
	// 新人专享券列表
	GetNewBuyVoucherList(context.Context, *NewPeopleVoucherRequest) (*NewPeopleVoucherResponse, error)
	// 新人专享券领取
	NewPeopleVoucherGet(context.Context, *NewPeopleVoucherGetRequest) (*BaseResponse, error)
	// 新人专享券设置
	NewPeopleVoucherAdd(context.Context, *NewPeopleVoucherAddRequest) (*NewPeopleVoucherAddResponse, error)
	// 获取新人专享券列表
	GetNewPeopleVoucherList(context.Context, *BaseRequest) (*NewBuyVoucherListResponse, error)
	// 获取参与新人专享活动商品（进行中）
	GetNewPeopleProduct(context.Context, *GetNewPeopleProductRequest) (*GetNewBuyProductListResponse, error)
	//新人专享活动信息
	GetNewBuyInfo(context.Context, *NewBuyInfoRequest) (*NewBuyInfoResponse, error)
	//新人专享活动信息
	GetNewBuyDetail(context.Context, *NewBuyInfoRequest) (*NewBuyInfoResponse, error)
	//创建新人专享活动
	CreateNewBuy(context.Context, *NewBuyRequest) (*BaseResponse, error)
	//更新新人专享活动
	UpdateNewBuy(context.Context, *NewBuyRequest) (*UpdateNewBuyResponse, error)
	// 新人专享banner
	NewPeopleBanner(context.Context, *NewPeopleBannerRequest) (*NewPeopleBannerResponse, error)
}

// UnimplementedNewBuyServiceServer can be embedded to have forward compatible implementations.
type UnimplementedNewBuyServiceServer struct {
}

func (*UnimplementedNewBuyServiceServer) GetNewBuyProductList(ctx context.Context, req *GetNewBuyProductListRequest) (*GetNewBuyProductListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewBuyProductList not implemented")
}
func (*UnimplementedNewBuyServiceServer) CreateNewBuyProduct(ctx context.Context, req *CreateNewBuyProductRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNewBuyProduct not implemented")
}
func (*UnimplementedNewBuyServiceServer) UpdateNewBuyProduct(ctx context.Context, req *UpdateNewBuyProductRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNewBuyProduct not implemented")
}
func (*UnimplementedNewBuyServiceServer) GetNewBuyProductDetail(ctx context.Context, req *NewBuyProductDetailRequest) (*GetNewBuyProductDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewBuyProductDetail not implemented")
}
func (*UnimplementedNewBuyServiceServer) DeleteNewBuyProduct(ctx context.Context, req *NewBuyProductIdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNewBuyProduct not implemented")
}
func (*UnimplementedNewBuyServiceServer) GetNewBuyUPetProductSelectList(ctx context.Context, req *GetNewBuyUPetProductSelectListRequest) (*GetNewBuyUPetProductSelectListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewBuyUPetProductSelectList not implemented")
}
func (*UnimplementedNewBuyServiceServer) CreateTask(ctx context.Context, req *CreateTaskRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTask not implemented")
}
func (*UnimplementedNewBuyServiceServer) GetTaskList(ctx context.Context, req *TaskListRequest) (*TaskListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskList not implemented")
}
func (*UnimplementedNewBuyServiceServer) GetNewBuyProductByMall(ctx context.Context, req *NewPeopleListRequest) (*NewPeopleListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewBuyProductByMall not implemented")
}
func (*UnimplementedNewBuyServiceServer) GetNewBuyVoucherList(ctx context.Context, req *NewPeopleVoucherRequest) (*NewPeopleVoucherResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewBuyVoucherList not implemented")
}
func (*UnimplementedNewBuyServiceServer) NewPeopleVoucherGet(ctx context.Context, req *NewPeopleVoucherGetRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewPeopleVoucherGet not implemented")
}
func (*UnimplementedNewBuyServiceServer) NewPeopleVoucherAdd(ctx context.Context, req *NewPeopleVoucherAddRequest) (*NewPeopleVoucherAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewPeopleVoucherAdd not implemented")
}
func (*UnimplementedNewBuyServiceServer) GetNewPeopleVoucherList(ctx context.Context, req *BaseRequest) (*NewBuyVoucherListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewPeopleVoucherList not implemented")
}
func (*UnimplementedNewBuyServiceServer) GetNewPeopleProduct(ctx context.Context, req *GetNewPeopleProductRequest) (*GetNewBuyProductListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewPeopleProduct not implemented")
}
func (*UnimplementedNewBuyServiceServer) GetNewBuyInfo(ctx context.Context, req *NewBuyInfoRequest) (*NewBuyInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewBuyInfo not implemented")
}
func (*UnimplementedNewBuyServiceServer) GetNewBuyDetail(ctx context.Context, req *NewBuyInfoRequest) (*NewBuyInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewBuyDetail not implemented")
}
func (*UnimplementedNewBuyServiceServer) CreateNewBuy(ctx context.Context, req *NewBuyRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNewBuy not implemented")
}
func (*UnimplementedNewBuyServiceServer) UpdateNewBuy(ctx context.Context, req *NewBuyRequest) (*UpdateNewBuyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNewBuy not implemented")
}
func (*UnimplementedNewBuyServiceServer) NewPeopleBanner(ctx context.Context, req *NewPeopleBannerRequest) (*NewPeopleBannerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewPeopleBanner not implemented")
}

func RegisterNewBuyServiceServer(s *grpc.Server, srv NewBuyServiceServer) {
	s.RegisterService(&_NewBuyService_serviceDesc, srv)
}

func _NewBuyService_GetNewBuyProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewBuyProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetNewBuyProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetNewBuyProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetNewBuyProductList(ctx, req.(*GetNewBuyProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_CreateNewBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNewBuyProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).CreateNewBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/CreateNewBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).CreateNewBuyProduct(ctx, req.(*CreateNewBuyProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_UpdateNewBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNewBuyProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).UpdateNewBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/UpdateNewBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).UpdateNewBuyProduct(ctx, req.(*UpdateNewBuyProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_GetNewBuyProductDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewBuyProductDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetNewBuyProductDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetNewBuyProductDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetNewBuyProductDetail(ctx, req.(*NewBuyProductDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_DeleteNewBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewBuyProductIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).DeleteNewBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/DeleteNewBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).DeleteNewBuyProduct(ctx, req.(*NewBuyProductIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_GetNewBuyUPetProductSelectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewBuyUPetProductSelectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetNewBuyUPetProductSelectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetNewBuyUPetProductSelectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetNewBuyUPetProductSelectList(ctx, req.(*GetNewBuyUPetProductSelectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_CreateTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).CreateTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/CreateTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).CreateTask(ctx, req.(*CreateTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_GetTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetTaskList(ctx, req.(*TaskListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_GetNewBuyProductByMall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewPeopleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetNewBuyProductByMall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetNewBuyProductByMall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetNewBuyProductByMall(ctx, req.(*NewPeopleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_GetNewBuyVoucherList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewPeopleVoucherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetNewBuyVoucherList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetNewBuyVoucherList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetNewBuyVoucherList(ctx, req.(*NewPeopleVoucherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_NewPeopleVoucherGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewPeopleVoucherGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).NewPeopleVoucherGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/NewPeopleVoucherGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).NewPeopleVoucherGet(ctx, req.(*NewPeopleVoucherGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_NewPeopleVoucherAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewPeopleVoucherAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).NewPeopleVoucherAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/NewPeopleVoucherAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).NewPeopleVoucherAdd(ctx, req.(*NewPeopleVoucherAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_GetNewPeopleVoucherList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetNewPeopleVoucherList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetNewPeopleVoucherList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetNewPeopleVoucherList(ctx, req.(*BaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_GetNewPeopleProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewPeopleProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetNewPeopleProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetNewPeopleProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetNewPeopleProduct(ctx, req.(*GetNewPeopleProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_GetNewBuyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewBuyInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetNewBuyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetNewBuyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetNewBuyInfo(ctx, req.(*NewBuyInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_GetNewBuyDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewBuyInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).GetNewBuyDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/GetNewBuyDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).GetNewBuyDetail(ctx, req.(*NewBuyInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_CreateNewBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).CreateNewBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/CreateNewBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).CreateNewBuy(ctx, req.(*NewBuyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_UpdateNewBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).UpdateNewBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/UpdateNewBuy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).UpdateNewBuy(ctx, req.(*NewBuyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewBuyService_NewPeopleBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewPeopleBannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewBuyServiceServer).NewPeopleBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.NewBuyService/NewPeopleBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewBuyServiceServer).NewPeopleBanner(ctx, req.(*NewPeopleBannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _NewBuyService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.NewBuyService",
	HandlerType: (*NewBuyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNewBuyProductList",
			Handler:    _NewBuyService_GetNewBuyProductList_Handler,
		},
		{
			MethodName: "CreateNewBuyProduct",
			Handler:    _NewBuyService_CreateNewBuyProduct_Handler,
		},
		{
			MethodName: "UpdateNewBuyProduct",
			Handler:    _NewBuyService_UpdateNewBuyProduct_Handler,
		},
		{
			MethodName: "GetNewBuyProductDetail",
			Handler:    _NewBuyService_GetNewBuyProductDetail_Handler,
		},
		{
			MethodName: "DeleteNewBuyProduct",
			Handler:    _NewBuyService_DeleteNewBuyProduct_Handler,
		},
		{
			MethodName: "GetNewBuyUPetProductSelectList",
			Handler:    _NewBuyService_GetNewBuyUPetProductSelectList_Handler,
		},
		{
			MethodName: "CreateTask",
			Handler:    _NewBuyService_CreateTask_Handler,
		},
		{
			MethodName: "GetTaskList",
			Handler:    _NewBuyService_GetTaskList_Handler,
		},
		{
			MethodName: "GetNewBuyProductByMall",
			Handler:    _NewBuyService_GetNewBuyProductByMall_Handler,
		},
		{
			MethodName: "GetNewBuyVoucherList",
			Handler:    _NewBuyService_GetNewBuyVoucherList_Handler,
		},
		{
			MethodName: "NewPeopleVoucherGet",
			Handler:    _NewBuyService_NewPeopleVoucherGet_Handler,
		},
		{
			MethodName: "NewPeopleVoucherAdd",
			Handler:    _NewBuyService_NewPeopleVoucherAdd_Handler,
		},
		{
			MethodName: "GetNewPeopleVoucherList",
			Handler:    _NewBuyService_GetNewPeopleVoucherList_Handler,
		},
		{
			MethodName: "GetNewPeopleProduct",
			Handler:    _NewBuyService_GetNewPeopleProduct_Handler,
		},
		{
			MethodName: "GetNewBuyInfo",
			Handler:    _NewBuyService_GetNewBuyInfo_Handler,
		},
		{
			MethodName: "GetNewBuyDetail",
			Handler:    _NewBuyService_GetNewBuyDetail_Handler,
		},
		{
			MethodName: "CreateNewBuy",
			Handler:    _NewBuyService_CreateNewBuy_Handler,
		},
		{
			MethodName: "UpdateNewBuy",
			Handler:    _NewBuyService_UpdateNewBuy_Handler,
		},
		{
			MethodName: "NewPeopleBanner",
			Handler:    _NewBuyService_NewPeopleBanner_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/new_buy_service.proto",
}
