// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/poster_fission.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PosterFissionListReq struct {
	// 每页数量
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 当前页码
	PageIndex            int32    `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PosterFissionListReq) Reset()         { *m = PosterFissionListReq{} }
func (m *PosterFissionListReq) String() string { return proto.CompactTextString(m) }
func (*PosterFissionListReq) ProtoMessage()    {}
func (*PosterFissionListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{0}
}

func (m *PosterFissionListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionListReq.Unmarshal(m, b)
}
func (m *PosterFissionListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionListReq.Marshal(b, m, deterministic)
}
func (m *PosterFissionListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionListReq.Merge(m, src)
}
func (m *PosterFissionListReq) XXX_Size() int {
	return xxx_messageInfo_PosterFissionListReq.Size(m)
}
func (m *PosterFissionListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionListReq proto.InternalMessageInfo

func (m *PosterFissionListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *PosterFissionListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type PosterFissionListRes struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*PosterFissionListRes_PosterFissionList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PosterFissionListRes) Reset()         { *m = PosterFissionListRes{} }
func (m *PosterFissionListRes) String() string { return proto.CompactTextString(m) }
func (*PosterFissionListRes) ProtoMessage()    {}
func (*PosterFissionListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{1}
}

func (m *PosterFissionListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionListRes.Unmarshal(m, b)
}
func (m *PosterFissionListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionListRes.Marshal(b, m, deterministic)
}
func (m *PosterFissionListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionListRes.Merge(m, src)
}
func (m *PosterFissionListRes) XXX_Size() int {
	return xxx_messageInfo_PosterFissionListRes.Size(m)
}
func (m *PosterFissionListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionListRes.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionListRes proto.InternalMessageInfo

func (m *PosterFissionListRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PosterFissionListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PosterFissionListRes) GetData() []*PosterFissionListRes_PosterFissionList {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PosterFissionListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type PosterFissionListRes_PosterFissionList struct {
	// 活动id
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动主题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 开始时间
	BeginTime string `protobuf:"bytes,13,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	// 结束时间
	EndTime              string   `protobuf:"bytes,14,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PosterFissionListRes_PosterFissionList) Reset() {
	*m = PosterFissionListRes_PosterFissionList{}
}
func (m *PosterFissionListRes_PosterFissionList) String() string { return proto.CompactTextString(m) }
func (*PosterFissionListRes_PosterFissionList) ProtoMessage()    {}
func (*PosterFissionListRes_PosterFissionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{1, 0}
}

func (m *PosterFissionListRes_PosterFissionList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionListRes_PosterFissionList.Unmarshal(m, b)
}
func (m *PosterFissionListRes_PosterFissionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionListRes_PosterFissionList.Marshal(b, m, deterministic)
}
func (m *PosterFissionListRes_PosterFissionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionListRes_PosterFissionList.Merge(m, src)
}
func (m *PosterFissionListRes_PosterFissionList) XXX_Size() int {
	return xxx_messageInfo_PosterFissionListRes_PosterFissionList.Size(m)
}
func (m *PosterFissionListRes_PosterFissionList) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionListRes_PosterFissionList.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionListRes_PosterFissionList proto.InternalMessageInfo

func (m *PosterFissionListRes_PosterFissionList) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PosterFissionListRes_PosterFissionList) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PosterFissionListRes_PosterFissionList) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *PosterFissionListRes_PosterFissionList) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type PosterFissionDetailReq struct {
	// 活动id
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PosterFissionDetailReq) Reset()         { *m = PosterFissionDetailReq{} }
func (m *PosterFissionDetailReq) String() string { return proto.CompactTextString(m) }
func (*PosterFissionDetailReq) ProtoMessage()    {}
func (*PosterFissionDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{2}
}

func (m *PosterFissionDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionDetailReq.Unmarshal(m, b)
}
func (m *PosterFissionDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionDetailReq.Marshal(b, m, deterministic)
}
func (m *PosterFissionDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionDetailReq.Merge(m, src)
}
func (m *PosterFissionDetailReq) XXX_Size() int {
	return xxx_messageInfo_PosterFissionDetailReq.Size(m)
}
func (m *PosterFissionDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionDetailReq proto.InternalMessageInfo

func (m *PosterFissionDetailReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type PosterFissionDetailRes struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 活动详情数据
	Data                 *PosterFission `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PosterFissionDetailRes) Reset()         { *m = PosterFissionDetailRes{} }
func (m *PosterFissionDetailRes) String() string { return proto.CompactTextString(m) }
func (*PosterFissionDetailRes) ProtoMessage()    {}
func (*PosterFissionDetailRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{3}
}

func (m *PosterFissionDetailRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionDetailRes.Unmarshal(m, b)
}
func (m *PosterFissionDetailRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionDetailRes.Marshal(b, m, deterministic)
}
func (m *PosterFissionDetailRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionDetailRes.Merge(m, src)
}
func (m *PosterFissionDetailRes) XXX_Size() int {
	return xxx_messageInfo_PosterFissionDetailRes.Size(m)
}
func (m *PosterFissionDetailRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionDetailRes.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionDetailRes proto.InternalMessageInfo

func (m *PosterFissionDetailRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PosterFissionDetailRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PosterFissionDetailRes) GetData() *PosterFission {
	if m != nil {
		return m.Data
	}
	return nil
}

type PosterFissionStateReq struct {
	// 前端不需要传，从token解析，允许不登陆请求
	ScrmId string `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 活动id
	PosterFissionId      int32    `protobuf:"varint,2,opt,name=poster_fission_id,json=posterFissionId,proto3" json:"poster_fission_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PosterFissionStateReq) Reset()         { *m = PosterFissionStateReq{} }
func (m *PosterFissionStateReq) String() string { return proto.CompactTextString(m) }
func (*PosterFissionStateReq) ProtoMessage()    {}
func (*PosterFissionStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{4}
}

func (m *PosterFissionStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionStateReq.Unmarshal(m, b)
}
func (m *PosterFissionStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionStateReq.Marshal(b, m, deterministic)
}
func (m *PosterFissionStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionStateReq.Merge(m, src)
}
func (m *PosterFissionStateReq) XXX_Size() int {
	return xxx_messageInfo_PosterFissionStateReq.Size(m)
}
func (m *PosterFissionStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionStateReq proto.InternalMessageInfo

func (m *PosterFissionStateReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *PosterFissionStateReq) GetPosterFissionId() int32 {
	if m != nil {
		return m.PosterFissionId
	}
	return 0
}

type PosterFissionStateRes struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string                      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *PosterFissionStateRes_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *PosterFissionStateRes) Reset()         { *m = PosterFissionStateRes{} }
func (m *PosterFissionStateRes) String() string { return proto.CompactTextString(m) }
func (*PosterFissionStateRes) ProtoMessage()    {}
func (*PosterFissionStateRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{5}
}

func (m *PosterFissionStateRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionStateRes.Unmarshal(m, b)
}
func (m *PosterFissionStateRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionStateRes.Marshal(b, m, deterministic)
}
func (m *PosterFissionStateRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionStateRes.Merge(m, src)
}
func (m *PosterFissionStateRes) XXX_Size() int {
	return xxx_messageInfo_PosterFissionStateRes.Size(m)
}
func (m *PosterFissionStateRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionStateRes.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionStateRes proto.InternalMessageInfo

func (m *PosterFissionStateRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PosterFissionStateRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PosterFissionStateRes) GetData() *PosterFissionStateRes_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type PosterFissionStateRes_Data struct {
	// 活动信息
	PosterFission *PosterFission `protobuf:"bytes,1,opt,name=poster_fission,json=posterFission,proto3" json:"poster_fission"`
	// 登录状态 0未登录，1登录了
	LoginState int32 `protobuf:"varint,2,opt,name=login_state,json=loginState,proto3" json:"login_state"`
	// 活动状态 10未开始 20进行中 30已结束
	State int32 `protobuf:"varint,3,opt,name=state,proto3" json:"state"`
	// 我的排名，rank大于0表示参加过，否则表示没参加过
	Rank int32 `protobuf:"varint,4,opt,name=rank,proto3" json:"rank"`
	// 总参与人数
	Total                int32    `protobuf:"varint,5,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PosterFissionStateRes_Data) Reset()         { *m = PosterFissionStateRes_Data{} }
func (m *PosterFissionStateRes_Data) String() string { return proto.CompactTextString(m) }
func (*PosterFissionStateRes_Data) ProtoMessage()    {}
func (*PosterFissionStateRes_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{5, 0}
}

func (m *PosterFissionStateRes_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionStateRes_Data.Unmarshal(m, b)
}
func (m *PosterFissionStateRes_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionStateRes_Data.Marshal(b, m, deterministic)
}
func (m *PosterFissionStateRes_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionStateRes_Data.Merge(m, src)
}
func (m *PosterFissionStateRes_Data) XXX_Size() int {
	return xxx_messageInfo_PosterFissionStateRes_Data.Size(m)
}
func (m *PosterFissionStateRes_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionStateRes_Data.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionStateRes_Data proto.InternalMessageInfo

func (m *PosterFissionStateRes_Data) GetPosterFission() *PosterFission {
	if m != nil {
		return m.PosterFission
	}
	return nil
}

func (m *PosterFissionStateRes_Data) GetLoginState() int32 {
	if m != nil {
		return m.LoginState
	}
	return 0
}

func (m *PosterFissionStateRes_Data) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *PosterFissionStateRes_Data) GetRank() int32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *PosterFissionStateRes_Data) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 参加活动
type PosterFissionJoinReq struct {
	// 前端不需要传，从token解析
	ScrmId string `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 活动id
	PosterFissionId      int32    `protobuf:"varint,2,opt,name=poster_fission_id,json=posterFissionId,proto3" json:"poster_fission_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PosterFissionJoinReq) Reset()         { *m = PosterFissionJoinReq{} }
func (m *PosterFissionJoinReq) String() string { return proto.CompactTextString(m) }
func (*PosterFissionJoinReq) ProtoMessage()    {}
func (*PosterFissionJoinReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{6}
}

func (m *PosterFissionJoinReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionJoinReq.Unmarshal(m, b)
}
func (m *PosterFissionJoinReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionJoinReq.Marshal(b, m, deterministic)
}
func (m *PosterFissionJoinReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionJoinReq.Merge(m, src)
}
func (m *PosterFissionJoinReq) XXX_Size() int {
	return xxx_messageInfo_PosterFissionJoinReq.Size(m)
}
func (m *PosterFissionJoinReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionJoinReq.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionJoinReq proto.InternalMessageInfo

func (m *PosterFissionJoinReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *PosterFissionJoinReq) GetPosterFissionId() int32 {
	if m != nil {
		return m.PosterFissionId
	}
	return 0
}

// 参加活动
type PosterFissionJoinRes struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string                     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *PosterFissionJoinRes_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PosterFissionJoinRes) Reset()         { *m = PosterFissionJoinRes{} }
func (m *PosterFissionJoinRes) String() string { return proto.CompactTextString(m) }
func (*PosterFissionJoinRes) ProtoMessage()    {}
func (*PosterFissionJoinRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{7}
}

func (m *PosterFissionJoinRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionJoinRes.Unmarshal(m, b)
}
func (m *PosterFissionJoinRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionJoinRes.Marshal(b, m, deterministic)
}
func (m *PosterFissionJoinRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionJoinRes.Merge(m, src)
}
func (m *PosterFissionJoinRes) XXX_Size() int {
	return xxx_messageInfo_PosterFissionJoinRes.Size(m)
}
func (m *PosterFissionJoinRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionJoinRes.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionJoinRes proto.InternalMessageInfo

func (m *PosterFissionJoinRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PosterFissionJoinRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PosterFissionJoinRes) GetData() *PosterFissionJoinRes_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type PosterFissionJoinRes_Data struct {
	// 我的排名，在参与的那一刻，总人数=我的排名
	Rank                 int32    `protobuf:"varint,1,opt,name=rank,proto3" json:"rank"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PosterFissionJoinRes_Data) Reset()         { *m = PosterFissionJoinRes_Data{} }
func (m *PosterFissionJoinRes_Data) String() string { return proto.CompactTextString(m) }
func (*PosterFissionJoinRes_Data) ProtoMessage()    {}
func (*PosterFissionJoinRes_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{7, 0}
}

func (m *PosterFissionJoinRes_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFissionJoinRes_Data.Unmarshal(m, b)
}
func (m *PosterFissionJoinRes_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFissionJoinRes_Data.Marshal(b, m, deterministic)
}
func (m *PosterFissionJoinRes_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFissionJoinRes_Data.Merge(m, src)
}
func (m *PosterFissionJoinRes_Data) XXX_Size() int {
	return xxx_messageInfo_PosterFissionJoinRes_Data.Size(m)
}
func (m *PosterFissionJoinRes_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFissionJoinRes_Data.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFissionJoinRes_Data proto.InternalMessageInfo

func (m *PosterFissionJoinRes_Data) GetRank() int32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type BackgroundImage struct {
	// 主页面底图
	Main string `protobuf:"bytes,1,opt,name=main,proto3" json:"main"`
	// 分享底图
	Share                string   `protobuf:"bytes,2,opt,name=share,proto3" json:"share"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BackgroundImage) Reset()         { *m = BackgroundImage{} }
func (m *BackgroundImage) String() string { return proto.CompactTextString(m) }
func (*BackgroundImage) ProtoMessage()    {}
func (*BackgroundImage) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{8}
}

func (m *BackgroundImage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BackgroundImage.Unmarshal(m, b)
}
func (m *BackgroundImage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BackgroundImage.Marshal(b, m, deterministic)
}
func (m *BackgroundImage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackgroundImage.Merge(m, src)
}
func (m *BackgroundImage) XXX_Size() int {
	return xxx_messageInfo_BackgroundImage.Size(m)
}
func (m *BackgroundImage) XXX_DiscardUnknown() {
	xxx_messageInfo_BackgroundImage.DiscardUnknown(m)
}

var xxx_messageInfo_BackgroundImage proto.InternalMessageInfo

func (m *BackgroundImage) GetMain() string {
	if m != nil {
		return m.Main
	}
	return ""
}

func (m *BackgroundImage) GetShare() string {
	if m != nil {
		return m.Share
	}
	return ""
}

type PosterFission struct {
	// 活动id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 活动主题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 多张底图
	BackgroundImages []*BackgroundImage `protobuf:"bytes,3,rep,name=background_images,json=backgroundImages,proto3" json:"background_images"`
	// 分享卡片标题
	ShareTitle string `protobuf:"bytes,5,opt,name=share_title,json=shareTitle,proto3" json:"share_title"`
	// 分享卡片底图
	ShareImage string `protobuf:"bytes,6,opt,name=share_image,json=shareImage,proto3" json:"share_image"`
	// 分享卡片数值坐标
	SharePositionX int32 `protobuf:"varint,7,opt,name=share_position_x,json=sharePositionX,proto3" json:"share_position_x"`
	// 分享卡片数值坐标
	SharePositionY int32 `protobuf:"varint,8,opt,name=share_position_y,json=sharePositionY,proto3" json:"share_position_y"`
	// 分享二维码图片
	QrCodeImage string `protobuf:"bytes,9,opt,name=qr_code_image,json=qrCodeImage,proto3" json:"qr_code_image"`
	// 假参与人数起始值
	StartRank int32 `protobuf:"varint,10,opt,name=start_rank,json=startRank,proto3" json:"start_rank"`
	// 活动按钮文案
	ButtonTitle string `protobuf:"bytes,11,opt,name=button_title,json=buttonTitle,proto3" json:"button_title"`
	// 微页面id
	WepageId int32 `protobuf:"varint,12,opt,name=wepage_id,json=wepageId,proto3" json:"wepage_id"`
	// 开始时间 2023-03-21 12:00，注意没有秒
	BeginTime string `protobuf:"bytes,13,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	// 结束时间 2023-03-21 12:00，注意没有秒
	EndTime              string   `protobuf:"bytes,14,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PosterFission) Reset()         { *m = PosterFission{} }
func (m *PosterFission) String() string { return proto.CompactTextString(m) }
func (*PosterFission) ProtoMessage()    {}
func (*PosterFission) Descriptor() ([]byte, []int) {
	return fileDescriptor_51eb04d66e7a4894, []int{9}
}

func (m *PosterFission) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PosterFission.Unmarshal(m, b)
}
func (m *PosterFission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PosterFission.Marshal(b, m, deterministic)
}
func (m *PosterFission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PosterFission.Merge(m, src)
}
func (m *PosterFission) XXX_Size() int {
	return xxx_messageInfo_PosterFission.Size(m)
}
func (m *PosterFission) XXX_DiscardUnknown() {
	xxx_messageInfo_PosterFission.DiscardUnknown(m)
}

var xxx_messageInfo_PosterFission proto.InternalMessageInfo

func (m *PosterFission) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PosterFission) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PosterFission) GetBackgroundImages() []*BackgroundImage {
	if m != nil {
		return m.BackgroundImages
	}
	return nil
}

func (m *PosterFission) GetShareTitle() string {
	if m != nil {
		return m.ShareTitle
	}
	return ""
}

func (m *PosterFission) GetShareImage() string {
	if m != nil {
		return m.ShareImage
	}
	return ""
}

func (m *PosterFission) GetSharePositionX() int32 {
	if m != nil {
		return m.SharePositionX
	}
	return 0
}

func (m *PosterFission) GetSharePositionY() int32 {
	if m != nil {
		return m.SharePositionY
	}
	return 0
}

func (m *PosterFission) GetQrCodeImage() string {
	if m != nil {
		return m.QrCodeImage
	}
	return ""
}

func (m *PosterFission) GetStartRank() int32 {
	if m != nil {
		return m.StartRank
	}
	return 0
}

func (m *PosterFission) GetButtonTitle() string {
	if m != nil {
		return m.ButtonTitle
	}
	return ""
}

func (m *PosterFission) GetWepageId() int32 {
	if m != nil {
		return m.WepageId
	}
	return 0
}

func (m *PosterFission) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *PosterFission) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func init() {
	proto.RegisterType((*PosterFissionListReq)(nil), "ac.PosterFissionListReq")
	proto.RegisterType((*PosterFissionListRes)(nil), "ac.PosterFissionListRes")
	proto.RegisterType((*PosterFissionListRes_PosterFissionList)(nil), "ac.PosterFissionListRes.PosterFissionList")
	proto.RegisterType((*PosterFissionDetailReq)(nil), "ac.PosterFissionDetailReq")
	proto.RegisterType((*PosterFissionDetailRes)(nil), "ac.PosterFissionDetailRes")
	proto.RegisterType((*PosterFissionStateReq)(nil), "ac.PosterFissionStateReq")
	proto.RegisterType((*PosterFissionStateRes)(nil), "ac.PosterFissionStateRes")
	proto.RegisterType((*PosterFissionStateRes_Data)(nil), "ac.PosterFissionStateRes.Data")
	proto.RegisterType((*PosterFissionJoinReq)(nil), "ac.PosterFissionJoinReq")
	proto.RegisterType((*PosterFissionJoinRes)(nil), "ac.PosterFissionJoinRes")
	proto.RegisterType((*PosterFissionJoinRes_Data)(nil), "ac.PosterFissionJoinRes.Data")
	proto.RegisterType((*BackgroundImage)(nil), "ac.BackgroundImage")
	proto.RegisterType((*PosterFission)(nil), "ac.PosterFission")
}

func init() { proto.RegisterFile("ac/poster_fission.proto", fileDescriptor_51eb04d66e7a4894) }

var fileDescriptor_51eb04d66e7a4894 = []byte{
	// 735 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0xcd, 0x6e, 0xd3, 0x40,
	0x10, 0x56, 0x7e, 0xdb, 0x4c, 0x9a, 0xb4, 0x59, 0x0a, 0x75, 0x5d, 0x15, 0x8a, 0x25, 0x44, 0xd4,
	0x43, 0x2a, 0xc2, 0x05, 0x81, 0x40, 0x15, 0x54, 0x48, 0x41, 0x1c, 0x2a, 0xa7, 0x07, 0x10, 0x48,
	0xd6, 0xc6, 0xbb, 0x84, 0x55, 0x13, 0x6f, 0xe2, 0xdd, 0x42, 0xe9, 0x4b, 0x70, 0xe0, 0xcc, 0x81,
	0xb7, 0xe0, 0x19, 0x78, 0x2a, 0xb4, 0xb3, 0x4e, 0x1a, 0x27, 0xf6, 0xa1, 0x88, 0x9b, 0x77, 0x66,
	0x3c, 0xdf, 0x7c, 0xdf, 0xce, 0xcc, 0xc2, 0x0e, 0x0d, 0x8f, 0x26, 0x52, 0x69, 0x1e, 0x07, 0x9f,
	0x84, 0x52, 0x42, 0x46, 0x9d, 0x49, 0x2c, 0xb5, 0x24, 0x45, 0x1a, 0xba, 0x0d, 0x1a, 0x1e, 0x0d,
	0xa8, 0xe2, 0xd6, 0xe4, 0xf9, 0xb0, 0x7d, 0x8a, 0xa1, 0xaf, 0x6d, 0xe4, 0x5b, 0xa1, 0xb4, 0xcf,
	0xa7, 0x64, 0x0f, 0x6a, 0x13, 0x3a, 0xe4, 0x81, 0x12, 0x57, 0xdc, 0x29, 0x1c, 0x14, 0xda, 0x15,
	0x7f, 0xdd, 0x18, 0xfa, 0xe2, 0x8a, 0x93, 0x7d, 0x00, 0x74, 0x8a, 0x88, 0xf1, 0x4b, 0xa7, 0x88,
	0x5e, 0x0c, 0xef, 0x19, 0x83, 0xf7, 0xbd, 0x98, 0x99, 0x54, 0x11, 0x02, 0xe5, 0x50, 0xb2, 0x59,
	0x3e, 0xfc, 0x26, 0x0e, 0xac, 0x8d, 0xb9, 0x52, 0x74, 0xc8, 0x31, 0x51, 0xcd, 0x9f, 0x1d, 0xc9,
	0x0b, 0x28, 0x33, 0xaa, 0xa9, 0x53, 0x3a, 0x28, 0xb5, 0xeb, 0xdd, 0xc3, 0x0e, 0x0d, 0x3b, 0x59,
	0x59, 0x33, 0x8c, 0xf8, 0x1f, 0xd9, 0x86, 0x8a, 0x96, 0x9a, 0x8e, 0x9c, 0x32, 0xc2, 0xd9, 0x83,
	0xab, 0xa0, 0xb5, 0xf2, 0x03, 0x69, 0x42, 0x51, 0x30, 0x2c, 0xab, 0xe1, 0x17, 0x05, 0xc3, 0x5f,
	0x85, 0x1e, 0xcd, 0x4a, 0xb2, 0x07, 0x43, 0x7b, 0xc0, 0x87, 0x22, 0x0a, 0xb4, 0x18, 0x73, 0xa7,
	0x81, 0xae, 0x1a, 0x5a, 0xce, 0xc4, 0x98, 0x93, 0x5d, 0x58, 0xe7, 0x11, 0xb3, 0xce, 0xa6, 0xa5,
	0xc2, 0x23, 0x66, 0x5c, 0x5e, 0x1b, 0xee, 0xa4, 0x40, 0x4f, 0xb8, 0xa6, 0x62, 0x64, 0x74, 0xbe,
	0x46, 0xae, 0x18, 0x64, 0x6f, 0x9c, 0x13, 0x79, 0x53, 0xf1, 0x1e, 0xcc, 0xc5, 0x2b, 0xb4, 0xeb,
	0xdd, 0xd6, 0x8a, 0x78, 0x56, 0x23, 0xef, 0x23, 0xdc, 0x4e, 0x99, 0xfb, 0x9a, 0x6a, 0x6e, 0xea,
	0xda, 0x81, 0x35, 0x15, 0xc6, 0xe3, 0x20, 0x29, 0xae, 0xe6, 0x57, 0xcd, 0xb1, 0xc7, 0xc8, 0x21,
	0xb4, 0xd2, 0xbd, 0x65, 0x42, 0x6c, 0x0b, 0x6c, 0x4e, 0x16, 0x53, 0xf5, 0x98, 0xf7, 0xb3, 0x98,
	0x9d, 0xfe, 0xa6, 0x64, 0xba, 0x29, 0x32, 0x77, 0x57, 0xc8, 0xcc, 0xd2, 0x76, 0x4e, 0xa8, 0xa6,
	0x96, 0x99, 0xfb, 0xab, 0x00, 0x65, 0x73, 0x24, 0x4f, 0xa0, 0x99, 0x2e, 0x18, 0x41, 0x33, 0x35,
	0x69, 0xa4, 0x08, 0x90, 0x7b, 0x50, 0x1f, 0x49, 0x73, 0xdf, 0xca, 0xe4, 0x4f, 0x48, 0x02, 0x9a,
	0x10, 0xd1, 0xb4, 0x89, 0x75, 0x95, 0x6c, 0x87, 0xe1, 0xc1, 0x70, 0x8b, 0x69, 0x74, 0x9e, 0xb4,
	0x1d, 0x7e, 0x5f, 0xf7, 0x62, 0x65, 0xa1, 0x17, 0xbd, 0x0f, 0x4b, 0x73, 0xf2, 0x46, 0x8a, 0xe8,
	0xbf, 0x89, 0xff, 0xa3, 0x90, 0x99, 0xfd, 0xa6, 0xda, 0x3f, 0x4a, 0x69, 0xbf, 0xbf, 0x22, 0x5a,
	0x92, 0x75, 0x51, 0x7a, 0x37, 0x51, 0x7e, 0x26, 0x44, 0xe1, 0x5a, 0x08, 0xef, 0x19, 0x6c, 0xbe,
	0xa4, 0xe1, 0xf9, 0x30, 0x96, 0x17, 0x11, 0xeb, 0x8d, 0x0d, 0x02, 0x81, 0xf2, 0x98, 0x8a, 0x28,
	0xa1, 0x8a, 0xdf, 0xa8, 0xec, 0x67, 0x1a, 0xcf, 0x07, 0x10, 0x0f, 0xde, 0x9f, 0x12, 0x34, 0x52,
	0xe0, 0xcb, 0xe3, 0x93, 0x33, 0xb8, 0xc7, 0xd0, 0x1a, 0xcc, 0x41, 0x03, 0x61, 0x50, 0x55, 0xb2,
	0x56, 0x6e, 0x19, 0x42, 0x4b, 0x15, 0xf9, 0x5b, 0x83, 0xb4, 0x41, 0x99, 0x56, 0xc0, 0x12, 0x02,
	0x9b, 0xbd, 0x82, 0xd9, 0x01, 0x4d, 0x67, 0x08, 0x31, 0x0f, 0xc0, 0xec, 0x4e, 0x75, 0x21, 0xc0,
	0xb2, 0x6c, 0xc3, 0x96, 0x0d, 0x98, 0x48, 0x25, 0xb4, 0xb9, 0xba, 0x4b, 0x67, 0x0d, 0xeb, 0x6e,
	0xa2, 0xfd, 0x34, 0x31, 0xbf, 0xcb, 0x88, 0xfc, 0xe6, 0xac, 0x67, 0x44, 0xbe, 0x27, 0x1e, 0x34,
	0xa6, 0x71, 0x60, 0x2e, 0x30, 0x81, 0xad, 0x21, 0x6c, 0x7d, 0x1a, 0xbf, 0x92, 0x2c, 0xc1, 0xdd,
	0x07, 0x50, 0x9a, 0xc6, 0x3a, 0xc0, 0xab, 0x00, 0xbb, 0xab, 0xd1, 0xe2, 0x9b, 0xc6, 0xbc, 0x0f,
	0x1b, 0x83, 0x0b, 0xad, 0x65, 0x94, 0x30, 0xab, 0xdb, 0x0c, 0xd6, 0x66, 0xa9, 0xed, 0x41, 0xed,
	0x2b, 0xb7, 0xfb, 0x9e, 0x39, 0x1b, 0xf6, 0x29, 0xb0, 0x86, 0x1e, 0xfb, 0xf7, 0x9d, 0xd8, 0xfd,
	0xbd, 0xfc, 0x4a, 0xf4, 0x79, 0xfc, 0x45, 0x84, 0x9c, 0x3c, 0x84, 0x4a, 0x5f, 0xcb, 0x98, 0x93,
	0xd5, 0x09, 0x75, 0xeb, 0xf6, 0xba, 0x14, 0x2e, 0x91, 0xa7, 0x50, 0xc6, 0xed, 0xed, 0xe4, 0x3c,
	0x0d, 0x53, 0x37, 0xcf, 0xa3, 0xc8, 0x31, 0x54, 0xed, 0x6a, 0x25, 0xee, 0x4a, 0xcc, 0x7c, 0x3b,
	0xbb, 0xf9, 0x3e, 0x45, 0x9e, 0x9b, 0x32, 0xcd, 0xbc, 0xef, 0xe6, 0xed, 0xa3, 0xa9, 0x9b, 0xeb,
	0xc2, 0xe2, 0xcd, 0xe8, 0x64, 0x14, 0x9f, 0x6c, 0x01, 0x37, 0xcf, 0xa3, 0x06, 0x55, 0x7c, 0xbb,
	0x1f, 0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x6f, 0xc5, 0xc2, 0xfe, 0xe9, 0x07, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PosterFissionServiceClient is the client API for PosterFissionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PosterFissionServiceClient interface {
	// 活动新增/编辑
	Store(ctx context.Context, in *PosterFission, opts ...grpc.CallOption) (*BaseRes, error)
	// 活动列表
	List(ctx context.Context, in *PosterFissionListReq, opts ...grpc.CallOption) (*PosterFissionListRes, error)
	// 活动详情
	Detail(ctx context.Context, in *PosterFissionDetailReq, opts ...grpc.CallOption) (*PosterFissionDetailRes, error)
	// 活动信息及状态
	State(ctx context.Context, in *PosterFissionStateReq, opts ...grpc.CallOption) (*PosterFissionStateRes, error)
	// 参加活动
	Join(ctx context.Context, in *PosterFissionJoinReq, opts ...grpc.CallOption) (*PosterFissionJoinRes, error)
}

type posterFissionServiceClient struct {
	cc *grpc.ClientConn
}

func NewPosterFissionServiceClient(cc *grpc.ClientConn) PosterFissionServiceClient {
	return &posterFissionServiceClient{cc}
}

func (c *posterFissionServiceClient) Store(ctx context.Context, in *PosterFission, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/ac.PosterFissionService/Store", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *posterFissionServiceClient) List(ctx context.Context, in *PosterFissionListReq, opts ...grpc.CallOption) (*PosterFissionListRes, error) {
	out := new(PosterFissionListRes)
	err := c.cc.Invoke(ctx, "/ac.PosterFissionService/List", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *posterFissionServiceClient) Detail(ctx context.Context, in *PosterFissionDetailReq, opts ...grpc.CallOption) (*PosterFissionDetailRes, error) {
	out := new(PosterFissionDetailRes)
	err := c.cc.Invoke(ctx, "/ac.PosterFissionService/Detail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *posterFissionServiceClient) State(ctx context.Context, in *PosterFissionStateReq, opts ...grpc.CallOption) (*PosterFissionStateRes, error) {
	out := new(PosterFissionStateRes)
	err := c.cc.Invoke(ctx, "/ac.PosterFissionService/State", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *posterFissionServiceClient) Join(ctx context.Context, in *PosterFissionJoinReq, opts ...grpc.CallOption) (*PosterFissionJoinRes, error) {
	out := new(PosterFissionJoinRes)
	err := c.cc.Invoke(ctx, "/ac.PosterFissionService/Join", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PosterFissionServiceServer is the server API for PosterFissionService service.
type PosterFissionServiceServer interface {
	// 活动新增/编辑
	Store(context.Context, *PosterFission) (*BaseRes, error)
	// 活动列表
	List(context.Context, *PosterFissionListReq) (*PosterFissionListRes, error)
	// 活动详情
	Detail(context.Context, *PosterFissionDetailReq) (*PosterFissionDetailRes, error)
	// 活动信息及状态
	State(context.Context, *PosterFissionStateReq) (*PosterFissionStateRes, error)
	// 参加活动
	Join(context.Context, *PosterFissionJoinReq) (*PosterFissionJoinRes, error)
}

// UnimplementedPosterFissionServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPosterFissionServiceServer struct {
}

func (*UnimplementedPosterFissionServiceServer) Store(ctx context.Context, req *PosterFission) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Store not implemented")
}
func (*UnimplementedPosterFissionServiceServer) List(ctx context.Context, req *PosterFissionListReq) (*PosterFissionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (*UnimplementedPosterFissionServiceServer) Detail(ctx context.Context, req *PosterFissionDetailReq) (*PosterFissionDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Detail not implemented")
}
func (*UnimplementedPosterFissionServiceServer) State(ctx context.Context, req *PosterFissionStateReq) (*PosterFissionStateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method State not implemented")
}
func (*UnimplementedPosterFissionServiceServer) Join(ctx context.Context, req *PosterFissionJoinReq) (*PosterFissionJoinRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Join not implemented")
}

func RegisterPosterFissionServiceServer(s *grpc.Server, srv PosterFissionServiceServer) {
	s.RegisterService(&_PosterFissionService_serviceDesc, srv)
}

func _PosterFissionService_Store_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PosterFission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PosterFissionServiceServer).Store(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.PosterFissionService/Store",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PosterFissionServiceServer).Store(ctx, req.(*PosterFission))
	}
	return interceptor(ctx, in, info, handler)
}

func _PosterFissionService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PosterFissionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PosterFissionServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.PosterFissionService/List",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PosterFissionServiceServer).List(ctx, req.(*PosterFissionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PosterFissionService_Detail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PosterFissionDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PosterFissionServiceServer).Detail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.PosterFissionService/Detail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PosterFissionServiceServer).Detail(ctx, req.(*PosterFissionDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PosterFissionService_State_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PosterFissionStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PosterFissionServiceServer).State(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.PosterFissionService/State",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PosterFissionServiceServer).State(ctx, req.(*PosterFissionStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PosterFissionService_Join_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PosterFissionJoinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PosterFissionServiceServer).Join(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.PosterFissionService/Join",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PosterFissionServiceServer).Join(ctx, req.(*PosterFissionJoinReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PosterFissionService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.PosterFissionService",
	HandlerType: (*PosterFissionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Store",
			Handler:    _PosterFissionService_Store_Handler,
		},
		{
			MethodName: "List",
			Handler:    _PosterFissionService_List_Handler,
		},
		{
			MethodName: "Detail",
			Handler:    _PosterFissionService_Detail_Handler,
		},
		{
			MethodName: "State",
			Handler:    _PosterFissionService_State_Handler,
		},
		{
			MethodName: "Join",
			Handler:    _PosterFissionService_Join_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/poster_fission.proto",
}
