// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/two_session_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type UpRepresentativeReq struct {
	//人大代表ID
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	ScrmId               string   `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpRepresentativeReq) Reset()         { *m = UpRepresentativeReq{} }
func (m *UpRepresentativeReq) String() string { return proto.CompactTextString(m) }
func (*UpRepresentativeReq) ProtoMessage()    {}
func (*UpRepresentativeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a3c5ee83586a83b, []int{0}
}

func (m *UpRepresentativeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpRepresentativeReq.Unmarshal(m, b)
}
func (m *UpRepresentativeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpRepresentativeReq.Marshal(b, m, deterministic)
}
func (m *UpRepresentativeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpRepresentativeReq.Merge(m, src)
}
func (m *UpRepresentativeReq) XXX_Size() int {
	return xxx_messageInfo_UpRepresentativeReq.Size(m)
}
func (m *UpRepresentativeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpRepresentativeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpRepresentativeReq proto.InternalMessageInfo

func (m *UpRepresentativeReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpRepresentativeReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type RepresentativeNumberRes struct {
	//人大代表支持人数
	Number               int32    `protobuf:"varint,1,opt,name=number,proto3" json:"number"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RepresentativeNumberRes) Reset()         { *m = RepresentativeNumberRes{} }
func (m *RepresentativeNumberRes) String() string { return proto.CompactTextString(m) }
func (*RepresentativeNumberRes) ProtoMessage()    {}
func (*RepresentativeNumberRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a3c5ee83586a83b, []int{1}
}

func (m *RepresentativeNumberRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RepresentativeNumberRes.Unmarshal(m, b)
}
func (m *RepresentativeNumberRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RepresentativeNumberRes.Marshal(b, m, deterministic)
}
func (m *RepresentativeNumberRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RepresentativeNumberRes.Merge(m, src)
}
func (m *RepresentativeNumberRes) XXX_Size() int {
	return xxx_messageInfo_RepresentativeNumberRes.Size(m)
}
func (m *RepresentativeNumberRes) XXX_DiscardUnknown() {
	xxx_messageInfo_RepresentativeNumberRes.DiscardUnknown(m)
}

var xxx_messageInfo_RepresentativeNumberRes proto.InternalMessageInfo

func (m *RepresentativeNumberRes) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

type LotteryStatusRes struct {
	IsUp                 int32    `protobuf:"varint,1,opt,name=is_up,json=isUp,proto3" json:"is_up"`
	IsShare              int32    `protobuf:"varint,2,opt,name=is_share,json=isShare,proto3" json:"is_share"`
	LotteryNum           int32    `protobuf:"varint,3,opt,name=lottery_num,json=lotteryNum,proto3" json:"lottery_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotteryStatusRes) Reset()         { *m = LotteryStatusRes{} }
func (m *LotteryStatusRes) String() string { return proto.CompactTextString(m) }
func (*LotteryStatusRes) ProtoMessage()    {}
func (*LotteryStatusRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a3c5ee83586a83b, []int{2}
}

func (m *LotteryStatusRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryStatusRes.Unmarshal(m, b)
}
func (m *LotteryStatusRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryStatusRes.Marshal(b, m, deterministic)
}
func (m *LotteryStatusRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryStatusRes.Merge(m, src)
}
func (m *LotteryStatusRes) XXX_Size() int {
	return xxx_messageInfo_LotteryStatusRes.Size(m)
}
func (m *LotteryStatusRes) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryStatusRes.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryStatusRes proto.InternalMessageInfo

func (m *LotteryStatusRes) GetIsUp() int32 {
	if m != nil {
		return m.IsUp
	}
	return 0
}

func (m *LotteryStatusRes) GetIsShare() int32 {
	if m != nil {
		return m.IsShare
	}
	return 0
}

func (m *LotteryStatusRes) GetLotteryNum() int32 {
	if m != nil {
		return m.LotteryNum
	}
	return 0
}

type LotteryReporteReq struct {
	//人大代表ID
	RepresentativeId     int32    `protobuf:"varint,1,opt,name=representative_id,json=representativeId,proto3" json:"representative_id"`
	ScrmId               string   `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	LotteryId            int32    `protobuf:"varint,3,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotteryReporteReq) Reset()         { *m = LotteryReporteReq{} }
func (m *LotteryReporteReq) String() string { return proto.CompactTextString(m) }
func (*LotteryReporteReq) ProtoMessage()    {}
func (*LotteryReporteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a3c5ee83586a83b, []int{3}
}

func (m *LotteryReporteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryReporteReq.Unmarshal(m, b)
}
func (m *LotteryReporteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryReporteReq.Marshal(b, m, deterministic)
}
func (m *LotteryReporteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryReporteReq.Merge(m, src)
}
func (m *LotteryReporteReq) XXX_Size() int {
	return xxx_messageInfo_LotteryReporteReq.Size(m)
}
func (m *LotteryReporteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryReporteReq.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryReporteReq proto.InternalMessageInfo

func (m *LotteryReporteReq) GetRepresentativeId() int32 {
	if m != nil {
		return m.RepresentativeId
	}
	return 0
}

func (m *LotteryReporteReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *LotteryReporteReq) GetLotteryId() int32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

func init() {
	proto.RegisterType((*UpRepresentativeReq)(nil), "ac.UpRepresentativeReq")
	proto.RegisterType((*RepresentativeNumberRes)(nil), "ac.RepresentativeNumberRes")
	proto.RegisterType((*LotteryStatusRes)(nil), "ac.LotteryStatusRes")
	proto.RegisterType((*LotteryReporteReq)(nil), "ac.LotteryReporteReq")
}

func init() { proto.RegisterFile("ac/two_session_service.proto", fileDescriptor_1a3c5ee83586a83b) }

var fileDescriptor_1a3c5ee83586a83b = []byte{
	// 446 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x93, 0x4f, 0x6f, 0xd3, 0x40,
	0x10, 0xc5, 0x95, 0xd0, 0xa4, 0x74, 0x0a, 0x69, 0xb2, 0x29, 0x38, 0x4d, 0x41, 0x54, 0x39, 0x55,
	0x42, 0x4a, 0xc5, 0x9f, 0x23, 0x2a, 0x08, 0x0e, 0x55, 0x50, 0x14, 0xa1, 0x35, 0x3d, 0x5b, 0x1b,
	0x7b, 0x24, 0x56, 0x8a, 0xbd, 0xdb, 0x9d, 0xdd, 0x84, 0x7e, 0x6d, 0x3e, 0x01, 0x5a, 0xdb, 0x09,
	0x75, 0xb0, 0x4f, 0x3d, 0xce, 0xfc, 0xc6, 0xef, 0xcd, 0xec, 0x93, 0xe1, 0x95, 0x88, 0xaf, 0xec,
	0x46, 0x45, 0x84, 0x44, 0x52, 0x65, 0x11, 0xa1, 0x59, 0xcb, 0x18, 0xa7, 0xda, 0x28, 0xab, 0x58,
	0x5b, 0xc4, 0xe3, 0x40, 0xc4, 0x57, 0x22, 0xb6, 0x72, 0x2d, 0xed, 0x7d, 0x94, 0xaa, 0x04, 0x57,
	0x05, 0x9c, 0x5c, 0xc3, 0xf0, 0x56, 0x73, 0xd4, 0x06, 0x09, 0x33, 0x2b, 0xac, 0x5c, 0x23, 0xc7,
	0x3b, 0xd6, 0x83, 0xb6, 0x4c, 0x46, 0xad, 0x8b, 0xd6, 0x65, 0x87, 0xb7, 0x65, 0xc2, 0x02, 0x38,
	0xa4, 0xd8, 0xa4, 0x91, 0x4c, 0x46, 0xed, 0x8b, 0xd6, 0xe5, 0x11, 0xef, 0xfa, 0x72, 0x96, 0x4c,
	0xde, 0x41, 0x50, 0xfd, 0x7a, 0xe1, 0xd2, 0x25, 0x1a, 0x8e, 0xc4, 0x5e, 0x42, 0x37, 0xcb, 0x8b,
	0x52, 0xa7, 0xac, 0x26, 0x31, 0xf4, 0xe7, 0xca, 0x5a, 0x34, 0xf7, 0xa1, 0x15, 0xd6, 0x91, 0x9f,
	0x1d, 0x42, 0x47, 0x52, 0xe4, 0x74, 0x39, 0x7a, 0x20, 0xe9, 0x56, 0xb3, 0x33, 0x78, 0x2a, 0x29,
	0xa2, 0x5f, 0xc2, 0x60, 0xee, 0xda, 0xe1, 0x87, 0x92, 0x42, 0x5f, 0xb2, 0x37, 0x70, 0xbc, 0x2a,
	0x34, 0xa2, 0xcc, 0xa5, 0xa3, 0x27, 0x39, 0x85, 0xb2, 0xb5, 0x70, 0xe9, 0xe4, 0x37, 0x0c, 0x4a,
	0x13, 0x8e, 0x5a, 0x19, 0x9b, 0x5f, 0xf5, 0x16, 0x06, 0xa6, 0xb2, 0x6c, 0xb4, 0x3b, 0xb2, 0x5f,
	0x05, 0xb3, 0xe6, 0x93, 0xd9, 0x6b, 0xd8, 0x1a, 0x79, 0x56, 0x58, 0x1f, 0x95, 0x9d, 0x59, 0xf2,
	0xfe, 0xcf, 0x01, 0x0c, 0x7e, 0x6e, 0x54, 0x88, 0x44, 0x4a, 0x66, 0x61, 0x11, 0x05, 0xfb, 0x02,
	0xfd, 0xfd, 0x77, 0x66, 0xc1, 0x54, 0xc4, 0xd3, 0x9a, 0xd7, 0x1f, 0x0f, 0x3d, 0xf8, 0x2a, 0x08,
	0x39, 0x92, 0x56, 0x19, 0xe1, 0x02, 0x37, 0xec, 0x1b, 0x0c, 0xf3, 0xdb, 0x1f, 0x25, 0x32, 0x87,
	0xd3, 0xba, 0xb8, 0x9a, 0x55, 0xce, 0x3d, 0x68, 0x4a, 0xf8, 0x1a, 0x9e, 0x57, 0x92, 0x6c, 0x96,
	0x39, 0xf5, 0xe0, 0xbf, 0xd4, 0x3f, 0x41, 0xaf, 0x1a, 0x12, 0x7b, 0xf1, 0x60, 0xee, 0x5f, 0x70,
	0xf5, 0xb7, 0xdc, 0x40, 0x2f, 0x74, 0xcb, 0x54, 0xda, 0x1f, 0x46, 0x69, 0x45, 0x62, 0xc5, 0xce,
	0xfc, 0x58, 0xb5, 0xc7, 0xf1, 0xce, 0x21, 0xd9, 0xf1, 0xb8, 0x0e, 0x15, 0x5a, 0xec, 0x33, 0x9c,
	0x84, 0x4e, 0x7b, 0xb3, 0x9d, 0x52, 0xb0, 0x35, 0xdc, 0xd7, 0xa9, 0xdd, 0xe4, 0x3b, 0x9c, 0xdc,
	0xe0, 0xee, 0xe3, 0xb9, 0x24, 0xcb, 0x72, 0xbf, 0xbd, 0xe6, 0x56, 0xe3, 0xbc, 0x96, 0x95, 0xcb,
	0x7c, 0x84, 0xe3, 0x07, 0xa8, 0x79, 0x91, 0x67, 0x1e, 0x6c, 0x9b, 0xcb, 0x6e, 0xfe, 0x37, 0x7f,
	0xf8, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xa1, 0x1b, 0xe0, 0xdc, 0x0a, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TwoSessoinServiceClient is the client API for TwoSessoinService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TwoSessoinServiceClient interface {
	// 支持人大代表
	UpRepresentative(ctx context.Context, in *UpRepresentativeReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 分享人大代表
	ShareRepresentative(ctx context.Context, in *UpRepresentativeReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 获取人大代表支持人数
	RepresentativeNumber(ctx context.Context, in *UpRepresentativeReq, opts ...grpc.CallOption) (*RepresentativeNumberRes, error)
	// 获取抽奖状态
	LotteryStatus(ctx context.Context, in *UpRepresentativeReq, opts ...grpc.CallOption) (*LotteryStatusRes, error)
	// 获取结果上报
	LotteryReporte(ctx context.Context, in *LotteryReporteReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 提交建议
	SubmitProposal(ctx context.Context, in *SubmitProposalRequest, opts ...grpc.CallOption) (*SubmitProposalResponse, error)
	// 支持建议
	SupportProposal(ctx context.Context, in *BaseProposalRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 获取建议列表
	GetProposalList(ctx context.Context, in *GetProposalListRequest, opts ...grpc.CallOption) (*GetProposalListResponse, error)
	// 获取单条建议
	GetProposal(ctx context.Context, in *BaseProposalRequest, opts ...grpc.CallOption) (*Proposal, error)
}

type twoSessoinServiceClient struct {
	cc *grpc.ClientConn
}

func NewTwoSessoinServiceClient(cc *grpc.ClientConn) TwoSessoinServiceClient {
	return &twoSessoinServiceClient{cc}
}

func (c *twoSessoinServiceClient) UpRepresentative(ctx context.Context, in *UpRepresentativeReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.TwoSessoinService/UpRepresentative", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *twoSessoinServiceClient) ShareRepresentative(ctx context.Context, in *UpRepresentativeReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.TwoSessoinService/ShareRepresentative", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *twoSessoinServiceClient) RepresentativeNumber(ctx context.Context, in *UpRepresentativeReq, opts ...grpc.CallOption) (*RepresentativeNumberRes, error) {
	out := new(RepresentativeNumberRes)
	err := c.cc.Invoke(ctx, "/ac.TwoSessoinService/RepresentativeNumber", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *twoSessoinServiceClient) LotteryStatus(ctx context.Context, in *UpRepresentativeReq, opts ...grpc.CallOption) (*LotteryStatusRes, error) {
	out := new(LotteryStatusRes)
	err := c.cc.Invoke(ctx, "/ac.TwoSessoinService/LotteryStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *twoSessoinServiceClient) LotteryReporte(ctx context.Context, in *LotteryReporteReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.TwoSessoinService/LotteryReporte", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *twoSessoinServiceClient) SubmitProposal(ctx context.Context, in *SubmitProposalRequest, opts ...grpc.CallOption) (*SubmitProposalResponse, error) {
	out := new(SubmitProposalResponse)
	err := c.cc.Invoke(ctx, "/ac.TwoSessoinService/SubmitProposal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *twoSessoinServiceClient) SupportProposal(ctx context.Context, in *BaseProposalRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.TwoSessoinService/SupportProposal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *twoSessoinServiceClient) GetProposalList(ctx context.Context, in *GetProposalListRequest, opts ...grpc.CallOption) (*GetProposalListResponse, error) {
	out := new(GetProposalListResponse)
	err := c.cc.Invoke(ctx, "/ac.TwoSessoinService/GetProposalList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *twoSessoinServiceClient) GetProposal(ctx context.Context, in *BaseProposalRequest, opts ...grpc.CallOption) (*Proposal, error) {
	out := new(Proposal)
	err := c.cc.Invoke(ctx, "/ac.TwoSessoinService/GetProposal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TwoSessoinServiceServer is the server API for TwoSessoinService service.
type TwoSessoinServiceServer interface {
	// 支持人大代表
	UpRepresentative(context.Context, *UpRepresentativeReq) (*BaseResponseNew, error)
	// 分享人大代表
	ShareRepresentative(context.Context, *UpRepresentativeReq) (*BaseResponseNew, error)
	// 获取人大代表支持人数
	RepresentativeNumber(context.Context, *UpRepresentativeReq) (*RepresentativeNumberRes, error)
	// 获取抽奖状态
	LotteryStatus(context.Context, *UpRepresentativeReq) (*LotteryStatusRes, error)
	// 获取结果上报
	LotteryReporte(context.Context, *LotteryReporteReq) (*BaseResponseNew, error)
	// 提交建议
	SubmitProposal(context.Context, *SubmitProposalRequest) (*SubmitProposalResponse, error)
	// 支持建议
	SupportProposal(context.Context, *BaseProposalRequest) (*BaseResponseNew, error)
	// 获取建议列表
	GetProposalList(context.Context, *GetProposalListRequest) (*GetProposalListResponse, error)
	// 获取单条建议
	GetProposal(context.Context, *BaseProposalRequest) (*Proposal, error)
}

// UnimplementedTwoSessoinServiceServer can be embedded to have forward compatible implementations.
type UnimplementedTwoSessoinServiceServer struct {
}

func (*UnimplementedTwoSessoinServiceServer) UpRepresentative(ctx context.Context, req *UpRepresentativeReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpRepresentative not implemented")
}
func (*UnimplementedTwoSessoinServiceServer) ShareRepresentative(ctx context.Context, req *UpRepresentativeReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShareRepresentative not implemented")
}
func (*UnimplementedTwoSessoinServiceServer) RepresentativeNumber(ctx context.Context, req *UpRepresentativeReq) (*RepresentativeNumberRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RepresentativeNumber not implemented")
}
func (*UnimplementedTwoSessoinServiceServer) LotteryStatus(ctx context.Context, req *UpRepresentativeReq) (*LotteryStatusRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LotteryStatus not implemented")
}
func (*UnimplementedTwoSessoinServiceServer) LotteryReporte(ctx context.Context, req *LotteryReporteReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LotteryReporte not implemented")
}
func (*UnimplementedTwoSessoinServiceServer) SubmitProposal(ctx context.Context, req *SubmitProposalRequest) (*SubmitProposalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitProposal not implemented")
}
func (*UnimplementedTwoSessoinServiceServer) SupportProposal(ctx context.Context, req *BaseProposalRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SupportProposal not implemented")
}
func (*UnimplementedTwoSessoinServiceServer) GetProposalList(ctx context.Context, req *GetProposalListRequest) (*GetProposalListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProposalList not implemented")
}
func (*UnimplementedTwoSessoinServiceServer) GetProposal(ctx context.Context, req *BaseProposalRequest) (*Proposal, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProposal not implemented")
}

func RegisterTwoSessoinServiceServer(s *grpc.Server, srv TwoSessoinServiceServer) {
	s.RegisterService(&_TwoSessoinService_serviceDesc, srv)
}

func _TwoSessoinService_UpRepresentative_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpRepresentativeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwoSessoinServiceServer).UpRepresentative(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.TwoSessoinService/UpRepresentative",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwoSessoinServiceServer).UpRepresentative(ctx, req.(*UpRepresentativeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TwoSessoinService_ShareRepresentative_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpRepresentativeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwoSessoinServiceServer).ShareRepresentative(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.TwoSessoinService/ShareRepresentative",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwoSessoinServiceServer).ShareRepresentative(ctx, req.(*UpRepresentativeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TwoSessoinService_RepresentativeNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpRepresentativeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwoSessoinServiceServer).RepresentativeNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.TwoSessoinService/RepresentativeNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwoSessoinServiceServer).RepresentativeNumber(ctx, req.(*UpRepresentativeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TwoSessoinService_LotteryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpRepresentativeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwoSessoinServiceServer).LotteryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.TwoSessoinService/LotteryStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwoSessoinServiceServer).LotteryStatus(ctx, req.(*UpRepresentativeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TwoSessoinService_LotteryReporte_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LotteryReporteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwoSessoinServiceServer).LotteryReporte(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.TwoSessoinService/LotteryReporte",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwoSessoinServiceServer).LotteryReporte(ctx, req.(*LotteryReporteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TwoSessoinService_SubmitProposal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitProposalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwoSessoinServiceServer).SubmitProposal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.TwoSessoinService/SubmitProposal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwoSessoinServiceServer).SubmitProposal(ctx, req.(*SubmitProposalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TwoSessoinService_SupportProposal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseProposalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwoSessoinServiceServer).SupportProposal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.TwoSessoinService/SupportProposal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwoSessoinServiceServer).SupportProposal(ctx, req.(*BaseProposalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TwoSessoinService_GetProposalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProposalListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwoSessoinServiceServer).GetProposalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.TwoSessoinService/GetProposalList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwoSessoinServiceServer).GetProposalList(ctx, req.(*GetProposalListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TwoSessoinService_GetProposal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseProposalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwoSessoinServiceServer).GetProposal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.TwoSessoinService/GetProposal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwoSessoinServiceServer).GetProposal(ctx, req.(*BaseProposalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _TwoSessoinService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.TwoSessoinService",
	HandlerType: (*TwoSessoinServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpRepresentative",
			Handler:    _TwoSessoinService_UpRepresentative_Handler,
		},
		{
			MethodName: "ShareRepresentative",
			Handler:    _TwoSessoinService_ShareRepresentative_Handler,
		},
		{
			MethodName: "RepresentativeNumber",
			Handler:    _TwoSessoinService_RepresentativeNumber_Handler,
		},
		{
			MethodName: "LotteryStatus",
			Handler:    _TwoSessoinService_LotteryStatus_Handler,
		},
		{
			MethodName: "LotteryReporte",
			Handler:    _TwoSessoinService_LotteryReporte_Handler,
		},
		{
			MethodName: "SubmitProposal",
			Handler:    _TwoSessoinService_SubmitProposal_Handler,
		},
		{
			MethodName: "SupportProposal",
			Handler:    _TwoSessoinService_SupportProposal_Handler,
		},
		{
			MethodName: "GetProposalList",
			Handler:    _TwoSessoinService_GetProposalList_Handler,
		},
		{
			MethodName: "GetProposal",
			Handler:    _TwoSessoinService_GetProposal_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/two_session_service.proto",
}
