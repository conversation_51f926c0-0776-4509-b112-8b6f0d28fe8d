syntax = "proto3";

package ac;

import "google/protobuf/struct.proto";

// 基础服务
service BaseService{
  // 活动删除
  rpc ActivityDelete(ActivityDeleteReq) returns (BaseRes);
  // 提交审核
  rpc SubmitCheck(SubmitCheckReq) returns (BaseRes);
  // 活动审核
  rpc Check(CheckReq) returns (BaseRes);
  // 异常标记
  rpc ExceptionMark(ExceptionMarkReq) returns(BaseRes);
  // 异常商品计数
  rpc RiskProductCount(RiskProductCountReq) returns(RiskProductCountRes);
  // 发送企业微信机器人消息
  rpc SendWebhookNotify(SendWebhookNotifyReq) returns(BaseRes);
  // 发送企业微信机器人md消息
  rpc SendWebhookMarkdownNotify(SendWebhookMarkdownNotifyReq) returns(BaseRes);
}

message BaseRes{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
}

message ActivityDeleteReq {
  // 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
  int32 type = 1;
  // 活动id
  int32 id = 2;
}

// 提交审核
message SubmitCheckReq {
  // 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
  int32 type = 1;
  // 活动id
  int32 id = 2;
}

// 通用审核
message CheckReq {
  // 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
  int32 type = 1;
  // 活动id
  int32 id = 2;
  // 1审核通过、0审核不通过
  int32 pass = 3;
  // 审核理由
  string check_reason = 4;
}

// 异常标记
message ExceptionMarkReq{
  // 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
  int32 type = 1;
  // 活动商品列表对应的id
  int32 activity_product_id = 2;
  // 活动商品列表对应的多个id
  repeated int32 ids = 4;
  // 1标记为异常、0取消标记为异常
  int32 state = 3;
}

message SendWebhookNotifyReq {
  // 机器人地址key
  string key = 1;
  // 要发送的数据map
  google.protobuf.Struct data = 2;
}

message SendWebhookMarkdownNotifyReq {
  // 机器人地址key
  string key = 1;
  // 要发送的内容
  string content = 2;
}

// 异常商品计数
message RiskProductCountReq {
  // 活动类型 2限时折扣、5拼团、6周期购、8预售、9新秒杀、99助力
  int32 type = 1;
  // 查询的条件
  string condition = 3;
}

message RiskProductCountRes{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message CountData {
    // 异常商品数
    int32 count = 1;
    // 总数
    int32 total = 2;
  }

  // 计数，key活动id，value异常商品计数，不存在则为0
  map <int32, CountData> data = 3;
}