syntax = "proto3";

package ac;

// 基础请求
message baseRequest {
  //手机号
  string mobile = 1;
}
message baseResponse{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
}

///////////////////////////////pet_tease(宠物吐槽活动)///////////////////////////

message petTeaseRequest{
  //开启人手机号
  string mobile = 1;
  //打开的宝箱类型 1：铜 2：银 3：金 4：--
  int32 treasure_level = 2;
}
message PetTeaseResponse{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //是否已经打开过 1：已经打开过 0：没打开过
  int32 is_opened = 4;
  // 内容
  petTease data = 5;
}
message petTeaseListResponse{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  //手机号
  string mobile = 4;
  // 内容
  repeated petTease data = 5;
}

message petTease{
  //手机号
  string mobile = 1;
  //宝箱层级
  int32 treasure_level = 2;
  //生效时间
  string begin_time = 3;
  //结束时间
  string end_time = 4;
  //创建时间
  string create_time = 5;
  //满足金额
  string reach_money = 6;
  //减免金额
  string reduce_money = 7;
  //优惠券名称
  string coupon_name = 8;
  //是否抢到 1是 0 否
  int32 is_catch = 9;
  //核销码
  string coupon_no = 10;
}

//////////////////////////520爱猫日活动///////////////////////////
message examineListResponse {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  repeated examine Data = 4;
}

message examine {
  int32   id = 1;
  string  topic = 2;
  repeated string answer_list = 3;
  string answer = 4;
}

message SendCouponReq {
  //手机号
  string mobile = 1;

  //优惠卷类型Id
  int32 couponId = 2;

}

message SendCouponRes{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;

  string data = 4;
}

message SaveShareReq{
  string sharer_mobile = 1;
  string help_mobile = 2;
  string avatar = 3;
}

message GetMemberShareReq{
  string mobile = 1;
  //0：活动首页，1：海报首页
  int32 use = 2;
}



message GetMemberShareRes{

  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;

  MemberShareData data = 4;
}
message MemberShareData{
  //
  string mobile = 1;
  int32 binding_number = 2;
  //  //爱猫指数
  int32 cat_index = 3;
  // 宠主类型
  int32 master_type = 4;

  //宠主称号
  string master_title = 5;
  //宠主性格类型
  string character = 6;
  //在猫眼里的印象
  string impress = 7;
  //爱猫宣言
  string manifesto = 8;
  string avatar = 9;
  repeated ShareRelationshipData share_relationship_data = 10;
  repeated CouponData coupon_data = 11;

  //昵称
  string  nick = 12;
}

message ShareRelationshipData{
  //助力人手机号码
  string help_mobile = 1;
  //助力人头像
  string avatar = 2;
}
message CouponData{
  //金额(单位分)
  int32 coupon_amount = 1;
  //优惠券ID
  int32 coupon_id = 2;
  //0:未读,1已读
  int32 status = 3;
  //优惠卷名称
  string coupon_name = 4;
}

message SaveExamineAnswerReq {
  //手机号
  string mobile = 1;
  // 答案
  repeated Answer data = 2;
  //头像
  string Avatar = 3;
  //微信昵称
  string Nick = 4;
}

message Answer {
  int32 id = 1;
  string answer = 2;
}

message SaveExamineAnswerRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  MemberAnswer data = 4;
}

message MemberAnswer {
  //爱猫指数
  int32 cat_index = 1;
  // 宠主类型
  string master_type = 2;
  //宠主称号
  string master_title = 3;
  //宠主性格类型
  string character = 4;
  //在猫眼里的印象
  string impress = 5;
  //爱猫宣言
  string manifesto = 6;
}

message  GetCouponTemplateReq{
  int32  tid = 1;
}

message  GetCouponTemplateRes{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;

  repeated CouponTemplateData data = 4;
}

message  CouponTemplateData {
  //优惠券模板ID
  string voucher_t_id = 1;
  //标题
  string voucher_t_title = 2;
  //描述信息
  string voucher_t_desc = 3;
  //优惠券模版面额
  string voucher_t_price = 4;
  //状态(1-有效,2-失效)
  string voucher_t_state = 5;
  //状态文字说明
  string voucher_t_state_text = 6;
}

message SaveMemberCouponReq{
  //优惠券ID
  int32 coupon_id = 2;

  //手机号码
  string mobile = 1;

  //1:电商优惠卷,2:门店优惠卷,3:门店体检优惠卷
  int32 coupon_type=3;
}

message  GetMemberCouponReq{
  //手机号码
  string mobile = 1;
  //1:电商优惠卷,2:门店优惠卷,3:门店体检优惠卷
  int32 coupon_type=2;
}

message GetMemberCouponRes{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  MemberCouponData data = 4;
}
message MemberCouponData{
  //优惠券ID
  int32 coupon_id = 1;
}

message  SaveSubscribeMessageReq{
  //手机号码
  string mobile = 1;
  //OpenId
  string open_id = 2;

  repeated TemplateData data = 3;
}

message TemplateData{
  //模板Id
  string template_id = 1;
  //跳转地址
  string page_url = 2;

  //是否定时
  int32 message_type=3;
}

message  CancelSubscribeMessageReq{
  //手机号码
  string mobile = 1;
  //OpenId
  string open_id = 2;
}

message  CouponMessageRes{
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;

  repeated CouponMessageData data = 4;
}

message CouponMessageData {
  string  open_id = 1;
  string  template_id = 2;
  string  mobile = 3;
  string  page_url = 4;
  int32   message_type = 5;
  int32   message_status = 6;
  int64   coupon_amount = 7;
  int64   coupon_id = 8;
  string  coupon_mobile = 9;
  int32   status = 10;
  string  coupon_name = 11;
  int32   coupon_type = 12;
  int64   effective_data = 13;
  string  use_rule = 14;
  string  remarks = 15;
}

message  GetCatMonthInfoRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  // 错误信息
  string error = 3;
  CatMonthData data = 4;
}

message CatMonthData {
  // 是否参与过 1是 0否
  int32 status = 1;
  // 图片地址
  string img_url = 2;
  // 颜值分数
  float activity_score = 3;
  // 红包金额
  int64 coupon_amount = 4;
  // 评语
  string cat_remark = 5;
}


message  SaveCatMonthInfoReq {
  string mobile = 1;
  string img_url = 2;
  float  activity_score = 3;
  int32  is_cat = 4;
}

message SaveWatermarkReq {
  //水印ID，新增水印传0
  int32 watermark_id = 1;
  //水印名称
  string name = 2;
  //开始时间
  int64 start_time = 3;
  //结束时间
  int64 end_time = 4;
  //水印图片地址
  string watermark_img = 5;
  //是否展示活动价
  bool  is_display_activity_price=6;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 7;
}

message SaveWatermarkRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  //水印ID
  int64 watermark_id = 3;
}

message WatermarkListReq  {
  //水印名称
  string name = 1;
  //水印状态
  int32 status = 2;
  int32 page_size = 3;
  int32 page_index = 4;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 5;
}

message WatermarkListRes  {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  //水印列表
  repeated WatermarkData data = 3;
  //总条数
  int32 total = 4;
}

message WatermarkData {
  //水印ID
  int64 id = 1;
  //水印名称
  string name = 2;
  //水印图片地址
  string watermark_img = 3;
  //商品数量
  int32 goods_num = 4;
  //开始时间
  int64 start_time = 5;
  //结束时间
  int64 end_time = 6;
  //活动状态 1未开始  2进行中 3已终止  4已结束
  int32 status = 7;
  //是否展示活动价
  bool  is_display_activity_price=8;
}

message GetWatermarkReq {
  //水印ID
  int64 id = 1;
}

message GetWatermarkRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  //水印ID
  int64 id = 3;
  //水印名称
  string name = 4;
  //水印图片地址
  string watermark_img = 5;
  //商品数量
  int32 goods_num = 6;
  //开始时间
  int64 start_time = 7;
  //结束时间
  int64 end_time = 8;
  //活动状态 1未开始  2进行中 3已终止  4已结束
  int32 status = 9;
  //是否展示活动价
  bool  is_display_activity_price=10;
}

message GoodsListReq {
  //水印ID
  int64 watermark_id = 1;
  //sku id
  int32 sku = 2;
  //商品名称
  string goods_name = 3;
  //水印名称
  string watermark_name = 4;
  int32 page_size = 5;
  int32 page_index = 6;
}

message GoodsListRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  //总条数
  int32 total = 3;
  //数据列表
  repeated GoodsListData data = 4;
}

message GoodsListData {
  //主键ID
  int64 id = 1;
  //商品ID
  int64 goods_id = 2;
  //商品名称
  string goods_name = 3;
  //商品图片
  string watermark_img = 4;
  //商品价格
  float goods_price = 5;
  //活动价格
  float activity_price = 6;
  //水印名称
  string watermark_name = 7;
  //开始时间
  int64 start_time = 8;
  //结束时间
  int64 end_time = 9;
  //水印ID
  int64 watermark_id = 10;
  //商品图片
  string goods_image = 11;
  // 色值
  string color = 12;
  //是否展示活动价
  bool  is_display_activity_price=13;
}

message GoodsDeleteReq {
  //水印ID
  int64 watermark_id = 1;
  //数据ID, 可批量删除，传数组
  repeated int64 id = 2;
}

message GoodsAddReq {
  //水印ID
  int64 watermark_id = 1;
  //商品ID(sku_id)
  int64 goods_id = 2;
  //活动价格
  float price = 3;
  // 色值
  string color = 4;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 5;
}

message SearchGoodsReq {
  //SKUID
  int64 sku_id = 1;
  //SPUID
  int64 spu_id = 2;
  //商品名称
  string name = 3;
  int32 page_size = 4;
  int32 page_index = 5;
  //水印ID
  int64 watermark_id = 6;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 7;
}

message SearchGoodsRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  //总条数
  int32 total = 3;
  //商品列表
  repeated SearchGoodsData data = 4;
}

message SearchGoodsData {
  //SKUID
  int64 sku_id = 1;
  //SPUID
  int64 spu_id = 2;
  //商品名称
  string name = 3;
  //商品图片
  string goods_image = 4;
  //商品价格
  float goods_price = 5;
  //是否添加了iiqg
  int32 is_watermark = 6;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 7;
}

message GoodsPriceReq {
  //数据ID
  int64 id = 1;
  //水印ID
  int64 watermark_id = 2;
  //价格
  float price = 3;
  // 色值
  string color =4;
}

message StopWatermarkReq {
  //水印ID
  int32 watermark_id = 1;
}

message GoodsImportReq {
  //水印ID
  int64 watermark_id = 1;
  //文件URL路径
  string file_path = 2;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 3;
}

message GoodsImportRes {
  // 响应代码 200 成功 非 200 失败查看 message
  int32 code = 1;
  // 不成功的错误信息
  string message = 2;
  //导入失败商品列表
  repeated ImportFailGoods data = 3;
}

message ImportFailGoods {
  //SKUID
  int64 sku_id = 1;
  //价格
  float price = 2;
  //导入失败原因
  string remark = 3;
}

message ChristmasAddAddressReq {
  //奖品列表ID
  int32 id = 1;
  //收件人姓名
  string name = 2;
  //收件地址
  string address = 3;
  //手机号
  string mobile = 4;
  //宠物类型 1猫 2狗
  int32 pet_type = 5;
  //scrm_userid
  string scrm_userid = 6;
}

message BaseResponseNew {
  string msg = 1;
}

message ChannelStatisticsReq {
  //渠道名称
  string channel_name = 1;
  //渠道参数
  string parameter = 2;
  //网址
  string url = 3;
  //ip地址（前端不需要传）
  string ip = 4;
}
///////////////////////////////Coupon(派发优惠券)///////////////////////////

message HandOutCouponRequest{
    //优惠券ID
    string couponId = 1;
    //1门店券 2优惠券
    int32 type = 2;
    //手机号
    string mobile = 3;
    // 来源
    int32 from = 4;
    // scrm_user_id
    string scrm_user_id = 5;
}

message HandOutCouponResponse{
    //商城券返回的是派发的券ID，门店券返回的是券code
    string CouponCode = 1;
}

///////////////////////////////twoSessions(两会活动活动)///////////////////////////


message BaseProposalRequest{
    //用户Id
    string userId = 1;
    //建议Id
    int64 proposalId = 2;
    //支持的用户id
    string supportUserId = 3;
}

message SubmitProposalRequest{
    //用户id
    string userId = 1;
    //建议内容
    string proposalBody = 2;
    //头像
    string profile = 3;
    //手机
    string mobile = 4;
}

message SubmitProposalResponse{
    int64 id =1;
}
message GetProposalListRequest{
    //获取前面多少名，默认3名
    int32 size= 1;
}


message GetProposalListResponse{
    //排名建议
    repeated Proposal list = 1;
}

message Proposal{
    int64 id = 1;
    //用户id
    string userId = 2;
    //建议内容
    string proposalBody = 3;
    //头像
    string profile = 4;
    //用户名
    string userName = 5;
    //支持数
    int64 supportNum = 6;
    //是否支持过 0 否 1 是
    int32 isSupport = 7;
}

message GetPetMarketUserCouponReq{
  // 用户id
  string scrm_user_id = 1;
  // 优惠券模板id
  repeated int64 voucher_t_ids = 2;
  // 领取开始时间戳
  int64 start_time = 3;
  // 领取结束时间戳
  int64 end_time = 4;
}

message UserVoucher{
  int64 voucher_id = 1;
  int64 voucher_t_id = 2;
  int64 voucher_owner_id = 3;
  // 代金券状态(1-未用,2-已用,3-过期,4-收回)
  int64 voucher_state = 4;
}

message GetPetMarketUserCouponResponse{
  int64 code = 1;
  string message = 2;
  repeated UserVoucher list = 3;
}

message GetPetMarketTasksReq {
    
}

message PetMarketTask {
    int64 Id = 1;
    string title = 2;
    string sub_title = 3;
    string obj_value = 4;
    string reward_value = 5;
}

message GetPetMarketTasksResponse {
    int64 code = 1;
    string message = 2;
    repeated PetMarketTask list = 3;
}

message AddPetMarketUserTaskReq {
    string scrm_userid = 1;
    int64 task_id = 2;
}

message GetPetMarketUserTasksReq {
    string scrm_userid = 1;
    int64 task_id = 2;
}

message PetMarketUserTask {
    int64 id = 1;
    string scrm_userid = 2;
    int64 task_id = 3;
}

message GetPetMarketUserTasksResponse {
    int64 code = 1;
    string message = 2;
    repeated PetMarketUserTask list = 3;
}

message AddPetMarketRewardReq {
    string scrm_userid = 1;
    int64 prize_type = 2;
    string receiver = 3;
    string mobile = 4;
    string address = 5;
}

message GetPetMarketUserRewardReq {
    string scrm_userid = 1;
}

message PetMarketUserReward {
    int64 id = 1;
    string scrm_userid = 2;
    int64 prize_type = 3;
    string receiver = 4;
    string mobile = 5;
    string address = 6;
}

message GetPetMarketUserRewardResponse {
    int64 code = 1;
    string message = 2;
    PetMarketUserReward detail = 3;
}

message CountPetMarketRewardReq {

}

message CountPetMarketRewardReqResponse {
  int64 code = 1;
  string message = 2;
  int64 total = 3;
}

message CountUserOrderWithSkuidsRequest{
  string member_id = 1;
  repeated int64 skuids = 2;
  string start_time = 3;
  string end_time = 4;
}

message CountUserOrderWithSkuidsResponse{
  int64 Code = 1;
  string message = 2;
  int64 total = 3;
}

message CouponSendMultiReq {
  //优惠券模版id
  repeated int32 template_id_arr = 1;
  //每个手机号的发放数量
  int32 number = 2;
  //渠道
  int32 channel = 3;
  //要发放的手机号数组
  repeated string phone_arr = 4;
}

message CouponSendMultiRes {
  int32 code = 1;
  string msg = 2;
  repeated CouponSendMultiData data = 3;
  int32 status = 4;
}

message CouponSendMultiData {
  string coupon_template_id = 1;
  string coupon_name = 2;
  repeated CouponList coupon_list = 3;
}

message CouponList {
  int32 coupon_id = 1;
  string coupon_code = 2;
  string user_phone = 3;
  string uuid = 4;
}


message LuckyDrawActivityRecommendReq {
  // 1 推荐  2取消
  int32 down_or_up = 1;
  // id
  int32 id = 2;
}


message LuckyDrawActivityRecommendResponse{

  int32 code = 1;
  // id
  string msg = 2;

}