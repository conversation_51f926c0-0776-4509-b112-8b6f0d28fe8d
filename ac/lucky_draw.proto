syntax = "proto3";

package ac;

message MiniLuckyDrawRequest{
    //活动id
    string activity_id = 1;
    //scrm用户id
    string user_id = 2;

    string mobile =3;
}

message LuckyDrawActivityRequest{
    //活动id
    int64 id = 1;
    int64 page_size = 2;
    int64 page_index = 3;
    //是否积分抽奖
    int32 is_integral = 4;
}

message LuckyDrawActivityListRes{
    string msg = 1;
    int32 total = 2;
    repeated LuckyDrawActivityList data = 3;
}

message LuckyDrawActivityListResponse{
    repeated LuckyDrawActivityList data = 1;
}
message LuckyDrawActivityList{
    //自增id
    int64 id = 1;
    //类型 1 总计可抽 2 每天可抽
    int32 type = 2;
    //次数
    int32 number = 3;
    //活动开始时间
    string start_time =4;
    //结束时间
    string end_time = 5;
    //活动id
    string activity_id = 6;
    // 推荐积分商城时间
    string recommend_time = 7;
    // 是否积分抽奖
    int32 integral = 8;
}

message LuckyDrawActivityInfoResponse{
    LuckyDrawActivity data = 1;
}

message LuckyDrawActivity{
    //自增id
    int64 id = 1;
    //奖项默认背景色值
    string default_color = 2;
    //背景图
    string background_img = 3;
    //类型 1 总计可抽 2 每天可抽
    int32 type = 4;
    //次数
    int32 number = 5;
    //分享好友再抽一次 1开启 0关闭
    int32 is_share = 6;
    //奖品
    repeated LuckyDrawPrize prizes = 7;
    //点击抽奖按钮图像
    string lucky_draw_button_image = 8;
    //点击分享按钮
    string share_button_image = 9;
    //活动已结束切图
    string activity_end_image = 10;
    //次数已用完切图
    string number_run_out_image = 11;
    //客服微信二维码
    string service_code_url = 12;
    //活动开始时间
    string start_time =13;
    //结束时间
    string end_time = 14;
    //活动规则
    string activity_rule = 15;
    //奖项选中背景色值
    string select_color = 16;
    //分享按钮标题
    string share_button_title = 17;
    //分享按钮跳转路径
    string share_button_jump = 18;
    //分享出去的图片路径
    string share_image_url = 19;
    //抽奖按钮置灰图片
    string lucky_button_gray_img = 20;
    //是否允许积分抽奖 0 否1是
    int32 integral = 21;
    //每次消耗多少积分
    int32 integral_pay = 22;
}

message LuckyDrawPrize{
    int64 id = 1;
    //九宫格切图
    string prize_image_url = 2;
    //奖品类型 1 商城优惠券 2 门店券 3实物奖品 4 很遗憾 没中奖 5 跳转链接
    int32 prize_type = 3;
    //中奖图
    string winning_image_url = 4;
    //奖品数量
    int32 prizes_number = 5;
    //中奖概率
    int32 probability = 6;
    //券ID
    int32 coupon_id = 7;
    // 跳转链接地址
    string link_url = 8;
}

message WinningRecordReq {
    //用户ID（前端不用传）
    string scrm_user_id = 1;
    //活动ID，活动列表返回的ID
    int32 activity_id = 2;
    int32 page_size = 3;
    int32 page_index = 4;
}

message WinningRecordRes{
    string msg = 1;
    int32 total = 2;
    repeated WinningRecord data = 3;
}

message WinningRecord{
    int64 id = 1;
    //中奖图
    string winning_image_url = 2;
    //奖品类型 1 商城优惠券 2 门店券 3实物奖品 4 很遗憾 没中奖 5 跳转链接
    int32 prize_type = 3;
    //抽奖时间
    string prize_time = 4;
    //门店券ID
    int32 coupon_id = 5;
    //收件地址
    string address = 6;
    //中奖记录ID
    int32 lucky_draw_recode_id = 7;
    // 跳转链接
    string link_url = 8;
}

message LuckyDrawActivityInfoReq {
    int64 id = 1;
}

message LuckyDrawActivityInfoRes {
    string msg = 1;
    LuckyDrawActivity data = 2;
}

message MiniLuckDrawInfoResponse{
    string msg =1;
    LuckDrawInfo data = 2;
}

message LuckDrawInfo{
    //可抽数量
    int32 number = 1;
    //抽奖类型 1 总共可抽 2 每天可抽
    int32 number_type = 2;
    //分享可抽数量
    int32 share_number = 3;
    //是否可分享 1可以 2不可以
    int32 can_share = 4;
    //状态 1抽奖次数用完 2 活动结束
    int32 state = 5;
    //最大抽奖数
    int32 max_number = 6;
    //是否允许积分抽奖 0否 1是
    int32 integral = 7;
    //每次消耗多少积分
    int32 integral_pay = 8;
    // 活动的id
    string activity_id = 9;

}

message MiniLuckyDrawPrize{
    int64 id = 1;
    //九宫格切图
    string prize_image_url = 2;
    //奖品类型 1 商城优惠券 2 门店券 3实物奖品 4 很遗憾 没中奖
    int32 prize_type = 3;
}

message MiniLuckDrawStartResponse{
    string msg =1;
    WinningRecord data = 2;
}

message MiniLuckyDrawActivityResponse{
    string msg =1;
    MiniLuckyDrawActivity data = 2;
}

message MiniLuckyDrawActivity{
    string activity_id =1;
    //奖项默认背景色值
    string default_color = 2;
    //背景图
    string background_img = 3;
    //奖品
    repeated MiniLuckyDrawPrize prizes = 4;
    //点击抽奖按钮图像
    string lucky_draw_button_image = 5;
    //点击分享按钮
    string share_button_image = 6;
    //活动已结束切图
    string activity_end_image = 7;
    //次数已用完切图
    string number_run_out_image = 8;
    //客服微信二维码
    string service_code_url = 9;
    //活动开始时间
    string start_time =10;
    //结束时间
    string end_time = 11;
    //活动规则
    string activity_rule = 12;
    //奖项选中背景色值
    string select_color = 13;
    //主键ID
    int32 id = 14;
    //分享按钮标题
    string share_button_title = 15;
    //分享按钮跳转路径
    string share_button_jump = 16;
    //分享出去的图片路径
    string share_image_url = 17;
    string lucky_button_gray_img = 18;
    //是否允许积分抽奖 0否 1是
    int32 integral = 19;
    //每次消耗多少积分
    int32 integral_pay = 20;
}

message LuckyDrawAddressReq{
    //用户ID（前端不用传）
    string user_id = 1;
    //中奖记录ID（中奖记录列表返回的ID）
    int32 lucky_draw_recode_id = 2;
    //收件人
    string receiver = 3;
    //手机号
    string mobile = 4;
    //地址
    string address = 5;
}