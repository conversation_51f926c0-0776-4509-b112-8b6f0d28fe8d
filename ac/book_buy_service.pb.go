// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/book_buy_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//预售商品商品列表的请求数据
type GetBookBuyProductListRequest struct {
	//产品名称
	ProductName string `protobuf:"bytes,2,opt,name=productName,proto3" json:"productName"`
	//商品sku id
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	//商品的产品id
	ChannelId int32 `protobuf:"varint,5,opt,name=ChannelId,proto3" json:"ChannelId"`
	//商品状态 -1全部 -4审核失败，-3:待提交，-2:待审核/下架，1=>进行中，2=>已结束...
	Status int32 `protobuf:"varint,6,opt,name=Status,proto3" json:"Status"`
	//排序 0 按商品排序设置排序，1；按真实订单数排序 默认为0
	OrderBy int32 `protobuf:"varint,7,opt,name=orderBy,proto3" json:"orderBy"`
	//分页参数
	Pagination *PaginationParam `protobuf:"bytes,8,opt,name=pagination,proto3" json:"pagination"`
	//是否导出 1导出
	Export int32 `protobuf:"varint,9,opt,name=export,proto3" json:"export"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,10,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBookBuyProductListRequest) Reset()         { *m = GetBookBuyProductListRequest{} }
func (m *GetBookBuyProductListRequest) String() string { return proto.CompactTextString(m) }
func (*GetBookBuyProductListRequest) ProtoMessage()    {}
func (*GetBookBuyProductListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{0}
}

func (m *GetBookBuyProductListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBookBuyProductListRequest.Unmarshal(m, b)
}
func (m *GetBookBuyProductListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBookBuyProductListRequest.Marshal(b, m, deterministic)
}
func (m *GetBookBuyProductListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBookBuyProductListRequest.Merge(m, src)
}
func (m *GetBookBuyProductListRequest) XXX_Size() int {
	return xxx_messageInfo_GetBookBuyProductListRequest.Size(m)
}
func (m *GetBookBuyProductListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBookBuyProductListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBookBuyProductListRequest proto.InternalMessageInfo

func (m *GetBookBuyProductListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetBookBuyProductListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetBookBuyProductListRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetBookBuyProductListRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetBookBuyProductListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetBookBuyProductListRequest) GetOrderBy() int32 {
	if m != nil {
		return m.OrderBy
	}
	return 0
}

func (m *GetBookBuyProductListRequest) GetPagination() *PaginationParam {
	if m != nil {
		return m.Pagination
	}
	return nil
}

func (m *GetBookBuyProductListRequest) GetExport() int32 {
	if m != nil {
		return m.Export
	}
	return 0
}

func (m *GetBookBuyProductListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//预售商品商品列表
type GetBookBuyProductListResponse struct {
	//响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//预售商品商品信息
	Data                 []*BookBuyProductData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetBookBuyProductListResponse) Reset()         { *m = GetBookBuyProductListResponse{} }
func (m *GetBookBuyProductListResponse) String() string { return proto.CompactTextString(m) }
func (*GetBookBuyProductListResponse) ProtoMessage()    {}
func (*GetBookBuyProductListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{1}
}

func (m *GetBookBuyProductListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBookBuyProductListResponse.Unmarshal(m, b)
}
func (m *GetBookBuyProductListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBookBuyProductListResponse.Marshal(b, m, deterministic)
}
func (m *GetBookBuyProductListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBookBuyProductListResponse.Merge(m, src)
}
func (m *GetBookBuyProductListResponse) XXX_Size() int {
	return xxx_messageInfo_GetBookBuyProductListResponse.Size(m)
}
func (m *GetBookBuyProductListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBookBuyProductListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBookBuyProductListResponse proto.InternalMessageInfo

func (m *GetBookBuyProductListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetBookBuyProductListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetBookBuyProductListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetBookBuyProductListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetBookBuyProductListResponse) GetData() []*BookBuyProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

//预售商品商品数据
type BookBuyProductData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//渠道ID
	ChannelId int32 `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId"`
	//商品sku id
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	//商品名称
	ProductName string `protobuf:"bytes,5,opt,name=productName,proto3" json:"productName"`
	//单买价 单位分
	Price int32 `protobuf:"varint,6,opt,name=Price,proto3" json:"Price"`
	//预售价 单位分
	BookPrice int32 `protobuf:"varint,7,opt,name=bookPrice,proto3" json:"bookPrice"`
	//定金 单位分
	Deposit int32 `protobuf:"varint,8,opt,name=deposit,proto3" json:"deposit"`
	//定金开始时间
	DepositBeginDate string `protobuf:"bytes,9,opt,name=depositBeginDate,proto3" json:"depositBeginDate"`
	//定金结束时间
	DepositEndDate string `protobuf:"bytes,10,opt,name=depositEndDate,proto3" json:"depositEndDate"`
	//尾款开始时间
	RestBeginDate string `protobuf:"bytes,11,opt,name=restBeginDate,proto3" json:"restBeginDate"`
	//尾款结束时间
	RestEndDate string `protobuf:"bytes,12,opt,name=restEndDate,proto3" json:"restEndDate"`
	//状态 -1删除 0默认
	Status int32 `protobuf:"varint,13,opt,name=status,proto3" json:"status"`
	// 是否可被编辑 0 不可编辑 1 可编辑 用于boss后台
	CanBeEdited int32 `protobuf:"varint,14,opt,name=canBeEdited,proto3" json:"canBeEdited"`
	// 是否可被删除 0 不可删除 1 可删除 用于boss后台
	CanBeDeleted int32 `protobuf:"varint,15,opt,name=canBeDeleted,proto3" json:"canBeDeleted"`
	// 商品图片
	Pic string `protobuf:"bytes,16,opt,name=pic,proto3" json:"pic"`
	// 商品库存
	Stock int32 `protobuf:"varint,17,opt,name=stock,proto3" json:"stock"`
	// 是否是虚拟商品 1是 0 否
	IsVirtual int32 `protobuf:"varint,18,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,19,opt,name=goodsType,proto3" json:"goodsType"`
	//创建时间
	CreateTime string `protobuf:"bytes,20,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime string `protobuf:"bytes,21,opt,name=updateTime,proto3" json:"updateTime"`
	//是否免邮费 0否1是
	IsShippingFree int32 `protobuf:"varint,22,opt,name=is_shipping_free,json=isShippingFree,proto3" json:"is_shipping_free"`
	// 虚拟库存，如果没开启会返回-，否则就是具体的库存
	VirtualStorage string `protobuf:"bytes,23,opt,name=virtual_storage,json=virtualStorage,proto3" json:"virtual_storage"`
	//商品折扣率
	PriceRatio float64 `protobuf:"fixed64,24,opt,name=priceRatio,proto3" json:"priceRatio"`
	// 是否异常 1:异常 0：正常
	IsNormal int32 `protobuf:"varint,25,opt,name=is_normal,json=isNormal,proto3" json:"is_normal"`
	// 是否标记 1:是 0：否
	IsMark int32 `protobuf:"varint,26,opt,name=is_mark,json=isMark,proto3" json:"is_mark"`
	// 采购价格
	PurchasePrice int32 `protobuf:"varint,27,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price"`
	// 标记为正常的异常折扣
	MarkDiscount float64 `protobuf:"fixed64,28,opt,name=MarkDiscount,proto3" json:"MarkDiscount"`
	// 标记为正常的采购价(分)
	MarkPurchasePrice int32 `protobuf:"varint,29,opt,name=MarkPurchasePrice,proto3" json:"MarkPurchasePrice"`
	// 审核原因
	CheckReason          string   `protobuf:"bytes,30,opt,name=check_reason,json=checkReason,proto3" json:"check_reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BookBuyProductData) Reset()         { *m = BookBuyProductData{} }
func (m *BookBuyProductData) String() string { return proto.CompactTextString(m) }
func (*BookBuyProductData) ProtoMessage()    {}
func (*BookBuyProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{2}
}

func (m *BookBuyProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BookBuyProductData.Unmarshal(m, b)
}
func (m *BookBuyProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BookBuyProductData.Marshal(b, m, deterministic)
}
func (m *BookBuyProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BookBuyProductData.Merge(m, src)
}
func (m *BookBuyProductData) XXX_Size() int {
	return xxx_messageInfo_BookBuyProductData.Size(m)
}
func (m *BookBuyProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_BookBuyProductData.DiscardUnknown(m)
}

var xxx_messageInfo_BookBuyProductData proto.InternalMessageInfo

func (m *BookBuyProductData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BookBuyProductData) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BookBuyProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *BookBuyProductData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *BookBuyProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *BookBuyProductData) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *BookBuyProductData) GetBookPrice() int32 {
	if m != nil {
		return m.BookPrice
	}
	return 0
}

func (m *BookBuyProductData) GetDeposit() int32 {
	if m != nil {
		return m.Deposit
	}
	return 0
}

func (m *BookBuyProductData) GetDepositBeginDate() string {
	if m != nil {
		return m.DepositBeginDate
	}
	return ""
}

func (m *BookBuyProductData) GetDepositEndDate() string {
	if m != nil {
		return m.DepositEndDate
	}
	return ""
}

func (m *BookBuyProductData) GetRestBeginDate() string {
	if m != nil {
		return m.RestBeginDate
	}
	return ""
}

func (m *BookBuyProductData) GetRestEndDate() string {
	if m != nil {
		return m.RestEndDate
	}
	return ""
}

func (m *BookBuyProductData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *BookBuyProductData) GetCanBeEdited() int32 {
	if m != nil {
		return m.CanBeEdited
	}
	return 0
}

func (m *BookBuyProductData) GetCanBeDeleted() int32 {
	if m != nil {
		return m.CanBeDeleted
	}
	return 0
}

func (m *BookBuyProductData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *BookBuyProductData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *BookBuyProductData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *BookBuyProductData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *BookBuyProductData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *BookBuyProductData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *BookBuyProductData) GetIsShippingFree() int32 {
	if m != nil {
		return m.IsShippingFree
	}
	return 0
}

func (m *BookBuyProductData) GetVirtualStorage() string {
	if m != nil {
		return m.VirtualStorage
	}
	return ""
}

func (m *BookBuyProductData) GetPriceRatio() float64 {
	if m != nil {
		return m.PriceRatio
	}
	return 0
}

func (m *BookBuyProductData) GetIsNormal() int32 {
	if m != nil {
		return m.IsNormal
	}
	return 0
}

func (m *BookBuyProductData) GetIsMark() int32 {
	if m != nil {
		return m.IsMark
	}
	return 0
}

func (m *BookBuyProductData) GetPurchasePrice() int32 {
	if m != nil {
		return m.PurchasePrice
	}
	return 0
}

func (m *BookBuyProductData) GetMarkDiscount() float64 {
	if m != nil {
		return m.MarkDiscount
	}
	return 0
}

func (m *BookBuyProductData) GetMarkPurchasePrice() int32 {
	if m != nil {
		return m.MarkPurchasePrice
	}
	return 0
}

func (m *BookBuyProductData) GetCheckReason() string {
	if m != nil {
		return m.CheckReason
	}
	return ""
}

//新增预售商品活动商品
type CreateBookBuyProductRequest struct {
	//商品sku id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,2,opt,name=productId,proto3" json:"productId"`
	//用户Id，即userno
	UserId string `protobuf:"bytes,3,opt,name=userId,proto3" json:"userId"`
	//用户名称，即登录人姓名
	UserName string `protobuf:"bytes,4,opt,name=userName,proto3" json:"userName"`
	//主体信息
	SaveData *SaveBookBuyProductData `protobuf:"bytes,5,opt,name=saveData,proto3" json:"saveData"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateBookBuyProductRequest) Reset()         { *m = CreateBookBuyProductRequest{} }
func (m *CreateBookBuyProductRequest) String() string { return proto.CompactTextString(m) }
func (*CreateBookBuyProductRequest) ProtoMessage()    {}
func (*CreateBookBuyProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{3}
}

func (m *CreateBookBuyProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateBookBuyProductRequest.Unmarshal(m, b)
}
func (m *CreateBookBuyProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateBookBuyProductRequest.Marshal(b, m, deterministic)
}
func (m *CreateBookBuyProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateBookBuyProductRequest.Merge(m, src)
}
func (m *CreateBookBuyProductRequest) XXX_Size() int {
	return xxx_messageInfo_CreateBookBuyProductRequest.Size(m)
}
func (m *CreateBookBuyProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateBookBuyProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateBookBuyProductRequest proto.InternalMessageInfo

func (m *CreateBookBuyProductRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CreateBookBuyProductRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *CreateBookBuyProductRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CreateBookBuyProductRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *CreateBookBuyProductRequest) GetSaveData() *SaveBookBuyProductData {
	if m != nil {
		return m.SaveData
	}
	return nil
}

func (m *CreateBookBuyProductRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//添加/编辑预售商品商品
type SaveBookBuyProductData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//预售价 单位分
	BookPrice int32 `protobuf:"varint,7,opt,name=bookPrice,proto3" json:"bookPrice"`
	//定金 单位分
	Deposit int32 `protobuf:"varint,8,opt,name=deposit,proto3" json:"deposit"`
	//定金开始时间
	DepositBeginDate string `protobuf:"bytes,9,opt,name=depositBeginDate,proto3" json:"depositBeginDate"`
	//定金结束时间
	DepositEndDate string `protobuf:"bytes,10,opt,name=depositEndDate,proto3" json:"depositEndDate"`
	//尾款开始时间
	RestBeginDate string `protobuf:"bytes,11,opt,name=restBeginDate,proto3" json:"restBeginDate"`
	//尾款结束时间
	RestEndDate string `protobuf:"bytes,12,opt,name=restEndDate,proto3" json:"restEndDate"`
	// 是否包邮
	IsShippingFree       int32    `protobuf:"varint,13,opt,name=isShippingFree,proto3" json:"isShippingFree"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveBookBuyProductData) Reset()         { *m = SaveBookBuyProductData{} }
func (m *SaveBookBuyProductData) String() string { return proto.CompactTextString(m) }
func (*SaveBookBuyProductData) ProtoMessage()    {}
func (*SaveBookBuyProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{4}
}

func (m *SaveBookBuyProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveBookBuyProductData.Unmarshal(m, b)
}
func (m *SaveBookBuyProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveBookBuyProductData.Marshal(b, m, deterministic)
}
func (m *SaveBookBuyProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveBookBuyProductData.Merge(m, src)
}
func (m *SaveBookBuyProductData) XXX_Size() int {
	return xxx_messageInfo_SaveBookBuyProductData.Size(m)
}
func (m *SaveBookBuyProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveBookBuyProductData.DiscardUnknown(m)
}

var xxx_messageInfo_SaveBookBuyProductData proto.InternalMessageInfo

func (m *SaveBookBuyProductData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SaveBookBuyProductData) GetBookPrice() int32 {
	if m != nil {
		return m.BookPrice
	}
	return 0
}

func (m *SaveBookBuyProductData) GetDeposit() int32 {
	if m != nil {
		return m.Deposit
	}
	return 0
}

func (m *SaveBookBuyProductData) GetDepositBeginDate() string {
	if m != nil {
		return m.DepositBeginDate
	}
	return ""
}

func (m *SaveBookBuyProductData) GetDepositEndDate() string {
	if m != nil {
		return m.DepositEndDate
	}
	return ""
}

func (m *SaveBookBuyProductData) GetRestBeginDate() string {
	if m != nil {
		return m.RestBeginDate
	}
	return ""
}

func (m *SaveBookBuyProductData) GetRestEndDate() string {
	if m != nil {
		return m.RestEndDate
	}
	return ""
}

func (m *SaveBookBuyProductData) GetIsShippingFree() int32 {
	if m != nil {
		return m.IsShippingFree
	}
	return 0
}

// 更新预售商品商品
type UpdateBookBuyProductRequest struct {
	//需要更新的记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品sku id
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,3,opt,name=productId,proto3" json:"productId"`
	//用户Id，即userno
	UserId string `protobuf:"bytes,4,opt,name=userId,proto3" json:"userId"`
	//用户名称，即登录人姓名
	UserName string `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName"`
	//主体信息
	SaveData *SaveBookBuyProductData `protobuf:"bytes,6,opt,name=saveData,proto3" json:"saveData"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBookBuyProductRequest) Reset()         { *m = UpdateBookBuyProductRequest{} }
func (m *UpdateBookBuyProductRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateBookBuyProductRequest) ProtoMessage()    {}
func (*UpdateBookBuyProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{5}
}

func (m *UpdateBookBuyProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBookBuyProductRequest.Unmarshal(m, b)
}
func (m *UpdateBookBuyProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBookBuyProductRequest.Marshal(b, m, deterministic)
}
func (m *UpdateBookBuyProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBookBuyProductRequest.Merge(m, src)
}
func (m *UpdateBookBuyProductRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateBookBuyProductRequest.Size(m)
}
func (m *UpdateBookBuyProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBookBuyProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBookBuyProductRequest proto.InternalMessageInfo

func (m *UpdateBookBuyProductRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateBookBuyProductRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *UpdateBookBuyProductRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UpdateBookBuyProductRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *UpdateBookBuyProductRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *UpdateBookBuyProductRequest) GetSaveData() *SaveBookBuyProductData {
	if m != nil {
		return m.SaveData
	}
	return nil
}

func (m *UpdateBookBuyProductRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// 预售商品商品只需要id的请求
type BookBuyProductDetailRequest struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品skuId
	SkuId int32 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,4,opt,name=productId,proto3" json:"productId"`
	//渠道 1电商 5商城
	ChannelId int32 `protobuf:"varint,5,opt,name=channelId,proto3" json:"channelId"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BookBuyProductDetailRequest) Reset()         { *m = BookBuyProductDetailRequest{} }
func (m *BookBuyProductDetailRequest) String() string { return proto.CompactTextString(m) }
func (*BookBuyProductDetailRequest) ProtoMessage()    {}
func (*BookBuyProductDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{6}
}

func (m *BookBuyProductDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BookBuyProductDetailRequest.Unmarshal(m, b)
}
func (m *BookBuyProductDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BookBuyProductDetailRequest.Marshal(b, m, deterministic)
}
func (m *BookBuyProductDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BookBuyProductDetailRequest.Merge(m, src)
}
func (m *BookBuyProductDetailRequest) XXX_Size() int {
	return xxx_messageInfo_BookBuyProductDetailRequest.Size(m)
}
func (m *BookBuyProductDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BookBuyProductDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BookBuyProductDetailRequest proto.InternalMessageInfo

func (m *BookBuyProductDetailRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BookBuyProductDetailRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *BookBuyProductDetailRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *BookBuyProductDetailRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BookBuyProductDetailRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//获取预售商品商品信息
type GetBookBuyProductDetailResponse struct {
	//响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//预售商品商品信息
	Data                 *BookBuyProductDetailData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetBookBuyProductDetailResponse) Reset()         { *m = GetBookBuyProductDetailResponse{} }
func (m *GetBookBuyProductDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetBookBuyProductDetailResponse) ProtoMessage()    {}
func (*GetBookBuyProductDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{7}
}

func (m *GetBookBuyProductDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBookBuyProductDetailResponse.Unmarshal(m, b)
}
func (m *GetBookBuyProductDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBookBuyProductDetailResponse.Marshal(b, m, deterministic)
}
func (m *GetBookBuyProductDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBookBuyProductDetailResponse.Merge(m, src)
}
func (m *GetBookBuyProductDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetBookBuyProductDetailResponse.Size(m)
}
func (m *GetBookBuyProductDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBookBuyProductDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBookBuyProductDetailResponse proto.InternalMessageInfo

func (m *GetBookBuyProductDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetBookBuyProductDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetBookBuyProductDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetBookBuyProductDetailResponse) GetData() *BookBuyProductDetailData {
	if m != nil {
		return m.Data
	}
	return nil
}

//预售商品商品详细数据包含部分预售商品信息
type BookBuyProductDetailData struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//渠道ID
	ChannelId int32 `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	//商品sku id
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	//商品名称
	ProductName string `protobuf:"bytes,6,opt,name=productName,proto3" json:"productName"`
	//预售价 单位分
	BookPrice int32 `protobuf:"varint,7,opt,name=bookPrice,proto3" json:"bookPrice"`
	//定金 单位分
	Deposit int32 `protobuf:"varint,8,opt,name=deposit,proto3" json:"deposit"`
	//定金开始时间
	DepositBeginDate string `protobuf:"bytes,9,opt,name=depositBeginDate,proto3" json:"depositBeginDate"`
	//定金结束时间
	DepositEndDate string `protobuf:"bytes,10,opt,name=depositEndDate,proto3" json:"depositEndDate"`
	//尾款开始时间
	RestBeginDate string `protobuf:"bytes,11,opt,name=restBeginDate,proto3" json:"restBeginDate"`
	//尾款结束时间
	RestEndDate string `protobuf:"bytes,12,opt,name=restEndDate,proto3" json:"restEndDate"`
	//状态 -1删除 0默认
	Status int32 `protobuf:"varint,13,opt,name=status,proto3" json:"status"`
	//创建时间
	CreateTime string `protobuf:"bytes,14,opt,name=createTime,proto3" json:"createTime"`
	//更新时间
	UpdateTime string `protobuf:"bytes,15,opt,name=updateTime,proto3" json:"updateTime"`
	// 是否是虚拟商品 1是 0 否
	IsVirtual int32 `protobuf:"varint,16,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,17,opt,name=goodsType,proto3" json:"goodsType"`
	//R1集采价
	R1PurchasePrice int32 `protobuf:"varint,18,opt,name=R1PurchasePrice,proto3" json:"R1PurchasePrice"`
	// 折扣率
	PriceRatio           float64  `protobuf:"fixed64,19,opt,name=priceRatio,proto3" json:"priceRatio"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BookBuyProductDetailData) Reset()         { *m = BookBuyProductDetailData{} }
func (m *BookBuyProductDetailData) String() string { return proto.CompactTextString(m) }
func (*BookBuyProductDetailData) ProtoMessage()    {}
func (*BookBuyProductDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{8}
}

func (m *BookBuyProductDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BookBuyProductDetailData.Unmarshal(m, b)
}
func (m *BookBuyProductDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BookBuyProductDetailData.Marshal(b, m, deterministic)
}
func (m *BookBuyProductDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BookBuyProductDetailData.Merge(m, src)
}
func (m *BookBuyProductDetailData) XXX_Size() int {
	return xxx_messageInfo_BookBuyProductDetailData.Size(m)
}
func (m *BookBuyProductDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_BookBuyProductDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_BookBuyProductDetailData proto.InternalMessageInfo

func (m *BookBuyProductDetailData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BookBuyProductDetailData) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BookBuyProductDetailData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *BookBuyProductDetailData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *BookBuyProductDetailData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *BookBuyProductDetailData) GetBookPrice() int32 {
	if m != nil {
		return m.BookPrice
	}
	return 0
}

func (m *BookBuyProductDetailData) GetDeposit() int32 {
	if m != nil {
		return m.Deposit
	}
	return 0
}

func (m *BookBuyProductDetailData) GetDepositBeginDate() string {
	if m != nil {
		return m.DepositBeginDate
	}
	return ""
}

func (m *BookBuyProductDetailData) GetDepositEndDate() string {
	if m != nil {
		return m.DepositEndDate
	}
	return ""
}

func (m *BookBuyProductDetailData) GetRestBeginDate() string {
	if m != nil {
		return m.RestBeginDate
	}
	return ""
}

func (m *BookBuyProductDetailData) GetRestEndDate() string {
	if m != nil {
		return m.RestEndDate
	}
	return ""
}

func (m *BookBuyProductDetailData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *BookBuyProductDetailData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *BookBuyProductDetailData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *BookBuyProductDetailData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *BookBuyProductDetailData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *BookBuyProductDetailData) GetR1PurchasePrice() int32 {
	if m != nil {
		return m.R1PurchasePrice
	}
	return 0
}

func (m *BookBuyProductDetailData) GetPriceRatio() float64 {
	if m != nil {
		return m.PriceRatio
	}
	return 0
}

//预售商品商品只需要id的请求
type BookBuyProductIdRequest struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//用户Id，即userno
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId"`
	//用户名称，即登录人姓名
	UserName string `protobuf:"bytes,3,opt,name=userName,proto3" json:"userName"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BookBuyProductIdRequest) Reset()         { *m = BookBuyProductIdRequest{} }
func (m *BookBuyProductIdRequest) String() string { return proto.CompactTextString(m) }
func (*BookBuyProductIdRequest) ProtoMessage()    {}
func (*BookBuyProductIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{9}
}

func (m *BookBuyProductIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BookBuyProductIdRequest.Unmarshal(m, b)
}
func (m *BookBuyProductIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BookBuyProductIdRequest.Marshal(b, m, deterministic)
}
func (m *BookBuyProductIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BookBuyProductIdRequest.Merge(m, src)
}
func (m *BookBuyProductIdRequest) XXX_Size() int {
	return xxx_messageInfo_BookBuyProductIdRequest.Size(m)
}
func (m *BookBuyProductIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BookBuyProductIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BookBuyProductIdRequest proto.InternalMessageInfo

func (m *BookBuyProductIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BookBuyProductIdRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *BookBuyProductIdRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *BookBuyProductIdRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//阿闻电商参加预售商品活动的商品
type GetBookBuyUPetProductSelectListRequest struct {
	//商品sku_id 对应商城的goods_id
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	// 商品的产品id 对应商城的goods_commonid
	ProductId int32 `protobuf:"varint,3,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	ProductName string `protobuf:"bytes,4,opt,name=productName,proto3" json:"productName"`
	//分页参数
	Pagination *PaginationParam `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,6,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBookBuyUPetProductSelectListRequest) Reset() {
	*m = GetBookBuyUPetProductSelectListRequest{}
}
func (m *GetBookBuyUPetProductSelectListRequest) String() string { return proto.CompactTextString(m) }
func (*GetBookBuyUPetProductSelectListRequest) ProtoMessage()    {}
func (*GetBookBuyUPetProductSelectListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{10}
}

func (m *GetBookBuyUPetProductSelectListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBookBuyUPetProductSelectListRequest.Unmarshal(m, b)
}
func (m *GetBookBuyUPetProductSelectListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBookBuyUPetProductSelectListRequest.Marshal(b, m, deterministic)
}
func (m *GetBookBuyUPetProductSelectListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBookBuyUPetProductSelectListRequest.Merge(m, src)
}
func (m *GetBookBuyUPetProductSelectListRequest) XXX_Size() int {
	return xxx_messageInfo_GetBookBuyUPetProductSelectListRequest.Size(m)
}
func (m *GetBookBuyUPetProductSelectListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBookBuyUPetProductSelectListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBookBuyUPetProductSelectListRequest proto.InternalMessageInfo

func (m *GetBookBuyUPetProductSelectListRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetBookBuyUPetProductSelectListRequest) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetBookBuyUPetProductSelectListRequest) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GetBookBuyUPetProductSelectListRequest) GetPagination() *PaginationParam {
	if m != nil {
		return m.Pagination
	}
	return nil
}

func (m *GetBookBuyUPetProductSelectListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//阿闻电商参加预售商品活动的商品
type GetBookBuyUPetProductSelectListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//预售商品商品信息
	Data                 []*BookBuySelectUPetProductData `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetBookBuyUPetProductSelectListResponse) Reset() {
	*m = GetBookBuyUPetProductSelectListResponse{}
}
func (m *GetBookBuyUPetProductSelectListResponse) String() string { return proto.CompactTextString(m) }
func (*GetBookBuyUPetProductSelectListResponse) ProtoMessage()    {}
func (*GetBookBuyUPetProductSelectListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{11}
}

func (m *GetBookBuyUPetProductSelectListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBookBuyUPetProductSelectListResponse.Unmarshal(m, b)
}
func (m *GetBookBuyUPetProductSelectListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBookBuyUPetProductSelectListResponse.Marshal(b, m, deterministic)
}
func (m *GetBookBuyUPetProductSelectListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBookBuyUPetProductSelectListResponse.Merge(m, src)
}
func (m *GetBookBuyUPetProductSelectListResponse) XXX_Size() int {
	return xxx_messageInfo_GetBookBuyUPetProductSelectListResponse.Size(m)
}
func (m *GetBookBuyUPetProductSelectListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBookBuyUPetProductSelectListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBookBuyUPetProductSelectListResponse proto.InternalMessageInfo

func (m *GetBookBuyUPetProductSelectListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetBookBuyUPetProductSelectListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetBookBuyUPetProductSelectListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetBookBuyUPetProductSelectListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetBookBuyUPetProductSelectListResponse) GetData() []*BookBuySelectUPetProductData {
	if m != nil {
		return m.Data
	}
	return nil
}

type BookBuySelectUPetProductData struct {
	//商品sku_id 对应商城的goods_id
	SkuId int32 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	// 商品的产品id 对应商城的goods_commonid
	ProductId int32 `protobuf:"varint,2,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	ProductName string `protobuf:"bytes,3,opt,name=productName,proto3" json:"productName"`
	// 是否与其他活动有时间上的冲突，0表示没有冲突，1表示有冲突，该冲突基于当前活动的起止时间与其他活动进行比较
	TimeConflict int32 `protobuf:"varint,4,opt,name=timeConflict,proto3" json:"timeConflict"`
	//商品图片
	Pic string `protobuf:"bytes,5,opt,name=pic,proto3" json:"pic"`
	//库存
	Stock int32 `protobuf:"varint,6,opt,name=stock,proto3" json:"stock"`
	//价格 单位分
	MarketPrice int32 `protobuf:"varint,7,opt,name=marketPrice,proto3" json:"marketPrice"`
	// 采购价、分
	PurchasePrice int32 `protobuf:"varint,11,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price"`
	//是否时虚拟产品 1是 0 否
	IsVirtual int32 `protobuf:"varint,8,opt,name=isVirtual,proto3" json:"isVirtual"`
	//商品类型 0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,9,opt,name=goodsType,proto3" json:"goodsType"`
	//组合商品的子商品信息
	ChildSkuIds          []*Child `protobuf:"bytes,10,rep,name=childSkuIds,proto3" json:"childSkuIds"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BookBuySelectUPetProductData) Reset()         { *m = BookBuySelectUPetProductData{} }
func (m *BookBuySelectUPetProductData) String() string { return proto.CompactTextString(m) }
func (*BookBuySelectUPetProductData) ProtoMessage()    {}
func (*BookBuySelectUPetProductData) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{12}
}

func (m *BookBuySelectUPetProductData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BookBuySelectUPetProductData.Unmarshal(m, b)
}
func (m *BookBuySelectUPetProductData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BookBuySelectUPetProductData.Marshal(b, m, deterministic)
}
func (m *BookBuySelectUPetProductData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BookBuySelectUPetProductData.Merge(m, src)
}
func (m *BookBuySelectUPetProductData) XXX_Size() int {
	return xxx_messageInfo_BookBuySelectUPetProductData.Size(m)
}
func (m *BookBuySelectUPetProductData) XXX_DiscardUnknown() {
	xxx_messageInfo_BookBuySelectUPetProductData.DiscardUnknown(m)
}

var xxx_messageInfo_BookBuySelectUPetProductData proto.InternalMessageInfo

func (m *BookBuySelectUPetProductData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *BookBuySelectUPetProductData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *BookBuySelectUPetProductData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *BookBuySelectUPetProductData) GetTimeConflict() int32 {
	if m != nil {
		return m.TimeConflict
	}
	return 0
}

func (m *BookBuySelectUPetProductData) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *BookBuySelectUPetProductData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *BookBuySelectUPetProductData) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *BookBuySelectUPetProductData) GetPurchasePrice() int32 {
	if m != nil {
		return m.PurchasePrice
	}
	return 0
}

func (m *BookBuySelectUPetProductData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *BookBuySelectUPetProductData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *BookBuySelectUPetProductData) GetChildSkuIds() []*Child {
	if m != nil {
		return m.ChildSkuIds
	}
	return nil
}

//团购商品列表
type BookBuyProductCustomListResponse struct {
	// 响应代码 200 成功 非 200 失败查看 message
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 不成功的错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总的条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//预售商品信息
	Data                 []*BookBuyProductCustomList `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BookBuyProductCustomListResponse) Reset()         { *m = BookBuyProductCustomListResponse{} }
func (m *BookBuyProductCustomListResponse) String() string { return proto.CompactTextString(m) }
func (*BookBuyProductCustomListResponse) ProtoMessage()    {}
func (*BookBuyProductCustomListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{13}
}

func (m *BookBuyProductCustomListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BookBuyProductCustomListResponse.Unmarshal(m, b)
}
func (m *BookBuyProductCustomListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BookBuyProductCustomListResponse.Marshal(b, m, deterministic)
}
func (m *BookBuyProductCustomListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BookBuyProductCustomListResponse.Merge(m, src)
}
func (m *BookBuyProductCustomListResponse) XXX_Size() int {
	return xxx_messageInfo_BookBuyProductCustomListResponse.Size(m)
}
func (m *BookBuyProductCustomListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BookBuyProductCustomListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BookBuyProductCustomListResponse proto.InternalMessageInfo

func (m *BookBuyProductCustomListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BookBuyProductCustomListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BookBuyProductCustomListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *BookBuyProductCustomListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *BookBuyProductCustomListResponse) GetData() []*BookBuyProductCustomList {
	if m != nil {
		return m.Data
	}
	return nil
}

//预售商品列表客户端返回数据
type BookBuyProductCustomList struct {
	//活动商品信息记录id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 预售价 单位分
	BookPrice int32 `protobuf:"varint,3,opt,name=bookPrice,proto3" json:"bookPrice"`
	//商品sku id
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//商品的产品id
	ProductId int32 `protobuf:"varint,5,opt,name=productId,proto3" json:"productId"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,6,opt,name=channelId,proto3" json:"channelId"`
	// 市场价格 单位分
	MarketPrice int32 `protobuf:"varint,7,opt,name=marketPrice,proto3" json:"marketPrice"`
	// 商品图片
	Pic string `protobuf:"bytes,8,opt,name=pic,proto3" json:"pic"`
	//商品名称
	ProductName          string   `protobuf:"bytes,9,opt,name=productName,proto3" json:"productName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BookBuyProductCustomList) Reset()         { *m = BookBuyProductCustomList{} }
func (m *BookBuyProductCustomList) String() string { return proto.CompactTextString(m) }
func (*BookBuyProductCustomList) ProtoMessage()    {}
func (*BookBuyProductCustomList) Descriptor() ([]byte, []int) {
	return fileDescriptor_e97cc5e6d418f676, []int{14}
}

func (m *BookBuyProductCustomList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BookBuyProductCustomList.Unmarshal(m, b)
}
func (m *BookBuyProductCustomList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BookBuyProductCustomList.Marshal(b, m, deterministic)
}
func (m *BookBuyProductCustomList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BookBuyProductCustomList.Merge(m, src)
}
func (m *BookBuyProductCustomList) XXX_Size() int {
	return xxx_messageInfo_BookBuyProductCustomList.Size(m)
}
func (m *BookBuyProductCustomList) XXX_DiscardUnknown() {
	xxx_messageInfo_BookBuyProductCustomList.DiscardUnknown(m)
}

var xxx_messageInfo_BookBuyProductCustomList proto.InternalMessageInfo

func (m *BookBuyProductCustomList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BookBuyProductCustomList) GetBookPrice() int32 {
	if m != nil {
		return m.BookPrice
	}
	return 0
}

func (m *BookBuyProductCustomList) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *BookBuyProductCustomList) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *BookBuyProductCustomList) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BookBuyProductCustomList) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *BookBuyProductCustomList) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *BookBuyProductCustomList) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func init() {
	proto.RegisterType((*GetBookBuyProductListRequest)(nil), "ac.GetBookBuyProductListRequest")
	proto.RegisterType((*GetBookBuyProductListResponse)(nil), "ac.GetBookBuyProductListResponse")
	proto.RegisterType((*BookBuyProductData)(nil), "ac.BookBuyProductData")
	proto.RegisterType((*CreateBookBuyProductRequest)(nil), "ac.CreateBookBuyProductRequest")
	proto.RegisterType((*SaveBookBuyProductData)(nil), "ac.SaveBookBuyProductData")
	proto.RegisterType((*UpdateBookBuyProductRequest)(nil), "ac.UpdateBookBuyProductRequest")
	proto.RegisterType((*BookBuyProductDetailRequest)(nil), "ac.BookBuyProductDetailRequest")
	proto.RegisterType((*GetBookBuyProductDetailResponse)(nil), "ac.GetBookBuyProductDetailResponse")
	proto.RegisterType((*BookBuyProductDetailData)(nil), "ac.BookBuyProductDetailData")
	proto.RegisterType((*BookBuyProductIdRequest)(nil), "ac.BookBuyProductIdRequest")
	proto.RegisterType((*GetBookBuyUPetProductSelectListRequest)(nil), "ac.GetBookBuyUPetProductSelectListRequest")
	proto.RegisterType((*GetBookBuyUPetProductSelectListResponse)(nil), "ac.GetBookBuyUPetProductSelectListResponse")
	proto.RegisterType((*BookBuySelectUPetProductData)(nil), "ac.BookBuySelectUPetProductData")
	proto.RegisterType((*BookBuyProductCustomListResponse)(nil), "ac.BookBuyProductCustomListResponse")
	proto.RegisterType((*BookBuyProductCustomList)(nil), "ac.BookBuyProductCustomList")
}

func init() { proto.RegisterFile("ac/book_buy_service.proto", fileDescriptor_e97cc5e6d418f676) }

var fileDescriptor_e97cc5e6d418f676 = []byte{
	// 1377 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x58, 0xdd, 0x6e, 0x1b, 0x45,
	0x14, 0xd6, 0xfa, 0x2f, 0xf1, 0x71, 0xea, 0xa4, 0xd3, 0x34, 0x99, 0x3a, 0x69, 0xeb, 0x9a, 0xd2,
	0x5a, 0x2d, 0x6a, 0x4b, 0x8b, 0x78, 0x80, 0x24, 0x2d, 0x8a, 0x04, 0x55, 0xb4, 0x69, 0xb9, 0x42,
	0xb2, 0x26, 0xb3, 0x53, 0x67, 0x64, 0x7b, 0x67, 0x99, 0x19, 0x47, 0x84, 0xc7, 0xe0, 0x02, 0x5e,
	0x00, 0x24, 0xae, 0xb8, 0xe7, 0x11, 0xe0, 0x8a, 0x0b, 0x1e, 0x81, 0x07, 0x40, 0x3c, 0x01, 0x9a,
	0x99, 0xb5, 0xbd, 0xbb, 0xde, 0xdd, 0x94, 0x08, 0xb8, 0x80, 0xbb, 0x3d, 0xdf, 0x39, 0x3b, 0x3f,
	0xe7, 0xef, 0x3b, 0xbb, 0x70, 0x83, 0xd0, 0xc7, 0x27, 0x42, 0x8c, 0x06, 0x27, 0xd3, 0xf3, 0x81,
	0x62, 0xf2, 0x8c, 0x53, 0xf6, 0x28, 0x92, 0x42, 0x0b, 0x54, 0x21, 0xb4, 0xb3, 0x4d, 0xe8, 0x63,
	0x42, 0x35, 0x3f, 0xe3, 0xfa, 0x7c, 0x30, 0x11, 0x01, 0x1b, 0x3b, 0x65, 0xa7, 0x43, 0xe8, 0x63,
	0x7a, 0x4e, 0xc7, 0x6c, 0xf9, 0xc5, 0xde, 0xf7, 0x15, 0xd8, 0xfd, 0x88, 0xe9, 0x3d, 0x21, 0x46,
	0x7b, 0xd3, 0xf3, 0x23, 0x29, 0x82, 0x29, 0xd5, 0x1f, 0x73, 0xa5, 0x7d, 0xf6, 0xf9, 0x94, 0x29,
	0x8d, 0xba, 0xd0, 0x8a, 0x1c, 0xfa, 0x92, 0x4c, 0x18, 0xae, 0x74, 0xbd, 0x7e, 0xd3, 0x4f, 0x42,
	0x68, 0x13, 0xea, 0x6a, 0x34, 0x3d, 0x0c, 0x70, 0xb5, 0xeb, 0xf5, 0xeb, 0xbe, 0x13, 0xd0, 0x2e,
	0x34, 0x63, 0xa3, 0xc3, 0x00, 0xd7, 0xac, 0x66, 0x01, 0x18, 0xed, 0xfe, 0x29, 0x09, 0x43, 0x36,
	0x3e, 0x0c, 0x70, 0xdd, 0x69, 0xe7, 0x00, 0xda, 0x82, 0xc6, 0xb1, 0x26, 0x7a, 0xaa, 0x70, 0xc3,
	0xaa, 0x62, 0x09, 0x61, 0x58, 0x11, 0x32, 0x60, 0x72, 0xef, 0x1c, 0xaf, 0x58, 0xc5, 0x4c, 0x44,
	0xcf, 0x00, 0x22, 0x32, 0xe4, 0x21, 0xd1, 0x5c, 0x84, 0x78, 0xb5, 0xeb, 0xf5, 0x5b, 0x4f, 0xaf,
	0x3d, 0x22, 0xf4, 0xd1, 0xd1, 0x1c, 0x3d, 0x22, 0x92, 0x4c, 0xfc, 0x84, 0x99, 0xd9, 0x86, 0x7d,
	0x11, 0x09, 0xa9, 0x71, 0xd3, 0x6d, 0xe3, 0x24, 0x74, 0x1d, 0x1a, 0x42, 0x0e, 0x07, 0x3c, 0xc0,
	0xe0, 0x6e, 0x24, 0xe4, 0xf0, 0x30, 0xe8, 0x7d, 0xe7, 0xc1, 0xcd, 0x02, 0x57, 0xa9, 0x48, 0x84,
	0x8a, 0x21, 0x04, 0x35, 0x2a, 0x02, 0x86, 0x3d, 0xfb, 0x9a, 0x7d, 0x36, 0x67, 0x9e, 0x30, 0xa5,
	0xc8, 0x70, 0xe6, 0xbb, 0x99, 0x68, 0xfc, 0xc6, 0xa4, 0x14, 0xd2, 0xfa, 0xad, 0xe9, 0x3b, 0xc1,
	0xa0, 0x5a, 0x68, 0x32, 0x8e, 0x7d, 0xe6, 0x04, 0xf4, 0x00, 0x6a, 0x01, 0xd1, 0x04, 0xd7, 0xbb,
	0xd5, 0x7e, 0xeb, 0xe9, 0x96, 0xb9, 0x59, 0xfa, 0x1c, 0x07, 0x44, 0x13, 0xdf, 0xda, 0xf4, 0x7e,
	0x5a, 0x01, 0xb4, 0xac, 0x44, 0x6d, 0xa8, 0xf0, 0x20, 0x3e, 0x5a, 0x85, 0xdb, 0x10, 0xd0, 0x79,
	0x08, 0x2a, 0x2e, 0x04, 0x73, 0xe0, 0x52, 0x41, 0xcd, 0xa4, 0x4a, 0x3d, 0x37, 0x55, 0x8e, 0x24,
	0xa7, 0x2c, 0x8e, 0xab, 0x13, 0xcc, 0xaa, 0x26, 0xad, 0x9d, 0xc6, 0x05, 0x76, 0x01, 0x18, 0x07,
	0x06, 0x2c, 0x12, 0x8a, 0x6b, 0x1b, 0xd7, 0xba, 0x3f, 0x13, 0xd1, 0x03, 0xd8, 0x88, 0x1f, 0xf7,
	0xd8, 0x90, 0x87, 0x07, 0x44, 0x33, 0x1b, 0xc9, 0xa6, 0xbf, 0x84, 0xa3, 0x7b, 0xd0, 0x8e, 0xb1,
	0xe7, 0x61, 0x60, 0x2d, 0xc1, 0x5a, 0x66, 0x50, 0x74, 0x17, 0xae, 0x48, 0xa6, 0x12, 0x0b, 0xb6,
	0xac, 0x59, 0x1a, 0x34, 0x37, 0x35, 0xc0, 0x6c, 0xa9, 0x35, 0x77, 0xd3, 0x04, 0x64, 0x72, 0x4b,
	0xb9, 0x14, 0xbe, 0xe2, 0x72, 0xcb, 0x49, 0xe6, 0x4d, 0x4a, 0xc2, 0x3d, 0xf6, 0x3c, 0xe0, 0x9a,
	0x05, 0xb8, 0x6d, 0x95, 0x49, 0x08, 0xf5, 0x60, 0xcd, 0x8a, 0x07, 0x6c, 0xcc, 0x8c, 0xc9, 0xba,
	0x35, 0x49, 0x61, 0x68, 0x03, 0xaa, 0x11, 0xa7, 0x78, 0xc3, 0xee, 0x6b, 0x1e, 0x6d, 0xbc, 0xb4,
	0xa0, 0x23, 0x7c, 0x35, 0x8e, 0x97, 0x11, 0x8c, 0x67, 0xb9, 0xfa, 0x94, 0x4b, 0x3d, 0x25, 0x63,
	0x8c, 0x9c, 0x67, 0xe7, 0x80, 0xd1, 0x0e, 0x85, 0x08, 0xd4, 0xab, 0xf3, 0x88, 0xe1, 0x6b, 0x4e,
	0x3b, 0x07, 0xd0, 0x2d, 0x00, 0x2a, 0x19, 0xd1, 0xec, 0x15, 0x9f, 0x30, 0xbc, 0x69, 0xb7, 0x4a,
	0x20, 0x46, 0x3f, 0x8d, 0x82, 0x99, 0xfe, 0xba, 0xd3, 0x2f, 0x10, 0xd4, 0x87, 0x0d, 0xae, 0x06,
	0xea, 0x94, 0x47, 0x11, 0x0f, 0x87, 0x83, 0x37, 0x92, 0x31, 0xbc, 0x65, 0x37, 0x69, 0x73, 0x75,
	0x1c, 0xc3, 0x2f, 0x24, 0x63, 0xe8, 0x3e, 0xac, 0x9f, 0xb9, 0x23, 0x0d, 0x94, 0x16, 0xd2, 0x94,
	0xca, 0xb6, 0x0b, 0x4e, 0x0c, 0x1f, 0x3b, 0xd4, 0x6c, 0x19, 0x99, 0x9c, 0xf0, 0x4d, 0xfd, 0x62,
	0xdc, 0xf5, 0xfa, 0x9e, 0x9f, 0x40, 0xd0, 0x8e, 0xb9, 0xee, 0x20, 0x14, 0x72, 0x42, 0xc6, 0xf8,
	0x86, 0xdd, 0x6b, 0x95, 0xab, 0x97, 0x56, 0x46, 0xdb, 0xb0, 0xc2, 0xd5, 0x60, 0x42, 0xe4, 0x08,
	0x77, 0x5c, 0x48, 0xb8, 0xfa, 0x84, 0xc8, 0x11, 0x7a, 0x17, 0xda, 0xd1, 0x54, 0xd2, 0x53, 0xa2,
	0xd8, 0xc0, 0x2e, 0x86, 0x77, 0xac, 0xfe, 0xca, 0x0c, 0x75, 0x79, 0xd8, 0x83, 0x35, 0x63, 0x7e,
	0xc0, 0x15, 0x15, 0xd3, 0x50, 0xe3, 0x5d, 0xbb, 0x7d, 0x0a, 0x43, 0xef, 0xc1, 0x55, 0x23, 0x1f,
	0x25, 0x5f, 0xc4, 0x37, 0xed, 0x6a, 0xcb, 0x0a, 0x74, 0x07, 0xd6, 0xe8, 0x29, 0xa3, 0xa3, 0x81,
	0x64, 0x44, 0x89, 0x10, 0xdf, 0x72, 0x69, 0x64, 0x31, 0xdf, 0x42, 0xbd, 0x5f, 0x3d, 0xd8, 0xd9,
	0xb7, 0x3e, 0x4f, 0x57, 0xf4, 0xac, 0x3b, 0xcf, 0xcb, 0xd4, 0x2b, 0x2c, 0xd3, 0x4a, 0xb6, 0x4c,
	0xb7, 0xa0, 0x31, 0x55, 0x4c, 0xc6, 0xb5, 0xdd, 0xf4, 0x63, 0x09, 0x75, 0x60, 0xd5, 0x3c, 0xd9,
	0xda, 0xad, 0x59, 0xcd, 0x5c, 0x46, 0x1f, 0xc2, 0xaa, 0x22, 0x67, 0xec, 0xc0, 0xf5, 0x20, 0xd3,
	0x5d, 0x3b, 0xa6, 0x07, 0x1d, 0x93, 0x33, 0x96, 0xd3, 0x87, 0xe6, 0xb6, 0x89, 0x56, 0xda, 0x48,
	0xb6, 0xd2, 0x6f, 0x2b, 0xb0, 0x95, 0xff, 0x6e, 0x5e, 0x9b, 0xfa, 0xcf, 0x35, 0x87, 0x7b, 0x90,
	0x29, 0x81, 0xb8, 0x49, 0x64, 0xd0, 0xde, 0x6f, 0x1e, 0xec, 0xbc, 0xb6, 0x15, 0x95, 0x1f, 0xfd,
	0xac, 0xaf, 0xe6, 0xd9, 0x50, 0x29, 0xcc, 0x86, 0x6a, 0x71, 0x36, 0xd4, 0x0a, 0xb3, 0xa1, 0x5e,
	0x92, 0x0d, 0x8d, 0x4b, 0x65, 0xc3, 0x4a, 0x32, 0x1b, 0xbe, 0xf6, 0x60, 0x27, 0xf3, 0x1e, 0xd3,
	0x84, 0x8f, 0x2f, 0xbc, 0xe6, 0x5f, 0x1b, 0x38, 0x68, 0x76, 0xe0, 0x58, 0xb0, 0x5d, 0x41, 0x9a,
	0x7e, 0xe3, 0xc1, 0xed, 0x25, 0xc6, 0x9f, 0x9d, 0xed, 0x6f, 0xe4, 0xfc, 0x27, 0x31, 0xbb, 0xd7,
	0xac, 0x2f, 0x77, 0x73, 0xd8, 0xdd, 0xee, 0x99, 0xe0, 0xf8, 0x5f, 0x6a, 0x80, 0x8b, 0x4c, 0xca,
	0x99, 0xbe, 0x5a, 0xc8, 0xf4, 0xb5, 0x42, 0x6f, 0xd6, 0x2f, 0x60, 0xfa, 0xc6, 0x32, 0xd3, 0xff,
	0x9f, 0x38, 0x3d, 0xcd, 0x94, 0xed, 0x0b, 0x98, 0x72, 0x7d, 0x89, 0x29, 0x53, 0x2c, 0xbd, 0x51,
	0xca, 0xd2, 0x57, 0xb3, 0x2c, 0xdd, 0x87, 0x75, 0xff, 0xfd, 0x34, 0xdf, 0x38, 0x9e, 0xcf, 0xc2,
	0x19, 0xf2, 0xbc, 0x96, 0x25, 0xcf, 0x9e, 0x86, 0xed, 0x74, 0x46, 0x1d, 0x06, 0x45, 0x05, 0xb8,
	0xe8, 0x19, 0x95, 0xc2, 0x9e, 0x51, 0xcd, 0xf4, 0x8c, 0x45, 0x89, 0xd5, 0x92, 0x25, 0xf6, 0xb3,
	0x07, 0xf7, 0x16, 0x25, 0xf6, 0xfa, 0x88, 0xe9, 0x78, 0xf7, 0x63, 0x36, 0x66, 0xe9, 0x2f, 0x91,
	0xcb, 0x74, 0xb7, 0x4c, 0xa2, 0xd6, 0x96, 0x13, 0x35, 0xfd, 0xe5, 0x50, 0x7f, 0xbb, 0x2f, 0x87,
	0x82, 0x7e, 0xf1, 0xa3, 0x07, 0xf7, 0x2f, 0xbc, 0xcc, 0x3f, 0xfe, 0xad, 0xf0, 0x41, 0xea, 0x5b,
	0xa1, 0x9b, 0xe8, 0x26, 0xee, 0x18, 0x89, 0x73, 0x25, 0x3a, 0xca, 0x1f, 0x15, 0xd8, 0x2d, 0x33,
	0xbb, 0xd4, 0xa8, 0x91, 0x71, 0x7f, 0x75, 0xd9, 0xfd, 0x3d, 0x58, 0xd3, 0x7c, 0xc2, 0xf6, 0x45,
	0xf8, 0x66, 0xcc, 0xa9, 0x8e, 0x6f, 0x92, 0xc2, 0x66, 0xd3, 0x6e, 0x3d, 0x67, 0xda, 0x6d, 0x24,
	0xa7, 0xdd, 0x2e, 0xb4, 0xcc, 0x78, 0x67, 0x8e, 0xbd, 0xe8, 0x3a, 0x49, 0x28, 0x67, 0xd4, 0x6b,
	0xe5, 0x8d, 0x7a, 0xa9, 0x82, 0x5c, 0x2d, 0x2d, 0xc8, 0x66, 0xb6, 0x20, 0x1f, 0x42, 0x8b, 0x9e,
	0xf2, 0x71, 0x70, 0x6c, 0xdc, 0xa3, 0x30, 0xd8, 0x20, 0x34, 0x4d, 0x10, 0xf6, 0x0d, 0xec, 0x27,
	0xb5, 0xbd, 0x1f, 0x3c, 0xe8, 0xa6, 0x8b, 0x6e, 0x7f, 0xaa, 0xb4, 0x98, 0xfc, 0x4b, 0x99, 0xf2,
	0x24, 0x95, 0x29, 0x39, 0xbc, 0x93, 0x38, 0x8d, 0xcb, 0x92, 0xdf, 0xbd, 0x2c, 0xef, 0x2c, 0x4c,
	0xca, 0x47, 0xb7, 0x6a, 0x96, 0x03, 0x2e, 0xc3, 0x3b, 0x29, 0x26, 0x6b, 0x64, 0x99, 0xec, 0xe2,
	0xf8, 0xc7, 0x99, 0xb4, 0xba, 0xc8, 0xa4, 0x4c, 0x86, 0x36, 0x97, 0x32, 0xf4, 0xe9, 0x57, 0x75,
	0x68, 0xcf, 0x0b, 0xc3, 0xfe, 0x3a, 0x41, 0x9f, 0xc1, 0xf5, 0xdc, 0x1f, 0x01, 0xc8, 0x16, 0x5b,
	0xd9, 0xef, 0x94, 0xce, 0x9d, 0x12, 0x8b, 0x38, 0xde, 0x87, 0xb0, 0x99, 0x37, 0xf2, 0xa3, 0xdb,
	0x36, 0x89, 0x8a, 0x3f, 0x06, 0x3a, 0x1b, 0xc6, 0xe0, 0x84, 0x28, 0x96, 0x5c, 0x2a, 0x6f, 0x7e,
	0x74, 0x4b, 0x95, 0x4c, 0x96, 0x39, 0x4b, 0x0d, 0x60, 0xbb, 0x60, 0x14, 0x72, 0xab, 0x95, 0x0c,
	0x70, 0x9d, 0x77, 0x72, 0x2f, 0x9d, 0x19, 0xa4, 0x9e, 0xc3, 0xa6, 0xfb, 0xbc, 0xcd, 0x9c, 0x75,
	0x67, 0x79, 0xf5, 0x39, 0x33, 0xe5, 0x9c, 0xf3, 0xcb, 0xe4, 0xc8, 0x96, 0xdb, 0x82, 0xd1, 0x83,
	0xf4, 0x71, 0xca, 0x48, 0xa7, 0xf3, 0xf0, 0xad, 0x6c, 0xe3, 0xbd, 0x4f, 0xe1, 0x56, 0x6e, 0x68,
	0x5f, 0x08, 0xe9, 0xca, 0xe4, 0x2d, 0x12, 0xe4, 0x6e, 0x69, 0x15, 0xc6, 0x3b, 0x9d, 0x34, 0xec,
	0xdf, 0xbb, 0x67, 0x7f, 0x06, 0x00, 0x00, 0xff, 0xff, 0xd6, 0x8c, 0xd4, 0xcb, 0x13, 0x14, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BookBuyServiceClient is the client API for BookBuyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BookBuyServiceClient interface {
	//获取预售商品商品列表 boss
	GetBookBuyProductList(ctx context.Context, in *GetBookBuyProductListRequest, opts ...grpc.CallOption) (*GetBookBuyProductListResponse, error)
	//创建预售商品商品
	CreateBookBuyProduct(ctx context.Context, in *CreateBookBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//更新预售商品商品
	UpdateBookBuyProduct(ctx context.Context, in *UpdateBookBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取预售商品商品详情
	GetBookBuyProductDetail(ctx context.Context, in *BookBuyProductDetailRequest, opts ...grpc.CallOption) (*GetBookBuyProductDetailResponse, error)
	//删除预售商品商品
	DeleteBookBuyProduct(ctx context.Context, in *BookBuyProductIdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取可以参加预售商品活动的阿闻电商渠道的商品
	GetBookBuyUPetProductSelectList(ctx context.Context, in *GetBookBuyUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetBookBuyUPetProductSelectListResponse, error)
	// 获取预售商品列表 小程序
	GetBookBuyProductListForCustom(ctx context.Context, in *GetBookBuyProductListRequest, opts ...grpc.CallOption) (*BookBuyProductCustomListResponse, error)
}

type bookBuyServiceClient struct {
	cc *grpc.ClientConn
}

func NewBookBuyServiceClient(cc *grpc.ClientConn) BookBuyServiceClient {
	return &bookBuyServiceClient{cc}
}

func (c *bookBuyServiceClient) GetBookBuyProductList(ctx context.Context, in *GetBookBuyProductListRequest, opts ...grpc.CallOption) (*GetBookBuyProductListResponse, error) {
	out := new(GetBookBuyProductListResponse)
	err := c.cc.Invoke(ctx, "/ac.BookBuyService/GetBookBuyProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookBuyServiceClient) CreateBookBuyProduct(ctx context.Context, in *CreateBookBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.BookBuyService/CreateBookBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookBuyServiceClient) UpdateBookBuyProduct(ctx context.Context, in *UpdateBookBuyProductRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.BookBuyService/UpdateBookBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookBuyServiceClient) GetBookBuyProductDetail(ctx context.Context, in *BookBuyProductDetailRequest, opts ...grpc.CallOption) (*GetBookBuyProductDetailResponse, error) {
	out := new(GetBookBuyProductDetailResponse)
	err := c.cc.Invoke(ctx, "/ac.BookBuyService/GetBookBuyProductDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookBuyServiceClient) DeleteBookBuyProduct(ctx context.Context, in *BookBuyProductIdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ac.BookBuyService/DeleteBookBuyProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookBuyServiceClient) GetBookBuyUPetProductSelectList(ctx context.Context, in *GetBookBuyUPetProductSelectListRequest, opts ...grpc.CallOption) (*GetBookBuyUPetProductSelectListResponse, error) {
	out := new(GetBookBuyUPetProductSelectListResponse)
	err := c.cc.Invoke(ctx, "/ac.BookBuyService/GetBookBuyUPetProductSelectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookBuyServiceClient) GetBookBuyProductListForCustom(ctx context.Context, in *GetBookBuyProductListRequest, opts ...grpc.CallOption) (*BookBuyProductCustomListResponse, error) {
	out := new(BookBuyProductCustomListResponse)
	err := c.cc.Invoke(ctx, "/ac.BookBuyService/GetBookBuyProductListForCustom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookBuyServiceServer is the server API for BookBuyService service.
type BookBuyServiceServer interface {
	//获取预售商品商品列表 boss
	GetBookBuyProductList(context.Context, *GetBookBuyProductListRequest) (*GetBookBuyProductListResponse, error)
	//创建预售商品商品
	CreateBookBuyProduct(context.Context, *CreateBookBuyProductRequest) (*BaseResponse, error)
	//更新预售商品商品
	UpdateBookBuyProduct(context.Context, *UpdateBookBuyProductRequest) (*BaseResponse, error)
	//获取预售商品商品详情
	GetBookBuyProductDetail(context.Context, *BookBuyProductDetailRequest) (*GetBookBuyProductDetailResponse, error)
	//删除预售商品商品
	DeleteBookBuyProduct(context.Context, *BookBuyProductIdRequest) (*BaseResponse, error)
	// 获取可以参加预售商品活动的阿闻电商渠道的商品
	GetBookBuyUPetProductSelectList(context.Context, *GetBookBuyUPetProductSelectListRequest) (*GetBookBuyUPetProductSelectListResponse, error)
	// 获取预售商品列表 小程序
	GetBookBuyProductListForCustom(context.Context, *GetBookBuyProductListRequest) (*BookBuyProductCustomListResponse, error)
}

// UnimplementedBookBuyServiceServer can be embedded to have forward compatible implementations.
type UnimplementedBookBuyServiceServer struct {
}

func (*UnimplementedBookBuyServiceServer) GetBookBuyProductList(ctx context.Context, req *GetBookBuyProductListRequest) (*GetBookBuyProductListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookBuyProductList not implemented")
}
func (*UnimplementedBookBuyServiceServer) CreateBookBuyProduct(ctx context.Context, req *CreateBookBuyProductRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBookBuyProduct not implemented")
}
func (*UnimplementedBookBuyServiceServer) UpdateBookBuyProduct(ctx context.Context, req *UpdateBookBuyProductRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBookBuyProduct not implemented")
}
func (*UnimplementedBookBuyServiceServer) GetBookBuyProductDetail(ctx context.Context, req *BookBuyProductDetailRequest) (*GetBookBuyProductDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookBuyProductDetail not implemented")
}
func (*UnimplementedBookBuyServiceServer) DeleteBookBuyProduct(ctx context.Context, req *BookBuyProductIdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBookBuyProduct not implemented")
}
func (*UnimplementedBookBuyServiceServer) GetBookBuyUPetProductSelectList(ctx context.Context, req *GetBookBuyUPetProductSelectListRequest) (*GetBookBuyUPetProductSelectListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookBuyUPetProductSelectList not implemented")
}
func (*UnimplementedBookBuyServiceServer) GetBookBuyProductListForCustom(ctx context.Context, req *GetBookBuyProductListRequest) (*BookBuyProductCustomListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookBuyProductListForCustom not implemented")
}

func RegisterBookBuyServiceServer(s *grpc.Server, srv BookBuyServiceServer) {
	s.RegisterService(&_BookBuyService_serviceDesc, srv)
}

func _BookBuyService_GetBookBuyProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookBuyProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookBuyServiceServer).GetBookBuyProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BookBuyService/GetBookBuyProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookBuyServiceServer).GetBookBuyProductList(ctx, req.(*GetBookBuyProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookBuyService_CreateBookBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBookBuyProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookBuyServiceServer).CreateBookBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BookBuyService/CreateBookBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookBuyServiceServer).CreateBookBuyProduct(ctx, req.(*CreateBookBuyProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookBuyService_UpdateBookBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBookBuyProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookBuyServiceServer).UpdateBookBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BookBuyService/UpdateBookBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookBuyServiceServer).UpdateBookBuyProduct(ctx, req.(*UpdateBookBuyProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookBuyService_GetBookBuyProductDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookBuyProductDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookBuyServiceServer).GetBookBuyProductDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BookBuyService/GetBookBuyProductDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookBuyServiceServer).GetBookBuyProductDetail(ctx, req.(*BookBuyProductDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookBuyService_DeleteBookBuyProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookBuyProductIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookBuyServiceServer).DeleteBookBuyProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BookBuyService/DeleteBookBuyProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookBuyServiceServer).DeleteBookBuyProduct(ctx, req.(*BookBuyProductIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookBuyService_GetBookBuyUPetProductSelectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookBuyUPetProductSelectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookBuyServiceServer).GetBookBuyUPetProductSelectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BookBuyService/GetBookBuyUPetProductSelectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookBuyServiceServer).GetBookBuyUPetProductSelectList(ctx, req.(*GetBookBuyUPetProductSelectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookBuyService_GetBookBuyProductListForCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookBuyProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookBuyServiceServer).GetBookBuyProductListForCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.BookBuyService/GetBookBuyProductListForCustom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookBuyServiceServer).GetBookBuyProductListForCustom(ctx, req.(*GetBookBuyProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BookBuyService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.BookBuyService",
	HandlerType: (*BookBuyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBookBuyProductList",
			Handler:    _BookBuyService_GetBookBuyProductList_Handler,
		},
		{
			MethodName: "CreateBookBuyProduct",
			Handler:    _BookBuyService_CreateBookBuyProduct_Handler,
		},
		{
			MethodName: "UpdateBookBuyProduct",
			Handler:    _BookBuyService_UpdateBookBuyProduct_Handler,
		},
		{
			MethodName: "GetBookBuyProductDetail",
			Handler:    _BookBuyService_GetBookBuyProductDetail_Handler,
		},
		{
			MethodName: "DeleteBookBuyProduct",
			Handler:    _BookBuyService_DeleteBookBuyProduct_Handler,
		},
		{
			MethodName: "GetBookBuyUPetProductSelectList",
			Handler:    _BookBuyService_GetBookBuyUPetProductSelectList_Handler,
		},
		{
			MethodName: "GetBookBuyProductListForCustom",
			Handler:    _BookBuyService_GetBookBuyProductListForCustom_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/book_buy_service.proto",
}
