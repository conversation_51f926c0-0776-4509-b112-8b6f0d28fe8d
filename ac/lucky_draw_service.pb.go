// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ac/lucky_draw_service.proto

package ac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("ac/lucky_draw_service.proto", fileDescriptor_b9ff6b22eff729b2) }

var fileDescriptor_b9ff6b22eff729b2 = []byte{
	// 329 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x93, 0x4f, 0x4f, 0xfa, 0x30,
	0x18, 0xc7, 0x93, 0xdf, 0x81, 0x43, 0x93, 0x5f, 0x82, 0x45, 0x02, 0xa9, 0x78, 0xe0, 0xe2, 0x11,
	0x12, 0xbd, 0xab, 0xf8, 0x27, 0x51, 0x83, 0x1e, 0xc6, 0x81, 0xe3, 0x52, 0xdb, 0x47, 0x6c, 0x64,
	0x2d, 0xb4, 0x85, 0x85, 0xf7, 0xe2, 0x8b, 0x35, 0x5d, 0xd9, 0xdc, 0x5c, 0x19, 0xd7, 0xef, 0xe7,
	0xfb, 0x7c, 0x28, 0xed, 0x33, 0x74, 0x46, 0xd9, 0x78, 0xb9, 0x61, 0x5f, 0xbb, 0x98, 0x6b, 0x9a,
	0xc6, 0x06, 0xf4, 0x56, 0x30, 0x18, 0xad, 0xb4, 0xb2, 0x0a, 0xff, 0xa3, 0x8c, 0x74, 0x2a, 0x05,
	0x0f, 0x48, 0x8f, 0xb2, 0x31, 0x65, 0x56, 0x6c, 0x85, 0xdd, 0xc5, 0x89, 0xe2, 0xb0, 0xf4, 0xe0,
	0xf2, 0xbb, 0x85, 0xda, 0x53, 0xd7, 0x7e, 0xd0, 0x34, 0x9d, 0x79, 0x19, 0x9e, 0xa1, 0x6e, 0x91,
	0x4d, 0xf6, 0x53, 0x53, 0x61, 0x2c, 0x1e, 0x8c, 0x28, 0x1b, 0xd5, 0x50, 0x04, 0xeb, 0x0d, 0x18,
	0x4b, 0xc2, 0xd4, 0x0d, 0x46, 0x60, 0x82, 0xd2, 0x67, 0xf9, 0xa1, 0x0e, 0x48, 0x1d, 0x8a, 0x60,
	0x4d, 0x9a, 0xa8, 0xc1, 0xf7, 0xa8, 0xfb, 0xc8, 0x85, 0xad, 0x71, 0xdc, 0x0d, 0x8e, 0x91, 0x8e,
	0x8b, 0xef, 0xa8, 0x81, 0x08, 0xcc, 0x4a, 0x49, 0x03, 0x6f, 0x90, 0xe2, 0x39, 0xea, 0xbf, 0x0a,
	0x29, 0x5c, 0xbb, 0x76, 0xb8, 0xbe, 0x1b, 0xc8, 0x69, 0xe6, 0xca, 0xff, 0xed, 0xb0, 0x46, 0x7e,
	0xef, 0xc3, 0xbb, 0xf1, 0x0b, 0x6a, 0x97, 0xc5, 0x47, 0x84, 0x83, 0x32, 0xc9, 0xfb, 0x85, 0xeb,
	0x09, 0xfd, 0xcf, 0xf3, 0x99, 0xa5, 0xda, 0x36, 0x88, 0xce, 0xff, 0x8a, 0xb2, 0x81, 0xc2, 0x74,
	0x5d, 0x32, 0x7d, 0x52, 0x0d, 0x0d, 0xa6, 0xe0, 0x75, 0xdd, 0xa0, 0x93, 0xb9, 0x90, 0x52, 0xc8,
	0x45, 0x04, 0x4c, 0x69, 0x9e, 0x6d, 0xc6, 0xa9, 0x6b, 0x56, 0x62, 0xf7, 0x78, 0xa1, 0xd4, 0xe0,
	0xdb, 0xd2, 0xca, 0x4d, 0x38, 0xd7, 0x60, 0x0c, 0xee, 0x55, 0xdf, 0xcb, 0xa7, 0x4e, 0x11, 0x3c,
	0xc2, 0x02, 0x91, 0xc0, 0xad, 0x33, 0x95, 0x24, 0x20, 0x39, 0x1e, 0x1e, 0xd8, 0xd2, 0x3d, 0x77,
	0xd6, 0x8b, 0x63, 0x15, 0xff, 0x5b, 0xef, 0xad, 0xec, 0x2b, 0xb9, 0xfa, 0x09, 0x00, 0x00, 0xff,
	0xff, 0x78, 0x21, 0xae, 0x91, 0x76, 0x03, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// LuckyDrawServiceClient is the client API for LuckyDrawService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LuckyDrawServiceClient interface {
	//抽奖组件-活动列表
	LuckyDrawActivityList(ctx context.Context, in *LuckyDrawActivityRequest, opts ...grpc.CallOption) (*LuckyDrawActivityListRes, error)
	//抽奖组件-活动详情
	LuckyDrawActivityInfo(ctx context.Context, in *LuckyDrawActivityInfoReq, opts ...grpc.CallOption) (*LuckyDrawActivityInfoRes, error)
	//抽奖组件-编辑或新增活动
	EditLuckyDrawActivity(ctx context.Context, in *LuckyDrawActivity, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//抽奖组件-小程序获取抽奖活动内容
	MiniLuckDrawActivityInfo(ctx context.Context, in *MiniLuckyDrawRequest, opts ...grpc.CallOption) (*MiniLuckyDrawActivityResponse, error)
	//抽奖组件-小程序获取用户抽奖情况
	MiniLuckDrawInfo(ctx context.Context, in *MiniLuckyDrawRequest, opts ...grpc.CallOption) (*MiniLuckDrawInfoResponse, error)
	//抽奖组件-抽奖
	LuckDrawStart(ctx context.Context, in *MiniLuckyDrawRequest, opts ...grpc.CallOption) (*MiniLuckDrawStartResponse, error)
	//抽奖组件-分享
	LuckDrawShare(ctx context.Context, in *MiniLuckyDrawRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//抽奖组件-抽奖记录
	WinningRecordList(ctx context.Context, in *WinningRecordReq, opts ...grpc.CallOption) (*WinningRecordRes, error)
	//抽奖组件-收货地址
	LuckyDrawAddress(ctx context.Context, in *LuckyDrawAddressReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//抽奖组件-推荐或者取消到积分商城
	LuckyDrawActivityRecommend(ctx context.Context, in *LuckyDrawActivityRecommendReq, opts ...grpc.CallOption) (*LuckyDrawActivityRecommendResponse, error)
}

type luckyDrawServiceClient struct {
	cc *grpc.ClientConn
}

func NewLuckyDrawServiceClient(cc *grpc.ClientConn) LuckyDrawServiceClient {
	return &luckyDrawServiceClient{cc}
}

func (c *luckyDrawServiceClient) LuckyDrawActivityList(ctx context.Context, in *LuckyDrawActivityRequest, opts ...grpc.CallOption) (*LuckyDrawActivityListRes, error) {
	out := new(LuckyDrawActivityListRes)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/LuckyDrawActivityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luckyDrawServiceClient) LuckyDrawActivityInfo(ctx context.Context, in *LuckyDrawActivityInfoReq, opts ...grpc.CallOption) (*LuckyDrawActivityInfoRes, error) {
	out := new(LuckyDrawActivityInfoRes)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/LuckyDrawActivityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luckyDrawServiceClient) EditLuckyDrawActivity(ctx context.Context, in *LuckyDrawActivity, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/EditLuckyDrawActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luckyDrawServiceClient) MiniLuckDrawActivityInfo(ctx context.Context, in *MiniLuckyDrawRequest, opts ...grpc.CallOption) (*MiniLuckyDrawActivityResponse, error) {
	out := new(MiniLuckyDrawActivityResponse)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/MiniLuckDrawActivityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luckyDrawServiceClient) MiniLuckDrawInfo(ctx context.Context, in *MiniLuckyDrawRequest, opts ...grpc.CallOption) (*MiniLuckDrawInfoResponse, error) {
	out := new(MiniLuckDrawInfoResponse)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/MiniLuckDrawInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luckyDrawServiceClient) LuckDrawStart(ctx context.Context, in *MiniLuckyDrawRequest, opts ...grpc.CallOption) (*MiniLuckDrawStartResponse, error) {
	out := new(MiniLuckDrawStartResponse)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/LuckDrawStart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luckyDrawServiceClient) LuckDrawShare(ctx context.Context, in *MiniLuckyDrawRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/LuckDrawShare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luckyDrawServiceClient) WinningRecordList(ctx context.Context, in *WinningRecordReq, opts ...grpc.CallOption) (*WinningRecordRes, error) {
	out := new(WinningRecordRes)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/WinningRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luckyDrawServiceClient) LuckyDrawAddress(ctx context.Context, in *LuckyDrawAddressReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/LuckyDrawAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luckyDrawServiceClient) LuckyDrawActivityRecommend(ctx context.Context, in *LuckyDrawActivityRecommendReq, opts ...grpc.CallOption) (*LuckyDrawActivityRecommendResponse, error) {
	out := new(LuckyDrawActivityRecommendResponse)
	err := c.cc.Invoke(ctx, "/ac.LuckyDrawService/LuckyDrawActivityRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LuckyDrawServiceServer is the server API for LuckyDrawService service.
type LuckyDrawServiceServer interface {
	//抽奖组件-活动列表
	LuckyDrawActivityList(context.Context, *LuckyDrawActivityRequest) (*LuckyDrawActivityListRes, error)
	//抽奖组件-活动详情
	LuckyDrawActivityInfo(context.Context, *LuckyDrawActivityInfoReq) (*LuckyDrawActivityInfoRes, error)
	//抽奖组件-编辑或新增活动
	EditLuckyDrawActivity(context.Context, *LuckyDrawActivity) (*BaseResponseNew, error)
	//抽奖组件-小程序获取抽奖活动内容
	MiniLuckDrawActivityInfo(context.Context, *MiniLuckyDrawRequest) (*MiniLuckyDrawActivityResponse, error)
	//抽奖组件-小程序获取用户抽奖情况
	MiniLuckDrawInfo(context.Context, *MiniLuckyDrawRequest) (*MiniLuckDrawInfoResponse, error)
	//抽奖组件-抽奖
	LuckDrawStart(context.Context, *MiniLuckyDrawRequest) (*MiniLuckDrawStartResponse, error)
	//抽奖组件-分享
	LuckDrawShare(context.Context, *MiniLuckyDrawRequest) (*BaseResponseNew, error)
	//抽奖组件-抽奖记录
	WinningRecordList(context.Context, *WinningRecordReq) (*WinningRecordRes, error)
	//抽奖组件-收货地址
	LuckyDrawAddress(context.Context, *LuckyDrawAddressReq) (*BaseResponseNew, error)
	//抽奖组件-推荐或者取消到积分商城
	LuckyDrawActivityRecommend(context.Context, *LuckyDrawActivityRecommendReq) (*LuckyDrawActivityRecommendResponse, error)
}

// UnimplementedLuckyDrawServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLuckyDrawServiceServer struct {
}

func (*UnimplementedLuckyDrawServiceServer) LuckyDrawActivityList(ctx context.Context, req *LuckyDrawActivityRequest) (*LuckyDrawActivityListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuckyDrawActivityList not implemented")
}
func (*UnimplementedLuckyDrawServiceServer) LuckyDrawActivityInfo(ctx context.Context, req *LuckyDrawActivityInfoReq) (*LuckyDrawActivityInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuckyDrawActivityInfo not implemented")
}
func (*UnimplementedLuckyDrawServiceServer) EditLuckyDrawActivity(ctx context.Context, req *LuckyDrawActivity) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditLuckyDrawActivity not implemented")
}
func (*UnimplementedLuckyDrawServiceServer) MiniLuckDrawActivityInfo(ctx context.Context, req *MiniLuckyDrawRequest) (*MiniLuckyDrawActivityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MiniLuckDrawActivityInfo not implemented")
}
func (*UnimplementedLuckyDrawServiceServer) MiniLuckDrawInfo(ctx context.Context, req *MiniLuckyDrawRequest) (*MiniLuckDrawInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MiniLuckDrawInfo not implemented")
}
func (*UnimplementedLuckyDrawServiceServer) LuckDrawStart(ctx context.Context, req *MiniLuckyDrawRequest) (*MiniLuckDrawStartResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuckDrawStart not implemented")
}
func (*UnimplementedLuckyDrawServiceServer) LuckDrawShare(ctx context.Context, req *MiniLuckyDrawRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuckDrawShare not implemented")
}
func (*UnimplementedLuckyDrawServiceServer) WinningRecordList(ctx context.Context, req *WinningRecordReq) (*WinningRecordRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WinningRecordList not implemented")
}
func (*UnimplementedLuckyDrawServiceServer) LuckyDrawAddress(ctx context.Context, req *LuckyDrawAddressReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuckyDrawAddress not implemented")
}
func (*UnimplementedLuckyDrawServiceServer) LuckyDrawActivityRecommend(ctx context.Context, req *LuckyDrawActivityRecommendReq) (*LuckyDrawActivityRecommendResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuckyDrawActivityRecommend not implemented")
}

func RegisterLuckyDrawServiceServer(s *grpc.Server, srv LuckyDrawServiceServer) {
	s.RegisterService(&_LuckyDrawService_serviceDesc, srv)
}

func _LuckyDrawService_LuckyDrawActivityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuckyDrawActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).LuckyDrawActivityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/LuckyDrawActivityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).LuckyDrawActivityList(ctx, req.(*LuckyDrawActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuckyDrawService_LuckyDrawActivityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuckyDrawActivityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).LuckyDrawActivityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/LuckyDrawActivityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).LuckyDrawActivityInfo(ctx, req.(*LuckyDrawActivityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuckyDrawService_EditLuckyDrawActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuckyDrawActivity)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).EditLuckyDrawActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/EditLuckyDrawActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).EditLuckyDrawActivity(ctx, req.(*LuckyDrawActivity))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuckyDrawService_MiniLuckDrawActivityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MiniLuckyDrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).MiniLuckDrawActivityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/MiniLuckDrawActivityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).MiniLuckDrawActivityInfo(ctx, req.(*MiniLuckyDrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuckyDrawService_MiniLuckDrawInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MiniLuckyDrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).MiniLuckDrawInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/MiniLuckDrawInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).MiniLuckDrawInfo(ctx, req.(*MiniLuckyDrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuckyDrawService_LuckDrawStart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MiniLuckyDrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).LuckDrawStart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/LuckDrawStart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).LuckDrawStart(ctx, req.(*MiniLuckyDrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuckyDrawService_LuckDrawShare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MiniLuckyDrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).LuckDrawShare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/LuckDrawShare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).LuckDrawShare(ctx, req.(*MiniLuckyDrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuckyDrawService_WinningRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WinningRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).WinningRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/WinningRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).WinningRecordList(ctx, req.(*WinningRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuckyDrawService_LuckyDrawAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuckyDrawAddressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).LuckyDrawAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/LuckyDrawAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).LuckyDrawAddress(ctx, req.(*LuckyDrawAddressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LuckyDrawService_LuckyDrawActivityRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuckyDrawActivityRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuckyDrawServiceServer).LuckyDrawActivityRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ac.LuckyDrawService/LuckyDrawActivityRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuckyDrawServiceServer).LuckyDrawActivityRecommend(ctx, req.(*LuckyDrawActivityRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _LuckyDrawService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ac.LuckyDrawService",
	HandlerType: (*LuckyDrawServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LuckyDrawActivityList",
			Handler:    _LuckyDrawService_LuckyDrawActivityList_Handler,
		},
		{
			MethodName: "LuckyDrawActivityInfo",
			Handler:    _LuckyDrawService_LuckyDrawActivityInfo_Handler,
		},
		{
			MethodName: "EditLuckyDrawActivity",
			Handler:    _LuckyDrawService_EditLuckyDrawActivity_Handler,
		},
		{
			MethodName: "MiniLuckDrawActivityInfo",
			Handler:    _LuckyDrawService_MiniLuckDrawActivityInfo_Handler,
		},
		{
			MethodName: "MiniLuckDrawInfo",
			Handler:    _LuckyDrawService_MiniLuckDrawInfo_Handler,
		},
		{
			MethodName: "LuckDrawStart",
			Handler:    _LuckyDrawService_LuckDrawStart_Handler,
		},
		{
			MethodName: "LuckDrawShare",
			Handler:    _LuckyDrawService_LuckDrawShare_Handler,
		},
		{
			MethodName: "WinningRecordList",
			Handler:    _LuckyDrawService_WinningRecordList_Handler,
		},
		{
			MethodName: "LuckyDrawAddress",
			Handler:    _LuckyDrawService_LuckyDrawAddress_Handler,
		},
		{
			MethodName: "LuckyDrawActivityRecommend",
			Handler:    _LuckyDrawService_LuckyDrawActivityRecommend_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ac/lucky_draw_service.proto",
}
