package ac

import (
	"context"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	lock sync.Mutex
	Conn *grpc.ClientConn
	RPC  ActivityServiceClient
	GB   GroupBuyServiceClient   //拼团
	CB   CycleBuyServiceClient   //周期购
	SK   SeckillServiceClient    //秒杀
	BB   BookBuyServiceClient    //预售
	NB   NewBuyServiceClient     //新人专享
	CS   ChristmasServiceClient  // 双旦活动
	DN   DinnerServiceClient     // 2022年夜饭
	TS   TwoSessoinServiceClient //2022两会活动
	GC   GroupChatServiceClient  //2022两会活动
	LD   LuckyDrawServiceClient  //抽奖活动
	UC   UserCoinServiceClient   //抽奖活动
	BS   BaseServiceClient
	Base BaseServiceClient
	PF   PosterFissionServiceClient // 海报分裂
	Ctx  context.Context
}

var grpcClient *Client

func init() {
	grpcClient = new(Client)
}

func GetActivityCenterClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 30*time.Second)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return newClient()
}

func newClient() *Client {
	var err error
	url := config.GetString("grpc.activity-center")
	//url = "10.1.1.248:7074"
	if url == "" {
		url = "127.0.0.1:7074"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("datacenter，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = NewActivityServiceClient(grpcClient.Conn)
		grpcClient.GB = NewGroupBuyServiceClient(grpcClient.Conn)
		grpcClient.CB = NewCycleBuyServiceClient(grpcClient.Conn)
		grpcClient.BB = NewBookBuyServiceClient(grpcClient.Conn)
		grpcClient.SK = NewSeckillServiceClient(grpcClient.Conn)
		grpcClient.NB = NewNewBuyServiceClient(grpcClient.Conn)
		grpcClient.CS = NewChristmasServiceClient(grpcClient.Conn)
		grpcClient.DN = NewDinnerServiceClient(grpcClient.Conn)
		grpcClient.TS = NewTwoSessoinServiceClient(grpcClient.Conn)
		grpcClient.GC = NewGroupChatServiceClient(grpcClient.Conn)
		grpcClient.LD = NewLuckyDrawServiceClient(grpcClient.Conn)
		grpcClient.UC = NewUserCoinServiceClient(grpcClient.Conn)
		grpcClient.BS = NewBaseServiceClient(grpcClient.Conn)
		grpcClient.Base = NewBaseServiceClient(grpcClient.Conn)
		grpcClient.PF = NewPosterFissionServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
	//c.Cf()
}
