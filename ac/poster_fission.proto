syntax = "proto3";

package ac;

import "ac/base.proto";

service PosterFissionService{
  // 活动新增/编辑
  rpc Store(PosterFission) returns (BaseRes);
  // 活动列表
  rpc List(PosterFissionListReq) returns (PosterFissionListRes);
  // 活动详情
  rpc Detail(PosterFissionDetailReq) returns(PosterFissionDetailRes);
  // 活动信息及状态
  rpc State(PosterFissionStateReq) returns(PosterFissionStateRes);
  // 参加活动
  rpc Join(PosterFissionJoinReq) returns(PosterFissionJoinRes);
}

message PosterFissionListReq {
  // 每页数量
  int32 page_size = 1;
  // 当前页码
  int32 page_index = 2;
}

message PosterFissionListRes {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message PosterFissionList {
    // 活动id
    uint32 id = 1;
    // 活动主题
    string title = 2;
    // 开始时间
    string begin_time = 13;
    // 结束时间
    string end_time = 14;
  }

  repeated PosterFissionList data = 3;
  // 总数
  int32 total = 4;
}

message PosterFissionDetailReq {
  // 活动id
  int32 id = 1;
}

message PosterFissionDetailRes {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 活动详情数据
  PosterFission data = 3;
}

message PosterFissionStateReq {
  // 前端不需要传，从token解析，允许不登陆请求
  string scrm_id =1;
  // 活动id
  int32 poster_fission_id = 2;
}

message PosterFissionStateRes {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message Data {
    // 活动信息
    PosterFission poster_fission = 1;
    // 登录状态 0未登录，1登录了
    int32 login_state = 2;
    // 活动状态 10未开始 20进行中 30已结束
    int32 state = 3;
    // 我的排名，rank大于0表示参加过，否则表示没参加过
    int32 rank = 4;
    // 总参与人数
    int32 total = 5;
  }
  Data data = 3;
}

// 参加活动
message PosterFissionJoinReq {
  // 前端不需要传，从token解析
  string scrm_id =1;
  // 活动id
  int32 poster_fission_id = 2;
}

// 参加活动
message PosterFissionJoinRes {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message Data {
    // 我的排名，在参与的那一刻，总人数=我的排名
    int32 rank = 1;
  }

  Data data = 3;
}

message BackgroundImage {
  // 主页面底图
  string main = 1;
  // 分享底图
  string share = 2;
}

message PosterFission {
  // 活动id
  int32 id = 1;
  // 活动主题
  string title = 2;
  // 多张底图
  repeated BackgroundImage background_images = 3;
  // 分享卡片标题
  string share_title = 5;
  // 分享卡片底图
  string share_image = 6;
  // 分享卡片数值坐标
  int32 share_position_x = 7;
  // 分享卡片数值坐标
  int32 share_position_y = 8;
  // 分享二维码图片
  string qr_code_image = 9;
  // 假参与人数起始值
  int32 start_rank = 10;
  // 活动按钮文案
  string button_title = 11;
  // 微页面id
  int32 wepage_id = 12;
  // 开始时间 2023-03-21 12:00，注意没有秒
  string begin_time = 13;
  // 结束时间 2023-03-21 12:00，注意没有秒
  string end_time = 14;
}