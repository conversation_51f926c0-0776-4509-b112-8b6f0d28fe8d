syntax = "proto3";

package ac;

service DinnerService{
  // 设置列表
  rpc Settings(DinnerEmptyRequest) returns (DinnerSettingsResponse);
  // 设置更新
  rpc SettingsUpdate(DinnerSettingsData) returns(DinnerResponse);
  // 参数值
  rpc SettingValue(DinnerSettingValueRequest) returns(DinnerSettingValueResponse);
  // 活动状态
  rpc State(DinnerStateRequest) returns (DinnerStateResponse);
  // 捐赠
  rpc Donate(DinnerDonateRequest) returns (DinnerResponse);
  // 捐赠信息
  rpc DonateInfo(DinnerDonateInfoRequest) returns (DinnerDonateInfoResponse);
  // 订阅消息
  rpc Subscribe(DinnerSubscribeRequest) returns (DinnerResponse);
  // 查询所有按钮订阅状态
  rpc SubscribeState(DinnerSubscribeStateRequest) returns (DinnerSubscribeStateResponse);
  // 发送订阅消息
  rpc SubscribeSend(DinnerSubscribeSendRequest) returns (DinnerResponse);
  // 单条配置更新
  rpc SettingUpdate(SettingUpdateRequest) returns (DinnerResponse);
}

message DinnerResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
}

message DinnerEmptyRequest{
}

message DinnerSettingsData{
  // 是否开启活动，'0'关闭、'1'开启
  string enable = 1;
  // 支付成功微页面链接
  string wepage_link_pay_success = 2;
  // 消费兑换比例，每10份兑换
  string consume_donate_rate = 3;
}

message DinnerSettingsResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 设置项
  DinnerSettingsData data = 3;
}

message DinnerSettingValueRequest{
  // 参数key，支付成功微页面链接 wepage_link_pay_success
  string name = 1;
}
message SettingUpdateRequest{
  // 配置名称
  string name = 1;
  // 配置值
  string value = 2;
}

message DinnerSettingValueResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 参数值，注意是字符串类型
  string value = 3;
}

// 活动状态
message DinnerStateRequest {
  // 前端不需要传，仅用于rpc标记用户
  string scrm_id = 1;
}

message DinnerStateData{
  // 当前状态，会先判断活动有效性， 0关闭了、1结束了、2未开始、3未参加无消费记录，4未参加有消费记录、5参加过、6未登录
  int32 state = 1;
  // 捐赠商品id，当state=3有效
  string goods_id = 2;
  // 可捐赠份数，当state=4有效
  int32 qty = 3;
  // 捐赠总人数，自带单位如万
  string donate_number = 4;
}

message DinnerStateResponse{
  // 状态码
  int32 code = 1;
  // 消息
  string message = 2;
  // 捐赠状态
  DinnerStateData data = 3;
}

message DinnerDonateRequest {
  // 捐赠类型 1消费、2分享、3没养宠物免费、4支付1分钱捐赠
  int32 type = 1;
  // 前端不需要传，仅用于rpc标记用户
  string scrm_id = 2;
}

// 活动状态
message DinnerDonateInfoRequest {
  // 前端不需要传，仅用于rpc标记用户
  string scrm_id = 1;
}

message DinnerDonateInfo{
  // 捐赠排名
  int32 rank = 1;
  // 剩余分享次数
  int32 remain_share_times = 2;
  // 剩余可捐赠份数
  int32 remain_donate_qty = 3;
  // 已捐赠数量
  int32 donated_qty = 4;
  // 消费总金额，元
  double consume_total = 5;
  // 消费兑换比例，每10份兑换
  int32 consume_donate_rate = 6;
}

message DinnerDonateInfoResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 捐赠信息
  DinnerDonateInfo data = 3;
}

message DinnerSubscribeRequest {
  // 订阅的按钮id
  int32 button_id = 1;
  // 订阅的状态 'accept'、'reject'、'ban'、'filter'
  string state = 2;
  // 前端不需要传，仅用于rpc标记用户
  string scrm_id = 3;
  // 前端不需要传，会从token解析
  string openid = 4;
}

message DinnerSubscribeStateRequest {
  // 前端不需要传，仅用于rpc标记用户
  string scrm_id = 2;
}

message DinnerSubscribeStateData {
  // 订阅的按钮id，1-9
  int32 button_id = 1;
  // 是否订阅了 true or false
  bool is_subscribed = 3;
  // 当is_subscribed = false 时返回需要订阅的模板id
  string template_id = 4;
}

message DinnerSubscribeStateResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 订阅状态数据
  repeated DinnerSubscribeStateData data = 3;
}

message DinnerSubscribeSendRequest {
  // 要发送的订阅id
  string ids = 1;
  // 发送的模板数据，json字符串
  string data = 2;
  // 跳转的页面
  string page = 3;
  // 签名
  string sign = 4;
}