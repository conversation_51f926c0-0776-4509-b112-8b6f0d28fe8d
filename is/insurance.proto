syntax = "proto3";
package is;



//扬子江保险模块
service YangZiJiangServices{
    //扬子江核保承保接口
    rpc ValidatePolicy (YangZiJiangRequest)  returns(YangZiJiangResponse);

    rpc InsuranceOrderOnLine (InsuranceOnLineRequest)  returns(YangZiJiangResponse);
}

message InsuranceOnLineRequest {
//外部业务流水号
string businessNo=1;
//保单号
string policy_no=2;
//支付申请号
string pay_apply_no=3;
//支付金额
int32 pay_amount=4;
//支付时间
string pay_time=5;
//电子保单地址
string elec_policy_url=6;
//总保额
int32 total_insured_amt=7;
//实付总保费
int32 ActualPremiumAmt=8;
//保险起始时间
string InsuredBgnTime=9;
//保险截止时间
string InsuredEndTime =10;
//投保人
Holder holder=11;
//被保人
repeated Insureds insureds=12;

Target target=13;
}

//请求中报返回结果
message YangZiJiangResponse{
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    //错误信息
    string error = 3;
    //仓库信息
   repeated YangZiJiangResults results = 4;
}
//请求中报返回结果
message YangZiJiangResults{
    //投保单号
    string policy_app_no=1;
    //保单号
    string policy_no=2;
    //电子保单地址
    string elec_addr=3;
}
//扬子江投保请求数据
message YangZiJiangRequest  {
//渠道信息
SaleInfo saleInfo =1;
//产品信息
Product product=2;
//投保信息
Policy policy=3;
//投保人
Holder holder=4;
//被保人
repeated Insureds insureds=5;
//MAP数据结构，数据数据参考yangzijiangdto 里面的 Target
Target target=6;
//商品的SKUID，因为现在的保险期限，保额等都是写死的，需要SKUID来匹配
string skuid=7;
//内部订单号
string order_id=8;
}

//渠道信息
message SaleInfo  {
//接入用户
string access_user=1;
//渠道编码
string channelCode=2;
//接入密码
string accessPassword=3;
}

//产品信息
message Product  {
//产品代码
string product_code=1;
//套餐代码
string package_code=2;
//保司编码
string insur_code =3;
}
//投保信息
message Policy  {
//外部业务流水号
string BusinessNo=1;
//实付总保费
int32 ActualPremiumAmt=2;
//投保份数
int32 InsuredQuantity=3;
//原始总保费
int32 OriginalPremiumAmt=4;
//总保额
int32 TotalInsuredAmt=5;
//保险起始时间
string InsuredBgnTime=6;
//保险截止时间
string InsuredEndTime=7;
//投保时间
string AppTime=8;
}

//投保人
message Holder  {
//投保人名称
string HolderName=1;
//证件类型
string IdcartType=2;
//证件号码
string IdcartNo=3;
//投保人类型  1：个人  2：团体
string HolderType=4;
//手机号
string Mobile=5;
//出生日期
string BornDate=6;
//性别
//M	男
//F	女
string Sex=7;
//固话
string Telphone=8;
//邮箱
string Mail=9;
}

//被保人
message Insureds  {
//投保人名称
string InsuredName=1;
//证件类型
// 01	身份证
//02	护照
//03	军人证
//04	学生证
//05	台胞证
//06	港澳返乡证
//07	出生证
//08	出生日期（未成年人使用）
//09	统一社会信用代码
//13	纳税人识别号
//14	外国人永久居留身份证
//99	其他
string IdcartType=2;
//证件号码
string IdcartNo=3;
//被保人类型 1：个人  2：团体
string InsuredType=4;
//出生日期
string BornDate=5;
//性别
//M	男
//F	女
string Sex=6;
//被保人与投保人关系
//0	本人
//1	配偶
//2	父母
//3	子女
//4	兄弟姐妹
//5	雇佣
//6	监护人
//7	被监护人
//8	抚养
//9	赡养
//10	朋友
//11	亲属
//12	法定继承人
//13	身故受益人
//14	其他
string RelationWithHolder=7;
//手机号 TE
string Mobile=8;
}

//宠物等信息
message Target {
//养犬许可证号码
string DogLicenseCode=1;
//犬类免疫证号码
string ImmunityCertifiCode  =2;
//宠物犬种类
string PetDogBreed  =3;
//所在城市
string HouseCity =4;
//详细地址
string HouseAddress  =5;
//宠物名称
string PetName =6;
//1-猫，2-犬
string Category =7;
//小的分类名称 种类
string CategoryName  =8;
//出生日期
string Birthday  =9;
//宠物性别
string Gender  =10;
//是否绝育
bool Sterilization =11;
//是否免疫
bool Immune  =12;
//投保照片ZIP压缩包的base64格式
string Base64Str  =13;
//业务模式
int32 BizMode  =14;
}