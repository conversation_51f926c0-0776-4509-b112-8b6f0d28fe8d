package proto

import (
	"testing"
)

func TestBuildProto(t *testing.T) {
	tests := []struct {
		name string
		want error
	}{
		{
			name: " ",
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := BuildProto(); got != tt.want {
				t.<PERSON>("BuildProto() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSmartBuildProto(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SmartBuildProto()
		})
	}
}
