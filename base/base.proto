syntax = "proto3";
package base;

//基础公用的数据结构
//app客户端信息
message ClientInfo {
  //设备序列号
  string deviceSeries = 1;
  //设备品牌
  string deviceBrand = 2;
  //设备操作系统
  string os = 3;
  //设备操作系统版本号
  string osVersion = 4;
  //网络
  string network = 5;
  //网络运营商
  string networkOperator = 6;
  //分辨率
  string resolution = 7;
  //产品版本号
  string version = 8;
  //基础库版本
  string sdkVersion = 9;
  //基础库版本
  string appletVersion = 10;
  //基础库版本
  string openId = 11;
}

message BaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}