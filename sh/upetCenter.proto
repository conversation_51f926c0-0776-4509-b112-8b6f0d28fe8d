syntax = "proto3";
import "google/api/annotations.proto";
package sh;

service TestService{
    rpc Test (HelloRequest) returns (HelloResponse);
}

message HelloRequest {
    string params=1;
}
message HelloResponse {
    string params=1; 
}

service ProductService{
    rpc SkuRelList (SkuRelRequest) returns (SkuRelResponse);
    //查询关联名称列表
    rpc GoodsRelevanceList (SkuRelListRequest) returns (SkuRelListResponse);
    //新增关联名称
    rpc NewGoodsRelevance (GoodsRelevanceRequest) returns (BaseResponse);
    //删除关联名称
    rpc DeleteGoodsRelevance (GoodsRelevanceDeleteRequest) returns (BaseResponse);

    //删除关联商品
    rpc DeleteGoodsRelevanceSku (GoodsRevanceSkuRequest) returns (BaseResponse);

    // 更新商品到ES
    rpc UpdateGoodsToEs(GoodsToEsUpdateRequest) returns (BaseResponse);

    // 获取商品分类
    rpc GetGoodsClasses(GetGoodsClassesRequest) returns (GetGoodsClassesResponse);
    // 获取会员商品分类
    rpc GoodsClassList(GoodsClassListReq) returns (GoodsClassListRes);
    // 获取会员商品列表
    rpc GoodsList(GoodsListReq) returns (GoodsListRes);

    // 获取用户会员等级
    rpc UserLevel(UserLevelRequest) returns (UserLevelResponse);

    // 使用消息订阅次数
    rpc ProductMemberPrice(ProductMemberPriceRequest) returns (ProductMemberPriceResponse);
}

message GoodsListReq {
    //商品名称
    string goods_name = 1;
    //分类ID
    int32 gc_id = 2;
    //排序类别 1按销量  2按价格
    int32 type = 3;
    //升序还是降序 1升序 2降序
    int32 sort = 4;
    int32 page_size = 5;
    int32 page_index = 6;
    //userid, 前端不用传
    string scrm_userid = 7;
    //主体：1-阿闻，2-极宠家，3-福码购
    int32 org_id = 8;
}

message GoodsListRes {
    string msg = 1;
    int32 total = 2;
    repeated GoodsListData data = 3;
}

message GoodsListData {
    //商品id(SKU)
    int64 goods_id = 1;
    //商品名称
    string goods_name = 2;
    //商品价格(原价)
    float goods_price = 3;
    //商品主图
    string goods_image = 4;
    //会员价
    float member_price = 5;
    //0普通商品，1实实组合，2虚虚组合，3虚实组合
    int32 goods_type = 6;
    //普通商品中有实物，有虚拟 0-实物 1-虚拟
    int32 is_virtual = 7;
    //商品促销价格
    float goods_promotion_price = 8;
    //促销类型 0无促销，1团购，2限时折扣 12水印
    int32 goods_promotion_type = 9;
    //销售数量
    int32 goods_salenum = 10;
    //商品广告词
    string goods_jingle = 11;
    //商品简称
    string short_name = 12;
    //是否启用会员价 1启用，0不启用
    int32 enable_member_price = 13;
    //商品common_id
    int64 goods_commonid = 14;
    //颜色规格id
    int64 color_id = 15;
    //店铺id
    int64 store_id = 16;
    // 会员价是否生效
    int32 is_member_price = 17;
}

message GoodsClassListReq{
    //分类ID（默认为0，获取全部一级分类）
    int32 gc_id = 1;
    //主体：1-阿闻，2-极宠家，3-福码购
    int32 org_id = 2;
}

message GoodsClassListRes {
    string msg = 1;
    repeated GoodsClassList data = 2;
}

message GoodsClassList{
    //分类ID
    int32 gc_id = 1;
    //分类名称
    string gc_name = 2;
}

message GoodsRelevanceRequest{
    int32 id = 1;
    //名称
    string name = 2;
    //规格文字
    string desc = 3;
    //视频
    string video = 4;
    //图片
    string imgs = 5;
    //商品规格
    repeated ManyGoodSku goods_skus = 6;
}
message GoodsRelevanceDeleteRequest{
    //关联名称索引ID
    string id = 1;
}
message ManyGoodSku {
    int32 goods_id = 1;
    string relevance_sku_sort = 2;
    int32 goods_spec = 3;
}
//关联sku列表请求
message SkuRelRequest{
    //名称
    string name = 1;
    //查询方式
    string type = 2;
}

//关联sku列表响应
message SkuRelResponse{
    //code
    int32 code=1;
    //消息提示
    string message=2;
    //错误信息
    string error = 3;
    repeated SkuRelList data = 4;
}

//关联sku列表
message SkuRelList {
    //关联id
    int32 id = 1;
    //名称
    string name = 2;
    //创建时间
    string create_time = 3;
    //最后编辑时间
    string update_time = 4;
}

//关联sku列表
message SkuRelListRequest {
    //当前页
    int32 page_index = 1;
    //每页数据量
    int32 page_size = 2;
    //搜索类型
    int32 keyword_type = 3;
    //关键字
    string keyword = 4;
    //电商店铺id
    int32 store_id = 5;
}

message BaseResponse {
    int32 code = 1;
    string message = 2;
    string error = 3;
    repeated string details = 4;
}

message SkuRelListResponse{
    //code
    int32 code=1;
    //消息提示
    string message=2;
    //错误信息
    string error = 3;
    repeated GoodsRelevancesList data = 4;
    int32 total_count = 5;
}

message GoodsRelevancesList{
    int32 id = 1;
    //名称
    string name = 2;
    //规格文字
    string desc = 3;
    //添加时间
    string addtime = 4;
    //更新时间
    string edittime = 5;
}

message GoodsRevanceSkuRequest {
    int32 id = 1;
}

// 商品更新到ES 
message GoodsToEsUpdateRequest {
     // 多个skuids，以英文逗号分割
     string ids=1;
     //活动商品
     repeated GoodsActivity goods_date = 2;
     //是否VIP商品  0否，1是
     int32 IsVip =3;
    //电商机构ID
    int32 DsOrgId =4;
    //阿闻机构ID
    int32 AwOrgId =5;
    //店铺id
    int32 ShopId = 6;
}
message GoodsActivity{
    string sku_id =1;
    int32 price =2;
    int32 is_effective =3;
    //0：默认 1：预售
    int32 type =4;
}

message GetGoodsClassesRequest{
    repeated int64 gc_ids = 1;
}

message GetGoodsClassesResponse{
    int64 code = 1;
    string message = 2;
    Data data = 3;
    message Data{
        repeated GoodsClass parent_goods_class = 1;
        repeated GoodsClass children_list = 2;
    };
    message GoodsClass{
        int64 gc_id = 1;
        string gc_name = 2;
        int64 gc_parent_id = 3;
    };
}

message UserLevelRequest{
    string scrm_user_id = 1;
    int64 with_equity = 2;
}

message UserEquity{
    int64 id = 1;
    // 权益名称
    string equity_name = 2;
    // 权益icon
    string icon = 3;
    // 权益简介
    string equity_info = 4;
}

message UserLevelResponse{
    int64 code = 1;
    string message = 2;
    string user_level = 3;
    int64 user_level_id = 4;
    int64 user_level_stime = 5;
    int64 user_level_etime = 6;
    repeated UserEquity user_equities = 7;
}

//查询对应等级的会员价
message ProductMemberPriceRequest {
    // 商品Sku列表
    repeated string sku_ids = 1;
    int32 member_level = 2;
    //主体：1-阿闻，2-极宠家，3-福码购
    int32 org_id = 3;
}
message ProductMemberPriceResponse {
    repeated ProductMemberPrice data = 1;
}
message ProductMemberPrice{
    //商品skuid
    string sku_id = 1;
    //是否启用会员价，1启用，0不启用
    int32 enable_member_price = 2;
}