// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sh/activity.proto

package sh

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 幸运码保存
type LuckyNumSaveRequest struct {
	LuckyNum             string   `protobuf:"bytes,1,opt,name=lucky_num,json=luckyNum,proto3" json:"lucky_num"`
	LotteryId            int32    `protobuf:"varint,2,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id"`
	Type                 int32    `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	ScrmId               string   `protobuf:"bytes,4,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	InviteMemberId       int64    `protobuf:"varint,5,opt,name=invite_member_id,json=inviteMemberId,proto3" json:"invite_member_id"`
	InviteScrmId         string   `protobuf:"bytes,6,opt,name=invite_scrm_id,json=inviteScrmId,proto3" json:"invite_scrm_id"`
	OrderSn              string   `protobuf:"bytes,7,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyNumSaveRequest) Reset()         { *m = LuckyNumSaveRequest{} }
func (m *LuckyNumSaveRequest) String() string { return proto.CompactTextString(m) }
func (*LuckyNumSaveRequest) ProtoMessage()    {}
func (*LuckyNumSaveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{0}
}

func (m *LuckyNumSaveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyNumSaveRequest.Unmarshal(m, b)
}
func (m *LuckyNumSaveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyNumSaveRequest.Marshal(b, m, deterministic)
}
func (m *LuckyNumSaveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyNumSaveRequest.Merge(m, src)
}
func (m *LuckyNumSaveRequest) XXX_Size() int {
	return xxx_messageInfo_LuckyNumSaveRequest.Size(m)
}
func (m *LuckyNumSaveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyNumSaveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyNumSaveRequest proto.InternalMessageInfo

func (m *LuckyNumSaveRequest) GetLuckyNum() string {
	if m != nil {
		return m.LuckyNum
	}
	return ""
}

func (m *LuckyNumSaveRequest) GetLotteryId() int32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

func (m *LuckyNumSaveRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *LuckyNumSaveRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *LuckyNumSaveRequest) GetInviteMemberId() int64 {
	if m != nil {
		return m.InviteMemberId
	}
	return 0
}

func (m *LuckyNumSaveRequest) GetInviteScrmId() string {
	if m != nil {
		return m.InviteScrmId
	}
	return ""
}

func (m *LuckyNumSaveRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type LuckyNumSaveResponse struct {
	// 200-正常，400-错误
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误提示信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 内部错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	// 是否删除已生成的幸运码，1-删除
	IsDelLuckyNum        int32    `protobuf:"varint,4,opt,name=is_del_lucky_num,json=isDelLuckyNum,proto3" json:"is_del_lucky_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyNumSaveResponse) Reset()         { *m = LuckyNumSaveResponse{} }
func (m *LuckyNumSaveResponse) String() string { return proto.CompactTextString(m) }
func (*LuckyNumSaveResponse) ProtoMessage()    {}
func (*LuckyNumSaveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{1}
}

func (m *LuckyNumSaveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyNumSaveResponse.Unmarshal(m, b)
}
func (m *LuckyNumSaveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyNumSaveResponse.Marshal(b, m, deterministic)
}
func (m *LuckyNumSaveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyNumSaveResponse.Merge(m, src)
}
func (m *LuckyNumSaveResponse) XXX_Size() int {
	return xxx_messageInfo_LuckyNumSaveResponse.Size(m)
}
func (m *LuckyNumSaveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyNumSaveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyNumSaveResponse proto.InternalMessageInfo

func (m *LuckyNumSaveResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *LuckyNumSaveResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *LuckyNumSaveResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *LuckyNumSaveResponse) GetIsDelLuckyNum() int32 {
	if m != nil {
		return m.IsDelLuckyNum
	}
	return 0
}

// 我的幸运码
type MyLuckyNumGetRequest struct {
	// 此参数不用传，后端通过token获取用户scrm_id
	ScrmId string `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 活动id
	LotteryId            int32    `protobuf:"varint,2,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyLuckyNumGetRequest) Reset()         { *m = MyLuckyNumGetRequest{} }
func (m *MyLuckyNumGetRequest) String() string { return proto.CompactTextString(m) }
func (*MyLuckyNumGetRequest) ProtoMessage()    {}
func (*MyLuckyNumGetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{2}
}

func (m *MyLuckyNumGetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyLuckyNumGetRequest.Unmarshal(m, b)
}
func (m *MyLuckyNumGetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyLuckyNumGetRequest.Marshal(b, m, deterministic)
}
func (m *MyLuckyNumGetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyLuckyNumGetRequest.Merge(m, src)
}
func (m *MyLuckyNumGetRequest) XXX_Size() int {
	return xxx_messageInfo_MyLuckyNumGetRequest.Size(m)
}
func (m *MyLuckyNumGetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MyLuckyNumGetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MyLuckyNumGetRequest proto.InternalMessageInfo

func (m *MyLuckyNumGetRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *MyLuckyNumGetRequest) GetLotteryId() int32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

type MyLuckyNumGetResponse struct {
	// 200-正常，400-错误
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误提示信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 内部错误信息
	Error                string               `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 []*MyLuckyNumGetData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *MyLuckyNumGetResponse) Reset()         { *m = MyLuckyNumGetResponse{} }
func (m *MyLuckyNumGetResponse) String() string { return proto.CompactTextString(m) }
func (*MyLuckyNumGetResponse) ProtoMessage()    {}
func (*MyLuckyNumGetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{3}
}

func (m *MyLuckyNumGetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyLuckyNumGetResponse.Unmarshal(m, b)
}
func (m *MyLuckyNumGetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyLuckyNumGetResponse.Marshal(b, m, deterministic)
}
func (m *MyLuckyNumGetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyLuckyNumGetResponse.Merge(m, src)
}
func (m *MyLuckyNumGetResponse) XXX_Size() int {
	return xxx_messageInfo_MyLuckyNumGetResponse.Size(m)
}
func (m *MyLuckyNumGetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MyLuckyNumGetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MyLuckyNumGetResponse proto.InternalMessageInfo

func (m *MyLuckyNumGetResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MyLuckyNumGetResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MyLuckyNumGetResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *MyLuckyNumGetResponse) GetData() []*MyLuckyNumGetData {
	if m != nil {
		return m.Data
	}
	return nil
}

type MyLuckyNumGetData struct {
	// 日期
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date"`
	// 获取幸运码的说明
	TypeName string `protobuf:"bytes,2,opt,name=type_name,json=typeName,proto3" json:"type_name"`
	// 幸运码
	LuckyNum             string   `protobuf:"bytes,3,opt,name=lucky_num,json=luckyNum,proto3" json:"lucky_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyLuckyNumGetData) Reset()         { *m = MyLuckyNumGetData{} }
func (m *MyLuckyNumGetData) String() string { return proto.CompactTextString(m) }
func (*MyLuckyNumGetData) ProtoMessage()    {}
func (*MyLuckyNumGetData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{4}
}

func (m *MyLuckyNumGetData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyLuckyNumGetData.Unmarshal(m, b)
}
func (m *MyLuckyNumGetData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyLuckyNumGetData.Marshal(b, m, deterministic)
}
func (m *MyLuckyNumGetData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyLuckyNumGetData.Merge(m, src)
}
func (m *MyLuckyNumGetData) XXX_Size() int {
	return xxx_messageInfo_MyLuckyNumGetData.Size(m)
}
func (m *MyLuckyNumGetData) XXX_DiscardUnknown() {
	xxx_messageInfo_MyLuckyNumGetData.DiscardUnknown(m)
}

var xxx_messageInfo_MyLuckyNumGetData proto.InternalMessageInfo

func (m *MyLuckyNumGetData) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *MyLuckyNumGetData) GetTypeName() string {
	if m != nil {
		return m.TypeName
	}
	return ""
}

func (m *MyLuckyNumGetData) GetLuckyNum() string {
	if m != nil {
		return m.LuckyNum
	}
	return ""
}

// 中奖信息
type LuckyOpenInfoRequest struct {
	// 用户id
	ScrmId string `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 活动id
	LotteryId            int32    `protobuf:"varint,2,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyOpenInfoRequest) Reset()         { *m = LuckyOpenInfoRequest{} }
func (m *LuckyOpenInfoRequest) String() string { return proto.CompactTextString(m) }
func (*LuckyOpenInfoRequest) ProtoMessage()    {}
func (*LuckyOpenInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{5}
}

func (m *LuckyOpenInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyOpenInfoRequest.Unmarshal(m, b)
}
func (m *LuckyOpenInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyOpenInfoRequest.Marshal(b, m, deterministic)
}
func (m *LuckyOpenInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyOpenInfoRequest.Merge(m, src)
}
func (m *LuckyOpenInfoRequest) XXX_Size() int {
	return xxx_messageInfo_LuckyOpenInfoRequest.Size(m)
}
func (m *LuckyOpenInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyOpenInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyOpenInfoRequest proto.InternalMessageInfo

func (m *LuckyOpenInfoRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *LuckyOpenInfoRequest) GetLotteryId() int32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

type LuckyOpenInfoResponse struct {
	// 200-正常，400-错误
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误提示信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 内部错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	// 开奖信息标题
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title"`
	// 开奖时间信息
	DateInfo []*OpenInfoDate `protobuf:"bytes,5,rep,name=date_info,json=dateInfo,proto3" json:"date_info"`
	// 中奖信息
	RewardInfo []*LuckyOpenInfoData `protobuf:"bytes,6,rep,name=reward_info,json=rewardInfo,proto3" json:"reward_info"`
	// 领奖方式
	RewardWay            *RewardWayInfo `protobuf:"bytes,7,opt,name=reward_way,json=rewardWay,proto3" json:"reward_way"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *LuckyOpenInfoResponse) Reset()         { *m = LuckyOpenInfoResponse{} }
func (m *LuckyOpenInfoResponse) String() string { return proto.CompactTextString(m) }
func (*LuckyOpenInfoResponse) ProtoMessage()    {}
func (*LuckyOpenInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{6}
}

func (m *LuckyOpenInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyOpenInfoResponse.Unmarshal(m, b)
}
func (m *LuckyOpenInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyOpenInfoResponse.Marshal(b, m, deterministic)
}
func (m *LuckyOpenInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyOpenInfoResponse.Merge(m, src)
}
func (m *LuckyOpenInfoResponse) XXX_Size() int {
	return xxx_messageInfo_LuckyOpenInfoResponse.Size(m)
}
func (m *LuckyOpenInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyOpenInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyOpenInfoResponse proto.InternalMessageInfo

func (m *LuckyOpenInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *LuckyOpenInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *LuckyOpenInfoResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *LuckyOpenInfoResponse) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *LuckyOpenInfoResponse) GetDateInfo() []*OpenInfoDate {
	if m != nil {
		return m.DateInfo
	}
	return nil
}

func (m *LuckyOpenInfoResponse) GetRewardInfo() []*LuckyOpenInfoData {
	if m != nil {
		return m.RewardInfo
	}
	return nil
}

func (m *LuckyOpenInfoResponse) GetRewardWay() *RewardWayInfo {
	if m != nil {
		return m.RewardWay
	}
	return nil
}

type RewardWayInfo struct {
	// 是否中免单奖，1-中奖，显示相关信息和客服二维码
	IsFree int32 `protobuf:"varint,1,opt,name=is_free,json=isFree,proto3" json:"is_free"`
	// 客服二维码地址
	QrCodeUrl string `protobuf:"bytes,2,opt,name=qr_code_url,json=qrCodeUrl,proto3" json:"qr_code_url"`
	// 是否显示填写收获地址，1-是，表示中实物奖，显示收货地址
	IsShowAddress int32 `protobuf:"varint,3,opt,name=is_show_address,json=isShowAddress,proto3" json:"is_show_address"`
	// 奖品收获地址信息
	AddressInfo          *RewardAddressRequest `protobuf:"bytes,4,opt,name=address_info,json=addressInfo,proto3" json:"address_info"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *RewardWayInfo) Reset()         { *m = RewardWayInfo{} }
func (m *RewardWayInfo) String() string { return proto.CompactTextString(m) }
func (*RewardWayInfo) ProtoMessage()    {}
func (*RewardWayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{7}
}

func (m *RewardWayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RewardWayInfo.Unmarshal(m, b)
}
func (m *RewardWayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RewardWayInfo.Marshal(b, m, deterministic)
}
func (m *RewardWayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RewardWayInfo.Merge(m, src)
}
func (m *RewardWayInfo) XXX_Size() int {
	return xxx_messageInfo_RewardWayInfo.Size(m)
}
func (m *RewardWayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RewardWayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RewardWayInfo proto.InternalMessageInfo

func (m *RewardWayInfo) GetIsFree() int32 {
	if m != nil {
		return m.IsFree
	}
	return 0
}

func (m *RewardWayInfo) GetQrCodeUrl() string {
	if m != nil {
		return m.QrCodeUrl
	}
	return ""
}

func (m *RewardWayInfo) GetIsShowAddress() int32 {
	if m != nil {
		return m.IsShowAddress
	}
	return 0
}

func (m *RewardWayInfo) GetAddressInfo() *RewardAddressRequest {
	if m != nil {
		return m.AddressInfo
	}
	return nil
}

type OpenInfoDate struct {
	// 开奖状态
	StatusName string `protobuf:"bytes,1,opt,name=status_name,json=statusName,proto3" json:"status_name"`
	// 开奖日期
	Date string `protobuf:"bytes,2,opt,name=date,proto3" json:"date"`
	// 开奖状态，1-开奖成功，0-未开奖
	Status               int32    `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenInfoDate) Reset()         { *m = OpenInfoDate{} }
func (m *OpenInfoDate) String() string { return proto.CompactTextString(m) }
func (*OpenInfoDate) ProtoMessage()    {}
func (*OpenInfoDate) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{8}
}

func (m *OpenInfoDate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenInfoDate.Unmarshal(m, b)
}
func (m *OpenInfoDate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenInfoDate.Marshal(b, m, deterministic)
}
func (m *OpenInfoDate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenInfoDate.Merge(m, src)
}
func (m *OpenInfoDate) XXX_Size() int {
	return xxx_messageInfo_OpenInfoDate.Size(m)
}
func (m *OpenInfoDate) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenInfoDate.DiscardUnknown(m)
}

var xxx_messageInfo_OpenInfoDate proto.InternalMessageInfo

func (m *OpenInfoDate) GetStatusName() string {
	if m != nil {
		return m.StatusName
	}
	return ""
}

func (m *OpenInfoDate) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *OpenInfoDate) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type LuckyOpenInfoData struct {
	// 中奖幸运码
	LuckyNum string `protobuf:"bytes,1,opt,name=lucky_num,json=luckyNum,proto3" json:"lucky_num"`
	// 头像地址
	Avatar string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar"`
	// 手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	// 奖品,1-免单，2-奖品
	Reward               int32    `protobuf:"varint,4,opt,name=reward,proto3" json:"reward"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyOpenInfoData) Reset()         { *m = LuckyOpenInfoData{} }
func (m *LuckyOpenInfoData) String() string { return proto.CompactTextString(m) }
func (*LuckyOpenInfoData) ProtoMessage()    {}
func (*LuckyOpenInfoData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{9}
}

func (m *LuckyOpenInfoData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyOpenInfoData.Unmarshal(m, b)
}
func (m *LuckyOpenInfoData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyOpenInfoData.Marshal(b, m, deterministic)
}
func (m *LuckyOpenInfoData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyOpenInfoData.Merge(m, src)
}
func (m *LuckyOpenInfoData) XXX_Size() int {
	return xxx_messageInfo_LuckyOpenInfoData.Size(m)
}
func (m *LuckyOpenInfoData) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyOpenInfoData.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyOpenInfoData proto.InternalMessageInfo

func (m *LuckyOpenInfoData) GetLuckyNum() string {
	if m != nil {
		return m.LuckyNum
	}
	return ""
}

func (m *LuckyOpenInfoData) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *LuckyOpenInfoData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *LuckyOpenInfoData) GetReward() int32 {
	if m != nil {
		return m.Reward
	}
	return 0
}

type LuckyOpenInfoAllRequest struct {
	// 活动id
	LotteryId            int32    `protobuf:"varint,1,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyOpenInfoAllRequest) Reset()         { *m = LuckyOpenInfoAllRequest{} }
func (m *LuckyOpenInfoAllRequest) String() string { return proto.CompactTextString(m) }
func (*LuckyOpenInfoAllRequest) ProtoMessage()    {}
func (*LuckyOpenInfoAllRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{10}
}

func (m *LuckyOpenInfoAllRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyOpenInfoAllRequest.Unmarshal(m, b)
}
func (m *LuckyOpenInfoAllRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyOpenInfoAllRequest.Marshal(b, m, deterministic)
}
func (m *LuckyOpenInfoAllRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyOpenInfoAllRequest.Merge(m, src)
}
func (m *LuckyOpenInfoAllRequest) XXX_Size() int {
	return xxx_messageInfo_LuckyOpenInfoAllRequest.Size(m)
}
func (m *LuckyOpenInfoAllRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyOpenInfoAllRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyOpenInfoAllRequest proto.InternalMessageInfo

func (m *LuckyOpenInfoAllRequest) GetLotteryId() int32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

type LuckyOpenInfoAllResponse struct {
	// 200-正常，400-错误
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误提示信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 内部错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	// 中奖信息
	Data                 []*LuckyOpenInfoAllData `protobuf:"bytes,6,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *LuckyOpenInfoAllResponse) Reset()         { *m = LuckyOpenInfoAllResponse{} }
func (m *LuckyOpenInfoAllResponse) String() string { return proto.CompactTextString(m) }
func (*LuckyOpenInfoAllResponse) ProtoMessage()    {}
func (*LuckyOpenInfoAllResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{11}
}

func (m *LuckyOpenInfoAllResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyOpenInfoAllResponse.Unmarshal(m, b)
}
func (m *LuckyOpenInfoAllResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyOpenInfoAllResponse.Marshal(b, m, deterministic)
}
func (m *LuckyOpenInfoAllResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyOpenInfoAllResponse.Merge(m, src)
}
func (m *LuckyOpenInfoAllResponse) XXX_Size() int {
	return xxx_messageInfo_LuckyOpenInfoAllResponse.Size(m)
}
func (m *LuckyOpenInfoAllResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyOpenInfoAllResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyOpenInfoAllResponse proto.InternalMessageInfo

func (m *LuckyOpenInfoAllResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *LuckyOpenInfoAllResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *LuckyOpenInfoAllResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *LuckyOpenInfoAllResponse) GetData() []*LuckyOpenInfoAllData {
	if m != nil {
		return m.Data
	}
	return nil
}

type LuckyOpenInfoAllData struct {
	// 中奖日期
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	// 中奖信息
	RewardInfo           []*LuckyOpenInfoAllInfoData `protobuf:"bytes,2,rep,name=reward_info,json=rewardInfo,proto3" json:"reward_info"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *LuckyOpenInfoAllData) Reset()         { *m = LuckyOpenInfoAllData{} }
func (m *LuckyOpenInfoAllData) String() string { return proto.CompactTextString(m) }
func (*LuckyOpenInfoAllData) ProtoMessage()    {}
func (*LuckyOpenInfoAllData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{12}
}

func (m *LuckyOpenInfoAllData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyOpenInfoAllData.Unmarshal(m, b)
}
func (m *LuckyOpenInfoAllData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyOpenInfoAllData.Marshal(b, m, deterministic)
}
func (m *LuckyOpenInfoAllData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyOpenInfoAllData.Merge(m, src)
}
func (m *LuckyOpenInfoAllData) XXX_Size() int {
	return xxx_messageInfo_LuckyOpenInfoAllData.Size(m)
}
func (m *LuckyOpenInfoAllData) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyOpenInfoAllData.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyOpenInfoAllData proto.InternalMessageInfo

func (m *LuckyOpenInfoAllData) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *LuckyOpenInfoAllData) GetRewardInfo() []*LuckyOpenInfoAllInfoData {
	if m != nil {
		return m.RewardInfo
	}
	return nil
}

type LuckyOpenInfoAllInfoData struct {
	// 中奖幸运码
	LuckyNum string `protobuf:"bytes,1,opt,name=lucky_num,json=luckyNum,proto3" json:"lucky_num"`
	// 手机号
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	// 头像地址
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar"`
	// 奖品,1-免单，2-奖品
	Reward               int32    `protobuf:"varint,4,opt,name=reward,proto3" json:"reward"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LuckyOpenInfoAllInfoData) Reset()         { *m = LuckyOpenInfoAllInfoData{} }
func (m *LuckyOpenInfoAllInfoData) String() string { return proto.CompactTextString(m) }
func (*LuckyOpenInfoAllInfoData) ProtoMessage()    {}
func (*LuckyOpenInfoAllInfoData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{13}
}

func (m *LuckyOpenInfoAllInfoData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LuckyOpenInfoAllInfoData.Unmarshal(m, b)
}
func (m *LuckyOpenInfoAllInfoData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LuckyOpenInfoAllInfoData.Marshal(b, m, deterministic)
}
func (m *LuckyOpenInfoAllInfoData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LuckyOpenInfoAllInfoData.Merge(m, src)
}
func (m *LuckyOpenInfoAllInfoData) XXX_Size() int {
	return xxx_messageInfo_LuckyOpenInfoAllInfoData.Size(m)
}
func (m *LuckyOpenInfoAllInfoData) XXX_DiscardUnknown() {
	xxx_messageInfo_LuckyOpenInfoAllInfoData.DiscardUnknown(m)
}

var xxx_messageInfo_LuckyOpenInfoAllInfoData proto.InternalMessageInfo

func (m *LuckyOpenInfoAllInfoData) GetLuckyNum() string {
	if m != nil {
		return m.LuckyNum
	}
	return ""
}

func (m *LuckyOpenInfoAllInfoData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *LuckyOpenInfoAllInfoData) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *LuckyOpenInfoAllInfoData) GetReward() int32 {
	if m != nil {
		return m.Reward
	}
	return 0
}

// 中奖地址提交
type RewardAddressRequest struct {
	// 收件人
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 城市
	City string `protobuf:"bytes,2,opt,name=city,proto3" json:"city"`
	//收货地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	// 手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	// 城市编码
	Adcode string `protobuf:"bytes,5,opt,name=adcode,proto3" json:"adcode"`
	// 腾讯地图纬度
	TxLat float32 `protobuf:"fixed32,6,opt,name=tx_lat,json=txLat,proto3" json:"tx_lat"`
	// 腾讯地图经纬度
	TxLng float32 `protobuf:"fixed32,7,opt,name=tx_lng,json=txLng,proto3" json:"tx_lng"`
	// 门牌号
	HouseInfo string `protobuf:"bytes,8,opt,name=house_info,json=houseInfo,proto3" json:"house_info"`
	// 活动id
	LotteryId int32 `protobuf:"varint,9,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id"`
	// 用户scrm_id,后端token获取
	ScrmUserId           string   `protobuf:"bytes,10,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RewardAddressRequest) Reset()         { *m = RewardAddressRequest{} }
func (m *RewardAddressRequest) String() string { return proto.CompactTextString(m) }
func (*RewardAddressRequest) ProtoMessage()    {}
func (*RewardAddressRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{14}
}

func (m *RewardAddressRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RewardAddressRequest.Unmarshal(m, b)
}
func (m *RewardAddressRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RewardAddressRequest.Marshal(b, m, deterministic)
}
func (m *RewardAddressRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RewardAddressRequest.Merge(m, src)
}
func (m *RewardAddressRequest) XXX_Size() int {
	return xxx_messageInfo_RewardAddressRequest.Size(m)
}
func (m *RewardAddressRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RewardAddressRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RewardAddressRequest proto.InternalMessageInfo

func (m *RewardAddressRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RewardAddressRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *RewardAddressRequest) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *RewardAddressRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *RewardAddressRequest) GetAdcode() string {
	if m != nil {
		return m.Adcode
	}
	return ""
}

func (m *RewardAddressRequest) GetTxLat() float32 {
	if m != nil {
		return m.TxLat
	}
	return 0
}

func (m *RewardAddressRequest) GetTxLng() float32 {
	if m != nil {
		return m.TxLng
	}
	return 0
}

func (m *RewardAddressRequest) GetHouseInfo() string {
	if m != nil {
		return m.HouseInfo
	}
	return ""
}

func (m *RewardAddressRequest) GetLotteryId() int32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

func (m *RewardAddressRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

type RewardAddressResponse struct {
	// 200-正常，400-错误
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误提示信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 内部错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RewardAddressResponse) Reset()         { *m = RewardAddressResponse{} }
func (m *RewardAddressResponse) String() string { return proto.CompactTextString(m) }
func (*RewardAddressResponse) ProtoMessage()    {}
func (*RewardAddressResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a12edfc068cd6904, []int{15}
}

func (m *RewardAddressResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RewardAddressResponse.Unmarshal(m, b)
}
func (m *RewardAddressResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RewardAddressResponse.Marshal(b, m, deterministic)
}
func (m *RewardAddressResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RewardAddressResponse.Merge(m, src)
}
func (m *RewardAddressResponse) XXX_Size() int {
	return xxx_messageInfo_RewardAddressResponse.Size(m)
}
func (m *RewardAddressResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RewardAddressResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RewardAddressResponse proto.InternalMessageInfo

func (m *RewardAddressResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RewardAddressResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RewardAddressResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func init() {
	proto.RegisterType((*LuckyNumSaveRequest)(nil), "sh.LuckyNumSaveRequest")
	proto.RegisterType((*LuckyNumSaveResponse)(nil), "sh.LuckyNumSaveResponse")
	proto.RegisterType((*MyLuckyNumGetRequest)(nil), "sh.MyLuckyNumGetRequest")
	proto.RegisterType((*MyLuckyNumGetResponse)(nil), "sh.MyLuckyNumGetResponse")
	proto.RegisterType((*MyLuckyNumGetData)(nil), "sh.MyLuckyNumGetData")
	proto.RegisterType((*LuckyOpenInfoRequest)(nil), "sh.LuckyOpenInfoRequest")
	proto.RegisterType((*LuckyOpenInfoResponse)(nil), "sh.LuckyOpenInfoResponse")
	proto.RegisterType((*RewardWayInfo)(nil), "sh.RewardWayInfo")
	proto.RegisterType((*OpenInfoDate)(nil), "sh.OpenInfoDate")
	proto.RegisterType((*LuckyOpenInfoData)(nil), "sh.LuckyOpenInfoData")
	proto.RegisterType((*LuckyOpenInfoAllRequest)(nil), "sh.LuckyOpenInfoAllRequest")
	proto.RegisterType((*LuckyOpenInfoAllResponse)(nil), "sh.LuckyOpenInfoAllResponse")
	proto.RegisterType((*LuckyOpenInfoAllData)(nil), "sh.LuckyOpenInfoAllData")
	proto.RegisterType((*LuckyOpenInfoAllInfoData)(nil), "sh.LuckyOpenInfoAllInfoData")
	proto.RegisterType((*RewardAddressRequest)(nil), "sh.RewardAddressRequest")
	proto.RegisterType((*RewardAddressResponse)(nil), "sh.RewardAddressResponse")
}

func init() { proto.RegisterFile("sh/activity.proto", fileDescriptor_a12edfc068cd6904) }

var fileDescriptor_a12edfc068cd6904 = []byte{
	// 932 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xcd, 0x6e, 0xe4, 0x44,
	0x10, 0x96, 0x3d, 0x3f, 0x19, 0xd7, 0x4c, 0xd8, 0xa4, 0x99, 0xd9, 0x78, 0xd9, 0x05, 0x46, 0x16,
	0x82, 0x41, 0x82, 0x80, 0x82, 0x84, 0x90, 0x10, 0x87, 0x11, 0x11, 0x28, 0xd2, 0x26, 0x48, 0x1e,
	0xad, 0x38, 0xec, 0xc1, 0xea, 0x8c, 0x3b, 0x19, 0x6b, 0xfd, 0x93, 0x74, 0xf7, 0x4c, 0x32, 0x27,
	0x0e, 0x48, 0x1c, 0x10, 0x67, 0xde, 0x82, 0x07, 0xe3, 0x01, 0xb8, 0xa3, 0xae, 0x6e, 0x33, 0x6e,
	0xdb, 0x44, 0x48, 0xe4, 0xd6, 0xf5, 0xe3, 0xea, 0xaa, 0xaf, 0xbe, 0xaa, 0x36, 0x1c, 0x8a, 0xd5,
	0x67, 0x74, 0x29, 0x93, 0x4d, 0x22, 0xb7, 0xc7, 0x37, 0xbc, 0x90, 0x05, 0x71, 0xc5, 0x2a, 0xf8,
	0xd3, 0x81, 0xb7, 0x5f, 0xae, 0x97, 0x6f, 0xb6, 0x17, 0xeb, 0x6c, 0x41, 0x37, 0x2c, 0x64, 0xb7,
	0x6b, 0x26, 0x24, 0x79, 0x0e, 0x5e, 0xaa, 0xd4, 0x51, 0xbe, 0xce, 0x7c, 0x67, 0xea, 0xcc, 0xbc,
	0x70, 0x90, 0x1a, 0x3f, 0xf2, 0x2e, 0x40, 0x5a, 0x48, 0xc9, 0xf8, 0x36, 0x4a, 0x62, 0xdf, 0x9d,
	0x3a, 0xb3, 0x5e, 0xe8, 0x19, 0xcd, 0x59, 0x4c, 0x08, 0x74, 0xe5, 0xf6, 0x86, 0xf9, 0x1d, 0x34,
	0xe0, 0x99, 0x1c, 0xc1, 0x9e, 0x58, 0xf2, 0x4c, 0xf9, 0x77, 0x31, 0x5a, 0x5f, 0x89, 0x67, 0x31,
	0x99, 0xc1, 0x41, 0x92, 0x6f, 0x12, 0xc9, 0xa2, 0x8c, 0x65, 0x97, 0x8c, 0x2b, 0x8f, 0xde, 0xd4,
	0x99, 0x75, 0xc2, 0xb7, 0xb4, 0xfe, 0x1c, 0xd5, 0x67, 0x31, 0xf9, 0x00, 0x8c, 0x26, 0x2a, 0x23,
	0xf5, 0x31, 0xd2, 0x48, 0x6b, 0x17, 0x3a, 0xde, 0x33, 0x18, 0x14, 0x3c, 0x66, 0x3c, 0x12, 0xb9,
	0xbf, 0x87, 0xf6, 0x3d, 0x94, 0x17, 0x79, 0xf0, 0xb3, 0x03, 0x63, 0xbb, 0x56, 0x71, 0x53, 0xe4,
	0x82, 0xa9, 0x84, 0x97, 0x45, 0xcc, 0xb0, 0xce, 0x5e, 0x88, 0x67, 0xe2, 0xc3, 0x5e, 0xc6, 0x84,
	0xa0, 0xd7, 0x0c, 0x0b, 0xf4, 0xc2, 0x52, 0x24, 0x63, 0xe8, 0x31, 0xce, 0x0b, 0x8e, 0xf5, 0x79,
	0xa1, 0x16, 0xc8, 0x47, 0x70, 0x90, 0x88, 0x28, 0x66, 0x69, 0xb4, 0xc3, 0xad, 0x8b, 0xf1, 0xf6,
	0x13, 0x71, 0xca, 0xd2, 0xf2, 0xe2, 0xe0, 0x02, 0xc6, 0xe7, 0xdb, 0x52, 0xfa, 0x9e, 0xc9, 0x12,
	0xf1, 0x0a, 0x42, 0x8e, 0x85, 0xd0, 0xc3, 0x68, 0x07, 0xbf, 0x38, 0x30, 0xa9, 0x05, 0x7c, 0xc4,
	0xb2, 0x3e, 0x86, 0x6e, 0x4c, 0x25, 0xf5, 0xbb, 0xd3, 0xce, 0x6c, 0x78, 0x32, 0x39, 0x16, 0xab,
	0x63, 0xeb, 0xb2, 0x53, 0x2a, 0x69, 0x88, 0x2e, 0x01, 0x85, 0xc3, 0x86, 0x49, 0xe5, 0x10, 0x53,
	0xc9, 0x4c, 0x49, 0x78, 0x56, 0xdc, 0x52, 0x9c, 0x88, 0x72, 0x9a, 0x95, 0x59, 0x0c, 0x94, 0xe2,
	0x82, 0x66, 0xcc, 0x26, 0x5e, 0xc7, 0x26, 0x9e, 0xc2, 0x0e, 0x2f, 0xf8, 0xe1, 0x86, 0xe5, 0x67,
	0xf9, 0x55, 0xf1, 0x7f, 0xb1, 0xfb, 0xd5, 0x85, 0x49, 0x2d, 0xe0, 0x23, 0x62, 0x37, 0x86, 0x9e,
	0x4c, 0x64, 0xca, 0x0c, 0xe3, 0xb5, 0x40, 0x3e, 0x05, 0x4f, 0xa1, 0x10, 0x25, 0xf9, 0x55, 0xe1,
	0xf7, 0x10, 0xd6, 0x03, 0x05, 0x6b, 0x99, 0xc2, 0x29, 0x95, 0x2c, 0x1c, 0x28, 0x17, 0x25, 0x91,
	0x2f, 0x61, 0xc8, 0xd9, 0x1d, 0xe5, 0xb1, 0xfe, 0xa0, 0xbf, 0xeb, 0x83, 0x95, 0x38, 0xf6, 0x01,
	0xb4, 0x27, 0x7e, 0xf7, 0x39, 0x18, 0x29, 0xba, 0xa3, 0x5b, 0x9c, 0x84, 0xe1, 0xc9, 0xa1, 0xfa,
	0x2c, 0x44, 0xed, 0x8f, 0x74, 0x8b, 0xf5, 0x7a, 0xbc, 0x14, 0x83, 0x3f, 0x1c, 0xd8, 0xb7, 0x8c,
	0x0a, 0xd6, 0x44, 0x44, 0x57, 0x9c, 0x95, 0x38, 0xf4, 0x13, 0xf1, 0x1d, 0x67, 0x8c, 0xbc, 0x07,
	0xc3, 0x5b, 0x1e, 0x29, 0x50, 0xa2, 0x35, 0x4f, 0x0d, 0x1a, 0xde, 0x2d, 0xff, 0xb6, 0x88, 0xd9,
	0x2b, 0x9e, 0x92, 0x0f, 0xe1, 0x49, 0x22, 0x22, 0xb1, 0x2a, 0xee, 0x22, 0x1a, 0xc7, 0x9c, 0x09,
	0x61, 0x96, 0xc1, 0x7e, 0x22, 0x16, 0xab, 0xe2, 0x6e, 0xae, 0x95, 0xe4, 0x6b, 0x18, 0x19, 0xbb,
	0xae, 0xae, 0x8b, 0x69, 0xfa, 0xbb, 0x34, 0x8d, 0xa3, 0xe9, 0x73, 0x38, 0x34, 0xde, 0x2a, 0xbb,
	0xe0, 0x35, 0x8c, 0xaa, 0x98, 0x91, 0xf7, 0x61, 0x28, 0x24, 0x95, 0x6b, 0xa1, 0x89, 0xa5, 0x89,
	0x00, 0x5a, 0x85, 0xd4, 0x2a, 0xb9, 0xe8, 0x56, 0xb8, 0xf8, 0x14, 0xfa, 0xda, 0xc3, 0x24, 0x68,
	0xa4, 0xe0, 0x1e, 0x0e, 0x1b, 0xf8, 0x3e, 0xbc, 0x14, 0x9f, 0x42, 0x9f, 0x6e, 0xa8, 0xa4, 0xdc,
	0xc4, 0x37, 0x92, 0xd2, 0x67, 0xc5, 0x65, 0x92, 0x32, 0x43, 0x0e, 0x23, 0x29, 0xbd, 0xc6, 0xde,
	0xac, 0x09, 0x23, 0x05, 0x5f, 0xc1, 0x91, 0x75, 0xf3, 0x3c, 0x4d, 0x4b, 0x9a, 0xdb, 0x6c, 0x76,
	0xea, 0x6c, 0xfe, 0xcd, 0x01, 0xbf, 0xf9, 0xe9, 0x23, 0x12, 0xfa, 0x13, 0xb3, 0x0c, 0x34, 0x09,
	0xfd, 0x06, 0x09, 0xe7, 0x69, 0x5a, 0xd9, 0x07, 0x6f, 0x6a, 0xc3, 0x6a, 0xac, 0xbb, 0xb1, 0x70,
	0xaa, 0x63, 0xf1, 0x8d, 0xcd, 0x73, 0x17, 0xaf, 0x78, 0xd1, 0x76, 0x45, 0x1b, 0xdd, 0x83, 0x9f,
	0x9a, 0xa5, 0xff, 0xe7, 0xb6, 0x99, 0xf6, 0xb8, 0xf5, 0xf6, 0x98, 0x76, 0x76, 0xea, 0xed, 0x6c,
	0x6d, 0xdb, 0xef, 0x2e, 0x8c, 0xdb, 0x38, 0xab, 0x80, 0xaf, 0xf0, 0x11, 0xcf, 0xd8, 0x8c, 0x44,
	0x6e, 0x4b, 0x26, 0xaa, 0xb3, 0x6a, 0x46, 0x75, 0x56, 0xbc, 0xb0, 0x14, 0x2b, 0x29, 0x76, 0x1b,
	0x29, 0xc6, 0xd8, 0xd4, 0x9e, 0x49, 0x11, 0x25, 0x32, 0x81, 0xbe, 0xbc, 0x8f, 0x52, 0x2a, 0xf1,
	0x81, 0x74, 0xc3, 0x9e, 0xbc, 0x7f, 0x49, 0x65, 0xa9, 0xce, 0xaf, 0x71, 0x1b, 0x68, 0x75, 0x7e,
	0xad, 0x48, 0xb5, 0x2a, 0xd6, 0xc2, 0x2c, 0xa4, 0x81, 0x1e, 0x65, 0xd4, 0xe0, 0x0e, 0xb0, 0x39,
	0xe7, 0xd5, 0xdf, 0xfa, 0x29, 0x8c, 0x70, 0xf3, 0xae, 0x85, 0x7e, 0xba, 0xc1, 0x4c, 0xdd, 0x92,
	0x67, 0xaf, 0x84, 0x7a, 0xb6, 0x83, 0xd7, 0x30, 0xa9, 0xe1, 0xf2, 0x78, 0x8c, 0x3c, 0xf9, 0xcb,
	0x85, 0x27, 0x73, 0xf3, 0x57, 0xb3, 0x60, 0x7c, 0x93, 0x2c, 0x19, 0x99, 0xc3, 0xa8, 0xfa, 0xca,
	0x93, 0xa3, 0x7f, 0x48, 0x64, 0xff, 0xe3, 0xbc, 0xe3, 0x37, 0x0d, 0x26, 0xb5, 0x53, 0xd8, 0xb7,
	0x9e, 0x32, 0xe2, 0x37, 0x1e, 0xbe, 0x32, 0xc8, 0xb3, 0x16, 0xcb, 0x2e, 0x8a, 0xc5, 0x49, 0xd2,
	0x9c, 0x18, 0x2b, 0x4a, 0xfb, 0x4b, 0x74, 0x0e, 0x07, 0x75, 0x66, 0x93, 0xe7, 0x6d, 0x73, 0x51,
	0xc6, 0x7a, 0xd1, 0x6e, 0xdc, 0x25, 0x65, 0xb5, 0x83, 0xfc, 0xeb, 0xb6, 0xd5, 0x49, 0xb5, 0xf6,
	0xee, 0xb2, 0x8f, 0x7f, 0x90, 0x5f, 0xfc, 0x1d, 0x00, 0x00, 0xff, 0xff, 0xfb, 0x46, 0x4f, 0x16,
	0x56, 0x0a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ActivityServiceClient is the client API for ActivityService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ActivityServiceClient interface {
	// 幸运号存储
	LuckyNumSave(ctx context.Context, in *LuckyNumSaveRequest, opts ...grpc.CallOption) (*LuckyNumSaveResponse, error)
	// 我的幸运码
	MyLuckyNumGet(ctx context.Context, in *MyLuckyNumGetRequest, opts ...grpc.CallOption) (*MyLuckyNumGetResponse, error)
	// 开奖信息
	LuckyOpenInfo(ctx context.Context, in *LuckyOpenInfoRequest, opts ...grpc.CallOption) (*LuckyOpenInfoResponse, error)
	// 所有开奖信息
	LuckyOpenInfoAll(ctx context.Context, in *LuckyOpenInfoAllRequest, opts ...grpc.CallOption) (*LuckyOpenInfoAllResponse, error)
	// 中奖地址提交修改
	RewardAddress(ctx context.Context, in *RewardAddressRequest, opts ...grpc.CallOption) (*RewardAddressResponse, error)
}

type activityServiceClient struct {
	cc *grpc.ClientConn
}

func NewActivityServiceClient(cc *grpc.ClientConn) ActivityServiceClient {
	return &activityServiceClient{cc}
}

func (c *activityServiceClient) LuckyNumSave(ctx context.Context, in *LuckyNumSaveRequest, opts ...grpc.CallOption) (*LuckyNumSaveResponse, error) {
	out := new(LuckyNumSaveResponse)
	err := c.cc.Invoke(ctx, "/sh.ActivityService/LuckyNumSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) MyLuckyNumGet(ctx context.Context, in *MyLuckyNumGetRequest, opts ...grpc.CallOption) (*MyLuckyNumGetResponse, error) {
	out := new(MyLuckyNumGetResponse)
	err := c.cc.Invoke(ctx, "/sh.ActivityService/MyLuckyNumGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) LuckyOpenInfo(ctx context.Context, in *LuckyOpenInfoRequest, opts ...grpc.CallOption) (*LuckyOpenInfoResponse, error) {
	out := new(LuckyOpenInfoResponse)
	err := c.cc.Invoke(ctx, "/sh.ActivityService/LuckyOpenInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) LuckyOpenInfoAll(ctx context.Context, in *LuckyOpenInfoAllRequest, opts ...grpc.CallOption) (*LuckyOpenInfoAllResponse, error) {
	out := new(LuckyOpenInfoAllResponse)
	err := c.cc.Invoke(ctx, "/sh.ActivityService/LuckyOpenInfoAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) RewardAddress(ctx context.Context, in *RewardAddressRequest, opts ...grpc.CallOption) (*RewardAddressResponse, error) {
	out := new(RewardAddressResponse)
	err := c.cc.Invoke(ctx, "/sh.ActivityService/RewardAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ActivityServiceServer is the server API for ActivityService service.
type ActivityServiceServer interface {
	// 幸运号存储
	LuckyNumSave(context.Context, *LuckyNumSaveRequest) (*LuckyNumSaveResponse, error)
	// 我的幸运码
	MyLuckyNumGet(context.Context, *MyLuckyNumGetRequest) (*MyLuckyNumGetResponse, error)
	// 开奖信息
	LuckyOpenInfo(context.Context, *LuckyOpenInfoRequest) (*LuckyOpenInfoResponse, error)
	// 所有开奖信息
	LuckyOpenInfoAll(context.Context, *LuckyOpenInfoAllRequest) (*LuckyOpenInfoAllResponse, error)
	// 中奖地址提交修改
	RewardAddress(context.Context, *RewardAddressRequest) (*RewardAddressResponse, error)
}

// UnimplementedActivityServiceServer can be embedded to have forward compatible implementations.
type UnimplementedActivityServiceServer struct {
}

func (*UnimplementedActivityServiceServer) LuckyNumSave(ctx context.Context, req *LuckyNumSaveRequest) (*LuckyNumSaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuckyNumSave not implemented")
}
func (*UnimplementedActivityServiceServer) MyLuckyNumGet(ctx context.Context, req *MyLuckyNumGetRequest) (*MyLuckyNumGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MyLuckyNumGet not implemented")
}
func (*UnimplementedActivityServiceServer) LuckyOpenInfo(ctx context.Context, req *LuckyOpenInfoRequest) (*LuckyOpenInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuckyOpenInfo not implemented")
}
func (*UnimplementedActivityServiceServer) LuckyOpenInfoAll(ctx context.Context, req *LuckyOpenInfoAllRequest) (*LuckyOpenInfoAllResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LuckyOpenInfoAll not implemented")
}
func (*UnimplementedActivityServiceServer) RewardAddress(ctx context.Context, req *RewardAddressRequest) (*RewardAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RewardAddress not implemented")
}

func RegisterActivityServiceServer(s *grpc.Server, srv ActivityServiceServer) {
	s.RegisterService(&_ActivityService_serviceDesc, srv)
}

func _ActivityService_LuckyNumSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuckyNumSaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).LuckyNumSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ActivityService/LuckyNumSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).LuckyNumSave(ctx, req.(*LuckyNumSaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_MyLuckyNumGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MyLuckyNumGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).MyLuckyNumGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ActivityService/MyLuckyNumGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).MyLuckyNumGet(ctx, req.(*MyLuckyNumGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_LuckyOpenInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuckyOpenInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).LuckyOpenInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ActivityService/LuckyOpenInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).LuckyOpenInfo(ctx, req.(*LuckyOpenInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_LuckyOpenInfoAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LuckyOpenInfoAllRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).LuckyOpenInfoAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ActivityService/LuckyOpenInfoAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).LuckyOpenInfoAll(ctx, req.(*LuckyOpenInfoAllRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_RewardAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RewardAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).RewardAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ActivityService/RewardAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).RewardAddress(ctx, req.(*RewardAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ActivityService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sh.ActivityService",
	HandlerType: (*ActivityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LuckyNumSave",
			Handler:    _ActivityService_LuckyNumSave_Handler,
		},
		{
			MethodName: "MyLuckyNumGet",
			Handler:    _ActivityService_MyLuckyNumGet_Handler,
		},
		{
			MethodName: "LuckyOpenInfo",
			Handler:    _ActivityService_LuckyOpenInfo_Handler,
		},
		{
			MethodName: "LuckyOpenInfoAll",
			Handler:    _ActivityService_LuckyOpenInfoAll_Handler,
		},
		{
			MethodName: "RewardAddress",
			Handler:    _ActivityService_RewardAddress_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sh/activity.proto",
}
