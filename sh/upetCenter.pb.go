// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sh/upetCenter.proto

package sh

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type HelloRequest struct {
	Params               string   `protobuf:"bytes,1,opt,name=params,proto3" json:"params"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HelloRequest) Reset()         { *m = HelloRequest{} }
func (m *HelloRequest) String() string { return proto.CompactTextString(m) }
func (*HelloRequest) ProtoMessage()    {}
func (*HelloRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{0}
}

func (m *HelloRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HelloRequest.Unmarshal(m, b)
}
func (m *HelloRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HelloRequest.Marshal(b, m, deterministic)
}
func (m *HelloRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HelloRequest.Merge(m, src)
}
func (m *HelloRequest) XXX_Size() int {
	return xxx_messageInfo_HelloRequest.Size(m)
}
func (m *HelloRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HelloRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HelloRequest proto.InternalMessageInfo

func (m *HelloRequest) GetParams() string {
	if m != nil {
		return m.Params
	}
	return ""
}

type HelloResponse struct {
	Params               string   `protobuf:"bytes,1,opt,name=params,proto3" json:"params"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HelloResponse) Reset()         { *m = HelloResponse{} }
func (m *HelloResponse) String() string { return proto.CompactTextString(m) }
func (*HelloResponse) ProtoMessage()    {}
func (*HelloResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{1}
}

func (m *HelloResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HelloResponse.Unmarshal(m, b)
}
func (m *HelloResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HelloResponse.Marshal(b, m, deterministic)
}
func (m *HelloResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HelloResponse.Merge(m, src)
}
func (m *HelloResponse) XXX_Size() int {
	return xxx_messageInfo_HelloResponse.Size(m)
}
func (m *HelloResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HelloResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HelloResponse proto.InternalMessageInfo

func (m *HelloResponse) GetParams() string {
	if m != nil {
		return m.Params
	}
	return ""
}

type GoodsListReq struct {
	//商品名称
	GoodsName string `protobuf:"bytes,1,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//分类ID
	GcId int32 `protobuf:"varint,2,opt,name=gc_id,json=gcId,proto3" json:"gc_id"`
	//排序类别 1按销量  2按价格
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	//升序还是降序 1升序 2降序
	Sort      int32 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort"`
	PageSize  int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageIndex int32 `protobuf:"varint,6,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//userid, 前端不用传
	ScrmUserid string `protobuf:"bytes,7,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,8,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsListReq) Reset()         { *m = GoodsListReq{} }
func (m *GoodsListReq) String() string { return proto.CompactTextString(m) }
func (*GoodsListReq) ProtoMessage()    {}
func (*GoodsListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{2}
}

func (m *GoodsListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsListReq.Unmarshal(m, b)
}
func (m *GoodsListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsListReq.Marshal(b, m, deterministic)
}
func (m *GoodsListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsListReq.Merge(m, src)
}
func (m *GoodsListReq) XXX_Size() int {
	return xxx_messageInfo_GoodsListReq.Size(m)
}
func (m *GoodsListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsListReq proto.InternalMessageInfo

func (m *GoodsListReq) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *GoodsListReq) GetGcId() int32 {
	if m != nil {
		return m.GcId
	}
	return 0
}

func (m *GoodsListReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GoodsListReq) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *GoodsListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GoodsListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GoodsListReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

func (m *GoodsListReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GoodsListRes struct {
	Msg                  string           `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Total                int32            `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	Data                 []*GoodsListData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GoodsListRes) Reset()         { *m = GoodsListRes{} }
func (m *GoodsListRes) String() string { return proto.CompactTextString(m) }
func (*GoodsListRes) ProtoMessage()    {}
func (*GoodsListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{3}
}

func (m *GoodsListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsListRes.Unmarshal(m, b)
}
func (m *GoodsListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsListRes.Marshal(b, m, deterministic)
}
func (m *GoodsListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsListRes.Merge(m, src)
}
func (m *GoodsListRes) XXX_Size() int {
	return xxx_messageInfo_GoodsListRes.Size(m)
}
func (m *GoodsListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsListRes.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsListRes proto.InternalMessageInfo

func (m *GoodsListRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *GoodsListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GoodsListRes) GetData() []*GoodsListData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GoodsListData struct {
	//商品id(SKU)
	GoodsId int64 `protobuf:"varint,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	//商品名称
	GoodsName string `protobuf:"bytes,2,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	//商品价格(原价)
	GoodsPrice float32 `protobuf:"fixed32,3,opt,name=goods_price,json=goodsPrice,proto3" json:"goods_price"`
	//商品主图
	GoodsImage string `protobuf:"bytes,4,opt,name=goods_image,json=goodsImage,proto3" json:"goods_image"`
	//会员价
	MemberPrice float32 `protobuf:"fixed32,5,opt,name=member_price,json=memberPrice,proto3" json:"member_price"`
	//0普通商品，1实实组合，2虚虚组合，3虚实组合
	GoodsType int32 `protobuf:"varint,6,opt,name=goods_type,json=goodsType,proto3" json:"goods_type"`
	//普通商品中有实物，有虚拟 0-实物 1-虚拟
	IsVirtual int32 `protobuf:"varint,7,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	//商品促销价格
	GoodsPromotionPrice float32 `protobuf:"fixed32,8,opt,name=goods_promotion_price,json=goodsPromotionPrice,proto3" json:"goods_promotion_price"`
	//促销类型 0无促销，1团购，2限时折扣 12水印
	GoodsPromotionType int32 `protobuf:"varint,9,opt,name=goods_promotion_type,json=goodsPromotionType,proto3" json:"goods_promotion_type"`
	//销售数量
	GoodsSalenum int32 `protobuf:"varint,10,opt,name=goods_salenum,json=goodsSalenum,proto3" json:"goods_salenum"`
	//商品广告词
	GoodsJingle string `protobuf:"bytes,11,opt,name=goods_jingle,json=goodsJingle,proto3" json:"goods_jingle"`
	//商品简称
	ShortName string `protobuf:"bytes,12,opt,name=short_name,json=shortName,proto3" json:"short_name"`
	//是否启用会员价 1启用，0不启用
	EnableMemberPrice int32 `protobuf:"varint,13,opt,name=enable_member_price,json=enableMemberPrice,proto3" json:"enable_member_price"`
	//商品common_id
	GoodsCommonid int64 `protobuf:"varint,14,opt,name=goods_commonid,json=goodsCommonid,proto3" json:"goods_commonid"`
	//颜色规格id
	ColorId int64 `protobuf:"varint,15,opt,name=color_id,json=colorId,proto3" json:"color_id"`
	//店铺id
	StoreId int64 `protobuf:"varint,16,opt,name=store_id,json=storeId,proto3" json:"store_id"`
	// 会员价是否生效
	IsMemberPrice        int32    `protobuf:"varint,17,opt,name=is_member_price,json=isMemberPrice,proto3" json:"is_member_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsListData) Reset()         { *m = GoodsListData{} }
func (m *GoodsListData) String() string { return proto.CompactTextString(m) }
func (*GoodsListData) ProtoMessage()    {}
func (*GoodsListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{4}
}

func (m *GoodsListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsListData.Unmarshal(m, b)
}
func (m *GoodsListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsListData.Marshal(b, m, deterministic)
}
func (m *GoodsListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsListData.Merge(m, src)
}
func (m *GoodsListData) XXX_Size() int {
	return xxx_messageInfo_GoodsListData.Size(m)
}
func (m *GoodsListData) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsListData.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsListData proto.InternalMessageInfo

func (m *GoodsListData) GetGoodsId() int64 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

func (m *GoodsListData) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *GoodsListData) GetGoodsPrice() float32 {
	if m != nil {
		return m.GoodsPrice
	}
	return 0
}

func (m *GoodsListData) GetGoodsImage() string {
	if m != nil {
		return m.GoodsImage
	}
	return ""
}

func (m *GoodsListData) GetMemberPrice() float32 {
	if m != nil {
		return m.MemberPrice
	}
	return 0
}

func (m *GoodsListData) GetGoodsType() int32 {
	if m != nil {
		return m.GoodsType
	}
	return 0
}

func (m *GoodsListData) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *GoodsListData) GetGoodsPromotionPrice() float32 {
	if m != nil {
		return m.GoodsPromotionPrice
	}
	return 0
}

func (m *GoodsListData) GetGoodsPromotionType() int32 {
	if m != nil {
		return m.GoodsPromotionType
	}
	return 0
}

func (m *GoodsListData) GetGoodsSalenum() int32 {
	if m != nil {
		return m.GoodsSalenum
	}
	return 0
}

func (m *GoodsListData) GetGoodsJingle() string {
	if m != nil {
		return m.GoodsJingle
	}
	return ""
}

func (m *GoodsListData) GetShortName() string {
	if m != nil {
		return m.ShortName
	}
	return ""
}

func (m *GoodsListData) GetEnableMemberPrice() int32 {
	if m != nil {
		return m.EnableMemberPrice
	}
	return 0
}

func (m *GoodsListData) GetGoodsCommonid() int64 {
	if m != nil {
		return m.GoodsCommonid
	}
	return 0
}

func (m *GoodsListData) GetColorId() int64 {
	if m != nil {
		return m.ColorId
	}
	return 0
}

func (m *GoodsListData) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *GoodsListData) GetIsMemberPrice() int32 {
	if m != nil {
		return m.IsMemberPrice
	}
	return 0
}

type GoodsClassListReq struct {
	//分类ID（默认为0，获取全部一级分类）
	GcId int32 `protobuf:"varint,1,opt,name=gc_id,json=gcId,proto3" json:"gc_id"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsClassListReq) Reset()         { *m = GoodsClassListReq{} }
func (m *GoodsClassListReq) String() string { return proto.CompactTextString(m) }
func (*GoodsClassListReq) ProtoMessage()    {}
func (*GoodsClassListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{5}
}

func (m *GoodsClassListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsClassListReq.Unmarshal(m, b)
}
func (m *GoodsClassListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsClassListReq.Marshal(b, m, deterministic)
}
func (m *GoodsClassListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsClassListReq.Merge(m, src)
}
func (m *GoodsClassListReq) XXX_Size() int {
	return xxx_messageInfo_GoodsClassListReq.Size(m)
}
func (m *GoodsClassListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsClassListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsClassListReq proto.InternalMessageInfo

func (m *GoodsClassListReq) GetGcId() int32 {
	if m != nil {
		return m.GcId
	}
	return 0
}

func (m *GoodsClassListReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GoodsClassListRes struct {
	Msg                  string            `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 []*GoodsClassList `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GoodsClassListRes) Reset()         { *m = GoodsClassListRes{} }
func (m *GoodsClassListRes) String() string { return proto.CompactTextString(m) }
func (*GoodsClassListRes) ProtoMessage()    {}
func (*GoodsClassListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{6}
}

func (m *GoodsClassListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsClassListRes.Unmarshal(m, b)
}
func (m *GoodsClassListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsClassListRes.Marshal(b, m, deterministic)
}
func (m *GoodsClassListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsClassListRes.Merge(m, src)
}
func (m *GoodsClassListRes) XXX_Size() int {
	return xxx_messageInfo_GoodsClassListRes.Size(m)
}
func (m *GoodsClassListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsClassListRes.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsClassListRes proto.InternalMessageInfo

func (m *GoodsClassListRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *GoodsClassListRes) GetData() []*GoodsClassList {
	if m != nil {
		return m.Data
	}
	return nil
}

type GoodsClassList struct {
	//分类ID
	GcId int32 `protobuf:"varint,1,opt,name=gc_id,json=gcId,proto3" json:"gc_id"`
	//分类名称
	GcName               string   `protobuf:"bytes,2,opt,name=gc_name,json=gcName,proto3" json:"gc_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsClassList) Reset()         { *m = GoodsClassList{} }
func (m *GoodsClassList) String() string { return proto.CompactTextString(m) }
func (*GoodsClassList) ProtoMessage()    {}
func (*GoodsClassList) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{7}
}

func (m *GoodsClassList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsClassList.Unmarshal(m, b)
}
func (m *GoodsClassList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsClassList.Marshal(b, m, deterministic)
}
func (m *GoodsClassList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsClassList.Merge(m, src)
}
func (m *GoodsClassList) XXX_Size() int {
	return xxx_messageInfo_GoodsClassList.Size(m)
}
func (m *GoodsClassList) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsClassList.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsClassList proto.InternalMessageInfo

func (m *GoodsClassList) GetGcId() int32 {
	if m != nil {
		return m.GcId
	}
	return 0
}

func (m *GoodsClassList) GetGcName() string {
	if m != nil {
		return m.GcName
	}
	return ""
}

type GoodsRelevanceRequest struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//规格文字
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	//视频
	Video string `protobuf:"bytes,4,opt,name=video,proto3" json:"video"`
	//图片
	Imgs string `protobuf:"bytes,5,opt,name=imgs,proto3" json:"imgs"`
	//商品规格
	GoodsSkus            []*ManyGoodSku `protobuf:"bytes,6,rep,name=goods_skus,json=goodsSkus,proto3" json:"goods_skus"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GoodsRelevanceRequest) Reset()         { *m = GoodsRelevanceRequest{} }
func (m *GoodsRelevanceRequest) String() string { return proto.CompactTextString(m) }
func (*GoodsRelevanceRequest) ProtoMessage()    {}
func (*GoodsRelevanceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{8}
}

func (m *GoodsRelevanceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsRelevanceRequest.Unmarshal(m, b)
}
func (m *GoodsRelevanceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsRelevanceRequest.Marshal(b, m, deterministic)
}
func (m *GoodsRelevanceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsRelevanceRequest.Merge(m, src)
}
func (m *GoodsRelevanceRequest) XXX_Size() int {
	return xxx_messageInfo_GoodsRelevanceRequest.Size(m)
}
func (m *GoodsRelevanceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsRelevanceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsRelevanceRequest proto.InternalMessageInfo

func (m *GoodsRelevanceRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GoodsRelevanceRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GoodsRelevanceRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GoodsRelevanceRequest) GetVideo() string {
	if m != nil {
		return m.Video
	}
	return ""
}

func (m *GoodsRelevanceRequest) GetImgs() string {
	if m != nil {
		return m.Imgs
	}
	return ""
}

func (m *GoodsRelevanceRequest) GetGoodsSkus() []*ManyGoodSku {
	if m != nil {
		return m.GoodsSkus
	}
	return nil
}

type GoodsRelevanceDeleteRequest struct {
	//关联名称索引ID
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsRelevanceDeleteRequest) Reset()         { *m = GoodsRelevanceDeleteRequest{} }
func (m *GoodsRelevanceDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*GoodsRelevanceDeleteRequest) ProtoMessage()    {}
func (*GoodsRelevanceDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{9}
}

func (m *GoodsRelevanceDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsRelevanceDeleteRequest.Unmarshal(m, b)
}
func (m *GoodsRelevanceDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsRelevanceDeleteRequest.Marshal(b, m, deterministic)
}
func (m *GoodsRelevanceDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsRelevanceDeleteRequest.Merge(m, src)
}
func (m *GoodsRelevanceDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_GoodsRelevanceDeleteRequest.Size(m)
}
func (m *GoodsRelevanceDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsRelevanceDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsRelevanceDeleteRequest proto.InternalMessageInfo

func (m *GoodsRelevanceDeleteRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type ManyGoodSku struct {
	GoodsId              int32    `protobuf:"varint,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	RelevanceSkuSort     string   `protobuf:"bytes,2,opt,name=relevance_sku_sort,json=relevanceSkuSort,proto3" json:"relevance_sku_sort"`
	GoodsSpec            int32    `protobuf:"varint,3,opt,name=goods_spec,json=goodsSpec,proto3" json:"goods_spec"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManyGoodSku) Reset()         { *m = ManyGoodSku{} }
func (m *ManyGoodSku) String() string { return proto.CompactTextString(m) }
func (*ManyGoodSku) ProtoMessage()    {}
func (*ManyGoodSku) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{10}
}

func (m *ManyGoodSku) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManyGoodSku.Unmarshal(m, b)
}
func (m *ManyGoodSku) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManyGoodSku.Marshal(b, m, deterministic)
}
func (m *ManyGoodSku) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManyGoodSku.Merge(m, src)
}
func (m *ManyGoodSku) XXX_Size() int {
	return xxx_messageInfo_ManyGoodSku.Size(m)
}
func (m *ManyGoodSku) XXX_DiscardUnknown() {
	xxx_messageInfo_ManyGoodSku.DiscardUnknown(m)
}

var xxx_messageInfo_ManyGoodSku proto.InternalMessageInfo

func (m *ManyGoodSku) GetGoodsId() int32 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

func (m *ManyGoodSku) GetRelevanceSkuSort() string {
	if m != nil {
		return m.RelevanceSkuSort
	}
	return ""
}

func (m *ManyGoodSku) GetGoodsSpec() int32 {
	if m != nil {
		return m.GoodsSpec
	}
	return 0
}

//关联sku列表请求
type SkuRelRequest struct {
	//名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	//查询方式
	Type                 string   `protobuf:"bytes,2,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuRelRequest) Reset()         { *m = SkuRelRequest{} }
func (m *SkuRelRequest) String() string { return proto.CompactTextString(m) }
func (*SkuRelRequest) ProtoMessage()    {}
func (*SkuRelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{11}
}

func (m *SkuRelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuRelRequest.Unmarshal(m, b)
}
func (m *SkuRelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuRelRequest.Marshal(b, m, deterministic)
}
func (m *SkuRelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuRelRequest.Merge(m, src)
}
func (m *SkuRelRequest) XXX_Size() int {
	return xxx_messageInfo_SkuRelRequest.Size(m)
}
func (m *SkuRelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuRelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SkuRelRequest proto.InternalMessageInfo

func (m *SkuRelRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SkuRelRequest) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

//关联sku列表响应
type SkuRelResponse struct {
	//code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息提示
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string        `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 []*SkuRelList `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SkuRelResponse) Reset()         { *m = SkuRelResponse{} }
func (m *SkuRelResponse) String() string { return proto.CompactTextString(m) }
func (*SkuRelResponse) ProtoMessage()    {}
func (*SkuRelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{12}
}

func (m *SkuRelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuRelResponse.Unmarshal(m, b)
}
func (m *SkuRelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuRelResponse.Marshal(b, m, deterministic)
}
func (m *SkuRelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuRelResponse.Merge(m, src)
}
func (m *SkuRelResponse) XXX_Size() int {
	return xxx_messageInfo_SkuRelResponse.Size(m)
}
func (m *SkuRelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuRelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SkuRelResponse proto.InternalMessageInfo

func (m *SkuRelResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SkuRelResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SkuRelResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *SkuRelResponse) GetData() []*SkuRelList {
	if m != nil {
		return m.Data
	}
	return nil
}

//关联sku列表
type SkuRelList struct {
	//关联id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//创建时间
	CreateTime string `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//最后编辑时间
	UpdateTime           string   `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuRelList) Reset()         { *m = SkuRelList{} }
func (m *SkuRelList) String() string { return proto.CompactTextString(m) }
func (*SkuRelList) ProtoMessage()    {}
func (*SkuRelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{13}
}

func (m *SkuRelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuRelList.Unmarshal(m, b)
}
func (m *SkuRelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuRelList.Marshal(b, m, deterministic)
}
func (m *SkuRelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuRelList.Merge(m, src)
}
func (m *SkuRelList) XXX_Size() int {
	return xxx_messageInfo_SkuRelList.Size(m)
}
func (m *SkuRelList) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuRelList.DiscardUnknown(m)
}

var xxx_messageInfo_SkuRelList proto.InternalMessageInfo

func (m *SkuRelList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SkuRelList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SkuRelList) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *SkuRelList) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

//关联sku列表
type SkuRelListRequest struct {
	//当前页
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页数据量
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//搜索类型
	KeywordType int32 `protobuf:"varint,3,opt,name=keyword_type,json=keywordType,proto3" json:"keyword_type"`
	//关键字
	Keyword string `protobuf:"bytes,4,opt,name=keyword,proto3" json:"keyword"`
	//电商店铺id
	StoreId              int32    `protobuf:"varint,5,opt,name=store_id,json=storeId,proto3" json:"store_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuRelListRequest) Reset()         { *m = SkuRelListRequest{} }
func (m *SkuRelListRequest) String() string { return proto.CompactTextString(m) }
func (*SkuRelListRequest) ProtoMessage()    {}
func (*SkuRelListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{14}
}

func (m *SkuRelListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuRelListRequest.Unmarshal(m, b)
}
func (m *SkuRelListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuRelListRequest.Marshal(b, m, deterministic)
}
func (m *SkuRelListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuRelListRequest.Merge(m, src)
}
func (m *SkuRelListRequest) XXX_Size() int {
	return xxx_messageInfo_SkuRelListRequest.Size(m)
}
func (m *SkuRelListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuRelListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SkuRelListRequest proto.InternalMessageInfo

func (m *SkuRelListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *SkuRelListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *SkuRelListRequest) GetKeywordType() int32 {
	if m != nil {
		return m.KeywordType
	}
	return 0
}

func (m *SkuRelListRequest) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *SkuRelListRequest) GetStoreId() int32 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

type BaseResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details              []string `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{15}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *BaseResponse) GetDetails() []string {
	if m != nil {
		return m.Details
	}
	return nil
}

type SkuRelListResponse struct {
	//code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息提示
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 []*GoodsRelevancesList `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	TotalCount           int32                  `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SkuRelListResponse) Reset()         { *m = SkuRelListResponse{} }
func (m *SkuRelListResponse) String() string { return proto.CompactTextString(m) }
func (*SkuRelListResponse) ProtoMessage()    {}
func (*SkuRelListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{16}
}

func (m *SkuRelListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuRelListResponse.Unmarshal(m, b)
}
func (m *SkuRelListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuRelListResponse.Marshal(b, m, deterministic)
}
func (m *SkuRelListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuRelListResponse.Merge(m, src)
}
func (m *SkuRelListResponse) XXX_Size() int {
	return xxx_messageInfo_SkuRelListResponse.Size(m)
}
func (m *SkuRelListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuRelListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SkuRelListResponse proto.InternalMessageInfo

func (m *SkuRelListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SkuRelListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SkuRelListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *SkuRelListResponse) GetData() []*GoodsRelevancesList {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *SkuRelListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type GoodsRelevancesList struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//规格文字
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	//添加时间
	Addtime string `protobuf:"bytes,4,opt,name=addtime,proto3" json:"addtime"`
	//更新时间
	Edittime             string   `protobuf:"bytes,5,opt,name=edittime,proto3" json:"edittime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsRelevancesList) Reset()         { *m = GoodsRelevancesList{} }
func (m *GoodsRelevancesList) String() string { return proto.CompactTextString(m) }
func (*GoodsRelevancesList) ProtoMessage()    {}
func (*GoodsRelevancesList) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{17}
}

func (m *GoodsRelevancesList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsRelevancesList.Unmarshal(m, b)
}
func (m *GoodsRelevancesList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsRelevancesList.Marshal(b, m, deterministic)
}
func (m *GoodsRelevancesList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsRelevancesList.Merge(m, src)
}
func (m *GoodsRelevancesList) XXX_Size() int {
	return xxx_messageInfo_GoodsRelevancesList.Size(m)
}
func (m *GoodsRelevancesList) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsRelevancesList.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsRelevancesList proto.InternalMessageInfo

func (m *GoodsRelevancesList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GoodsRelevancesList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GoodsRelevancesList) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GoodsRelevancesList) GetAddtime() string {
	if m != nil {
		return m.Addtime
	}
	return ""
}

func (m *GoodsRelevancesList) GetEdittime() string {
	if m != nil {
		return m.Edittime
	}
	return ""
}

type GoodsRevanceSkuRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsRevanceSkuRequest) Reset()         { *m = GoodsRevanceSkuRequest{} }
func (m *GoodsRevanceSkuRequest) String() string { return proto.CompactTextString(m) }
func (*GoodsRevanceSkuRequest) ProtoMessage()    {}
func (*GoodsRevanceSkuRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{18}
}

func (m *GoodsRevanceSkuRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsRevanceSkuRequest.Unmarshal(m, b)
}
func (m *GoodsRevanceSkuRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsRevanceSkuRequest.Marshal(b, m, deterministic)
}
func (m *GoodsRevanceSkuRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsRevanceSkuRequest.Merge(m, src)
}
func (m *GoodsRevanceSkuRequest) XXX_Size() int {
	return xxx_messageInfo_GoodsRevanceSkuRequest.Size(m)
}
func (m *GoodsRevanceSkuRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsRevanceSkuRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsRevanceSkuRequest proto.InternalMessageInfo

func (m *GoodsRevanceSkuRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 商品更新到ES
type GoodsToEsUpdateRequest struct {
	// 多个skuids，以英文逗号分割
	Ids string `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids"`
	//活动商品
	GoodsDate []*GoodsActivity `protobuf:"bytes,2,rep,name=goods_date,json=goodsDate,proto3" json:"goods_date"`
	//是否VIP商品  0否，1是
	IsVip int32 `protobuf:"varint,3,opt,name=IsVip,proto3" json:"IsVip"`
	//电商机构ID
	DsOrgId int32 `protobuf:"varint,4,opt,name=DsOrgId,proto3" json:"DsOrgId"`
	//阿闻机构ID
	AwOrgId int32 `protobuf:"varint,5,opt,name=AwOrgId,proto3" json:"AwOrgId"`
	//店铺id
	ShopId               int32    `protobuf:"varint,6,opt,name=ShopId,proto3" json:"ShopId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsToEsUpdateRequest) Reset()         { *m = GoodsToEsUpdateRequest{} }
func (m *GoodsToEsUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*GoodsToEsUpdateRequest) ProtoMessage()    {}
func (*GoodsToEsUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{19}
}

func (m *GoodsToEsUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsToEsUpdateRequest.Unmarshal(m, b)
}
func (m *GoodsToEsUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsToEsUpdateRequest.Marshal(b, m, deterministic)
}
func (m *GoodsToEsUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsToEsUpdateRequest.Merge(m, src)
}
func (m *GoodsToEsUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_GoodsToEsUpdateRequest.Size(m)
}
func (m *GoodsToEsUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsToEsUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsToEsUpdateRequest proto.InternalMessageInfo

func (m *GoodsToEsUpdateRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

func (m *GoodsToEsUpdateRequest) GetGoodsDate() []*GoodsActivity {
	if m != nil {
		return m.GoodsDate
	}
	return nil
}

func (m *GoodsToEsUpdateRequest) GetIsVip() int32 {
	if m != nil {
		return m.IsVip
	}
	return 0
}

func (m *GoodsToEsUpdateRequest) GetDsOrgId() int32 {
	if m != nil {
		return m.DsOrgId
	}
	return 0
}

func (m *GoodsToEsUpdateRequest) GetAwOrgId() int32 {
	if m != nil {
		return m.AwOrgId
	}
	return 0
}

func (m *GoodsToEsUpdateRequest) GetShopId() int32 {
	if m != nil {
		return m.ShopId
	}
	return 0
}

type GoodsActivity struct {
	SkuId       string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	Price       int32  `protobuf:"varint,2,opt,name=price,proto3" json:"price"`
	IsEffective int32  `protobuf:"varint,3,opt,name=is_effective,json=isEffective,proto3" json:"is_effective"`
	//0：默认 1：预售
	Type                 int32    `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsActivity) Reset()         { *m = GoodsActivity{} }
func (m *GoodsActivity) String() string { return proto.CompactTextString(m) }
func (*GoodsActivity) ProtoMessage()    {}
func (*GoodsActivity) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{20}
}

func (m *GoodsActivity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsActivity.Unmarshal(m, b)
}
func (m *GoodsActivity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsActivity.Marshal(b, m, deterministic)
}
func (m *GoodsActivity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsActivity.Merge(m, src)
}
func (m *GoodsActivity) XXX_Size() int {
	return xxx_messageInfo_GoodsActivity.Size(m)
}
func (m *GoodsActivity) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsActivity.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsActivity proto.InternalMessageInfo

func (m *GoodsActivity) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *GoodsActivity) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GoodsActivity) GetIsEffective() int32 {
	if m != nil {
		return m.IsEffective
	}
	return 0
}

func (m *GoodsActivity) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetGoodsClassesRequest struct {
	GcIds                []int64  `protobuf:"varint,1,rep,packed,name=gc_ids,json=gcIds,proto3" json:"gc_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGoodsClassesRequest) Reset()         { *m = GetGoodsClassesRequest{} }
func (m *GetGoodsClassesRequest) String() string { return proto.CompactTextString(m) }
func (*GetGoodsClassesRequest) ProtoMessage()    {}
func (*GetGoodsClassesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{21}
}

func (m *GetGoodsClassesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGoodsClassesRequest.Unmarshal(m, b)
}
func (m *GetGoodsClassesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGoodsClassesRequest.Marshal(b, m, deterministic)
}
func (m *GetGoodsClassesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGoodsClassesRequest.Merge(m, src)
}
func (m *GetGoodsClassesRequest) XXX_Size() int {
	return xxx_messageInfo_GetGoodsClassesRequest.Size(m)
}
func (m *GetGoodsClassesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGoodsClassesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGoodsClassesRequest proto.InternalMessageInfo

func (m *GetGoodsClassesRequest) GetGcIds() []int64 {
	if m != nil {
		return m.GcIds
	}
	return nil
}

type GetGoodsClassesResponse struct {
	Code                 int64                         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *GetGoodsClassesResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetGoodsClassesResponse) Reset()         { *m = GetGoodsClassesResponse{} }
func (m *GetGoodsClassesResponse) String() string { return proto.CompactTextString(m) }
func (*GetGoodsClassesResponse) ProtoMessage()    {}
func (*GetGoodsClassesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{22}
}

func (m *GetGoodsClassesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGoodsClassesResponse.Unmarshal(m, b)
}
func (m *GetGoodsClassesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGoodsClassesResponse.Marshal(b, m, deterministic)
}
func (m *GetGoodsClassesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGoodsClassesResponse.Merge(m, src)
}
func (m *GetGoodsClassesResponse) XXX_Size() int {
	return xxx_messageInfo_GetGoodsClassesResponse.Size(m)
}
func (m *GetGoodsClassesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGoodsClassesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGoodsClassesResponse proto.InternalMessageInfo

func (m *GetGoodsClassesResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGoodsClassesResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGoodsClassesResponse) GetData() *GetGoodsClassesResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetGoodsClassesResponse_Data struct {
	ParentGoodsClass     []*GetGoodsClassesResponse_GoodsClass `protobuf:"bytes,1,rep,name=parent_goods_class,json=parentGoodsClass,proto3" json:"parent_goods_class"`
	ChildrenList         []*GetGoodsClassesResponse_GoodsClass `protobuf:"bytes,2,rep,name=children_list,json=childrenList,proto3" json:"children_list"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *GetGoodsClassesResponse_Data) Reset()         { *m = GetGoodsClassesResponse_Data{} }
func (m *GetGoodsClassesResponse_Data) String() string { return proto.CompactTextString(m) }
func (*GetGoodsClassesResponse_Data) ProtoMessage()    {}
func (*GetGoodsClassesResponse_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{22, 0}
}

func (m *GetGoodsClassesResponse_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGoodsClassesResponse_Data.Unmarshal(m, b)
}
func (m *GetGoodsClassesResponse_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGoodsClassesResponse_Data.Marshal(b, m, deterministic)
}
func (m *GetGoodsClassesResponse_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGoodsClassesResponse_Data.Merge(m, src)
}
func (m *GetGoodsClassesResponse_Data) XXX_Size() int {
	return xxx_messageInfo_GetGoodsClassesResponse_Data.Size(m)
}
func (m *GetGoodsClassesResponse_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGoodsClassesResponse_Data.DiscardUnknown(m)
}

var xxx_messageInfo_GetGoodsClassesResponse_Data proto.InternalMessageInfo

func (m *GetGoodsClassesResponse_Data) GetParentGoodsClass() []*GetGoodsClassesResponse_GoodsClass {
	if m != nil {
		return m.ParentGoodsClass
	}
	return nil
}

func (m *GetGoodsClassesResponse_Data) GetChildrenList() []*GetGoodsClassesResponse_GoodsClass {
	if m != nil {
		return m.ChildrenList
	}
	return nil
}

type GetGoodsClassesResponse_GoodsClass struct {
	GcId                 int64    `protobuf:"varint,1,opt,name=gc_id,json=gcId,proto3" json:"gc_id"`
	GcName               string   `protobuf:"bytes,2,opt,name=gc_name,json=gcName,proto3" json:"gc_name"`
	GcParentId           int64    `protobuf:"varint,3,opt,name=gc_parent_id,json=gcParentId,proto3" json:"gc_parent_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGoodsClassesResponse_GoodsClass) Reset()         { *m = GetGoodsClassesResponse_GoodsClass{} }
func (m *GetGoodsClassesResponse_GoodsClass) String() string { return proto.CompactTextString(m) }
func (*GetGoodsClassesResponse_GoodsClass) ProtoMessage()    {}
func (*GetGoodsClassesResponse_GoodsClass) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{22, 1}
}

func (m *GetGoodsClassesResponse_GoodsClass) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGoodsClassesResponse_GoodsClass.Unmarshal(m, b)
}
func (m *GetGoodsClassesResponse_GoodsClass) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGoodsClassesResponse_GoodsClass.Marshal(b, m, deterministic)
}
func (m *GetGoodsClassesResponse_GoodsClass) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGoodsClassesResponse_GoodsClass.Merge(m, src)
}
func (m *GetGoodsClassesResponse_GoodsClass) XXX_Size() int {
	return xxx_messageInfo_GetGoodsClassesResponse_GoodsClass.Size(m)
}
func (m *GetGoodsClassesResponse_GoodsClass) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGoodsClassesResponse_GoodsClass.DiscardUnknown(m)
}

var xxx_messageInfo_GetGoodsClassesResponse_GoodsClass proto.InternalMessageInfo

func (m *GetGoodsClassesResponse_GoodsClass) GetGcId() int64 {
	if m != nil {
		return m.GcId
	}
	return 0
}

func (m *GetGoodsClassesResponse_GoodsClass) GetGcName() string {
	if m != nil {
		return m.GcName
	}
	return ""
}

func (m *GetGoodsClassesResponse_GoodsClass) GetGcParentId() int64 {
	if m != nil {
		return m.GcParentId
	}
	return 0
}

type UserLevelRequest struct {
	ScrmUserId           string   `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	WithEquity           int64    `protobuf:"varint,2,opt,name=with_equity,json=withEquity,proto3" json:"with_equity"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelRequest) Reset()         { *m = UserLevelRequest{} }
func (m *UserLevelRequest) String() string { return proto.CompactTextString(m) }
func (*UserLevelRequest) ProtoMessage()    {}
func (*UserLevelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{23}
}

func (m *UserLevelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelRequest.Unmarshal(m, b)
}
func (m *UserLevelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelRequest.Marshal(b, m, deterministic)
}
func (m *UserLevelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelRequest.Merge(m, src)
}
func (m *UserLevelRequest) XXX_Size() int {
	return xxx_messageInfo_UserLevelRequest.Size(m)
}
func (m *UserLevelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelRequest proto.InternalMessageInfo

func (m *UserLevelRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *UserLevelRequest) GetWithEquity() int64 {
	if m != nil {
		return m.WithEquity
	}
	return 0
}

type UserEquity struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 权益名称
	EquityName string `protobuf:"bytes,2,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	// 权益icon
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	// 权益简介
	EquityInfo           string   `protobuf:"bytes,4,opt,name=equity_info,json=equityInfo,proto3" json:"equity_info"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserEquity) Reset()         { *m = UserEquity{} }
func (m *UserEquity) String() string { return proto.CompactTextString(m) }
func (*UserEquity) ProtoMessage()    {}
func (*UserEquity) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{24}
}

func (m *UserEquity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEquity.Unmarshal(m, b)
}
func (m *UserEquity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEquity.Marshal(b, m, deterministic)
}
func (m *UserEquity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEquity.Merge(m, src)
}
func (m *UserEquity) XXX_Size() int {
	return xxx_messageInfo_UserEquity.Size(m)
}
func (m *UserEquity) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEquity.DiscardUnknown(m)
}

var xxx_messageInfo_UserEquity proto.InternalMessageInfo

func (m *UserEquity) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserEquity) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *UserEquity) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *UserEquity) GetEquityInfo() string {
	if m != nil {
		return m.EquityInfo
	}
	return ""
}

type UserLevelResponse struct {
	Code                 int64         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	UserLevel            string        `protobuf:"bytes,3,opt,name=user_level,json=userLevel,proto3" json:"user_level"`
	UserLevelId          int64         `protobuf:"varint,4,opt,name=user_level_id,json=userLevelId,proto3" json:"user_level_id"`
	UserLevelStime       int64         `protobuf:"varint,5,opt,name=user_level_stime,json=userLevelStime,proto3" json:"user_level_stime"`
	UserLevelEtime       int64         `protobuf:"varint,6,opt,name=user_level_etime,json=userLevelEtime,proto3" json:"user_level_etime"`
	UserEquities         []*UserEquity `protobuf:"bytes,7,rep,name=user_equities,json=userEquities,proto3" json:"user_equities"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserLevelResponse) Reset()         { *m = UserLevelResponse{} }
func (m *UserLevelResponse) String() string { return proto.CompactTextString(m) }
func (*UserLevelResponse) ProtoMessage()    {}
func (*UserLevelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{25}
}

func (m *UserLevelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelResponse.Unmarshal(m, b)
}
func (m *UserLevelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelResponse.Marshal(b, m, deterministic)
}
func (m *UserLevelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelResponse.Merge(m, src)
}
func (m *UserLevelResponse) XXX_Size() int {
	return xxx_messageInfo_UserLevelResponse.Size(m)
}
func (m *UserLevelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelResponse proto.InternalMessageInfo

func (m *UserLevelResponse) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserLevelResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserLevelResponse) GetUserLevel() string {
	if m != nil {
		return m.UserLevel
	}
	return ""
}

func (m *UserLevelResponse) GetUserLevelId() int64 {
	if m != nil {
		return m.UserLevelId
	}
	return 0
}

func (m *UserLevelResponse) GetUserLevelStime() int64 {
	if m != nil {
		return m.UserLevelStime
	}
	return 0
}

func (m *UserLevelResponse) GetUserLevelEtime() int64 {
	if m != nil {
		return m.UserLevelEtime
	}
	return 0
}

func (m *UserLevelResponse) GetUserEquities() []*UserEquity {
	if m != nil {
		return m.UserEquities
	}
	return nil
}

//查询对应等级的会员价
type ProductMemberPriceRequest struct {
	// 商品Sku列表
	SkuIds      []string `protobuf:"bytes,1,rep,name=sku_ids,json=skuIds,proto3" json:"sku_ids"`
	MemberLevel int32    `protobuf:"varint,2,opt,name=member_level,json=memberLevel,proto3" json:"member_level"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductMemberPriceRequest) Reset()         { *m = ProductMemberPriceRequest{} }
func (m *ProductMemberPriceRequest) String() string { return proto.CompactTextString(m) }
func (*ProductMemberPriceRequest) ProtoMessage()    {}
func (*ProductMemberPriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{26}
}

func (m *ProductMemberPriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductMemberPriceRequest.Unmarshal(m, b)
}
func (m *ProductMemberPriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductMemberPriceRequest.Marshal(b, m, deterministic)
}
func (m *ProductMemberPriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductMemberPriceRequest.Merge(m, src)
}
func (m *ProductMemberPriceRequest) XXX_Size() int {
	return xxx_messageInfo_ProductMemberPriceRequest.Size(m)
}
func (m *ProductMemberPriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductMemberPriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ProductMemberPriceRequest proto.InternalMessageInfo

func (m *ProductMemberPriceRequest) GetSkuIds() []string {
	if m != nil {
		return m.SkuIds
	}
	return nil
}

func (m *ProductMemberPriceRequest) GetMemberLevel() int32 {
	if m != nil {
		return m.MemberLevel
	}
	return 0
}

func (m *ProductMemberPriceRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type ProductMemberPriceResponse struct {
	Data                 []*ProductMemberPrice `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ProductMemberPriceResponse) Reset()         { *m = ProductMemberPriceResponse{} }
func (m *ProductMemberPriceResponse) String() string { return proto.CompactTextString(m) }
func (*ProductMemberPriceResponse) ProtoMessage()    {}
func (*ProductMemberPriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{27}
}

func (m *ProductMemberPriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductMemberPriceResponse.Unmarshal(m, b)
}
func (m *ProductMemberPriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductMemberPriceResponse.Marshal(b, m, deterministic)
}
func (m *ProductMemberPriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductMemberPriceResponse.Merge(m, src)
}
func (m *ProductMemberPriceResponse) XXX_Size() int {
	return xxx_messageInfo_ProductMemberPriceResponse.Size(m)
}
func (m *ProductMemberPriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductMemberPriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ProductMemberPriceResponse proto.InternalMessageInfo

func (m *ProductMemberPriceResponse) GetData() []*ProductMemberPrice {
	if m != nil {
		return m.Data
	}
	return nil
}

type ProductMemberPrice struct {
	//商品skuid
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//是否启用会员价，1启用，0不启用
	EnableMemberPrice    int32    `protobuf:"varint,2,opt,name=enable_member_price,json=enableMemberPrice,proto3" json:"enable_member_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductMemberPrice) Reset()         { *m = ProductMemberPrice{} }
func (m *ProductMemberPrice) String() string { return proto.CompactTextString(m) }
func (*ProductMemberPrice) ProtoMessage()    {}
func (*ProductMemberPrice) Descriptor() ([]byte, []int) {
	return fileDescriptor_f43744542c773f08, []int{28}
}

func (m *ProductMemberPrice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductMemberPrice.Unmarshal(m, b)
}
func (m *ProductMemberPrice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductMemberPrice.Marshal(b, m, deterministic)
}
func (m *ProductMemberPrice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductMemberPrice.Merge(m, src)
}
func (m *ProductMemberPrice) XXX_Size() int {
	return xxx_messageInfo_ProductMemberPrice.Size(m)
}
func (m *ProductMemberPrice) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductMemberPrice.DiscardUnknown(m)
}

var xxx_messageInfo_ProductMemberPrice proto.InternalMessageInfo

func (m *ProductMemberPrice) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *ProductMemberPrice) GetEnableMemberPrice() int32 {
	if m != nil {
		return m.EnableMemberPrice
	}
	return 0
}

func init() {
	proto.RegisterType((*HelloRequest)(nil), "sh.HelloRequest")
	proto.RegisterType((*HelloResponse)(nil), "sh.HelloResponse")
	proto.RegisterType((*GoodsListReq)(nil), "sh.GoodsListReq")
	proto.RegisterType((*GoodsListRes)(nil), "sh.GoodsListRes")
	proto.RegisterType((*GoodsListData)(nil), "sh.GoodsListData")
	proto.RegisterType((*GoodsClassListReq)(nil), "sh.GoodsClassListReq")
	proto.RegisterType((*GoodsClassListRes)(nil), "sh.GoodsClassListRes")
	proto.RegisterType((*GoodsClassList)(nil), "sh.GoodsClassList")
	proto.RegisterType((*GoodsRelevanceRequest)(nil), "sh.GoodsRelevanceRequest")
	proto.RegisterType((*GoodsRelevanceDeleteRequest)(nil), "sh.GoodsRelevanceDeleteRequest")
	proto.RegisterType((*ManyGoodSku)(nil), "sh.ManyGoodSku")
	proto.RegisterType((*SkuRelRequest)(nil), "sh.SkuRelRequest")
	proto.RegisterType((*SkuRelResponse)(nil), "sh.SkuRelResponse")
	proto.RegisterType((*SkuRelList)(nil), "sh.SkuRelList")
	proto.RegisterType((*SkuRelListRequest)(nil), "sh.SkuRelListRequest")
	proto.RegisterType((*BaseResponse)(nil), "sh.BaseResponse")
	proto.RegisterType((*SkuRelListResponse)(nil), "sh.SkuRelListResponse")
	proto.RegisterType((*GoodsRelevancesList)(nil), "sh.GoodsRelevancesList")
	proto.RegisterType((*GoodsRevanceSkuRequest)(nil), "sh.GoodsRevanceSkuRequest")
	proto.RegisterType((*GoodsToEsUpdateRequest)(nil), "sh.GoodsToEsUpdateRequest")
	proto.RegisterType((*GoodsActivity)(nil), "sh.GoodsActivity")
	proto.RegisterType((*GetGoodsClassesRequest)(nil), "sh.GetGoodsClassesRequest")
	proto.RegisterType((*GetGoodsClassesResponse)(nil), "sh.GetGoodsClassesResponse")
	proto.RegisterType((*GetGoodsClassesResponse_Data)(nil), "sh.GetGoodsClassesResponse.Data")
	proto.RegisterType((*GetGoodsClassesResponse_GoodsClass)(nil), "sh.GetGoodsClassesResponse.GoodsClass")
	proto.RegisterType((*UserLevelRequest)(nil), "sh.UserLevelRequest")
	proto.RegisterType((*UserEquity)(nil), "sh.UserEquity")
	proto.RegisterType((*UserLevelResponse)(nil), "sh.UserLevelResponse")
	proto.RegisterType((*ProductMemberPriceRequest)(nil), "sh.ProductMemberPriceRequest")
	proto.RegisterType((*ProductMemberPriceResponse)(nil), "sh.ProductMemberPriceResponse")
	proto.RegisterType((*ProductMemberPrice)(nil), "sh.ProductMemberPrice")
}

func init() { proto.RegisterFile("sh/upetCenter.proto", fileDescriptor_f43744542c773f08) }

var fileDescriptor_f43744542c773f08 = []byte{
	// 1771 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x5f, 0x73, 0x1c, 0x47,
	0x11, 0xaf, 0xfb, 0xaf, 0xeb, 0x3b, 0x9d, 0x74, 0x23, 0x4b, 0x5a, 0x9f, 0x31, 0x52, 0x96, 0x8a,
	0x51, 0x11, 0x90, 0x52, 0x32, 0x55, 0x50, 0x3c, 0x10, 0x82, 0xec, 0x72, 0x2e, 0xc4, 0xc6, 0xb5,
	0x27, 0xe7, 0x05, 0xa8, 0xab, 0xf5, 0xee, 0xf8, 0x34, 0x68, 0x6f, 0xe7, 0xbc, 0x33, 0x2b, 0x47,
	0x79, 0xe5, 0x81, 0x27, 0xbe, 0x06, 0x54, 0xbe, 0x04, 0x1f, 0x85, 0x6f, 0x00, 0x9f, 0x81, 0xea,
	0x9e, 0xd9, 0x7f, 0x77, 0xab, 0x24, 0x95, 0xf2, 0xdb, 0xf4, 0x6f, 0xba, 0x7b, 0x7a, 0x7a, 0xfa,
	0xdf, 0x2e, 0xec, 0xa9, 0xab, 0xb3, 0x74, 0xc5, 0xf5, 0x05, 0x8f, 0x35, 0x4f, 0x4e, 0x57, 0x89,
	0xd4, 0x92, 0x35, 0xd5, 0xd5, 0xe4, 0x47, 0x0b, 0x29, 0x17, 0x11, 0x3f, 0xf3, 0x57, 0xe2, 0xcc,
	0x8f, 0x63, 0xa9, 0x7d, 0x2d, 0x64, 0xac, 0x0c, 0x87, 0xfb, 0x08, 0x86, 0x9f, 0xf1, 0x28, 0x92,
	0x1e, 0x7f, 0x9b, 0x72, 0xa5, 0xd9, 0x01, 0x74, 0x57, 0x7e, 0xe2, 0x2f, 0x95, 0xd3, 0x38, 0x6e,
	0x9c, 0xf4, 0x3d, 0x4b, 0xb9, 0x3f, 0x85, 0x6d, 0xcb, 0xa7, 0x56, 0x32, 0x56, 0xfc, 0x4e, 0xc6,
	0xff, 0x34, 0x60, 0xf8, 0x4c, 0xca, 0x50, 0x7d, 0x21, 0x94, 0xf6, 0xf8, 0x5b, 0xf6, 0x10, 0x60,
	0x81, 0xf4, 0x3c, 0xf6, 0x97, 0xdc, 0x32, 0xf7, 0x09, 0x79, 0xe1, 0x2f, 0x39, 0xdb, 0x83, 0xce,
	0x22, 0x98, 0x8b, 0xd0, 0x69, 0x1e, 0x37, 0x4e, 0x3a, 0x5e, 0x7b, 0x11, 0x4c, 0x43, 0xc6, 0xa0,
	0xad, 0x6f, 0x57, 0xdc, 0x69, 0x19, 0x0c, 0xd7, 0x88, 0x29, 0x99, 0x68, 0xa7, 0x6d, 0x30, 0x5c,
	0xb3, 0x07, 0xd0, 0x5f, 0xf9, 0x0b, 0x3e, 0x57, 0xe2, 0x6b, 0xee, 0x74, 0x68, 0x63, 0x0b, 0x81,
	0x99, 0xf8, 0x9a, 0xe3, 0xc1, 0xb4, 0x29, 0xe2, 0x90, 0x7f, 0xe5, 0x74, 0x69, 0x97, 0xd8, 0xa7,
	0x08, 0xb0, 0x23, 0x18, 0xa8, 0x20, 0x59, 0xce, 0x53, 0xc5, 0x13, 0x11, 0x3a, 0x3d, 0x32, 0x0c,
	0x10, 0x7a, 0x45, 0x08, 0xdb, 0x87, 0xae, 0x4c, 0x16, 0x68, 0xda, 0x16, 0xc9, 0x76, 0x64, 0xb2,
	0x98, 0x86, 0xee, 0x5f, 0x2a, 0xf7, 0x53, 0x6c, 0x17, 0x5a, 0x4b, 0xb5, 0xb0, 0x17, 0xc3, 0x25,
	0xbb, 0x07, 0x1d, 0x2d, 0xb5, 0x1f, 0xd9, 0x2b, 0x19, 0x82, 0x7d, 0x08, 0xed, 0xd0, 0xd7, 0xbe,
	0xd3, 0x3a, 0x6e, 0x9d, 0x0c, 0xce, 0xc7, 0xa7, 0xea, 0xea, 0x34, 0xd7, 0xf3, 0xc4, 0xd7, 0xbe,
	0x47, 0xdb, 0xee, 0xff, 0xda, 0xb0, 0x5d, 0xc1, 0xd9, 0x7d, 0xd8, 0x32, 0x0e, 0x14, 0x21, 0x9d,
	0xd2, 0xf2, 0x7a, 0x44, 0x4f, 0xc3, 0x35, 0xdf, 0x36, 0xd7, 0x7d, 0x7b, 0x04, 0x03, 0xb3, 0xbd,
	0x4a, 0x44, 0x60, 0xbc, 0xd9, 0xf4, 0x8c, 0xc4, 0x4b, 0x44, 0x0a, 0x06, 0xb1, 0xf4, 0x17, 0x9c,
	0x5c, 0xdb, 0xb7, 0x0c, 0x53, 0x44, 0xd8, 0x07, 0x30, 0x5c, 0xf2, 0xe5, 0x6b, 0x9e, 0x58, 0x15,
	0x1d, 0x52, 0x31, 0x30, 0x98, 0xd1, 0x91, 0xdb, 0x40, 0x2f, 0x66, 0xdd, 0x4c, 0xc8, 0x25, 0x3e,
	0xdb, 0x43, 0x00, 0xa1, 0xe6, 0x37, 0x22, 0xd1, 0xa9, 0x1f, 0x91, 0x97, 0x3b, 0x5e, 0x5f, 0xa8,
	0x2f, 0x0d, 0xc0, 0xce, 0x61, 0x3f, 0x33, 0x51, 0x2e, 0x25, 0x46, 0xa6, 0x3d, 0x69, 0x8b, 0x4e,
	0xda, 0xb3, 0xc6, 0xda, 0x3d, 0x73, 0xe2, 0xc7, 0x70, 0x6f, 0x5d, 0x86, 0xce, 0xee, 0x93, 0x72,
	0x56, 0x15, 0x21, 0x23, 0x7e, 0x02, 0xdb, 0x46, 0x42, 0xf9, 0x11, 0x8f, 0xd3, 0xa5, 0x03, 0xc4,
	0x3a, 0x24, 0x70, 0x66, 0x30, 0xbc, 0xab, 0x61, 0xfa, 0xab, 0x88, 0x17, 0x11, 0x77, 0x06, 0xe4,
	0x0d, 0xe3, 0xa0, 0xcf, 0x09, 0xc2, 0xcb, 0xa8, 0x2b, 0x99, 0x68, 0xe3, 0xef, 0xa1, 0xf1, 0x37,
	0x21, 0xe4, 0xef, 0x53, 0xd8, 0xe3, 0xb1, 0xff, 0x3a, 0xe2, 0xf3, 0x8a, 0xd3, 0xb6, 0xe9, 0xb0,
	0xb1, 0xd9, 0x7a, 0x5e, 0x72, 0xdd, 0x87, 0x30, 0x32, 0x27, 0x06, 0x72, 0xb9, 0x94, 0xb1, 0x08,
	0x9d, 0x11, 0xbd, 0xaf, 0x31, 0xf6, 0xc2, 0x82, 0x18, 0x00, 0x81, 0x8c, 0x64, 0x82, 0x01, 0xb0,
	0x63, 0x02, 0x80, 0xe8, 0x29, 0x6d, 0x29, 0x2d, 0x13, 0x8e, 0x5b, 0xbb, 0x66, 0x8b, 0xe8, 0x69,
	0xc8, 0x1e, 0xc1, 0x8e, 0x50, 0x55, 0x43, 0xc6, 0x64, 0xc8, 0xb6, 0x50, 0x25, 0x23, 0xdc, 0x4f,
	0x60, 0x4c, 0xf1, 0x76, 0x11, 0xf9, 0x2a, 0x4f, 0xda, 0x3c, 0x2b, 0x1b, 0xa5, 0xac, 0x2c, 0x12,
	0xa2, 0x59, 0x4e, 0x88, 0xe7, 0x9b, 0x0a, 0xea, 0xb2, 0xe2, 0x91, 0x8d, 0xff, 0x26, 0xc5, 0x3f,
	0xcb, 0xe3, 0xbf, 0x10, 0x33, 0x09, 0xf0, 0x5b, 0x18, 0x55, 0xf1, 0x7a, 0x63, 0x0e, 0xa1, 0xb7,
	0x08, 0xca, 0x71, 0xdf, 0x5d, 0x04, 0xf8, 0x08, 0xee, 0x37, 0x0d, 0xd8, 0x27, 0x05, 0x1e, 0x8f,
	0xf8, 0x8d, 0x1f, 0x07, 0x3c, 0xab, 0x6d, 0x23, 0x68, 0xe6, 0x4a, 0x9a, 0x82, 0xaa, 0x4c, 0x49,
	0x9e, 0xd6, 0x88, 0x85, 0x5c, 0x05, 0x94, 0x2b, 0x7d, 0x8f, 0xd6, 0x98, 0xcf, 0x37, 0x22, 0xe4,
	0xd2, 0xe6, 0x87, 0x21, 0x90, 0x53, 0x2c, 0x17, 0x8a, 0x52, 0xa2, 0xef, 0xd1, 0x9a, 0x9d, 0x66,
	0xb9, 0xa0, 0xae, 0x53, 0xe5, 0x74, 0xe9, 0xa6, 0x3b, 0x78, 0xd3, 0xe7, 0x7e, 0x7c, 0x8b, 0x46,
	0xcd, 0xae, 0x53, 0x9b, 0x1c, 0xb3, 0xeb, 0x54, 0xb9, 0xbf, 0x80, 0x07, 0x55, 0x53, 0x9f, 0xf0,
	0x88, 0xeb, 0x1a, 0x83, 0xfb, 0x68, 0xb0, 0x9b, 0xc2, 0xa0, 0xa4, 0x68, 0xa3, 0x30, 0x74, 0x8a,
	0xc2, 0xf0, 0x73, 0x60, 0x49, 0xa6, 0x13, 0x8d, 0x99, 0x53, 0xe9, 0x34, 0x17, 0xdd, 0xcd, 0x77,
	0x66, 0xd7, 0xe9, 0x0c, 0xcb, 0x68, 0x9e, 0xc2, 0x6a, 0xc5, 0x03, 0x5b, 0x74, 0xad, 0x95, 0x2b,
	0x1e, 0xb8, 0xbf, 0x82, 0x6d, 0xb4, 0x9b, 0x47, 0x99, 0x5d, 0x99, 0xe3, 0x1a, 0x55, 0xc7, 0x51,
	0x12, 0x5a, 0x67, 0xe2, 0xda, 0xfd, 0x0a, 0x46, 0x99, 0xa0, 0xed, 0x1a, 0x0c, 0xda, 0x81, 0x0c,
	0x79, 0xf6, 0x92, 0xb8, 0x66, 0x0e, 0xf4, 0x96, 0x5c, 0x29, 0x2c, 0x40, 0x46, 0x38, 0x23, 0xd1,
	0xf1, 0x3c, 0x49, 0x64, 0x62, 0x5f, 0xc3, 0x10, 0xcc, 0xb5, 0x81, 0xd4, 0x26, 0xf7, 0x8e, 0xd0,
	0xbd, 0xe6, 0x94, 0x52, 0x10, 0x25, 0x00, 0x05, 0xf6, 0xbd, 0x1e, 0xfe, 0x08, 0x06, 0x41, 0xc2,
	0x7d, 0xcd, 0xe7, 0x5a, 0x2c, 0xb9, 0x3d, 0x11, 0x0c, 0x74, 0x29, 0x0c, 0x43, 0xba, 0x0a, 0x73,
	0x06, 0x5b, 0x2b, 0x0d, 0x84, 0x0c, 0xee, 0xbf, 0x1a, 0x30, 0x2e, 0x19, 0x62, 0x7d, 0x55, 0xed,
	0x42, 0x8d, 0xf5, 0x2e, 0x54, 0xe9, 0x60, 0xcd, 0xb5, 0x0e, 0xf6, 0x01, 0x0c, 0xaf, 0xf9, 0xed,
	0x3b, 0x99, 0x84, 0xf3, 0x52, 0x3b, 0x1c, 0x58, 0x8c, 0x2a, 0x9b, 0x03, 0x3d, 0x4b, 0x5a, 0x8b,
	0x32, 0xb2, 0x52, 0x1a, 0x4c, 0x6b, 0xcc, 0x4a, 0x83, 0x1b, 0xc1, 0xf0, 0xf7, 0xbe, 0xe2, 0xef,
	0xf5, 0x55, 0x1c, 0xe8, 0x85, 0x5c, 0xfb, 0x22, 0x52, 0xf4, 0x30, 0x7d, 0x2f, 0x23, 0xdd, 0x7f,
	0x36, 0x80, 0x95, 0xfd, 0xf2, 0x1e, 0x0f, 0xfd, 0xa8, 0x12, 0x0a, 0x87, 0x79, 0x4d, 0xc9, 0xf3,
	0xa9, 0x54, 0x58, 0xf0, 0x01, 0xa9, 0x13, 0xcf, 0x03, 0x99, 0xc6, 0xda, 0xfa, 0x04, 0x08, 0xba,
	0x40, 0xc4, 0xfd, 0x5b, 0x03, 0xf6, 0x6a, 0xc4, 0x7f, 0x70, 0xdd, 0x70, 0xa0, 0xe7, 0x87, 0x61,
	0x29, 0x5a, 0x32, 0x92, 0x4d, 0x60, 0x8b, 0x87, 0x42, 0xd3, 0x96, 0xa9, 0x1f, 0x39, 0xed, 0x9e,
	0xc0, 0x81, 0x35, 0x22, 0xcb, 0xd1, 0x3b, 0xea, 0x97, 0xfb, 0xef, 0x86, 0x65, 0xbd, 0x94, 0x4f,
	0xd5, 0x2b, 0x0a, 0xc4, 0x8c, 0x75, 0x17, 0x5a, 0x22, 0xcc, 0x46, 0x33, 0x5c, 0xb2, 0x8f, 0xb3,
	0x1c, 0x47, 0x36, 0x5b, 0x84, 0x8b, 0x21, 0xe4, 0xd3, 0x40, 0x8b, 0x1b, 0xa1, 0x6f, 0x6d, 0xda,
	0x3f, 0xf1, 0x35, 0xb9, 0x7c, 0xaa, 0xbe, 0x14, 0x2b, 0x1b, 0x76, 0x86, 0xc0, 0x4b, 0x3d, 0x51,
	0x7f, 0xc4, 0xc2, 0x6f, 0x27, 0xb1, 0x8c, 0xc4, 0x9d, 0x4f, 0xdf, 0x99, 0x1d, 0x1b, 0x6f, 0x96,
	0xc4, 0x59, 0x71, 0x76, 0x25, 0x57, 0xd3, 0xd0, 0x8e, 0x07, 0x96, 0x72, 0x95, 0x1d, 0x75, 0xb2,
	0xd3, 0xb1, 0xc3, 0x60, 0xb1, 0xca, 0x8b, 0x5e, 0x47, 0x5d, 0xa7, 0xd3, 0x10, 0x2d, 0x31, 0x0d,
	0xcc, 0xf6, 0x1d, 0x22, 0x30, 0x3b, 0x84, 0x9a, 0xf3, 0x37, 0x6f, 0x38, 0x8a, 0xe7, 0xd9, 0x21,
	0xd4, 0xd3, 0x0c, 0xca, 0x8b, 0x52, 0xbb, 0x98, 0x23, 0xdd, 0x33, 0x38, 0x78, 0xc6, 0x75, 0xd1,
	0x62, 0xb8, 0xca, 0x9c, 0xb6, 0x0f, 0x5d, 0xea, 0x33, 0xe8, 0xb7, 0xd6, 0x49, 0xcb, 0xeb, 0x60,
	0xa3, 0x51, 0xee, 0xdf, 0x5b, 0x70, 0xb8, 0x21, 0x51, 0x13, 0xc4, 0xad, 0xef, 0x0c, 0xe2, 0x5f,
	0xe6, 0x23, 0x60, 0xe3, 0x64, 0x70, 0x7e, 0x4c, 0xde, 0xaf, 0x57, 0x7c, 0x5a, 0x4c, 0x84, 0x93,
	0x6f, 0x1a, 0xd0, 0xa6, 0x41, 0xf0, 0x12, 0xd8, 0xca, 0x4f, 0x78, 0xac, 0xe7, 0x76, 0x6a, 0x40,
	0x19, 0xb2, 0x75, 0x70, 0xfe, 0xe8, 0xdb, 0x94, 0x15, 0xa0, 0xb7, 0x6b, 0x34, 0x14, 0x08, 0xfb,
	0x03, 0x6c, 0x07, 0x57, 0x22, 0x0a, 0x13, 0x1e, 0xcf, 0x23, 0xa1, 0xb4, 0x8d, 0x8d, 0xef, 0xab,
	0x70, 0x98, 0x09, 0x63, 0xaa, 0x4c, 0xfe, 0x0c, 0x50, 0x52, 0x5d, 0x69, 0xdc, 0xad, 0xef, 0x68,
	0xdc, 0xec, 0x18, 0x86, 0x8b, 0x60, 0x6e, 0x6f, 0x28, 0x42, 0xf2, 0x52, 0xcb, 0x83, 0x45, 0xf0,
	0x92, 0xa0, 0x69, 0xe8, 0xbe, 0x82, 0x5d, 0x9c, 0xcd, 0xbf, 0xe0, 0x37, 0x45, 0x2f, 0x3a, 0x86,
	0x61, 0x3e, 0xc6, 0x17, 0x81, 0x93, 0xcf, 0xf1, 0xd3, 0x10, 0xf3, 0xfe, 0x9d, 0xd0, 0x57, 0x73,
	0xfe, 0x36, 0x15, 0xfa, 0x96, 0x0e, 0x6d, 0x79, 0x80, 0xd0, 0x53, 0x42, 0xb0, 0x59, 0x20, 0xab,
	0xa1, 0x4a, 0x59, 0xd6, 0xa2, 0x6c, 0x3f, 0x82, 0x81, 0x91, 0x2c, 0xdb, 0x0c, 0x06, 0x7a, 0x61,
	0x53, 0x5f, 0x04, 0x32, 0xce, 0x52, 0x1f, 0xd7, 0x25, 0x21, 0x11, 0xbf, 0xc9, 0x06, 0x07, 0x2b,
	0x34, 0x8d, 0xdf, 0x48, 0xf7, 0x1f, 0x4d, 0x18, 0x97, 0xee, 0xf2, 0x83, 0xc2, 0xe9, 0x21, 0x00,
	0xdd, 0x3a, 0x42, 0x1d, 0xf6, 0xf8, 0x7e, 0x9a, 0x29, 0x65, 0x2e, 0x6c, 0x17, 0xdb, 0xe8, 0x9a,
	0x36, 0x69, 0x1d, 0xe4, 0x1c, 0xd3, 0x90, 0x9d, 0xc0, 0x6e, 0x89, 0x47, 0xe5, 0x05, 0xa9, 0xe5,
	0x8d, 0x72, 0xb6, 0x19, 0x95, 0xac, 0x2a, 0x27, 0x27, 0xce, 0xee, 0x1a, 0xe7, 0x53, 0xe2, 0x7c,
	0x6c, 0xcf, 0xa5, 0xdb, 0x0a, 0xae, 0x9c, 0x5e, 0xd1, 0xa8, 0x0b, 0x3f, 0x7b, 0xc3, 0x34, 0x5b,
	0x0b, 0xae, 0xdc, 0x18, 0xee, 0xbf, 0x4c, 0x64, 0x98, 0x06, 0xba, 0x34, 0x9b, 0x66, 0x6f, 0x7c,
	0x08, 0x3d, 0x53, 0x16, 0x4c, 0xb4, 0xf7, 0xbd, 0x2e, 0xd5, 0x05, 0x55, 0xfa, 0x3c, 0x31, 0x3e,
	0x30, 0xf5, 0xc1, 0x7e, 0x9e, 0x18, 0x2f, 0x14, 0x43, 0x6b, 0xab, 0x3c, 0xb4, 0x7e, 0x06, 0x93,
	0xba, 0xf3, 0xec, 0x3b, 0xfc, 0xcc, 0x26, 0xaa, 0xc9, 0xad, 0x03, 0xb4, 0xbc, 0x86, 0xdb, 0x8c,
	0x1a, 0x7f, 0x02, 0xb6, 0xb9, 0x77, 0x57, 0x25, 0xbb, 0xe3, 0x0b, 0xa1, 0x79, 0xc7, 0x17, 0xc2,
	0xf9, 0x6f, 0x60, 0x70, 0xc9, 0x95, 0x9e, 0xf1, 0xe4, 0x06, 0xb5, 0x7e, 0x04, 0xed, 0x4b, 0x2a,
	0xef, 0x68, 0x51, 0xf9, 0xbb, 0x7d, 0x32, 0x2e, 0x21, 0xe6, 0x12, 0xe7, 0xff, 0xed, 0xc0, 0xc8,
	0x5a, 0x96, 0xc9, 0x3f, 0xae, 0x8c, 0x45, 0xe3, 0x62, 0x74, 0xca, 0xd4, 0xb0, 0x32, 0x64, 0x9d,
	0x71, 0x01, 0xac, 0xda, 0x15, 0x49, 0x78, 0x7f, 0x6d, 0xee, 0xb2, 0x0a, 0x0e, 0xd6, 0x61, 0xab,
	0xe4, 0x77, 0x30, 0x7e, 0xc1, 0xdf, 0x55, 0xf5, 0xb0, 0xfb, 0x9b, 0x0d, 0x3b, 0xd3, 0x43, 0x37,
	0xac, 0x0c, 0x29, 0x53, 0xb8, 0x67, 0xa6, 0xe3, 0x35, 0x25, 0x47, 0x9b, 0x4a, 0x2a, 0x53, 0x74,
	0x8d, 0xaa, 0x67, 0x70, 0x58, 0xa7, 0x0a, 0x67, 0xea, 0x49, 0x49, 0xdb, 0x5a, 0xff, 0xad, 0x51,
	0xf4, 0x09, 0xec, 0x98, 0xbe, 0x9b, 0xb7, 0xe1, 0x92, 0x82, 0x8d, 0xae, 0x5c, 0xa3, 0xe0, 0x73,
	0xd8, 0x59, 0xab, 0xb1, 0x56, 0x41, 0x6d, 0x87, 0x9a, 0x3c, 0xf8, 0x96, 0xa2, 0xcc, 0x36, 0x3f,
	0x9c, 0xf6, 0x6b, 0x3e, 0xb2, 0xf8, 0xdb, 0x49, 0x2d, 0xac, 0xd8, 0x19, 0xf4, 0xf3, 0x1f, 0x0f,
	0x26, 0xc2, 0xca, 0xff, 0x71, 0x26, 0xeb, 0x88, 0x62, 0xbf, 0x86, 0x7e, 0x5e, 0xc2, 0xd8, 0xbd,
	0x2c, 0xbd, 0xcb, 0xd5, 0xd9, 0x1c, 0xb5, 0x59, 0xe7, 0x66, 0xb5, 0x39, 0xf3, 0xf0, 0x8e, 0x3c,
	0xb3, 0xba, 0x7e, 0x7c, 0xd7, 0xb6, 0x51, 0xfa, 0xba, 0x4b, 0x7f, 0xb4, 0x1e, 0xff, 0x3f, 0x00,
	0x00, 0xff, 0xff, 0x32, 0x96, 0xd8, 0x1c, 0x0a, 0x13, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TestServiceClient is the client API for TestService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TestServiceClient interface {
	Test(ctx context.Context, in *HelloRequest, opts ...grpc.CallOption) (*HelloResponse, error)
}

type testServiceClient struct {
	cc *grpc.ClientConn
}

func NewTestServiceClient(cc *grpc.ClientConn) TestServiceClient {
	return &testServiceClient{cc}
}

func (c *testServiceClient) Test(ctx context.Context, in *HelloRequest, opts ...grpc.CallOption) (*HelloResponse, error) {
	out := new(HelloResponse)
	err := c.cc.Invoke(ctx, "/sh.TestService/Test", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TestServiceServer is the server API for TestService service.
type TestServiceServer interface {
	Test(context.Context, *HelloRequest) (*HelloResponse, error)
}

// UnimplementedTestServiceServer can be embedded to have forward compatible implementations.
type UnimplementedTestServiceServer struct {
}

func (*UnimplementedTestServiceServer) Test(ctx context.Context, req *HelloRequest) (*HelloResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Test not implemented")
}

func RegisterTestServiceServer(s *grpc.Server, srv TestServiceServer) {
	s.RegisterService(&_TestService_serviceDesc, srv)
}

func _TestService_Test_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).Test(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.TestService/Test",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).Test(ctx, req.(*HelloRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _TestService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sh.TestService",
	HandlerType: (*TestServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Test",
			Handler:    _TestService_Test_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sh/upetCenter.proto",
}

// ProductServiceClient is the client API for ProductService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ProductServiceClient interface {
	SkuRelList(ctx context.Context, in *SkuRelRequest, opts ...grpc.CallOption) (*SkuRelResponse, error)
	//查询关联名称列表
	GoodsRelevanceList(ctx context.Context, in *SkuRelListRequest, opts ...grpc.CallOption) (*SkuRelListResponse, error)
	//新增关联名称
	NewGoodsRelevance(ctx context.Context, in *GoodsRelevanceRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//删除关联名称
	DeleteGoodsRelevance(ctx context.Context, in *GoodsRelevanceDeleteRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//删除关联商品
	DeleteGoodsRelevanceSku(ctx context.Context, in *GoodsRevanceSkuRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 更新商品到ES
	UpdateGoodsToEs(ctx context.Context, in *GoodsToEsUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取商品分类
	GetGoodsClasses(ctx context.Context, in *GetGoodsClassesRequest, opts ...grpc.CallOption) (*GetGoodsClassesResponse, error)
	// 获取会员商品分类
	GoodsClassList(ctx context.Context, in *GoodsClassListReq, opts ...grpc.CallOption) (*GoodsClassListRes, error)
	// 获取会员商品列表
	GoodsList(ctx context.Context, in *GoodsListReq, opts ...grpc.CallOption) (*GoodsListRes, error)
	// 获取用户会员等级
	UserLevel(ctx context.Context, in *UserLevelRequest, opts ...grpc.CallOption) (*UserLevelResponse, error)
	// 使用消息订阅次数
	ProductMemberPrice(ctx context.Context, in *ProductMemberPriceRequest, opts ...grpc.CallOption) (*ProductMemberPriceResponse, error)
}

type productServiceClient struct {
	cc *grpc.ClientConn
}

func NewProductServiceClient(cc *grpc.ClientConn) ProductServiceClient {
	return &productServiceClient{cc}
}

func (c *productServiceClient) SkuRelList(ctx context.Context, in *SkuRelRequest, opts ...grpc.CallOption) (*SkuRelResponse, error) {
	out := new(SkuRelResponse)
	err := c.cc.Invoke(ctx, "/sh.ProductService/SkuRelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) GoodsRelevanceList(ctx context.Context, in *SkuRelListRequest, opts ...grpc.CallOption) (*SkuRelListResponse, error) {
	out := new(SkuRelListResponse)
	err := c.cc.Invoke(ctx, "/sh.ProductService/GoodsRelevanceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) NewGoodsRelevance(ctx context.Context, in *GoodsRelevanceRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sh.ProductService/NewGoodsRelevance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) DeleteGoodsRelevance(ctx context.Context, in *GoodsRelevanceDeleteRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sh.ProductService/DeleteGoodsRelevance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) DeleteGoodsRelevanceSku(ctx context.Context, in *GoodsRevanceSkuRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sh.ProductService/DeleteGoodsRelevanceSku", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) UpdateGoodsToEs(ctx context.Context, in *GoodsToEsUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sh.ProductService/UpdateGoodsToEs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) GetGoodsClasses(ctx context.Context, in *GetGoodsClassesRequest, opts ...grpc.CallOption) (*GetGoodsClassesResponse, error) {
	out := new(GetGoodsClassesResponse)
	err := c.cc.Invoke(ctx, "/sh.ProductService/GetGoodsClasses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) GoodsClassList(ctx context.Context, in *GoodsClassListReq, opts ...grpc.CallOption) (*GoodsClassListRes, error) {
	out := new(GoodsClassListRes)
	err := c.cc.Invoke(ctx, "/sh.ProductService/GoodsClassList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) GoodsList(ctx context.Context, in *GoodsListReq, opts ...grpc.CallOption) (*GoodsListRes, error) {
	out := new(GoodsListRes)
	err := c.cc.Invoke(ctx, "/sh.ProductService/GoodsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) UserLevel(ctx context.Context, in *UserLevelRequest, opts ...grpc.CallOption) (*UserLevelResponse, error) {
	out := new(UserLevelResponse)
	err := c.cc.Invoke(ctx, "/sh.ProductService/UserLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *productServiceClient) ProductMemberPrice(ctx context.Context, in *ProductMemberPriceRequest, opts ...grpc.CallOption) (*ProductMemberPriceResponse, error) {
	out := new(ProductMemberPriceResponse)
	err := c.cc.Invoke(ctx, "/sh.ProductService/ProductMemberPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProductServiceServer is the server API for ProductService service.
type ProductServiceServer interface {
	SkuRelList(context.Context, *SkuRelRequest) (*SkuRelResponse, error)
	//查询关联名称列表
	GoodsRelevanceList(context.Context, *SkuRelListRequest) (*SkuRelListResponse, error)
	//新增关联名称
	NewGoodsRelevance(context.Context, *GoodsRelevanceRequest) (*BaseResponse, error)
	//删除关联名称
	DeleteGoodsRelevance(context.Context, *GoodsRelevanceDeleteRequest) (*BaseResponse, error)
	//删除关联商品
	DeleteGoodsRelevanceSku(context.Context, *GoodsRevanceSkuRequest) (*BaseResponse, error)
	// 更新商品到ES
	UpdateGoodsToEs(context.Context, *GoodsToEsUpdateRequest) (*BaseResponse, error)
	// 获取商品分类
	GetGoodsClasses(context.Context, *GetGoodsClassesRequest) (*GetGoodsClassesResponse, error)
	// 获取会员商品分类
	GoodsClassList(context.Context, *GoodsClassListReq) (*GoodsClassListRes, error)
	// 获取会员商品列表
	GoodsList(context.Context, *GoodsListReq) (*GoodsListRes, error)
	// 获取用户会员等级
	UserLevel(context.Context, *UserLevelRequest) (*UserLevelResponse, error)
	// 使用消息订阅次数
	ProductMemberPrice(context.Context, *ProductMemberPriceRequest) (*ProductMemberPriceResponse, error)
}

// UnimplementedProductServiceServer can be embedded to have forward compatible implementations.
type UnimplementedProductServiceServer struct {
}

func (*UnimplementedProductServiceServer) SkuRelList(ctx context.Context, req *SkuRelRequest) (*SkuRelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SkuRelList not implemented")
}
func (*UnimplementedProductServiceServer) GoodsRelevanceList(ctx context.Context, req *SkuRelListRequest) (*SkuRelListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsRelevanceList not implemented")
}
func (*UnimplementedProductServiceServer) NewGoodsRelevance(ctx context.Context, req *GoodsRelevanceRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewGoodsRelevance not implemented")
}
func (*UnimplementedProductServiceServer) DeleteGoodsRelevance(ctx context.Context, req *GoodsRelevanceDeleteRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGoodsRelevance not implemented")
}
func (*UnimplementedProductServiceServer) DeleteGoodsRelevanceSku(ctx context.Context, req *GoodsRevanceSkuRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGoodsRelevanceSku not implemented")
}
func (*UnimplementedProductServiceServer) UpdateGoodsToEs(ctx context.Context, req *GoodsToEsUpdateRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGoodsToEs not implemented")
}
func (*UnimplementedProductServiceServer) GetGoodsClasses(ctx context.Context, req *GetGoodsClassesRequest) (*GetGoodsClassesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsClasses not implemented")
}
func (*UnimplementedProductServiceServer) GoodsClassList(ctx context.Context, req *GoodsClassListReq) (*GoodsClassListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsClassList not implemented")
}
func (*UnimplementedProductServiceServer) GoodsList(ctx context.Context, req *GoodsListReq) (*GoodsListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsList not implemented")
}
func (*UnimplementedProductServiceServer) UserLevel(ctx context.Context, req *UserLevelRequest) (*UserLevelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLevel not implemented")
}
func (*UnimplementedProductServiceServer) ProductMemberPrice(ctx context.Context, req *ProductMemberPriceRequest) (*ProductMemberPriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductMemberPrice not implemented")
}

func RegisterProductServiceServer(s *grpc.Server, srv ProductServiceServer) {
	s.RegisterService(&_ProductService_serviceDesc, srv)
}

func _ProductService_SkuRelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SkuRelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).SkuRelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/SkuRelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).SkuRelList(ctx, req.(*SkuRelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_GoodsRelevanceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SkuRelListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).GoodsRelevanceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/GoodsRelevanceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).GoodsRelevanceList(ctx, req.(*SkuRelListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_NewGoodsRelevance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsRelevanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).NewGoodsRelevance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/NewGoodsRelevance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).NewGoodsRelevance(ctx, req.(*GoodsRelevanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_DeleteGoodsRelevance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsRelevanceDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).DeleteGoodsRelevance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/DeleteGoodsRelevance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).DeleteGoodsRelevance(ctx, req.(*GoodsRelevanceDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_DeleteGoodsRelevanceSku_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsRevanceSkuRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).DeleteGoodsRelevanceSku(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/DeleteGoodsRelevanceSku",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).DeleteGoodsRelevanceSku(ctx, req.(*GoodsRevanceSkuRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_UpdateGoodsToEs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsToEsUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).UpdateGoodsToEs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/UpdateGoodsToEs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).UpdateGoodsToEs(ctx, req.(*GoodsToEsUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_GetGoodsClasses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoodsClassesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).GetGoodsClasses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/GetGoodsClasses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).GetGoodsClasses(ctx, req.(*GetGoodsClassesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_GoodsClassList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsClassListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).GoodsClassList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/GoodsClassList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).GoodsClassList(ctx, req.(*GoodsClassListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_GoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).GoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/GoodsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).GoodsList(ctx, req.(*GoodsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_UserLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLevelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).UserLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/UserLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).UserLevel(ctx, req.(*UserLevelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProductService_ProductMemberPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductMemberPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProductServiceServer).ProductMemberPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sh.ProductService/ProductMemberPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProductServiceServer).ProductMemberPrice(ctx, req.(*ProductMemberPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ProductService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sh.ProductService",
	HandlerType: (*ProductServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SkuRelList",
			Handler:    _ProductService_SkuRelList_Handler,
		},
		{
			MethodName: "GoodsRelevanceList",
			Handler:    _ProductService_GoodsRelevanceList_Handler,
		},
		{
			MethodName: "NewGoodsRelevance",
			Handler:    _ProductService_NewGoodsRelevance_Handler,
		},
		{
			MethodName: "DeleteGoodsRelevance",
			Handler:    _ProductService_DeleteGoodsRelevance_Handler,
		},
		{
			MethodName: "DeleteGoodsRelevanceSku",
			Handler:    _ProductService_DeleteGoodsRelevanceSku_Handler,
		},
		{
			MethodName: "UpdateGoodsToEs",
			Handler:    _ProductService_UpdateGoodsToEs_Handler,
		},
		{
			MethodName: "GetGoodsClasses",
			Handler:    _ProductService_GetGoodsClasses_Handler,
		},
		{
			MethodName: "GoodsClassList",
			Handler:    _ProductService_GoodsClassList_Handler,
		},
		{
			MethodName: "GoodsList",
			Handler:    _ProductService_GoodsList_Handler,
		},
		{
			MethodName: "UserLevel",
			Handler:    _ProductService_UserLevel_Handler,
		},
		{
			MethodName: "ProductMemberPrice",
			Handler:    _ProductService_ProductMemberPrice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sh/upetCenter.proto",
}
