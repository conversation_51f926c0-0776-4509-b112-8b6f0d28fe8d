syntax = "proto3";
package sh;

service ActivityService {
    // 幸运号存储
    rpc LuckyNumSave (LuckyNumSaveRequest) returns (LuckyNumSaveResponse);
    // 我的幸运码
    rpc MyLuckyNumGet (MyLuckyNumGetRequest) returns (MyLuckyNumGetResponse);
    // 开奖信息
    rpc LuckyOpenInfo (LuckyOpenInfoRequest) returns (LuckyOpenInfoResponse);
    // 所有开奖信息
    rpc LuckyOpenInfoAll (LuckyOpenInfoAllRequest) returns (LuckyOpenInfoAllResponse);
    // 中奖地址提交修改
    rpc RewardAddress (RewardAddressRequest) returns (RewardAddressResponse);
}

// 幸运码保存
message LuckyNumSaveRequest {
    string lucky_num = 1;
    int32 lottery_id = 2;
    int32 type = 3;
    string scrm_id = 4;
    int64 invite_member_id = 5;
    string invite_scrm_id = 6;
    string order_sn = 7;
}
message LuckyNumSaveResponse {
    // 200-正常，400-错误
    int32 code = 1;
    // 错误提示信息
    string message = 2;
    // 内部错误信息
    string error = 3;
    // 是否删除已生成的幸运码，1-删除
    int32 is_del_lucky_num = 4;
}

// 我的幸运码
message MyLuckyNumGetRequest {
    // 此参数不用传，后端通过token获取用户scrm_id
    string scrm_id = 1;
    // 活动id
    int32 lottery_id = 2;
}
message MyLuckyNumGetResponse {
    // 200-正常，400-错误
    int32 code = 1;
    // 错误提示信息
    string message = 2;
    // 内部错误信息
    string error = 3;
    repeated MyLuckyNumGetData data = 4;
}
message MyLuckyNumGetData{
    // 日期
    string date = 1;
    // 获取幸运码的说明
    string type_name = 2;
    // 幸运码
    string lucky_num = 3;
}

// 中奖信息
message LuckyOpenInfoRequest {
    // 用户id
    string scrm_id = 1;
    // 活动id
    int32 lottery_id = 2;
}
message LuckyOpenInfoResponse {
    // 200-正常，400-错误
    int32 code = 1;
    // 错误提示信息
    string message = 2;
    // 内部错误信息
    string error = 3;
    // 开奖信息标题
    string title = 4;
    // 开奖时间信息
    repeated OpenInfoDate date_info = 5;
    // 中奖信息
    repeated LuckyOpenInfoData reward_info = 6;
    // 领奖方式
    RewardWayInfo reward_way = 7;
}
message RewardWayInfo {
    // 是否中免单奖，1-中奖，显示相关信息和客服二维码
    int32 is_free = 1;
    // 客服二维码地址
    string qr_code_url = 2;
    // 是否显示填写收获地址，1-是，表示中实物奖，显示收货地址
    int32 is_show_address = 3;
    // 奖品收获地址信息
    RewardAddressRequest address_info = 4;
}
message OpenInfoDate {
    // 开奖状态
    string status_name = 1;
    // 开奖日期
    string date = 2;
    // 开奖状态，1-开奖成功，0-未开奖
    int32 status = 3;
}
message LuckyOpenInfoData {
    // 中奖幸运码
    string lucky_num = 1;
    // 头像地址
    string avatar = 2;
    // 手机号
    string mobile = 3;
    // 奖品,1-免单，2-奖品
    int32 reward = 4;
}

message LuckyOpenInfoAllRequest{
    // 活动id
    int32 lottery_id = 1;
}
message LuckyOpenInfoAllResponse {
    // 200-正常，400-错误
    int32 code = 1;
    // 错误提示信息
    string message = 2;
    // 内部错误信息
    string error = 3;
    // 中奖信息
    repeated LuckyOpenInfoAllData data = 6;
}
message LuckyOpenInfoAllData {
    // 中奖日期
    string title = 1;
    // 中奖信息
    repeated LuckyOpenInfoAllInfoData reward_info = 2;
}
message LuckyOpenInfoAllInfoData {
    // 中奖幸运码
    string lucky_num = 1;
    // 手机号
    string mobile = 2;
    // 头像地址
    string avatar = 3;
    // 奖品,1-免单，2-奖品
    int32 reward = 4;
}

// 中奖地址提交
message RewardAddressRequest {
    // 收件人
    string name = 1;
    // 城市
    string city = 2;
    //收货地址
    string address = 3;
    // 手机号
    string mobile = 4;
    // 城市编码
    string adcode = 5;
    // 腾讯地图纬度
    float tx_lat = 6;
    // 腾讯地图经纬度
    float tx_lng = 7;
    // 门牌号
    string house_info = 8;
    // 活动id
    int32 lottery_id = 9;
    // 用户scrm_id,后端token获取
    string scrm_user_id = 10;
}
message RewardAddressResponse {
    // 200-正常，400-错误
    int32 code = 1;
    // 错误提示信息
    string message = 2;
    // 内部错误信息
    string error = 3;
}