package sh

import (
	"context"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	lock sync.Mutex
	Conn *grpc.ClientConn
	PS   ProductServiceClient
	DS   DistributionServiceClient
	VS   VoucherServiceClient
	AS   ActivityServiceClient
	Ctx  context.Context
}

var grpcClient *Client

func init() {
	grpcClient = new(Client)
}

func GetUpetCenterClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 30*time.Second)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return newClient()
}

func newClient() *Client {
	var err error
	url := config.GetString("grpc.upet-center")
	//url = "10.1.1.248:11010"
	if url == "" {
		url = "127.0.0.1:11010"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("upetcenter，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.PS = NewProductServiceClient(grpcClient.Conn)
		grpcClient.DS = NewDistributionServiceClient(grpcClient.Conn)
		grpcClient.VS = NewVoucherServiceClient(grpcClient.Conn)
		grpcClient.AS = NewActivityServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	// c.Conn.Close()
	// c.Cf()
}
