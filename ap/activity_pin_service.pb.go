// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ap/activity_pin_service.proto

package ap

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("ap/activity_pin_service.proto", fileDescriptor_50dc83308dc74869) }

var fileDescriptor_50dc83308dc74869 = []byte{
	// 469 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x94, 0x5d, 0x6f, 0xd3, 0x3c,
	0x14, 0xc7, 0xa5, 0xe7, 0xe2, 0x91, 0x76, 0x90, 0xca, 0xe6, 0x56, 0x0c, 0xba, 0x17, 0xb8, 0x42,
	0x82, 0x8b, 0x22, 0xb1, 0x5b, 0x10, 0x88, 0x82, 0xaa, 0x49, 0xeb, 0x88, 0x56, 0x0d, 0xc4, 0xd5,
	0xe4, 0x26, 0xa7, 0xc8, 0x52, 0xb1, 0x8d, 0x7d, 0xb2, 0xa9, 0xdf, 0x9c, 0x4b, 0x94, 0x63, 0x3b,
	0x4d, 0xb2, 0x06, 0xb8, 0xcc, 0xff, 0xe5, 0x77, 0x1c, 0x27, 0x36, 0x9c, 0x48, 0xfb, 0x4a, 0xe6,
	0xa4, 0x6e, 0x15, 0x6d, 0x6e, 0xac, 0xd2, 0x37, 0x1e, 0xdd, 0xad, 0xca, 0x71, 0x62, 0x9d, 0x21,
	0x23, 0xfe, 0x93, 0x76, 0x7c, 0xd4, 0x8d, 0xfc, 0x30, 0x05, 0xae, 0x43, 0xe0, 0xf5, 0xaf, 0x3d,
	0x78, 0x98, 0x29, 0x3d, 0x73, 0xa6, 0xb4, 0x8b, 0x50, 0x15, 0x77, 0x70, 0x3a, 0x43, 0x4a, 0x6a,
	0xe6, 0x4c, 0x51, 0xe6, 0xf4, 0xd9, 0x15, 0xe8, 0x16, 0x24, 0x49, 0xe5, 0x5e, 0xbc, 0x98, 0x48,
	0x3b, 0xf9, 0x73, 0xe6, 0x0a, 0x7f, 0x96, 0xe8, 0x69, 0xfc, 0xf2, 0x5f, 0xa2, 0xde, 0x1a, 0xed,
	0x51, 0x7c, 0x82, 0x01, 0x67, 0xd8, 0xbc, 0x50, 0x9e, 0xc4, 0x61, 0x6c, 0xb3, 0x5c, 0x29, 0x09,
	0x3b, 0x66, 0xa3, 0x15, 0xae, 0x31, 0x17, 0x30, 0x4c, 0xd3, 0xd8, 0xfc, 0x88, 0x24, 0xd5, 0x5a,
	0x3c, 0x69, 0xb2, 0x82, 0xd6, 0xa6, 0x75, 0xac, 0x48, 0xfb, 0x0a, 0xa3, 0xb0, 0x70, 0xe9, 0x48,
	0xe5, 0xca, 0x4a, 0x4d, 0xbc, 0xb4, 0xd3, 0x66, 0xa7, 0x61, 0x26, 0xe6, 0xd3, 0x5e, 0x3f, 0x82,
	0x25, 0x1c, 0x4e, 0xa5, 0x6e, 0x38, 0xdb, 0xd7, 0xfe, 0x1b, 0xfb, 0x79, 0xf4, 0x7b, 0xfa, 0xf5,
	0x88, 0x19, 0x0c, 0x2f, 0xf1, 0x6e, 0x51, 0x2e, 0x7d, 0xee, 0xd4, 0x12, 0xe7, 0xe8, 0xbd, 0xfc,
	0x8e, 0x01, 0xbf, 0xc3, 0x48, 0xf8, 0xfd, 0xca, 0xff, 0x20, 0x3d, 0xd6, 0xa0, 0x6f, 0x30, 0xea,
	0x86, 0x79, 0xa1, 0xfc, 0x92, 0xbb, 0x9c, 0x84, 0x7a, 0xd6, 0x1f, 0x88, 0xe8, 0x39, 0xec, 0x4f,
	0x1d, 0x4a, 0xc2, 0xed, 0x07, 0x13, 0x47, 0x55, 0xab, 0xab, 0x26, 0xe4, 0xf1, 0x6e, 0x33, 0xe2,
	0xde, 0xc1, 0x83, 0x4c, 0x69, 0xd6, 0x32, 0xb9, 0x11, 0x8f, 0xaa, 0x70, 0x43, 0x48, 0x90, 0xc7,
	0x95, 0x7e, 0xad, 0xd5, 0x4a, 0x61, 0xd1, 0x06, 0xbc, 0x87, 0x83, 0x46, 0xfe, 0xd2, 0x90, 0x5a,
	0x6d, 0xc2, 0xbf, 0xd3, 0xd6, 0xfa, 0x37, 0xeb, 0x2d, 0x0c, 0xae, 0x70, 0x55, 0xea, 0x22, 0x71,
	0x42, 0x3d, 0x3d, 0x05, 0xaf, 0xbf, 0xfe, 0x86, 0x4f, 0x64, 0x48, 0xc5, 0xf1, 0x7c, 0x0c, 0x9a,
	0x4a, 0x7f, 0xfb, 0x1c, 0x06, 0x53, 0xa9, 0x73, 0x5c, 0xd7, 0xc3, 0xc3, 0x66, 0xb2, 0x76, 0x7f,
	0x33, 0xc7, 0x5b, 0x73, 0xbb, 0xbe, 0x88, 0x3a, 0x83, 0xbd, 0x05, 0x19, 0xcb, 0x25, 0x31, 0xe2,
	0x0f, 0x99, 0x1e, 0xfb, 0xe7, 0x67, 0x70, 0x30, 0x43, 0xba, 0xf6, 0xe8, 0x18, 0x36, 0x35, 0xa5,
	0x26, 0x71, 0x1c, 0xff, 0xd7, 0xb6, 0x9c, 0x20, 0x27, 0x3d, 0x6e, 0x24, 0x7e, 0x81, 0x61, 0xb8,
	0x3f, 0xd8, 0x9b, 0x4b, 0xa5, 0xcf, 0xf5, 0xca, 0xd4, 0x67, 0xa4, 0x6b, 0x74, 0xcf, 0xdf, 0x7d,
	0x3f, 0x70, 0x97, 0xff, 0xf3, 0x0d, 0x78, 0xf6, 0x3b, 0x00, 0x00, 0xff, 0xff, 0xdf, 0x03, 0x84,
	0x65, 0x43, 0x05, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PinGroupServiceClient is the client API for PinGroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PinGroupServiceClient interface {
	//获取拼团商品的订单统计 同步查询，后期有性能问题 可能需要通过异步数据处理
	GetPinGroupProductOrderStatics(ctx context.Context, in *GetPinGroupProductOrderStaticsRequest, opts ...grpc.CallOption) (*GetPinGroupProductOrderStaticsResponse, error)
	//拼团订单列表
	GroupOrderList(ctx context.Context, in *GetGroupListRequest, opts ...grpc.CallOption) (*GroupOrderListResponse, error)
	// 拼团订单详情
	PinGroupOrderDetail(ctx context.Context, in *GetGroupDetailRequest, opts ...grpc.CallOption) (*GetGroupDetailResponse, error)
	// 商品参团信息
	GroupParticipantList(ctx context.Context, in *GetGroupParticipantRequest, opts ...grpc.CallOption) (*GetGroupParticipantResponse, error)
	// 可参团订单列表
	CanParticipantOrderList(ctx context.Context, in *GetGroupParticipantRequest, opts ...grpc.CallOption) (*GetCanParticipantOrderListResponse, error)
	// 订阅微信消息
	NewSubscribeMessage(ctx context.Context, in *NewSubscribeMessageRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 查询订阅微信小程序模板消息
	SubscribeMessageList(ctx context.Context, in *SubscribeMessageListRequest, opts ...grpc.CallOption) (*SubscribeMessageListResponse, error)
	//创建拼团单
	CreateGroupOrder(ctx context.Context, in *CreateGroupOrderRequest, opts ...grpc.CallOption) (*CreateGroupOrderResponse, error)
	//拼团单支付
	PinOrderPay(ctx context.Context, in *PinOrderPayRequest, opts ...grpc.CallOption) (*UnifiedOrderResponse, error)
	//拼团单支付回调
	PinOrderPayNotify(ctx context.Context, in *OrderPayNotifyRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//拼团单退款
	RefundPinOrder(ctx context.Context, in *PinOrderRefundRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//拼团单退款回调
	PinRefundNotify(ctx context.Context, in *RefundNotifyRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//拼团单取消
	CancelPinOrder(ctx context.Context, in *CancelGroupOrderRequest, opts ...grpc.CallOption) (*CancelPinOrderResponse, error)
	//拼团终止活动
	StopGroup(ctx context.Context, in *StopGroupRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//某个活动下某个商品被用户购买了多少件
	GetUserOrderCount(ctx context.Context, in *GetUserOrderCountRequest, opts ...grpc.CallOption) (*GetUserOrderCountResponse, error)
	//获取团信息
	GetPinOrderMainInfo(ctx context.Context, in *GetPinOrderMainInfoRequest, opts ...grpc.CallOption) (*GetPinOrderMainInfoResponse, error)
}

type pinGroupServiceClient struct {
	cc *grpc.ClientConn
}

func NewPinGroupServiceClient(cc *grpc.ClientConn) PinGroupServiceClient {
	return &pinGroupServiceClient{cc}
}

func (c *pinGroupServiceClient) GetPinGroupProductOrderStatics(ctx context.Context, in *GetPinGroupProductOrderStaticsRequest, opts ...grpc.CallOption) (*GetPinGroupProductOrderStaticsResponse, error) {
	out := new(GetPinGroupProductOrderStaticsResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/GetPinGroupProductOrderStatics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) GroupOrderList(ctx context.Context, in *GetGroupListRequest, opts ...grpc.CallOption) (*GroupOrderListResponse, error) {
	out := new(GroupOrderListResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/GroupOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) PinGroupOrderDetail(ctx context.Context, in *GetGroupDetailRequest, opts ...grpc.CallOption) (*GetGroupDetailResponse, error) {
	out := new(GetGroupDetailResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/PinGroupOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) GroupParticipantList(ctx context.Context, in *GetGroupParticipantRequest, opts ...grpc.CallOption) (*GetGroupParticipantResponse, error) {
	out := new(GetGroupParticipantResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/GroupParticipantList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) CanParticipantOrderList(ctx context.Context, in *GetGroupParticipantRequest, opts ...grpc.CallOption) (*GetCanParticipantOrderListResponse, error) {
	out := new(GetCanParticipantOrderListResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/CanParticipantOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) NewSubscribeMessage(ctx context.Context, in *NewSubscribeMessageRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/NewSubscribeMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) SubscribeMessageList(ctx context.Context, in *SubscribeMessageListRequest, opts ...grpc.CallOption) (*SubscribeMessageListResponse, error) {
	out := new(SubscribeMessageListResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/SubscribeMessageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) CreateGroupOrder(ctx context.Context, in *CreateGroupOrderRequest, opts ...grpc.CallOption) (*CreateGroupOrderResponse, error) {
	out := new(CreateGroupOrderResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/CreateGroupOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) PinOrderPay(ctx context.Context, in *PinOrderPayRequest, opts ...grpc.CallOption) (*UnifiedOrderResponse, error) {
	out := new(UnifiedOrderResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/PinOrderPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) PinOrderPayNotify(ctx context.Context, in *OrderPayNotifyRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/PinOrderPayNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) RefundPinOrder(ctx context.Context, in *PinOrderRefundRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/RefundPinOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) PinRefundNotify(ctx context.Context, in *RefundNotifyRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/PinRefundNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) CancelPinOrder(ctx context.Context, in *CancelGroupOrderRequest, opts ...grpc.CallOption) (*CancelPinOrderResponse, error) {
	out := new(CancelPinOrderResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/CancelPinOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) StopGroup(ctx context.Context, in *StopGroupRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/StopGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) GetUserOrderCount(ctx context.Context, in *GetUserOrderCountRequest, opts ...grpc.CallOption) (*GetUserOrderCountResponse, error) {
	out := new(GetUserOrderCountResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/GetUserOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinGroupServiceClient) GetPinOrderMainInfo(ctx context.Context, in *GetPinOrderMainInfoRequest, opts ...grpc.CallOption) (*GetPinOrderMainInfoResponse, error) {
	out := new(GetPinOrderMainInfoResponse)
	err := c.cc.Invoke(ctx, "/ap.PinGroupService/GetPinOrderMainInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PinGroupServiceServer is the server API for PinGroupService service.
type PinGroupServiceServer interface {
	//获取拼团商品的订单统计 同步查询，后期有性能问题 可能需要通过异步数据处理
	GetPinGroupProductOrderStatics(context.Context, *GetPinGroupProductOrderStaticsRequest) (*GetPinGroupProductOrderStaticsResponse, error)
	//拼团订单列表
	GroupOrderList(context.Context, *GetGroupListRequest) (*GroupOrderListResponse, error)
	// 拼团订单详情
	PinGroupOrderDetail(context.Context, *GetGroupDetailRequest) (*GetGroupDetailResponse, error)
	// 商品参团信息
	GroupParticipantList(context.Context, *GetGroupParticipantRequest) (*GetGroupParticipantResponse, error)
	// 可参团订单列表
	CanParticipantOrderList(context.Context, *GetGroupParticipantRequest) (*GetCanParticipantOrderListResponse, error)
	// 订阅微信消息
	NewSubscribeMessage(context.Context, *NewSubscribeMessageRequest) (*BaseResponse, error)
	// 查询订阅微信小程序模板消息
	SubscribeMessageList(context.Context, *SubscribeMessageListRequest) (*SubscribeMessageListResponse, error)
	//创建拼团单
	CreateGroupOrder(context.Context, *CreateGroupOrderRequest) (*CreateGroupOrderResponse, error)
	//拼团单支付
	PinOrderPay(context.Context, *PinOrderPayRequest) (*UnifiedOrderResponse, error)
	//拼团单支付回调
	PinOrderPayNotify(context.Context, *OrderPayNotifyRequest) (*BaseResponse, error)
	//拼团单退款
	RefundPinOrder(context.Context, *PinOrderRefundRequest) (*BaseResponse, error)
	//拼团单退款回调
	PinRefundNotify(context.Context, *RefundNotifyRequest) (*BaseResponse, error)
	//拼团单取消
	CancelPinOrder(context.Context, *CancelGroupOrderRequest) (*CancelPinOrderResponse, error)
	//拼团终止活动
	StopGroup(context.Context, *StopGroupRequest) (*BaseResponse, error)
	//某个活动下某个商品被用户购买了多少件
	GetUserOrderCount(context.Context, *GetUserOrderCountRequest) (*GetUserOrderCountResponse, error)
	//获取团信息
	GetPinOrderMainInfo(context.Context, *GetPinOrderMainInfoRequest) (*GetPinOrderMainInfoResponse, error)
}

// UnimplementedPinGroupServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPinGroupServiceServer struct {
}

func (*UnimplementedPinGroupServiceServer) GetPinGroupProductOrderStatics(ctx context.Context, req *GetPinGroupProductOrderStaticsRequest) (*GetPinGroupProductOrderStaticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPinGroupProductOrderStatics not implemented")
}
func (*UnimplementedPinGroupServiceServer) GroupOrderList(ctx context.Context, req *GetGroupListRequest) (*GroupOrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupOrderList not implemented")
}
func (*UnimplementedPinGroupServiceServer) PinGroupOrderDetail(ctx context.Context, req *GetGroupDetailRequest) (*GetGroupDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PinGroupOrderDetail not implemented")
}
func (*UnimplementedPinGroupServiceServer) GroupParticipantList(ctx context.Context, req *GetGroupParticipantRequest) (*GetGroupParticipantResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupParticipantList not implemented")
}
func (*UnimplementedPinGroupServiceServer) CanParticipantOrderList(ctx context.Context, req *GetGroupParticipantRequest) (*GetCanParticipantOrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CanParticipantOrderList not implemented")
}
func (*UnimplementedPinGroupServiceServer) NewSubscribeMessage(ctx context.Context, req *NewSubscribeMessageRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewSubscribeMessage not implemented")
}
func (*UnimplementedPinGroupServiceServer) SubscribeMessageList(ctx context.Context, req *SubscribeMessageListRequest) (*SubscribeMessageListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscribeMessageList not implemented")
}
func (*UnimplementedPinGroupServiceServer) CreateGroupOrder(ctx context.Context, req *CreateGroupOrderRequest) (*CreateGroupOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGroupOrder not implemented")
}
func (*UnimplementedPinGroupServiceServer) PinOrderPay(ctx context.Context, req *PinOrderPayRequest) (*UnifiedOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PinOrderPay not implemented")
}
func (*UnimplementedPinGroupServiceServer) PinOrderPayNotify(ctx context.Context, req *OrderPayNotifyRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PinOrderPayNotify not implemented")
}
func (*UnimplementedPinGroupServiceServer) RefundPinOrder(ctx context.Context, req *PinOrderRefundRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundPinOrder not implemented")
}
func (*UnimplementedPinGroupServiceServer) PinRefundNotify(ctx context.Context, req *RefundNotifyRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PinRefundNotify not implemented")
}
func (*UnimplementedPinGroupServiceServer) CancelPinOrder(ctx context.Context, req *CancelGroupOrderRequest) (*CancelPinOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelPinOrder not implemented")
}
func (*UnimplementedPinGroupServiceServer) StopGroup(ctx context.Context, req *StopGroupRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopGroup not implemented")
}
func (*UnimplementedPinGroupServiceServer) GetUserOrderCount(ctx context.Context, req *GetUserOrderCountRequest) (*GetUserOrderCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserOrderCount not implemented")
}
func (*UnimplementedPinGroupServiceServer) GetPinOrderMainInfo(ctx context.Context, req *GetPinOrderMainInfoRequest) (*GetPinOrderMainInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPinOrderMainInfo not implemented")
}

func RegisterPinGroupServiceServer(s *grpc.Server, srv PinGroupServiceServer) {
	s.RegisterService(&_PinGroupService_serviceDesc, srv)
}

func _PinGroupService_GetPinGroupProductOrderStatics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPinGroupProductOrderStaticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).GetPinGroupProductOrderStatics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/GetPinGroupProductOrderStatics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).GetPinGroupProductOrderStatics(ctx, req.(*GetPinGroupProductOrderStaticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_GroupOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).GroupOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/GroupOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).GroupOrderList(ctx, req.(*GetGroupListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_PinGroupOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).PinGroupOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/PinGroupOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).PinGroupOrderDetail(ctx, req.(*GetGroupDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_GroupParticipantList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupParticipantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).GroupParticipantList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/GroupParticipantList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).GroupParticipantList(ctx, req.(*GetGroupParticipantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_CanParticipantOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupParticipantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).CanParticipantOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/CanParticipantOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).CanParticipantOrderList(ctx, req.(*GetGroupParticipantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_NewSubscribeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewSubscribeMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).NewSubscribeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/NewSubscribeMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).NewSubscribeMessage(ctx, req.(*NewSubscribeMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_SubscribeMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeMessageListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).SubscribeMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/SubscribeMessageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).SubscribeMessageList(ctx, req.(*SubscribeMessageListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_CreateGroupOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGroupOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).CreateGroupOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/CreateGroupOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).CreateGroupOrder(ctx, req.(*CreateGroupOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_PinOrderPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PinOrderPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).PinOrderPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/PinOrderPay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).PinOrderPay(ctx, req.(*PinOrderPayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_PinOrderPayNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderPayNotifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).PinOrderPayNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/PinOrderPayNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).PinOrderPayNotify(ctx, req.(*OrderPayNotifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_RefundPinOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PinOrderRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).RefundPinOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/RefundPinOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).RefundPinOrder(ctx, req.(*PinOrderRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_PinRefundNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundNotifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).PinRefundNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/PinRefundNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).PinRefundNotify(ctx, req.(*RefundNotifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_CancelPinOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelGroupOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).CancelPinOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/CancelPinOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).CancelPinOrder(ctx, req.(*CancelGroupOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_StopGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).StopGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/StopGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).StopGroup(ctx, req.(*StopGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_GetUserOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOrderCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).GetUserOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/GetUserOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).GetUserOrderCount(ctx, req.(*GetUserOrderCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PinGroupService_GetPinOrderMainInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPinOrderMainInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinGroupServiceServer).GetPinOrderMainInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ap.PinGroupService/GetPinOrderMainInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinGroupServiceServer).GetPinOrderMainInfo(ctx, req.(*GetPinOrderMainInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PinGroupService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ap.PinGroupService",
	HandlerType: (*PinGroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPinGroupProductOrderStatics",
			Handler:    _PinGroupService_GetPinGroupProductOrderStatics_Handler,
		},
		{
			MethodName: "GroupOrderList",
			Handler:    _PinGroupService_GroupOrderList_Handler,
		},
		{
			MethodName: "PinGroupOrderDetail",
			Handler:    _PinGroupService_PinGroupOrderDetail_Handler,
		},
		{
			MethodName: "GroupParticipantList",
			Handler:    _PinGroupService_GroupParticipantList_Handler,
		},
		{
			MethodName: "CanParticipantOrderList",
			Handler:    _PinGroupService_CanParticipantOrderList_Handler,
		},
		{
			MethodName: "NewSubscribeMessage",
			Handler:    _PinGroupService_NewSubscribeMessage_Handler,
		},
		{
			MethodName: "SubscribeMessageList",
			Handler:    _PinGroupService_SubscribeMessageList_Handler,
		},
		{
			MethodName: "CreateGroupOrder",
			Handler:    _PinGroupService_CreateGroupOrder_Handler,
		},
		{
			MethodName: "PinOrderPay",
			Handler:    _PinGroupService_PinOrderPay_Handler,
		},
		{
			MethodName: "PinOrderPayNotify",
			Handler:    _PinGroupService_PinOrderPayNotify_Handler,
		},
		{
			MethodName: "RefundPinOrder",
			Handler:    _PinGroupService_RefundPinOrder_Handler,
		},
		{
			MethodName: "PinRefundNotify",
			Handler:    _PinGroupService_PinRefundNotify_Handler,
		},
		{
			MethodName: "CancelPinOrder",
			Handler:    _PinGroupService_CancelPinOrder_Handler,
		},
		{
			MethodName: "StopGroup",
			Handler:    _PinGroupService_StopGroup_Handler,
		},
		{
			MethodName: "GetUserOrderCount",
			Handler:    _PinGroupService_GetUserOrderCount_Handler,
		},
		{
			MethodName: "GetPinOrderMainInfo",
			Handler:    _PinGroupService_GetPinOrderMainInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ap/activity_pin_service.proto",
}
