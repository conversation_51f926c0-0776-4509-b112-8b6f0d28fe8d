package ap

import (
	"context"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	lock sync.Mutex
	Conn *grpc.ClientConn
	Ctx  context.Context
	RPC  PinGroupServiceClient
}

var grpcClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}
}

type PlatformChannel struct {
	ChannelId  int
	UserAgent  int
	AppChannel int
}

type GrpcContext struct {
	Channel PlatformChannel
}

func GetPinGroupServiceClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 5*time.Minute)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return NewClient()
}

func NewClient(customUrl ...string) *Client {
	var (
		err error
		url string
	)

	if len(customUrl) > 0 {
		url = customUrl[0]
	} else {
		url = config.GetString("grpc.activity-pin")
	}
	if url == "" {
		url = "127.0.0.1:7079"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("activity-pin，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = NewPinGroupServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
}
