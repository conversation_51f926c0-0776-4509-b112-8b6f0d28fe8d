syntax = "proto3";
package ext;

//美团门店类API
service AdvertisementMpService {
  rpc PushMp(PushMpRequest) returns (MpBaseResponse);
  rpc PushMpForTakeOut(PushMpForTakeOutRequest) returns (MpBaseResponse);
}

//通用返回
message MpBaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message PushMpRequest{
  PushMpData Data = 1;
  //用户渠道来源
  int32 user_agent = 2;
}
message PushMpData{
  //用于标识数据归属权。
  string user_action_set_id = 1;
  //转化行为发生页面的URL，小程序可在路径前增加"http://www." 或直接填写 "http://www.qq.com"
  //校验逻辑：
  //1.以『http://www.』 开头；
  //2.必须为纯小写；
  //3.必须包含『.』
  string url = 2;
  //行为发生时，客户端的时间点。广告平台使用的是GMT+8的时间，容错范围是前后10秒，UNIX时间，单位为秒，如果不填将使用服务端时间填写
  int64 action_time = 3;
  //预定义的行为类型，目前只支持COMPLETE_ORDER（下单）及RESERVATION（表单预约）、CONFIRM_EFFECTIVE_LEADS（有效销售线索）
  string action_type = 4;
  MpTrace trace = 5;
  //去重标识，平台会基于user_action_set_id，outer_action_id 和action_type三个字段做去重 。
  //如果历史上报数据中存在某条数据的这三个字段与当前上报数据完全一样的，则当前数据会被过滤掉。
  //注：字段长度最小1字节，最大长度255字节，且只能为数字，字母，下划线，连接符组成。
  string outer_action_id = 6;
  //行为所带的参数，转化行为价值（例如金额），详见附录，字段长度最小 1 字节，长度最大 204800 字节
  ActionParam action_param = 7;
}

message MpTrace{
  //目前仅支持click_id 落地页URL中的click_id，对于微信流量为URL中的gdt_vid
  string click_id = 1;
}

message ActionParam{
  //代表订单金额，单位为分，需要填写到param中获取，例如商品单价40元，需赋值为4000
  int32 value = 1;
  //回传有效销售线索必填 有效线索的来源区分，目前支持PHONE（电话直呼），RESERVE（表单预约）
  string leads_type = 2;
}

message PushMpForTakeOutRequest{
  PushMpForTakeOutData Data = 1;
  //用户渠道来源
  int32 user_agent = 2;
}
message PushMpForTakeOutData{
  //用于标识数据归属权。
  string user_action_set_id = 1;
  //转化行为发生页面的URL，小程序可在路径前增加"http://www." 或直接填写 "http://www.qq.com"
  //校验逻辑：
  //1.以『http://www.』 开头；
  //2.必须为纯小写；
  //3.必须包含『.』
  string url = 2;
  //行为发生时，客户端的时间点。广告平台使用的是GMT+8的时间，容错范围是前后10秒，UNIX时间，单位为秒，如果不填将使用服务端时间填写
  int64 action_time = 3;
  //预定义的行为类型，目前只支持COMPLETE_ORDER（下单）及RESERVATION（表单预约）、CONFIRM_EFFECTIVE_LEADS（有效销售线索）
  string action_type = 4;
  MpTrace trace = 5;
  //行为所带的参数，转化行为价值（例如金额），详见附录，字段长度最小 1 字节，长度最大 204800 字节
  ActionParamForTakeOut action_param = 6;
}

message ActionParamForTakeOut{
  //代表订单金额，单位为分，需要填写到param中获取，例如商品单价40元，需赋值为4000
  int32 value = 1;
  //回传有效销售线索必填 有效线索的来源区分，目前支持PHONE（电话直呼），RESERVE（表单预约）
  string leads_type = 2;
  //用户当前位置的经纬度信息，如22.5228070000,113.9353380000 注：必须使用gcj02坐标系
  string user_locations = 3;
  //菜名 单价:分，例如商品单价40元，需赋值为4000 份数 ["米饭_500_2","番茄鸡蛋_1000_1"]
  repeated string order_detail = 4;
  //收货地址经纬度信息，依序为纬度，经度。如23.0413930,113.3730940 注：必须使用gcj02坐标系
  string shipping_locations = 5;
}
