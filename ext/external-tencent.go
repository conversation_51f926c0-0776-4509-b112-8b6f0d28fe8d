package ext

import (
	"context"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"time"
)

type Client struct {
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	RPC  TencentServiceClient
	MP   AdvertisementMpServiceClient
}

func GetExternalTencentClient() *Client {
	var client Client
	var err error
	url := config.GetString("grpc.external-tencent")
	//url = "10.1.1.248:11031"
	if url == "" {
		url = "127.0.0.1:7076"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = NewTencentServiceClient(client.Conn)
		client.MP = NewAdvertisementMpServiceClient(client.Conn)
		client.Ctx, client.Cf = context.WithTimeout(context.Background(), time.Second*30)
		return &client
	}
}

// 关闭链接
func (c *Client) Close() {
	c.Conn.Close()
	c.Cf()
}
