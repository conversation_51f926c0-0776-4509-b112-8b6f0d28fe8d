// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ext/advertisement_mp.proto

package ext

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//通用返回
type MpBaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MpBaseResponse) Reset()         { *m = MpBaseResponse{} }
func (m *MpBaseResponse) String() string { return proto.CompactTextString(m) }
func (*MpBaseResponse) ProtoMessage()    {}
func (*MpBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a04e905adbab2b96, []int{0}
}

func (m *MpBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MpBaseResponse.Unmarshal(m, b)
}
func (m *MpBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MpBaseResponse.Marshal(b, m, deterministic)
}
func (m *MpBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MpBaseResponse.Merge(m, src)
}
func (m *MpBaseResponse) XXX_Size() int {
	return xxx_messageInfo_MpBaseResponse.Size(m)
}
func (m *MpBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MpBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MpBaseResponse proto.InternalMessageInfo

func (m *MpBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MpBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MpBaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type PushMpRequest struct {
	Data *PushMpData `protobuf:"bytes,1,opt,name=Data,proto3" json:"Data"`
	//用户渠道来源
	UserAgent            int32    `protobuf:"varint,2,opt,name=user_agent,json=userAgent,proto3" json:"user_agent"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMpRequest) Reset()         { *m = PushMpRequest{} }
func (m *PushMpRequest) String() string { return proto.CompactTextString(m) }
func (*PushMpRequest) ProtoMessage()    {}
func (*PushMpRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a04e905adbab2b96, []int{1}
}

func (m *PushMpRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMpRequest.Unmarshal(m, b)
}
func (m *PushMpRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMpRequest.Marshal(b, m, deterministic)
}
func (m *PushMpRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMpRequest.Merge(m, src)
}
func (m *PushMpRequest) XXX_Size() int {
	return xxx_messageInfo_PushMpRequest.Size(m)
}
func (m *PushMpRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMpRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PushMpRequest proto.InternalMessageInfo

func (m *PushMpRequest) GetData() *PushMpData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PushMpRequest) GetUserAgent() int32 {
	if m != nil {
		return m.UserAgent
	}
	return 0
}

type PushMpData struct {
	//用于标识数据归属权。
	UserActionSetId string `protobuf:"bytes,1,opt,name=user_action_set_id,json=userActionSetId,proto3" json:"user_action_set_id"`
	//转化行为发生页面的URL，小程序可在路径前增加"http://www." 或直接填写 "http://www.qq.com"
	//校验逻辑：
	//1.以『http://www.』 开头；
	//2.必须为纯小写；
	//3.必须包含『.』
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url"`
	//行为发生时，客户端的时间点。广告平台使用的是GMT+8的时间，容错范围是前后10秒，UNIX时间，单位为秒，如果不填将使用服务端时间填写
	ActionTime int64 `protobuf:"varint,3,opt,name=action_time,json=actionTime,proto3" json:"action_time"`
	//预定义的行为类型，目前只支持COMPLETE_ORDER（下单）及RESERVATION（表单预约）、CONFIRM_EFFECTIVE_LEADS（有效销售线索）
	ActionType string   `protobuf:"bytes,4,opt,name=action_type,json=actionType,proto3" json:"action_type"`
	Trace      *MpTrace `protobuf:"bytes,5,opt,name=trace,proto3" json:"trace"`
	//去重标识，平台会基于user_action_set_id，outer_action_id 和action_type三个字段做去重 。
	//如果历史上报数据中存在某条数据的这三个字段与当前上报数据完全一样的，则当前数据会被过滤掉。
	//注：字段长度最小1字节，最大长度255字节，且只能为数字，字母，下划线，连接符组成。
	OuterActionId string `protobuf:"bytes,6,opt,name=outer_action_id,json=outerActionId,proto3" json:"outer_action_id"`
	//行为所带的参数，转化行为价值（例如金额），详见附录，字段长度最小 1 字节，长度最大 204800 字节
	ActionParam          *ActionParam `protobuf:"bytes,7,opt,name=action_param,json=actionParam,proto3" json:"action_param"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PushMpData) Reset()         { *m = PushMpData{} }
func (m *PushMpData) String() string { return proto.CompactTextString(m) }
func (*PushMpData) ProtoMessage()    {}
func (*PushMpData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a04e905adbab2b96, []int{2}
}

func (m *PushMpData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMpData.Unmarshal(m, b)
}
func (m *PushMpData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMpData.Marshal(b, m, deterministic)
}
func (m *PushMpData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMpData.Merge(m, src)
}
func (m *PushMpData) XXX_Size() int {
	return xxx_messageInfo_PushMpData.Size(m)
}
func (m *PushMpData) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMpData.DiscardUnknown(m)
}

var xxx_messageInfo_PushMpData proto.InternalMessageInfo

func (m *PushMpData) GetUserActionSetId() string {
	if m != nil {
		return m.UserActionSetId
	}
	return ""
}

func (m *PushMpData) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PushMpData) GetActionTime() int64 {
	if m != nil {
		return m.ActionTime
	}
	return 0
}

func (m *PushMpData) GetActionType() string {
	if m != nil {
		return m.ActionType
	}
	return ""
}

func (m *PushMpData) GetTrace() *MpTrace {
	if m != nil {
		return m.Trace
	}
	return nil
}

func (m *PushMpData) GetOuterActionId() string {
	if m != nil {
		return m.OuterActionId
	}
	return ""
}

func (m *PushMpData) GetActionParam() *ActionParam {
	if m != nil {
		return m.ActionParam
	}
	return nil
}

type MpTrace struct {
	//目前仅支持click_id 落地页URL中的click_id，对于微信流量为URL中的gdt_vid
	ClickId              string   `protobuf:"bytes,1,opt,name=click_id,json=clickId,proto3" json:"click_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MpTrace) Reset()         { *m = MpTrace{} }
func (m *MpTrace) String() string { return proto.CompactTextString(m) }
func (*MpTrace) ProtoMessage()    {}
func (*MpTrace) Descriptor() ([]byte, []int) {
	return fileDescriptor_a04e905adbab2b96, []int{3}
}

func (m *MpTrace) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MpTrace.Unmarshal(m, b)
}
func (m *MpTrace) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MpTrace.Marshal(b, m, deterministic)
}
func (m *MpTrace) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MpTrace.Merge(m, src)
}
func (m *MpTrace) XXX_Size() int {
	return xxx_messageInfo_MpTrace.Size(m)
}
func (m *MpTrace) XXX_DiscardUnknown() {
	xxx_messageInfo_MpTrace.DiscardUnknown(m)
}

var xxx_messageInfo_MpTrace proto.InternalMessageInfo

func (m *MpTrace) GetClickId() string {
	if m != nil {
		return m.ClickId
	}
	return ""
}

type ActionParam struct {
	//代表订单金额，单位为分，需要填写到param中获取，例如商品单价40元，需赋值为4000
	Value int32 `protobuf:"varint,1,opt,name=value,proto3" json:"value"`
	//回传有效销售线索必填 有效线索的来源区分，目前支持PHONE（电话直呼），RESERVE（表单预约）
	LeadsType            string   `protobuf:"bytes,2,opt,name=leads_type,json=leadsType,proto3" json:"leads_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActionParam) Reset()         { *m = ActionParam{} }
func (m *ActionParam) String() string { return proto.CompactTextString(m) }
func (*ActionParam) ProtoMessage()    {}
func (*ActionParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_a04e905adbab2b96, []int{4}
}

func (m *ActionParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActionParam.Unmarshal(m, b)
}
func (m *ActionParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActionParam.Marshal(b, m, deterministic)
}
func (m *ActionParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActionParam.Merge(m, src)
}
func (m *ActionParam) XXX_Size() int {
	return xxx_messageInfo_ActionParam.Size(m)
}
func (m *ActionParam) XXX_DiscardUnknown() {
	xxx_messageInfo_ActionParam.DiscardUnknown(m)
}

var xxx_messageInfo_ActionParam proto.InternalMessageInfo

func (m *ActionParam) GetValue() int32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *ActionParam) GetLeadsType() string {
	if m != nil {
		return m.LeadsType
	}
	return ""
}

type PushMpForTakeOutRequest struct {
	Data *PushMpForTakeOutData `protobuf:"bytes,1,opt,name=Data,proto3" json:"Data"`
	//用户渠道来源
	UserAgent            int32    `protobuf:"varint,2,opt,name=user_agent,json=userAgent,proto3" json:"user_agent"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMpForTakeOutRequest) Reset()         { *m = PushMpForTakeOutRequest{} }
func (m *PushMpForTakeOutRequest) String() string { return proto.CompactTextString(m) }
func (*PushMpForTakeOutRequest) ProtoMessage()    {}
func (*PushMpForTakeOutRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a04e905adbab2b96, []int{5}
}

func (m *PushMpForTakeOutRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMpForTakeOutRequest.Unmarshal(m, b)
}
func (m *PushMpForTakeOutRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMpForTakeOutRequest.Marshal(b, m, deterministic)
}
func (m *PushMpForTakeOutRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMpForTakeOutRequest.Merge(m, src)
}
func (m *PushMpForTakeOutRequest) XXX_Size() int {
	return xxx_messageInfo_PushMpForTakeOutRequest.Size(m)
}
func (m *PushMpForTakeOutRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMpForTakeOutRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PushMpForTakeOutRequest proto.InternalMessageInfo

func (m *PushMpForTakeOutRequest) GetData() *PushMpForTakeOutData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PushMpForTakeOutRequest) GetUserAgent() int32 {
	if m != nil {
		return m.UserAgent
	}
	return 0
}

type PushMpForTakeOutData struct {
	//用于标识数据归属权。
	UserActionSetId string `protobuf:"bytes,1,opt,name=user_action_set_id,json=userActionSetId,proto3" json:"user_action_set_id"`
	//转化行为发生页面的URL，小程序可在路径前增加"http://www." 或直接填写 "http://www.qq.com"
	//校验逻辑：
	//1.以『http://www.』 开头；
	//2.必须为纯小写；
	//3.必须包含『.』
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url"`
	//行为发生时，客户端的时间点。广告平台使用的是GMT+8的时间，容错范围是前后10秒，UNIX时间，单位为秒，如果不填将使用服务端时间填写
	ActionTime int64 `protobuf:"varint,3,opt,name=action_time,json=actionTime,proto3" json:"action_time"`
	//预定义的行为类型，目前只支持COMPLETE_ORDER（下单）及RESERVATION（表单预约）、CONFIRM_EFFECTIVE_LEADS（有效销售线索）
	ActionType string   `protobuf:"bytes,4,opt,name=action_type,json=actionType,proto3" json:"action_type"`
	Trace      *MpTrace `protobuf:"bytes,5,opt,name=trace,proto3" json:"trace"`
	//行为所带的参数，转化行为价值（例如金额），详见附录，字段长度最小 1 字节，长度最大 204800 字节
	ActionParam          *ActionParamForTakeOut `protobuf:"bytes,6,opt,name=action_param,json=actionParam,proto3" json:"action_param"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *PushMpForTakeOutData) Reset()         { *m = PushMpForTakeOutData{} }
func (m *PushMpForTakeOutData) String() string { return proto.CompactTextString(m) }
func (*PushMpForTakeOutData) ProtoMessage()    {}
func (*PushMpForTakeOutData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a04e905adbab2b96, []int{6}
}

func (m *PushMpForTakeOutData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMpForTakeOutData.Unmarshal(m, b)
}
func (m *PushMpForTakeOutData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMpForTakeOutData.Marshal(b, m, deterministic)
}
func (m *PushMpForTakeOutData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMpForTakeOutData.Merge(m, src)
}
func (m *PushMpForTakeOutData) XXX_Size() int {
	return xxx_messageInfo_PushMpForTakeOutData.Size(m)
}
func (m *PushMpForTakeOutData) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMpForTakeOutData.DiscardUnknown(m)
}

var xxx_messageInfo_PushMpForTakeOutData proto.InternalMessageInfo

func (m *PushMpForTakeOutData) GetUserActionSetId() string {
	if m != nil {
		return m.UserActionSetId
	}
	return ""
}

func (m *PushMpForTakeOutData) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PushMpForTakeOutData) GetActionTime() int64 {
	if m != nil {
		return m.ActionTime
	}
	return 0
}

func (m *PushMpForTakeOutData) GetActionType() string {
	if m != nil {
		return m.ActionType
	}
	return ""
}

func (m *PushMpForTakeOutData) GetTrace() *MpTrace {
	if m != nil {
		return m.Trace
	}
	return nil
}

func (m *PushMpForTakeOutData) GetActionParam() *ActionParamForTakeOut {
	if m != nil {
		return m.ActionParam
	}
	return nil
}

type ActionParamForTakeOut struct {
	//代表订单金额，单位为分，需要填写到param中获取，例如商品单价40元，需赋值为4000
	Value int32 `protobuf:"varint,1,opt,name=value,proto3" json:"value"`
	//回传有效销售线索必填 有效线索的来源区分，目前支持PHONE（电话直呼），RESERVE（表单预约）
	LeadsType string `protobuf:"bytes,2,opt,name=leads_type,json=leadsType,proto3" json:"leads_type"`
	//用户当前位置的经纬度信息，如22.5228070000,113.9353380000 注：必须使用gcj02坐标系
	UserLocations string `protobuf:"bytes,3,opt,name=user_locations,json=userLocations,proto3" json:"user_locations"`
	//菜名 单价:分，例如商品单价40元，需赋值为4000 份数 ["米饭_500_2","番茄鸡蛋_1000_1"]
	OrderDetail []string `protobuf:"bytes,4,rep,name=order_detail,json=orderDetail,proto3" json:"order_detail"`
	//收货地址经纬度信息，依序为纬度，经度。如23.0413930,113.3730940 注：必须使用gcj02坐标系
	ShippingLocations    string   `protobuf:"bytes,5,opt,name=shipping_locations,json=shippingLocations,proto3" json:"shipping_locations"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActionParamForTakeOut) Reset()         { *m = ActionParamForTakeOut{} }
func (m *ActionParamForTakeOut) String() string { return proto.CompactTextString(m) }
func (*ActionParamForTakeOut) ProtoMessage()    {}
func (*ActionParamForTakeOut) Descriptor() ([]byte, []int) {
	return fileDescriptor_a04e905adbab2b96, []int{7}
}

func (m *ActionParamForTakeOut) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActionParamForTakeOut.Unmarshal(m, b)
}
func (m *ActionParamForTakeOut) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActionParamForTakeOut.Marshal(b, m, deterministic)
}
func (m *ActionParamForTakeOut) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActionParamForTakeOut.Merge(m, src)
}
func (m *ActionParamForTakeOut) XXX_Size() int {
	return xxx_messageInfo_ActionParamForTakeOut.Size(m)
}
func (m *ActionParamForTakeOut) XXX_DiscardUnknown() {
	xxx_messageInfo_ActionParamForTakeOut.DiscardUnknown(m)
}

var xxx_messageInfo_ActionParamForTakeOut proto.InternalMessageInfo

func (m *ActionParamForTakeOut) GetValue() int32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *ActionParamForTakeOut) GetLeadsType() string {
	if m != nil {
		return m.LeadsType
	}
	return ""
}

func (m *ActionParamForTakeOut) GetUserLocations() string {
	if m != nil {
		return m.UserLocations
	}
	return ""
}

func (m *ActionParamForTakeOut) GetOrderDetail() []string {
	if m != nil {
		return m.OrderDetail
	}
	return nil
}

func (m *ActionParamForTakeOut) GetShippingLocations() string {
	if m != nil {
		return m.ShippingLocations
	}
	return ""
}

func init() {
	proto.RegisterType((*MpBaseResponse)(nil), "ext.MpBaseResponse")
	proto.RegisterType((*PushMpRequest)(nil), "ext.PushMpRequest")
	proto.RegisterType((*PushMpData)(nil), "ext.PushMpData")
	proto.RegisterType((*MpTrace)(nil), "ext.MpTrace")
	proto.RegisterType((*ActionParam)(nil), "ext.ActionParam")
	proto.RegisterType((*PushMpForTakeOutRequest)(nil), "ext.PushMpForTakeOutRequest")
	proto.RegisterType((*PushMpForTakeOutData)(nil), "ext.PushMpForTakeOutData")
	proto.RegisterType((*ActionParamForTakeOut)(nil), "ext.ActionParamForTakeOut")
}

func init() { proto.RegisterFile("ext/advertisement_mp.proto", fileDescriptor_a04e905adbab2b96) }

var fileDescriptor_a04e905adbab2b96 = []byte{
	// 545 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x54, 0x4d, 0x6f, 0xd3, 0x40,
	0x10, 0x95, 0x9b, 0x38, 0x21, 0x93, 0x7e, 0x31, 0x14, 0x70, 0x2b, 0x10, 0xc1, 0x7c, 0x28, 0x12,
	0x6a, 0x10, 0xed, 0x99, 0x43, 0xab, 0x82, 0x14, 0x89, 0x88, 0xca, 0xc9, 0x3d, 0x5a, 0xec, 0x51,
	0x6a, 0xd5, 0x8e, 0x97, 0xdd, 0x75, 0x94, 0xfe, 0x09, 0x0e, 0xfc, 0x27, 0xfe, 0x14, 0x27, 0xe4,
	0x59, 0x9b, 0x38, 0x51, 0x84, 0x10, 0x37, 0x6e, 0x9e, 0xb7, 0x6f, 0xde, 0xce, 0xcc, 0x1b, 0x2f,
	0x9c, 0xd0, 0xd2, 0xbc, 0x15, 0xd1, 0x82, 0x94, 0x89, 0x35, 0xa5, 0x34, 0x37, 0xd3, 0x54, 0x0e,
	0xa4, 0xca, 0x4c, 0x86, 0x0d, 0x5a, 0x1a, 0x7f, 0x02, 0xfb, 0x23, 0x79, 0x29, 0x34, 0x05, 0xa4,
	0x65, 0x36, 0xd7, 0x84, 0x08, 0xcd, 0x30, 0x8b, 0xc8, 0x73, 0x7a, 0x4e, 0xdf, 0x0d, 0xf8, 0x1b,
	0x3d, 0x68, 0xa7, 0xa4, 0xb5, 0x98, 0x91, 0xb7, 0xd3, 0x73, 0xfa, 0x9d, 0xa0, 0x0a, 0xf1, 0x08,
	0x5c, 0x52, 0x2a, 0x53, 0x5e, 0x83, 0x71, 0x1b, 0xf8, 0x63, 0xd8, 0xbb, 0xce, 0xf5, 0xcd, 0x48,
	0x06, 0xf4, 0x35, 0x27, 0x6d, 0xf0, 0x05, 0x34, 0xaf, 0x84, 0x11, 0x2c, 0xda, 0x3d, 0x3b, 0x18,
	0xd0, 0xd2, 0x0c, 0x2c, 0xa3, 0x80, 0x03, 0x3e, 0xc4, 0xa7, 0x00, 0xb9, 0x26, 0x35, 0x15, 0x33,
	0x9a, 0x1b, 0xbe, 0xc8, 0x0d, 0x3a, 0x05, 0x72, 0x51, 0x00, 0xfe, 0xb7, 0x1d, 0x80, 0x55, 0x0e,
	0xbe, 0x01, 0xb4, 0xec, 0xd0, 0xc4, 0xd9, 0x7c, 0xaa, 0xc9, 0x4c, 0xe3, 0x88, 0x2f, 0xe8, 0x04,
	0x07, 0x9c, 0xc5, 0x07, 0x63, 0x32, 0xc3, 0x08, 0x0f, 0xa1, 0x91, 0xab, 0xa4, 0x2c, 0xbe, 0xf8,
	0xc4, 0x67, 0xd0, 0x2d, 0x33, 0x4d, 0x9c, 0x12, 0x97, 0xdf, 0x08, 0xc0, 0x42, 0x93, 0x38, 0xa5,
	0x3a, 0xe1, 0x4e, 0x92, 0xd7, 0xe4, 0xd4, 0x8a, 0x70, 0x27, 0x09, 0x7d, 0x70, 0x8d, 0x12, 0x21,
	0x79, 0x2e, 0x37, 0xb5, 0xcb, 0x4d, 0x8d, 0xe4, 0xa4, 0xc0, 0x02, 0x7b, 0x84, 0xaf, 0xe1, 0x20,
	0xcb, 0xcd, 0xaa, 0xca, 0x38, 0xf2, 0x5a, 0x2c, 0xb4, 0xc7, 0xb0, 0x2d, 0x71, 0x18, 0xe1, 0x39,
	0xec, 0x96, 0x0c, 0x29, 0x94, 0x48, 0xbd, 0x36, 0x4b, 0x1e, 0xb2, 0xa4, 0x25, 0x5d, 0x17, 0x78,
	0x50, 0x96, 0xc4, 0x81, 0xff, 0x12, 0xda, 0xe5, 0x75, 0x78, 0x0c, 0xf7, 0xc2, 0x24, 0x0e, 0x6f,
	0x57, 0x23, 0x68, 0x73, 0x3c, 0x8c, 0xfc, 0x4b, 0xe8, 0xd6, 0x14, 0x0a, 0xc3, 0x16, 0x22, 0xc9,
	0x2b, 0x7f, 0x6d, 0x50, 0x8c, 0x3e, 0x21, 0x11, 0x69, 0xdb, 0xab, 0x1d, 0x53, 0x87, 0x91, 0xa2,
	0x55, 0x7f, 0x06, 0x8f, 0xed, 0xe4, 0x3f, 0x66, 0x6a, 0x22, 0x6e, 0xe9, 0x73, 0x6e, 0x2a, 0x67,
	0x4f, 0xd7, 0x9c, 0x3d, 0xae, 0x39, 0xbb, 0xe2, 0xfe, 0xbd, 0xc7, 0x3f, 0x1d, 0x38, 0xda, 0x96,
	0xfd, 0x7f, 0xb8, 0xfd, 0x7e, 0xc3, 0xc5, 0x16, 0x53, 0x4f, 0x36, 0x5d, 0xac, 0x0d, 0x71, 0xcd,
	0xcf, 0x1f, 0x0e, 0x3c, 0xdc, 0x4a, 0xfb, 0x27, 0xd3, 0xf0, 0x15, 0xec, 0xf3, 0xc8, 0x92, 0x2c,
	0x14, 0x85, 0xa8, 0x2e, 0xff, 0xd1, 0xbd, 0x02, 0xfd, 0x54, 0x81, 0xf8, 0x1c, 0x76, 0x33, 0x15,
	0x91, 0x9a, 0x46, 0x64, 0x44, 0x9c, 0x78, 0xcd, 0x5e, 0xa3, 0xdf, 0x09, 0xba, 0x8c, 0x5d, 0x31,
	0x84, 0xa7, 0x80, 0xfa, 0x26, 0x96, 0x32, 0x9e, 0xcf, 0x6a, 0x6a, 0x2e, 0xab, 0xdd, 0xaf, 0x4e,
	0x7e, 0x2b, 0x9e, 0x7d, 0x77, 0xe0, 0xd1, 0x45, 0xfd, 0xcd, 0x19, 0xc9, 0x31, 0xa9, 0x45, 0x1c,
	0x12, 0xbe, 0x83, 0x96, 0xb5, 0x17, 0xb1, 0xb6, 0x29, 0xe5, 0x2e, 0x9d, 0x3c, 0x28, 0x87, 0xba,
	0xf6, 0x1e, 0x7d, 0x80, 0xc3, 0xcd, 0x8d, 0xc0, 0x27, 0x5b, 0xd7, 0xec, 0x4f, 0x32, 0x5f, 0x5a,
	0xfc, 0xe8, 0x9d, 0xff, 0x0a, 0x00, 0x00, 0xff, 0xff, 0xe6, 0x2d, 0x25, 0xcf, 0x12, 0x05, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AdvertisementMpServiceClient is the client API for AdvertisementMpService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AdvertisementMpServiceClient interface {
	PushMp(ctx context.Context, in *PushMpRequest, opts ...grpc.CallOption) (*MpBaseResponse, error)
	PushMpForTakeOut(ctx context.Context, in *PushMpForTakeOutRequest, opts ...grpc.CallOption) (*MpBaseResponse, error)
}

type advertisementMpServiceClient struct {
	cc *grpc.ClientConn
}

func NewAdvertisementMpServiceClient(cc *grpc.ClientConn) AdvertisementMpServiceClient {
	return &advertisementMpServiceClient{cc}
}

func (c *advertisementMpServiceClient) PushMp(ctx context.Context, in *PushMpRequest, opts ...grpc.CallOption) (*MpBaseResponse, error) {
	out := new(MpBaseResponse)
	err := c.cc.Invoke(ctx, "/ext.AdvertisementMpService/PushMp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *advertisementMpServiceClient) PushMpForTakeOut(ctx context.Context, in *PushMpForTakeOutRequest, opts ...grpc.CallOption) (*MpBaseResponse, error) {
	out := new(MpBaseResponse)
	err := c.cc.Invoke(ctx, "/ext.AdvertisementMpService/PushMpForTakeOut", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdvertisementMpServiceServer is the server API for AdvertisementMpService service.
type AdvertisementMpServiceServer interface {
	PushMp(context.Context, *PushMpRequest) (*MpBaseResponse, error)
	PushMpForTakeOut(context.Context, *PushMpForTakeOutRequest) (*MpBaseResponse, error)
}

// UnimplementedAdvertisementMpServiceServer can be embedded to have forward compatible implementations.
type UnimplementedAdvertisementMpServiceServer struct {
}

func (*UnimplementedAdvertisementMpServiceServer) PushMp(ctx context.Context, req *PushMpRequest) (*MpBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushMp not implemented")
}
func (*UnimplementedAdvertisementMpServiceServer) PushMpForTakeOut(ctx context.Context, req *PushMpForTakeOutRequest) (*MpBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushMpForTakeOut not implemented")
}

func RegisterAdvertisementMpServiceServer(s *grpc.Server, srv AdvertisementMpServiceServer) {
	s.RegisterService(&_AdvertisementMpService_serviceDesc, srv)
}

func _AdvertisementMpService_PushMp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdvertisementMpServiceServer).PushMp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ext.AdvertisementMpService/PushMp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdvertisementMpServiceServer).PushMp(ctx, req.(*PushMpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdvertisementMpService_PushMpForTakeOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMpForTakeOutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdvertisementMpServiceServer).PushMpForTakeOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ext.AdvertisementMpService/PushMpForTakeOut",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdvertisementMpServiceServer).PushMpForTakeOut(ctx, req.(*PushMpForTakeOutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AdvertisementMpService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ext.AdvertisementMpService",
	HandlerType: (*AdvertisementMpServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushMp",
			Handler:    _AdvertisementMpService_PushMp_Handler,
		},
		{
			MethodName: "PushMpForTakeOut",
			Handler:    _AdvertisementMpService_PushMpForTakeOut_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ext/advertisement_mp.proto",
}
