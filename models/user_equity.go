package models

import (
	"encoding/json"
	"time"

	"github.com/go-xorm/xorm"
)

type IconInfo struct {
	LevelId int64  `json:"level_id"`
	Icon    string `json:"icon"`
}

type UserEquity struct {
	Id          int32     `json:"id"`
	EquityType  int32     `json:"equity_type"`
	EquityName  string    `json:"equity_name"`  // 权益名
	Icon        string    `json:"icon"`         // 权益图标,存JSON,例：[{"level_id":1, "icon":"https://www.aliyun.com/sss"}, {"level_id":2, "icon":"https://www.aliyun.com/ccc"}]
	EquityInfo  string    `json:"equity_info"`  // 权益简介
	EquityRules string    `json:"equity_rules"` // 规则
	CreateTime  time.Time `json:"create_time"`  // 创建时间
	UpdateTime  time.Time `json:"update_time"`
	IsVoucher   int32     `json:"is_voucher"`
	IsDisplay   int32     `json:"is_display"`

	Icons []IconInfo `json:"icons" xorm:"-"`
}

// 解析数组icon
func (m *UserEquity) AfterSet(name string, cell xorm.Cell) {
	if name == "icon" {
		json.Unmarshal((*cell).([]byte), &m.Icons)
	}
}
