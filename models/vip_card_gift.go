package models

import (
	"time"
)

type VipCardGift struct {
	Id         int32     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	PackName   string    `json:"pack_name" xorm:"default '' comment('礼包名称') VARCHAR(20) 'pack_name'"`
	PackDesc   string    `json:"pack_desc" xorm:"default '' comment('礼包描述') VARCHAR(50) 'pack_desc'"`
	PackPrice  float32   `json:"pack_price" xorm:"default '0.00' comment('礼包价值') DECIMAL(10) 'pack_price'"`
	PackImage  string    `json:"pack_image" xorm:"default '' comment('礼包图片') VARCHAR(200) 'pack_image'"`
	PackType   int32     `json:"pack_type" xorm:"default 0 comment('礼包商品类型') TINYINT(1) 'pack_type'"`
	PackSkuId  int32     `json:"pack_sku_id" xorm:"default 0 comment('礼包商品ID') INT(11) 'pack_sku_id'"`
	IsMain     int32     `json:"is_main" xorm:"default 0 comment('是否主推 0-否 1-是') TINYINT(1) 'is_main'"`
	State      int32     `json:"state" xorm:"default 0 comment('状态2-下架，1-上架') TINYINT(1) 'state'"`
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'create_time' created"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'update_time' updated"`
}

type VipCardGiftExtends struct {
	VipCardGift `xorm:"extends"`
	UserNo      string `json:"user_no"`
	UserName    string `json:"user_name"`
}

type VipOrderGiftExtends struct {
	Id          int32   `json:"id"`
	PackName    string  `json:"pack_name"`
	PackDesc    string  `json:"pack_desc"`
	PackPrice   float32 `json:"pack_price"`
	PackImage   string  `json:"pack_image"`
	PackSkuId   int32   `json:"pack_sku_id"`
	OrderSn     string  `json:"order_sn"`
	GiftOrderSn string  `json:"gift_order_sn"`
}
