package models

import (
	"_/proto/cc"
	"fmt"
	"github.com/ppkg/kit/cast"
	"time"
)

type UserStatistics struct {
	Id               int64     `json:"id" xorm:"pk autoincr not null INT(11)"`
	Type          int32     `xorm:"not null default 0 comment('类型：0=昨天，1=最近7天，2=近30天，3=近60天，4=近三个月，5=近半年') TINYINT(1)"`
	Level        int32    `xorm:"not null default 0 comment('0=v0,1=v1……') INT(11)"`
	UserTotal      int32    `xorm:"not null default 0 comment('累计会员数') INT(11)"`
	NewUserTotal      int32    `xorm:"not null default 0 comment('新增会员数') INT(11)"`
	PayUserTotal      int32    `xorm:"not null default 0 comment('支付会员数') INT(11)"`
	CouponUserTotal      int32    `xorm:"not null default 0 comment('领券会员数') INT(11)"`
	CouponNum      int32    `xorm:"not null default 0 comment('领券数') INT(11)"`
	CouponVerifyRate      float64    `xorm:"not null default 0 comment('领券会员核销率') decimal(11,2)"`
	UserPayAmount      float64    `xorm:"not null default 0 comment('会员支付金额') decimal(11,2)"`
	UserOrderTotal      int32    `xorm:"not null default 0 comment('会员支付订单数') INT(11)"`
	UserOrderPrice      float64    `xorm:"not null default 0 comment('会员客单价') decimal(11,2)"`
	UserNum      int32    `xorm:"not null default 0 comment('会员人数') INT(11)"`
	UserNumRate      float64    `xorm:"not null default 0 comment('会员人数占比') decimal(11,2)"`
	UserUpgradeNum      int32    `xorm:"not null default 0 comment('会员升级人数') INT(11)"`
	UserUpgradeNumRate      float64    `xorm:"not null default 0 comment('会员升级占比') decimal(11,2)"`
	UserDowngradeNum      int32    `xorm:"not null default 0 comment('会员降级人数') INT(11)"`
	UserDowngradeNumRate      float64    `xorm:"not null default 0 comment('会员降级占比') decimal(11,2)"`
	UpdateTime       time.Time `xorm:"default 'current_timestamp()' comment('最后更新时间') DATETIME updated"`
}

func (u *UserStatistics)ToMemberCountsDto() *cc.MemberCounts {
	memberCounts := &cc.MemberCounts{
		Level:                u.Level,
		UserTotal:            u.UserTotal,
		NewUserTotal:         u.NewUserTotal,
		PayUserTotal:         u.PayUserTotal,
		CouponUserTotal:      u.CouponUserTotal,
		CouponVerifyRate:     cast.ToFloat32(fmt.Sprintf("%.2f", u.CouponVerifyRate)),
		UserPayAmount:        cast.ToFloat32(fmt.Sprintf("%.2f", u.UserPayAmount)),
		UserOrderTotal:       u.UserOrderTotal,
		UserOrderPrice:       cast.ToFloat32(fmt.Sprintf("%.2f", u.UserOrderPrice)),
		UserNum:              u.UserNum,
		UserNumRate:          cast.ToFloat32(fmt.Sprintf("%.2f", u.UserNumRate)),
		UserUpgradeNum:       u.UserUpgradeNum,
		UserUpgradeNumRate:   cast.ToFloat32(fmt.Sprintf("%.2f", u.UserUpgradeNumRate)),
		UserDowngradeNum:     u.UserDowngradeNum,
		UserDowngradeNumRate: cast.ToFloat32(fmt.Sprintf("%.2f", u.UserDowngradeNumRate)),
	}

	return memberCounts
}



type Counts struct {
	Level int32 `json:"level"`
	Type int32 `json:"type"`
	Num int32 `json:"num"`
}
type ZlMemberCoupon struct {
	LevelId int32 `json:"level_id"`
	CreateTime time.Time `json:"create_time"`
	CouponCode string `json:"coupon_code"`
}
type CouponWriteOffDate struct {
	VoucherId int64 `json:"voucher_id"`
	WriteOffDate time.Time `json:"write_off_date"`
}

type ZlCouponWriteOffDate struct {
	PromotionNumber string `json:"promotion_number"`
	WriteOffDate time.Time `json:"write_off_date"`
}

type CouponUserCounts struct {
	//等级
	LevelId int32 `json:"level_id"`
	//会员数
	UserNum int32 `json:"user_num"`
}
type CouponCounts struct {
	//等级
	LevelId int32 `json:"level_id"`
	//券类型
	CouponType int32  `json:"coupon_type"`
	//总数
	TotalNum int32 `json:"total_num"`
}

type CouponWriteOff struct {
	LevelId int32 `json:"level_id"`
	TotalNum int32 `json:"total_num"`
}

type MemberInfoCounts struct {
	UserId string `json:"user_id"`
	Level int32 `json:"level"`
}

type PayCounts struct {
	LevelId int32 `json:"level_id"`
	PayUserTotal int32 `json:"pay_user_total"`
	UserOrderTotal int32 `json:"user_order_total"`
	TotalPayAmount int64 `json:"total_pay_amount"`
}











