package models

import (
	"time"
)

type VipCardEquity struct {
	Id                int32     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	OrIds             string    `json:"or_ids" xorm:"not null default '' comment('所属大区') varchar(20) 'or_ids'"`
	EquityIcon        string    `json:"equity_icon" xorm:"not null default '' comment('权益icon') VARCHAR(200) 'equity_icon'"`
	EquityName        string    `json:"equity_name" xorm:"default '' comment('权益名称') VARCHAR(50) 'equity_name'"`
	EquityShortName   string    `json:"equity_short_name" xorm:"default '' comment('权益短名称') VARCHAR(50) 'equity_short_name'"`
	EquityCopy        string    `json:"equity_copy" xorm:"default 'null' comment('权益方案') TEXT 'equity_copy'"`
	EquityPrice       float64   `json:"equity_price" xorm:"default '0.00' comment('权益价值') DECIMAL(10) 'equity_price'"`
	EquityType        int32     `json:"equity_type" xorm:"default 0 comment('权益类型') TINYINT(1) 'equity_type'"`
	MatchType         int32     `json:"match_type" xorm:"default 0 comment('匹配结果 1-单项填写,2-多项填写') TINYINT(1) 'match_type'"`
	IssueType         int32     `json:"issue_type" xorm:"default 0 comment('发放日期 1-开卡立即下发') TINYINT(1) 'issue_type'"`
	CollectionIds     string    `json:"collection_ids" xorm:"default '' comment('领取类型 1-首次开卡、2-续费开卡') VARCHAR(10) 'collection_ids'"`
	Status            int32     `json:"status" xorm:"default 0 comment('显示状态：0-未生效即将上线，1-生效正常显示') TINYINT(1) 'status'"`
	JumpUrl           string    `json:"jump_url" xorm:"not null default '' comment('跳转链接') VARCHAR(255) 'jump_url'"`
	EquityInfo        string    `json:"equity_info" xorm:"default 'null' comment('权益介绍') TEXT 'equity_info'"`
	EquityRule        string    `json:"equity_rule" xorm:"default 'null' comment('权益规则') TEXT 'equity_rule'"`
	EquityImg         string    `json:"equity_img" xorm:"default 'null' comment('权益宣传图') VARCHAR(255) 'equity_img'"`
	ExpiryDay         int32     `json:"expiry_day" xorm:"default 0 comment('权益有效期') TINYINT(2) 'expiry_day'"`
	ReceiveType       int32     `json:"receive_type" xorm:"default 0 comment('领取个数类型 1-多选一 2-多选多') TINYINT(2) 'receive_type'"`
	IsActive          int32     `json:"is_active" xorm:"default 0 comment('1.主动领取 2.被动领取') TINYINT(2) 'is_active'"`
	MainTitle         string    `json:"main_title" xorm:"not null default '' comment('未领取权益主标题') VARCHAR(20) 'main_title'"`
	SubTitle          string    `json:"sub_title" xorm:"default 'null' comment('未领取权益副标题') VARCHAR(20) 'sub_title'"`
	EquityReceiveIcon string    `json:"equity_receive_icon" xorm:"not null default '' comment('权益领取图标') VARCHAR(255) 'equity_receive_icon'"`
	CreateTime        time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'create_time' created"`
	UpdateTime        time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'update_time' updated"`
}

type BaseVipCardEquity struct {
	EquityIcon  string  `json:"equity_icon" xorm:"not null default '' comment('权益icon') VARCHAR(200) 'equity_icon'"`
	EquityName  string  `json:"equity_name" xorm:"default '' comment('权益名称') VARCHAR(50) 'equity_name'"`
	EquityCopy  string  `json:"equity_copy" xorm:"default 'null' comment('权益方案') TEXT 'equity_copy'"`
	EquityPrice float64 `json:"equity_price" xorm:"default '0.00' comment('权益价值') DECIMAL(10) 'equity_price'"`
	EquityType  int32   `json:"equity_type" xorm:"default 0 comment('权益类型：1-商城优惠券，2-子龙门店券，3-积分翻倍，4-会员价商品，5-大牌礼包，6-家庭服务包 7-宠物医保链接(洗护报销、买药报销、看病报销)') TINYINT(1) 'equity_type'"`
}
