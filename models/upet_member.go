package models

type UpetMember struct {
	MemberId              int64  `json:"member_id"`               // 会员id
	ScrmUserId            string `json:"scrm_user_id"`            // 用户ID作为唯一标示
	MemberName            string `json:"member_name"`             // 会员名称
	MemberTruename        string `json:"member_truename"`         // 真实姓名
	MemberAvatar          string `json:"member_avatar"`           // 会员头像
	MemberWxavatar        string `json:"member_wxavatar"`         // 微信头像
	MemberSex             int64  `json:"member_sex"`              // 会员性别
	MemberBirthday        string `json:"member_birthday"`         // 生日
	MemberPasswd          string `json:"member_passwd"`           // 会员密码
	MemberPaypwd          string `json:"member_paypwd"`           // 支付密码
	MemberEmail           string `json:"member_email"`            // 会员邮箱
	MemberEmailBind       int64  `json:"member_email_bind"`       // 0未绑定1已绑定
	MemberMobile          string `json:"member_mobile"`           // 手机号
	MemberMobileBind      int64  `json:"member_mobile_bind"`      // 0未绑定1已绑定
	MemberQq              string `json:"member_qq"`               // qq
	MemberWw              string `json:"member_ww"`               // 阿里旺旺
	MemberLoginNum        int64  `json:"member_login_num"`        // 登录次数
	MemberTime            string `json:"member_time"`             // 会员注册时间
	MemberLoginTime       string `json:"member_login_time"`       // 当前登录时间
	MemberOldLoginTime    string `json:"member_old_login_time"`   // 上次登录时间
	MemberLoginIp         string `json:"member_login_ip"`         // 当前登录ip
	MemberOldLoginIp      string `json:"member_old_login_ip"`     // 上次登录ip
	MemberQqopenid        string `json:"member_qqopenid"`         // qq互联id
	MemberQqinfo          string `json:"member_qqinfo"`           // qq账号相关信息
	MemberSinaopenid      string `json:"member_sinaopenid"`       // 新浪微博登录id
	MemberSinainfo        string `json:"member_sinainfo"`         // 新浪账号相关信息序列化值
	WeixinUnionid         string `json:"weixin_unionid"`          // 微信用户统一标识
	WeixinInfo            string `json:"weixin_info"`             // 微信用户相关信息
	MemberPoints          int64  `json:"member_points"`           // 会员积分
	AvailablePredeposit   string `json:"available_predeposit"`    // 预存款可用金额
	FreezePredeposit      string `json:"freeze_predeposit"`       // 预存款冻结金额
	AvailableRcBalance    string `json:"available_rc_balance"`    // 可用充值卡余额
	FreezeRcBalance       string `json:"freeze_rc_balance"`       // 冻结充值卡余额
	InformAllow           int64  `json:"inform_allow"`            // 是否允许举报(1可以/2不可以)
	IsBuy                 int64  `json:"is_buy"`                  // 会员是否有购买权限 1为开启 0为关闭
	IsAllowtalk           int64  `json:"is_allowtalk"`            // 会员是否有咨询和发送站内信的权限 1为开启 0为关闭
	MemberState           int64  `json:"member_state"`            // 会员的开启状态 1为开启 0为关闭
	MemberAreaid          int64  `json:"member_areaid"`           // 地区ID
	MemberCityid          int64  `json:"member_cityid"`           // 城市ID
	MemberProvinceid      int64  `json:"member_provinceid"`       // 省份ID
	MemberAreainfo        string `json:"member_areainfo"`         // 地区内容
	MemberPrivacy         string `json:"member_privacy"`          // 隐私设定
	MemberExppoints       int32  `json:"member_exppoints"`        // 会员经验值
	TradAmount            string `json:"trad_amount"`             // 可提现金额
	AuthMessage           string `json:"auth_message"`            // 审核意见
	DistriState           int64  `json:"distri_state"`            // 分销状态 0未申请 1待审核 2已通过 3未通过 4清退 5退出
	BillUserName          string `json:"bill_user_name"`          // 收款人姓名
	BillTypeCode          string `json:"bill_type_code"`          // 结算账户类型
	BillTypeNumber        string `json:"bill_type_number"`        // 收款账号
	BillBankName          string `json:"bill_bank_name"`          // 开户行
	FreezeTrad            string `json:"freeze_trad"`             // 冻结佣金
	DistriCode            string `json:"distri_code"`             // 分销代码
	DistriChainid         int64  `json:"distri_chainid"`          // 分销门店ID
	DistriBrandid         int64  `json:"distri_brandid"`          // 品牌ID
	DistriTime            int64  `json:"distri_time"`             // 申请时间
	DistriHandleTime      int64  `json:"distri_handle_time"`      // 处理时间
	DistriShow            int64  `json:"distri_show"`             // 分销中心是否显示 0不显示 1显示
	QuitTime              int64  `json:"quit_time"`               // 退出时间
	DistriApplyTimes      int64  `json:"distri_apply_times"`      // 申请次数
	DistriQuitTimes       int64  `json:"distri_quit_times"`       // 退出次数
	MemberSnsvisitnum     int64  `json:"member_snsvisitnum"`      // 消息订阅次数
	WeixinMpOpenid        string `json:"weixin_mp_openid"`        // 微信公众号OpenID
	IsCash                int64  `json:"is_cash"`                 // 是否允许提现，0否，1是
	IdCardName            string `json:"id_card_name"`            // 实名认证姓名
	IdCardCode            string `json:"id_card_code"`            // 身份证号
	IdCardBind            int64  `json:"id_card_bind"`            // 是否实名认证0否,1是
	IdCardState           int64  `json:"id_card_state"`           // 审核状态0未申请，1待审核，2审核成功，3审核失败
	IdCardExplain         string `json:"id_card_explain"`         // 审核说明
	IdCardImg             string `json:"id_card_img"`             // 身份证正反面图片
	WeixinMiniOpenid      string `json:"weixin_mini_openid"`      // 小程序openid(阿闻宠物-北京那边用)
	WeixinMiniAddtime     int64  `json:"weixin_mini_addtime"`     // 小程序绑定时间(阿闻宠物-北京那边用)
	WeixinMiniOpenidshop  string `json:"weixin_mini_openidshop"`  // 小程序openid(阿闻智慧门店-自用)
	WeixinMiniAddtimeshop int64  `json:"weixin_mini_addtimeshop"` // 小程序绑定时间(阿闻智慧门店-自用)
	WeixinMiniOpenidasq   string `json:"weixin_mini_openidasq"`   // 小程序openid(阿闻爱省钱-自用)
	WeixinMiniAddtimeasq  int64  `json:"weixin_mini_addtimeasq"`  // 小程序绑定时间(阿闻爱省钱-自用)
	WeixinMiniAddtimemall int64  `json:"weixin_mini_addtimemall"` // 小程序绑定时间(阿闻商城-自用)
	EarnestMoney          string `json:"earnest_money"`           // 保证金金额
	GevalCommentStatus    int64  `json:"geval_comment_status"`    // 0为电商 1为采集 2为宠医云 3阿闻智慧宠物医院4阿闻宠物小程序5阿闻爱省钱6阿闻商城7数据中心8佳雯会员
	DisTradMoney          string `json:"dis_trad_money"`          // 累计收益
	BillBankBranch        string `json:"bill_bank_branch"`        // 开户银行支行名称
	MemberIsvip           int64  `json:"member_isvip"`            // 0.默认1.198会员
	MemberIsbzk           int64  `json:"member_isbzk"`            // 0.默认1.保障卡
	MemberVipstime        int64  `json:"member_vipstime"`         // 会员开始时间
	MemberVipetime        int64  `json:"member_vipetime"`         // 会员过期时间
	MemberBzkstime        int64  `json:"member_bzkstime"`         // 保障卡开始时间
	MemberBzketime        int64  `json:"member_bzketime"`         // 保障卡结束时间
	UserLevelId           int64  `json:"user_level_id"`           // 会员等级
	UserLevelStime        int64  `json:"user_level_stime"`        // 会员等级开始时间
	UserLevelEtime        int64  `json:"user_level_etime"`        // 会员等级过期时间
	MemberIdentity        string `json:"member_identity"`         // 分销员身份证
	MemberMobileBefore    string `json:"member_mobile_before"`    // 修改前手机号
	MemberVipstarttime    int64  `json:"member_vipstarttime"`     // 电商198会员开始时间
	MemberVipendtime      int64  `json:"member_vipendtime"`       // 电商198会员结束时间
	NewcomerTag           int64  `json:"newcomer_tag"`            // 新用户标记，null未更新、1=新人、2，有可能成为新人、3=老用户
	VipCardState          int64  `json:"vip_card_state"`          // 付费会员:0-否，1-是
	WeixinMiniOpenid2     string `json:"weixin_mini_openid2"`     // 极宠家微信小程序openid
}
