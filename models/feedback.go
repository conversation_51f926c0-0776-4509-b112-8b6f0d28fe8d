package models

import (
	"time"
)

type Feedback struct {
	Id         int32     `xorm:"not null pk autoincr INT(10)"`
	UserId     string    `xorm:"not null default '''' comment('用户id(scrmId)') VARCHAR(100)"`
	UserName   string    `xorm:"not null default '''' comment('用户昵称') VARCHAR(20)"`
	Type       int32     `xorm:"not null default 0 comment('类型：0：建议  1咨询 2投诉') TINYINT(1)"`
	Phone      string    `xorm:"not null default '''' comment('用户手机号') VARCHAR(11)"`
	Content    string    `xorm:"default 'NULL' comment('反馈内容') VARCHAR(200)"`
	IsDraft    int32     `xorm:"not null default 0 comment('是否是草稿 1是 0否') TINYINT(1)"`
	Images     string    `xorm:"not null default '''' comment('反馈图片') VARCHAR(1024)"`
	ClientInfo string    `xorm:"default 'NULL' comment('客户端信息') TEXT"`
	Version    string    `xorm:"default 'NULL' comment('客户端版本') VARCHAR(20)"`
	StoreId    int32     `xorm:"not null default 1 comment('小程序主体：1-默认，2-极宠家') TINYINT(4)"`
	CreateTime time.Time `xorm:"created not null default 'current_timestamp()' comment('创建时间') index DATETIME"`
	UpdateTime time.Time `xorm:"updated default 'NULL' comment('更新时间') DATETIME"`
}
