package models

import "_/proto/cc"

type BaseZlResponse struct {
	Message       string      `json:"message"`
	SystemError   interface{} `json:"systemError"`
	BusinessError interface{} `json:"businessError"`
	StatusCode    int         `json:"statusCode"`
	Success       bool        `json:"success"`
	Result        string      `json:"result"`
}

//----------------------------宠物花色

type PetFlowerDictBjRes struct {
	Result        PetFlowerResult `json:"result"`
	Message       string          `json:"message"`
	SystemError   string          `json:"systemError"`
	BusinessError string          `json:"businessError"`
	StatusCode    int32           `json:"statusCode"`
	Extensions    struct {
	} `json:"extensions"`
	Success bool `json:"success"`
}
type PetFlowerResult struct {
	Id          int32            `json:"id"`
	Code        int32            `json:"code"`
	Name        string           `json:"name"`
	Status      int32            `json:"status"`
	Orders      int32            `json:"orders"`
	EnglishName string           `json:"englishName"`
	Path        string           `json:"path"`
	ParentCode  int32            `json:"parentCode"`
	Child       []*PetFlowerDict `json:"child"`
}
type PetFlowerDict struct {
	Id          int32  `json:"id"`
	Code        string `json:"code"` //宠物花色
	Name        string `json:"name"` //花色编码
	Status      int32  `json:"status"`
	Orders      int32  `json:"orders"`
	EnglishName string `json:"englishName"`
	Path        string `json:"path"`
	ParentCode  int32  `json:"parentCode"`
}

type PetListRequest struct {
}
type PetListResponse struct {
	BaseZlResponse
	Result []*cc.PetInfo `json:"result"`
}

type LastDiagnosisRecordResponse struct {
	BaseZlResponse
	Result *cc.LastDiagnosis `json:"result"`
}

//type ExpellingParasiteResponse struct {
//	BaseZlResponse
//	Result []*cc.ExpellingParasite `json:"result"`
//}

type VaccinateRecordResponse struct {
	BaseZlResponse
	Result VaccinateRecordData `json:"result"`
}
type VaccinateRecordData struct {
	PageSize  int                   `json:"pageSize"`
	PageIndex int                   `json:"pageIndex"`
	Total     int                   `json:"total"`
	PageCount int                   `json:"pageCount"`
	Result    []*cc.VaccinateRecord `json:"result"`
}

type LastDiagnosis struct {
	//治疗意见
	TreatmentOpinion string `json:"treatmentOpinion"`
	//疾病诊断
	MainSymptom string `json:"mainSymptom"`
	//开始时间
	StartTime string `json:"startTime"`
	//结束时间
	EndTime string `json:"endTime"`
	//医嘱
	DoctorAdvice string `json:"doctorAdvice"`
}

type NextImmunityResponse struct {
	BaseZlResponse
	Result *cc.NextImmunity `json:"result"`
}
type NextImmunity struct {
	Date string `json:"date"`
	Tip  string `json:"tip"`
}
type NextExpellingParasiteResponse struct {
	BaseZlResponse
	Result *cc.NextExpellingParasite `json:"result"`
}
type NextExpellingParasite struct {
	Age           string `json:"age"`
	RecordTime    string `json:"recordTime"`
	Content       string `json:"content"`
	ContentDetail string `json:"contentDetail"`
	Tip           string `json:"tip"`
}

type MedRecordList struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Total    int `json:"total"`
		Page     int `json:"page"`
		PageSize int `json:"page_size"`
		List     []struct {
			Orgid          int    `json:"orgid"`
			RegID          int    `json:"reg_id"`
			MedType        int    `json:"med_type"`
			PhysicianID    int    `json:"physician_id"`
			PhysicianName  string `json:"physician_name"`
			CusLogicid     int    `json:"cus_logicid"`
			CusName        string `json:"cus_name"`
			PetLogicid     int    `json:"pet_logicid"`
			PetName        string `json:"pet_name"`
			CreateTime     string `json:"create_time"`
			EndTime        string `json:"end_time"`
			RecordType     int    `json:"record_type"`
			HospitalName   string `json:"hospital_name"`
			RecordTypeText string `json:"record_type_text"`
			MainSymptom    string `json:"main_symptom"`
		} `json:"list"`
	} `json:"data"`
}

type MedRecordInfo struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Orgid                 int    `json:"orgid"`
		MedType               string `json:"med_type"`
		RegID                 int    `json:"reg_id"`
		PhysicianID           int    `json:"physician_id"`
		PhysicianName         string `json:"physician_name"`
		MainSymptom           string `json:"main_symptom"`
		CusLogicid            int    `json:"cus_logicid"`
		CusName               string `json:"cus_name"`
		PetName               string `json:"pet_name"`
		PetLogicid            int    `json:"pet_logicid"`
		CreateTime            string `json:"create_time"`
		EndTime               string `json:"end_time"`
		ChiefComplaint        string `json:"chief_complaint"`
		PastHistory           string `json:"past_history"`
		PhysicalDescription   string `json:"physical_description"`
		TreatmentOpinion      string `json:"treatment_opinion"`
		DoctorAdvice          string `json:"doctor_advice"`
		HospitalName          string `json:"hospital_name"`
		MedLevelText          string `json:"med_level_text"`
		DiseaseUrgencyText    string `json:"disease_urgency_text"`
		PhysicalLevelText     string `json:"physical_level_text"`
		MedResultText         string `json:"med_result_text"`
		LeaveSymptom          string `json:"leave_symptom"`
		MedTypeText           string `json:"med_type_text"`
		IllnessDesc           string `json:"illness_desc"`
		PhysicalDesc          string `json:"physical_desc"`
		InpatientDoctorAdvice string `json:"inpatient_doctor_advice"`
		PhysicalExamination   []struct {
			Weight        string `json:"weight"`
			Temperature   string `json:"temperature"`
			BreathingRate string `json:"breathing_rate"`
			HeartRate     string `json:"heart_rate"`
		} `json:"physical_examination"`
		MedMedias []struct {
			ID     int         `json:"id"`
			PicURL string      `json:"pic_url"`
			Remark interface{} `json:"remark"`
		} `json:"med_medias"`
		SpecificTreatments []struct {
			PhysicianID       int    `json:"physician_id"`
			PhysicianName     string `json:"physician_name"`
			ExecPhysicianID   string `json:"exec_physician_id"`
			ExecPhysicianName string `json:"exec_physician_name"`
			InsertTime        string `json:"insert_time"`
			Description       string `json:"description"`
		} `json:"specific_treatments"`
		PetInfo struct {
			PetName          string  `json:"pet_name"`
			PetLogicid       int     `json:"pet_logicid"`
			PetGenderText    string  `json:"pet_gender_text"`
			PetKindofText    string  `json:"pet_kindof_text"`
			PetVarietyText   string  `json:"pet_variety_text"`
			ColorTxt         string  `json:"color_txt"`
			PetAge           string  `json:"pet_age"`
			PetWeight        float64 `json:"pet_weight"`
			PetNeuteringText string  `json:"pet_neutering_text"`
			PetStatusText    string  `json:"pet_status_text"`
			IsVaccinatedTxt  string  `json:"is_vaccinated_txt"`
			IsDewormingTxt   string  `json:"is_deworming_txt"`
		} `json:"pet_info"`
	} `json:"data"`
}

type ZlReturnError struct {
	Code int           `json:"code"`
	Msg  string        `json:"msg"`
	Data []interface{} `json:"data"`
}

type NewMedRecordList struct {
	PageCount int        `json:"page_count"`
	Message   string     `json:"message"`
	Data      []*PetCase `json:"data"`
}

type PetCase struct {
	// 机构id
	Orgid          int    `json:"orgid"`
	RegId          int    `json:"reg_id"`         // 挂号id
	MedType        int    `json:"med_type"`       // 病例类型 【0-门诊；1-住院】
	PhysicianId    int    `json:"physician_id"`   // 医生id
	PhysicianName  string `json:"physician_name"` // 医生名称
	CusLogicid     int    `json:"cus_logicid"`    // 客户id
	CusName        string `json:"cus_name"`       // 客户名称
	PetLogicid     int    `json:"pet_logicid"`    //  宠物id
	PetName        string `json:"pet_name"`       // 宠物名称
	CreateTime     string `json:"create_time"`    // 开始时间
	EndTime        string `json:"end_time"`       // 结束时间
	RecordType     int    `json:"record_type"`    // 挂号类型【1020 初诊； 1021-复诊】
	HospitalName   string `json:"hospital_name"`  // 机构名称
	HospitalCode   string `json:"hospital_code"`
	RecordTypeText string `json:"record_type_text"` // 挂号类型文本
	MainSymptom    string `json:"main_symptom"`     // 诊断信息
}

type BaseZlInfoUpdateResponse struct {
	Message       string      `json:"message"`
	SystemError   interface{} `json:"systemError"`
	BusinessError interface{} `json:"businessError"`
	StatusCode    int         `json:"statusCode"`
	Success       bool        `json:"success"`
	Result        interface{} `json:"result"`
}
