package models

import (
	"time"
)

const (
	// 日志类型，1编辑会员等级、2启用会员等级、3停用会员等级
	UserLevelLogTypeEdit = iota + 1
	UserLevelLogTypeEnable
	UserLevelLogTypeDisanable
)

type UserLevelLog struct {
	Id                int64     `json:"id" xorm:"pk autoincr not null comment('自增ID') INT(10) 'id'"`
	LevelId           int64     `json:"level_id" xorm:"not null default 0 comment('等级ID') TINYINT(4) 'level_id'"`
	LogType           int64     `json:"log_type" xorm:"not null default 0 comment('日志类型，1编辑会员等级、2启用会员等级、3停用会员等级') TINYINT(1) 'log_type'"`
	Content           string    `json:"content" xorm:"not null default '' comment('会员等级操作日志') VARCHAR(255) 'content'"`
	LevelRefreshState int64     `json:"level_refresh_state" xorm:"not null default 0 comment('会员等级刷新状态，1待刷新、2刷新中、3已结束、4异常') TINYINT(1) 'level_refresh_state'"`
	LevelRefreshError string    `json:"level_refresh_error" xorm:"not null default '' comment('会员刷新异常信息') VARCHAR(255) 'level_refresh_error'"`
	MemberPriceState  int64     `json:"member_price_state" xorm:"not null default 0 comment('商品会员价格刷新状态，1待刷新、2刷新中、3已结束、4异常') TINYINT(1) 'member_price_state'"`
	MemberPriceError  string    `json:"member_price_error" xorm:"not null default '' comment('商品会员价格刷新状态异常信息') VARCHAR(255) 'member_price_error'"`
	CreateTime        time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime        time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}
