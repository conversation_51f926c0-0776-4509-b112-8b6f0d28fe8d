package models

import (
	"time"
)

type WechatSubscribeMessageRecord struct {
	Id            int64     `json:"id" xorm:"pk autoincr not null INT(10) 'id'"`
	ScrmUserId    string    `json:"scrm_user_id" xorm:"not null default '' comment('用户ID作为唯一标识') VARCHAR(32) 'scrm_user_id'"`
	TemplateId    string    `json:"template_id" xorm:"not null default '' comment('微信订阅消息模板id') VARCHAR(100) 'template_id'"`
	MessageData   string    `json:"message_data" xorm:"not null default '' comment('消息内容') VARCHAR(500) 'message_data'"`
	MessageParams string    `json:"message_params" xorm:"not null default '' comment('消息参数') VARCHAR(255) 'message_params'"`
	CreateTime    time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime    time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}
