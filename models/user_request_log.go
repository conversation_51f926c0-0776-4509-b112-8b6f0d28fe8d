package models

type RequestInit struct {
	Id              int64  `xorm:"pk autoincr BIGINT(20)"`
	GlobalRequestId string `xorm:"default 'NULL' comment('全局请求ID（唯一）') unique VARCHAR(36)"`
	UserId          string `xorm:"default 'NULL' comment('用户ID') index VARCHAR(36)"`
	Imei            string `xorm:"default 'NULL' comment('手机IMEI设备ID') VARCHAR(50)"`
	ChannelId       int    `xorm:"default NULL comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店') INT(11)"`
	UserAgent       int    `xorm:"default NULL comment('渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它，7-竖屏') INT(11)"`
	City            string `xorm:"default 'NULL' comment('城市名称') VARCHAR(50)"`
	LonLat          string `xorm:"default 'NULL' comment('经纬度') VARCHAR(50)"`
	Os              string `xorm:"default 'NULL' comment('系统（android或者ios等）') VARCHAR(50)"`
	Device          string `xorm:"default 'NULL' comment('设备型号（小米6，iPhone4）') VARCHAR(50)"`
	RequestTime     string `xorm:"default 'NULL' comment('前端用户时间') TIMESTAMP"`
}

type RequestApi struct {
	Id              int64  `xorm:"pk autoincr BIGINT(20)"`
	GlobalRequestId string `xorm:"default 'NULL' comment('全局请求ID（唯一）') VARCHAR(36)"`
	PageRequestId   string `xorm:"default 'NULL' comment('页面请求ID，当前页面唯一') VARCHAR(36)"`
	Path            string `xorm:"default 'NULL' comment('接口地址') VARCHAR(500)"`
	RawQuery        string `xorm:"default 'NULL' comment('path参数') VARCHAR(10000)"`
	RequestTime     string `xorm:"default 'NULL' DATETIME"`
}

type RequestApiData struct {
	Id            int64  `xorm:"pk autoincr BIGINT(20)"`
	PageRequestId string `xorm:"default 'NULL' VARCHAR(36)"`
	Header        string `xorm:"default 'NULL' comment('请求头') TEXT"`
	RequestBody   string `xorm:"default 'NULL' comment('请求体') TEXT"`
	ResponseBody  string `xorm:"default 'NULL' comment('响应体') TEXT"`
}

type PageInfo struct {
	Id        int64  `xorm:"pk autoincr BIGINT(20)"`
	Path      string `xorm:"default 'NULL' comment('页路径/事件名称/控制器名称') unique(path) VARCHAR(255)"`
	Type      int    `xorm:"default NULL comment('path类型（1=页面，2=事件名称）') unique(path) INT(11)"`
	Name      string `xorm:"default 'NULL' comment('页面名称') VARCHAR(255)"`
	ChannelId int    `xorm:"default NULL comment('渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店') unique(path) INT(11)"`
	UserAgent int    `xorm:"default NULL comment('渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它，7-竖屏') unique(path) INT(11)"`
}

type RequestPage struct {
	Id              int64  `xorm:"pk autoincr BIGINT(20)"`
	GlobalRequestId string `xorm:"default 'NULL' comment('全局请求ID') VARCHAR(36)"`
	PageRequestId   string `xorm:"default 'NULL' comment('页面请求ID，当前页面唯一') unique VARCHAR(36)"`
	Path            string `xorm:"default 'NULL' comment('页面/事件/控制器') VARCHAR(255)"`
	Args            string `xorm:"default 'NULL' comment('页面参数') VARCHAR(255)"`
	BeforePath      string `xorm:"default 'NULL' comment('上一页面') VARCHAR(255)"`
	RequestTime     string `xorm:"default 'NULL' comment('请求时间') TIMESTAMP"`
	Type            int    `xorm:"default 'NULL' comment('事件类型1=页面，2=按钮') INT(11)"`
	EventName       string `xorm:"default 'NULL' comment('事件名称') VARCHAR(255)"`
	Extra           string `xorm:"default 'NULL' comment('业务参数') VARCHAR(255)"`
}
type RequestPageNew struct {
	Id              int64       `xorm:"pk autoincr BIGINT(20)"`
	GlobalRequestId string      `xorm:"default 'NULL' comment('全局请求ID') VARCHAR(36)"`
	PageRequestId   string      `xorm:"default 'NULL' comment('页面请求ID，当前页面唯一') unique VARCHAR(36)"`
	Path            string      `xorm:"default 'NULL' comment('页面/事件/控制器') VARCHAR(255)"`
	Args            string      `xorm:"default 'NULL' comment('页面参数') VARCHAR(255)"`
	BeforePath      string      `xorm:"default 'NULL' comment('上一页面') VARCHAR(255)"`
	RequestTime     string      `xorm:"default 'NULL' comment('请求时间') TIMESTAMP"`
	Type            int         `xorm:"default 'NULL' comment('事件类型1=页面，2=按钮') INT(11)"`
	EventName       string      `xorm:"default 'NULL' comment('事件名称') VARCHAR(255)"`
	Extra           interface{} `xorm:"default 'NULL' comment('业务参数') VARCHAR(255)"`
}

type UserLogKfk struct {
	UserId      string
	Imei        string
	ChannelId   int
	UserAgent   int
	City        string
	LonLat      string
	Os          string
	Device      string
	Path        string
	Args        string
	BeforePath  string
	Type        int
	PageIndex   int
	RequestTime string
	Sku         []string
	Tips        []string
	KeyWords    string
}
