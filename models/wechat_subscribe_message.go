package models

import "time"

// 微信订阅消息次数
type WechatSubscribeMessage struct {
	Id         int64     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	ScrmUserId string    `json:"scrm_user_id" xorm:"not null default '' comment('用户ID作为唯一标识') VARCHAR(32) 'scrm_user_id'"`
	TemplateId string    `json:"template_id" xorm:"not null default '' comment('微信消息模板id') VARCHAR(100) 'template_id'"`
	Number     int64     `json:"number" xorm:"not null default 0 comment('模板订阅次数') INT(11) 'number'"`
	CreateTime time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}
