package models

import "time"

type PosOrders struct {
	Id                  int       `json:"id" xorm:"pk autoincr not null comment('主键') INT 'id'"`
	OrderSn             string    `json:"order_sn" xorm:"not null comment('订单编号') VARCHAR(255) 'order_sn'"`
	ZlCustomerId        int       `json:"zl_customer_id" xorm:"not null comment('子龙用户id') INT 'zl_customer_id'"`
	ScrmUserId          string    `json:"scrm_user_id" xorm:"default 'null' comment('用户编号') VARCHAR(50) 'scrm_user_id'"`
	TotalAmount         int   `json:"total_amount" xorm:"default 0 comment('总金额') DECIMAL(19) 'total_amount'"`
	ActualAmount        int   `json:"actual_amount" xorm:"default 0 comment('实付金额') DECIMAL(19) 'actual_amount'"`
	DiscountAmount      int   `json:"discount_amount" xorm:"default 0 comment('优惠金额') DECIMAL(19) 'discount_amount'"`
	PresentPayedAmount  int   `json:"present_payed_amount" xorm:"default 0 comment('赠送支付金额') DECIMAL(19) 'present_payed_amount'"`
	RefundAmount        int   `json:"refund_amount" xorm:"default 0 comment('退款金额') DECIMAL(19) 'refund_amount'"`
	RefundPresentAmount int   `json:"refund_present_amount" xorm:"default 0 comment('退款赠送金额') DECIMAL(19) 'refund_present_amount'"`
	IsPush              int       `json:"is_push" xorm:"default 0 comment('推送健康值状态（0待推送 1 不推送 2 已推送（根据推送次数，如果为0 ，推送次数为5就不会再重试））') INT 'is_push'"`
	PushNum             int       `json:"push_num" xorm:"default 0 comment('推送次数') INT 'push_num'"`
	CreatedTime         time.Time `json:"created_time" xorm:"default 'null' comment('创建时间') DATETIME 'created_time'"`
	ShopId              string    `json:"shop_id" xorm:"default '' VARCHAR(32) 'shop_id'"`
	ShopName            string    `json:"shop_name" xorm:"default '' VARCHAR(50) 'shop_name'"`
	PaymentType              int       `json:"payment_type" xorm:"default 0 comment('子龙支付方式') INT 'payment_type'"`
}