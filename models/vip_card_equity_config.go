package models

import (
	"time"
)

type VipCardEquityConfig struct {
	CardTid      int32     `json:"card_tid" xorm:"pk not null default 0 comment('卡付费周期id') INT(11) 'card_tid'"`
	OrId         int64     `json:"or_id" xorm:"not null default 0 comment('所属大区') TINYINT(2) 'or_id'"`
	EquityId     int32     `json:"equity_id" xorm:"pk not null default 0 comment('权益id') TINYINT(1) 'equity_id'"`
	Status       int32     `json:"status" xorm:"not null default 1 comment('显示状态：0-即将上线，1-正常显示') TINYINT(1) 'status'"`
	ReceiveNum   int32     `json:"receive_num" xorm:"not null default 1 comment('领取个数') TINYINT(2) 'receive_num'"`
	Sort         int       `json:"sort" xorm:"not null default 1 comment('排序') TINYINT(1) 'sort'"`
	PrivilegeIds string    `json:"privilege_ids" xorm:"default 'null' comment('权益值') TEXT 'privilege_ids'"`
	Refundable   int32     `json:"refundable" xorm:"default '0' comment('是否不可退：0-否；1-是') TINYINT(4) 'refundable'"`
	CreateTime   time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime   time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}

type VipCardEquityConfigExtend struct {
	VipCardEquityConfig `xorm:"extends"`
	EquityShortName     string `json:"equity_short_name" xorm:"default '' comment('权益短名称') VARCHAR(50) 'equity_short_name'"`
	EquityType          int32  `json:"equity_type" xorm:"default 0 comment('权益类型') TINYINT(1) 'equity_type'"`
}

type EquityConfig struct {
	CardTid    int32     `json:"card_tid" xorm:"pk not null default 0 comment('卡付费周期id') INT(11) 'card_tid'"`
	OrId       int64     `json:"or_id" xorm:"not null default 0 comment('所属大区') TINYINT(2) 'or_id'"`
	CardName   string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	EquityName string    `json:"equity_name" xorm:"default '' comment('权益名称') VARCHAR(50) 'equity_name'"`
	OpterNo    string    `json:"opter_no" xorm:"default '' comment('员工编号') VARCHAR(20) 'opter_no'"`
	Opter      string    `json:"opter" xorm:"default '' comment('操作人姓名') VARCHAR(20) 'opter'"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	CardCycle  int32     `json:"card_cycle" xorm:"default 0 comment('付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡') TINYINT(1) 'card_cycle'"`
}

type NoRefundableEquityConfig struct {
	CardTid    int32  `json:"card_tid" xorm:"pk not null default 0 comment('卡付费周期id') INT(11) 'card_tid'"`
	CardName   string `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	CardCycle  int32  `json:"card_cycle" xorm:"default 0 comment('付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡') TINYINT(1) 'card_cycle'"`
	EquityId   int32  `json:"equity_id" xorm:"pk not null default 0 comment('权益id') TINYINT(1) 'equity_id'"`
	EquityName string `json:"equity_name" xorm:"default '' comment('权益名称') VARCHAR(50) 'equity_name'"`
	EquityRule string `json:"equity_rule" xorm:"default 'null' comment('权益规则') TEXT 'equity_rule'"`
	EquityType int32  `json:"equity_type" xorm:"default 0 comment('权益类型') TINYINT(1) 'equity_type'"`
}
