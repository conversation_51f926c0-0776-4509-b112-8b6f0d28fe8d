package models

import (
	"_/proto/cc"
)

//用户信息表
type ScrmUserInfo struct {
	ScrmUserId string `json:"scrm_user_id" xorm:"user_id"`
	UserName   string `json:"user_name"`
	UserAvatar string `json:"user_avatar"`
	UserMobile string `json:"user_mobile"`
}

type TScrmUserInfo struct {
	Id                int    `xorm:"not null pk autoincr INT(10)"`
	UserId            string `xorm:"not null comment('用户ID作为唯一标示') unique VARCHAR(32)"`
	UserImId          string `xorm:"not null comment('用户IM唯一标示') VARCHAR(32)"`
	UserImToken       string `xorm:"not null comment('im用户token密码') VARCHAR(128)"`
	UserName          string `xorm:"not null default '''' comment('用户姓名') VARCHAR(32)"`
	UserSex           int    `xorm:"not null default 0 comment('用户性别 0未知 1男 2女') TINYINT(3)"`
	UserMobile        string `xorm:"not null default '''' comment('用户手机号') index VARCHAR(16)"`
	UserSource        int    `xorm:"not null default 0 comment('用户来源 0阿闻小程序') TINYINT(3)"`
	UserStatus        int    `xorm:"not null default 0 comment('用户状态 0正常 1锁定 -1删除') TINYINT(3)"`
	UserAvatar        string `xorm:"not null default '''' comment('用户头像') VARCHAR(256)"`
	UserBirthday      string `xorm:"<-"`
	FirstRaisesPet    string `xorm:"<-"`
	Country           string `xorm:"not null default '''' comment('用户所在国家') VARCHAR(32)"`
	Province          string `xorm:"not null default '''' comment('用户所在省份') VARCHAR(32)"`
	City              string `xorm:"not null default '''' comment('用户所在城市') VARCHAR(32)"`
	Area              string `xorm:"not null default '''' comment('用户所在区域') VARCHAR(32)"`
	UserRemark        string `xorm:"not null default '''' comment('用户备注') VARCHAR(128)"`
	CreateTime        string `xorm:"<-"`
	UpdateTime        string `xorm:"<-"`
	BigdataUserId     string `xorm:"default 'NULL' comment('大数据唯一Id') index VARCHAR(32)"`
	RemoteTreatStatus int    `xorm:"not null default 0 comment('远程诊疗  0:否 1:是') TINYINT(2)"`
	LevelId           string `xorm:"not null default '''' comment('会员等级') VARCHAR(32)"`
	IsReal            int    `xorm:"default 0 comment('是否实名认证 0否1是') TINYINT(2)"`
}

func (t *TScrmUserInfo) ToUserInfoResponse() *cc.UserInfoResponse {
	var dto = new(cc.UserInfoResponse)
	dto.Id = int32(t.Id)
	dto.UserId = t.UserId
	dto.UserImId = t.UserImId
	dto.UserImToken = t.UserImToken
	dto.UserName = t.UserName
	dto.UserSex = int32(t.UserSex)
	dto.UserMobile = t.UserMobile
	dto.UserSource = int32(t.UserSource)
	dto.UserStatus = int32(t.UserStatus)
	dto.UserAvatar = t.UserAvatar
	dto.UserBirthday = t.UserBirthday
	dto.FirstRaisesPet = t.FirstRaisesPet
	dto.City = t.City
	dto.Country = t.Country
	dto.Province = t.Province
	dto.Area = t.Area
	dto.UserRemark = t.UserRemark
	dto.CreateTime = t.CreateTime
	dto.UpdateTime = t.UpdateTime
	dto.BigdataUserId = t.BigdataUserId
	dto.IsReal = int32(t.IsReal)
	return dto
}

