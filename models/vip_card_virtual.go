package models

import "time"

// 虚拟卡表
type VipCardVirtual struct {
	CardId     int64     `json:"card_id" xorm:"pk autoincr not null comment('卡号') BIGINT(20) 'card_id'"`
	CardPass   string    `json:"card_pass" xorm:"not null comment('卡密') VARCHAR(50) 'card_pass'"`
	BatchId    string    `json:"batch_id" xorm:"not null comment('批次号') VARCHAR(50) 'batch_id'"`
	OrgId      int32     `json:"org_id" xorm:"not null comment('组织ID') INT(11) 'org_id'"`
	OrgName    string    `json:"org_name" xorm:"default 'null' comment('组织名称') VARCHAR(50) 'org_name'"`
	TemplateId int32     `json:"template_id" xorm:"default 'null' comment('卡模板ID') INT(11) 'template_id'"`
	Status     int32     `json:"status" xorm:"not null default 0 comment('卡状态,0-已卖出，1-已激活，2-已注销') INT(11) 'status'"`
	UserId     string    `json:"user_id" xorm:"default '' comment('兑换用户id') VARCHAR(32) 'user_id'"`
	UserMobile string    `json:"user_mobile" xorm:"default '' comment('兑换用户手机号') VARCHAR(20) 'user_mobile'"`
	UseTime    time.Time `json:"use_time" xorm:"default 'null' comment('兑换时间') DATETIME 'use_time'"`
	ExpireTime time.Time `json:"expire_time" xorm:"default 'null' comment('到期时间') DATETIME 'expire_time'"`
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	SellType   int32     `json:"sell_type" xorm:"default 'null' comment('1内销 2外采') INT(11) 'sell_type'"`
}
