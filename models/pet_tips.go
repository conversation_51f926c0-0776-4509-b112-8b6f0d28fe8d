package models

import (
	"_/proto/cc"
	"_/utils"
	"time"
)

// 贴士表
type PetTips struct {
	Id             int    `xorm:"pk INT autoincr"`
	Title          string `xorm:"notnull varchar(30)"`
	Icon           string `xorm:"notnull varchar(256)"`
	Reading        int    `xorm:"notnull int"`
	IsDeleted      int8
	CreateAt       time.Time `xorm:"created"`
	CreateBy       string
	UpdateAt       time.Time `xorm:"updated"`
	UpdateBy       string
	VirtualReading int `xorm:"notnull int"`
}

// 转化dot
func (model *PetTips) ToPetTipsDto() *cc.PetTipsDto {
	var dto = new(cc.PetTipsDto)
	dto.Id = int32(model.Id)
	dto.Title = model.Title
	dto.Icon = model.Icon
	dto.Reading = int32(model.Reading)
	dto.CreateAt = model.CreateAt.Format(utils.DATE_TIME_LAYOUT)
	dto.CreateBy = model.CreateBy
	if model.UpdateAt.Year() > 2019 {
		dto.UpdateAt = model.UpdateAt.Format(utils.DATE_TIME_LAYOUT)
		dto.UpdateBy = model.UpdateBy
	}
	dto.VirtualReading = int32(model.VirtualReading)
	return dto
}
