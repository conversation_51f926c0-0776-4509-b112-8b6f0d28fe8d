package models

import "time"

//月度发券表
type VipUserEquityMonth struct {
	Id              int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
	OrderSn         string    `json:"order_sn" xorm:"not null default '' comment('订单号') VARCHAR(50) 'order_sn'"`
	CreateTime      time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('订单的创建时间') DATETIME 'create_time'"`
	EquityId        int8      `json:"equity_id" xorm:"default 'null' comment('权益id') TINYINT 'equity_id'"`
	EquityCount     int       `json:"equity_count" xorm:"default 'null' comment('领取权益次数') INT 'equity_count'"`
	BeginTime       time.Time `json:"begin_time" xorm:"default 'CURRENT_TIMESTAMP' comment('下次发券时间') DATETIME 'begin_time'"`
	EquityShortName string    `json:"equity_short_name" xorm:"default '' comment('领券开始时间') VARCHAR(50) 'equity_short_name'"`
	EndTime         time.Time `json:"end_time" xorm:"default 'CURRENT_TIMESTAMP' comment('领券结束时间') DATETIME 'end_time'"`
	Status          int       `json:"status" xorm:"default 0 comment('0未领取，1已领取') INT 'status'"`
}
