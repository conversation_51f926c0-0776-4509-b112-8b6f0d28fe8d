package models

import "time"

type UserLevel struct {
	Id                   int64     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	LevelId              int64     `json:"level_id" xorm:"not null default 0 comment('等级ID') TINYINT(4) 'level_id'"`
	LevelName            string    `json:"level_name" xorm:"not null default '' comment('等级名称') VARCHAR(20) 'level_name'"`
	LevelIcon            string    `json:"level_icon" xorm:"not null default '' comment('等级图标') VARCHAR(200) 'level_icon'"`
	Background           string    `json:"background" xorm:"not null default '' comment('等级背景') VARCHAR(500) 'background'"`
	CenterBackground     string    `json:"center_background" xorm:"not null default '' comment('个人中心背景') VARCHAR(500) 'center_background'"`
	HealthVal            int64     `json:"health_val" xorm:"not null default 0 comment('健康值') INT(1) 'health_val'"`
	LevelStatus          int64     `json:"level_status" xorm:"not null default 0 comment('状态 0停用 1启用') TINYINT(4) 'level_status'"`
	PrivilegeIds         string    `json:"privilege_ids" xorm:"not null comment('权益id，多个,分割') VARCHAR(255) 'privilege_ids'"`
	GoodsUpgradeVouchers string    `json:"goods_upgrade_vouchers" xorm:"not null default '' comment('商城升级券，多个,分割') VARCHAR(255) 'goods_upgrade_vouchers'"`
	StoreUpgradeVouchers string    `json:"store_upgrade_vouchers" xorm:"not null default '' comment('到店礼券升级券，子龙那边的券，多个,分割') VARCHAR(255) 'store_upgrade_vouchers'"`
	StoreWeekVouchers    string    `json:"store_week_vouchers" xorm:"not null default '' comment('到店礼券周特权券，子龙那边的券，多个,分割') VARCHAR(255) 'store_week_vouchers'"`
	MemberPrice          string    `json:"member_price" xorm:"not null comment('会员价，折扣') DECIMAL(2) 'member_price'"`
	CreateTime           time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime           time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}
