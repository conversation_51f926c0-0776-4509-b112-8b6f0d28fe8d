package models

type ResData struct {
	Result []struct {
		ID               int    `json:"id"`
		PetDictID        string `json:"petDictId"`
		PetDictParentID  string `json:"petDictParentId"`
		PetDictName      string `json:"petDictName"`
		PetDictShortName string `json:"petDictShortName"`
		PetDictStatus    int    `json:"petDictStatus"`
		CreateTime       string `json:"createTime"`
		FirstLetter      string `json:"firstLetter"`
		UpdateTime       string `json:"updateTime"`
	} `json:"result"`
	Message       interface{} `json:"message"`
	SystemError   interface{} `json:"systemError"`
	BusinessError interface{} `json:"businessError"`
	StatusCode    int         `json:"statusCode"`
	Extensions    struct {
	} `json:"extensions"`
	Success bool `json:"success"`
}
