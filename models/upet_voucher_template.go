package models

type UpetVoucherTemplate struct {
	VoucherTId    int64  `json:"voucher_t_id" xorm:"pk autoincr not null comment('代金券模版编号') INT(11) 'voucher_t_id'"`
	VoucherTTitle string `json:"voucher_t_title" xorm:"not null comment('代金券模版名称') VARCHAR(50) 'voucher_t_title'"`
	// VoucherTDesc        string `json:"voucher_t_desc" xorm:"not null comment('代金券模版描述') VARCHAR(255) 'voucher_t_desc'"`
	VoucherTStartDate int64  `json:"voucher_t_start_date" xorm:"not null comment('代金券模版有效期开始时间') INT(11) 'voucher_t_start_date'"`
	VoucherTEndDate   int64  `json:"voucher_t_end_date" xorm:"not null comment('代金券模版有效期结束时间') INT(11) 'voucher_t_end_date'"`
	VoucherTPrice     int64  `json:"voucher_t_price" xorm:"not null comment('代金券模版面额') INT(11) 'voucher_t_price'"`
	VoucherTLimit     string `json:"voucher_t_limit" xorm:"not null comment('代金券使用时的订单限额') DECIMAL(10) 'voucher_t_limit'"`
	// VoucherTStoreId     int64  `json:"voucher_t_store_id" xorm:"not null comment('代金券模版的店铺id') INT(11) 'voucher_t_store_id'"`
	// VoucherTStorename   string `json:"voucher_t_storename" xorm:"default 'null' comment('店铺名称') VARCHAR(100) 'voucher_t_storename'"`
	// VoucherTScId        int64  `json:"voucher_t_sc_id" xorm:"not null default 0 comment('所属店铺分类ID') INT(11) 'voucher_t_sc_id'"`
	// VoucherTCreatorId   int64  `json:"voucher_t_creator_id" xorm:"not null comment('代金券模版的创建者id') INT(11) 'voucher_t_creator_id'"`
	VoucherTState   int64 `json:"voucher_t_state" xorm:"not null comment('代金券模版状态(1-有效,2-失效)') TINYINT(4) 'voucher_t_state'"`
	VoucherTTotal   int64 `json:"voucher_t_total" xorm:"not null comment('模版可发放的代金券总数') INT(11) 'voucher_t_total'"`
	VoucherTGiveout int64 `json:"voucher_t_giveout" xorm:"not null comment('模版已发放的代金券数量') INT(11) 'voucher_t_giveout'"`
	// VoucherTUsed        int64  `json:"voucher_t_used" xorm:"not null comment('模版已经使用过的代金券') INT(11) 'voucher_t_used'"`
	VoucherTAddDate int64 `json:"voucher_t_add_date" xorm:"not null comment('模版的创建时间') INT(11) 'voucher_t_add_date'"`
	// VoucherTQuotaid     int64  `json:"voucher_t_quotaid" xorm:"not null comment('套餐编号') INT(11) 'voucher_t_quotaid'"`
	// VoucherTPoints      int64  `json:"voucher_t_points" xorm:"not null default 0 comment('兑换所需积分') INT(11) 'voucher_t_points'"`
	VoucherTEachlimit int64 `json:"voucher_t_eachlimit" xorm:"not null default 1 comment('每人限领张数') INT(11) 'voucher_t_eachlimit'"`
	// VoucherTStyleimg    string `json:"voucher_t_styleimg" xorm:"default 'null' comment('样式模版图片') VARCHAR(200) 'voucher_t_styleimg'"`
	// VoucherTCustomimg   string `json:"voucher_t_customimg" xorm:"default 'null' comment('自定义代金券模板图片') VARCHAR(200) 'voucher_t_customimg'"`
	// VoucherTRecommend   int64  `json:"voucher_t_recommend" xorm:"not null default 0 comment('是否推荐 0不推荐 1推荐') TINYINT(1) 'voucher_t_recommend'"`
	// VoucherTGettype     int64  `json:"voucher_t_gettype" xorm:"not null default 1 comment('领取方式 1积分兑换 2卡密兑换 3免费领取 4自动派发 5支付有礼') TINYINT(1) 'voucher_t_gettype'"`
	// VoucherTIsbuild     int64  `json:"voucher_t_isbuild" xorm:"not null default 0 comment('领取方式为卡密兑换是否已经生成下属代金券 0未生成 1已生成') TINYINT(1) 'voucher_t_isbuild'"`
	// VoucherTMgradelimit int64  `json:"voucher_t_mgradelimit" xorm:"not null default 0 comment('领取代金券限制的会员等级') TINYINT(4) 'voucher_t_mgradelimit'"`
	// VoucherTType        int64  `json:"voucher_t_type" xorm:"default 0 comment('使用范围1,无限制2，限单品(SKU)3,限全品(SPU) 4,限品类5限品牌6活动页面商品7，新用户专享8，老用户专享') TINYINT(4) 'voucher_t_type'"`
	// VoucherTGcId        string `json:"voucher_t_gc_id" xorm:"default 0 comment('分类id') VARCHAR(100) 'voucher_t_gc_id'"`
	// VoucherSpecialId    int64  `json:"voucher_special_id" xorm:"default 0 comment('活动板块id') INT(11) 'voucher_special_id'"`
	// VoucherBrandId      int64  `json:"voucher_brand_id" xorm:"default 0 comment('品牌id') INT(11) 'voucher_brand_id'"`
	// VoucherVip          string `json:"voucher_vip" xorm:"default '' comment('是否会员卡使用') VARCHAR(20) 'voucher_vip'"`
	// VoucherStartDay     int64  `json:"voucher_start_day" xorm:"default 0 comment('开始日') INT(11) 'voucher_start_day'"`
	VoucherDays int64 `json:"voucher_days" xorm:"default 0 comment('多少天内可用') INT(11) 'voucher_days'"`
	// VoucherWxCard       int64  `json:"voucher_wx_card" xorm:"default 2 comment('是否同步到微信卡包，2不同步，1同步') TINYINT(4) 'voucher_wx_card'"`
	// VoucherShowState    int64  `json:"voucher_show_state" xorm:"default 1 comment('是否在固定领劵页展示，2不展示，1展示') TINYINT(4) 'voucher_show_state'"`
	// WxCardId            string `json:"wx_card_id" xorm:"default 'null' comment('微信卡券id') VARCHAR(255) 'wx_card_id'"`
	// WxStockId           string `json:"wx_stock_id" xorm:"default 'null' comment('微信商家券批次id') VARCHAR(255) 'wx_stock_id'"`
	// VoucherImage        string `json:"voucher_image" xorm:"default 'null' comment('支付有礼时，券详情图片') VARCHAR(255) 'voucher_image'"`
	// MerchantLogoUrl     string `json:"merchant_logo_url" xorm:"default 'null' comment('商户logo') VARCHAR(255) 'merchant_logo_url'"`
	// CouponImageUrl      string `json:"coupon_image_url" xorm:"default 'null' comment('券详情') VARCHAR(255) 'coupon_image_url'"`
}
