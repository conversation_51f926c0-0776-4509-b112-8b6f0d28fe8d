package models

type SearchHistory struct {
	Id        int64  `xorm:"pk autoincr BIGINT(20)"`
	UserId    string `xorm:"not null default '' comment('用户id') VARCHAR(100)"`
	KeyWord   string `xorm:"not null default '' comment('关键字') VARCHAR(255)"`
	SearchNum int    `xorm:"<-"`
	// UpdateTime time.Time `xorm:"not null comment('更新时间') DATETIME"`
	// CreateTime time.Time `xorm:"not null comment('创建时间') DATETIME"`
}
