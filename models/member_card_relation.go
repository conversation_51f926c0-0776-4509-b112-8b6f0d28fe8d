package models

import (
	"_/proto/cc"
	kit "github.com/tricobbler/rp-kit"
	"time"
)

type MemberCardRelation struct {
	Id          int64     `xorm:"pk INT autoincr NULL BIGINT(20)"`
	//Id          int64     `xorm:" default NULL BIGINT(11)"`
	Userid      string    `xorm:"default '''' comment('会员id(实时查询用户基础信息)') VARCHAR(50)"`
	Createsource int    `xorm:"default 0 comment('创建来源') INT(4)"`
	Financecode         string       `xorm:"default '''' comment('财务编码-查询门店信息') VARCHAR(32)"`
	Batchcode      string       `xorm:"default '''' comment('子龙批次號') VARCHAR(32)"`
	Orderid       string       `xorm:"default '''' comment('结算单号(erp的单号)') VARCHAR(32)"`
	Mallorderid       string       `xorm:"default '''' comment('mall商城订单号') VARCHAR(32)"`
	Vipcode   string       `xorm:"default '''' comment('会员卡id') VARCHAR(32)"`
	Cardtype int    `xorm:"default 0 comment('卡类型(1-保障卡，2-会员卡)') INT(4)"`
	Cardlevel int    `xorm:"default 0 comment('卡等级 保障卡（1001-198 1002-298 1003-398  1004-498） 会员卡（ 2001-198 2002-298 2003-398 2004-498）') INT(4)"`
	Vipstatus int    `xorm:"default 0 comment('会员卡状态(0-开卡中，1-正常，2-退款中，3-已退款，4-已过期)') INT(4)"`
	Expirystartdate  time.Time `xorm:"default '''' comment('卡有效期开始时间') DATETIME"`
	Expiryenddate  time.Time `xorm:"default '''' comment('卡有效期结束时间') DATETIME"`
	Presellcode       string       `xorm:"default '''' comment('预售单号') VARCHAR(32)"`
	Presellname       string       `xorm:"default '''' comment('预售名称') VARCHAR(300)"`
	Presellcount int    `xorm:"default 0 comment('预售数量)') INT(4)"`
	Presellunit       string       `xorm:"default '''' comment('预售单位') VARCHAR(8)"`
	Createdate  time.Time `xorm:"default '''' comment('创建时间') DATETIME"`
	Lastdate  time.Time `xorm:"default '''' comment('最后修改时间') DATETIME"`
}

var(

	//卡种类型
	VipCardType = map[int]string{
		1: "会员卡",
		2:   "保障卡",
	}
	//卡状态
	VipCardStatus = map[int]string{
		0: "开卡中",
		1: "正常",
		2: "退款中",
		3:"已退款",
		4:"已过期",
	}
)

func (m *MemberCardRelation) ToUserVipInfo() *cc.UserVipInfo {
	var dto = new(cc.UserVipInfo)
	dto.Code = m.Vipcode
	dto.Type = VipCardType[m.Cardtype]
	dto.State = VipCardStatus[m.Vipstatus]
	dto.EffectiveDate = kit.GetTimeNow(m.Expirystartdate)
	dto.ExpirationDate = kit.GetTimeNow(m.Expiryenddate)
	return dto
}

