package models

import "time"

type UserTask struct {
	Id         int32     `json:"id"`
	TaskId     int8      `json:"task_id"` // upet_user_task的id
	UserId     string    `json:"user_id"` // 用户id
	Status     int8      `json:"status"`  // 任务状态 0待完成 1待领取 2已领取
	CreateTime time.Time `xorm:"<-" json:"create_time"`
	UpdateTime time.Time `xorm:"<-" json:"update_time"`
}

type UserTaskAndManage struct {
	Id       int32  `json:"id"`
	TaskName string `xorm:"default NULL comment('任务名称') VARCHAR(20)"`
	TaskVal  int32  `xorm:"default 0 comment('任务值') mediumint(1)"`
	Status   int8   `json:"status"` // 任务状态 0待完成 1待领取 2已领取
	Icon     string `json:"icon"`
}
