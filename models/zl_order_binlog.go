package models

import "time"

type PosPaymentMaster struct {
	//id
	Id int `json:"id"`
	//机构Id
	OrgId int `json:"org_id"`
	//支付流水号
	OrderNumber string `json:"order_number"`
	//会员Id
	CustomerId int `json:"customer_id"`
	//总金额
	TotalAmount float64 `json:"total_amount"`
	//状态（0:未支付 5：待发货10:已支付 11:记账支付成功 13:退款中14：冻结 15:部分退款 20:全部退款）
	OrderStatus int `json:"order_status"`
	//支付类型（1:正常 2:记账 3:预售 4:订单中心 5:预约）
	OrderType int `json:"order_type"`
	//实付金额
	ActulyPayed float64 `json:"actuly_payed"`
	//优惠金额
	DiscountAmount float64 `json:"discount_amount"`
	//赠送支付金额
	PresentPayedAmount float64 `json:"present_payed_amount"`
	//创建时间
	CreatedDate time.Time `json:"created_date"`
	//店铺财务编码
	ShopId string `json:"shop_id"`
	//店铺名称
	ShopName string `json:"shop_name"`
	//支付方式
	PaymentType int `json:"payment_type"`
}

type PosRmaMaster struct {
	Id int `json:"id"`
	OrgId int `json:"org_id"`
	OrderNumber string `json:"order_number"`
	RmaNumber string `json:"rma_number"`
	CustomerId int `json:"customer_id"`
	RmaAmount float64 `json:"rma_amount"`
	RmaStatus int `json:"rma_status"`
	RmaTotalAmount float64 `json:"rma_total_amount"`
	RmaType int `json:"rma_type"`
	PresentRmaAmount float64 `json:"present_rma_amount"`
	//店铺财务编码
	ShopId string `json:"shop_id"`
	//店铺名称
	ShopName string `json:"shop_name"`
}

