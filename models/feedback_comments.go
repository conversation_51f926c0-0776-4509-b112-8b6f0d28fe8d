package models

import (
	"time"
)

type FeedbackComments struct {
	Id         int32     `xorm:"not null pk autoincr INT(11)"`
	FeedbackId int32     `xorm:"not null comment('所属反馈的ID') index INT(11)"`
	IsOfficial int32     `xorm:"not null default 0 comment('是否是官方回复 1是 0否') TINYINT(1)"`
	UserName   string    `xorm:"not null default '''' comment('用户昵称') VARCHAR(20)"`
	UserId     string    `xorm:"not null comment('用户id') INT(11)"`
	Content    string    `xorm:"default 'NULL' comment('评论内容') VARCHAR(200)"`
	CreateTime time.Time `xorm:"created not null comment('创建时间') DATETIME"`
}
