package models

import "time"

const (
	// 会员升降类型，1升，2降
	MemberLevelLiftTypeUp = int64(1) + iota
	MemberLevelLiftTypeDown
)

type UpetMemberLevelLog struct {
	Id             int64     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	ScrmUserId     string    `json:"scrm_user_id" xorm:"not null default '' VARCHAR(32) 'scrm_user_id'"`
	LiftType       int64     `json:"lift_type" xorm:"not null default 0 comment('会员升降类型，1升，2降') TINYINT(4) 'lift_type'"`
	OldUserLevelId int64     `json:"old_user_level_id" xorm:"not null default 0 comment('旧会员等级') TINYINT(1) 'old_user_level_id'"`
	NewUserLevelId int64     `json:"new_user_level_id" xorm:"not null default 0 comment('新会员等级') TINYINT(1) 'new_user_level_id'"`
	Content        string    `json:"content" xorm:"not null default '' comment('用户等级变更说明') VARCHAR(255) 'content'"`
	CreateTime     time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime     time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}
