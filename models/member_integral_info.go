package models

import "time"

type MemberIntegralInfo struct {
	MemberId        string    `xorm:"not null pk comment('用户ID') CHAR(50) 'memberid'"`
	Integral        int32     `xorm:"not null comment('积分') int(10)"`
	FreezeHealthVal int32     `json:"freeze_health_val" xorm:"default 0 comment('冻结健康值') INT(10) 'freeze_health_val'"`
	HealthVal       int32     `json:"health_val" xorm:"default 0 comment('会员健康值') INT(10) 'health_val'"`
	Lasttime        time.Time `json:"lasttime" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'lasttime'"` // 最后操作时间
}
