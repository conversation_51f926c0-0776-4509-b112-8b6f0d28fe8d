package models

import "time"

type UserCoupon struct {
	Id                int32     `json:"id"`
	CouponType        int8      `json:"coupon_type"`          // 券类型：1商品券 2门店券
	CouponId          int32     `json:"coupon_id"`            // 优惠券id
	UserId            string    `json:"user_id"`              // 用户id
	VoucherId         string    `json:"voucher_id"`           //商城券id
	CouponCode        string    `json:"coupon_code"`          //门店券code
	Type              int8      `json:"type"`                 //1=升级券, 2=周特权券'
	LevelId           int8      `json:"user_level_id"`        // 用户领券时的等级
	VoucherTStartDate int32     `json:"voucher_t_start_date"` // 代金券模版有效期开始时间
	VoucherTEndDate   int32     `json:"voucher_t_end_date"`   // 代金券模版有效期结束时间
	CreateTime        time.Time `xorm:"<-" json:"create_time"`
	UpdateTime        time.Time `xorm:"<-" json:"update_time"`
}
