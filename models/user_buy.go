package models

import (
	"time"
)

type UserBuy struct {
	Id          int64     `xorm:"pk INT autoincr NULL BIGINT(11)"`
	//Id          int64     `xorm:" default NULL BIGINT(11)"`
	UserId      string    `xorm:"default 'NULL' comment('用户id') VARCHAR(100)"`
	ProductName string    `xorm:"default 'NULL' comment('产品名称') VARCHAR(255)"`
	ProductId   int       `xorm:"default NULL comment('产品id') INT(11)"`
	Sku         int       `xorm:"default 'NULL' comment('sku') VARCHAR(255)"`
	Number      int       `xorm:"default NULL comment('购买数量') INT(11)"`
	Price       int       `xorm:"default NULL comment('商品单价') INT(11)"`
	Image       string    `xorm:"default 'NULL' comment('商品图片') VARCHAR(500)"`
	ChannelId   int       `xorm:"default NULL comment('平台id') INT(11)"`
	CreateTime  time.Time `xorm:"default 'NULL' comment('最后修改时间') DATETIME"`
}


type UserOrderDto struct {
	UserId      string `xorm:"default 'NULL' comment('用户id') VARCHAR(100)"`
	ProductName string `xorm:"default 'NULL' comment('产品名称') VARCHAR(255)"`
	ProductId   int
	Sku         string
	Number      int    `xorm:"default NULL comment('购买数量') INT(11)"`
	Price       int    `xorm:"default NULL comment('商品单价') INT(11)"`
	Image       string `xorm:"default 'NULL' comment('商品图片') VARCHAR(500)"`
	ChannelId   int    `xorm:"default NULL comment('平台id') INT(11)"`
	MemberTel   string `json:"member_tel"`
}
