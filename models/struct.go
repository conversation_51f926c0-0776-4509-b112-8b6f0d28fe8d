package models

import "time"

type MqZlOrderBinLogTask struct {
	// 1 支付 2 退款
	Type int32 `json:"type"`
	Id   int   `json:"id"`
}

type PetRecordDate struct {
	PetId         string `json:"pet_id"`
	RecordType    int64  `json:"record_type"`
	OperationDate string `json:"operation_date"`
	RecordPhoto   string `json:"record_photo"`
	ProductName   string `json:"product_name"`
	CreateTime    string `json:"create_time"`
}

type ShowPetNeuteringData struct {
	PetBirthday  string `json:"pet_birthday"`
	PetNeutering int    `json:"pet_neutering"`
	PetKindof    int32  `json:"pet_kindof"`
}

type TScrmPetPhoto struct {
	Id          int       `xorm:"not null pk autoincr INT(10)"`
	FaceId      string    `xorm:"not null comment('宠物faceId') index VARCHAR(60)"`
	PetPhoto    string    `xorm:"not null comment('宠物图片') VARCHAR(200)"`
	CreateTime  time.Time `xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME"`
	PhotoType   int       `xorm:"comment('1：正脸，2：全身，3：合影') TINYINT(3)"`
	PhotoStatus int       `xorm:"not null default 0 comment('0正常录入 1验证图片') TINYINT(3)"`
}
