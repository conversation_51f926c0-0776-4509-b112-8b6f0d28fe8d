package models

import (
	"time"
)

type UpdateLog struct {
	Id         int64     `xorm:"pk autoincr BIGINT(20)"`
	UserId     string    `xorm:"default 'NULL' comment('用户ID') VARCHAR(32)"`
	UpdateType int       `xorm:"default NULL comment('1用户信息 2宠物信息') TINYINT(1)"`
	Nickname   string    `xorm:"default 'NULL' comment('昵称') VARCHAR(255)"`
	Avatar     string    `xorm:"default 'NULL' comment('头像') VARCHAR(255)"`
	CreateTime time.Time `xorm:"default 'NULL' comment('修改时间') DATETIME"`
}
