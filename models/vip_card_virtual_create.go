package models

import "time"

//虚拟会员卡生成任务表
type VipCardVirtualCreate struct {
	Id         int       `json:"id" xorm:"pk autoincr not null comment('主键') INT(11) 'id'"`
	BatchId    string    `json:"batch_id" xorm:"default 'null' comment('批次号') VARCHAR(50) 'batch_id'"`
	OrgId      int       `json:"org_id" xorm:"default 'null' comment('组织ID') INT(11) 'org_id'"`
	OrgName    string    `json:"org_name" xorm:"default 'null' comment('组织名称') VARCHAR(50) 'org_name'"`
	Status     int       `json:"status" xorm:"default 'null' comment('状态，0未执行，1执行中，2执行失败，3执行成功') INT(11) 'status'"`
	TemplateId int       `json:"template_id" xorm:"default 'null' comment('卡模板ID') INT(11) 'template_id'"`
	CardCount  int       `json:"card_count" xorm:"default 'null' comment('生成卡券数量') INT(11) 'card_count'"`
	CardPrice  int       `json:"card_price" xorm:"default 'null' comment('单张金额') INT(11) 'card_price'"`
	UserId     string    `json:"user_id" xorm:"default 'null' comment('创建人ID') VARCHAR(100) 'user_id'"`
	UserName   string    `json:"user_name" xorm:"default 'null' comment('创建人名称') VARCHAR(100) 'user_name'"`
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	SellType   int       `json:"sell_type" xorm:"default 'null' comment('1内销 2外采') INT(11) 'sell_type'"`
	FileUrl    string    `json:"file_url" xorm:"default 'null' comment('导出的URL') INT(11) 'file_url'"`
}
