package models

type UpetVoucher struct {
	VoucherId         int32  `json:"voucher_id,omitempty"`          // 代金券编号
	VoucherCode       string `json:"voucher_code,omitempty"`        // 代金券编码
	VoucherTId        int32  `json:"voucher_t_id,omitempty"`        // 代金券模版编号
	VoucherTitle      string `json:"voucher_title,omitempty"`       // 代金券标题
	VoucherDesc       string `json:"voucher_desc,omitempty"`        // 代金券描述
	VoucherStartDate  int32  `json:"voucher_start_date,omitempty"`  // 代金券有效期开始时间
	VoucherEndDate    int32  `json:"voucher_end_date,omitempty"`    // 代金券有效期结束时间
	VoucherPrice      int32  `json:"voucher_price,omitempty"`       // 代金券面额
	VoucherLimit      string `json:"voucher_limit,omitempty"`       // 代金券使用时的订单限额
	VoucherStoreId    int32  `json:"voucher_store_id,omitempty"`    // 代金券的店铺id
	VoucherState      int8   `json:"voucher_state,omitempty"`       // 代金券状态(1-未用,2-已用,3-过期,4-收回)
	VoucherActiveDate int32  `json:"voucher_active_date,omitempty"` // 代金券发放日期
	VoucherType       int8   `json:"voucher_type,omitempty"`        // 代金券类别
	VoucherOwnerId    int32  `json:"voucher_owner_id,omitempty"`    // 代金券所有者id
	VoucherOwnerName  string `json:"voucher_owner_name,omitempty"`  // 代金券所有者名称
	VoucherOrderId    int32  `json:"voucher_order_id,omitempty"`    // 使用该代金券的订单编号
	VoucherPwd        string `json:"voucher_pwd,omitempty"`         // 代金券卡密不可逆
	VoucherPwd2       string `json:"voucher_pwd2,omitempty"`        // 代金券卡密2可逆
	VoucherTType      int8   `json:"voucher_t_type,omitempty"`      // 适用范围
	VoucherTGcId      string `json:"voucher_t_gc_id,omitempty"`     // 分类ID
	VoucherSpecialId  int32  `json:"voucher_special_id,omitempty"`  // 对应活动页面id
	VoucherBrandId    int32  `json:"voucher_brand_id,omitempty"`    // 品牌id
	VoucherVip        string `json:"voucher_vip,omitempty"`         // 是否会员卡使用
	VoucherFreeze     int8   `json:"voucher_freeze,omitempty"`      // 是否冻结 0否 1是
	VoucherFrom       int8   `json:"voucher_from,omitempty"`        // 派券来源 0默认 1子龙办卡赠送,3新用户，4老用户，5支付成功赠送6 答题活动，7双旦活动抽奖，8宠物集市活动
	WxCardCode        string `json:"wx_card_code,omitempty"`        // 微信卡券code
	WxCouponCode      string `json:"wx_coupon_code,omitempty"`      // 微信商家券code
	FromKey           string `json:"from_key,omitempty"`            // 微页面领取key
}
