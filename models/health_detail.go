package models

import "time"

type HealthDetail struct {
	Id              int64     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	UserId          string    `json:"user_id" xorm:"not null default '' comment('用户scrm_user_id') VARCHAR(50) 'user_id'"`
	Type            int64     `json:"type" xorm:"not null default 0 comment('默认0,1=收入，2=支出 3=冻结') TINYINT(1) 'type'"`
	Title           string    `json:"title" xorm:"not null default '' comment('标题') VARCHAR(32) 'title'"`
	Content         string    `json:"content" xorm:"not null default '' comment('明细说明') VARCHAR(255) 'content'"`
	PayAmount       string    `json:"pay_amount" xorm:"default '0.00' comment('支付金额') DECIMAL(10) 'pay_amount'"`
	RefundAmount    string    `json:"refund_amount" xorm:"default '0.00' comment('退款金额') DECIMAL(10) 'refund_amount'"`
	OrderSn         string    `json:"order_sn" xorm:"default 'null' VARCHAR(32) 'order_sn'"`
	ShopId          string    `json:"shop_id" xorm:"default 'null' VARCHAR(32) 'shop_id'"`
	ShopName        string    `json:"shop_name" xorm:"default 'null' VARCHAR(50) 'shop_name'"`
	ActiveVal       int64     `json:"active_val" xorm:"not null default 0 comment('活跃值') INT(11) 'active_val'"`
	HealthVal       int64     `json:"health_val" xorm:"not null default 0 comment('健康值') INT(11) 'health_val'"`
	PrevHealthCount int64     `json:"prev_health_count" xorm:"not null default 0 comment('上次记录的健康值总数') INT(11) 'prev_health_count'"`
	CurrHealthCount int64     `json:"curr_health_count" xorm:"not null default 0 comment('本次记录的健康值总数') INT(11) 'curr_health_count'"`
	HealthType      int64     `json:"health_type" xorm:"not null default 0 comment('健康值类型 1线上消费 2线下消费 3做升级任务 4会员等级失效') INT(11) 'health_type'"`
	EffectTime      time.Time `json:"effect_time" xorm:"default 'null' comment('生效时间') DATETIME 'effect_time'"`
	CreateTime      time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime      time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
	PayTime         time.Time `json:"pay_time" xorm:"comment('支付时间') DATETIME 'pay_time'"`
}
