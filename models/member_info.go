package models

import (
	"time"
)

type MemberInfo struct {
	Memberid     string    `xorm:"not null pk comment('用户唯一标示') CHAR(16)"`
	Nickname     string    `xorm:"default 'NULL' comment('用户昵称') VARCHAR(50)"`
	Membername   string    `xorm:"default 'NULL' comment('用户名称') VARCHAR(50)"`
	Avatarurl    string    `xorm:"default ''http://file.rvet.cn/avatarimg.png'' comment('全平台用户头像') VARCHAR(100)"`
	Membersource int       `xorm:"default NULL comment('用户来源(枚举)') INT(11)"`
	Sex          int       `xorm:"default NULL comment('用户性别(1-男，0-女)') INT(11)"`
	Levelcardid  int       `xorm:"default NULL comment('用户会员卡号') INT(11)"`
	Securitcode  string    `xorm:"default 'NULL' comment('账户密码(md5)') VARCHAR(50)"`
	Paycode      string    `xorm:"default 'NULL' comment('支付密码(md5)') VARCHAR(50)"`
	Mobile       string    `xorm:"not null comment('手机号码') VARCHAR(30)"`
	Fixphone     string    `xorm:"default 'NULL' comment('固定电话') VARCHAR(20)"`
	Province     int       `xorm:"default NULL comment('省') INT(11)"`
	City         int       `xorm:"default NULL comment('市') INT(11)"`
	Area         int       `xorm:"default NULL comment('省市区中的区id(city中的path来找上级城市)') INT(11)"`
	Isvalid      int       `xorm:"not null comment('是否有效(1-有效0-无效)') INT(11)"`
	Isvip        int       `xorm:"not null default 0 comment('是否是VIP(1-是0-否)') INT(11)"`
	Remark       string    `xorm:"default 'NULL' comment('用户信息备注') VARCHAR(200)"`
	Lasttime     time.Time `xorm:"not null default 'current_timestamp()' comment('最后操作时间') TIMESTAMP"`
	Lastuser     string    `xorm:"default 'NULL' comment('最后操作人') VARCHAR(50)"`
	Createtime   time.Time `xorm:"not null default 'current_timestamp()' comment('创建日期，开卡日期') TIMESTAMP"`
	Hospitalid   int       `xorm:"default 0 comment('所在医院') INT(10)"`
	Isnewmember  int       `xorm:"default b'0' comment('是否新会员') BIT(1)"`
	Convertdate  time.Time `xorm:"default ''2019-07-02 00:00:00'' comment('新老会员转换时间') TIMESTAMP"`
	Platform     int       `xorm:"default NULL comment('会员数据归属') INT(11)"`
	Useragent    int       `xorm:"default NULL comment('渠道id') INT(11)"`
}

// 接收ERP数据（可删）
type MemberShortInfo struct {
	Memberid string `json:"Memberid"`
	Mobile   string `json:"Mobile"`
}
