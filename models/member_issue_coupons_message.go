package models

import "time"

type MemberIssueCouponsMessage struct {
	Id          int       `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	UserId      string    `json:"user_id" xorm:"not null default '' comment('用户scrm_user_id') VARCHAR(50) 'user_id'"`
	MessageType int       `json:"message_type" xorm:"default 0 comment('1、升级券 2、周特权') INT(11) 'message_type'"`
	MessageBody string    `json:"message_body" xorm:"default '' comment('消息内容') VARCHAR(255) 'message_body'"`
	CouponType  int       `json:"coupon_type" xorm:"default 0 comment('券类型：1门店券  2商品券 ') INT(11) 'coupon_type'"`
	HideTime    time.Time `json:"hide_time" xorm:"comment('隐藏时间') DATETIME 'hide_time'"`
	CreateTime  time.Time `json:"create_time" xorm:"comment('创建时间') DATETIME 'create_time'"`
}