package models

import (
	"time"
)

type TScrmPetDict struct {
	Id               int       `xorm:"not null pk autoincr INT(10)"`
	PetDictId        string    `xorm:"not null comment('宠物字典ID') unique VARCHAR(32)"`
	PetDictParentId  string    `xorm:"not null comment('宠物字典父ID') index VARCHAR(32)"`
	PetDictName      string    `xorm:"not null comment('宠物字典名称') VARCHAR(32)"`
	PetDictShortName string    `xorm:"not null comment('宠物字典简称') VARCHAR(32)"`
	PetDictStatus    int       `xorm:"not null default 0 comment('宠物字段状态 0 正常 -1删除 1隐藏') index TINYINT(3)"`
	CreateTime       time.Time `xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME"`
	UpdateTime       time.Time `xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME"`
}
