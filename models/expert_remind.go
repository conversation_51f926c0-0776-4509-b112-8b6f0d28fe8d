package models

import "time"

type ExpertRemind struct {
	Id             int64     `xorm:"not null pk autoincr INT(11)"`
	UserId          string    `xorm:"not null default '''' comment('用户id') VARCHAR(50)"`
	PetId        string    `xorm:"default '''' comment('宠物id') VARCHAR(255)"`
	TemplateId        string    `xorm:"default '''' comment('模板id') VARCHAR(50)"`
	Type         int32     `xorm:"not null default 0 comment('1 专家提醒') INT"`
	Status         int32     `xorm:"not null default 0 comment('状态 0待发送 1已发送') INT"`
	CreateTime     time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
}
