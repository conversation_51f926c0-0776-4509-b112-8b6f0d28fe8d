package models

import "time"

// 微信消息订阅模板
type WechatSubscribeMessageTemplate struct {
	Id           int64     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	TemplateKey  string    `json:"template_key" xorm:"not null default '' comment('微信消息key') VARCHAR(100) 'template_key'"`
	TemplateType int32     `json:"template_type" xorm:"not null default 0 comment('模板类型 1、积分通知 2、用户等级通知 3、优惠券通知 4、医院挂号排队通知 5、预约挂号通知 7核销码使用 8核销码过期提醒 9-会员权益过期通知') INT 'template_type'"`
	TemplateId   string    `json:"template_id" xorm:"not null default '' comment('微信消息模板id') VARCHAR(100) 'template_id'"`
	Page         string    `json:"page" xorm:"not null default '' comment('微信小程序跳转页面') VARCHAR(100) 'page'"`
	Content      string    `json:"content" xorm:"not null default '' comment('消息内容') VARCHAR(255) 'content'"`
	Comment      string    `json:"comment" xorm:"not null default '' comment('模板说明') VARCHAR(100) 'comment'"`
	CreateTime   time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime   time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}
