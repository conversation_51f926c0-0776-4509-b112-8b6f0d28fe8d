package models

import (
	"strings"
	"time"

	"github.com/ppkg/kit"
)

type TScrmPetVaccinateRecord struct {
	Id            int       `xorm:"not null pk autoincr INT(11)"`
	PetId         string    `xorm:"not null default '''' comment('SCRM宠物id') index VARCHAR(32)"`
	OperationYear int       `xorm:"not null comment('接种日期/驱虫年份') index INT(1)"`
	OperationDate time.Time `xorm:"not null comment('接种日期/驱虫日期') DATE"`
	ShopName      string    `xorm:"not null default '''' comment('接诊机构') VARCHAR(30)"`
	ProductName   string    `xorm:"not null default '''' comment('产品名称') VARCHAR(30)"`
	Type          int       `xorm:"not null comment('1疫苗记录 2驱虫记录 3口腔 4体检') index TINYINT(1)"`
	RecordPhoto   string    `xorm:"not null default '' comment('记录照片') VARCHAR(200) 'record_photo'"`
	CreateTime    time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime    time.Time `xorm:"not null default 'current_timestamp()' comment('更新时间') TIMESTAMP"`
}

type HisRecordRes struct {
	Code int            `json:"code"`
	Msg  string         `json:"msg"`
	Data *HisRecordPage `json:"data"`
}

type HisRecordPage struct {
	//Page       string       `json:"page"`
	TotalCount int64        `json:"total_count"`
	List       []*HisRecord `json:"list"`
}

type HisRecord struct {
	PetId        int64  `json:"pet_id"`
	TypeInfo     int    `json:"type"`
	Year         int    `json:"year"`
	ItemName     string `json:"item_name"`
	ActuallyDate string `json:"actually_date"`
	PetName      string `json:"pet_name"`
	TypeTxt      string `json:"type_txt"`
	// 医院名称
	HostName string `json:"host_name"`
}

type NewHisRecordRes struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data []*NewHisRecord `json:"data"`
}

type NewHisRecord struct {
	Id         int64  `json:"id"`          // 免疫记录id
	PetLogicid string `json:"pet_logicid"` // 宠物id
	Birthday   string `json:"birthday"`

	Age          string `json:"age"`
	Type         string `json:"type"`          // 类别（免疫：200000084，驱虫：200000085，绝育：200000086，洗牙：200000087）
	TypeName     string `json:"type_name"`     // 类别名称
	Category     string `json:"category"`      // 类型（核心疫苗：200000088，狂犬疫苗：200000089，抗体检测：200000090）
	CategoryName string `json:"category_name"` // 类型名称

	CYear           int    `json:"c_year"`            // 记录年份：yyyy
	CDate           MyTime `json:"c_date"`            // 记录日期：yyyy-mm-dd
	Number          int    `json:"number"`            // 针次：3（未知：-1）
	Source          int    `json:"source"`            // 记录来源（子龙系统记录:1、子龙医生记录:2、阿闻用户手动填写:3）
	SourceText      string `json:"source_text"`       // 记录来源文本
	StructCode      string `json:"struct_code"`       // 来源分院编码
	StructName      string `json:"struct_name"`       // 接诊机构名称
	ExecuteTime     string `json:"execute_time"`      // 执行日期
	ProductCode     string `json:"product_code"`      // 产品编码
	ProductName     string `json:"product_name"`      // 产品名称（通用名）
	ExecuteUser     int    `json:"execute_user"`      // 创建人id
	ExecuteUserName string `json:"execute_user_name"` // 创建人名字

}
type MyTime struct {
	Timestamp time.Time
}

func (my *MyTime) UnmarshalJSON(data []byte) error {
	var err error
	s := strings.Trim(string(data), "\"")
	my.Timestamp, err = time.ParseInLocation(kit.DATE_LAYOUT, s, time.Local)
	return err
}
