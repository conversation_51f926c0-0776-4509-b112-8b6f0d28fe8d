package models

import "time"

type VipCardEquityValue struct {
	CardTid     int32     `json:"card_tid" xorm:"pk not null default 0 comment('卡付费周期id') INT(11) 'card_tid'"`
	OrId        int64     `json:"or_id" xorm:"not null default 0 comment('所属大区') TINYINT(2) 'or_id'"`
	EquityId    int32     `json:"equity_id" xorm:"pk not null default 0 comment('权益id') TINYINT(1) 'equity_id'"`
	PrivilegeId string    `json:"privilege_id" xorm:"not null comment('权益值') VARCHAR(255) 'privilege_id'"`
	FreeQuality int32     `json:"free_quality" xorm:"default '0' comment('赠送价值(分)') INT(11) 'free_quality'"`
	SalesType   int32     `json:"sales_type" xorm:"default '0' comment('销售方式：1-主动购买、分销购买；2-充值赠送') TINYINT(4) 'sales_type'"`
	CreateTime  time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime  time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}

type CardOrderValue struct {
	OrderSn     string `json:"order_sn"`
	EquityId    int32  `json:"equity_id"`
	SalesType   int32  `json:"sales_type"`
	EquityName  string `json:"equity_name"`
	FreeQuality int32  `json:"free_quality"`
	GiftOrderSn string `json:"gift_order_sn"`
}
