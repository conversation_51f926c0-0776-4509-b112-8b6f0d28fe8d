package models

import (
	"time"
)

type StoreOperateLog struct {
	Id          int32     `json:"id" xorm:"pk autoincr not null INT 'id'"`
	Type        int32     `json:"type" xorm:"default 'null' comment('操作类型 1配送方式 2.权益配置 3.开卡礼包') INT 'type'"`
	FinanceCode string    `json:"finance_code" xorm:"default '' comment('财务编码') VARCHAR(50) 'finance_code'"`
	FromId      int32     `json:"from_id" xorm:"default 'null' comment('来自表id') INT 'from_id'"`
	Desc        string    `json:"desc" xorm:"default '' comment('描述') VARCHAR(255) 'desc'"`
	IpLocation  string    `json:"ip_location" xorm:"default '' comment('ip位置') VARCHAR(20) 'ip_location'"`
	IpAddr      string    `json:"ip_addr" xorm:"default '' comment('ip地址') VARCHAR(20) 'ip_addr'"`
	UserNo      string    `json:"user_no" xorm:"default '' comment('用户编号') VARCHAR(64) 'user_no'"`
	UserName    string    `json:"user_name" xorm:"default '' comment('用户名称') VARCHAR(30) 'user_name'"`
	CreateTime  time.Time `json:"create_time" xorm:"default 'null' comment('日志时间') DATETIME 'create_time' created"`
	BeforeJson  string    `json:"before_json" xorm:"comment('修改前的数据') TEXT 'before_json'"`
	AfterJson   string    `json:"after_json" xorm:"comment('修改后的数据') TEXT 'after_json'"`
}
