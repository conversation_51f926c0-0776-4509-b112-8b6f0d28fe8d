package models

import "_/proto/cc"

type Tags struct {
	Id          int    `xorm:"not null pk autoincr INT(11)"`
	Name        string `xorm:"default 'NULL' comment('标签名称') VARCHAR(20)"`
	Value       string `xorm:"default 'NULL' comment('标签值，逗号分隔') VARCHAR(255)"`
	HasAsterisk int    `xorm:"default NULL comment('是否属于带星内容') INT(11)"`
	Sort        int    `xorm:"default NULL comment('排序') INT(11)"`
	Groups      int    `xorm:"default NULL comment('分组') INT(11)"`
	DataUrl     string `xorm:"default 'NULL' comment('获取数据的url') VARCHAR(256)"`
	OrgId       int    `json:"org_id" xorm:"not null default 1 comment('主体id') INT 'org_id'"`
}

func (model *Tags) ToTagsDto() *cc.TagsDto {
	var dto = new(cc.TagsDto)
	dto.Name = model.Name
	dto.Value = model.Value
	dto.HasAsterisk = int32(model.HasAsterisk)
	dto.Sort = int32(model.Sort)
	dto.DataUrl = model.DataUrl
	return dto
}
