package models

import "time"

type PetTipsRecommend struct {
	Id        int    `xorm:"pk INT autoincr"`
	Title     string `xorm:"notnull varchar(30)"`
	Icon      string `xorm:"notnull varchar(256)"`
	Reading   int    `xorm:"notnull int"`
	IsDeleted int8
	CreateAt  time.Time `xorm:"created"`
	CreateBy  string
	UpdateAt  time.Time `xorm:"updated"`
	UpdateBy  string
	Content   string `xorm:"not null TEXT"`
}
