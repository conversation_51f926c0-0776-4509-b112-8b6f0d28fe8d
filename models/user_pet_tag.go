package models

import (
	"_/proto/cc"
	"time"
)

//用户宠物标签信息
type UserPetTag struct {
	UserId   string
	PetId    string
	TagName  string
	TagValue string
}

func (model *UserPetTag) ToPetTagDto() *cc.PetTagDto {
	var petTag = new(cc.PetTagDto)
	petTag.UserId = model.UserId
	petTag.PetId = model.PetId
	petTag.TagName = model.TagName
	petTag.TagValue = model.TagValue
	return petTag
}

// 宠物信息
type ScrmUserPet struct {
	PetId        string
	UserId       string
	PetSex       int
	PetKindof    int
	PetVariety   int
	PetNeutering int
	PetBirthday  time.Time
}

func (*ScrmUserPet) TableName() string {
	return "t_scrm_pet_info"
}
