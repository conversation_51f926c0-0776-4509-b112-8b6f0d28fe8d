package models

import "time"

// 微信订阅消息通知开关
type WechatSubscribeNotification struct {
	Id            int64     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	ScrmUserId    string    `json:"scrm_user_id" xorm:"not null default '' comment('用户ID作为唯一标识') VARCHAR(32) 'scrm_user_id'"`
	All           int64     `json:"all" xorm:"not null default 0 comment('所有通知总开关，1开启、0关闭') TINYINT(4) 'all'"`
	UserLevel     int64     `json:"user_level" xorm:"not null default 0 comment('用户会员等级通知，1开启、0关闭') TINYINT(4) 'user_level'"`
	Integral      int64     `json:"integral" xorm:"not null default 0 comment('积分通知，1开启、0关闭') TINYINT(4) 'integral'"`
	Voucher       int64     `json:"voucher" xorm:"not null default 0 comment('优惠券通知，1开启、0关闭') TINYINT(4) 'voucher'"`
	Register      int64     `json:"register" xorm:"not null default 0 comment('医院挂号通知，1开启、0关闭') TINYINT(4) 'register'"`
	Queuing       int64     `json:"queuing" xorm:"not null default 0 comment('医院挂号排队通知，1开启、0关闭') TINYINT(4) 'queuing'"`
	VrCodeUse     int64     `json:"vr_code_use" xorm:"not null default 0 comment('核销码使用，1开启、2关闭') TINYINT(4) 'vr_code_use'"`
	Service       int64     `json:"service" xorm:"not null default 0 comment('子龙美容服务通知，1开启、2关闭') TINYINT(4) 'service'"`
	VrCodeExpire  int64     `json:"vr_code_expire" xorm:"not null default 0 comment('核销码过期提醒，1开启、2关闭') TINYINT(4) 'vr_code_expire'"`
	VipCardExpire int64     `json:"vip_card_expire" xorm:"not null default 0 comment('会员权益过期通知，1开启、2关闭') TINYINT(4) 'vip_card_expire'"`
	CreateTime    time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime    time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
	StoreId       int32     `json:"store_id" xorm:"not null default 1 comment('主体：1：默认，2-极宠家') TINYINT(4) 'store_id'"`
}
