package models

import "_/proto/cc"

type PetTipsTag struct {
	PetTipsId int    `xorm:"not null pk comment('标签贴士Id') INT(11)"`
	TagName   string `xorm:"not null comment('标签名称') VARCHAR(32)"`
	TagValue  string `xorm:"not null comment('标签值') VARCHAR(32)"`
}

func (model *PetTipsTag) ToPetTipsTagDto() *cc.PetTipsTagDto {
	var dto = new(cc.PetTipsTagDto)
	dto.Name = model.TagName
	dto.Value = model.TagValue
	return dto
}
