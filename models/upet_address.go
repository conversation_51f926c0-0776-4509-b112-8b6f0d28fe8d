package models

import (
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

type UpetAddress struct {
	AddressId   int32   `xorm:"pk INT autoincr NULL int(10)"`
	MemberId    int64   `xorm:"not null default 0 comment('会员ID') int(10)"`
	TrueName    string  `xorm:"NOT NULL comment('会员姓名') VARCHAR(50)"`
	AreaId      int32   `xorm:"NOT NULL DEFAULT '0' COMMENT('地区ID') int(10)"`
	CityId      int32   `xorm:"DEFAULT NULL COMMENT('市级ID') int(10)"`
	AreaInfo    string  `xorm:"varchar(255) NOT NULL DEFAULT '' COMMENT('地区内容')"`
	Address     string  `xorm:"varchar(255) NOT NULL COMMENT('地址')"`
	TelPhone    string  `xorm:"varchar(20) DEFAULT NULL COMMENT('座机电话')"`
	MobPhone    string  `xorm:"varchar(15) DEFAULT NULL COMMENT('手机电话')"`
	IsDefault   int32   `xorm:"enum('0','1') NOT NULL DEFAULT '0' COMMENT('1默认收货地址')"`
	DlypId      int32   `xorm:"int(11) DEFAULT '0' COMMENT('自提点ID')"`
	AreaLat     float64 `xorm:"decimal(17,13) DEFAULT '0.0000000000000' COMMENT('纬度')"`
	AreaLng     float64 `xorm:"decimal(17,13) DEFAULT '0.0000000000000' COMMENT('经度')"`
	AreaTxlat   float64 `xorm:"decimal(17,13) DEFAULT '0.0000000000000' COMMENT('腾讯纬度')"`
	AreaTxlng   float64 `xorm:"decimal(17,13) DEFAULT '0.0000000000000' COMMENT('腾讯经度')"`
	Isdeal      int32   `xorm:"tinyint(1) DEFAULT '0' COMMENT('0默认1已处理腾讯坐标转换')"`
	AddressDesc string  `xorm:"varchar(150) DEFAULT '' COMMENT('地图地址说明')"`
	HouseInfo   string  `xorm:"varchar(100) DEFAULT '' COMMENT('门牌号')"`
	AreaAdcode  int32   `xorm:"int(11) DEFAULT '0' COMMENT('城市编码')"`
	MapAddress  string  `xorm:"varchar(255) DEFAULT NULL COMMENT('地图地址')"`
	StoreId     int32   `xorm:"not null default 1 comment('小程序主体：1-默认，2-极宠家 3-宠商云 4-百林康源 6-saas') TINYINT(4)"`
}

type Response struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Result  struct {
		AdInfo struct {
			Province string `json:"province"`
			City     string `json:"city"`
			District string `json:"district"`
		} `json:"ad_info"`
		AddressReference struct {
			Town struct {
				Title string `json:"title"`
			} `json:"town"`
		} `json:"address_reference"`
	} `json:"result"`
}

// UpdateAreaInfo 通过区域编码更新区域相关信息
func (address *UpetAddress) UpdateAreaInfo(db *xorm.Engine) error {
	if address.AreaAdcode > 0 {
		area := new(UpetAddress)
		if has, err := db.Table("upet_area").Alias("a3").
			Join("inner", "upet_area as a2", "a3.area_parent_id = a2.area_id").
			Join("inner", "upet_area as a1", "a2.area_parent_id = a1.area_id").
			Where("a3.area_adcode = ?", address.AreaAdcode).
			Select("concat_ws(' ',a1.area_name,a2.area_name,a3.area_name) as area_info,a2.area_id as city_id,a3.area_id").
			Get(area); err != nil {
			glog.Info("getAreaInfoByCode 查询出错：", err.Error(), "，编码：", address.AreaAdcode)
			return err
		} else if has {
			address.AreaInfo = area.AreaInfo
			address.AreaId = area.AreaId
			address.CityId = area.CityId
		}
	}
	//特殊地区特殊获取第三级城市地址
	if strings.Index(address.AreaInfo, "东莞市") > 0 {
		var res Response
		var location string
		location = cast.ToString(address.AreaTxlat) + "," + cast.ToString(address.AreaTxlng)
		resp, err := http.Get("https://apis.map.qq.com/ws/geocoder/v1/?location=" + location + "&output=json&key=" + config.GetString("tengxun_map_key"))
		if err != nil {
			glog.Error(err.Error())
		}
		defer resp.Body.Close()
		if resBody, err := io.ReadAll(resp.Body); err != nil {
			glog.Error(err.Error())
		} else {
			if err = json.Unmarshal(resBody, &res); err != nil {
				glog.Error(err.Error())
			}
			if res.Status != 0 {
				glog.Error(res.Message)
			}
			if len(res.Result.AddressReference.Town.Title) > 0 {
				address.AreaInfo = res.Result.AdInfo.Province + " " + res.Result.AdInfo.City + " " + res.Result.AddressReference.Town.Title
			}
		}
	} else if strings.Index(address.AreaInfo, "市") == 6 {
		address.AreaInfo = strings.Replace(address.AreaInfo, "市", "", 1)
	}

	return nil
}
