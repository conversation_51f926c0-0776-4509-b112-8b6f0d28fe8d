package models

import (
	"time"
)

type VipCardTemplate struct {
	Id              int32     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	SkuId           int32     `json:"sku_id" xorm:"not null default 0 comment('商品sku_id') INT(11) 'sku_id'"`
	OrId            int64     `json:"or_id" xorm:"not null default 0 comment('所属大区') TINYINT(2) 'or_id'"`
	CardName        string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	TipTitle        string    `json:"tip_title" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'tip_title'"`
	CardType        int32     `json:"card_type" xorm:"default 0 comment('卡类型 1-付费卡 2-试用会员卡') TINYINT(1) 'card_type'"`
	CardCycle       int32     `json:"card_cycle" xorm:"default 0 comment('付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡') TINYINT(1) 'card_cycle'"`
	DurationDate    int32     `json:"duration_date" xorm:"default 0 comment('时长(天)') INT(11) 'duration_date'"`
	MemberPrice     float64   `json:"member_price" xorm:"default '0.00' comment('会员原价') DECIMAL(10) 'member_price'"`
	MemberDiscPrice float64   `json:"member_disc_price" xorm:"default '0.00' comment('会员折扣价') DECIMAL(10) 'member_disc_price'"`
	Type            int32     `json:"type" xorm:"not null default 0 comment('来源类型：1-会员卡 2-服务包') TINYINT(1) 'type'"`
	DisRate         float64   `json:"dis_rate" xorm:"default '0.00' comment('分销佣金比例') DECIMAL(4) 'dis_rate'"`
	WebId           int32     `json:"web_id" xorm:"not null default 0 comment('微页面id') INT(11) 'web_id'"`
	CreateTime      time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime      time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}
