package models

import (
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
)

type Address struct {
	AreaInfo string `json:"area_info"`
	Address  string `json:"address"`
	Lat      string `json:"lat"`
	Lng      string `json:"lng"`
}

func Test_UpdateAreaInfo(t *testing.T) {
	var res Response
	var address Address
	address.AreaInfo = "广东省东莞市东莞市"
	address.Address = "永盛大街120号新高购物广场2楼81号"
	address.Lat = "22.801605"
	address.Lng = "113.802345"
	if strings.Index(address.AreaInfo, "东莞市") > 0 {
		resp, err := http.Get("https://apis.map.qq.com/ws/geocoder/v1/?location=" + address.Lat + "," + address.Lng + "&output=json&key=" + config.GetString("tengxun_map_key"))
		if err != nil {
			glog.Error(err.Error())
		}
		defer resp.Body.Close()
		if resBody, err := io.ReadAll(resp.Body); err != nil {
			glog.Error(err.Error())
		} else {
			//{"status":0,"result":{"location":{"lng":114.14522694763153,"lat":22.74933632362202},"precise":0,"confidence":75,"comprehension":57,"level":"购物"}}
			if err = json.Unmarshal(resBody, &res); err != nil {
				glog.Error(err.Error())
			}
			if res.Status != 0 {
				glog.Error(res.Message)
			}
			if len(res.Result.AddressReference.Town.Title) > 0 {
				glog.Info(res.Result.AdInfo.Province + " " + res.Result.AdInfo.City + " " + res.Result.AddressReference.Town.Title)
			}
		}
	}
}

func TestUpetAddress_UpdateAreaInfo(t *testing.T) {
	type fields struct {
		AddressId   int32
		MemberId    int32
		TrueName    string
		AreaId      int32
		CityId      int32
		AreaInfo    string
		Address     string
		TelPhone    string
		MobPhone    string
		IsDefault   int32
		DlypId      int32
		AreaLat     float64
		AreaLng     float64
		AreaTxlat   float64
		AreaTxlng   float64
		Isdeal      int32
		AddressDesc string
		HouseInfo   string
		AreaAdcode  int32
		MapAddress  string
	}
	type args struct {
		db *xorm.Engine
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			fields: fields{
				AreaAdcode: 441900,
				AreaTxlat:  22.801605,
				AreaTxlng:  113.802345,
				AreaInfo:   "广东省东莞市东莞市",
				Address:    "永盛大街120号新高购物广场2楼",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			address := &UpetAddress{
				AddressId:   tt.fields.AddressId,
				MemberId:    tt.fields.MemberId,
				TrueName:    tt.fields.TrueName,
				AreaId:      tt.fields.AreaId,
				CityId:      tt.fields.CityId,
				AreaInfo:    tt.fields.AreaInfo,
				Address:     tt.fields.Address,
				TelPhone:    tt.fields.TelPhone,
				MobPhone:    tt.fields.MobPhone,
				IsDefault:   tt.fields.IsDefault,
				DlypId:      tt.fields.DlypId,
				AreaLat:     tt.fields.AreaLat,
				AreaLng:     tt.fields.AreaLng,
				AreaTxlat:   tt.fields.AreaTxlat,
				AreaTxlng:   tt.fields.AreaTxlng,
				Isdeal:      tt.fields.Isdeal,
				AddressDesc: tt.fields.AddressDesc,
				HouseInfo:   tt.fields.HouseInfo,
				AreaAdcode:  tt.fields.AreaAdcode,
				MapAddress:  tt.fields.MapAddress,
			}
			if err := address.UpdateAreaInfo(tt.args.db); (err != nil) != tt.wantErr {
				t.Errorf("UpdateAreaInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
