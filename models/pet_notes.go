package models

type PetNotes struct {
	Id         int    `xorm:"not null pk autoincr INT(11)"`
	UserId     string `xorm:"not null comment('用户id') VARCHAR(100)"`
	PetId      string `xorm:"not null comment('宠物id') VARCHAR(100)"`
	Title      string `xorm:"not null comment('标题') VARCHAR(30)"`
	Content    string `xorm:"not null comment('便签内容') TEXT"`
	IsRemind   int    `xorm:"not null default 0 comment('是否设置提醒 1 未设置 2 设置') INT(4)"`
	IsDeleted  int    `xorm:"not null default 1 comment('是否被删除 1 未删除 2 已删除') INT(4)"`
	RemindTime string `xorm:"default 'NULL' comment('提醒时间') DATETIME"`
	CreateTime string `xorm:"not null comment('创建时间') DATETIME"`
	CreateId   string `xorm:"not null comment('创建人id') VARCHAR(100)"`
	UpdateTime string `xorm:"not null comment('更新时间') DATETIME"`
	UpdateId   string `xorm:"not null comment('更新人id') VARCHAR(100)"`
}
