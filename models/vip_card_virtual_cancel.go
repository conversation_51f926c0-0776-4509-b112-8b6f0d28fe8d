package models

import (
	"time"
)

// VipCardVirtualCancel 会员卡虚拟卡销卡信息表
type VipCardVirtualCancel struct {
	Id           int64     `json:"id"`            // 自增、主键
	FileUrl      string    `json:"file_url"`      // 需要销卡的卡列表文件
	CancelRemark string    `json:"cancel_remark"` // 销卡备注
	OrgId        int32     `json:"org_id"`        // 组织ID
	OrgName      string    `json:"org_name"`      // 组织名称
	UserId       string    `json:"user_id"`       // 销卡申请人编号
	UserName     string    `json:"user_name"`     // 销卡申请人名称
	CancelNum    int32     `json:"cancel_num"`    // 销卡数量
	CancelStatus int32     `json:"cancel_status"` // 销卡状态(1-待执行，2-已执行，3-执行失败)
	CancelResult string    `json:"cancel_result"` // 执行结果
	CreateTime   time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime   time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	ErrorNum     int32     `json:"error_num"` // 执行失败的数量
}

func (m *VipCardVirtualCancel) TableName() string {
	return "vip_card_virtual_cancel"
}
