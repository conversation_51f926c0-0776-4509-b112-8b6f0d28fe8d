package zilong

import (
	"testing"
)

func TestUserCouponSendMulti(t *testing.T) {
	type args struct {
		req *CouponSendMultiReq
	}
	tests := []struct {
		name    string
		args    args
		wantOut []*CouponSendMultiData
		wantErr bool
	}{
		{
			args: args{req: &CouponSendMultiReq{
				Number:        1,
				PhoneArr:      []string{"17704021685"},
				TemplateIdArr: []int32{282},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, gotOut, err := CouponSendMulti(tt.args.req)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("CouponSendMulti() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestTemplateDetail(t *testing.T) {
	type args struct {
		templateId int32
	}
	tests := []struct {
		name    string
		args    args
		wantRes *Response
		wantErr bool
	}{
		{
			args: args{
				//templateId: 282,//多少天有效期
				templateId: 283, //2023-06-10~2023-07-31
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRes, data, err := CouponTemplateDetail(tt.args.templateId)
			t.Log(gotRes, data)
			if (err != nil) != tt.wantErr {
				t.Errorf("TemplateDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
