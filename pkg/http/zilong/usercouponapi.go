package zilong

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

// CouponResponse 优惠券接口响应
type CouponResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// CouponSendMultiReq 批量发放优惠券请求参数
type CouponSendMultiReq struct {
	Number        int32    `json:"number"`
	PhoneArr      []string `json:"phoneArr"`
	TemplateIdArr []int32  `json:"templateIdArr"`
	Source        string   `json:"source"`
	AppId         string   `json:"appId"`
}

type CouponSendMultiCouponList struct {
	CouponID   int32  `json:"couponId"`
	CouponCode string `json:"couponCode"`
	UserPhone  string `json:"userPhone"`
}

// CouponSendMultiData 批量发放优惠券响应Data
type CouponSendMultiData struct {
	CouponTemplateId        int32  `json:"couponTemplateId"`
	PeriodValidityBeginTime string `json:"periodValidityBeginTime"`
	PeriodValidityEndTime   string `json:"periodValidityEndTime"`
	CouponList              []*CouponSendMultiCouponList
}

// CouponSendMulti 批量发送门店券
// https://yapi.rp-field.com/project/181/interface/api/20650
func CouponSendMulti(req *CouponSendMultiReq) (res *CouponResponse, data []*CouponSendMultiData, err error) {
	if len(req.Source) == 0 {
		req.Source = "2"
	}
	if len(req.AppId) == 0 {
		req.AppId = config.GetString("bj.auth.appid")
	}

	res, err = CouponPost("couponapi/api/usercoupon/send-multi", req, &data)
	if err != nil {
		err = errors.New("兑换门店券 " + res.Msg)
		return
	}

	return
}

// CouponTemplateDetailData 模板详情数据
type CouponTemplateDetailData struct {
	TemplateID            int32  `json:"templateId"`
	TemplateName          string `json:"templateName"`
	TemplateType          int32  `json:"templateType"`
	TemplateTypeCn        string `json:"templateTypeCn"`
	TemplateValue         string `json:"templateValue"`
	TemplateDeliveryCount int32  `json:"templateDeliveryCount"`
	Status                string `json:"status"`         // 状态文本
	StatusValue           int32  `json:"statusValue"`    // 10审核中、20待提交、30待投放、40已投放、50已过期
	Inventory             int32  `json:"inventory"`      // 库存
	PeriodValidity        string `json:"periodValidity"` //有效期，type=2表示领取后after_day天，有效期period_validity天，type=1表示begin_time到end_time
}

// CouponTemplateDetail 门店券模板详情
// https://yapi.rp-field.com/project/181/interface/api/20920
func CouponTemplateDetail(templateId int32) (res *CouponResponse, data *CouponTemplateDetailData, err error) {
	res, err = CouponPost("couponapi/api/template/detail", map[string]interface{}{
		"templateId": templateId,
		"appId":      config.GetString("bj.auth.appid"),
	}, &data)

	if err != nil {
		err = errors.New("门店券详情 " + err.Error())
		return
	}

	return
}

// CouponPost 请求北京优惠券接口
func CouponPost(path string, data interface{}, result interface{}) (res *CouponResponse, err error) {
	resBody, _, err := Do("POST", path, data)
	defer func() {
		// 出错时记录请求日志
		if err != nil && errorShouldLog(path) {
			glog.Error(fmt.Sprintf("调用子龙接口(%s)出错，返回内容%s:%s，接口参数:%s", path, err.Error(), string(resBody), kit.JsonEncode(data)))
		}
	}()
	if err != nil {
		return
	}

	res = &CouponResponse{}
	// 如果不是空字符串，则自动处理结果赋值
	if result != "" {
		if reflect.ValueOf(result).Kind() != reflect.Ptr {
			return nil, errors.New("result参数必须是指针类型")
		}
		res.Data = result
	}
	if err = json.Unmarshal(resBody, res); err != nil {
		err = errors.New("解析响应body出错 " + err.Error())
	} else if res.Code > 0 {
		err = errors.New(fmt.Sprintf("%d %s", res.Code, res.Msg))
	}

	return
}
