package zilong

import (
	"testing"
)

func TestPost(t *testing.T) {
	type args struct {
		url  string
		data map[string]interface{}
	}
	tests := []struct {
		name    string
		args    args
		wantRes Response
		wantErr bool
	}{
		{
			name: "",
			args: args{url: "scrm-mini-api/wechat/accesstoken", data: map[string]interface{}{
				"systemCode":   "scrm_mini_customer",
				"forceRefresh": false,
			}},
		}, {
			name: "",
			args: args{url: "scrm-mini-api/wechat/accesstoken", data: map[string]interface{}{
				"systemCode":   "scrm_mini_customer",
				"forceRefresh": true,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRes, err := Post(tt.args.url, tt.args.data, "")
			t.Log(gotRes, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("Post() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
