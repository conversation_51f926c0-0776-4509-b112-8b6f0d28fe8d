// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sv/stockVisual.proto

package sv

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 空参数
type Empty struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Empty) Reset()         { *m = Empty{} }
func (m *Empty) String() string { return proto.CompactTextString(m) }
func (*Empty) ProtoMessage()    {}
func (*Empty) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{0}
}

func (m *Empty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Empty.Unmarshal(m, b)
}
func (m *Empty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Empty.Marshal(b, m, deterministic)
}
func (m *Empty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Empty.Merge(m, src)
}
func (m *Empty) XXX_Size() int {
	return xxx_messageInfo_Empty.Size(m)
}
func (m *Empty) XXX_DiscardUnknown() {
	xxx_messageInfo_Empty.DiscardUnknown(m)
}

var xxx_messageInfo_Empty proto.InternalMessageInfo

//通用返回
type BaseResponse struct {
	//
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{1}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

// 库存可视化列表请求参数
type WarehouseLStockistRequest struct {
	// 区域仓或前置仓id，多个用逗号分隔
	WarehouseId []int32 `protobuf:"varint,1,rep,packed,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 商品sku
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商品A8编码
	ThirdSkuId string `protobuf:"bytes,3,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	// 库存状态 默认0全部，1 是，2 否
	IsWarning int32 `protobuf:"varint,4,opt,name=is_warning,json=isWarning,proto3" json:"is_warning"`
	// 库存数量
	StockNum []int32 `protobuf:"varint,5,rep,packed,name=stock_num,json=stockNum,proto3" json:"stock_num"`
	// 补货数量
	GoodsNum []int32 `protobuf:"varint,6,rep,packed,name=goods_num,json=goodsNum,proto3" json:"goods_num"`
	// 前置仓所在城市
	City string `protobuf:"bytes,7,opt,name=city,proto3" json:"city"`
	// 分页页码
	PageIndex int32 `protobuf:"varint,8,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 分页数据量
	PageSize int32 `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 账面库存排序 1升序 2降序
	BookInventoryOrder   int32    `protobuf:"varint,10,opt,name=book_inventory_order,json=bookInventoryOrder,proto3" json:"book_inventory_order"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseLStockistRequest) Reset()         { *m = WarehouseLStockistRequest{} }
func (m *WarehouseLStockistRequest) String() string { return proto.CompactTextString(m) }
func (*WarehouseLStockistRequest) ProtoMessage()    {}
func (*WarehouseLStockistRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{2}
}

func (m *WarehouseLStockistRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseLStockistRequest.Unmarshal(m, b)
}
func (m *WarehouseLStockistRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseLStockistRequest.Marshal(b, m, deterministic)
}
func (m *WarehouseLStockistRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseLStockistRequest.Merge(m, src)
}
func (m *WarehouseLStockistRequest) XXX_Size() int {
	return xxx_messageInfo_WarehouseLStockistRequest.Size(m)
}
func (m *WarehouseLStockistRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseLStockistRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseLStockistRequest proto.InternalMessageInfo

func (m *WarehouseLStockistRequest) GetWarehouseId() []int32 {
	if m != nil {
		return m.WarehouseId
	}
	return nil
}

func (m *WarehouseLStockistRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *WarehouseLStockistRequest) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *WarehouseLStockistRequest) GetIsWarning() int32 {
	if m != nil {
		return m.IsWarning
	}
	return 0
}

func (m *WarehouseLStockistRequest) GetStockNum() []int32 {
	if m != nil {
		return m.StockNum
	}
	return nil
}

func (m *WarehouseLStockistRequest) GetGoodsNum() []int32 {
	if m != nil {
		return m.GoodsNum
	}
	return nil
}

func (m *WarehouseLStockistRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *WarehouseLStockistRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *WarehouseLStockistRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *WarehouseLStockistRequest) GetBookInventoryOrder() int32 {
	if m != nil {
		return m.BookInventoryOrder
	}
	return 0
}

// 区域仓库存可视化响应数据
type RegionWarehouseStockResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 数据
	Data []*RegionWarehouseStockData `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	// 消息
	Msg string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegionWarehouseStockResponse) Reset()         { *m = RegionWarehouseStockResponse{} }
func (m *RegionWarehouseStockResponse) String() string { return proto.CompactTextString(m) }
func (*RegionWarehouseStockResponse) ProtoMessage()    {}
func (*RegionWarehouseStockResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{3}
}

func (m *RegionWarehouseStockResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegionWarehouseStockResponse.Unmarshal(m, b)
}
func (m *RegionWarehouseStockResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegionWarehouseStockResponse.Marshal(b, m, deterministic)
}
func (m *RegionWarehouseStockResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionWarehouseStockResponse.Merge(m, src)
}
func (m *RegionWarehouseStockResponse) XXX_Size() int {
	return xxx_messageInfo_RegionWarehouseStockResponse.Size(m)
}
func (m *RegionWarehouseStockResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionWarehouseStockResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RegionWarehouseStockResponse proto.InternalMessageInfo

func (m *RegionWarehouseStockResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RegionWarehouseStockResponse) GetData() []*RegionWarehouseStockData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *RegionWarehouseStockResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *RegionWarehouseStockResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 前置仓库存可视化响应数据
type PreposeWarehouseStockResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 总数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 数据
	Data                 []*PreposeWarehouseStockData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PreposeWarehouseStockResponse) Reset()         { *m = PreposeWarehouseStockResponse{} }
func (m *PreposeWarehouseStockResponse) String() string { return proto.CompactTextString(m) }
func (*PreposeWarehouseStockResponse) ProtoMessage()    {}
func (*PreposeWarehouseStockResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{4}
}

func (m *PreposeWarehouseStockResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreposeWarehouseStockResponse.Unmarshal(m, b)
}
func (m *PreposeWarehouseStockResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreposeWarehouseStockResponse.Marshal(b, m, deterministic)
}
func (m *PreposeWarehouseStockResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreposeWarehouseStockResponse.Merge(m, src)
}
func (m *PreposeWarehouseStockResponse) XXX_Size() int {
	return xxx_messageInfo_PreposeWarehouseStockResponse.Size(m)
}
func (m *PreposeWarehouseStockResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PreposeWarehouseStockResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PreposeWarehouseStockResponse proto.InternalMessageInfo

func (m *PreposeWarehouseStockResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PreposeWarehouseStockResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *PreposeWarehouseStockResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *PreposeWarehouseStockResponse) GetData() []*PreposeWarehouseStockData {
	if m != nil {
		return m.Data
	}
	return nil
}

// 区域库存可视化数据
type RegionWarehouseStockData struct {
	// 商品sku
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// A8编码
	ThirdSkuId string `protobuf:"bytes,2,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	// 商品名称
	ProductName string `protobuf:"bytes,3,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 所属大仓id
	WarehouseId int32 `protobuf:"varint,4,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 所属大仓名称
	WarehouseName string `protobuf:"bytes,5,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	// 账目库存
	BookInventory int32 `protobuf:"varint,6,opt,name=book_inventory,json=bookInventory,proto3" json:"book_inventory"`
	// 可用库存
	AvailableStock int32 `protobuf:"varint,7,opt,name=available_stock,json=availableStock,proto3" json:"available_stock"`
	// 安全库存水位
	SafeStockLevel int32 `protobuf:"varint,8,opt,name=safe_stock_level,json=safeStockLevel,proto3" json:"safe_stock_level"`
	// 库存是否预警 1 是，2否
	StockWarning int32 `protobuf:"varint,9,opt,name=stock_warning,json=stockWarning,proto3" json:"stock_warning"`
	// 建议补货量
	SuggestedStock int32 `protobuf:"varint,10,opt,name=suggested_stock,json=suggestedStock,proto3" json:"suggested_stock"`
	// 历史销量
	// 7天
	History_7Sv int32 `protobuf:"varint,11,opt,name=history_7_sv,json=history7Sv,proto3" json:"history_7_sv"`
	// 15天
	History_15Sv int32 `protobuf:"varint,12,opt,name=history_15_sv,json=history15Sv,proto3" json:"history_15_sv"`
	// 30天
	History_30Sv int32 `protobuf:"varint,13,opt,name=history_30_sv,json=history30Sv,proto3" json:"history_30_sv"`
	// 60天
	History_60Sv int32 `protobuf:"varint,14,opt,name=history_60_sv,json=history60Sv,proto3" json:"history_60_sv"`
	// 90天
	History_90Sv int32 `protobuf:"varint,15,opt,name=history_90_sv,json=history90Sv,proto3" json:"history_90_sv"`
	// 预计销量
	// 7天
	Expect_7Sv int32 `protobuf:"varint,16,opt,name=expect_7_sv,json=expect7Sv,proto3" json:"expect_7_sv"`
	// 15天
	Expect_15Sv int32 `protobuf:"varint,17,opt,name=expect_15_sv,json=expect15Sv,proto3" json:"expect_15_sv"`
	// 30天
	Expect_30Sv int32 `protobuf:"varint,18,opt,name=expect_30_sv,json=expect30Sv,proto3" json:"expect_30_sv"`
	// 60天
	Expect_60Sv int32 `protobuf:"varint,19,opt,name=expect_60_sv,json=expect60Sv,proto3" json:"expect_60_sv"`
	// 90天
	Expect_90Sv          int32    `protobuf:"varint,20,opt,name=expect_90_sv,json=expect90Sv,proto3" json:"expect_90_sv"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegionWarehouseStockData) Reset()         { *m = RegionWarehouseStockData{} }
func (m *RegionWarehouseStockData) String() string { return proto.CompactTextString(m) }
func (*RegionWarehouseStockData) ProtoMessage()    {}
func (*RegionWarehouseStockData) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{5}
}

func (m *RegionWarehouseStockData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegionWarehouseStockData.Unmarshal(m, b)
}
func (m *RegionWarehouseStockData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegionWarehouseStockData.Marshal(b, m, deterministic)
}
func (m *RegionWarehouseStockData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionWarehouseStockData.Merge(m, src)
}
func (m *RegionWarehouseStockData) XXX_Size() int {
	return xxx_messageInfo_RegionWarehouseStockData.Size(m)
}
func (m *RegionWarehouseStockData) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionWarehouseStockData.DiscardUnknown(m)
}

var xxx_messageInfo_RegionWarehouseStockData proto.InternalMessageInfo

func (m *RegionWarehouseStockData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *RegionWarehouseStockData) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *RegionWarehouseStockData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *RegionWarehouseStockData) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *RegionWarehouseStockData) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

func (m *RegionWarehouseStockData) GetBookInventory() int32 {
	if m != nil {
		return m.BookInventory
	}
	return 0
}

func (m *RegionWarehouseStockData) GetAvailableStock() int32 {
	if m != nil {
		return m.AvailableStock
	}
	return 0
}

func (m *RegionWarehouseStockData) GetSafeStockLevel() int32 {
	if m != nil {
		return m.SafeStockLevel
	}
	return 0
}

func (m *RegionWarehouseStockData) GetStockWarning() int32 {
	if m != nil {
		return m.StockWarning
	}
	return 0
}

func (m *RegionWarehouseStockData) GetSuggestedStock() int32 {
	if m != nil {
		return m.SuggestedStock
	}
	return 0
}

func (m *RegionWarehouseStockData) GetHistory_7Sv() int32 {
	if m != nil {
		return m.History_7Sv
	}
	return 0
}

func (m *RegionWarehouseStockData) GetHistory_15Sv() int32 {
	if m != nil {
		return m.History_15Sv
	}
	return 0
}

func (m *RegionWarehouseStockData) GetHistory_30Sv() int32 {
	if m != nil {
		return m.History_30Sv
	}
	return 0
}

func (m *RegionWarehouseStockData) GetHistory_60Sv() int32 {
	if m != nil {
		return m.History_60Sv
	}
	return 0
}

func (m *RegionWarehouseStockData) GetHistory_90Sv() int32 {
	if m != nil {
		return m.History_90Sv
	}
	return 0
}

func (m *RegionWarehouseStockData) GetExpect_7Sv() int32 {
	if m != nil {
		return m.Expect_7Sv
	}
	return 0
}

func (m *RegionWarehouseStockData) GetExpect_15Sv() int32 {
	if m != nil {
		return m.Expect_15Sv
	}
	return 0
}

func (m *RegionWarehouseStockData) GetExpect_30Sv() int32 {
	if m != nil {
		return m.Expect_30Sv
	}
	return 0
}

func (m *RegionWarehouseStockData) GetExpect_60Sv() int32 {
	if m != nil {
		return m.Expect_60Sv
	}
	return 0
}

func (m *RegionWarehouseStockData) GetExpect_90Sv() int32 {
	if m != nil {
		return m.Expect_90Sv
	}
	return 0
}

// 前置库存可视化数据
type PreposeWarehouseStockData struct {
	// 商品sku
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// A8编码
	ThirdSkuId string `protobuf:"bytes,2,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	// 商品名称
	ProductName string `protobuf:"bytes,3,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 前置仓id
	WarehouseId int32 `protobuf:"varint,4,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 前置仓名称
	WarehouseName string `protobuf:"bytes,5,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	// 账目库存
	BookInventory int32 `protobuf:"varint,6,opt,name=book_inventory,json=bookInventory,proto3" json:"book_inventory"`
	// 占用库存
	OccupyStock int32 `protobuf:"varint,7,opt,name=occupy_stock,json=occupyStock,proto3" json:"occupy_stock"`
	// 可用库存
	AvailableStock int32 `protobuf:"varint,8,opt,name=available_stock,json=availableStock,proto3" json:"available_stock"`
	// 安全库存水位
	SafeStockLevel int32 `protobuf:"varint,9,opt,name=safe_stock_level,json=safeStockLevel,proto3" json:"safe_stock_level"`
	// 高库存水位
	HighStockLevel int32 `protobuf:"varint,10,opt,name=high_stock_level,json=highStockLevel,proto3" json:"high_stock_level"`
	// 库存是否预警 1 是，2否
	StockWarning int32 `protobuf:"varint,11,opt,name=stock_warning,json=stockWarning,proto3" json:"stock_warning"`
	// 触发库存预警的类型,1安全库存 2高库存
	WarningType int32 `protobuf:"varint,12,opt,name=warning_type,json=warningType,proto3" json:"warning_type"`
	// 所属区域仓名称
	RegionName string `protobuf:"bytes,13,opt,name=region_name,json=regionName,proto3" json:"region_name"`
	// 所属区域仓可用库存
	RegionUsableStock int32 `protobuf:"varint,14,opt,name=region_usable_stock,json=regionUsableStock,proto3" json:"region_usable_stock"`
	// 建议补货量
	SuggestedStock int32 `protobuf:"varint,15,opt,name=suggested_stock,json=suggestedStock,proto3" json:"suggested_stock"`
	// 历史销量
	// 7天
	History_7Sv int32 `protobuf:"varint,16,opt,name=history_7_sv,json=history7Sv,proto3" json:"history_7_sv"`
	// 15天
	History_15Sv int32 `protobuf:"varint,17,opt,name=history_15_sv,json=history15Sv,proto3" json:"history_15_sv"`
	// 30天
	History_30Sv int32 `protobuf:"varint,18,opt,name=history_30_sv,json=history30Sv,proto3" json:"history_30_sv"`
	// 60天
	History_60Sv int32 `protobuf:"varint,19,opt,name=history_60_sv,json=history60Sv,proto3" json:"history_60_sv"`
	// 90天
	History_90Sv int32 `protobuf:"varint,20,opt,name=history_90_sv,json=history90Sv,proto3" json:"history_90_sv"`
	// 预计销量
	// 7天
	Expect_7Sv int32 `protobuf:"varint,21,opt,name=expect_7_sv,json=expect7Sv,proto3" json:"expect_7_sv"`
	// 15天
	Expect_15Sv int32 `protobuf:"varint,22,opt,name=expect_15_sv,json=expect15Sv,proto3" json:"expect_15_sv"`
	// 30天
	Expect_30Sv int32 `protobuf:"varint,23,opt,name=expect_30_sv,json=expect30Sv,proto3" json:"expect_30_sv"`
	// 60天
	Expect_60Sv int32 `protobuf:"varint,24,opt,name=expect_60_sv,json=expect60Sv,proto3" json:"expect_60_sv"`
	// 90天
	Expect_90Sv int32 `protobuf:"varint,25,opt,name=expect_90_sv,json=expect90Sv,proto3" json:"expect_90_sv"`
	//  产品标签
	Tag string `protobuf:"bytes,26,opt,name=tag,proto3" json:"tag"`
	//  预测7天销量
	ForeCast_Day_7 int32 `protobuf:"varint,27,opt,name=fore_cast_Day_7,json=foreCastDay7,proto3" json:"fore_cast_Day_7"`
	//  预测15天销量
	ForeCast_Day_15 int32 `protobuf:"varint,28,opt,name=fore_cast_Day_15,json=foreCastDay15,proto3" json:"fore_cast_Day_15"`
	//  预测30天销量
	ForeCast_Day_30 int32 `protobuf:"varint,29,opt,name=fore_cast_Day_30,json=foreCastDay30,proto3" json:"fore_cast_Day_30"`
	// AI销量预测建议补货量
	AiSuggestedStock     int64    `protobuf:"varint,30,opt,name=ai_suggested_stock,json=aiSuggestedStock,proto3" json:"ai_suggested_stock"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreposeWarehouseStockData) Reset()         { *m = PreposeWarehouseStockData{} }
func (m *PreposeWarehouseStockData) String() string { return proto.CompactTextString(m) }
func (*PreposeWarehouseStockData) ProtoMessage()    {}
func (*PreposeWarehouseStockData) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{6}
}

func (m *PreposeWarehouseStockData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreposeWarehouseStockData.Unmarshal(m, b)
}
func (m *PreposeWarehouseStockData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreposeWarehouseStockData.Marshal(b, m, deterministic)
}
func (m *PreposeWarehouseStockData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreposeWarehouseStockData.Merge(m, src)
}
func (m *PreposeWarehouseStockData) XXX_Size() int {
	return xxx_messageInfo_PreposeWarehouseStockData.Size(m)
}
func (m *PreposeWarehouseStockData) XXX_DiscardUnknown() {
	xxx_messageInfo_PreposeWarehouseStockData.DiscardUnknown(m)
}

var xxx_messageInfo_PreposeWarehouseStockData proto.InternalMessageInfo

func (m *PreposeWarehouseStockData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *PreposeWarehouseStockData) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *PreposeWarehouseStockData) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

func (m *PreposeWarehouseStockData) GetBookInventory() int32 {
	if m != nil {
		return m.BookInventory
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetOccupyStock() int32 {
	if m != nil {
		return m.OccupyStock
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetAvailableStock() int32 {
	if m != nil {
		return m.AvailableStock
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetSafeStockLevel() int32 {
	if m != nil {
		return m.SafeStockLevel
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetHighStockLevel() int32 {
	if m != nil {
		return m.HighStockLevel
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetStockWarning() int32 {
	if m != nil {
		return m.StockWarning
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetWarningType() int32 {
	if m != nil {
		return m.WarningType
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetRegionName() string {
	if m != nil {
		return m.RegionName
	}
	return ""
}

func (m *PreposeWarehouseStockData) GetRegionUsableStock() int32 {
	if m != nil {
		return m.RegionUsableStock
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetSuggestedStock() int32 {
	if m != nil {
		return m.SuggestedStock
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetHistory_7Sv() int32 {
	if m != nil {
		return m.History_7Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetHistory_15Sv() int32 {
	if m != nil {
		return m.History_15Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetHistory_30Sv() int32 {
	if m != nil {
		return m.History_30Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetHistory_60Sv() int32 {
	if m != nil {
		return m.History_60Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetHistory_90Sv() int32 {
	if m != nil {
		return m.History_90Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetExpect_7Sv() int32 {
	if m != nil {
		return m.Expect_7Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetExpect_15Sv() int32 {
	if m != nil {
		return m.Expect_15Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetExpect_30Sv() int32 {
	if m != nil {
		return m.Expect_30Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetExpect_60Sv() int32 {
	if m != nil {
		return m.Expect_60Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetExpect_90Sv() int32 {
	if m != nil {
		return m.Expect_90Sv
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *PreposeWarehouseStockData) GetForeCast_Day_7() int32 {
	if m != nil {
		return m.ForeCast_Day_7
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetForeCast_Day_15() int32 {
	if m != nil {
		return m.ForeCast_Day_15
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetForeCast_Day_30() int32 {
	if m != nil {
		return m.ForeCast_Day_30
	}
	return 0
}

func (m *PreposeWarehouseStockData) GetAiSuggestedStock() int64 {
	if m != nil {
		return m.AiSuggestedStock
	}
	return 0
}

// 任务列表请求参数
type TaskListRequest struct {
	// 任务: 1 导出区域仓库存数据，2 导出前置仓库存数据，3 导出效期数据
	TaskContent int32 `protobuf:"varint,1,opt,name=task_content,json=taskContent,proto3" json:"task_content"`
	// 查询人id
	UserNo string `protobuf:"bytes,2,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// 分页页码
	PageIndex int32 `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 分页数据量
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 任务状态
	TaskStatus           int32    `protobuf:"varint,5,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskListRequest) Reset()         { *m = TaskListRequest{} }
func (m *TaskListRequest) String() string { return proto.CompactTextString(m) }
func (*TaskListRequest) ProtoMessage()    {}
func (*TaskListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{7}
}

func (m *TaskListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskListRequest.Unmarshal(m, b)
}
func (m *TaskListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskListRequest.Marshal(b, m, deterministic)
}
func (m *TaskListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskListRequest.Merge(m, src)
}
func (m *TaskListRequest) XXX_Size() int {
	return xxx_messageInfo_TaskListRequest.Size(m)
}
func (m *TaskListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TaskListRequest proto.InternalMessageInfo

func (m *TaskListRequest) GetTaskContent() int32 {
	if m != nil {
		return m.TaskContent
	}
	return 0
}

func (m *TaskListRequest) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *TaskListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *TaskListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *TaskListRequest) GetTaskStatus() int32 {
	if m != nil {
		return m.TaskStatus
	}
	return 0
}

// 任务列表响应参数
type TaskListResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 总数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 数据
	Data                 []*TaskData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *TaskListResponse) Reset()         { *m = TaskListResponse{} }
func (m *TaskListResponse) String() string { return proto.CompactTextString(m) }
func (*TaskListResponse) ProtoMessage()    {}
func (*TaskListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{8}
}

func (m *TaskListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskListResponse.Unmarshal(m, b)
}
func (m *TaskListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskListResponse.Marshal(b, m, deterministic)
}
func (m *TaskListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskListResponse.Merge(m, src)
}
func (m *TaskListResponse) XXX_Size() int {
	return xxx_messageInfo_TaskListResponse.Size(m)
}
func (m *TaskListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TaskListResponse proto.InternalMessageInfo

func (m *TaskListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TaskListResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *TaskListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *TaskListResponse) GetData() []*TaskData {
	if m != nil {
		return m.Data
	}
	return nil
}

// 异步任务创建
type CreateTaskRequest struct {
	// 任务类型 1 导出区域仓库存数据，2 导出前置仓库存数据，3 导出效期数据
	TaskContent int32 `protobuf:"varint,1,opt,name=task_content,json=taskContent,proto3" json:"task_content"`
	// 操作文件路径或者参数
	OperationFileUrl string `protobuf:"bytes,2,opt,name=operation_file_url,json=operationFileUrl,proto3" json:"operation_file_url"`
	// 操作请求的token值，类似userinfo
	RequestHeader string `protobuf:"bytes,3,opt,name=request_header,json=requestHeader,proto3" json:"request_header"`
	// 创建人id
	CreateId             string   `protobuf:"bytes,4,opt,name=create_id,json=createId,proto3" json:"create_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTaskRequest) Reset()         { *m = CreateTaskRequest{} }
func (m *CreateTaskRequest) String() string { return proto.CompactTextString(m) }
func (*CreateTaskRequest) ProtoMessage()    {}
func (*CreateTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{9}
}

func (m *CreateTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTaskRequest.Unmarshal(m, b)
}
func (m *CreateTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTaskRequest.Marshal(b, m, deterministic)
}
func (m *CreateTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTaskRequest.Merge(m, src)
}
func (m *CreateTaskRequest) XXX_Size() int {
	return xxx_messageInfo_CreateTaskRequest.Size(m)
}
func (m *CreateTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTaskRequest proto.InternalMessageInfo

func (m *CreateTaskRequest) GetTaskContent() int32 {
	if m != nil {
		return m.TaskContent
	}
	return 0
}

func (m *CreateTaskRequest) GetOperationFileUrl() string {
	if m != nil {
		return m.OperationFileUrl
	}
	return ""
}

func (m *CreateTaskRequest) GetRequestHeader() string {
	if m != nil {
		return m.RequestHeader
	}
	return ""
}

func (m *CreateTaskRequest) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

// 任务数据
type TaskData struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 任务类型 1 导出区域仓库存数据，2 导出前置仓库存数据，3 导出效期数据
	TaskContent int32 `protobuf:"varint,2,opt,name=task_content,json=taskContent,proto3" json:"task_content"`
	// 任务状态 1 进行中 2 已完成 3 失败
	TaskStatus int32 `protobuf:"varint,3,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	// 操作文件路径或者参数
	OperationFileUrl string `protobuf:"bytes,4,opt,name=operation_file_url,json=operationFileUrl,proto3" json:"operation_file_url"`
	// 操作请求的token值，类似userinfo
	RequestHeader string `protobuf:"bytes,5,opt,name=request_header,json=requestHeader,proto3" json:"request_header"`
	// 操作结果文件路径
	ResulteFileUrl string `protobuf:"bytes,6,opt,name=resulte_file_url,json=resulteFileUrl,proto3" json:"resulte_file_url"`
	// 创建时间
	CreateTime string `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 更新时间
	ModifyTime string `protobuf:"bytes,8,opt,name=modify_time,json=modifyTime,proto3" json:"modify_time"`
	// 创建人id
	CreateId string `protobuf:"bytes,9,opt,name=create_id,json=createId,proto3" json:"create_id"`
	// 任务详情
	TaskDetail           string   `protobuf:"bytes,10,opt,name=task_detail,json=taskDetail,proto3" json:"task_detail"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskData) Reset()         { *m = TaskData{} }
func (m *TaskData) String() string { return proto.CompactTextString(m) }
func (*TaskData) ProtoMessage()    {}
func (*TaskData) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{10}
}

func (m *TaskData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskData.Unmarshal(m, b)
}
func (m *TaskData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskData.Marshal(b, m, deterministic)
}
func (m *TaskData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskData.Merge(m, src)
}
func (m *TaskData) XXX_Size() int {
	return xxx_messageInfo_TaskData.Size(m)
}
func (m *TaskData) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskData.DiscardUnknown(m)
}

var xxx_messageInfo_TaskData proto.InternalMessageInfo

func (m *TaskData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TaskData) GetTaskContent() int32 {
	if m != nil {
		return m.TaskContent
	}
	return 0
}

func (m *TaskData) GetTaskStatus() int32 {
	if m != nil {
		return m.TaskStatus
	}
	return 0
}

func (m *TaskData) GetOperationFileUrl() string {
	if m != nil {
		return m.OperationFileUrl
	}
	return ""
}

func (m *TaskData) GetRequestHeader() string {
	if m != nil {
		return m.RequestHeader
	}
	return ""
}

func (m *TaskData) GetResulteFileUrl() string {
	if m != nil {
		return m.ResulteFileUrl
	}
	return ""
}

func (m *TaskData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *TaskData) GetModifyTime() string {
	if m != nil {
		return m.ModifyTime
	}
	return ""
}

func (m *TaskData) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *TaskData) GetTaskDetail() string {
	if m != nil {
		return m.TaskDetail
	}
	return ""
}

//仓库列表请求参数
type WarehouseListRequest struct {
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
	Category string `protobuf:"bytes,1,opt,name=category,proto3" json:"category"`
	//页码
	Pageindex int32 `protobuf:"varint,2,opt,name=pageindex,proto3" json:"pageindex"`
	//每页行数
	Pagesize int32 `protobuf:"varint,3,opt,name=pagesize,proto3" json:"pagesize"`
	//搜索关键词
	KeyWord string `protobuf:"bytes,4,opt,name=keyWord,proto3" json:"keyWord"`
	// 所属城市
	City string `protobuf:"bytes,5,opt,name=city,proto3" json:"city"`
	// 是否查询前置仓和前置虚拟仓, 默认 0 否，1 是
	PreposeCategory      int32    `protobuf:"varint,6,opt,name=prepose_category,json=preposeCategory,proto3" json:"prepose_category"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseListRequest) Reset()         { *m = WarehouseListRequest{} }
func (m *WarehouseListRequest) String() string { return proto.CompactTextString(m) }
func (*WarehouseListRequest) ProtoMessage()    {}
func (*WarehouseListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{11}
}

func (m *WarehouseListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseListRequest.Unmarshal(m, b)
}
func (m *WarehouseListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseListRequest.Marshal(b, m, deterministic)
}
func (m *WarehouseListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseListRequest.Merge(m, src)
}
func (m *WarehouseListRequest) XXX_Size() int {
	return xxx_messageInfo_WarehouseListRequest.Size(m)
}
func (m *WarehouseListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseListRequest proto.InternalMessageInfo

func (m *WarehouseListRequest) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *WarehouseListRequest) GetPageindex() int32 {
	if m != nil {
		return m.Pageindex
	}
	return 0
}

func (m *WarehouseListRequest) GetPagesize() int32 {
	if m != nil {
		return m.Pagesize
	}
	return 0
}

func (m *WarehouseListRequest) GetKeyWord() string {
	if m != nil {
		return m.KeyWord
	}
	return ""
}

func (m *WarehouseListRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *WarehouseListRequest) GetPreposeCategory() int32 {
	if m != nil {
		return m.PreposeCategory
	}
	return 0
}

//仓库列表返回数据
type WarehouseListResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总行数
	TotalCount int32 `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	//仓库数组
	WarehouseAarray      []*WarehouseList `protobuf:"bytes,5,rep,name=WarehouseAarray,proto3" json:"WarehouseAarray"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WarehouseListResponse) Reset()         { *m = WarehouseListResponse{} }
func (m *WarehouseListResponse) String() string { return proto.CompactTextString(m) }
func (*WarehouseListResponse) ProtoMessage()    {}
func (*WarehouseListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{12}
}

func (m *WarehouseListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseListResponse.Unmarshal(m, b)
}
func (m *WarehouseListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseListResponse.Marshal(b, m, deterministic)
}
func (m *WarehouseListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseListResponse.Merge(m, src)
}
func (m *WarehouseListResponse) XXX_Size() int {
	return xxx_messageInfo_WarehouseListResponse.Size(m)
}
func (m *WarehouseListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseListResponse proto.InternalMessageInfo

func (m *WarehouseListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *WarehouseListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *WarehouseListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *WarehouseListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *WarehouseListResponse) GetWarehouseAarray() []*WarehouseList {
	if m != nil {
		return m.WarehouseAarray
	}
	return nil
}

//仓库列表数据集
type WarehouseList struct {
	//ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//第三方仓库ID 例如a8id,管易ID
	Thirdid string `protobuf:"bytes,2,opt,name=thirdid,proto3" json:"thirdid"`
	//仓库编号
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	//仓库名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	//仓库归属(1-A8,2-管易)
	Comefrom int32 `protobuf:"varint,5,opt,name=comefrom,proto3" json:"comefrom"`
	//仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
	Level int32 `protobuf:"varint,6,opt,name=level,proto3" json:"level"`
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
	Category int32 `protobuf:"varint,7,opt,name=category,proto3" json:"category"`
	//仓库地址
	Address string `protobuf:"bytes,8,opt,name=address,proto3" json:"address"`
	//仓库联系人
	Contacts string `protobuf:"bytes,9,opt,name=contacts,proto3" json:"contacts"`
	//仓库联系方式
	Tel string `protobuf:"bytes,10,opt,name=tel,proto3" json:"tel"`
	//仓库状态（0-禁用，1-启用）
	Status int32 `protobuf:"varint,11,opt,name=status,proto3" json:"status"`
	//创建时间
	Createdate string `protobuf:"bytes,12,opt,name=createdate,proto3" json:"createdate"`
	//最后更新时间
	Lastdate string `protobuf:"bytes,13,opt,name=lastdate,proto3" json:"lastdate"`
	//所属系统 0:默认,1:ERP,2:子龙
	Subsystem int32 `protobuf:"varint,14,opt,name=subsystem,proto3" json:"subsystem"`
	//仓库比例
	Ratio int32 `protobuf:"varint,15,opt,name=ratio,proto3" json:"ratio"`
	//关联门店信息
	WarehouseInfo []string `protobuf:"bytes,16,rep,name=warehouse_info,json=warehouseInfo,proto3" json:"warehouse_info"`
	//仓库经度
	Lng int64 `protobuf:"varint,17,opt,name=lng,proto3" json:"lng"`
	//仓库纬度
	Lat int64 `protobuf:"varint,18,opt,name=lat,proto3" json:"lat"`
	// 所属区域
	Region string `protobuf:"bytes,19,opt,name=region,proto3" json:"region"`
	// 所属城市
	City                 string   `protobuf:"bytes,20,opt,name=city,proto3" json:"city"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseList) Reset()         { *m = WarehouseList{} }
func (m *WarehouseList) String() string { return proto.CompactTextString(m) }
func (*WarehouseList) ProtoMessage()    {}
func (*WarehouseList) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{13}
}

func (m *WarehouseList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseList.Unmarshal(m, b)
}
func (m *WarehouseList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseList.Marshal(b, m, deterministic)
}
func (m *WarehouseList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseList.Merge(m, src)
}
func (m *WarehouseList) XXX_Size() int {
	return xxx_messageInfo_WarehouseList.Size(m)
}
func (m *WarehouseList) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseList.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseList proto.InternalMessageInfo

func (m *WarehouseList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseList) GetThirdid() string {
	if m != nil {
		return m.Thirdid
	}
	return ""
}

func (m *WarehouseList) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *WarehouseList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WarehouseList) GetComefrom() int32 {
	if m != nil {
		return m.Comefrom
	}
	return 0
}

func (m *WarehouseList) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WarehouseList) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *WarehouseList) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *WarehouseList) GetContacts() string {
	if m != nil {
		return m.Contacts
	}
	return ""
}

func (m *WarehouseList) GetTel() string {
	if m != nil {
		return m.Tel
	}
	return ""
}

func (m *WarehouseList) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WarehouseList) GetCreatedate() string {
	if m != nil {
		return m.Createdate
	}
	return ""
}

func (m *WarehouseList) GetLastdate() string {
	if m != nil {
		return m.Lastdate
	}
	return ""
}

func (m *WarehouseList) GetSubsystem() int32 {
	if m != nil {
		return m.Subsystem
	}
	return 0
}

func (m *WarehouseList) GetRatio() int32 {
	if m != nil {
		return m.Ratio
	}
	return 0
}

func (m *WarehouseList) GetWarehouseInfo() []string {
	if m != nil {
		return m.WarehouseInfo
	}
	return nil
}

func (m *WarehouseList) GetLng() int64 {
	if m != nil {
		return m.Lng
	}
	return 0
}

func (m *WarehouseList) GetLat() int64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *WarehouseList) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *WarehouseList) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

// 新增权限信息请求
type AddAuthInfoRequest struct {
	Data                 []*AddAuthRequstList `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddAuthInfoRequest) Reset()         { *m = AddAuthInfoRequest{} }
func (m *AddAuthInfoRequest) String() string { return proto.CompactTextString(m) }
func (*AddAuthInfoRequest) ProtoMessage()    {}
func (*AddAuthInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{14}
}

func (m *AddAuthInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAuthInfoRequest.Unmarshal(m, b)
}
func (m *AddAuthInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAuthInfoRequest.Marshal(b, m, deterministic)
}
func (m *AddAuthInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAuthInfoRequest.Merge(m, src)
}
func (m *AddAuthInfoRequest) XXX_Size() int {
	return xxx_messageInfo_AddAuthInfoRequest.Size(m)
}
func (m *AddAuthInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAuthInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddAuthInfoRequest proto.InternalMessageInfo

func (m *AddAuthInfoRequest) GetData() []*AddAuthRequstList {
	if m != nil {
		return m.Data
	}
	return nil
}

type AddAuthRequstList struct {
	// 用户id
	UserNo string `protobuf:"bytes,1,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// 用户名
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 用户中文名
	StaffNameCn string `protobuf:"bytes,3,opt,name=staff_name_cn,json=staffNameCn,proto3" json:"staff_name_cn"`
	// 用户手机号
	UserMobile           string   `protobuf:"bytes,4,opt,name=user_mobile,json=userMobile,proto3" json:"user_mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAuthRequstList) Reset()         { *m = AddAuthRequstList{} }
func (m *AddAuthRequstList) String() string { return proto.CompactTextString(m) }
func (*AddAuthRequstList) ProtoMessage()    {}
func (*AddAuthRequstList) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{15}
}

func (m *AddAuthRequstList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAuthRequstList.Unmarshal(m, b)
}
func (m *AddAuthRequstList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAuthRequstList.Marshal(b, m, deterministic)
}
func (m *AddAuthRequstList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAuthRequstList.Merge(m, src)
}
func (m *AddAuthRequstList) XXX_Size() int {
	return xxx_messageInfo_AddAuthRequstList.Size(m)
}
func (m *AddAuthRequstList) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAuthRequstList.DiscardUnknown(m)
}

var xxx_messageInfo_AddAuthRequstList proto.InternalMessageInfo

func (m *AddAuthRequstList) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *AddAuthRequstList) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *AddAuthRequstList) GetStaffNameCn() string {
	if m != nil {
		return m.StaffNameCn
	}
	return ""
}

func (m *AddAuthRequstList) GetUserMobile() string {
	if m != nil {
		return m.UserMobile
	}
	return ""
}

// 编辑权限信息请求
type UpdateAuthInfoRequest struct {
	// 用户id
	UserNo string `protobuf:"bytes,1,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// 仓库id,逗号分隔
	WarehouseId          string   `protobuf:"bytes,2,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAuthInfoRequest) Reset()         { *m = UpdateAuthInfoRequest{} }
func (m *UpdateAuthInfoRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateAuthInfoRequest) ProtoMessage()    {}
func (*UpdateAuthInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{16}
}

func (m *UpdateAuthInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAuthInfoRequest.Unmarshal(m, b)
}
func (m *UpdateAuthInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAuthInfoRequest.Marshal(b, m, deterministic)
}
func (m *UpdateAuthInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAuthInfoRequest.Merge(m, src)
}
func (m *UpdateAuthInfoRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateAuthInfoRequest.Size(m)
}
func (m *UpdateAuthInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAuthInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAuthInfoRequest proto.InternalMessageInfo

func (m *UpdateAuthInfoRequest) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *UpdateAuthInfoRequest) GetWarehouseId() string {
	if m != nil {
		return m.WarehouseId
	}
	return ""
}

// 用户权限详细信息请求
type AuthInfoDetailRequest struct {
	// user_no
	UserNo               string   `protobuf:"bytes,1,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthInfoDetailRequest) Reset()         { *m = AuthInfoDetailRequest{} }
func (m *AuthInfoDetailRequest) String() string { return proto.CompactTextString(m) }
func (*AuthInfoDetailRequest) ProtoMessage()    {}
func (*AuthInfoDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{17}
}

func (m *AuthInfoDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthInfoDetailRequest.Unmarshal(m, b)
}
func (m *AuthInfoDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthInfoDetailRequest.Marshal(b, m, deterministic)
}
func (m *AuthInfoDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthInfoDetailRequest.Merge(m, src)
}
func (m *AuthInfoDetailRequest) XXX_Size() int {
	return xxx_messageInfo_AuthInfoDetailRequest.Size(m)
}
func (m *AuthInfoDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthInfoDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AuthInfoDetailRequest proto.InternalMessageInfo

func (m *AuthInfoDetailRequest) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

// 用户权限详细信息响应
type AuthInfoDetailResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回的message
	Msg                  string    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 *AuthInfo `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *AuthInfoDetailResponse) Reset()         { *m = AuthInfoDetailResponse{} }
func (m *AuthInfoDetailResponse) String() string { return proto.CompactTextString(m) }
func (*AuthInfoDetailResponse) ProtoMessage()    {}
func (*AuthInfoDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{18}
}

func (m *AuthInfoDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthInfoDetailResponse.Unmarshal(m, b)
}
func (m *AuthInfoDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthInfoDetailResponse.Marshal(b, m, deterministic)
}
func (m *AuthInfoDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthInfoDetailResponse.Merge(m, src)
}
func (m *AuthInfoDetailResponse) XXX_Size() int {
	return xxx_messageInfo_AuthInfoDetailResponse.Size(m)
}
func (m *AuthInfoDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthInfoDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AuthInfoDetailResponse proto.InternalMessageInfo

func (m *AuthInfoDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AuthInfoDetailResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *AuthInfoDetailResponse) GetData() *AuthInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

// 用户权限列表信息请求
type AuthInfoListRequest struct {
	// 页码
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 页面数据量
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 查询用户的字段，可以是name，user_no,或者mobile
	FindStr              string   `protobuf:"bytes,3,opt,name=find_str,json=findStr,proto3" json:"find_str"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthInfoListRequest) Reset()         { *m = AuthInfoListRequest{} }
func (m *AuthInfoListRequest) String() string { return proto.CompactTextString(m) }
func (*AuthInfoListRequest) ProtoMessage()    {}
func (*AuthInfoListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{19}
}

func (m *AuthInfoListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthInfoListRequest.Unmarshal(m, b)
}
func (m *AuthInfoListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthInfoListRequest.Marshal(b, m, deterministic)
}
func (m *AuthInfoListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthInfoListRequest.Merge(m, src)
}
func (m *AuthInfoListRequest) XXX_Size() int {
	return xxx_messageInfo_AuthInfoListRequest.Size(m)
}
func (m *AuthInfoListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthInfoListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AuthInfoListRequest proto.InternalMessageInfo

func (m *AuthInfoListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *AuthInfoListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *AuthInfoListRequest) GetFindStr() string {
	if m != nil {
		return m.FindStr
	}
	return ""
}

// 用户权限列表信息响应
type AuthInfoListResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回的message
	Msg  string      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data []*AuthInfo `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthInfoListResponse) Reset()         { *m = AuthInfoListResponse{} }
func (m *AuthInfoListResponse) String() string { return proto.CompactTextString(m) }
func (*AuthInfoListResponse) ProtoMessage()    {}
func (*AuthInfoListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{20}
}

func (m *AuthInfoListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthInfoListResponse.Unmarshal(m, b)
}
func (m *AuthInfoListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthInfoListResponse.Marshal(b, m, deterministic)
}
func (m *AuthInfoListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthInfoListResponse.Merge(m, src)
}
func (m *AuthInfoListResponse) XXX_Size() int {
	return xxx_messageInfo_AuthInfoListResponse.Size(m)
}
func (m *AuthInfoListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthInfoListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AuthInfoListResponse proto.InternalMessageInfo

func (m *AuthInfoListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AuthInfoListResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *AuthInfoListResponse) GetData() []*AuthInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *AuthInfoListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 用户权限信息
type AuthInfo struct {
	UserNo               string                  `protobuf:"bytes,1,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	UserName             string                  `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name"`
	StaffNameCn          string                  `protobuf:"bytes,3,opt,name=staff_name_cn,json=staffNameCn,proto3" json:"staff_name_cn"`
	UserMobile           string                  `protobuf:"bytes,4,opt,name=user_mobile,json=userMobile,proto3" json:"user_mobile"`
	UpdateDate           string                  `protobuf:"bytes,5,opt,name=update_date,json=updateDate,proto3" json:"update_date"`
	RegionWarehouse      []*RegionWarehouseInfo  `protobuf:"bytes,6,rep,name=region_warehouse,json=regionWarehouse,proto3" json:"region_warehouse"`
	PreposeWarehouse     []*PreposeWarehouseInfo `protobuf:"bytes,7,rep,name=prepose_warehouse,json=preposeWarehouse,proto3" json:"prepose_warehouse"`
	ButtonPermission     int32                   `protobuf:"varint,8,opt,name=button_permission,json=buttonPermission,proto3" json:"button_permission"`
	Id                   int32                   `protobuf:"varint,9,opt,name=id,proto3" json:"id"`
	DelProPermission     int32                   `protobuf:"varint,10,opt,name=del_pro_permission,json=delProPermission,proto3" json:"del_pro_permission"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AuthInfo) Reset()         { *m = AuthInfo{} }
func (m *AuthInfo) String() string { return proto.CompactTextString(m) }
func (*AuthInfo) ProtoMessage()    {}
func (*AuthInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{21}
}

func (m *AuthInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthInfo.Unmarshal(m, b)
}
func (m *AuthInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthInfo.Marshal(b, m, deterministic)
}
func (m *AuthInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthInfo.Merge(m, src)
}
func (m *AuthInfo) XXX_Size() int {
	return xxx_messageInfo_AuthInfo.Size(m)
}
func (m *AuthInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AuthInfo proto.InternalMessageInfo

func (m *AuthInfo) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *AuthInfo) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *AuthInfo) GetStaffNameCn() string {
	if m != nil {
		return m.StaffNameCn
	}
	return ""
}

func (m *AuthInfo) GetUserMobile() string {
	if m != nil {
		return m.UserMobile
	}
	return ""
}

func (m *AuthInfo) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

func (m *AuthInfo) GetRegionWarehouse() []*RegionWarehouseInfo {
	if m != nil {
		return m.RegionWarehouse
	}
	return nil
}

func (m *AuthInfo) GetPreposeWarehouse() []*PreposeWarehouseInfo {
	if m != nil {
		return m.PreposeWarehouse
	}
	return nil
}

func (m *AuthInfo) GetButtonPermission() int32 {
	if m != nil {
		return m.ButtonPermission
	}
	return 0
}

func (m *AuthInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AuthInfo) GetDelProPermission() int32 {
	if m != nil {
		return m.DelProPermission
	}
	return 0
}

// 区域仓信息
type RegionWarehouseInfo struct {
	// 仓库id
	WarehouseId int32 `protobuf:"varint,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 仓库编码
	WarehouseCode string `protobuf:"bytes,2,opt,name=warehouse_code,json=warehouseCode,proto3" json:"warehouse_code"`
	// 仓库名称
	WarehouseName        string   `protobuf:"bytes,3,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegionWarehouseInfo) Reset()         { *m = RegionWarehouseInfo{} }
func (m *RegionWarehouseInfo) String() string { return proto.CompactTextString(m) }
func (*RegionWarehouseInfo) ProtoMessage()    {}
func (*RegionWarehouseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{22}
}

func (m *RegionWarehouseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegionWarehouseInfo.Unmarshal(m, b)
}
func (m *RegionWarehouseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegionWarehouseInfo.Marshal(b, m, deterministic)
}
func (m *RegionWarehouseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionWarehouseInfo.Merge(m, src)
}
func (m *RegionWarehouseInfo) XXX_Size() int {
	return xxx_messageInfo_RegionWarehouseInfo.Size(m)
}
func (m *RegionWarehouseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionWarehouseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RegionWarehouseInfo proto.InternalMessageInfo

func (m *RegionWarehouseInfo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *RegionWarehouseInfo) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *RegionWarehouseInfo) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

// 前置仓信息
type PreposeWarehouseInfo struct {
	// 仓库id
	WarehouseId int32 `protobuf:"varint,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 仓库编码
	WarehouseCode string `protobuf:"bytes,2,opt,name=warehouse_code,json=warehouseCode,proto3" json:"warehouse_code"`
	// 仓库名称
	WarehouseName        string   `protobuf:"bytes,3,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreposeWarehouseInfo) Reset()         { *m = PreposeWarehouseInfo{} }
func (m *PreposeWarehouseInfo) String() string { return proto.CompactTextString(m) }
func (*PreposeWarehouseInfo) ProtoMessage()    {}
func (*PreposeWarehouseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{23}
}

func (m *PreposeWarehouseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreposeWarehouseInfo.Unmarshal(m, b)
}
func (m *PreposeWarehouseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreposeWarehouseInfo.Marshal(b, m, deterministic)
}
func (m *PreposeWarehouseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreposeWarehouseInfo.Merge(m, src)
}
func (m *PreposeWarehouseInfo) XXX_Size() int {
	return xxx_messageInfo_PreposeWarehouseInfo.Size(m)
}
func (m *PreposeWarehouseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PreposeWarehouseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PreposeWarehouseInfo proto.InternalMessageInfo

func (m *PreposeWarehouseInfo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *PreposeWarehouseInfo) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *PreposeWarehouseInfo) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

type ResponseData struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Data                 string   `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Msg                  string   `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResponseData) Reset()         { *m = ResponseData{} }
func (m *ResponseData) String() string { return proto.CompactTextString(m) }
func (*ResponseData) ProtoMessage()    {}
func (*ResponseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{24}
}

func (m *ResponseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResponseData.Unmarshal(m, b)
}
func (m *ResponseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResponseData.Marshal(b, m, deterministic)
}
func (m *ResponseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResponseData.Merge(m, src)
}
func (m *ResponseData) XXX_Size() int {
	return xxx_messageInfo_ResponseData.Size(m)
}
func (m *ResponseData) XXX_DiscardUnknown() {
	xxx_messageInfo_ResponseData.DiscardUnknown(m)
}

var xxx_messageInfo_ResponseData proto.InternalMessageInfo

func (m *ResponseData) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ResponseData) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func (m *ResponseData) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *ResponseData) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type WarehouseConfigurationResponse struct {
	Total                int32                               `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	Data                 *WarehouseRuleConfigurationResponse `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	Msg                  string                              `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
	Code                 int32                               `protobuf:"varint,4,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *WarehouseConfigurationResponse) Reset()         { *m = WarehouseConfigurationResponse{} }
func (m *WarehouseConfigurationResponse) String() string { return proto.CompactTextString(m) }
func (*WarehouseConfigurationResponse) ProtoMessage()    {}
func (*WarehouseConfigurationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{25}
}

func (m *WarehouseConfigurationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseConfigurationResponse.Unmarshal(m, b)
}
func (m *WarehouseConfigurationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseConfigurationResponse.Marshal(b, m, deterministic)
}
func (m *WarehouseConfigurationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseConfigurationResponse.Merge(m, src)
}
func (m *WarehouseConfigurationResponse) XXX_Size() int {
	return xxx_messageInfo_WarehouseConfigurationResponse.Size(m)
}
func (m *WarehouseConfigurationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseConfigurationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseConfigurationResponse proto.InternalMessageInfo

func (m *WarehouseConfigurationResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *WarehouseConfigurationResponse) GetData() *WarehouseRuleConfigurationResponse {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *WarehouseConfigurationResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *WarehouseConfigurationResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

// 仓库配置表
type WarehouseRuleConfigurationResponse struct {
	// id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 仓库的id
	WarehouseId int32 `protobuf:"varint,2,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 安全库存
	SafeStock int32 `protobuf:"varint,3,opt,name=safe_stock,json=safeStock,proto3" json:"safe_stock"`
	//  安全周转天数
	SafeDays int32 `protobuf:"varint,4,opt,name=safe_days,json=safeDays,proto3" json:"safe_days"`
	//  状态 1启用 0 禁用
	Status               int32    `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	CreateDate           string   `protobuf:"bytes,6,opt,name=create_date,json=createDate,proto3" json:"create_date"`
	UpdateDate           string   `protobuf:"bytes,7,opt,name=update_date,json=updateDate,proto3" json:"update_date"`
	Type                 int32    `protobuf:"varint,8,opt,name=type,proto3" json:"type"`
	DeadStock            int32    `protobuf:"varint,9,opt,name=dead_stock,json=deadStock,proto3" json:"dead_stock"`
	DeadStockDays        int32    `protobuf:"varint,10,opt,name=dead_stock_days,json=deadStockDays,proto3" json:"dead_stock_days"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseRuleConfigurationResponse) Reset()         { *m = WarehouseRuleConfigurationResponse{} }
func (m *WarehouseRuleConfigurationResponse) String() string { return proto.CompactTextString(m) }
func (*WarehouseRuleConfigurationResponse) ProtoMessage()    {}
func (*WarehouseRuleConfigurationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{26}
}

func (m *WarehouseRuleConfigurationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseRuleConfigurationResponse.Unmarshal(m, b)
}
func (m *WarehouseRuleConfigurationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseRuleConfigurationResponse.Marshal(b, m, deterministic)
}
func (m *WarehouseRuleConfigurationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseRuleConfigurationResponse.Merge(m, src)
}
func (m *WarehouseRuleConfigurationResponse) XXX_Size() int {
	return xxx_messageInfo_WarehouseRuleConfigurationResponse.Size(m)
}
func (m *WarehouseRuleConfigurationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseRuleConfigurationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseRuleConfigurationResponse proto.InternalMessageInfo

func (m *WarehouseRuleConfigurationResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseRuleConfigurationResponse) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *WarehouseRuleConfigurationResponse) GetSafeStock() int32 {
	if m != nil {
		return m.SafeStock
	}
	return 0
}

func (m *WarehouseRuleConfigurationResponse) GetSafeDays() int32 {
	if m != nil {
		return m.SafeDays
	}
	return 0
}

func (m *WarehouseRuleConfigurationResponse) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WarehouseRuleConfigurationResponse) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

func (m *WarehouseRuleConfigurationResponse) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

func (m *WarehouseRuleConfigurationResponse) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *WarehouseRuleConfigurationResponse) GetDeadStock() int32 {
	if m != nil {
		return m.DeadStock
	}
	return 0
}

func (m *WarehouseRuleConfigurationResponse) GetDeadStockDays() int32 {
	if m != nil {
		return m.DeadStockDays
	}
	return 0
}

type ProductConfigurationResponse struct {
	Total                int32                               `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	Data                 []*ProductRuleConfigurationResponse `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	Msg                  string                              `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
	Code                 int32                               `protobuf:"varint,4,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *ProductConfigurationResponse) Reset()         { *m = ProductConfigurationResponse{} }
func (m *ProductConfigurationResponse) String() string { return proto.CompactTextString(m) }
func (*ProductConfigurationResponse) ProtoMessage()    {}
func (*ProductConfigurationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{27}
}

func (m *ProductConfigurationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductConfigurationResponse.Unmarshal(m, b)
}
func (m *ProductConfigurationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductConfigurationResponse.Marshal(b, m, deterministic)
}
func (m *ProductConfigurationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductConfigurationResponse.Merge(m, src)
}
func (m *ProductConfigurationResponse) XXX_Size() int {
	return xxx_messageInfo_ProductConfigurationResponse.Size(m)
}
func (m *ProductConfigurationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductConfigurationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ProductConfigurationResponse proto.InternalMessageInfo

func (m *ProductConfigurationResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ProductConfigurationResponse) GetData() []*ProductRuleConfigurationResponse {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ProductConfigurationResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ProductConfigurationResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

type ProductResVo struct {
	WarehouseId int32                               `protobuf:"varint,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	Type        int32                               `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	Data        []*ProductRuleConfigurationResponse `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 操作人
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductResVo) Reset()         { *m = ProductResVo{} }
func (m *ProductResVo) String() string { return proto.CompactTextString(m) }
func (*ProductResVo) ProtoMessage()    {}
func (*ProductResVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{28}
}

func (m *ProductResVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductResVo.Unmarshal(m, b)
}
func (m *ProductResVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductResVo.Marshal(b, m, deterministic)
}
func (m *ProductResVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductResVo.Merge(m, src)
}
func (m *ProductResVo) XXX_Size() int {
	return xxx_messageInfo_ProductResVo.Size(m)
}
func (m *ProductResVo) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductResVo.DiscardUnknown(m)
}

var xxx_messageInfo_ProductResVo proto.InternalMessageInfo

func (m *ProductResVo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *ProductResVo) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ProductResVo) GetData() []*ProductRuleConfigurationResponse {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ProductResVo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 商品规则配置表
type ProductRuleConfigurationResponse struct {
	Id          int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	WarehouseId int32  `protobuf:"varint,2,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	Type        int32  `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	SkuId       string `protobuf:"bytes,4,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	ThirdSkuId  string `protobuf:"bytes,5,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	// 商品名称
	ProductName string `protobuf:"bytes,6,opt,name=product_name,json=productName,proto3" json:"product_name"`
	//   安全库存
	SafeStock int32 `protobuf:"varint,7,opt,name=safe_stock,json=safeStock,proto3" json:"safe_stock"`
	//   安全周转天数
	SafeDays int32 `protobuf:"varint,8,opt,name=safe_days,json=safeDays,proto3" json:"safe_days"`
	//   滞销库存
	DeadStock int32 `protobuf:"varint,9,opt,name=dead_stock,json=deadStock,proto3" json:"dead_stock"`
	//   滞销周转天数
	DeadStockDays int32 `protobuf:"varint,10,opt,name=dead_stock_days,json=deadStockDays,proto3" json:"dead_stock_days"`
	//   最小补货数
	MinimumOrderQuantity int32 `protobuf:"varint,11,opt,name=minimum_order_quantity,json=minimumOrderQuantity,proto3" json:"minimum_order_quantity"`
	//   规则状态 1启用 2 禁用
	Status     int32  `protobuf:"varint,12,opt,name=status,proto3" json:"status"`
	Operator   string `protobuf:"bytes,13,opt,name=operator,proto3" json:"operator"`
	CreateDate string `protobuf:"bytes,14,opt,name=create_date,json=createDate,proto3" json:"create_date"`
	UpdateDate string `protobuf:"bytes,15,opt,name=update_date,json=updateDate,proto3" json:"update_date"`
	// 补货系数
	ReplenishmentRate    float32  `protobuf:"fixed32,16,opt,name=replenishment_rate,json=replenishmentRate,proto3" json:"replenishment_rate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductRuleConfigurationResponse) Reset()         { *m = ProductRuleConfigurationResponse{} }
func (m *ProductRuleConfigurationResponse) String() string { return proto.CompactTextString(m) }
func (*ProductRuleConfigurationResponse) ProtoMessage()    {}
func (*ProductRuleConfigurationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{29}
}

func (m *ProductRuleConfigurationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductRuleConfigurationResponse.Unmarshal(m, b)
}
func (m *ProductRuleConfigurationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductRuleConfigurationResponse.Marshal(b, m, deterministic)
}
func (m *ProductRuleConfigurationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductRuleConfigurationResponse.Merge(m, src)
}
func (m *ProductRuleConfigurationResponse) XXX_Size() int {
	return xxx_messageInfo_ProductRuleConfigurationResponse.Size(m)
}
func (m *ProductRuleConfigurationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductRuleConfigurationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ProductRuleConfigurationResponse proto.InternalMessageInfo

func (m *ProductRuleConfigurationResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ProductRuleConfigurationResponse) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *ProductRuleConfigurationResponse) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ProductRuleConfigurationResponse) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *ProductRuleConfigurationResponse) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *ProductRuleConfigurationResponse) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *ProductRuleConfigurationResponse) GetSafeStock() int32 {
	if m != nil {
		return m.SafeStock
	}
	return 0
}

func (m *ProductRuleConfigurationResponse) GetSafeDays() int32 {
	if m != nil {
		return m.SafeDays
	}
	return 0
}

func (m *ProductRuleConfigurationResponse) GetDeadStock() int32 {
	if m != nil {
		return m.DeadStock
	}
	return 0
}

func (m *ProductRuleConfigurationResponse) GetDeadStockDays() int32 {
	if m != nil {
		return m.DeadStockDays
	}
	return 0
}

func (m *ProductRuleConfigurationResponse) GetMinimumOrderQuantity() int32 {
	if m != nil {
		return m.MinimumOrderQuantity
	}
	return 0
}

func (m *ProductRuleConfigurationResponse) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ProductRuleConfigurationResponse) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ProductRuleConfigurationResponse) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

func (m *ProductRuleConfigurationResponse) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

func (m *ProductRuleConfigurationResponse) GetReplenishmentRate() float32 {
	if m != nil {
		return m.ReplenishmentRate
	}
	return 0
}

type ProductRuleConfigurationVo struct {
	WarehouseId          int32    `protobuf:"varint,1,opt,name=WarehouseId,proto3" json:"WarehouseId"`
	Type                 int32    `protobuf:"varint,2,opt,name=Type,proto3" json:"Type"`
	SkuId                string   `protobuf:"bytes,3,opt,name=SkuId,proto3" json:"SkuId"`
	ThirdSkuId           string   `protobuf:"bytes,4,opt,name=ThirdSkuId,proto3" json:"ThirdSkuId"`
	Status               int32    `protobuf:"varint,5,opt,name=Status,proto3" json:"Status"`
	PageIndex            int32    `protobuf:"varint,6,opt,name=PageIndex,proto3" json:"PageIndex"`
	PageSize             int32    `protobuf:"varint,7,opt,name=PageSize,proto3" json:"PageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductRuleConfigurationVo) Reset()         { *m = ProductRuleConfigurationVo{} }
func (m *ProductRuleConfigurationVo) String() string { return proto.CompactTextString(m) }
func (*ProductRuleConfigurationVo) ProtoMessage()    {}
func (*ProductRuleConfigurationVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{30}
}

func (m *ProductRuleConfigurationVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductRuleConfigurationVo.Unmarshal(m, b)
}
func (m *ProductRuleConfigurationVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductRuleConfigurationVo.Marshal(b, m, deterministic)
}
func (m *ProductRuleConfigurationVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductRuleConfigurationVo.Merge(m, src)
}
func (m *ProductRuleConfigurationVo) XXX_Size() int {
	return xxx_messageInfo_ProductRuleConfigurationVo.Size(m)
}
func (m *ProductRuleConfigurationVo) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductRuleConfigurationVo.DiscardUnknown(m)
}

var xxx_messageInfo_ProductRuleConfigurationVo proto.InternalMessageInfo

func (m *ProductRuleConfigurationVo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *ProductRuleConfigurationVo) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ProductRuleConfigurationVo) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *ProductRuleConfigurationVo) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *ProductRuleConfigurationVo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ProductRuleConfigurationVo) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ProductRuleConfigurationVo) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type EffectiveManagementResponseData struct {
	Total                int32                          `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	Msg                  string                         `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Error                string                         `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 []*EffectiveManagementResponse `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *EffectiveManagementResponseData) Reset()         { *m = EffectiveManagementResponseData{} }
func (m *EffectiveManagementResponseData) String() string { return proto.CompactTextString(m) }
func (*EffectiveManagementResponseData) ProtoMessage()    {}
func (*EffectiveManagementResponseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{31}
}

func (m *EffectiveManagementResponseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EffectiveManagementResponseData.Unmarshal(m, b)
}
func (m *EffectiveManagementResponseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EffectiveManagementResponseData.Marshal(b, m, deterministic)
}
func (m *EffectiveManagementResponseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EffectiveManagementResponseData.Merge(m, src)
}
func (m *EffectiveManagementResponseData) XXX_Size() int {
	return xxx_messageInfo_EffectiveManagementResponseData.Size(m)
}
func (m *EffectiveManagementResponseData) XXX_DiscardUnknown() {
	xxx_messageInfo_EffectiveManagementResponseData.DiscardUnknown(m)
}

var xxx_messageInfo_EffectiveManagementResponseData proto.InternalMessageInfo

func (m *EffectiveManagementResponseData) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *EffectiveManagementResponseData) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *EffectiveManagementResponseData) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *EffectiveManagementResponseData) GetData() []*EffectiveManagementResponse {
	if m != nil {
		return m.Data
	}
	return nil
}

// 效期列表返回
type EffectiveManagementResponse struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// skuid
	SkuId string `protobuf:"bytes,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 第三方货号
	ThirdSkuId string `protobuf:"bytes,3,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	// 产品名称
	ProductName string `protobuf:"bytes,4,opt,name=product_name,json=productName,proto3" json:"product_name"`
	WarehouseId int32  `protobuf:"varint,5,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 仓库名称
	WarehouseName string `protobuf:"bytes,6,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	// 入库数量
	StorageStock int32 `protobuf:"varint,7,opt,name=storage_stock,json=storageStock,proto3" json:"storage_stock"`
	//入库的单号
	StorageOrder string `protobuf:"bytes,8,opt,name=storage_order,json=storageOrder,proto3" json:"storage_order"`
	// 入库时间
	StorageDate string `protobuf:"bytes,9,opt,name=storage_date,json=storageDate,proto3" json:"storage_date"`
	// 生产时间
	ProductionDate string `protobuf:"bytes,10,opt,name=ProductionDate,proto3" json:"ProductionDate"`
	// 生产批次
	ProductionBatch string `protobuf:"bytes,11,opt,name=ProductionBatch,proto3" json:"ProductionBatch"`
	// 失效时间
	ExpirationDate string `protobuf:"bytes,12,opt,name=ExpirationDate,proto3" json:"ExpirationDate"`
	// 效期天数
	EffectiveDays int32 `protobuf:"varint,13,opt,name=effective_days,json=effectiveDays,proto3" json:"effective_days"`
	// 1优先出货 2重点关注 3二级预警 4一级预警 5即将过期 6已过期
	EffectiveState int32 `protobuf:"varint,14,opt,name=effective_state,json=effectiveState,proto3" json:"effective_state"`
	// 当前库存
	CurrentStock int32 `protobuf:"varint,15,opt,name=current_stock,json=currentStock,proto3" json:"current_stock"`
	// 已售库存
	OffStock int32 `protobuf:"varint,16,opt,name=off_stock,json=offStock,proto3" json:"off_stock"`
	// 导入时间
	CreateDate string `protobuf:"bytes,17,opt,name=create_date,json=createDate,proto3" json:"create_date"`
	// 更新时间
	UpdateDate           string   `protobuf:"bytes,18,opt,name=update_date,json=updateDate,proto3" json:"update_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EffectiveManagementResponse) Reset()         { *m = EffectiveManagementResponse{} }
func (m *EffectiveManagementResponse) String() string { return proto.CompactTextString(m) }
func (*EffectiveManagementResponse) ProtoMessage()    {}
func (*EffectiveManagementResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{32}
}

func (m *EffectiveManagementResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EffectiveManagementResponse.Unmarshal(m, b)
}
func (m *EffectiveManagementResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EffectiveManagementResponse.Marshal(b, m, deterministic)
}
func (m *EffectiveManagementResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EffectiveManagementResponse.Merge(m, src)
}
func (m *EffectiveManagementResponse) XXX_Size() int {
	return xxx_messageInfo_EffectiveManagementResponse.Size(m)
}
func (m *EffectiveManagementResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EffectiveManagementResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EffectiveManagementResponse proto.InternalMessageInfo

func (m *EffectiveManagementResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *EffectiveManagementResponse) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *EffectiveManagementResponse) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *EffectiveManagementResponse) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *EffectiveManagementResponse) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *EffectiveManagementResponse) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

func (m *EffectiveManagementResponse) GetStorageStock() int32 {
	if m != nil {
		return m.StorageStock
	}
	return 0
}

func (m *EffectiveManagementResponse) GetStorageOrder() string {
	if m != nil {
		return m.StorageOrder
	}
	return ""
}

func (m *EffectiveManagementResponse) GetStorageDate() string {
	if m != nil {
		return m.StorageDate
	}
	return ""
}

func (m *EffectiveManagementResponse) GetProductionDate() string {
	if m != nil {
		return m.ProductionDate
	}
	return ""
}

func (m *EffectiveManagementResponse) GetProductionBatch() string {
	if m != nil {
		return m.ProductionBatch
	}
	return ""
}

func (m *EffectiveManagementResponse) GetExpirationDate() string {
	if m != nil {
		return m.ExpirationDate
	}
	return ""
}

func (m *EffectiveManagementResponse) GetEffectiveDays() int32 {
	if m != nil {
		return m.EffectiveDays
	}
	return 0
}

func (m *EffectiveManagementResponse) GetEffectiveState() int32 {
	if m != nil {
		return m.EffectiveState
	}
	return 0
}

func (m *EffectiveManagementResponse) GetCurrentStock() int32 {
	if m != nil {
		return m.CurrentStock
	}
	return 0
}

func (m *EffectiveManagementResponse) GetOffStock() int32 {
	if m != nil {
		return m.OffStock
	}
	return 0
}

func (m *EffectiveManagementResponse) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

func (m *EffectiveManagementResponse) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

type EffectiveManagementResVo struct {
	WarehouseId string `protobuf:"bytes,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	SkuId       string `protobuf:"bytes,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	ThirdSkuId  string `protobuf:"bytes,3,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	Status      int32  `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	StartDay    int32  `protobuf:"varint,5,opt,name=start_day,json=startDay,proto3" json:"start_day"`
	EndDay      int32  `protobuf:"varint,6,opt,name=end_day,json=endDay,proto3" json:"end_day"`
	PageSize    int32  `protobuf:"varint,7,opt,name=pageSize,proto3" json:"pageSize"`
	PageIndex   int32  `protobuf:"varint,8,opt,name=PageIndex,proto3" json:"PageIndex"`
	// 过滤权限
	Operator             string   `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EffectiveManagementResVo) Reset()         { *m = EffectiveManagementResVo{} }
func (m *EffectiveManagementResVo) String() string { return proto.CompactTextString(m) }
func (*EffectiveManagementResVo) ProtoMessage()    {}
func (*EffectiveManagementResVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{33}
}

func (m *EffectiveManagementResVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EffectiveManagementResVo.Unmarshal(m, b)
}
func (m *EffectiveManagementResVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EffectiveManagementResVo.Marshal(b, m, deterministic)
}
func (m *EffectiveManagementResVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EffectiveManagementResVo.Merge(m, src)
}
func (m *EffectiveManagementResVo) XXX_Size() int {
	return xxx_messageInfo_EffectiveManagementResVo.Size(m)
}
func (m *EffectiveManagementResVo) XXX_DiscardUnknown() {
	xxx_messageInfo_EffectiveManagementResVo.DiscardUnknown(m)
}

var xxx_messageInfo_EffectiveManagementResVo proto.InternalMessageInfo

func (m *EffectiveManagementResVo) GetWarehouseId() string {
	if m != nil {
		return m.WarehouseId
	}
	return ""
}

func (m *EffectiveManagementResVo) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *EffectiveManagementResVo) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *EffectiveManagementResVo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *EffectiveManagementResVo) GetStartDay() int32 {
	if m != nil {
		return m.StartDay
	}
	return 0
}

func (m *EffectiveManagementResVo) GetEndDay() int32 {
	if m != nil {
		return m.EndDay
	}
	return 0
}

func (m *EffectiveManagementResVo) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *EffectiveManagementResVo) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *EffectiveManagementResVo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type WarehouseRuleConfigurationVo struct {
	Id          int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	WarehouseId int32 `protobuf:"varint,2,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 安全库存
	SafeStock int32 `protobuf:"varint,3,opt,name=safe_stock,json=safeStock,proto3" json:"safe_stock"`
	// 安全周转天数
	SafeDays int32 `protobuf:"varint,4,opt,name=safe_days,json=safeDays,proto3" json:"safe_days"`
	// '启用1 禁用2'
	Status int32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	// 类型 1:安全规则预警 2:高库存预警
	Type int32 `protobuf:"varint,6,opt,name=type,proto3" json:"type"`
	// 滞销库存数量
	DeadStock int32 `protobuf:"varint,7,opt,name=dead_stock,json=deadStock,proto3" json:"dead_stock"`
	// 滞销库存周转天数
	DeadStockDays        int32    `protobuf:"varint,8,opt,name=dead_stock_days,json=deadStockDays,proto3" json:"dead_stock_days"`
	Operator             string   `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseRuleConfigurationVo) Reset()         { *m = WarehouseRuleConfigurationVo{} }
func (m *WarehouseRuleConfigurationVo) String() string { return proto.CompactTextString(m) }
func (*WarehouseRuleConfigurationVo) ProtoMessage()    {}
func (*WarehouseRuleConfigurationVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{34}
}

func (m *WarehouseRuleConfigurationVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseRuleConfigurationVo.Unmarshal(m, b)
}
func (m *WarehouseRuleConfigurationVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseRuleConfigurationVo.Marshal(b, m, deterministic)
}
func (m *WarehouseRuleConfigurationVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseRuleConfigurationVo.Merge(m, src)
}
func (m *WarehouseRuleConfigurationVo) XXX_Size() int {
	return xxx_messageInfo_WarehouseRuleConfigurationVo.Size(m)
}
func (m *WarehouseRuleConfigurationVo) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseRuleConfigurationVo.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseRuleConfigurationVo proto.InternalMessageInfo

func (m *WarehouseRuleConfigurationVo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseRuleConfigurationVo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *WarehouseRuleConfigurationVo) GetSafeStock() int32 {
	if m != nil {
		return m.SafeStock
	}
	return 0
}

func (m *WarehouseRuleConfigurationVo) GetSafeDays() int32 {
	if m != nil {
		return m.SafeDays
	}
	return 0
}

func (m *WarehouseRuleConfigurationVo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WarehouseRuleConfigurationVo) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *WarehouseRuleConfigurationVo) GetDeadStock() int32 {
	if m != nil {
		return m.DeadStock
	}
	return 0
}

func (m *WarehouseRuleConfigurationVo) GetDeadStockDays() int32 {
	if m != nil {
		return m.DeadStockDays
	}
	return 0
}

func (m *WarehouseRuleConfigurationVo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type GetWarehouseIdVo struct {
	WarehouseId int32 `protobuf:"varint,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 类型 1:安全规则预警 2:高库存预警
	Type                 int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarehouseIdVo) Reset()         { *m = GetWarehouseIdVo{} }
func (m *GetWarehouseIdVo) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseIdVo) ProtoMessage()    {}
func (*GetWarehouseIdVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{35}
}

func (m *GetWarehouseIdVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseIdVo.Unmarshal(m, b)
}
func (m *GetWarehouseIdVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseIdVo.Marshal(b, m, deterministic)
}
func (m *GetWarehouseIdVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseIdVo.Merge(m, src)
}
func (m *GetWarehouseIdVo) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseIdVo.Size(m)
}
func (m *GetWarehouseIdVo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseIdVo.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseIdVo proto.InternalMessageInfo

func (m *GetWarehouseIdVo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *GetWarehouseIdVo) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetChannelProductVo struct {
	SkuId                string   `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	ThirdSkuId           string   `protobuf:"bytes,2,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	Type                 int32    `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	PageIndex            int32    `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	WarehouseId          int32    `protobuf:"varint,6,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelProductVo) Reset()         { *m = GetChannelProductVo{} }
func (m *GetChannelProductVo) String() string { return proto.CompactTextString(m) }
func (*GetChannelProductVo) ProtoMessage()    {}
func (*GetChannelProductVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{36}
}

func (m *GetChannelProductVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelProductVo.Unmarshal(m, b)
}
func (m *GetChannelProductVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelProductVo.Marshal(b, m, deterministic)
}
func (m *GetChannelProductVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelProductVo.Merge(m, src)
}
func (m *GetChannelProductVo) XXX_Size() int {
	return xxx_messageInfo_GetChannelProductVo.Size(m)
}
func (m *GetChannelProductVo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelProductVo.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelProductVo proto.InternalMessageInfo

func (m *GetChannelProductVo) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *GetChannelProductVo) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *GetChannelProductVo) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetChannelProductVo) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetChannelProductVo) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetChannelProductVo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

type GetChannelProductRes struct {
	SkuId                string   `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	ThirdSkuId           string   `protobuf:"bytes,2,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	ProductName          string   `protobuf:"bytes,3,opt,name=product_name,json=productName,proto3" json:"product_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelProductRes) Reset()         { *m = GetChannelProductRes{} }
func (m *GetChannelProductRes) String() string { return proto.CompactTextString(m) }
func (*GetChannelProductRes) ProtoMessage()    {}
func (*GetChannelProductRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{37}
}

func (m *GetChannelProductRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelProductRes.Unmarshal(m, b)
}
func (m *GetChannelProductRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelProductRes.Marshal(b, m, deterministic)
}
func (m *GetChannelProductRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelProductRes.Merge(m, src)
}
func (m *GetChannelProductRes) XXX_Size() int {
	return xxx_messageInfo_GetChannelProductRes.Size(m)
}
func (m *GetChannelProductRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelProductRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelProductRes proto.InternalMessageInfo

func (m *GetChannelProductRes) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *GetChannelProductRes) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *GetChannelProductRes) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

type ChannelProductResponseResponse struct {
	Code                 int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg                  string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 []*GetChannelProductRes `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	Total                int32                   `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ChannelProductResponseResponse) Reset()         { *m = ChannelProductResponseResponse{} }
func (m *ChannelProductResponseResponse) String() string { return proto.CompactTextString(m) }
func (*ChannelProductResponseResponse) ProtoMessage()    {}
func (*ChannelProductResponseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{38}
}

func (m *ChannelProductResponseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductResponseResponse.Unmarshal(m, b)
}
func (m *ChannelProductResponseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductResponseResponse.Marshal(b, m, deterministic)
}
func (m *ChannelProductResponseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductResponseResponse.Merge(m, src)
}
func (m *ChannelProductResponseResponse) XXX_Size() int {
	return xxx_messageInfo_ChannelProductResponseResponse.Size(m)
}
func (m *ChannelProductResponseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductResponseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductResponseResponse proto.InternalMessageInfo

func (m *ChannelProductResponseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChannelProductResponseResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ChannelProductResponseResponse) GetData() []*GetChannelProductRes {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ChannelProductResponseResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DeleteProduct struct {
	Ids                  []int32  `protobuf:"varint,1,rep,packed,name=Ids,proto3" json:"Ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteProduct) Reset()         { *m = DeleteProduct{} }
func (m *DeleteProduct) String() string { return proto.CompactTextString(m) }
func (*DeleteProduct) ProtoMessage()    {}
func (*DeleteProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{39}
}

func (m *DeleteProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteProduct.Unmarshal(m, b)
}
func (m *DeleteProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteProduct.Marshal(b, m, deterministic)
}
func (m *DeleteProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteProduct.Merge(m, src)
}
func (m *DeleteProduct) XXX_Size() int {
	return xxx_messageInfo_DeleteProduct.Size(m)
}
func (m *DeleteProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteProduct.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteProduct proto.InternalMessageInfo

func (m *DeleteProduct) GetIds() []int32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type BatchUpdateProductVo struct {
	Ids                  []int32  `protobuf:"varint,1,rep,packed,name=Ids,proto3" json:"Ids"`
	Status               int32    `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateProductVo) Reset()         { *m = BatchUpdateProductVo{} }
func (m *BatchUpdateProductVo) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateProductVo) ProtoMessage()    {}
func (*BatchUpdateProductVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{40}
}

func (m *BatchUpdateProductVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateProductVo.Unmarshal(m, b)
}
func (m *BatchUpdateProductVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateProductVo.Marshal(b, m, deterministic)
}
func (m *BatchUpdateProductVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateProductVo.Merge(m, src)
}
func (m *BatchUpdateProductVo) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateProductVo.Size(m)
}
func (m *BatchUpdateProductVo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateProductVo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateProductVo proto.InternalMessageInfo

func (m *BatchUpdateProductVo) GetIds() []int32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *BatchUpdateProductVo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type SalesRecordVos struct {
	Data                 []*SalesRecordVo `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SalesRecordVos) Reset()         { *m = SalesRecordVos{} }
func (m *SalesRecordVos) String() string { return proto.CompactTextString(m) }
func (*SalesRecordVos) ProtoMessage()    {}
func (*SalesRecordVos) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{41}
}

func (m *SalesRecordVos) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalesRecordVos.Unmarshal(m, b)
}
func (m *SalesRecordVos) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalesRecordVos.Marshal(b, m, deterministic)
}
func (m *SalesRecordVos) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalesRecordVos.Merge(m, src)
}
func (m *SalesRecordVos) XXX_Size() int {
	return xxx_messageInfo_SalesRecordVos.Size(m)
}
func (m *SalesRecordVos) XXX_DiscardUnknown() {
	xxx_messageInfo_SalesRecordVos.DiscardUnknown(m)
}

var xxx_messageInfo_SalesRecordVos proto.InternalMessageInfo

func (m *SalesRecordVos) GetData() []*SalesRecordVo {
	if m != nil {
		return m.Data
	}
	return nil
}

type SalesRecordVo struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 退款单号
	RefundOrderSn string `protobuf:"bytes,2,opt,name=refund_order_sn,json=refundOrderSn,proto3" json:"refund_order_sn"`
	// skuid
	SkuId string `protobuf:"bytes,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//   status  1: 下单  2 退款
	Status int32 `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	// 下单数量 num
	Num                  int32    `protobuf:"varint,5,opt,name=num,proto3" json:"num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalesRecordVo) Reset()         { *m = SalesRecordVo{} }
func (m *SalesRecordVo) String() string { return proto.CompactTextString(m) }
func (*SalesRecordVo) ProtoMessage()    {}
func (*SalesRecordVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{42}
}

func (m *SalesRecordVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalesRecordVo.Unmarshal(m, b)
}
func (m *SalesRecordVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalesRecordVo.Marshal(b, m, deterministic)
}
func (m *SalesRecordVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalesRecordVo.Merge(m, src)
}
func (m *SalesRecordVo) XXX_Size() int {
	return xxx_messageInfo_SalesRecordVo.Size(m)
}
func (m *SalesRecordVo) XXX_DiscardUnknown() {
	xxx_messageInfo_SalesRecordVo.DiscardUnknown(m)
}

var xxx_messageInfo_SalesRecordVo proto.InternalMessageInfo

func (m *SalesRecordVo) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *SalesRecordVo) GetRefundOrderSn() string {
	if m != nil {
		return m.RefundOrderSn
	}
	return ""
}

func (m *SalesRecordVo) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *SalesRecordVo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SalesRecordVo) GetNum() int32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type CheckSkuidOrThirSkuidVo struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckSkuidOrThirSkuidVo) Reset()         { *m = CheckSkuidOrThirSkuidVo{} }
func (m *CheckSkuidOrThirSkuidVo) String() string { return proto.CompactTextString(m) }
func (*CheckSkuidOrThirSkuidVo) ProtoMessage()    {}
func (*CheckSkuidOrThirSkuidVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{43}
}

func (m *CheckSkuidOrThirSkuidVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSkuidOrThirSkuidVo.Unmarshal(m, b)
}
func (m *CheckSkuidOrThirSkuidVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSkuidOrThirSkuidVo.Marshal(b, m, deterministic)
}
func (m *CheckSkuidOrThirSkuidVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSkuidOrThirSkuidVo.Merge(m, src)
}
func (m *CheckSkuidOrThirSkuidVo) XXX_Size() int {
	return xxx_messageInfo_CheckSkuidOrThirSkuidVo.Size(m)
}
func (m *CheckSkuidOrThirSkuidVo) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSkuidOrThirSkuidVo.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSkuidOrThirSkuidVo proto.InternalMessageInfo

// 初始化折扣数据的接口
type DiscountTaskRequest struct {
	// 开始日期,以当前日期开始往后算30天的折扣数据
	BeginDate            string   `protobuf:"bytes,1,opt,name=begin_date,json=beginDate,proto3" json:"begin_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiscountTaskRequest) Reset()         { *m = DiscountTaskRequest{} }
func (m *DiscountTaskRequest) String() string { return proto.CompactTextString(m) }
func (*DiscountTaskRequest) ProtoMessage()    {}
func (*DiscountTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{44}
}

func (m *DiscountTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscountTaskRequest.Unmarshal(m, b)
}
func (m *DiscountTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscountTaskRequest.Marshal(b, m, deterministic)
}
func (m *DiscountTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscountTaskRequest.Merge(m, src)
}
func (m *DiscountTaskRequest) XXX_Size() int {
	return xxx_messageInfo_DiscountTaskRequest.Size(m)
}
func (m *DiscountTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscountTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DiscountTaskRequest proto.InternalMessageInfo

func (m *DiscountTaskRequest) GetBeginDate() string {
	if m != nil {
		return m.BeginDate
	}
	return ""
}

// 获取折扣数据查询北京的预测数据的接口
type DiscountFromBeijingRequest struct {
	Data                 []*DiscountRequest `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *DiscountFromBeijingRequest) Reset()         { *m = DiscountFromBeijingRequest{} }
func (m *DiscountFromBeijingRequest) String() string { return proto.CompactTextString(m) }
func (*DiscountFromBeijingRequest) ProtoMessage()    {}
func (*DiscountFromBeijingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{45}
}

func (m *DiscountFromBeijingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscountFromBeijingRequest.Unmarshal(m, b)
}
func (m *DiscountFromBeijingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscountFromBeijingRequest.Marshal(b, m, deterministic)
}
func (m *DiscountFromBeijingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscountFromBeijingRequest.Merge(m, src)
}
func (m *DiscountFromBeijingRequest) XXX_Size() int {
	return xxx_messageInfo_DiscountFromBeijingRequest.Size(m)
}
func (m *DiscountFromBeijingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscountFromBeijingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DiscountFromBeijingRequest proto.InternalMessageInfo

func (m *DiscountFromBeijingRequest) GetData() []*DiscountRequest {
	if m != nil {
		return m.Data
	}
	return nil
}

type DiscountRequest struct {
	// sku_id
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 仓库的id
	WarehouseId          string   `protobuf:"bytes,2,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiscountRequest) Reset()         { *m = DiscountRequest{} }
func (m *DiscountRequest) String() string { return proto.CompactTextString(m) }
func (*DiscountRequest) ProtoMessage()    {}
func (*DiscountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{46}
}

func (m *DiscountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscountRequest.Unmarshal(m, b)
}
func (m *DiscountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscountRequest.Marshal(b, m, deterministic)
}
func (m *DiscountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscountRequest.Merge(m, src)
}
func (m *DiscountRequest) XXX_Size() int {
	return xxx_messageInfo_DiscountRequest.Size(m)
}
func (m *DiscountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DiscountRequest proto.InternalMessageInfo

func (m *DiscountRequest) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *DiscountRequest) GetWarehouseId() string {
	if m != nil {
		return m.WarehouseId
	}
	return ""
}

type DisCountBeijingResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 *Resp    `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisCountBeijingResponse) Reset()         { *m = DisCountBeijingResponse{} }
func (m *DisCountBeijingResponse) String() string { return proto.CompactTextString(m) }
func (*DisCountBeijingResponse) ProtoMessage()    {}
func (*DisCountBeijingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{47}
}

func (m *DisCountBeijingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisCountBeijingResponse.Unmarshal(m, b)
}
func (m *DisCountBeijingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisCountBeijingResponse.Marshal(b, m, deterministic)
}
func (m *DisCountBeijingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisCountBeijingResponse.Merge(m, src)
}
func (m *DisCountBeijingResponse) XXX_Size() int {
	return xxx_messageInfo_DisCountBeijingResponse.Size(m)
}
func (m *DisCountBeijingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DisCountBeijingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DisCountBeijingResponse proto.InternalMessageInfo

func (m *DisCountBeijingResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisCountBeijingResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *DisCountBeijingResponse) GetData() *Resp {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DisCountBeijingResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type Resp struct {
	Status               int32              `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	Contents             []*RespDataBeijing `protobuf:"bytes,2,rep,name=contents,proto3" json:"contents"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *Resp) Reset()         { *m = Resp{} }
func (m *Resp) String() string { return proto.CompactTextString(m) }
func (*Resp) ProtoMessage()    {}
func (*Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{48}
}

func (m *Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Resp.Unmarshal(m, b)
}
func (m *Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Resp.Marshal(b, m, deterministic)
}
func (m *Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Resp.Merge(m, src)
}
func (m *Resp) XXX_Size() int {
	return xxx_messageInfo_Resp.Size(m)
}
func (m *Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_Resp.DiscardUnknown(m)
}

var xxx_messageInfo_Resp proto.InternalMessageInfo

func (m *Resp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Resp) GetContents() []*RespDataBeijing {
	if m != nil {
		return m.Contents
	}
	return nil
}

type RespDataBeijing struct {
	SkuId                int32    `protobuf:"varint,1,opt,name=SkuId,proto3" json:"SkuId"`
	WarehouseId          int32    `protobuf:"varint,2,opt,name=WarehouseId,proto3" json:"WarehouseId"`
	Tag                  string   `protobuf:"bytes,3,opt,name=Tag,proto3" json:"Tag"`
	ForecastDay7         int32    `protobuf:"varint,4,opt,name=ForecastDay7,proto3" json:"ForecastDay7"`
	ForecastDay15        int32    `protobuf:"varint,5,opt,name=ForecastDay15,proto3" json:"ForecastDay15"`
	ForecastDay30        int32    `protobuf:"varint,6,opt,name=ForecastDay30,proto3" json:"ForecastDay30"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RespDataBeijing) Reset()         { *m = RespDataBeijing{} }
func (m *RespDataBeijing) String() string { return proto.CompactTextString(m) }
func (*RespDataBeijing) ProtoMessage()    {}
func (*RespDataBeijing) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{49}
}

func (m *RespDataBeijing) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RespDataBeijing.Unmarshal(m, b)
}
func (m *RespDataBeijing) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RespDataBeijing.Marshal(b, m, deterministic)
}
func (m *RespDataBeijing) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RespDataBeijing.Merge(m, src)
}
func (m *RespDataBeijing) XXX_Size() int {
	return xxx_messageInfo_RespDataBeijing.Size(m)
}
func (m *RespDataBeijing) XXX_DiscardUnknown() {
	xxx_messageInfo_RespDataBeijing.DiscardUnknown(m)
}

var xxx_messageInfo_RespDataBeijing proto.InternalMessageInfo

func (m *RespDataBeijing) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *RespDataBeijing) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *RespDataBeijing) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *RespDataBeijing) GetForecastDay7() int32 {
	if m != nil {
		return m.ForecastDay7
	}
	return 0
}

func (m *RespDataBeijing) GetForecastDay15() int32 {
	if m != nil {
		return m.ForecastDay15
	}
	return 0
}

func (m *RespDataBeijing) GetForecastDay30() int32 {
	if m != nil {
		return m.ForecastDay30
	}
	return 0
}

type IncrementPromotionDiscountRes struct {
	PromotionId          int32    `protobuf:"varint,1,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id"`
	UpdateDate           string   `protobuf:"bytes,2,opt,name=update_date,json=updateDate,proto3" json:"update_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrementPromotionDiscountRes) Reset()         { *m = IncrementPromotionDiscountRes{} }
func (m *IncrementPromotionDiscountRes) String() string { return proto.CompactTextString(m) }
func (*IncrementPromotionDiscountRes) ProtoMessage()    {}
func (*IncrementPromotionDiscountRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{50}
}

func (m *IncrementPromotionDiscountRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrementPromotionDiscountRes.Unmarshal(m, b)
}
func (m *IncrementPromotionDiscountRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrementPromotionDiscountRes.Marshal(b, m, deterministic)
}
func (m *IncrementPromotionDiscountRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrementPromotionDiscountRes.Merge(m, src)
}
func (m *IncrementPromotionDiscountRes) XXX_Size() int {
	return xxx_messageInfo_IncrementPromotionDiscountRes.Size(m)
}
func (m *IncrementPromotionDiscountRes) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrementPromotionDiscountRes.DiscardUnknown(m)
}

var xxx_messageInfo_IncrementPromotionDiscountRes proto.InternalMessageInfo

func (m *IncrementPromotionDiscountRes) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *IncrementPromotionDiscountRes) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

type AddButtonPermissionsVo struct {
	IsPermission         int32    `protobuf:"varint,1,opt,name=is_permission,json=isPermission,proto3" json:"is_permission"`
	Id                   int32    `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	DelProPermission     int32    `protobuf:"varint,3,opt,name=del_pro_permission,json=delProPermission,proto3" json:"del_pro_permission"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddButtonPermissionsVo) Reset()         { *m = AddButtonPermissionsVo{} }
func (m *AddButtonPermissionsVo) String() string { return proto.CompactTextString(m) }
func (*AddButtonPermissionsVo) ProtoMessage()    {}
func (*AddButtonPermissionsVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa987b33dd1e86ea, []int{51}
}

func (m *AddButtonPermissionsVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddButtonPermissionsVo.Unmarshal(m, b)
}
func (m *AddButtonPermissionsVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddButtonPermissionsVo.Marshal(b, m, deterministic)
}
func (m *AddButtonPermissionsVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddButtonPermissionsVo.Merge(m, src)
}
func (m *AddButtonPermissionsVo) XXX_Size() int {
	return xxx_messageInfo_AddButtonPermissionsVo.Size(m)
}
func (m *AddButtonPermissionsVo) XXX_DiscardUnknown() {
	xxx_messageInfo_AddButtonPermissionsVo.DiscardUnknown(m)
}

var xxx_messageInfo_AddButtonPermissionsVo proto.InternalMessageInfo

func (m *AddButtonPermissionsVo) GetIsPermission() int32 {
	if m != nil {
		return m.IsPermission
	}
	return 0
}

func (m *AddButtonPermissionsVo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AddButtonPermissionsVo) GetDelProPermission() int32 {
	if m != nil {
		return m.DelProPermission
	}
	return 0
}

func init() {
	proto.RegisterType((*Empty)(nil), "sv.Empty")
	proto.RegisterType((*BaseResponse)(nil), "sv.BaseResponse")
	proto.RegisterType((*WarehouseLStockistRequest)(nil), "sv.WarehouseLStockistRequest")
	proto.RegisterType((*RegionWarehouseStockResponse)(nil), "sv.RegionWarehouseStockResponse")
	proto.RegisterType((*PreposeWarehouseStockResponse)(nil), "sv.PreposeWarehouseStockResponse")
	proto.RegisterType((*RegionWarehouseStockData)(nil), "sv.RegionWarehouseStockData")
	proto.RegisterType((*PreposeWarehouseStockData)(nil), "sv.PreposeWarehouseStockData")
	proto.RegisterType((*TaskListRequest)(nil), "sv.TaskListRequest")
	proto.RegisterType((*TaskListResponse)(nil), "sv.TaskListResponse")
	proto.RegisterType((*CreateTaskRequest)(nil), "sv.CreateTaskRequest")
	proto.RegisterType((*TaskData)(nil), "sv.TaskData")
	proto.RegisterType((*WarehouseListRequest)(nil), "sv.WarehouseListRequest")
	proto.RegisterType((*WarehouseListResponse)(nil), "sv.WarehouseListResponse")
	proto.RegisterType((*WarehouseList)(nil), "sv.WarehouseList")
	proto.RegisterType((*AddAuthInfoRequest)(nil), "sv.AddAuthInfoRequest")
	proto.RegisterType((*AddAuthRequstList)(nil), "sv.AddAuthRequstList")
	proto.RegisterType((*UpdateAuthInfoRequest)(nil), "sv.UpdateAuthInfoRequest")
	proto.RegisterType((*AuthInfoDetailRequest)(nil), "sv.AuthInfoDetailRequest")
	proto.RegisterType((*AuthInfoDetailResponse)(nil), "sv.AuthInfoDetailResponse")
	proto.RegisterType((*AuthInfoListRequest)(nil), "sv.AuthInfoListRequest")
	proto.RegisterType((*AuthInfoListResponse)(nil), "sv.AuthInfoListResponse")
	proto.RegisterType((*AuthInfo)(nil), "sv.AuthInfo")
	proto.RegisterType((*RegionWarehouseInfo)(nil), "sv.RegionWarehouseInfo")
	proto.RegisterType((*PreposeWarehouseInfo)(nil), "sv.PreposeWarehouseInfo")
	proto.RegisterType((*ResponseData)(nil), "sv.ResponseData")
	proto.RegisterType((*WarehouseConfigurationResponse)(nil), "sv.WarehouseConfigurationResponse")
	proto.RegisterType((*WarehouseRuleConfigurationResponse)(nil), "sv.WarehouseRuleConfigurationResponse")
	proto.RegisterType((*ProductConfigurationResponse)(nil), "sv.ProductConfigurationResponse")
	proto.RegisterType((*ProductResVo)(nil), "sv.ProductResVo")
	proto.RegisterType((*ProductRuleConfigurationResponse)(nil), "sv.ProductRuleConfigurationResponse")
	proto.RegisterType((*ProductRuleConfigurationVo)(nil), "sv.ProductRuleConfigurationVo")
	proto.RegisterType((*EffectiveManagementResponseData)(nil), "sv.EffectiveManagementResponseData")
	proto.RegisterType((*EffectiveManagementResponse)(nil), "sv.EffectiveManagementResponse")
	proto.RegisterType((*EffectiveManagementResVo)(nil), "sv.EffectiveManagementResVo")
	proto.RegisterType((*WarehouseRuleConfigurationVo)(nil), "sv.WarehouseRuleConfigurationVo")
	proto.RegisterType((*GetWarehouseIdVo)(nil), "sv.GetWarehouseIdVo")
	proto.RegisterType((*GetChannelProductVo)(nil), "sv.GetChannelProductVo")
	proto.RegisterType((*GetChannelProductRes)(nil), "sv.GetChannelProductRes")
	proto.RegisterType((*ChannelProductResponseResponse)(nil), "sv.ChannelProductResponseResponse")
	proto.RegisterType((*DeleteProduct)(nil), "sv.DeleteProduct")
	proto.RegisterType((*BatchUpdateProductVo)(nil), "sv.BatchUpdateProductVo")
	proto.RegisterType((*SalesRecordVos)(nil), "sv.SalesRecordVos")
	proto.RegisterType((*SalesRecordVo)(nil), "sv.SalesRecordVo")
	proto.RegisterType((*CheckSkuidOrThirSkuidVo)(nil), "sv.CheckSkuidOrThirSkuidVo")
	proto.RegisterType((*DiscountTaskRequest)(nil), "sv.DiscountTaskRequest")
	proto.RegisterType((*DiscountFromBeijingRequest)(nil), "sv.DiscountFromBeijingRequest")
	proto.RegisterType((*DiscountRequest)(nil), "sv.DiscountRequest")
	proto.RegisterType((*DisCountBeijingResponse)(nil), "sv.DisCountBeijingResponse")
	proto.RegisterType((*Resp)(nil), "sv.Resp")
	proto.RegisterType((*RespDataBeijing)(nil), "sv.RespDataBeijing")
	proto.RegisterType((*IncrementPromotionDiscountRes)(nil), "sv.IncrementPromotionDiscountRes")
	proto.RegisterType((*AddButtonPermissionsVo)(nil), "sv.AddButtonPermissionsVo")
}

func init() { proto.RegisterFile("sv/stockVisual.proto", fileDescriptor_aa987b33dd1e86ea) }

var fileDescriptor_aa987b33dd1e86ea = []byte{
	// 3464 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x5a, 0xcd, 0x73, 0x1b, 0xc7,
	0x95, 0x2f, 0x00, 0x04, 0x09, 0x3c, 0x00, 0x04, 0xd8, 0x04, 0x25, 0x88, 0x22, 0x29, 0x6a, 0x6c,
	0x59, 0x74, 0xad, 0x56, 0xa6, 0xbe, 0x2c, 0x79, 0xb7, 0x5c, 0x6b, 0x89, 0x94, 0x65, 0xee, 0xda,
	0x96, 0x16, 0x90, 0xa8, 0xdd, 0xda, 0xda, 0xc5, 0x8e, 0x30, 0x0d, 0x70, 0x42, 0x60, 0x06, 0x9e,
	0x6e, 0xd0, 0x86, 0x73, 0x4f, 0xaa, 0x52, 0x29, 0x5f, 0x92, 0x43, 0x2a, 0xf1, 0x2d, 0x97, 0x54,
	0xfe, 0x82, 0x5c, 0x73, 0x48, 0x55, 0xaa, 0x72, 0xcb, 0x31, 0x55, 0xbe, 0xe4, 0x7f, 0x48, 0xce,
	0xa9, 0x7e, 0xdd, 0x3d, 0x33, 0x3d, 0x18, 0x7c, 0x50, 0xe5, 0x4a, 0x52, 0xb9, 0xcd, 0xbc, 0x7e,
	0xf3, 0xfa, 0xf5, 0xeb, 0xdf, 0xfb, 0xe8, 0xd7, 0x03, 0x75, 0x76, 0xf6, 0x0e, 0xe3, 0x7e, 0xe7,
	0xf4, 0xd8, 0x65, 0x23, 0xbb, 0x7f, 0x73, 0x18, 0xf8, 0xdc, 0x27, 0x59, 0x76, 0x66, 0xad, 0x40,
	0xfe, 0xf1, 0x60, 0xc8, 0xc7, 0xd6, 0x5d, 0x28, 0x3f, 0xb2, 0x19, 0x6d, 0x52, 0x36, 0xf4, 0x3d,
	0x46, 0x09, 0x81, 0xa5, 0x8e, 0xef, 0xd0, 0x46, 0x66, 0x37, 0xb3, 0x97, 0x6f, 0xe2, 0x33, 0xa9,
	0x41, 0x6e, 0xc0, 0x7a, 0x8d, 0xec, 0x6e, 0x66, 0xaf, 0xd8, 0x14, 0x8f, 0xd6, 0xef, 0xb3, 0x70,
	0xe9, 0xa5, 0x1d, 0xd0, 0x13, 0x7f, 0xc4, 0xe8, 0xc7, 0x2d, 0x31, 0x85, 0xcb, 0x78, 0x93, 0x7e,
	0x36, 0xa2, 0x8c, 0x93, 0xab, 0x50, 0xfe, 0x5c, 0x0f, 0xb6, 0x5d, 0xa7, 0x91, 0xd9, 0xcd, 0xed,
	0xe5, 0x9b, 0xa5, 0x90, 0x76, 0xe4, 0x90, 0x0d, 0x58, 0x66, 0xa7, 0x23, 0x31, 0x98, 0xc5, 0x89,
	0xf2, 0xec, 0x74, 0x74, 0xe4, 0x90, 0x5d, 0x28, 0xf3, 0x13, 0x37, 0x70, 0xda, 0x6a, 0x30, 0x87,
	0x53, 0x02, 0xd2, 0x5a, 0xc8, 0xb1, 0x0d, 0xe0, 0xb2, 0xf6, 0xe7, 0x76, 0xe0, 0xb9, 0x5e, 0xaf,
	0xb1, 0x84, 0x1f, 0x17, 0x5d, 0xf6, 0x52, 0x12, 0xc8, 0x65, 0x28, 0xe2, 0x82, 0xdb, 0xde, 0x68,
	0xd0, 0xc8, 0xe3, 0xbc, 0x05, 0x24, 0x7c, 0x3a, 0x1a, 0x88, 0xc1, 0x9e, 0xef, 0x3b, 0x0c, 0x07,
	0x97, 0xe5, 0x20, 0x12, 0xc4, 0xa0, 0x58, 0xb8, 0xcb, 0xc7, 0x8d, 0x15, 0x9c, 0x12, 0x9f, 0xc5,
	0x64, 0x43, 0xbb, 0x47, 0xdb, 0xae, 0xe7, 0xd0, 0x2f, 0x1a, 0x05, 0x39, 0x99, 0xa0, 0x1c, 0x09,
	0x82, 0x90, 0x87, 0xc3, 0xcc, 0xfd, 0x92, 0x36, 0x8a, 0x38, 0x5a, 0x10, 0x84, 0x96, 0xfb, 0x25,
	0x25, 0xfb, 0x50, 0x7f, 0xe5, 0xfb, 0xa7, 0x6d, 0xd7, 0x3b, 0xa3, 0x1e, 0xf7, 0x83, 0x71, 0xdb,
	0x0f, 0x1c, 0x1a, 0x34, 0x00, 0xf9, 0x88, 0x18, 0x3b, 0xd2, 0x43, 0x4f, 0xc5, 0x88, 0xf5, 0xc3,
	0x0c, 0x6c, 0x35, 0x69, 0xcf, 0xf5, 0xbd, 0xd0, 0xb4, 0x68, 0xd9, 0x99, 0x7b, 0xb3, 0x0f, 0x4b,
	0x8e, 0xcd, 0xed, 0x46, 0x76, 0x37, 0xb7, 0x57, 0xba, 0xbd, 0x75, 0x93, 0x9d, 0xdd, 0x4c, 0x93,
	0x71, 0x68, 0x73, 0xbb, 0x89, 0x9c, 0x7a, 0x37, 0x73, 0xe1, 0x6e, 0x92, 0x3a, 0xe4, 0xb9, 0xcf,
	0xed, 0xbe, 0x32, 0xa7, 0x7c, 0xb1, 0xbe, 0xca, 0xc0, 0xf6, 0xb3, 0x80, 0x0e, 0x7d, 0x46, 0xcf,
	0xa1, 0xcf, 0x04, 0x56, 0x22, 0xe9, 0xb9, 0x98, 0x74, 0x72, 0x4b, 0xe9, 0xbd, 0x84, 0x7a, 0x6f,
	0x0b, 0xbd, 0x53, 0x27, 0x8b, 0x14, 0xb7, 0xfe, 0x98, 0x87, 0xc6, 0xb4, 0xb5, 0xc5, 0x00, 0x95,
	0x99, 0x05, 0xa8, 0xec, 0x04, 0xa0, 0xae, 0x42, 0x79, 0x18, 0xf8, 0xce, 0xa8, 0xc3, 0xdb, 0x9e,
	0x3d, 0xa0, 0xca, 0x2e, 0x25, 0x45, 0xfb, 0xd4, 0x1e, 0xd0, 0x09, 0x3c, 0x4b, 0x33, 0x19, 0x78,
	0xbe, 0x06, 0xab, 0x11, 0x0b, 0xca, 0xc9, 0xa3, 0x9c, 0x4a, 0x48, 0x45, 0x49, 0xd7, 0x60, 0xd5,
	0x04, 0x45, 0x63, 0x19, 0x65, 0x55, 0x0c, 0x38, 0x90, 0xeb, 0x50, 0xb5, 0xcf, 0x6c, 0xb7, 0x6f,
	0xbf, 0xea, 0xd3, 0x36, 0xc2, 0x17, 0x61, 0x99, 0x6f, 0xae, 0x86, 0x64, 0x5c, 0x39, 0xd9, 0x83,
	0x1a, 0xb3, 0xbb, 0x8a, 0xa7, 0xdd, 0xa7, 0x67, 0xb4, 0xaf, 0x60, 0xba, 0x2a, 0xe8, 0xc8, 0xf4,
	0xb1, 0xa0, 0x92, 0x37, 0xa0, 0x22, 0x99, 0xb4, 0xeb, 0x48, 0xbc, 0x96, 0x91, 0xa8, 0xbd, 0xe7,
	0x3a, 0x54, 0xd9, 0xa8, 0xd7, 0xa3, 0x8c, 0x53, 0x47, 0xcd, 0x0b, 0x4a, 0x9a, 0x26, 0xcb, 0x79,
	0x77, 0xa1, 0x7c, 0xe2, 0x32, 0x44, 0xf5, 0xfd, 0x36, 0x3b, 0x6b, 0x94, 0x90, 0x0b, 0x14, 0xed,
	0x7e, 0xeb, 0x8c, 0x58, 0x50, 0xd1, 0x1c, 0xb7, 0xee, 0x09, 0x96, 0xb2, 0x34, 0x9a, 0x22, 0xde,
	0xba, 0x67, 0xf2, 0xdc, 0xd9, 0x17, 0x3c, 0x15, 0x83, 0xe7, 0xce, 0xbe, 0xc9, 0xf3, 0x2e, 0xf2,
	0xac, 0x1a, 0x3c, 0xef, 0x26, 0x78, 0xde, 0x43, 0x9e, 0xaa, 0xc1, 0xf3, 0x9e, 0xe0, 0xd9, 0x81,
	0x12, 0xfd, 0x62, 0x48, 0x3b, 0x5c, 0x2a, 0x5c, 0x93, 0xbe, 0x2c, 0x49, 0x42, 0xdf, 0x5d, 0x28,
	0xab, 0x71, 0xa9, 0xee, 0x9a, 0x5c, 0x91, 0xa4, 0xa1, 0xb6, 0x11, 0x87, 0x54, 0x96, 0xc4, 0x39,
	0x50, 0xd7, 0x88, 0x43, 0xaa, 0xba, 0x1e, 0xe7, 0x78, 0xd7, 0xe4, 0x90, 0x8a, 0xd6, 0xe3, 0x1c,
	0x42, 0x4f, 0xeb, 0x9b, 0x02, 0x5c, 0x9a, 0xea, 0x08, 0xff, 0x58, 0x28, 0xbf, 0x0a, 0x65, 0xbf,
	0xd3, 0x19, 0x0d, 0xc7, 0x06, 0xc4, 0x4b, 0x92, 0x26, 0x71, 0x96, 0xe2, 0x08, 0x85, 0x85, 0x1d,
	0xa1, 0x98, 0xea, 0x08, 0x7b, 0x50, 0x3b, 0x71, 0x7b, 0x27, 0x06, 0xa7, 0x02, 0xb9, 0xa0, 0xcf,
	0x72, 0x99, 0x52, 0x8a, 0xcb, 0x48, 0xab, 0x89, 0xc7, 0x36, 0x1f, 0x0f, 0xa9, 0x86, 0xb9, 0xa2,
	0x3d, 0x1f, 0x0f, 0x29, 0xb9, 0x02, 0xa5, 0x00, 0xc3, 0x96, 0x34, 0x59, 0x45, 0x6e, 0x8e, 0x24,
	0xa1, 0xbd, 0x6e, 0xc2, 0xba, 0x62, 0x18, 0xb1, 0xd8, 0x4a, 0x25, 0xd2, 0xd7, 0xe4, 0xd0, 0x0b,
	0x16, 0x2d, 0x36, 0xc5, 0x4d, 0xab, 0x0b, 0xb9, 0x69, 0x6d, 0xbe, 0x9b, 0xae, 0x2d, 0xe0, 0xa6,
	0x64, 0x01, 0x37, 0x5d, 0x5f, 0xc0, 0x4d, 0xeb, 0x73, 0xdd, 0x74, 0x63, 0x9e, 0x9b, 0x5e, 0x98,
	0xeb, 0xa6, 0x17, 0xe7, 0xba, 0x69, 0x63, 0xae, 0x9b, 0x5e, 0x4a, 0xba, 0xa9, 0x48, 0x73, 0xdc,
	0xee, 0x35, 0x36, 0x65, 0x9a, 0xe3, 0x76, 0x8f, 0x5c, 0x83, 0x6a, 0xd7, 0x0f, 0x68, 0xbb, 0x63,
	0x33, 0xde, 0x3e, 0xb4, 0xc7, 0xed, 0xfb, 0x8d, 0xcb, 0x12, 0x2f, 0x82, 0x7c, 0x60, 0x33, 0x7e,
	0x68, 0x8f, 0xef, 0x93, 0xeb, 0x50, 0x33, 0xd9, 0x6e, 0xdd, 0x6b, 0x6c, 0x49, 0xef, 0x88, 0xf1,
	0xdd, 0xba, 0x37, 0xc9, 0x78, 0x67, 0xbf, 0xb1, 0x3d, 0xc1, 0x78, 0x67, 0x9f, 0xdc, 0x00, 0x62,
	0xbb, 0xed, 0x24, 0x20, 0x76, 0x76, 0x33, 0x7b, 0xb9, 0x66, 0xcd, 0x76, 0x5b, 0x06, 0x24, 0xac,
	0x5f, 0x66, 0xa0, 0xfa, 0xdc, 0x66, 0xa7, 0x1f, 0x9b, 0xf5, 0x1a, 0xb7, 0xd9, 0x69, 0xbb, 0xe3,
	0x7b, 0x9c, 0x7a, 0x5c, 0xc5, 0x96, 0x92, 0xa0, 0x1d, 0x48, 0x12, 0xb9, 0x08, 0x2b, 0x23, 0x46,
	0x83, 0xb6, 0xe7, 0xab, 0xe0, 0xb2, 0x2c, 0x5e, 0x3f, 0xf5, 0x13, 0x25, 0x52, 0x6e, 0x66, 0x89,
	0xb4, 0x94, 0x28, 0x91, 0xae, 0x00, 0xce, 0xd1, 0x66, 0xdc, 0xe6, 0x23, 0x86, 0xb1, 0x24, 0xdf,
	0x04, 0x41, 0x6a, 0x21, 0xc5, 0x1a, 0x42, 0x2d, 0xd2, 0xf5, 0x5b, 0x28, 0x3a, 0x76, 0x8d, 0xa2,
	0xa3, 0x2c, 0x8a, 0x0e, 0x21, 0x3f, 0x56, 0x63, 0xfc, 0x22, 0x03, 0x6b, 0x07, 0x01, 0xb5, 0x39,
	0x15, 0x03, 0xe7, 0x30, 0xd0, 0x0d, 0x20, 0xfe, 0x90, 0x06, 0x36, 0x17, 0x6e, 0xdc, 0x75, 0xfb,
	0xb4, 0x3d, 0x0a, 0xfa, 0x4a, 0xa3, 0x5a, 0x38, 0xf2, 0xa1, 0xdb, 0xa7, 0x2f, 0x82, 0xbe, 0x88,
	0x90, 0x81, 0x94, 0xdd, 0x3e, 0xa1, 0xb6, 0x28, 0x0b, 0x65, 0x40, 0xae, 0x28, 0xea, 0x47, 0x48,
	0x14, 0xd6, 0xeb, 0xa0, 0x32, 0x3a, 0x1e, 0x17, 0x9b, 0x05, 0x49, 0x38, 0x72, 0xac, 0x6f, 0xb2,
	0x50, 0xd0, 0xda, 0x93, 0x55, 0xc8, 0x86, 0x49, 0x21, 0xeb, 0x3a, 0x13, 0x1a, 0x67, 0x27, 0x35,
	0x4e, 0x58, 0x3f, 0x97, 0xb4, 0xfe, 0x94, 0x25, 0x2d, 0x2d, 0xbc, 0xa4, 0x7c, 0xda, 0x92, 0xf6,
	0xa0, 0x16, 0x50, 0x36, 0xea, 0x73, 0x1a, 0x89, 0x5c, 0x46, 0xc6, 0x55, 0x45, 0xd7, 0x02, 0xaf,
	0x40, 0x49, 0x2d, 0x9e, 0xbb, 0x03, 0xaa, 0xea, 0x72, 0x90, 0xa4, 0xe7, 0xee, 0x00, 0xe1, 0x33,
	0xf0, 0x1d, 0xb7, 0x3b, 0x96, 0x0c, 0x05, 0xc9, 0x20, 0x49, 0xc8, 0x60, 0x98, 0xaf, 0x68, 0x9a,
	0x2f, 0x5c, 0xbe, 0x43, 0xb9, 0xed, 0xca, 0x14, 0x50, 0x94, 0xcb, 0x3f, 0x44, 0x8a, 0xf5, 0x9b,
	0x0c, 0xd4, 0xa3, 0x33, 0x4e, 0xcc, 0x5d, 0x36, 0xa1, 0xd0, 0xb1, 0x39, 0xed, 0x89, 0xc4, 0x96,
	0x51, 0x52, 0xd5, 0x3b, 0xd9, 0x92, 0x78, 0x97, 0xde, 0x90, 0x8d, 0xbc, 0x01, 0x09, 0xe2, 0x4b,
	0xf1, 0x82, 0xce, 0x90, 0x8b, 0x9c, 0x41, 0xbc, 0x93, 0x06, 0xac, 0x9c, 0xd2, 0xf1, 0x4b, 0x3f,
	0xd0, 0x3b, 0xad, 0x5f, 0xc3, 0x93, 0x49, 0x3e, 0x76, 0x32, 0x79, 0x1b, 0x6a, 0x43, 0x59, 0x25,
	0xb4, 0x43, 0x5d, 0x64, 0x92, 0xad, 0x2a, 0xfa, 0x81, 0x22, 0x5b, 0xbf, 0xca, 0xc0, 0x46, 0x62,
	0x1d, 0x33, 0x5c, 0xa9, 0x01, 0x2b, 0x03, 0xca, 0x98, 0xdd, 0xa3, 0x0a, 0xbc, 0xfa, 0x55, 0xb8,
	0x14, 0x0d, 0x02, 0x5f, 0x43, 0x55, 0xbe, 0x90, 0x1d, 0x00, 0xf4, 0xad, 0x03, 0x7f, 0xe4, 0x71,
	0xe5, 0xe1, 0x31, 0x0a, 0xf9, 0x57, 0xa8, 0x86, 0x93, 0x3f, 0xb4, 0x83, 0xc0, 0x1e, 0xe3, 0xb1,
	0xac, 0x74, 0x7b, 0x4d, 0x78, 0x9f, 0xa9, 0x57, 0x92, 0xd3, 0xfa, 0x73, 0x0e, 0x2a, 0x06, 0xcb,
	0x04, 0xce, 0x1b, 0xb0, 0x82, 0x55, 0x4e, 0x58, 0xf4, 0xe8, 0xd7, 0x70, 0x71, 0x39, 0x65, 0x35,
	0xb1, 0x38, 0x02, 0x4b, 0x98, 0x82, 0xa5, 0x81, 0xf1, 0x19, 0x77, 0xd3, 0x1f, 0xd0, 0x6e, 0xe0,
	0x0f, 0x54, 0x04, 0x0a, 0xdf, 0xc5, 0x92, 0x65, 0x81, 0x20, 0x4d, 0x2b, 0x5f, 0x8c, 0xfd, 0x5f,
	0x51, 0x5f, 0xe8, 0xfd, 0x6f, 0xc0, 0x8a, 0xed, 0x38, 0x01, 0x65, 0x4c, 0xe1, 0x51, 0xbf, 0xca,
	0x79, 0x3c, 0x6e, 0x77, 0x38, 0x0b, 0xb1, 0xa8, 0xde, 0x31, 0x9b, 0x50, 0x8d, 0x41, 0xf1, 0x48,
	0x2e, 0xc0, 0xb2, 0xf2, 0x4b, 0x59, 0x74, 0xa8, 0x37, 0x61, 0x6e, 0x89, 0x60, 0xc7, 0xe6, 0xb2,
	0xd8, 0x08, 0x7d, 0x42, 0x50, 0xc4, 0x2c, 0x7d, 0x9b, 0x71, 0x1c, 0x95, 0x85, 0x46, 0xf8, 0x2e,
	0xb0, 0xc9, 0x46, 0xaf, 0xd8, 0x98, 0x71, 0x3a, 0x50, 0xc5, 0x45, 0x44, 0x10, 0x6b, 0x45, 0x87,
	0x56, 0xa5, 0x84, 0x7c, 0x31, 0x2b, 0x3e, 0xd7, 0xeb, 0xfa, 0x8d, 0xda, 0x6e, 0xce, 0xa8, 0xf8,
	0x8e, 0xbc, 0xae, 0x2f, 0x16, 0xd0, 0xf7, 0x7a, 0x58, 0x3c, 0xe4, 0x9a, 0xe2, 0x11, 0x29, 0x36,
	0xc7, 0x52, 0x41, 0x50, 0x6c, 0x2e, 0x96, 0x24, 0x4b, 0x19, 0xac, 0x0d, 0x8a, 0x4d, 0xf5, 0x16,
	0xc2, 0xbb, 0x1e, 0xc1, 0xdb, 0xfa, 0x37, 0x20, 0x0f, 0x1d, 0xe7, 0xe1, 0x88, 0x9f, 0x08, 0xf1,
	0xda, 0xf1, 0xde, 0x56, 0xe1, 0x3b, 0x83, 0x00, 0xda, 0x10, 0x00, 0x52, 0x5c, 0x82, 0x83, 0x71,
	0x04, 0x91, 0x8c, 0xe3, 0x5f, 0x65, 0x60, 0x6d, 0x62, 0x2c, 0x9e, 0xc5, 0x32, 0x46, 0x16, 0xbb,
	0x0c, 0x45, 0x39, 0x20, 0xd0, 0x21, 0x81, 0x54, 0xc0, 0x21, 0x81, 0x10, 0x4b, 0xd4, 0x81, 0x76,
	0xb7, 0x8b, 0xa3, 0xed, 0x8e, 0xa7, 0x8b, 0x67, 0x24, 0x0a, 0x8e, 0x03, 0x4f, 0x44, 0x13, 0x14,
	0x30, 0xf0, 0x5f, 0xb9, 0x7d, 0x0d, 0x30, 0x10, 0xa4, 0x4f, 0x90, 0x62, 0xb5, 0x60, 0xe3, 0xc5,
	0x50, 0x6c, 0x43, 0x72, 0x51, 0x53, 0x75, 0x4a, 0xd6, 0xe3, 0x52, 0xad, 0x78, 0x3d, 0x6e, 0xed,
	0xc3, 0x86, 0x16, 0x27, 0x83, 0xd6, 0x3c, 0xa1, 0xd6, 0xff, 0xc3, 0x85, 0xe4, 0x17, 0xe7, 0xca,
	0xab, 0x3a, 0x83, 0x0a, 0x13, 0xa8, 0x0c, 0x1a, 0x2e, 0x48, 0x5a, 0xbe, 0x0f, 0xeb, 0x9a, 0x12,
	0x0f, 0x9a, 0x66, 0x9d, 0x90, 0x99, 0x59, 0x27, 0x64, 0x13, 0x75, 0xc2, 0x25, 0x28, 0x74, 0x5d,
	0x4f, 0x54, 0x36, 0x3a, 0xf8, 0xac, 0x88, 0xf7, 0x16, 0x0f, 0x2c, 0x0e, 0x75, 0x73, 0xb6, 0xd7,
	0x5c, 0x4d, 0x2e, 0x7d, 0x35, 0x53, 0x5a, 0x23, 0x5f, 0xe7, 0xa0, 0xa0, 0x19, 0xff, 0x86, 0xa0,
	0x42, 0x06, 0x04, 0x55, 0x1b, 0x1d, 0x3e, 0xaf, 0x18, 0x90, 0x74, 0x28, 0x5c, 0xfe, 0x91, 0xc8,
	0xb6, 0x78, 0xb2, 0x08, 0x61, 0x83, 0x8d, 0xaf, 0xd2, 0xed, 0x8b, 0x29, 0x9d, 0x22, 0x5c, 0x77,
	0x35, 0x30, 0x89, 0xe4, 0x31, 0xac, 0xe9, 0x54, 0x13, 0x09, 0x59, 0x41, 0x21, 0x8d, 0xb4, 0xb6,
	0x0d, 0x4a, 0xd1, 0xd9, 0x29, 0x12, 0xf3, 0x4f, 0xb0, 0xf6, 0x6a, 0xc4, 0xb9, 0xef, 0xb5, 0x87,
	0x34, 0x18, 0xb8, 0x8c, 0x89, 0x48, 0x20, 0x0f, 0x73, 0x35, 0x39, 0xf0, 0x2c, 0xa4, 0xab, 0x30,
	0x5f, 0x0c, 0xc3, 0xfc, 0x0d, 0x20, 0x0e, 0xed, 0xb7, 0x87, 0x81, 0x1f, 0xff, 0x5a, 0x1e, 0xdb,
	0x6a, 0x0e, 0xed, 0x3f, 0x0b, 0xfc, 0xe8, 0x6b, 0xeb, 0x7b, 0x19, 0x58, 0x4f, 0x59, 0x5a, 0x4a,
	0x5f, 0x72, 0xf6, 0x09, 0x17, 0x11, 0x94, 0x4d, 0x9c, 0x70, 0x0f, 0x04, 0x94, 0x26, 0x0f, 0xc2,
	0xb9, 0x94, 0x83, 0xb0, 0xf5, 0xfd, 0x0c, 0xd4, 0xd3, 0xcc, 0xf3, 0xd7, 0xd7, 0xe4, 0xff, 0xa0,
	0xac, 0x7d, 0x03, 0xeb, 0xc5, 0x34, 0xff, 0x20, 0x61, 0x2b, 0x11, 0x03, 0xb1, 0xc6, 0x7f, 0x4a,
	0xd2, 0x57, 0x9e, 0xb4, 0x14, 0x35, 0x84, 0x7f, 0x92, 0x81, 0x9d, 0x97, 0x91, 0x62, 0x5e, 0xd7,
	0xed, 0x8d, 0x64, 0x7d, 0x18, 0xba, 0x64, 0xe8, 0x4a, 0x99, 0x78, 0x49, 0xfe, 0x2f, 0xb1, 0x49,
	0x4b, 0xb7, 0xdf, 0x32, 0x8a, 0x82, 0xe6, 0xa8, 0x9f, 0x2e, 0x6b, 0x6a, 0x27, 0x53, 0x2f, 0x6b,
	0x29, 0x5a, 0x96, 0xf5, 0xdb, 0x2c, 0x58, 0xf3, 0x45, 0xa6, 0x55, 0xd0, 0x13, 0xe1, 0x37, 0xb1,
	0x45, 0xdb, 0x00, 0x51, 0xd3, 0x41, 0x9f, 0x7d, 0xc2, 0x76, 0x03, 0xf6, 0xa2, 0xc5, 0xb0, 0x63,
	0x8f, 0x99, 0x3e, 0xfb, 0x08, 0xc2, 0xa1, 0x3d, 0x66, 0xb1, 0x04, 0x9f, 0x37, 0x12, 0x7c, 0x54,
	0xf5, 0xa2, 0x4b, 0x2f, 0xc7, 0x33, 0x3c, 0xba, 0x74, 0xc2, 0xe7, 0x57, 0x26, 0x7c, 0x9e, 0xc0,
	0x12, 0x76, 0x22, 0xa4, 0x6f, 0xe1, 0xb3, 0xd0, 0xd4, 0xa1, 0xb6, 0x3e, 0x1b, 0x4a, 0xbf, 0x2a,
	0x0a, 0x8a, 0xd4, 0xf4, 0x2d, 0xa8, 0x46, 0xc3, 0x52, 0x5f, 0xe9, 0x5b, 0x95, 0x90, 0x47, 0x28,
	0x6d, 0xfd, 0x38, 0x03, 0x5b, 0xcf, 0x64, 0xcb, 0xe8, 0x3c, 0x7b, 0xfc, 0xc0, 0xe8, 0x51, 0xbf,
	0x29, 0x83, 0x06, 0x4a, 0xf9, 0x76, 0x76, 0xf8, 0x67, 0x19, 0x28, 0x6b, 0x81, 0x94, 0x1d, 0x2f,
	0xe4, 0x5e, 0xda, 0x4a, 0xd9, 0x98, 0x95, 0x1e, 0x18, 0xe9, 0xe0, 0x3c, 0x7a, 0x6e, 0x42, 0x41,
	0x1e, 0x88, 0xfc, 0x40, 0x9f, 0xd3, 0xf4, 0xbb, 0xf5, 0xf3, 0x25, 0xd8, 0x9d, 0x27, 0xe6, 0x75,
	0xd0, 0xa7, 0x57, 0x90, 0x8b, 0xad, 0x20, 0xea, 0x0f, 0x4a, 0x2d, 0xa6, 0xf4, 0x07, 0xf3, 0x73,
	0xfb, 0x83, 0xcb, 0x93, 0xfd, 0x41, 0x13, 0xed, 0x2b, 0x33, 0xd1, 0x5e, 0x48, 0xa0, 0xfd, 0xdb,
	0xc1, 0x1f, 0xb9, 0x0b, 0x17, 0x06, 0xae, 0xe7, 0x0e, 0x46, 0x03, 0x79, 0x99, 0xd2, 0xfe, 0x6c,
	0x64, 0x7b, 0x5c, 0x14, 0x8f, 0xb2, 0x4a, 0xae, 0xab, 0x51, 0xbc, 0x4f, 0xf9, 0x4f, 0x35, 0x16,
	0x73, 0xb5, 0xb2, 0xe1, 0x6a, 0xf1, 0x4d, 0xab, 0x98, 0x9b, 0x96, 0x74, 0xc3, 0xd5, 0x79, 0x6e,
	0x58, 0x9d, 0x70, 0xc3, 0x7f, 0x06, 0x12, 0xd0, 0x61, 0x9f, 0x7a, 0x2e, 0x3b, 0x19, 0x50, 0x8f,
	0xb7, 0x03, 0xc1, 0x57, 0xdb, 0xcd, 0xec, 0x65, 0x9b, 0x6b, 0xc6, 0x48, 0xd3, 0xe6, 0xd4, 0xfa,
	0x43, 0x06, 0x36, 0xa7, 0xa1, 0xe4, 0xd8, 0x27, 0xbb, 0x50, 0x8a, 0x32, 0x48, 0x08, 0xe8, 0x97,
	0x26, 0x1c, 0x9e, 0xc7, 0x00, 0x8d, 0x9d, 0xc7, 0x3a, 0xe4, 0x71, 0x7b, 0x75, 0xf4, 0x96, 0x7b,
	0xbd, 0x03, 0xf0, 0x3c, 0xdc, 0x79, 0x5d, 0x55, 0x44, 0x14, 0x61, 0xaf, 0x96, 0x11, 0x9a, 0x54,
	0x3f, 0x60, 0x0b, 0x8a, 0xcf, 0x74, 0xc1, 0xa6, 0x4e, 0x44, 0x11, 0x41, 0x58, 0xf3, 0x99, 0x2a,
	0xd8, 0xf4, 0xa9, 0x48, 0xbf, 0x8b, 0xb8, 0x71, 0xe5, 0x71, 0xb7, 0x4b, 0x3b, 0xdc, 0x3d, 0xa3,
	0x9f, 0xd8, 0x9e, 0xdd, 0xa3, 0xb8, 0xf0, 0x78, 0x46, 0x4a, 0x0f, 0x1d, 0xa9, 0x9d, 0x9d, 0x94,
	0x8c, 0x74, 0xc7, 0xe8, 0xec, 0x5c, 0x11, 0xae, 0x3b, 0x63, 0x42, 0x7d, 0x48, 0xc8, 0xc3, 0xe5,
	0x19, 0x5c, 0x13, 0x4e, 0x69, 0x5e, 0x5a, 0x16, 0x17, 0xbf, 0xb4, 0x4c, 0x7a, 0xd7, 0xd2, 0xfc,
	0xee, 0x7b, 0x7e, 0x91, 0xee, 0xfb, 0x72, 0x5a, 0xf7, 0x5d, 0xb6, 0xad, 0x03, 0xac, 0xa6, 0x63,
	0xae, 0x5a, 0x56, 0x44, 0xe9, 0x71, 0x31, 0x26, 0x79, 0x2d, 0x29, 0x4f, 0xab, 0x9a, 0x09, 0x1d,
	0x48, 0xe8, 0xa4, 0x99, 0x10, 0xe4, 0x45, 0x5d, 0xa2, 0x22, 0x0d, 0x51, 0xfe, 0x16, 0xac, 0x2a,
	0xd4, 0xba, 0xbe, 0x27, 0x28, 0xea, 0x10, 0x9b, 0xa0, 0x92, 0x3d, 0xa8, 0x46, 0x94, 0x47, 0x36,
	0xef, 0x9c, 0xa0, 0xcb, 0x16, 0x9b, 0x49, 0xb2, 0x90, 0xf8, 0xf8, 0x8b, 0xa1, 0x2b, 0x91, 0x7f,
	0x18, 0x9d, 0x72, 0x13, 0x54, 0x61, 0x0d, 0xaa, 0xf7, 0x4e, 0x86, 0x0c, 0x79, 0x7b, 0x54, 0x09,
	0xa9, 0x18, 0x32, 0xae, 0x43, 0x35, 0x62, 0x13, 0x8e, 0x4f, 0xd5, 0xd1, 0x37, 0xfa, 0x5a, 0xc0,
	0x1b, 0xcd, 0xd6, 0x19, 0x05, 0x81, 0xf0, 0xd4, 0x78, 0x4b, 0xbd, 0xac, 0x88, 0x61, 0x90, 0xf3,
	0xbb, 0x5d, 0xc5, 0x20, 0xbb, 0xe9, 0x05, 0xbf, 0xdb, 0x95, 0x83, 0x89, 0x98, 0xb1, 0x36, 0x2f,
	0x66, 0x90, 0x64, 0xcc, 0xb0, 0x7e, 0x9a, 0x85, 0x46, 0x3a, 0x20, 0xa7, 0x24, 0xb5, 0xe2, 0xac,
	0x5b, 0xf5, 0x73, 0x00, 0x34, 0x0a, 0x91, 0x4b, 0x46, 0x88, 0xc4, 0xeb, 0x74, 0x3b, 0xe0, 0xc2,
	0xc0, 0xba, 0x3b, 0x82, 0x84, 0x43, 0x7b, 0x2c, 0x0e, 0x3e, 0xd4, 0x73, 0x70, 0x48, 0x46, 0x83,
	0x65, 0xea, 0x39, 0x62, 0x40, 0xb5, 0xb9, 0xe2, 0xa1, 0x20, 0x3c, 0xcb, 0x19, 0x41, 0xa4, 0x90,
	0x12, 0x44, 0xc2, 0x90, 0x5c, 0x4c, 0xe4, 0xd1, 0xaf, 0xb3, 0xb0, 0x35, 0xbd, 0x8e, 0x3b, 0xf6,
	0xff, 0x5e, 0x2a, 0x38, 0x9d, 0x97, 0x97, 0xa7, 0xd6, 0x5f, 0x2b, 0x0b, 0xe4, 0xbf, 0x42, 0x5a,
	0xfe, 0x9b, 0x65, 0x9e, 0x23, 0xa8, 0x3d, 0xa1, 0x3c, 0x96, 0x11, 0x5e, 0xbb, 0x0e, 0xb2, 0x7e,
	0x9d, 0x81, 0xf5, 0x27, 0x94, 0x1f, 0x9c, 0xd8, 0x9e, 0x87, 0x67, 0x2b, 0xe1, 0xa0, 0xc7, 0x7e,
	0xe2, 0xf6, 0xb1, 0xb8, 0xf8, 0xed, 0x63, 0x5a, 0xa9, 0x62, 0x36, 0x04, 0x96, 0x66, 0x36, 0x04,
	0xf2, 0x09, 0x10, 0x25, 0xd7, 0xb5, 0x3c, 0xb1, 0x2e, 0x2b, 0x80, 0xfa, 0xc4, 0x12, 0x9a, 0x94,
	0xbd, 0xfe, 0x1a, 0xe6, 0xdf, 0xa0, 0x5a, 0x3f, 0xc8, 0xc0, 0xce, 0xc4, 0x8c, 0x32, 0xe1, 0x9c,
	0xaf, 0x2f, 0x71, 0xc3, 0x28, 0x44, 0xf1, 0x94, 0x9d, 0xb6, 0x98, 0x99, 0x3d, 0x8a, 0xab, 0x50,
	0x39, 0xa4, 0x7d, 0xca, 0xa9, 0xe2, 0x17, 0xd3, 0x1c, 0x39, 0x4c, 0xfd, 0x8c, 0x23, 0x1e, 0xad,
	0x0f, 0xa0, 0x8e, 0x31, 0x57, 0x36, 0xa6, 0xa2, 0x7d, 0x9e, 0xe0, 0x8c, 0x61, 0x3d, 0x1b, 0xc7,
	0xba, 0x75, 0x1f, 0x56, 0x5b, 0x76, 0x9f, 0xb2, 0x26, 0xed, 0xf8, 0x81, 0x73, 0xec, 0x33, 0x72,
	0xcd, 0xe8, 0xd1, 0x61, 0x93, 0xd7, 0xe0, 0x50, 0xa9, 0xf7, 0x47, 0x19, 0xa8, 0x18, 0x74, 0x72,
	0x09, 0x0a, 0xb2, 0xa6, 0x63, 0x9e, 0xda, 0x9a, 0x15, 0x7c, 0x6f, 0x79, 0xc2, 0x3d, 0x02, 0xda,
	0x1d, 0x79, 0x4e, 0x3b, 0xe4, 0xc8, 0xea, 0xbb, 0x05, 0x41, 0x7e, 0xaa, 0xf8, 0xa2, 0xbd, 0xcd,
	0xc5, 0xf7, 0x76, 0x5a, 0x70, 0xab, 0x41, 0x4e, 0xfe, 0x25, 0x24, 0x88, 0xe2, 0xd1, 0xba, 0x04,
	0x17, 0x0f, 0x4e, 0x68, 0xe7, 0xb4, 0x75, 0x3a, 0x72, 0x9d, 0xa7, 0x81, 0xa8, 0x89, 0xf0, 0xf1,
	0xd8, 0xb7, 0xee, 0xc2, 0xfa, 0xa1, 0xcb, 0x3a, 0xfe, 0xc8, 0xe3, 0xf1, 0x9b, 0xa1, 0x6d, 0x80,
	0x57, 0xb4, 0xe7, 0x7a, 0x32, 0xa2, 0x4b, 0xbd, 0x8b, 0x48, 0xc1, 0x80, 0xfe, 0x18, 0x36, 0xf5,
	0x57, 0x1f, 0x06, 0xfe, 0xe0, 0x11, 0x75, 0xbf, 0xe3, 0x7a, 0x3d, 0xfd, 0xf1, 0x75, 0xc3, 0x56,
	0xeb, 0xc2, 0x56, 0x9a, 0x5b, 0xb1, 0x28, 0x6b, 0xfd, 0x07, 0x54, 0x13, 0x03, 0xd3, 0x70, 0xbc,
	0x40, 0xd3, 0x90, 0xc1, 0xc5, 0x43, 0x97, 0x61, 0x77, 0x3e, 0xd4, 0xe7, 0x5c, 0xe8, 0xdc, 0x32,
	0x7a, 0x80, 0x05, 0xd9, 0x48, 0x62, 0xc3, 0x99, 0x68, 0x7c, 0x0a, 0x4b, 0x82, 0x27, 0xb6, 0x17,
	0x19, 0x63, 0x2f, 0xde, 0x91, 0xdd, 0x71, 0xea, 0x71, 0xa6, 0x8e, 0x89, 0xeb, 0x5a, 0xae, 0xa8,
	0x0e, 0xb5, 0xa2, 0x21, 0x93, 0xf5, 0xbb, 0x0c, 0x54, 0x13, 0xa3, 0x51, 0xb9, 0xab, 0x4a, 0xc8,
	0x96, 0x72, 0x6d, 0xa3, 0x74, 0xce, 0x4e, 0x96, 0xce, 0x35, 0xc8, 0x3d, 0xb7, 0xc3, 0x53, 0xe6,
	0x73, 0xbb, 0x47, 0x2c, 0x28, 0x7f, 0xe8, 0x07, 0xb4, 0xa3, 0x6e, 0x6d, 0xd5, 0x5a, 0x0c, 0x1a,
	0x79, 0x13, 0x2a, 0xb1, 0xf7, 0x5b, 0xf7, 0x14, 0x90, 0x4c, 0x62, 0x82, 0xeb, 0xce, 0xbe, 0xfe,
	0x15, 0xc2, 0x20, 0x5a, 0x1d, 0xd8, 0x3e, 0xf2, 0x3a, 0x01, 0x66, 0xfb, 0x67, 0x81, 0x3f, 0xf0,
	0xb1, 0xcc, 0x09, 0xb7, 0x9c, 0xa9, 0xe8, 0x23, 0xe9, 0xb1, 0x48, 0x1e, 0xd2, 0xe4, 0x85, 0x56,
	0xbc, 0xba, 0xc8, 0x4e, 0x54, 0x17, 0xdf, 0x85, 0x0b, 0x0f, 0x1d, 0xe7, 0x51, 0xa2, 0xd7, 0x26,
	0x4a, 0x8b, 0x37, 0xa0, 0xe2, 0xb2, 0x78, 0x67, 0x4d, 0x8a, 0x2f, 0xbb, 0x6c, 0xa2, 0x27, 0x97,
	0x9d, 0xd3, 0x93, 0xcb, 0xa5, 0xf7, 0xe4, 0x6e, 0xff, 0xa9, 0x02, 0xcb, 0xf2, 0x2f, 0x44, 0xf2,
	0x3f, 0xe9, 0xbf, 0x71, 0x61, 0x87, 0x7e, 0xdb, 0xbc, 0x15, 0x4a, 0xfc, 0x59, 0xb8, 0xb9, 0x3b,
	0xed, 0xff, 0xb6, 0x10, 0xc2, 0xff, 0x3b, 0xe5, 0xf7, 0x99, 0x45, 0xa4, 0x5f, 0x9d, 0xfa, 0x17,
	0x5a, 0x28, 0xfe, 0x89, 0x99, 0x65, 0x51, 0x6a, 0x63, 0xf2, 0x26, 0x4b, 0x09, 0xbc, 0x94, 0x32,
	0xa2, 0x04, 0xdd, 0x03, 0x88, 0xee, 0x99, 0x09, 0xde, 0x65, 0x4c, 0xdc, 0x3b, 0x6f, 0xd6, 0x04,
	0xd9, 0xf8, 0x3d, 0xf3, 0x01, 0x94, 0x9e, 0x50, 0xae, 0x2f, 0xc5, 0xc9, 0xba, 0xbe, 0xc2, 0x8e,
	0xcf, 0x5a, 0x37, 0x89, 0xea, 0xcb, 0x16, 0x6c, 0x4f, 0xaf, 0x9e, 0x1e, 0x3a, 0x0e, 0xd9, 0x9d,
	0xdd, 0x7b, 0x3b, 0xf6, 0xa5, 0x3a, 0xc6, 0xa1, 0xed, 0xbf, 0x61, 0x3b, 0x6e, 0x8e, 0x89, 0x8f,
	0x48, 0x5d, 0xe5, 0x2e, 0xa3, 0x2e, 0xd9, 0xb4, 0x8c, 0xa9, 0xd2, 0x3b, 0x22, 0x2d, 0xa8, 0xb7,
	0xa8, 0x1d, 0x74, 0x4e, 0xcc, 0xb4, 0x47, 0x2e, 0xa6, 0x66, 0x43, 0x2d, 0x74, 0x4e, 0xfa, 0x7d,
	0x0c, 0xd5, 0x96, 0x7d, 0x46, 0x63, 0x07, 0x6d, 0x52, 0x8b, 0xb7, 0x79, 0x44, 0xa1, 0x2d, 0x41,
	0x36, 0xb3, 0xcd, 0xf5, 0x11, 0xd4, 0x30, 0x4f, 0xcc, 0x96, 0xb3, 0x88, 0x42, 0x0f, 0x60, 0xcd,
	0xc8, 0xd2, 0x28, 0x0a, 0xb3, 0xa6, 0x41, 0x4e, 0x31, 0xfd, 0x07, 0x40, 0x26, 0x93, 0xb7, 0xc4,
	0x62, 0x5a, 0x52, 0x4f, 0x91, 0x70, 0x0c, 0x44, 0x5a, 0x38, 0x36, 0x37, 0x23, 0x3b, 0xb3, 0xda,
	0x5e, 0x0b, 0x59, 0xe7, 0xbf, 0x60, 0x5d, 0xca, 0x0d, 0x8f, 0x32, 0x88, 0xd5, 0xad, 0xe9, 0x87,
	0xf2, 0x63, 0x7f, 0xf3, 0x8d, 0x39, 0x47, 0x76, 0xd4, 0xf8, 0x3e, 0x94, 0x62, 0xd7, 0x82, 0xe4,
	0x42, 0xec, 0x06, 0x30, 0x76, 0xa5, 0x96, 0xe2, 0x36, 0xef, 0xc3, 0xaa, 0x79, 0xfb, 0x46, 0xd0,
	0x35, 0x53, 0x6f, 0xe4, 0x52, 0x3e, 0x7f, 0x0c, 0x35, 0xc1, 0xf4, 0x82, 0xd1, 0x40, 0xdf, 0x34,
	0x49, 0x1c, 0xa6, 0xdc, 0x74, 0x6d, 0x36, 0x26, 0x07, 0x94, 0x98, 0x7f, 0x87, 0xb5, 0x27, 0x94,
	0x9b, 0xf7, 0x6f, 0x52, 0x91, 0xd4, 0x5b, 0xbc, 0xcd, 0xcd, 0xb4, 0x21, 0x25, 0xeb, 0x2e, 0x6c,
	0x7c, 0x64, 0x7b, 0x8e, 0xfa, 0x25, 0x4c, 0x46, 0x56, 0x0c, 0x25, 0x45, 0x34, 0xe4, 0x60, 0xc8,
	0xc7, 0xa9, 0x76, 0xd8, 0x38, 0xf2, 0x18, 0x0d, 0xf8, 0x91, 0xc7, 0xfd, 0x58, 0xfd, 0x45, 0xc8,
	0x44, 0xa1, 0xc6, 0x52, 0x10, 0xf3, 0x02, 0x2e, 0x3c, 0xa1, 0x3c, 0xa5, 0xa2, 0x91, 0xa8, 0x99,
	0x5e, 0xea, 0x6c, 0x5e, 0x56, 0xe3, 0xa9, 0x65, 0xc7, 0xfb, 0x50, 0x3b, 0xf2, 0x5c, 0x1e, 0xaf,
	0xaf, 0xa4, 0x79, 0x53, 0x2a, 0xae, 0x94, 0x45, 0xbd, 0x80, 0x9d, 0xe9, 0xc9, 0x13, 0x85, 0x61,
	0x60, 0x9f, 0x99, 0x60, 0x53, 0xc4, 0x1e, 0x42, 0x3d, 0x2d, 0x5d, 0x92, 0x4d, 0x85, 0xba, 0x94,
	0x44, 0x3a, 0x29, 0xe5, 0xd5, 0x32, 0xfe, 0x73, 0x7f, 0xe7, 0x2f, 0x01, 0x00, 0x00, 0xff, 0xff,
	0x0e, 0xa7, 0x21, 0xde, 0x8b, 0x2f, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VisualClient is the client API for Visual service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VisualClient interface {
	//区域仓库存列表
	RegionWarehouseStockList(ctx context.Context, in *WarehouseLStockistRequest, opts ...grpc.CallOption) (*RegionWarehouseStockResponse, error)
	//前置仓库存列表
	PreposeWarehouseStockList(ctx context.Context, in *WarehouseLStockistRequest, opts ...grpc.CallOption) (*PreposeWarehouseStockResponse, error)
	// 查询权限下的仓库列表
	GetWarehouseList(ctx context.Context, in *WarehouseListRequest, opts ...grpc.CallOption) (*WarehouseListResponse, error)
	//创建异步导出任务
	CreateTask(ctx context.Context, in *CreateTaskRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//查询异步任务列表
	GetTaskList(ctx context.Context, in *TaskListRequest, opts ...grpc.CallOption) (*TaskListResponse, error)
	// 保存仓库配置信息
	WarehouseRuleConfigurationAdd(ctx context.Context, in *WarehouseRuleConfigurationVo, opts ...grpc.CallOption) (*ResponseData, error)
	// 查询仓库规则信息
	GetWarehouseRuleConfiguration(ctx context.Context, in *GetWarehouseIdVo, opts ...grpc.CallOption) (*WarehouseConfigurationResponse, error)
	// 搜索渠道商品信息
	SearchChannelProduct(ctx context.Context, in *GetChannelProductVo, opts ...grpc.CallOption) (*ChannelProductResponseResponse, error)
	// 保存商品规则信息
	SaveProductRule(ctx context.Context, in *ProductResVo, opts ...grpc.CallOption) (*ProductConfigurationResponse, error)
	// 校验excle商品规则则信息
	CheckProductRule(ctx context.Context, in *ProductResVo, opts ...grpc.CallOption) (*ChannelProductResponseResponse, error)
	// 删除商品信息
	DeleteProductRule(ctx context.Context, in *DeleteProduct, opts ...grpc.CallOption) (*ResponseData, error)
	// 批量启用停用
	BatchUpdateProduct(ctx context.Context, in *BatchUpdateProductVo, opts ...grpc.CallOption) (*ResponseData, error)
	//查询商品信息
	SearchProductRules(ctx context.Context, in *ProductRuleConfigurationVo, opts ...grpc.CallOption) (*ProductConfigurationResponse, error)
	// 查询效期列表信息
	SearchEffectiveList(ctx context.Context, in *EffectiveManagementResVo, opts ...grpc.CallOption) (*EffectiveManagementResponseData, error)
	// 新增权限设置
	AddAuthInfo(ctx context.Context, in *AddAuthInfoRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 新增权限设置
	UpdateAuthInfo(ctx context.Context, in *UpdateAuthInfoRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 新增权限设置
	AuthUserInfoList(ctx context.Context, in *AuthInfoListRequest, opts ...grpc.CallOption) (*AuthInfoListResponse, error)
	// 新增权限设置
	GetAuthInfoDetail(ctx context.Context, in *AuthInfoDetailRequest, opts ...grpc.CallOption) (*AuthInfoDetailResponse, error)
	// 调用处理库存可视化任务
	HandleStockVisualTask(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*BaseResponse, error)
	// rpc 处理下单和退款的保存接口
	InsertIntoSalesRecord(ctx context.Context, in *SalesRecordVos, opts ...grpc.CallOption) (*ResponseData, error)
	// 查询北京的预测销量的接口
	GetDiscountFromBeijing(ctx context.Context, in *DiscountFromBeijingRequest, opts ...grpc.CallOption) (*DisCountBeijingResponse, error)
	// 初始化折扣数据的接口
	InitDiscountTask(ctx context.Context, in *DiscountTaskRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 增量折扣数据处理
	IncrementPromotionDiscountTask(ctx context.Context, in *IncrementPromotionDiscountRes, opts ...grpc.CallOption) (*BaseResponse, error)
	// 增量按钮权限
	AddButtonPermissions(ctx context.Context, in *AddButtonPermissionsVo, opts ...grpc.CallOption) (*BaseResponse, error)
}

type visualClient struct {
	cc *grpc.ClientConn
}

func NewVisualClient(cc *grpc.ClientConn) VisualClient {
	return &visualClient{cc}
}

func (c *visualClient) RegionWarehouseStockList(ctx context.Context, in *WarehouseLStockistRequest, opts ...grpc.CallOption) (*RegionWarehouseStockResponse, error) {
	out := new(RegionWarehouseStockResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/RegionWarehouseStockList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) PreposeWarehouseStockList(ctx context.Context, in *WarehouseLStockistRequest, opts ...grpc.CallOption) (*PreposeWarehouseStockResponse, error) {
	out := new(PreposeWarehouseStockResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/PreposeWarehouseStockList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) GetWarehouseList(ctx context.Context, in *WarehouseListRequest, opts ...grpc.CallOption) (*WarehouseListResponse, error) {
	out := new(WarehouseListResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/GetWarehouseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) CreateTask(ctx context.Context, in *CreateTaskRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/CreateTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) GetTaskList(ctx context.Context, in *TaskListRequest, opts ...grpc.CallOption) (*TaskListResponse, error) {
	out := new(TaskListResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/GetTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) WarehouseRuleConfigurationAdd(ctx context.Context, in *WarehouseRuleConfigurationVo, opts ...grpc.CallOption) (*ResponseData, error) {
	out := new(ResponseData)
	err := c.cc.Invoke(ctx, "/sv.Visual/WarehouseRuleConfigurationAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) GetWarehouseRuleConfiguration(ctx context.Context, in *GetWarehouseIdVo, opts ...grpc.CallOption) (*WarehouseConfigurationResponse, error) {
	out := new(WarehouseConfigurationResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/GetWarehouseRuleConfiguration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) SearchChannelProduct(ctx context.Context, in *GetChannelProductVo, opts ...grpc.CallOption) (*ChannelProductResponseResponse, error) {
	out := new(ChannelProductResponseResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/SearchChannelProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) SaveProductRule(ctx context.Context, in *ProductResVo, opts ...grpc.CallOption) (*ProductConfigurationResponse, error) {
	out := new(ProductConfigurationResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/SaveProductRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) CheckProductRule(ctx context.Context, in *ProductResVo, opts ...grpc.CallOption) (*ChannelProductResponseResponse, error) {
	out := new(ChannelProductResponseResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/CheckProductRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) DeleteProductRule(ctx context.Context, in *DeleteProduct, opts ...grpc.CallOption) (*ResponseData, error) {
	out := new(ResponseData)
	err := c.cc.Invoke(ctx, "/sv.Visual/DeleteProductRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) BatchUpdateProduct(ctx context.Context, in *BatchUpdateProductVo, opts ...grpc.CallOption) (*ResponseData, error) {
	out := new(ResponseData)
	err := c.cc.Invoke(ctx, "/sv.Visual/BatchUpdateProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) SearchProductRules(ctx context.Context, in *ProductRuleConfigurationVo, opts ...grpc.CallOption) (*ProductConfigurationResponse, error) {
	out := new(ProductConfigurationResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/SearchProductRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) SearchEffectiveList(ctx context.Context, in *EffectiveManagementResVo, opts ...grpc.CallOption) (*EffectiveManagementResponseData, error) {
	out := new(EffectiveManagementResponseData)
	err := c.cc.Invoke(ctx, "/sv.Visual/SearchEffectiveList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) AddAuthInfo(ctx context.Context, in *AddAuthInfoRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/AddAuthInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) UpdateAuthInfo(ctx context.Context, in *UpdateAuthInfoRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/UpdateAuthInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) AuthUserInfoList(ctx context.Context, in *AuthInfoListRequest, opts ...grpc.CallOption) (*AuthInfoListResponse, error) {
	out := new(AuthInfoListResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/AuthUserInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) GetAuthInfoDetail(ctx context.Context, in *AuthInfoDetailRequest, opts ...grpc.CallOption) (*AuthInfoDetailResponse, error) {
	out := new(AuthInfoDetailResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/GetAuthInfoDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) HandleStockVisualTask(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/HandleStockVisualTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) InsertIntoSalesRecord(ctx context.Context, in *SalesRecordVos, opts ...grpc.CallOption) (*ResponseData, error) {
	out := new(ResponseData)
	err := c.cc.Invoke(ctx, "/sv.Visual/InsertIntoSalesRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) GetDiscountFromBeijing(ctx context.Context, in *DiscountFromBeijingRequest, opts ...grpc.CallOption) (*DisCountBeijingResponse, error) {
	out := new(DisCountBeijingResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/GetDiscountFromBeijing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) InitDiscountTask(ctx context.Context, in *DiscountTaskRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/InitDiscountTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) IncrementPromotionDiscountTask(ctx context.Context, in *IncrementPromotionDiscountRes, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/IncrementPromotionDiscountTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visualClient) AddButtonPermissions(ctx context.Context, in *AddButtonPermissionsVo, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/sv.Visual/AddButtonPermissions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VisualServer is the server API for Visual service.
type VisualServer interface {
	//区域仓库存列表
	RegionWarehouseStockList(context.Context, *WarehouseLStockistRequest) (*RegionWarehouseStockResponse, error)
	//前置仓库存列表
	PreposeWarehouseStockList(context.Context, *WarehouseLStockistRequest) (*PreposeWarehouseStockResponse, error)
	// 查询权限下的仓库列表
	GetWarehouseList(context.Context, *WarehouseListRequest) (*WarehouseListResponse, error)
	//创建异步导出任务
	CreateTask(context.Context, *CreateTaskRequest) (*BaseResponse, error)
	//查询异步任务列表
	GetTaskList(context.Context, *TaskListRequest) (*TaskListResponse, error)
	// 保存仓库配置信息
	WarehouseRuleConfigurationAdd(context.Context, *WarehouseRuleConfigurationVo) (*ResponseData, error)
	// 查询仓库规则信息
	GetWarehouseRuleConfiguration(context.Context, *GetWarehouseIdVo) (*WarehouseConfigurationResponse, error)
	// 搜索渠道商品信息
	SearchChannelProduct(context.Context, *GetChannelProductVo) (*ChannelProductResponseResponse, error)
	// 保存商品规则信息
	SaveProductRule(context.Context, *ProductResVo) (*ProductConfigurationResponse, error)
	// 校验excle商品规则则信息
	CheckProductRule(context.Context, *ProductResVo) (*ChannelProductResponseResponse, error)
	// 删除商品信息
	DeleteProductRule(context.Context, *DeleteProduct) (*ResponseData, error)
	// 批量启用停用
	BatchUpdateProduct(context.Context, *BatchUpdateProductVo) (*ResponseData, error)
	//查询商品信息
	SearchProductRules(context.Context, *ProductRuleConfigurationVo) (*ProductConfigurationResponse, error)
	// 查询效期列表信息
	SearchEffectiveList(context.Context, *EffectiveManagementResVo) (*EffectiveManagementResponseData, error)
	// 新增权限设置
	AddAuthInfo(context.Context, *AddAuthInfoRequest) (*BaseResponse, error)
	// 新增权限设置
	UpdateAuthInfo(context.Context, *UpdateAuthInfoRequest) (*BaseResponse, error)
	// 新增权限设置
	AuthUserInfoList(context.Context, *AuthInfoListRequest) (*AuthInfoListResponse, error)
	// 新增权限设置
	GetAuthInfoDetail(context.Context, *AuthInfoDetailRequest) (*AuthInfoDetailResponse, error)
	// 调用处理库存可视化任务
	HandleStockVisualTask(context.Context, *Empty) (*BaseResponse, error)
	// rpc 处理下单和退款的保存接口
	InsertIntoSalesRecord(context.Context, *SalesRecordVos) (*ResponseData, error)
	// 查询北京的预测销量的接口
	GetDiscountFromBeijing(context.Context, *DiscountFromBeijingRequest) (*DisCountBeijingResponse, error)
	// 初始化折扣数据的接口
	InitDiscountTask(context.Context, *DiscountTaskRequest) (*BaseResponse, error)
	// 增量折扣数据处理
	IncrementPromotionDiscountTask(context.Context, *IncrementPromotionDiscountRes) (*BaseResponse, error)
	// 增量按钮权限
	AddButtonPermissions(context.Context, *AddButtonPermissionsVo) (*BaseResponse, error)
}

// UnimplementedVisualServer can be embedded to have forward compatible implementations.
type UnimplementedVisualServer struct {
}

func (*UnimplementedVisualServer) RegionWarehouseStockList(ctx context.Context, req *WarehouseLStockistRequest) (*RegionWarehouseStockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegionWarehouseStockList not implemented")
}
func (*UnimplementedVisualServer) PreposeWarehouseStockList(ctx context.Context, req *WarehouseLStockistRequest) (*PreposeWarehouseStockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreposeWarehouseStockList not implemented")
}
func (*UnimplementedVisualServer) GetWarehouseList(ctx context.Context, req *WarehouseListRequest) (*WarehouseListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseList not implemented")
}
func (*UnimplementedVisualServer) CreateTask(ctx context.Context, req *CreateTaskRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTask not implemented")
}
func (*UnimplementedVisualServer) GetTaskList(ctx context.Context, req *TaskListRequest) (*TaskListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskList not implemented")
}
func (*UnimplementedVisualServer) WarehouseRuleConfigurationAdd(ctx context.Context, req *WarehouseRuleConfigurationVo) (*ResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WarehouseRuleConfigurationAdd not implemented")
}
func (*UnimplementedVisualServer) GetWarehouseRuleConfiguration(ctx context.Context, req *GetWarehouseIdVo) (*WarehouseConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseRuleConfiguration not implemented")
}
func (*UnimplementedVisualServer) SearchChannelProduct(ctx context.Context, req *GetChannelProductVo) (*ChannelProductResponseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchChannelProduct not implemented")
}
func (*UnimplementedVisualServer) SaveProductRule(ctx context.Context, req *ProductResVo) (*ProductConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveProductRule not implemented")
}
func (*UnimplementedVisualServer) CheckProductRule(ctx context.Context, req *ProductResVo) (*ChannelProductResponseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckProductRule not implemented")
}
func (*UnimplementedVisualServer) DeleteProductRule(ctx context.Context, req *DeleteProduct) (*ResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProductRule not implemented")
}
func (*UnimplementedVisualServer) BatchUpdateProduct(ctx context.Context, req *BatchUpdateProductVo) (*ResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateProduct not implemented")
}
func (*UnimplementedVisualServer) SearchProductRules(ctx context.Context, req *ProductRuleConfigurationVo) (*ProductConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchProductRules not implemented")
}
func (*UnimplementedVisualServer) SearchEffectiveList(ctx context.Context, req *EffectiveManagementResVo) (*EffectiveManagementResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchEffectiveList not implemented")
}
func (*UnimplementedVisualServer) AddAuthInfo(ctx context.Context, req *AddAuthInfoRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAuthInfo not implemented")
}
func (*UnimplementedVisualServer) UpdateAuthInfo(ctx context.Context, req *UpdateAuthInfoRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuthInfo not implemented")
}
func (*UnimplementedVisualServer) AuthUserInfoList(ctx context.Context, req *AuthInfoListRequest) (*AuthInfoListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthUserInfoList not implemented")
}
func (*UnimplementedVisualServer) GetAuthInfoDetail(ctx context.Context, req *AuthInfoDetailRequest) (*AuthInfoDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthInfoDetail not implemented")
}
func (*UnimplementedVisualServer) HandleStockVisualTask(ctx context.Context, req *Empty) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleStockVisualTask not implemented")
}
func (*UnimplementedVisualServer) InsertIntoSalesRecord(ctx context.Context, req *SalesRecordVos) (*ResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsertIntoSalesRecord not implemented")
}
func (*UnimplementedVisualServer) GetDiscountFromBeijing(ctx context.Context, req *DiscountFromBeijingRequest) (*DisCountBeijingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountFromBeijing not implemented")
}
func (*UnimplementedVisualServer) InitDiscountTask(ctx context.Context, req *DiscountTaskRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitDiscountTask not implemented")
}
func (*UnimplementedVisualServer) IncrementPromotionDiscountTask(ctx context.Context, req *IncrementPromotionDiscountRes) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IncrementPromotionDiscountTask not implemented")
}
func (*UnimplementedVisualServer) AddButtonPermissions(ctx context.Context, req *AddButtonPermissionsVo) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddButtonPermissions not implemented")
}

func RegisterVisualServer(s *grpc.Server, srv VisualServer) {
	s.RegisterService(&_Visual_serviceDesc, srv)
}

func _Visual_RegionWarehouseStockList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseLStockistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).RegionWarehouseStockList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/RegionWarehouseStockList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).RegionWarehouseStockList(ctx, req.(*WarehouseLStockistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_PreposeWarehouseStockList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseLStockistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).PreposeWarehouseStockList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/PreposeWarehouseStockList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).PreposeWarehouseStockList(ctx, req.(*WarehouseLStockistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_GetWarehouseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).GetWarehouseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/GetWarehouseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).GetWarehouseList(ctx, req.(*WarehouseListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_CreateTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).CreateTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/CreateTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).CreateTask(ctx, req.(*CreateTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_GetTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).GetTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/GetTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).GetTaskList(ctx, req.(*TaskListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_WarehouseRuleConfigurationAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseRuleConfigurationVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).WarehouseRuleConfigurationAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/WarehouseRuleConfigurationAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).WarehouseRuleConfigurationAdd(ctx, req.(*WarehouseRuleConfigurationVo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_GetWarehouseRuleConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWarehouseIdVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).GetWarehouseRuleConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/GetWarehouseRuleConfiguration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).GetWarehouseRuleConfiguration(ctx, req.(*GetWarehouseIdVo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_SearchChannelProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelProductVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).SearchChannelProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/SearchChannelProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).SearchChannelProduct(ctx, req.(*GetChannelProductVo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_SaveProductRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductResVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).SaveProductRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/SaveProductRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).SaveProductRule(ctx, req.(*ProductResVo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_CheckProductRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductResVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).CheckProductRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/CheckProductRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).CheckProductRule(ctx, req.(*ProductResVo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_DeleteProductRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteProduct)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).DeleteProductRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/DeleteProductRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).DeleteProductRule(ctx, req.(*DeleteProduct))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_BatchUpdateProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateProductVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).BatchUpdateProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/BatchUpdateProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).BatchUpdateProduct(ctx, req.(*BatchUpdateProductVo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_SearchProductRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductRuleConfigurationVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).SearchProductRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/SearchProductRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).SearchProductRules(ctx, req.(*ProductRuleConfigurationVo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_SearchEffectiveList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EffectiveManagementResVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).SearchEffectiveList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/SearchEffectiveList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).SearchEffectiveList(ctx, req.(*EffectiveManagementResVo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_AddAuthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAuthInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).AddAuthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/AddAuthInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).AddAuthInfo(ctx, req.(*AddAuthInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_UpdateAuthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAuthInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).UpdateAuthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/UpdateAuthInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).UpdateAuthInfo(ctx, req.(*UpdateAuthInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_AuthUserInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthInfoListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).AuthUserInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/AuthUserInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).AuthUserInfoList(ctx, req.(*AuthInfoListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_GetAuthInfoDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthInfoDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).GetAuthInfoDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/GetAuthInfoDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).GetAuthInfoDetail(ctx, req.(*AuthInfoDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_HandleStockVisualTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).HandleStockVisualTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/HandleStockVisualTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).HandleStockVisualTask(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_InsertIntoSalesRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SalesRecordVos)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).InsertIntoSalesRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/InsertIntoSalesRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).InsertIntoSalesRecord(ctx, req.(*SalesRecordVos))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_GetDiscountFromBeijing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscountFromBeijingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).GetDiscountFromBeijing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/GetDiscountFromBeijing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).GetDiscountFromBeijing(ctx, req.(*DiscountFromBeijingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_InitDiscountTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscountTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).InitDiscountTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/InitDiscountTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).InitDiscountTask(ctx, req.(*DiscountTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_IncrementPromotionDiscountTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrementPromotionDiscountRes)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).IncrementPromotionDiscountTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/IncrementPromotionDiscountTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).IncrementPromotionDiscountTask(ctx, req.(*IncrementPromotionDiscountRes))
	}
	return interceptor(ctx, in, info, handler)
}

func _Visual_AddButtonPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddButtonPermissionsVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisualServer).AddButtonPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sv.Visual/AddButtonPermissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisualServer).AddButtonPermissions(ctx, req.(*AddButtonPermissionsVo))
	}
	return interceptor(ctx, in, info, handler)
}

var _Visual_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sv.Visual",
	HandlerType: (*VisualServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RegionWarehouseStockList",
			Handler:    _Visual_RegionWarehouseStockList_Handler,
		},
		{
			MethodName: "PreposeWarehouseStockList",
			Handler:    _Visual_PreposeWarehouseStockList_Handler,
		},
		{
			MethodName: "GetWarehouseList",
			Handler:    _Visual_GetWarehouseList_Handler,
		},
		{
			MethodName: "CreateTask",
			Handler:    _Visual_CreateTask_Handler,
		},
		{
			MethodName: "GetTaskList",
			Handler:    _Visual_GetTaskList_Handler,
		},
		{
			MethodName: "WarehouseRuleConfigurationAdd",
			Handler:    _Visual_WarehouseRuleConfigurationAdd_Handler,
		},
		{
			MethodName: "GetWarehouseRuleConfiguration",
			Handler:    _Visual_GetWarehouseRuleConfiguration_Handler,
		},
		{
			MethodName: "SearchChannelProduct",
			Handler:    _Visual_SearchChannelProduct_Handler,
		},
		{
			MethodName: "SaveProductRule",
			Handler:    _Visual_SaveProductRule_Handler,
		},
		{
			MethodName: "CheckProductRule",
			Handler:    _Visual_CheckProductRule_Handler,
		},
		{
			MethodName: "DeleteProductRule",
			Handler:    _Visual_DeleteProductRule_Handler,
		},
		{
			MethodName: "BatchUpdateProduct",
			Handler:    _Visual_BatchUpdateProduct_Handler,
		},
		{
			MethodName: "SearchProductRules",
			Handler:    _Visual_SearchProductRules_Handler,
		},
		{
			MethodName: "SearchEffectiveList",
			Handler:    _Visual_SearchEffectiveList_Handler,
		},
		{
			MethodName: "AddAuthInfo",
			Handler:    _Visual_AddAuthInfo_Handler,
		},
		{
			MethodName: "UpdateAuthInfo",
			Handler:    _Visual_UpdateAuthInfo_Handler,
		},
		{
			MethodName: "AuthUserInfoList",
			Handler:    _Visual_AuthUserInfoList_Handler,
		},
		{
			MethodName: "GetAuthInfoDetail",
			Handler:    _Visual_GetAuthInfoDetail_Handler,
		},
		{
			MethodName: "HandleStockVisualTask",
			Handler:    _Visual_HandleStockVisualTask_Handler,
		},
		{
			MethodName: "InsertIntoSalesRecord",
			Handler:    _Visual_InsertIntoSalesRecord_Handler,
		},
		{
			MethodName: "GetDiscountFromBeijing",
			Handler:    _Visual_GetDiscountFromBeijing_Handler,
		},
		{
			MethodName: "InitDiscountTask",
			Handler:    _Visual_InitDiscountTask_Handler,
		},
		{
			MethodName: "IncrementPromotionDiscountTask",
			Handler:    _Visual_IncrementPromotionDiscountTask_Handler,
		},
		{
			MethodName: "AddButtonPermissions",
			Handler:    _Visual_AddButtonPermissions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sv/stockVisual.proto",
}
