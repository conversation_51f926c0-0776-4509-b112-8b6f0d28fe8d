syntax = "proto3";


package dgc;

// @Desc    	在线问诊 微信小程序 消息通知
// <AUTHOR>
// @Date		2021-10-14
service MINIProgramMSGService {
  // @Desc    	发送小程序消息通知
  // <AUTHOR>
  // @Date		2021-10-13
  rpc PushTemplate(PushTemplateRequest) returns (PushTemplateResponse) {}
  // @Desc    	用户订阅消息
  // <AUTHOR>
  // @Date		2021-10-16
  rpc UserSubscribe(UserSubscribeRequest) returns (UserSubscribeResponse) {}

}
//小程序消息推送数据请求
message PushTemplateRequest{

  //小程序消息推送类型：1咨询提交成功通知（给用户看的） 2订单待支付通知  3医生接诊通知 4医生第一次回复消息通知 5医生回复消息通知 20新咨询单提醒(给医生看的) 21用户回复消息通知
  int32 push_type = 1;
  //订单号
  string order_sn = 2;

}
//小程序消息推送数据返回
message PushTemplateResponse{
    string msg = 1;
}

//用户订阅消息
message UserSubscribeRequest{
  //订单编号
 string order_sn = 1;
  // 用户openId
  string open_id = 2;
  //订阅消息类型：1咨询提交成功通知（给用户看的） 2订单待支付通知  3医生接诊通知 4医生第一次回复消息通知 5医生回复消息通知 6待接诊取消并退款 7已结束并由后台操作退款 20新咨询单提醒(给医生看的) 21用户回复消息通知
  string subscribe_type = 3;
}
//用户订阅消息
message UserSubscribeResponse{
  string msg = 1;
}
