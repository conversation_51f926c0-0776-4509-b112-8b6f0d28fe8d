syntax = "proto3";
import "dgc/order.proto";
// 问诊订单相关
package dgc;

// 在线问诊 医生模块
service DoctorService {
  // 获取医生列表
  rpc DoctorList(DoctorListRequest) returns (DoctorListResponse) {}

  // 获取医生信息
  rpc DoctorInfo(StatisticsType) returns (DoctorInfoResponse) {}
  // 问诊列表提示语（提示内容：已在门店排班不支持接单，待空闲时将继续为你推送问诊订单）
  rpc DoctorTips(DoctorTipsRequest) returns (BaseResponseNew){}
  // 用户端获取医生信息
  rpc GetDoctorInfo(GetDoctorInfoRequest) returns (GetDoctorInfoResponse) {}
  // 用户端获取问诊价格
  rpc GetDoctorPrice(GetDoctorPriceRequest) returns (GetDoctorPriceResponse) {}

  // 在线问诊 医生接单设置信息配置
  rpc DoctorSetting(DoctorSettingRequest) returns (DoctorSettingReponse) {}
  // 获取医生 接单设置信息
  rpc DoctorSettingInfo(DoctorSettingInfoRequest) returns (DoctorSettingInfoResponse) {}
  // 开启接单、关闭接单
  rpc DoctorSettingSwitch(DoctorSettingSwitchRequest) returns (DoctorSettingSwitchResponse) {}

  // 获取可派单的互联网医生信息一个
  rpc GetDispatchDoctor (GetDispatchDoctorReq) returns (DoctorInfoResponse) {}
  // 根据医生openid获取医生信息
  rpc GetDispatchDoctorByOpenId (GetDispatchDoctorByOpenIdReq) returns (DoctorInfoResponse) {}
  // 注册医生端小程序
  rpc RegisterDoctorMiniProgram (RegisterDoctorMiniProgramReq) returns (RegisterDoctorMiniProgramRes) {}
  // 注销医生端小程序
  rpc LogoutDoctorMiniProgram (LogoutDoctorMiniProgramReq) returns (BaseResponseNew) {}
  //获取问诊详情
  rpc DiagnoseDetails(DiagnoseDetailsRequest) returns (DiagnoseDetailsResponse) {}
  //获取问诊列表
  rpc DiagnoseList(DiagnoseListRequest) returns (DiagnoseListResponse) {}
  //获取SCRM医生列表
  rpc ScrmDoctorList(ScrmDoctorListRequest) returns (ScrmDoctorListResponse) {}
  //医生接单设置
  rpc DiagnoseSet(DiagnoseSetRequest) returns (BaseResponseNew) {}
  //获取医生接单设置
  rpc DiagnoseSystemInfo(EmptyRequest) returns (DiagnoseSystemInfoResponse) {}
  //新增互联网医生
  rpc DiagnoseInternetDoctorAdd(InternetDoctorAddRequest) returns (BaseResponseNew) {}
  //启用禁用互联网医生
  rpc InternetDoctorForbidden(InternetDoctorForbiddenRequest) returns (BaseResponseNew) {}
  //获取互联网医生信息
  rpc InternetDoctorInfo(InternetDoctorInfoRequest) returns (InternetDoctorInfoResponse) {}
  //获取互联网医生列表
  rpc DiagnoseInternetDoctorList(InternetDoctorListRequest) returns (InternetDoctorListResponse) {}
  //启用禁用互联网医生
  rpc DoctorOnlineChange(DoctorOnlineChangeRequest) returns (BaseResponseNew) {}
  //医生接单后台 医生信息状态
  rpc DoctorStatus(DoctorStatusRequest) returns (DoctorStatusResponse) {}
  //平台医院列表
  rpc HospitalList(HospitalListRequest) returns (HospitalListResponse) {}
  //导出列表
  rpc ScrmDoctorListExport(ScrmDoctorListExportRequest) returns (ScrmDoctorListResponse) {}

  //通知医生抢单
  rpc NotifyGrabOrder(NotifyGrabOrderRequest) returns (BaseResponseNew) {}
  //医生禁用启用
  rpc DoctorIsForbidden(DoctorIsForbiddenRequest) returns (BaseResponseNew) {}

  //获取医生协议状态
  rpc GetDoctorProtocolState(GetDoctorProtocolStateRequest) returns (GetDoctorProtocolStateResponse){}

  //编辑医生协议状态
  rpc EditDoctorProtocolState(EditDoctorProtocolStateRequest) returns (EditDoctorProtocolStateResponse){}
}

message GetDoctorProtocolStateRequest{}
message GetDoctorProtocolStateResponse{
  int32 protocol_state = 1;
}
message EditDoctorProtocolStateRequest{
  int32 protocol_state = 1;
}
message EditDoctorProtocolStateResponse{

}
message  GetDispatchDoctorByOpenIdReq{
  string open_id = 1;
}

//用户端获取问诊价格請求
message GetDoctorPriceRequest{}

//用户端获取问诊价格返回
message GetDoctorPriceResponse{
  //快速图文价格 单位分
  int32 quick_image_text_price = 1;
  //免费图文问诊时长（单位分钟）
  int32 free_image_text_duration = 2;
  //快速图文问诊时长（单位分钟）
  int32 quick_image_text_duration = 3;
}

message DoctorIsForbiddenRequest {
  string doctor_code = 1;
  int32 is_forbidden = 2;
}
message DoctorTipsRequest{

}

message GetDoctorInfoRequest{
  string user_id = 1;
  string doctor_code = 2;
}

message GetDoctorInfoResponse{
  //医生编号
  string doctor_code = 1;
  //名称
  string doctor_name = 2;
  //称号（职级、岗位）
  string doctor_level = 3;
  //医生擅长
  string doctor_speciality = 4;
  //医生简介
  string doctor_present = 5;
  //医院code
  string hospital_code = 6;
  //医生照片
  string doctor_img = 7;
  //医院名称
  string hospital_name = 8;
  //医生tag
  string tag_name = 9;
  //找医生图文问诊费用/分
  int32 image_text_price = 10;
  //找医生电话问诊费用/分
  int32 phone_price = 11;
  //找医生视频问诊费用/分
  int32 video_price = 12;
  //是否开始图文 0 未开启 1开启
  int32 image_text_status = 13;
  //是否开启视频 0 未开启 1开启
  int32 video_price_status = 14;
  //是否电话 0 未开启 1开启
  int32 phone_price_status = 15;
  //图片问诊 分钟
  int32 image_text_duration = 16;
  //电话问诊 分钟
  int32 phone_duration = 17;
  //视频问诊 分钟
  int32 video_duration = 18;
  //用户与医生最后的订单号
  string latest_order_sn = 19;
  //用户与医生最后的问诊状态 1待接入，2待回复，3问诊中，4已完成，5已取消，6已退款
  int32 latest_diagnose_state = 20;
}

message ScrmDoctorListExportRequest {
  //搜索医生 医生姓名或手机号
  string doctor_name = 1;
  //任职医院
  string hospital_name = 2;
  //医生状态
  int32 doctor_status = 3;
  //问诊项目
  int32 diagnose_project = 4;
  //问诊形式
  int32 diagnose_form = 5;
}

message HospitalListRequest {
  //医院名称
  string name = 1;
  // 当前多少页,从1开始
  int32 page_index = 2;
  // 每页多少条数据
  int32 page_size = 3;
}

message HospitalListResponse {
  // 总条数
  int32 total = 1;
  //列表数据
  repeated ScrmHospital data = 2;
}

message ScrmHospital {
  int32 id = 1;
  //医院code
  string hospital_code = 2;
  //医院名称
  string hospital_name = 3;
  //1中心医院，2专科医院，3社区医院，4全科医院，5小型全科医院，6中型全科医院，7宠物店
  int32 hospital_type = 4;
  //网络医院（虚拟）医院
  string hospital_internet_id = 5;
  //医院简称
  string hospital_short_name = 6;
  //医院特长
  string hospital_speciality = 7;
  //医院简称
  string hospital_present = 8;
  //医院图片
  string hospital_img = 9;
  //医院封面图
  string hospital_avatar = 10;
}

message DoctorStatusRequest {
  //医生手机号
  string mobile = 1;
}
message DoctorStatusResponse {
  //在线状态 1在线 0不在线
  int32 on_line = 1;
}

message DoctorOnlineChangeRequest {
  //医生编号
  string doctor_code = 1;
  //是否在线:0-否，1-是
  int32 on_line = 2;
}

message RegisterDoctorMiniProgramReq {
  string  doctor_code = 1; //医生编号
  string js_code = 2; //微信小程序的jscode
  string  doctor_mobile = 3; //医生手机号
  string  open_id = 4; //医生授权的OPENID
  string  come_from = 5; //来源
}
message  LogoutDoctorMiniProgramReq{
  string  doctor_code = 1; //医生编号
}

message EmptyRequest {
}

message InternetDoctorListRequest {
  //搜索医生 姓名或手机号
  string doctor_name = 1;
  // 当前多少页,从1开始
  int32 page_index = 2;
  // 每页多少条数据
  int32 page_size = 3;
}

message InternetDoctorListResponse {
  // 总条数
  int32 total = 1;
  //列表数据
  repeated InternetDoctorInfoResponse data = 2;
}

message InternetDoctorForbiddenRequest {
  string doctor_code = 1;
  int32 is_forbidden = 2;
}

message InternetDoctorInfoRequest {
  //医生编号
  string doctor_code = 1;
}

message InternetDoctorInfoResponse {
  //医生姓名
  string doctor_name = 1;
  //医生手机号
  string mobile = 2;
  //是否开启在线问诊服务:0-否，1-是
  int32 on_line = 3;
  //是否被禁用：0-否，1-是
  int32 is_forbidden = 4;
  //创建日期
  string create_time = 5;
  //最后更新时间
  string update_time = 6;
  //医生编号
  string doctor_code = 7;
}

message InternetDoctorAddRequest {
  //姓名
  string name = 1;
  //手机号
  string mobile = 2;
}

message DiagnoseSystemInfoResponse {
  //主键ID
  int32 id = 1;
  //医生排班时间内不接单开关：0关，1开
  int32 work_on_off = 2;
  //免费图文问诊时长（单位分钟）
  int32 free_image_text_duration = 3;
  //快速图文问诊时长（单位分钟）
  int32 quick_image_text_duration = 4;
  //找医生图文问诊时长（单位分钟）
  int32 find_phone_duration = 5;
  //找医生视频问诊时长（单位分钟）
  int32 find_video_duration = 6;
}

message DiagnoseSetRequest {
  //医生排班时间内不接单开关：0关，1开
  int32 work_on_off = 1;
}

//医生列表请求参数
message ScrmDoctorListRequest {
  //搜索医生 医生姓名或手机号
  string doctor_name = 1;
  //任职医院
  string hospital_name = 2;
  //医生状态
  int32 doctor_status = 3;
  //问诊项目
  int32 diagnose_project = 4;
  //问诊形式
  int32 diagnose_form = 5;
  // 当前多少页,从1开始
  int32 page_index = 6;
  // 每页多少条数据
  int32 page_size = 7;
}

message ScrmDoctorListResponse {
  // 总条数
  int32 total = 1;
  // 医生列表
  repeated ScrmDoctorSettingInfo data = 2;
}

message ScrmDoctorSettingInfo {
  //医生编号
  string doctor_code = 1;
  //兽医证号
  string doctor_num = 2;
  //名称
  string doctor_name = 3;
  //称号（职级、岗位）
  string doctor_level = 4;
  //医生特长
  string doctor_speciality = 5;
  //介绍
  string doctor_present = 6;
  //医院ID
  string hospital_id = 7;
  //电话
  string doctor_phone = 8;
  //医生照片
  string doctor_img = 9;
  //医生性别 0未知 1男 2女
  int32 doctor_sex = 10;
  //医院名称
  string hospital_name = 11;
  //更新时间
  string update_time = 12;
  //财务编码
  string epp_code = 13;
  //大区
  string region = 14;
  //城市
  string hospital_city = 15;
  //是否开启在线问诊功能:0-否，1-是
  int32 open_diagnose = 16;
  //是否开启在线问诊服务:0-否，1-是
  int32 open_diagnose_service = 17;
  //医生开启的问诊形式：1-图文，2-电话，3-视频，多个用逗号隔开
  int32 diagnose_forms = 18;
  //是否被禁用：0-否，1-是
  int32 is_forbidden = 19;
  //是否接受抢单：0-否，1-是
  int32 accept_preempt = 20;
  //是否接受派单(指用户能找到该医生)：0-否，1-是
  int32 accept_send = 21;
  //后台关闭排班时间不接受咨询时，医生设置排班时间内不接受电话/视频问诊开关0关闭，1开启
  int32 work_on_off = 22;
  //快速图文问诊费用（单位分）
  int32 quick_image_text_price = 23;
  //图文问诊费用（单位分）
  int32 image_text_price = 24;
  //电话问诊费用（单位分）
  int32 phone_price = 25;
  //视频问诊费用（单位分）
  int32 video_price = 26;
}

message ScrmDoctor {
  //主键ID
  int32 id = 1;
  //医生ID
  string doctor_code = 2;
  //医生姓名
  string doctor_name = 3;
  //手机号
  string doctor_phone = 4;
  //医生职级
  string doctor_level = 5;
  //任职医院
  string hospital_name = 6;
  //上次操作时间
  string update_time = 7;
  //财务编码
  string epp_code = 8;
  //大区
  string region = 9;
  //城市
  string hospital_city = 10;
  //状态 0正常 4停职 8待离职 16离职 64禁用 -1已删'
  int32 doctor_status = 11;
  DiagnoseDoctorSetting diagnose_doctor_setting = 12;
}
message DiagnoseDoctorSetting{
  //图文问诊费用（单位分）
  int32 image_text_price = 1;
  //电话问诊费用（单位分）
  int32 phone_price = 2;
  //视频问诊费用（单位分）
  int32 video_price = 3;
  //快速问诊图文问诊费用（单位分）
  int32 quick_image_text_price = 4;
  //是否被禁用：0-否，1-是
  int32 is_forbidden = 5;
}

//问诊列表请求参数
message DiagnoseListRequest {
  //问诊日期开始
  string start_date = 1;
  //问诊结束日期
  string end_date = 2;
  //问诊单号
  string order_sn = 3;
  //搜索医生 医生姓名或手机号
  string doctor_name = 4;
  //问诊项目
  int32 diagnose_project = 5;
  //订单状态
  int32 state = 6;
  //问诊形式
  int32 diagnose_form = 7;
  // 当前多少页,从1开始
  int32 page_index = 8;
  // 每页多少条数据
  int32 page_size = 9;
}

//问诊列表返回数据
message DiagnoseListResponse {
  int32 total = 1;
  repeated DiagnoseInfo data = 2;
}

message DiagnoseInfo {
  //问诊ID
  int32 id = 1;
  //订单ID
  string order_sn = 2;
  //问诊时间
  string create_time = 3;
  //医生名称
  string doctor_name = 4;
  //医生手机号
  string doctor_phone = 5;
  //医生职级
  string doctor_level = 6;
  //医院名称
  string hospital_name = 7;
  //客户姓名
  string user_name = 8;
  //客户手机号
  string user_phone = 9;
  //问诊项目：1-免费义诊，2-快速咨询，3-找医生
  int32 diagnose_project = 10;
  //问诊形式：1-图文，2-电话，3-视频
  int32 diagnose_form = 11;
  //医生接入时间
  string doctor_join = 12;
  //订单状态
  int32 state = 13;
  //用户所在城市
  string user_city = 14;
  //支付费用
  int32 pay_amount = 15;
  //医生首次回复时间
  string first_reply_time = 16;
  //首次回复间隔时间
  string interval_time = 17;
}

//在线问诊医生列表
message DoctorListRequest{
  //页码
  int32 page_index = 1;
  //页数
  int32 page_size = 2;
  //城市
  string hospital_city = 3;
  //职称
  string doctor_level = 4;
  //特长
  string doctor_speciality = 5;
  //价格区间最小价格
  int32 min_price = 6;
  //价格区间最大价格
  int32 max_price = 7;
  //坐标longitude
  string longitude = 8;
  //坐标latitude
  string latitude = 9;
  //医生名称/医院名称
  string key = 10;
}
message DoctorListResponse{
  //医生结构体
  message DoctorList{
    //医生code
    string doctor_code = 1;
    //医生名称
    string doctor_name = 2;
    //医生等级
    string doctor_level = 3;
    //医生tag
    string tag_name = 4;
    //医生图片
    string doctor_img = 5;
    //快速问诊费用/分
    int32 quick_image_text_price = 6;
    //找医生图文问诊费用/分
    int32 image_text_price = 7;
    //找医生电话问诊费用/分
    int32 phone_price = 8;
    //找医生视频问诊费用/分
    int32 video_price = 9;
    //医院名称
    string hospital_name = 10;
    //医生专长
    string doctor_speciality = 11;
  }
  //总条数
  int32 total = 1;
  //列表数据
  repeated  DoctorList list = 6;
}

message RegisterDoctorMiniProgramRes {
  string  doctor_code = 1; //医生编号
  string  doctor_name = 2; //医生手机号
  string  doctor_mobile = 3; //医生手机号
  bool is_internet_doctor = 4; //是否是互联网医生
}

message OrderList {
  //医生编号
  string doctor_code = 1;
  //用户id
  string scrm_user_id = 2;
  //用户名称
  string user_name = 3;
  //用户头像
  string user_avatar = 4;
  //问诊项目：1-免费义诊，2-快速咨询，3-找医生
  int32 diagnose_project = 5;
  //问诊形式：1-图文，2-电话，3-视频
  int32 diagnose_form = 6;
  //症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
  string symptom = 7;
  //补充症状(选择【其他】关键词，输入框描述症状必填) ， 如果症状是其他， 则显示补充症状， 最多显示14个字符
  string symptom_desc = 8;
  //订单编号
  string order_sn = 9;
  //最后对话时间
  string last_reply_time = 10;
  //问诊时间
  string create_time = 11;
}
//开启接单、关闭接单
message DoctorSettingSwitchRequest {
  int32 operate_type = 1;
}
//开启接单、关闭接单
message DoctorSettingSwitchResponse {
  int64 affect_rows = 1;
}
// 在线问诊 配置 医生接单设置信息
message DoctorSettingRequest {
  //医生开启的问诊形式：1-图文，2-电话，3-视频，多个用逗号隔开
  string diagnose_forms = 1;
  //是否被禁用：0-否，1-是
  int32 is_forbidden = 2;
  //是否接受抢单：0-否，1-是
  int32 accept_preempt = 3;
  //是否接受派单(指用户能找到该医生)：0-否，1-是
  int32 accept_send = 4;
  //后台关闭排班时间不接受咨询时，医生设置排班时间内不接受电话/视频问诊开关0关闭，1开启
  int32 work_on_off = 5;
  //设置可电话/视频的开始时间
  string start_time = 6;
  //设置可电话/视频的结束时间
  string end_time = 7;
  //图文问诊费用（单位分）
  int32 image_text_price = 8;
  //电话问诊费用（单位分）
  int32 phone_price = 9;
  //视频问诊费用（单位分）
  int32 video_price = 10;
  //医生编号
  string doctor_code = 11;
}
// 在线问诊 配置 医生接单设置信息
message DoctorSettingReponse {
  int64 affect_rows = 1;
}
//获取在线问诊 医生接单设置信息
message DoctorSettingInfoRequest {

}

//获取在线问诊 医生接单设置信息
message DoctorSettingInfoResponse {
  DiagnoseSetting  info = 1;
  bool done = 2;
}
// 在线问诊医生接单设置信息
message DiagnoseSetting {
  //自增id
  int32 id = 1;
  //医生编号
  string doctor_code = 2;
  //医生类别：1门店医生，2互联网医生
  int32 doctor_type = 3;
  //否开启在线问诊功能:0-否，1-是
  int32 open_diagnose = 4;
  //是否开启在线问诊服务:0-否，1-是
  int32 open_diagnose_service = 5;
  //医生开启的问诊形式：1-图文，2-电话，3-视频，多个用逗号隔开
  string diagnose_forms = 6;
  //是否被禁用：0-否，1-是
  int32 is_forbidden = 7;
  //是否接受抢单：0-否，1-是
  int32 accept_preempt = 8;
  //是否接受派单(指用户能找到该医生)：0-否，1-是
  int32 accept_send = 9;
  //后台关闭排班时间不接受咨询时，医生设置排班时间内不接受电话/视频问诊开关0关闭，1开启
  int32 work_on_off = 10;
  //设置可电话/视频的开始时间
  string start_time = 11;
  //设置可电话/视频的结束时间
  string end_time = 12;
  //图文问诊费用（单位分）
  int32 image_text_price = 13;
  //电话问诊费用（单位分）
  int32 phone_price = 14;
  //视频问诊费用（单位分）
  int32 video_price = 15;
  //创建日期
  string create_time = 16;
  //最后更新时间
  string update_time = 17;
}

message GetDispatchDoctorReq{
  int32 doctor_type = 1 ;//1、互联网医生2、门店医生
}
//统计类别：1服务待结算 2问诊订单 3我的排名 4客户评分，5抢单数 6派单数 7今日接单数  多个用英文逗号隔开，不传代表不需要统计信息
message StatisticsType  {
  string statistics_type = 1;
}
message DoctorInfoResponse {
  DoctorInfo doctor_info = 1;
  Statistics statistics = 2;
}
// 医生基本信息
message DoctorInfo {
  //医生编号
  string doctor_code = 1;
  //医生类别:1门店医生，2互联网医生
  int32 doctor_type = 2;
  //兽医证号
  string doctor_num = 3;
  //名称
  string doctor_name = 4;
  //称号（职级、岗位）
  string doctor_level = 5;
  //医生特长
  string doctor_speciality = 6;
  //介绍
  string doctor_present = 7;
  //医院code
  string hospital_code = 8;
  //电话
  string doctor_phone = 9;
  //医生照片
  string doctor_img = 10;
  //医院名称
  string hospital_name = 11;
  //科室
  string tag_name = 12;
  //从业时间
  string start_time = 13;
  //是否被禁用：是否被禁用:0-否，1-是
  int32 is_forbidden = 14;
  //是否在线（互联网医生用）
  int32 on_line = 15;
}

//统计结构体
message Statistics{
  //服务待结算(单位元)
  int32 order_left_price = 1;
  //问诊订单
  int32 total_order_cnt = 2;
  //我的排名
  int32 my_ranking = 3;
  //客户评分
  float score = 4;
  //抢单数统计
  int64 preempt_cnt = 5;
  //派单数统计
  int64 send_cnt = 6;
  //今日接单数
  int64 today_cnt = 7;
}

message DiagnoseDetailsRequest {
  int32 id = 1;
}

message DiagnoseDetailsResponse {
  DiagnoseData diagnose_details = 1;
}

message DiagnoseData {
  //问诊ID
  int32 id = 1;
  //订单ID
  string order_sn = 2;
  //问诊时间
  string create_time = 3;
  //医生名称
  string doctor_name = 4;
  //医生手机号
  string doctor_phone = 5;
  //医生职级
  int32 doctor_level = 6;
  //医院名称
  string hospital_name = 7;
  //客户姓名
  string user_name = 8;
  //客户手机号
  string user_phone = 9;
  //问诊项目：1-免费义诊，2-快速咨询，3-找医生
  int32 diagnose_project = 10;
  //问诊形式：1-图文，2-电话，3-视频
  int32 diagnose_form = 11;
  //回复间隔(秒)
  int32 reply_interval = 12;
  //订单状态
  int32 state = 13;
  //用户所在城市
  string user_city = 14;
  //支付费用
  int32 pay_amount = 15;
  //是否已退款
  int32 is_refund = 16;
  //支付时间
  string pay_time = 17;
}

message NotifyGrabOrderRequest {
  //经度
  string lng = 1;
  //纬度
  string lat = 2;
  //订单号
  string  order_sn = 3;
  //用户id
  string member_id = 4;
}

