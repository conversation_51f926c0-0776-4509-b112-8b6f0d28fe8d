// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dgc/order.proto

// 问诊订单相关

package dgc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//请求参数
type DiagnoseAddFinishMessageRequest struct {
	//订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//回复次数
	Number               int32    `protobuf:"varint,2,opt,name=number,proto3" json:"number"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseAddFinishMessageRequest) Reset()         { *m = DiagnoseAddFinishMessageRequest{} }
func (m *DiagnoseAddFinishMessageRequest) String() string { return proto.CompactTextString(m) }
func (*DiagnoseAddFinishMessageRequest) ProtoMessage()    {}
func (*DiagnoseAddFinishMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{0}
}

func (m *DiagnoseAddFinishMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseAddFinishMessageRequest.Unmarshal(m, b)
}
func (m *DiagnoseAddFinishMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseAddFinishMessageRequest.Marshal(b, m, deterministic)
}
func (m *DiagnoseAddFinishMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseAddFinishMessageRequest.Merge(m, src)
}
func (m *DiagnoseAddFinishMessageRequest) XXX_Size() int {
	return xxx_messageInfo_DiagnoseAddFinishMessageRequest.Size(m)
}
func (m *DiagnoseAddFinishMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseAddFinishMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseAddFinishMessageRequest proto.InternalMessageInfo

func (m *DiagnoseAddFinishMessageRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *DiagnoseAddFinishMessageRequest) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

//返回参数
type DiagnoseAddFinishMessageResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnoseAddFinishMessageResponse) Reset()         { *m = DiagnoseAddFinishMessageResponse{} }
func (m *DiagnoseAddFinishMessageResponse) String() string { return proto.CompactTextString(m) }
func (*DiagnoseAddFinishMessageResponse) ProtoMessage()    {}
func (*DiagnoseAddFinishMessageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{1}
}

func (m *DiagnoseAddFinishMessageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnoseAddFinishMessageResponse.Unmarshal(m, b)
}
func (m *DiagnoseAddFinishMessageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnoseAddFinishMessageResponse.Marshal(b, m, deterministic)
}
func (m *DiagnoseAddFinishMessageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnoseAddFinishMessageResponse.Merge(m, src)
}
func (m *DiagnoseAddFinishMessageResponse) XXX_Size() int {
	return xxx_messageInfo_DiagnoseAddFinishMessageResponse.Size(m)
}
func (m *DiagnoseAddFinishMessageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnoseAddFinishMessageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnoseAddFinishMessageResponse proto.InternalMessageInfo

//请求参数
type GetDiagnoseInfoRequest struct {
	//订单号
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDiagnoseInfoRequest) Reset()         { *m = GetDiagnoseInfoRequest{} }
func (m *GetDiagnoseInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetDiagnoseInfoRequest) ProtoMessage()    {}
func (*GetDiagnoseInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{2}
}

func (m *GetDiagnoseInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDiagnoseInfoRequest.Unmarshal(m, b)
}
func (m *GetDiagnoseInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDiagnoseInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetDiagnoseInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDiagnoseInfoRequest.Merge(m, src)
}
func (m *GetDiagnoseInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetDiagnoseInfoRequest.Size(m)
}
func (m *GetDiagnoseInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDiagnoseInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDiagnoseInfoRequest proto.InternalMessageInfo

func (m *GetDiagnoseInfoRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//返回参数
type GetDiagnoseInfoResponse struct {
	//订单信息
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//宠物id
	PetId string `protobuf:"bytes,2,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//宠物头像
	PetAvatar string `protobuf:"bytes,3,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar"`
	//宠物名称
	PetName string `protobuf:"bytes,4,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//宠物种类大分类
	PetKindof string `protobuf:"bytes,5,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	//宠物种类
	PetVariety string `protobuf:"bytes,6,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//宠物生日
	PetBirthday string `protobuf:"bytes,7,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	//宠物是否绝育 1：已绝育 0：未绝育
	PetNeutering int32 `protobuf:"varint,8,opt,name=pet_neutering,json=petNeutering,proto3" json:"pet_neutering"`
	//宠物性别 1GG,2MM
	PetSex int32 `protobuf:"varint,9,opt,name=pet_sex,json=petSex,proto3" json:"pet_sex"`
	//免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
	ImmuneStatus int32 `protobuf:"varint,10,opt,name=immune_status,json=immuneStatus,proto3" json:"immune_status"`
	//症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
	Symptom string `protobuf:"bytes,11,opt,name=symptom,proto3" json:"symptom"`
	//补充症状(选择【其他】关键词，输入框描述症状必填)
	SymptomDesc string `protobuf:"bytes,12,opt,name=symptom_desc,json=symptomDesc,proto3" json:"symptom_desc"`
	//症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
	SymptomRecent int32 `protobuf:"varint,13,opt,name=symptom_recent,json=symptomRecent,proto3" json:"symptom_recent"`
	//宠物症状照片，多个用英文逗号隔开
	Image string `protobuf:"bytes,14,opt,name=image,proto3" json:"image"`
	//是否就诊过：0未就诊，1就诊过
	HaveHospital int32 `protobuf:"varint,15,opt,name=have_hospital,json=haveHospital,proto3" json:"have_hospital"`
	//就诊过的医院名称
	HaveHospitalName string `protobuf:"bytes,16,opt,name=have_hospital_name,json=haveHospitalName,proto3" json:"have_hospital_name"`
	//医生诊断结果与治疗方案
	HaveHospitalResult string `protobuf:"bytes,17,opt,name=have_hospital_result,json=haveHospitalResult,proto3" json:"have_hospital_result"`
	//历史就诊的检查照片/药品照片
	HaveHospitalImage string `protobuf:"bytes,18,opt,name=have_hospital_image,json=haveHospitalImage,proto3" json:"have_hospital_image"`
	//是否有其他病史：0否，1是
	MedicalHistory int32 `protobuf:"varint,19,opt,name=medical_history,json=medicalHistory,proto3" json:"medical_history"`
	//其他病史信息
	MedicalHistoryInfo   string   `protobuf:"bytes,20,opt,name=medical_history_info,json=medicalHistoryInfo,proto3" json:"medical_history_info"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDiagnoseInfoResponse) Reset()         { *m = GetDiagnoseInfoResponse{} }
func (m *GetDiagnoseInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetDiagnoseInfoResponse) ProtoMessage()    {}
func (*GetDiagnoseInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{3}
}

func (m *GetDiagnoseInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDiagnoseInfoResponse.Unmarshal(m, b)
}
func (m *GetDiagnoseInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDiagnoseInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetDiagnoseInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDiagnoseInfoResponse.Merge(m, src)
}
func (m *GetDiagnoseInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetDiagnoseInfoResponse.Size(m)
}
func (m *GetDiagnoseInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDiagnoseInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDiagnoseInfoResponse proto.InternalMessageInfo

func (m *GetDiagnoseInfoResponse) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetPetKindof() string {
	if m != nil {
		return m.PetKindof
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetPetVariety() string {
	if m != nil {
		return m.PetVariety
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetPetNeutering() int32 {
	if m != nil {
		return m.PetNeutering
	}
	return 0
}

func (m *GetDiagnoseInfoResponse) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *GetDiagnoseInfoResponse) GetImmuneStatus() int32 {
	if m != nil {
		return m.ImmuneStatus
	}
	return 0
}

func (m *GetDiagnoseInfoResponse) GetSymptom() string {
	if m != nil {
		return m.Symptom
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetSymptomDesc() string {
	if m != nil {
		return m.SymptomDesc
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetSymptomRecent() int32 {
	if m != nil {
		return m.SymptomRecent
	}
	return 0
}

func (m *GetDiagnoseInfoResponse) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetHaveHospital() int32 {
	if m != nil {
		return m.HaveHospital
	}
	return 0
}

func (m *GetDiagnoseInfoResponse) GetHaveHospitalName() string {
	if m != nil {
		return m.HaveHospitalName
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetHaveHospitalResult() string {
	if m != nil {
		return m.HaveHospitalResult
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetHaveHospitalImage() string {
	if m != nil {
		return m.HaveHospitalImage
	}
	return ""
}

func (m *GetDiagnoseInfoResponse) GetMedicalHistory() int32 {
	if m != nil {
		return m.MedicalHistory
	}
	return 0
}

func (m *GetDiagnoseInfoResponse) GetMedicalHistoryInfo() string {
	if m != nil {
		return m.MedicalHistoryInfo
	}
	return ""
}

//获取最新的一条订单请求参数
type GetLatestOrderRequest struct {
	//医生标识
	DoctorId string `protobuf:"bytes,1,opt,name=doctor_id,json=doctorId,proto3" json:"doctor_id"`
	//用户标识
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//订单号
	OrderSn              string   `protobuf:"bytes,3,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLatestOrderRequest) Reset()         { *m = GetLatestOrderRequest{} }
func (m *GetLatestOrderRequest) String() string { return proto.CompactTextString(m) }
func (*GetLatestOrderRequest) ProtoMessage()    {}
func (*GetLatestOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{4}
}

func (m *GetLatestOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLatestOrderRequest.Unmarshal(m, b)
}
func (m *GetLatestOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLatestOrderRequest.Marshal(b, m, deterministic)
}
func (m *GetLatestOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLatestOrderRequest.Merge(m, src)
}
func (m *GetLatestOrderRequest) XXX_Size() int {
	return xxx_messageInfo_GetLatestOrderRequest.Size(m)
}
func (m *GetLatestOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLatestOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLatestOrderRequest proto.InternalMessageInfo

func (m *GetLatestOrderRequest) GetDoctorId() string {
	if m != nil {
		return m.DoctorId
	}
	return ""
}

func (m *GetLatestOrderRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetLatestOrderRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//获取最新的一条订单返回参数
type GetLatestOrderResponse struct {
	//医生标识
	DoctorId string `protobuf:"bytes,1,opt,name=doctor_id,json=doctorId,proto3" json:"doctor_id"`
	//医生头像
	DoctorAvatar string `protobuf:"bytes,2,opt,name=doctor_avatar,json=doctorAvatar,proto3" json:"doctor_avatar"`
	//医生名称
	DoctorName string `protobuf:"bytes,3,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生所在医院
	DoctorHospital string `protobuf:"bytes,4,opt,name=doctor_hospital,json=doctorHospital,proto3" json:"doctor_hospital"`
	//医生职级
	DoctorLevel string `protobuf:"bytes,5,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//用户id
	UserId string `protobuf:"bytes,6,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//用户头像
	UserAvatar string `protobuf:"bytes,7,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar"`
	//用户名称
	UserName string `protobuf:"bytes,8,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//订单信息
	OrderSn string `protobuf:"bytes,9,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//问诊单状态：1待接入，2待回复，3问诊中，4已完成
	DiagnoseState int32 `protobuf:"varint,10,opt,name=diagnose_state,json=diagnoseState,proto3" json:"diagnose_state"`
	//剩余时间
	DiagnoseResidueTime int32 `protobuf:"varint,11,opt,name=diagnose_residue_time,json=diagnoseResidueTime,proto3" json:"diagnose_residue_time"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `protobuf:"varint,12,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `protobuf:"varint,13,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	//宠物id
	PetId string `protobuf:"bytes,14,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//宠物头像
	PetAvatar string `protobuf:"bytes,15,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar"`
	//宠物名称
	PetName string `protobuf:"bytes,16,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//宠物种类大分类
	PetKindof string `protobuf:"bytes,17,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	//宠物种类
	PetVariety string `protobuf:"bytes,18,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//宠物生日
	PetBirthday string `protobuf:"bytes,19,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	//宠物是否绝育 1：已绝育 0：未绝育
	PetNeutering int32 `protobuf:"varint,20,opt,name=pet_neutering,json=petNeutering,proto3" json:"pet_neutering"`
	//宠物性别 1GG,2MM
	PetSex int32 `protobuf:"varint,21,opt,name=pet_sex,json=petSex,proto3" json:"pet_sex"`
	//已追加回复次数
	DiagnoseFinishMessage int32 `protobuf:"varint,22,opt,name=diagnose_finish_message,json=diagnoseFinishMessage,proto3" json:"diagnose_finish_message"`
	//订单状态：0未支付，1待接入，2待回复，3问诊中，4已完成，5已取消，6已退款
	OrderState           int32    `protobuf:"varint,23,opt,name=order_state,json=orderState,proto3" json:"order_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLatestOrderResponse) Reset()         { *m = GetLatestOrderResponse{} }
func (m *GetLatestOrderResponse) String() string { return proto.CompactTextString(m) }
func (*GetLatestOrderResponse) ProtoMessage()    {}
func (*GetLatestOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{5}
}

func (m *GetLatestOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLatestOrderResponse.Unmarshal(m, b)
}
func (m *GetLatestOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLatestOrderResponse.Marshal(b, m, deterministic)
}
func (m *GetLatestOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLatestOrderResponse.Merge(m, src)
}
func (m *GetLatestOrderResponse) XXX_Size() int {
	return xxx_messageInfo_GetLatestOrderResponse.Size(m)
}
func (m *GetLatestOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLatestOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLatestOrderResponse proto.InternalMessageInfo

func (m *GetLatestOrderResponse) GetDoctorId() string {
	if m != nil {
		return m.DoctorId
	}
	return ""
}

func (m *GetLatestOrderResponse) GetDoctorAvatar() string {
	if m != nil {
		return m.DoctorAvatar
	}
	return ""
}

func (m *GetLatestOrderResponse) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *GetLatestOrderResponse) GetDoctorHospital() string {
	if m != nil {
		return m.DoctorHospital
	}
	return ""
}

func (m *GetLatestOrderResponse) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *GetLatestOrderResponse) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetLatestOrderResponse) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *GetLatestOrderResponse) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *GetLatestOrderResponse) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *GetLatestOrderResponse) GetDiagnoseState() int32 {
	if m != nil {
		return m.DiagnoseState
	}
	return 0
}

func (m *GetLatestOrderResponse) GetDiagnoseResidueTime() int32 {
	if m != nil {
		return m.DiagnoseResidueTime
	}
	return 0
}

func (m *GetLatestOrderResponse) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *GetLatestOrderResponse) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

func (m *GetLatestOrderResponse) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *GetLatestOrderResponse) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *GetLatestOrderResponse) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *GetLatestOrderResponse) GetPetKindof() string {
	if m != nil {
		return m.PetKindof
	}
	return ""
}

func (m *GetLatestOrderResponse) GetPetVariety() string {
	if m != nil {
		return m.PetVariety
	}
	return ""
}

func (m *GetLatestOrderResponse) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *GetLatestOrderResponse) GetPetNeutering() int32 {
	if m != nil {
		return m.PetNeutering
	}
	return 0
}

func (m *GetLatestOrderResponse) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *GetLatestOrderResponse) GetDiagnoseFinishMessage() int32 {
	if m != nil {
		return m.DiagnoseFinishMessage
	}
	return 0
}

func (m *GetLatestOrderResponse) GetOrderState() int32 {
	if m != nil {
		return m.OrderState
	}
	return 0
}

type WaitOrderListRequest struct {
	//排序
	OrderBy string `protobuf:"bytes,1,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WaitOrderListRequest) Reset()         { *m = WaitOrderListRequest{} }
func (m *WaitOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*WaitOrderListRequest) ProtoMessage()    {}
func (*WaitOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{6}
}

func (m *WaitOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WaitOrderListRequest.Unmarshal(m, b)
}
func (m *WaitOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WaitOrderListRequest.Marshal(b, m, deterministic)
}
func (m *WaitOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WaitOrderListRequest.Merge(m, src)
}
func (m *WaitOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_WaitOrderListRequest.Size(m)
}
func (m *WaitOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WaitOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WaitOrderListRequest proto.InternalMessageInfo

func (m *WaitOrderListRequest) GetOrderBy() string {
	if m != nil {
		return m.OrderBy
	}
	return ""
}

func (m *WaitOrderListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *WaitOrderListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

//医生端-接诊操作
type OrderAcceptRequest struct {
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderAcceptRequest) Reset()         { *m = OrderAcceptRequest{} }
func (m *OrderAcceptRequest) String() string { return proto.CompactTextString(m) }
func (*OrderAcceptRequest) ProtoMessage()    {}
func (*OrderAcceptRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{7}
}

func (m *OrderAcceptRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderAcceptRequest.Unmarshal(m, b)
}
func (m *OrderAcceptRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderAcceptRequest.Marshal(b, m, deterministic)
}
func (m *OrderAcceptRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderAcceptRequest.Merge(m, src)
}
func (m *OrderAcceptRequest) XXX_Size() int {
	return xxx_messageInfo_OrderAcceptRequest.Size(m)
}
func (m *OrderAcceptRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderAcceptRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderAcceptRequest proto.InternalMessageInfo

func (m *OrderAcceptRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//医生端-接诊操作
type OrderAcceptResponse struct {
	AffectRows           int64    `protobuf:"varint,1,opt,name=affect_rows,json=affectRows,proto3" json:"affect_rows"`
	ScrmUserId           string   `protobuf:"bytes,2,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderAcceptResponse) Reset()         { *m = OrderAcceptResponse{} }
func (m *OrderAcceptResponse) String() string { return proto.CompactTextString(m) }
func (*OrderAcceptResponse) ProtoMessage()    {}
func (*OrderAcceptResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{8}
}

func (m *OrderAcceptResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderAcceptResponse.Unmarshal(m, b)
}
func (m *OrderAcceptResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderAcceptResponse.Marshal(b, m, deterministic)
}
func (m *OrderAcceptResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderAcceptResponse.Merge(m, src)
}
func (m *OrderAcceptResponse) XXX_Size() int {
	return xxx_messageInfo_OrderAcceptResponse.Size(m)
}
func (m *OrderAcceptResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderAcceptResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderAcceptResponse proto.InternalMessageInfo

func (m *OrderAcceptResponse) GetAffectRows() int64 {
	if m != nil {
		return m.AffectRows
	}
	return 0
}

func (m *OrderAcceptResponse) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

//医生端-我的订单列表
type DoctorOrderListRequest struct {
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//排序（样例create_time asc）
	OrderBy string `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	//根据下单时间搜索（低值）
	MinCreateTime string `protobuf:"bytes,4,opt,name=min_create_time,json=minCreateTime,proto3" json:"min_create_time"`
	//根据下单时间搜索（高值）
	MaxCreateTime        string   `protobuf:"bytes,5,opt,name=max_create_time,json=maxCreateTime,proto3" json:"max_create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoctorOrderListRequest) Reset()         { *m = DoctorOrderListRequest{} }
func (m *DoctorOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*DoctorOrderListRequest) ProtoMessage()    {}
func (*DoctorOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{9}
}

func (m *DoctorOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoctorOrderListRequest.Unmarshal(m, b)
}
func (m *DoctorOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoctorOrderListRequest.Marshal(b, m, deterministic)
}
func (m *DoctorOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoctorOrderListRequest.Merge(m, src)
}
func (m *DoctorOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_DoctorOrderListRequest.Size(m)
}
func (m *DoctorOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoctorOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoctorOrderListRequest proto.InternalMessageInfo

func (m *DoctorOrderListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *DoctorOrderListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DoctorOrderListRequest) GetOrderBy() string {
	if m != nil {
		return m.OrderBy
	}
	return ""
}

func (m *DoctorOrderListRequest) GetMinCreateTime() string {
	if m != nil {
		return m.MinCreateTime
	}
	return ""
}

func (m *DoctorOrderListRequest) GetMaxCreateTime() string {
	if m != nil {
		return m.MaxCreateTime
	}
	return ""
}

//订单列表
type OrderListResponse struct {
	Total                int64    `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List                 []*Order `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListResponse) Reset()         { *m = OrderListResponse{} }
func (m *OrderListResponse) String() string { return proto.CompactTextString(m) }
func (*OrderListResponse) ProtoMessage()    {}
func (*OrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{10}
}

func (m *OrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListResponse.Unmarshal(m, b)
}
func (m *OrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListResponse.Marshal(b, m, deterministic)
}
func (m *OrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListResponse.Merge(m, src)
}
func (m *OrderListResponse) XXX_Size() int {
	return xxx_messageInfo_OrderListResponse.Size(m)
}
func (m *OrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListResponse proto.InternalMessageInfo

func (m *OrderListResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *OrderListResponse) GetList() []*Order {
	if m != nil {
		return m.List
	}
	return nil
}

type Order struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//医生编号
	DoctorCode string `protobuf:"bytes,2,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//用户id
	ScrmUserId string `protobuf:"bytes,3,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//用户名称
	UserName string `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//用户头像
	UserAvatar string `protobuf:"bytes,5,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `protobuf:"varint,6,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊项目文本
	DiagnoseProjectText string `protobuf:"bytes,7,opt,name=diagnose_project_text,json=diagnoseProjectText,proto3" json:"diagnose_project_text"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `protobuf:"varint,8,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	//问诊形式文本
	DiagnoseFormText string `protobuf:"bytes,9,opt,name=diagnose_form_text,json=diagnoseFormText,proto3" json:"diagnose_form_text"`
	//症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
	Symptom string `protobuf:"bytes,10,opt,name=symptom,proto3" json:"symptom"`
	//症状文本
	SymptomText string `protobuf:"bytes,11,opt,name=symptom_text,json=symptomText,proto3" json:"symptom_text"`
	//补充症状(选择【其他】关键词，输入框描述症状必填) ， 如果症状是其他， 则显示补充症状， 最多显示14个字符
	SymptomDesc string `protobuf:"bytes,12,opt,name=symptom_desc,json=symptomDesc,proto3" json:"symptom_desc"`
	//订单编号
	OrderSn string `protobuf:"bytes,13,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//主订单状态：0已取消,10(默认)未付款,20已付款,30已完成'（order_main.order_status）
	OrderStatus int32 `protobuf:"varint,14,opt,name=order_status,json=orderStatus,proto3" json:"order_status"`
	//订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败,9:撤销退款(refund_order.refund_state)
	RefundState int32 `protobuf:"varint,15,opt,name=refund_state,json=refundState,proto3" json:"refund_state"`
	//问诊单状态：0未支付，1待接入，2待回复，3问诊中，4已完成，5已取消，6已退款
	State int32 `protobuf:"varint,16,opt,name=state,proto3" json:"state"`
	// 问诊状态文本
	StateText string `protobuf:"bytes,17,opt,name=state_text,json=stateText,proto3" json:"state_text"`
	//问诊时间
	CreateTime string `protobuf:"bytes,18,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//宠物id
	PetId string `protobuf:"bytes,20,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//宠物头像
	PetAvatar string `protobuf:"bytes,21,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar"`
	//宠物名字
	PetName string `protobuf:"bytes,22,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//大种类
	PetKindof string `protobuf:"bytes,23,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	//种类
	PetVariety string `protobuf:"bytes,24,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//生日例如：2021-10-01 00:00:00
	PetBirthday string `protobuf:"bytes,25,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	//1：已绝育 0：未绝育
	PetNeutering int32 `protobuf:"varint,26,opt,name=pet_neutering,json=petNeutering,proto3" json:"pet_neutering"`
	//绝育文本：1：已绝育 0：未绝育
	PetNeuteringText string `protobuf:"bytes,27,opt,name=pet_neutering_text,json=petNeuteringText,proto3" json:"pet_neutering_text"`
	//性别：1GG,2MM
	PetSex int32 `protobuf:"varint,28,opt,name=pet_sex,json=petSex,proto3" json:"pet_sex"`
	//性别文本：公， 母
	PetSexText string `protobuf:"bytes,29,opt,name=pet_sex_text,json=petSexText,proto3" json:"pet_sex_text"`
	//医生名称
	DoctorName string `protobuf:"bytes,30,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//支付或问诊结束时间，状态为待支付时为支付结束时间，状态为待回复和问诊中时为问诊结束时间
	EndTime string `protobuf:"bytes,31,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//问诊时长
	Duration int32 `protobuf:"varint,32,opt,name=duration,proto3" json:"duration"`
	//医生接入时间
	DoctorJoin string `protobuf:"bytes,33,opt,name=doctor_join,json=doctorJoin,proto3" json:"doctor_join"`
	//正常结束问诊时间（医生接入时间+问诊时长）
	CountdownFinishTime string `protobuf:"bytes,34,opt,name=countdown_finish_time,json=countdownFinishTime,proto3" json:"countdown_finish_time"`
	//问诊单状态：1待接入，2待回复，3问诊中，4已完成
	DiagnoseState int32 `protobuf:"varint,35,opt,name=diagnose_state,json=diagnoseState,proto3" json:"diagnose_state"`
	// 症状 信息（symptom等于其他时，则显示symptom_desc，否则显示symptom）
	SymptomSummarize string `protobuf:"bytes,36,opt,name=symptom_summarize,json=symptomSummarize,proto3" json:"symptom_summarize"`
	//订单支付时间
	PayTime              string   `protobuf:"bytes,37,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Order) Reset()         { *m = Order{} }
func (m *Order) String() string { return proto.CompactTextString(m) }
func (*Order) ProtoMessage()    {}
func (*Order) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{11}
}

func (m *Order) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Order.Unmarshal(m, b)
}
func (m *Order) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Order.Marshal(b, m, deterministic)
}
func (m *Order) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Order.Merge(m, src)
}
func (m *Order) XXX_Size() int {
	return xxx_messageInfo_Order.Size(m)
}
func (m *Order) XXX_DiscardUnknown() {
	xxx_messageInfo_Order.DiscardUnknown(m)
}

var xxx_messageInfo_Order proto.InternalMessageInfo

func (m *Order) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Order) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *Order) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *Order) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *Order) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *Order) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *Order) GetDiagnoseProjectText() string {
	if m != nil {
		return m.DiagnoseProjectText
	}
	return ""
}

func (m *Order) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

func (m *Order) GetDiagnoseFormText() string {
	if m != nil {
		return m.DiagnoseFormText
	}
	return ""
}

func (m *Order) GetSymptom() string {
	if m != nil {
		return m.Symptom
	}
	return ""
}

func (m *Order) GetSymptomText() string {
	if m != nil {
		return m.SymptomText
	}
	return ""
}

func (m *Order) GetSymptomDesc() string {
	if m != nil {
		return m.SymptomDesc
	}
	return ""
}

func (m *Order) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *Order) GetOrderStatus() int32 {
	if m != nil {
		return m.OrderStatus
	}
	return 0
}

func (m *Order) GetRefundState() int32 {
	if m != nil {
		return m.RefundState
	}
	return 0
}

func (m *Order) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *Order) GetStateText() string {
	if m != nil {
		return m.StateText
	}
	return ""
}

func (m *Order) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *Order) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *Order) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *Order) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *Order) GetPetKindof() string {
	if m != nil {
		return m.PetKindof
	}
	return ""
}

func (m *Order) GetPetVariety() string {
	if m != nil {
		return m.PetVariety
	}
	return ""
}

func (m *Order) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *Order) GetPetNeutering() int32 {
	if m != nil {
		return m.PetNeutering
	}
	return 0
}

func (m *Order) GetPetNeuteringText() string {
	if m != nil {
		return m.PetNeuteringText
	}
	return ""
}

func (m *Order) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *Order) GetPetSexText() string {
	if m != nil {
		return m.PetSexText
	}
	return ""
}

func (m *Order) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *Order) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *Order) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *Order) GetDoctorJoin() string {
	if m != nil {
		return m.DoctorJoin
	}
	return ""
}

func (m *Order) GetCountdownFinishTime() string {
	if m != nil {
		return m.CountdownFinishTime
	}
	return ""
}

func (m *Order) GetDiagnoseState() int32 {
	if m != nil {
		return m.DiagnoseState
	}
	return 0
}

func (m *Order) GetSymptomSummarize() string {
	if m != nil {
		return m.SymptomSummarize
	}
	return ""
}

func (m *Order) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

type AddDiagnoseOrderRequest struct {
	//订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//宠物id
	PetId string `protobuf:"bytes,2,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//医生编号
	DoctorCode string `protobuf:"bytes,3,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `protobuf:"varint,4,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `protobuf:"varint,5,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	//免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
	ImmuneStatus int32 `protobuf:"varint,6,opt,name=immune_status,json=immuneStatus,proto3" json:"immune_status"`
	//症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
	Symptom string `protobuf:"bytes,7,opt,name=symptom,proto3" json:"symptom"`
	//补充症状(选择【其他】关键词，输入框描述症状必填)
	SymptomDesc string `protobuf:"bytes,8,opt,name=symptom_desc,json=symptomDesc,proto3" json:"symptom_desc"`
	//症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
	SymptomRecent int32 `protobuf:"varint,9,opt,name=symptom_recent,json=symptomRecent,proto3" json:"symptom_recent"`
	//宠物症状照片，多个用英文逗号隔开
	Image string `protobuf:"bytes,10,opt,name=image,proto3" json:"image"`
	//是否就诊过：0未就诊，1就诊过
	HaveHospital int32 `protobuf:"varint,11,opt,name=have_hospital,json=haveHospital,proto3" json:"have_hospital"`
	//就诊过的医院名称
	HaveHospitalName string `protobuf:"bytes,12,opt,name=have_hospital_name,json=haveHospitalName,proto3" json:"have_hospital_name"`
	//医生诊断结果与治疗方案
	HaveHospitalResult string `protobuf:"bytes,13,opt,name=have_hospital_result,json=haveHospitalResult,proto3" json:"have_hospital_result"`
	//是否有其他病史：0否，1是
	MedicalHistory int32 `protobuf:"varint,14,opt,name=medical_history,json=medicalHistory,proto3" json:"medical_history"`
	//其他病史信息
	MedicalHistoryInfo string `protobuf:"bytes,15,opt,name=medical_history_info,json=medicalHistoryInfo,proto3" json:"medical_history_info"`
	//宠物头像
	PetAvatar string `protobuf:"bytes,16,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar"`
	//宠物名称
	PetName string `protobuf:"bytes,17,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//宠物种类
	PetVariety string `protobuf:"bytes,18,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//宠物生日
	PetBirthday string `protobuf:"bytes,19,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	//宠物性别 性别：1GG,2MM
	PetSex int32 `protobuf:"varint,20,opt,name=pet_sex,json=petSex,proto3" json:"pet_sex"`
	//1：已绝育 0：未绝育
	PetNeutering int32 `protobuf:"varint,21,opt,name=pet_neutering,json=petNeutering,proto3" json:"pet_neutering"`
	//用户ScrmId
	UserId string `protobuf:"bytes,22,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//金额
	Amount int32 `protobuf:"varint,23,opt,name=amount,proto3" json:"amount"`
	//问诊时长
	Duration             int32    `protobuf:"varint,24,opt,name=duration,proto3" json:"duration"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddDiagnoseOrderRequest) Reset()         { *m = AddDiagnoseOrderRequest{} }
func (m *AddDiagnoseOrderRequest) String() string { return proto.CompactTextString(m) }
func (*AddDiagnoseOrderRequest) ProtoMessage()    {}
func (*AddDiagnoseOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{12}
}

func (m *AddDiagnoseOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDiagnoseOrderRequest.Unmarshal(m, b)
}
func (m *AddDiagnoseOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDiagnoseOrderRequest.Marshal(b, m, deterministic)
}
func (m *AddDiagnoseOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDiagnoseOrderRequest.Merge(m, src)
}
func (m *AddDiagnoseOrderRequest) XXX_Size() int {
	return xxx_messageInfo_AddDiagnoseOrderRequest.Size(m)
}
func (m *AddDiagnoseOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDiagnoseOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddDiagnoseOrderRequest proto.InternalMessageInfo

func (m *AddDiagnoseOrderRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *AddDiagnoseOrderRequest) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

func (m *AddDiagnoseOrderRequest) GetImmuneStatus() int32 {
	if m != nil {
		return m.ImmuneStatus
	}
	return 0
}

func (m *AddDiagnoseOrderRequest) GetSymptom() string {
	if m != nil {
		return m.Symptom
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetSymptomDesc() string {
	if m != nil {
		return m.SymptomDesc
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetSymptomRecent() int32 {
	if m != nil {
		return m.SymptomRecent
	}
	return 0
}

func (m *AddDiagnoseOrderRequest) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetHaveHospital() int32 {
	if m != nil {
		return m.HaveHospital
	}
	return 0
}

func (m *AddDiagnoseOrderRequest) GetHaveHospitalName() string {
	if m != nil {
		return m.HaveHospitalName
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetHaveHospitalResult() string {
	if m != nil {
		return m.HaveHospitalResult
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetMedicalHistory() int32 {
	if m != nil {
		return m.MedicalHistory
	}
	return 0
}

func (m *AddDiagnoseOrderRequest) GetMedicalHistoryInfo() string {
	if m != nil {
		return m.MedicalHistoryInfo
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetPetVariety() string {
	if m != nil {
		return m.PetVariety
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *AddDiagnoseOrderRequest) GetPetNeutering() int32 {
	if m != nil {
		return m.PetNeutering
	}
	return 0
}

func (m *AddDiagnoseOrderRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *AddDiagnoseOrderRequest) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *AddDiagnoseOrderRequest) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

type BaseResponseNew struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=Msg,proto3" json:"Msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponseNew) Reset()         { *m = BaseResponseNew{} }
func (m *BaseResponseNew) String() string { return proto.CompactTextString(m) }
func (*BaseResponseNew) ProtoMessage()    {}
func (*BaseResponseNew) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{13}
}

func (m *BaseResponseNew) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponseNew.Unmarshal(m, b)
}
func (m *BaseResponseNew) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponseNew.Marshal(b, m, deterministic)
}
func (m *BaseResponseNew) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponseNew.Merge(m, src)
}
func (m *BaseResponseNew) XXX_Size() int {
	return xxx_messageInfo_BaseResponseNew.Size(m)
}
func (m *BaseResponseNew) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponseNew.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponseNew proto.InternalMessageInfo

func (m *BaseResponseNew) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type OrderStateChangeRequest struct {
	//操作类型：1结束问诊，2取消问诊
	OperateType int32 `protobuf:"varint,1,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	//订单号
	OrderSn              string   `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderStateChangeRequest) Reset()         { *m = OrderStateChangeRequest{} }
func (m *OrderStateChangeRequest) String() string { return proto.CompactTextString(m) }
func (*OrderStateChangeRequest) ProtoMessage()    {}
func (*OrderStateChangeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{14}
}

func (m *OrderStateChangeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderStateChangeRequest.Unmarshal(m, b)
}
func (m *OrderStateChangeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderStateChangeRequest.Marshal(b, m, deterministic)
}
func (m *OrderStateChangeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderStateChangeRequest.Merge(m, src)
}
func (m *OrderStateChangeRequest) XXX_Size() int {
	return xxx_messageInfo_OrderStateChangeRequest.Size(m)
}
func (m *OrderStateChangeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderStateChangeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderStateChangeRequest proto.InternalMessageInfo

func (m *OrderStateChangeRequest) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

func (m *OrderStateChangeRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

//医生端-我的订单列表
type UserOrderListRequest struct {
	// 当前多少页,从1开始
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页多少条数据
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//根据下单时间搜索（低值）
	MinCreateTime string `protobuf:"bytes,3,opt,name=min_create_time,json=minCreateTime,proto3" json:"min_create_time"`
	//根据下单时间搜索（高值）
	MaxCreateTime string `protobuf:"bytes,4,opt,name=max_create_time,json=maxCreateTime,proto3" json:"max_create_time"`
	//tab切换：0全部，1待支付，2待接诊/回复，3问诊中，4已退款
	TabType              int32    `protobuf:"varint,5,opt,name=tab_type,json=tabType,proto3" json:"tab_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOrderListRequest) Reset()         { *m = UserOrderListRequest{} }
func (m *UserOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*UserOrderListRequest) ProtoMessage()    {}
func (*UserOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{15}
}

func (m *UserOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOrderListRequest.Unmarshal(m, b)
}
func (m *UserOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOrderListRequest.Marshal(b, m, deterministic)
}
func (m *UserOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOrderListRequest.Merge(m, src)
}
func (m *UserOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_UserOrderListRequest.Size(m)
}
func (m *UserOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserOrderListRequest proto.InternalMessageInfo

func (m *UserOrderListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *UserOrderListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *UserOrderListRequest) GetMinCreateTime() string {
	if m != nil {
		return m.MinCreateTime
	}
	return ""
}

func (m *UserOrderListRequest) GetMaxCreateTime() string {
	if m != nil {
		return m.MaxCreateTime
	}
	return ""
}

func (m *UserOrderListRequest) GetTabType() int32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

type UserOrderDetailRequest struct {
	//订单编号
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOrderDetailRequest) Reset()         { *m = UserOrderDetailRequest{} }
func (m *UserOrderDetailRequest) String() string { return proto.CompactTextString(m) }
func (*UserOrderDetailRequest) ProtoMessage()    {}
func (*UserOrderDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{16}
}

func (m *UserOrderDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOrderDetailRequest.Unmarshal(m, b)
}
func (m *UserOrderDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOrderDetailRequest.Marshal(b, m, deterministic)
}
func (m *UserOrderDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOrderDetailRequest.Merge(m, src)
}
func (m *UserOrderDetailRequest) XXX_Size() int {
	return xxx_messageInfo_UserOrderDetailRequest.Size(m)
}
func (m *UserOrderDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOrderDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserOrderDetailRequest proto.InternalMessageInfo

func (m *UserOrderDetailRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type UserOrderDetailResponse struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//医生编号
	DoctorCode string `protobuf:"bytes,2,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//用户id
	ScrmUserId string `protobuf:"bytes,3,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//用户名称
	UserName string `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//用户头像
	UserAvatar string `protobuf:"bytes,5,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `protobuf:"varint,6,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	//问诊项目文本
	DiagnoseProjectText string `protobuf:"bytes,7,opt,name=diagnose_project_text,json=diagnoseProjectText,proto3" json:"diagnose_project_text"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `protobuf:"varint,8,opt,name=diagnose_form,json=diagnoseForm,proto3" json:"diagnose_form"`
	//问诊形式文本
	DiagnoseFormText string `protobuf:"bytes,9,opt,name=diagnose_form_text,json=diagnoseFormText,proto3" json:"diagnose_form_text"`
	//症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
	Symptom string `protobuf:"bytes,10,opt,name=symptom,proto3" json:"symptom"`
	//症状文本
	SymptomText string `protobuf:"bytes,11,opt,name=symptom_text,json=symptomText,proto3" json:"symptom_text"`
	//补充症状(选择【其他】关键词，输入框描述症状必填) ， 如果症状是其他， 则显示补充症状， 最多显示14个字符
	SymptomDesc string `protobuf:"bytes,12,opt,name=symptom_desc,json=symptomDesc,proto3" json:"symptom_desc"`
	//订单编号
	OrderSn string `protobuf:"bytes,13,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//主订单状态：0已取消,10(默认)未付款,20已付款,30已完成'（order_main.order_status）
	OrderStatus int32 `protobuf:"varint,14,opt,name=order_status,json=orderStatus,proto3" json:"order_status"`
	//最后对话时间
	LastReplyTime string `protobuf:"bytes,15,opt,name=last_reply_time,json=lastReplyTime,proto3" json:"last_reply_time"`
	//问诊单状态：0未支付，1待接入，2待回复，3问诊中，4已完成，5已取消，6已退款
	State int32 `protobuf:"varint,16,opt,name=state,proto3" json:"state"`
	// 问诊状态文本
	StateText string `protobuf:"bytes,17,opt,name=state_text,json=stateText,proto3" json:"state_text"`
	//问诊时间
	CreateTime string `protobuf:"bytes,18,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//宠物id
	PetId string `protobuf:"bytes,19,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//宠物头像
	PetAvatar string `protobuf:"bytes,20,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar"`
	//宠物名字
	PetName string `protobuf:"bytes,21,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//种类
	PetVariety string `protobuf:"bytes,22,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//生日例如：2021-10-01 00:00:00
	PetBirthday string `protobuf:"bytes,23,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	//生日文本：如0岁2月
	PetBirthdaysText string `protobuf:"bytes,24,opt,name=pet_birthdays_text,json=petBirthdaysText,proto3" json:"pet_birthdays_text"`
	//1：已绝育 0：未绝育
	PetNeutering int32 `protobuf:"varint,25,opt,name=pet_neutering,json=petNeutering,proto3" json:"pet_neutering"`
	//绝育文本：1：已绝育 0：未绝育
	PetNeuteringText string `protobuf:"bytes,26,opt,name=pet_neutering_text,json=petNeuteringText,proto3" json:"pet_neutering_text"`
	//性别：1GG,2MM
	PetSex int32 `protobuf:"varint,27,opt,name=pet_sex,json=petSex,proto3" json:"pet_sex"`
	//性别文本：公， 母
	PetSexText string `protobuf:"bytes,28,opt,name=pet_sex_text,json=petSexText,proto3" json:"pet_sex_text"`
	//医生名称
	DoctorName string `protobuf:"bytes,29,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生照片
	DoctorImg string `protobuf:"bytes,30,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	//支付或问诊结束时间，状态为待支付时为支付结束时间，状态为待回复和问诊中时为问诊结束时间
	EndTime string `protobuf:"bytes,31,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
	SymptomRecent int32 `protobuf:"varint,32,opt,name=symptom_recent,json=symptomRecent,proto3" json:"symptom_recent"`
	//宠物症状照片，多个用英文逗号隔开
	Image string `protobuf:"bytes,33,opt,name=image,proto3" json:"image"`
	//是否就诊过：0未就诊，1就诊过
	HaveHospital int32 `protobuf:"varint,34,opt,name=have_hospital,json=haveHospital,proto3" json:"have_hospital"`
	//就诊过的医院名称
	HaveHospitalName string `protobuf:"bytes,35,opt,name=have_hospital_name,json=haveHospitalName,proto3" json:"have_hospital_name"`
	//医生诊断结果与治疗方案
	HaveHospitalResult string `protobuf:"bytes,36,opt,name=have_hospital_result,json=haveHospitalResult,proto3" json:"have_hospital_result"`
	//历史就诊的检查照片/药品照片
	HaveHospitalImage string `protobuf:"bytes,37,opt,name=have_hospital_image,json=haveHospitalImage,proto3" json:"have_hospital_image"`
	//是否有其他病史：0否，1是
	MedicalHistory int32 `protobuf:"varint,38,opt,name=medical_history,json=medicalHistory,proto3" json:"medical_history"`
	//其他病史信息
	MedicalHistoryInfo string `protobuf:"bytes,39,opt,name=medical_history_info,json=medicalHistoryInfo,proto3" json:"medical_history_info"`
	//问诊金额
	Amount int32 `protobuf:"varint,40,opt,name=amount,proto3" json:"amount"`
	//问诊单状态：1待接入，2待回复，3问诊中，4已完成
	DiagnoseState int32 `protobuf:"varint,41,opt,name=diagnose_state,json=diagnoseState,proto3" json:"diagnose_state"`
	//订单支付时间
	PayTime string `protobuf:"bytes,42,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
	ImmuneStatus int32 `protobuf:"varint,43,opt,name=immune_status,json=immuneStatus,proto3" json:"immune_status"`
	//宠物种类大分类
	PetKindof            string   `protobuf:"bytes,44,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOrderDetailResponse) Reset()         { *m = UserOrderDetailResponse{} }
func (m *UserOrderDetailResponse) String() string { return proto.CompactTextString(m) }
func (*UserOrderDetailResponse) ProtoMessage()    {}
func (*UserOrderDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{17}
}

func (m *UserOrderDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOrderDetailResponse.Unmarshal(m, b)
}
func (m *UserOrderDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOrderDetailResponse.Marshal(b, m, deterministic)
}
func (m *UserOrderDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOrderDetailResponse.Merge(m, src)
}
func (m *UserOrderDetailResponse) XXX_Size() int {
	return xxx_messageInfo_UserOrderDetailResponse.Size(m)
}
func (m *UserOrderDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOrderDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserOrderDetailResponse proto.InternalMessageInfo

func (m *UserOrderDetailResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserOrderDetailResponse) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *UserOrderDetailResponse) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *UserOrderDetailResponse) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *UserOrderDetailResponse) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *UserOrderDetailResponse) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

func (m *UserOrderDetailResponse) GetDiagnoseProjectText() string {
	if m != nil {
		return m.DiagnoseProjectText
	}
	return ""
}

func (m *UserOrderDetailResponse) GetDiagnoseForm() int32 {
	if m != nil {
		return m.DiagnoseForm
	}
	return 0
}

func (m *UserOrderDetailResponse) GetDiagnoseFormText() string {
	if m != nil {
		return m.DiagnoseFormText
	}
	return ""
}

func (m *UserOrderDetailResponse) GetSymptom() string {
	if m != nil {
		return m.Symptom
	}
	return ""
}

func (m *UserOrderDetailResponse) GetSymptomText() string {
	if m != nil {
		return m.SymptomText
	}
	return ""
}

func (m *UserOrderDetailResponse) GetSymptomDesc() string {
	if m != nil {
		return m.SymptomDesc
	}
	return ""
}

func (m *UserOrderDetailResponse) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *UserOrderDetailResponse) GetOrderStatus() int32 {
	if m != nil {
		return m.OrderStatus
	}
	return 0
}

func (m *UserOrderDetailResponse) GetLastReplyTime() string {
	if m != nil {
		return m.LastReplyTime
	}
	return ""
}

func (m *UserOrderDetailResponse) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *UserOrderDetailResponse) GetStateText() string {
	if m != nil {
		return m.StateText
	}
	return ""
}

func (m *UserOrderDetailResponse) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *UserOrderDetailResponse) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *UserOrderDetailResponse) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *UserOrderDetailResponse) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *UserOrderDetailResponse) GetPetVariety() string {
	if m != nil {
		return m.PetVariety
	}
	return ""
}

func (m *UserOrderDetailResponse) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *UserOrderDetailResponse) GetPetBirthdaysText() string {
	if m != nil {
		return m.PetBirthdaysText
	}
	return ""
}

func (m *UserOrderDetailResponse) GetPetNeutering() int32 {
	if m != nil {
		return m.PetNeutering
	}
	return 0
}

func (m *UserOrderDetailResponse) GetPetNeuteringText() string {
	if m != nil {
		return m.PetNeuteringText
	}
	return ""
}

func (m *UserOrderDetailResponse) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *UserOrderDetailResponse) GetPetSexText() string {
	if m != nil {
		return m.PetSexText
	}
	return ""
}

func (m *UserOrderDetailResponse) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *UserOrderDetailResponse) GetDoctorImg() string {
	if m != nil {
		return m.DoctorImg
	}
	return ""
}

func (m *UserOrderDetailResponse) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *UserOrderDetailResponse) GetSymptomRecent() int32 {
	if m != nil {
		return m.SymptomRecent
	}
	return 0
}

func (m *UserOrderDetailResponse) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *UserOrderDetailResponse) GetHaveHospital() int32 {
	if m != nil {
		return m.HaveHospital
	}
	return 0
}

func (m *UserOrderDetailResponse) GetHaveHospitalName() string {
	if m != nil {
		return m.HaveHospitalName
	}
	return ""
}

func (m *UserOrderDetailResponse) GetHaveHospitalResult() string {
	if m != nil {
		return m.HaveHospitalResult
	}
	return ""
}

func (m *UserOrderDetailResponse) GetHaveHospitalImage() string {
	if m != nil {
		return m.HaveHospitalImage
	}
	return ""
}

func (m *UserOrderDetailResponse) GetMedicalHistory() int32 {
	if m != nil {
		return m.MedicalHistory
	}
	return 0
}

func (m *UserOrderDetailResponse) GetMedicalHistoryInfo() string {
	if m != nil {
		return m.MedicalHistoryInfo
	}
	return ""
}

func (m *UserOrderDetailResponse) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *UserOrderDetailResponse) GetDiagnoseState() int32 {
	if m != nil {
		return m.DiagnoseState
	}
	return 0
}

func (m *UserOrderDetailResponse) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *UserOrderDetailResponse) GetImmuneStatus() int32 {
	if m != nil {
		return m.ImmuneStatus
	}
	return 0
}

func (m *UserOrderDetailResponse) GetPetKindof() string {
	if m != nil {
		return m.PetKindof
	}
	return ""
}

type GetUserDiagnoseRequest struct {
	//用户编号
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//医生编号
	DoctorCode string `protobuf:"bytes,2,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject      int32    `protobuf:"varint,3,opt,name=diagnose_project,json=diagnoseProject,proto3" json:"diagnose_project"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDiagnoseRequest) Reset()         { *m = GetUserDiagnoseRequest{} }
func (m *GetUserDiagnoseRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserDiagnoseRequest) ProtoMessage()    {}
func (*GetUserDiagnoseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{18}
}

func (m *GetUserDiagnoseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDiagnoseRequest.Unmarshal(m, b)
}
func (m *GetUserDiagnoseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDiagnoseRequest.Marshal(b, m, deterministic)
}
func (m *GetUserDiagnoseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDiagnoseRequest.Merge(m, src)
}
func (m *GetUserDiagnoseRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserDiagnoseRequest.Size(m)
}
func (m *GetUserDiagnoseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDiagnoseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDiagnoseRequest proto.InternalMessageInfo

func (m *GetUserDiagnoseRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetUserDiagnoseRequest) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *GetUserDiagnoseRequest) GetDiagnoseProject() int32 {
	if m != nil {
		return m.DiagnoseProject
	}
	return 0
}

type GetUserDiagnoseResponse struct {
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	DoctorCode           string   `protobuf:"bytes,2,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDiagnoseResponse) Reset()         { *m = GetUserDiagnoseResponse{} }
func (m *GetUserDiagnoseResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserDiagnoseResponse) ProtoMessage()    {}
func (*GetUserDiagnoseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c7ca964f1ccc335e, []int{19}
}

func (m *GetUserDiagnoseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDiagnoseResponse.Unmarshal(m, b)
}
func (m *GetUserDiagnoseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDiagnoseResponse.Marshal(b, m, deterministic)
}
func (m *GetUserDiagnoseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDiagnoseResponse.Merge(m, src)
}
func (m *GetUserDiagnoseResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserDiagnoseResponse.Size(m)
}
func (m *GetUserDiagnoseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDiagnoseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDiagnoseResponse proto.InternalMessageInfo

func (m *GetUserDiagnoseResponse) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *GetUserDiagnoseResponse) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func init() {
	proto.RegisterType((*DiagnoseAddFinishMessageRequest)(nil), "dgc.DiagnoseAddFinishMessageRequest")
	proto.RegisterType((*DiagnoseAddFinishMessageResponse)(nil), "dgc.DiagnoseAddFinishMessageResponse")
	proto.RegisterType((*GetDiagnoseInfoRequest)(nil), "dgc.GetDiagnoseInfoRequest")
	proto.RegisterType((*GetDiagnoseInfoResponse)(nil), "dgc.GetDiagnoseInfoResponse")
	proto.RegisterType((*GetLatestOrderRequest)(nil), "dgc.GetLatestOrderRequest")
	proto.RegisterType((*GetLatestOrderResponse)(nil), "dgc.GetLatestOrderResponse")
	proto.RegisterType((*WaitOrderListRequest)(nil), "dgc.WaitOrderListRequest")
	proto.RegisterType((*OrderAcceptRequest)(nil), "dgc.OrderAcceptRequest")
	proto.RegisterType((*OrderAcceptResponse)(nil), "dgc.OrderAcceptResponse")
	proto.RegisterType((*DoctorOrderListRequest)(nil), "dgc.DoctorOrderListRequest")
	proto.RegisterType((*OrderListResponse)(nil), "dgc.OrderListResponse")
	proto.RegisterType((*Order)(nil), "dgc.Order")
	proto.RegisterType((*AddDiagnoseOrderRequest)(nil), "dgc.AddDiagnoseOrderRequest")
	proto.RegisterType((*BaseResponseNew)(nil), "dgc.BaseResponseNew")
	proto.RegisterType((*OrderStateChangeRequest)(nil), "dgc.OrderStateChangeRequest")
	proto.RegisterType((*UserOrderListRequest)(nil), "dgc.UserOrderListRequest")
	proto.RegisterType((*UserOrderDetailRequest)(nil), "dgc.UserOrderDetailRequest")
	proto.RegisterType((*UserOrderDetailResponse)(nil), "dgc.UserOrderDetailResponse")
	proto.RegisterType((*GetUserDiagnoseRequest)(nil), "dgc.GetUserDiagnoseRequest")
	proto.RegisterType((*GetUserDiagnoseResponse)(nil), "dgc.GetUserDiagnoseResponse")
}

func init() { proto.RegisterFile("dgc/order.proto", fileDescriptor_c7ca964f1ccc335e) }

var fileDescriptor_c7ca964f1ccc335e = []byte{
	// 1963 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0xdd, 0x72, 0xdb, 0xc6,
	0x15, 0x8e, 0x44, 0x91, 0x22, 0x0f, 0x7f, 0xb5, 0xa2, 0x48, 0x88, 0xb2, 0x23, 0x09, 0xb2, 0x6c,
	0xa7, 0xf1, 0x38, 0x1d, 0x7b, 0xa6, 0xf7, 0xb6, 0x35, 0x89, 0xd5, 0x38, 0x4e, 0x87, 0x72, 0x9a,
	0xde, 0x61, 0x20, 0x60, 0x45, 0x21, 0x25, 0x00, 0x16, 0xbb, 0x94, 0xc8, 0xcc, 0xf4, 0x19, 0xfa,
	0x06, 0x7d, 0x85, 0x3e, 0x41, 0x27, 0x37, 0x7d, 0x85, 0xbe, 0x4f, 0x67, 0xcf, 0x2e, 0x40, 0x2c,
	0x01, 0x82, 0xd4, 0x4c, 0x7a, 0xd3, 0xe9, 0x1d, 0xf6, 0xdb, 0xb3, 0xbb, 0x67, 0x7f, 0xce, 0x77,
	0xbe, 0x5d, 0x40, 0xdb, 0x1d, 0x39, 0x5f, 0x85, 0x91, 0x4b, 0xa3, 0x97, 0x93, 0x28, 0xe4, 0x21,
	0x29, 0xb9, 0x23, 0xc7, 0xfc, 0x04, 0xc7, 0x17, 0x9e, 0x3d, 0x0a, 0x42, 0x46, 0xdf, 0xb8, 0xee,
	0xd7, 0x5e, 0xe0, 0xb1, 0xdb, 0xef, 0x28, 0x63, 0xf6, 0x88, 0x0e, 0xe9, 0x5f, 0xa6, 0x94, 0x71,
	0x72, 0x08, 0x55, 0x6c, 0x66, 0xb1, 0xc0, 0xd8, 0x3a, 0xd9, 0x7a, 0x5e, 0x1b, 0xee, 0x62, 0xf9,
	0x2a, 0x20, 0x3d, 0xa8, 0x04, 0x53, 0xff, 0x9a, 0x46, 0xc6, 0xf6, 0xc9, 0xd6, 0xf3, 0xf2, 0x50,
	0x95, 0x4c, 0x13, 0x4e, 0x56, 0xf7, 0xca, 0x26, 0x61, 0xc0, 0xa8, 0xf9, 0x1a, 0x7a, 0xdf, 0x50,
	0x1e, 0x9b, 0x5d, 0x06, 0x37, 0xe1, 0xfa, 0x01, 0xcd, 0x7f, 0x97, 0xa1, 0x9f, 0x69, 0x25, 0x3b,
	0x2c, 0xf2, 0xf3, 0x00, 0x2a, 0x13, 0xca, 0x2d, 0xcf, 0x45, 0x3f, 0x6b, 0xc3, 0xf2, 0x84, 0xf2,
	0x4b, 0x97, 0x3c, 0x06, 0x10, 0xb0, 0x7d, 0x67, 0x73, 0x3b, 0x32, 0x4a, 0x58, 0x55, 0x9b, 0x50,
	0xfe, 0x06, 0x01, 0xd1, 0xa1, 0xa8, 0x0e, 0x6c, 0x9f, 0x1a, 0x3b, 0xb2, 0xc3, 0x09, 0xe5, 0x1f,
	0x6d, 0x9f, 0xc6, 0x2d, 0xff, 0xec, 0x05, 0x6e, 0x78, 0x63, 0x94, 0x93, 0x96, 0xdf, 0x22, 0x40,
	0x8e, 0xa1, 0x2e, 0xaa, 0xef, 0xec, 0xc8, 0xa3, 0x7c, 0x6e, 0x54, 0xb0, 0x5e, 0xb4, 0xf8, 0xa3,
	0x44, 0xc8, 0x29, 0x34, 0x84, 0xc1, 0xb5, 0x17, 0xf1, 0x5b, 0xd7, 0x9e, 0x1b, 0xbb, 0x68, 0x21,
	0x1a, 0xbd, 0x55, 0x10, 0x39, 0x83, 0x26, 0x8e, 0x4e, 0xa7, 0x9c, 0x46, 0x5e, 0x30, 0x32, 0xaa,
	0xb8, 0xc4, 0xa2, 0xdd, 0xc7, 0x18, 0x23, 0x7d, 0x10, 0x2e, 0x59, 0x8c, 0xce, 0x8c, 0x9a, 0xdc,
	0x81, 0x09, 0xe5, 0x57, 0x74, 0x26, 0x5a, 0x7b, 0xbe, 0x3f, 0x0d, 0xa8, 0xc5, 0xb8, 0xcd, 0xa7,
	0xcc, 0x00, 0xd9, 0x5a, 0x82, 0x57, 0x88, 0x11, 0x03, 0x76, 0xd9, 0xdc, 0x9f, 0xf0, 0xd0, 0x37,
	0xea, 0x72, 0x7e, 0xaa, 0x28, 0xfc, 0x53, 0x9f, 0x96, 0x4b, 0x99, 0x63, 0x34, 0xa4, 0x7f, 0x0a,
	0xbb, 0xa0, 0xcc, 0x21, 0xe7, 0xd0, 0x8a, 0x4d, 0x22, 0xea, 0xd0, 0x80, 0x1b, 0x4d, 0x1c, 0xa2,
	0xa9, 0xd0, 0x21, 0x82, 0xa4, 0x0b, 0x65, 0xcf, 0xb7, 0x47, 0xd4, 0x68, 0xc9, 0x95, 0xc7, 0x82,
	0x70, 0xef, 0xd6, 0xbe, 0xa3, 0xd6, 0x6d, 0xc8, 0x26, 0x1e, 0xb7, 0xc7, 0x46, 0x5b, 0xba, 0x27,
	0xc0, 0xf7, 0x0a, 0x23, 0x2f, 0x80, 0x68, 0x46, 0x72, 0x27, 0x3a, 0xd8, 0x4f, 0x27, 0x6d, 0x89,
	0x5b, 0xf2, 0x5b, 0xe8, 0xea, 0xd6, 0x11, 0x65, 0xd3, 0x31, 0x37, 0xf6, 0xd0, 0x9e, 0xa4, 0xed,
	0x87, 0x58, 0x43, 0x5e, 0xc2, 0xbe, 0xde, 0x42, 0x3a, 0x4a, 0xb0, 0xc1, 0x5e, 0xba, 0xc1, 0x25,
	0x3a, 0xfd, 0x0c, 0xda, 0x3e, 0x75, 0x3d, 0xc7, 0x1e, 0x5b, 0xb7, 0x1e, 0xe3, 0x61, 0x34, 0x37,
	0xf6, 0xd1, 0xed, 0x96, 0x82, 0xdf, 0x4b, 0x54, 0xb8, 0xb2, 0x64, 0x68, 0x79, 0xc1, 0x4d, 0x68,
	0x74, 0xa5, 0x2b, 0xba, 0xb5, 0x38, 0xc3, 0xe6, 0x2d, 0x1c, 0x7c, 0x43, 0xf9, 0x07, 0x9b, 0x53,
	0xc6, 0xbf, 0x17, 0x87, 0x36, 0x8e, 0x85, 0x23, 0xa8, 0xb9, 0xa1, 0xc3, 0xc3, 0x48, 0x1c, 0x5e,
	0x79, 0xaa, 0xab, 0x12, 0xb8, 0x74, 0xc5, 0xee, 0x4f, 0x19, 0x8d, 0x16, 0xe7, 0xba, 0x22, 0x8a,
	0x97, 0xae, 0x16, 0x0a, 0x25, 0x3d, 0x82, 0xfe, 0x51, 0xc1, 0xb8, 0xd3, 0x86, 0x52, 0x01, 0x54,
	0x38, 0xd6, 0x19, 0x34, 0x55, 0xa5, 0x0a, 0x17, 0x39, 0x62, 0x43, 0x82, 0x2a, 0x62, 0x8e, 0xa1,
	0xae, 0x8c, 0x70, 0xab, 0xe4, 0xd0, 0x20, 0x21, 0xdc, 0xa4, 0x67, 0xd0, 0x56, 0x06, 0xc9, 0xce,
	0xcb, 0xc8, 0x6a, 0x49, 0x38, 0xd9, 0xfb, 0x53, 0x50, 0x3d, 0x5b, 0x63, 0x7a, 0x47, 0xc7, 0x2a,
	0xc4, 0x54, 0xef, 0x1f, 0x04, 0x94, 0x9e, 0x7d, 0x45, 0x9b, 0xfd, 0x31, 0xd4, 0xb1, 0x42, 0x39,
	0x2a, 0x63, 0x0b, 0x04, 0xa4, 0xdc, 0x3c, 0x82, 0x1a, 0x1a, 0xa0, 0x93, 0x55, 0x39, 0x51, 0x01,
	0xa0, 0x8b, 0xe9, 0xb5, 0xab, 0xe9, 0x34, 0x72, 0x0e, 0x2d, 0x57, 0x31, 0x0f, 0x86, 0x15, 0x55,
	0x51, 0xd5, 0x8c, 0x51, 0x11, 0x57, 0x94, 0xbc, 0x82, 0x83, 0xc4, 0x2c, 0xa2, 0xcc, 0x73, 0xa7,
	0xd4, 0xe2, 0x9e, 0x4f, 0x31, 0xc8, 0xca, 0xc3, 0xfd, 0xb8, 0x72, 0x28, 0xeb, 0x3e, 0x79, 0x3e,
	0x25, 0x5f, 0x40, 0x27, 0x69, 0x33, 0x89, 0xc2, 0x9f, 0xa8, 0xc3, 0x31, 0xe8, 0xca, 0xc3, 0x76,
	0x8c, 0xff, 0x41, 0xc2, 0xb8, 0x13, 0xb1, 0xe9, 0x4d, 0x18, 0xf9, 0x2a, 0xee, 0x1a, 0x31, 0xf8,
	0x75, 0x18, 0xf9, 0x29, 0xc6, 0x6b, 0xad, 0x66, 0xbc, 0x76, 0x11, 0xe3, 0x75, 0x8a, 0x18, 0x6f,
	0x6f, 0x0d, 0xe3, 0x91, 0xb5, 0x8c, 0xb7, 0xbf, 0x01, 0xe3, 0x75, 0x8b, 0x19, 0xef, 0x40, 0x63,
	0xbc, 0xdf, 0x41, 0x7f, 0xb1, 0x2c, 0x98, 0x71, 0x2c, 0x5f, 0xa6, 0x1c, 0xa3, 0x87, 0x86, 0xc9,
	0xa6, 0x68, 0xf9, 0x48, 0x78, 0xae, 0xf6, 0x1b, 0x77, 0xb4, 0x8f, 0xb6, 0x20, 0xb7, 0x5c, 0x20,
	0xa6, 0x0f, 0xdd, 0x1f, 0x6d, 0x4f, 0xc6, 0xca, 0x07, 0x8f, 0xf1, 0x4c, 0x9a, 0xba, 0x9e, 0x6b,
	0xf9, 0xe6, 0xed, 0x1c, 0x17, 0xcb, 0x1e, 0x51, 0xcb, 0x0b, 0x5c, 0x3a, 0x53, 0xb9, 0xb1, 0x26,
	0x90, 0x4b, 0x01, 0x88, 0xf3, 0x87, 0xd5, 0xcc, 0xfb, 0x59, 0x06, 0x49, 0x79, 0x58, 0x15, 0xc0,
	0x95, 0xf7, 0x33, 0x35, 0xbf, 0x02, 0x82, 0x43, 0xbd, 0x71, 0x1c, 0x3a, 0xe1, 0x1b, 0xe4, 0xc4,
	0x3f, 0xc1, 0xbe, 0xd6, 0x40, 0x45, 0xf3, 0x31, 0xd4, 0xed, 0x9b, 0x1b, 0xea, 0x70, 0x2b, 0x0a,
	0xef, 0x19, 0x36, 0x2a, 0x0d, 0x41, 0x42, 0xc3, 0xf0, 0x9e, 0x91, 0x13, 0x68, 0x30, 0x27, 0xf2,
	0x2d, 0x9d, 0x42, 0x40, 0x60, 0x3f, 0x60, 0x20, 0x99, 0xbf, 0x6c, 0x41, 0xef, 0x02, 0x23, 0x2e,
	0x33, 0x79, 0x7d, 0x86, 0x5b, 0x85, 0x33, 0xdc, 0xd6, 0x67, 0xa8, 0x2d, 0x5c, 0x49, 0x5f, 0xb8,
	0xa7, 0xd0, 0xf6, 0xbd, 0xc0, 0x72, 0x22, 0x6a, 0x73, 0x15, 0x34, 0x92, 0x1f, 0x9a, 0xbe, 0x17,
	0xbc, 0x43, 0x14, 0xc3, 0x45, 0xd8, 0xd9, 0x33, 0xcd, 0xae, 0xac, 0xec, 0xec, 0xd9, 0xc2, 0xce,
	0xbc, 0x84, 0xbd, 0x94, 0xeb, 0x6a, 0x65, 0xba, 0x50, 0xe6, 0xa1, 0xa0, 0x1e, 0xb9, 0x26, 0xb2,
	0x40, 0x3e, 0x87, 0x9d, 0xb1, 0xc7, 0xb8, 0xb1, 0x7d, 0x52, 0x7a, 0x5e, 0x7f, 0x05, 0x2f, 0xdd,
	0x91, 0xf3, 0x52, 0xf2, 0x23, 0xe2, 0xe6, 0x2f, 0x35, 0x28, 0x63, 0x99, 0xb4, 0x60, 0x5b, 0x11,
	0x64, 0x79, 0xb8, 0xed, 0xb9, 0x29, 0xd6, 0x73, 0x42, 0x97, 0xc6, 0xeb, 0x28, 0xa1, 0x77, 0xa1,
	0x4b, 0x33, 0x2b, 0x5d, 0x5a, 0x5e, 0x69, 0x9d, 0x91, 0x76, 0x96, 0x18, 0x69, 0x89, 0xcf, 0xca,
	0x19, 0x3e, 0xcb, 0x23, 0x8f, 0x4a, 0x3e, 0x79, 0xa4, 0xb9, 0x49, 0x99, 0x5a, 0x9c, 0xce, 0xb8,
	0x62, 0xc9, 0xfd, 0x25, 0xfb, 0x4f, 0x74, 0x96, 0x43, 0x38, 0xd5, 0x1c, 0xc2, 0x79, 0x01, 0x44,
	0x33, 0x92, 0xbd, 0x4a, 0x02, 0xed, 0xa4, 0x2d, 0xb1, 0xcb, 0x94, 0xf2, 0x80, 0x95, 0xca, 0x03,
	0x7b, 0xa8, 0x6b, 0xca, 0x03, 0x1b, 0x6f, 0x20, 0x4e, 0xd2, 0xe1, 0xd2, 0xd4, 0x49, 0xfc, 0x14,
	0x1a, 0x8b, 0x78, 0x9f, 0x32, 0xe4, 0xc7, 0xf2, 0xb0, 0x9e, 0x04, 0xfc, 0x94, 0x09, 0x93, 0x88,
	0xde, 0x4c, 0x03, 0x57, 0x71, 0x82, 0x14, 0x27, 0x75, 0x89, 0x49, 0x8e, 0xef, 0x42, 0x59, 0xd6,
	0x75, 0xb0, 0x4e, 0x16, 0x44, 0x54, 0xe0, 0x87, 0x74, 0x5d, 0x91, 0x24, 0x22, 0xe8, 0xf8, 0x31,
	0xd4, 0xd3, 0x27, 0x56, 0x91, 0xa4, 0xb3, 0x38, 0xd6, 0x0b, 0xd6, 0xee, 0xae, 0x66, 0xed, 0x83,
	0x22, 0xd6, 0xee, 0x15, 0xb1, 0x76, 0x7f, 0x0d, 0x6b, 0x1b, 0x6b, 0x59, 0xfb, 0x70, 0x03, 0xd6,
	0x1e, 0xe4, 0xb0, 0xf6, 0x0b, 0x20, 0x9a, 0x91, 0x5c, 0xa0, 0x23, 0x79, 0x3a, 0xd2, 0x96, 0xb8,
	0x4e, 0x29, 0x8e, 0x7f, 0xa4, 0x71, 0xfc, 0x89, 0x74, 0x87, 0xd1, 0x99, 0xec, 0xe0, 0x71, 0xe2,
	0xf0, 0x15, 0x9d, 0xc5, 0x4b, 0x9c, 0x56, 0x20, 0x9f, 0x67, 0x14, 0xc8, 0x21, 0x54, 0x69, 0xe0,
	0xca, 0x0d, 0x38, 0x96, 0x8b, 0x45, 0x03, 0x17, 0x57, 0x7f, 0x00, 0x55, 0x77, 0x1a, 0xd9, 0xdc,
	0x0b, 0x03, 0xe3, 0x44, 0x72, 0x56, 0x5c, 0x4e, 0xf5, 0xfb, 0x53, 0xe8, 0x05, 0xc6, 0x69, 0xba,
	0xdf, 0xdf, 0x87, 0x5e, 0x20, 0x02, 0xcb, 0x09, 0xa7, 0x01, 0x77, 0xc3, 0xfb, 0x20, 0xce, 0x3f,
	0x38, 0x88, 0x29, 0x03, 0x2b, 0xa9, 0x94, 0xd9, 0x07, 0x07, 0xcc, 0xea, 0x89, 0xb3, 0x3c, 0x3d,
	0xf1, 0x25, 0xec, 0xc5, 0xe7, 0x9d, 0x4d, 0x7d, 0xdf, 0x8e, 0x04, 0xa9, 0x3e, 0x91, 0x6b, 0xa7,
	0x2a, 0xae, 0x62, 0x1c, 0x0f, 0x83, 0x3d, 0x97, 0x43, 0x9f, 0xab, 0xc3, 0x60, 0xcf, 0x91, 0x0c,
	0xff, 0x55, 0x81, 0xfe, 0x1b, 0xd7, 0x8d, 0x2f, 0x4f, 0x9a, 0xce, 0x7c, 0xf8, 0xe5, 0x69, 0x89,
	0xf5, 0x4a, 0x19, 0xd6, 0xcb, 0x63, 0xa5, 0x9d, 0x0d, 0x25, 0x4d, 0x39, 0x87, 0x61, 0x32, 0x57,
	0x9a, 0x4a, 0xf1, 0x95, 0x66, 0xb7, 0xf8, 0x4a, 0x53, 0xdd, 0xe4, 0x4a, 0x53, 0x2b, 0xbc, 0xd2,
	0x40, 0xe1, 0x95, 0xa6, 0xbe, 0xf1, 0x95, 0xa6, 0xf1, 0xc0, 0x2b, 0x4d, 0x73, 0xe5, 0x95, 0x26,
	0xe7, 0x8a, 0xd2, 0x7a, 0xd0, 0x15, 0xa5, 0xbd, 0xea, 0x8a, 0xb2, 0x44, 0x42, 0x9d, 0x22, 0x12,
	0xda, 0xd3, 0x49, 0xe8, 0xd7, 0xd0, 0x86, 0x29, 0x4a, 0xe8, 0x2e, 0x5f, 0x74, 0x75, 0xfa, 0x39,
	0xc8, 0x17, 0x8d, 0x71, 0xee, 0xed, 0x69, 0x57, 0x85, 0x1e, 0x54, 0x6c, 0x5f, 0x44, 0xa6, 0xd2,
	0x7d, 0xaa, 0xa4, 0x51, 0x81, 0xa1, 0x53, 0x81, 0x79, 0x06, 0xed, 0xb7, 0x36, 0x4b, 0x1e, 0x32,
	0x3e, 0xd2, 0x7b, 0xd2, 0x81, 0xd2, 0x77, 0x6c, 0xa4, 0x02, 0x47, 0x7c, 0x9a, 0x3f, 0x42, 0xff,
	0xfb, 0x44, 0x42, 0xbe, 0xbb, 0xb5, 0x83, 0xc5, 0x7b, 0x8a, 0x48, 0x40, 0x13, 0x1a, 0x61, 0x1a,
	0x98, 0x4f, 0xa8, 0x12, 0x12, 0x75, 0x85, 0x7d, 0x9a, 0x4f, 0xf4, 0x3b, 0xc8, 0xb6, 0xae, 0xf6,
	0xfe, 0xb9, 0x05, 0x5d, 0x21, 0x1a, 0x7e, 0x55, 0x45, 0x96, 0x23, 0xbb, 0x4a, 0x1b, 0xca, 0xae,
	0x9d, 0x1c, 0xd9, 0x25, 0xfc, 0xe7, 0xf6, 0xb5, 0x9c, 0x9e, 0x0c, 0xe5, 0x5d, 0x6e, 0x5f, 0x8b,
	0xa9, 0x99, 0xaf, 0xa1, 0x97, 0xb8, 0x7f, 0x41, 0xb9, 0xed, 0x8d, 0x37, 0x90, 0xb8, 0x7f, 0x6b,
	0x40, 0x3f, 0xd3, 0x4a, 0xa9, 0xb9, 0xff, 0xab, 0xb1, 0xff, 0x71, 0x35, 0xf6, 0x14, 0xda, 0x63,
	0x9b, 0x71, 0x2b, 0xa2, 0x93, 0xb1, 0x4a, 0x6c, 0x92, 0xa5, 0x9a, 0x02, 0x1e, 0x0a, 0x14, 0x0f,
	0xdd, 0x7f, 0x5b, 0x92, 0xed, 0xaf, 0x96, 0x64, 0xdd, 0x22, 0x36, 0x3c, 0x28, 0x64, 0xc3, 0xde,
	0x5a, 0x36, 0xec, 0x67, 0xd9, 0x50, 0xc9, 0xa9, 0xd8, 0x84, 0xc9, 0xc9, 0x19, 0x89, 0x9c, 0x8a,
	0x0d, 0x59, 0x7c, 0x62, 0x74, 0x8a, 0x3c, 0xdc, 0x58, 0xa1, 0x0d, 0xd6, 0x2b, 0xb4, 0xa3, 0x42,
	0x85, 0xf6, 0x68, 0x9d, 0x42, 0x7b, 0x9c, 0x51, 0x68, 0x8f, 0x01, 0xe2, 0x67, 0x28, 0x7f, 0xa4,
	0x14, 0x9c, 0x7a, 0x98, 0xba, 0xf4, 0x47, 0x45, 0x02, 0x2e, 0x9b, 0xbf, 0x4f, 0x0a, 0xf3, 0xf7,
	0x69, 0x61, 0xfe, 0x36, 0x37, 0xce, 0xdf, 0x67, 0x0f, 0xcc, 0xdf, 0x4f, 0x1e, 0xfa, 0x24, 0x79,
	0xfe, 0x80, 0x27, 0xc9, 0xa7, 0x0f, 0xca, 0xf7, 0xcf, 0x56, 0xe6, 0xfb, 0x45, 0x6a, 0x7c, 0xae,
	0xa5, 0xc6, 0xac, 0x68, 0xfd, 0x22, 0x4f, 0xb4, 0xa6, 0x75, 0xe8, 0x6f, 0x34, 0x1d, 0x9a, 0x15,
	0x72, 0x5f, 0xe6, 0x08, 0x39, 0xfd, 0xe6, 0xf2, 0x62, 0xe9, 0xe6, 0x62, 0xfe, 0x15, 0x5f, 0x31,
	0x05, 0x5f, 0x5f, 0x24, 0xaf, 0x69, 0x32, 0x8d, 0xa4, 0x72, 0xfd, 0xd6, 0xf2, 0xb3, 0x60, 0x71,
	0x62, 0xc8, 0x23, 0xee, 0x52, 0x2e, 0x71, 0x9b, 0x3f, 0xe0, 0x6f, 0x08, 0x7d, 0xf8, 0xf5, 0xbf,
	0x21, 0xd6, 0x79, 0xf0, 0xea, 0xef, 0x15, 0x68, 0x48, 0xd9, 0x40, 0xa3, 0x3b, 0xcf, 0xa1, 0xe4,
	0x3d, 0xb4, 0x97, 0x1e, 0x60, 0xc8, 0x11, 0xbe, 0x4c, 0xe4, 0x3f, 0xcb, 0x0c, 0x7a, 0x8b, 0x67,
	0x8b, 0xf4, 0x93, 0x87, 0xf9, 0x19, 0xb9, 0x80, 0xa6, 0xf6, 0x8a, 0x45, 0x0e, 0xd1, 0x34, 0xef,
	0x65, 0xab, 0xa0, 0x97, 0xb7, 0x50, 0x4f, 0xbd, 0x35, 0x91, 0xfe, 0xc2, 0x50, 0x7b, 0xae, 0x1a,
	0x18, 0xd9, 0x8a, 0xa4, 0x8f, 0xf7, 0xd0, 0x59, 0x96, 0x46, 0xe4, 0xd1, 0xc2, 0x3e, 0xab, 0x98,
	0x06, 0x5d, 0xac, 0x5d, 0x12, 0x5d, 0x72, 0x4e, 0x9a, 0x14, 0x52, 0x73, 0xca, 0x93, 0x47, 0x05,
	0x73, 0xfa, 0x08, 0xed, 0x25, 0x6d, 0xa1, 0xd6, 0x38, 0x5f, 0xa7, 0x0c, 0x1e, 0xe5, 0x57, 0x26,
	0xfd, 0x7d, 0x0b, 0x2d, 0xfd, 0x81, 0x9d, 0x0c, 0xb0, 0x45, 0xee, 0x03, 0xff, 0xe0, 0x28, 0xb7,
	0x2e, 0xe9, 0x6c, 0x08, 0xfb, 0x4b, 0x07, 0x0d, 0x83, 0x33, 0x69, 0x95, 0x13, 0x01, 0xca, 0xc1,
	0x15, 0xe7, 0x53, 0x4e, 0x78, 0xe9, 0x1f, 0xda, 0xa2, 0xbf, 0x9c, 0xff, 0x71, 0x8b, 0xfe, 0xf2,
	0x7e, 0xbb, 0x99, 0x9f, 0x11, 0x0f, 0x8c, 0x55, 0x7f, 0xfb, 0xc8, 0x13, 0x79, 0x5a, 0x8b, 0x7f,
	0x31, 0x0e, 0xce, 0xd7, 0x58, 0xc5, 0x43, 0x5d, 0x57, 0xf0, 0xd7, 0xe5, 0xeb, 0xff, 0x04, 0x00,
	0x00, 0xff, 0xff, 0x4f, 0x0a, 0x95, 0xd4, 0xcd, 0x1c, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// OrderServiceClient is the client API for OrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OrderServiceClient interface {
	// @Desc    	医生端-我的订单列表
	// <AUTHOR>
	// @Date		2021-10-09
	DoctorOrderList(ctx context.Context, in *DoctorOrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error)
	// @Desc    	医生端-待接诊订单列表
	// <AUTHOR>
	// @Date		2021-10-12
	WaitOrderList(ctx context.Context, in *WaitOrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error)
	// @Desc    	医生端-接诊操作
	// <AUTHOR>
	// @Date		2021-10-12
	OrderAccept(ctx context.Context, in *OrderAcceptRequest, opts ...grpc.CallOption) (*OrderAcceptResponse, error)
	// @Desc    	用户端-修改问诊订单状态
	// <AUTHOR>
	// @Date		2021-10-12
	OrderStateChange(ctx context.Context, in *OrderStateChangeRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//用户端-订单列表
	UserOrderList(ctx context.Context, in *UserOrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error)
	//用户端-订单详情
	UserOrderDetail(ctx context.Context, in *UserOrderDetailRequest, opts ...grpc.CallOption) (*UserOrderDetailResponse, error)
	//用户端-通过医生id和用户id获取最新的一条订单
	GetLatestOrder(ctx context.Context, in *GetLatestOrderRequest, opts ...grpc.CallOption) (*GetLatestOrderResponse, error)
	//用户端-获取用户是否存在有效订单
	GetUserDiagnoseInfo(ctx context.Context, in *GetUserDiagnoseRequest, opts ...grpc.CallOption) (*GetUserDiagnoseResponse, error)
	//获取问诊信息
	GetDiagnoseInfo(ctx context.Context, in *GetDiagnoseInfoRequest, opts ...grpc.CallOption) (*GetDiagnoseInfoResponse, error)
	//追加次数更新
	DiagnoseAddFinishMessage(ctx context.Context, in *DiagnoseAddFinishMessageRequest, opts ...grpc.CallOption) (*DiagnoseAddFinishMessageResponse, error)
}

type orderServiceClient struct {
	cc *grpc.ClientConn
}

func NewOrderServiceClient(cc *grpc.ClientConn) OrderServiceClient {
	return &orderServiceClient{cc}
}

func (c *orderServiceClient) DoctorOrderList(ctx context.Context, in *DoctorOrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error) {
	out := new(OrderListResponse)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/DoctorOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) WaitOrderList(ctx context.Context, in *WaitOrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error) {
	out := new(OrderListResponse)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/WaitOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) OrderAccept(ctx context.Context, in *OrderAcceptRequest, opts ...grpc.CallOption) (*OrderAcceptResponse, error) {
	out := new(OrderAcceptResponse)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/OrderAccept", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) OrderStateChange(ctx context.Context, in *OrderStateChangeRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/OrderStateChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) UserOrderList(ctx context.Context, in *UserOrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error) {
	out := new(OrderListResponse)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/UserOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) UserOrderDetail(ctx context.Context, in *UserOrderDetailRequest, opts ...grpc.CallOption) (*UserOrderDetailResponse, error) {
	out := new(UserOrderDetailResponse)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/UserOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) GetLatestOrder(ctx context.Context, in *GetLatestOrderRequest, opts ...grpc.CallOption) (*GetLatestOrderResponse, error) {
	out := new(GetLatestOrderResponse)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/GetLatestOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) GetUserDiagnoseInfo(ctx context.Context, in *GetUserDiagnoseRequest, opts ...grpc.CallOption) (*GetUserDiagnoseResponse, error) {
	out := new(GetUserDiagnoseResponse)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/GetUserDiagnoseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) GetDiagnoseInfo(ctx context.Context, in *GetDiagnoseInfoRequest, opts ...grpc.CallOption) (*GetDiagnoseInfoResponse, error) {
	out := new(GetDiagnoseInfoResponse)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/GetDiagnoseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) DiagnoseAddFinishMessage(ctx context.Context, in *DiagnoseAddFinishMessageRequest, opts ...grpc.CallOption) (*DiagnoseAddFinishMessageResponse, error) {
	out := new(DiagnoseAddFinishMessageResponse)
	err := c.cc.Invoke(ctx, "/dgc.OrderService/DiagnoseAddFinishMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderServiceServer is the server API for OrderService service.
type OrderServiceServer interface {
	// @Desc    	医生端-我的订单列表
	// <AUTHOR>
	// @Date		2021-10-09
	DoctorOrderList(context.Context, *DoctorOrderListRequest) (*OrderListResponse, error)
	// @Desc    	医生端-待接诊订单列表
	// <AUTHOR>
	// @Date		2021-10-12
	WaitOrderList(context.Context, *WaitOrderListRequest) (*OrderListResponse, error)
	// @Desc    	医生端-接诊操作
	// <AUTHOR>
	// @Date		2021-10-12
	OrderAccept(context.Context, *OrderAcceptRequest) (*OrderAcceptResponse, error)
	// @Desc    	用户端-修改问诊订单状态
	// <AUTHOR>
	// @Date		2021-10-12
	OrderStateChange(context.Context, *OrderStateChangeRequest) (*BaseResponseNew, error)
	//用户端-订单列表
	UserOrderList(context.Context, *UserOrderListRequest) (*OrderListResponse, error)
	//用户端-订单详情
	UserOrderDetail(context.Context, *UserOrderDetailRequest) (*UserOrderDetailResponse, error)
	//用户端-通过医生id和用户id获取最新的一条订单
	GetLatestOrder(context.Context, *GetLatestOrderRequest) (*GetLatestOrderResponse, error)
	//用户端-获取用户是否存在有效订单
	GetUserDiagnoseInfo(context.Context, *GetUserDiagnoseRequest) (*GetUserDiagnoseResponse, error)
	//获取问诊信息
	GetDiagnoseInfo(context.Context, *GetDiagnoseInfoRequest) (*GetDiagnoseInfoResponse, error)
	//追加次数更新
	DiagnoseAddFinishMessage(context.Context, *DiagnoseAddFinishMessageRequest) (*DiagnoseAddFinishMessageResponse, error)
}

// UnimplementedOrderServiceServer can be embedded to have forward compatible implementations.
type UnimplementedOrderServiceServer struct {
}

func (*UnimplementedOrderServiceServer) DoctorOrderList(ctx context.Context, req *DoctorOrderListRequest) (*OrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoctorOrderList not implemented")
}
func (*UnimplementedOrderServiceServer) WaitOrderList(ctx context.Context, req *WaitOrderListRequest) (*OrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WaitOrderList not implemented")
}
func (*UnimplementedOrderServiceServer) OrderAccept(ctx context.Context, req *OrderAcceptRequest) (*OrderAcceptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderAccept not implemented")
}
func (*UnimplementedOrderServiceServer) OrderStateChange(ctx context.Context, req *OrderStateChangeRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderStateChange not implemented")
}
func (*UnimplementedOrderServiceServer) UserOrderList(ctx context.Context, req *UserOrderListRequest) (*OrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserOrderList not implemented")
}
func (*UnimplementedOrderServiceServer) UserOrderDetail(ctx context.Context, req *UserOrderDetailRequest) (*UserOrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserOrderDetail not implemented")
}
func (*UnimplementedOrderServiceServer) GetLatestOrder(ctx context.Context, req *GetLatestOrderRequest) (*GetLatestOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestOrder not implemented")
}
func (*UnimplementedOrderServiceServer) GetUserDiagnoseInfo(ctx context.Context, req *GetUserDiagnoseRequest) (*GetUserDiagnoseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDiagnoseInfo not implemented")
}
func (*UnimplementedOrderServiceServer) GetDiagnoseInfo(ctx context.Context, req *GetDiagnoseInfoRequest) (*GetDiagnoseInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiagnoseInfo not implemented")
}
func (*UnimplementedOrderServiceServer) DiagnoseAddFinishMessage(ctx context.Context, req *DiagnoseAddFinishMessageRequest) (*DiagnoseAddFinishMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiagnoseAddFinishMessage not implemented")
}

func RegisterOrderServiceServer(s *grpc.Server, srv OrderServiceServer) {
	s.RegisterService(&_OrderService_serviceDesc, srv)
}

func _OrderService_DoctorOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoctorOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).DoctorOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/DoctorOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).DoctorOrderList(ctx, req.(*DoctorOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_WaitOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WaitOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).WaitOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/WaitOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).WaitOrderList(ctx, req.(*WaitOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_OrderAccept_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderAcceptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).OrderAccept(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/OrderAccept",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).OrderAccept(ctx, req.(*OrderAcceptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_OrderStateChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderStateChangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).OrderStateChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/OrderStateChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).OrderStateChange(ctx, req.(*OrderStateChangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_UserOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).UserOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/UserOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).UserOrderList(ctx, req.(*UserOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_UserOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).UserOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/UserOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).UserOrderDetail(ctx, req.(*UserOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_GetLatestOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).GetLatestOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/GetLatestOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).GetLatestOrder(ctx, req.(*GetLatestOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_GetUserDiagnoseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDiagnoseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).GetUserDiagnoseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/GetUserDiagnoseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).GetUserDiagnoseInfo(ctx, req.(*GetUserDiagnoseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_GetDiagnoseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiagnoseInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).GetDiagnoseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/GetDiagnoseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).GetDiagnoseInfo(ctx, req.(*GetDiagnoseInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_DiagnoseAddFinishMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiagnoseAddFinishMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).DiagnoseAddFinishMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dgc.OrderService/DiagnoseAddFinishMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).DiagnoseAddFinishMessage(ctx, req.(*DiagnoseAddFinishMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _OrderService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dgc.OrderService",
	HandlerType: (*OrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DoctorOrderList",
			Handler:    _OrderService_DoctorOrderList_Handler,
		},
		{
			MethodName: "WaitOrderList",
			Handler:    _OrderService_WaitOrderList_Handler,
		},
		{
			MethodName: "OrderAccept",
			Handler:    _OrderService_OrderAccept_Handler,
		},
		{
			MethodName: "OrderStateChange",
			Handler:    _OrderService_OrderStateChange_Handler,
		},
		{
			MethodName: "UserOrderList",
			Handler:    _OrderService_UserOrderList_Handler,
		},
		{
			MethodName: "UserOrderDetail",
			Handler:    _OrderService_UserOrderDetail_Handler,
		},
		{
			MethodName: "GetLatestOrder",
			Handler:    _OrderService_GetLatestOrder_Handler,
		},
		{
			MethodName: "GetUserDiagnoseInfo",
			Handler:    _OrderService_GetUserDiagnoseInfo_Handler,
		},
		{
			MethodName: "GetDiagnoseInfo",
			Handler:    _OrderService_GetDiagnoseInfo_Handler,
		},
		{
			MethodName: "DiagnoseAddFinishMessage",
			Handler:    _OrderService_DiagnoseAddFinishMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dgc/order.proto",
}
