package main

import (
	"_/proto/cc"
	"context"
	"fmt"
	"log"
	"time"

	"google.golang.org/grpc"
)

func main() {
	// 连接到gRPC服务器
	conn, err := grpc.Dial("localhost:8080", grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}
	defer conn.Close()

	// 创建客户端
	client := cc.NewMobileCryptoServiceClient(conn)

	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	// 测试手机号加密
	mobile := "13800138000"
	fmt.Printf("原始手机号: %s\n", mobile)

	encryptReq := &cc.MobileEncryptRequest{
		Mobile: mobile,
	}

	encryptResp, err := client.MobileEncrypt(ctx, encryptReq)
	if err != nil {
		log.Fatalf("MobileEncrypt failed: %v", err)
	}

	if encryptResp.Error != "" {
		log.Fatalf("MobileEncrypt error: %s", encryptResp.Error)
	}

	fmt.Printf("加密后: %s\n", encryptResp.Ciphertext)

	// 测试手机号解密
	decryptReq := &cc.MobileDecryptRequest{
		Ciphertext: encryptResp.Ciphertext,
	}

	decryptResp, err := client.MobileDecrypt(ctx, decryptReq)
	if err != nil {
		log.Fatalf("MobileDecrypt failed: %v", err)
	}

	if decryptResp.Error != "" {
		log.Fatalf("MobileDecrypt error: %s", decryptResp.Error)
	}

	fmt.Printf("解密后: %s\n", decryptResp.Mobile)

	// 验证加密解密是否正确
	if decryptResp.Mobile == mobile {
		fmt.Println("✅ 加密解密测试成功!")
	} else {
		fmt.Printf("❌ 加密解密测试失败! 原始: %s, 解密后: %s\n", mobile, decryptResp.Mobile)
	}
}
