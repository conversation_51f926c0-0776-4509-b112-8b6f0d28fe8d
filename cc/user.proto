syntax = "proto3";

package cc;

//阿闻小程序——搜索相关服务
service UserService {
    // 获取用户积分
    rpc UserIntegral(UserIntegralRequest) returns(UserIntegralResponse);
    // 获取用户信息
    rpc UserInfo(UserInfoRequest) returns(UserInfoResponse);
    // 获取用户会员信息
    rpc UserVipInfo(UserInfoRequest) returns(UserVipInfoResponse);
    // 订阅用户专家提醒
    rpc SubscribeExpertRemind(SubscribeExpertRemindRequest) returns(BaseReq);
    // 获取用户订阅数
    rpc GetExpertRemindNum(GetExpertRemindNumRequest) returns(GetExpertRemindNumResponse);
    //会员概况统计
    rpc MemberCounts(MemberCountsRequest) returns(MemberCountsResponse);
    // 检查黑名单
    rpc CheckBlackList(CheckBlackListReq) returns(BaseRes);
    // 通过scrmId 查子龙的customerId
    rpc QueryCustomerId(QueryCustomerIdReq) returns(QueryCustomerIdRes);
}

//数据中心渠道商品模块服务
service DcTaskCustomer {
    rpc ChooseTaskRun (TaskRunVo) returns (BaseReq);
}

message TaskRunVo{
    // 启动类型 1：会员统计 2:重置会员等级
    int32 data = 1;
}

message BaseRes {
    int32 code = 1;
    string message = 2;
}

message BaseReq {
  int32 code = 1;
  string message = 2;
}

message UserIntegralRequest{
    //用户编号
    string memberId = 1;
    //主体ID  11
    int32 orgId=2;
}
message UserIntegralResponse{
    //剩余积分
    int32 integral = 1;
    //最后时间
    string lastTime = 2;
}
message UserInfoRequest{
    //手机号
    string mobile = 1;
    //用户id
    string memberId = 2;
}
message UserInfoResponse{
    int32 id = 1;
    string userId = 2;
    string userImId = 3;
    string userImToken = 4;
    string userName = 5;
    int32 userSex = 6;
    string userMobile = 7;
    int32 userSource = 8;
    int32 userStatus = 9;
    string userAvatar = 10;
    string userBirthday = 11;
    string firstRaisesPet = 12;
    string city = 13;
    string country = 14;
    string province = 15;
    string area = 16;
    string userRemark = 17;
    string createTime = 18;
    string updateTime = 19;
    string bigdataUserId = 20;
    int32 isReal = 21;
    string systemError = 22;
    string businessError = 23;
}

message UserVipInfoResponse{
    repeated userVipInfo data = 1;
}
message userVipInfo{
    //卡号
    string code = 1;
    //卡类型 1:会员卡 2：保障卡
    string type = 2;
    //卡状态 0：开卡中 1：正常 2：退款中 3：已退款 4：已过期
    string state = 3;
    //生效时间
    string effectiveDate = 4;
    //失效时间
    string expirationDate = 5;
}

message SubscribeExpertRemindRequest{
    //用户ID
    string userId = 1;
    //宠物id
    string petId = 2;
    //模板id
    string templateId = 3;
    //1 专家提醒
    int32 type = 4;
}
message GetExpertRemindNumRequest{
    //用户ID
    string userId = 1;
    //宠物id
    string petId = 2;
    //模板id
    string templateId = 3;
    //1 专家提醒
    int32 type = 4;
}
message GetExpertRemindNumResponse{
    int64 num = 1;
}

message MemberCountsRequest {
    string begin_time= 1;
    string end_time = 2;
}
message MemberCountsResponse {
  repeated MemberCounts  data = 1;
}
message MemberCounts {
    //vip等级 0=v0 1=v1 ……
    int32 level = 1;
    //累计会员数
    int32 user_total = 2;
    //新增会员数
    int32 new_user_total = 3;
    //支付会员数
    int32 pay_user_total = 4;
    //领券会员数
    int32 coupon_user_total = 5;
    //领券会员核销率
    float coupon_verify_rate = 6;
    //会员支付金额
    float user_pay_amount = 7;
    //会员支付订单数
    int32 user_order_total = 8;
    //会员客单价
    float user_order_price = 9;
    //会员人数
    int32 user_num = 10;
    //会员人数占比
    float user_num_rate = 11;
    //会员升级人数
    int32 user_upgrade_num = 12;
    //会员占比
    float user_upgrade_num_rate = 13;
    //会员降级人数
    int32 user_downgrade_num = 14;
    //会员降级占比
    float user_downgrade_num_rate = 15;
    //商城领券数
    int32 mall_coupon_total = 16;
    //商城核销率
    float mall_coupon_verify_rate = 17;
    //门店领券数
    int32 shop_coupon_total = 18;
    //门店核销率
    float shop_coupon_verify_rate = 197;
}

// 检查黑名单
message CheckBlackListReq {
    // 手机号
    string mobile =1;
    // 收货手机号
    string receiver_mobile = 2;
}

message QueryCustomerIdReq {
    // 通过scrmId 查子龙的customerId
    string scrm_id = 1;
}

message QueryCustomerIdRes{
    // 状态码，200正常，非200错误
    int32 code = 1;
    // 消息
    string message = 2;
    // 客户id
    repeated string customer_id = 3;
}

