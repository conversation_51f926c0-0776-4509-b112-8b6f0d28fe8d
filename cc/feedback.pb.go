// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/feedback.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type FeedBaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FeedBaseResponse) Reset()         { *m = FeedBaseResponse{} }
func (m *FeedBaseResponse) String() string { return proto.CompactTextString(m) }
func (*FeedBaseResponse) ProtoMessage()    {}
func (*FeedBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{0}
}

func (m *FeedBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FeedBaseResponse.Unmarshal(m, b)
}
func (m *FeedBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FeedBaseResponse.Marshal(b, m, deterministic)
}
func (m *FeedBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FeedBaseResponse.Merge(m, src)
}
func (m *FeedBaseResponse) XXX_Size() int {
	return xxx_messageInfo_FeedBaseResponse.Size(m)
}
func (m *FeedBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FeedBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FeedBaseResponse proto.InternalMessageInfo

func (m *FeedBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *FeedBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FeedBaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//添加反馈请求参数
type AddFeedbackRequest struct {
	// 反馈内容 必传 不能为空 最长不超过200字
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content"`
	//反馈类型 0：建议  1咨询 2投诉 不传则默认为0
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	//电话
	Phone string `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone"`
	//是否是草稿 0 不是 1 是 不传则默认为0
	IsDraft int32 `protobuf:"varint,4,opt,name=is_draft,json=isDraft,proto3" json:"is_draft"`
	//反馈的图片 多张使用英文逗号分隔
	Images string `protobuf:"bytes,5,opt,name=images,proto3" json:"images"`
	//客户端信息
	ClientInfo string `protobuf:"bytes,6,opt,name=client_info,json=clientInfo,proto3" json:"client_info"`
	//用户id
	UserId string `protobuf:"bytes,7,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//用户名称
	UserName string `protobuf:"bytes,8,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//小程序主体：1-默认，2-极宠家
	OrgId                int32    `protobuf:"varint,9,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFeedbackRequest) Reset()         { *m = AddFeedbackRequest{} }
func (m *AddFeedbackRequest) String() string { return proto.CompactTextString(m) }
func (*AddFeedbackRequest) ProtoMessage()    {}
func (*AddFeedbackRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{1}
}

func (m *AddFeedbackRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFeedbackRequest.Unmarshal(m, b)
}
func (m *AddFeedbackRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFeedbackRequest.Marshal(b, m, deterministic)
}
func (m *AddFeedbackRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFeedbackRequest.Merge(m, src)
}
func (m *AddFeedbackRequest) XXX_Size() int {
	return xxx_messageInfo_AddFeedbackRequest.Size(m)
}
func (m *AddFeedbackRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFeedbackRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddFeedbackRequest proto.InternalMessageInfo

func (m *AddFeedbackRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AddFeedbackRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AddFeedbackRequest) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *AddFeedbackRequest) GetIsDraft() int32 {
	if m != nil {
		return m.IsDraft
	}
	return 0
}

func (m *AddFeedbackRequest) GetImages() string {
	if m != nil {
		return m.Images
	}
	return ""
}

func (m *AddFeedbackRequest) GetClientInfo() string {
	if m != nil {
		return m.ClientInfo
	}
	return ""
}

func (m *AddFeedbackRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *AddFeedbackRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *AddFeedbackRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//反馈回复请求参数
type AddFeedbackCommentRequest struct {
	// 回复内容 必传 不能为空 最长不超过200字
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content"`
	//所属反馈id
	FeedbackId int32 `protobuf:"varint,2,opt,name=feedback_id,json=feedbackId,proto3" json:"feedback_id"`
	//用户id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//用户id
	UserName string `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//是否是官方 回复 1是 0否
	IsOfficial           int32    `protobuf:"varint,5,opt,name=is_official,json=isOfficial,proto3" json:"is_official"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFeedbackCommentRequest) Reset()         { *m = AddFeedbackCommentRequest{} }
func (m *AddFeedbackCommentRequest) String() string { return proto.CompactTextString(m) }
func (*AddFeedbackCommentRequest) ProtoMessage()    {}
func (*AddFeedbackCommentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{2}
}

func (m *AddFeedbackCommentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFeedbackCommentRequest.Unmarshal(m, b)
}
func (m *AddFeedbackCommentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFeedbackCommentRequest.Marshal(b, m, deterministic)
}
func (m *AddFeedbackCommentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFeedbackCommentRequest.Merge(m, src)
}
func (m *AddFeedbackCommentRequest) XXX_Size() int {
	return xxx_messageInfo_AddFeedbackCommentRequest.Size(m)
}
func (m *AddFeedbackCommentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFeedbackCommentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddFeedbackCommentRequest proto.InternalMessageInfo

func (m *AddFeedbackCommentRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AddFeedbackCommentRequest) GetFeedbackId() int32 {
	if m != nil {
		return m.FeedbackId
	}
	return 0
}

func (m *AddFeedbackCommentRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *AddFeedbackCommentRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *AddFeedbackCommentRequest) GetIsOfficial() int32 {
	if m != nil {
		return m.IsOfficial
	}
	return 0
}

//获取反馈详情请求参数
type GetFeedbackRequest struct {
	//反馈反馈ID号
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//用户id
	UserId               string   `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFeedbackRequest) Reset()         { *m = GetFeedbackRequest{} }
func (m *GetFeedbackRequest) String() string { return proto.CompactTextString(m) }
func (*GetFeedbackRequest) ProtoMessage()    {}
func (*GetFeedbackRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{3}
}

func (m *GetFeedbackRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFeedbackRequest.Unmarshal(m, b)
}
func (m *GetFeedbackRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFeedbackRequest.Marshal(b, m, deterministic)
}
func (m *GetFeedbackRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFeedbackRequest.Merge(m, src)
}
func (m *GetFeedbackRequest) XXX_Size() int {
	return xxx_messageInfo_GetFeedbackRequest.Size(m)
}
func (m *GetFeedbackRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFeedbackRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFeedbackRequest proto.InternalMessageInfo

func (m *GetFeedbackRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetFeedbackRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

//获取反馈详情请求参数
type GetFeedbackResponse struct {
	Code                 int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string    `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *Feedback `protobuf:"bytes,4,opt,name=Data,proto3" json:"Data"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetFeedbackResponse) Reset()         { *m = GetFeedbackResponse{} }
func (m *GetFeedbackResponse) String() string { return proto.CompactTextString(m) }
func (*GetFeedbackResponse) ProtoMessage()    {}
func (*GetFeedbackResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{4}
}

func (m *GetFeedbackResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFeedbackResponse.Unmarshal(m, b)
}
func (m *GetFeedbackResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFeedbackResponse.Marshal(b, m, deterministic)
}
func (m *GetFeedbackResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFeedbackResponse.Merge(m, src)
}
func (m *GetFeedbackResponse) XXX_Size() int {
	return xxx_messageInfo_GetFeedbackResponse.Size(m)
}
func (m *GetFeedbackResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFeedbackResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFeedbackResponse proto.InternalMessageInfo

func (m *GetFeedbackResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetFeedbackResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetFeedbackResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetFeedbackResponse) GetData() *Feedback {
	if m != nil {
		return m.Data
	}
	return nil
}

//获取反馈详情返回参数
type Feedback struct {
	//反馈id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 反馈内容
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	//反馈类型 0：建议  1咨询 2投诉
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	//电话
	Phone string `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone"`
	//反馈的图片 多张使用英文逗号分隔
	Images string `protobuf:"bytes,5,opt,name=images,proto3" json:"images"`
	//是否是草稿 0 不是 1 是
	IsDraft int32 `protobuf:"varint,6,opt,name=is_draft,json=isDraft,proto3" json:"is_draft"`
	//用户id
	UserId string `protobuf:"bytes,7,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//用户名称
	UserName string `protobuf:"bytes,8,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//客户端信息
	ClientInfo string `protobuf:"bytes,9,opt,name=client_info,json=clientInfo,proto3" json:"client_info"`
	//创建时间
	CreateTime           string   `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Feedback) Reset()         { *m = Feedback{} }
func (m *Feedback) String() string { return proto.CompactTextString(m) }
func (*Feedback) ProtoMessage()    {}
func (*Feedback) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{5}
}

func (m *Feedback) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Feedback.Unmarshal(m, b)
}
func (m *Feedback) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Feedback.Marshal(b, m, deterministic)
}
func (m *Feedback) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Feedback.Merge(m, src)
}
func (m *Feedback) XXX_Size() int {
	return xxx_messageInfo_Feedback.Size(m)
}
func (m *Feedback) XXX_DiscardUnknown() {
	xxx_messageInfo_Feedback.DiscardUnknown(m)
}

var xxx_messageInfo_Feedback proto.InternalMessageInfo

func (m *Feedback) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Feedback) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *Feedback) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Feedback) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *Feedback) GetImages() string {
	if m != nil {
		return m.Images
	}
	return ""
}

func (m *Feedback) GetIsDraft() int32 {
	if m != nil {
		return m.IsDraft
	}
	return 0
}

func (m *Feedback) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *Feedback) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *Feedback) GetClientInfo() string {
	if m != nil {
		return m.ClientInfo
	}
	return ""
}

func (m *Feedback) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

//获取反馈列表请求参数
type GetFeedbackListRequest struct {
	//当前多少页 从1开始 不传默认为 1
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	//每页多少条数据 不传默认为 15
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	//用户手机号
	Phone string `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone"`
	//用户id
	UserId string `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//创建时间区间
	CreateTime string `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//反馈内容 模糊搜索
	Content string `protobuf:"bytes,6,opt,name=content,proto3" json:"content"`
	//查询时间节点，进查询该时间之后的数据
	StartTime string `protobuf:"bytes,7,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//反馈类型 0：建议  1咨询 2投诉 不传则默认为0
	Type int32 `protobuf:"varint,8,opt,name=type,proto3" json:"type"`
	//小程序主体：1-默认，2-极宠家
	OrgId                int32    `protobuf:"varint,9,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFeedbackListRequest) Reset()         { *m = GetFeedbackListRequest{} }
func (m *GetFeedbackListRequest) String() string { return proto.CompactTextString(m) }
func (*GetFeedbackListRequest) ProtoMessage()    {}
func (*GetFeedbackListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{6}
}

func (m *GetFeedbackListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFeedbackListRequest.Unmarshal(m, b)
}
func (m *GetFeedbackListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFeedbackListRequest.Marshal(b, m, deterministic)
}
func (m *GetFeedbackListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFeedbackListRequest.Merge(m, src)
}
func (m *GetFeedbackListRequest) XXX_Size() int {
	return xxx_messageInfo_GetFeedbackListRequest.Size(m)
}
func (m *GetFeedbackListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFeedbackListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFeedbackListRequest proto.InternalMessageInfo

func (m *GetFeedbackListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetFeedbackListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetFeedbackListRequest) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *GetFeedbackListRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetFeedbackListRequest) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *GetFeedbackListRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GetFeedbackListRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GetFeedbackListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetFeedbackListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//获取反馈列表返回参数
type GetFeedbackListResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//反馈列表
	Data                 []*Feedback `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetFeedbackListResponse) Reset()         { *m = GetFeedbackListResponse{} }
func (m *GetFeedbackListResponse) String() string { return proto.CompactTextString(m) }
func (*GetFeedbackListResponse) ProtoMessage()    {}
func (*GetFeedbackListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{7}
}

func (m *GetFeedbackListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFeedbackListResponse.Unmarshal(m, b)
}
func (m *GetFeedbackListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFeedbackListResponse.Marshal(b, m, deterministic)
}
func (m *GetFeedbackListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFeedbackListResponse.Merge(m, src)
}
func (m *GetFeedbackListResponse) XXX_Size() int {
	return xxx_messageInfo_GetFeedbackListResponse.Size(m)
}
func (m *GetFeedbackListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFeedbackListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFeedbackListResponse proto.InternalMessageInfo

func (m *GetFeedbackListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetFeedbackListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetFeedbackListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetFeedbackListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetFeedbackListResponse) GetData() []*Feedback {
	if m != nil {
		return m.Data
	}
	return nil
}

//获取反馈列表请求参数
type GetFeedbackCommentListRequest struct {
	//当前多少页 从1开始 不传默认为 1
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	//每页多少条数据 不传默认为 15
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	//所属反馈id
	FeedbackId           int32    `protobuf:"varint,3,opt,name=feedback_id,json=feedbackId,proto3" json:"feedback_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFeedbackCommentListRequest) Reset()         { *m = GetFeedbackCommentListRequest{} }
func (m *GetFeedbackCommentListRequest) String() string { return proto.CompactTextString(m) }
func (*GetFeedbackCommentListRequest) ProtoMessage()    {}
func (*GetFeedbackCommentListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{8}
}

func (m *GetFeedbackCommentListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFeedbackCommentListRequest.Unmarshal(m, b)
}
func (m *GetFeedbackCommentListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFeedbackCommentListRequest.Marshal(b, m, deterministic)
}
func (m *GetFeedbackCommentListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFeedbackCommentListRequest.Merge(m, src)
}
func (m *GetFeedbackCommentListRequest) XXX_Size() int {
	return xxx_messageInfo_GetFeedbackCommentListRequest.Size(m)
}
func (m *GetFeedbackCommentListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFeedbackCommentListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFeedbackCommentListRequest proto.InternalMessageInfo

func (m *GetFeedbackCommentListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetFeedbackCommentListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetFeedbackCommentListRequest) GetFeedbackId() int32 {
	if m != nil {
		return m.FeedbackId
	}
	return 0
}

//获取反馈列表返回参数
type GetFeedbackCommentListResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总条数
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//反馈列表
	Data                 []*FeedbackComment `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetFeedbackCommentListResponse) Reset()         { *m = GetFeedbackCommentListResponse{} }
func (m *GetFeedbackCommentListResponse) String() string { return proto.CompactTextString(m) }
func (*GetFeedbackCommentListResponse) ProtoMessage()    {}
func (*GetFeedbackCommentListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{9}
}

func (m *GetFeedbackCommentListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFeedbackCommentListResponse.Unmarshal(m, b)
}
func (m *GetFeedbackCommentListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFeedbackCommentListResponse.Marshal(b, m, deterministic)
}
func (m *GetFeedbackCommentListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFeedbackCommentListResponse.Merge(m, src)
}
func (m *GetFeedbackCommentListResponse) XXX_Size() int {
	return xxx_messageInfo_GetFeedbackCommentListResponse.Size(m)
}
func (m *GetFeedbackCommentListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFeedbackCommentListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFeedbackCommentListResponse proto.InternalMessageInfo

func (m *GetFeedbackCommentListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetFeedbackCommentListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetFeedbackCommentListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetFeedbackCommentListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetFeedbackCommentListResponse) GetData() []*FeedbackComment {
	if m != nil {
		return m.Data
	}
	return nil
}

//获取反馈详情返回参数
type FeedbackComment struct {
	// 反馈内容
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content"`
	//是否是阿闻官方回复 1是 0 否
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//用户昵称
	UserName string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//回复时间
	CreateTime string `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//是否是官方 回复 1是 0否
	IsOfficial           int32    `protobuf:"varint,5,opt,name=is_official,json=isOfficial,proto3" json:"is_official"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FeedbackComment) Reset()         { *m = FeedbackComment{} }
func (m *FeedbackComment) String() string { return proto.CompactTextString(m) }
func (*FeedbackComment) ProtoMessage()    {}
func (*FeedbackComment) Descriptor() ([]byte, []int) {
	return fileDescriptor_0fc82193d8023649, []int{10}
}

func (m *FeedbackComment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FeedbackComment.Unmarshal(m, b)
}
func (m *FeedbackComment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FeedbackComment.Marshal(b, m, deterministic)
}
func (m *FeedbackComment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FeedbackComment.Merge(m, src)
}
func (m *FeedbackComment) XXX_Size() int {
	return xxx_messageInfo_FeedbackComment.Size(m)
}
func (m *FeedbackComment) XXX_DiscardUnknown() {
	xxx_messageInfo_FeedbackComment.DiscardUnknown(m)
}

var xxx_messageInfo_FeedbackComment proto.InternalMessageInfo

func (m *FeedbackComment) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *FeedbackComment) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *FeedbackComment) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *FeedbackComment) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *FeedbackComment) GetIsOfficial() int32 {
	if m != nil {
		return m.IsOfficial
	}
	return 0
}

func init() {
	proto.RegisterType((*FeedBaseResponse)(nil), "cc.FeedBaseResponse")
	proto.RegisterType((*AddFeedbackRequest)(nil), "cc.AddFeedbackRequest")
	proto.RegisterType((*AddFeedbackCommentRequest)(nil), "cc.AddFeedbackCommentRequest")
	proto.RegisterType((*GetFeedbackRequest)(nil), "cc.GetFeedbackRequest")
	proto.RegisterType((*GetFeedbackResponse)(nil), "cc.GetFeedbackResponse")
	proto.RegisterType((*Feedback)(nil), "cc.Feedback")
	proto.RegisterType((*GetFeedbackListRequest)(nil), "cc.GetFeedbackListRequest")
	proto.RegisterType((*GetFeedbackListResponse)(nil), "cc.GetFeedbackListResponse")
	proto.RegisterType((*GetFeedbackCommentListRequest)(nil), "cc.GetFeedbackCommentListRequest")
	proto.RegisterType((*GetFeedbackCommentListResponse)(nil), "cc.GetFeedbackCommentListResponse")
	proto.RegisterType((*FeedbackComment)(nil), "cc.FeedbackComment")
}

func init() { proto.RegisterFile("cc/feedback.proto", fileDescriptor_0fc82193d8023649) }

var fileDescriptor_0fc82193d8023649 = []byte{
	// 709 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0xcd, 0x6e, 0xd3, 0x4a,
	0x14, 0x96, 0xff, 0x92, 0xf8, 0xe4, 0xea, 0xf6, 0xde, 0x69, 0x69, 0xdd, 0x94, 0xd2, 0xe0, 0x0d,
	0x5d, 0x15, 0xa9, 0x2c, 0x11, 0x48, 0x40, 0x55, 0x14, 0x84, 0x40, 0x72, 0x11, 0xdb, 0x68, 0x6a,
	0x9f, 0x84, 0x11, 0xb5, 0x27, 0x78, 0xa6, 0x08, 0xca, 0xbe, 0x12, 0x0f, 0xc0, 0x13, 0x74, 0xcd,
	0x03, 0xb2, 0x43, 0x33, 0xb6, 0xdb, 0xb1, 0x1d, 0xb7, 0x12, 0x2a, 0x3b, 0x9f, 0x73, 0xe6, 0xfc,
	0x7f, 0xf3, 0x8d, 0xe1, 0xff, 0x38, 0x7e, 0x38, 0x43, 0x4c, 0x8e, 0x69, 0xfc, 0x71, 0x6f, 0x91,
	0x73, 0xc9, 0x89, 0x1d, 0xc7, 0xe1, 0x7b, 0xf8, 0xef, 0x10, 0x31, 0x79, 0x4e, 0x05, 0x46, 0x28,
	0x16, 0x3c, 0x13, 0x48, 0x08, 0xb8, 0x31, 0x4f, 0x30, 0xb0, 0xc6, 0xd6, 0xae, 0x17, 0xe9, 0x6f,
	0x12, 0x40, 0x3f, 0x45, 0x21, 0xe8, 0x1c, 0x03, 0x7b, 0x6c, 0xed, 0xfa, 0x51, 0x25, 0x92, 0x35,
	0xf0, 0x30, 0xcf, 0x79, 0x1e, 0x38, 0x5a, 0x5f, 0x08, 0xe1, 0x2f, 0x0b, 0xc8, 0xb3, 0x24, 0x39,
	0x2c, 0x33, 0x46, 0xf8, 0xe9, 0x14, 0x85, 0x54, 0x61, 0x62, 0x9e, 0x49, 0xcc, 0xa4, 0x8e, 0xee,
	0x47, 0x95, 0xa8, 0x92, 0xca, 0xaf, 0x8b, 0x22, 0xba, 0x17, 0xe9, 0x6f, 0x15, 0x7a, 0xf1, 0x81,
	0x67, 0x58, 0x85, 0xd6, 0x02, 0xd9, 0x84, 0x01, 0x13, 0xd3, 0x24, 0xa7, 0x33, 0x19, 0xb8, 0xfa,
	0x74, 0x9f, 0x89, 0x03, 0x25, 0x92, 0x75, 0xe8, 0xb1, 0x94, 0xce, 0x51, 0x04, 0x9e, 0xf6, 0x28,
	0x25, 0xb2, 0x03, 0xc3, 0xf8, 0x84, 0x61, 0x26, 0xa7, 0x2c, 0x9b, 0xf1, 0xa0, 0xa7, 0x8d, 0x50,
	0xa8, 0x26, 0xd9, 0x8c, 0x93, 0x0d, 0xe8, 0x9f, 0x0a, 0xcc, 0xa7, 0x2c, 0x09, 0xfa, 0x85, 0xa7,
	0x12, 0x27, 0x09, 0xd9, 0x02, 0x5f, 0x1b, 0x32, 0x9a, 0x62, 0x30, 0xd0, 0xa6, 0x81, 0x52, 0xbc,
	0xa1, 0x29, 0x92, 0x3b, 0xd0, 0xe3, 0xf9, 0x5c, 0x39, 0xf9, 0xba, 0x0e, 0x8f, 0xe7, 0xf3, 0x49,
	0x12, 0xfe, 0xb4, 0x60, 0xd3, 0xe8, 0xfd, 0x05, 0x4f, 0x53, 0xcc, 0xe4, 0xcd, 0x23, 0xd8, 0x81,
	0x61, 0xb5, 0x21, 0x15, 0xb3, 0x98, 0x04, 0x54, 0xaa, 0x49, 0x62, 0x56, 0xe9, 0x74, 0x57, 0xe9,
	0x36, 0xaa, 0xdc, 0x81, 0x21, 0x13, 0x53, 0x3e, 0x9b, 0xb1, 0x98, 0xd1, 0x13, 0x3d, 0x19, 0x2f,
	0x02, 0x26, 0xde, 0x96, 0x9a, 0xf0, 0x09, 0x90, 0x97, 0x28, 0x9b, 0xab, 0xfa, 0x17, 0x6c, 0x96,
	0x94, 0x18, 0xb0, 0x59, 0x2d, 0xb9, 0x6d, 0x26, 0x0f, 0xbf, 0xc1, 0x6a, 0xcd, 0xfd, 0xf6, 0x50,
	0x44, 0xc6, 0xe0, 0x1e, 0x50, 0x49, 0x75, 0x4b, 0xc3, 0xfd, 0x7f, 0xf6, 0xe2, 0x78, 0xef, 0x32,
	0x8f, 0xb6, 0x84, 0xdf, 0x6d, 0x18, 0x54, 0xaa, 0x56, 0xc9, 0xc6, 0xa8, 0xed, 0xe5, 0x68, 0x73,
	0x96, 0xa1, 0xcd, 0x35, 0xd1, 0xd6, 0x05, 0x29, 0x13, 0x85, 0xbd, 0x3a, 0x0a, 0xff, 0x0c, 0x4c,
	0x0d, 0x8c, 0xfa, 0x2d, 0x8c, 0xaa, 0x03, 0x39, 0x52, 0x89, 0x53, 0xc9, 0x52, 0x0c, 0xa0, 0x3c,
	0xa0, 0x55, 0xef, 0x58, 0x8a, 0xe1, 0xb9, 0x0d, 0xeb, 0xc6, 0x26, 0x5e, 0x33, 0x71, 0x09, 0xba,
	0xbb, 0xe0, 0x2f, 0xe8, 0x1c, 0x27, 0x59, 0x82, 0x5f, 0xca, 0x01, 0x5d, 0x29, 0xc8, 0x08, 0x06,
	0x4a, 0x38, 0x62, 0x67, 0xd5, 0xfd, 0xbb, 0x94, 0x3b, 0xee, 0xa0, 0xd1, 0xa2, 0x5b, 0x6b, 0xb1,
	0x51, 0xa4, 0xd7, 0x2c, 0xd2, 0xdc, 0x49, 0xaf, 0xbe, 0x93, 0x6d, 0x00, 0x21, 0x69, 0x2e, 0x0b,
	0xcf, 0x62, 0x72, 0xbe, 0xd6, 0x68, 0xc7, 0x6a, 0x65, 0x03, 0x63, 0x65, 0x1d, 0x17, 0xf0, 0x87,
	0x05, 0x1b, 0xad, 0x41, 0xdc, 0x22, 0x2c, 0xd7, 0xc0, 0x93, 0x5c, 0xd2, 0x93, 0x92, 0x7e, 0x0a,
	0x41, 0x81, 0x35, 0x51, 0x60, 0xf5, 0xc6, 0x4e, 0x1b, 0xac, 0xca, 0x12, 0x9e, 0xc1, 0xb6, 0x51,
	0x56, 0xc9, 0x0b, 0xb7, 0xb3, 0xa6, 0x06, 0x77, 0x38, 0x4d, 0xee, 0x08, 0x2f, 0x2c, 0xb8, 0xd7,
	0x95, 0xfc, 0xaf, 0x8f, 0xe6, 0x41, 0x6d, 0x34, 0xab, 0xe6, 0x68, 0x2a, 0x76, 0x2c, 0x26, 0x74,
	0x61, 0xc1, 0x4a, 0xc3, 0x72, 0x0d, 0x61, 0x76, 0x51, 0x52, 0xfd, 0xa2, 0x39, 0x4b, 0x2e, 0x9a,
	0x01, 0x51, 0xb7, 0x05, 0xd1, 0x9b, 0x08, 0x73, 0xff, 0xdc, 0xb9, 0xaa, 0xf2, 0x08, 0xf3, 0xcf,
	0x2c, 0x46, 0xf2, 0x18, 0x86, 0x06, 0xe7, 0x93, 0x75, 0xd5, 0x63, 0xfb, 0x01, 0x1c, 0xad, 0x55,
	0xbd, 0xd7, 0x5e, 0xdc, 0xa7, 0x30, 0x34, 0x76, 0x53, 0x38, 0xb7, 0x29, 0x79, 0xb4, 0xd1, 0xd2,
	0x97, 0xfe, 0xaf, 0x60, 0xa5, 0x81, 0x77, 0x32, 0x6a, 0x9c, 0x35, 0x60, 0x36, 0xda, 0x5a, 0x6a,
	0x2b, 0x63, 0x4d, 0x6a, 0x0f, 0x77, 0xb5, 0x84, 0xed, 0x46, 0x3f, 0xf5, 0x47, 0xad, 0xa3, 0xad,
	0x69, 0x8d, 0x8f, 0x0c, 0xc8, 0x91, 0xfb, 0x8d, 0x0a, 0xda, 0x77, 0x61, 0x14, 0x5e, 0x77, 0xa4,
	0x48, 0x70, 0xdc, 0xd3, 0x3f, 0x32, 0x8f, 0x7e, 0x07, 0x00, 0x00, 0xff, 0xff, 0x0f, 0x88, 0xcb,
	0x25, 0xdd, 0x08, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// FeedbackServiceClient is the client API for FeedbackService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FeedbackServiceClient interface {
	//添加用户反馈
	AddFeedback(ctx context.Context, in *AddFeedbackRequest, opts ...grpc.CallOption) (*FeedBaseResponse, error)
	//获取用户反馈详情
	GetFeedback(ctx context.Context, in *GetFeedbackRequest, opts ...grpc.CallOption) (*GetFeedbackResponse, error)
	//获取用户反馈列表
	GetFeedbackList(ctx context.Context, in *GetFeedbackListRequest, opts ...grpc.CallOption) (*GetFeedbackListResponse, error)
	//添加用户反馈回复
	AddFeedbackComment(ctx context.Context, in *AddFeedbackCommentRequest, opts ...grpc.CallOption) (*FeedBaseResponse, error)
	//用户反馈回复列表
	GetFeedbackCommentList(ctx context.Context, in *GetFeedbackCommentListRequest, opts ...grpc.CallOption) (*GetFeedbackCommentListResponse, error)
}

type feedbackServiceClient struct {
	cc *grpc.ClientConn
}

func NewFeedbackServiceClient(cc *grpc.ClientConn) FeedbackServiceClient {
	return &feedbackServiceClient{cc}
}

func (c *feedbackServiceClient) AddFeedback(ctx context.Context, in *AddFeedbackRequest, opts ...grpc.CallOption) (*FeedBaseResponse, error) {
	out := new(FeedBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.FeedbackService/AddFeedback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *feedbackServiceClient) GetFeedback(ctx context.Context, in *GetFeedbackRequest, opts ...grpc.CallOption) (*GetFeedbackResponse, error) {
	out := new(GetFeedbackResponse)
	err := c.cc.Invoke(ctx, "/cc.FeedbackService/GetFeedback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *feedbackServiceClient) GetFeedbackList(ctx context.Context, in *GetFeedbackListRequest, opts ...grpc.CallOption) (*GetFeedbackListResponse, error) {
	out := new(GetFeedbackListResponse)
	err := c.cc.Invoke(ctx, "/cc.FeedbackService/GetFeedbackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *feedbackServiceClient) AddFeedbackComment(ctx context.Context, in *AddFeedbackCommentRequest, opts ...grpc.CallOption) (*FeedBaseResponse, error) {
	out := new(FeedBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.FeedbackService/AddFeedbackComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *feedbackServiceClient) GetFeedbackCommentList(ctx context.Context, in *GetFeedbackCommentListRequest, opts ...grpc.CallOption) (*GetFeedbackCommentListResponse, error) {
	out := new(GetFeedbackCommentListResponse)
	err := c.cc.Invoke(ctx, "/cc.FeedbackService/GetFeedbackCommentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FeedbackServiceServer is the server API for FeedbackService service.
type FeedbackServiceServer interface {
	//添加用户反馈
	AddFeedback(context.Context, *AddFeedbackRequest) (*FeedBaseResponse, error)
	//获取用户反馈详情
	GetFeedback(context.Context, *GetFeedbackRequest) (*GetFeedbackResponse, error)
	//获取用户反馈列表
	GetFeedbackList(context.Context, *GetFeedbackListRequest) (*GetFeedbackListResponse, error)
	//添加用户反馈回复
	AddFeedbackComment(context.Context, *AddFeedbackCommentRequest) (*FeedBaseResponse, error)
	//用户反馈回复列表
	GetFeedbackCommentList(context.Context, *GetFeedbackCommentListRequest) (*GetFeedbackCommentListResponse, error)
}

// UnimplementedFeedbackServiceServer can be embedded to have forward compatible implementations.
type UnimplementedFeedbackServiceServer struct {
}

func (*UnimplementedFeedbackServiceServer) AddFeedback(ctx context.Context, req *AddFeedbackRequest) (*FeedBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddFeedback not implemented")
}
func (*UnimplementedFeedbackServiceServer) GetFeedback(ctx context.Context, req *GetFeedbackRequest) (*GetFeedbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFeedback not implemented")
}
func (*UnimplementedFeedbackServiceServer) GetFeedbackList(ctx context.Context, req *GetFeedbackListRequest) (*GetFeedbackListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFeedbackList not implemented")
}
func (*UnimplementedFeedbackServiceServer) AddFeedbackComment(ctx context.Context, req *AddFeedbackCommentRequest) (*FeedBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddFeedbackComment not implemented")
}
func (*UnimplementedFeedbackServiceServer) GetFeedbackCommentList(ctx context.Context, req *GetFeedbackCommentListRequest) (*GetFeedbackCommentListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFeedbackCommentList not implemented")
}

func RegisterFeedbackServiceServer(s *grpc.Server, srv FeedbackServiceServer) {
	s.RegisterService(&_FeedbackService_serviceDesc, srv)
}

func _FeedbackService_AddFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedbackServiceServer).AddFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.FeedbackService/AddFeedback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedbackServiceServer).AddFeedback(ctx, req.(*AddFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeedbackService_GetFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedbackServiceServer).GetFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.FeedbackService/GetFeedback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedbackServiceServer).GetFeedback(ctx, req.(*GetFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeedbackService_GetFeedbackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFeedbackListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedbackServiceServer).GetFeedbackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.FeedbackService/GetFeedbackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedbackServiceServer).GetFeedbackList(ctx, req.(*GetFeedbackListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeedbackService_AddFeedbackComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFeedbackCommentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedbackServiceServer).AddFeedbackComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.FeedbackService/AddFeedbackComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedbackServiceServer).AddFeedbackComment(ctx, req.(*AddFeedbackCommentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeedbackService_GetFeedbackCommentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFeedbackCommentListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedbackServiceServer).GetFeedbackCommentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.FeedbackService/GetFeedbackCommentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedbackServiceServer).GetFeedbackCommentList(ctx, req.(*GetFeedbackCommentListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _FeedbackService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.FeedbackService",
	HandlerType: (*FeedbackServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddFeedback",
			Handler:    _FeedbackService_AddFeedback_Handler,
		},
		{
			MethodName: "GetFeedback",
			Handler:    _FeedbackService_GetFeedback_Handler,
		},
		{
			MethodName: "GetFeedbackList",
			Handler:    _FeedbackService_GetFeedbackList_Handler,
		},
		{
			MethodName: "AddFeedbackComment",
			Handler:    _FeedbackService_AddFeedbackComment_Handler,
		},
		{
			MethodName: "GetFeedbackCommentList",
			Handler:    _FeedbackService_GetFeedbackCommentList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/feedback.proto",
}
