// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/user.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type TaskRunVo struct {
	// 启动类型 1：会员统计 2:重置会员等级
	Data                 int32    `protobuf:"varint,1,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskRunVo) Reset()         { *m = TaskRunVo{} }
func (m *TaskRunVo) String() string { return proto.CompactTextString(m) }
func (*TaskRunVo) ProtoMessage()    {}
func (*TaskRunVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{0}
}

func (m *TaskRunVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskRunVo.Unmarshal(m, b)
}
func (m *TaskRunVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskRunVo.Marshal(b, m, deterministic)
}
func (m *TaskRunVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskRunVo.Merge(m, src)
}
func (m *TaskRunVo) XXX_Size() int {
	return xxx_messageInfo_TaskRunVo.Size(m)
}
func (m *TaskRunVo) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskRunVo.DiscardUnknown(m)
}

var xxx_messageInfo_TaskRunVo proto.InternalMessageInfo

func (m *TaskRunVo) GetData() int32 {
	if m != nil {
		return m.Data
	}
	return 0
}

type BaseRes struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRes) Reset()         { *m = BaseRes{} }
func (m *BaseRes) String() string { return proto.CompactTextString(m) }
func (*BaseRes) ProtoMessage()    {}
func (*BaseRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{1}
}

func (m *BaseRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRes.Unmarshal(m, b)
}
func (m *BaseRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRes.Marshal(b, m, deterministic)
}
func (m *BaseRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRes.Merge(m, src)
}
func (m *BaseRes) XXX_Size() int {
	return xxx_messageInfo_BaseRes.Size(m)
}
func (m *BaseRes) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRes.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRes proto.InternalMessageInfo

func (m *BaseRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type BaseReq struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseReq) Reset()         { *m = BaseReq{} }
func (m *BaseReq) String() string { return proto.CompactTextString(m) }
func (*BaseReq) ProtoMessage()    {}
func (*BaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{2}
}

func (m *BaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseReq.Unmarshal(m, b)
}
func (m *BaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseReq.Marshal(b, m, deterministic)
}
func (m *BaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseReq.Merge(m, src)
}
func (m *BaseReq) XXX_Size() int {
	return xxx_messageInfo_BaseReq.Size(m)
}
func (m *BaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_BaseReq proto.InternalMessageInfo

func (m *BaseReq) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseReq) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type UserIntegralRequest struct {
	//用户编号
	MemberId string `protobuf:"bytes,1,opt,name=memberId,proto3" json:"memberId"`
	//主体ID  11
	OrgId                int32    `protobuf:"varint,2,opt,name=orgId,proto3" json:"orgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserIntegralRequest) Reset()         { *m = UserIntegralRequest{} }
func (m *UserIntegralRequest) String() string { return proto.CompactTextString(m) }
func (*UserIntegralRequest) ProtoMessage()    {}
func (*UserIntegralRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{3}
}

func (m *UserIntegralRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserIntegralRequest.Unmarshal(m, b)
}
func (m *UserIntegralRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserIntegralRequest.Marshal(b, m, deterministic)
}
func (m *UserIntegralRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserIntegralRequest.Merge(m, src)
}
func (m *UserIntegralRequest) XXX_Size() int {
	return xxx_messageInfo_UserIntegralRequest.Size(m)
}
func (m *UserIntegralRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserIntegralRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserIntegralRequest proto.InternalMessageInfo

func (m *UserIntegralRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *UserIntegralRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type UserIntegralResponse struct {
	//剩余积分
	Integral int32 `protobuf:"varint,1,opt,name=integral,proto3" json:"integral"`
	//最后时间
	LastTime             string   `protobuf:"bytes,2,opt,name=lastTime,proto3" json:"lastTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserIntegralResponse) Reset()         { *m = UserIntegralResponse{} }
func (m *UserIntegralResponse) String() string { return proto.CompactTextString(m) }
func (*UserIntegralResponse) ProtoMessage()    {}
func (*UserIntegralResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{4}
}

func (m *UserIntegralResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserIntegralResponse.Unmarshal(m, b)
}
func (m *UserIntegralResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserIntegralResponse.Marshal(b, m, deterministic)
}
func (m *UserIntegralResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserIntegralResponse.Merge(m, src)
}
func (m *UserIntegralResponse) XXX_Size() int {
	return xxx_messageInfo_UserIntegralResponse.Size(m)
}
func (m *UserIntegralResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserIntegralResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserIntegralResponse proto.InternalMessageInfo

func (m *UserIntegralResponse) GetIntegral() int32 {
	if m != nil {
		return m.Integral
	}
	return 0
}

func (m *UserIntegralResponse) GetLastTime() string {
	if m != nil {
		return m.LastTime
	}
	return ""
}

type UserInfoRequest struct {
	//手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//用户id
	MemberId             string   `protobuf:"bytes,2,opt,name=memberId,proto3" json:"memberId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfoRequest) Reset()         { *m = UserInfoRequest{} }
func (m *UserInfoRequest) String() string { return proto.CompactTextString(m) }
func (*UserInfoRequest) ProtoMessage()    {}
func (*UserInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{5}
}

func (m *UserInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoRequest.Unmarshal(m, b)
}
func (m *UserInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoRequest.Marshal(b, m, deterministic)
}
func (m *UserInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoRequest.Merge(m, src)
}
func (m *UserInfoRequest) XXX_Size() int {
	return xxx_messageInfo_UserInfoRequest.Size(m)
}
func (m *UserInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoRequest proto.InternalMessageInfo

func (m *UserInfoRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *UserInfoRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

type UserInfoResponse struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	UserId               string   `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId"`
	UserImId             string   `protobuf:"bytes,3,opt,name=userImId,proto3" json:"userImId"`
	UserImToken          string   `protobuf:"bytes,4,opt,name=userImToken,proto3" json:"userImToken"`
	UserName             string   `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName"`
	UserSex              int32    `protobuf:"varint,6,opt,name=userSex,proto3" json:"userSex"`
	UserMobile           string   `protobuf:"bytes,7,opt,name=userMobile,proto3" json:"userMobile"`
	UserSource           int32    `protobuf:"varint,8,opt,name=userSource,proto3" json:"userSource"`
	UserStatus           int32    `protobuf:"varint,9,opt,name=userStatus,proto3" json:"userStatus"`
	UserAvatar           string   `protobuf:"bytes,10,opt,name=userAvatar,proto3" json:"userAvatar"`
	UserBirthday         string   `protobuf:"bytes,11,opt,name=userBirthday,proto3" json:"userBirthday"`
	FirstRaisesPet       string   `protobuf:"bytes,12,opt,name=firstRaisesPet,proto3" json:"firstRaisesPet"`
	City                 string   `protobuf:"bytes,13,opt,name=city,proto3" json:"city"`
	Country              string   `protobuf:"bytes,14,opt,name=country,proto3" json:"country"`
	Province             string   `protobuf:"bytes,15,opt,name=province,proto3" json:"province"`
	Area                 string   `protobuf:"bytes,16,opt,name=area,proto3" json:"area"`
	UserRemark           string   `protobuf:"bytes,17,opt,name=userRemark,proto3" json:"userRemark"`
	CreateTime           string   `protobuf:"bytes,18,opt,name=createTime,proto3" json:"createTime"`
	UpdateTime           string   `protobuf:"bytes,19,opt,name=updateTime,proto3" json:"updateTime"`
	BigdataUserId        string   `protobuf:"bytes,20,opt,name=bigdataUserId,proto3" json:"bigdataUserId"`
	IsReal               int32    `protobuf:"varint,21,opt,name=isReal,proto3" json:"isReal"`
	SystemError          string   `protobuf:"bytes,22,opt,name=systemError,proto3" json:"systemError"`
	BusinessError        string   `protobuf:"bytes,23,opt,name=businessError,proto3" json:"businessError"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfoResponse) Reset()         { *m = UserInfoResponse{} }
func (m *UserInfoResponse) String() string { return proto.CompactTextString(m) }
func (*UserInfoResponse) ProtoMessage()    {}
func (*UserInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{6}
}

func (m *UserInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoResponse.Unmarshal(m, b)
}
func (m *UserInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoResponse.Marshal(b, m, deterministic)
}
func (m *UserInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoResponse.Merge(m, src)
}
func (m *UserInfoResponse) XXX_Size() int {
	return xxx_messageInfo_UserInfoResponse.Size(m)
}
func (m *UserInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoResponse proto.InternalMessageInfo

func (m *UserInfoResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserInfoResponse) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *UserInfoResponse) GetUserImId() string {
	if m != nil {
		return m.UserImId
	}
	return ""
}

func (m *UserInfoResponse) GetUserImToken() string {
	if m != nil {
		return m.UserImToken
	}
	return ""
}

func (m *UserInfoResponse) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *UserInfoResponse) GetUserSex() int32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *UserInfoResponse) GetUserMobile() string {
	if m != nil {
		return m.UserMobile
	}
	return ""
}

func (m *UserInfoResponse) GetUserSource() int32 {
	if m != nil {
		return m.UserSource
	}
	return 0
}

func (m *UserInfoResponse) GetUserStatus() int32 {
	if m != nil {
		return m.UserStatus
	}
	return 0
}

func (m *UserInfoResponse) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *UserInfoResponse) GetUserBirthday() string {
	if m != nil {
		return m.UserBirthday
	}
	return ""
}

func (m *UserInfoResponse) GetFirstRaisesPet() string {
	if m != nil {
		return m.FirstRaisesPet
	}
	return ""
}

func (m *UserInfoResponse) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *UserInfoResponse) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *UserInfoResponse) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *UserInfoResponse) GetArea() string {
	if m != nil {
		return m.Area
	}
	return ""
}

func (m *UserInfoResponse) GetUserRemark() string {
	if m != nil {
		return m.UserRemark
	}
	return ""
}

func (m *UserInfoResponse) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *UserInfoResponse) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *UserInfoResponse) GetBigdataUserId() string {
	if m != nil {
		return m.BigdataUserId
	}
	return ""
}

func (m *UserInfoResponse) GetIsReal() int32 {
	if m != nil {
		return m.IsReal
	}
	return 0
}

func (m *UserInfoResponse) GetSystemError() string {
	if m != nil {
		return m.SystemError
	}
	return ""
}

func (m *UserInfoResponse) GetBusinessError() string {
	if m != nil {
		return m.BusinessError
	}
	return ""
}

type UserVipInfoResponse struct {
	Data                 []*UserVipInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserVipInfoResponse) Reset()         { *m = UserVipInfoResponse{} }
func (m *UserVipInfoResponse) String() string { return proto.CompactTextString(m) }
func (*UserVipInfoResponse) ProtoMessage()    {}
func (*UserVipInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{7}
}

func (m *UserVipInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserVipInfoResponse.Unmarshal(m, b)
}
func (m *UserVipInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserVipInfoResponse.Marshal(b, m, deterministic)
}
func (m *UserVipInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserVipInfoResponse.Merge(m, src)
}
func (m *UserVipInfoResponse) XXX_Size() int {
	return xxx_messageInfo_UserVipInfoResponse.Size(m)
}
func (m *UserVipInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserVipInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserVipInfoResponse proto.InternalMessageInfo

func (m *UserVipInfoResponse) GetData() []*UserVipInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type UserVipInfo struct {
	//卡号
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	//卡类型 1:会员卡 2：保障卡
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type"`
	//卡状态 0：开卡中 1：正常 2：退款中 3：已退款 4：已过期
	State string `protobuf:"bytes,3,opt,name=state,proto3" json:"state"`
	//生效时间
	EffectiveDate string `protobuf:"bytes,4,opt,name=effectiveDate,proto3" json:"effectiveDate"`
	//失效时间
	ExpirationDate       string   `protobuf:"bytes,5,opt,name=expirationDate,proto3" json:"expirationDate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserVipInfo) Reset()         { *m = UserVipInfo{} }
func (m *UserVipInfo) String() string { return proto.CompactTextString(m) }
func (*UserVipInfo) ProtoMessage()    {}
func (*UserVipInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{8}
}

func (m *UserVipInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserVipInfo.Unmarshal(m, b)
}
func (m *UserVipInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserVipInfo.Marshal(b, m, deterministic)
}
func (m *UserVipInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserVipInfo.Merge(m, src)
}
func (m *UserVipInfo) XXX_Size() int {
	return xxx_messageInfo_UserVipInfo.Size(m)
}
func (m *UserVipInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserVipInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserVipInfo proto.InternalMessageInfo

func (m *UserVipInfo) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *UserVipInfo) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *UserVipInfo) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *UserVipInfo) GetEffectiveDate() string {
	if m != nil {
		return m.EffectiveDate
	}
	return ""
}

func (m *UserVipInfo) GetExpirationDate() string {
	if m != nil {
		return m.ExpirationDate
	}
	return ""
}

type SubscribeExpertRemindRequest struct {
	//用户ID
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//宠物id
	PetId string `protobuf:"bytes,2,opt,name=petId,proto3" json:"petId"`
	//模板id
	TemplateId string `protobuf:"bytes,3,opt,name=templateId,proto3" json:"templateId"`
	//1 专家提醒
	Type                 int32    `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeExpertRemindRequest) Reset()         { *m = SubscribeExpertRemindRequest{} }
func (m *SubscribeExpertRemindRequest) String() string { return proto.CompactTextString(m) }
func (*SubscribeExpertRemindRequest) ProtoMessage()    {}
func (*SubscribeExpertRemindRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{9}
}

func (m *SubscribeExpertRemindRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeExpertRemindRequest.Unmarshal(m, b)
}
func (m *SubscribeExpertRemindRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeExpertRemindRequest.Marshal(b, m, deterministic)
}
func (m *SubscribeExpertRemindRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeExpertRemindRequest.Merge(m, src)
}
func (m *SubscribeExpertRemindRequest) XXX_Size() int {
	return xxx_messageInfo_SubscribeExpertRemindRequest.Size(m)
}
func (m *SubscribeExpertRemindRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeExpertRemindRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeExpertRemindRequest proto.InternalMessageInfo

func (m *SubscribeExpertRemindRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *SubscribeExpertRemindRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *SubscribeExpertRemindRequest) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *SubscribeExpertRemindRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetExpertRemindNumRequest struct {
	//用户ID
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//宠物id
	PetId string `protobuf:"bytes,2,opt,name=petId,proto3" json:"petId"`
	//模板id
	TemplateId string `protobuf:"bytes,3,opt,name=templateId,proto3" json:"templateId"`
	//1 专家提醒
	Type                 int32    `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExpertRemindNumRequest) Reset()         { *m = GetExpertRemindNumRequest{} }
func (m *GetExpertRemindNumRequest) String() string { return proto.CompactTextString(m) }
func (*GetExpertRemindNumRequest) ProtoMessage()    {}
func (*GetExpertRemindNumRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{10}
}

func (m *GetExpertRemindNumRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExpertRemindNumRequest.Unmarshal(m, b)
}
func (m *GetExpertRemindNumRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExpertRemindNumRequest.Marshal(b, m, deterministic)
}
func (m *GetExpertRemindNumRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExpertRemindNumRequest.Merge(m, src)
}
func (m *GetExpertRemindNumRequest) XXX_Size() int {
	return xxx_messageInfo_GetExpertRemindNumRequest.Size(m)
}
func (m *GetExpertRemindNumRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExpertRemindNumRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetExpertRemindNumRequest proto.InternalMessageInfo

func (m *GetExpertRemindNumRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetExpertRemindNumRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *GetExpertRemindNumRequest) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *GetExpertRemindNumRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetExpertRemindNumResponse struct {
	Num                  int64    `protobuf:"varint,1,opt,name=num,proto3" json:"num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExpertRemindNumResponse) Reset()         { *m = GetExpertRemindNumResponse{} }
func (m *GetExpertRemindNumResponse) String() string { return proto.CompactTextString(m) }
func (*GetExpertRemindNumResponse) ProtoMessage()    {}
func (*GetExpertRemindNumResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{11}
}

func (m *GetExpertRemindNumResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExpertRemindNumResponse.Unmarshal(m, b)
}
func (m *GetExpertRemindNumResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExpertRemindNumResponse.Marshal(b, m, deterministic)
}
func (m *GetExpertRemindNumResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExpertRemindNumResponse.Merge(m, src)
}
func (m *GetExpertRemindNumResponse) XXX_Size() int {
	return xxx_messageInfo_GetExpertRemindNumResponse.Size(m)
}
func (m *GetExpertRemindNumResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExpertRemindNumResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetExpertRemindNumResponse proto.InternalMessageInfo

func (m *GetExpertRemindNumResponse) GetNum() int64 {
	if m != nil {
		return m.Num
	}
	return 0
}

type MemberCountsRequest struct {
	BeginTime            string   `protobuf:"bytes,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	EndTime              string   `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberCountsRequest) Reset()         { *m = MemberCountsRequest{} }
func (m *MemberCountsRequest) String() string { return proto.CompactTextString(m) }
func (*MemberCountsRequest) ProtoMessage()    {}
func (*MemberCountsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{12}
}

func (m *MemberCountsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCountsRequest.Unmarshal(m, b)
}
func (m *MemberCountsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCountsRequest.Marshal(b, m, deterministic)
}
func (m *MemberCountsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCountsRequest.Merge(m, src)
}
func (m *MemberCountsRequest) XXX_Size() int {
	return xxx_messageInfo_MemberCountsRequest.Size(m)
}
func (m *MemberCountsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCountsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCountsRequest proto.InternalMessageInfo

func (m *MemberCountsRequest) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *MemberCountsRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type MemberCountsResponse struct {
	Data                 []*MemberCounts `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MemberCountsResponse) Reset()         { *m = MemberCountsResponse{} }
func (m *MemberCountsResponse) String() string { return proto.CompactTextString(m) }
func (*MemberCountsResponse) ProtoMessage()    {}
func (*MemberCountsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{13}
}

func (m *MemberCountsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCountsResponse.Unmarshal(m, b)
}
func (m *MemberCountsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCountsResponse.Marshal(b, m, deterministic)
}
func (m *MemberCountsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCountsResponse.Merge(m, src)
}
func (m *MemberCountsResponse) XXX_Size() int {
	return xxx_messageInfo_MemberCountsResponse.Size(m)
}
func (m *MemberCountsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCountsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCountsResponse proto.InternalMessageInfo

func (m *MemberCountsResponse) GetData() []*MemberCounts {
	if m != nil {
		return m.Data
	}
	return nil
}

type MemberCounts struct {
	//vip等级 0=v0 1=v1 ……
	Level int32 `protobuf:"varint,1,opt,name=level,proto3" json:"level"`
	//累计会员数
	UserTotal int32 `protobuf:"varint,2,opt,name=user_total,json=userTotal,proto3" json:"user_total"`
	//新增会员数
	NewUserTotal int32 `protobuf:"varint,3,opt,name=new_user_total,json=newUserTotal,proto3" json:"new_user_total"`
	//支付会员数
	PayUserTotal int32 `protobuf:"varint,4,opt,name=pay_user_total,json=payUserTotal,proto3" json:"pay_user_total"`
	//领券会员数
	CouponUserTotal int32 `protobuf:"varint,5,opt,name=coupon_user_total,json=couponUserTotal,proto3" json:"coupon_user_total"`
	//领券会员核销率
	CouponVerifyRate float32 `protobuf:"fixed32,6,opt,name=coupon_verify_rate,json=couponVerifyRate,proto3" json:"coupon_verify_rate"`
	//会员支付金额
	UserPayAmount float32 `protobuf:"fixed32,7,opt,name=user_pay_amount,json=userPayAmount,proto3" json:"user_pay_amount"`
	//会员支付订单数
	UserOrderTotal int32 `protobuf:"varint,8,opt,name=user_order_total,json=userOrderTotal,proto3" json:"user_order_total"`
	//会员客单价
	UserOrderPrice float32 `protobuf:"fixed32,9,opt,name=user_order_price,json=userOrderPrice,proto3" json:"user_order_price"`
	//会员人数
	UserNum int32 `protobuf:"varint,10,opt,name=user_num,json=userNum,proto3" json:"user_num"`
	//会员人数占比
	UserNumRate float32 `protobuf:"fixed32,11,opt,name=user_num_rate,json=userNumRate,proto3" json:"user_num_rate"`
	//会员升级人数
	UserUpgradeNum int32 `protobuf:"varint,12,opt,name=user_upgrade_num,json=userUpgradeNum,proto3" json:"user_upgrade_num"`
	//会员占比
	UserUpgradeNumRate float32 `protobuf:"fixed32,13,opt,name=user_upgrade_num_rate,json=userUpgradeNumRate,proto3" json:"user_upgrade_num_rate"`
	//会员降级人数
	UserDowngradeNum int32 `protobuf:"varint,14,opt,name=user_downgrade_num,json=userDowngradeNum,proto3" json:"user_downgrade_num"`
	//会员降级占比
	UserDowngradeNumRate float32 `protobuf:"fixed32,15,opt,name=user_downgrade_num_rate,json=userDowngradeNumRate,proto3" json:"user_downgrade_num_rate"`
	//商城领券数
	MallCouponTotal int32 `protobuf:"varint,16,opt,name=mall_coupon_total,json=mallCouponTotal,proto3" json:"mall_coupon_total"`
	//商城核销率
	MallCouponVerifyRate float32 `protobuf:"fixed32,17,opt,name=mall_coupon_verify_rate,json=mallCouponVerifyRate,proto3" json:"mall_coupon_verify_rate"`
	//门店领券数
	ShopCouponTotal int32 `protobuf:"varint,18,opt,name=shop_coupon_total,json=shopCouponTotal,proto3" json:"shop_coupon_total"`
	//门店核销率
	ShopCouponVerifyRate float32  `protobuf:"fixed32,197,opt,name=shop_coupon_verify_rate,json=shopCouponVerifyRate,proto3" json:"shop_coupon_verify_rate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberCounts) Reset()         { *m = MemberCounts{} }
func (m *MemberCounts) String() string { return proto.CompactTextString(m) }
func (*MemberCounts) ProtoMessage()    {}
func (*MemberCounts) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{14}
}

func (m *MemberCounts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCounts.Unmarshal(m, b)
}
func (m *MemberCounts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCounts.Marshal(b, m, deterministic)
}
func (m *MemberCounts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCounts.Merge(m, src)
}
func (m *MemberCounts) XXX_Size() int {
	return xxx_messageInfo_MemberCounts.Size(m)
}
func (m *MemberCounts) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCounts.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCounts proto.InternalMessageInfo

func (m *MemberCounts) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *MemberCounts) GetUserTotal() int32 {
	if m != nil {
		return m.UserTotal
	}
	return 0
}

func (m *MemberCounts) GetNewUserTotal() int32 {
	if m != nil {
		return m.NewUserTotal
	}
	return 0
}

func (m *MemberCounts) GetPayUserTotal() int32 {
	if m != nil {
		return m.PayUserTotal
	}
	return 0
}

func (m *MemberCounts) GetCouponUserTotal() int32 {
	if m != nil {
		return m.CouponUserTotal
	}
	return 0
}

func (m *MemberCounts) GetCouponVerifyRate() float32 {
	if m != nil {
		return m.CouponVerifyRate
	}
	return 0
}

func (m *MemberCounts) GetUserPayAmount() float32 {
	if m != nil {
		return m.UserPayAmount
	}
	return 0
}

func (m *MemberCounts) GetUserOrderTotal() int32 {
	if m != nil {
		return m.UserOrderTotal
	}
	return 0
}

func (m *MemberCounts) GetUserOrderPrice() float32 {
	if m != nil {
		return m.UserOrderPrice
	}
	return 0
}

func (m *MemberCounts) GetUserNum() int32 {
	if m != nil {
		return m.UserNum
	}
	return 0
}

func (m *MemberCounts) GetUserNumRate() float32 {
	if m != nil {
		return m.UserNumRate
	}
	return 0
}

func (m *MemberCounts) GetUserUpgradeNum() int32 {
	if m != nil {
		return m.UserUpgradeNum
	}
	return 0
}

func (m *MemberCounts) GetUserUpgradeNumRate() float32 {
	if m != nil {
		return m.UserUpgradeNumRate
	}
	return 0
}

func (m *MemberCounts) GetUserDowngradeNum() int32 {
	if m != nil {
		return m.UserDowngradeNum
	}
	return 0
}

func (m *MemberCounts) GetUserDowngradeNumRate() float32 {
	if m != nil {
		return m.UserDowngradeNumRate
	}
	return 0
}

func (m *MemberCounts) GetMallCouponTotal() int32 {
	if m != nil {
		return m.MallCouponTotal
	}
	return 0
}

func (m *MemberCounts) GetMallCouponVerifyRate() float32 {
	if m != nil {
		return m.MallCouponVerifyRate
	}
	return 0
}

func (m *MemberCounts) GetShopCouponTotal() int32 {
	if m != nil {
		return m.ShopCouponTotal
	}
	return 0
}

func (m *MemberCounts) GetShopCouponVerifyRate() float32 {
	if m != nil {
		return m.ShopCouponVerifyRate
	}
	return 0
}

// 检查黑名单
type CheckBlackListReq struct {
	// 手机号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	// 收货手机号
	ReceiverMobile       string   `protobuf:"bytes,2,opt,name=receiver_mobile,json=receiverMobile,proto3" json:"receiver_mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckBlackListReq) Reset()         { *m = CheckBlackListReq{} }
func (m *CheckBlackListReq) String() string { return proto.CompactTextString(m) }
func (*CheckBlackListReq) ProtoMessage()    {}
func (*CheckBlackListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{15}
}

func (m *CheckBlackListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckBlackListReq.Unmarshal(m, b)
}
func (m *CheckBlackListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckBlackListReq.Marshal(b, m, deterministic)
}
func (m *CheckBlackListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckBlackListReq.Merge(m, src)
}
func (m *CheckBlackListReq) XXX_Size() int {
	return xxx_messageInfo_CheckBlackListReq.Size(m)
}
func (m *CheckBlackListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckBlackListReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckBlackListReq proto.InternalMessageInfo

func (m *CheckBlackListReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CheckBlackListReq) GetReceiverMobile() string {
	if m != nil {
		return m.ReceiverMobile
	}
	return ""
}

type QueryCustomerIdReq struct {
	// 通过scrmId 查子龙的customerId
	ScrmId               string   `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryCustomerIdReq) Reset()         { *m = QueryCustomerIdReq{} }
func (m *QueryCustomerIdReq) String() string { return proto.CompactTextString(m) }
func (*QueryCustomerIdReq) ProtoMessage()    {}
func (*QueryCustomerIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{16}
}

func (m *QueryCustomerIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCustomerIdReq.Unmarshal(m, b)
}
func (m *QueryCustomerIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCustomerIdReq.Marshal(b, m, deterministic)
}
func (m *QueryCustomerIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCustomerIdReq.Merge(m, src)
}
func (m *QueryCustomerIdReq) XXX_Size() int {
	return xxx_messageInfo_QueryCustomerIdReq.Size(m)
}
func (m *QueryCustomerIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCustomerIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCustomerIdReq proto.InternalMessageInfo

func (m *QueryCustomerIdReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type QueryCustomerIdRes struct {
	// 状态码，200正常，非200错误
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 客户id
	CustomerId           []string `protobuf:"bytes,3,rep,name=customer_id,json=customerId,proto3" json:"customer_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryCustomerIdRes) Reset()         { *m = QueryCustomerIdRes{} }
func (m *QueryCustomerIdRes) String() string { return proto.CompactTextString(m) }
func (*QueryCustomerIdRes) ProtoMessage()    {}
func (*QueryCustomerIdRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_a4874506a1c26064, []int{17}
}

func (m *QueryCustomerIdRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCustomerIdRes.Unmarshal(m, b)
}
func (m *QueryCustomerIdRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCustomerIdRes.Marshal(b, m, deterministic)
}
func (m *QueryCustomerIdRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCustomerIdRes.Merge(m, src)
}
func (m *QueryCustomerIdRes) XXX_Size() int {
	return xxx_messageInfo_QueryCustomerIdRes.Size(m)
}
func (m *QueryCustomerIdRes) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCustomerIdRes.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCustomerIdRes proto.InternalMessageInfo

func (m *QueryCustomerIdRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryCustomerIdRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryCustomerIdRes) GetCustomerId() []string {
	if m != nil {
		return m.CustomerId
	}
	return nil
}

func init() {
	proto.RegisterType((*TaskRunVo)(nil), "cc.TaskRunVo")
	proto.RegisterType((*BaseRes)(nil), "cc.BaseRes")
	proto.RegisterType((*BaseReq)(nil), "cc.BaseReq")
	proto.RegisterType((*UserIntegralRequest)(nil), "cc.UserIntegralRequest")
	proto.RegisterType((*UserIntegralResponse)(nil), "cc.UserIntegralResponse")
	proto.RegisterType((*UserInfoRequest)(nil), "cc.UserInfoRequest")
	proto.RegisterType((*UserInfoResponse)(nil), "cc.UserInfoResponse")
	proto.RegisterType((*UserVipInfoResponse)(nil), "cc.UserVipInfoResponse")
	proto.RegisterType((*UserVipInfo)(nil), "cc.userVipInfo")
	proto.RegisterType((*SubscribeExpertRemindRequest)(nil), "cc.SubscribeExpertRemindRequest")
	proto.RegisterType((*GetExpertRemindNumRequest)(nil), "cc.GetExpertRemindNumRequest")
	proto.RegisterType((*GetExpertRemindNumResponse)(nil), "cc.GetExpertRemindNumResponse")
	proto.RegisterType((*MemberCountsRequest)(nil), "cc.MemberCountsRequest")
	proto.RegisterType((*MemberCountsResponse)(nil), "cc.MemberCountsResponse")
	proto.RegisterType((*MemberCounts)(nil), "cc.MemberCounts")
	proto.RegisterType((*CheckBlackListReq)(nil), "cc.CheckBlackListReq")
	proto.RegisterType((*QueryCustomerIdReq)(nil), "cc.QueryCustomerIdReq")
	proto.RegisterType((*QueryCustomerIdRes)(nil), "cc.QueryCustomerIdRes")
}

func init() { proto.RegisterFile("cc/user.proto", fileDescriptor_a4874506a1c26064) }

var fileDescriptor_a4874506a1c26064 = []byte{
	// 1311 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x57, 0x5b, 0x6f, 0x1b, 0xb7,
	0x12, 0x86, 0x65, 0xcb, 0xb6, 0x46, 0x57, 0xd3, 0x72, 0xb4, 0x11, 0x4e, 0x12, 0x63, 0x4f, 0x70,
	0x8e, 0x91, 0x93, 0xe3, 0xa2, 0x69, 0xd3, 0x02, 0xbd, 0x3c, 0x38, 0x4e, 0x1a, 0x18, 0x68, 0x9c,
	0x74, 0x6d, 0xe7, 0x55, 0xa0, 0x76, 0xc7, 0xf6, 0xc2, 0xda, 0x4b, 0x48, 0xae, 0x12, 0x3d, 0x14,
	0xe8, 0xdf, 0xe8, 0x43, 0x7f, 0x42, 0xff, 0x42, 0xdf, 0xfa, 0xbf, 0x8a, 0x21, 0xb9, 0x37, 0x59,
	0x29, 0x90, 0x97, 0xbe, 0x71, 0xbe, 0xf9, 0xe6, 0xb2, 0xe4, 0xcc, 0x90, 0x0b, 0x5d, 0xdf, 0xff,
	0x2c, 0x93, 0x28, 0x0e, 0x53, 0x91, 0xa8, 0x84, 0x35, 0x7c, 0xdf, 0x7d, 0x00, 0xad, 0x73, 0x2e,
	0x6f, 0xbc, 0x2c, 0x7e, 0x9b, 0x30, 0x06, 0x1b, 0x01, 0x57, 0xdc, 0x59, 0xdb, 0x5f, 0x3b, 0x68,
	0x7a, 0x7a, 0xed, 0x7e, 0x0d, 0x5b, 0xcf, 0xb8, 0x44, 0x0f, 0x25, 0xa9, 0xfd, 0x24, 0xc0, 0x5c,
	0x4d, 0x6b, 0xe6, 0xc0, 0x56, 0x84, 0x52, 0xf2, 0x2b, 0x74, 0x1a, 0xfb, 0x6b, 0x07, 0x2d, 0x2f,
	0x17, 0x4b, 0xc3, 0x77, 0x9f, 0x68, 0xf8, 0x12, 0x76, 0x2f, 0x24, 0x8a, 0x93, 0x58, 0xe1, 0x95,
	0xe0, 0x33, 0x0f, 0xdf, 0x65, 0x28, 0x15, 0x1b, 0xc3, 0x76, 0x84, 0xd1, 0x14, 0xc5, 0x49, 0xa0,
	0x1d, 0xb5, 0xbc, 0x42, 0x66, 0x43, 0x68, 0x26, 0xe2, 0xea, 0x24, 0xd0, 0xae, 0x9a, 0x9e, 0x11,
	0xdc, 0x53, 0x18, 0xd6, 0x1d, 0xc9, 0x34, 0x89, 0x25, 0x92, 0xa7, 0xd0, 0x62, 0x36, 0xa5, 0x42,
	0x26, 0xdd, 0x8c, 0x4b, 0x75, 0x1e, 0x46, 0x79, 0x5e, 0x85, 0xec, 0xbe, 0x80, 0xbe, 0xf1, 0x77,
	0x99, 0xe4, 0x49, 0xdd, 0x81, 0xcd, 0x28, 0x99, 0x86, 0x33, 0xb4, 0x29, 0x59, 0xa9, 0x96, 0x6c,
	0xa3, 0x9e, 0xac, 0xfb, 0x47, 0x13, 0x06, 0xa5, 0x1f, 0x9b, 0x53, 0x0f, 0x1a, 0x61, 0x60, 0xb3,
	0x69, 0x84, 0x01, 0x39, 0xa6, 0x93, 0x2a, 0xcc, 0xad, 0x44, 0x8e, 0xf5, 0x2a, 0x3a, 0x09, 0x9c,
	0x75, 0xe3, 0x38, 0x97, 0xd9, 0x3e, 0xb4, 0xcd, 0xfa, 0x3c, 0xb9, 0xc1, 0xd8, 0xd9, 0xd0, 0xea,
	0x2a, 0x94, 0x5b, 0x9f, 0xf2, 0x08, 0x9d, 0x66, 0x69, 0x4d, 0x32, 0x1d, 0x08, 0xad, 0xcf, 0xf0,
	0x83, 0xb3, 0xa9, 0xd3, 0xc8, 0x45, 0x76, 0x1f, 0x80, 0x96, 0xaf, 0xcc, 0x87, 0x6e, 0x69, 0xbb,
	0x0a, 0x92, 0xeb, 0xcf, 0x92, 0x4c, 0xf8, 0xe8, 0x6c, 0x6b, 0xe3, 0x0a, 0x52, 0xe8, 0x15, 0x57,
	0x99, 0x74, 0x5a, 0x15, 0xbd, 0x46, 0x72, 0xfd, 0xd1, 0x9c, 0x2b, 0x2e, 0x1c, 0x28, 0xfd, 0x1b,
	0x84, 0xb9, 0xd0, 0x21, 0xe9, 0x59, 0x28, 0xd4, 0x75, 0xc0, 0x17, 0x4e, 0x5b, 0x33, 0x6a, 0x18,
	0xfb, 0x0f, 0xf4, 0x2e, 0x43, 0x21, 0x95, 0xc7, 0x43, 0x89, 0xf2, 0x0d, 0x2a, 0xa7, 0xa3, 0x59,
	0x4b, 0xa8, 0x2e, 0xc5, 0x50, 0x2d, 0x9c, 0xae, 0xd6, 0xea, 0x35, 0x7d, 0xb9, 0x9f, 0x64, 0xb1,
	0x12, 0x0b, 0xa7, 0x67, 0x4a, 0xd1, 0x8a, 0xb4, 0x5f, 0xa9, 0x48, 0xe6, 0x61, 0xec, 0xa3, 0xd3,
	0x37, 0xfb, 0x95, 0xcb, 0xe4, 0x89, 0x0b, 0xe4, 0xce, 0xc0, 0x78, 0xa2, 0x75, 0xfe, 0x25, 0x1e,
	0x46, 0x5c, 0xdc, 0x38, 0x3b, 0xe5, 0x97, 0x18, 0x84, 0xf4, 0xbe, 0x40, 0xae, 0x50, 0xd7, 0x17,
	0x33, 0xfa, 0x12, 0xd1, 0xf6, 0x69, 0x90, 0xeb, 0x77, 0xad, 0x7d, 0x81, 0xb0, 0x87, 0xd0, 0x9d,
	0x86, 0x57, 0xd4, 0x97, 0x17, 0xa6, 0x38, 0x86, 0x9a, 0x52, 0x07, 0xa9, 0x76, 0x42, 0xe9, 0x21,
	0x9f, 0x39, 0x7b, 0x7a, 0xaf, 0xad, 0x44, 0xf5, 0x21, 0x17, 0x52, 0x61, 0xf4, 0x42, 0x88, 0x44,
	0x38, 0x77, 0x4c, 0x7d, 0x54, 0x20, 0xed, 0x3f, 0x93, 0x61, 0x8c, 0x52, 0x1a, 0xce, 0xc8, 0xfa,
	0xaf, 0x82, 0xee, 0x37, 0xa6, 0x41, 0xdf, 0x86, 0x69, 0xad, 0x84, 0xff, 0x5d, 0x4c, 0x8f, 0xf5,
	0x83, 0xf6, 0x93, 0xfe, 0xa1, 0xef, 0x1f, 0x66, 0x15, 0x9a, 0x19, 0x27, 0xbf, 0xae, 0x99, 0x22,
	0xb5, 0x68, 0x6d, 0x34, 0xb4, 0xec, 0x68, 0x60, 0xb0, 0xa1, 0x16, 0x69, 0xde, 0x7f, 0x7a, 0x4d,
	0x1d, 0x2e, 0x15, 0x57, 0x68, 0x8b, 0xde, 0x08, 0x94, 0x2f, 0x5e, 0x5e, 0xa2, 0xaf, 0xc2, 0x39,
	0x3e, 0x27, 0xad, 0xa9, 0xf9, 0x3a, 0x48, 0xb5, 0x81, 0x1f, 0xd2, 0x50, 0x70, 0x15, 0x26, 0xb1,
	0xa6, 0x99, 0xda, 0x5f, 0x42, 0xdd, 0x5f, 0xd6, 0xe0, 0x5f, 0x67, 0xd9, 0x54, 0xfa, 0x22, 0x9c,
	0xe2, 0x8b, 0x0f, 0x29, 0x0a, 0xe5, 0x61, 0x14, 0xc6, 0x41, 0xa5, 0xdb, 0x6d, 0x53, 0xae, 0xd5,
	0x9a, 0x72, 0x08, 0xcd, 0x14, 0x55, 0xd1, 0xab, 0x46, 0xa0, 0xc3, 0x54, 0x18, 0xa5, 0x33, 0xae,
	0xb0, 0x68, 0xd6, 0x0a, 0x52, 0x7c, 0xe6, 0x86, 0x99, 0x8a, 0xb4, 0x76, 0x7f, 0x86, 0xbb, 0x2f,
	0x51, 0x55, 0x63, 0x9f, 0x66, 0xd1, 0x3f, 0x17, 0xfe, 0x10, 0xc6, 0xab, 0xc2, 0xdb, 0x03, 0x1e,
	0xc0, 0x7a, 0x9c, 0x45, 0x3a, 0xf8, 0xba, 0x47, 0x4b, 0xf7, 0x35, 0xec, 0xbe, 0xd2, 0x63, 0xed,
	0x98, 0x1a, 0x46, 0xe6, 0x89, 0xde, 0x03, 0x98, 0xe2, 0x55, 0x18, 0x4f, 0x14, 0x95, 0xb1, 0x49,
	0xb6, 0xa5, 0x11, 0x5d, 0xc5, 0x77, 0x61, 0x1b, 0xe3, 0xc0, 0x28, 0xed, 0xec, 0xc7, 0x38, 0xd0,
	0x23, 0xf6, 0x3b, 0x18, 0xd6, 0x1d, 0xda, 0xd0, 0x0f, 0x6b, 0xb5, 0x35, 0xa0, 0xda, 0xaa, 0xf1,
	0x4c, 0x71, 0xfd, 0xbe, 0x09, 0x9d, 0x2a, 0x4c, 0x3b, 0x33, 0xc3, 0x39, 0xe6, 0x63, 0xde, 0x08,
	0x94, 0x1e, 0xed, 0xdc, 0x44, 0x25, 0x8a, 0xcf, 0xec, 0x95, 0xd1, 0x22, 0xe4, 0x9c, 0x00, 0xf6,
	0x10, 0x7a, 0x31, 0xbe, 0x9f, 0x54, 0x28, 0xeb, 0x9a, 0xd2, 0x89, 0xf1, 0xfd, 0x45, 0x95, 0x95,
	0xf2, 0x45, 0x95, 0x65, 0x36, 0xb2, 0x93, 0xf2, 0x45, 0xc9, 0x7a, 0x04, 0x3b, 0x7e, 0x92, 0xa5,
	0x49, 0x5c, 0x25, 0x36, 0x35, 0xb1, 0x6f, 0x14, 0x25, 0xf7, 0x31, 0x30, 0xcb, 0x9d, 0xa3, 0x08,
	0x2f, 0x17, 0x13, 0x41, 0xa5, 0x4a, 0xb3, 0xb8, 0xe1, 0x0d, 0x8c, 0xe6, 0xad, 0x56, 0x78, 0xa6,
	0xa8, 0xfb, 0xda, 0x25, 0x25, 0xc1, 0x23, 0xfa, 0x5c, 0x3d, 0x99, 0x1b, 0x5e, 0x97, 0xe0, 0x37,
	0x7c, 0x71, 0xa4, 0x41, 0x76, 0x00, 0x03, 0xcd, 0x4b, 0x44, 0x50, 0x24, 0x60, 0x46, 0x74, 0x8f,
	0xf0, 0xd7, 0x04, 0x9b, 0xf8, 0x75, 0x66, 0x2a, 0x42, 0x1f, 0xf5, 0xb0, 0x6e, 0x54, 0x98, 0x6f,
	0x08, 0xa5, 0x03, 0xd4, 0x4c, 0xaa, 0x06, 0x28, 0xef, 0x8a, 0xd3, 0x2c, 0x62, 0x2e, 0x74, 0x73,
	0x95, 0xc9, 0xbf, 0xad, 0x3d, 0xb4, 0xad, 0x5e, 0xa7, 0x9e, 0x07, 0xca, 0xd2, 0x2b, 0xc1, 0x03,
	0xd4, 0x6e, 0x3a, 0x65, 0x4a, 0x17, 0x06, 0x26, 0x6f, 0x9f, 0xc3, 0xde, 0x32, 0xd3, 0x78, 0xed,
	0x6a, 0xaf, 0xac, 0x4e, 0xd7, 0xce, 0x1f, 0x83, 0x46, 0x27, 0x41, 0xf2, 0x3e, 0x2e, 0xdd, 0xf7,
	0xb4, 0x7b, 0x1d, 0xf6, 0x79, 0xae, 0xa0, 0x00, 0x4f, 0x61, 0x74, 0x9b, 0x6d, 0x42, 0xf4, 0x75,
	0x88, 0xe1, 0xb2, 0x89, 0x0e, 0xf2, 0x08, 0x76, 0x22, 0x3e, 0x9b, 0x4d, 0xec, 0x79, 0x99, 0x5d,
	0x1d, 0x98, 0x63, 0x25, 0xc5, 0xb1, 0xc6, 0xcd, 0xb6, 0x3e, 0x85, 0x51, 0x95, 0x5b, 0x3d, 0xdb,
	0x1d, 0x13, 0xa2, 0xb4, 0xa8, 0x9c, 0xef, 0x23, 0xd8, 0x91, 0xd7, 0x49, 0x5a, 0x0f, 0xc1, 0x4c,
	0x08, 0x52, 0x54, 0x43, 0x7c, 0x05, 0xa3, 0x2a, 0xb7, 0x1a, 0xe2, 0xcf, 0x35, 0x13, 0xa3, 0x34,
	0x29, 0x63, 0xb8, 0xe7, 0xb0, 0x73, 0x7c, 0x8d, 0xfe, 0xcd, 0xb3, 0x19, 0xf7, 0x6f, 0x7e, 0x0c,
	0xa5, 0xa2, 0xc7, 0xda, 0xc7, 0x9e, 0x34, 0xff, 0x85, 0xbe, 0x40, 0x1f, 0xc3, 0x39, 0x8a, 0x89,
	0x25, 0x98, 0xe6, 0xed, 0xe5, 0xb0, 0x79, 0x0e, 0xb8, 0xff, 0x07, 0xf6, 0x53, 0x86, 0x62, 0x71,
	0x9c, 0x49, 0x95, 0x44, 0x34, 0xa1, 0xc8, 0xed, 0x08, 0xb6, 0xa4, 0x2f, 0xa2, 0x49, 0x58, 0x4c,
	0x2f, 0x12, 0x4f, 0x02, 0xd7, 0x5f, 0x41, 0xff, 0xc4, 0xb7, 0x26, 0x7b, 0x00, 0x6d, 0xdf, 0x9a,
	0x53, 0x80, 0xf5, 0xfd, 0x75, 0x7d, 0xb1, 0x16, 0x1e, 0x9f, 0xfc, 0xb6, 0x01, 0xed, 0x0b, 0xfd,
	0x9c, 0x11, 0x73, 0xaa, 0xe0, 0x23, 0xe8, 0x54, 0x9f, 0x86, 0x6c, 0x44, 0x13, 0x65, 0xc5, 0xab,
	0x73, 0xec, 0xdc, 0x56, 0xd8, 0x91, 0xf4, 0x14, 0xb6, 0xf3, 0x57, 0x1c, 0xdb, 0x2d, 0x59, 0xc5,
	0xdb, 0x70, 0x3c, 0xac, 0x83, 0xd6, 0xec, 0x5b, 0x93, 0x48, 0x7e, 0xff, 0xad, 0xb4, 0x2c, 0xb2,
	0x59, 0xbe, 0x62, 0x7f, 0x80, 0xbd, 0x95, 0x17, 0x14, 0xdb, 0x27, 0x8b, 0xbf, 0xbb, 0xbb, 0xc6,
	0x6d, 0x62, 0xe4, 0x0f, 0xf2, 0x33, 0x60, 0xb7, 0xe7, 0x3c, 0xbb, 0x47, 0x94, 0x8f, 0x5e, 0x3f,
	0xe3, 0xfb, 0x1f, 0x53, 0xdb, 0xe4, 0x8e, 0x96, 0x86, 0xef, 0xe8, 0xd6, 0x94, 0xae, 0xee, 0xe9,
	0xca, 0x31, 0xff, 0x25, 0xf4, 0xea, 0x05, 0xc9, 0xf6, 0x88, 0x7b, 0xab, 0x48, 0xab, 0x5f, 0x23,
	0xd9, 0x11, 0xf4, 0x97, 0x2a, 0x88, 0xdd, 0x21, 0xfd, 0xed, 0x2a, 0x1c, 0xaf, 0xc6, 0xe5, 0x93,
	0xef, 0xa1, 0xf7, 0xdc, 0xa7, 0x1f, 0xa1, 0x1c, 0x66, 0xff, 0x83, 0xee, 0xf1, 0x75, 0x92, 0x48,
	0xb4, 0xbf, 0x47, 0xac, 0x4b, 0xa6, 0xc5, 0xbf, 0x52, 0x6d, 0x3f, 0xa7, 0x9b, 0xfa, 0x87, 0xea,
	0x8b, 0xbf, 0x02, 0x00, 0x00, 0xff, 0xff, 0x25, 0x39, 0x2f, 0x7f, 0x61, 0x0d, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserServiceClient interface {
	// 获取用户积分
	UserIntegral(ctx context.Context, in *UserIntegralRequest, opts ...grpc.CallOption) (*UserIntegralResponse, error)
	// 获取用户信息
	UserInfo(ctx context.Context, in *UserInfoRequest, opts ...grpc.CallOption) (*UserInfoResponse, error)
	// 获取用户会员信息
	UserVipInfo(ctx context.Context, in *UserInfoRequest, opts ...grpc.CallOption) (*UserVipInfoResponse, error)
	// 订阅用户专家提醒
	SubscribeExpertRemind(ctx context.Context, in *SubscribeExpertRemindRequest, opts ...grpc.CallOption) (*BaseReq, error)
	// 获取用户订阅数
	GetExpertRemindNum(ctx context.Context, in *GetExpertRemindNumRequest, opts ...grpc.CallOption) (*GetExpertRemindNumResponse, error)
	//会员概况统计
	MemberCounts(ctx context.Context, in *MemberCountsRequest, opts ...grpc.CallOption) (*MemberCountsResponse, error)
	// 检查黑名单
	CheckBlackList(ctx context.Context, in *CheckBlackListReq, opts ...grpc.CallOption) (*BaseRes, error)
	// 通过scrmId 查子龙的customerId
	QueryCustomerId(ctx context.Context, in *QueryCustomerIdReq, opts ...grpc.CallOption) (*QueryCustomerIdRes, error)
}

type userServiceClient struct {
	cc *grpc.ClientConn
}

func NewUserServiceClient(cc *grpc.ClientConn) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) UserIntegral(ctx context.Context, in *UserIntegralRequest, opts ...grpc.CallOption) (*UserIntegralResponse, error) {
	out := new(UserIntegralResponse)
	err := c.cc.Invoke(ctx, "/cc.UserService/UserIntegral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UserInfo(ctx context.Context, in *UserInfoRequest, opts ...grpc.CallOption) (*UserInfoResponse, error) {
	out := new(UserInfoResponse)
	err := c.cc.Invoke(ctx, "/cc.UserService/UserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UserVipInfo(ctx context.Context, in *UserInfoRequest, opts ...grpc.CallOption) (*UserVipInfoResponse, error) {
	out := new(UserVipInfoResponse)
	err := c.cc.Invoke(ctx, "/cc.UserService/UserVipInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SubscribeExpertRemind(ctx context.Context, in *SubscribeExpertRemindRequest, opts ...grpc.CallOption) (*BaseReq, error) {
	out := new(BaseReq)
	err := c.cc.Invoke(ctx, "/cc.UserService/SubscribeExpertRemind", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetExpertRemindNum(ctx context.Context, in *GetExpertRemindNumRequest, opts ...grpc.CallOption) (*GetExpertRemindNumResponse, error) {
	out := new(GetExpertRemindNumResponse)
	err := c.cc.Invoke(ctx, "/cc.UserService/GetExpertRemindNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) MemberCounts(ctx context.Context, in *MemberCountsRequest, opts ...grpc.CallOption) (*MemberCountsResponse, error) {
	out := new(MemberCountsResponse)
	err := c.cc.Invoke(ctx, "/cc.UserService/MemberCounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CheckBlackList(ctx context.Context, in *CheckBlackListReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/cc.UserService/CheckBlackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) QueryCustomerId(ctx context.Context, in *QueryCustomerIdReq, opts ...grpc.CallOption) (*QueryCustomerIdRes, error) {
	out := new(QueryCustomerIdRes)
	err := c.cc.Invoke(ctx, "/cc.UserService/QueryCustomerId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
type UserServiceServer interface {
	// 获取用户积分
	UserIntegral(context.Context, *UserIntegralRequest) (*UserIntegralResponse, error)
	// 获取用户信息
	UserInfo(context.Context, *UserInfoRequest) (*UserInfoResponse, error)
	// 获取用户会员信息
	UserVipInfo(context.Context, *UserInfoRequest) (*UserVipInfoResponse, error)
	// 订阅用户专家提醒
	SubscribeExpertRemind(context.Context, *SubscribeExpertRemindRequest) (*BaseReq, error)
	// 获取用户订阅数
	GetExpertRemindNum(context.Context, *GetExpertRemindNumRequest) (*GetExpertRemindNumResponse, error)
	//会员概况统计
	MemberCounts(context.Context, *MemberCountsRequest) (*MemberCountsResponse, error)
	// 检查黑名单
	CheckBlackList(context.Context, *CheckBlackListReq) (*BaseRes, error)
	// 通过scrmId 查子龙的customerId
	QueryCustomerId(context.Context, *QueryCustomerIdReq) (*QueryCustomerIdRes, error)
}

// UnimplementedUserServiceServer can be embedded to have forward compatible implementations.
type UnimplementedUserServiceServer struct {
}

func (*UnimplementedUserServiceServer) UserIntegral(ctx context.Context, req *UserIntegralRequest) (*UserIntegralResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserIntegral not implemented")
}
func (*UnimplementedUserServiceServer) UserInfo(ctx context.Context, req *UserInfoRequest) (*UserInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfo not implemented")
}
func (*UnimplementedUserServiceServer) UserVipInfo(ctx context.Context, req *UserInfoRequest) (*UserVipInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserVipInfo not implemented")
}
func (*UnimplementedUserServiceServer) SubscribeExpertRemind(ctx context.Context, req *SubscribeExpertRemindRequest) (*BaseReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscribeExpertRemind not implemented")
}
func (*UnimplementedUserServiceServer) GetExpertRemindNum(ctx context.Context, req *GetExpertRemindNumRequest) (*GetExpertRemindNumResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpertRemindNum not implemented")
}
func (*UnimplementedUserServiceServer) MemberCounts(ctx context.Context, req *MemberCountsRequest) (*MemberCountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberCounts not implemented")
}
func (*UnimplementedUserServiceServer) CheckBlackList(ctx context.Context, req *CheckBlackListReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBlackList not implemented")
}
func (*UnimplementedUserServiceServer) QueryCustomerId(ctx context.Context, req *QueryCustomerIdReq) (*QueryCustomerIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCustomerId not implemented")
}

func RegisterUserServiceServer(s *grpc.Server, srv UserServiceServer) {
	s.RegisterService(&_UserService_serviceDesc, srv)
}

func _UserService_UserIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIntegralRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UserIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.UserService/UserIntegral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UserIntegral(ctx, req.(*UserIntegralRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.UserService/UserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UserInfo(ctx, req.(*UserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UserVipInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UserVipInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.UserService/UserVipInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UserVipInfo(ctx, req.(*UserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SubscribeExpertRemind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeExpertRemindRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SubscribeExpertRemind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.UserService/SubscribeExpertRemind",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SubscribeExpertRemind(ctx, req.(*SubscribeExpertRemindRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetExpertRemindNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpertRemindNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetExpertRemindNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.UserService/GetExpertRemindNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetExpertRemindNum(ctx, req.(*GetExpertRemindNumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_MemberCounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberCountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).MemberCounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.UserService/MemberCounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).MemberCounts(ctx, req.(*MemberCountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CheckBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CheckBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.UserService/CheckBlackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CheckBlackList(ctx, req.(*CheckBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_QueryCustomerId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCustomerIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).QueryCustomerId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.UserService/QueryCustomerId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).QueryCustomerId(ctx, req.(*QueryCustomerIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserIntegral",
			Handler:    _UserService_UserIntegral_Handler,
		},
		{
			MethodName: "UserInfo",
			Handler:    _UserService_UserInfo_Handler,
		},
		{
			MethodName: "UserVipInfo",
			Handler:    _UserService_UserVipInfo_Handler,
		},
		{
			MethodName: "SubscribeExpertRemind",
			Handler:    _UserService_SubscribeExpertRemind_Handler,
		},
		{
			MethodName: "GetExpertRemindNum",
			Handler:    _UserService_GetExpertRemindNum_Handler,
		},
		{
			MethodName: "MemberCounts",
			Handler:    _UserService_MemberCounts_Handler,
		},
		{
			MethodName: "CheckBlackList",
			Handler:    _UserService_CheckBlackList_Handler,
		},
		{
			MethodName: "QueryCustomerId",
			Handler:    _UserService_QueryCustomerId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/user.proto",
}

// DcTaskCustomerClient is the client API for DcTaskCustomer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DcTaskCustomerClient interface {
	ChooseTaskRun(ctx context.Context, in *TaskRunVo, opts ...grpc.CallOption) (*BaseReq, error)
}

type dcTaskCustomerClient struct {
	cc *grpc.ClientConn
}

func NewDcTaskCustomerClient(cc *grpc.ClientConn) DcTaskCustomerClient {
	return &dcTaskCustomerClient{cc}
}

func (c *dcTaskCustomerClient) ChooseTaskRun(ctx context.Context, in *TaskRunVo, opts ...grpc.CallOption) (*BaseReq, error) {
	out := new(BaseReq)
	err := c.cc.Invoke(ctx, "/cc.DcTaskCustomer/ChooseTaskRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DcTaskCustomerServer is the server API for DcTaskCustomer service.
type DcTaskCustomerServer interface {
	ChooseTaskRun(context.Context, *TaskRunVo) (*BaseReq, error)
}

// UnimplementedDcTaskCustomerServer can be embedded to have forward compatible implementations.
type UnimplementedDcTaskCustomerServer struct {
}

func (*UnimplementedDcTaskCustomerServer) ChooseTaskRun(ctx context.Context, req *TaskRunVo) (*BaseReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChooseTaskRun not implemented")
}

func RegisterDcTaskCustomerServer(s *grpc.Server, srv DcTaskCustomerServer) {
	s.RegisterService(&_DcTaskCustomer_serviceDesc, srv)
}

func _DcTaskCustomer_ChooseTaskRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRunVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcTaskCustomerServer).ChooseTaskRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.DcTaskCustomer/ChooseTaskRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcTaskCustomerServer).ChooseTaskRun(ctx, req.(*TaskRunVo))
	}
	return interceptor(ctx, in, info, handler)
}

var _DcTaskCustomer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.DcTaskCustomer",
	HandlerType: (*DcTaskCustomerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChooseTaskRun",
			Handler:    _DcTaskCustomer_ChooseTaskRun_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/user.proto",
}
