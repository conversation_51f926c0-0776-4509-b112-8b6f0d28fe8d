syntax = "proto3";
import "google/protobuf/empty.proto";
package cc;

service VipCardService {
  // 获取会员卡模版列表
  rpc GetVipCardTemplateList(VipCardTemplateListRequest) returns (VipCardTemplateListResponse);
  //获取指定会员卡模版详情
  rpc GetVipCardTemplateDetail(BaseIdRequest) returns (VipCardTemplateDetailResponse);
  //添加会员卡模版
  rpc AddVipCardTemplate(VipCardTemplateAddRequest) returns (VcBaseResponse);
  //更新会员卡模版
  rpc UpdateVipCardTemplate(VipCardTemplateUpdateRequest) returns (VcBaseResponse);

  //付费会员卡权益列表
  rpc ListVipEquity(ListPageRequest) returns (ListVipEquityResponse);
  //付费会员卡权益详情
  rpc GetVipEquity(BaseIdRequest) returns (GetVipEquityResponse);
  //付费会员卡权益新增/更新
  rpc CreateOrUpdateVipEquity(VipEquity) returns (VcBaseResponse);

  //权益配置详情
  rpc GetEquityConfig(GetEquityConfigRequest) returns (GetEquityConfigResponse);
  //权益配置列表
  rpc ListEquityConfigs(ListPageRequest) returns (ListEquityConfigsResponse);
  //权益配置列表
  rpc ListNoRefundableEquityConfig(ListPageRequest) returns (ListNoRefundableEquityConfigsResponse);
  //权益配置新增/更新
  rpc SaveEquityConfig(CreateEquityConfigRequest) returns (VcBaseResponse);

  //开卡礼包
  rpc GetGiftList(GiftListRequest) returns (GetGiftListResponse);
  rpc GetGift(BaseIdRequest) returns (GetGiftResponse);
  rpc SaveGift(GiftData) returns (VcBaseResponse);
  rpc DeleteGift(BaseIdRequest) returns (VcBaseResponse);
  rpc UpDownGift(BaseIdRequest) returns (VcBaseResponse);

  //优惠券检查
  rpc CheckVouchers(VoucherRequest) returns (DataBaseResponse);

  //会员中心权益信息
  rpc GetCardInfo(BaseCardRequest) returns (GetCardInfoResponse);

  // 会员中心权益内容信息
  rpc GetEquityList(google.protobuf.Empty) returns (GetEquityListResponse);
  //我的福利
  rpc GetWelfareList(BaseCardOrderRequest) returns (GetWelfareResponse);
  //礼包已领取
  rpc GetGiftInfo(BaseCardOrderRequest) returns (GetGiftInfoResponse);
  //合并手机号请求
  rpc GetUserInfo(GetUserInfoRequest) returns (GetUserInfoResponse);

  // 查询卡权益价值
  rpc CardValue(CardValueReq) returns(CardValueRes);

  // 查询会员卡下已使用子龙门店券的权益赠送价值
  rpc CardOrderValue(CardOrderValueReq) returns(CardOrderValueResp);
}

message DataBaseResponse {
  int32 code = 1;
  string message =2;
  repeated string data =3;
}

message VcBaseResponse {
  int32 code = 1;
  string message =2;
}

message BaseIdRequest {
  int32 id = 1;
}

message BaseUserRequest {
  //用户id(前端不用传)
  string user_id = 1;
}

message BaseCardRequest {
  //卡id
  int32 id = 1;
  //用户id(前端不用传)
  string user_id = 2;
  // 类型 0-默认 1-购买查询卡id权益
  int32 type = 3;
  // 订单号
  string order_sn = 4;
}

message BaseCardOrderRequest {
  //卡id
  string order_sn = 1;
  //用户id(前端不用传)
  string user_id = 2;
}

message ListPageRequest {
  // 当前页
  int32 pageIndex=1;
  // 每页数量
  int32 pageSize=2;
  //大区id
  int32 or_id =3;
}

message VipCardTemplate {
  int32 id = 1;
  //卡名称
  string card_name = 2;
  //卡类型 1-付费卡 2-试用会员卡
  int32 card_type = 3;
  //付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
  int32 card_cycle = 4;
  //时长(天)
  int32 duration_date = 5;
  //会员原价(元)
  double member_price = 6;
  //会员折扣价(元)
  double member_disc_price = 7;
  //来源类型：1-会员卡 2-服务包
  int32 type = 8;
  string create_time = 9;
  string update_time = 10;
  //周期名称
  string cycle_name = 11;
  //副标题
  string tip_title = 12;
  //大区id
  int64 or_id =13;
  //分销佣金比例
  double dis_rate =14;
  //微页面id
  int32 web_id = 15;
  //组织名称
  string or_name = 16;
  //商品sku_id
  int32 sku_id =17;
}

message VipCardTemplateListRequest {
  // 当前页
  int32 pageIndex=1;
  // 每页数量
  int32 pageSize=2;
  //1-会员卡 2-服务包
  int32 type =3;
  //大区id
  int32 or_id =4;
}

message VipCardTemplateListResponse {
  int32 code = 1;
  string message = 2;
  int32 total = 3;
  repeated VipCardTemplate data = 4;
}

message VipCardTemplateDetailResponse {
  int32 code = 1;
  string message = 2;
  VipCardTemplate data = 3;
}

message VipCardTemplateAddRequest {
  //卡名称
  string card_name = 1;
  //卡类型 1-付费卡 2-试用会员卡
  int32 card_type = 2;
  //付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
  int32 card_cycle = 3;
  //时长(天)
  int32 duration_date = 4;
  //会员原价
  double member_price = 5;
  //会员折扣价
  double member_disc_price = 6;
  //1-会员卡 2-服务包
  int32 type = 7;
  //副标题
  string tip_title = 8;
}



message VipCardTemplateUpdateRequest {
  int32 id = 1;
  //卡名称
  string card_name = 2;
  //卡类型 1-付费卡 2-试用会员卡
  int32 card_type = 3;
  //付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
  int32 card_cycle = 4;
  //时长(天)
  int32 duration_date = 5;
  //会员原价
  double member_price = 6;
  //会员折扣价
  double member_disc_price = 7;
  //1-会员卡 2-服务包
  int32 type = 8;
  //分销佣金比例
  double dis_rate =9;
  //微页面id
  int32 web_id = 10;
  //副标题
  string tip_title =11;
}

message VipEquity {
  int32 id = 1;
  //权益icon
  string equity_icon = 2;
  //权益名称
  string equity_name = 3;
  //权益方案
  string equity_copy = 4;
  //权益价值
  double equity_price = 5;
  //权益类型：1-商城优惠券，2-子龙门店券，3-积分翻倍，4-会员价商品，5-大牌礼包，6-家庭服务包 7-宠物医保链接(洗护报销、买药报销、看病报销) 8-子龙打折卡
  int32 equity_type = 6;
  //匹配结果 1-单项填写,2-多项填写
  int32 match_type = 7;
  //发放日期 1-开卡立即下发  2-每月开卡日下发
  int32 issue_type = 8;
  //领取类型 1-首次开卡、2-续费开卡
  string collection_ids = 9;
  //显示状态：0-未生效即将上线，1-生效正常显示
  int32 status = 10;
  string create_time = 11;
  string update_time = 12;
  //大区id值,英文逗号隔开
  string or_ids = 13;
  //跳转链接
  string jump_url = 14;
  //权益介绍
  string equity_info =15;
  //权益规则
  string equity_rule = 16;
  //权益宣传图
  string equity_img = 17;
  //权益有效期1-12个月
  int32  expiry_day = 18;
  //领取个数类型 1-多选一 2-多选多
  int32 receive_type =19;
  //权益短名称
  string equity_short_name = 20;
  //1.主动领取 2.被动领取
  int32 is_active = 21;
  //未领取权益主标题
  string main_title = 22;
  //未领取权益副标题
  string sub_title = 23;
  //未领取权益副标题
  string equity_receive_icon = 24;
}

message GetVipEquityResponse {
  int32 code = 1;
  string message = 2;
  VipEquity data = 3;
}

message ListVipEquityResponse {
  int32 code = 1;
  string message = 2;
  int32 total = 3;
  repeated VipEquity data = 4;
}

message CreateOrUpdateVipEquityRequest {
  VipEquity user_equity = 1;
}

// 权益配置
message EquityConfig {
  //卡id
  int32 card_tid = 1;
  //权益表id
  int32 equity_id = 2;
  //显示状态：0-即将上线，1-正常显示
  int32 status = 3;
  //权益值
  string privilege_ids = 4;
  // 员工编号
  string opter_no =5;
  // 操作人名称
  string opter =6;
  //创建时间
  string create_time = 7;
  //更新时间
  string update_time = 8;
  //权益类型：1-商城优惠券，2-子龙门店券，3-积分翻倍，4-会员价商品，5-大牌礼包 6-家庭服务包 7-宠物医保链接(洗护报销、买药报销、看病报销) 8-子龙打折卡
  int32 equity_type = 9;
  //领取个数
  int32 receive_num = 10;
  //权益短名称
  string equity_short_name =11;
  //是否可退：1-否；0-是(默认1)
  int32 refundable =12;
  // 赠送价值
  repeated EquityConfigValue free_value = 13;
}

message EquityConfigValue {
  // 权益值
  string privilege_id = 1;
  // 赠送价值
  int32 free_quality = 2;
  // 销售方式：1-主动购买、分销购买；2-充值赠送
  int32 sales_type = 3;
}

message GetEquityConfigRequest {
  // 卡模板id
  int32 card_tid = 1;
  // 大区id
  int64 or_id =2;
}

message GetEquityConfigResponse {
  int32 code = 1;
  string message = 2;
  repeated EquityConfig data = 3;
}

message ListEquityConfig{
  //卡付费周期id
  int32 card_tid =1;
  //权益id
  string card_name =2;
  //权益名称
  string equity_name = 3;
  //操作人id
  string user_no = 4;
  //操作人名称
  string user_name = 5;
  string update_time =6;
  //付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
  int32 card_cycle = 7;
  //周期名称
  string cycle_name = 8;
  //大区id
  int64  or_id =9;
  //大区名称
  string or_name = 10;
}

message ListEquityConfigsResponse {
  int32 code = 1;
  string message = 2;
  int32 total =3;
  repeated ListEquityConfig data = 4;
}

message ListNoRefundableEquityConfigsResponse {
  int32 code = 1;
  string message = 2;
  int32 total =3;
  repeated ListNoRefundableEquityConfig data = 4;
}

message ListNoRefundableEquityConfig {
  //卡付费周期id
  int32 card_tid =1;
  //卡名称
  string card_name =2;
  //付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
  int32 card_cycle = 3;
  //权益id
  int32 equity_id = 4;
  //规则名称（权益名称）
  string equity_name = 5;
  //规则条件
  string equity_rule = 6;
  //规则值
  string rule_value = 7;
  //权益类型
  int32 equity_type = 8;
}

message CreateEquityConfigRequest {
  //卡ID
  int32 card_tid = 1;
  //用户id
  string user_id = 2;
  //用户名称
  string user_name = 3;
  repeated EquityConfig data = 4;
  //大区id
  int64 or_id = 5;
}

message GiftData {
  int32 id = 1;
  //礼包名称
  string pack_name = 2;
  //礼包描述
  string pack_desc = 3;
  //礼包价值
  float pack_price = 4;
  //礼包图片
  string pack_image = 5;
  //礼包商品类型
  int32 pack_type = 6;
  //礼包商品ID
  int32 pack_sku_id = 7;
  //状态 2-下架，1-上架
  int32 state = 8;
  //是否主推 2-否 1-是
  int32 is_main = 9;
  string create_time = 10;
  string update_time = 11;
  //操作人id(前端不用传)
  string user_no =12;
  //操作人(前端不用传)
  string user_name = 13;
  //库存
  int32 stock = 14;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 15;
}

message GiftListRequest {
  //当前页
  int32 pageIndex=1;
  // 每页数量
  int32 pageSize=2;
  //礼包ID
  int32 id =3;
  //礼包名称
  string pack_name = 4;
  //礼包状态 -1 全部 1-上架 2-下架
  int32 state = 5;
  //是否主推 默认-全部  1-是 2-否
  int32 is_main = 9;
  //前端库存判断,1-返回库存
  int32 stock_state =10;
}

message GetGiftRequest {
  int32 id = 1;
}

message GetGiftListResponse {
  int32 code = 1;
  string message = 2;
  int32 total =3;
  repeated GiftData data = 4;
}

message GetGiftResponse {
  int32 code = 1;
  string message = 2;
  GiftData data = 3;
}

message VoucherRequest{
  //优惠券id,多个用英文逗号隔开
  string vid = 1;
  //1-商城券 2-门店券
  int32 type=2;
}

message CardInfo{
  //卡付费周期id
  int32 card_tid =1;
  //权益id
  string card_name =2;
  //主推礼包名称
  string gift_name = 3;
  //会员原价(元)
  double member_price = 4;
  //会员折扣价(元)
  double member_disc_price = 5;
  //购买健康卡会员预估可省
  double save_money = 6;
  //是否开通付费会员 0-否 1-开通
  int32 vip_card_state = 7;
  //权益信息
  repeated VipEquityInfo data =8;
  //购买记录
  repeated BuyNoticesList notices_list = 9;
  //卡有效期
  string expiry_date = 10;
  //卡订单号
  string order_sn =11;
  //宠物体验券配置skuid
  string vip_pet_coupon = 12;
  //副标题
  string tip_title = 13;
  //商品sku_id
  int32 sku_id = 14;
  //显示健康卡标识
  int32 show_card_state = 15;
}

message VipEquityInfo {
  int32 id =1;
  //权益icon
  string equity_icon = 2;
  //权益名称
  string equity_name = 3;
  //权益方案
  string equity_copy = 4;
  //权益价值
  double equity_price = 5;
  //类型 1-商品礼券  2到店礼券 3-积分兑换 4-开卡礼包0元领 5-宠物体检券 6-家庭医生服务包 7-门店专享 8-医疗礼包 9-医疗权益 10-宠物医保 11-月度领券
  int32 equity_type = 6;
  //显示状态：0-未生效即将上线，1-生效正常显示
  int32 status = 7;
  //跳转链接
  string jump_url = 8;
  //权益介绍
  string equity_info =9;
  //权益规则
  string equity_rule = 10;
  //权益宣传图
  string equity_img = 11;
  //未领取权益主标题
  string main_title = 12;
  //权益领取图标
  string sub_title = 13;
}

message GetCardInfoResponse{
  int32 code = 1;
  string message = 2;
  CardInfo data = 3;
}

message BuyNoticesList{
  //用户名
  string user_name = 1;
  //时间
  int32 last_time = 2;
  //备注
  string buy_msg =3;
}

message GetEquityListResponse{
  int32 code = 1;
  string message = 2;
  CardInfo data = 3;
}

message WelfareInfo{
  //福利名称
  string name = 1;
  //状态 -1-投保中 0-待领取/待投保 1-已领取/投保成功 2-已过期/投保失败
  int32 state =2;
  //类型 1-商品礼券  2到店礼券 3-积分兑换 4-开卡礼包0元领 5-宠物体检券 6-家庭医生服务包 7-门店专享 8-医疗礼包 9-医疗权益 10-宠物医保， 11-月度返券，12-健康服务金
  int32 type = 5;
  //商城券列表
  repeated CouponList coupon_list = 3;
  //门店券列表
  repeated CouponList store_vouchers = 4;
  //链接跳转
  string url = 6;
  //权益icon
  string equity_icon = 7;
  //权益领取icon
  string equity_receive_icon = 8;
  //权益宣传文案
  string equity_copy = 9;
}

message GetWelfareResponse{
  int32 code = 1;
  string message = 2;
  //免费会员福利
  repeated WelfareInfo free_data = 3;
  //付费会员福利
  repeated WelfareInfo paid_data = 4;
}

message CouponList{
  //券ID
  string coupon_id = 1;
  //券名称
  string coupon_name = 2;
  //券状态 1未领取 2已领取 3已失效 4已过期 5已抢光
  int32 status = 3;
  //券金额（分）
  int32 voucher_t_price = 4;
  //代金券使用时的订单限额(元)
  float voucher_t_limit = 5;
  //适用范围 1全部 其他的是部分
  int32 applicable_scope = 6;
  //代金券模版有效期开始时间
  string voucher_start_date_text = 7;
  //代金券模版有效期结束时间
  string voucher_end_date_text = 8;
  //多少天内可用
  int64 voucher_days = 9;
  //类型有效期，type=2表示领取后有效期voucher_days天，type=1表示begin_time到end_time
  int32  type =10;
  //权益id,领取时要传这个id,区分不同的权益领取的
  int32 equity_id =11;
}

message GetGiftInfoResponse{
  int32 code = 1;
  string message = 2;
  GiftInfo data =3;
}

message GiftInfo{
  int32 id = 1;
  //礼包名称
  string pack_name = 2;
  //礼包描述
  string pack_desc = 3;
  //礼包价值
  float pack_price = 4;
  //礼包图片
  string pack_image = 5;
  //礼包商品ID
  int32 pack_sku_id = 6;
  //订单号
  string order_sn = 7;
}

message GetUserInfoRequest{
  string mobile = 1;
}
message GetUserInfoResponse{
  // 状态码，200正常，非200错误
  int32 code = 1;
  //消息
  string message = 2;
  //0-否 1-是付费会员
  int32 vip_card_state =3;
}

message CardValueReq {
  // 卡id
  int32 card_id = 1;
  // 用户id
  string scrm_id = 2;
}

message CardOrderValueReq {
  // 卡id
  repeated string order_sn = 1;
}

message CardValueRes{
  // 状态码，200正常，非200错误
  int32 code = 1;
  //消息
  string message = 2;
  repeated CardValueData data =3;
}

message CardOrderValueResp{
  // 状态码，200正常，非200错误
  int32 code = 1;
  //消息
  string message = 2;
  repeated CardOrderValueData data =3;
}

message CardValueData{
  //权益名称
  string equity_name = 1;
  //券编码
  string coupon_code = 2;
  //权益价值
  int32 free_quality =3;
}

message CardOrderValueData{
  //会员卡订单id
  string order_sn = 1;
  //赠送价值，单位：分
  repeated CardOrderEquity equity = 2;
}

message CardOrderEquity {
  // 权益名称
  string equity_name = 1;
  // 赠送价值，单位：分
  int32 free_quality = 2;
}