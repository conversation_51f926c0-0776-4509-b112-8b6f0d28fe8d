// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/vip_card.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DataBaseResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []string `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DataBaseResponse) Reset()         { *m = DataBaseResponse{} }
func (m *DataBaseResponse) String() string { return proto.CompactTextString(m) }
func (*DataBaseResponse) ProtoMessage()    {}
func (*DataBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{0}
}

func (m *DataBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataBaseResponse.Unmarshal(m, b)
}
func (m *DataBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataBaseResponse.Marshal(b, m, deterministic)
}
func (m *DataBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataBaseResponse.Merge(m, src)
}
func (m *DataBaseResponse) XXX_Size() int {
	return xxx_messageInfo_DataBaseResponse.Size(m)
}
func (m *DataBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DataBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DataBaseResponse proto.InternalMessageInfo

func (m *DataBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DataBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DataBaseResponse) GetData() []string {
	if m != nil {
		return m.Data
	}
	return nil
}

type VcBaseResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VcBaseResponse) Reset()         { *m = VcBaseResponse{} }
func (m *VcBaseResponse) String() string { return proto.CompactTextString(m) }
func (*VcBaseResponse) ProtoMessage()    {}
func (*VcBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{1}
}

func (m *VcBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VcBaseResponse.Unmarshal(m, b)
}
func (m *VcBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VcBaseResponse.Marshal(b, m, deterministic)
}
func (m *VcBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VcBaseResponse.Merge(m, src)
}
func (m *VcBaseResponse) XXX_Size() int {
	return xxx_messageInfo_VcBaseResponse.Size(m)
}
func (m *VcBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_VcBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_VcBaseResponse proto.InternalMessageInfo

func (m *VcBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VcBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type BaseIdRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseIdRequest) Reset()         { *m = BaseIdRequest{} }
func (m *BaseIdRequest) String() string { return proto.CompactTextString(m) }
func (*BaseIdRequest) ProtoMessage()    {}
func (*BaseIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{2}
}

func (m *BaseIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseIdRequest.Unmarshal(m, b)
}
func (m *BaseIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseIdRequest.Marshal(b, m, deterministic)
}
func (m *BaseIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseIdRequest.Merge(m, src)
}
func (m *BaseIdRequest) XXX_Size() int {
	return xxx_messageInfo_BaseIdRequest.Size(m)
}
func (m *BaseIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseIdRequest proto.InternalMessageInfo

func (m *BaseIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type BaseUserRequest struct {
	//用户id(前端不用传)
	UserId               string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseUserRequest) Reset()         { *m = BaseUserRequest{} }
func (m *BaseUserRequest) String() string { return proto.CompactTextString(m) }
func (*BaseUserRequest) ProtoMessage()    {}
func (*BaseUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{3}
}

func (m *BaseUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseUserRequest.Unmarshal(m, b)
}
func (m *BaseUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseUserRequest.Marshal(b, m, deterministic)
}
func (m *BaseUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseUserRequest.Merge(m, src)
}
func (m *BaseUserRequest) XXX_Size() int {
	return xxx_messageInfo_BaseUserRequest.Size(m)
}
func (m *BaseUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseUserRequest proto.InternalMessageInfo

func (m *BaseUserRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type BaseCardRequest struct {
	//卡id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//用户id(前端不用传)
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 类型 0-默认 1-购买查询卡id权益
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	// 订单号
	OrderSn              string   `protobuf:"bytes,4,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseCardRequest) Reset()         { *m = BaseCardRequest{} }
func (m *BaseCardRequest) String() string { return proto.CompactTextString(m) }
func (*BaseCardRequest) ProtoMessage()    {}
func (*BaseCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{4}
}

func (m *BaseCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseCardRequest.Unmarshal(m, b)
}
func (m *BaseCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseCardRequest.Marshal(b, m, deterministic)
}
func (m *BaseCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseCardRequest.Merge(m, src)
}
func (m *BaseCardRequest) XXX_Size() int {
	return xxx_messageInfo_BaseCardRequest.Size(m)
}
func (m *BaseCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseCardRequest proto.InternalMessageInfo

func (m *BaseCardRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BaseCardRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *BaseCardRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BaseCardRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type BaseCardOrderRequest struct {
	//卡id
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//用户id(前端不用传)
	UserId               string   `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseCardOrderRequest) Reset()         { *m = BaseCardOrderRequest{} }
func (m *BaseCardOrderRequest) String() string { return proto.CompactTextString(m) }
func (*BaseCardOrderRequest) ProtoMessage()    {}
func (*BaseCardOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{5}
}

func (m *BaseCardOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseCardOrderRequest.Unmarshal(m, b)
}
func (m *BaseCardOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseCardOrderRequest.Marshal(b, m, deterministic)
}
func (m *BaseCardOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseCardOrderRequest.Merge(m, src)
}
func (m *BaseCardOrderRequest) XXX_Size() int {
	return xxx_messageInfo_BaseCardOrderRequest.Size(m)
}
func (m *BaseCardOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseCardOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseCardOrderRequest proto.InternalMessageInfo

func (m *BaseCardOrderRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *BaseCardOrderRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type ListPageRequest struct {
	// 当前页
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 每页数量
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	//大区id
	OrId                 int32    `protobuf:"varint,3,opt,name=or_id,json=orId,proto3" json:"or_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListPageRequest) Reset()         { *m = ListPageRequest{} }
func (m *ListPageRequest) String() string { return proto.CompactTextString(m) }
func (*ListPageRequest) ProtoMessage()    {}
func (*ListPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{6}
}

func (m *ListPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPageRequest.Unmarshal(m, b)
}
func (m *ListPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPageRequest.Marshal(b, m, deterministic)
}
func (m *ListPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPageRequest.Merge(m, src)
}
func (m *ListPageRequest) XXX_Size() int {
	return xxx_messageInfo_ListPageRequest.Size(m)
}
func (m *ListPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListPageRequest proto.InternalMessageInfo

func (m *ListPageRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ListPageRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ListPageRequest) GetOrId() int32 {
	if m != nil {
		return m.OrId
	}
	return 0
}

type VipCardTemplate struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//卡名称
	CardName string `protobuf:"bytes,2,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	//卡类型 1-付费卡 2-试用会员卡
	CardType int32 `protobuf:"varint,3,opt,name=card_type,json=cardType,proto3" json:"card_type"`
	//付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
	CardCycle int32 `protobuf:"varint,4,opt,name=card_cycle,json=cardCycle,proto3" json:"card_cycle"`
	//时长(天)
	DurationDate int32 `protobuf:"varint,5,opt,name=duration_date,json=durationDate,proto3" json:"duration_date"`
	//会员原价(元)
	MemberPrice float64 `protobuf:"fixed64,6,opt,name=member_price,json=memberPrice,proto3" json:"member_price"`
	//会员折扣价(元)
	MemberDiscPrice float64 `protobuf:"fixed64,7,opt,name=member_disc_price,json=memberDiscPrice,proto3" json:"member_disc_price"`
	//来源类型：1-会员卡 2-服务包
	Type       int32  `protobuf:"varint,8,opt,name=type,proto3" json:"type"`
	CreateTime string `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime string `protobuf:"bytes,10,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//周期名称
	CycleName string `protobuf:"bytes,11,opt,name=cycle_name,json=cycleName,proto3" json:"cycle_name"`
	//副标题
	TipTitle string `protobuf:"bytes,12,opt,name=tip_title,json=tipTitle,proto3" json:"tip_title"`
	//大区id
	OrId int64 `protobuf:"varint,13,opt,name=or_id,json=orId,proto3" json:"or_id"`
	//分销佣金比例
	DisRate float64 `protobuf:"fixed64,14,opt,name=dis_rate,json=disRate,proto3" json:"dis_rate"`
	//微页面id
	WebId int32 `protobuf:"varint,15,opt,name=web_id,json=webId,proto3" json:"web_id"`
	//组织名称
	OrName string `protobuf:"bytes,16,opt,name=or_name,json=orName,proto3" json:"or_name"`
	//商品sku_id
	SkuId                int32    `protobuf:"varint,17,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipCardTemplate) Reset()         { *m = VipCardTemplate{} }
func (m *VipCardTemplate) String() string { return proto.CompactTextString(m) }
func (*VipCardTemplate) ProtoMessage()    {}
func (*VipCardTemplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{7}
}

func (m *VipCardTemplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipCardTemplate.Unmarshal(m, b)
}
func (m *VipCardTemplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipCardTemplate.Marshal(b, m, deterministic)
}
func (m *VipCardTemplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipCardTemplate.Merge(m, src)
}
func (m *VipCardTemplate) XXX_Size() int {
	return xxx_messageInfo_VipCardTemplate.Size(m)
}
func (m *VipCardTemplate) XXX_DiscardUnknown() {
	xxx_messageInfo_VipCardTemplate.DiscardUnknown(m)
}

var xxx_messageInfo_VipCardTemplate proto.InternalMessageInfo

func (m *VipCardTemplate) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VipCardTemplate) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *VipCardTemplate) GetCardType() int32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *VipCardTemplate) GetCardCycle() int32 {
	if m != nil {
		return m.CardCycle
	}
	return 0
}

func (m *VipCardTemplate) GetDurationDate() int32 {
	if m != nil {
		return m.DurationDate
	}
	return 0
}

func (m *VipCardTemplate) GetMemberPrice() float64 {
	if m != nil {
		return m.MemberPrice
	}
	return 0
}

func (m *VipCardTemplate) GetMemberDiscPrice() float64 {
	if m != nil {
		return m.MemberDiscPrice
	}
	return 0
}

func (m *VipCardTemplate) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *VipCardTemplate) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *VipCardTemplate) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *VipCardTemplate) GetCycleName() string {
	if m != nil {
		return m.CycleName
	}
	return ""
}

func (m *VipCardTemplate) GetTipTitle() string {
	if m != nil {
		return m.TipTitle
	}
	return ""
}

func (m *VipCardTemplate) GetOrId() int64 {
	if m != nil {
		return m.OrId
	}
	return 0
}

func (m *VipCardTemplate) GetDisRate() float64 {
	if m != nil {
		return m.DisRate
	}
	return 0
}

func (m *VipCardTemplate) GetWebId() int32 {
	if m != nil {
		return m.WebId
	}
	return 0
}

func (m *VipCardTemplate) GetOrName() string {
	if m != nil {
		return m.OrName
	}
	return ""
}

func (m *VipCardTemplate) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

type VipCardTemplateListRequest struct {
	// 当前页
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 每页数量
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	//1-会员卡 2-服务包
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	//大区id
	OrId                 int32    `protobuf:"varint,4,opt,name=or_id,json=orId,proto3" json:"or_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipCardTemplateListRequest) Reset()         { *m = VipCardTemplateListRequest{} }
func (m *VipCardTemplateListRequest) String() string { return proto.CompactTextString(m) }
func (*VipCardTemplateListRequest) ProtoMessage()    {}
func (*VipCardTemplateListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{8}
}

func (m *VipCardTemplateListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipCardTemplateListRequest.Unmarshal(m, b)
}
func (m *VipCardTemplateListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipCardTemplateListRequest.Marshal(b, m, deterministic)
}
func (m *VipCardTemplateListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipCardTemplateListRequest.Merge(m, src)
}
func (m *VipCardTemplateListRequest) XXX_Size() int {
	return xxx_messageInfo_VipCardTemplateListRequest.Size(m)
}
func (m *VipCardTemplateListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VipCardTemplateListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VipCardTemplateListRequest proto.InternalMessageInfo

func (m *VipCardTemplateListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *VipCardTemplateListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *VipCardTemplateListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *VipCardTemplateListRequest) GetOrId() int32 {
	if m != nil {
		return m.OrId
	}
	return 0
}

type VipCardTemplateListResponse struct {
	Code                 int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32              `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*VipCardTemplate `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *VipCardTemplateListResponse) Reset()         { *m = VipCardTemplateListResponse{} }
func (m *VipCardTemplateListResponse) String() string { return proto.CompactTextString(m) }
func (*VipCardTemplateListResponse) ProtoMessage()    {}
func (*VipCardTemplateListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{9}
}

func (m *VipCardTemplateListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipCardTemplateListResponse.Unmarshal(m, b)
}
func (m *VipCardTemplateListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipCardTemplateListResponse.Marshal(b, m, deterministic)
}
func (m *VipCardTemplateListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipCardTemplateListResponse.Merge(m, src)
}
func (m *VipCardTemplateListResponse) XXX_Size() int {
	return xxx_messageInfo_VipCardTemplateListResponse.Size(m)
}
func (m *VipCardTemplateListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_VipCardTemplateListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_VipCardTemplateListResponse proto.InternalMessageInfo

func (m *VipCardTemplateListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VipCardTemplateListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *VipCardTemplateListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *VipCardTemplateListResponse) GetData() []*VipCardTemplate {
	if m != nil {
		return m.Data
	}
	return nil
}

type VipCardTemplateDetailResponse struct {
	Code                 int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *VipCardTemplate `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *VipCardTemplateDetailResponse) Reset()         { *m = VipCardTemplateDetailResponse{} }
func (m *VipCardTemplateDetailResponse) String() string { return proto.CompactTextString(m) }
func (*VipCardTemplateDetailResponse) ProtoMessage()    {}
func (*VipCardTemplateDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{10}
}

func (m *VipCardTemplateDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipCardTemplateDetailResponse.Unmarshal(m, b)
}
func (m *VipCardTemplateDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipCardTemplateDetailResponse.Marshal(b, m, deterministic)
}
func (m *VipCardTemplateDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipCardTemplateDetailResponse.Merge(m, src)
}
func (m *VipCardTemplateDetailResponse) XXX_Size() int {
	return xxx_messageInfo_VipCardTemplateDetailResponse.Size(m)
}
func (m *VipCardTemplateDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_VipCardTemplateDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_VipCardTemplateDetailResponse proto.InternalMessageInfo

func (m *VipCardTemplateDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VipCardTemplateDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *VipCardTemplateDetailResponse) GetData() *VipCardTemplate {
	if m != nil {
		return m.Data
	}
	return nil
}

type VipCardTemplateAddRequest struct {
	//卡名称
	CardName string `protobuf:"bytes,1,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	//卡类型 1-付费卡 2-试用会员卡
	CardType int32 `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3" json:"card_type"`
	//付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
	CardCycle int32 `protobuf:"varint,3,opt,name=card_cycle,json=cardCycle,proto3" json:"card_cycle"`
	//时长(天)
	DurationDate int32 `protobuf:"varint,4,opt,name=duration_date,json=durationDate,proto3" json:"duration_date"`
	//会员原价
	MemberPrice float64 `protobuf:"fixed64,5,opt,name=member_price,json=memberPrice,proto3" json:"member_price"`
	//会员折扣价
	MemberDiscPrice float64 `protobuf:"fixed64,6,opt,name=member_disc_price,json=memberDiscPrice,proto3" json:"member_disc_price"`
	//1-会员卡 2-服务包
	Type int32 `protobuf:"varint,7,opt,name=type,proto3" json:"type"`
	//副标题
	TipTitle             string   `protobuf:"bytes,8,opt,name=tip_title,json=tipTitle,proto3" json:"tip_title"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipCardTemplateAddRequest) Reset()         { *m = VipCardTemplateAddRequest{} }
func (m *VipCardTemplateAddRequest) String() string { return proto.CompactTextString(m) }
func (*VipCardTemplateAddRequest) ProtoMessage()    {}
func (*VipCardTemplateAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{11}
}

func (m *VipCardTemplateAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipCardTemplateAddRequest.Unmarshal(m, b)
}
func (m *VipCardTemplateAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipCardTemplateAddRequest.Marshal(b, m, deterministic)
}
func (m *VipCardTemplateAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipCardTemplateAddRequest.Merge(m, src)
}
func (m *VipCardTemplateAddRequest) XXX_Size() int {
	return xxx_messageInfo_VipCardTemplateAddRequest.Size(m)
}
func (m *VipCardTemplateAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VipCardTemplateAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VipCardTemplateAddRequest proto.InternalMessageInfo

func (m *VipCardTemplateAddRequest) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *VipCardTemplateAddRequest) GetCardType() int32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *VipCardTemplateAddRequest) GetCardCycle() int32 {
	if m != nil {
		return m.CardCycle
	}
	return 0
}

func (m *VipCardTemplateAddRequest) GetDurationDate() int32 {
	if m != nil {
		return m.DurationDate
	}
	return 0
}

func (m *VipCardTemplateAddRequest) GetMemberPrice() float64 {
	if m != nil {
		return m.MemberPrice
	}
	return 0
}

func (m *VipCardTemplateAddRequest) GetMemberDiscPrice() float64 {
	if m != nil {
		return m.MemberDiscPrice
	}
	return 0
}

func (m *VipCardTemplateAddRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *VipCardTemplateAddRequest) GetTipTitle() string {
	if m != nil {
		return m.TipTitle
	}
	return ""
}

type VipCardTemplateUpdateRequest struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//卡名称
	CardName string `protobuf:"bytes,2,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	//卡类型 1-付费卡 2-试用会员卡
	CardType int32 `protobuf:"varint,3,opt,name=card_type,json=cardType,proto3" json:"card_type"`
	//付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
	CardCycle int32 `protobuf:"varint,4,opt,name=card_cycle,json=cardCycle,proto3" json:"card_cycle"`
	//时长(天)
	DurationDate int32 `protobuf:"varint,5,opt,name=duration_date,json=durationDate,proto3" json:"duration_date"`
	//会员原价
	MemberPrice float64 `protobuf:"fixed64,6,opt,name=member_price,json=memberPrice,proto3" json:"member_price"`
	//会员折扣价
	MemberDiscPrice float64 `protobuf:"fixed64,7,opt,name=member_disc_price,json=memberDiscPrice,proto3" json:"member_disc_price"`
	//1-会员卡 2-服务包
	Type int32 `protobuf:"varint,8,opt,name=type,proto3" json:"type"`
	//分销佣金比例
	DisRate float64 `protobuf:"fixed64,9,opt,name=dis_rate,json=disRate,proto3" json:"dis_rate"`
	//微页面id
	WebId int32 `protobuf:"varint,10,opt,name=web_id,json=webId,proto3" json:"web_id"`
	//副标题
	TipTitle             string   `protobuf:"bytes,11,opt,name=tip_title,json=tipTitle,proto3" json:"tip_title"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipCardTemplateUpdateRequest) Reset()         { *m = VipCardTemplateUpdateRequest{} }
func (m *VipCardTemplateUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*VipCardTemplateUpdateRequest) ProtoMessage()    {}
func (*VipCardTemplateUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{12}
}

func (m *VipCardTemplateUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipCardTemplateUpdateRequest.Unmarshal(m, b)
}
func (m *VipCardTemplateUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipCardTemplateUpdateRequest.Marshal(b, m, deterministic)
}
func (m *VipCardTemplateUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipCardTemplateUpdateRequest.Merge(m, src)
}
func (m *VipCardTemplateUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_VipCardTemplateUpdateRequest.Size(m)
}
func (m *VipCardTemplateUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VipCardTemplateUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VipCardTemplateUpdateRequest proto.InternalMessageInfo

func (m *VipCardTemplateUpdateRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VipCardTemplateUpdateRequest) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *VipCardTemplateUpdateRequest) GetCardType() int32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *VipCardTemplateUpdateRequest) GetCardCycle() int32 {
	if m != nil {
		return m.CardCycle
	}
	return 0
}

func (m *VipCardTemplateUpdateRequest) GetDurationDate() int32 {
	if m != nil {
		return m.DurationDate
	}
	return 0
}

func (m *VipCardTemplateUpdateRequest) GetMemberPrice() float64 {
	if m != nil {
		return m.MemberPrice
	}
	return 0
}

func (m *VipCardTemplateUpdateRequest) GetMemberDiscPrice() float64 {
	if m != nil {
		return m.MemberDiscPrice
	}
	return 0
}

func (m *VipCardTemplateUpdateRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *VipCardTemplateUpdateRequest) GetDisRate() float64 {
	if m != nil {
		return m.DisRate
	}
	return 0
}

func (m *VipCardTemplateUpdateRequest) GetWebId() int32 {
	if m != nil {
		return m.WebId
	}
	return 0
}

func (m *VipCardTemplateUpdateRequest) GetTipTitle() string {
	if m != nil {
		return m.TipTitle
	}
	return ""
}

type VipEquity struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//权益icon
	EquityIcon string `protobuf:"bytes,2,opt,name=equity_icon,json=equityIcon,proto3" json:"equity_icon"`
	//权益名称
	EquityName string `protobuf:"bytes,3,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//权益方案
	EquityCopy string `protobuf:"bytes,4,opt,name=equity_copy,json=equityCopy,proto3" json:"equity_copy"`
	//权益价值
	EquityPrice float64 `protobuf:"fixed64,5,opt,name=equity_price,json=equityPrice,proto3" json:"equity_price"`
	//权益类型：1-商城优惠券，2-子龙门店券，3-积分翻倍，4-会员价商品，5-大牌礼包，6-家庭服务包 7-宠物医保链接(洗护报销、买药报销、看病报销) 8-子龙打折卡
	EquityType int32 `protobuf:"varint,6,opt,name=equity_type,json=equityType,proto3" json:"equity_type"`
	//匹配结果 1-单项填写,2-多项填写
	MatchType int32 `protobuf:"varint,7,opt,name=match_type,json=matchType,proto3" json:"match_type"`
	//发放日期 1-开卡立即下发  2-每月开卡日下发
	IssueType int32 `protobuf:"varint,8,opt,name=issue_type,json=issueType,proto3" json:"issue_type"`
	//领取类型 1-首次开卡、2-续费开卡
	CollectionIds string `protobuf:"bytes,9,opt,name=collection_ids,json=collectionIds,proto3" json:"collection_ids"`
	//显示状态：0-未生效即将上线，1-生效正常显示
	Status     int32  `protobuf:"varint,10,opt,name=status,proto3" json:"status"`
	CreateTime string `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime string `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//大区id值,英文逗号隔开
	OrIds string `protobuf:"bytes,13,opt,name=or_ids,json=orIds,proto3" json:"or_ids"`
	//跳转链接
	JumpUrl string `protobuf:"bytes,14,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url"`
	//权益介绍
	EquityInfo string `protobuf:"bytes,15,opt,name=equity_info,json=equityInfo,proto3" json:"equity_info"`
	//权益规则
	EquityRule string `protobuf:"bytes,16,opt,name=equity_rule,json=equityRule,proto3" json:"equity_rule"`
	//权益宣传图
	EquityImg string `protobuf:"bytes,17,opt,name=equity_img,json=equityImg,proto3" json:"equity_img"`
	//权益有效期1-12个月
	ExpiryDay int32 `protobuf:"varint,18,opt,name=expiry_day,json=expiryDay,proto3" json:"expiry_day"`
	//领取个数类型 1-多选一 2-多选多
	ReceiveType int32 `protobuf:"varint,19,opt,name=receive_type,json=receiveType,proto3" json:"receive_type"`
	//权益短名称
	EquityShortName string `protobuf:"bytes,20,opt,name=equity_short_name,json=equityShortName,proto3" json:"equity_short_name"`
	//1.主动领取 2.被动领取
	IsActive int32 `protobuf:"varint,21,opt,name=is_active,json=isActive,proto3" json:"is_active"`
	//未领取权益主标题
	MainTitle string `protobuf:"bytes,22,opt,name=main_title,json=mainTitle,proto3" json:"main_title"`
	//未领取权益副标题
	SubTitle string `protobuf:"bytes,23,opt,name=sub_title,json=subTitle,proto3" json:"sub_title"`
	//未领取权益副标题
	EquityReceiveIcon    string   `protobuf:"bytes,24,opt,name=equity_receive_icon,json=equityReceiveIcon,proto3" json:"equity_receive_icon"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipEquity) Reset()         { *m = VipEquity{} }
func (m *VipEquity) String() string { return proto.CompactTextString(m) }
func (*VipEquity) ProtoMessage()    {}
func (*VipEquity) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{13}
}

func (m *VipEquity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipEquity.Unmarshal(m, b)
}
func (m *VipEquity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipEquity.Marshal(b, m, deterministic)
}
func (m *VipEquity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipEquity.Merge(m, src)
}
func (m *VipEquity) XXX_Size() int {
	return xxx_messageInfo_VipEquity.Size(m)
}
func (m *VipEquity) XXX_DiscardUnknown() {
	xxx_messageInfo_VipEquity.DiscardUnknown(m)
}

var xxx_messageInfo_VipEquity proto.InternalMessageInfo

func (m *VipEquity) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VipEquity) GetEquityIcon() string {
	if m != nil {
		return m.EquityIcon
	}
	return ""
}

func (m *VipEquity) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *VipEquity) GetEquityCopy() string {
	if m != nil {
		return m.EquityCopy
	}
	return ""
}

func (m *VipEquity) GetEquityPrice() float64 {
	if m != nil {
		return m.EquityPrice
	}
	return 0
}

func (m *VipEquity) GetEquityType() int32 {
	if m != nil {
		return m.EquityType
	}
	return 0
}

func (m *VipEquity) GetMatchType() int32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *VipEquity) GetIssueType() int32 {
	if m != nil {
		return m.IssueType
	}
	return 0
}

func (m *VipEquity) GetCollectionIds() string {
	if m != nil {
		return m.CollectionIds
	}
	return ""
}

func (m *VipEquity) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *VipEquity) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *VipEquity) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *VipEquity) GetOrIds() string {
	if m != nil {
		return m.OrIds
	}
	return ""
}

func (m *VipEquity) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *VipEquity) GetEquityInfo() string {
	if m != nil {
		return m.EquityInfo
	}
	return ""
}

func (m *VipEquity) GetEquityRule() string {
	if m != nil {
		return m.EquityRule
	}
	return ""
}

func (m *VipEquity) GetEquityImg() string {
	if m != nil {
		return m.EquityImg
	}
	return ""
}

func (m *VipEquity) GetExpiryDay() int32 {
	if m != nil {
		return m.ExpiryDay
	}
	return 0
}

func (m *VipEquity) GetReceiveType() int32 {
	if m != nil {
		return m.ReceiveType
	}
	return 0
}

func (m *VipEquity) GetEquityShortName() string {
	if m != nil {
		return m.EquityShortName
	}
	return ""
}

func (m *VipEquity) GetIsActive() int32 {
	if m != nil {
		return m.IsActive
	}
	return 0
}

func (m *VipEquity) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *VipEquity) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *VipEquity) GetEquityReceiveIcon() string {
	if m != nil {
		return m.EquityReceiveIcon
	}
	return ""
}

type GetVipEquityResponse struct {
	Code                 int32      `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *VipEquity `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetVipEquityResponse) Reset()         { *m = GetVipEquityResponse{} }
func (m *GetVipEquityResponse) String() string { return proto.CompactTextString(m) }
func (*GetVipEquityResponse) ProtoMessage()    {}
func (*GetVipEquityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{14}
}

func (m *GetVipEquityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVipEquityResponse.Unmarshal(m, b)
}
func (m *GetVipEquityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVipEquityResponse.Marshal(b, m, deterministic)
}
func (m *GetVipEquityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVipEquityResponse.Merge(m, src)
}
func (m *GetVipEquityResponse) XXX_Size() int {
	return xxx_messageInfo_GetVipEquityResponse.Size(m)
}
func (m *GetVipEquityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVipEquityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVipEquityResponse proto.InternalMessageInfo

func (m *GetVipEquityResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetVipEquityResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetVipEquityResponse) GetData() *VipEquity {
	if m != nil {
		return m.Data
	}
	return nil
}

type ListVipEquityResponse struct {
	Code                 int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32        `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*VipEquity `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListVipEquityResponse) Reset()         { *m = ListVipEquityResponse{} }
func (m *ListVipEquityResponse) String() string { return proto.CompactTextString(m) }
func (*ListVipEquityResponse) ProtoMessage()    {}
func (*ListVipEquityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{15}
}

func (m *ListVipEquityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListVipEquityResponse.Unmarshal(m, b)
}
func (m *ListVipEquityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListVipEquityResponse.Marshal(b, m, deterministic)
}
func (m *ListVipEquityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListVipEquityResponse.Merge(m, src)
}
func (m *ListVipEquityResponse) XXX_Size() int {
	return xxx_messageInfo_ListVipEquityResponse.Size(m)
}
func (m *ListVipEquityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListVipEquityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListVipEquityResponse proto.InternalMessageInfo

func (m *ListVipEquityResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ListVipEquityResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ListVipEquityResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListVipEquityResponse) GetData() []*VipEquity {
	if m != nil {
		return m.Data
	}
	return nil
}

type CreateOrUpdateVipEquityRequest struct {
	UserEquity           *VipEquity `protobuf:"bytes,1,opt,name=user_equity,json=userEquity,proto3" json:"user_equity"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CreateOrUpdateVipEquityRequest) Reset()         { *m = CreateOrUpdateVipEquityRequest{} }
func (m *CreateOrUpdateVipEquityRequest) String() string { return proto.CompactTextString(m) }
func (*CreateOrUpdateVipEquityRequest) ProtoMessage()    {}
func (*CreateOrUpdateVipEquityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{16}
}

func (m *CreateOrUpdateVipEquityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateOrUpdateVipEquityRequest.Unmarshal(m, b)
}
func (m *CreateOrUpdateVipEquityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateOrUpdateVipEquityRequest.Marshal(b, m, deterministic)
}
func (m *CreateOrUpdateVipEquityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateOrUpdateVipEquityRequest.Merge(m, src)
}
func (m *CreateOrUpdateVipEquityRequest) XXX_Size() int {
	return xxx_messageInfo_CreateOrUpdateVipEquityRequest.Size(m)
}
func (m *CreateOrUpdateVipEquityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateOrUpdateVipEquityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateOrUpdateVipEquityRequest proto.InternalMessageInfo

func (m *CreateOrUpdateVipEquityRequest) GetUserEquity() *VipEquity {
	if m != nil {
		return m.UserEquity
	}
	return nil
}

// 权益配置
type EquityConfig struct {
	//卡id
	CardTid int32 `protobuf:"varint,1,opt,name=card_tid,json=cardTid,proto3" json:"card_tid"`
	//权益表id
	EquityId int32 `protobuf:"varint,2,opt,name=equity_id,json=equityId,proto3" json:"equity_id"`
	//显示状态：0-即将上线，1-正常显示
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	//权益值
	PrivilegeIds string `protobuf:"bytes,4,opt,name=privilege_ids,json=privilegeIds,proto3" json:"privilege_ids"`
	// 员工编号
	OpterNo string `protobuf:"bytes,5,opt,name=opter_no,json=opterNo,proto3" json:"opter_no"`
	// 操作人名称
	Opter string `protobuf:"bytes,6,opt,name=opter,proto3" json:"opter"`
	//创建时间
	CreateTime string `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//更新时间
	UpdateTime string `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//权益类型：1-商城优惠券，2-子龙门店券，3-积分翻倍，4-会员价商品，5-大牌礼包 6-家庭服务包 7-宠物医保链接(洗护报销、买药报销、看病报销) 8-子龙打折卡
	EquityType int32 `protobuf:"varint,9,opt,name=equity_type,json=equityType,proto3" json:"equity_type"`
	//领取个数
	ReceiveNum int32 `protobuf:"varint,10,opt,name=receive_num,json=receiveNum,proto3" json:"receive_num"`
	//权益短名称
	EquityShortName string `protobuf:"bytes,11,opt,name=equity_short_name,json=equityShortName,proto3" json:"equity_short_name"`
	//是否可退：1-否；0-是(默认1)
	Refundable int32 `protobuf:"varint,12,opt,name=refundable,proto3" json:"refundable"`
	// 赠送价值
	FreeValue            []*EquityConfigValue `protobuf:"bytes,13,rep,name=free_value,json=freeValue,proto3" json:"free_value"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *EquityConfig) Reset()         { *m = EquityConfig{} }
func (m *EquityConfig) String() string { return proto.CompactTextString(m) }
func (*EquityConfig) ProtoMessage()    {}
func (*EquityConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{17}
}

func (m *EquityConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EquityConfig.Unmarshal(m, b)
}
func (m *EquityConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EquityConfig.Marshal(b, m, deterministic)
}
func (m *EquityConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EquityConfig.Merge(m, src)
}
func (m *EquityConfig) XXX_Size() int {
	return xxx_messageInfo_EquityConfig.Size(m)
}
func (m *EquityConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_EquityConfig.DiscardUnknown(m)
}

var xxx_messageInfo_EquityConfig proto.InternalMessageInfo

func (m *EquityConfig) GetCardTid() int32 {
	if m != nil {
		return m.CardTid
	}
	return 0
}

func (m *EquityConfig) GetEquityId() int32 {
	if m != nil {
		return m.EquityId
	}
	return 0
}

func (m *EquityConfig) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *EquityConfig) GetPrivilegeIds() string {
	if m != nil {
		return m.PrivilegeIds
	}
	return ""
}

func (m *EquityConfig) GetOpterNo() string {
	if m != nil {
		return m.OpterNo
	}
	return ""
}

func (m *EquityConfig) GetOpter() string {
	if m != nil {
		return m.Opter
	}
	return ""
}

func (m *EquityConfig) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *EquityConfig) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *EquityConfig) GetEquityType() int32 {
	if m != nil {
		return m.EquityType
	}
	return 0
}

func (m *EquityConfig) GetReceiveNum() int32 {
	if m != nil {
		return m.ReceiveNum
	}
	return 0
}

func (m *EquityConfig) GetEquityShortName() string {
	if m != nil {
		return m.EquityShortName
	}
	return ""
}

func (m *EquityConfig) GetRefundable() int32 {
	if m != nil {
		return m.Refundable
	}
	return 0
}

func (m *EquityConfig) GetFreeValue() []*EquityConfigValue {
	if m != nil {
		return m.FreeValue
	}
	return nil
}

type EquityConfigValue struct {
	// 权益值
	PrivilegeId string `protobuf:"bytes,1,opt,name=privilege_id,json=privilegeId,proto3" json:"privilege_id"`
	// 赠送价值
	FreeQuality int32 `protobuf:"varint,2,opt,name=free_quality,json=freeQuality,proto3" json:"free_quality"`
	// 销售方式：1-主动购买、分销购买；2-充值赠送
	SalesType            int32    `protobuf:"varint,3,opt,name=sales_type,json=salesType,proto3" json:"sales_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EquityConfigValue) Reset()         { *m = EquityConfigValue{} }
func (m *EquityConfigValue) String() string { return proto.CompactTextString(m) }
func (*EquityConfigValue) ProtoMessage()    {}
func (*EquityConfigValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{18}
}

func (m *EquityConfigValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EquityConfigValue.Unmarshal(m, b)
}
func (m *EquityConfigValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EquityConfigValue.Marshal(b, m, deterministic)
}
func (m *EquityConfigValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EquityConfigValue.Merge(m, src)
}
func (m *EquityConfigValue) XXX_Size() int {
	return xxx_messageInfo_EquityConfigValue.Size(m)
}
func (m *EquityConfigValue) XXX_DiscardUnknown() {
	xxx_messageInfo_EquityConfigValue.DiscardUnknown(m)
}

var xxx_messageInfo_EquityConfigValue proto.InternalMessageInfo

func (m *EquityConfigValue) GetPrivilegeId() string {
	if m != nil {
		return m.PrivilegeId
	}
	return ""
}

func (m *EquityConfigValue) GetFreeQuality() int32 {
	if m != nil {
		return m.FreeQuality
	}
	return 0
}

func (m *EquityConfigValue) GetSalesType() int32 {
	if m != nil {
		return m.SalesType
	}
	return 0
}

type GetEquityConfigRequest struct {
	// 卡模板id
	CardTid int32 `protobuf:"varint,1,opt,name=card_tid,json=cardTid,proto3" json:"card_tid"`
	// 大区id
	OrId                 int64    `protobuf:"varint,2,opt,name=or_id,json=orId,proto3" json:"or_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEquityConfigRequest) Reset()         { *m = GetEquityConfigRequest{} }
func (m *GetEquityConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetEquityConfigRequest) ProtoMessage()    {}
func (*GetEquityConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{19}
}

func (m *GetEquityConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEquityConfigRequest.Unmarshal(m, b)
}
func (m *GetEquityConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEquityConfigRequest.Marshal(b, m, deterministic)
}
func (m *GetEquityConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEquityConfigRequest.Merge(m, src)
}
func (m *GetEquityConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetEquityConfigRequest.Size(m)
}
func (m *GetEquityConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEquityConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEquityConfigRequest proto.InternalMessageInfo

func (m *GetEquityConfigRequest) GetCardTid() int32 {
	if m != nil {
		return m.CardTid
	}
	return 0
}

func (m *GetEquityConfigRequest) GetOrId() int64 {
	if m != nil {
		return m.OrId
	}
	return 0
}

type GetEquityConfigResponse struct {
	Code                 int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string          `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*EquityConfig `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetEquityConfigResponse) Reset()         { *m = GetEquityConfigResponse{} }
func (m *GetEquityConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetEquityConfigResponse) ProtoMessage()    {}
func (*GetEquityConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{20}
}

func (m *GetEquityConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEquityConfigResponse.Unmarshal(m, b)
}
func (m *GetEquityConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEquityConfigResponse.Marshal(b, m, deterministic)
}
func (m *GetEquityConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEquityConfigResponse.Merge(m, src)
}
func (m *GetEquityConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetEquityConfigResponse.Size(m)
}
func (m *GetEquityConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEquityConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEquityConfigResponse proto.InternalMessageInfo

func (m *GetEquityConfigResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetEquityConfigResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetEquityConfigResponse) GetData() []*EquityConfig {
	if m != nil {
		return m.Data
	}
	return nil
}

type ListEquityConfig struct {
	//卡付费周期id
	CardTid int32 `protobuf:"varint,1,opt,name=card_tid,json=cardTid,proto3" json:"card_tid"`
	//权益id
	CardName string `protobuf:"bytes,2,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	//权益名称
	EquityName string `protobuf:"bytes,3,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//操作人id
	UserNo string `protobuf:"bytes,4,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	//操作人名称
	UserName   string `protobuf:"bytes,5,opt,name=user_name,json=userName,proto3" json:"user_name"`
	UpdateTime string `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
	CardCycle int32 `protobuf:"varint,7,opt,name=card_cycle,json=cardCycle,proto3" json:"card_cycle"`
	//周期名称
	CycleName string `protobuf:"bytes,8,opt,name=cycle_name,json=cycleName,proto3" json:"cycle_name"`
	//大区id
	OrId int64 `protobuf:"varint,9,opt,name=or_id,json=orId,proto3" json:"or_id"`
	//大区名称
	OrName               string   `protobuf:"bytes,10,opt,name=or_name,json=orName,proto3" json:"or_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListEquityConfig) Reset()         { *m = ListEquityConfig{} }
func (m *ListEquityConfig) String() string { return proto.CompactTextString(m) }
func (*ListEquityConfig) ProtoMessage()    {}
func (*ListEquityConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{21}
}

func (m *ListEquityConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListEquityConfig.Unmarshal(m, b)
}
func (m *ListEquityConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListEquityConfig.Marshal(b, m, deterministic)
}
func (m *ListEquityConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListEquityConfig.Merge(m, src)
}
func (m *ListEquityConfig) XXX_Size() int {
	return xxx_messageInfo_ListEquityConfig.Size(m)
}
func (m *ListEquityConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ListEquityConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ListEquityConfig proto.InternalMessageInfo

func (m *ListEquityConfig) GetCardTid() int32 {
	if m != nil {
		return m.CardTid
	}
	return 0
}

func (m *ListEquityConfig) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *ListEquityConfig) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *ListEquityConfig) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *ListEquityConfig) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *ListEquityConfig) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *ListEquityConfig) GetCardCycle() int32 {
	if m != nil {
		return m.CardCycle
	}
	return 0
}

func (m *ListEquityConfig) GetCycleName() string {
	if m != nil {
		return m.CycleName
	}
	return ""
}

func (m *ListEquityConfig) GetOrId() int64 {
	if m != nil {
		return m.OrId
	}
	return 0
}

func (m *ListEquityConfig) GetOrName() string {
	if m != nil {
		return m.OrName
	}
	return ""
}

type ListEquityConfigsResponse struct {
	Code                 int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32               `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*ListEquityConfig `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ListEquityConfigsResponse) Reset()         { *m = ListEquityConfigsResponse{} }
func (m *ListEquityConfigsResponse) String() string { return proto.CompactTextString(m) }
func (*ListEquityConfigsResponse) ProtoMessage()    {}
func (*ListEquityConfigsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{22}
}

func (m *ListEquityConfigsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListEquityConfigsResponse.Unmarshal(m, b)
}
func (m *ListEquityConfigsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListEquityConfigsResponse.Marshal(b, m, deterministic)
}
func (m *ListEquityConfigsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListEquityConfigsResponse.Merge(m, src)
}
func (m *ListEquityConfigsResponse) XXX_Size() int {
	return xxx_messageInfo_ListEquityConfigsResponse.Size(m)
}
func (m *ListEquityConfigsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListEquityConfigsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListEquityConfigsResponse proto.InternalMessageInfo

func (m *ListEquityConfigsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ListEquityConfigsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ListEquityConfigsResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListEquityConfigsResponse) GetData() []*ListEquityConfig {
	if m != nil {
		return m.Data
	}
	return nil
}

type ListNoRefundableEquityConfigsResponse struct {
	Code                 int32                           `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                          `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32                           `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*ListNoRefundableEquityConfig `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *ListNoRefundableEquityConfigsResponse) Reset()         { *m = ListNoRefundableEquityConfigsResponse{} }
func (m *ListNoRefundableEquityConfigsResponse) String() string { return proto.CompactTextString(m) }
func (*ListNoRefundableEquityConfigsResponse) ProtoMessage()    {}
func (*ListNoRefundableEquityConfigsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{23}
}

func (m *ListNoRefundableEquityConfigsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListNoRefundableEquityConfigsResponse.Unmarshal(m, b)
}
func (m *ListNoRefundableEquityConfigsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListNoRefundableEquityConfigsResponse.Marshal(b, m, deterministic)
}
func (m *ListNoRefundableEquityConfigsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListNoRefundableEquityConfigsResponse.Merge(m, src)
}
func (m *ListNoRefundableEquityConfigsResponse) XXX_Size() int {
	return xxx_messageInfo_ListNoRefundableEquityConfigsResponse.Size(m)
}
func (m *ListNoRefundableEquityConfigsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListNoRefundableEquityConfigsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListNoRefundableEquityConfigsResponse proto.InternalMessageInfo

func (m *ListNoRefundableEquityConfigsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ListNoRefundableEquityConfigsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ListNoRefundableEquityConfigsResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListNoRefundableEquityConfigsResponse) GetData() []*ListNoRefundableEquityConfig {
	if m != nil {
		return m.Data
	}
	return nil
}

type ListNoRefundableEquityConfig struct {
	//卡付费周期id
	CardTid int32 `protobuf:"varint,1,opt,name=card_tid,json=cardTid,proto3" json:"card_tid"`
	//卡名称
	CardName string `protobuf:"bytes,2,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	//付费周期 1-年卡 2-季卡 3-月卡 4-周卡 5-日卡
	CardCycle int32 `protobuf:"varint,3,opt,name=card_cycle,json=cardCycle,proto3" json:"card_cycle"`
	//权益id
	EquityId int32 `protobuf:"varint,4,opt,name=equity_id,json=equityId,proto3" json:"equity_id"`
	//规则名称（权益名称）
	EquityName string `protobuf:"bytes,5,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//规则条件
	EquityRule string `protobuf:"bytes,6,opt,name=equity_rule,json=equityRule,proto3" json:"equity_rule"`
	//规则值
	RuleValue string `protobuf:"bytes,7,opt,name=rule_value,json=ruleValue,proto3" json:"rule_value"`
	//权益类型
	EquityType           int32    `protobuf:"varint,8,opt,name=equity_type,json=equityType,proto3" json:"equity_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListNoRefundableEquityConfig) Reset()         { *m = ListNoRefundableEquityConfig{} }
func (m *ListNoRefundableEquityConfig) String() string { return proto.CompactTextString(m) }
func (*ListNoRefundableEquityConfig) ProtoMessage()    {}
func (*ListNoRefundableEquityConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{24}
}

func (m *ListNoRefundableEquityConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListNoRefundableEquityConfig.Unmarshal(m, b)
}
func (m *ListNoRefundableEquityConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListNoRefundableEquityConfig.Marshal(b, m, deterministic)
}
func (m *ListNoRefundableEquityConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListNoRefundableEquityConfig.Merge(m, src)
}
func (m *ListNoRefundableEquityConfig) XXX_Size() int {
	return xxx_messageInfo_ListNoRefundableEquityConfig.Size(m)
}
func (m *ListNoRefundableEquityConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ListNoRefundableEquityConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ListNoRefundableEquityConfig proto.InternalMessageInfo

func (m *ListNoRefundableEquityConfig) GetCardTid() int32 {
	if m != nil {
		return m.CardTid
	}
	return 0
}

func (m *ListNoRefundableEquityConfig) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *ListNoRefundableEquityConfig) GetCardCycle() int32 {
	if m != nil {
		return m.CardCycle
	}
	return 0
}

func (m *ListNoRefundableEquityConfig) GetEquityId() int32 {
	if m != nil {
		return m.EquityId
	}
	return 0
}

func (m *ListNoRefundableEquityConfig) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *ListNoRefundableEquityConfig) GetEquityRule() string {
	if m != nil {
		return m.EquityRule
	}
	return ""
}

func (m *ListNoRefundableEquityConfig) GetRuleValue() string {
	if m != nil {
		return m.RuleValue
	}
	return ""
}

func (m *ListNoRefundableEquityConfig) GetEquityType() int32 {
	if m != nil {
		return m.EquityType
	}
	return 0
}

type CreateEquityConfigRequest struct {
	//卡ID
	CardTid int32 `protobuf:"varint,1,opt,name=card_tid,json=cardTid,proto3" json:"card_tid"`
	//用户id
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//用户名称
	UserName string          `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	Data     []*EquityConfig `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	//大区id
	OrId                 int64    `protobuf:"varint,5,opt,name=or_id,json=orId,proto3" json:"or_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateEquityConfigRequest) Reset()         { *m = CreateEquityConfigRequest{} }
func (m *CreateEquityConfigRequest) String() string { return proto.CompactTextString(m) }
func (*CreateEquityConfigRequest) ProtoMessage()    {}
func (*CreateEquityConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{25}
}

func (m *CreateEquityConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEquityConfigRequest.Unmarshal(m, b)
}
func (m *CreateEquityConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEquityConfigRequest.Marshal(b, m, deterministic)
}
func (m *CreateEquityConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEquityConfigRequest.Merge(m, src)
}
func (m *CreateEquityConfigRequest) XXX_Size() int {
	return xxx_messageInfo_CreateEquityConfigRequest.Size(m)
}
func (m *CreateEquityConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEquityConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEquityConfigRequest proto.InternalMessageInfo

func (m *CreateEquityConfigRequest) GetCardTid() int32 {
	if m != nil {
		return m.CardTid
	}
	return 0
}

func (m *CreateEquityConfigRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CreateEquityConfigRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *CreateEquityConfigRequest) GetData() []*EquityConfig {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *CreateEquityConfigRequest) GetOrId() int64 {
	if m != nil {
		return m.OrId
	}
	return 0
}

type GiftData struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//礼包名称
	PackName string `protobuf:"bytes,2,opt,name=pack_name,json=packName,proto3" json:"pack_name"`
	//礼包描述
	PackDesc string `protobuf:"bytes,3,opt,name=pack_desc,json=packDesc,proto3" json:"pack_desc"`
	//礼包价值
	PackPrice float32 `protobuf:"fixed32,4,opt,name=pack_price,json=packPrice,proto3" json:"pack_price"`
	//礼包图片
	PackImage string `protobuf:"bytes,5,opt,name=pack_image,json=packImage,proto3" json:"pack_image"`
	//礼包商品类型
	PackType int32 `protobuf:"varint,6,opt,name=pack_type,json=packType,proto3" json:"pack_type"`
	//礼包商品ID
	PackSkuId int32 `protobuf:"varint,7,opt,name=pack_sku_id,json=packSkuId,proto3" json:"pack_sku_id"`
	//状态 2-下架，1-上架
	State int32 `protobuf:"varint,8,opt,name=state,proto3" json:"state"`
	//是否主推 2-否 1-是
	IsMain     int32  `protobuf:"varint,9,opt,name=is_main,json=isMain,proto3" json:"is_main"`
	CreateTime string `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime string `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	//操作人id(前端不用传)
	UserNo string `protobuf:"bytes,12,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	//操作人(前端不用传)
	UserName string `protobuf:"bytes,13,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//库存
	Stock int32 `protobuf:"varint,14,opt,name=stock,proto3" json:"stock"`
	//主体：1-阿闻，2-极宠家，3-福码购
	OrgId                int32    `protobuf:"varint,15,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftData) Reset()         { *m = GiftData{} }
func (m *GiftData) String() string { return proto.CompactTextString(m) }
func (*GiftData) ProtoMessage()    {}
func (*GiftData) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{26}
}

func (m *GiftData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftData.Unmarshal(m, b)
}
func (m *GiftData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftData.Marshal(b, m, deterministic)
}
func (m *GiftData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftData.Merge(m, src)
}
func (m *GiftData) XXX_Size() int {
	return xxx_messageInfo_GiftData.Size(m)
}
func (m *GiftData) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftData.DiscardUnknown(m)
}

var xxx_messageInfo_GiftData proto.InternalMessageInfo

func (m *GiftData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GiftData) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *GiftData) GetPackDesc() string {
	if m != nil {
		return m.PackDesc
	}
	return ""
}

func (m *GiftData) GetPackPrice() float32 {
	if m != nil {
		return m.PackPrice
	}
	return 0
}

func (m *GiftData) GetPackImage() string {
	if m != nil {
		return m.PackImage
	}
	return ""
}

func (m *GiftData) GetPackType() int32 {
	if m != nil {
		return m.PackType
	}
	return 0
}

func (m *GiftData) GetPackSkuId() int32 {
	if m != nil {
		return m.PackSkuId
	}
	return 0
}

func (m *GiftData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *GiftData) GetIsMain() int32 {
	if m != nil {
		return m.IsMain
	}
	return 0
}

func (m *GiftData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *GiftData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *GiftData) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *GiftData) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *GiftData) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *GiftData) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GiftListRequest struct {
	//当前页
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 每页数量
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	//礼包ID
	Id int32 `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	//礼包名称
	PackName string `protobuf:"bytes,4,opt,name=pack_name,json=packName,proto3" json:"pack_name"`
	//礼包状态 -1 全部 1-上架 2-下架
	State int32 `protobuf:"varint,5,opt,name=state,proto3" json:"state"`
	//是否主推 默认-全部  1-是 2-否
	IsMain int32 `protobuf:"varint,9,opt,name=is_main,json=isMain,proto3" json:"is_main"`
	//前端库存判断,1-返回库存
	StockState           int32    `protobuf:"varint,10,opt,name=stock_state,json=stockState,proto3" json:"stock_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftListRequest) Reset()         { *m = GiftListRequest{} }
func (m *GiftListRequest) String() string { return proto.CompactTextString(m) }
func (*GiftListRequest) ProtoMessage()    {}
func (*GiftListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{27}
}

func (m *GiftListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftListRequest.Unmarshal(m, b)
}
func (m *GiftListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftListRequest.Marshal(b, m, deterministic)
}
func (m *GiftListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftListRequest.Merge(m, src)
}
func (m *GiftListRequest) XXX_Size() int {
	return xxx_messageInfo_GiftListRequest.Size(m)
}
func (m *GiftListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GiftListRequest proto.InternalMessageInfo

func (m *GiftListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GiftListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GiftListRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GiftListRequest) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *GiftListRequest) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *GiftListRequest) GetIsMain() int32 {
	if m != nil {
		return m.IsMain
	}
	return 0
}

func (m *GiftListRequest) GetStockState() int32 {
	if m != nil {
		return m.StockState
	}
	return 0
}

type GetGiftRequest struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGiftRequest) Reset()         { *m = GetGiftRequest{} }
func (m *GetGiftRequest) String() string { return proto.CompactTextString(m) }
func (*GetGiftRequest) ProtoMessage()    {}
func (*GetGiftRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{28}
}

func (m *GetGiftRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftRequest.Unmarshal(m, b)
}
func (m *GetGiftRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftRequest.Marshal(b, m, deterministic)
}
func (m *GetGiftRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftRequest.Merge(m, src)
}
func (m *GetGiftRequest) XXX_Size() int {
	return xxx_messageInfo_GetGiftRequest.Size(m)
}
func (m *GetGiftRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftRequest proto.InternalMessageInfo

func (m *GetGiftRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetGiftListResponse struct {
	Code                 int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32       `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*GiftData `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGiftListResponse) Reset()         { *m = GetGiftListResponse{} }
func (m *GetGiftListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGiftListResponse) ProtoMessage()    {}
func (*GetGiftListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{29}
}

func (m *GetGiftListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftListResponse.Unmarshal(m, b)
}
func (m *GetGiftListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftListResponse.Marshal(b, m, deterministic)
}
func (m *GetGiftListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftListResponse.Merge(m, src)
}
func (m *GetGiftListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGiftListResponse.Size(m)
}
func (m *GetGiftListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftListResponse proto.InternalMessageInfo

func (m *GetGiftListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGiftListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGiftListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetGiftListResponse) GetData() []*GiftData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetGiftResponse struct {
	Code                 int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *GiftData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetGiftResponse) Reset()         { *m = GetGiftResponse{} }
func (m *GetGiftResponse) String() string { return proto.CompactTextString(m) }
func (*GetGiftResponse) ProtoMessage()    {}
func (*GetGiftResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{30}
}

func (m *GetGiftResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftResponse.Unmarshal(m, b)
}
func (m *GetGiftResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftResponse.Marshal(b, m, deterministic)
}
func (m *GetGiftResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftResponse.Merge(m, src)
}
func (m *GetGiftResponse) XXX_Size() int {
	return xxx_messageInfo_GetGiftResponse.Size(m)
}
func (m *GetGiftResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftResponse proto.InternalMessageInfo

func (m *GetGiftResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGiftResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGiftResponse) GetData() *GiftData {
	if m != nil {
		return m.Data
	}
	return nil
}

type VoucherRequest struct {
	//优惠券id,多个用英文逗号隔开
	Vid string `protobuf:"bytes,1,opt,name=vid,proto3" json:"vid"`
	//1-商城券 2-门店券
	Type                 int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoucherRequest) Reset()         { *m = VoucherRequest{} }
func (m *VoucherRequest) String() string { return proto.CompactTextString(m) }
func (*VoucherRequest) ProtoMessage()    {}
func (*VoucherRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{31}
}

func (m *VoucherRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoucherRequest.Unmarshal(m, b)
}
func (m *VoucherRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoucherRequest.Marshal(b, m, deterministic)
}
func (m *VoucherRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoucherRequest.Merge(m, src)
}
func (m *VoucherRequest) XXX_Size() int {
	return xxx_messageInfo_VoucherRequest.Size(m)
}
func (m *VoucherRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VoucherRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VoucherRequest proto.InternalMessageInfo

func (m *VoucherRequest) GetVid() string {
	if m != nil {
		return m.Vid
	}
	return ""
}

func (m *VoucherRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type CardInfo struct {
	//卡付费周期id
	CardTid int32 `protobuf:"varint,1,opt,name=card_tid,json=cardTid,proto3" json:"card_tid"`
	//权益id
	CardName string `protobuf:"bytes,2,opt,name=card_name,json=cardName,proto3" json:"card_name"`
	//主推礼包名称
	GiftName string `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	//会员原价(元)
	MemberPrice float64 `protobuf:"fixed64,4,opt,name=member_price,json=memberPrice,proto3" json:"member_price"`
	//会员折扣价(元)
	MemberDiscPrice float64 `protobuf:"fixed64,5,opt,name=member_disc_price,json=memberDiscPrice,proto3" json:"member_disc_price"`
	//购买健康卡会员预估可省
	SaveMoney float64 `protobuf:"fixed64,6,opt,name=save_money,json=saveMoney,proto3" json:"save_money"`
	//是否开通付费会员 0-否 1-开通
	VipCardState int32 `protobuf:"varint,7,opt,name=vip_card_state,json=vipCardState,proto3" json:"vip_card_state"`
	//权益信息
	Data []*VipEquityInfo `protobuf:"bytes,8,rep,name=data,proto3" json:"data"`
	//购买记录
	NoticesList []*BuyNoticesList `protobuf:"bytes,9,rep,name=notices_list,json=noticesList,proto3" json:"notices_list"`
	//卡有效期
	ExpiryDate string `protobuf:"bytes,10,opt,name=expiry_date,json=expiryDate,proto3" json:"expiry_date"`
	//卡订单号
	OrderSn string `protobuf:"bytes,11,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//宠物体验券配置skuid
	VipPetCoupon string `protobuf:"bytes,12,opt,name=vip_pet_coupon,json=vipPetCoupon,proto3" json:"vip_pet_coupon"`
	//副标题
	TipTitle string `protobuf:"bytes,13,opt,name=tip_title,json=tipTitle,proto3" json:"tip_title"`
	//商品sku_id
	SkuId int32 `protobuf:"varint,14,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//显示健康卡标识
	ShowCardState        int32    `protobuf:"varint,15,opt,name=show_card_state,json=showCardState,proto3" json:"show_card_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardInfo) Reset()         { *m = CardInfo{} }
func (m *CardInfo) String() string { return proto.CompactTextString(m) }
func (*CardInfo) ProtoMessage()    {}
func (*CardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{32}
}

func (m *CardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardInfo.Unmarshal(m, b)
}
func (m *CardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardInfo.Marshal(b, m, deterministic)
}
func (m *CardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardInfo.Merge(m, src)
}
func (m *CardInfo) XXX_Size() int {
	return xxx_messageInfo_CardInfo.Size(m)
}
func (m *CardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CardInfo proto.InternalMessageInfo

func (m *CardInfo) GetCardTid() int32 {
	if m != nil {
		return m.CardTid
	}
	return 0
}

func (m *CardInfo) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *CardInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *CardInfo) GetMemberPrice() float64 {
	if m != nil {
		return m.MemberPrice
	}
	return 0
}

func (m *CardInfo) GetMemberDiscPrice() float64 {
	if m != nil {
		return m.MemberDiscPrice
	}
	return 0
}

func (m *CardInfo) GetSaveMoney() float64 {
	if m != nil {
		return m.SaveMoney
	}
	return 0
}

func (m *CardInfo) GetVipCardState() int32 {
	if m != nil {
		return m.VipCardState
	}
	return 0
}

func (m *CardInfo) GetData() []*VipEquityInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *CardInfo) GetNoticesList() []*BuyNoticesList {
	if m != nil {
		return m.NoticesList
	}
	return nil
}

func (m *CardInfo) GetExpiryDate() string {
	if m != nil {
		return m.ExpiryDate
	}
	return ""
}

func (m *CardInfo) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CardInfo) GetVipPetCoupon() string {
	if m != nil {
		return m.VipPetCoupon
	}
	return ""
}

func (m *CardInfo) GetTipTitle() string {
	if m != nil {
		return m.TipTitle
	}
	return ""
}

func (m *CardInfo) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *CardInfo) GetShowCardState() int32 {
	if m != nil {
		return m.ShowCardState
	}
	return 0
}

type VipEquityInfo struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//权益icon
	EquityIcon string `protobuf:"bytes,2,opt,name=equity_icon,json=equityIcon,proto3" json:"equity_icon"`
	//权益名称
	EquityName string `protobuf:"bytes,3,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//权益方案
	EquityCopy string `protobuf:"bytes,4,opt,name=equity_copy,json=equityCopy,proto3" json:"equity_copy"`
	//权益价值
	EquityPrice float64 `protobuf:"fixed64,5,opt,name=equity_price,json=equityPrice,proto3" json:"equity_price"`
	//类型 1-商品礼券  2到店礼券 3-积分兑换 4-开卡礼包0元领 5-宠物体检券 6-家庭医生服务包 7-门店专享 8-医疗礼包 9-医疗权益 10-宠物医保 11-月度领券
	EquityType int32 `protobuf:"varint,6,opt,name=equity_type,json=equityType,proto3" json:"equity_type"`
	//显示状态：0-未生效即将上线，1-生效正常显示
	Status int32 `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	//跳转链接
	JumpUrl string `protobuf:"bytes,8,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url"`
	//权益介绍
	EquityInfo string `protobuf:"bytes,9,opt,name=equity_info,json=equityInfo,proto3" json:"equity_info"`
	//权益规则
	EquityRule string `protobuf:"bytes,10,opt,name=equity_rule,json=equityRule,proto3" json:"equity_rule"`
	//权益宣传图
	EquityImg string `protobuf:"bytes,11,opt,name=equity_img,json=equityImg,proto3" json:"equity_img"`
	//未领取权益主标题
	MainTitle string `protobuf:"bytes,12,opt,name=main_title,json=mainTitle,proto3" json:"main_title"`
	//权益领取图标
	SubTitle             string   `protobuf:"bytes,13,opt,name=sub_title,json=subTitle,proto3" json:"sub_title"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipEquityInfo) Reset()         { *m = VipEquityInfo{} }
func (m *VipEquityInfo) String() string { return proto.CompactTextString(m) }
func (*VipEquityInfo) ProtoMessage()    {}
func (*VipEquityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{33}
}

func (m *VipEquityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipEquityInfo.Unmarshal(m, b)
}
func (m *VipEquityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipEquityInfo.Marshal(b, m, deterministic)
}
func (m *VipEquityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipEquityInfo.Merge(m, src)
}
func (m *VipEquityInfo) XXX_Size() int {
	return xxx_messageInfo_VipEquityInfo.Size(m)
}
func (m *VipEquityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VipEquityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VipEquityInfo proto.InternalMessageInfo

func (m *VipEquityInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VipEquityInfo) GetEquityIcon() string {
	if m != nil {
		return m.EquityIcon
	}
	return ""
}

func (m *VipEquityInfo) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *VipEquityInfo) GetEquityCopy() string {
	if m != nil {
		return m.EquityCopy
	}
	return ""
}

func (m *VipEquityInfo) GetEquityPrice() float64 {
	if m != nil {
		return m.EquityPrice
	}
	return 0
}

func (m *VipEquityInfo) GetEquityType() int32 {
	if m != nil {
		return m.EquityType
	}
	return 0
}

func (m *VipEquityInfo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *VipEquityInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *VipEquityInfo) GetEquityInfo() string {
	if m != nil {
		return m.EquityInfo
	}
	return ""
}

func (m *VipEquityInfo) GetEquityRule() string {
	if m != nil {
		return m.EquityRule
	}
	return ""
}

func (m *VipEquityInfo) GetEquityImg() string {
	if m != nil {
		return m.EquityImg
	}
	return ""
}

func (m *VipEquityInfo) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *VipEquityInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

type GetCardInfoResponse struct {
	Code                 int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *CardInfo `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetCardInfoResponse) Reset()         { *m = GetCardInfoResponse{} }
func (m *GetCardInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetCardInfoResponse) ProtoMessage()    {}
func (*GetCardInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{34}
}

func (m *GetCardInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCardInfoResponse.Unmarshal(m, b)
}
func (m *GetCardInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCardInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetCardInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCardInfoResponse.Merge(m, src)
}
func (m *GetCardInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetCardInfoResponse.Size(m)
}
func (m *GetCardInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCardInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCardInfoResponse proto.InternalMessageInfo

func (m *GetCardInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCardInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetCardInfoResponse) GetData() *CardInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type BuyNoticesList struct {
	//用户名
	UserName string `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//时间
	LastTime int32 `protobuf:"varint,2,opt,name=last_time,json=lastTime,proto3" json:"last_time"`
	//备注
	BuyMsg               string   `protobuf:"bytes,3,opt,name=buy_msg,json=buyMsg,proto3" json:"buy_msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyNoticesList) Reset()         { *m = BuyNoticesList{} }
func (m *BuyNoticesList) String() string { return proto.CompactTextString(m) }
func (*BuyNoticesList) ProtoMessage()    {}
func (*BuyNoticesList) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{35}
}

func (m *BuyNoticesList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyNoticesList.Unmarshal(m, b)
}
func (m *BuyNoticesList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyNoticesList.Marshal(b, m, deterministic)
}
func (m *BuyNoticesList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyNoticesList.Merge(m, src)
}
func (m *BuyNoticesList) XXX_Size() int {
	return xxx_messageInfo_BuyNoticesList.Size(m)
}
func (m *BuyNoticesList) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyNoticesList.DiscardUnknown(m)
}

var xxx_messageInfo_BuyNoticesList proto.InternalMessageInfo

func (m *BuyNoticesList) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *BuyNoticesList) GetLastTime() int32 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

func (m *BuyNoticesList) GetBuyMsg() string {
	if m != nil {
		return m.BuyMsg
	}
	return ""
}

type GetEquityListResponse struct {
	Code                 int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *CardInfo `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetEquityListResponse) Reset()         { *m = GetEquityListResponse{} }
func (m *GetEquityListResponse) String() string { return proto.CompactTextString(m) }
func (*GetEquityListResponse) ProtoMessage()    {}
func (*GetEquityListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{36}
}

func (m *GetEquityListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEquityListResponse.Unmarshal(m, b)
}
func (m *GetEquityListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEquityListResponse.Marshal(b, m, deterministic)
}
func (m *GetEquityListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEquityListResponse.Merge(m, src)
}
func (m *GetEquityListResponse) XXX_Size() int {
	return xxx_messageInfo_GetEquityListResponse.Size(m)
}
func (m *GetEquityListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEquityListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEquityListResponse proto.InternalMessageInfo

func (m *GetEquityListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetEquityListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetEquityListResponse) GetData() *CardInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type WelfareInfo struct {
	//福利名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	//状态 -1-投保中 0-待领取/待投保 1-已领取/投保成功 2-已过期/投保失败
	State int32 `protobuf:"varint,2,opt,name=state,proto3" json:"state"`
	//类型 1-商品礼券  2到店礼券 3-积分兑换 4-开卡礼包0元领 5-宠物体检券 6-家庭医生服务包 7-门店专享 8-医疗礼包 9-医疗权益 10-宠物医保， 11-月度返券，12-健康服务金
	Type int32 `protobuf:"varint,5,opt,name=type,proto3" json:"type"`
	//商城券列表
	CouponList []*CouponList `protobuf:"bytes,3,rep,name=coupon_list,json=couponList,proto3" json:"coupon_list"`
	//门店券列表
	StoreVouchers []*CouponList `protobuf:"bytes,4,rep,name=store_vouchers,json=storeVouchers,proto3" json:"store_vouchers"`
	//链接跳转
	Url string `protobuf:"bytes,6,opt,name=url,proto3" json:"url"`
	//权益icon
	EquityIcon string `protobuf:"bytes,7,opt,name=equity_icon,json=equityIcon,proto3" json:"equity_icon"`
	//权益领取icon
	EquityReceiveIcon string `protobuf:"bytes,8,opt,name=equity_receive_icon,json=equityReceiveIcon,proto3" json:"equity_receive_icon"`
	//权益宣传文案
	EquityCopy           string   `protobuf:"bytes,9,opt,name=equity_copy,json=equityCopy,proto3" json:"equity_copy"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WelfareInfo) Reset()         { *m = WelfareInfo{} }
func (m *WelfareInfo) String() string { return proto.CompactTextString(m) }
func (*WelfareInfo) ProtoMessage()    {}
func (*WelfareInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{37}
}

func (m *WelfareInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WelfareInfo.Unmarshal(m, b)
}
func (m *WelfareInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WelfareInfo.Marshal(b, m, deterministic)
}
func (m *WelfareInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WelfareInfo.Merge(m, src)
}
func (m *WelfareInfo) XXX_Size() int {
	return xxx_messageInfo_WelfareInfo.Size(m)
}
func (m *WelfareInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WelfareInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WelfareInfo proto.InternalMessageInfo

func (m *WelfareInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WelfareInfo) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *WelfareInfo) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *WelfareInfo) GetCouponList() []*CouponList {
	if m != nil {
		return m.CouponList
	}
	return nil
}

func (m *WelfareInfo) GetStoreVouchers() []*CouponList {
	if m != nil {
		return m.StoreVouchers
	}
	return nil
}

func (m *WelfareInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *WelfareInfo) GetEquityIcon() string {
	if m != nil {
		return m.EquityIcon
	}
	return ""
}

func (m *WelfareInfo) GetEquityReceiveIcon() string {
	if m != nil {
		return m.EquityReceiveIcon
	}
	return ""
}

func (m *WelfareInfo) GetEquityCopy() string {
	if m != nil {
		return m.EquityCopy
	}
	return ""
}

type GetWelfareResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//免费会员福利
	FreeData []*WelfareInfo `protobuf:"bytes,3,rep,name=free_data,json=freeData,proto3" json:"free_data"`
	//付费会员福利
	PaidData             []*WelfareInfo `protobuf:"bytes,4,rep,name=paid_data,json=paidData,proto3" json:"paid_data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetWelfareResponse) Reset()         { *m = GetWelfareResponse{} }
func (m *GetWelfareResponse) String() string { return proto.CompactTextString(m) }
func (*GetWelfareResponse) ProtoMessage()    {}
func (*GetWelfareResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{38}
}

func (m *GetWelfareResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWelfareResponse.Unmarshal(m, b)
}
func (m *GetWelfareResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWelfareResponse.Marshal(b, m, deterministic)
}
func (m *GetWelfareResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWelfareResponse.Merge(m, src)
}
func (m *GetWelfareResponse) XXX_Size() int {
	return xxx_messageInfo_GetWelfareResponse.Size(m)
}
func (m *GetWelfareResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWelfareResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWelfareResponse proto.InternalMessageInfo

func (m *GetWelfareResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetWelfareResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetWelfareResponse) GetFreeData() []*WelfareInfo {
	if m != nil {
		return m.FreeData
	}
	return nil
}

func (m *GetWelfareResponse) GetPaidData() []*WelfareInfo {
	if m != nil {
		return m.PaidData
	}
	return nil
}

type CouponList struct {
	//券ID
	CouponId string `protobuf:"bytes,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	//券名称
	CouponName string `protobuf:"bytes,2,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name"`
	//券状态 1未领取 2已领取 3已失效 4已过期 5已抢光
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	//券金额（分）
	VoucherTPrice int32 `protobuf:"varint,4,opt,name=voucher_t_price,json=voucherTPrice,proto3" json:"voucher_t_price"`
	//代金券使用时的订单限额(元)
	VoucherTLimit float32 `protobuf:"fixed32,5,opt,name=voucher_t_limit,json=voucherTLimit,proto3" json:"voucher_t_limit"`
	//适用范围 1全部 其他的是部分
	ApplicableScope int32 `protobuf:"varint,6,opt,name=applicable_scope,json=applicableScope,proto3" json:"applicable_scope"`
	//代金券模版有效期开始时间
	VoucherStartDateText string `protobuf:"bytes,7,opt,name=voucher_start_date_text,json=voucherStartDateText,proto3" json:"voucher_start_date_text"`
	//代金券模版有效期结束时间
	VoucherEndDateText string `protobuf:"bytes,8,opt,name=voucher_end_date_text,json=voucherEndDateText,proto3" json:"voucher_end_date_text"`
	//多少天内可用
	VoucherDays int64 `protobuf:"varint,9,opt,name=voucher_days,json=voucherDays,proto3" json:"voucher_days"`
	//类型有效期，type=2表示领取后有效期voucher_days天，type=1表示begin_time到end_time
	Type int32 `protobuf:"varint,10,opt,name=type,proto3" json:"type"`
	//权益id,领取时要传这个id,区分不同的权益领取的
	EquityId             int32    `protobuf:"varint,11,opt,name=equity_id,json=equityId,proto3" json:"equity_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponList) Reset()         { *m = CouponList{} }
func (m *CouponList) String() string { return proto.CompactTextString(m) }
func (*CouponList) ProtoMessage()    {}
func (*CouponList) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{39}
}

func (m *CouponList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponList.Unmarshal(m, b)
}
func (m *CouponList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponList.Marshal(b, m, deterministic)
}
func (m *CouponList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponList.Merge(m, src)
}
func (m *CouponList) XXX_Size() int {
	return xxx_messageInfo_CouponList.Size(m)
}
func (m *CouponList) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponList.DiscardUnknown(m)
}

var xxx_messageInfo_CouponList proto.InternalMessageInfo

func (m *CouponList) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *CouponList) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *CouponList) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CouponList) GetVoucherTPrice() int32 {
	if m != nil {
		return m.VoucherTPrice
	}
	return 0
}

func (m *CouponList) GetVoucherTLimit() float32 {
	if m != nil {
		return m.VoucherTLimit
	}
	return 0
}

func (m *CouponList) GetApplicableScope() int32 {
	if m != nil {
		return m.ApplicableScope
	}
	return 0
}

func (m *CouponList) GetVoucherStartDateText() string {
	if m != nil {
		return m.VoucherStartDateText
	}
	return ""
}

func (m *CouponList) GetVoucherEndDateText() string {
	if m != nil {
		return m.VoucherEndDateText
	}
	return ""
}

func (m *CouponList) GetVoucherDays() int64 {
	if m != nil {
		return m.VoucherDays
	}
	return 0
}

func (m *CouponList) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CouponList) GetEquityId() int32 {
	if m != nil {
		return m.EquityId
	}
	return 0
}

type GetGiftInfoResponse struct {
	Code                 int32     `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *GiftInfo `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetGiftInfoResponse) Reset()         { *m = GetGiftInfoResponse{} }
func (m *GetGiftInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetGiftInfoResponse) ProtoMessage()    {}
func (*GetGiftInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{40}
}

func (m *GetGiftInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftInfoResponse.Unmarshal(m, b)
}
func (m *GetGiftInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetGiftInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftInfoResponse.Merge(m, src)
}
func (m *GetGiftInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetGiftInfoResponse.Size(m)
}
func (m *GetGiftInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftInfoResponse proto.InternalMessageInfo

func (m *GetGiftInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGiftInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGiftInfoResponse) GetData() *GiftInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type GiftInfo struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//礼包名称
	PackName string `protobuf:"bytes,2,opt,name=pack_name,json=packName,proto3" json:"pack_name"`
	//礼包描述
	PackDesc string `protobuf:"bytes,3,opt,name=pack_desc,json=packDesc,proto3" json:"pack_desc"`
	//礼包价值
	PackPrice float32 `protobuf:"fixed32,4,opt,name=pack_price,json=packPrice,proto3" json:"pack_price"`
	//礼包图片
	PackImage string `protobuf:"bytes,5,opt,name=pack_image,json=packImage,proto3" json:"pack_image"`
	//礼包商品ID
	PackSkuId int32 `protobuf:"varint,6,opt,name=pack_sku_id,json=packSkuId,proto3" json:"pack_sku_id"`
	//订单号
	OrderSn              string   `protobuf:"bytes,7,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftInfo) Reset()         { *m = GiftInfo{} }
func (m *GiftInfo) String() string { return proto.CompactTextString(m) }
func (*GiftInfo) ProtoMessage()    {}
func (*GiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{41}
}

func (m *GiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftInfo.Unmarshal(m, b)
}
func (m *GiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftInfo.Marshal(b, m, deterministic)
}
func (m *GiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftInfo.Merge(m, src)
}
func (m *GiftInfo) XXX_Size() int {
	return xxx_messageInfo_GiftInfo.Size(m)
}
func (m *GiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GiftInfo proto.InternalMessageInfo

func (m *GiftInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GiftInfo) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *GiftInfo) GetPackDesc() string {
	if m != nil {
		return m.PackDesc
	}
	return ""
}

func (m *GiftInfo) GetPackPrice() float32 {
	if m != nil {
		return m.PackPrice
	}
	return 0
}

func (m *GiftInfo) GetPackImage() string {
	if m != nil {
		return m.PackImage
	}
	return ""
}

func (m *GiftInfo) GetPackSkuId() int32 {
	if m != nil {
		return m.PackSkuId
	}
	return 0
}

func (m *GiftInfo) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type GetUserInfoRequest struct {
	Mobile               string   `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoRequest) Reset()         { *m = GetUserInfoRequest{} }
func (m *GetUserInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoRequest) ProtoMessage()    {}
func (*GetUserInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{42}
}

func (m *GetUserInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoRequest.Unmarshal(m, b)
}
func (m *GetUserInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetUserInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoRequest.Merge(m, src)
}
func (m *GetUserInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoRequest.Size(m)
}
func (m *GetUserInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoRequest proto.InternalMessageInfo

func (m *GetUserInfoRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

type GetUserInfoResponse struct {
	// 状态码，200正常，非200错误
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//0-否 1-是付费会员
	VipCardState         int32    `protobuf:"varint,3,opt,name=vip_card_state,json=vipCardState,proto3" json:"vip_card_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoResponse) Reset()         { *m = GetUserInfoResponse{} }
func (m *GetUserInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoResponse) ProtoMessage()    {}
func (*GetUserInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{43}
}

func (m *GetUserInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoResponse.Unmarshal(m, b)
}
func (m *GetUserInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetUserInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoResponse.Merge(m, src)
}
func (m *GetUserInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoResponse.Size(m)
}
func (m *GetUserInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoResponse proto.InternalMessageInfo

func (m *GetUserInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetUserInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetUserInfoResponse) GetVipCardState() int32 {
	if m != nil {
		return m.VipCardState
	}
	return 0
}

type CardValueReq struct {
	// 卡id
	CardId int32 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id"`
	// 用户id
	ScrmId               string   `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardValueReq) Reset()         { *m = CardValueReq{} }
func (m *CardValueReq) String() string { return proto.CompactTextString(m) }
func (*CardValueReq) ProtoMessage()    {}
func (*CardValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{44}
}

func (m *CardValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardValueReq.Unmarshal(m, b)
}
func (m *CardValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardValueReq.Marshal(b, m, deterministic)
}
func (m *CardValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardValueReq.Merge(m, src)
}
func (m *CardValueReq) XXX_Size() int {
	return xxx_messageInfo_CardValueReq.Size(m)
}
func (m *CardValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardValueReq proto.InternalMessageInfo

func (m *CardValueReq) GetCardId() int32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *CardValueReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type CardOrderValueReq struct {
	// 卡id
	OrderSn              []string `protobuf:"bytes,1,rep,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardOrderValueReq) Reset()         { *m = CardOrderValueReq{} }
func (m *CardOrderValueReq) String() string { return proto.CompactTextString(m) }
func (*CardOrderValueReq) ProtoMessage()    {}
func (*CardOrderValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{45}
}

func (m *CardOrderValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardOrderValueReq.Unmarshal(m, b)
}
func (m *CardOrderValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardOrderValueReq.Marshal(b, m, deterministic)
}
func (m *CardOrderValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardOrderValueReq.Merge(m, src)
}
func (m *CardOrderValueReq) XXX_Size() int {
	return xxx_messageInfo_CardOrderValueReq.Size(m)
}
func (m *CardOrderValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CardOrderValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_CardOrderValueReq proto.InternalMessageInfo

func (m *CardOrderValueReq) GetOrderSn() []string {
	if m != nil {
		return m.OrderSn
	}
	return nil
}

type CardValueRes struct {
	// 状态码，200正常，非200错误
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message              string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*CardValueData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *CardValueRes) Reset()         { *m = CardValueRes{} }
func (m *CardValueRes) String() string { return proto.CompactTextString(m) }
func (*CardValueRes) ProtoMessage()    {}
func (*CardValueRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{46}
}

func (m *CardValueRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardValueRes.Unmarshal(m, b)
}
func (m *CardValueRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardValueRes.Marshal(b, m, deterministic)
}
func (m *CardValueRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardValueRes.Merge(m, src)
}
func (m *CardValueRes) XXX_Size() int {
	return xxx_messageInfo_CardValueRes.Size(m)
}
func (m *CardValueRes) XXX_DiscardUnknown() {
	xxx_messageInfo_CardValueRes.DiscardUnknown(m)
}

var xxx_messageInfo_CardValueRes proto.InternalMessageInfo

func (m *CardValueRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CardValueRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CardValueRes) GetData() []*CardValueData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CardOrderValueResp struct {
	// 状态码，200正常，非200错误
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message              string                `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*CardOrderValueData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CardOrderValueResp) Reset()         { *m = CardOrderValueResp{} }
func (m *CardOrderValueResp) String() string { return proto.CompactTextString(m) }
func (*CardOrderValueResp) ProtoMessage()    {}
func (*CardOrderValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{47}
}

func (m *CardOrderValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardOrderValueResp.Unmarshal(m, b)
}
func (m *CardOrderValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardOrderValueResp.Marshal(b, m, deterministic)
}
func (m *CardOrderValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardOrderValueResp.Merge(m, src)
}
func (m *CardOrderValueResp) XXX_Size() int {
	return xxx_messageInfo_CardOrderValueResp.Size(m)
}
func (m *CardOrderValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CardOrderValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_CardOrderValueResp proto.InternalMessageInfo

func (m *CardOrderValueResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CardOrderValueResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CardOrderValueResp) GetData() []*CardOrderValueData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CardValueData struct {
	//权益名称
	EquityName string `protobuf:"bytes,1,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//券编码
	CouponCode string `protobuf:"bytes,2,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code"`
	//权益价值
	FreeQuality          int32    `protobuf:"varint,3,opt,name=free_quality,json=freeQuality,proto3" json:"free_quality"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardValueData) Reset()         { *m = CardValueData{} }
func (m *CardValueData) String() string { return proto.CompactTextString(m) }
func (*CardValueData) ProtoMessage()    {}
func (*CardValueData) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{48}
}

func (m *CardValueData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardValueData.Unmarshal(m, b)
}
func (m *CardValueData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardValueData.Marshal(b, m, deterministic)
}
func (m *CardValueData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardValueData.Merge(m, src)
}
func (m *CardValueData) XXX_Size() int {
	return xxx_messageInfo_CardValueData.Size(m)
}
func (m *CardValueData) XXX_DiscardUnknown() {
	xxx_messageInfo_CardValueData.DiscardUnknown(m)
}

var xxx_messageInfo_CardValueData proto.InternalMessageInfo

func (m *CardValueData) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *CardValueData) GetCouponCode() string {
	if m != nil {
		return m.CouponCode
	}
	return ""
}

func (m *CardValueData) GetFreeQuality() int32 {
	if m != nil {
		return m.FreeQuality
	}
	return 0
}

type CardOrderValueData struct {
	//会员卡订单id
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//赠送价值，单位：分
	Equity               []*CardOrderEquity `protobuf:"bytes,2,rep,name=equity,proto3" json:"equity"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CardOrderValueData) Reset()         { *m = CardOrderValueData{} }
func (m *CardOrderValueData) String() string { return proto.CompactTextString(m) }
func (*CardOrderValueData) ProtoMessage()    {}
func (*CardOrderValueData) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{49}
}

func (m *CardOrderValueData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardOrderValueData.Unmarshal(m, b)
}
func (m *CardOrderValueData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardOrderValueData.Marshal(b, m, deterministic)
}
func (m *CardOrderValueData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardOrderValueData.Merge(m, src)
}
func (m *CardOrderValueData) XXX_Size() int {
	return xxx_messageInfo_CardOrderValueData.Size(m)
}
func (m *CardOrderValueData) XXX_DiscardUnknown() {
	xxx_messageInfo_CardOrderValueData.DiscardUnknown(m)
}

var xxx_messageInfo_CardOrderValueData proto.InternalMessageInfo

func (m *CardOrderValueData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CardOrderValueData) GetEquity() []*CardOrderEquity {
	if m != nil {
		return m.Equity
	}
	return nil
}

type CardOrderEquity struct {
	// 权益名称
	EquityName string `protobuf:"bytes,1,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	// 赠送价值，单位：分
	FreeQuality          int32    `protobuf:"varint,2,opt,name=free_quality,json=freeQuality,proto3" json:"free_quality"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardOrderEquity) Reset()         { *m = CardOrderEquity{} }
func (m *CardOrderEquity) String() string { return proto.CompactTextString(m) }
func (*CardOrderEquity) ProtoMessage()    {}
func (*CardOrderEquity) Descriptor() ([]byte, []int) {
	return fileDescriptor_aa9da4cd1bc1430d, []int{50}
}

func (m *CardOrderEquity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardOrderEquity.Unmarshal(m, b)
}
func (m *CardOrderEquity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardOrderEquity.Marshal(b, m, deterministic)
}
func (m *CardOrderEquity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardOrderEquity.Merge(m, src)
}
func (m *CardOrderEquity) XXX_Size() int {
	return xxx_messageInfo_CardOrderEquity.Size(m)
}
func (m *CardOrderEquity) XXX_DiscardUnknown() {
	xxx_messageInfo_CardOrderEquity.DiscardUnknown(m)
}

var xxx_messageInfo_CardOrderEquity proto.InternalMessageInfo

func (m *CardOrderEquity) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *CardOrderEquity) GetFreeQuality() int32 {
	if m != nil {
		return m.FreeQuality
	}
	return 0
}

func init() {
	proto.RegisterType((*DataBaseResponse)(nil), "cc.DataBaseResponse")
	proto.RegisterType((*VcBaseResponse)(nil), "cc.VcBaseResponse")
	proto.RegisterType((*BaseIdRequest)(nil), "cc.BaseIdRequest")
	proto.RegisterType((*BaseUserRequest)(nil), "cc.BaseUserRequest")
	proto.RegisterType((*BaseCardRequest)(nil), "cc.BaseCardRequest")
	proto.RegisterType((*BaseCardOrderRequest)(nil), "cc.BaseCardOrderRequest")
	proto.RegisterType((*ListPageRequest)(nil), "cc.ListPageRequest")
	proto.RegisterType((*VipCardTemplate)(nil), "cc.VipCardTemplate")
	proto.RegisterType((*VipCardTemplateListRequest)(nil), "cc.VipCardTemplateListRequest")
	proto.RegisterType((*VipCardTemplateListResponse)(nil), "cc.VipCardTemplateListResponse")
	proto.RegisterType((*VipCardTemplateDetailResponse)(nil), "cc.VipCardTemplateDetailResponse")
	proto.RegisterType((*VipCardTemplateAddRequest)(nil), "cc.VipCardTemplateAddRequest")
	proto.RegisterType((*VipCardTemplateUpdateRequest)(nil), "cc.VipCardTemplateUpdateRequest")
	proto.RegisterType((*VipEquity)(nil), "cc.VipEquity")
	proto.RegisterType((*GetVipEquityResponse)(nil), "cc.GetVipEquityResponse")
	proto.RegisterType((*ListVipEquityResponse)(nil), "cc.ListVipEquityResponse")
	proto.RegisterType((*CreateOrUpdateVipEquityRequest)(nil), "cc.CreateOrUpdateVipEquityRequest")
	proto.RegisterType((*EquityConfig)(nil), "cc.EquityConfig")
	proto.RegisterType((*EquityConfigValue)(nil), "cc.EquityConfigValue")
	proto.RegisterType((*GetEquityConfigRequest)(nil), "cc.GetEquityConfigRequest")
	proto.RegisterType((*GetEquityConfigResponse)(nil), "cc.GetEquityConfigResponse")
	proto.RegisterType((*ListEquityConfig)(nil), "cc.ListEquityConfig")
	proto.RegisterType((*ListEquityConfigsResponse)(nil), "cc.ListEquityConfigsResponse")
	proto.RegisterType((*ListNoRefundableEquityConfigsResponse)(nil), "cc.ListNoRefundableEquityConfigsResponse")
	proto.RegisterType((*ListNoRefundableEquityConfig)(nil), "cc.ListNoRefundableEquityConfig")
	proto.RegisterType((*CreateEquityConfigRequest)(nil), "cc.CreateEquityConfigRequest")
	proto.RegisterType((*GiftData)(nil), "cc.GiftData")
	proto.RegisterType((*GiftListRequest)(nil), "cc.GiftListRequest")
	proto.RegisterType((*GetGiftRequest)(nil), "cc.GetGiftRequest")
	proto.RegisterType((*GetGiftListResponse)(nil), "cc.GetGiftListResponse")
	proto.RegisterType((*GetGiftResponse)(nil), "cc.GetGiftResponse")
	proto.RegisterType((*VoucherRequest)(nil), "cc.VoucherRequest")
	proto.RegisterType((*CardInfo)(nil), "cc.CardInfo")
	proto.RegisterType((*VipEquityInfo)(nil), "cc.VipEquityInfo")
	proto.RegisterType((*GetCardInfoResponse)(nil), "cc.GetCardInfoResponse")
	proto.RegisterType((*BuyNoticesList)(nil), "cc.BuyNoticesList")
	proto.RegisterType((*GetEquityListResponse)(nil), "cc.GetEquityListResponse")
	proto.RegisterType((*WelfareInfo)(nil), "cc.WelfareInfo")
	proto.RegisterType((*GetWelfareResponse)(nil), "cc.GetWelfareResponse")
	proto.RegisterType((*CouponList)(nil), "cc.CouponList")
	proto.RegisterType((*GetGiftInfoResponse)(nil), "cc.GetGiftInfoResponse")
	proto.RegisterType((*GiftInfo)(nil), "cc.GiftInfo")
	proto.RegisterType((*GetUserInfoRequest)(nil), "cc.GetUserInfoRequest")
	proto.RegisterType((*GetUserInfoResponse)(nil), "cc.GetUserInfoResponse")
	proto.RegisterType((*CardValueReq)(nil), "cc.CardValueReq")
	proto.RegisterType((*CardOrderValueReq)(nil), "cc.CardOrderValueReq")
	proto.RegisterType((*CardValueRes)(nil), "cc.CardValueRes")
	proto.RegisterType((*CardOrderValueResp)(nil), "cc.CardOrderValueResp")
	proto.RegisterType((*CardValueData)(nil), "cc.CardValueData")
	proto.RegisterType((*CardOrderValueData)(nil), "cc.CardOrderValueData")
	proto.RegisterType((*CardOrderEquity)(nil), "cc.CardOrderEquity")
}

func init() { proto.RegisterFile("cc/vip_card.proto", fileDescriptor_aa9da4cd1bc1430d) }

var fileDescriptor_aa9da4cd1bc1430d = []byte{
	// 2976 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3a, 0x4b, 0x6f, 0x1c, 0x59,
	0xd5, 0xea, 0x6e, 0xf7, 0xa3, 0x4e, 0xbf, 0xec, 0x72, 0xec, 0x74, 0xda, 0x93, 0xc4, 0xa9, 0x99,
	0xcc, 0x78, 0xf2, 0x45, 0x8e, 0xe6, 0xf5, 0x49, 0x68, 0xd0, 0x3c, 0x62, 0x47, 0xc1, 0xa3, 0x49,
	0x26, 0xb4, 0x9d, 0x20, 0x24, 0x44, 0x51, 0xae, 0xba, 0xee, 0x14, 0xee, 0xee, 0xaa, 0xd4, 0xad,
	0x72, 0xd2, 0x20, 0xc1, 0x1a, 0x58, 0xb0, 0x41, 0x48, 0x08, 0x09, 0x09, 0xb1, 0x60, 0xc7, 0x16,
	0x89, 0x5f, 0x00, 0x0b, 0xfe, 0x02, 0x7f, 0x83, 0x2d, 0x3a, 0xe7, 0xde, 0xaa, 0xbe, 0xb7, 0xba,
	0xda, 0x9d, 0x78, 0x82, 0x10, 0xec, 0xea, 0x9e, 0x73, 0xee, 0xeb, 0xbc, 0xcf, 0xb9, 0x05, 0x6b,
	0xae, 0x7b, 0xe7, 0xcc, 0x0f, 0x6d, 0xd7, 0x89, 0xbc, 0xdd, 0x30, 0x0a, 0xe2, 0xc0, 0x2c, 0xbb,
	0x6e, 0x7f, 0x6b, 0x18, 0x04, 0xc3, 0x11, 0xbb, 0x43, 0x90, 0xe3, 0xe4, 0xe4, 0x0e, 0x1b, 0x87,
	0xf1, 0x54, 0x10, 0x58, 0x47, 0xb0, 0xba, 0xef, 0xc4, 0xce, 0x5d, 0x87, 0xb3, 0x01, 0xe3, 0x61,
	0x30, 0xe1, 0xcc, 0x34, 0x61, 0xc5, 0x0d, 0x3c, 0xd6, 0x2b, 0x6d, 0x97, 0x76, 0xaa, 0x03, 0xfa,
	0x36, 0x7b, 0x50, 0x1f, 0x33, 0xce, 0x9d, 0x21, 0xeb, 0x95, 0xb7, 0x4b, 0x3b, 0xc6, 0x20, 0x1d,
	0x22, 0xb5, 0xe7, 0xc4, 0x4e, 0xaf, 0xb2, 0x5d, 0xd9, 0x31, 0x06, 0xf4, 0x6d, 0x7d, 0x02, 0x9d,
	0x27, 0xee, 0xc5, 0xd7, 0xb4, 0xae, 0x43, 0x1b, 0x67, 0x1f, 0x78, 0x03, 0xf6, 0x2c, 0x61, 0x3c,
	0x36, 0x3b, 0x50, 0xf6, 0x3d, 0x39, 0xb9, 0xec, 0x7b, 0xd6, 0x2d, 0xe8, 0x22, 0xc1, 0x63, 0xce,
	0xa2, 0x94, 0xe4, 0x32, 0xd4, 0x13, 0xce, 0x22, 0x5b, 0xd2, 0x19, 0x83, 0x1a, 0x0e, 0x0f, 0x3c,
	0xcb, 0x17, 0xb4, 0x7b, 0x4e, 0xb4, 0x68, 0x39, 0x75, 0x6e, 0x59, 0x9d, 0x8b, 0xc7, 0x8e, 0xa7,
	0x21, 0xeb, 0x55, 0xc4, 0xb1, 0xf1, 0xdb, 0xbc, 0x02, 0x8d, 0x20, 0xf2, 0x58, 0x64, 0xf3, 0x49,
	0x6f, 0x45, 0x9c, 0x9b, 0xc6, 0x87, 0x13, 0xeb, 0x0b, 0xb8, 0x94, 0x6e, 0xf5, 0x15, 0x82, 0xd2,
	0xfd, 0xd4, 0x29, 0x25, 0x6d, 0xca, 0xc2, 0xad, 0xad, 0x1f, 0x40, 0xf7, 0x4b, 0x9f, 0xc7, 0x8f,
	0x9c, 0x21, 0x4b, 0x97, 0x79, 0x03, 0x8c, 0xd0, 0x19, 0xb2, 0x83, 0x89, 0xc7, 0x5e, 0xc8, 0xd3,
	0xcf, 0x00, 0x66, 0x1f, 0x1a, 0x38, 0x38, 0xf4, 0x7f, 0x24, 0xf8, 0x59, 0x1d, 0x64, 0x63, 0x73,
	0x1d, 0xaa, 0x01, 0xed, 0x21, 0x2f, 0x12, 0xe0, 0x0e, 0xff, 0xac, 0x40, 0xf7, 0x89, 0x1f, 0xe2,
	0x69, 0x8f, 0xd8, 0x38, 0x1c, 0x39, 0x31, 0x9b, 0xe3, 0xcc, 0x16, 0x18, 0xa8, 0x4e, 0xf6, 0xc4,
	0x19, 0xa7, 0x52, 0x6a, 0x20, 0xe0, 0xa1, 0x33, 0x66, 0x19, 0x52, 0x61, 0x11, 0x21, 0x8f, 0x90,
	0x4d, 0x57, 0x01, 0x08, 0xe9, 0x4e, 0xdd, 0x11, 0x23, 0x46, 0x55, 0x07, 0x44, 0xbe, 0x87, 0x00,
	0xf3, 0x4d, 0x68, 0x7b, 0x49, 0xe4, 0xc4, 0x7e, 0x30, 0xb1, 0x3d, 0x27, 0x66, 0xbd, 0x2a, 0x51,
	0xb4, 0x52, 0xe0, 0x3e, 0x9e, 0xe6, 0x06, 0xb4, 0xc6, 0x6c, 0x7c, 0xcc, 0x22, 0x3b, 0x8c, 0x7c,
	0x97, 0xf5, 0x6a, 0xdb, 0xa5, 0x9d, 0xd2, 0xa0, 0x29, 0x60, 0x8f, 0x10, 0x64, 0xde, 0x82, 0x35,
	0x49, 0xe2, 0xf9, 0xdc, 0x95, 0x74, 0x75, 0xa2, 0xeb, 0x0a, 0xc4, 0xbe, 0xcf, 0x5d, 0x41, 0x9b,
	0x4a, 0xb3, 0xa1, 0x48, 0xf3, 0x3a, 0x34, 0xdd, 0x88, 0x39, 0x31, 0xb3, 0x63, 0x7f, 0xcc, 0x7a,
	0x06, 0x5d, 0x11, 0x04, 0xe8, 0xc8, 0x1f, 0x13, 0x41, 0x12, 0x7a, 0x19, 0x01, 0x08, 0x02, 0x01,
	0x22, 0x02, 0xbc, 0x28, 0x5e, 0x49, 0xf0, 0xa8, 0x49, 0x78, 0x83, 0x20, 0x29, 0x93, 0x62, 0x3f,
	0xb4, 0x63, 0x3f, 0x1e, 0xb1, 0x5e, 0x4b, 0x70, 0x30, 0xf6, 0xc3, 0x23, 0x1c, 0xcf, 0xe4, 0xd2,
	0xde, 0x2e, 0xed, 0x54, 0x84, 0x5c, 0x50, 0x5b, 0x3c, 0x9f, 0xdb, 0x11, 0x72, 0xa5, 0x43, 0x37,
	0xa9, 0x7b, 0x3e, 0x1f, 0x20, 0x43, 0x36, 0xa0, 0xf6, 0x9c, 0x1d, 0xe3, 0x84, 0x2e, 0xdd, 0xa1,
	0xfa, 0x9c, 0x1d, 0x1f, 0x90, 0xfe, 0x06, 0x91, 0xd8, 0x7f, 0x55, 0x28, 0x51, 0x10, 0xd1, 0xe6,
	0x1b, 0x50, 0xe3, 0xa7, 0x09, 0xd2, 0xaf, 0x09, 0x7a, 0x7e, 0x9a, 0x1c, 0x78, 0xd6, 0x4f, 0xa1,
	0x9f, 0x13, 0x3c, 0xaa, 0xda, 0xd7, 0x57, 0xb3, 0x22, 0x73, 0xc9, 0xae, 0xb8, 0xa2, 0xa8, 0xde,
	0xcf, 0x4b, 0xb0, 0x55, 0x78, 0x82, 0x0b, 0xb9, 0xa0, 0x4b, 0x50, 0x8d, 0x83, 0xd8, 0x19, 0xc9,
	0x7d, 0xc5, 0xc0, 0x7c, 0x47, 0x3a, 0xa6, 0x95, 0xed, 0xca, 0x4e, 0xf3, 0xfd, 0xf5, 0x5d, 0xd7,
	0xdd, 0xcd, 0x6d, 0x29, 0xbd, 0xd5, 0x19, 0x5c, 0xcd, 0x21, 0xf6, 0x59, 0xec, 0xf8, 0xa3, 0x0b,
	0x9e, 0xe6, 0x9d, 0xcc, 0x21, 0x96, 0xce, 0xdf, 0xf7, 0x37, 0x65, 0xb8, 0x92, 0xc3, 0x7c, 0xee,
	0x65, 0x3e, 0x4a, 0xb3, 0xbc, 0xd2, 0x79, 0x96, 0x57, 0x3e, 0xd7, 0xf2, 0x2a, 0x4b, 0x2d, 0x6f,
	0xe5, 0x25, 0x2c, 0xaf, 0xfa, 0x92, 0x96, 0x57, 0x3b, 0xdf, 0xf2, 0xea, 0x8a, 0x62, 0x68, 0x86,
	0xd1, 0xd0, 0x0d, 0xc3, 0xfa, 0x47, 0x19, 0xde, 0xc8, 0xf1, 0xe6, 0x31, 0x99, 0xdc, 0x22, 0x17,
	0xfe, 0x3f, 0xef, 0xa8, 0x54, 0xaf, 0x60, 0x2c, 0xf2, 0x0a, 0xa0, 0x7a, 0x05, 0x8d, 0xc1, 0xcd,
	0x1c, 0x83, 0x7f, 0x51, 0x03, 0xe3, 0x89, 0x1f, 0xde, 0x7b, 0x96, 0xf8, 0xf1, 0x74, 0x8e, 0x9b,
	0xd7, 0xa1, 0xc9, 0x08, 0x63, 0xfb, 0x6e, 0x30, 0x91, 0xfc, 0x04, 0x01, 0x3a, 0x70, 0x83, 0x89,
	0x42, 0x40, 0x0c, 0xaf, 0xa8, 0x04, 0xc4, 0xf2, 0x19, 0x81, 0x1b, 0x84, 0x53, 0x19, 0x28, 0x25,
	0xc1, 0x5e, 0x10, 0x4e, 0x91, 0x65, 0x92, 0x40, 0xd3, 0x30, 0x01, 0x13, 0x6c, 0x98, 0xad, 0x41,
	0xdc, 0xa8, 0xd1, 0xf1, 0xe4, 0x1a, 0xa9, 0xe8, 0xc6, 0x4e, 0xec, 0x3e, 0xb5, 0x15, 0xe5, 0x32,
	0x08, 0x92, 0xa2, 0x7d, 0xce, 0x13, 0x66, 0x2b, 0xcc, 0x34, 0x08, 0x42, 0xe8, 0x9b, 0xd0, 0x71,
	0x83, 0xd1, 0x88, 0xb9, 0x24, 0x5b, 0xdf, 0xe3, 0xd2, 0xfb, 0xb7, 0x67, 0xd0, 0x03, 0x8f, 0x9b,
	0x9b, 0x50, 0xe3, 0xb1, 0x13, 0x27, 0x5c, 0x72, 0x57, 0x8e, 0xf2, 0x91, 0xa3, 0xb9, 0x2c, 0x72,
	0xb4, 0xe6, 0x22, 0xc7, 0x06, 0xd4, 0xc8, 0x35, 0x72, 0x72, 0xff, 0xc6, 0xa0, 0x8a, 0xbe, 0x91,
	0xa3, 0xa4, 0x7f, 0x98, 0x8c, 0x43, 0x3b, 0x89, 0x46, 0xe4, 0xff, 0x8d, 0x41, 0x1d, 0xc7, 0x8f,
	0xa3, 0x91, 0x2a, 0x97, 0xc9, 0x49, 0x40, 0x41, 0x60, 0x26, 0x97, 0xc9, 0x49, 0xa0, 0x10, 0x44,
	0xc9, 0x28, 0x8d, 0x06, 0x92, 0x60, 0x90, 0x8c, 0x88, 0x27, 0xe9, 0x0a, 0xe3, 0x21, 0x45, 0x05,
	0x63, 0x60, 0xc8, 0x05, 0xc6, 0x43, 0x42, 0xbf, 0x08, 0xfd, 0x68, 0x6a, 0x7b, 0xce, 0xb4, 0x67,
	0x0a, 0x96, 0x09, 0xc8, 0xbe, 0x43, 0x42, 0x8b, 0x98, 0xcb, 0xfc, 0x33, 0xc9, 0xd3, 0x75, 0x22,
	0x68, 0x4a, 0x18, 0x71, 0xf5, 0x16, 0xac, 0xc9, 0x0d, 0xf8, 0xd3, 0x20, 0x8a, 0x85, 0x7e, 0x5c,
	0xa2, 0x7d, 0xba, 0x02, 0x71, 0x88, 0xf0, 0xd4, 0x2e, 0x7d, 0x6e, 0x3b, 0x6e, 0xec, 0x9f, 0xb1,
	0xde, 0x86, 0xb0, 0x4b, 0x9f, 0x7f, 0x4e, 0x63, 0x21, 0x5c, 0x7f, 0x22, 0xf5, 0x77, 0x53, 0x9c,
	0x14, 0x21, 0x22, 0x74, 0x6e, 0x81, 0xc1, 0x93, 0x63, 0x89, 0xbd, 0x2c, 0xb4, 0x9b, 0x27, 0xc7,
	0x02, 0xb9, 0x0b, 0xeb, 0x29, 0x1b, 0xe4, 0x71, 0x49, 0x8f, 0x7b, 0x44, 0x26, 0xcf, 0x37, 0x10,
	0x18, 0x54, 0x67, 0x6b, 0x08, 0x97, 0xee, 0xb3, 0x38, 0xb3, 0x87, 0x0b, 0x7a, 0xfe, 0x1b, 0x9a,
	0xe7, 0x6f, 0x4b, 0xcf, 0x2f, 0x97, 0x14, 0x3e, 0xff, 0x27, 0xb0, 0x81, 0x81, 0xee, 0xeb, 0xee,
	0x54, 0x1c, 0xf1, 0x6e, 0x68, 0x11, 0xaf, 0x70, 0xff, 0x47, 0x70, 0x6d, 0x8f, 0x34, 0xf4, 0xab,
	0x48, 0xf8, 0x53, 0xe5, 0x24, 0xc2, 0xb1, 0xee, 0x42, 0x93, 0x12, 0x52, 0xc1, 0x24, 0x3a, 0xcf,
	0xdc, 0x5a, 0x80, 0x14, 0xe2, 0xdb, 0xfa, 0x4b, 0x05, 0x5a, 0xf7, 0xa4, 0x59, 0x4f, 0x4e, 0xfc,
	0x21, 0xaa, 0xaf, 0x70, 0xb6, 0x99, 0x47, 0xa9, 0x93, 0xaf, 0x15, 0x4e, 0x3a, 0x55, 0x3e, 0x2f,
	0x0d, 0x5b, 0x52, 0xf7, 0x3c, 0xc5, 0xce, 0x2a, 0x9a, 0x9d, 0xbd, 0x09, 0xed, 0x30, 0xf2, 0xcf,
	0xfc, 0x11, 0x1b, 0x32, 0x32, 0x16, 0xe1, 0x4b, 0x5a, 0x19, 0x50, 0xda, 0x4c, 0x10, 0xc6, 0x2c,
	0xb2, 0x27, 0x01, 0x79, 0x12, 0xcc, 0xb0, 0x71, 0xfc, 0x30, 0x40, 0x5e, 0xd1, 0x27, 0xf9, 0x0f,
	0x34, 0x32, 0x1c, 0xe4, 0xad, 0xb7, 0xbe, 0xcc, 0x7a, 0x1b, 0x73, 0xd6, 0x9b, 0xf3, 0x4e, 0xc6,
	0x9c, 0x77, 0xba, 0x0e, 0xa9, 0x61, 0xd8, 0x93, 0x64, 0x2c, 0xbd, 0x07, 0x48, 0xd0, 0xc3, 0x64,
	0x5c, 0x6c, 0x2a, 0xcd, 0x62, 0x53, 0xb9, 0x06, 0x10, 0xb1, 0x93, 0x64, 0xe2, 0x39, 0xc7, 0x32,
	0x8f, 0xa4, 0xb5, 0x52, 0x88, 0xf9, 0x21, 0xc0, 0x49, 0xc4, 0x98, 0x7d, 0xe6, 0x8c, 0x12, 0xd6,
	0x6b, 0x93, 0x06, 0x6c, 0xa0, 0xd4, 0x54, 0xd9, 0x3c, 0x41, 0xe4, 0xc0, 0x40, 0x42, 0xfa, 0xb4,
	0x5e, 0xc0, 0xda, 0x1c, 0x1e, 0x8d, 0x5c, 0x65, 0xb8, 0x4c, 0x3e, 0x9a, 0x0a, 0xbf, 0x91, 0x84,
	0x76, 0x7b, 0x96, 0x38, 0x23, 0xd4, 0x12, 0x21, 0xcb, 0x26, 0xc2, 0xbe, 0x2d, 0x40, 0x68, 0xbe,
	0xdc, 0x19, 0x31, 0xae, 0x06, 0x5d, 0x83, 0x20, 0xc8, 0x1c, 0xeb, 0x5b, 0xb0, 0x79, 0x9f, 0xc5,
	0xea, 0xe6, 0x4a, 0xb1, 0xb4, 0x48, 0x7f, 0xb2, 0x5c, 0xb2, 0x3c, 0x4b, 0x97, 0xad, 0x31, 0x5c,
	0x9e, 0x5b, 0xe9, 0x42, 0x46, 0xf5, 0x96, 0x52, 0xc9, 0x36, 0xdf, 0x5f, 0xcd, 0x33, 0x4f, 0x5a,
	0xd0, 0x9f, 0xca, 0xb0, 0x8a, 0x26, 0xfc, 0x0a, 0x3a, 0xbf, 0x38, 0x31, 0x59, 0x1a, 0x46, 0xd3,
	0xf2, 0x70, 0x12, 0x48, 0xb5, 0xa7, 0xf2, 0xf0, 0x61, 0x80, 0xcb, 0x0a, 0x04, 0xce, 0x13, 0x1a,
	0xdf, 0x20, 0x94, 0x33, 0xaf, 0xbb, 0xb5, 0xc2, 0x9a, 0x65, 0x96, 0xf3, 0xd4, 0xf3, 0x39, 0x8f,
	0x5e, 0xd2, 0x34, 0xf2, 0x25, 0x4d, 0x26, 0x06, 0x43, 0xa9, 0x5a, 0x94, 0x1a, 0x04, 0xd4, 0x1a,
	0xc4, 0xfa, 0x59, 0x09, 0xae, 0xe4, 0x19, 0xc6, 0x5f, 0xab, 0xdf, 0xdb, 0xd1, 0xfc, 0xde, 0x25,
	0x14, 0x5c, 0x7e, 0x43, 0x29, 0xbc, 0xdf, 0x97, 0xe0, 0x26, 0xa2, 0x1e, 0x06, 0x83, 0xcc, 0x74,
	0xfe, 0x7d, 0xe7, 0xfa, 0x50, 0x3b, 0xd7, 0x76, 0x7a, 0xae, 0x45, 0x9b, 0xcb, 0x33, 0xfe, 0xaa,
	0x0c, 0x6f, 0x9c, 0x47, 0x76, 0x61, 0x65, 0x5b, 0x52, 0x17, 0x68, 0xce, 0x79, 0x25, 0xe7, 0x9c,
	0x73, 0x8a, 0x5a, 0x3d, 0x27, 0xdf, 0xa3, 0xc4, 0xa3, 0x56, 0x94, 0x78, 0x20, 0x46, 0x3a, 0x28,
	0xe1, 0x6f, 0x0d, 0x84, 0x08, 0xa7, 0x93, 0xf3, 0xa6, 0x8d, 0xbc, 0x37, 0xb5, 0xfe, 0x58, 0x82,
	0x2b, 0x22, 0x74, 0xbd, 0xa2, 0xd3, 0x58, 0xd8, 0xdc, 0xd1, 0x4c, 0xa8, 0x92, 0x33, 0xa1, 0xb7,
	0x34, 0xd9, 0x2d, 0x70, 0x06, 0x33, 0x4b, 0xa8, 0x2a, 0x0e, 0xe9, 0x0f, 0x15, 0x68, 0xdc, 0xf7,
	0x4f, 0xe2, 0x7d, 0xa4, 0x28, 0xa8, 0x53, 0x42, 0xc7, 0x3d, 0xd5, 0x24, 0x84, 0x80, 0x34, 0x1f,
	0x22, 0xa4, 0xc7, 0xb8, 0x9b, 0x9e, 0x08, 0x01, 0xfb, 0x8c, 0xbb, 0xc8, 0x40, 0x42, 0x8a, 0x74,
	0x19, 0x05, 0x54, 0x1e, 0x10, 0xb9, 0x48, 0x96, 0x53, 0xb4, 0x3f, 0x46, 0xfd, 0x14, 0x02, 0x22,
	0xf4, 0x01, 0x02, 0xb2, 0xa5, 0x95, 0x4c, 0x9a, 0x96, 0xa6, 0x48, 0x75, 0x0d, 0x9a, 0x84, 0x94,
	0xbd, 0x82, 0x7a, 0x5a, 0xf3, 0xbb, 0xa7, 0x87, 0xa7, 0xc9, 0x81, 0x87, 0xea, 0x8d, 0xc1, 0x38,
	0x15, 0x8b, 0x18, 0x20, 0x63, 0x7d, 0x6e, 0x63, 0x46, 0x26, 0x83, 0x5f, 0xcd, 0xe7, 0x0f, 0x1c,
	0x7f, 0x92, 0x8f, 0xad, 0xb0, 0x2c, 0xb6, 0x36, 0xe7, 0xfc, 0x93, 0xe2, 0xf6, 0x5a, 0x8b, 0xdd,
	0x5e, 0x3b, 0x27, 0x33, 0x3a, 0x66, 0xe0, 0x9e, 0x52, 0xd6, 0x4c, 0xc7, 0x0c, 0xdc, 0x53, 0x91,
	0x65, 0x0f, 0x95, 0x9e, 0x49, 0x10, 0x0d, 0x0f, 0x3c, 0xeb, 0xaf, 0x25, 0xe8, 0xa2, 0x94, 0x5e,
	0x4f, 0xe7, 0x43, 0x88, 0xb9, 0x52, 0x2c, 0xe6, 0x95, 0x9c, 0x98, 0x33, 0x76, 0x56, 0x5f, 0x96,
	0x9d, 0x74, 0x13, 0x5b, 0x4c, 0x92, 0x79, 0x04, 0x81, 0x0e, 0x11, 0x62, 0x6d, 0x43, 0xe7, 0x3e,
	0x8b, 0xf1, 0x32, 0x8b, 0xfa, 0xa5, 0x3f, 0x86, 0x75, 0x49, 0xf1, 0xda, 0xdb, 0x2c, 0xdb, 0x9a,
	0xa1, 0xb4, 0xd0, 0x50, 0x52, 0xe5, 0x97, 0x0e, 0xcd, 0x81, 0x6e, 0x76, 0xbc, 0x0b, 0x6d, 0xbc,
	0xad, 0xe5, 0xd5, 0x45, 0x5b, 0xfc, 0x3f, 0x74, 0x9e, 0x04, 0x89, 0xfb, 0x74, 0xd6, 0x72, 0x5d,
	0x85, 0xca, 0x59, 0x96, 0xbb, 0xe0, 0x67, 0x56, 0x54, 0x97, 0x67, 0x45, 0xb5, 0xf5, 0xcb, 0x15,
	0x68, 0xec, 0x39, 0x91, 0x47, 0xb5, 0xd3, 0x45, 0xfd, 0xea, 0x16, 0x18, 0x43, 0xff, 0x24, 0xd6,
	0xfc, 0x08, 0x02, 0x08, 0x99, 0xef, 0x0c, 0xac, 0xbc, 0x64, 0x67, 0xa0, 0x5a, 0xdc, 0x19, 0xa0,
	0xac, 0xea, 0x8c, 0xd9, 0xe3, 0x60, 0xc2, 0xa6, 0xb2, 0xcd, 0x60, 0x20, 0xe4, 0x01, 0x02, 0xcc,
	0xb7, 0xa0, 0x93, 0xbe, 0x00, 0x48, 0x6d, 0x11, 0xb6, 0xdc, 0x3a, 0x13, 0xbd, 0x14, 0xd2, 0x17,
	0xf3, 0xa6, 0xe4, 0x67, 0x83, 0x44, 0xb6, 0xa6, 0xe5, 0xf6, 0xc8, 0x09, 0xe9, 0xdc, 0x3e, 0x82,
	0xd6, 0x24, 0x88, 0x7d, 0x97, 0x71, 0x7b, 0xe4, 0xf3, 0xb8, 0x67, 0x10, 0xb9, 0x89, 0xe4, 0x77,
	0x93, 0xe9, 0x43, 0x81, 0x22, 0x7d, 0x6a, 0x4e, 0x66, 0x03, 0xf2, 0xe4, 0x69, 0x09, 0x19, 0x67,
	0xd6, 0x9f, 0xd6, 0x90, 0xb1, 0xde, 0x40, 0x6f, 0xea, 0xdd, 0x70, 0x79, 0xfe, 0x90, 0xc5, 0xb6,
	0x1b, 0x24, 0x61, 0x30, 0x91, 0xe6, 0x8f, 0xe7, 0x7f, 0xc4, 0xe2, 0x3d, 0x82, 0xe9, 0x8d, 0x8d,
	0x76, 0xae, 0xa5, 0x3a, 0x6b, 0x79, 0x76, 0x94, 0x96, 0xa7, 0xf9, 0x36, 0x74, 0xf9, 0xd3, 0xe0,
	0xb9, 0xca, 0x1a, 0xe1, 0x0e, 0xda, 0x08, 0xce, 0x78, 0x63, 0xfd, 0xba, 0x02, 0x6d, 0x8d, 0x19,
	0xff, 0xad, 0xbd, 0x91, 0x59, 0x39, 0x55, 0xd7, 0xca, 0x29, 0xb5, 0xbb, 0xd0, 0x38, 0xb7, 0xbb,
	0x60, 0x2c, 0xeb, 0x2e, 0xc0, 0x92, 0xee, 0x42, 0xb3, 0xa0, 0xbb, 0xa0, 0x94, 0xf4, 0xad, 0x73,
	0x4b, 0xfa, 0xb6, 0x5e, 0xd2, 0x5b, 0x8c, 0x5c, 0x58, 0x6a, 0xac, 0xaf, 0xcf, 0x93, 0x64, 0x2b,
	0x0a, 0x4f, 0xe2, 0x42, 0x47, 0x57, 0x6e, 0x3d, 0xe4, 0x94, 0x72, 0x21, 0x67, 0x0b, 0x8c, 0x91,
	0xc3, 0x63, 0x11, 0xc7, 0x64, 0x50, 0x40, 0x40, 0x1a, 0xc5, 0x8e, 0x93, 0xa9, 0x3d, 0xe6, 0x43,
	0xa9, 0x04, 0xb5, 0xe3, 0x64, 0xfa, 0x80, 0x0f, 0xad, 0x21, 0x6c, 0x64, 0x25, 0xcb, 0xd7, 0x70,
	0xc8, 0xcb, 0x6f, 0xf3, 0xe7, 0x32, 0x34, 0xbf, 0xc3, 0x46, 0x27, 0x4e, 0xc4, 0x48, 0x80, 0x26,
	0xac, 0x28, 0xd7, 0xa0, 0xef, 0x59, 0x34, 0x2a, 0xab, 0xd1, 0x28, 0xf5, 0x96, 0x55, 0xa5, 0x05,
	0x79, 0x07, 0x9a, 0xc2, 0x2a, 0x85, 0x3f, 0x10, 0x75, 0x52, 0x87, 0xb6, 0x25, 0x30, 0x5d, 0x05,
	0xdc, 0xec, 0xdb, 0xfc, 0x08, 0x3a, 0x3c, 0x0e, 0x22, 0x66, 0x9f, 0x09, 0xe7, 0xcc, 0x65, 0x94,
	0xc8, 0xcf, 0x69, 0x13, 0x95, 0xf4, 0xe0, 0x1c, 0x7d, 0x37, 0x6a, 0xa7, 0xc8, 0x21, 0xf1, 0x33,
	0x6f, 0x73, 0xf5, 0x39, 0x9b, 0x5b, 0xd0, 0xf0, 0x69, 0x2c, 0x68, 0xf8, 0xe4, 0x4d, 0xd0, 0xc8,
	0x9b, 0xa0, 0xf5, 0xbb, 0x12, 0x98, 0xf7, 0x59, 0x2c, 0x99, 0x77, 0x41, 0x01, 0xdd, 0x06, 0xaa,
	0xb5, 0x6d, 0xa5, 0xac, 0xec, 0xe2, 0xd5, 0x15, 0x91, 0x0c, 0x1a, 0x48, 0x41, 0xa9, 0xe2, 0x6d,
	0xcc, 0x19, 0x7c, 0xcf, 0x56, 0xc2, 0xe9, 0x3c, 0x35, 0x52, 0x20, 0xb5, 0xf5, 0xdb, 0x0a, 0xc0,
	0x8c, 0x85, 0x14, 0xa1, 0x84, 0x6c, 0xb2, 0xa8, 0xd7, 0x10, 0x00, 0x91, 0xbd, 0x4b, 0xa4, 0x12,
	0xc0, 0xa4, 0xa0, 0x48, 0x8d, 0x17, 0xf5, 0x5e, 0xde, 0x86, 0xae, 0x14, 0x9d, 0x1d, 0x2b, 0x01,
	0xac, 0x3a, 0x68, 0x4b, 0xf0, 0x91, 0xf0, 0x46, 0x1a, 0xdd, 0xc8, 0x1f, 0xfb, 0x31, 0x29, 0x4e,
	0x79, 0x46, 0xf7, 0x25, 0x02, 0xcd, 0x77, 0x61, 0xd5, 0x09, 0xc3, 0x91, 0xef, 0x62, 0x51, 0x63,
	0x73, 0x37, 0xc8, 0x5c, 0x57, 0x77, 0x06, 0x3f, 0x44, 0xb0, 0xf9, 0x11, 0x5c, 0x4e, 0x97, 0xe4,
	0xb1, 0x13, 0xc5, 0xb6, 0xc8, 0x17, 0xd9, 0x8b, 0x58, 0x8a, 0xff, 0x92, 0x44, 0x1f, 0x22, 0x16,
	0x03, 0xcb, 0x11, 0x7b, 0x11, 0x9b, 0xef, 0xc1, 0x46, 0x3a, 0x8d, 0x4d, 0x3c, 0x65, 0x92, 0x50,
	0x05, 0x53, 0x22, 0xef, 0x4d, 0xbc, 0x6c, 0xca, 0x0d, 0x68, 0xa5, 0x53, 0x3c, 0x67, 0xca, 0x65,
	0x55, 0xdb, 0x94, 0xb0, 0x7d, 0x67, 0xca, 0x33, 0x6b, 0x00, 0xfd, 0xfd, 0x62, 0x56, 0x2f, 0x35,
	0xf5, 0x7a, 0x49, 0x7a, 0x2b, 0xcc, 0x52, 0x5e, 0xaf, 0xb7, 0xca, 0x56, 0x14, 0xf6, 0xfd, 0xf7,
	0x92, 0x28, 0x35, 0x0a, 0x03, 0xd5, 0x7f, 0xaa, 0xd4, 0xc8, 0x55, 0x13, 0xb5, 0x7c, 0x35, 0xa1,
	0xc6, 0xff, 0xba, 0xfe, 0x80, 0x7e, 0x9b, 0x8c, 0xee, 0x31, 0xd6, 0x67, 0xc4, 0x36, 0x91, 0xcb,
	0x6d, 0x42, 0x6d, 0x1c, 0x1c, 0xfb, 0xa3, 0xd4, 0x6f, 0xc9, 0x91, 0xe5, 0x13, 0x93, 0x67, 0xd4,
	0x17, 0xec, 0xfa, 0xe4, 0x53, 0xa6, 0xca, 0x7c, 0xca, 0x64, 0x7d, 0x06, 0x2d, 0x1c, 0x88, 0x06,
	0x1a, 0x7b, 0x86, 0xae, 0x9d, 0x66, 0x64, 0x0c, 0xaf, 0xe1, 0x50, 0xb4, 0x41, 0xb8, 0x1b, 0x8d,
	0x95, 0x6a, 0x13, 0x87, 0x07, 0x9e, 0xb5, 0x0b, 0x6b, 0xd9, 0x7f, 0x01, 0xd9, 0x32, 0xfa, 0x8f,
	0x01, 0x15, 0x95, 0x15, 0xae, 0xb6, 0x23, 0x7f, 0xc5, 0x5b, 0xdd, 0xd4, 0x7a, 0x59, 0x6b, 0x69,
	0x68, 0xa0, 0xd5, 0x94, 0xbc, 0x79, 0x02, 0x66, 0xfe, 0x50, 0x3c, 0x7c, 0xc5, 0xad, 0x6e, 0x69,
	0x5b, 0x6d, 0xa6, 0x5b, 0xcd, 0xd6, 0x54, 0xf6, 0x8b, 0xa1, 0xad, 0x1d, 0x23, 0x9f, 0x2b, 0x95,
	0x8a, 0x72, 0x25, 0xe9, 0xba, 0xe8, 0x48, 0x9a, 0xeb, 0xda, 0xc3, 0x83, 0xe5, 0x5b, 0x91, 0x95,
	0xb9, 0x56, 0xa4, 0xf5, 0xbd, 0xfc, 0x2d, 0x69, 0xeb, 0x73, 0x7e, 0xca, 0xf8, 0x3f, 0xa8, 0xc9,
	0xf6, 0x77, 0x79, 0xf6, 0x78, 0x9c, 0x2d, 0x21, 0x9b, 0xe0, 0x92, 0xc4, 0x7a, 0x0c, 0xdd, 0x1c,
	0x6a, 0xf9, 0xad, 0x96, 0xf7, 0x4f, 0xdf, 0xff, 0x5b, 0x0b, 0x3a, 0xf2, 0x05, 0xf4, 0x90, 0x45,
	0x67, 0x68, 0x77, 0xdf, 0xa5, 0x9e, 0x69, 0xc1, 0xbb, 0xb9, 0x79, 0xad, 0xe0, 0x95, 0x59, 0x29,
	0x6c, 0xfb, 0xd7, 0x17, 0xe2, 0xa5, 0xcd, 0x3c, 0x82, 0xde, 0xfc, 0xd2, 0xe2, 0x19, 0xdc, 0x24,
	0xed, 0xd1, 0xfe, 0xc7, 0xe9, 0xdf, 0x28, 0x58, 0x2f, 0xf7, 0x68, 0x7e, 0x1f, 0xcc, 0xcf, 0x3d,
	0x2f, 0xff, 0x7f, 0xc9, 0xd5, 0x82, 0x89, 0xb3, 0x47, 0xef, 0x3e, 0x15, 0x17, 0xb9, 0x5f, 0x87,
	0x1e, 0xc0, 0x46, 0xf6, 0x54, 0xa1, 0xad, 0xb5, 0x5d, 0xb0, 0x96, 0xf6, 0x48, 0x5c, 0xb8, 0xdc,
	0xa7, 0xd0, 0xd6, 0x5e, 0x60, 0xcc, 0xf5, 0xb4, 0x2f, 0xa7, 0xfc, 0x6a, 0xd3, 0xbf, 0x92, 0x02,
	0xe7, 0x5f, 0x6a, 0x3e, 0x86, 0x96, 0xfa, 0x56, 0x54, 0xc4, 0x9e, 0x1e, 0xb9, 0xea, 0xa2, 0x07,
	0xa5, 0x6f, 0xc2, 0xe5, 0x05, 0xef, 0x2f, 0xa6, 0xfe, 0xc6, 0x52, 0x78, 0xf6, 0x2f, 0xa8, 0x92,
	0xd6, 0x9a, 0x81, 0x7d, 0xb9, 0x55, 0x41, 0x53, 0xac, 0xbf, 0x55, 0x88, 0xcb, 0xe4, 0xb3, 0x36,
	0xd7, 0x95, 0x2d, 0xe6, 0xc5, 0xd5, 0xa2, 0x86, 0xea, 0xac, 0x53, 0xfa, 0xfd, 0x25, 0xed, 0xca,
	0xc2, 0x35, 0xdf, 0x5d, 0xd6, 0x0c, 0x9d, 0xad, 0x7f, 0x0f, 0x56, 0x0f, 0x9d, 0x33, 0x7d, 0x4d,
	0x3a, 0xd2, 0xc2, 0x6e, 0x60, 0x21, 0xef, 0x3e, 0x86, 0xa6, 0xd2, 0x02, 0x11, 0xa7, 0xca, 0xf5,
	0x7f, 0xfa, 0x97, 0x25, 0xc3, 0xe6, 0x1a, 0x25, 0xef, 0x41, 0x5d, 0x82, 0x8b, 0xc4, 0xbd, 0xae,
	0x4c, 0xcb, 0xa6, 0xdc, 0x86, 0x06, 0x1e, 0x9b, 0xe6, 0x68, 0x2d, 0x8b, 0xc2, 0xd3, 0x7d, 0x00,
	0xb0, 0xcf, 0x46, 0x2c, 0x66, 0x8b, 0xf6, 0x58, 0x30, 0xe9, 0x71, 0xb8, 0x1f, 0x3c, 0x9f, 0xbc,
	0xca, 0xa4, 0x6f, 0x40, 0x7b, 0xef, 0x29, 0x73, 0x4f, 0xb3, 0x6c, 0x5b, 0x10, 0x69, 0xdd, 0x93,
	0x3e, 0xf5, 0xd0, 0xe7, 0x7e, 0x0c, 0x14, 0x2c, 0xcc, 0xfa, 0x25, 0xeb, 0xe9, 0x86, 0xca, 0xaf,
	0x75, 0x19, 0x0b, 0xe7, 0x0a, 0xb5, 0xbb, 0xd0, 0xd6, 0x6a, 0x1e, 0x73, 0x73, 0x57, 0xfc, 0x98,
	0xb8, 0x9b, 0xfe, 0x98, 0xb8, 0x7b, 0x6f, 0x1c, 0xc6, 0x53, 0x61, 0x7a, 0xc5, 0xe5, 0xd1, 0x5d,
	0x6a, 0x74, 0xc9, 0x7c, 0x98, 0x16, 0xe9, 0xa9, 0x67, 0x50, 0xff, 0xb9, 0xeb, 0x6f, 0xca, 0x65,
	0xf2, 0x19, 0xfc, 0x67, 0x99, 0x1e, 0xd0, 0x25, 0x16, 0x2f, 0xa0, 0x2a, 0x83, 0x76, 0x93, 0x4f,
	0x68, 0x85, 0x34, 0xed, 0x30, 0xd3, 0x8d, 0x72, 0x59, 0x4b, 0x36, 0x7f, 0x2e, 0x3f, 0xb9, 0x03,
	0x46, 0x16, 0x04, 0xcd, 0x55, 0x2d, 0x34, 0x0f, 0xd8, 0xb3, 0x7e, 0x1e, 0xc2, 0xcd, 0x4f, 0xa1,
	0xa3, 0xc7, 0x2f, 0x73, 0x63, 0x3e, 0xca, 0xe2, 0xd4, 0xcd, 0x22, 0x30, 0x0f, 0x8f, 0x6b, 0xc4,
	0xe2, 0x0f, 0xfe, 0x15, 0x00, 0x00, 0xff, 0xff, 0x8f, 0xc7, 0x64, 0x35, 0x22, 0x2a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VipCardServiceClient is the client API for VipCardService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VipCardServiceClient interface {
	// 获取会员卡模版列表
	GetVipCardTemplateList(ctx context.Context, in *VipCardTemplateListRequest, opts ...grpc.CallOption) (*VipCardTemplateListResponse, error)
	//获取指定会员卡模版详情
	GetVipCardTemplateDetail(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*VipCardTemplateDetailResponse, error)
	//添加会员卡模版
	AddVipCardTemplate(ctx context.Context, in *VipCardTemplateAddRequest, opts ...grpc.CallOption) (*VcBaseResponse, error)
	//更新会员卡模版
	UpdateVipCardTemplate(ctx context.Context, in *VipCardTemplateUpdateRequest, opts ...grpc.CallOption) (*VcBaseResponse, error)
	//付费会员卡权益列表
	ListVipEquity(ctx context.Context, in *ListPageRequest, opts ...grpc.CallOption) (*ListVipEquityResponse, error)
	//付费会员卡权益详情
	GetVipEquity(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*GetVipEquityResponse, error)
	//付费会员卡权益新增/更新
	CreateOrUpdateVipEquity(ctx context.Context, in *VipEquity, opts ...grpc.CallOption) (*VcBaseResponse, error)
	//权益配置详情
	GetEquityConfig(ctx context.Context, in *GetEquityConfigRequest, opts ...grpc.CallOption) (*GetEquityConfigResponse, error)
	//权益配置列表
	ListEquityConfigs(ctx context.Context, in *ListPageRequest, opts ...grpc.CallOption) (*ListEquityConfigsResponse, error)
	//权益配置列表
	ListNoRefundableEquityConfig(ctx context.Context, in *ListPageRequest, opts ...grpc.CallOption) (*ListNoRefundableEquityConfigsResponse, error)
	//权益配置新增/更新
	SaveEquityConfig(ctx context.Context, in *CreateEquityConfigRequest, opts ...grpc.CallOption) (*VcBaseResponse, error)
	//开卡礼包
	GetGiftList(ctx context.Context, in *GiftListRequest, opts ...grpc.CallOption) (*GetGiftListResponse, error)
	GetGift(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*GetGiftResponse, error)
	SaveGift(ctx context.Context, in *GiftData, opts ...grpc.CallOption) (*VcBaseResponse, error)
	DeleteGift(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*VcBaseResponse, error)
	UpDownGift(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*VcBaseResponse, error)
	//优惠券检查
	CheckVouchers(ctx context.Context, in *VoucherRequest, opts ...grpc.CallOption) (*DataBaseResponse, error)
	//会员中心权益信息
	GetCardInfo(ctx context.Context, in *BaseCardRequest, opts ...grpc.CallOption) (*GetCardInfoResponse, error)
	// 会员中心权益内容信息
	GetEquityList(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*GetEquityListResponse, error)
	//我的福利
	GetWelfareList(ctx context.Context, in *BaseCardOrderRequest, opts ...grpc.CallOption) (*GetWelfareResponse, error)
	//礼包已领取
	GetGiftInfo(ctx context.Context, in *BaseCardOrderRequest, opts ...grpc.CallOption) (*GetGiftInfoResponse, error)
	//合并手机号请求
	GetUserInfo(ctx context.Context, in *GetUserInfoRequest, opts ...grpc.CallOption) (*GetUserInfoResponse, error)
	// 查询卡权益价值
	CardValue(ctx context.Context, in *CardValueReq, opts ...grpc.CallOption) (*CardValueRes, error)
	// 查询会员卡下已使用子龙门店券的权益赠送价值
	CardOrderValue(ctx context.Context, in *CardOrderValueReq, opts ...grpc.CallOption) (*CardOrderValueResp, error)
}

type vipCardServiceClient struct {
	cc *grpc.ClientConn
}

func NewVipCardServiceClient(cc *grpc.ClientConn) VipCardServiceClient {
	return &vipCardServiceClient{cc}
}

func (c *vipCardServiceClient) GetVipCardTemplateList(ctx context.Context, in *VipCardTemplateListRequest, opts ...grpc.CallOption) (*VipCardTemplateListResponse, error) {
	out := new(VipCardTemplateListResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetVipCardTemplateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetVipCardTemplateDetail(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*VipCardTemplateDetailResponse, error) {
	out := new(VipCardTemplateDetailResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetVipCardTemplateDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) AddVipCardTemplate(ctx context.Context, in *VipCardTemplateAddRequest, opts ...grpc.CallOption) (*VcBaseResponse, error) {
	out := new(VcBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/AddVipCardTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) UpdateVipCardTemplate(ctx context.Context, in *VipCardTemplateUpdateRequest, opts ...grpc.CallOption) (*VcBaseResponse, error) {
	out := new(VcBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/UpdateVipCardTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) ListVipEquity(ctx context.Context, in *ListPageRequest, opts ...grpc.CallOption) (*ListVipEquityResponse, error) {
	out := new(ListVipEquityResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/ListVipEquity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetVipEquity(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*GetVipEquityResponse, error) {
	out := new(GetVipEquityResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetVipEquity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) CreateOrUpdateVipEquity(ctx context.Context, in *VipEquity, opts ...grpc.CallOption) (*VcBaseResponse, error) {
	out := new(VcBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/CreateOrUpdateVipEquity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetEquityConfig(ctx context.Context, in *GetEquityConfigRequest, opts ...grpc.CallOption) (*GetEquityConfigResponse, error) {
	out := new(GetEquityConfigResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetEquityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) ListEquityConfigs(ctx context.Context, in *ListPageRequest, opts ...grpc.CallOption) (*ListEquityConfigsResponse, error) {
	out := new(ListEquityConfigsResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/ListEquityConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) ListNoRefundableEquityConfig(ctx context.Context, in *ListPageRequest, opts ...grpc.CallOption) (*ListNoRefundableEquityConfigsResponse, error) {
	out := new(ListNoRefundableEquityConfigsResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/ListNoRefundableEquityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) SaveEquityConfig(ctx context.Context, in *CreateEquityConfigRequest, opts ...grpc.CallOption) (*VcBaseResponse, error) {
	out := new(VcBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/SaveEquityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetGiftList(ctx context.Context, in *GiftListRequest, opts ...grpc.CallOption) (*GetGiftListResponse, error) {
	out := new(GetGiftListResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetGiftList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetGift(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*GetGiftResponse, error) {
	out := new(GetGiftResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) SaveGift(ctx context.Context, in *GiftData, opts ...grpc.CallOption) (*VcBaseResponse, error) {
	out := new(VcBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/SaveGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) DeleteGift(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*VcBaseResponse, error) {
	out := new(VcBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/DeleteGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) UpDownGift(ctx context.Context, in *BaseIdRequest, opts ...grpc.CallOption) (*VcBaseResponse, error) {
	out := new(VcBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/UpDownGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) CheckVouchers(ctx context.Context, in *VoucherRequest, opts ...grpc.CallOption) (*DataBaseResponse, error) {
	out := new(DataBaseResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/CheckVouchers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetCardInfo(ctx context.Context, in *BaseCardRequest, opts ...grpc.CallOption) (*GetCardInfoResponse, error) {
	out := new(GetCardInfoResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetEquityList(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*GetEquityListResponse, error) {
	out := new(GetEquityListResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetEquityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetWelfareList(ctx context.Context, in *BaseCardOrderRequest, opts ...grpc.CallOption) (*GetWelfareResponse, error) {
	out := new(GetWelfareResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetWelfareList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetGiftInfo(ctx context.Context, in *BaseCardOrderRequest, opts ...grpc.CallOption) (*GetGiftInfoResponse, error) {
	out := new(GetGiftInfoResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetGiftInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) GetUserInfo(ctx context.Context, in *GetUserInfoRequest, opts ...grpc.CallOption) (*GetUserInfoResponse, error) {
	out := new(GetUserInfoResponse)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/GetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) CardValue(ctx context.Context, in *CardValueReq, opts ...grpc.CallOption) (*CardValueRes, error) {
	out := new(CardValueRes)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/CardValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vipCardServiceClient) CardOrderValue(ctx context.Context, in *CardOrderValueReq, opts ...grpc.CallOption) (*CardOrderValueResp, error) {
	out := new(CardOrderValueResp)
	err := c.cc.Invoke(ctx, "/cc.VipCardService/CardOrderValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VipCardServiceServer is the server API for VipCardService service.
type VipCardServiceServer interface {
	// 获取会员卡模版列表
	GetVipCardTemplateList(context.Context, *VipCardTemplateListRequest) (*VipCardTemplateListResponse, error)
	//获取指定会员卡模版详情
	GetVipCardTemplateDetail(context.Context, *BaseIdRequest) (*VipCardTemplateDetailResponse, error)
	//添加会员卡模版
	AddVipCardTemplate(context.Context, *VipCardTemplateAddRequest) (*VcBaseResponse, error)
	//更新会员卡模版
	UpdateVipCardTemplate(context.Context, *VipCardTemplateUpdateRequest) (*VcBaseResponse, error)
	//付费会员卡权益列表
	ListVipEquity(context.Context, *ListPageRequest) (*ListVipEquityResponse, error)
	//付费会员卡权益详情
	GetVipEquity(context.Context, *BaseIdRequest) (*GetVipEquityResponse, error)
	//付费会员卡权益新增/更新
	CreateOrUpdateVipEquity(context.Context, *VipEquity) (*VcBaseResponse, error)
	//权益配置详情
	GetEquityConfig(context.Context, *GetEquityConfigRequest) (*GetEquityConfigResponse, error)
	//权益配置列表
	ListEquityConfigs(context.Context, *ListPageRequest) (*ListEquityConfigsResponse, error)
	//权益配置列表
	ListNoRefundableEquityConfig(context.Context, *ListPageRequest) (*ListNoRefundableEquityConfigsResponse, error)
	//权益配置新增/更新
	SaveEquityConfig(context.Context, *CreateEquityConfigRequest) (*VcBaseResponse, error)
	//开卡礼包
	GetGiftList(context.Context, *GiftListRequest) (*GetGiftListResponse, error)
	GetGift(context.Context, *BaseIdRequest) (*GetGiftResponse, error)
	SaveGift(context.Context, *GiftData) (*VcBaseResponse, error)
	DeleteGift(context.Context, *BaseIdRequest) (*VcBaseResponse, error)
	UpDownGift(context.Context, *BaseIdRequest) (*VcBaseResponse, error)
	//优惠券检查
	CheckVouchers(context.Context, *VoucherRequest) (*DataBaseResponse, error)
	//会员中心权益信息
	GetCardInfo(context.Context, *BaseCardRequest) (*GetCardInfoResponse, error)
	// 会员中心权益内容信息
	GetEquityList(context.Context, *empty.Empty) (*GetEquityListResponse, error)
	//我的福利
	GetWelfareList(context.Context, *BaseCardOrderRequest) (*GetWelfareResponse, error)
	//礼包已领取
	GetGiftInfo(context.Context, *BaseCardOrderRequest) (*GetGiftInfoResponse, error)
	//合并手机号请求
	GetUserInfo(context.Context, *GetUserInfoRequest) (*GetUserInfoResponse, error)
	// 查询卡权益价值
	CardValue(context.Context, *CardValueReq) (*CardValueRes, error)
	// 查询会员卡下已使用子龙门店券的权益赠送价值
	CardOrderValue(context.Context, *CardOrderValueReq) (*CardOrderValueResp, error)
}

// UnimplementedVipCardServiceServer can be embedded to have forward compatible implementations.
type UnimplementedVipCardServiceServer struct {
}

func (*UnimplementedVipCardServiceServer) GetVipCardTemplateList(ctx context.Context, req *VipCardTemplateListRequest) (*VipCardTemplateListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVipCardTemplateList not implemented")
}
func (*UnimplementedVipCardServiceServer) GetVipCardTemplateDetail(ctx context.Context, req *BaseIdRequest) (*VipCardTemplateDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVipCardTemplateDetail not implemented")
}
func (*UnimplementedVipCardServiceServer) AddVipCardTemplate(ctx context.Context, req *VipCardTemplateAddRequest) (*VcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddVipCardTemplate not implemented")
}
func (*UnimplementedVipCardServiceServer) UpdateVipCardTemplate(ctx context.Context, req *VipCardTemplateUpdateRequest) (*VcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVipCardTemplate not implemented")
}
func (*UnimplementedVipCardServiceServer) ListVipEquity(ctx context.Context, req *ListPageRequest) (*ListVipEquityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVipEquity not implemented")
}
func (*UnimplementedVipCardServiceServer) GetVipEquity(ctx context.Context, req *BaseIdRequest) (*GetVipEquityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVipEquity not implemented")
}
func (*UnimplementedVipCardServiceServer) CreateOrUpdateVipEquity(ctx context.Context, req *VipEquity) (*VcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateVipEquity not implemented")
}
func (*UnimplementedVipCardServiceServer) GetEquityConfig(ctx context.Context, req *GetEquityConfigRequest) (*GetEquityConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEquityConfig not implemented")
}
func (*UnimplementedVipCardServiceServer) ListEquityConfigs(ctx context.Context, req *ListPageRequest) (*ListEquityConfigsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEquityConfigs not implemented")
}
func (*UnimplementedVipCardServiceServer) ListNoRefundableEquityConfig(ctx context.Context, req *ListPageRequest) (*ListNoRefundableEquityConfigsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNoRefundableEquityConfig not implemented")
}
func (*UnimplementedVipCardServiceServer) SaveEquityConfig(ctx context.Context, req *CreateEquityConfigRequest) (*VcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveEquityConfig not implemented")
}
func (*UnimplementedVipCardServiceServer) GetGiftList(ctx context.Context, req *GiftListRequest) (*GetGiftListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGiftList not implemented")
}
func (*UnimplementedVipCardServiceServer) GetGift(ctx context.Context, req *BaseIdRequest) (*GetGiftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGift not implemented")
}
func (*UnimplementedVipCardServiceServer) SaveGift(ctx context.Context, req *GiftData) (*VcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveGift not implemented")
}
func (*UnimplementedVipCardServiceServer) DeleteGift(ctx context.Context, req *BaseIdRequest) (*VcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGift not implemented")
}
func (*UnimplementedVipCardServiceServer) UpDownGift(ctx context.Context, req *BaseIdRequest) (*VcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpDownGift not implemented")
}
func (*UnimplementedVipCardServiceServer) CheckVouchers(ctx context.Context, req *VoucherRequest) (*DataBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckVouchers not implemented")
}
func (*UnimplementedVipCardServiceServer) GetCardInfo(ctx context.Context, req *BaseCardRequest) (*GetCardInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardInfo not implemented")
}
func (*UnimplementedVipCardServiceServer) GetEquityList(ctx context.Context, req *empty.Empty) (*GetEquityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEquityList not implemented")
}
func (*UnimplementedVipCardServiceServer) GetWelfareList(ctx context.Context, req *BaseCardOrderRequest) (*GetWelfareResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWelfareList not implemented")
}
func (*UnimplementedVipCardServiceServer) GetGiftInfo(ctx context.Context, req *BaseCardOrderRequest) (*GetGiftInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGiftInfo not implemented")
}
func (*UnimplementedVipCardServiceServer) GetUserInfo(ctx context.Context, req *GetUserInfoRequest) (*GetUserInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (*UnimplementedVipCardServiceServer) CardValue(ctx context.Context, req *CardValueReq) (*CardValueRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CardValue not implemented")
}
func (*UnimplementedVipCardServiceServer) CardOrderValue(ctx context.Context, req *CardOrderValueReq) (*CardOrderValueResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CardOrderValue not implemented")
}

func RegisterVipCardServiceServer(s *grpc.Server, srv VipCardServiceServer) {
	s.RegisterService(&_VipCardService_serviceDesc, srv)
}

func _VipCardService_GetVipCardTemplateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VipCardTemplateListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetVipCardTemplateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetVipCardTemplateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetVipCardTemplateList(ctx, req.(*VipCardTemplateListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetVipCardTemplateDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetVipCardTemplateDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetVipCardTemplateDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetVipCardTemplateDetail(ctx, req.(*BaseIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_AddVipCardTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VipCardTemplateAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).AddVipCardTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/AddVipCardTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).AddVipCardTemplate(ctx, req.(*VipCardTemplateAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_UpdateVipCardTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VipCardTemplateUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).UpdateVipCardTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/UpdateVipCardTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).UpdateVipCardTemplate(ctx, req.(*VipCardTemplateUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_ListVipEquity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).ListVipEquity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/ListVipEquity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).ListVipEquity(ctx, req.(*ListPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetVipEquity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetVipEquity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetVipEquity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetVipEquity(ctx, req.(*BaseIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_CreateOrUpdateVipEquity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VipEquity)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).CreateOrUpdateVipEquity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/CreateOrUpdateVipEquity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).CreateOrUpdateVipEquity(ctx, req.(*VipEquity))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetEquityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEquityConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetEquityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetEquityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetEquityConfig(ctx, req.(*GetEquityConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_ListEquityConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).ListEquityConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/ListEquityConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).ListEquityConfigs(ctx, req.(*ListPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_ListNoRefundableEquityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).ListNoRefundableEquityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/ListNoRefundableEquityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).ListNoRefundableEquityConfig(ctx, req.(*ListPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_SaveEquityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEquityConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).SaveEquityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/SaveEquityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).SaveEquityConfig(ctx, req.(*CreateEquityConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetGiftList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiftListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetGiftList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetGiftList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetGiftList(ctx, req.(*GiftListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetGift(ctx, req.(*BaseIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_SaveGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiftData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).SaveGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/SaveGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).SaveGift(ctx, req.(*GiftData))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_DeleteGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).DeleteGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/DeleteGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).DeleteGift(ctx, req.(*BaseIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_UpDownGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).UpDownGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/UpDownGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).UpDownGift(ctx, req.(*BaseIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_CheckVouchers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VoucherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).CheckVouchers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/CheckVouchers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).CheckVouchers(ctx, req.(*VoucherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetCardInfo(ctx, req.(*BaseCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetEquityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(empty.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetEquityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetEquityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetEquityList(ctx, req.(*empty.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetWelfareList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseCardOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetWelfareList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetWelfareList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetWelfareList(ctx, req.(*BaseCardOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetGiftInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseCardOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetGiftInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetGiftInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetGiftInfo(ctx, req.(*BaseCardOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/GetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).GetUserInfo(ctx, req.(*GetUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_CardValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).CardValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/CardValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).CardValue(ctx, req.(*CardValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VipCardService_CardOrderValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CardOrderValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VipCardServiceServer).CardOrderValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.VipCardService/CardOrderValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VipCardServiceServer).CardOrderValue(ctx, req.(*CardOrderValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _VipCardService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.VipCardService",
	HandlerType: (*VipCardServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVipCardTemplateList",
			Handler:    _VipCardService_GetVipCardTemplateList_Handler,
		},
		{
			MethodName: "GetVipCardTemplateDetail",
			Handler:    _VipCardService_GetVipCardTemplateDetail_Handler,
		},
		{
			MethodName: "AddVipCardTemplate",
			Handler:    _VipCardService_AddVipCardTemplate_Handler,
		},
		{
			MethodName: "UpdateVipCardTemplate",
			Handler:    _VipCardService_UpdateVipCardTemplate_Handler,
		},
		{
			MethodName: "ListVipEquity",
			Handler:    _VipCardService_ListVipEquity_Handler,
		},
		{
			MethodName: "GetVipEquity",
			Handler:    _VipCardService_GetVipEquity_Handler,
		},
		{
			MethodName: "CreateOrUpdateVipEquity",
			Handler:    _VipCardService_CreateOrUpdateVipEquity_Handler,
		},
		{
			MethodName: "GetEquityConfig",
			Handler:    _VipCardService_GetEquityConfig_Handler,
		},
		{
			MethodName: "ListEquityConfigs",
			Handler:    _VipCardService_ListEquityConfigs_Handler,
		},
		{
			MethodName: "ListNoRefundableEquityConfig",
			Handler:    _VipCardService_ListNoRefundableEquityConfig_Handler,
		},
		{
			MethodName: "SaveEquityConfig",
			Handler:    _VipCardService_SaveEquityConfig_Handler,
		},
		{
			MethodName: "GetGiftList",
			Handler:    _VipCardService_GetGiftList_Handler,
		},
		{
			MethodName: "GetGift",
			Handler:    _VipCardService_GetGift_Handler,
		},
		{
			MethodName: "SaveGift",
			Handler:    _VipCardService_SaveGift_Handler,
		},
		{
			MethodName: "DeleteGift",
			Handler:    _VipCardService_DeleteGift_Handler,
		},
		{
			MethodName: "UpDownGift",
			Handler:    _VipCardService_UpDownGift_Handler,
		},
		{
			MethodName: "CheckVouchers",
			Handler:    _VipCardService_CheckVouchers_Handler,
		},
		{
			MethodName: "GetCardInfo",
			Handler:    _VipCardService_GetCardInfo_Handler,
		},
		{
			MethodName: "GetEquityList",
			Handler:    _VipCardService_GetEquityList_Handler,
		},
		{
			MethodName: "GetWelfareList",
			Handler:    _VipCardService_GetWelfareList_Handler,
		},
		{
			MethodName: "GetGiftInfo",
			Handler:    _VipCardService_GetGiftInfo_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _VipCardService_GetUserInfo_Handler,
		},
		{
			MethodName: "CardValue",
			Handler:    _VipCardService_CardValue_Handler,
		},
		{
			MethodName: "CardOrderValue",
			Handler:    _VipCardService_CardOrderValue_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/vip_card.proto",
}
