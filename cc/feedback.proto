syntax = "proto3";
package cc;

message FeedBaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

//用户反馈相关服务
service FeedbackService {
  //添加用户反馈
  rpc AddFeedback (AddFeedbackRequest) returns (FeedBaseResponse);
  //获取用户反馈详情
  rpc GetFeedback (GetFeedbackRequest) returns (GetFeedbackResponse);
  //获取用户反馈列表
  rpc GetFeedbackList (GetFeedbackListRequest) returns (GetFeedbackListResponse);
  //添加用户反馈回复
  rpc AddFeedbackComment (AddFeedbackCommentRequest) returns (FeedBaseResponse);
  //用户反馈回复列表
  rpc GetFeedbackCommentList (GetFeedbackCommentListRequest) returns (GetFeedbackCommentListResponse);
}

//添加反馈请求参数
message AddFeedbackRequest  {
  // 反馈内容 必传 不能为空 最长不超过200字
  string content = 1;
  //反馈类型 0：建议  1咨询 2投诉 不传则默认为0
  int32 type = 2;
  //电话
  string phone = 3;
  //是否是草稿 0 不是 1 是 不传则默认为0
  int32 is_draft = 4;
  //反馈的图片 多张使用英文逗号分隔
  string images = 5;
  //客户端信息
  string client_info = 6;
  //用户id
  string user_id = 7;
  //用户名称
  string user_name = 8;
  //小程序主体：1-默认，2-极宠家
  int32 org_id = 9;
}

//反馈回复请求参数
message AddFeedbackCommentRequest {
  // 回复内容 必传 不能为空 最长不超过200字
  string content = 1;
  //所属反馈id
  int32 feedback_id = 2;
  //用户id
  string user_id = 3;
  //用户id
  string user_name = 4;
  //是否是官方 回复 1是 0否
  int32 is_official = 5;
}

//获取反馈详情请求参数
message GetFeedbackRequest {
  //反馈反馈ID号
  int32 id = 1;
  //用户id
  string user_id = 2;
}

//获取反馈详情请求参数
message GetFeedbackResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  Feedback Data = 4;
}

//获取反馈详情返回参数
message Feedback {
  //反馈id
  int32 id = 1;
  // 反馈内容
  string content = 2;
  //反馈类型 0：建议  1咨询 2投诉
  int32 type = 3;
  //电话
  string phone = 4;
  //反馈的图片 多张使用英文逗号分隔
  string images = 5;
  //是否是草稿 0 不是 1 是
  int32 is_draft = 6;
  //用户id
  string user_id = 7;
  //用户名称
  string user_name = 8;
  //客户端信息
  string client_info = 9;
  //创建时间
  string create_time = 10;
}

//获取反馈列表请求参数
message GetFeedbackListRequest {
  //当前多少页 从1开始 不传默认为 1
  int32 pageIndex = 1;
  //每页多少条数据 不传默认为 15
  int32 pageSize = 2;
  //用户手机号
  string phone = 3;
  //用户id
  string user_id = 4;
  //创建时间区间
  string create_time = 5;
  //反馈内容 模糊搜索
  string content = 6;
  //查询时间节点，进查询该时间之后的数据
  string start_time = 7;
  //反馈类型 0：建议  1咨询 2投诉 不传则默认为0
  int32 type = 8;
  //小程序主体：1-默认，2-极宠家
  int32 org_id = 9;
}

//获取反馈列表返回参数
message GetFeedbackListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //总条数
  int32 total = 4;
  //反馈列表
  repeated Feedback data = 5;
}

//获取反馈列表请求参数
message GetFeedbackCommentListRequest {
  //当前多少页 从1开始 不传默认为 1
  int32 pageIndex = 1;
  //每页多少条数据 不传默认为 15
  int32 pageSize = 2;
  //所属反馈id
  int32 feedback_id = 3;
}

//获取反馈列表返回参数
message GetFeedbackCommentListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //总条数
  int32 total = 4;
  //反馈列表
  repeated FeedbackComment data = 5;
}

//获取反馈详情返回参数
message FeedbackComment {
  // 反馈内容
  string content = 1;
  //是否是阿闻官方回复 1是 0 否
  string user_id = 2;
  //用户昵称
  string user_name = 3;
  //回复时间
  string create_time = 4;
  //是否是官方 回复 1是 0否
  int32 is_official = 5;
}


