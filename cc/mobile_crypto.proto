syntax = "proto3";

package cc;

option go_package = "_/proto/cc";

// 手机号加密解密服务
service MobileCryptoService {
    // 手机号解密
    rpc MobileDecrypt(MobileDecryptRequest) returns (MobileDecryptResponse);
    
    // 手机号加密
    rpc MobileEncrypt(MobileEncryptRequest) returns (MobileEncryptResponse);
}

// 手机号解密请求
message MobileDecryptRequest {
    string ciphertext = 1; // 加密的手机号
}

// 手机号解密响应
message MobileDecryptResponse {
    string mobile = 1; // 解密后的手机号
    string error = 2;  // 错误信息
}

// 手机号加密请求
message MobileEncryptRequest {
    string mobile = 1; // 明文手机号
}

// 手机号加密响应
message MobileEncryptResponse {
    string ciphertext = 1; // 加密后的手机号
    string error = 2;      // 错误信息
}
