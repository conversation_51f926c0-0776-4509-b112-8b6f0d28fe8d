// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/pet_tips.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 数据传输对象
type PetTipsDto struct {
	// Id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 主图
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	// 阅读量
	Reading int32 `protobuf:"varint,10,opt,name=reading,proto3" json:"reading"`
	// 详细内容
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content"`
	// 创建日期
	CreateAt string `protobuf:"bytes,5,opt,name=createAt,proto3" json:"createAt"`
	// 创建人
	CreateBy string `protobuf:"bytes,6,opt,name=createBy,proto3" json:"createBy"`
	// 更新日期
	UpdateAt string `protobuf:"bytes,7,opt,name=updateAt,proto3" json:"updateAt"`
	// 更新人
	UpdateBy string `protobuf:"bytes,8,opt,name=updateBy,proto3" json:"updateBy"`
	// 标签信息
	Tags []*PetTipsTagDto `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags"`
	//展示阅读量，虚拟的随机生成
	VirtualReading       int32    `protobuf:"varint,11,opt,name=virtualReading,proto3" json:"virtualReading"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTipsDto) Reset()         { *m = PetTipsDto{} }
func (m *PetTipsDto) String() string { return proto.CompactTextString(m) }
func (*PetTipsDto) ProtoMessage()    {}
func (*PetTipsDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{0}
}

func (m *PetTipsDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsDto.Unmarshal(m, b)
}
func (m *PetTipsDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsDto.Marshal(b, m, deterministic)
}
func (m *PetTipsDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsDto.Merge(m, src)
}
func (m *PetTipsDto) XXX_Size() int {
	return xxx_messageInfo_PetTipsDto.Size(m)
}
func (m *PetTipsDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsDto.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsDto proto.InternalMessageInfo

func (m *PetTipsDto) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetTipsDto) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PetTipsDto) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *PetTipsDto) GetReading() int32 {
	if m != nil {
		return m.Reading
	}
	return 0
}

func (m *PetTipsDto) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PetTipsDto) GetCreateAt() string {
	if m != nil {
		return m.CreateAt
	}
	return ""
}

func (m *PetTipsDto) GetCreateBy() string {
	if m != nil {
		return m.CreateBy
	}
	return ""
}

func (m *PetTipsDto) GetUpdateAt() string {
	if m != nil {
		return m.UpdateAt
	}
	return ""
}

func (m *PetTipsDto) GetUpdateBy() string {
	if m != nil {
		return m.UpdateBy
	}
	return ""
}

func (m *PetTipsDto) GetTags() []*PetTipsTagDto {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *PetTipsDto) GetVirtualReading() int32 {
	if m != nil {
		return m.VirtualReading
	}
	return 0
}

// 标签
type PetTipsTagDto struct {
	// 名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 值
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTipsTagDto) Reset()         { *m = PetTipsTagDto{} }
func (m *PetTipsTagDto) String() string { return proto.CompactTextString(m) }
func (*PetTipsTagDto) ProtoMessage()    {}
func (*PetTipsTagDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{1}
}

func (m *PetTipsTagDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsTagDto.Unmarshal(m, b)
}
func (m *PetTipsTagDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsTagDto.Marshal(b, m, deterministic)
}
func (m *PetTipsTagDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsTagDto.Merge(m, src)
}
func (m *PetTipsTagDto) XXX_Size() int {
	return xxx_messageInfo_PetTipsTagDto.Size(m)
}
func (m *PetTipsTagDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsTagDto.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsTagDto proto.InternalMessageInfo

func (m *PetTipsTagDto) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PetTipsTagDto) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

// 新增或修改宠物贴士
type PetTipsEditRequest struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 主图
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	//内容
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content"`
	//操作用户代码
	UserNo string `protobuf:"bytes,5,opt,name=userNo,proto3" json:"userNo"`
	// 标签信息
	Tags                 []*PetTipsTagDto `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PetTipsEditRequest) Reset()         { *m = PetTipsEditRequest{} }
func (m *PetTipsEditRequest) String() string { return proto.CompactTextString(m) }
func (*PetTipsEditRequest) ProtoMessage()    {}
func (*PetTipsEditRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{2}
}

func (m *PetTipsEditRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsEditRequest.Unmarshal(m, b)
}
func (m *PetTipsEditRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsEditRequest.Marshal(b, m, deterministic)
}
func (m *PetTipsEditRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsEditRequest.Merge(m, src)
}
func (m *PetTipsEditRequest) XXX_Size() int {
	return xxx_messageInfo_PetTipsEditRequest.Size(m)
}
func (m *PetTipsEditRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsEditRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsEditRequest proto.InternalMessageInfo

func (m *PetTipsEditRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetTipsEditRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PetTipsEditRequest) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *PetTipsEditRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PetTipsEditRequest) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *PetTipsEditRequest) GetTags() []*PetTipsTagDto {
	if m != nil {
		return m.Tags
	}
	return nil
}

// 删除
type PetTipsDeleteRequest struct {
	//单个id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// ids 删除多个，逗号分割id列表
	Ids                  string   `protobuf:"bytes,2,opt,name=ids,proto3" json:"ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTipsDeleteRequest) Reset()         { *m = PetTipsDeleteRequest{} }
func (m *PetTipsDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*PetTipsDeleteRequest) ProtoMessage()    {}
func (*PetTipsDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{3}
}

func (m *PetTipsDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsDeleteRequest.Unmarshal(m, b)
}
func (m *PetTipsDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsDeleteRequest.Marshal(b, m, deterministic)
}
func (m *PetTipsDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsDeleteRequest.Merge(m, src)
}
func (m *PetTipsDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_PetTipsDeleteRequest.Size(m)
}
func (m *PetTipsDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsDeleteRequest proto.InternalMessageInfo

func (m *PetTipsDeleteRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetTipsDeleteRequest) GetIds() string {
	if m != nil {
		return m.Ids
	}
	return ""
}

// 新增或修改宠物贴士响应
type PetTipsEditResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTipsEditResponse) Reset()         { *m = PetTipsEditResponse{} }
func (m *PetTipsEditResponse) String() string { return proto.CompactTextString(m) }
func (*PetTipsEditResponse) ProtoMessage()    {}
func (*PetTipsEditResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{4}
}

func (m *PetTipsEditResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsEditResponse.Unmarshal(m, b)
}
func (m *PetTipsEditResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsEditResponse.Marshal(b, m, deterministic)
}
func (m *PetTipsEditResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsEditResponse.Merge(m, src)
}
func (m *PetTipsEditResponse) XXX_Size() int {
	return xxx_messageInfo_PetTipsEditResponse.Size(m)
}
func (m *PetTipsEditResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsEditResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsEditResponse proto.InternalMessageInfo

func (m *PetTipsEditResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetTipsEditResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 查询
type PetTipsQueryRequest struct {
	// 搜索
	SearchKey string `protobuf:"bytes,1,opt,name=searchKey,proto3" json:"searchKey"`
	// id
	Id int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	// 页索引
	PageIndex int32 `protobuf:"varint,3,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize             int32    `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTipsQueryRequest) Reset()         { *m = PetTipsQueryRequest{} }
func (m *PetTipsQueryRequest) String() string { return proto.CompactTextString(m) }
func (*PetTipsQueryRequest) ProtoMessage()    {}
func (*PetTipsQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{5}
}

func (m *PetTipsQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsQueryRequest.Unmarshal(m, b)
}
func (m *PetTipsQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsQueryRequest.Marshal(b, m, deterministic)
}
func (m *PetTipsQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsQueryRequest.Merge(m, src)
}
func (m *PetTipsQueryRequest) XXX_Size() int {
	return xxx_messageInfo_PetTipsQueryRequest.Size(m)
}
func (m *PetTipsQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsQueryRequest proto.InternalMessageInfo

func (m *PetTipsQueryRequest) GetSearchKey() string {
	if m != nil {
		return m.SearchKey
	}
	return ""
}

func (m *PetTipsQueryRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetTipsQueryRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *PetTipsQueryRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 随机查询宠物贴士
type PetTipsRandQueryRequest struct {
	// 随机查询数量
	Size                 int32    `protobuf:"varint,1,opt,name=size,proto3" json:"size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTipsRandQueryRequest) Reset()         { *m = PetTipsRandQueryRequest{} }
func (m *PetTipsRandQueryRequest) String() string { return proto.CompactTextString(m) }
func (*PetTipsRandQueryRequest) ProtoMessage()    {}
func (*PetTipsRandQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{6}
}

func (m *PetTipsRandQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsRandQueryRequest.Unmarshal(m, b)
}
func (m *PetTipsRandQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsRandQueryRequest.Marshal(b, m, deterministic)
}
func (m *PetTipsRandQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsRandQueryRequest.Merge(m, src)
}
func (m *PetTipsRandQueryRequest) XXX_Size() int {
	return xxx_messageInfo_PetTipsRandQueryRequest.Size(m)
}
func (m *PetTipsRandQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsRandQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsRandQueryRequest proto.InternalMessageInfo

func (m *PetTipsRandQueryRequest) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

// 查询响应
type PetTipsQueryResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总记录条数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 分页数据
	Data                 []*PetTipsDto `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PetTipsQueryResponse) Reset()         { *m = PetTipsQueryResponse{} }
func (m *PetTipsQueryResponse) String() string { return proto.CompactTextString(m) }
func (*PetTipsQueryResponse) ProtoMessage()    {}
func (*PetTipsQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{7}
}

func (m *PetTipsQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsQueryResponse.Unmarshal(m, b)
}
func (m *PetTipsQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsQueryResponse.Marshal(b, m, deterministic)
}
func (m *PetTipsQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsQueryResponse.Merge(m, src)
}
func (m *PetTipsQueryResponse) XXX_Size() int {
	return xxx_messageInfo_PetTipsQueryResponse.Size(m)
}
func (m *PetTipsQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsQueryResponse proto.InternalMessageInfo

func (m *PetTipsQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetTipsQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PetTipsQueryResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *PetTipsQueryResponse) GetData() []*PetTipsDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// 查询单个贴士信息
type PetTipsGetRequest struct {
	// id
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTipsGetRequest) Reset()         { *m = PetTipsGetRequest{} }
func (m *PetTipsGetRequest) String() string { return proto.CompactTextString(m) }
func (*PetTipsGetRequest) ProtoMessage()    {}
func (*PetTipsGetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{8}
}

func (m *PetTipsGetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsGetRequest.Unmarshal(m, b)
}
func (m *PetTipsGetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsGetRequest.Marshal(b, m, deterministic)
}
func (m *PetTipsGetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsGetRequest.Merge(m, src)
}
func (m *PetTipsGetRequest) XXX_Size() int {
	return xxx_messageInfo_PetTipsGetRequest.Size(m)
}
func (m *PetTipsGetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsGetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsGetRequest proto.InternalMessageInfo

func (m *PetTipsGetRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 查询单个贴士信息响应
type PetTipsGetResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 单个数据
	Data                 *PetTipsDto `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *PetTipsGetResponse) Reset()         { *m = PetTipsGetResponse{} }
func (m *PetTipsGetResponse) String() string { return proto.CompactTextString(m) }
func (*PetTipsGetResponse) ProtoMessage()    {}
func (*PetTipsGetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{9}
}

func (m *PetTipsGetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipsGetResponse.Unmarshal(m, b)
}
func (m *PetTipsGetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipsGetResponse.Marshal(b, m, deterministic)
}
func (m *PetTipsGetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipsGetResponse.Merge(m, src)
}
func (m *PetTipsGetResponse) XXX_Size() int {
	return xxx_messageInfo_PetTipsGetResponse.Size(m)
}
func (m *PetTipsGetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipsGetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipsGetResponse proto.InternalMessageInfo

func (m *PetTipsGetResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetTipsGetResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PetTipsGetResponse) GetData() *PetTipsDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// scrm 宠物品种查询
type ScrmPetVarietyRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScrmPetVarietyRequest) Reset()         { *m = ScrmPetVarietyRequest{} }
func (m *ScrmPetVarietyRequest) String() string { return proto.CompactTextString(m) }
func (*ScrmPetVarietyRequest) ProtoMessage()    {}
func (*ScrmPetVarietyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{10}
}

func (m *ScrmPetVarietyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmPetVarietyRequest.Unmarshal(m, b)
}
func (m *ScrmPetVarietyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmPetVarietyRequest.Marshal(b, m, deterministic)
}
func (m *ScrmPetVarietyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmPetVarietyRequest.Merge(m, src)
}
func (m *ScrmPetVarietyRequest) XXX_Size() int {
	return xxx_messageInfo_ScrmPetVarietyRequest.Size(m)
}
func (m *ScrmPetVarietyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmPetVarietyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmPetVarietyRequest proto.InternalMessageInfo

func (m *ScrmPetVarietyRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

// scrm 宠物品种dto
type ScrmPetVarietyDto struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Extend               string   `protobuf:"bytes,3,opt,name=extend,proto3" json:"extend"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScrmPetVarietyDto) Reset()         { *m = ScrmPetVarietyDto{} }
func (m *ScrmPetVarietyDto) String() string { return proto.CompactTextString(m) }
func (*ScrmPetVarietyDto) ProtoMessage()    {}
func (*ScrmPetVarietyDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{11}
}

func (m *ScrmPetVarietyDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmPetVarietyDto.Unmarshal(m, b)
}
func (m *ScrmPetVarietyDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmPetVarietyDto.Marshal(b, m, deterministic)
}
func (m *ScrmPetVarietyDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmPetVarietyDto.Merge(m, src)
}
func (m *ScrmPetVarietyDto) XXX_Size() int {
	return xxx_messageInfo_ScrmPetVarietyDto.Size(m)
}
func (m *ScrmPetVarietyDto) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmPetVarietyDto.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmPetVarietyDto proto.InternalMessageInfo

func (m *ScrmPetVarietyDto) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ScrmPetVarietyDto) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ScrmPetVarietyDto) GetExtend() string {
	if m != nil {
		return m.Extend
	}
	return ""
}

// scrm 宠物品种响应
type ScrmPetVarietyResponse struct {
	Code                 int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*ScrmPetVarietyDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ScrmPetVarietyResponse) Reset()         { *m = ScrmPetVarietyResponse{} }
func (m *ScrmPetVarietyResponse) String() string { return proto.CompactTextString(m) }
func (*ScrmPetVarietyResponse) ProtoMessage()    {}
func (*ScrmPetVarietyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{12}
}

func (m *ScrmPetVarietyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmPetVarietyResponse.Unmarshal(m, b)
}
func (m *ScrmPetVarietyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmPetVarietyResponse.Marshal(b, m, deterministic)
}
func (m *ScrmPetVarietyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmPetVarietyResponse.Merge(m, src)
}
func (m *ScrmPetVarietyResponse) XXX_Size() int {
	return xxx_messageInfo_ScrmPetVarietyResponse.Size(m)
}
func (m *ScrmPetVarietyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmPetVarietyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmPetVarietyResponse proto.InternalMessageInfo

func (m *ScrmPetVarietyResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ScrmPetVarietyResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ScrmPetVarietyResponse) GetData() []*ScrmPetVarietyDto {
	if m != nil {
		return m.Data
	}
	return nil
}

//Request
type TipsForCustomizedRequest struct {
	TipIds               []int32  `protobuf:"varint,1,rep,packed,name=tipIds,proto3" json:"tipIds"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TipsForCustomizedRequest) Reset()         { *m = TipsForCustomizedRequest{} }
func (m *TipsForCustomizedRequest) String() string { return proto.CompactTextString(m) }
func (*TipsForCustomizedRequest) ProtoMessage()    {}
func (*TipsForCustomizedRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{13}
}

func (m *TipsForCustomizedRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TipsForCustomizedRequest.Unmarshal(m, b)
}
func (m *TipsForCustomizedRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TipsForCustomizedRequest.Marshal(b, m, deterministic)
}
func (m *TipsForCustomizedRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TipsForCustomizedRequest.Merge(m, src)
}
func (m *TipsForCustomizedRequest) XXX_Size() int {
	return xxx_messageInfo_TipsForCustomizedRequest.Size(m)
}
func (m *TipsForCustomizedRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TipsForCustomizedRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TipsForCustomizedRequest proto.InternalMessageInfo

func (m *TipsForCustomizedRequest) GetTipIds() []int32 {
	if m != nil {
		return m.TipIds
	}
	return nil
}

//Response
type TipsForCustomizedResponse struct {
	Code                 int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*TipsForCustomized `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *TipsForCustomizedResponse) Reset()         { *m = TipsForCustomizedResponse{} }
func (m *TipsForCustomizedResponse) String() string { return proto.CompactTextString(m) }
func (*TipsForCustomizedResponse) ProtoMessage()    {}
func (*TipsForCustomizedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{14}
}

func (m *TipsForCustomizedResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TipsForCustomizedResponse.Unmarshal(m, b)
}
func (m *TipsForCustomizedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TipsForCustomizedResponse.Marshal(b, m, deterministic)
}
func (m *TipsForCustomizedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TipsForCustomizedResponse.Merge(m, src)
}
func (m *TipsForCustomizedResponse) XXX_Size() int {
	return xxx_messageInfo_TipsForCustomizedResponse.Size(m)
}
func (m *TipsForCustomizedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TipsForCustomizedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TipsForCustomizedResponse proto.InternalMessageInfo

func (m *TipsForCustomizedResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TipsForCustomizedResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *TipsForCustomizedResponse) GetData() []*TipsForCustomized {
	if m != nil {
		return m.Data
	}
	return nil
}

type TipsForCustomized struct {
	//主键id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	//橱窗图片
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	//阅读量
	Reading int32 `protobuf:"varint,4,opt,name=reading,proto3" json:"reading"`
	//是否被删除 0 未 1 已删除
	IsDeleted int32 `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted"`
	//创建日期
	CreateAt string `protobuf:"bytes,6,opt,name=create_at,json=createAt,proto3" json:"create_at"`
	//创建人
	CreateBy string `protobuf:"bytes,7,opt,name=create_by,json=createBy,proto3" json:"create_by"`
	//更新日期
	UpdateAt string `protobuf:"bytes,8,opt,name=update_at,json=updateAt,proto3" json:"update_at"`
	//更新人id
	UpdateBy string `protobuf:"bytes,9,opt,name=update_by,json=updateBy,proto3" json:"update_by"`
	//贴士标签
	Tag                  []*TipsTag `protobuf:"bytes,10,rep,name=tag,proto3" json:"tag"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *TipsForCustomized) Reset()         { *m = TipsForCustomized{} }
func (m *TipsForCustomized) String() string { return proto.CompactTextString(m) }
func (*TipsForCustomized) ProtoMessage()    {}
func (*TipsForCustomized) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{15}
}

func (m *TipsForCustomized) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TipsForCustomized.Unmarshal(m, b)
}
func (m *TipsForCustomized) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TipsForCustomized.Marshal(b, m, deterministic)
}
func (m *TipsForCustomized) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TipsForCustomized.Merge(m, src)
}
func (m *TipsForCustomized) XXX_Size() int {
	return xxx_messageInfo_TipsForCustomized.Size(m)
}
func (m *TipsForCustomized) XXX_DiscardUnknown() {
	xxx_messageInfo_TipsForCustomized.DiscardUnknown(m)
}

var xxx_messageInfo_TipsForCustomized proto.InternalMessageInfo

func (m *TipsForCustomized) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TipsForCustomized) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *TipsForCustomized) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *TipsForCustomized) GetReading() int32 {
	if m != nil {
		return m.Reading
	}
	return 0
}

func (m *TipsForCustomized) GetIsDeleted() int32 {
	if m != nil {
		return m.IsDeleted
	}
	return 0
}

func (m *TipsForCustomized) GetCreateAt() string {
	if m != nil {
		return m.CreateAt
	}
	return ""
}

func (m *TipsForCustomized) GetCreateBy() string {
	if m != nil {
		return m.CreateBy
	}
	return ""
}

func (m *TipsForCustomized) GetUpdateAt() string {
	if m != nil {
		return m.UpdateAt
	}
	return ""
}

func (m *TipsForCustomized) GetUpdateBy() string {
	if m != nil {
		return m.UpdateBy
	}
	return ""
}

func (m *TipsForCustomized) GetTag() []*TipsTag {
	if m != nil {
		return m.Tag
	}
	return nil
}

type TipsTag struct {
	//标签名称
	TagName string `protobuf:"bytes,1,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	//标签值
	TagValue             string   `protobuf:"bytes,2,opt,name=tag_value,json=tagValue,proto3" json:"tag_value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TipsTag) Reset()         { *m = TipsTag{} }
func (m *TipsTag) String() string { return proto.CompactTextString(m) }
func (*TipsTag) ProtoMessage()    {}
func (*TipsTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{16}
}

func (m *TipsTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TipsTag.Unmarshal(m, b)
}
func (m *TipsTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TipsTag.Marshal(b, m, deterministic)
}
func (m *TipsTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TipsTag.Merge(m, src)
}
func (m *TipsTag) XXX_Size() int {
	return xxx_messageInfo_TipsTag.Size(m)
}
func (m *TipsTag) XXX_DiscardUnknown() {
	xxx_messageInfo_TipsTag.DiscardUnknown(m)
}

var xxx_messageInfo_TipsTag proto.InternalMessageInfo

func (m *TipsTag) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *TipsTag) GetTagValue() string {
	if m != nil {
		return m.TagValue
	}
	return ""
}

//Request
type QueryBySpeciesRequest struct {
	//物种
	Species string `protobuf:"bytes,1,opt,name=species,proto3" json:"species"`
	// 页索引
	PageIndex int32 `protobuf:"varint,2,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize             int32    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryBySpeciesRequest) Reset()         { *m = QueryBySpeciesRequest{} }
func (m *QueryBySpeciesRequest) String() string { return proto.CompactTextString(m) }
func (*QueryBySpeciesRequest) ProtoMessage()    {}
func (*QueryBySpeciesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{17}
}

func (m *QueryBySpeciesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryBySpeciesRequest.Unmarshal(m, b)
}
func (m *QueryBySpeciesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryBySpeciesRequest.Marshal(b, m, deterministic)
}
func (m *QueryBySpeciesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryBySpeciesRequest.Merge(m, src)
}
func (m *QueryBySpeciesRequest) XXX_Size() int {
	return xxx_messageInfo_QueryBySpeciesRequest.Size(m)
}
func (m *QueryBySpeciesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryBySpeciesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryBySpeciesRequest proto.InternalMessageInfo

func (m *QueryBySpeciesRequest) GetSpecies() string {
	if m != nil {
		return m.Species
	}
	return ""
}

func (m *QueryBySpeciesRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *QueryBySpeciesRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

//Request
type QueryByConditionRequest struct {
	//物种
	Species string `protobuf:"bytes,1,opt,name=species,proto3" json:"species"`
	//年龄
	Age                  string   `protobuf:"bytes,2,opt,name=age,proto3" json:"age"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryByConditionRequest) Reset()         { *m = QueryByConditionRequest{} }
func (m *QueryByConditionRequest) String() string { return proto.CompactTextString(m) }
func (*QueryByConditionRequest) ProtoMessage()    {}
func (*QueryByConditionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{18}
}

func (m *QueryByConditionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryByConditionRequest.Unmarshal(m, b)
}
func (m *QueryByConditionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryByConditionRequest.Marshal(b, m, deterministic)
}
func (m *QueryByConditionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryByConditionRequest.Merge(m, src)
}
func (m *QueryByConditionRequest) XXX_Size() int {
	return xxx_messageInfo_QueryByConditionRequest.Size(m)
}
func (m *QueryByConditionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryByConditionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryByConditionRequest proto.InternalMessageInfo

func (m *QueryByConditionRequest) GetSpecies() string {
	if m != nil {
		return m.Species
	}
	return ""
}

func (m *QueryByConditionRequest) GetAge() string {
	if m != nil {
		return m.Age
	}
	return ""
}

//Response
type QueryByConditionResponse struct {
	Code                 int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*QueryByCondition `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *QueryByConditionResponse) Reset()         { *m = QueryByConditionResponse{} }
func (m *QueryByConditionResponse) String() string { return proto.CompactTextString(m) }
func (*QueryByConditionResponse) ProtoMessage()    {}
func (*QueryByConditionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{19}
}

func (m *QueryByConditionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryByConditionResponse.Unmarshal(m, b)
}
func (m *QueryByConditionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryByConditionResponse.Marshal(b, m, deterministic)
}
func (m *QueryByConditionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryByConditionResponse.Merge(m, src)
}
func (m *QueryByConditionResponse) XXX_Size() int {
	return xxx_messageInfo_QueryByConditionResponse.Size(m)
}
func (m *QueryByConditionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryByConditionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryByConditionResponse proto.InternalMessageInfo

func (m *QueryByConditionResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryByConditionResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryByConditionResponse) GetData() []*QueryByCondition {
	if m != nil {
		return m.Data
	}
	return nil
}

type QueryByCondition struct {
	//主键id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	//橱窗图片
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	//阅读量
	Reading int32 `protobuf:"varint,4,opt,name=reading,proto3" json:"reading"`
	//是否被删除 0 未 1 已删除
	IsDeleted int32 `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted"`
	//创建日期
	CreateAt string `protobuf:"bytes,6,opt,name=create_at,json=createAt,proto3" json:"create_at"`
	//创建人
	CreateBy string `protobuf:"bytes,7,opt,name=create_by,json=createBy,proto3" json:"create_by"`
	//更新日期
	UpdateAt string `protobuf:"bytes,8,opt,name=update_at,json=updateAt,proto3" json:"update_at"`
	//更新人id
	UpdateBy string `protobuf:"bytes,9,opt,name=update_by,json=updateBy,proto3" json:"update_by"`
	//贴士标签
	Tag                  []*TipsTag `protobuf:"bytes,10,rep,name=tag,proto3" json:"tag"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *QueryByCondition) Reset()         { *m = QueryByCondition{} }
func (m *QueryByCondition) String() string { return proto.CompactTextString(m) }
func (*QueryByCondition) ProtoMessage()    {}
func (*QueryByCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{20}
}

func (m *QueryByCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryByCondition.Unmarshal(m, b)
}
func (m *QueryByCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryByCondition.Marshal(b, m, deterministic)
}
func (m *QueryByCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryByCondition.Merge(m, src)
}
func (m *QueryByCondition) XXX_Size() int {
	return xxx_messageInfo_QueryByCondition.Size(m)
}
func (m *QueryByCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryByCondition.DiscardUnknown(m)
}

var xxx_messageInfo_QueryByCondition proto.InternalMessageInfo

func (m *QueryByCondition) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *QueryByCondition) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *QueryByCondition) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *QueryByCondition) GetReading() int32 {
	if m != nil {
		return m.Reading
	}
	return 0
}

func (m *QueryByCondition) GetIsDeleted() int32 {
	if m != nil {
		return m.IsDeleted
	}
	return 0
}

func (m *QueryByCondition) GetCreateAt() string {
	if m != nil {
		return m.CreateAt
	}
	return ""
}

func (m *QueryByCondition) GetCreateBy() string {
	if m != nil {
		return m.CreateBy
	}
	return ""
}

func (m *QueryByCondition) GetUpdateAt() string {
	if m != nil {
		return m.UpdateAt
	}
	return ""
}

func (m *QueryByCondition) GetUpdateBy() string {
	if m != nil {
		return m.UpdateBy
	}
	return ""
}

func (m *QueryByCondition) GetTag() []*TipsTag {
	if m != nil {
		return m.Tag
	}
	return nil
}

//Request
type GetTipsListRequest struct {
	//贴士id
	TipId int32 `protobuf:"varint,1,opt,name=tip_id,json=tipId,proto3" json:"tip_id"`
	// 页索引
	PageIndex int32 `protobuf:"varint,2,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize             int32    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTipsListRequest) Reset()         { *m = GetTipsListRequest{} }
func (m *GetTipsListRequest) String() string { return proto.CompactTextString(m) }
func (*GetTipsListRequest) ProtoMessage()    {}
func (*GetTipsListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{21}
}

func (m *GetTipsListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTipsListRequest.Unmarshal(m, b)
}
func (m *GetTipsListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTipsListRequest.Marshal(b, m, deterministic)
}
func (m *GetTipsListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTipsListRequest.Merge(m, src)
}
func (m *GetTipsListRequest) XXX_Size() int {
	return xxx_messageInfo_GetTipsListRequest.Size(m)
}
func (m *GetTipsListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTipsListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTipsListRequest proto.InternalMessageInfo

func (m *GetTipsListRequest) GetTipId() int32 {
	if m != nil {
		return m.TipId
	}
	return 0
}

func (m *GetTipsListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetTipsListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

//Response
type GetTipsListResponse struct {
	Code                 int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total                int32          `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*GetTipsList `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetTipsListResponse) Reset()         { *m = GetTipsListResponse{} }
func (m *GetTipsListResponse) String() string { return proto.CompactTextString(m) }
func (*GetTipsListResponse) ProtoMessage()    {}
func (*GetTipsListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{22}
}

func (m *GetTipsListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTipsListResponse.Unmarshal(m, b)
}
func (m *GetTipsListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTipsListResponse.Marshal(b, m, deterministic)
}
func (m *GetTipsListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTipsListResponse.Merge(m, src)
}
func (m *GetTipsListResponse) XXX_Size() int {
	return xxx_messageInfo_GetTipsListResponse.Size(m)
}
func (m *GetTipsListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTipsListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTipsListResponse proto.InternalMessageInfo

func (m *GetTipsListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetTipsListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetTipsListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetTipsListResponse) GetData() []*GetTipsList {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetTipsList struct {
	//主键id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	//橱窗图片
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	//阅读量
	Reading int32 `protobuf:"varint,4,opt,name=reading,proto3" json:"reading"`
	//是否被删除 0 未 1 已删除
	IsDeleted int32 `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted"`
	//创建日期
	CreateAt string `protobuf:"bytes,6,opt,name=create_at,json=createAt,proto3" json:"create_at"`
	//创建人
	CreateBy string `protobuf:"bytes,7,opt,name=create_by,json=createBy,proto3" json:"create_by"`
	//更新日期
	UpdateAt string `protobuf:"bytes,8,opt,name=update_at,json=updateAt,proto3" json:"update_at"`
	//更新人id
	UpdateBy string `protobuf:"bytes,9,opt,name=update_by,json=updateBy,proto3" json:"update_by"`
	//贴士标签
	Tag                  []*TipsTag `protobuf:"bytes,10,rep,name=tag,proto3" json:"tag"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTipsList) Reset()         { *m = GetTipsList{} }
func (m *GetTipsList) String() string { return proto.CompactTextString(m) }
func (*GetTipsList) ProtoMessage()    {}
func (*GetTipsList) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{23}
}

func (m *GetTipsList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTipsList.Unmarshal(m, b)
}
func (m *GetTipsList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTipsList.Marshal(b, m, deterministic)
}
func (m *GetTipsList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTipsList.Merge(m, src)
}
func (m *GetTipsList) XXX_Size() int {
	return xxx_messageInfo_GetTipsList.Size(m)
}
func (m *GetTipsList) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTipsList.DiscardUnknown(m)
}

var xxx_messageInfo_GetTipsList proto.InternalMessageInfo

func (m *GetTipsList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetTipsList) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetTipsList) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetTipsList) GetReading() int32 {
	if m != nil {
		return m.Reading
	}
	return 0
}

func (m *GetTipsList) GetIsDeleted() int32 {
	if m != nil {
		return m.IsDeleted
	}
	return 0
}

func (m *GetTipsList) GetCreateAt() string {
	if m != nil {
		return m.CreateAt
	}
	return ""
}

func (m *GetTipsList) GetCreateBy() string {
	if m != nil {
		return m.CreateBy
	}
	return ""
}

func (m *GetTipsList) GetUpdateAt() string {
	if m != nil {
		return m.UpdateAt
	}
	return ""
}

func (m *GetTipsList) GetUpdateBy() string {
	if m != nil {
		return m.UpdateBy
	}
	return ""
}

func (m *GetTipsList) GetTag() []*TipsTag {
	if m != nil {
		return m.Tag
	}
	return nil
}

//Request
type RecommendTipsRequest struct {
	//物种
	Species              string   `protobuf:"bytes,1,opt,name=species,proto3" json:"species"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendTipsRequest) Reset()         { *m = RecommendTipsRequest{} }
func (m *RecommendTipsRequest) String() string { return proto.CompactTextString(m) }
func (*RecommendTipsRequest) ProtoMessage()    {}
func (*RecommendTipsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{24}
}

func (m *RecommendTipsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendTipsRequest.Unmarshal(m, b)
}
func (m *RecommendTipsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendTipsRequest.Marshal(b, m, deterministic)
}
func (m *RecommendTipsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendTipsRequest.Merge(m, src)
}
func (m *RecommendTipsRequest) XXX_Size() int {
	return xxx_messageInfo_RecommendTipsRequest.Size(m)
}
func (m *RecommendTipsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendTipsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendTipsRequest proto.InternalMessageInfo

func (m *RecommendTipsRequest) GetSpecies() string {
	if m != nil {
		return m.Species
	}
	return ""
}

type RecommendTipsResponse struct {
	Code                 int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*RecommendTips `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *RecommendTipsResponse) Reset()         { *m = RecommendTipsResponse{} }
func (m *RecommendTipsResponse) String() string { return proto.CompactTextString(m) }
func (*RecommendTipsResponse) ProtoMessage()    {}
func (*RecommendTipsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{25}
}

func (m *RecommendTipsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendTipsResponse.Unmarshal(m, b)
}
func (m *RecommendTipsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendTipsResponse.Marshal(b, m, deterministic)
}
func (m *RecommendTipsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendTipsResponse.Merge(m, src)
}
func (m *RecommendTipsResponse) XXX_Size() int {
	return xxx_messageInfo_RecommendTipsResponse.Size(m)
}
func (m *RecommendTipsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendTipsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendTipsResponse proto.InternalMessageInfo

func (m *RecommendTipsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RecommendTipsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RecommendTipsResponse) GetData() []*RecommendTips {
	if m != nil {
		return m.Data
	}
	return nil
}

type RecommendTips struct {
	//主键id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	//橱窗图片
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	//阅读量
	Reading int32 `protobuf:"varint,4,opt,name=reading,proto3" json:"reading"`
	//是否被删除 0 未 1 已删除
	IsDeleted int32 `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted"`
	//创建日期
	CreateAt string `protobuf:"bytes,6,opt,name=create_at,json=createAt,proto3" json:"create_at"`
	//创建人
	CreateBy string `protobuf:"bytes,7,opt,name=create_by,json=createBy,proto3" json:"create_by"`
	//更新日期
	UpdateAt string `protobuf:"bytes,8,opt,name=update_at,json=updateAt,proto3" json:"update_at"`
	//更新人id
	UpdateBy string `protobuf:"bytes,9,opt,name=update_by,json=updateBy,proto3" json:"update_by"`
	//贴士内容
	Content string `protobuf:"bytes,10,opt,name=content,proto3" json:"content"`
	//贴士标签
	Tag                  []*TipsTag `protobuf:"bytes,11,rep,name=tag,proto3" json:"tag"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *RecommendTips) Reset()         { *m = RecommendTips{} }
func (m *RecommendTips) String() string { return proto.CompactTextString(m) }
func (*RecommendTips) ProtoMessage()    {}
func (*RecommendTips) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{26}
}

func (m *RecommendTips) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendTips.Unmarshal(m, b)
}
func (m *RecommendTips) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendTips.Marshal(b, m, deterministic)
}
func (m *RecommendTips) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendTips.Merge(m, src)
}
func (m *RecommendTips) XXX_Size() int {
	return xxx_messageInfo_RecommendTips.Size(m)
}
func (m *RecommendTips) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendTips.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendTips proto.InternalMessageInfo

func (m *RecommendTips) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecommendTips) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *RecommendTips) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *RecommendTips) GetReading() int32 {
	if m != nil {
		return m.Reading
	}
	return 0
}

func (m *RecommendTips) GetIsDeleted() int32 {
	if m != nil {
		return m.IsDeleted
	}
	return 0
}

func (m *RecommendTips) GetCreateAt() string {
	if m != nil {
		return m.CreateAt
	}
	return ""
}

func (m *RecommendTips) GetCreateBy() string {
	if m != nil {
		return m.CreateBy
	}
	return ""
}

func (m *RecommendTips) GetUpdateAt() string {
	if m != nil {
		return m.UpdateAt
	}
	return ""
}

func (m *RecommendTips) GetUpdateBy() string {
	if m != nil {
		return m.UpdateBy
	}
	return ""
}

func (m *RecommendTips) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *RecommendTips) GetTag() []*TipsTag {
	if m != nil {
		return m.Tag
	}
	return nil
}

type QueryByIdRequest struct {
	//贴士id
	PetTipsId            []int32  `protobuf:"varint,1,rep,packed,name=pet_tips_id,json=petTipsId,proto3" json:"pet_tips_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryByIdRequest) Reset()         { *m = QueryByIdRequest{} }
func (m *QueryByIdRequest) String() string { return proto.CompactTextString(m) }
func (*QueryByIdRequest) ProtoMessage()    {}
func (*QueryByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{27}
}

func (m *QueryByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryByIdRequest.Unmarshal(m, b)
}
func (m *QueryByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryByIdRequest.Marshal(b, m, deterministic)
}
func (m *QueryByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryByIdRequest.Merge(m, src)
}
func (m *QueryByIdRequest) XXX_Size() int {
	return xxx_messageInfo_QueryByIdRequest.Size(m)
}
func (m *QueryByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryByIdRequest proto.InternalMessageInfo

func (m *QueryByIdRequest) GetPetTipsId() []int32 {
	if m != nil {
		return m.PetTipsId
	}
	return nil
}

type QueryTipWithTagsRequest struct {
	// 页索引
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize             int32    `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTipWithTagsRequest) Reset()         { *m = QueryTipWithTagsRequest{} }
func (m *QueryTipWithTagsRequest) String() string { return proto.CompactTextString(m) }
func (*QueryTipWithTagsRequest) ProtoMessage()    {}
func (*QueryTipWithTagsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{28}
}

func (m *QueryTipWithTagsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTipWithTagsRequest.Unmarshal(m, b)
}
func (m *QueryTipWithTagsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTipWithTagsRequest.Marshal(b, m, deterministic)
}
func (m *QueryTipWithTagsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTipWithTagsRequest.Merge(m, src)
}
func (m *QueryTipWithTagsRequest) XXX_Size() int {
	return xxx_messageInfo_QueryTipWithTagsRequest.Size(m)
}
func (m *QueryTipWithTagsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTipWithTagsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTipWithTagsRequest proto.InternalMessageInfo

func (m *QueryTipWithTagsRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *QueryTipWithTagsRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 文章小贴士推荐
type ArticleTipsRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleTipsRequest) Reset()         { *m = ArticleTipsRequest{} }
func (m *ArticleTipsRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleTipsRequest) ProtoMessage()    {}
func (*ArticleTipsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{29}
}

func (m *ArticleTipsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleTipsRequest.Unmarshal(m, b)
}
func (m *ArticleTipsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleTipsRequest.Marshal(b, m, deterministic)
}
func (m *ArticleTipsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleTipsRequest.Merge(m, src)
}
func (m *ArticleTipsRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleTipsRequest.Size(m)
}
func (m *ArticleTipsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleTipsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleTipsRequest proto.InternalMessageInfo

type ArticleTipsResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message              string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*ArticleTipsData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ArticleTipsResponse) Reset()         { *m = ArticleTipsResponse{} }
func (m *ArticleTipsResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleTipsResponse) ProtoMessage()    {}
func (*ArticleTipsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{30}
}

func (m *ArticleTipsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleTipsResponse.Unmarshal(m, b)
}
func (m *ArticleTipsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleTipsResponse.Marshal(b, m, deterministic)
}
func (m *ArticleTipsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleTipsResponse.Merge(m, src)
}
func (m *ArticleTipsResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleTipsResponse.Size(m)
}
func (m *ArticleTipsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleTipsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleTipsResponse proto.InternalMessageInfo

func (m *ArticleTipsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleTipsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleTipsResponse) GetData() []*ArticleTipsData {
	if m != nil {
		return m.Data
	}
	return nil
}

type ArticleTipsData struct {
	// 文章id
	ArticleId int64 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 文章标题
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleTipsData) Reset()         { *m = ArticleTipsData{} }
func (m *ArticleTipsData) String() string { return proto.CompactTextString(m) }
func (*ArticleTipsData) ProtoMessage()    {}
func (*ArticleTipsData) Descriptor() ([]byte, []int) {
	return fileDescriptor_0facd45ca23e6e57, []int{31}
}

func (m *ArticleTipsData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleTipsData.Unmarshal(m, b)
}
func (m *ArticleTipsData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleTipsData.Marshal(b, m, deterministic)
}
func (m *ArticleTipsData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleTipsData.Merge(m, src)
}
func (m *ArticleTipsData) XXX_Size() int {
	return xxx_messageInfo_ArticleTipsData.Size(m)
}
func (m *ArticleTipsData) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleTipsData.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleTipsData proto.InternalMessageInfo

func (m *ArticleTipsData) GetArticleId() int64 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleTipsData) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func init() {
	proto.RegisterType((*PetTipsDto)(nil), "cc.petTipsDto")
	proto.RegisterType((*PetTipsTagDto)(nil), "cc.petTipsTagDto")
	proto.RegisterType((*PetTipsEditRequest)(nil), "cc.petTipsEditRequest")
	proto.RegisterType((*PetTipsDeleteRequest)(nil), "cc.petTipsDeleteRequest")
	proto.RegisterType((*PetTipsEditResponse)(nil), "cc.petTipsEditResponse")
	proto.RegisterType((*PetTipsQueryRequest)(nil), "cc.petTipsQueryRequest")
	proto.RegisterType((*PetTipsRandQueryRequest)(nil), "cc.petTipsRandQueryRequest")
	proto.RegisterType((*PetTipsQueryResponse)(nil), "cc.petTipsQueryResponse")
	proto.RegisterType((*PetTipsGetRequest)(nil), "cc.petTipsGetRequest")
	proto.RegisterType((*PetTipsGetResponse)(nil), "cc.petTipsGetResponse")
	proto.RegisterType((*ScrmPetVarietyRequest)(nil), "cc.scrmPetVarietyRequest")
	proto.RegisterType((*ScrmPetVarietyDto)(nil), "cc.scrmPetVarietyDto")
	proto.RegisterType((*ScrmPetVarietyResponse)(nil), "cc.scrmPetVarietyResponse")
	proto.RegisterType((*TipsForCustomizedRequest)(nil), "cc.tipsForCustomizedRequest")
	proto.RegisterType((*TipsForCustomizedResponse)(nil), "cc.tipsForCustomizedResponse")
	proto.RegisterType((*TipsForCustomized)(nil), "cc.tipsForCustomized")
	proto.RegisterType((*TipsTag)(nil), "cc.tipsTag")
	proto.RegisterType((*QueryBySpeciesRequest)(nil), "cc.QueryBySpeciesRequest")
	proto.RegisterType((*QueryByConditionRequest)(nil), "cc.QueryByConditionRequest")
	proto.RegisterType((*QueryByConditionResponse)(nil), "cc.QueryByConditionResponse")
	proto.RegisterType((*QueryByCondition)(nil), "cc.QueryByCondition")
	proto.RegisterType((*GetTipsListRequest)(nil), "cc.GetTipsListRequest")
	proto.RegisterType((*GetTipsListResponse)(nil), "cc.GetTipsListResponse")
	proto.RegisterType((*GetTipsList)(nil), "cc.GetTipsList")
	proto.RegisterType((*RecommendTipsRequest)(nil), "cc.recommendTipsRequest")
	proto.RegisterType((*RecommendTipsResponse)(nil), "cc.recommendTipsResponse")
	proto.RegisterType((*RecommendTips)(nil), "cc.recommendTips")
	proto.RegisterType((*QueryByIdRequest)(nil), "cc.queryByIdRequest")
	proto.RegisterType((*QueryTipWithTagsRequest)(nil), "cc.queryTipWithTagsRequest")
	proto.RegisterType((*ArticleTipsRequest)(nil), "cc.ArticleTipsRequest")
	proto.RegisterType((*ArticleTipsResponse)(nil), "cc.ArticleTipsResponse")
	proto.RegisterType((*ArticleTipsData)(nil), "cc.ArticleTipsData")
}

func init() { proto.RegisterFile("cc/pet_tips.proto", fileDescriptor_0facd45ca23e6e57) }

var fileDescriptor_0facd45ca23e6e57 = []byte{
	// 1246 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0x5b, 0x6f, 0x1b, 0xc5,
	0x17, 0x97, 0xd7, 0x97, 0x64, 0x8f, 0x95, 0x34, 0x99, 0xd8, 0xce, 0xc6, 0x49, 0xff, 0x8a, 0xb6,
	0xea, 0xbf, 0xe1, 0x81, 0x80, 0x02, 0x42, 0x14, 0x10, 0x22, 0x4e, 0xd2, 0xc8, 0xa2, 0x14, 0xba,
	0x31, 0xe5, 0x31, 0x9a, 0xec, 0x0c, 0xee, 0x08, 0x7b, 0xd7, 0xd9, 0x1d, 0x47, 0x75, 0x25, 0xfa,
	0x25, 0x78, 0x81, 0x4f, 0xc0, 0x23, 0xcf, 0xfd, 0x76, 0x68, 0x2e, 0xbb, 0x9e, 0xf1, 0x2d, 0xaa,
	0x9b, 0x37, 0xfa, 0xb6, 0xe7, 0x9c, 0x39, 0x67, 0xce, 0x6d, 0x7e, 0x73, 0x66, 0x61, 0x33, 0x0c,
	0x3f, 0x19, 0x50, 0x7e, 0xc9, 0xd9, 0x20, 0x3d, 0x1c, 0x24, 0x31, 0x8f, 0x91, 0x13, 0x86, 0xfe,
	0x5b, 0x07, 0x60, 0x40, 0x79, 0x87, 0x0d, 0xd2, 0x53, 0x1e, 0xa3, 0x75, 0x70, 0x18, 0xf1, 0x0a,
	0xfb, 0x85, 0x83, 0x72, 0xe0, 0x30, 0x82, 0x6a, 0x50, 0xe6, 0x8c, 0xf7, 0xa8, 0xe7, 0xec, 0x17,
	0x0e, 0xdc, 0x40, 0x11, 0x08, 0x41, 0x89, 0x85, 0x71, 0xe4, 0x15, 0x25, 0x53, 0x7e, 0x23, 0x0f,
	0x56, 0x12, 0x8a, 0x09, 0x8b, 0xba, 0x1e, 0x48, 0xf5, 0x8c, 0x14, 0x92, 0x30, 0x8e, 0x38, 0x8d,
	0xb8, 0x57, 0x92, 0x0a, 0x19, 0x89, 0x9a, 0xb0, 0x1a, 0x26, 0x14, 0x73, 0x7a, 0xcc, 0xbd, 0xb2,
	0x14, 0xe5, 0xf4, 0x58, 0xd6, 0x1a, 0x79, 0x15, 0x53, 0xd6, 0x1a, 0x09, 0xd9, 0x70, 0x40, 0x94,
	0xde, 0x8a, 0x92, 0x65, 0xf4, 0x58, 0xd6, 0x1a, 0x79, 0xab, 0xa6, 0xac, 0x35, 0x42, 0x0f, 0xa1,
	0xc4, 0x71, 0x37, 0xf5, 0xdc, 0xfd, 0xe2, 0x41, 0xf5, 0x68, 0xf3, 0x30, 0x0c, 0x0f, 0x75, 0xec,
	0x1d, 0xdc, 0x3d, 0xe5, 0x71, 0x20, 0xc5, 0xe8, 0xff, 0xb0, 0x7e, 0xc3, 0x12, 0x3e, 0xc4, 0xbd,
	0x40, 0x47, 0x54, 0x95, 0x11, 0x4d, 0x70, 0xfd, 0xc7, 0xb0, 0x66, 0xa9, 0x8b, 0xbc, 0x44, 0xb8,
	0x4f, 0x65, 0xfe, 0xdc, 0x40, 0x7e, 0x8b, 0x0c, 0xde, 0xe0, 0xde, 0x30, 0xcf, 0xa0, 0x24, 0xfc,
	0xbf, 0x0b, 0x80, 0xb4, 0xee, 0x19, 0x61, 0x3c, 0xa0, 0xd7, 0x43, 0x9a, 0xf2, 0xf7, 0x4b, 0xff,
	0x9c, 0x24, 0x37, 0xa0, 0x32, 0x4c, 0x69, 0xf2, 0x2c, 0xd6, 0x29, 0xd6, 0x54, 0x9e, 0x8c, 0xca,
	0xc2, 0x64, 0xf8, 0x5f, 0x42, 0x2d, 0xeb, 0x0f, 0xda, 0xa3, 0x9c, 0xce, 0x73, 0x75, 0x03, 0x8a,
	0x8c, 0xa4, 0xda, 0x51, 0xf1, 0xe9, 0x9f, 0xc0, 0x96, 0x15, 0x62, 0x3a, 0x88, 0xa3, 0x54, 0x7a,
	0x1f, 0xc6, 0x84, 0x6a, 0x55, 0xf9, 0x2d, 0xbc, 0xef, 0xd3, 0x34, 0xc5, 0xdd, 0x2c, 0xd2, 0x8c,
	0xf4, 0x7f, 0xcf, 0x8d, 0x3c, 0x1f, 0xd2, 0x64, 0x94, 0xed, 0xbe, 0x07, 0x6e, 0x4a, 0x71, 0x12,
	0xbe, 0xfc, 0x9e, 0x8e, 0x74, 0xba, 0xc7, 0x0c, 0xed, 0x9b, 0x93, 0xfb, 0xb6, 0x07, 0xee, 0x00,
	0x77, 0x69, 0x3b, 0x22, 0xf4, 0x95, 0xcc, 0x5a, 0x39, 0x18, 0x33, 0x44, 0xc7, 0x08, 0xe2, 0x82,
	0xbd, 0xa6, 0x32, 0x77, 0xe5, 0x20, 0xa7, 0xfd, 0x8f, 0x61, 0x5b, 0x6f, 0x1f, 0xe0, 0x88, 0x58,
	0x2e, 0x20, 0x28, 0xa5, 0x42, 0x45, 0xc7, 0x21, 0xbe, 0xfd, 0x37, 0x79, 0xb2, 0xf4, 0xd2, 0x65,
	0x62, 0x96, 0x55, 0x8f, 0x39, 0xee, 0x69, 0x57, 0x15, 0x81, 0x7c, 0x28, 0x11, 0xcc, 0xb1, 0x57,
	0x92, 0xf5, 0x5a, 0x37, 0xea, 0x25, 0x8b, 0x25, 0x64, 0xfe, 0x03, 0xd8, 0xd4, 0xbc, 0x73, 0x3a,
	0xaf, 0xa9, 0xfc, 0x5f, 0xf3, 0xd6, 0x93, 0x8b, 0x96, 0x72, 0x31, 0x73, 0x46, 0x78, 0x38, 0xcf,
	0x99, 0x47, 0x50, 0x4f, 0xc3, 0xa4, 0xff, 0x13, 0xe5, 0x2f, 0x70, 0xc2, 0x28, 0x1f, 0x4d, 0x3b,
	0xe4, 0x4a, 0x87, 0x7e, 0x84, 0x4d, 0x7b, 0xa1, 0x8d, 0x44, 0x72, 0x51, 0x7e, 0xb6, 0x1c, 0xe3,
	0x6c, 0x35, 0xa0, 0x42, 0x5f, 0x71, 0x1a, 0x11, 0x7d, 0x14, 0x34, 0xe5, 0x5f, 0x43, 0x63, 0x72,
	0xe7, 0xa5, 0xa2, 0xfc, 0x28, 0x8f, 0x52, 0xa4, 0xbc, 0x2e, 0xa2, 0x9c, 0x72, 0x54, 0x07, 0x7b,
	0x04, 0x9e, 0x40, 0xd6, 0x27, 0x71, 0x72, 0x32, 0x4c, 0x79, 0xdc, 0x67, 0xaf, 0x29, 0xc9, 0xe2,
	0x6d, 0x40, 0x85, 0xb3, 0x41, 0x9b, 0xa4, 0x5e, 0x61, 0xbf, 0x78, 0x50, 0x0e, 0x34, 0xe5, 0x73,
	0xd8, 0x99, 0xa1, 0x73, 0x57, 0x9e, 0x4e, 0x9b, 0x56, 0x9e, 0xfe, 0xe5, 0xc0, 0xe6, 0x94, 0xec,
	0x6e, 0x80, 0xbf, 0x64, 0x03, 0xff, 0x7d, 0x00, 0x96, 0x5e, 0x12, 0x09, 0x1b, 0x44, 0xa2, 0x4f,
	0x39, 0x70, 0x99, 0xc6, 0x11, 0x82, 0x76, 0xc1, 0x55, 0x88, 0x7e, 0x89, 0xb9, 0x0d, 0xf1, 0xc7,
	0xdc, 0x10, 0x5e, 0x8d, 0x32, 0x8c, 0xcf, 0xf1, 0x7f, 0x17, 0x5c, 0x85, 0xe9, 0x42, 0x73, 0x75,
	0xe2, 0x02, 0x18, 0x0b, 0xaf, 0x46, 0x9e, 0x3b, 0x71, 0x03, 0xdc, 0x87, 0x22, 0xc7, 0xe2, 0x86,
	0x12, 0x69, 0xaa, 0x66, 0x69, 0xea, 0xe0, 0x6e, 0x20, 0xf8, 0xfe, 0x31, 0xac, 0x68, 0x1a, 0xed,
	0xc0, 0x2a, 0xc7, 0xdd, 0x4b, 0x03, 0xcf, 0x57, 0x38, 0xee, 0x3e, 0x13, 0x6d, 0xb7, 0x0b, 0xae,
	0x10, 0x99, 0xb0, 0x2e, 0xd6, 0xbe, 0x90, 0xc8, 0xfe, 0x1b, 0xd4, 0xe5, 0xd9, 0x6f, 0x8d, 0x2e,
	0x06, 0x34, 0x64, 0x34, 0xcd, 0xba, 0xc0, 0x83, 0x95, 0x54, 0x71, 0x32, 0x7b, 0x9a, 0xb4, 0xe1,
	0xc9, 0x59, 0x04, 0x4f, 0xc5, 0x09, 0x78, 0x3a, 0x83, 0x6d, 0xbd, 0xd9, 0x49, 0x1c, 0x11, 0xc6,
	0x59, 0x1c, 0xdd, 0xbe, 0xdd, 0x06, 0x14, 0xc7, 0x1d, 0x24, 0x3e, 0xfd, 0x04, 0xbc, 0x69, 0x33,
	0x4b, 0xf5, 0xe1, 0x81, 0xd5, 0x87, 0x35, 0x91, 0xe0, 0x29, 0xcb, 0xaa, 0x0d, 0xff, 0x74, 0x60,
	0x63, 0x52, 0xf4, 0xa1, 0x0b, 0x65, 0x17, 0x52, 0x40, 0xe7, 0x0a, 0x4c, 0x9f, 0xb2, 0x34, 0x87,
	0xf1, 0xba, 0x44, 0x91, 0xcb, 0x3c, 0x3f, 0x65, 0x89, 0x22, 0xef, 0xd1, 0x3c, 0x6f, 0x60, 0xcb,
	0xda, 0xe6, 0x0e, 0xef, 0xaa, 0x07, 0xd6, 0x5d, 0x75, 0x4f, 0x44, 0x68, 0x6e, 0xa5, 0x3a, 0xe0,
	0x0f, 0x07, 0xaa, 0x06, 0xf7, 0x43, 0xf1, 0x65, 0xf1, 0x3f, 0x85, 0x5a, 0x42, 0xc3, 0xb8, 0xdf,
	0xa7, 0x11, 0x91, 0x73, 0xc7, 0x6d, 0xe7, 0xd9, 0xef, 0x41, 0x7d, 0x42, 0x63, 0xa9, 0x4a, 0x3e,
	0xb4, 0x8e, 0xae, 0x9c, 0x07, 0x6d, 0xb3, 0xaa, 0x6a, 0xff, 0x38, 0xb0, 0x66, 0xf1, 0xff, 0x2b,
	0x75, 0x33, 0x26, 0x6c, 0xb0, 0x27, 0x6c, 0x5d, 0xd1, 0xea, 0x9c, 0x8a, 0x1e, 0xc1, 0xc6, 0xb5,
	0x02, 0xba, 0x76, 0x3e, 0x12, 0xfc, 0x0f, 0xaa, 0xd9, 0x63, 0x4c, 0x9d, 0xe8, 0xa2, 0x3c, 0xb7,
	0xea, 0x34, 0xb4, 0x89, 0x7f, 0x01, 0xdb, 0x52, 0xa7, 0xc3, 0x06, 0xbf, 0x30, 0xfe, 0xb2, 0x83,
	0xbb, 0xa9, 0x31, 0xfa, 0x8e, 0x0f, 0x7c, 0x61, 0xd1, 0x81, 0x77, 0x26, 0x0e, 0x7c, 0x0d, 0xd0,
	0x71, 0xc2, 0x59, 0xd8, 0xa3, 0x46, 0x63, 0xf9, 0x3d, 0xd8, 0xb2, 0xb8, 0x4b, 0x35, 0xcf, 0x23,
	0xab, 0x79, 0xb6, 0x44, 0x0e, 0x0c, 0xa3, 0xa7, 0x98, 0x63, 0xdd, 0x3e, 0x4f, 0xe0, 0xde, 0x84,
	0x40, 0xd4, 0x1a, 0x2b, 0x56, 0x06, 0x6e, 0xc5, 0xc0, 0xd5, 0x9c, 0xf6, 0x9c, 0x76, 0x3a, 0x7a,
	0xbb, 0x0a, 0xeb, 0x3a, 0x5d, 0x17, 0x34, 0xb9, 0x61, 0x21, 0x45, 0x67, 0xb0, 0x6e, 0xdf, 0xbc,
	0x68, 0xc7, 0xb8, 0x7f, 0xec, 0xdb, 0xb8, 0xe9, 0x19, 0x23, 0xab, 0x3d, 0xab, 0xb7, 0xf5, 0xbd,
	0x64, 0xa4, 0x1e, 0xed, 0x8a, 0xd5, 0x73, 0x0a, 0xb2, 0xc0, 0xd4, 0x37, 0x00, 0xcf, 0xb3, 0xca,
	0xa7, 0xa8, 0x96, 0x1b, 0x31, 0x3a, 0x61, 0x81, 0xf6, 0x17, 0x50, 0x3c, 0x26, 0x04, 0x35, 0x8c,
	0x05, 0xc6, 0x5b, 0xb1, 0xb9, 0x3d, 0xc5, 0xd7, 0x7a, 0x8f, 0xa1, 0xf2, 0xb3, 0x6c, 0xda, 0x77,
	0x57, 0xfd, 0x0e, 0xd6, 0x94, 0xaa, 0x7e, 0xe2, 0xbe, 0xbb, 0x85, 0xaf, 0xa1, 0xa2, 0x0e, 0x29,
	0x32, 0x03, 0xb3, 0x9e, 0x8e, 0xf3, 0x95, 0xbf, 0x82, 0xb2, 0x4c, 0x01, 0xda, 0x9e, 0x4e, 0xca,
	0x6d, 0xd9, 0x6a, 0x81, 0xab, 0x18, 0x38, 0x22, 0xaa, 0x5e, 0x73, 0x1e, 0x6e, 0x0b, 0x6c, 0x3c,
	0x85, 0x2d, 0xc9, 0xb8, 0xb0, 0x86, 0x7c, 0xd5, 0x46, 0x33, 0x9f, 0x32, 0xcd, 0xe6, 0x2c, 0x91,
	0xb6, 0xf6, 0x39, 0x14, 0xcf, 0x29, 0x47, 0x75, 0x63, 0xbb, 0xf1, 0xab, 0xac, 0xd9, 0x98, 0x64,
	0xe7, 0x3e, 0xac, 0xe9, 0x9e, 0xe9, 0xc8, 0x57, 0x02, 0xda, 0x9b, 0x3d, 0xcc, 0x6b, 0x33, 0xf7,
	0xe7, 0x48, 0xb5, 0xb5, 0x1f, 0x66, 0x0c, 0x59, 0xbb, 0x33, 0xa7, 0x32, 0x6d, 0x6f, 0x6f, 0xb6,
	0x50, 0x9b, 0xfb, 0xd6, 0xbe, 0xb1, 0x1b, 0x93, 0x17, 0xbb, 0x59, 0xe0, 0x59, 0xb3, 0xc5, 0x29,
	0xac, 0x05, 0xd6, 0xdd, 0xe1, 0x4d, 0x5f, 0x33, 0xda, 0xc6, 0xce, 0x0c, 0xc9, 0xd8, 0x0b, 0x03,
	0x43, 0x94, 0x17, 0xd3, 0xc0, 0xa6, 0xbc, 0x98, 0x01, 0x6d, 0x57, 0x15, 0xf9, 0xfb, 0xeb, 0xb3,
	0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x56, 0xb4, 0x65, 0xf1, 0x13, 0x13, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PetTipsServiceClient is the client API for PetTipsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PetTipsServiceClient interface {
	//根据物种分页获取数据
	QueryBySpecies(ctx context.Context, in *QueryBySpeciesRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error)
	//分页获取贴士信息(带上标签数据)
	QueryTipWithTags(ctx context.Context, in *QueryTipWithTagsRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error)
	//通过贴士id([]int32)获取贴士信息
	QueryByIds(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error)
	// 新增
	Add(ctx context.Context, in *PetTipsEditRequest, opts ...grpc.CallOption) (*PetTipsEditResponse, error)
	// 修改
	Update(ctx context.Context, in *PetTipsEditRequest, opts ...grpc.CallOption) (*PetTipsEditResponse, error)
	// 更新阅读量
	UpdateReading(ctx context.Context, in *PetTipsEditRequest, opts ...grpc.CallOption) (*PetTipsEditResponse, error)
	// 删除
	Delete(ctx context.Context, in *PetTipsDeleteRequest, opts ...grpc.CallOption) (*PetTipsEditResponse, error)
	// 查询
	Query(ctx context.Context, in *PetTipsQueryRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error)
	// 随机查询
	QueryRand(ctx context.Context, in *PetTipsRandQueryRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error)
	// scrm 品种查询
	QueryScrmPetVariety(ctx context.Context, in *ScrmPetVarietyRequest, opts ...grpc.CallOption) (*ScrmPetVarietyResponse, error)
	// 查询单个
	Get(ctx context.Context, in *PetTipsGetRequest, opts ...grpc.CallOption) (*PetTipsGetResponse, error)
	//通过贴士ids获取信息
	QueryByTipIds(ctx context.Context, in *TipsForCustomizedRequest, opts ...grpc.CallOption) (*TipsForCustomizedResponse, error)
	//根据特定条件获取数据
	QueryByCondition(ctx context.Context, in *QueryByConditionRequest, opts ...grpc.CallOption) (*QueryByConditionResponse, error)
	//未登录时分页获取贴士信息
	GetTipsList(ctx context.Context, in *GetTipsListRequest, opts ...grpc.CallOption) (*GetTipsListResponse, error)
	//旺财小贴士 - 宠物贴士推荐
	RecommendTips(ctx context.Context, in *RecommendTipsRequest, opts ...grpc.CallOption) (*RecommendTipsResponse, error)
	// content-api内容中心推荐宠物贴士-小程序
	ArticleTips(ctx context.Context, in *ArticleTipsRequest, opts ...grpc.CallOption) (*ArticleTipsResponse, error)
}

type petTipsServiceClient struct {
	cc *grpc.ClientConn
}

func NewPetTipsServiceClient(cc *grpc.ClientConn) PetTipsServiceClient {
	return &petTipsServiceClient{cc}
}

func (c *petTipsServiceClient) QueryBySpecies(ctx context.Context, in *QueryBySpeciesRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error) {
	out := new(PetTipsQueryResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/QueryBySpecies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) QueryTipWithTags(ctx context.Context, in *QueryTipWithTagsRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error) {
	out := new(PetTipsQueryResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/QueryTipWithTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) QueryByIds(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error) {
	out := new(PetTipsQueryResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/QueryByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) Add(ctx context.Context, in *PetTipsEditRequest, opts ...grpc.CallOption) (*PetTipsEditResponse, error) {
	out := new(PetTipsEditResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/Add", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) Update(ctx context.Context, in *PetTipsEditRequest, opts ...grpc.CallOption) (*PetTipsEditResponse, error) {
	out := new(PetTipsEditResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) UpdateReading(ctx context.Context, in *PetTipsEditRequest, opts ...grpc.CallOption) (*PetTipsEditResponse, error) {
	out := new(PetTipsEditResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/UpdateReading", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) Delete(ctx context.Context, in *PetTipsDeleteRequest, opts ...grpc.CallOption) (*PetTipsEditResponse, error) {
	out := new(PetTipsEditResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) Query(ctx context.Context, in *PetTipsQueryRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error) {
	out := new(PetTipsQueryResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/Query", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) QueryRand(ctx context.Context, in *PetTipsRandQueryRequest, opts ...grpc.CallOption) (*PetTipsQueryResponse, error) {
	out := new(PetTipsQueryResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/QueryRand", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) QueryScrmPetVariety(ctx context.Context, in *ScrmPetVarietyRequest, opts ...grpc.CallOption) (*ScrmPetVarietyResponse, error) {
	out := new(ScrmPetVarietyResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/QueryScrmPetVariety", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) Get(ctx context.Context, in *PetTipsGetRequest, opts ...grpc.CallOption) (*PetTipsGetResponse, error) {
	out := new(PetTipsGetResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/Get", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) QueryByTipIds(ctx context.Context, in *TipsForCustomizedRequest, opts ...grpc.CallOption) (*TipsForCustomizedResponse, error) {
	out := new(TipsForCustomizedResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/QueryByTipIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) QueryByCondition(ctx context.Context, in *QueryByConditionRequest, opts ...grpc.CallOption) (*QueryByConditionResponse, error) {
	out := new(QueryByConditionResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/QueryByCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) GetTipsList(ctx context.Context, in *GetTipsListRequest, opts ...grpc.CallOption) (*GetTipsListResponse, error) {
	out := new(GetTipsListResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/GetTipsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) RecommendTips(ctx context.Context, in *RecommendTipsRequest, opts ...grpc.CallOption) (*RecommendTipsResponse, error) {
	out := new(RecommendTipsResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/RecommendTips", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petTipsServiceClient) ArticleTips(ctx context.Context, in *ArticleTipsRequest, opts ...grpc.CallOption) (*ArticleTipsResponse, error) {
	out := new(ArticleTipsResponse)
	err := c.cc.Invoke(ctx, "/cc.petTipsService/ArticleTips", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetTipsServiceServer is the server API for PetTipsService service.
type PetTipsServiceServer interface {
	//根据物种分页获取数据
	QueryBySpecies(context.Context, *QueryBySpeciesRequest) (*PetTipsQueryResponse, error)
	//分页获取贴士信息(带上标签数据)
	QueryTipWithTags(context.Context, *QueryTipWithTagsRequest) (*PetTipsQueryResponse, error)
	//通过贴士id([]int32)获取贴士信息
	QueryByIds(context.Context, *QueryByIdRequest) (*PetTipsQueryResponse, error)
	// 新增
	Add(context.Context, *PetTipsEditRequest) (*PetTipsEditResponse, error)
	// 修改
	Update(context.Context, *PetTipsEditRequest) (*PetTipsEditResponse, error)
	// 更新阅读量
	UpdateReading(context.Context, *PetTipsEditRequest) (*PetTipsEditResponse, error)
	// 删除
	Delete(context.Context, *PetTipsDeleteRequest) (*PetTipsEditResponse, error)
	// 查询
	Query(context.Context, *PetTipsQueryRequest) (*PetTipsQueryResponse, error)
	// 随机查询
	QueryRand(context.Context, *PetTipsRandQueryRequest) (*PetTipsQueryResponse, error)
	// scrm 品种查询
	QueryScrmPetVariety(context.Context, *ScrmPetVarietyRequest) (*ScrmPetVarietyResponse, error)
	// 查询单个
	Get(context.Context, *PetTipsGetRequest) (*PetTipsGetResponse, error)
	//通过贴士ids获取信息
	QueryByTipIds(context.Context, *TipsForCustomizedRequest) (*TipsForCustomizedResponse, error)
	//根据特定条件获取数据
	QueryByCondition(context.Context, *QueryByConditionRequest) (*QueryByConditionResponse, error)
	//未登录时分页获取贴士信息
	GetTipsList(context.Context, *GetTipsListRequest) (*GetTipsListResponse, error)
	//旺财小贴士 - 宠物贴士推荐
	RecommendTips(context.Context, *RecommendTipsRequest) (*RecommendTipsResponse, error)
	// content-api内容中心推荐宠物贴士-小程序
	ArticleTips(context.Context, *ArticleTipsRequest) (*ArticleTipsResponse, error)
}

// UnimplementedPetTipsServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPetTipsServiceServer struct {
}

func (*UnimplementedPetTipsServiceServer) QueryBySpecies(ctx context.Context, req *QueryBySpeciesRequest) (*PetTipsQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryBySpecies not implemented")
}
func (*UnimplementedPetTipsServiceServer) QueryTipWithTags(ctx context.Context, req *QueryTipWithTagsRequest) (*PetTipsQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryTipWithTags not implemented")
}
func (*UnimplementedPetTipsServiceServer) QueryByIds(ctx context.Context, req *QueryByIdRequest) (*PetTipsQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryByIds not implemented")
}
func (*UnimplementedPetTipsServiceServer) Add(ctx context.Context, req *PetTipsEditRequest) (*PetTipsEditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Add not implemented")
}
func (*UnimplementedPetTipsServiceServer) Update(ctx context.Context, req *PetTipsEditRequest) (*PetTipsEditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPetTipsServiceServer) UpdateReading(ctx context.Context, req *PetTipsEditRequest) (*PetTipsEditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReading not implemented")
}
func (*UnimplementedPetTipsServiceServer) Delete(ctx context.Context, req *PetTipsDeleteRequest) (*PetTipsEditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPetTipsServiceServer) Query(ctx context.Context, req *PetTipsQueryRequest) (*PetTipsQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPetTipsServiceServer) QueryRand(ctx context.Context, req *PetTipsRandQueryRequest) (*PetTipsQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRand not implemented")
}
func (*UnimplementedPetTipsServiceServer) QueryScrmPetVariety(ctx context.Context, req *ScrmPetVarietyRequest) (*ScrmPetVarietyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryScrmPetVariety not implemented")
}
func (*UnimplementedPetTipsServiceServer) Get(ctx context.Context, req *PetTipsGetRequest) (*PetTipsGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (*UnimplementedPetTipsServiceServer) QueryByTipIds(ctx context.Context, req *TipsForCustomizedRequest) (*TipsForCustomizedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryByTipIds not implemented")
}
func (*UnimplementedPetTipsServiceServer) QueryByCondition(ctx context.Context, req *QueryByConditionRequest) (*QueryByConditionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryByCondition not implemented")
}
func (*UnimplementedPetTipsServiceServer) GetTipsList(ctx context.Context, req *GetTipsListRequest) (*GetTipsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTipsList not implemented")
}
func (*UnimplementedPetTipsServiceServer) RecommendTips(ctx context.Context, req *RecommendTipsRequest) (*RecommendTipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecommendTips not implemented")
}
func (*UnimplementedPetTipsServiceServer) ArticleTips(ctx context.Context, req *ArticleTipsRequest) (*ArticleTipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleTips not implemented")
}

func RegisterPetTipsServiceServer(s *grpc.Server, srv PetTipsServiceServer) {
	s.RegisterService(&_PetTipsService_serviceDesc, srv)
}

func _PetTipsService_QueryBySpecies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryBySpeciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).QueryBySpecies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/QueryBySpecies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).QueryBySpecies(ctx, req.(*QueryBySpeciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_QueryTipWithTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTipWithTagsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).QueryTipWithTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/QueryTipWithTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).QueryTipWithTags(ctx, req.(*QueryTipWithTagsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_QueryByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).QueryByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/QueryByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).QueryByIds(ctx, req.(*QueryByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_Add_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTipsEditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).Add(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/Add",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).Add(ctx, req.(*PetTipsEditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTipsEditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).Update(ctx, req.(*PetTipsEditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_UpdateReading_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTipsEditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).UpdateReading(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/UpdateReading",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).UpdateReading(ctx, req.(*PetTipsEditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTipsDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).Delete(ctx, req.(*PetTipsDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTipsQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/Query",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).Query(ctx, req.(*PetTipsQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_QueryRand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTipsRandQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).QueryRand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/QueryRand",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).QueryRand(ctx, req.(*PetTipsRandQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_QueryScrmPetVariety_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScrmPetVarietyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).QueryScrmPetVariety(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/QueryScrmPetVariety",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).QueryScrmPetVariety(ctx, req.(*ScrmPetVarietyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTipsGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/Get",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).Get(ctx, req.(*PetTipsGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_QueryByTipIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TipsForCustomizedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).QueryByTipIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/QueryByTipIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).QueryByTipIds(ctx, req.(*TipsForCustomizedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_QueryByCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).QueryByCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/QueryByCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).QueryByCondition(ctx, req.(*QueryByConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_GetTipsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTipsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).GetTipsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/GetTipsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).GetTipsList(ctx, req.(*GetTipsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_RecommendTips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendTipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).RecommendTips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/RecommendTips",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).RecommendTips(ctx, req.(*RecommendTipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetTipsService_ArticleTips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleTipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetTipsServiceServer).ArticleTips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.petTipsService/ArticleTips",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetTipsServiceServer).ArticleTips(ctx, req.(*ArticleTipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PetTipsService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.petTipsService",
	HandlerType: (*PetTipsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryBySpecies",
			Handler:    _PetTipsService_QueryBySpecies_Handler,
		},
		{
			MethodName: "QueryTipWithTags",
			Handler:    _PetTipsService_QueryTipWithTags_Handler,
		},
		{
			MethodName: "QueryByIds",
			Handler:    _PetTipsService_QueryByIds_Handler,
		},
		{
			MethodName: "Add",
			Handler:    _PetTipsService_Add_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PetTipsService_Update_Handler,
		},
		{
			MethodName: "UpdateReading",
			Handler:    _PetTipsService_UpdateReading_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PetTipsService_Delete_Handler,
		},
		{
			MethodName: "Query",
			Handler:    _PetTipsService_Query_Handler,
		},
		{
			MethodName: "QueryRand",
			Handler:    _PetTipsService_QueryRand_Handler,
		},
		{
			MethodName: "QueryScrmPetVariety",
			Handler:    _PetTipsService_QueryScrmPetVariety_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _PetTipsService_Get_Handler,
		},
		{
			MethodName: "QueryByTipIds",
			Handler:    _PetTipsService_QueryByTipIds_Handler,
		},
		{
			MethodName: "QueryByCondition",
			Handler:    _PetTipsService_QueryByCondition_Handler,
		},
		{
			MethodName: "GetTipsList",
			Handler:    _PetTipsService_GetTipsList_Handler,
		},
		{
			MethodName: "RecommendTips",
			Handler:    _PetTipsService_RecommendTips_Handler,
		},
		{
			MethodName: "ArticleTips",
			Handler:    _PetTipsService_ArticleTips_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/pet_tips.proto",
}
