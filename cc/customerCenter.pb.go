// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/customerCenter.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type EmptyReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyReq) Reset()         { *m = EmptyReq{} }
func (m *EmptyReq) String() string { return proto.CompactTextString(m) }
func (*EmptyReq) ProtoMessage()    {}
func (*EmptyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{0}
}

func (m *EmptyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyReq.Unmarshal(m, b)
}
func (m *EmptyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyReq.Marshal(b, m, deterministic)
}
func (m *EmptyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyReq.Merge(m, src)
}
func (m *EmptyReq) XXX_Size() int {
	return xxx_messageInfo_EmptyReq.Size(m)
}
func (m *EmptyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyReq.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyReq proto.InternalMessageInfo

type Response struct {
	Code                 int64    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{1}
}

func (m *Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Response.Unmarshal(m, b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Response.Marshal(b, m, deterministic)
}
func (m *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(m, src)
}
func (m *Response) XXX_Size() int {
	return xxx_messageInfo_Response.Size(m)
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *Response) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type BaseResponseNew struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponseNew) Reset()         { *m = BaseResponseNew{} }
func (m *BaseResponseNew) String() string { return proto.CompactTextString(m) }
func (*BaseResponseNew) ProtoMessage()    {}
func (*BaseResponseNew) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{2}
}

func (m *BaseResponseNew) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponseNew.Unmarshal(m, b)
}
func (m *BaseResponseNew) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponseNew.Marshal(b, m, deterministic)
}
func (m *BaseResponseNew) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponseNew.Merge(m, src)
}
func (m *BaseResponseNew) XXX_Size() int {
	return xxx_messageInfo_BaseResponseNew.Size(m)
}
func (m *BaseResponseNew) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponseNew.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponseNew proto.InternalMessageInfo

func (m *BaseResponseNew) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type EquityGetcouponReq struct {
	//券ID
	CouponId int32 `protobuf:"varint,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	//券类型 1门店券 2优惠券(商城券)
	CouponType int32 `protobuf:"varint,2,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type"`
	//用户ID（前端不用传）
	ScrmUserid           string   `protobuf:"bytes,3,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EquityGetcouponReq) Reset()         { *m = EquityGetcouponReq{} }
func (m *EquityGetcouponReq) String() string { return proto.CompactTextString(m) }
func (*EquityGetcouponReq) ProtoMessage()    {}
func (*EquityGetcouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{3}
}

func (m *EquityGetcouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EquityGetcouponReq.Unmarshal(m, b)
}
func (m *EquityGetcouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EquityGetcouponReq.Marshal(b, m, deterministic)
}
func (m *EquityGetcouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EquityGetcouponReq.Merge(m, src)
}
func (m *EquityGetcouponReq) XXX_Size() int {
	return xxx_messageInfo_EquityGetcouponReq.Size(m)
}
func (m *EquityGetcouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EquityGetcouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_EquityGetcouponReq proto.InternalMessageInfo

func (m *EquityGetcouponReq) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *EquityGetcouponReq) GetCouponType() int32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

func (m *EquityGetcouponReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

type MemberEquityDetailReq struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberEquityDetailReq) Reset()         { *m = MemberEquityDetailReq{} }
func (m *MemberEquityDetailReq) String() string { return proto.CompactTextString(m) }
func (*MemberEquityDetailReq) ProtoMessage()    {}
func (*MemberEquityDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{4}
}

func (m *MemberEquityDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberEquityDetailReq.Unmarshal(m, b)
}
func (m *MemberEquityDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberEquityDetailReq.Marshal(b, m, deterministic)
}
func (m *MemberEquityDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberEquityDetailReq.Merge(m, src)
}
func (m *MemberEquityDetailReq) XXX_Size() int {
	return xxx_messageInfo_MemberEquityDetailReq.Size(m)
}
func (m *MemberEquityDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberEquityDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_MemberEquityDetailReq proto.InternalMessageInfo

func (m *MemberEquityDetailReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type MemberEquitySetReq struct {
	//权益ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//操作 1显示 0隐藏
	IsDisplay            int32    `protobuf:"varint,2,opt,name=is_display,json=isDisplay,proto3" json:"is_display"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberEquitySetReq) Reset()         { *m = MemberEquitySetReq{} }
func (m *MemberEquitySetReq) String() string { return proto.CompactTextString(m) }
func (*MemberEquitySetReq) ProtoMessage()    {}
func (*MemberEquitySetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{5}
}

func (m *MemberEquitySetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberEquitySetReq.Unmarshal(m, b)
}
func (m *MemberEquitySetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberEquitySetReq.Marshal(b, m, deterministic)
}
func (m *MemberEquitySetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberEquitySetReq.Merge(m, src)
}
func (m *MemberEquitySetReq) XXX_Size() int {
	return xxx_messageInfo_MemberEquitySetReq.Size(m)
}
func (m *MemberEquitySetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberEquitySetReq.DiscardUnknown(m)
}

var xxx_messageInfo_MemberEquitySetReq proto.InternalMessageInfo

func (m *MemberEquitySetReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MemberEquitySetReq) GetIsDisplay() int32 {
	if m != nil {
		return m.IsDisplay
	}
	return 0
}

type MemberEquityDetailRes struct {
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Id  int32  `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	//权益名称
	EquityName string `protobuf:"bytes,3,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//权益图标
	Icon string `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon"`
	//权益简介
	EquityInfo string `protobuf:"bytes,5,opt,name=equity_info,json=equityInfo,proto3" json:"equity_info"`
	//规则
	EquityRules string `protobuf:"bytes,6,opt,name=equity_rules,json=equityRules,proto3" json:"equity_rules"`
	//1显示  0隐藏
	IsDisplay int32 `protobuf:"varint,7,opt,name=is_display,json=isDisplay,proto3" json:"is_display"`
	//是否有赠券 1是 0否
	IsVoucher            int32    `protobuf:"varint,8,opt,name=is_voucher,json=isVoucher,proto3" json:"is_voucher"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberEquityDetailRes) Reset()         { *m = MemberEquityDetailRes{} }
func (m *MemberEquityDetailRes) String() string { return proto.CompactTextString(m) }
func (*MemberEquityDetailRes) ProtoMessage()    {}
func (*MemberEquityDetailRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{6}
}

func (m *MemberEquityDetailRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberEquityDetailRes.Unmarshal(m, b)
}
func (m *MemberEquityDetailRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberEquityDetailRes.Marshal(b, m, deterministic)
}
func (m *MemberEquityDetailRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberEquityDetailRes.Merge(m, src)
}
func (m *MemberEquityDetailRes) XXX_Size() int {
	return xxx_messageInfo_MemberEquityDetailRes.Size(m)
}
func (m *MemberEquityDetailRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberEquityDetailRes.DiscardUnknown(m)
}

var xxx_messageInfo_MemberEquityDetailRes proto.InternalMessageInfo

func (m *MemberEquityDetailRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MemberEquityDetailRes) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MemberEquityDetailRes) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *MemberEquityDetailRes) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MemberEquityDetailRes) GetEquityInfo() string {
	if m != nil {
		return m.EquityInfo
	}
	return ""
}

func (m *MemberEquityDetailRes) GetEquityRules() string {
	if m != nil {
		return m.EquityRules
	}
	return ""
}

func (m *MemberEquityDetailRes) GetIsDisplay() int32 {
	if m != nil {
		return m.IsDisplay
	}
	return 0
}

func (m *MemberEquityDetailRes) GetIsVoucher() int32 {
	if m != nil {
		return m.IsVoucher
	}
	return 0
}

type EquityIndexReq struct {
	//用户ID，前端不用传
	ScrmUserid           string   `protobuf:"bytes,1,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EquityIndexReq) Reset()         { *m = EquityIndexReq{} }
func (m *EquityIndexReq) String() string { return proto.CompactTextString(m) }
func (*EquityIndexReq) ProtoMessage()    {}
func (*EquityIndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{7}
}

func (m *EquityIndexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EquityIndexReq.Unmarshal(m, b)
}
func (m *EquityIndexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EquityIndexReq.Marshal(b, m, deterministic)
}
func (m *EquityIndexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EquityIndexReq.Merge(m, src)
}
func (m *EquityIndexReq) XXX_Size() int {
	return xxx_messageInfo_EquityIndexReq.Size(m)
}
func (m *EquityIndexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EquityIndexReq.DiscardUnknown(m)
}

var xxx_messageInfo_EquityIndexReq proto.InternalMessageInfo

func (m *EquityIndexReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

type EquityIndexRes struct {
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	//用户当前处于什么级别
	LevelId int32 `protobuf:"varint,2,opt,name=level_id,json=levelId,proto3" json:"level_id"`
	//用户当前级别会员过期时间
	UserLevelEtime int32 `protobuf:"varint,3,opt,name=user_level_etime,json=userLevelEtime,proto3" json:"user_level_etime"`
	//当前健康值
	HealthVal int32 `protobuf:"varint,4,opt,name=health_val,json=healthVal,proto3" json:"health_val"`
	//冻结健康值
	FreezeHealthVal int32 `protobuf:"varint,5,opt,name=freeze_health_val,json=freezeHealthVal,proto3" json:"freeze_health_val"`
	//通知状态，1开启，0关闭
	NotificationState int32 `protobuf:"varint,6,opt,name=notification_state,json=notificationState,proto3" json:"notification_state"`
	//各等级详情
	Data                 []*EquityIndex `protobuf:"bytes,7,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *EquityIndexRes) Reset()         { *m = EquityIndexRes{} }
func (m *EquityIndexRes) String() string { return proto.CompactTextString(m) }
func (*EquityIndexRes) ProtoMessage()    {}
func (*EquityIndexRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{8}
}

func (m *EquityIndexRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EquityIndexRes.Unmarshal(m, b)
}
func (m *EquityIndexRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EquityIndexRes.Marshal(b, m, deterministic)
}
func (m *EquityIndexRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EquityIndexRes.Merge(m, src)
}
func (m *EquityIndexRes) XXX_Size() int {
	return xxx_messageInfo_EquityIndexRes.Size(m)
}
func (m *EquityIndexRes) XXX_DiscardUnknown() {
	xxx_messageInfo_EquityIndexRes.DiscardUnknown(m)
}

var xxx_messageInfo_EquityIndexRes proto.InternalMessageInfo

func (m *EquityIndexRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *EquityIndexRes) GetLevelId() int32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *EquityIndexRes) GetUserLevelEtime() int32 {
	if m != nil {
		return m.UserLevelEtime
	}
	return 0
}

func (m *EquityIndexRes) GetHealthVal() int32 {
	if m != nil {
		return m.HealthVal
	}
	return 0
}

func (m *EquityIndexRes) GetFreezeHealthVal() int32 {
	if m != nil {
		return m.FreezeHealthVal
	}
	return 0
}

func (m *EquityIndexRes) GetNotificationState() int32 {
	if m != nil {
		return m.NotificationState
	}
	return 0
}

func (m *EquityIndexRes) GetData() []*EquityIndex {
	if m != nil {
		return m.Data
	}
	return nil
}

type EquityIndex struct {
	//等级ID
	LevelId int32 `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id"`
	//等级名称
	LevelName string `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name"`
	//当前级别需要的健康值
	HealthVal int32 `protobuf:"varint,3,opt,name=health_val,json=healthVal,proto3" json:"health_val"`
	//等级背景
	Background string `protobuf:"bytes,4,opt,name=background,proto3" json:"background"`
	// 个人中心背景
	CenterBackground string `protobuf:"bytes,6,opt,name=center_background,json=centerBackground,proto3" json:"center_background"`
	//当前享受的权益
	EquityList []*EquityDataList `protobuf:"bytes,5,rep,name=equity_list,json=equityList,proto3" json:"equity_list"`
	//等级图标
	LevelIcon            string   `protobuf:"bytes,7,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EquityIndex) Reset()         { *m = EquityIndex{} }
func (m *EquityIndex) String() string { return proto.CompactTextString(m) }
func (*EquityIndex) ProtoMessage()    {}
func (*EquityIndex) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{9}
}

func (m *EquityIndex) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EquityIndex.Unmarshal(m, b)
}
func (m *EquityIndex) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EquityIndex.Marshal(b, m, deterministic)
}
func (m *EquityIndex) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EquityIndex.Merge(m, src)
}
func (m *EquityIndex) XXX_Size() int {
	return xxx_messageInfo_EquityIndex.Size(m)
}
func (m *EquityIndex) XXX_DiscardUnknown() {
	xxx_messageInfo_EquityIndex.DiscardUnknown(m)
}

var xxx_messageInfo_EquityIndex proto.InternalMessageInfo

func (m *EquityIndex) GetLevelId() int32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *EquityIndex) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *EquityIndex) GetHealthVal() int32 {
	if m != nil {
		return m.HealthVal
	}
	return 0
}

func (m *EquityIndex) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *EquityIndex) GetCenterBackground() string {
	if m != nil {
		return m.CenterBackground
	}
	return ""
}

func (m *EquityIndex) GetEquityList() []*EquityDataList {
	if m != nil {
		return m.EquityList
	}
	return nil
}

func (m *EquityIndex) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

type EquityDataList struct {
	//权益名称
	EquityName string `protobuf:"bytes,1,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//图标
	Icon string `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon"`
	//是否点亮 1是 0否
	IsSelect int32 `protobuf:"varint,3,opt,name=is_select,json=isSelect,proto3" json:"is_select"`
	//权益ID
	Id                   int32    `protobuf:"varint,4,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EquityDataList) Reset()         { *m = EquityDataList{} }
func (m *EquityDataList) String() string { return proto.CompactTextString(m) }
func (*EquityDataList) ProtoMessage()    {}
func (*EquityDataList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{10}
}

func (m *EquityDataList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EquityDataList.Unmarshal(m, b)
}
func (m *EquityDataList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EquityDataList.Marshal(b, m, deterministic)
}
func (m *EquityDataList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EquityDataList.Merge(m, src)
}
func (m *EquityDataList) XXX_Size() int {
	return xxx_messageInfo_EquityDataList.Size(m)
}
func (m *EquityDataList) XXX_DiscardUnknown() {
	xxx_messageInfo_EquityDataList.DiscardUnknown(m)
}

var xxx_messageInfo_EquityDataList proto.InternalMessageInfo

func (m *EquityDataList) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *EquityDataList) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *EquityDataList) GetIsSelect() int32 {
	if m != nil {
		return m.IsSelect
	}
	return 0
}

func (m *EquityDataList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type TaskFinishReq struct {
	//任务ID
	TaskId int32 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	//操作类型 1去完成 2去领取
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	//userid, 前端不用传
	ScrmUserid           string   `protobuf:"bytes,3,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskFinishReq) Reset()         { *m = TaskFinishReq{} }
func (m *TaskFinishReq) String() string { return proto.CompactTextString(m) }
func (*TaskFinishReq) ProtoMessage()    {}
func (*TaskFinishReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{11}
}

func (m *TaskFinishReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskFinishReq.Unmarshal(m, b)
}
func (m *TaskFinishReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskFinishReq.Marshal(b, m, deterministic)
}
func (m *TaskFinishReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskFinishReq.Merge(m, src)
}
func (m *TaskFinishReq) XXX_Size() int {
	return xxx_messageInfo_TaskFinishReq.Size(m)
}
func (m *TaskFinishReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskFinishReq.DiscardUnknown(m)
}

var xxx_messageInfo_TaskFinishReq proto.InternalMessageInfo

func (m *TaskFinishReq) GetTaskId() int32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *TaskFinishReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *TaskFinishReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

type UserEquityReq struct {
	//userid,前端不用传
	ScrmUserid           string   `protobuf:"bytes,1,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserEquityReq) Reset()         { *m = UserEquityReq{} }
func (m *UserEquityReq) String() string { return proto.CompactTextString(m) }
func (*UserEquityReq) ProtoMessage()    {}
func (*UserEquityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{12}
}

func (m *UserEquityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEquityReq.Unmarshal(m, b)
}
func (m *UserEquityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEquityReq.Marshal(b, m, deterministic)
}
func (m *UserEquityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEquityReq.Merge(m, src)
}
func (m *UserEquityReq) XXX_Size() int {
	return xxx_messageInfo_UserEquityReq.Size(m)
}
func (m *UserEquityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEquityReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserEquityReq proto.InternalMessageInfo

func (m *UserEquityReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

type UserEquityRes struct {
	Msg                  string            `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 []*UserEquityData `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserEquityRes) Reset()         { *m = UserEquityRes{} }
func (m *UserEquityRes) String() string { return proto.CompactTextString(m) }
func (*UserEquityRes) ProtoMessage()    {}
func (*UserEquityRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{13}
}

func (m *UserEquityRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEquityRes.Unmarshal(m, b)
}
func (m *UserEquityRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEquityRes.Marshal(b, m, deterministic)
}
func (m *UserEquityRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEquityRes.Merge(m, src)
}
func (m *UserEquityRes) XXX_Size() int {
	return xxx_messageInfo_UserEquityRes.Size(m)
}
func (m *UserEquityRes) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEquityRes.DiscardUnknown(m)
}

var xxx_messageInfo_UserEquityRes proto.InternalMessageInfo

func (m *UserEquityRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *UserEquityRes) GetData() []*UserEquityData {
	if m != nil {
		return m.Data
	}
	return nil
}

type UserEquityData struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//权益名称
	EquityName string `protobuf:"bytes,2,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//权益简介
	EquityInfo string `protobuf:"bytes,3,opt,name=equity_info,json=equityInfo,proto3" json:"equity_info"`
	//权益等级
	EquityLevel string `protobuf:"bytes,4,opt,name=equity_level,json=equityLevel,proto3" json:"equity_level"`
	//权益规则
	EquityRules string `protobuf:"bytes,5,opt,name=equity_rules,json=equityRules,proto3" json:"equity_rules"`
	//是否有赠券 1是 0否
	IsVoucher int32 `protobuf:"varint,6,opt,name=is_voucher,json=isVoucher,proto3" json:"is_voucher"`
	//图标
	Icon string `protobuf:"bytes,7,opt,name=icon,proto3" json:"icon"`
	//商城券列表
	CouponList []*EquityCoupon `protobuf:"bytes,8,rep,name=coupon_list,json=couponList,proto3" json:"coupon_list"`
	//门店券列表
	StoreVouchers        []*EquityCoupon `protobuf:"bytes,9,rep,name=store_vouchers,json=storeVouchers,proto3" json:"store_vouchers"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UserEquityData) Reset()         { *m = UserEquityData{} }
func (m *UserEquityData) String() string { return proto.CompactTextString(m) }
func (*UserEquityData) ProtoMessage()    {}
func (*UserEquityData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{14}
}

func (m *UserEquityData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEquityData.Unmarshal(m, b)
}
func (m *UserEquityData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEquityData.Marshal(b, m, deterministic)
}
func (m *UserEquityData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEquityData.Merge(m, src)
}
func (m *UserEquityData) XXX_Size() int {
	return xxx_messageInfo_UserEquityData.Size(m)
}
func (m *UserEquityData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEquityData.DiscardUnknown(m)
}

var xxx_messageInfo_UserEquityData proto.InternalMessageInfo

func (m *UserEquityData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserEquityData) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *UserEquityData) GetEquityInfo() string {
	if m != nil {
		return m.EquityInfo
	}
	return ""
}

func (m *UserEquityData) GetEquityLevel() string {
	if m != nil {
		return m.EquityLevel
	}
	return ""
}

func (m *UserEquityData) GetEquityRules() string {
	if m != nil {
		return m.EquityRules
	}
	return ""
}

func (m *UserEquityData) GetIsVoucher() int32 {
	if m != nil {
		return m.IsVoucher
	}
	return 0
}

func (m *UserEquityData) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *UserEquityData) GetCouponList() []*EquityCoupon {
	if m != nil {
		return m.CouponList
	}
	return nil
}

func (m *UserEquityData) GetStoreVouchers() []*EquityCoupon {
	if m != nil {
		return m.StoreVouchers
	}
	return nil
}

type EquityCoupon struct {
	//券ID
	CouponId string `protobuf:"bytes,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	//券名称
	CouponName string `protobuf:"bytes,2,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name"`
	//券状态 1未领取 2已领取 3已失效 4已过期 5已抢光
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	//券金额（分）
	VoucherTPrice int32 `protobuf:"varint,4,opt,name=voucher_t_price,json=voucherTPrice,proto3" json:"voucher_t_price"`
	//代金券使用时的订单限额(元)
	VoucherTLimit float32 `protobuf:"fixed32,5,opt,name=voucher_t_limit,json=voucherTLimit,proto3" json:"voucher_t_limit"`
	//适用范围 1全部 其他的是部分
	ApplicableScope int32 `protobuf:"varint,6,opt,name=applicable_scope,json=applicableScope,proto3" json:"applicable_scope"`
	//代金券模版有效期开始时间
	VoucherStartDateText string `protobuf:"bytes,7,opt,name=voucher_start_date_text,json=voucherStartDateText,proto3" json:"voucher_start_date_text"`
	//代金券模版有效期结束时间
	VoucherEndDateText string `protobuf:"bytes,8,opt,name=voucher_end_date_text,json=voucherEndDateText,proto3" json:"voucher_end_date_text"`
	//多少天内可用
	VoucherDays int64 `protobuf:"varint,9,opt,name=voucher_days,json=voucherDays,proto3" json:"voucher_days"`
	//有效期类型，type=2表示领取后after_day天，有效期period_validity天，type=1表示begin_time到end_time
	Type                 int32    `protobuf:"varint,10,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EquityCoupon) Reset()         { *m = EquityCoupon{} }
func (m *EquityCoupon) String() string { return proto.CompactTextString(m) }
func (*EquityCoupon) ProtoMessage()    {}
func (*EquityCoupon) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{15}
}

func (m *EquityCoupon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EquityCoupon.Unmarshal(m, b)
}
func (m *EquityCoupon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EquityCoupon.Marshal(b, m, deterministic)
}
func (m *EquityCoupon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EquityCoupon.Merge(m, src)
}
func (m *EquityCoupon) XXX_Size() int {
	return xxx_messageInfo_EquityCoupon.Size(m)
}
func (m *EquityCoupon) XXX_DiscardUnknown() {
	xxx_messageInfo_EquityCoupon.DiscardUnknown(m)
}

var xxx_messageInfo_EquityCoupon proto.InternalMessageInfo

func (m *EquityCoupon) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *EquityCoupon) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *EquityCoupon) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *EquityCoupon) GetVoucherTPrice() int32 {
	if m != nil {
		return m.VoucherTPrice
	}
	return 0
}

func (m *EquityCoupon) GetVoucherTLimit() float32 {
	if m != nil {
		return m.VoucherTLimit
	}
	return 0
}

func (m *EquityCoupon) GetApplicableScope() int32 {
	if m != nil {
		return m.ApplicableScope
	}
	return 0
}

func (m *EquityCoupon) GetVoucherStartDateText() string {
	if m != nil {
		return m.VoucherStartDateText
	}
	return ""
}

func (m *EquityCoupon) GetVoucherEndDateText() string {
	if m != nil {
		return m.VoucherEndDateText
	}
	return ""
}

func (m *EquityCoupon) GetVoucherDays() int64 {
	if m != nil {
		return m.VoucherDays
	}
	return 0
}

func (m *EquityCoupon) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type MemberHealthDetailReq struct {
	//类别 0全部 1=收入 2=支出 3冻结
	Type      int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	PageSize  int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageIndex int32 `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//scrm_userid,前端不用传
	ScrmUserid           string   `protobuf:"bytes,4,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberHealthDetailReq) Reset()         { *m = MemberHealthDetailReq{} }
func (m *MemberHealthDetailReq) String() string { return proto.CompactTextString(m) }
func (*MemberHealthDetailReq) ProtoMessage()    {}
func (*MemberHealthDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{16}
}

func (m *MemberHealthDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberHealthDetailReq.Unmarshal(m, b)
}
func (m *MemberHealthDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberHealthDetailReq.Marshal(b, m, deterministic)
}
func (m *MemberHealthDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberHealthDetailReq.Merge(m, src)
}
func (m *MemberHealthDetailReq) XXX_Size() int {
	return xxx_messageInfo_MemberHealthDetailReq.Size(m)
}
func (m *MemberHealthDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberHealthDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_MemberHealthDetailReq proto.InternalMessageInfo

func (m *MemberHealthDetailReq) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *MemberHealthDetailReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *MemberHealthDetailReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *MemberHealthDetailReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

func (m *MemberHealthDetailReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type MemberHealthDetailRes struct {
	Msg                  string                    `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Total                int32                     `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	Data                 []*MemberHealthDetailData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *MemberHealthDetailRes) Reset()         { *m = MemberHealthDetailRes{} }
func (m *MemberHealthDetailRes) String() string { return proto.CompactTextString(m) }
func (*MemberHealthDetailRes) ProtoMessage()    {}
func (*MemberHealthDetailRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{17}
}

func (m *MemberHealthDetailRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberHealthDetailRes.Unmarshal(m, b)
}
func (m *MemberHealthDetailRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberHealthDetailRes.Marshal(b, m, deterministic)
}
func (m *MemberHealthDetailRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberHealthDetailRes.Merge(m, src)
}
func (m *MemberHealthDetailRes) XXX_Size() int {
	return xxx_messageInfo_MemberHealthDetailRes.Size(m)
}
func (m *MemberHealthDetailRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberHealthDetailRes.DiscardUnknown(m)
}

var xxx_messageInfo_MemberHealthDetailRes proto.InternalMessageInfo

func (m *MemberHealthDetailRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MemberHealthDetailRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *MemberHealthDetailRes) GetData() []*MemberHealthDetailData {
	if m != nil {
		return m.Data
	}
	return nil
}

type MemberHealthDetailData struct {
	//类别 1=收入 2=支出 3冻结
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	//标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	//支付金额
	PayAmount float32 `protobuf:"fixed32,3,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//退款金额
	RefundAmount float32 `protobuf:"fixed32,4,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	//订单号
	OrderSn string `protobuf:"bytes,5,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//店铺名称
	ShopName string `protobuf:"bytes,6,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	//明细
	Content string `protobuf:"bytes,7,opt,name=content,proto3" json:"content"`
	//健康值
	HealthVal int32 `protobuf:"varint,8,opt,name=health_val,json=healthVal,proto3" json:"health_val"`
	//健康值类型 1线上消费 2线下消费 3做升级任务 4会员等级失效
	HealthType int32 `protobuf:"varint,9,opt,name=health_type,json=healthType,proto3" json:"health_type"`
	//生效时间
	EffectTime string `protobuf:"bytes,10,opt,name=effect_time,json=effectTime,proto3" json:"effect_time"`
	//获取时间
	CreateTime string `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//支付时间
	PayTime              string   `protobuf:"bytes,12,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberHealthDetailData) Reset()         { *m = MemberHealthDetailData{} }
func (m *MemberHealthDetailData) String() string { return proto.CompactTextString(m) }
func (*MemberHealthDetailData) ProtoMessage()    {}
func (*MemberHealthDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{18}
}

func (m *MemberHealthDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberHealthDetailData.Unmarshal(m, b)
}
func (m *MemberHealthDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberHealthDetailData.Marshal(b, m, deterministic)
}
func (m *MemberHealthDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberHealthDetailData.Merge(m, src)
}
func (m *MemberHealthDetailData) XXX_Size() int {
	return xxx_messageInfo_MemberHealthDetailData.Size(m)
}
func (m *MemberHealthDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberHealthDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_MemberHealthDetailData proto.InternalMessageInfo

func (m *MemberHealthDetailData) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *MemberHealthDetailData) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MemberHealthDetailData) GetPayAmount() float32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *MemberHealthDetailData) GetRefundAmount() float32 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *MemberHealthDetailData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *MemberHealthDetailData) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *MemberHealthDetailData) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *MemberHealthDetailData) GetHealthVal() int32 {
	if m != nil {
		return m.HealthVal
	}
	return 0
}

func (m *MemberHealthDetailData) GetHealthType() int32 {
	if m != nil {
		return m.HealthType
	}
	return 0
}

func (m *MemberHealthDetailData) GetEffectTime() string {
	if m != nil {
		return m.EffectTime
	}
	return ""
}

func (m *MemberHealthDetailData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *MemberHealthDetailData) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

type MemberHealthValReq struct {
	//用户ID，前端不用传
	ScrmUserId           string   `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberHealthValReq) Reset()         { *m = MemberHealthValReq{} }
func (m *MemberHealthValReq) String() string { return proto.CompactTextString(m) }
func (*MemberHealthValReq) ProtoMessage()    {}
func (*MemberHealthValReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{19}
}

func (m *MemberHealthValReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberHealthValReq.Unmarshal(m, b)
}
func (m *MemberHealthValReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberHealthValReq.Marshal(b, m, deterministic)
}
func (m *MemberHealthValReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberHealthValReq.Merge(m, src)
}
func (m *MemberHealthValReq) XXX_Size() int {
	return xxx_messageInfo_MemberHealthValReq.Size(m)
}
func (m *MemberHealthValReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberHealthValReq.DiscardUnknown(m)
}

var xxx_messageInfo_MemberHealthValReq proto.InternalMessageInfo

func (m *MemberHealthValReq) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

type MemberHealthValRes struct {
	//当前健康值
	HealthVal int32 `protobuf:"varint,1,opt,name=health_val,json=healthVal,proto3" json:"health_val"`
	//还差多少值可升级
	GapVal int32  `protobuf:"varint,2,opt,name=gap_val,json=gapVal,proto3" json:"gap_val"`
	Msg    string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
	//当前级别
	CurrentLevel int32 `protobuf:"varint,4,opt,name=current_level,json=currentLevel,proto3" json:"current_level"`
	//下一等级的门槛值
	NextLevelVal int32 `protobuf:"varint,5,opt,name=next_level_val,json=nextLevelVal,proto3" json:"next_level_val"`
	//当前等级的门槛值
	CurrentLevelVal int32 `protobuf:"varint,6,opt,name=current_level_val,json=currentLevelVal,proto3" json:"current_level_val"`
	//当前级别名称
	CurrentLevelName     string   `protobuf:"bytes,7,opt,name=current_level_name,json=currentLevelName,proto3" json:"current_level_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberHealthValRes) Reset()         { *m = MemberHealthValRes{} }
func (m *MemberHealthValRes) String() string { return proto.CompactTextString(m) }
func (*MemberHealthValRes) ProtoMessage()    {}
func (*MemberHealthValRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{20}
}

func (m *MemberHealthValRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberHealthValRes.Unmarshal(m, b)
}
func (m *MemberHealthValRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberHealthValRes.Marshal(b, m, deterministic)
}
func (m *MemberHealthValRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberHealthValRes.Merge(m, src)
}
func (m *MemberHealthValRes) XXX_Size() int {
	return xxx_messageInfo_MemberHealthValRes.Size(m)
}
func (m *MemberHealthValRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberHealthValRes.DiscardUnknown(m)
}

var xxx_messageInfo_MemberHealthValRes proto.InternalMessageInfo

func (m *MemberHealthValRes) GetHealthVal() int32 {
	if m != nil {
		return m.HealthVal
	}
	return 0
}

func (m *MemberHealthValRes) GetGapVal() int32 {
	if m != nil {
		return m.GapVal
	}
	return 0
}

func (m *MemberHealthValRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MemberHealthValRes) GetCurrentLevel() int32 {
	if m != nil {
		return m.CurrentLevel
	}
	return 0
}

func (m *MemberHealthValRes) GetNextLevelVal() int32 {
	if m != nil {
		return m.NextLevelVal
	}
	return 0
}

func (m *MemberHealthValRes) GetCurrentLevelVal() int32 {
	if m != nil {
		return m.CurrentLevelVal
	}
	return 0
}

func (m *MemberHealthValRes) GetCurrentLevelName() string {
	if m != nil {
		return m.CurrentLevelName
	}
	return ""
}

type MemberTaskListReq struct {
	//用户ID，前端不用传
	ScrmUserid           string   `protobuf:"bytes,1,opt,name=scrm_userid,json=scrmUserid,proto3" json:"scrm_userid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberTaskListReq) Reset()         { *m = MemberTaskListReq{} }
func (m *MemberTaskListReq) String() string { return proto.CompactTextString(m) }
func (*MemberTaskListReq) ProtoMessage()    {}
func (*MemberTaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{21}
}

func (m *MemberTaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberTaskListReq.Unmarshal(m, b)
}
func (m *MemberTaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberTaskListReq.Marshal(b, m, deterministic)
}
func (m *MemberTaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberTaskListReq.Merge(m, src)
}
func (m *MemberTaskListReq) XXX_Size() int {
	return xxx_messageInfo_MemberTaskListReq.Size(m)
}
func (m *MemberTaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberTaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_MemberTaskListReq proto.InternalMessageInfo

func (m *MemberTaskListReq) GetScrmUserid() string {
	if m != nil {
		return m.ScrmUserid
	}
	return ""
}

type MemberTaskListRes struct {
	Msg                  string              `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 *MemberTaskListData `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MemberTaskListRes) Reset()         { *m = MemberTaskListRes{} }
func (m *MemberTaskListRes) String() string { return proto.CompactTextString(m) }
func (*MemberTaskListRes) ProtoMessage()    {}
func (*MemberTaskListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{22}
}

func (m *MemberTaskListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberTaskListRes.Unmarshal(m, b)
}
func (m *MemberTaskListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberTaskListRes.Marshal(b, m, deterministic)
}
func (m *MemberTaskListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberTaskListRes.Merge(m, src)
}
func (m *MemberTaskListRes) XXX_Size() int {
	return xxx_messageInfo_MemberTaskListRes.Size(m)
}
func (m *MemberTaskListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberTaskListRes.DiscardUnknown(m)
}

var xxx_messageInfo_MemberTaskListRes proto.InternalMessageInfo

func (m *MemberTaskListRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MemberTaskListRes) GetData() *MemberTaskListData {
	if m != nil {
		return m.Data
	}
	return nil
}

type MemberTaskListData struct {
	//当前健康值
	HealthVal int32 `protobuf:"varint,1,opt,name=health_val,json=healthVal,proto3" json:"health_val"`
	//当前级别
	LevelId              int32             `protobuf:"varint,2,opt,name=level_id,json=levelId,proto3" json:"level_id"`
	TaskList             []*MemberTaskList `protobuf:"bytes,3,rep,name=task_list,json=taskList,proto3" json:"task_list"`
	LevelList            []*LevelList      `protobuf:"bytes,4,rep,name=level_list,json=levelList,proto3" json:"level_list"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MemberTaskListData) Reset()         { *m = MemberTaskListData{} }
func (m *MemberTaskListData) String() string { return proto.CompactTextString(m) }
func (*MemberTaskListData) ProtoMessage()    {}
func (*MemberTaskListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{23}
}

func (m *MemberTaskListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberTaskListData.Unmarshal(m, b)
}
func (m *MemberTaskListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberTaskListData.Marshal(b, m, deterministic)
}
func (m *MemberTaskListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberTaskListData.Merge(m, src)
}
func (m *MemberTaskListData) XXX_Size() int {
	return xxx_messageInfo_MemberTaskListData.Size(m)
}
func (m *MemberTaskListData) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberTaskListData.DiscardUnknown(m)
}

var xxx_messageInfo_MemberTaskListData proto.InternalMessageInfo

func (m *MemberTaskListData) GetHealthVal() int32 {
	if m != nil {
		return m.HealthVal
	}
	return 0
}

func (m *MemberTaskListData) GetLevelId() int32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *MemberTaskListData) GetTaskList() []*MemberTaskList {
	if m != nil {
		return m.TaskList
	}
	return nil
}

func (m *MemberTaskListData) GetLevelList() []*LevelList {
	if m != nil {
		return m.LevelList
	}
	return nil
}

type LevelList struct {
	//等级ID
	LevelId int32 `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id"`
	//等级名称
	LevelName string `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name"`
	//健康值
	HealthVal            int32    `protobuf:"varint,3,opt,name=health_val,json=healthVal,proto3" json:"health_val"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelList) Reset()         { *m = LevelList{} }
func (m *LevelList) String() string { return proto.CompactTextString(m) }
func (*LevelList) ProtoMessage()    {}
func (*LevelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{24}
}

func (m *LevelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelList.Unmarshal(m, b)
}
func (m *LevelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelList.Marshal(b, m, deterministic)
}
func (m *LevelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelList.Merge(m, src)
}
func (m *LevelList) XXX_Size() int {
	return xxx_messageInfo_LevelList.Size(m)
}
func (m *LevelList) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelList.DiscardUnknown(m)
}

var xxx_messageInfo_LevelList proto.InternalMessageInfo

func (m *LevelList) GetLevelId() int32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *LevelList) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelList) GetHealthVal() int32 {
	if m != nil {
		return m.HealthVal
	}
	return 0
}

type MemberTaskList struct {
	//任务ID
	TaskId int32 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	//任务名称
	TaskName string `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name"`
	//任务值
	TaskVal int32 `protobuf:"varint,3,opt,name=task_val,json=taskVal,proto3" json:"task_val"`
	//任务状态 0待完成 1待领取 2已领取
	Status int32 `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	//图标
	Icon                 string   `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberTaskList) Reset()         { *m = MemberTaskList{} }
func (m *MemberTaskList) String() string { return proto.CompactTextString(m) }
func (*MemberTaskList) ProtoMessage()    {}
func (*MemberTaskList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{25}
}

func (m *MemberTaskList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberTaskList.Unmarshal(m, b)
}
func (m *MemberTaskList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberTaskList.Marshal(b, m, deterministic)
}
func (m *MemberTaskList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberTaskList.Merge(m, src)
}
func (m *MemberTaskList) XXX_Size() int {
	return xxx_messageInfo_MemberTaskList.Size(m)
}
func (m *MemberTaskList) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberTaskList.DiscardUnknown(m)
}

var xxx_messageInfo_MemberTaskList proto.InternalMessageInfo

func (m *MemberTaskList) GetTaskId() int32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *MemberTaskList) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *MemberTaskList) GetTaskVal() int32 {
	if m != nil {
		return m.TaskVal
	}
	return 0
}

func (m *MemberTaskList) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MemberTaskList) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

type UserLevelEquitiesReq struct {
	// 会员等级id
	LevelId              int64    `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelEquitiesReq) Reset()         { *m = UserLevelEquitiesReq{} }
func (m *UserLevelEquitiesReq) String() string { return proto.CompactTextString(m) }
func (*UserLevelEquitiesReq) ProtoMessage()    {}
func (*UserLevelEquitiesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{26}
}

func (m *UserLevelEquitiesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelEquitiesReq.Unmarshal(m, b)
}
func (m *UserLevelEquitiesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelEquitiesReq.Marshal(b, m, deterministic)
}
func (m *UserLevelEquitiesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelEquitiesReq.Merge(m, src)
}
func (m *UserLevelEquitiesReq) XXX_Size() int {
	return xxx_messageInfo_UserLevelEquitiesReq.Size(m)
}
func (m *UserLevelEquitiesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelEquitiesReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelEquitiesReq proto.InternalMessageInfo

func (m *UserLevelEquitiesReq) GetLevelId() int64 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

type UserEquity struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 权益名称
	EquityName string `protobuf:"bytes,2,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	// 权益icon
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	// 权益简介
	EquityInfo           string   `protobuf:"bytes,4,opt,name=equity_info,json=equityInfo,proto3" json:"equity_info"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserEquity) Reset()         { *m = UserEquity{} }
func (m *UserEquity) String() string { return proto.CompactTextString(m) }
func (*UserEquity) ProtoMessage()    {}
func (*UserEquity) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{27}
}

func (m *UserEquity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEquity.Unmarshal(m, b)
}
func (m *UserEquity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEquity.Marshal(b, m, deterministic)
}
func (m *UserEquity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEquity.Merge(m, src)
}
func (m *UserEquity) XXX_Size() int {
	return xxx_messageInfo_UserEquity.Size(m)
}
func (m *UserEquity) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEquity.DiscardUnknown(m)
}

var xxx_messageInfo_UserEquity proto.InternalMessageInfo

func (m *UserEquity) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserEquity) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *UserEquity) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *UserEquity) GetEquityInfo() string {
	if m != nil {
		return m.EquityInfo
	}
	return ""
}

type UserLevelEquitiesRes struct {
	// 错误信息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	// 会员权益列表
	List                 []*UserEquity `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserLevelEquitiesRes) Reset()         { *m = UserLevelEquitiesRes{} }
func (m *UserLevelEquitiesRes) String() string { return proto.CompactTextString(m) }
func (*UserLevelEquitiesRes) ProtoMessage()    {}
func (*UserLevelEquitiesRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{28}
}

func (m *UserLevelEquitiesRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelEquitiesRes.Unmarshal(m, b)
}
func (m *UserLevelEquitiesRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelEquitiesRes.Marshal(b, m, deterministic)
}
func (m *UserLevelEquitiesRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelEquitiesRes.Merge(m, src)
}
func (m *UserLevelEquitiesRes) XXX_Size() int {
	return xxx_messageInfo_UserLevelEquitiesRes.Size(m)
}
func (m *UserLevelEquitiesRes) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelEquitiesRes.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelEquitiesRes proto.InternalMessageInfo

func (m *UserLevelEquitiesRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *UserLevelEquitiesRes) GetList() []*UserEquity {
	if m != nil {
		return m.List
	}
	return nil
}

type MemberEquityEditReq struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	EquityName string `protobuf:"bytes,2,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//图标，传JSON，例：[{"level_id":1, "icon":"https://www.aliyun.com/sss"}, {"level_id":2, "icon":"https://www.aliyun.com/ccc"}]
	EquityIcon string `protobuf:"bytes,3,opt,name=equity_icon,json=equityIcon,proto3" json:"equity_icon"`
	//权益简介
	EquityInfo string `protobuf:"bytes,4,opt,name=equity_info,json=equityInfo,proto3" json:"equity_info"`
	//详细规则
	EquityRules string `protobuf:"bytes,5,opt,name=equity_rules,json=equityRules,proto3" json:"equity_rules"`
	//1显示 0隐藏
	IsDisplay int32 `protobuf:"varint,6,opt,name=is_display,json=isDisplay,proto3" json:"is_display"`
	//是否有赠券 1是 0否
	IsVoucher            int32    `protobuf:"varint,7,opt,name=is_voucher,json=isVoucher,proto3" json:"is_voucher"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberEquityEditReq) Reset()         { *m = MemberEquityEditReq{} }
func (m *MemberEquityEditReq) String() string { return proto.CompactTextString(m) }
func (*MemberEquityEditReq) ProtoMessage()    {}
func (*MemberEquityEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{29}
}

func (m *MemberEquityEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberEquityEditReq.Unmarshal(m, b)
}
func (m *MemberEquityEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberEquityEditReq.Marshal(b, m, deterministic)
}
func (m *MemberEquityEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberEquityEditReq.Merge(m, src)
}
func (m *MemberEquityEditReq) XXX_Size() int {
	return xxx_messageInfo_MemberEquityEditReq.Size(m)
}
func (m *MemberEquityEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberEquityEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_MemberEquityEditReq proto.InternalMessageInfo

func (m *MemberEquityEditReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MemberEquityEditReq) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *MemberEquityEditReq) GetEquityIcon() string {
	if m != nil {
		return m.EquityIcon
	}
	return ""
}

func (m *MemberEquityEditReq) GetEquityInfo() string {
	if m != nil {
		return m.EquityInfo
	}
	return ""
}

func (m *MemberEquityEditReq) GetEquityRules() string {
	if m != nil {
		return m.EquityRules
	}
	return ""
}

func (m *MemberEquityEditReq) GetIsDisplay() int32 {
	if m != nil {
		return m.IsDisplay
	}
	return 0
}

func (m *MemberEquityEditReq) GetIsVoucher() int32 {
	if m != nil {
		return m.IsVoucher
	}
	return 0
}

type MemberEquityListReq struct {
	//名称
	EquityName string `protobuf:"bytes,1,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	PageIndex  int32  `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize   int32  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//1显示 2隐藏 0全部
	IsDisplay            int32    `protobuf:"varint,4,opt,name=is_display,json=isDisplay,proto3" json:"is_display"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberEquityListReq) Reset()         { *m = MemberEquityListReq{} }
func (m *MemberEquityListReq) String() string { return proto.CompactTextString(m) }
func (*MemberEquityListReq) ProtoMessage()    {}
func (*MemberEquityListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{30}
}

func (m *MemberEquityListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberEquityListReq.Unmarshal(m, b)
}
func (m *MemberEquityListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberEquityListReq.Marshal(b, m, deterministic)
}
func (m *MemberEquityListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberEquityListReq.Merge(m, src)
}
func (m *MemberEquityListReq) XXX_Size() int {
	return xxx_messageInfo_MemberEquityListReq.Size(m)
}
func (m *MemberEquityListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberEquityListReq.DiscardUnknown(m)
}

var xxx_messageInfo_MemberEquityListReq proto.InternalMessageInfo

func (m *MemberEquityListReq) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *MemberEquityListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *MemberEquityListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *MemberEquityListReq) GetIsDisplay() int32 {
	if m != nil {
		return m.IsDisplay
	}
	return 0
}

type MemberEquityListRes struct {
	Msg                  string                  `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Total                int32                   `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	Data                 []*MemberEquityListData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *MemberEquityListRes) Reset()         { *m = MemberEquityListRes{} }
func (m *MemberEquityListRes) String() string { return proto.CompactTextString(m) }
func (*MemberEquityListRes) ProtoMessage()    {}
func (*MemberEquityListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{31}
}

func (m *MemberEquityListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberEquityListRes.Unmarshal(m, b)
}
func (m *MemberEquityListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberEquityListRes.Marshal(b, m, deterministic)
}
func (m *MemberEquityListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberEquityListRes.Merge(m, src)
}
func (m *MemberEquityListRes) XXX_Size() int {
	return xxx_messageInfo_MemberEquityListRes.Size(m)
}
func (m *MemberEquityListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberEquityListRes.DiscardUnknown(m)
}

var xxx_messageInfo_MemberEquityListRes proto.InternalMessageInfo

func (m *MemberEquityListRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *MemberEquityListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *MemberEquityListRes) GetData() []*MemberEquityListData {
	if m != nil {
		return m.Data
	}
	return nil
}

type MemberEquityListData struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	EquityName string `protobuf:"bytes,2,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	//图标
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	//简介
	EquityInfo string `protobuf:"bytes,4,opt,name=equity_info,json=equityInfo,proto3" json:"equity_info"`
	//1显示  0隐藏
	IsDisplay            int32    `protobuf:"varint,5,opt,name=is_display,json=isDisplay,proto3" json:"is_display"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberEquityListData) Reset()         { *m = MemberEquityListData{} }
func (m *MemberEquityListData) String() string { return proto.CompactTextString(m) }
func (*MemberEquityListData) ProtoMessage()    {}
func (*MemberEquityListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{32}
}

func (m *MemberEquityListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberEquityListData.Unmarshal(m, b)
}
func (m *MemberEquityListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberEquityListData.Marshal(b, m, deterministic)
}
func (m *MemberEquityListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberEquityListData.Merge(m, src)
}
func (m *MemberEquityListData) XXX_Size() int {
	return xxx_messageInfo_MemberEquityListData.Size(m)
}
func (m *MemberEquityListData) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberEquityListData.DiscardUnknown(m)
}

var xxx_messageInfo_MemberEquityListData proto.InternalMessageInfo

func (m *MemberEquityListData) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MemberEquityListData) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *MemberEquityListData) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MemberEquityListData) GetEquityInfo() string {
	if m != nil {
		return m.EquityInfo
	}
	return ""
}

func (m *MemberEquityListData) GetIsDisplay() int32 {
	if m != nil {
		return m.IsDisplay
	}
	return 0
}

type TaskSaveReq struct {
	List                 []*TaskList `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *TaskSaveReq) Reset()         { *m = TaskSaveReq{} }
func (m *TaskSaveReq) String() string { return proto.CompactTextString(m) }
func (*TaskSaveReq) ProtoMessage()    {}
func (*TaskSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{33}
}

func (m *TaskSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskSaveReq.Unmarshal(m, b)
}
func (m *TaskSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskSaveReq.Marshal(b, m, deterministic)
}
func (m *TaskSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskSaveReq.Merge(m, src)
}
func (m *TaskSaveReq) XXX_Size() int {
	return xxx_messageInfo_TaskSaveReq.Size(m)
}
func (m *TaskSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_TaskSaveReq proto.InternalMessageInfo

func (m *TaskSaveReq) GetList() []*TaskList {
	if m != nil {
		return m.List
	}
	return nil
}

type TaskListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskListReq) Reset()         { *m = TaskListReq{} }
func (m *TaskListReq) String() string { return proto.CompactTextString(m) }
func (*TaskListReq) ProtoMessage()    {}
func (*TaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{34}
}

func (m *TaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskListReq.Unmarshal(m, b)
}
func (m *TaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskListReq.Marshal(b, m, deterministic)
}
func (m *TaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskListReq.Merge(m, src)
}
func (m *TaskListReq) XXX_Size() int {
	return xxx_messageInfo_TaskListReq.Size(m)
}
func (m *TaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_TaskListReq proto.InternalMessageInfo

type TaskListRes struct {
	Msg                  string      `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 []*TaskList `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *TaskListRes) Reset()         { *m = TaskListRes{} }
func (m *TaskListRes) String() string { return proto.CompactTextString(m) }
func (*TaskListRes) ProtoMessage()    {}
func (*TaskListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{35}
}

func (m *TaskListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskListRes.Unmarshal(m, b)
}
func (m *TaskListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskListRes.Marshal(b, m, deterministic)
}
func (m *TaskListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskListRes.Merge(m, src)
}
func (m *TaskListRes) XXX_Size() int {
	return xxx_messageInfo_TaskListRes.Size(m)
}
func (m *TaskListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskListRes.DiscardUnknown(m)
}

var xxx_messageInfo_TaskListRes proto.InternalMessageInfo

func (m *TaskListRes) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *TaskListRes) GetData() []*TaskList {
	if m != nil {
		return m.Data
	}
	return nil
}

type TaskList struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//任务名称
	TaskName string `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name"`
	//任务值
	TaskVal int32 `protobuf:"varint,3,opt,name=task_val,json=taskVal,proto3" json:"task_val"`
	//是否勾选 1是 0否
	IsSelect             int32    `protobuf:"varint,4,opt,name=is_select,json=isSelect,proto3" json:"is_select"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskList) Reset()         { *m = TaskList{} }
func (m *TaskList) String() string { return proto.CompactTextString(m) }
func (*TaskList) ProtoMessage()    {}
func (*TaskList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{36}
}

func (m *TaskList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskList.Unmarshal(m, b)
}
func (m *TaskList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskList.Marshal(b, m, deterministic)
}
func (m *TaskList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskList.Merge(m, src)
}
func (m *TaskList) XXX_Size() int {
	return xxx_messageInfo_TaskList.Size(m)
}
func (m *TaskList) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskList.DiscardUnknown(m)
}

var xxx_messageInfo_TaskList proto.InternalMessageInfo

func (m *TaskList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TaskList) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *TaskList) GetTaskVal() int32 {
	if m != nil {
		return m.TaskVal
	}
	return 0
}

func (m *TaskList) GetIsSelect() int32 {
	if m != nil {
		return m.IsSelect
	}
	return 0
}

type UserEditEquity struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	EquityType           int64    `protobuf:"varint,2,opt,name=equity_type,json=equityType,proto3" json:"equity_type"`
	EquityName           string   `protobuf:"bytes,3,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	IsVoucher            int64    `protobuf:"varint,4,opt,name=is_voucher,json=isVoucher,proto3" json:"is_voucher"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserEditEquity) Reset()         { *m = UserEditEquity{} }
func (m *UserEditEquity) String() string { return proto.CompactTextString(m) }
func (*UserEditEquity) ProtoMessage()    {}
func (*UserEditEquity) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{37}
}

func (m *UserEditEquity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEditEquity.Unmarshal(m, b)
}
func (m *UserEditEquity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEditEquity.Marshal(b, m, deterministic)
}
func (m *UserEditEquity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEditEquity.Merge(m, src)
}
func (m *UserEditEquity) XXX_Size() int {
	return xxx_messageInfo_UserEditEquity.Size(m)
}
func (m *UserEditEquity) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEditEquity.DiscardUnknown(m)
}

var xxx_messageInfo_UserEditEquity proto.InternalMessageInfo

func (m *UserEditEquity) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserEditEquity) GetEquityType() int64 {
	if m != nil {
		return m.EquityType
	}
	return 0
}

func (m *UserEditEquity) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

func (m *UserEditEquity) GetIsVoucher() int64 {
	if m != nil {
		return m.IsVoucher
	}
	return 0
}

type UserEditEquityListRes struct {
	Code    int64  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 可编辑权益项
	List                 []*UserEditEquity `protobuf:"bytes,3,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserEditEquityListRes) Reset()         { *m = UserEditEquityListRes{} }
func (m *UserEditEquityListRes) String() string { return proto.CompactTextString(m) }
func (*UserEditEquityListRes) ProtoMessage()    {}
func (*UserEditEquityListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{38}
}

func (m *UserEditEquityListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEditEquityListRes.Unmarshal(m, b)
}
func (m *UserEditEquityListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEditEquityListRes.Marshal(b, m, deterministic)
}
func (m *UserEditEquityListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEditEquityListRes.Merge(m, src)
}
func (m *UserEditEquityListRes) XXX_Size() int {
	return xxx_messageInfo_UserEditEquityListRes.Size(m)
}
func (m *UserEditEquityListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEditEquityListRes.DiscardUnknown(m)
}

var xxx_messageInfo_UserEditEquityListRes proto.InternalMessageInfo

func (m *UserEditEquityListRes) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserEditEquityListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserEditEquityListRes) GetList() []*UserEditEquity {
	if m != nil {
		return m.List
	}
	return nil
}

type UserLevelEditReq struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	LevelName string `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name"`
	//背景
	Background string `protobuf:"bytes,3,opt,name=background,proto3" json:"background"`
	//获利等级条件
	HealthVal int64 `protobuf:"varint,4,opt,name=health_val,json=healthVal,proto3" json:"health_val"`
	//权益id
	PrivilegeIds string `protobuf:"bytes,5,opt,name=privilege_ids,json=privilegeIds,proto3" json:"privilege_ids"`
	//商城升级券，多个,分割
	GoodsUpgradeVouchers string `protobuf:"bytes,6,opt,name=goods_upgrade_vouchers,json=goodsUpgradeVouchers,proto3" json:"goods_upgrade_vouchers"`
	//会员折扣，1.0~9.9
	MemberPrice string `protobuf:"bytes,8,opt,name=member_price,json=memberPrice,proto3" json:"member_price"`
	//到店礼券升级券，子龙那边的券，多个,分割
	StoreUpgradeVouchers string `protobuf:"bytes,9,opt,name=store_upgrade_vouchers,json=storeUpgradeVouchers,proto3" json:"store_upgrade_vouchers"`
	//到店礼券周特权券，子龙那边的券，多个,分割
	StoreWeekVouchers string `protobuf:"bytes,10,opt,name=store_week_vouchers,json=storeWeekVouchers,proto3" json:"store_week_vouchers"`
	// 个人中心背景
	CenterBackground string `protobuf:"bytes,11,opt,name=center_background,json=centerBackground,proto3" json:"center_background"`
	//图标
	LevelIcon            string   `protobuf:"bytes,12,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelEditReq) Reset()         { *m = UserLevelEditReq{} }
func (m *UserLevelEditReq) String() string { return proto.CompactTextString(m) }
func (*UserLevelEditReq) ProtoMessage()    {}
func (*UserLevelEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{39}
}

func (m *UserLevelEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelEditReq.Unmarshal(m, b)
}
func (m *UserLevelEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelEditReq.Marshal(b, m, deterministic)
}
func (m *UserLevelEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelEditReq.Merge(m, src)
}
func (m *UserLevelEditReq) XXX_Size() int {
	return xxx_messageInfo_UserLevelEditReq.Size(m)
}
func (m *UserLevelEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelEditReq proto.InternalMessageInfo

func (m *UserLevelEditReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserLevelEditReq) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *UserLevelEditReq) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *UserLevelEditReq) GetHealthVal() int64 {
	if m != nil {
		return m.HealthVal
	}
	return 0
}

func (m *UserLevelEditReq) GetPrivilegeIds() string {
	if m != nil {
		return m.PrivilegeIds
	}
	return ""
}

func (m *UserLevelEditReq) GetGoodsUpgradeVouchers() string {
	if m != nil {
		return m.GoodsUpgradeVouchers
	}
	return ""
}

func (m *UserLevelEditReq) GetMemberPrice() string {
	if m != nil {
		return m.MemberPrice
	}
	return ""
}

func (m *UserLevelEditReq) GetStoreUpgradeVouchers() string {
	if m != nil {
		return m.StoreUpgradeVouchers
	}
	return ""
}

func (m *UserLevelEditReq) GetStoreWeekVouchers() string {
	if m != nil {
		return m.StoreWeekVouchers
	}
	return ""
}

func (m *UserLevelEditReq) GetCenterBackground() string {
	if m != nil {
		return m.CenterBackground
	}
	return ""
}

func (m *UserLevelEditReq) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

type UserLevelDetailReq struct {
	//会员等级id
	UserLevelId          int64    `protobuf:"varint,1,opt,name=user_level_id,json=userLevelId,proto3" json:"user_level_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelDetailReq) Reset()         { *m = UserLevelDetailReq{} }
func (m *UserLevelDetailReq) String() string { return proto.CompactTextString(m) }
func (*UserLevelDetailReq) ProtoMessage()    {}
func (*UserLevelDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{40}
}

func (m *UserLevelDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelDetailReq.Unmarshal(m, b)
}
func (m *UserLevelDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelDetailReq.Marshal(b, m, deterministic)
}
func (m *UserLevelDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelDetailReq.Merge(m, src)
}
func (m *UserLevelDetailReq) XXX_Size() int {
	return xxx_messageInfo_UserLevelDetailReq.Size(m)
}
func (m *UserLevelDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelDetailReq proto.InternalMessageInfo

func (m *UserLevelDetailReq) GetUserLevelId() int64 {
	if m != nil {
		return m.UserLevelId
	}
	return 0
}

type LevelDetailEquity struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	EquityType           int64    `protobuf:"varint,2,opt,name=equity_type,json=equityType,proto3" json:"equity_type"`
	EquityName           string   `protobuf:"bytes,3,opt,name=equity_name,json=equityName,proto3" json:"equity_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelDetailEquity) Reset()         { *m = LevelDetailEquity{} }
func (m *LevelDetailEquity) String() string { return proto.CompactTextString(m) }
func (*LevelDetailEquity) ProtoMessage()    {}
func (*LevelDetailEquity) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{41}
}

func (m *LevelDetailEquity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelDetailEquity.Unmarshal(m, b)
}
func (m *LevelDetailEquity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelDetailEquity.Marshal(b, m, deterministic)
}
func (m *LevelDetailEquity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelDetailEquity.Merge(m, src)
}
func (m *LevelDetailEquity) XXX_Size() int {
	return xxx_messageInfo_LevelDetailEquity.Size(m)
}
func (m *LevelDetailEquity) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelDetailEquity.DiscardUnknown(m)
}

var xxx_messageInfo_LevelDetailEquity proto.InternalMessageInfo

func (m *LevelDetailEquity) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LevelDetailEquity) GetEquityType() int64 {
	if m != nil {
		return m.EquityType
	}
	return 0
}

func (m *LevelDetailEquity) GetEquityName() string {
	if m != nil {
		return m.EquityName
	}
	return ""
}

type UserLevelDetailRes struct {
	Code       int64  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message    string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	LevelId    int64  `protobuf:"varint,3,opt,name=level_id,json=levelId,proto3" json:"level_id"`
	LevelName  string `protobuf:"bytes,4,opt,name=level_name,json=levelName,proto3" json:"level_name"`
	Background string `protobuf:"bytes,5,opt,name=background,proto3" json:"background"`
	// 个人中心背景
	CenterBackground     string               `protobuf:"bytes,14,opt,name=center_background,json=centerBackground,proto3" json:"center_background"`
	HealthVal            int64                `protobuf:"varint,6,opt,name=health_val,json=healthVal,proto3" json:"health_val"`
	LevelStatus          int64                `protobuf:"varint,7,opt,name=level_status,json=levelStatus,proto3" json:"level_status"`
	PrivilegeIds         string               `protobuf:"bytes,8,opt,name=privilege_ids,json=privilegeIds,proto3" json:"privilege_ids"`
	GoodsUpgradeVouchers string               `protobuf:"bytes,9,opt,name=goods_upgrade_vouchers,json=goodsUpgradeVouchers,proto3" json:"goods_upgrade_vouchers"`
	MemberPrice          string               `protobuf:"bytes,10,opt,name=member_price,json=memberPrice,proto3" json:"member_price"`
	StoreUpgradeVouchers string               `protobuf:"bytes,11,opt,name=store_upgrade_vouchers,json=storeUpgradeVouchers,proto3" json:"store_upgrade_vouchers"`
	StoreWeekVouchers    string               `protobuf:"bytes,12,opt,name=store_week_vouchers,json=storeWeekVouchers,proto3" json:"store_week_vouchers"`
	EquityList           []*LevelDetailEquity `protobuf:"bytes,13,rep,name=equity_list,json=equityList,proto3" json:"equity_list"`
	//等级图标
	LevelIcon            string   `protobuf:"bytes,15,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelDetailRes) Reset()         { *m = UserLevelDetailRes{} }
func (m *UserLevelDetailRes) String() string { return proto.CompactTextString(m) }
func (*UserLevelDetailRes) ProtoMessage()    {}
func (*UserLevelDetailRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{42}
}

func (m *UserLevelDetailRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelDetailRes.Unmarshal(m, b)
}
func (m *UserLevelDetailRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelDetailRes.Marshal(b, m, deterministic)
}
func (m *UserLevelDetailRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelDetailRes.Merge(m, src)
}
func (m *UserLevelDetailRes) XXX_Size() int {
	return xxx_messageInfo_UserLevelDetailRes.Size(m)
}
func (m *UserLevelDetailRes) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelDetailRes.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelDetailRes proto.InternalMessageInfo

func (m *UserLevelDetailRes) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserLevelDetailRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserLevelDetailRes) GetLevelId() int64 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *UserLevelDetailRes) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *UserLevelDetailRes) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *UserLevelDetailRes) GetCenterBackground() string {
	if m != nil {
		return m.CenterBackground
	}
	return ""
}

func (m *UserLevelDetailRes) GetHealthVal() int64 {
	if m != nil {
		return m.HealthVal
	}
	return 0
}

func (m *UserLevelDetailRes) GetLevelStatus() int64 {
	if m != nil {
		return m.LevelStatus
	}
	return 0
}

func (m *UserLevelDetailRes) GetPrivilegeIds() string {
	if m != nil {
		return m.PrivilegeIds
	}
	return ""
}

func (m *UserLevelDetailRes) GetGoodsUpgradeVouchers() string {
	if m != nil {
		return m.GoodsUpgradeVouchers
	}
	return ""
}

func (m *UserLevelDetailRes) GetMemberPrice() string {
	if m != nil {
		return m.MemberPrice
	}
	return ""
}

func (m *UserLevelDetailRes) GetStoreUpgradeVouchers() string {
	if m != nil {
		return m.StoreUpgradeVouchers
	}
	return ""
}

func (m *UserLevelDetailRes) GetStoreWeekVouchers() string {
	if m != nil {
		return m.StoreWeekVouchers
	}
	return ""
}

func (m *UserLevelDetailRes) GetEquityList() []*LevelDetailEquity {
	if m != nil {
		return m.EquityList
	}
	return nil
}

func (m *UserLevelDetailRes) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

type UserLevelEquityList struct {
	LevelId              int64         `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id"`
	EquityList           []*UserEquity `protobuf:"bytes,2,rep,name=equity_list,json=equityList,proto3" json:"equity_list"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserLevelEquityList) Reset()         { *m = UserLevelEquityList{} }
func (m *UserLevelEquityList) String() string { return proto.CompactTextString(m) }
func (*UserLevelEquityList) ProtoMessage()    {}
func (*UserLevelEquityList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{43}
}

func (m *UserLevelEquityList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelEquityList.Unmarshal(m, b)
}
func (m *UserLevelEquityList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelEquityList.Marshal(b, m, deterministic)
}
func (m *UserLevelEquityList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelEquityList.Merge(m, src)
}
func (m *UserLevelEquityList) XXX_Size() int {
	return xxx_messageInfo_UserLevelEquityList.Size(m)
}
func (m *UserLevelEquityList) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelEquityList.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelEquityList proto.InternalMessageInfo

func (m *UserLevelEquityList) GetLevelId() int64 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *UserLevelEquityList) GetEquityList() []*UserEquity {
	if m != nil {
		return m.EquityList
	}
	return nil
}

type AllUserLevelEquityListRes struct {
	Code    int64  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//会员权益
	LevelEquityList      []*UserLevelEquityList `protobuf:"bytes,3,rep,name=level_equity_list,json=levelEquityList,proto3" json:"level_equity_list"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AllUserLevelEquityListRes) Reset()         { *m = AllUserLevelEquityListRes{} }
func (m *AllUserLevelEquityListRes) String() string { return proto.CompactTextString(m) }
func (*AllUserLevelEquityListRes) ProtoMessage()    {}
func (*AllUserLevelEquityListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{44}
}

func (m *AllUserLevelEquityListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllUserLevelEquityListRes.Unmarshal(m, b)
}
func (m *AllUserLevelEquityListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllUserLevelEquityListRes.Marshal(b, m, deterministic)
}
func (m *AllUserLevelEquityListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllUserLevelEquityListRes.Merge(m, src)
}
func (m *AllUserLevelEquityListRes) XXX_Size() int {
	return xxx_messageInfo_AllUserLevelEquityListRes.Size(m)
}
func (m *AllUserLevelEquityListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_AllUserLevelEquityListRes.DiscardUnknown(m)
}

var xxx_messageInfo_AllUserLevelEquityListRes proto.InternalMessageInfo

func (m *AllUserLevelEquityListRes) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AllUserLevelEquityListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AllUserLevelEquityListRes) GetLevelEquityList() []*UserLevelEquityList {
	if m != nil {
		return m.LevelEquityList
	}
	return nil
}

type AddUserHealthValReq struct {
	// 用户scrm_user_id
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 健康值类型 1线上消费 2线下消费 3做升级任务 4会员等级失效 5退款
	HealthType int64 `protobuf:"varint,2,opt,name=health_type,json=healthType,proto3" json:"health_type"`
	// 默认0,1=收入，2=支出 3=冻结
	Type int64 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	// 明细标题
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title"`
	// 明细说明
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content"`
	// 订单编号
	OrderSn string `protobuf:"bytes,6,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 支付金额
	PayAmount string `protobuf:"bytes,7,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	// 退款金额
	RefundAmount string `protobuf:"bytes,8,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	// 门店财务编码
	ShopId string `protobuf:"bytes,9,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	// 门店名称
	ShopName string `protobuf:"bytes,10,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	// 生效时间
	EffectTime string `protobuf:"bytes,11,opt,name=effect_time,json=effectTime,proto3" json:"effect_time"`
	// 支付时间
	PayTime              string   `protobuf:"bytes,12,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserHealthValReq) Reset()         { *m = AddUserHealthValReq{} }
func (m *AddUserHealthValReq) String() string { return proto.CompactTextString(m) }
func (*AddUserHealthValReq) ProtoMessage()    {}
func (*AddUserHealthValReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{45}
}

func (m *AddUserHealthValReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserHealthValReq.Unmarshal(m, b)
}
func (m *AddUserHealthValReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserHealthValReq.Marshal(b, m, deterministic)
}
func (m *AddUserHealthValReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserHealthValReq.Merge(m, src)
}
func (m *AddUserHealthValReq) XXX_Size() int {
	return xxx_messageInfo_AddUserHealthValReq.Size(m)
}
func (m *AddUserHealthValReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserHealthValReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserHealthValReq proto.InternalMessageInfo

func (m *AddUserHealthValReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *AddUserHealthValReq) GetHealthType() int64 {
	if m != nil {
		return m.HealthType
	}
	return 0
}

func (m *AddUserHealthValReq) GetType() int64 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AddUserHealthValReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AddUserHealthValReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AddUserHealthValReq) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *AddUserHealthValReq) GetPayAmount() string {
	if m != nil {
		return m.PayAmount
	}
	return ""
}

func (m *AddUserHealthValReq) GetRefundAmount() string {
	if m != nil {
		return m.RefundAmount
	}
	return ""
}

func (m *AddUserHealthValReq) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *AddUserHealthValReq) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *AddUserHealthValReq) GetEffectTime() string {
	if m != nil {
		return m.EffectTime
	}
	return ""
}

func (m *AddUserHealthValReq) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

type NotificationState struct {
	// 订阅类型：all 所有通知总开关、user_level 用户会员等级通知、integral 积分通知、voucher 优惠券通知
	NotificationType string `protobuf:"bytes,1,opt,name=notification_type,json=notificationType,proto3" json:"notification_type"`
	// 是否开启，1开启，0关闭
	State                int64    `protobuf:"varint,2,opt,name=state,proto3" json:"state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotificationState) Reset()         { *m = NotificationState{} }
func (m *NotificationState) String() string { return proto.CompactTextString(m) }
func (*NotificationState) ProtoMessage()    {}
func (*NotificationState) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{46}
}

func (m *NotificationState) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotificationState.Unmarshal(m, b)
}
func (m *NotificationState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotificationState.Marshal(b, m, deterministic)
}
func (m *NotificationState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotificationState.Merge(m, src)
}
func (m *NotificationState) XXX_Size() int {
	return xxx_messageInfo_NotificationState.Size(m)
}
func (m *NotificationState) XXX_DiscardUnknown() {
	xxx_messageInfo_NotificationState.DiscardUnknown(m)
}

var xxx_messageInfo_NotificationState proto.InternalMessageInfo

func (m *NotificationState) GetNotificationType() string {
	if m != nil {
		return m.NotificationType
	}
	return ""
}

func (m *NotificationState) GetState() int64 {
	if m != nil {
		return m.State
	}
	return 0
}

type SetNotificationStateReq struct {
	//用户id
	ScrmUserId string `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	// all 所有通知总开关，1开启，2关闭
	All int64 `protobuf:"varint,2,opt,name=all,proto3" json:"all"`
	// user_level 用户会员等级通知，1开启，2关闭
	UserLevel int64 `protobuf:"varint,3,opt,name=user_level,json=userLevel,proto3" json:"user_level"`
	// integral 积分通知，1开启，2关闭
	Integral int64 `protobuf:"varint,4,opt,name=integral,proto3" json:"integral"`
	// voucher 优惠券通知，1开启，2关闭
	Voucher int64 `protobuf:"varint,5,opt,name=voucher,proto3" json:"voucher"`
	//register 预约挂号通知，1开启，2关闭
	Register int64 `protobuf:"varint,7,opt,name=register,proto3" json:"register"`
	// queuing 医院挂号排队通知，1开启，2关闭
	Queuing int64 `protobuf:"varint,6,opt,name=queuing,proto3" json:"queuing"`
	// 核销码使用
	VrCodeUse int64 `protobuf:"varint,9,opt,name=vr_code_use,json=vrCodeUse,proto3" json:"vr_code_use"`
	// 核销码过期提醒
	VrCodeExpire int64 `protobuf:"varint,10,opt,name=vr_code_expire,json=vrCodeExpire,proto3" json:"vr_code_expire"`
	// 会员权益过期通知
	VipCardExpire int64 `protobuf:"varint,11,opt,name=vip_card_expire,json=vipCardExpire,proto3" json:"vip_card_expire"`
	//小程序主体：1：默认，2-极宠家
	OrgId                int32    `protobuf:"varint,12,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNotificationStateReq) Reset()         { *m = SetNotificationStateReq{} }
func (m *SetNotificationStateReq) String() string { return proto.CompactTextString(m) }
func (*SetNotificationStateReq) ProtoMessage()    {}
func (*SetNotificationStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{47}
}

func (m *SetNotificationStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNotificationStateReq.Unmarshal(m, b)
}
func (m *SetNotificationStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNotificationStateReq.Marshal(b, m, deterministic)
}
func (m *SetNotificationStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNotificationStateReq.Merge(m, src)
}
func (m *SetNotificationStateReq) XXX_Size() int {
	return xxx_messageInfo_SetNotificationStateReq.Size(m)
}
func (m *SetNotificationStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNotificationStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetNotificationStateReq proto.InternalMessageInfo

func (m *SetNotificationStateReq) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *SetNotificationStateReq) GetAll() int64 {
	if m != nil {
		return m.All
	}
	return 0
}

func (m *SetNotificationStateReq) GetUserLevel() int64 {
	if m != nil {
		return m.UserLevel
	}
	return 0
}

func (m *SetNotificationStateReq) GetIntegral() int64 {
	if m != nil {
		return m.Integral
	}
	return 0
}

func (m *SetNotificationStateReq) GetVoucher() int64 {
	if m != nil {
		return m.Voucher
	}
	return 0
}

func (m *SetNotificationStateReq) GetRegister() int64 {
	if m != nil {
		return m.Register
	}
	return 0
}

func (m *SetNotificationStateReq) GetQueuing() int64 {
	if m != nil {
		return m.Queuing
	}
	return 0
}

func (m *SetNotificationStateReq) GetVrCodeUse() int64 {
	if m != nil {
		return m.VrCodeUse
	}
	return 0
}

func (m *SetNotificationStateReq) GetVrCodeExpire() int64 {
	if m != nil {
		return m.VrCodeExpire
	}
	return 0
}

func (m *SetNotificationStateReq) GetVipCardExpire() int64 {
	if m != nil {
		return m.VipCardExpire
	}
	return 0
}

func (m *SetNotificationStateReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type NotificationMessage struct {
	//微信订阅消息模板
	TemplateId string `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	//订阅类型，1订阅，0取消订阅
	Type                 int64    `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotificationMessage) Reset()         { *m = NotificationMessage{} }
func (m *NotificationMessage) String() string { return proto.CompactTextString(m) }
func (*NotificationMessage) ProtoMessage()    {}
func (*NotificationMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{48}
}

func (m *NotificationMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotificationMessage.Unmarshal(m, b)
}
func (m *NotificationMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotificationMessage.Marshal(b, m, deterministic)
}
func (m *NotificationMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotificationMessage.Merge(m, src)
}
func (m *NotificationMessage) XXX_Size() int {
	return xxx_messageInfo_NotificationMessage.Size(m)
}
func (m *NotificationMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_NotificationMessage.DiscardUnknown(m)
}

var xxx_messageInfo_NotificationMessage proto.InternalMessageInfo

func (m *NotificationMessage) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *NotificationMessage) GetType() int64 {
	if m != nil {
		return m.Type
	}
	return 0
}

type AddNotificationMessageReq struct {
	ScrmUserId string `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//订阅类型，数组
	Messages             []*NotificationMessage `protobuf:"bytes,2,rep,name=messages,proto3" json:"messages"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddNotificationMessageReq) Reset()         { *m = AddNotificationMessageReq{} }
func (m *AddNotificationMessageReq) String() string { return proto.CompactTextString(m) }
func (*AddNotificationMessageReq) ProtoMessage()    {}
func (*AddNotificationMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{49}
}

func (m *AddNotificationMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNotificationMessageReq.Unmarshal(m, b)
}
func (m *AddNotificationMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNotificationMessageReq.Marshal(b, m, deterministic)
}
func (m *AddNotificationMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNotificationMessageReq.Merge(m, src)
}
func (m *AddNotificationMessageReq) XXX_Size() int {
	return xxx_messageInfo_AddNotificationMessageReq.Size(m)
}
func (m *AddNotificationMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNotificationMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddNotificationMessageReq proto.InternalMessageInfo

func (m *AddNotificationMessageReq) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *AddNotificationMessageReq) GetMessages() []*NotificationMessage {
	if m != nil {
		return m.Messages
	}
	return nil
}

type CanSendWechatSubscribeMessageReq struct {
	//用户id
	ScrmUserId string `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//订阅类型，user_level会员等级、integral积分、voucher优惠券
	SubscribeType string `protobuf:"bytes,2,opt,name=subscribe_type,json=subscribeType,proto3" json:"subscribe_type"`
	//微信消息模板id
	TemplateId           string   `protobuf:"bytes,3,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CanSendWechatSubscribeMessageReq) Reset()         { *m = CanSendWechatSubscribeMessageReq{} }
func (m *CanSendWechatSubscribeMessageReq) String() string { return proto.CompactTextString(m) }
func (*CanSendWechatSubscribeMessageReq) ProtoMessage()    {}
func (*CanSendWechatSubscribeMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{50}
}

func (m *CanSendWechatSubscribeMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CanSendWechatSubscribeMessageReq.Unmarshal(m, b)
}
func (m *CanSendWechatSubscribeMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CanSendWechatSubscribeMessageReq.Marshal(b, m, deterministic)
}
func (m *CanSendWechatSubscribeMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CanSendWechatSubscribeMessageReq.Merge(m, src)
}
func (m *CanSendWechatSubscribeMessageReq) XXX_Size() int {
	return xxx_messageInfo_CanSendWechatSubscribeMessageReq.Size(m)
}
func (m *CanSendWechatSubscribeMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CanSendWechatSubscribeMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_CanSendWechatSubscribeMessageReq proto.InternalMessageInfo

func (m *CanSendWechatSubscribeMessageReq) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *CanSendWechatSubscribeMessageReq) GetSubscribeType() string {
	if m != nil {
		return m.SubscribeType
	}
	return ""
}

func (m *CanSendWechatSubscribeMessageReq) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

type UserNotificationReq struct {
	//用户id
	ScrmUserId string `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//小程序主体：1：默认，2-极宠家
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserNotificationReq) Reset()         { *m = UserNotificationReq{} }
func (m *UserNotificationReq) String() string { return proto.CompactTextString(m) }
func (*UserNotificationReq) ProtoMessage()    {}
func (*UserNotificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{51}
}

func (m *UserNotificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNotificationReq.Unmarshal(m, b)
}
func (m *UserNotificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNotificationReq.Marshal(b, m, deterministic)
}
func (m *UserNotificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNotificationReq.Merge(m, src)
}
func (m *UserNotificationReq) XXX_Size() int {
	return xxx_messageInfo_UserNotificationReq.Size(m)
}
func (m *UserNotificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNotificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserNotificationReq proto.InternalMessageInfo

func (m *UserNotificationReq) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *UserNotificationReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type UserNotificationRes struct {
	Code    int64  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//全部订阅通知开关，1开启 !=1关闭
	All int64 `protobuf:"varint,3,opt,name=all,proto3" json:"all"`
	//会员等级通知
	UserLevel int64 `protobuf:"varint,4,opt,name=user_level,json=userLevel,proto3" json:"user_level"`
	//用户积分通知
	Integral int64 `protobuf:"varint,5,opt,name=integral,proto3" json:"integral"`
	//优惠券通知
	Voucher int64 `protobuf:"varint,6,opt,name=voucher,proto3" json:"voucher"`
	//预约挂号通知
	Register int64 `protobuf:"varint,7,opt,name=register,proto3" json:"register"`
	//医院挂号排队通知
	Queuing int64 `protobuf:"varint,8,opt,name=queuing,proto3" json:"queuing"`
	//子龙美容服务通知，1开启、2关闭
	Service int64 `protobuf:"varint,9,opt,name=service,proto3" json:"service"`
	// 核销码使用
	VrCodeUse int64 `protobuf:"varint,10,opt,name=vr_code_use,json=vrCodeUse,proto3" json:"vr_code_use"`
	// 核销码过期提醒
	VrCodeExpire int64 `protobuf:"varint,11,opt,name=vr_code_expire,json=vrCodeExpire,proto3" json:"vr_code_expire"`
	//会员权益过期通知
	VipCardExpire        int64    `protobuf:"varint,12,opt,name=vip_card_expire,json=vipCardExpire,proto3" json:"vip_card_expire"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserNotificationRes) Reset()         { *m = UserNotificationRes{} }
func (m *UserNotificationRes) String() string { return proto.CompactTextString(m) }
func (*UserNotificationRes) ProtoMessage()    {}
func (*UserNotificationRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{52}
}

func (m *UserNotificationRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNotificationRes.Unmarshal(m, b)
}
func (m *UserNotificationRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNotificationRes.Marshal(b, m, deterministic)
}
func (m *UserNotificationRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNotificationRes.Merge(m, src)
}
func (m *UserNotificationRes) XXX_Size() int {
	return xxx_messageInfo_UserNotificationRes.Size(m)
}
func (m *UserNotificationRes) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNotificationRes.DiscardUnknown(m)
}

var xxx_messageInfo_UserNotificationRes proto.InternalMessageInfo

func (m *UserNotificationRes) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserNotificationRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserNotificationRes) GetAll() int64 {
	if m != nil {
		return m.All
	}
	return 0
}

func (m *UserNotificationRes) GetUserLevel() int64 {
	if m != nil {
		return m.UserLevel
	}
	return 0
}

func (m *UserNotificationRes) GetIntegral() int64 {
	if m != nil {
		return m.Integral
	}
	return 0
}

func (m *UserNotificationRes) GetVoucher() int64 {
	if m != nil {
		return m.Voucher
	}
	return 0
}

func (m *UserNotificationRes) GetRegister() int64 {
	if m != nil {
		return m.Register
	}
	return 0
}

func (m *UserNotificationRes) GetQueuing() int64 {
	if m != nil {
		return m.Queuing
	}
	return 0
}

func (m *UserNotificationRes) GetService() int64 {
	if m != nil {
		return m.Service
	}
	return 0
}

func (m *UserNotificationRes) GetVrCodeUse() int64 {
	if m != nil {
		return m.VrCodeUse
	}
	return 0
}

func (m *UserNotificationRes) GetVrCodeExpire() int64 {
	if m != nil {
		return m.VrCodeExpire
	}
	return 0
}

func (m *UserNotificationRes) GetVipCardExpire() int64 {
	if m != nil {
		return m.VipCardExpire
	}
	return 0
}

type MessageValue struct {
	//消息参数数据类型，int、string、float
	Type                 string   `protobuf:"bytes,1,opt,name=type,proto3" json:"type"`
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageValue) Reset()         { *m = MessageValue{} }
func (m *MessageValue) String() string { return proto.CompactTextString(m) }
func (*MessageValue) ProtoMessage()    {}
func (*MessageValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{53}
}

func (m *MessageValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageValue.Unmarshal(m, b)
}
func (m *MessageValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageValue.Marshal(b, m, deterministic)
}
func (m *MessageValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageValue.Merge(m, src)
}
func (m *MessageValue) XXX_Size() int {
	return xxx_messageInfo_MessageValue.Size(m)
}
func (m *MessageValue) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageValue.DiscardUnknown(m)
}

var xxx_messageInfo_MessageValue proto.InternalMessageInfo

func (m *MessageValue) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *MessageValue) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type SendSubscribeMessageReq struct {
	//用户id
	ScrmUserId string `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//订阅类型：user_level会员等级通知、integral积分通知、voucher优惠券通知、queuing医院挂号排队通知
	SubscribeType string `protobuf:"bytes,2,opt,name=subscribe_type,json=subscribeType,proto3" json:"subscribe_type"`
	//微信消息key：register-queuing预约队列
	TemplateKey string `protobuf:"bytes,3,opt,name=template_key,json=templateKey,proto3" json:"template_key"`
	//微信订阅消息参数值
	Values []*MessageValue `protobuf:"bytes,4,rep,name=values,proto3" json:"values"`
	//微信页面跳转参数
	PageParams string `protobuf:"bytes,5,opt,name=page_params,json=pageParams,proto3" json:"page_params"`
	// 指定页面
	Page string `protobuf:"bytes,6,opt,name=page,proto3" json:"page"`
	//小程序主体：1：默认，2-极宠家
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendSubscribeMessageReq) Reset()         { *m = SendSubscribeMessageReq{} }
func (m *SendSubscribeMessageReq) String() string { return proto.CompactTextString(m) }
func (*SendSubscribeMessageReq) ProtoMessage()    {}
func (*SendSubscribeMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{54}
}

func (m *SendSubscribeMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendSubscribeMessageReq.Unmarshal(m, b)
}
func (m *SendSubscribeMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendSubscribeMessageReq.Marshal(b, m, deterministic)
}
func (m *SendSubscribeMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendSubscribeMessageReq.Merge(m, src)
}
func (m *SendSubscribeMessageReq) XXX_Size() int {
	return xxx_messageInfo_SendSubscribeMessageReq.Size(m)
}
func (m *SendSubscribeMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendSubscribeMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendSubscribeMessageReq proto.InternalMessageInfo

func (m *SendSubscribeMessageReq) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *SendSubscribeMessageReq) GetSubscribeType() string {
	if m != nil {
		return m.SubscribeType
	}
	return ""
}

func (m *SendSubscribeMessageReq) GetTemplateKey() string {
	if m != nil {
		return m.TemplateKey
	}
	return ""
}

func (m *SendSubscribeMessageReq) GetValues() []*MessageValue {
	if m != nil {
		return m.Values
	}
	return nil
}

func (m *SendSubscribeMessageReq) GetPageParams() string {
	if m != nil {
		return m.PageParams
	}
	return ""
}

func (m *SendSubscribeMessageReq) GetPage() string {
	if m != nil {
		return m.Page
	}
	return ""
}

func (m *SendSubscribeMessageReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type UserLevelSetReq struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//状态 0停用 1启用
	Status               int64    `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelSetReq) Reset()         { *m = UserLevelSetReq{} }
func (m *UserLevelSetReq) String() string { return proto.CompactTextString(m) }
func (*UserLevelSetReq) ProtoMessage()    {}
func (*UserLevelSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{55}
}

func (m *UserLevelSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelSetReq.Unmarshal(m, b)
}
func (m *UserLevelSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelSetReq.Marshal(b, m, deterministic)
}
func (m *UserLevelSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelSetReq.Merge(m, src)
}
func (m *UserLevelSetReq) XXX_Size() int {
	return xxx_messageInfo_UserLevelSetReq.Size(m)
}
func (m *UserLevelSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelSetReq proto.InternalMessageInfo

func (m *UserLevelSetReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserLevelSetReq) GetStatus() int64 {
	if m != nil {
		return m.Status
	}
	return 0
}

type UserLevelListReq struct {
	PageIndex            int64    `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int64    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelListReq) Reset()         { *m = UserLevelListReq{} }
func (m *UserLevelListReq) String() string { return proto.CompactTextString(m) }
func (*UserLevelListReq) ProtoMessage()    {}
func (*UserLevelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{56}
}

func (m *UserLevelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelListReq.Unmarshal(m, b)
}
func (m *UserLevelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelListReq.Marshal(b, m, deterministic)
}
func (m *UserLevelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelListReq.Merge(m, src)
}
func (m *UserLevelListReq) XXX_Size() int {
	return xxx_messageInfo_UserLevelListReq.Size(m)
}
func (m *UserLevelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelListReq proto.InternalMessageInfo

func (m *UserLevelListReq) GetPageIndex() int64 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *UserLevelListReq) GetPageSize() int64 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type UserLevelListRes struct {
	Code    int64  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Total   int64  `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	//会员等级状态，0无状态，可以编辑会员等级、1更新了会员规则、2会员等级更新中
	LevelState int64 `protobuf:"varint,4,opt,name=level_state,json=levelState,proto3" json:"level_state"`
	//会员等级列表
	List                 []*UserLevelList `protobuf:"bytes,5,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserLevelListRes) Reset()         { *m = UserLevelListRes{} }
func (m *UserLevelListRes) String() string { return proto.CompactTextString(m) }
func (*UserLevelListRes) ProtoMessage()    {}
func (*UserLevelListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{57}
}

func (m *UserLevelListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelListRes.Unmarshal(m, b)
}
func (m *UserLevelListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelListRes.Marshal(b, m, deterministic)
}
func (m *UserLevelListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelListRes.Merge(m, src)
}
func (m *UserLevelListRes) XXX_Size() int {
	return xxx_messageInfo_UserLevelListRes.Size(m)
}
func (m *UserLevelListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelListRes.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelListRes proto.InternalMessageInfo

func (m *UserLevelListRes) GetCode() int64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserLevelListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserLevelListRes) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *UserLevelListRes) GetLevelState() int64 {
	if m != nil {
		return m.LevelState
	}
	return 0
}

func (m *UserLevelListRes) GetList() []*UserLevelList {
	if m != nil {
		return m.List
	}
	return nil
}

type UserLevelList struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//会员等级
	LevelId int64 `protobuf:"varint,2,opt,name=level_id,json=levelId,proto3" json:"level_id"`
	//名称
	LevelName string `protobuf:"bytes,3,opt,name=level_name,json=levelName,proto3" json:"level_name"`
	//获利等级条件
	HealthVal int64 `protobuf:"varint,4,opt,name=health_val,json=healthVal,proto3" json:"health_val"`
	//卡面
	Background string `protobuf:"bytes,5,opt,name=background,proto3" json:"background"`
	//状态 0停用 1启用
	LevelStatus int64 `protobuf:"varint,6,opt,name=level_status,json=levelStatus,proto3" json:"level_status"`
	//等级图标
	LevelIcon            string   `protobuf:"bytes,7,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelList) Reset()         { *m = UserLevelList{} }
func (m *UserLevelList) String() string { return proto.CompactTextString(m) }
func (*UserLevelList) ProtoMessage()    {}
func (*UserLevelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{58}
}

func (m *UserLevelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelList.Unmarshal(m, b)
}
func (m *UserLevelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelList.Marshal(b, m, deterministic)
}
func (m *UserLevelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelList.Merge(m, src)
}
func (m *UserLevelList) XXX_Size() int {
	return xxx_messageInfo_UserLevelList.Size(m)
}
func (m *UserLevelList) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelList.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelList proto.InternalMessageInfo

func (m *UserLevelList) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserLevelList) GetLevelId() int64 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *UserLevelList) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *UserLevelList) GetHealthVal() int64 {
	if m != nil {
		return m.HealthVal
	}
	return 0
}

func (m *UserLevelList) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *UserLevelList) GetLevelStatus() int64 {
	if m != nil {
		return m.LevelStatus
	}
	return 0
}

func (m *UserLevelList) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

type UserRequestLogRequest struct {
	To                   string   `protobuf:"bytes,1,opt,name=to,proto3" json:"to"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRequestLogRequest) Reset()         { *m = UserRequestLogRequest{} }
func (m *UserRequestLogRequest) String() string { return proto.CompactTextString(m) }
func (*UserRequestLogRequest) ProtoMessage()    {}
func (*UserRequestLogRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{59}
}

func (m *UserRequestLogRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRequestLogRequest.Unmarshal(m, b)
}
func (m *UserRequestLogRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRequestLogRequest.Marshal(b, m, deterministic)
}
func (m *UserRequestLogRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRequestLogRequest.Merge(m, src)
}
func (m *UserRequestLogRequest) XXX_Size() int {
	return xxx_messageInfo_UserRequestLogRequest.Size(m)
}
func (m *UserRequestLogRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRequestLogRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserRequestLogRequest proto.InternalMessageInfo

func (m *UserRequestLogRequest) GetTo() string {
	if m != nil {
		return m.To
	}
	return ""
}

func (m *UserRequestLogRequest) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type UserRequestLogResonse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRequestLogResonse) Reset()         { *m = UserRequestLogResonse{} }
func (m *UserRequestLogResonse) String() string { return proto.CompactTextString(m) }
func (*UserRequestLogResonse) ProtoMessage()    {}
func (*UserRequestLogResonse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{60}
}

func (m *UserRequestLogResonse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRequestLogResonse.Unmarshal(m, b)
}
func (m *UserRequestLogResonse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRequestLogResonse.Marshal(b, m, deterministic)
}
func (m *UserRequestLogResonse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRequestLogResonse.Merge(m, src)
}
func (m *UserRequestLogResonse) XXX_Size() int {
	return xxx_messageInfo_UserRequestLogResonse.Size(m)
}
func (m *UserRequestLogResonse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRequestLogResonse.DiscardUnknown(m)
}

var xxx_messageInfo_UserRequestLogResonse proto.InternalMessageInfo

func (m *UserRequestLogResonse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UserRequestLogResonse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UserRequestLogResonse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type ManualQueryUsuallyRecordProductRequest struct {
	//开始时间
	StartTime string `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime              string   `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualQueryUsuallyRecordProductRequest) Reset() {
	*m = ManualQueryUsuallyRecordProductRequest{}
}
func (m *ManualQueryUsuallyRecordProductRequest) String() string { return proto.CompactTextString(m) }
func (*ManualQueryUsuallyRecordProductRequest) ProtoMessage()    {}
func (*ManualQueryUsuallyRecordProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{61}
}

func (m *ManualQueryUsuallyRecordProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualQueryUsuallyRecordProductRequest.Unmarshal(m, b)
}
func (m *ManualQueryUsuallyRecordProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualQueryUsuallyRecordProductRequest.Marshal(b, m, deterministic)
}
func (m *ManualQueryUsuallyRecordProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualQueryUsuallyRecordProductRequest.Merge(m, src)
}
func (m *ManualQueryUsuallyRecordProductRequest) XXX_Size() int {
	return xxx_messageInfo_ManualQueryUsuallyRecordProductRequest.Size(m)
}
func (m *ManualQueryUsuallyRecordProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualQueryUsuallyRecordProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ManualQueryUsuallyRecordProductRequest proto.InternalMessageInfo

func (m *ManualQueryUsuallyRecordProductRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *ManualQueryUsuallyRecordProductRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type ManualQueryUsuallyRecordProductResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualQueryUsuallyRecordProductResponse) Reset() {
	*m = ManualQueryUsuallyRecordProductResponse{}
}
func (m *ManualQueryUsuallyRecordProductResponse) String() string { return proto.CompactTextString(m) }
func (*ManualQueryUsuallyRecordProductResponse) ProtoMessage()    {}
func (*ManualQueryUsuallyRecordProductResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{62}
}

func (m *ManualQueryUsuallyRecordProductResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualQueryUsuallyRecordProductResponse.Unmarshal(m, b)
}
func (m *ManualQueryUsuallyRecordProductResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualQueryUsuallyRecordProductResponse.Marshal(b, m, deterministic)
}
func (m *ManualQueryUsuallyRecordProductResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualQueryUsuallyRecordProductResponse.Merge(m, src)
}
func (m *ManualQueryUsuallyRecordProductResponse) XXX_Size() int {
	return xxx_messageInfo_ManualQueryUsuallyRecordProductResponse.Size(m)
}
func (m *ManualQueryUsuallyRecordProductResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualQueryUsuallyRecordProductResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ManualQueryUsuallyRecordProductResponse proto.InternalMessageInfo

func (m *ManualQueryUsuallyRecordProductResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ManualQueryUsuallyRecordProductResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type AddressInfoRequest struct {
	//姓名
	TrueName string `protobuf:"bytes,1,opt,name=true_name,json=trueName,proto3" json:"true_name"`
	//城市ID
	CityId string `protobuf:"bytes,2,opt,name=city_id,json=cityId,proto3" json:"city_id"`
	//区域ID
	AreaId string `protobuf:"bytes,3,opt,name=area_id,json=areaId,proto3" json:"area_id"`
	//区域信息
	AreaInfo string `protobuf:"bytes,4,opt,name=area_info,json=areaInfo,proto3" json:"area_info"`
	//地址
	Address string `protobuf:"bytes,5,opt,name=address,proto3" json:"address"`
	//地图地址说明
	AddressDesc string `protobuf:"bytes,6,opt,name=address_desc,json=addressDesc,proto3" json:"address_desc"`
	//电话
	TelPhone string `protobuf:"bytes,7,opt,name=tel_phone,json=telPhone,proto3" json:"tel_phone"`
	//手机
	MobPhone string `protobuf:"bytes,8,opt,name=mob_phone,json=mobPhone,proto3" json:"mob_phone"`
	//是否默认地址  1是  0否
	IsDefault string `protobuf:"bytes,9,opt,name=is_default,json=isDefault,proto3" json:"is_default"`
	//chain_id
	ChainId string `protobuf:"bytes,10,opt,name=chain_id,json=chainId,proto3" json:"chain_id"`
	//adcode
	Adcode string `protobuf:"bytes,11,opt,name=adcode,proto3" json:"adcode"`
	//tx_lat
	TxLat float32 `protobuf:"fixed32,12,opt,name=tx_lat,json=txLat,proto3" json:"tx_lat"`
	//tx_lng
	TxLng float32 `protobuf:"fixed32,13,opt,name=tx_lng,json=txLng,proto3" json:"tx_lng"`
	//house_info
	HouseInfo string `protobuf:"bytes,14,opt,name=house_info,json=houseInfo,proto3" json:"house_info"`
	//地址ID
	AddressId string `protobuf:"bytes,15,opt,name=address_id,json=addressId,proto3" json:"address_id"`
	//自提点ID
	DlypId string `protobuf:"bytes,16,opt,name=dlyp_id,json=dlypId,proto3" json:"dlyp_id"`
	//城市编码
	AreaAdcode string `protobuf:"bytes,17,opt,name=area_adcode,json=areaAdcode,proto3" json:"area_adcode"`
	//地图地址
	MapAddress string `protobuf:"bytes,18,opt,name=map_address,json=mapAddress,proto3" json:"map_address"`
	//ScrmuserId(无需前端传)
	ScrmUserId string `protobuf:"bytes,19,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	//主体id
	OrgId                int32    `protobuf:"varint,20,opt,name=OrgId,proto3" json:"OrgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddressInfoRequest) Reset()         { *m = AddressInfoRequest{} }
func (m *AddressInfoRequest) String() string { return proto.CompactTextString(m) }
func (*AddressInfoRequest) ProtoMessage()    {}
func (*AddressInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{63}
}

func (m *AddressInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddressInfoRequest.Unmarshal(m, b)
}
func (m *AddressInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddressInfoRequest.Marshal(b, m, deterministic)
}
func (m *AddressInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddressInfoRequest.Merge(m, src)
}
func (m *AddressInfoRequest) XXX_Size() int {
	return xxx_messageInfo_AddressInfoRequest.Size(m)
}
func (m *AddressInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddressInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddressInfoRequest proto.InternalMessageInfo

func (m *AddressInfoRequest) GetTrueName() string {
	if m != nil {
		return m.TrueName
	}
	return ""
}

func (m *AddressInfoRequest) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *AddressInfoRequest) GetAreaId() string {
	if m != nil {
		return m.AreaId
	}
	return ""
}

func (m *AddressInfoRequest) GetAreaInfo() string {
	if m != nil {
		return m.AreaInfo
	}
	return ""
}

func (m *AddressInfoRequest) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *AddressInfoRequest) GetAddressDesc() string {
	if m != nil {
		return m.AddressDesc
	}
	return ""
}

func (m *AddressInfoRequest) GetTelPhone() string {
	if m != nil {
		return m.TelPhone
	}
	return ""
}

func (m *AddressInfoRequest) GetMobPhone() string {
	if m != nil {
		return m.MobPhone
	}
	return ""
}

func (m *AddressInfoRequest) GetIsDefault() string {
	if m != nil {
		return m.IsDefault
	}
	return ""
}

func (m *AddressInfoRequest) GetChainId() string {
	if m != nil {
		return m.ChainId
	}
	return ""
}

func (m *AddressInfoRequest) GetAdcode() string {
	if m != nil {
		return m.Adcode
	}
	return ""
}

func (m *AddressInfoRequest) GetTxLat() float32 {
	if m != nil {
		return m.TxLat
	}
	return 0
}

func (m *AddressInfoRequest) GetTxLng() float32 {
	if m != nil {
		return m.TxLng
	}
	return 0
}

func (m *AddressInfoRequest) GetHouseInfo() string {
	if m != nil {
		return m.HouseInfo
	}
	return ""
}

func (m *AddressInfoRequest) GetAddressId() string {
	if m != nil {
		return m.AddressId
	}
	return ""
}

func (m *AddressInfoRequest) GetDlypId() string {
	if m != nil {
		return m.DlypId
	}
	return ""
}

func (m *AddressInfoRequest) GetAreaAdcode() string {
	if m != nil {
		return m.AreaAdcode
	}
	return ""
}

func (m *AddressInfoRequest) GetMapAddress() string {
	if m != nil {
		return m.MapAddress
	}
	return ""
}

func (m *AddressInfoRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *AddressInfoRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GetAddressInfoRequest struct {
	AddressId            int32    `protobuf:"varint,1,opt,name=address_id,json=addressId,proto3" json:"address_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAddressInfoRequest) Reset()         { *m = GetAddressInfoRequest{} }
func (m *GetAddressInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetAddressInfoRequest) ProtoMessage()    {}
func (*GetAddressInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{64}
}

func (m *GetAddressInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAddressInfoRequest.Unmarshal(m, b)
}
func (m *GetAddressInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAddressInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetAddressInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAddressInfoRequest.Merge(m, src)
}
func (m *GetAddressInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetAddressInfoRequest.Size(m)
}
func (m *GetAddressInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAddressInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAddressInfoRequest proto.InternalMessageInfo

func (m *GetAddressInfoRequest) GetAddressId() int32 {
	if m != nil {
		return m.AddressId
	}
	return 0
}

type GetAddressInfoResponse struct {
	Code                 int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *AddressInfoRequest `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAddressInfoResponse) Reset()         { *m = GetAddressInfoResponse{} }
func (m *GetAddressInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetAddressInfoResponse) ProtoMessage()    {}
func (*GetAddressInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{65}
}

func (m *GetAddressInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAddressInfoResponse.Unmarshal(m, b)
}
func (m *GetAddressInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAddressInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetAddressInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAddressInfoResponse.Merge(m, src)
}
func (m *GetAddressInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetAddressInfoResponse.Size(m)
}
func (m *GetAddressInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAddressInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAddressInfoResponse proto.InternalMessageInfo

func (m *GetAddressInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetAddressInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetAddressInfoResponse) GetData() *AddressInfoRequest {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetAddressListResponse struct {
	Code                 int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*AddressList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetAddressListResponse) Reset()         { *m = GetAddressListResponse{} }
func (m *GetAddressListResponse) String() string { return proto.CompactTextString(m) }
func (*GetAddressListResponse) ProtoMessage()    {}
func (*GetAddressListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{66}
}

func (m *GetAddressListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAddressListResponse.Unmarshal(m, b)
}
func (m *GetAddressListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAddressListResponse.Marshal(b, m, deterministic)
}
func (m *GetAddressListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAddressListResponse.Merge(m, src)
}
func (m *GetAddressListResponse) XXX_Size() int {
	return xxx_messageInfo_GetAddressListResponse.Size(m)
}
func (m *GetAddressListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAddressListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAddressListResponse proto.InternalMessageInfo

func (m *GetAddressListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetAddressListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetAddressListResponse) GetData() []*AddressList {
	if m != nil {
		return m.Data
	}
	return nil
}

type AddressList struct {
	AddressId            string   `protobuf:"bytes,1,opt,name=address_id,json=addressId,proto3" json:"address_id"`
	MemberId             string   `protobuf:"bytes,2,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	TrueName             string   `protobuf:"bytes,3,opt,name=true_name,json=trueName,proto3" json:"true_name"`
	AreaId               string   `protobuf:"bytes,4,opt,name=area_id,json=areaId,proto3" json:"area_id"`
	CityId               string   `protobuf:"bytes,5,opt,name=city_id,json=cityId,proto3" json:"city_id"`
	AreaInfo             string   `protobuf:"bytes,6,opt,name=area_info,json=areaInfo,proto3" json:"area_info"`
	Address              string   `protobuf:"bytes,7,opt,name=address,proto3" json:"address"`
	TelPhone             string   `protobuf:"bytes,8,opt,name=tel_phone,json=telPhone,proto3" json:"tel_phone"`
	MobPhone             string   `protobuf:"bytes,9,opt,name=mob_phone,json=mobPhone,proto3" json:"mob_phone"`
	IsDefault            string   `protobuf:"bytes,10,opt,name=is_default,json=isDefault,proto3" json:"is_default"`
	DlypId               string   `protobuf:"bytes,11,opt,name=dlyp_id,json=dlypId,proto3" json:"dlyp_id"`
	AreaLat              string   `protobuf:"bytes,12,opt,name=area_lat,json=areaLat,proto3" json:"area_lat"`
	AreaLng              string   `protobuf:"bytes,13,opt,name=area_lng,json=areaLng,proto3" json:"area_lng"`
	AreaTxlat            string   `protobuf:"bytes,14,opt,name=area_txlat,json=areaTxlat,proto3" json:"area_txlat"`
	AreaTxlng            string   `protobuf:"bytes,15,opt,name=area_txlng,json=areaTxlng,proto3" json:"area_txlng"`
	Isdeal               string   `protobuf:"bytes,16,opt,name=isdeal,proto3" json:"isdeal"`
	AddressDesc          string   `protobuf:"bytes,17,opt,name=address_desc,json=addressDesc,proto3" json:"address_desc"`
	HouseInfo            string   `protobuf:"bytes,18,opt,name=house_info,json=houseInfo,proto3" json:"house_info"`
	AreaAdcode           string   `protobuf:"bytes,19,opt,name=area_adcode,json=areaAdcode,proto3" json:"area_adcode"`
	MapAddress           string   `protobuf:"bytes,20,opt,name=map_address,json=mapAddress,proto3" json:"map_address"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddressList) Reset()         { *m = AddressList{} }
func (m *AddressList) String() string { return proto.CompactTextString(m) }
func (*AddressList) ProtoMessage()    {}
func (*AddressList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{67}
}

func (m *AddressList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddressList.Unmarshal(m, b)
}
func (m *AddressList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddressList.Marshal(b, m, deterministic)
}
func (m *AddressList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddressList.Merge(m, src)
}
func (m *AddressList) XXX_Size() int {
	return xxx_messageInfo_AddressList.Size(m)
}
func (m *AddressList) XXX_DiscardUnknown() {
	xxx_messageInfo_AddressList.DiscardUnknown(m)
}

var xxx_messageInfo_AddressList proto.InternalMessageInfo

func (m *AddressList) GetAddressId() string {
	if m != nil {
		return m.AddressId
	}
	return ""
}

func (m *AddressList) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *AddressList) GetTrueName() string {
	if m != nil {
		return m.TrueName
	}
	return ""
}

func (m *AddressList) GetAreaId() string {
	if m != nil {
		return m.AreaId
	}
	return ""
}

func (m *AddressList) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *AddressList) GetAreaInfo() string {
	if m != nil {
		return m.AreaInfo
	}
	return ""
}

func (m *AddressList) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *AddressList) GetTelPhone() string {
	if m != nil {
		return m.TelPhone
	}
	return ""
}

func (m *AddressList) GetMobPhone() string {
	if m != nil {
		return m.MobPhone
	}
	return ""
}

func (m *AddressList) GetIsDefault() string {
	if m != nil {
		return m.IsDefault
	}
	return ""
}

func (m *AddressList) GetDlypId() string {
	if m != nil {
		return m.DlypId
	}
	return ""
}

func (m *AddressList) GetAreaLat() string {
	if m != nil {
		return m.AreaLat
	}
	return ""
}

func (m *AddressList) GetAreaLng() string {
	if m != nil {
		return m.AreaLng
	}
	return ""
}

func (m *AddressList) GetAreaTxlat() string {
	if m != nil {
		return m.AreaTxlat
	}
	return ""
}

func (m *AddressList) GetAreaTxlng() string {
	if m != nil {
		return m.AreaTxlng
	}
	return ""
}

func (m *AddressList) GetIsdeal() string {
	if m != nil {
		return m.Isdeal
	}
	return ""
}

func (m *AddressList) GetAddressDesc() string {
	if m != nil {
		return m.AddressDesc
	}
	return ""
}

func (m *AddressList) GetHouseInfo() string {
	if m != nil {
		return m.HouseInfo
	}
	return ""
}

func (m *AddressList) GetAreaAdcode() string {
	if m != nil {
		return m.AreaAdcode
	}
	return ""
}

func (m *AddressList) GetMapAddress() string {
	if m != nil {
		return m.MapAddress
	}
	return ""
}

type GetAddressListRequest struct {
	ScrmUserId           string   `protobuf:"bytes,1,opt,name=scrm_user_id,json=scrmUserId,proto3" json:"scrm_user_id"`
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAddressListRequest) Reset()         { *m = GetAddressListRequest{} }
func (m *GetAddressListRequest) String() string { return proto.CompactTextString(m) }
func (*GetAddressListRequest) ProtoMessage()    {}
func (*GetAddressListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{68}
}

func (m *GetAddressListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAddressListRequest.Unmarshal(m, b)
}
func (m *GetAddressListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAddressListRequest.Marshal(b, m, deterministic)
}
func (m *GetAddressListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAddressListRequest.Merge(m, src)
}
func (m *GetAddressListRequest) XXX_Size() int {
	return xxx_messageInfo_GetAddressListRequest.Size(m)
}
func (m *GetAddressListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAddressListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAddressListRequest proto.InternalMessageInfo

func (m *GetAddressListRequest) GetScrmUserId() string {
	if m != nil {
		return m.ScrmUserId
	}
	return ""
}

func (m *GetAddressListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type BaseResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{69}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type ResResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResResponse) Reset()         { *m = ResResponse{} }
func (m *ResResponse) String() string { return proto.CompactTextString(m) }
func (*ResResponse) ProtoMessage()    {}
func (*ResResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{70}
}

func (m *ResResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResResponse.Unmarshal(m, b)
}
func (m *ResResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResResponse.Marshal(b, m, deterministic)
}
func (m *ResResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResResponse.Merge(m, src)
}
func (m *ResResponse) XXX_Size() int {
	return xxx_messageInfo_ResResponse.Size(m)
}
func (m *ResResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ResResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ResResponse proto.InternalMessageInfo

func (m *ResResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ResResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ResResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type TagsDto struct {
	// 名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 值,逗号分割
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value"`
	// 排序
	Sort int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort"`
	// 是否必填
	HasAsterisk int32 `protobuf:"varint,4,opt,name=hasAsterisk,proto3" json:"hasAsterisk"`
	// 对于选择数据，提供一个获取数据的url
	DataUrl              string   `protobuf:"bytes,5,opt,name=dataUrl,proto3" json:"dataUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagsDto) Reset()         { *m = TagsDto{} }
func (m *TagsDto) String() string { return proto.CompactTextString(m) }
func (*TagsDto) ProtoMessage()    {}
func (*TagsDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{71}
}

func (m *TagsDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagsDto.Unmarshal(m, b)
}
func (m *TagsDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagsDto.Marshal(b, m, deterministic)
}
func (m *TagsDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagsDto.Merge(m, src)
}
func (m *TagsDto) XXX_Size() int {
	return xxx_messageInfo_TagsDto.Size(m)
}
func (m *TagsDto) XXX_DiscardUnknown() {
	xxx_messageInfo_TagsDto.DiscardUnknown(m)
}

var xxx_messageInfo_TagsDto proto.InternalMessageInfo

func (m *TagsDto) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TagsDto) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *TagsDto) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *TagsDto) GetHasAsterisk() int32 {
	if m != nil {
		return m.HasAsterisk
	}
	return 0
}

func (m *TagsDto) GetDataUrl() string {
	if m != nil {
		return m.DataUrl
	}
	return ""
}

// 数据请求
type TagsQueryRequest struct {
	// 分组
	Groups int32 `protobuf:"varint,1,opt,name=groups,proto3" json:"groups"`
	// 请求来源，content-内容中心
	From                 string   `protobuf:"bytes,2,opt,name=from,proto3" json:"from"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagsQueryRequest) Reset()         { *m = TagsQueryRequest{} }
func (m *TagsQueryRequest) String() string { return proto.CompactTextString(m) }
func (*TagsQueryRequest) ProtoMessage()    {}
func (*TagsQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{72}
}

func (m *TagsQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagsQueryRequest.Unmarshal(m, b)
}
func (m *TagsQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagsQueryRequest.Marshal(b, m, deterministic)
}
func (m *TagsQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagsQueryRequest.Merge(m, src)
}
func (m *TagsQueryRequest) XXX_Size() int {
	return xxx_messageInfo_TagsQueryRequest.Size(m)
}
func (m *TagsQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TagsQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TagsQueryRequest proto.InternalMessageInfo

func (m *TagsQueryRequest) GetGroups() int32 {
	if m != nil {
		return m.Groups
	}
	return 0
}

func (m *TagsQueryRequest) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

// 数据响应
type TagsQueryResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 标签列表
	Data                 []*TagsDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *TagsQueryResponse) Reset()         { *m = TagsQueryResponse{} }
func (m *TagsQueryResponse) String() string { return proto.CompactTextString(m) }
func (*TagsQueryResponse) ProtoMessage()    {}
func (*TagsQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{73}
}

func (m *TagsQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagsQueryResponse.Unmarshal(m, b)
}
func (m *TagsQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagsQueryResponse.Marshal(b, m, deterministic)
}
func (m *TagsQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagsQueryResponse.Merge(m, src)
}
func (m *TagsQueryResponse) XXX_Size() int {
	return xxx_messageInfo_TagsQueryResponse.Size(m)
}
func (m *TagsQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TagsQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TagsQueryResponse proto.InternalMessageInfo

func (m *TagsQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TagsQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *TagsQueryResponse) GetData() []*TagsDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// 推荐商品查询
type RecommendProductQueryRequest struct {
	// 页索引
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	// 用户Id
	UserId string `protobuf:"bytes,3,opt,name=userId,proto3" json:"userId"`
	// 宠物id
	PetId                string   `protobuf:"bytes,4,opt,name=petId,proto3" json:"petId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendProductQueryRequest) Reset()         { *m = RecommendProductQueryRequest{} }
func (m *RecommendProductQueryRequest) String() string { return proto.CompactTextString(m) }
func (*RecommendProductQueryRequest) ProtoMessage()    {}
func (*RecommendProductQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{74}
}

func (m *RecommendProductQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendProductQueryRequest.Unmarshal(m, b)
}
func (m *RecommendProductQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendProductQueryRequest.Marshal(b, m, deterministic)
}
func (m *RecommendProductQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendProductQueryRequest.Merge(m, src)
}
func (m *RecommendProductQueryRequest) XXX_Size() int {
	return xxx_messageInfo_RecommendProductQueryRequest.Size(m)
}
func (m *RecommendProductQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendProductQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendProductQueryRequest proto.InternalMessageInfo

func (m *RecommendProductQueryRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *RecommendProductQueryRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *RecommendProductQueryRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *RecommendProductQueryRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

// 推荐商品模型
type RecommendProductDto struct {
	// sku
	SkuId                string   `protobuf:"bytes,1,opt,name=skuId,proto3" json:"skuId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendProductDto) Reset()         { *m = RecommendProductDto{} }
func (m *RecommendProductDto) String() string { return proto.CompactTextString(m) }
func (*RecommendProductDto) ProtoMessage()    {}
func (*RecommendProductDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{75}
}

func (m *RecommendProductDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendProductDto.Unmarshal(m, b)
}
func (m *RecommendProductDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendProductDto.Marshal(b, m, deterministic)
}
func (m *RecommendProductDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendProductDto.Merge(m, src)
}
func (m *RecommendProductDto) XXX_Size() int {
	return xxx_messageInfo_RecommendProductDto.Size(m)
}
func (m *RecommendProductDto) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendProductDto.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendProductDto proto.InternalMessageInfo

func (m *RecommendProductDto) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

// 推荐商品数据响应
type RecommendProductQueryResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总条数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 标签列表
	Data                 []*RecommendProductDto `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	SkuId                []int32                `protobuf:"varint,5,rep,packed,name=sku_id,json=skuId,proto3" json:"sku_id"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *RecommendProductQueryResponse) Reset()         { *m = RecommendProductQueryResponse{} }
func (m *RecommendProductQueryResponse) String() string { return proto.CompactTextString(m) }
func (*RecommendProductQueryResponse) ProtoMessage()    {}
func (*RecommendProductQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{76}
}

func (m *RecommendProductQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendProductQueryResponse.Unmarshal(m, b)
}
func (m *RecommendProductQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendProductQueryResponse.Marshal(b, m, deterministic)
}
func (m *RecommendProductQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendProductQueryResponse.Merge(m, src)
}
func (m *RecommendProductQueryResponse) XXX_Size() int {
	return xxx_messageInfo_RecommendProductQueryResponse.Size(m)
}
func (m *RecommendProductQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendProductQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendProductQueryResponse proto.InternalMessageInfo

func (m *RecommendProductQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RecommendProductQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RecommendProductQueryResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *RecommendProductQueryResponse) GetData() []*RecommendProductDto {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *RecommendProductQueryResponse) GetSkuId() []int32 {
	if m != nil {
		return m.SkuId
	}
	return nil
}

// 宠物标签请求
type PetTagQueryRequest struct {
	UserId               string   `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	PetId                string   `protobuf:"bytes,2,opt,name=petId,proto3" json:"petId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTagQueryRequest) Reset()         { *m = PetTagQueryRequest{} }
func (m *PetTagQueryRequest) String() string { return proto.CompactTextString(m) }
func (*PetTagQueryRequest) ProtoMessage()    {}
func (*PetTagQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{77}
}

func (m *PetTagQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTagQueryRequest.Unmarshal(m, b)
}
func (m *PetTagQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTagQueryRequest.Marshal(b, m, deterministic)
}
func (m *PetTagQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTagQueryRequest.Merge(m, src)
}
func (m *PetTagQueryRequest) XXX_Size() int {
	return xxx_messageInfo_PetTagQueryRequest.Size(m)
}
func (m *PetTagQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTagQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetTagQueryRequest proto.InternalMessageInfo

func (m *PetTagQueryRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetTagQueryRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

// 宠物标签生成请求
type PetTagGenerateRequest struct {
	UserId               string   `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	PetId                string   `protobuf:"bytes,2,opt,name=petId,proto3" json:"petId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTagGenerateRequest) Reset()         { *m = PetTagGenerateRequest{} }
func (m *PetTagGenerateRequest) String() string { return proto.CompactTextString(m) }
func (*PetTagGenerateRequest) ProtoMessage()    {}
func (*PetTagGenerateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{78}
}

func (m *PetTagGenerateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTagGenerateRequest.Unmarshal(m, b)
}
func (m *PetTagGenerateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTagGenerateRequest.Marshal(b, m, deterministic)
}
func (m *PetTagGenerateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTagGenerateRequest.Merge(m, src)
}
func (m *PetTagGenerateRequest) XXX_Size() int {
	return xxx_messageInfo_PetTagGenerateRequest.Size(m)
}
func (m *PetTagGenerateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTagGenerateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetTagGenerateRequest proto.InternalMessageInfo

func (m *PetTagGenerateRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetTagGenerateRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

type PetTagDto struct {
	UserId               string   `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	PetId                string   `protobuf:"bytes,2,opt,name=petId,proto3" json:"petId"`
	TagName              string   `protobuf:"bytes,3,opt,name=tagName,proto3" json:"tagName"`
	TagValue             string   `protobuf:"bytes,4,opt,name=tagValue,proto3" json:"tagValue"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetTagDto) Reset()         { *m = PetTagDto{} }
func (m *PetTagDto) String() string { return proto.CompactTextString(m) }
func (*PetTagDto) ProtoMessage()    {}
func (*PetTagDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{79}
}

func (m *PetTagDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTagDto.Unmarshal(m, b)
}
func (m *PetTagDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTagDto.Marshal(b, m, deterministic)
}
func (m *PetTagDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTagDto.Merge(m, src)
}
func (m *PetTagDto) XXX_Size() int {
	return xxx_messageInfo_PetTagDto.Size(m)
}
func (m *PetTagDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTagDto.DiscardUnknown(m)
}

var xxx_messageInfo_PetTagDto proto.InternalMessageInfo

func (m *PetTagDto) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetTagDto) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *PetTagDto) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *PetTagDto) GetTagValue() string {
	if m != nil {
		return m.TagValue
	}
	return ""
}

// 宠物标签响应
type PetTagQueryResponse struct {
	Code                 int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*PetTagDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PetTagQueryResponse) Reset()         { *m = PetTagQueryResponse{} }
func (m *PetTagQueryResponse) String() string { return proto.CompactTextString(m) }
func (*PetTagQueryResponse) ProtoMessage()    {}
func (*PetTagQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{80}
}

func (m *PetTagQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTagQueryResponse.Unmarshal(m, b)
}
func (m *PetTagQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTagQueryResponse.Marshal(b, m, deterministic)
}
func (m *PetTagQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTagQueryResponse.Merge(m, src)
}
func (m *PetTagQueryResponse) XXX_Size() int {
	return xxx_messageInfo_PetTagQueryResponse.Size(m)
}
func (m *PetTagQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTagQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetTagQueryResponse proto.InternalMessageInfo

func (m *PetTagQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PetTagQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PetTagQueryResponse) GetData() []*PetTagDto {
	if m != nil {
		return m.Data
	}
	return nil
}

type QueryUsuallyRecordProductResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总条数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 标签列表
	Data                 []*ProductInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *QueryUsuallyRecordProductResponse) Reset()         { *m = QueryUsuallyRecordProductResponse{} }
func (m *QueryUsuallyRecordProductResponse) String() string { return proto.CompactTextString(m) }
func (*QueryUsuallyRecordProductResponse) ProtoMessage()    {}
func (*QueryUsuallyRecordProductResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{81}
}

func (m *QueryUsuallyRecordProductResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryUsuallyRecordProductResponse.Unmarshal(m, b)
}
func (m *QueryUsuallyRecordProductResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryUsuallyRecordProductResponse.Marshal(b, m, deterministic)
}
func (m *QueryUsuallyRecordProductResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryUsuallyRecordProductResponse.Merge(m, src)
}
func (m *QueryUsuallyRecordProductResponse) XXX_Size() int {
	return xxx_messageInfo_QueryUsuallyRecordProductResponse.Size(m)
}
func (m *QueryUsuallyRecordProductResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryUsuallyRecordProductResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryUsuallyRecordProductResponse proto.InternalMessageInfo

func (m *QueryUsuallyRecordProductResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryUsuallyRecordProductResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryUsuallyRecordProductResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *QueryUsuallyRecordProductResponse) GetData() []*ProductInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type ProductInfo struct {
	SkuId                int32    `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	ChannelId            int32    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	SalesVolume          int32    `protobuf:"varint,3,opt,name=sales_volume,json=salesVolume,proto3" json:"sales_volume"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductInfo) Reset()         { *m = ProductInfo{} }
func (m *ProductInfo) String() string { return proto.CompactTextString(m) }
func (*ProductInfo) ProtoMessage()    {}
func (*ProductInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{82}
}

func (m *ProductInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductInfo.Unmarshal(m, b)
}
func (m *ProductInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductInfo.Marshal(b, m, deterministic)
}
func (m *ProductInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductInfo.Merge(m, src)
}
func (m *ProductInfo) XXX_Size() int {
	return xxx_messageInfo_ProductInfo.Size(m)
}
func (m *ProductInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ProductInfo proto.InternalMessageInfo

func (m *ProductInfo) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ProductInfo) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ProductInfo) GetSalesVolume() int32 {
	if m != nil {
		return m.SalesVolume
	}
	return 0
}

// 标签操作
type TagOperateRequest struct {
	// 组标签名称
	GroupName string `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	// 标签名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 更新前的标签名称
	OldName string `protobuf:"bytes,3,opt,name=old_name,json=oldName,proto3" json:"old_name"`
	// 操作，add-新增，update-更新，delete-删除
	Opt string `protobuf:"bytes,4,opt,name=opt,proto3" json:"opt"`
	//主体id
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagOperateRequest) Reset()         { *m = TagOperateRequest{} }
func (m *TagOperateRequest) String() string { return proto.CompactTextString(m) }
func (*TagOperateRequest) ProtoMessage()    {}
func (*TagOperateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{83}
}

func (m *TagOperateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagOperateRequest.Unmarshal(m, b)
}
func (m *TagOperateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagOperateRequest.Marshal(b, m, deterministic)
}
func (m *TagOperateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagOperateRequest.Merge(m, src)
}
func (m *TagOperateRequest) XXX_Size() int {
	return xxx_messageInfo_TagOperateRequest.Size(m)
}
func (m *TagOperateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TagOperateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TagOperateRequest proto.InternalMessageInfo

func (m *TagOperateRequest) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *TagOperateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TagOperateRequest) GetOldName() string {
	if m != nil {
		return m.OldName
	}
	return ""
}

func (m *TagOperateRequest) GetOpt() string {
	if m != nil {
		return m.Opt
	}
	return ""
}

func (m *TagOperateRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type TagOperateResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 文章id集合
	ArticleIds           []int32  `protobuf:"varint,3,rep,packed,name=article_ids,json=articleIds,proto3" json:"article_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagOperateResponse) Reset()         { *m = TagOperateResponse{} }
func (m *TagOperateResponse) String() string { return proto.CompactTextString(m) }
func (*TagOperateResponse) ProtoMessage()    {}
func (*TagOperateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{84}
}

func (m *TagOperateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagOperateResponse.Unmarshal(m, b)
}
func (m *TagOperateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagOperateResponse.Marshal(b, m, deterministic)
}
func (m *TagOperateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagOperateResponse.Merge(m, src)
}
func (m *TagOperateResponse) XXX_Size() int {
	return xxx_messageInfo_TagOperateResponse.Size(m)
}
func (m *TagOperateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TagOperateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TagOperateResponse proto.InternalMessageInfo

func (m *TagOperateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TagOperateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *TagOperateResponse) GetArticleIds() []int32 {
	if m != nil {
		return m.ArticleIds
	}
	return nil
}

// 内容标签查询
type ContentTagRequest struct {
	//主体id
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContentTagRequest) Reset()         { *m = ContentTagRequest{} }
func (m *ContentTagRequest) String() string { return proto.CompactTextString(m) }
func (*ContentTagRequest) ProtoMessage()    {}
func (*ContentTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{85}
}

func (m *ContentTagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContentTagRequest.Unmarshal(m, b)
}
func (m *ContentTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContentTagRequest.Marshal(b, m, deterministic)
}
func (m *ContentTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContentTagRequest.Merge(m, src)
}
func (m *ContentTagRequest) XXX_Size() int {
	return xxx_messageInfo_ContentTagRequest.Size(m)
}
func (m *ContentTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ContentTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ContentTagRequest proto.InternalMessageInfo

func (m *ContentTagRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type ContentTagResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message              string          `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*TagQueryData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ContentTagResponse) Reset()         { *m = ContentTagResponse{} }
func (m *ContentTagResponse) String() string { return proto.CompactTextString(m) }
func (*ContentTagResponse) ProtoMessage()    {}
func (*ContentTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{86}
}

func (m *ContentTagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContentTagResponse.Unmarshal(m, b)
}
func (m *ContentTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContentTagResponse.Marshal(b, m, deterministic)
}
func (m *ContentTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContentTagResponse.Merge(m, src)
}
func (m *ContentTagResponse) XXX_Size() int {
	return xxx_messageInfo_ContentTagResponse.Size(m)
}
func (m *ContentTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ContentTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ContentTagResponse proto.InternalMessageInfo

func (m *ContentTagResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ContentTagResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ContentTagResponse) GetData() []*TagQueryData {
	if m != nil {
		return m.Data
	}
	return nil
}

type TagQueryData struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Tags                 string   `protobuf:"bytes,2,opt,name=tags,proto3" json:"tags"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagQueryData) Reset()         { *m = TagQueryData{} }
func (m *TagQueryData) String() string { return proto.CompactTextString(m) }
func (*TagQueryData) ProtoMessage()    {}
func (*TagQueryData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{87}
}

func (m *TagQueryData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagQueryData.Unmarshal(m, b)
}
func (m *TagQueryData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagQueryData.Marshal(b, m, deterministic)
}
func (m *TagQueryData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagQueryData.Merge(m, src)
}
func (m *TagQueryData) XXX_Size() int {
	return xxx_messageInfo_TagQueryData.Size(m)
}
func (m *TagQueryData) XXX_DiscardUnknown() {
	xxx_messageInfo_TagQueryData.DiscardUnknown(m)
}

var xxx_messageInfo_TagQueryData proto.InternalMessageInfo

func (m *TagQueryData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TagQueryData) GetTags() string {
	if m != nil {
		return m.Tags
	}
	return ""
}

type MemberProductDiscountRequest struct {
	UserId               string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberProductDiscountRequest) Reset()         { *m = MemberProductDiscountRequest{} }
func (m *MemberProductDiscountRequest) String() string { return proto.CompactTextString(m) }
func (*MemberProductDiscountRequest) ProtoMessage()    {}
func (*MemberProductDiscountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{88}
}

func (m *MemberProductDiscountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberProductDiscountRequest.Unmarshal(m, b)
}
func (m *MemberProductDiscountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberProductDiscountRequest.Marshal(b, m, deterministic)
}
func (m *MemberProductDiscountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberProductDiscountRequest.Merge(m, src)
}
func (m *MemberProductDiscountRequest) XXX_Size() int {
	return xxx_messageInfo_MemberProductDiscountRequest.Size(m)
}
func (m *MemberProductDiscountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberProductDiscountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MemberProductDiscountRequest proto.InternalMessageInfo

func (m *MemberProductDiscountRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type MemberProductDiscountResponse struct {
	Discount             string   `protobuf:"bytes,1,opt,name=discount,proto3" json:"discount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberProductDiscountResponse) Reset()         { *m = MemberProductDiscountResponse{} }
func (m *MemberProductDiscountResponse) String() string { return proto.CompactTextString(m) }
func (*MemberProductDiscountResponse) ProtoMessage()    {}
func (*MemberProductDiscountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{89}
}

func (m *MemberProductDiscountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberProductDiscountResponse.Unmarshal(m, b)
}
func (m *MemberProductDiscountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberProductDiscountResponse.Marshal(b, m, deterministic)
}
func (m *MemberProductDiscountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberProductDiscountResponse.Merge(m, src)
}
func (m *MemberProductDiscountResponse) XXX_Size() int {
	return xxx_messageInfo_MemberProductDiscountResponse.Size(m)
}
func (m *MemberProductDiscountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberProductDiscountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MemberProductDiscountResponse proto.InternalMessageInfo

func (m *MemberProductDiscountResponse) GetDiscount() string {
	if m != nil {
		return m.Discount
	}
	return ""
}

type MessageInfoRequest struct {
	//用户ID
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//主体：1-阿闻，2-极宠家 3-宠商云 4-百林康源+宠利扫
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageInfoRequest) Reset()         { *m = MessageInfoRequest{} }
func (m *MessageInfoRequest) String() string { return proto.CompactTextString(m) }
func (*MessageInfoRequest) ProtoMessage()    {}
func (*MessageInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{90}
}

func (m *MessageInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageInfoRequest.Unmarshal(m, b)
}
func (m *MessageInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageInfoRequest.Marshal(b, m, deterministic)
}
func (m *MessageInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageInfoRequest.Merge(m, src)
}
func (m *MessageInfoRequest) XXX_Size() int {
	return xxx_messageInfo_MessageInfoRequest.Size(m)
}
func (m *MessageInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MessageInfoRequest proto.InternalMessageInfo

func (m *MessageInfoRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *MessageInfoRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type MessageInfoResponse struct {
	//身份
	StatusData []*MessageInfo `protobuf:"bytes,1,rep,name=status_data,json=statusData,proto3" json:"status_data"`
	//资产
	PropertyData []*MessageInfo `protobuf:"bytes,2,rep,name=property_data,json=propertyData,proto3" json:"property_data"`
	//业务
	BusinessData         []*MessageInfo `protobuf:"bytes,3,rep,name=business_data,json=businessData,proto3" json:"business_data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *MessageInfoResponse) Reset()         { *m = MessageInfoResponse{} }
func (m *MessageInfoResponse) String() string { return proto.CompactTextString(m) }
func (*MessageInfoResponse) ProtoMessage()    {}
func (*MessageInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{91}
}

func (m *MessageInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageInfoResponse.Unmarshal(m, b)
}
func (m *MessageInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageInfoResponse.Marshal(b, m, deterministic)
}
func (m *MessageInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageInfoResponse.Merge(m, src)
}
func (m *MessageInfoResponse) XXX_Size() int {
	return xxx_messageInfo_MessageInfoResponse.Size(m)
}
func (m *MessageInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MessageInfoResponse proto.InternalMessageInfo

func (m *MessageInfoResponse) GetStatusData() []*MessageInfo {
	if m != nil {
		return m.StatusData
	}
	return nil
}

func (m *MessageInfoResponse) GetPropertyData() []*MessageInfo {
	if m != nil {
		return m.PropertyData
	}
	return nil
}

func (m *MessageInfoResponse) GetBusinessData() []*MessageInfo {
	if m != nil {
		return m.BusinessData
	}
	return nil
}

type MessageInfo struct {
	//模板id
	TemplateId string `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	//名称
	MessageTitle string `protobuf:"bytes,2,opt,name=message_title,json=messageTitle,proto3" json:"message_title"`
	//类型 1、用户等级通知 2、积分通知 3、优惠券通知 4、医院挂号排队通知 5、预约挂号通知 6子龙美容服务通知 7核销码使用 8核销码过期提醒 9会员权益过期通知
	MessageType int32 `protobuf:"varint,3,opt,name=message_type,json=messageType,proto3" json:"message_type"`
	//次数
	Number int32 `protobuf:"varint,4,opt,name=number,proto3" json:"number"`
	//类型键值
	TemplateKey          string   `protobuf:"bytes,5,opt,name=template_key,json=templateKey,proto3" json:"template_key"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageInfo) Reset()         { *m = MessageInfo{} }
func (m *MessageInfo) String() string { return proto.CompactTextString(m) }
func (*MessageInfo) ProtoMessage()    {}
func (*MessageInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{92}
}

func (m *MessageInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageInfo.Unmarshal(m, b)
}
func (m *MessageInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageInfo.Marshal(b, m, deterministic)
}
func (m *MessageInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageInfo.Merge(m, src)
}
func (m *MessageInfo) XXX_Size() int {
	return xxx_messageInfo_MessageInfo.Size(m)
}
func (m *MessageInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MessageInfo proto.InternalMessageInfo

func (m *MessageInfo) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *MessageInfo) GetMessageTitle() string {
	if m != nil {
		return m.MessageTitle
	}
	return ""
}

func (m *MessageInfo) GetMessageType() int32 {
	if m != nil {
		return m.MessageType
	}
	return 0
}

func (m *MessageInfo) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *MessageInfo) GetTemplateKey() string {
	if m != nil {
		return m.TemplateKey
	}
	return ""
}

type MemberCouponsMessageResponse struct {
	Data                 []*MemberCouponsMessage `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *MemberCouponsMessageResponse) Reset()         { *m = MemberCouponsMessageResponse{} }
func (m *MemberCouponsMessageResponse) String() string { return proto.CompactTextString(m) }
func (*MemberCouponsMessageResponse) ProtoMessage()    {}
func (*MemberCouponsMessageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{93}
}

func (m *MemberCouponsMessageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCouponsMessageResponse.Unmarshal(m, b)
}
func (m *MemberCouponsMessageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCouponsMessageResponse.Marshal(b, m, deterministic)
}
func (m *MemberCouponsMessageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCouponsMessageResponse.Merge(m, src)
}
func (m *MemberCouponsMessageResponse) XXX_Size() int {
	return xxx_messageInfo_MemberCouponsMessageResponse.Size(m)
}
func (m *MemberCouponsMessageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCouponsMessageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCouponsMessageResponse proto.InternalMessageInfo

func (m *MemberCouponsMessageResponse) GetData() []*MemberCouponsMessage {
	if m != nil {
		return m.Data
	}
	return nil
}

type MemberCouponsMessage struct {
	//1、升级券 2、周特权
	MessageType int32 `protobuf:"varint,1,opt,name=message_type,json=messageType,proto3" json:"message_type"`
	//券类型：1门店券  2商品券
	CouponType int32 `protobuf:"varint,2,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type"`
	//内容
	MessageBody          string   `protobuf:"bytes,3,opt,name=message_body,json=messageBody,proto3" json:"message_body"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberCouponsMessage) Reset()         { *m = MemberCouponsMessage{} }
func (m *MemberCouponsMessage) String() string { return proto.CompactTextString(m) }
func (*MemberCouponsMessage) ProtoMessage()    {}
func (*MemberCouponsMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{94}
}

func (m *MemberCouponsMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberCouponsMessage.Unmarshal(m, b)
}
func (m *MemberCouponsMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberCouponsMessage.Marshal(b, m, deterministic)
}
func (m *MemberCouponsMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberCouponsMessage.Merge(m, src)
}
func (m *MemberCouponsMessage) XXX_Size() int {
	return xxx_messageInfo_MemberCouponsMessage.Size(m)
}
func (m *MemberCouponsMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberCouponsMessage.DiscardUnknown(m)
}

var xxx_messageInfo_MemberCouponsMessage proto.InternalMessageInfo

func (m *MemberCouponsMessage) GetMessageType() int32 {
	if m != nil {
		return m.MessageType
	}
	return 0
}

func (m *MemberCouponsMessage) GetCouponType() int32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

func (m *MemberCouponsMessage) GetMessageBody() string {
	if m != nil {
		return m.MessageBody
	}
	return ""
}

type MemberNewMessageRequest struct {
	//用户ID
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//token
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token"`
	//主体：1-阿闻，2-极宠家
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberNewMessageRequest) Reset()         { *m = MemberNewMessageRequest{} }
func (m *MemberNewMessageRequest) String() string { return proto.CompactTextString(m) }
func (*MemberNewMessageRequest) ProtoMessage()    {}
func (*MemberNewMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{95}
}

func (m *MemberNewMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberNewMessageRequest.Unmarshal(m, b)
}
func (m *MemberNewMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberNewMessageRequest.Marshal(b, m, deterministic)
}
func (m *MemberNewMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberNewMessageRequest.Merge(m, src)
}
func (m *MemberNewMessageRequest) XXX_Size() int {
	return xxx_messageInfo_MemberNewMessageRequest.Size(m)
}
func (m *MemberNewMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberNewMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MemberNewMessageRequest proto.InternalMessageInfo

func (m *MemberNewMessageRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *MemberNewMessageRequest) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *MemberNewMessageRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type MemberNewMessageResponseData struct {
	//病例
	MedRecord *MemberNewMessageResponseData_NewMessageData `protobuf:"bytes,1,opt,name=med_record,json=medRecord,proto3" json:"med_record"`
	//健康值
	Health *MemberNewMessageResponseData_NewMessageData `protobuf:"bytes,2,opt,name=health,proto3" json:"health"`
	//积分
	Integral             *MemberNewMessageResponseData_NewMessageData `protobuf:"bytes,3,opt,name=integral,proto3" json:"integral"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *MemberNewMessageResponseData) Reset()         { *m = MemberNewMessageResponseData{} }
func (m *MemberNewMessageResponseData) String() string { return proto.CompactTextString(m) }
func (*MemberNewMessageResponseData) ProtoMessage()    {}
func (*MemberNewMessageResponseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{96}
}

func (m *MemberNewMessageResponseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberNewMessageResponseData.Unmarshal(m, b)
}
func (m *MemberNewMessageResponseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberNewMessageResponseData.Marshal(b, m, deterministic)
}
func (m *MemberNewMessageResponseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberNewMessageResponseData.Merge(m, src)
}
func (m *MemberNewMessageResponseData) XXX_Size() int {
	return xxx_messageInfo_MemberNewMessageResponseData.Size(m)
}
func (m *MemberNewMessageResponseData) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberNewMessageResponseData.DiscardUnknown(m)
}

var xxx_messageInfo_MemberNewMessageResponseData proto.InternalMessageInfo

func (m *MemberNewMessageResponseData) GetMedRecord() *MemberNewMessageResponseData_NewMessageData {
	if m != nil {
		return m.MedRecord
	}
	return nil
}

func (m *MemberNewMessageResponseData) GetHealth() *MemberNewMessageResponseData_NewMessageData {
	if m != nil {
		return m.Health
	}
	return nil
}

func (m *MemberNewMessageResponseData) GetIntegral() *MemberNewMessageResponseData_NewMessageData {
	if m != nil {
		return m.Integral
	}
	return nil
}

type MemberNewMessageResponseData_NewMessageData struct {
	//时间
	Datetime string `protobuf:"bytes,1,opt,name=datetime,proto3" json:"datetime"`
	//内容
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberNewMessageResponseData_NewMessageData) Reset() {
	*m = MemberNewMessageResponseData_NewMessageData{}
}
func (m *MemberNewMessageResponseData_NewMessageData) String() string {
	return proto.CompactTextString(m)
}
func (*MemberNewMessageResponseData_NewMessageData) ProtoMessage() {}
func (*MemberNewMessageResponseData_NewMessageData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{96, 0}
}

func (m *MemberNewMessageResponseData_NewMessageData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberNewMessageResponseData_NewMessageData.Unmarshal(m, b)
}
func (m *MemberNewMessageResponseData_NewMessageData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberNewMessageResponseData_NewMessageData.Marshal(b, m, deterministic)
}
func (m *MemberNewMessageResponseData_NewMessageData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberNewMessageResponseData_NewMessageData.Merge(m, src)
}
func (m *MemberNewMessageResponseData_NewMessageData) XXX_Size() int {
	return xxx_messageInfo_MemberNewMessageResponseData_NewMessageData.Size(m)
}
func (m *MemberNewMessageResponseData_NewMessageData) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberNewMessageResponseData_NewMessageData.DiscardUnknown(m)
}

var xxx_messageInfo_MemberNewMessageResponseData_NewMessageData proto.InternalMessageInfo

func (m *MemberNewMessageResponseData_NewMessageData) GetDatetime() string {
	if m != nil {
		return m.Datetime
	}
	return ""
}

func (m *MemberNewMessageResponseData_NewMessageData) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type MemberNewMessageResponse struct {
	Data                 *MemberNewMessageResponseData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *MemberNewMessageResponse) Reset()         { *m = MemberNewMessageResponse{} }
func (m *MemberNewMessageResponse) String() string { return proto.CompactTextString(m) }
func (*MemberNewMessageResponse) ProtoMessage()    {}
func (*MemberNewMessageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{97}
}

func (m *MemberNewMessageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberNewMessageResponse.Unmarshal(m, b)
}
func (m *MemberNewMessageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberNewMessageResponse.Marshal(b, m, deterministic)
}
func (m *MemberNewMessageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberNewMessageResponse.Merge(m, src)
}
func (m *MemberNewMessageResponse) XXX_Size() int {
	return xxx_messageInfo_MemberNewMessageResponse.Size(m)
}
func (m *MemberNewMessageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberNewMessageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MemberNewMessageResponse proto.InternalMessageInfo

func (m *MemberNewMessageResponse) GetData() *MemberNewMessageResponseData {
	if m != nil {
		return m.Data
	}
	return nil
}

type InfoUpdateRequest struct {
	//用户ID
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//昵称
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name"`
	//性别
	UserSex int32 `protobuf:"varint,3,opt,name=user_sex,json=userSex,proto3" json:"user_sex"`
	//手机号码
	UserMobile string `protobuf:"bytes,4,opt,name=user_mobile,json=userMobile,proto3" json:"user_mobile"`
	//生日
	UserBirthday string `protobuf:"bytes,5,opt,name=user_birthday,json=userBirthday,proto3" json:"user_birthday"`
	//初次养宠物时间
	FirstRaisesPet string `protobuf:"bytes,6,opt,name=first_raises_pet,json=firstRaisesPet,proto3" json:"first_raises_pet"`
	//头像
	UserAvatar string `protobuf:"bytes,7,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar"`
	//省
	Province string `protobuf:"bytes,8,opt,name=province,proto3" json:"province"`
	//市
	City string `protobuf:"bytes,9,opt,name=city,proto3" json:"city"`
	//区
	Area string `protobuf:"bytes,10,opt,name=area,proto3" json:"area"`
	//
	RemoteTreatStatus int32 `protobuf:"varint,11,opt,name=remote_treat_status,json=remoteTreatStatus,proto3" json:"remote_treat_status"`
	//
	Token                string   `protobuf:"bytes,12,opt,name=token,proto3" json:"token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InfoUpdateRequest) Reset()         { *m = InfoUpdateRequest{} }
func (m *InfoUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*InfoUpdateRequest) ProtoMessage()    {}
func (*InfoUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{98}
}

func (m *InfoUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InfoUpdateRequest.Unmarshal(m, b)
}
func (m *InfoUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InfoUpdateRequest.Marshal(b, m, deterministic)
}
func (m *InfoUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InfoUpdateRequest.Merge(m, src)
}
func (m *InfoUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_InfoUpdateRequest.Size(m)
}
func (m *InfoUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InfoUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InfoUpdateRequest proto.InternalMessageInfo

func (m *InfoUpdateRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *InfoUpdateRequest) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *InfoUpdateRequest) GetUserSex() int32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *InfoUpdateRequest) GetUserMobile() string {
	if m != nil {
		return m.UserMobile
	}
	return ""
}

func (m *InfoUpdateRequest) GetUserBirthday() string {
	if m != nil {
		return m.UserBirthday
	}
	return ""
}

func (m *InfoUpdateRequest) GetFirstRaisesPet() string {
	if m != nil {
		return m.FirstRaisesPet
	}
	return ""
}

func (m *InfoUpdateRequest) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *InfoUpdateRequest) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *InfoUpdateRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *InfoUpdateRequest) GetArea() string {
	if m != nil {
		return m.Area
	}
	return ""
}

func (m *InfoUpdateRequest) GetRemoteTreatStatus() int32 {
	if m != nil {
		return m.RemoteTreatStatus
	}
	return 0
}

func (m *InfoUpdateRequest) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type PetInfoUpdateRequest struct {
	//ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//用户ID
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物ID
	PetId string `protobuf:"bytes,3,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//宠物头像
	PetAvatar string `protobuf:"bytes,4,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar"`
	//宠物名称
	PetName string `protobuf:"bytes,5,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//宠物生日
	PetBirthday          string   `protobuf:"bytes,6,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	PetHomeDay           string   `protobuf:"bytes,7,opt,name=pet_home_day,json=petHomeDay,proto3" json:"pet_home_day"`
	PetSex               int32    `protobuf:"varint,8,opt,name=pet_sex,json=petSex,proto3" json:"pet_sex"`
	PetKindof            int32    `protobuf:"varint,9,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	PetVariety           int32    `protobuf:"varint,10,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	PetLong              int32    `protobuf:"varint,11,opt,name=pet_long,json=petLong,proto3" json:"pet_long"`
	PetWeight            int32    `protobuf:"varint,12,opt,name=pet_weight,json=petWeight,proto3" json:"pet_weight"`
	PetNeutering         int32    `protobuf:"varint,13,opt,name=pet_neutering,json=petNeutering,proto3" json:"pet_neutering"`
	PetVaccinated        int32    `protobuf:"varint,14,opt,name=pet_vaccinated,json=petVaccinated,proto3" json:"pet_vaccinated"`
	PetDeworming         int32    `protobuf:"varint,15,opt,name=pet_deworming,json=petDeworming,proto3" json:"pet_deworming"`
	PetHeight            int32    `protobuf:"varint,16,opt,name=pet_height,json=petHeight,proto3" json:"pet_height"`
	PetSource            int32    `protobuf:"varint,17,opt,name=pet_source,json=petSource,proto3" json:"pet_source"`
	PetStatus            int32    `protobuf:"varint,18,opt,name=pet_status,json=petStatus,proto3" json:"pet_status"`
	PetRemark            string   `protobuf:"bytes,19,opt,name=pet_remark,json=petRemark,proto3" json:"pet_remark"`
	PetVarietyStr        string   `protobuf:"bytes,20,opt,name=pet_variety_str,json=petVarietyStr,proto3" json:"pet_variety_str"`
	PetKindofStr         string   `protobuf:"bytes,21,opt,name=pet_kindof_str,json=petKindofStr,proto3" json:"pet_kindof_str"`
	DogLicenceCode       string   `protobuf:"bytes,22,opt,name=dog_licence_code,json=dogLicenceCode,proto3" json:"dog_licence_code"`
	DogVaccinateCode     string   `protobuf:"bytes,23,opt,name=dog_vaccinate_code,json=dogVaccinateCode,proto3" json:"dog_vaccinate_code"`
	FaceId               string   `protobuf:"bytes,24,opt,name=face_id,json=faceId,proto3" json:"face_id"`
	PetCode              string   `protobuf:"bytes,25,opt,name=pet_code,json=petCode,proto3" json:"pet_code"`
	SendTime             string   `protobuf:"bytes,26,opt,name=send_time,json=sendTime,proto3" json:"send_time"`
	InsuranceFaceId      string   `protobuf:"bytes,27,opt,name=insurance_face_id,json=insuranceFaceId,proto3" json:"insurance_face_id"`
	EnsureCard           bool     `protobuf:"varint,28,opt,name=ensure_card,json=ensureCard,proto3" json:"ensure_card"`
	AgeStr               string   `protobuf:"bytes,30,opt,name=age_str,json=ageStr,proto3" json:"age_str"`
	AgeConversionStr     string   `protobuf:"bytes,31,opt,name=age_conversion_str,json=ageConversionStr,proto3" json:"age_conversion_str"`
	Token                string   `protobuf:"bytes,32,opt,name=token,proto3" json:"token"`
	OpenId               string   `protobuf:"bytes,33,opt,name=open_id,json=openId,proto3" json:"open_id"`
	PetFlower            string   `protobuf:"bytes,34,opt,name=pet_flower,json=petFlower,proto3" json:"pet_flower"`
	FlowerCode           string   `protobuf:"bytes,35,opt,name=flower_code,json=flowerCode,proto3" json:"flower_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetInfoUpdateRequest) Reset()         { *m = PetInfoUpdateRequest{} }
func (m *PetInfoUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*PetInfoUpdateRequest) ProtoMessage()    {}
func (*PetInfoUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{99}
}

func (m *PetInfoUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetInfoUpdateRequest.Unmarshal(m, b)
}
func (m *PetInfoUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetInfoUpdateRequest.Marshal(b, m, deterministic)
}
func (m *PetInfoUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetInfoUpdateRequest.Merge(m, src)
}
func (m *PetInfoUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_PetInfoUpdateRequest.Size(m)
}
func (m *PetInfoUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PetInfoUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PetInfoUpdateRequest proto.InternalMessageInfo

func (m *PetInfoUpdateRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetHomeDay() string {
	if m != nil {
		return m.PetHomeDay
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetKindof() int32 {
	if m != nil {
		return m.PetKindof
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetVariety() int32 {
	if m != nil {
		return m.PetVariety
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetLong() int32 {
	if m != nil {
		return m.PetLong
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetWeight() int32 {
	if m != nil {
		return m.PetWeight
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetNeutering() int32 {
	if m != nil {
		return m.PetNeutering
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetVaccinated() int32 {
	if m != nil {
		return m.PetVaccinated
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetDeworming() int32 {
	if m != nil {
		return m.PetDeworming
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetHeight() int32 {
	if m != nil {
		return m.PetHeight
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetSource() int32 {
	if m != nil {
		return m.PetSource
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetStatus() int32 {
	if m != nil {
		return m.PetStatus
	}
	return 0
}

func (m *PetInfoUpdateRequest) GetPetRemark() string {
	if m != nil {
		return m.PetRemark
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetVarietyStr() string {
	if m != nil {
		return m.PetVarietyStr
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetKindofStr() string {
	if m != nil {
		return m.PetKindofStr
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetDogLicenceCode() string {
	if m != nil {
		return m.DogLicenceCode
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetDogVaccinateCode() string {
	if m != nil {
		return m.DogVaccinateCode
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetFaceId() string {
	if m != nil {
		return m.FaceId
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetCode() string {
	if m != nil {
		return m.PetCode
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetSendTime() string {
	if m != nil {
		return m.SendTime
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetInsuranceFaceId() string {
	if m != nil {
		return m.InsuranceFaceId
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetEnsureCard() bool {
	if m != nil {
		return m.EnsureCard
	}
	return false
}

func (m *PetInfoUpdateRequest) GetAgeStr() string {
	if m != nil {
		return m.AgeStr
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetAgeConversionStr() string {
	if m != nil {
		return m.AgeConversionStr
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetPetFlower() string {
	if m != nil {
		return m.PetFlower
	}
	return ""
}

func (m *PetInfoUpdateRequest) GetFlowerCode() string {
	if m != nil {
		return m.FlowerCode
	}
	return ""
}

type PetInfoUpdateResponse struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Data                 string   `protobuf:"bytes,2,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetInfoUpdateResponse) Reset()         { *m = PetInfoUpdateResponse{} }
func (m *PetInfoUpdateResponse) String() string { return proto.CompactTextString(m) }
func (*PetInfoUpdateResponse) ProtoMessage()    {}
func (*PetInfoUpdateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1591c64521b238ef, []int{100}
}

func (m *PetInfoUpdateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetInfoUpdateResponse.Unmarshal(m, b)
}
func (m *PetInfoUpdateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetInfoUpdateResponse.Marshal(b, m, deterministic)
}
func (m *PetInfoUpdateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetInfoUpdateResponse.Merge(m, src)
}
func (m *PetInfoUpdateResponse) XXX_Size() int {
	return xxx_messageInfo_PetInfoUpdateResponse.Size(m)
}
func (m *PetInfoUpdateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PetInfoUpdateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PetInfoUpdateResponse proto.InternalMessageInfo

func (m *PetInfoUpdateResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *PetInfoUpdateResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func init() {
	proto.RegisterType((*EmptyReq)(nil), "cc.EmptyReq")
	proto.RegisterType((*Response)(nil), "cc.Response")
	proto.RegisterType((*BaseResponseNew)(nil), "cc.BaseResponseNew")
	proto.RegisterType((*EquityGetcouponReq)(nil), "cc.EquityGetcouponReq")
	proto.RegisterType((*MemberEquityDetailReq)(nil), "cc.MemberEquityDetailReq")
	proto.RegisterType((*MemberEquitySetReq)(nil), "cc.MemberEquitySetReq")
	proto.RegisterType((*MemberEquityDetailRes)(nil), "cc.MemberEquityDetailRes")
	proto.RegisterType((*EquityIndexReq)(nil), "cc.EquityIndexReq")
	proto.RegisterType((*EquityIndexRes)(nil), "cc.EquityIndexRes")
	proto.RegisterType((*EquityIndex)(nil), "cc.EquityIndex")
	proto.RegisterType((*EquityDataList)(nil), "cc.EquityDataList")
	proto.RegisterType((*TaskFinishReq)(nil), "cc.TaskFinishReq")
	proto.RegisterType((*UserEquityReq)(nil), "cc.UserEquityReq")
	proto.RegisterType((*UserEquityRes)(nil), "cc.UserEquityRes")
	proto.RegisterType((*UserEquityData)(nil), "cc.UserEquityData")
	proto.RegisterType((*EquityCoupon)(nil), "cc.EquityCoupon")
	proto.RegisterType((*MemberHealthDetailReq)(nil), "cc.MemberHealthDetailReq")
	proto.RegisterType((*MemberHealthDetailRes)(nil), "cc.MemberHealthDetailRes")
	proto.RegisterType((*MemberHealthDetailData)(nil), "cc.MemberHealthDetailData")
	proto.RegisterType((*MemberHealthValReq)(nil), "cc.MemberHealthValReq")
	proto.RegisterType((*MemberHealthValRes)(nil), "cc.MemberHealthValRes")
	proto.RegisterType((*MemberTaskListReq)(nil), "cc.MemberTaskListReq")
	proto.RegisterType((*MemberTaskListRes)(nil), "cc.MemberTaskListRes")
	proto.RegisterType((*MemberTaskListData)(nil), "cc.MemberTaskListData")
	proto.RegisterType((*LevelList)(nil), "cc.LevelList")
	proto.RegisterType((*MemberTaskList)(nil), "cc.MemberTaskList")
	proto.RegisterType((*UserLevelEquitiesReq)(nil), "cc.UserLevelEquitiesReq")
	proto.RegisterType((*UserEquity)(nil), "cc.UserEquity")
	proto.RegisterType((*UserLevelEquitiesRes)(nil), "cc.UserLevelEquitiesRes")
	proto.RegisterType((*MemberEquityEditReq)(nil), "cc.MemberEquityEditReq")
	proto.RegisterType((*MemberEquityListReq)(nil), "cc.MemberEquityListReq")
	proto.RegisterType((*MemberEquityListRes)(nil), "cc.MemberEquityListRes")
	proto.RegisterType((*MemberEquityListData)(nil), "cc.MemberEquityListData")
	proto.RegisterType((*TaskSaveReq)(nil), "cc.TaskSaveReq")
	proto.RegisterType((*TaskListReq)(nil), "cc.TaskListReq")
	proto.RegisterType((*TaskListRes)(nil), "cc.TaskListRes")
	proto.RegisterType((*TaskList)(nil), "cc.TaskList")
	proto.RegisterType((*UserEditEquity)(nil), "cc.UserEditEquity")
	proto.RegisterType((*UserEditEquityListRes)(nil), "cc.UserEditEquityListRes")
	proto.RegisterType((*UserLevelEditReq)(nil), "cc.UserLevelEditReq")
	proto.RegisterType((*UserLevelDetailReq)(nil), "cc.UserLevelDetailReq")
	proto.RegisterType((*LevelDetailEquity)(nil), "cc.LevelDetailEquity")
	proto.RegisterType((*UserLevelDetailRes)(nil), "cc.UserLevelDetailRes")
	proto.RegisterType((*UserLevelEquityList)(nil), "cc.UserLevelEquityList")
	proto.RegisterType((*AllUserLevelEquityListRes)(nil), "cc.AllUserLevelEquityListRes")
	proto.RegisterType((*AddUserHealthValReq)(nil), "cc.AddUserHealthValReq")
	proto.RegisterType((*NotificationState)(nil), "cc.NotificationState")
	proto.RegisterType((*SetNotificationStateReq)(nil), "cc.SetNotificationStateReq")
	proto.RegisterType((*NotificationMessage)(nil), "cc.NotificationMessage")
	proto.RegisterType((*AddNotificationMessageReq)(nil), "cc.AddNotificationMessageReq")
	proto.RegisterType((*CanSendWechatSubscribeMessageReq)(nil), "cc.CanSendWechatSubscribeMessageReq")
	proto.RegisterType((*UserNotificationReq)(nil), "cc.UserNotificationReq")
	proto.RegisterType((*UserNotificationRes)(nil), "cc.UserNotificationRes")
	proto.RegisterType((*MessageValue)(nil), "cc.MessageValue")
	proto.RegisterType((*SendSubscribeMessageReq)(nil), "cc.SendSubscribeMessageReq")
	proto.RegisterType((*UserLevelSetReq)(nil), "cc.UserLevelSetReq")
	proto.RegisterType((*UserLevelListReq)(nil), "cc.UserLevelListReq")
	proto.RegisterType((*UserLevelListRes)(nil), "cc.UserLevelListRes")
	proto.RegisterType((*UserLevelList)(nil), "cc.UserLevelList")
	proto.RegisterType((*UserRequestLogRequest)(nil), "cc.UserRequestLogRequest")
	proto.RegisterType((*UserRequestLogResonse)(nil), "cc.UserRequestLogResonse")
	proto.RegisterType((*ManualQueryUsuallyRecordProductRequest)(nil), "cc.ManualQueryUsuallyRecordProductRequest")
	proto.RegisterType((*ManualQueryUsuallyRecordProductResponse)(nil), "cc.ManualQueryUsuallyRecordProductResponse")
	proto.RegisterType((*AddressInfoRequest)(nil), "cc.AddressInfoRequest")
	proto.RegisterType((*GetAddressInfoRequest)(nil), "cc.GetAddressInfoRequest")
	proto.RegisterType((*GetAddressInfoResponse)(nil), "cc.GetAddressInfoResponse")
	proto.RegisterType((*GetAddressListResponse)(nil), "cc.GetAddressListResponse")
	proto.RegisterType((*AddressList)(nil), "cc.AddressList")
	proto.RegisterType((*GetAddressListRequest)(nil), "cc.GetAddressListRequest")
	proto.RegisterType((*BaseResponse)(nil), "cc.baseResponse")
	proto.RegisterType((*ResResponse)(nil), "cc.ResResponse")
	proto.RegisterType((*TagsDto)(nil), "cc.tagsDto")
	proto.RegisterType((*TagsQueryRequest)(nil), "cc.tagsQueryRequest")
	proto.RegisterType((*TagsQueryResponse)(nil), "cc.tagsQueryResponse")
	proto.RegisterType((*RecommendProductQueryRequest)(nil), "cc.recommendProductQueryRequest")
	proto.RegisterType((*RecommendProductDto)(nil), "cc.recommendProductDto")
	proto.RegisterType((*RecommendProductQueryResponse)(nil), "cc.recommendProductQueryResponse")
	proto.RegisterType((*PetTagQueryRequest)(nil), "cc.petTagQueryRequest")
	proto.RegisterType((*PetTagGenerateRequest)(nil), "cc.petTagGenerateRequest")
	proto.RegisterType((*PetTagDto)(nil), "cc.petTagDto")
	proto.RegisterType((*PetTagQueryResponse)(nil), "cc.petTagQueryResponse")
	proto.RegisterType((*QueryUsuallyRecordProductResponse)(nil), "cc.QueryUsuallyRecordProductResponse")
	proto.RegisterType((*ProductInfo)(nil), "cc.ProductInfo")
	proto.RegisterType((*TagOperateRequest)(nil), "cc.TagOperateRequest")
	proto.RegisterType((*TagOperateResponse)(nil), "cc.TagOperateResponse")
	proto.RegisterType((*ContentTagRequest)(nil), "cc.ContentTagRequest")
	proto.RegisterType((*ContentTagResponse)(nil), "cc.ContentTagResponse")
	proto.RegisterType((*TagQueryData)(nil), "cc.TagQueryData")
	proto.RegisterType((*MemberProductDiscountRequest)(nil), "cc.MemberProductDiscountRequest")
	proto.RegisterType((*MemberProductDiscountResponse)(nil), "cc.MemberProductDiscountResponse")
	proto.RegisterType((*MessageInfoRequest)(nil), "cc.MessageInfoRequest")
	proto.RegisterType((*MessageInfoResponse)(nil), "cc.MessageInfoResponse")
	proto.RegisterType((*MessageInfo)(nil), "cc.MessageInfo")
	proto.RegisterType((*MemberCouponsMessageResponse)(nil), "cc.MemberCouponsMessageResponse")
	proto.RegisterType((*MemberCouponsMessage)(nil), "cc.MemberCouponsMessage")
	proto.RegisterType((*MemberNewMessageRequest)(nil), "cc.MemberNewMessageRequest")
	proto.RegisterType((*MemberNewMessageResponseData)(nil), "cc.MemberNewMessageResponseData")
	proto.RegisterType((*MemberNewMessageResponseData_NewMessageData)(nil), "cc.MemberNewMessageResponseData.NewMessageData")
	proto.RegisterType((*MemberNewMessageResponse)(nil), "cc.MemberNewMessageResponse")
	proto.RegisterType((*InfoUpdateRequest)(nil), "cc.InfoUpdateRequest")
	proto.RegisterType((*PetInfoUpdateRequest)(nil), "cc.PetInfoUpdateRequest")
	proto.RegisterType((*PetInfoUpdateResponse)(nil), "cc.PetInfoUpdateResponse")
}

func init() { proto.RegisterFile("cc/customerCenter.proto", fileDescriptor_1591c64521b238ef) }

var fileDescriptor_1591c64521b238ef = []byte{
	// 5375 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x7c, 0x4b, 0x8f, 0x1c, 0x47,
	0x72, 0x30, 0xfa, 0x35, 0xdd, 0x1d, 0xdd, 0xf3, 0x2a, 0xce, 0xa3, 0xa7, 0x29, 0x2e, 0x87, 0x45,
	0x49, 0xcb, 0x4f, 0xe2, 0x47, 0xbd, 0x28, 0xad, 0x04, 0x79, 0x85, 0x1d, 0x71, 0x28, 0x6a, 0x56,
	0x24, 0x97, 0xea, 0x19, 0x52, 0x07, 0xdb, 0x68, 0xd4, 0x54, 0xe5, 0xf4, 0x14, 0xa6, 0xbb, 0xaa,
	0x58, 0x99, 0x3d, 0x64, 0x0b, 0x30, 0x60, 0x63, 0x81, 0x35, 0x60, 0xac, 0x0d, 0xf8, 0x6c, 0xc0,
	0x3e, 0xfa, 0x64, 0xc0, 0x86, 0xff, 0x83, 0xed, 0x83, 0x01, 0xc3, 0x57, 0x03, 0xbe, 0x19, 0x86,
	0x7d, 0xf3, 0xc5, 0x27, 0x1f, 0x6c, 0x64, 0x44, 0x66, 0x55, 0xd6, 0xa3, 0xe7, 0x85, 0xd5, 0xad,
	0x33, 0x22, 0x32, 0x33, 0x32, 0x32, 0x22, 0x32, 0x22, 0x32, 0xab, 0x61, 0xd3, 0x75, 0xdf, 0x73,
	0xa7, 0x5c, 0x84, 0x13, 0x16, 0x3f, 0x60, 0x81, 0x60, 0xf1, 0xbd, 0x28, 0x0e, 0x45, 0x68, 0x55,
	0x5d, 0xd7, 0x06, 0x68, 0x3d, 0x9c, 0x44, 0x62, 0x36, 0x60, 0x2f, 0xed, 0x4f, 0xa1, 0x35, 0x60,
	0x3c, 0x0a, 0x03, 0xce, 0x2c, 0x0b, 0xea, 0x6e, 0xe8, 0xb1, 0x5e, 0x65, 0xbb, 0x72, 0xa7, 0x36,
	0xc0, 0xdf, 0x56, 0x0f, 0x9a, 0x13, 0xc6, 0xb9, 0x33, 0x62, 0xbd, 0xea, 0x76, 0xe5, 0x4e, 0x7b,
	0xa0, 0x9b, 0xf6, 0x6d, 0x58, 0xfe, 0xd2, 0xe1, 0x4c, 0xf7, 0x7e, 0xca, 0x5e, 0x59, 0x2b, 0x50,
	0x9b, 0xf0, 0x11, 0xf6, 0x6f, 0x0f, 0xe4, 0x4f, 0x9b, 0x83, 0xf5, 0xf0, 0xe5, 0xd4, 0x17, 0xb3,
	0x47, 0x4c, 0xb8, 0xe1, 0x34, 0x0a, 0x83, 0x01, 0x7b, 0x69, 0x5d, 0x87, 0x36, 0x35, 0x86, 0xbe,
	0x87, 0xd4, 0x8d, 0x41, 0x8b, 0x00, 0x7b, 0x9e, 0x75, 0x13, 0x3a, 0x0a, 0x29, 0x66, 0x11, 0xcd,
	0xda, 0x18, 0x00, 0x81, 0x0e, 0x66, 0x11, 0x93, 0x04, 0xdc, 0x8d, 0x27, 0xc3, 0x29, 0x67, 0xb1,
	0xef, 0xf5, 0x6a, 0x38, 0x1b, 0x48, 0xd0, 0x73, 0x84, 0xd8, 0x3f, 0x86, 0xf5, 0x27, 0x6c, 0x72,
	0xc8, 0x62, 0x9a, 0x7a, 0x97, 0x09, 0xc7, 0x1f, 0xcb, 0x79, 0x97, 0xa0, 0x9a, 0x4c, 0x58, 0xf5,
	0x3d, 0xfb, 0x01, 0x58, 0x26, 0xe1, 0x3e, 0x13, 0x25, 0x54, 0xd6, 0x0d, 0x00, 0x9f, 0x0f, 0x3d,
	0x9f, 0x47, 0x63, 0x67, 0xa6, 0xf8, 0x69, 0xfb, 0x7c, 0x97, 0x00, 0xf6, 0x7f, 0x55, 0xca, 0xa7,
	0xe3, 0x45, 0x71, 0xa8, 0xa1, 0xab, 0xc9, 0xd0, 0x37, 0xa1, 0xc3, 0xb0, 0xd3, 0x30, 0x70, 0x26,
	0x4c, 0x2f, 0x85, 0x40, 0x4f, 0x9d, 0x09, 0x6e, 0x89, 0xef, 0x86, 0x41, 0xaf, 0x8e, 0x18, 0xfc,
	0x6d, 0x74, 0xf2, 0x83, 0xa3, 0xb0, 0xd7, 0x30, 0x3b, 0xed, 0x05, 0x47, 0xa1, 0x75, 0x0b, 0xba,
	0x8a, 0x20, 0x9e, 0x8e, 0x19, 0xef, 0x2d, 0x20, 0x85, 0xea, 0x34, 0x90, 0xa0, 0xdc, 0x9a, 0x9a,
	0xb9, 0x35, 0x29, 0xf4, 0x69, 0x38, 0x75, 0x8f, 0x59, 0xdc, 0x6b, 0x69, 0xf4, 0x0b, 0x02, 0xd8,
	0x1f, 0xc0, 0xd2, 0x43, 0x35, 0x9d, 0xc7, 0x5e, 0x4b, 0x99, 0xe5, 0xf6, 0xa4, 0x52, 0xd8, 0x93,
	0x5f, 0x55, 0x73, 0x7d, 0xca, 0xc4, 0xb3, 0x05, 0xad, 0x31, 0x3b, 0x65, 0xe3, 0x61, 0x22, 0xa4,
	0x26, 0xb6, 0xf7, 0x3c, 0xeb, 0x0e, 0xac, 0xc8, 0xb1, 0x87, 0x84, 0x67, 0xc2, 0x57, 0xe2, 0x6a,
	0x0c, 0x96, 0x24, 0xfc, 0xb1, 0x04, 0x3f, 0x94, 0x50, 0xc9, 0xfb, 0x31, 0x73, 0xc6, 0xe2, 0x78,
	0x78, 0xea, 0x8c, 0x51, 0x70, 0x8d, 0x41, 0x9b, 0x20, 0x2f, 0x9c, 0xb1, 0xf5, 0x0e, 0xac, 0x1e,
	0xc5, 0x8c, 0x7d, 0xcf, 0x86, 0x06, 0x55, 0x03, 0xa9, 0x96, 0x09, 0xf1, 0x75, 0x42, 0xfb, 0xff,
	0xc1, 0x0a, 0x42, 0xe1, 0x1f, 0xf9, 0xae, 0x23, 0xfc, 0x30, 0x18, 0x72, 0xe1, 0x08, 0x86, 0xe2,
	0x6c, 0x0c, 0x56, 0x4d, 0xcc, 0xbe, 0x44, 0x58, 0xb7, 0xa1, 0xee, 0x39, 0xc2, 0xe9, 0x35, 0xb7,
	0x6b, 0x77, 0x3a, 0x1f, 0x2e, 0xdf, 0x73, 0xdd, 0x7b, 0xe6, 0x92, 0x11, 0x69, 0xff, 0x61, 0x15,
	0x3a, 0x06, 0x34, 0xb3, 0xe6, 0x4a, 0x76, 0xcd, 0x37, 0x00, 0x08, 0x85, 0xca, 0x41, 0xe6, 0xd7,
	0x46, 0x08, 0xea, 0x46, 0x76, 0xa1, 0xb5, 0xfc, 0x42, 0x7f, 0x04, 0x70, 0xe8, 0xb8, 0x27, 0xa3,
	0x38, 0x9c, 0x06, 0x9e, 0x52, 0x20, 0x03, 0x62, 0xbd, 0x0b, 0xab, 0x2e, 0x7a, 0x86, 0xa1, 0x41,
	0x46, 0xaa, 0xb2, 0x42, 0x88, 0x2f, 0x53, 0xe2, 0x8f, 0x12, 0x9d, 0x1b, 0xfb, 0x5c, 0xf4, 0x1a,
	0xb8, 0x42, 0x2b, 0x5d, 0xe1, 0xae, 0x23, 0x9c, 0xc7, 0x3e, 0x17, 0x5a, 0x0f, 0xe5, 0xef, 0x94,
	0x7f, 0x54, 0xe1, 0xa6, 0xc1, 0xff, 0x9e, 0x1b, 0x06, 0x76, 0xac, 0x35, 0x42, 0x77, 0xce, 0x9b,
	0x43, 0x65, 0xae, 0x39, 0x54, 0x0d, 0x73, 0xb8, 0x0e, 0x6d, 0x9f, 0x0f, 0x39, 0x1b, 0x33, 0x57,
	0x28, 0x29, 0xb4, 0x7c, 0xbe, 0x8f, 0x6d, 0x65, 0x70, 0xf5, 0xc4, 0xe2, 0x7f, 0x17, 0x16, 0x0f,
	0x1c, 0x7e, 0xf2, 0x95, 0x1f, 0xf8, 0xfc, 0x58, 0x2a, 0xee, 0x26, 0x34, 0x85, 0xc3, 0x4f, 0x52,
	0xe9, 0x2f, 0xc8, 0xe6, 0x9e, 0x27, 0xa7, 0x32, 0xfc, 0x0f, 0xfe, 0x3e, 0xdf, 0xf3, 0xbc, 0x0f,
	0x8b, 0xf2, 0x17, 0x2d, 0xeb, 0x42, 0x76, 0xb1, 0x97, 0xed, 0x51, 0x66, 0x15, 0x6f, 0x2b, 0xb5,
	0xaa, 0xa6, 0x42, 0x4f, 0xbb, 0x48, 0xd9, 0x29, 0xcd, 0xfa, 0xfb, 0x2a, 0x2c, 0x65, 0x11, 0x05,
	0x57, 0x96, 0x13, 0x70, 0xb5, 0x20, 0xe0, 0x9c, 0x6f, 0xa9, 0x9d, 0xe1, 0x5b, 0x70, 0x23, 0x95,
	0x5e, 0xa9, 0x4e, 0x68, 0x85, 0x05, 0xf7, 0xd3, 0x98, 0xe7, 0x7e, 0xb4, 0x7f, 0x59, 0xc8, 0xf9,
	0x97, 0x64, 0x9b, 0x9b, 0xc6, 0x36, 0x7f, 0x90, 0x1c, 0x0b, 0xa8, 0x81, 0x2d, 0x14, 0xc6, 0x4a,
	0xaa, 0x81, 0x0f, 0xe8, 0x74, 0x51, 0x07, 0x05, 0xaa, 0xd3, 0x4f, 0x60, 0x89, 0x8b, 0x30, 0x66,
	0x7a, 0x22, 0xde, 0x6b, 0xcf, 0xe9, 0xb5, 0x88, 0x74, 0x6a, 0x7a, 0x6e, 0xff, 0x7e, 0x0d, 0xba,
	0x26, 0xbe, 0x78, 0x60, 0xb5, 0x4b, 0x0f, 0x2c, 0x53, 0xa8, 0x04, 0x42, 0xa1, 0x6e, 0xc0, 0x82,
	0xf4, 0x1c, 0x53, 0xae, 0xd4, 0x53, 0xb5, 0xac, 0xb7, 0x61, 0x59, 0x71, 0x36, 0x14, 0xc3, 0x28,
	0xf6, 0x5d, 0xa6, 0x34, 0x75, 0x51, 0x81, 0x0f, 0x9e, 0x49, 0x60, 0x96, 0x6e, 0xec, 0x4f, 0x7c,
	0x81, 0x32, 0xad, 0xa6, 0x74, 0x8f, 0x25, 0xd0, 0xfa, 0x7f, 0xb0, 0xe2, 0x44, 0xd1, 0xd8, 0x77,
	0x9d, 0xc3, 0x31, 0x1b, 0x72, 0x37, 0x8c, 0xb4, 0xb3, 0x5a, 0x4e, 0xe1, 0xfb, 0x12, 0x6c, 0x7d,
	0x0c, 0x9b, 0x7a, 0x48, 0x2e, 0x9c, 0x58, 0x0c, 0x3d, 0x47, 0xb0, 0xa1, 0x60, 0xaf, 0x85, 0x12,
	0xfa, 0x9a, 0x42, 0xef, 0x4b, 0xec, 0xae, 0x23, 0xd8, 0x01, 0x7b, 0x2d, 0xac, 0x0f, 0x60, 0x5d,
	0x77, 0x63, 0x81, 0x67, 0x74, 0x6a, 0x61, 0x27, 0x4b, 0x21, 0x1f, 0x06, 0x5e, 0xd2, 0xe5, 0x16,
	0x74, 0x75, 0x17, 0xcf, 0x99, 0xc9, 0x2d, 0x90, 0xc1, 0x45, 0x47, 0xc1, 0x76, 0x9d, 0x19, 0x4f,
	0x4c, 0x0d, 0x52, 0x53, 0xb3, 0xff, 0x22, 0x39, 0x55, 0xc9, 0x1d, 0xa7, 0x87, 0xb8, 0xa6, 0xae,
	0x18, 0x86, 0x79, 0x1d, 0xda, 0x91, 0x33, 0x62, 0x43, 0xee, 0x7f, 0xaf, 0x2d, 0xb6, 0x25, 0x01,
	0xfb, 0xfe, 0xf7, 0xe8, 0x27, 0x11, 0xe9, 0x4b, 0x7f, 0xab, 0xfd, 0xa4, 0x84, 0x90, 0x03, 0xce,
	0x99, 0x68, 0x3d, 0x6f, 0xa2, 0xd6, 0x3a, 0x2c, 0x84, 0xf1, 0x48, 0xee, 0x3c, 0x1d, 0x13, 0x8d,
	0x30, 0x1e, 0xed, 0x79, 0x76, 0x58, 0xce, 0x60, 0x99, 0x05, 0xaf, 0x41, 0x43, 0x84, 0xc2, 0x19,
	0x2b, 0xd6, 0xa8, 0x61, 0xdd, 0x53, 0x76, 0x5d, 0x43, 0xa5, 0xec, 0x4b, 0xa5, 0x2c, 0x0e, 0x68,
	0xd8, 0xf7, 0x7f, 0x57, 0x61, 0xa3, 0x9c, 0xa0, 0x54, 0x26, 0x72, 0x52, 0x5f, 0x8c, 0xb5, 0x42,
	0x52, 0x83, 0x84, 0x31, 0x1b, 0x3a, 0x93, 0x70, 0x1a, 0x90, 0xbb, 0xac, 0x4a, 0x61, 0xcc, 0x76,
	0x10, 0x60, 0xdd, 0x86, 0xc5, 0x98, 0x1d, 0x4d, 0x03, 0x4f, 0x53, 0xd4, 0x91, 0xa2, 0x4b, 0x40,
	0x45, 0xb4, 0x05, 0xad, 0x30, 0xf6, 0xa4, 0xea, 0x04, 0xca, 0xb8, 0x9b, 0xd8, 0xde, 0x47, 0x43,
	0xe1, 0xc7, 0x61, 0x44, 0x96, 0x40, 0x87, 0x49, 0x4b, 0x02, 0xd0, 0x0e, 0x7a, 0xd0, 0x74, 0xc3,
	0x40, 0xb0, 0x40, 0x2b, 0x99, 0x6e, 0xe6, 0x8e, 0xb2, 0x56, 0xfe, 0x28, 0xbb, 0x09, 0x1d, 0x85,
	0xc6, 0x55, 0xb6, 0x29, 0x24, 0x24, 0x90, 0x0e, 0x09, 0xd9, 0xd1, 0x11, 0x73, 0xc5, 0x10, 0x03,
	0x03, 0x50, 0x6e, 0x0b, 0x41, 0x07, 0x3e, 0xf9, 0x35, 0x37, 0x66, 0xa8, 0xae, 0x92, 0xa0, 0xa3,
	0x6c, 0x14, 0x41, 0x48, 0xb0, 0x05, 0x2d, 0x29, 0x17, 0xc4, 0x76, 0x89, 0xb9, 0xc8, 0x99, 0x49,
	0x94, 0xfd, 0x89, 0x8e, 0x12, 0x93, 0xc0, 0x40, 0xaa, 0xe1, 0x36, 0x74, 0x13, 0xb5, 0x19, 0x16,
	0x5d, 0xfb, 0x9e, 0x67, 0xff, 0xb2, 0x5a, 0xd2, 0x91, 0xe7, 0xd6, 0x5a, 0xc9, 0xaf, 0x75, 0x13,
	0x9a, 0x23, 0x27, 0x42, 0x1c, 0x69, 0xcb, 0xc2, 0xc8, 0x89, 0x24, 0x42, 0xa9, 0x55, 0x2d, 0x55,
	0xab, 0xdb, 0xb0, 0xe8, 0x4e, 0xe3, 0x98, 0x05, 0xc2, 0x70, 0xc6, 0x8d, 0x41, 0x57, 0x01, 0xc9,
	0x1b, 0xbf, 0x09, 0x4b, 0x01, 0x7b, 0xad, 0x28, 0x8c, 0x60, 0xa7, 0x2b, 0xa1, 0x48, 0xa2, 0xa2,
	0xa2, 0xcc, 0x50, 0x48, 0xa8, 0x7c, 0x87, 0x39, 0x9c, 0xa4, 0xbd, 0x0b, 0x56, 0x96, 0x16, 0x37,
	0xbb, 0xa9, 0x22, 0x07, 0x83, 0x58, 0x6e, 0xba, 0x7d, 0x1f, 0x56, 0x49, 0x08, 0xf2, 0xdc, 0xc5,
	0x10, 0xe1, 0x22, 0xc7, 0xe2, 0xb7, 0xc5, 0x5e, 0x65, 0x86, 0xf5, 0x4e, 0x72, 0x34, 0x56, 0xee,
	0x74, 0x3e, 0xdc, 0x48, 0x4d, 0x48, 0x77, 0x33, 0xcc, 0xe7, 0xaf, 0x2a, 0x7a, 0x3b, 0x4c, 0xe4,
	0x79, 0xdb, 0x71, 0x46, 0x48, 0xfa, 0x1e, 0xb4, 0x31, 0x74, 0xc0, 0xf3, 0xa8, 0x96, 0x1e, 0xce,
	0x39, 0xc6, 0x5b, 0x42, 0xfd, 0xb2, 0xee, 0xea, 0x78, 0x08, 0x7b, 0xd4, 0xb1, 0xc7, 0xa2, 0xec,
	0x81, 0xd2, 0x42, 0x62, 0x0a, 0x8f, 0xe4, 0x4f, 0xdb, 0x83, 0x76, 0x02, 0xff, 0xc1, 0xa2, 0x44,
	0xfb, 0x8f, 0x2b, 0xb0, 0x94, 0x65, 0x78, 0x7e, 0x48, 0x74, 0x5d, 0x2d, 0xd8, 0x98, 0x08, 0x17,
	0x87, 0xf3, 0x6c, 0x01, 0xfe, 0x36, 0x66, 0xc1, 0x51, 0xa4, 0x0c, 0xd3, 0xf3, 0xaf, 0x9e, 0x39,
	0xff, 0xf4, 0x31, 0xdf, 0x48, 0x8f, 0x79, 0xfb, 0x03, 0x58, 0x7b, 0x9e, 0xc4, 0xf3, 0xf2, 0x08,
	0xf6, 0x19, 0x97, 0x1a, 0x93, 0x17, 0x40, 0x2d, 0x11, 0x80, 0x1d, 0x03, 0xa4, 0x61, 0x8f, 0x11,
	0xf2, 0xd4, 0x2e, 0x16, 0xf2, 0x68, 0x2e, 0x6a, 0xf3, 0x53, 0xac, 0x7a, 0x3e, 0x0c, 0xb2, 0x1f,
	0x97, 0xb2, 0x59, 0xa6, 0xa2, 0x36, 0xd4, 0x71, 0xbb, 0x29, 0x7a, 0x5b, 0xca, 0x46, 0x6f, 0x03,
	0xc4, 0xd9, 0xff, 0x59, 0x81, 0x6b, 0x66, 0x0a, 0xf9, 0xd0, 0xf3, 0x4b, 0x33, 0xd1, 0xcb, 0x84,
	0x6f, 0xe9, 0x92, 0x34, 0xdf, 0x17, 0x59, 0xd8, 0xc5, 0x83, 0x37, 0x9d, 0x3b, 0x2e, 0x9c, 0x9d,
	0x3b, 0x36, 0xf3, 0xb9, 0xe3, 0x9f, 0xe6, 0xd6, 0x6a, 0xb8, 0x84, 0xb3, 0x63, 0xff, 0xec, 0x31,
	0x5e, 0xcd, 0x1f, 0xe3, 0x99, 0x10, 0xa0, 0x56, 0x0c, 0x01, 0x0c, 0x96, 0xeb, 0xf9, 0x14, 0xfe,
	0xa4, 0x8c, 0xa5, 0x8b, 0x1f, 0xe4, 0x77, 0x33, 0x07, 0x79, 0x2f, 0xf5, 0x01, 0xe9, 0x70, 0x86,
	0x1f, 0xfa, 0xb3, 0x0a, 0xac, 0x95, 0xa1, 0x2f, 0xbf, 0xdb, 0x57, 0xd1, 0xdc, 0x9c, 0x28, 0x1a,
	0x79, 0x51, 0xbc, 0x07, 0x1d, 0xe9, 0x08, 0xf6, 0x9d, 0x53, 0x46, 0xa7, 0x1c, 0x69, 0x6f, 0x05,
	0x97, 0xd6, 0x95, 0x4b, 0x4b, 0x1c, 0x1b, 0xe9, 0xee, 0x22, 0x75, 0x50, 0xdb, 0x68, 0xef, 0x98,
	0xcd, 0x32, 0x11, 0x6e, 0x67, 0xb2, 0x99, 0xdc, 0x88, 0x28, 0xa0, 0x10, 0x5a, 0x89, 0x2f, 0xca,
	0xcb, 0xe4, 0xaa, 0x2e, 0x28, 0x93, 0x24, 0xd6, 0xb3, 0x49, 0xa2, 0xfd, 0x07, 0x15, 0x95, 0x38,
	0x79, 0xbe, 0x38, 0xd7, 0x8b, 0x24, 0x49, 0x61, 0x4d, 0x8b, 0x35, 0x89, 0x40, 0xce, 0xac, 0xe4,
	0x64, 0xcd, 0xa2, 0x8e, 0x03, 0x18, 0x66, 0x31, 0x81, 0xf5, 0x2c, 0x0b, 0x5a, 0x82, 0x97, 0x2a,
	0xca, 0xc9, 0x5c, 0x31, 0x7f, 0x1c, 0x65, 0x87, 0x55, 0xbb, 0xf6, 0xd7, 0x35, 0x58, 0x49, 0x1d,
	0x58, 0xc1, 0xdd, 0xd4, 0x74, 0xe1, 0xeb, 0xac, 0x93, 0x25, 0x5b, 0x60, 0xa8, 0x15, 0x0a, 0x0c,
	0xc5, 0x42, 0x4c, 0xcd, 0x3c, 0x59, 0x6f, 0xc3, 0x62, 0x14, 0xfb, 0xa7, 0xfe, 0x98, 0x49, 0xa3,
	0xf6, 0xb4, 0xab, 0xe9, 0x26, 0xc0, 0x3d, 0x8f, 0x5b, 0xf7, 0x61, 0x63, 0x14, 0x86, 0x1e, 0x1f,
	0x4e, 0xa3, 0x51, 0xec, 0x78, 0x46, 0x2a, 0x47, 0xc1, 0xe5, 0x1a, 0x62, 0x9f, 0x13, 0x52, 0xe7,
	0x6f, 0xd2, 0x89, 0x4d, 0xd0, 0xc2, 0x54, 0x56, 0x45, 0xd9, 0x49, 0x87, 0x60, 0x94, 0x53, 0xdd,
	0x87, 0x0d, 0xca, 0x0d, 0x0b, 0x03, 0xb7, 0x69, 0x60, 0xc4, 0xe6, 0x07, 0xbe, 0x07, 0xd7, 0xa8,
	0xd7, 0x2b, 0xc6, 0x4e, 0xd2, 0x2e, 0x14, 0x6f, 0xae, 0x22, 0xea, 0x3b, 0xc6, 0x4e, 0x12, 0xfa,
	0xd2, 0x1a, 0x4b, 0x67, 0x4e, 0x8d, 0x25, 0x5b, 0x2e, 0xe9, 0xe6, 0xcb, 0x25, 0x9f, 0x82, 0x95,
	0xec, 0x58, 0x9a, 0x0d, 0xd9, 0xb0, 0x68, 0xd4, 0xc5, 0x92, 0xed, 0xeb, 0x24, 0x45, 0xb1, 0x3d,
	0xcf, 0x66, 0xb0, 0x6a, 0xf4, 0xfa, 0xa1, 0x34, 0xdc, 0xfe, 0xbb, 0x7a, 0x09, 0x87, 0x97, 0x55,
	0x60, 0xf3, 0x9c, 0xaf, 0x65, 0xce, 0xf9, 0x9c, 0x3a, 0xd6, 0xcf, 0x56, 0xc7, 0xc6, 0xc5, 0xea,
	0x5d, 0x4b, 0xf3, 0xf7, 0xc2, 0xd0, 0xdd, 0x85, 0xbc, 0xee, 0xde, 0x82, 0x2e, 0xb1, 0xa2, 0xe2,
	0x9a, 0x26, 0x09, 0x1d, 0x61, 0xfb, 0x14, 0xdc, 0x14, 0xd4, 0xbb, 0x75, 0x29, 0xf5, 0x6e, 0x5f,
	0x42, 0xbd, 0xe1, 0x32, 0xea, 0xdd, 0xb9, 0xbc, 0x7a, 0x77, 0xe7, 0xa9, 0xf7, 0x27, 0xd9, 0xaa,
	0xe0, 0x22, 0x3a, 0x9d, 0xf5, 0x24, 0xa2, 0x35, 0xf5, 0xed, 0x8c, 0xc2, 0xe0, 0x72, 0x5e, 0xd3,
	0x1d, 0xb8, 0x96, 0x0d, 0xae, 0x66, 0xa5, 0x31, 0x70, 0xcd, 0x0c, 0xc5, 0x33, 0x8c, 0x94, 0xc7,
	0x5a, 0x06, 0x07, 0xf6, 0x9f, 0x54, 0x60, 0x6b, 0x67, 0x3c, 0x2e, 0x99, 0xe6, 0xf2, 0x2a, 0xfb,
	0x00, 0x56, 0x55, 0x55, 0xda, 0x60, 0x81, 0x1c, 0xf0, 0xa6, 0x66, 0x21, 0x3f, 0xc9, 0xf2, 0x38,
	0x0b, 0xb0, 0xff, 0xad, 0x0a, 0xd7, 0x76, 0x3c, 0x4f, 0xd2, 0x66, 0xd2, 0xcc, 0x4d, 0x68, 0x66,
	0x33, 0xcc, 0x85, 0x29, 0x66, 0x97, 0xf9, 0x9c, 0x58, 0xd9, 0xab, 0x91, 0x13, 0xeb, 0x9a, 0x00,
	0x59, 0x51, 0xae, 0x26, 0x50, 0x37, 0x6b, 0x02, 0x46, 0x5e, 0xde, 0xc8, 0xe6, 0xe5, 0x66, 0xa6,
	0xbf, 0x90, 0xcd, 0xf4, 0xb3, 0x85, 0x04, 0x55, 0xdc, 0x3d, 0xa3, 0x90, 0xa0, 0xd4, 0x3f, 0x53,
	0x48, 0xd8, 0x84, 0x26, 0x56, 0x0b, 0x7c, 0x4f, 0xe9, 0xfb, 0x82, 0x6c, 0x52, 0xa6, 0x91, 0x96,
	0x11, 0x20, 0x57, 0x46, 0xc8, 0x25, 0xfb, 0x9d, 0x42, 0xb2, 0x7f, 0x46, 0x2e, 0xff, 0x02, 0x56,
	0x9f, 0x16, 0xea, 0xf6, 0xef, 0x42, 0xa6, 0x98, 0x3f, 0x4c, 0x4a, 0x29, 0xed, 0xc1, 0x8a, 0x89,
	0x38, 0x50, 0x22, 0xa4, 0x6b, 0x00, 0x92, 0x38, 0x35, 0xec, 0x7f, 0xad, 0xc2, 0xe6, 0x3e, 0x13,
	0x85, 0xb1, 0x2f, 0x54, 0x29, 0x90, 0x51, 0x92, 0x33, 0x1e, 0xab, 0x11, 0xe5, 0x4f, 0x29, 0xdd,
	0xd4, 0xad, 0xab, 0x2d, 0x6c, 0x27, 0x3e, 0xdd, 0xea, 0x43, 0xcb, 0x0f, 0x04, 0x1b, 0xc5, 0xc9,
	0xc1, 0x9a, 0xb4, 0xe5, 0x6e, 0xea, 0x28, 0xa3, 0x41, 0x56, 0xa2, 0x9a, 0xb2, 0x57, 0xcc, 0x46,
	0x3e, 0x17, 0x2a, 0x2e, 0xaf, 0x0d, 0x92, 0xb6, 0xec, 0xf5, 0x72, 0xca, 0xa6, 0x7e, 0x30, 0x52,
	0xde, 0x4e, 0x37, 0xad, 0x1f, 0x41, 0xe7, 0x34, 0x1e, 0x4a, 0x1b, 0x90, 0x2b, 0x50, 0xf5, 0xbb,
	0xf6, 0x69, 0xfc, 0x20, 0xf4, 0xd8, 0x73, 0xce, 0xac, 0x37, 0x61, 0x49, 0xe3, 0xd9, 0xeb, 0xc8,
	0x8f, 0x69, 0xc3, 0x6a, 0x83, 0x2e, 0x91, 0x3c, 0x44, 0x18, 0xd6, 0x30, 0xfd, 0x68, 0xe8, 0x3a,
	0xb1, 0xa7, 0xc9, 0x3a, 0x48, 0xb6, 0x78, 0xea, 0x47, 0x0f, 0x9c, 0xd8, 0x53, 0x74, 0x69, 0xb1,
	0xad, 0x6b, 0x16, 0xdb, 0x7e, 0x0e, 0xd7, 0x4c, 0xd9, 0x3e, 0x51, 0xa6, 0x77, 0x13, 0x3a, 0x82,
	0x4d, 0xa2, 0xb1, 0x23, 0x98, 0x4e, 0xd0, 0xdb, 0x03, 0xd0, 0x20, 0xa3, 0x8a, 0x6f, 0x18, 0x81,
	0x1d, 0xc3, 0xd6, 0x8e, 0xe7, 0x95, 0x0c, 0x77, 0xb1, 0xcd, 0xfa, 0x08, 0x5a, 0xca, 0xf2, 0xb9,
	0x72, 0x34, 0x68, 0xe5, 0x65, 0xe3, 0x25, 0x84, 0xf6, 0xaf, 0x2b, 0xb0, 0xfd, 0xc0, 0x09, 0xf6,
	0x59, 0xe0, 0x7d, 0xc7, 0xdc, 0x63, 0x47, 0xec, 0x4f, 0x0f, 0xb9, 0x1b, 0xfb, 0x87, 0xec, 0x52,
	0x73, 0xbf, 0x05, 0x4b, 0x5c, 0x77, 0x4c, 0xed, 0xbe, 0x3d, 0x58, 0x4c, 0xa0, 0xfa, 0xa8, 0x36,
	0xc5, 0x52, 0xcb, 0x8b, 0xc5, 0x7e, 0x4a, 0x1e, 0xd6, 0xe4, 0xf9, 0x62, 0x0c, 0xa4, 0xdb, 0x53,
	0x35, 0xb7, 0xe7, 0x3f, 0xaa, 0x65, 0x03, 0x5e, 0xd6, 0x91, 0x2a, 0x33, 0xa8, 0xcd, 0x33, 0x83,
	0xfa, 0x59, 0x66, 0xd0, 0x98, 0x6f, 0x06, 0x0b, 0x57, 0x32, 0x83, 0x56, 0xd6, 0x0c, 0x7a, 0xd0,
	0xe4, 0x2c, 0x3e, 0x95, 0xe7, 0x2d, 0x99, 0x80, 0x6e, 0xe6, 0x0d, 0x04, 0xce, 0x37, 0x90, 0xce,
	0xc5, 0x0c, 0xa4, 0x5b, 0x62, 0x20, 0xf6, 0xa7, 0xd0, 0x55, 0x2a, 0xf3, 0xc2, 0x19, 0x4f, 0x59,
	0xa6, 0xf4, 0xdb, 0x4e, 0xdd, 0xfc, 0xa9, 0x44, 0xea, 0xd2, 0x2f, 0x36, 0xec, 0xff, 0xa9, 0x48,
	0x1f, 0x15, 0x78, 0x3f, 0xa8, 0xea, 0xdd, 0x82, 0x6e, 0xa2, 0x7a, 0x27, 0x6c, 0xa6, 0x74, 0x2f,
	0x51, 0xc7, 0x6f, 0xd8, 0xcc, 0xba, 0x03, 0x0b, 0xc8, 0x10, 0x57, 0x25, 0xb0, 0x15, 0x4a, 0x98,
	0xd3, 0x35, 0x0d, 0x14, 0x5e, 0xea, 0x31, 0xe6, 0xf4, 0x91, 0x13, 0x3b, 0x13, 0x9d, 0x20, 0x60,
	0x15, 0xe0, 0x19, 0x42, 0xe4, 0xe2, 0x65, 0x4b, 0x9d, 0x4d, 0xf8, 0xdb, 0x50, 0xd1, 0xa6, 0xa9,
	0xa2, 0x9f, 0xc1, 0x72, 0x72, 0x10, 0x17, 0x2e, 0xfa, 0x29, 0x04, 0x4e, 0xeb, 0x54, 0xe4, 0x89,
	0x55, 0xcb, 0x7e, 0x6a, 0xe4, 0x4a, 0xba, 0x5c, 0x91, 0xad, 0x46, 0xd0, 0x18, 0xf3, 0xaa, 0x11,
	0x34, 0x5a, 0x52, 0x8d, 0xb0, 0xff, 0xbc, 0x52, 0x18, 0xf0, 0xb2, 0xa6, 0x92, 0x14, 0x22, 0xc8,
	0x58, 0x54, 0x21, 0xe2, 0x26, 0x74, 0xd2, 0xb0, 0x94, 0x29, 0x7b, 0x81, 0x24, 0x2a, 0x65, 0xd6,
	0x5b, 0x2a, 0x3d, 0xa4, 0xfb, 0xdb, 0xd5, 0x4c, 0x74, 0x62, 0xe4, 0xf4, 0xff, 0x52, 0xa1, 0x5b,
	0xc9, 0xb4, 0xfe, 0x98, 0x17, 0x55, 0xbe, 0x2c, 0x3a, 0x37, 0x4c, 0xaf, 0x9d, 0x5d, 0x8f, 0x2c,
	0x64, 0x85, 0xe7, 0x45, 0xf1, 0xf9, 0xc8, 0x7b, 0xa1, 0x18, 0x79, 0x9f, 0x73, 0xed, 0xfc, 0x19,
	0x65, 0xda, 0x03, 0xf6, 0x72, 0xca, 0xb8, 0x78, 0x1c, 0x8e, 0xd4, 0x2f, 0xb9, 0x46, 0x11, 0x2a,
	0xcd, 0xaf, 0x8a, 0x50, 0xd7, 0x2e, 0xaa, 0xe9, 0x6b, 0x96, 0xdf, 0x2e, 0x76, 0xe5, 0x85, 0x97,
	0x33, 0x8d, 0x8b, 0x6c, 0x1e, 0x8b, 0xe3, 0x30, 0x56, 0xc2, 0xa1, 0x86, 0x7d, 0x08, 0x6f, 0x3f,
	0x71, 0x82, 0xa9, 0x33, 0xfe, 0x76, 0xca, 0xe2, 0xd9, 0x73, 0x3e, 0x75, 0xc6, 0xe3, 0xd9, 0x80,
	0xb9, 0x61, 0xec, 0x3d, 0x8b, 0x43, 0x6f, 0xea, 0x0a, 0xcd, 0xe8, 0x0d, 0x00, 0xba, 0xb4, 0xc3,
	0x08, 0x87, 0x18, 0x6e, 0x23, 0x44, 0x87, 0x3f, 0x2c, 0xf0, 0x08, 0xa9, 0x66, 0x66, 0x81, 0x87,
	0xe1, 0xcf, 0x77, 0xf0, 0xe3, 0x73, 0xe7, 0x28, 0x79, 0x0c, 0x74, 0xee, 0x92, 0xec, 0x7f, 0xa8,
	0x83, 0xb5, 0xe3, 0x79, 0x31, 0xe3, 0x7c, 0x2f, 0x38, 0x0a, 0x35, 0xa7, 0xd7, 0xa1, 0x2d, 0xe2,
	0x29, 0x33, 0x4b, 0x7a, 0x2d, 0x09, 0x40, 0x4d, 0xd8, 0x84, 0xa6, 0x8b, 0x85, 0x2a, 0x7d, 0x70,
	0x2f, 0xc8, 0xe6, 0x9e, 0x27, 0x11, 0x4e, 0xcc, 0x9c, 0xf4, 0xe8, 0x5a, 0x90, 0x4d, 0x0a, 0x0b,
	0x09, 0x91, 0x96, 0xb6, 0x5a, 0x88, 0x0a, 0x8e, 0x42, 0xc9, 0x9c, 0x43, 0x1c, 0xe8, 0x28, 0x56,
	0x35, 0xa5, 0xce, 0xa8, 0x9f, 0x43, 0x8f, 0x71, 0x57, 0xbf, 0x87, 0x51, 0xb0, 0x5d, 0xc6, 0x5d,
	0x64, 0x94, 0x8d, 0x87, 0xd1, 0x71, 0x18, 0xe8, 0xab, 0x8c, 0x96, 0x60, 0xe3, 0x67, 0xb2, 0x2d,
	0x91, 0x93, 0xf0, 0x50, 0x21, 0x29, 0x8e, 0x6d, 0x4d, 0xc2, 0x43, 0x42, 0xaa, 0x7a, 0x1a, 0x3b,
	0x72, 0xa6, 0x63, 0xa1, 0xc2, 0xd8, 0xb6, 0xcf, 0x77, 0x09, 0x20, 0x37, 0xc3, 0x3d, 0x76, 0x7c,
	0xbc, 0x38, 0x06, 0x15, 0x5c, 0xcb, 0xf6, 0x1e, 0xba, 0x1b, 0xc7, 0x43, 0x19, 0x77, 0xd4, 0x2a,
	0xb1, 0x25, 0x1d, 0x98, 0x78, 0x3d, 0x1c, 0x3b, 0x02, 0x0f, 0x80, 0xea, 0xa0, 0x21, 0x5e, 0x3f,
	0x76, 0x84, 0x06, 0x07, 0xa3, 0xde, 0x62, 0x02, 0x0e, 0x46, 0x68, 0x4f, 0xe1, 0x94, 0x33, 0x12,
	0x0a, 0xe5, 0xb3, 0x6d, 0x84, 0xe8, 0x72, 0x9f, 0x5e, 0xbb, 0xef, 0xe9, 0x54, 0x4b, 0x41, 0x48,
	0xd4, 0xde, 0x78, 0x86, 0x11, 0xf8, 0x0a, 0x31, 0x21, 0x9b, 0x94, 0x5e, 0xa0, 0xa8, 0x15, 0x87,
	0xab, 0x64, 0x88, 0x12, 0xb4, 0x43, 0x5c, 0xde, 0x84, 0xce, 0xc4, 0x89, 0x86, 0x5a, 0xe4, 0x16,
	0x11, 0x4c, 0x9c, 0x48, 0xa9, 0x41, 0xe1, 0x48, 0xb9, 0x56, 0x38, 0x52, 0xd6, 0xa0, 0xf1, 0x0b,
	0xe9, 0x9b, 0x7b, 0x6b, 0xe4, 0xa8, 0xb1, 0x61, 0x7f, 0x02, 0xeb, 0x8f, 0x98, 0x28, 0x51, 0xa6,
	0xec, 0x52, 0xd4, 0x4d, 0x4d, 0xb2, 0x14, 0x3b, 0x86, 0x8d, 0x7c, 0xbf, 0xab, 0xa8, 0x72, 0x72,
	0xa7, 0x54, 0x4b, 0xef, 0x94, 0x8a, 0xcc, 0xa8, 0x52, 0xe5, 0x89, 0x39, 0xa7, 0xf2, 0xe4, 0x57,
	0x99, 0xf3, 0x76, 0xa6, 0x82, 0xbc, 0x6c, 0xcc, 0x69, 0xd4, 0x45, 0xff, 0xb9, 0x0e, 0x1d, 0x03,
	0x5a, 0x22, 0x8f, 0xcc, 0xd6, 0x4a, 0xad, 0xa5, 0x2a, 0x41, 0x62, 0x60, 0x2d, 0x02, 0xa8, 0xab,
	0x9c, 0xc4, 0x30, 0x6b, 0x45, 0xc3, 0xd4, 0xf6, 0x57, 0xcf, 0xd8, 0x9f, 0x61, 0xb1, 0x8d, 0x8c,
	0xc5, 0x66, 0x0c, 0x73, 0x61, 0xbe, 0x61, 0x36, 0xb3, 0x86, 0x99, 0xb1, 0xba, 0xd6, 0x59, 0x56,
	0xd7, 0x3e, 0xd3, 0xea, 0x20, 0x6f, 0x75, 0x86, 0x5a, 0x77, 0x32, 0x6a, 0xbd, 0x05, 0xc8, 0x57,
	0x62, 0x5d, 0x92, 0x99, 0x98, 0x39, 0xd2, 0xbe, 0x12, 0x94, 0xb2, 0x30, 0x8d, 0x22, 0x1b, 0x43,
	0x94, 0x78, 0x2d, 0xfb, 0x29, 0x1b, 0x93, 0x90, 0x03, 0x09, 0x30, 0xd1, 0xc1, 0x28, 0xb1, 0x31,
	0x42, 0x07, 0x23, 0x69, 0xe7, 0x3e, 0xf7, 0x98, 0x33, 0xd6, 0x26, 0x46, 0xad, 0x82, 0x5b, 0x5a,
	0x2d, 0xba, 0xa5, 0xac, 0x71, 0x5b, 0x79, 0xe3, 0xce, 0x19, 0xe9, 0xb5, 0xf3, 0x8c, 0x74, 0x2d,
	0x6f, 0xa4, 0xf6, 0x33, 0xd3, 0xd8, 0x54, 0x6c, 0x83, 0xc6, 0x76, 0xe5, 0x54, 0xe0, 0xb7, 0xa0,
	0x7b, 0x68, 0x3c, 0x0b, 0xbd, 0xe4, 0x39, 0xf2, 0x2d, 0x74, 0x06, 0x78, 0x93, 0x16, 0xfd, 0xc6,
	0xce, 0xd5, 0x5f, 0x56, 0xa0, 0x29, 0x9c, 0x11, 0xdf, 0x15, 0xa1, 0x1c, 0xcf, 0x38, 0x8a, 0xf0,
	0x77, 0x79, 0xb0, 0x2c, 0x29, 0x79, 0x18, 0xeb, 0x07, 0x65, 0xf8, 0xdb, 0xda, 0x86, 0xce, 0xb1,
	0xc3, 0x77, 0x64, 0xa2, 0xe0, 0xf3, 0x13, 0x75, 0x8d, 0x60, 0x82, 0x24, 0x6f, 0xd2, 0x54, 0x9f,
	0xc7, 0x63, 0x7d, 0x06, 0xa9, 0xa6, 0xfd, 0x05, 0xac, 0x48, 0x26, 0xf0, 0xdc, 0xd5, 0x32, 0xde,
	0x80, 0x05, 0x19, 0xd5, 0x44, 0x5c, 0xdf, 0xb3, 0x52, 0x4b, 0xce, 0x7d, 0x14, 0x87, 0x13, 0xfd,
	0xca, 0x4d, 0xfe, 0xb6, 0x0f, 0x61, 0xd5, 0xe8, 0x7f, 0x25, 0xf1, 0xdc, 0xcc, 0x38, 0x99, 0x8e,
	0x74, 0x32, 0x4a, 0x2e, 0xca, 0xc1, 0xfc, 0xaa, 0x02, 0x6f, 0xc4, 0xcc, 0x0d, 0x27, 0x13, 0x16,
	0xe8, 0x78, 0x20, 0xc3, 0xf0, 0x1b, 0x90, 0x86, 0xb8, 0xda, 0x01, 0xa7, 0x31, 0x6f, 0x1f, 0x92,
	0x10, 0xb7, 0xf0, 0x06, 0x67, 0x03, 0x54, 0xdd, 0x4a, 0x9f, 0xe8, 0xd3, 0xe4, 0x08, 0x88, 0x98,
	0xd8, 0xd3, 0x8e, 0x86, 0x1a, 0xf6, 0xbb, 0x70, 0x2d, 0xcf, 0x87, 0xdc, 0xbd, 0x35, 0x68, 0xf0,
	0x93, 0xe9, 0x9e, 0x56, 0x46, 0x6a, 0xd8, 0x7f, 0x59, 0x81, 0x1b, 0x73, 0xb8, 0xbe, 0xaa, 0x16,
	0xa5, 0xa1, 0x75, 0x72, 0xc7, 0xf7, 0xae, 0x12, 0x5e, 0x3d, 0xcd, 0xf8, 0x4b, 0x58, 0x24, 0x41,
	0x4a, 0xd3, 0xe0, 0x27, 0x53, 0x72, 0x93, 0x35, 0x39, 0x06, 0x71, 0xfa, 0x25, 0x58, 0x11, 0x13,
	0x07, 0xce, 0x28, 0xaf, 0x05, 0x4a, 0x34, 0x95, 0x72, 0xd1, 0x54, 0x4d, 0xd1, 0x3c, 0x84, 0x75,
	0x1a, 0xe3, 0x11, 0x0b, 0x58, 0x4c, 0x15, 0xa6, 0x2b, 0x0c, 0x13, 0x42, 0x9b, 0x86, 0x91, 0x72,
	0xbd, 0x54, 0x57, 0x29, 0x39, 0xe1, 0x8c, 0x9e, 0xa6, 0x07, 0x87, 0x6e, 0x4a, 0x05, 0x10, 0xce,
	0x08, 0x53, 0x38, 0x1d, 0x9d, 0xe9, 0xb6, 0x7d, 0x04, 0xd7, 0x32, 0x6b, 0xbf, 0xd2, 0xd6, 0xdc,
	0xca, 0x68, 0x30, 0x3e, 0x9d, 0x48, 0x56, 0xa1, 0x74, 0xf8, 0xd7, 0x15, 0xb8, 0xf5, 0x1b, 0x0e,
	0x6e, 0xe7, 0x68, 0xc4, 0xed, 0x8c, 0x46, 0xe0, 0x99, 0xad, 0xa6, 0xc1, 0x38, 0x81, 0xd8, 0x39,
	0x82, 0x8e, 0x01, 0x34, 0x14, 0x83, 0x66, 0x26, 0xc5, 0x90, 0x6e, 0xde, 0x3d, 0x76, 0x82, 0xc0,
	0x7c, 0x66, 0xd2, 0x56, 0x90, 0x3d, 0xcc, 0x79, 0xb8, 0x33, 0x66, 0x7c, 0x78, 0x1a, 0x8e, 0xa7,
	0xc9, 0xbb, 0xe7, 0x0e, 0xc2, 0x5e, 0x20, 0xc8, 0xfe, 0xa3, 0x0a, 0xac, 0x1e, 0x38, 0xa3, 0x5f,
	0x44, 0x19, 0x9d, 0xb8, 0x01, 0x80, 0x2e, 0xc5, 0x8c, 0xbf, 0xdb, 0x08, 0xd1, 0xf7, 0xc7, 0xc6,
	0xcd, 0x1e, 0x79, 0xc3, 0x2d, 0x68, 0x85, 0x63, 0xcf, 0x8c, 0x0b, 0x9a, 0xe1, 0xd8, 0x43, 0xf2,
	0x15, 0xa8, 0x85, 0x91, 0x50, 0x3b, 0x2b, 0x7f, 0xce, 0x7b, 0x19, 0xe7, 0x82, 0x65, 0xf2, 0x72,
	0x45, 0x67, 0xd5, 0x71, 0x62, 0xe1, 0xbb, 0x63, 0xba, 0x3c, 0xa9, 0xa1, 0x1d, 0x81, 0x02, 0xed,
	0x79, 0xdc, 0x7e, 0x07, 0x56, 0x1f, 0x50, 0x95, 0xfa, 0xc0, 0x49, 0x52, 0xb8, 0x94, 0xa1, 0x9a,
	0xc9, 0xd0, 0x31, 0x58, 0x26, 0xed, 0x95, 0x18, 0x7a, 0x33, 0xa3, 0x7b, 0x2b, 0x74, 0x6f, 0x4d,
	0xda, 0x6c, 0x5c, 0xee, 0x7f, 0x02, 0x5d, 0x13, 0x5a, 0x7a, 0xe0, 0x58, 0x50, 0x97, 0x7e, 0x57,
	0x8b, 0x5d, 0xfe, 0xb6, 0x7f, 0x02, 0x6f, 0x3c, 0x51, 0xd7, 0x37, 0xe4, 0x4b, 0x7c, 0xee, 0x86,
	0xd3, 0x20, 0x39, 0x8e, 0xe7, 0x5d, 0x03, 0xd8, 0x9f, 0xc3, 0x8d, 0x39, 0x1d, 0xd5, 0x2a, 0xfb,
	0xd0, 0xf2, 0x14, 0x4c, 0x67, 0x60, 0xba, 0x6d, 0xef, 0x82, 0xa5, 0xea, 0x2e, 0x66, 0x9c, 0x3d,
	0xf7, 0xca, 0x61, 0xce, 0x89, 0xff, 0xb7, 0xf8, 0xa2, 0xc3, 0x18, 0x46, 0xcd, 0xfc, 0x3e, 0x74,
	0x28, 0x49, 0x1f, 0xa2, 0xe0, 0x2a, 0xa9, 0x9d, 0x98, 0xd4, 0x40, 0x34, 0x28, 0xad, 0xfb, 0xb0,
	0x18, 0xc5, 0x61, 0xc4, 0x62, 0x31, 0x1b, 0x1a, 0x8f, 0x04, 0x0a, 0x7d, 0xba, 0x9a, 0x4a, 0xf7,
	0x3a, 0x9c, 0x72, 0x3f, 0xc0, 0x40, 0x2a, 0x17, 0x45, 0x67, 0x7a, 0x69, 0x2a, 0xd9, 0xcb, 0xfe,
	0x9b, 0x0a, 0x74, 0x0c, 0x6c, 0xbe, 0x66, 0x5a, 0x29, 0x94, 0x92, 0x6f, 0xc3, 0xa2, 0xd2, 0x85,
	0xa1, 0xf9, 0xae, 0xb2, 0xab, 0x80, 0x07, 0x78, 0x95, 0x82, 0x57, 0x73, 0x8a, 0x48, 0xd7, 0x9d,
	0x1b, 0x83, 0x8e, 0xa6, 0x99, 0x45, 0x78, 0x14, 0x06, 0x53, 0xb9, 0x63, 0xfa, 0x35, 0x14, 0xb5,
	0x0a, 0x95, 0xb3, 0x46, 0xa1, 0x72, 0x66, 0x3f, 0xd6, 0x5a, 0x42, 0xcf, 0x92, 0x79, 0x52, 0xc0,
	0x53, 0x12, 0xd7, 0x0f, 0x51, 0x2a, 0xf9, 0x87, 0x28, 0x39, 0x7a, 0xd2, 0xd5, 0xdf, 0xd3, 0xef,
	0x50, 0xb2, 0xd8, 0xc2, 0x1a, 0x2a, 0xc5, 0x35, 0x9c, 0xfb, 0x8d, 0x8e, 0x31, 0xc6, 0x61, 0xe8,
	0x25, 0x65, 0x40, 0x05, 0xfb, 0x32, 0xf4, 0x66, 0xf6, 0x10, 0x36, 0x69, 0xfa, 0xa7, 0xec, 0x55,
	0x5a, 0x89, 0x3c, 0x5b, 0x03, 0xd1, 0x13, 0x9f, 0xb0, 0x20, 0x79, 0xd3, 0x2a, 0x1b, 0xf3, 0xac,
	0xfe, 0x1f, 0xab, 0x5a, 0x5c, 0xe6, 0x0c, 0x24, 0x2a, 0x54, 0x9c, 0xa7, 0x00, 0x13, 0xe6, 0x0d,
	0x63, 0x3c, 0x22, 0x70, 0xa6, 0xce, 0x87, 0xef, 0xa5, 0x42, 0x2b, 0xef, 0x75, 0x2f, 0x05, 0xa3,
	0xdd, 0xb7, 0x27, 0xcc, 0xa3, 0x43, 0xc6, 0x7a, 0x04, 0x0b, 0x54, 0xc8, 0x52, 0xef, 0x11, 0x2f,
	0x3d, 0x96, 0xea, 0x6e, 0x7d, 0x63, 0xd4, 0xb5, 0x6b, 0x57, 0x1b, 0x2a, 0x19, 0xa0, 0xff, 0x15,
	0x2c, 0x65, 0x71, 0xe8, 0x12, 0x1c, 0xc1, 0x8c, 0xea, 0x51, 0xd2, 0x36, 0xef, 0x02, 0xab, 0x99,
	0xbb, 0x40, 0xfb, 0x19, 0xf4, 0xe6, 0x31, 0x60, 0xdd, 0xcf, 0xe4, 0xcc, 0xdb, 0xe7, 0x31, 0xab,
	0x14, 0xf0, 0x7f, 0xab, 0xb0, 0x2a, 0x6d, 0xef, 0x79, 0xe4, 0x19, 0x87, 0xd6, 0xdc, 0xcd, 0xbf,
	0x0e, 0x58, 0xfa, 0xcf, 0xbc, 0xfd, 0x91, 0x00, 0xfd, 0xf6, 0x07, 0x91, 0x3c, 0x79, 0xe2, 0x8d,
	0xa3, 0xec, 0xd3, 0x03, 0x6f, 0x44, 0x4d, 0xc2, 0x43, 0x3f, 0xb9, 0xfa, 0xc4, 0x7b, 0x85, 0x27,
	0x08, 0x91, 0x96, 0x8d, 0x04, 0x87, 0x7e, 0x2c, 0x8e, 0x3d, 0x47, 0x9b, 0x5e, 0x57, 0x02, 0xbf,
	0x54, 0x30, 0xeb, 0x0e, 0xac, 0x1c, 0xf9, 0x31, 0x17, 0xc3, 0xd8, 0xf1, 0x39, 0xe3, 0xc3, 0x88,
	0x09, 0x95, 0xe9, 0x2e, 0x21, 0x7c, 0x80, 0xe0, 0x67, 0x4c, 0x24, 0xf3, 0x39, 0xa7, 0x8e, 0x70,
	0x62, 0x95, 0xf3, 0xe2, 0x7c, 0x3b, 0x08, 0xc1, 0x40, 0x39, 0x0e, 0x4f, 0xfd, 0x20, 0x79, 0x9a,
	0x92, 0xb4, 0xf1, 0x50, 0xf2, 0xc5, 0x4c, 0x25, 0xbc, 0xf8, 0x5b, 0xc2, 0x64, 0x4e, 0xa7, 0xd2,
	0x5c, 0xfc, 0x6d, 0xdd, 0x93, 0x21, 0xf2, 0x24, 0x14, 0x6c, 0x28, 0x62, 0xe6, 0x08, 0x5d, 0x0e,
	0xed, 0xd0, 0xb7, 0x49, 0x84, 0x3a, 0x90, 0x18, 0x55, 0x14, 0x4d, 0x2c, 0xa7, 0x6b, 0x58, 0x8e,
	0xfd, 0x4f, 0x2d, 0x58, 0x7b, 0xc6, 0x44, 0x71, 0x13, 0xf2, 0xef, 0xae, 0x8c, 0x4d, 0xa9, 0xe6,
	0xcf, 0x84, 0x88, 0x89, 0xb4, 0x54, 0xa7, 0x82, 0xc4, 0x1b, 0x00, 0x12, 0xac, 0x44, 0xa0, 0xde,
	0x6a, 0x44, 0x4c, 0x28, 0x09, 0x6c, 0x41, 0x4b, 0xa2, 0x71, 0x27, 0x55, 0xa2, 0x14, 0x31, 0x81,
	0x1b, 0x79, 0x0b, 0xba, 0x12, 0x95, 0xec, 0x85, 0x2a, 0xd6, 0x45, 0x4c, 0x24, 0x5b, 0xb1, 0x4d,
	0x24, 0xc7, 0xe1, 0x84, 0x0d, 0x3d, 0xf5, 0xf9, 0x5a, 0x7b, 0x20, 0x27, 0xfc, 0x3a, 0x9c, 0xb0,
	0x5d, 0x67, 0x26, 0xd9, 0x95, 0x14, 0x52, 0x19, 0xe8, 0x31, 0xb9, 0x64, 0x52, 0xea, 0x82, 0xe2,
	0xeb, 0xc4, 0x0f, 0xbc, 0xf0, 0x48, 0x3d, 0x24, 0x97, 0x7c, 0x7d, 0x83, 0x00, 0xbc, 0x70, 0x60,
	0x62, 0x78, 0xea, 0xc4, 0x3e, 0x13, 0x33, 0xf5, 0x41, 0x82, 0xec, 0xf1, 0x82, 0x20, 0x9a, 0xf1,
	0x71, 0x18, 0x8c, 0x94, 0xac, 0xe5, 0x44, 0x8f, 0x43, 0x2a, 0x12, 0x48, 0xd4, 0x2b, 0xe6, 0x8f,
	0x8e, 0x85, 0xba, 0xbd, 0x94, 0x43, 0x7f, 0x87, 0x00, 0x7c, 0x0f, 0x22, 0x97, 0xcc, 0xa6, 0x32,
	0x57, 0x54, 0x35, 0x86, 0xc6, 0x40, 0xae, 0xe4, 0xa9, 0x86, 0x59, 0x6f, 0xc1, 0x12, 0xcd, 0xef,
	0xba, 0x7e, 0xe0, 0x08, 0x46, 0x0f, 0x54, 0x1a, 0x83, 0x45, 0x64, 0x41, 0x03, 0xf5, 0x58, 0x1e,
	0x7b, 0x15, 0xc6, 0x13, 0x5f, 0xd5, 0x1c, 0x68, 0xac, 0x5d, 0x0d, 0xd3, 0xfc, 0x1c, 0x13, 0x3f,
	0x2b, 0x09, 0x3f, 0x5f, 0x13, 0x3f, 0x0a, 0xcd, 0xc3, 0x69, 0xec, 0x52, 0x7d, 0x8f, 0xd0, 0xfb,
	0x08, 0x48, 0xd0, 0xa4, 0x56, 0x56, 0x8a, 0x4e, 0x6a, 0xec, 0x12, 0x1d, 0xb3, 0x89, 0x13, 0x9f,
	0xa8, 0xc2, 0x83, 0x44, 0x0f, 0x10, 0x60, 0xbd, 0x0d, 0xcb, 0x86, 0x1c, 0x87, 0x5c, 0xc4, 0xaa,
	0xf6, 0xb0, 0x98, 0xca, 0x72, 0x5f, 0xc4, 0xd6, 0x9b, 0xb4, 0x5e, 0xda, 0x0e, 0x24, 0x5b, 0x57,
	0xaf, 0x64, 0xf4, 0x96, 0x48, 0xaa, 0x3b, 0xb0, 0xe2, 0x85, 0xa3, 0xe1, 0xd8, 0x77, 0x59, 0xe0,
	0x32, 0xbc, 0x49, 0xeb, 0x6d, 0x90, 0xe9, 0x79, 0xe1, 0xe8, 0x31, 0x81, 0x1f, 0xc8, 0xf0, 0xed,
	0x2e, 0x58, 0x92, 0x32, 0x91, 0x1f, 0xd1, 0x6e, 0xd2, 0x55, 0xbe, 0x17, 0x8e, 0x12, 0x19, 0x22,
	0xf5, 0x26, 0x34, 0x8f, 0x1c, 0x17, 0x8f, 0xfb, 0x1e, 0x29, 0xb5, 0x6c, 0x52, 0x95, 0x48, 0xb2,
	0x85, 0x9d, 0xb7, 0x12, 0xf5, 0xc4, 0x3e, 0xd7, 0xa1, 0xcd, 0x93, 0xea, 0x7a, 0x5f, 0xbd, 0x4c,
	0x50, 0xe5, 0x75, 0xeb, 0x1d, 0x58, 0xf5, 0x03, 0x3e, 0x8d, 0x1d, 0xc9, 0xa6, 0x1e, 0xfa, 0x3a,
	0x12, 0x2d, 0x27, 0x88, 0xaf, 0x68, 0x8e, 0x9b, 0xd0, 0x61, 0x12, 0xc4, 0xf0, 0xca, 0xaf, 0xf7,
	0xc6, 0x76, 0xe5, 0x4e, 0x6b, 0x00, 0x04, 0x7a, 0xe0, 0xc4, 0x54, 0x05, 0x1f, 0x31, 0x14, 0xca,
	0x8f, 0x54, 0x15, 0x6e, 0xc4, 0xa4, 0x38, 0xee, 0x82, 0x25, 0x11, 0x6e, 0x18, 0x9c, 0xb2, 0x98,
	0xd3, 0x77, 0x89, 0x71, 0xef, 0x26, 0x2d, 0xd2, 0x19, 0xb1, 0x07, 0x09, 0x42, 0x52, 0x27, 0x86,
	0xbf, 0x6d, 0x1e, 0x99, 0x9b, 0xd0, 0x0c, 0x23, 0x86, 0x55, 0xe9, 0x5b, 0x34, 0xb8, 0x6c, 0xa6,
	0x86, 0x7b, 0x34, 0x0e, 0x5f, 0xb1, 0xb8, 0x67, 0x27, 0x1b, 0xfb, 0x15, 0x02, 0x24, 0xd7, 0x84,
	0x22, 0xe1, 0xdc, 0x26, 0xcb, 0x23, 0x90, 0x94, 0x8f, 0xfd, 0x53, 0x58, 0xcf, 0x39, 0x14, 0x75,
	0x44, 0x14, 0x5f, 0x82, 0x5a, 0xc6, 0xe3, 0xfd, 0x36, 0x1d, 0x09, 0x1f, 0xfe, 0x7b, 0x0f, 0xd6,
	0xb3, 0xdf, 0x2d, 0xef, 0xab, 0x5b, 0xd6, 0x4f, 0xa1, 0x8d, 0x61, 0xf5, 0x81, 0x33, 0xe2, 0xd6,
	0x9a, 0x2e, 0x5e, 0x98, 0x99, 0x74, 0x7f, 0x3d, 0x07, 0x55, 0x33, 0x7f, 0x01, 0x1d, 0x04, 0x3c,
	0xc3, 0x54, 0xd1, 0xda, 0x48, 0xd3, 0xc6, 0x4c, 0xef, 0xcd, 0x02, 0x5c, 0xf5, 0xff, 0x29, 0x2c,
	0xe9, 0x64, 0x5b, 0x0d, 0xb1, 0x95, 0x92, 0xe6, 0xd2, 0xf0, 0x3e, 0x26, 0x06, 0x99, 0x02, 0x98,
	0x07, 0x5b, 0x73, 0x13, 0x52, 0x6b, 0xbb, 0xac, 0x90, 0x90, 0x61, 0xeb, 0x2d, 0x49, 0x71, 0x7e,
	0x46, 0xfb, 0x3d, 0xdc, 0x3c, 0xe7, 0x66, 0xc7, 0x7a, 0x07, 0x8f, 0xe5, 0x0b, 0x5d, 0x31, 0xf5,
	0xdf, 0xbd, 0x10, 0xad, 0x9a, 0xfb, 0x63, 0x80, 0x1d, 0xcf, 0xd3, 0x75, 0xff, 0x39, 0x15, 0xf3,
	0x3e, 0xc6, 0xe3, 0x66, 0x71, 0xef, 0x53, 0x58, 0x24, 0x1d, 0xb9, 0x74, 0xcf, 0x47, 0x72, 0x47,
	0xcc, 0x52, 0x3f, 0xed, 0x48, 0xe9, 0xb5, 0x41, 0xbf, 0x5f, 0x86, 0x4a, 0x58, 0x80, 0x5d, 0x36,
	0xd6, 0xf3, 0x9f, 0x31, 0xc8, 0xd9, 0x2c, 0xd0, 0xf3, 0xb4, 0x6c, 0x6f, 0xa3, 0x98, 0x9a, 0x67,
	0x21, 0x73, 0x51, 0xf0, 0x18, 0xd6, 0x9e, 0x47, 0xe3, 0xd0, 0xf1, 0xb2, 0x37, 0x8b, 0x34, 0x5c,
	0xe9, 0x45, 0x65, 0xbf, 0x14, 0x45, 0x17, 0x91, 0x9f, 0x03, 0xa4, 0xa9, 0xb7, 0xb5, 0xae, 0xb2,
	0xd4, 0x6c, 0x59, 0xa0, 0xbf, 0x91, 0x07, 0x2b, 0x56, 0x7e, 0x06, 0xcb, 0xb8, 0xd9, 0x69, 0xae,
	0x4c, 0x23, 0x14, 0xf2, 0x6c, 0x1a, 0xa1, 0x24, 0xa5, 0xfe, 0x3c, 0x7f, 0x6f, 0xbc, 0x56, 0xbc,
	0x62, 0x66, 0x2f, 0xfb, 0x65, 0x50, 0x6e, 0x7d, 0x00, 0x5d, 0xf3, 0x86, 0xde, 0xba, 0x96, 0xa1,
	0xa2, 0x3b, 0xfb, 0x7e, 0x57, 0x6d, 0x84, 0x36, 0x4d, 0xab, 0xf8, 0x6a, 0xda, 0x42, 0x1a, 0xfd,
	0x0f, 0x07, 0xa9, 0xb4, 0x8a, 0x6f, 0xab, 0x3f, 0x32, 0xd8, 0x95, 0xd8, 0x1c, 0xbb, 0xea, 0x5d,
	0x74, 0x6e, 0xce, 0x1d, 0xe3, 0x21, 0x01, 0xbd, 0x70, 0x24, 0xc5, 0x2d, 0x3e, 0xce, 0xed, 0x97,
	0xc3, 0xb9, 0x75, 0x0f, 0x56, 0x06, 0xec, 0x28, 0x66, 0xfc, 0x38, 0x41, 0xe6, 0x98, 0xce, 0x4e,
	0xf9, 0x10, 0x36, 0xca, 0x1f, 0x2b, 0xe6, 0x7a, 0xdd, 0x40, 0x03, 0x9a, 0xfb, 0xac, 0xf1, 0x33,
	0x58, 0xc9, 0x3f, 0x31, 0xb4, 0x36, 0x95, 0xcd, 0xe5, 0x1f, 0x1e, 0x16, 0x16, 0xbd, 0x56, 0xf6,
	0xbc, 0xcd, 0xba, 0x2e, 0xa9, 0xe6, 0x3c, 0x7c, 0x2b, 0x59, 0x44, 0xe9, 0xb3, 0x2b, 0xeb, 0x86,
	0xe2, 0xa1, 0xfc, 0x49, 0x56, 0x6e, 0x98, 0x7d, 0xb8, 0x71, 0xe6, 0x43, 0x2a, 0xeb, 0x4d, 0xd4,
	0xcd, 0x73, 0xde, 0x5a, 0xe5, 0x06, 0xdd, 0xa5, 0x07, 0x19, 0x26, 0x03, 0x56, 0xf2, 0x76, 0x33,
	0xf7, 0x4a, 0xaa, 0x3f, 0x07, 0xc1, 0x49, 0x48, 0xc5, 0xf7, 0x35, 0x5a, 0x48, 0xa5, 0x2f, 0x6f,
	0x72, 0x8c, 0xdc, 0x35, 0xbe, 0x7d, 0x58, 0xce, 0x7c, 0x1b, 0xc1, 0x5e, 0xf6, 0x73, 0x00, 0x6e,
	0xbd, 0x4f, 0xd4, 0xfb, 0xce, 0x29, 0x4b, 0xa9, 0xd5, 0xa7, 0x1b, 0x7d, 0x34, 0x9f, 0xfc, 0x3f,
	0x74, 0xec, 0xc2, 0x4a, 0xfe, 0xdb, 0x13, 0x5a, 0x68, 0xc9, 0x27, 0x39, 0xfd, 0x39, 0x08, 0x6e,
	0xfd, 0x3c, 0xfb, 0xbf, 0x19, 0xca, 0x0a, 0xb6, 0xf2, 0xe4, 0xa9, 0x21, 0xcc, 0x45, 0x71, 0xeb,
	0x67, 0x59, 0x8e, 0xd0, 0x0c, 0x0b, 0x13, 0x6b, 0x4b, 0x2c, 0x5d, 0xd3, 0x23, 0x58, 0x2d, 0x7c,
	0x8b, 0x65, 0xf5, 0x8a, 0x2f, 0x6f, 0xe9, 0x4b, 0xb2, 0xfe, 0x3c, 0x0c, 0xb7, 0xbe, 0x80, 0xe5,
	0xdc, 0xdf, 0x81, 0x58, 0x1b, 0x79, 0x4e, 0x94, 0x1b, 0x2a, 0x65, 0xe4, 0x8b, 0xc2, 0xa7, 0x74,
	0xeb, 0x25, 0xdf, 0x03, 0xb2, 0x97, 0xfd, 0x52, 0xb0, 0xd4, 0x9f, 0xe5, 0xdc, 0xf7, 0xa2, 0xe6,
	0xfc, 0x19, 0xeb, 0x2c, 0x87, 0x1b, 0x3b, 0x63, 0x7e, 0x22, 0x6c, 0xee, 0x4c, 0xee, 0x6b, 0xea,
	0xfe, 0x5c, 0x14, 0xb7, 0x3e, 0xcc, 0x7c, 0x57, 0xb7, 0x9a, 0x7b, 0x4d, 0xcd, 0x5e, 0xf6, 0x0b,
	0x20, 0x14, 0x61, 0xee, 0xff, 0x5e, 0x68, 0x09, 0xc5, 0x3f, 0x81, 0x29, 0x17, 0xe1, 0x7d, 0x79,
	0x7e, 0xe9, 0xff, 0x67, 0xa0, 0x39, 0x33, 0xff, 0xd7, 0x50, 0xde, 0xeb, 0xe3, 0xec, 0x5f, 0x6a,
	0x58, 0xf9, 0x7f, 0xde, 0x60, 0x2f, 0xfb, 0x45, 0x18, 0xb7, 0x7e, 0x47, 0x7f, 0xc1, 0x9d, 0xab,
	0x9d, 0x5a, 0x46, 0x01, 0xa3, 0xbc, 0x1e, 0xdb, 0xbf, 0x75, 0x06, 0x45, 0x1a, 0x76, 0x9a, 0xf5,
	0xc5, 0x8d, 0x7c, 0x39, 0xd2, 0x0c, 0x3b, 0xcb, 0xca, 0xa7, 0xcf, 0xe6, 0x94, 0xe7, 0xe6, 0x0d,
	0xb4, 0x3d, 0xb7, 0xdc, 0xa7, 0x47, 0x7c, 0xa2, 0x4d, 0x2d, 0xad, 0xca, 0x90, 0x6f, 0x9a, 0x53,
	0x87, 0xeb, 0xbf, 0x71, 0x56, 0x21, 0x47, 0x06, 0x4f, 0x69, 0x9c, 0x4f, 0xaa, 0x5e, 0x28, 0x24,
	0xcc, 0x33, 0x94, 0xc5, 0x4c, 0x92, 0x40, 0xd6, 0x5a, 0x56, 0x88, 0x28, 0xef, 0xbf, 0x03, 0xa0,
	0x88, 0x77, 0x3c, 0xef, 0x8c, 0xce, 0x5b, 0x25, 0x18, 0x1a, 0xe5, 0x70, 0x01, 0xff, 0x0e, 0xe9,
	0xa3, 0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0x91, 0x2b, 0xce, 0x58, 0x29, 0x49, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CustomerCenterServiceClient is the client API for CustomerCenterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CustomerCenterServiceClient interface {
	// 查询标签信息
	QueryTags(ctx context.Context, in *TagsQueryRequest, opts ...grpc.CallOption) (*TagsQueryResponse, error)
	// 查询宠物标签信息
	QueryPetTag(ctx context.Context, in *PetTagQueryRequest, opts ...grpc.CallOption) (*PetTagQueryResponse, error)
	// 根据宠物ID生成标签
	GeneratePetTag(ctx context.Context, in *PetTagGenerateRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 查询用户的推荐商品--常购记录
	QueryUsuallyRecordProduct(ctx context.Context, in *RecommendProductQueryRequest, opts ...grpc.CallOption) (*QueryUsuallyRecordProductResponse, error)
	// 查询用户的推荐商品--常购记录  -- 手动执行任务接口
	ManualQueryUsuallyRecordProduct(ctx context.Context, in *ManualQueryUsuallyRecordProductRequest, opts ...grpc.CallOption) (*ManualQueryUsuallyRecordProductResponse, error)
	//添加收件地址
	AddAddress(ctx context.Context, in *AddressInfoRequest, opts ...grpc.CallOption) (*ResResponse, error)
	//更新收件地址
	UpdateAddress(ctx context.Context, in *AddressInfoRequest, opts ...grpc.CallOption) (*ResResponse, error)
	//根据ID获取收件地址
	GetAddressInfo(ctx context.Context, in *GetAddressInfoRequest, opts ...grpc.CallOption) (*GetAddressInfoResponse, error)
	//删除收件地址
	DelAddress(ctx context.Context, in *GetAddressInfoRequest, opts ...grpc.CallOption) (*ResResponse, error)
	//获取收货收件地址列表
	GetAddressList(ctx context.Context, in *GetAddressListRequest, opts ...grpc.CallOption) (*GetAddressListResponse, error)
	//上传用户请求日志，C端埋点用
	UploadUserRequestLog(ctx context.Context, in *UserRequestLogRequest, opts ...grpc.CallOption) (*UserRequestLogResonse, error)
	// 标签操作，增删改查
	TagOperate(ctx context.Context, in *TagOperateRequest, opts ...grpc.CallOption) (*TagOperateResponse, error)
	// 内容标签查询
	QueryContentTag(ctx context.Context, in *ContentTagRequest, opts ...grpc.CallOption) (*ContentTagResponse, error)
	//会员等级列表
	UserLevelList(ctx context.Context, in *UserLevelListReq, opts ...grpc.CallOption) (*UserLevelListRes, error)
	//会员等级启用停用
	UserLevelSet(ctx context.Context, in *UserLevelSetReq, opts ...grpc.CallOption) (*Response, error)
	//会员权益可编辑项
	UserEditEquityList(ctx context.Context, in *EmptyReq, opts ...grpc.CallOption) (*UserEditEquityListRes, error)
	//会员等级编辑
	UserLevelEdit(ctx context.Context, in *UserLevelEditReq, opts ...grpc.CallOption) (*Response, error)
	//会员等级关联的权益
	UserLevelDetail(ctx context.Context, in *UserLevelDetailReq, opts ...grpc.CallOption) (*UserLevelDetailRes, error)
	//更新会员等级
	RefreshUserLevel(ctx context.Context, in *EmptyReq, opts ...grpc.CallOption) (*Response, error)
	//所有会员等级对应的权益列表
	AllUserLevelEquityList(ctx context.Context, in *EmptyReq, opts ...grpc.CallOption) (*AllUserLevelEquityListRes, error)
	//加减用户健康值
	AddUserHealthVal(ctx context.Context, in *AddUserHealthValReq, opts ...grpc.CallOption) (*Response, error)
	//开启、关闭微信消息通知
	SetNotificationState(ctx context.Context, in *SetNotificationStateReq, opts ...grpc.CallOption) (*Response, error)
	//加减微信消息订阅数
	AddNotificationMessage(ctx context.Context, in *AddNotificationMessageReq, opts ...grpc.CallOption) (*Response, error)
	//用户是否可以发送订阅消息
	CanSendWechatSubscribeMessage(ctx context.Context, in *CanSendWechatSubscribeMessageReq, opts ...grpc.CallOption) (*Response, error)
	//用户订阅开关状态
	UserNotification(ctx context.Context, in *UserNotificationReq, opts ...grpc.CallOption) (*UserNotificationRes, error)
	//发送微信订阅消息
	SendSubscribeMessage(ctx context.Context, in *SendSubscribeMessageReq, opts ...grpc.CallOption) (*Response, error)
	//任务列表
	TaskList(ctx context.Context, in *TaskListReq, opts ...grpc.CallOption) (*TaskListRes, error)
	//任务保存
	TaskSave(ctx context.Context, in *TaskSaveReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//会员权益列表
	MemberEquityList(ctx context.Context, in *MemberEquityListReq, opts ...grpc.CallOption) (*MemberEquityListRes, error)
	//会员权益详情
	MemberEquityDetail(ctx context.Context, in *MemberEquityDetailReq, opts ...grpc.CallOption) (*MemberEquityDetailRes, error)
	//会员权益编辑
	MemberEquityEdit(ctx context.Context, in *MemberEquityEditReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	// 会员等级权益
	UserLevelEquities(ctx context.Context, in *UserLevelEquitiesReq, opts ...grpc.CallOption) (*UserLevelEquitiesRes, error)
	//会员权益显示或隐藏
	MemberEquitySet(ctx context.Context, in *MemberEquitySetReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//会员体系-任务中心
	MemberTaskList(ctx context.Context, in *MemberTaskListReq, opts ...grpc.CallOption) (*MemberTaskListRes, error)
	//个人中心-健康值
	MemberHealthVal(ctx context.Context, in *MemberHealthValReq, opts ...grpc.CallOption) (*MemberHealthValRes, error)
	//任务中心-健康值明细
	MemberHealthDetail(ctx context.Context, in *MemberHealthDetailReq, opts ...grpc.CallOption) (*MemberHealthDetailRes, error)
	//权益详情-特权列表
	UserEquity(ctx context.Context, in *UserEquityReq, opts ...grpc.CallOption) (*UserEquityRes, error)
	//权益详情-领券
	EquityGetcoupon(ctx context.Context, in *EquityGetcouponReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//完成任务
	TaskFinish(ctx context.Context, in *TaskFinishReq, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//会员中心主页
	EquityIndex(ctx context.Context, in *EquityIndexReq, opts ...grpc.CallOption) (*EquityIndexRes, error)
	//获取会员折扣，没有配置当前会员折扣则取有折扣的最高等级的折扣
	MemberProductDiscount(ctx context.Context, in *MemberProductDiscountRequest, opts ...grpc.CallOption) (*MemberProductDiscountResponse, error)
	//获取订阅通知详情
	MessageInfo(ctx context.Context, in *MessageInfoRequest, opts ...grpc.CallOption) (*MessageInfoResponse, error)
	//获取用户发券通知
	MemberCouponsMessage(ctx context.Context, in *MessageInfoRequest, opts ...grpc.CallOption) (*MemberCouponsMessageResponse, error)
	//获取最新消息
	MemberNewMessage(ctx context.Context, in *MemberNewMessageRequest, opts ...grpc.CallOption) (*MemberNewMessageResponse, error)
	//用户信息更新
	InfoUpdate(ctx context.Context, in *InfoUpdateRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//宠物信息更新
	PetInfoUpdate(ctx context.Context, in *PetInfoUpdateRequest, opts ...grpc.CallOption) (*BaseResponseNew, error)
	//宠物信息添加
	PetInfoAdd(ctx context.Context, in *PetInfoUpdateRequest, opts ...grpc.CallOption) (*PetInfoUpdateResponse, error)
}

type customerCenterServiceClient struct {
	cc *grpc.ClientConn
}

func NewCustomerCenterServiceClient(cc *grpc.ClientConn) CustomerCenterServiceClient {
	return &customerCenterServiceClient{cc}
}

func (c *customerCenterServiceClient) QueryTags(ctx context.Context, in *TagsQueryRequest, opts ...grpc.CallOption) (*TagsQueryResponse, error) {
	out := new(TagsQueryResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/QueryTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) QueryPetTag(ctx context.Context, in *PetTagQueryRequest, opts ...grpc.CallOption) (*PetTagQueryResponse, error) {
	out := new(PetTagQueryResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/QueryPetTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) GeneratePetTag(ctx context.Context, in *PetTagGenerateRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/GeneratePetTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) QueryUsuallyRecordProduct(ctx context.Context, in *RecommendProductQueryRequest, opts ...grpc.CallOption) (*QueryUsuallyRecordProductResponse, error) {
	out := new(QueryUsuallyRecordProductResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/QueryUsuallyRecordProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) ManualQueryUsuallyRecordProduct(ctx context.Context, in *ManualQueryUsuallyRecordProductRequest, opts ...grpc.CallOption) (*ManualQueryUsuallyRecordProductResponse, error) {
	out := new(ManualQueryUsuallyRecordProductResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/ManualQueryUsuallyRecordProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) AddAddress(ctx context.Context, in *AddressInfoRequest, opts ...grpc.CallOption) (*ResResponse, error) {
	out := new(ResResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/AddAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UpdateAddress(ctx context.Context, in *AddressInfoRequest, opts ...grpc.CallOption) (*ResResponse, error) {
	out := new(ResResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UpdateAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) GetAddressInfo(ctx context.Context, in *GetAddressInfoRequest, opts ...grpc.CallOption) (*GetAddressInfoResponse, error) {
	out := new(GetAddressInfoResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/GetAddressInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) DelAddress(ctx context.Context, in *GetAddressInfoRequest, opts ...grpc.CallOption) (*ResResponse, error) {
	out := new(ResResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/DelAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) GetAddressList(ctx context.Context, in *GetAddressListRequest, opts ...grpc.CallOption) (*GetAddressListResponse, error) {
	out := new(GetAddressListResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/GetAddressList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UploadUserRequestLog(ctx context.Context, in *UserRequestLogRequest, opts ...grpc.CallOption) (*UserRequestLogResonse, error) {
	out := new(UserRequestLogResonse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UploadUserRequestLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) TagOperate(ctx context.Context, in *TagOperateRequest, opts ...grpc.CallOption) (*TagOperateResponse, error) {
	out := new(TagOperateResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/TagOperate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) QueryContentTag(ctx context.Context, in *ContentTagRequest, opts ...grpc.CallOption) (*ContentTagResponse, error) {
	out := new(ContentTagResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/QueryContentTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UserLevelList(ctx context.Context, in *UserLevelListReq, opts ...grpc.CallOption) (*UserLevelListRes, error) {
	out := new(UserLevelListRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UserLevelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UserLevelSet(ctx context.Context, in *UserLevelSetReq, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UserLevelSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UserEditEquityList(ctx context.Context, in *EmptyReq, opts ...grpc.CallOption) (*UserEditEquityListRes, error) {
	out := new(UserEditEquityListRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UserEditEquityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UserLevelEdit(ctx context.Context, in *UserLevelEditReq, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UserLevelEdit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UserLevelDetail(ctx context.Context, in *UserLevelDetailReq, opts ...grpc.CallOption) (*UserLevelDetailRes, error) {
	out := new(UserLevelDetailRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UserLevelDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) RefreshUserLevel(ctx context.Context, in *EmptyReq, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/RefreshUserLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) AllUserLevelEquityList(ctx context.Context, in *EmptyReq, opts ...grpc.CallOption) (*AllUserLevelEquityListRes, error) {
	out := new(AllUserLevelEquityListRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/AllUserLevelEquityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) AddUserHealthVal(ctx context.Context, in *AddUserHealthValReq, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/AddUserHealthVal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) SetNotificationState(ctx context.Context, in *SetNotificationStateReq, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/SetNotificationState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) AddNotificationMessage(ctx context.Context, in *AddNotificationMessageReq, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/AddNotificationMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) CanSendWechatSubscribeMessage(ctx context.Context, in *CanSendWechatSubscribeMessageReq, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/CanSendWechatSubscribeMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UserNotification(ctx context.Context, in *UserNotificationReq, opts ...grpc.CallOption) (*UserNotificationRes, error) {
	out := new(UserNotificationRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UserNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) SendSubscribeMessage(ctx context.Context, in *SendSubscribeMessageReq, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/SendSubscribeMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) TaskList(ctx context.Context, in *TaskListReq, opts ...grpc.CallOption) (*TaskListRes, error) {
	out := new(TaskListRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/TaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) TaskSave(ctx context.Context, in *TaskSaveReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/TaskSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberEquityList(ctx context.Context, in *MemberEquityListReq, opts ...grpc.CallOption) (*MemberEquityListRes, error) {
	out := new(MemberEquityListRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberEquityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberEquityDetail(ctx context.Context, in *MemberEquityDetailReq, opts ...grpc.CallOption) (*MemberEquityDetailRes, error) {
	out := new(MemberEquityDetailRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberEquityDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberEquityEdit(ctx context.Context, in *MemberEquityEditReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberEquityEdit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UserLevelEquities(ctx context.Context, in *UserLevelEquitiesReq, opts ...grpc.CallOption) (*UserLevelEquitiesRes, error) {
	out := new(UserLevelEquitiesRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UserLevelEquities", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberEquitySet(ctx context.Context, in *MemberEquitySetReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberEquitySet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberTaskList(ctx context.Context, in *MemberTaskListReq, opts ...grpc.CallOption) (*MemberTaskListRes, error) {
	out := new(MemberTaskListRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberHealthVal(ctx context.Context, in *MemberHealthValReq, opts ...grpc.CallOption) (*MemberHealthValRes, error) {
	out := new(MemberHealthValRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberHealthVal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberHealthDetail(ctx context.Context, in *MemberHealthDetailReq, opts ...grpc.CallOption) (*MemberHealthDetailRes, error) {
	out := new(MemberHealthDetailRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberHealthDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) UserEquity(ctx context.Context, in *UserEquityReq, opts ...grpc.CallOption) (*UserEquityRes, error) {
	out := new(UserEquityRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/UserEquity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) EquityGetcoupon(ctx context.Context, in *EquityGetcouponReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/EquityGetcoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) TaskFinish(ctx context.Context, in *TaskFinishReq, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/TaskFinish", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) EquityIndex(ctx context.Context, in *EquityIndexReq, opts ...grpc.CallOption) (*EquityIndexRes, error) {
	out := new(EquityIndexRes)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/EquityIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberProductDiscount(ctx context.Context, in *MemberProductDiscountRequest, opts ...grpc.CallOption) (*MemberProductDiscountResponse, error) {
	out := new(MemberProductDiscountResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberProductDiscount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MessageInfo(ctx context.Context, in *MessageInfoRequest, opts ...grpc.CallOption) (*MessageInfoResponse, error) {
	out := new(MessageInfoResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MessageInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberCouponsMessage(ctx context.Context, in *MessageInfoRequest, opts ...grpc.CallOption) (*MemberCouponsMessageResponse, error) {
	out := new(MemberCouponsMessageResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberCouponsMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) MemberNewMessage(ctx context.Context, in *MemberNewMessageRequest, opts ...grpc.CallOption) (*MemberNewMessageResponse, error) {
	out := new(MemberNewMessageResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/MemberNewMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) InfoUpdate(ctx context.Context, in *InfoUpdateRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/InfoUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) PetInfoUpdate(ctx context.Context, in *PetInfoUpdateRequest, opts ...grpc.CallOption) (*BaseResponseNew, error) {
	out := new(BaseResponseNew)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/PetInfoUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerCenterServiceClient) PetInfoAdd(ctx context.Context, in *PetInfoUpdateRequest, opts ...grpc.CallOption) (*PetInfoUpdateResponse, error) {
	out := new(PetInfoUpdateResponse)
	err := c.cc.Invoke(ctx, "/cc.customerCenterService/PetInfoAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerCenterServiceServer is the server API for CustomerCenterService service.
type CustomerCenterServiceServer interface {
	// 查询标签信息
	QueryTags(context.Context, *TagsQueryRequest) (*TagsQueryResponse, error)
	// 查询宠物标签信息
	QueryPetTag(context.Context, *PetTagQueryRequest) (*PetTagQueryResponse, error)
	// 根据宠物ID生成标签
	GeneratePetTag(context.Context, *PetTagGenerateRequest) (*BaseResponse, error)
	// 查询用户的推荐商品--常购记录
	QueryUsuallyRecordProduct(context.Context, *RecommendProductQueryRequest) (*QueryUsuallyRecordProductResponse, error)
	// 查询用户的推荐商品--常购记录  -- 手动执行任务接口
	ManualQueryUsuallyRecordProduct(context.Context, *ManualQueryUsuallyRecordProductRequest) (*ManualQueryUsuallyRecordProductResponse, error)
	//添加收件地址
	AddAddress(context.Context, *AddressInfoRequest) (*ResResponse, error)
	//更新收件地址
	UpdateAddress(context.Context, *AddressInfoRequest) (*ResResponse, error)
	//根据ID获取收件地址
	GetAddressInfo(context.Context, *GetAddressInfoRequest) (*GetAddressInfoResponse, error)
	//删除收件地址
	DelAddress(context.Context, *GetAddressInfoRequest) (*ResResponse, error)
	//获取收货收件地址列表
	GetAddressList(context.Context, *GetAddressListRequest) (*GetAddressListResponse, error)
	//上传用户请求日志，C端埋点用
	UploadUserRequestLog(context.Context, *UserRequestLogRequest) (*UserRequestLogResonse, error)
	// 标签操作，增删改查
	TagOperate(context.Context, *TagOperateRequest) (*TagOperateResponse, error)
	// 内容标签查询
	QueryContentTag(context.Context, *ContentTagRequest) (*ContentTagResponse, error)
	//会员等级列表
	UserLevelList(context.Context, *UserLevelListReq) (*UserLevelListRes, error)
	//会员等级启用停用
	UserLevelSet(context.Context, *UserLevelSetReq) (*Response, error)
	//会员权益可编辑项
	UserEditEquityList(context.Context, *EmptyReq) (*UserEditEquityListRes, error)
	//会员等级编辑
	UserLevelEdit(context.Context, *UserLevelEditReq) (*Response, error)
	//会员等级关联的权益
	UserLevelDetail(context.Context, *UserLevelDetailReq) (*UserLevelDetailRes, error)
	//更新会员等级
	RefreshUserLevel(context.Context, *EmptyReq) (*Response, error)
	//所有会员等级对应的权益列表
	AllUserLevelEquityList(context.Context, *EmptyReq) (*AllUserLevelEquityListRes, error)
	//加减用户健康值
	AddUserHealthVal(context.Context, *AddUserHealthValReq) (*Response, error)
	//开启、关闭微信消息通知
	SetNotificationState(context.Context, *SetNotificationStateReq) (*Response, error)
	//加减微信消息订阅数
	AddNotificationMessage(context.Context, *AddNotificationMessageReq) (*Response, error)
	//用户是否可以发送订阅消息
	CanSendWechatSubscribeMessage(context.Context, *CanSendWechatSubscribeMessageReq) (*Response, error)
	//用户订阅开关状态
	UserNotification(context.Context, *UserNotificationReq) (*UserNotificationRes, error)
	//发送微信订阅消息
	SendSubscribeMessage(context.Context, *SendSubscribeMessageReq) (*Response, error)
	//任务列表
	TaskList(context.Context, *TaskListReq) (*TaskListRes, error)
	//任务保存
	TaskSave(context.Context, *TaskSaveReq) (*BaseResponseNew, error)
	//会员权益列表
	MemberEquityList(context.Context, *MemberEquityListReq) (*MemberEquityListRes, error)
	//会员权益详情
	MemberEquityDetail(context.Context, *MemberEquityDetailReq) (*MemberEquityDetailRes, error)
	//会员权益编辑
	MemberEquityEdit(context.Context, *MemberEquityEditReq) (*BaseResponseNew, error)
	// 会员等级权益
	UserLevelEquities(context.Context, *UserLevelEquitiesReq) (*UserLevelEquitiesRes, error)
	//会员权益显示或隐藏
	MemberEquitySet(context.Context, *MemberEquitySetReq) (*BaseResponseNew, error)
	//会员体系-任务中心
	MemberTaskList(context.Context, *MemberTaskListReq) (*MemberTaskListRes, error)
	//个人中心-健康值
	MemberHealthVal(context.Context, *MemberHealthValReq) (*MemberHealthValRes, error)
	//任务中心-健康值明细
	MemberHealthDetail(context.Context, *MemberHealthDetailReq) (*MemberHealthDetailRes, error)
	//权益详情-特权列表
	UserEquity(context.Context, *UserEquityReq) (*UserEquityRes, error)
	//权益详情-领券
	EquityGetcoupon(context.Context, *EquityGetcouponReq) (*BaseResponseNew, error)
	//完成任务
	TaskFinish(context.Context, *TaskFinishReq) (*BaseResponseNew, error)
	//会员中心主页
	EquityIndex(context.Context, *EquityIndexReq) (*EquityIndexRes, error)
	//获取会员折扣，没有配置当前会员折扣则取有折扣的最高等级的折扣
	MemberProductDiscount(context.Context, *MemberProductDiscountRequest) (*MemberProductDiscountResponse, error)
	//获取订阅通知详情
	MessageInfo(context.Context, *MessageInfoRequest) (*MessageInfoResponse, error)
	//获取用户发券通知
	MemberCouponsMessage(context.Context, *MessageInfoRequest) (*MemberCouponsMessageResponse, error)
	//获取最新消息
	MemberNewMessage(context.Context, *MemberNewMessageRequest) (*MemberNewMessageResponse, error)
	//用户信息更新
	InfoUpdate(context.Context, *InfoUpdateRequest) (*BaseResponseNew, error)
	//宠物信息更新
	PetInfoUpdate(context.Context, *PetInfoUpdateRequest) (*BaseResponseNew, error)
	//宠物信息添加
	PetInfoAdd(context.Context, *PetInfoUpdateRequest) (*PetInfoUpdateResponse, error)
}

// UnimplementedCustomerCenterServiceServer can be embedded to have forward compatible implementations.
type UnimplementedCustomerCenterServiceServer struct {
}

func (*UnimplementedCustomerCenterServiceServer) QueryTags(ctx context.Context, req *TagsQueryRequest) (*TagsQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryTags not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) QueryPetTag(ctx context.Context, req *PetTagQueryRequest) (*PetTagQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPetTag not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) GeneratePetTag(ctx context.Context, req *PetTagGenerateRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GeneratePetTag not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) QueryUsuallyRecordProduct(ctx context.Context, req *RecommendProductQueryRequest) (*QueryUsuallyRecordProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUsuallyRecordProduct not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) ManualQueryUsuallyRecordProduct(ctx context.Context, req *ManualQueryUsuallyRecordProductRequest) (*ManualQueryUsuallyRecordProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManualQueryUsuallyRecordProduct not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) AddAddress(ctx context.Context, req *AddressInfoRequest) (*ResResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAddress not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UpdateAddress(ctx context.Context, req *AddressInfoRequest) (*ResResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAddress not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) GetAddressInfo(ctx context.Context, req *GetAddressInfoRequest) (*GetAddressInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddressInfo not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) DelAddress(ctx context.Context, req *GetAddressInfoRequest) (*ResResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelAddress not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) GetAddressList(ctx context.Context, req *GetAddressListRequest) (*GetAddressListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddressList not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UploadUserRequestLog(ctx context.Context, req *UserRequestLogRequest) (*UserRequestLogResonse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadUserRequestLog not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) TagOperate(ctx context.Context, req *TagOperateRequest) (*TagOperateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagOperate not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) QueryContentTag(ctx context.Context, req *ContentTagRequest) (*ContentTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryContentTag not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UserLevelList(ctx context.Context, req *UserLevelListReq) (*UserLevelListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLevelList not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UserLevelSet(ctx context.Context, req *UserLevelSetReq) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLevelSet not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UserEditEquityList(ctx context.Context, req *EmptyReq) (*UserEditEquityListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserEditEquityList not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UserLevelEdit(ctx context.Context, req *UserLevelEditReq) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLevelEdit not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UserLevelDetail(ctx context.Context, req *UserLevelDetailReq) (*UserLevelDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLevelDetail not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) RefreshUserLevel(ctx context.Context, req *EmptyReq) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshUserLevel not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) AllUserLevelEquityList(ctx context.Context, req *EmptyReq) (*AllUserLevelEquityListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllUserLevelEquityList not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) AddUserHealthVal(ctx context.Context, req *AddUserHealthValReq) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUserHealthVal not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) SetNotificationState(ctx context.Context, req *SetNotificationStateReq) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetNotificationState not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) AddNotificationMessage(ctx context.Context, req *AddNotificationMessageReq) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddNotificationMessage not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) CanSendWechatSubscribeMessage(ctx context.Context, req *CanSendWechatSubscribeMessageReq) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CanSendWechatSubscribeMessage not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UserNotification(ctx context.Context, req *UserNotificationReq) (*UserNotificationRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserNotification not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) SendSubscribeMessage(ctx context.Context, req *SendSubscribeMessageReq) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSubscribeMessage not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) TaskList(ctx context.Context, req *TaskListReq) (*TaskListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskList not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) TaskSave(ctx context.Context, req *TaskSaveReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskSave not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberEquityList(ctx context.Context, req *MemberEquityListReq) (*MemberEquityListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberEquityList not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberEquityDetail(ctx context.Context, req *MemberEquityDetailReq) (*MemberEquityDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberEquityDetail not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberEquityEdit(ctx context.Context, req *MemberEquityEditReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberEquityEdit not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UserLevelEquities(ctx context.Context, req *UserLevelEquitiesReq) (*UserLevelEquitiesRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLevelEquities not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberEquitySet(ctx context.Context, req *MemberEquitySetReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberEquitySet not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberTaskList(ctx context.Context, req *MemberTaskListReq) (*MemberTaskListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberTaskList not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberHealthVal(ctx context.Context, req *MemberHealthValReq) (*MemberHealthValRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberHealthVal not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberHealthDetail(ctx context.Context, req *MemberHealthDetailReq) (*MemberHealthDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberHealthDetail not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) UserEquity(ctx context.Context, req *UserEquityReq) (*UserEquityRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserEquity not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) EquityGetcoupon(ctx context.Context, req *EquityGetcouponReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EquityGetcoupon not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) TaskFinish(ctx context.Context, req *TaskFinishReq) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskFinish not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) EquityIndex(ctx context.Context, req *EquityIndexReq) (*EquityIndexRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EquityIndex not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberProductDiscount(ctx context.Context, req *MemberProductDiscountRequest) (*MemberProductDiscountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberProductDiscount not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MessageInfo(ctx context.Context, req *MessageInfoRequest) (*MessageInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MessageInfo not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberCouponsMessage(ctx context.Context, req *MessageInfoRequest) (*MemberCouponsMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberCouponsMessage not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) MemberNewMessage(ctx context.Context, req *MemberNewMessageRequest) (*MemberNewMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberNewMessage not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) InfoUpdate(ctx context.Context, req *InfoUpdateRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InfoUpdate not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) PetInfoUpdate(ctx context.Context, req *PetInfoUpdateRequest) (*BaseResponseNew, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PetInfoUpdate not implemented")
}
func (*UnimplementedCustomerCenterServiceServer) PetInfoAdd(ctx context.Context, req *PetInfoUpdateRequest) (*PetInfoUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PetInfoAdd not implemented")
}

func RegisterCustomerCenterServiceServer(s *grpc.Server, srv CustomerCenterServiceServer) {
	s.RegisterService(&_CustomerCenterService_serviceDesc, srv)
}

func _CustomerCenterService_QueryTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagsQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).QueryTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/QueryTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).QueryTags(ctx, req.(*TagsQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_QueryPetTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTagQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).QueryPetTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/QueryPetTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).QueryPetTag(ctx, req.(*PetTagQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_GeneratePetTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetTagGenerateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).GeneratePetTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/GeneratePetTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).GeneratePetTag(ctx, req.(*PetTagGenerateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_QueryUsuallyRecordProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendProductQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).QueryUsuallyRecordProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/QueryUsuallyRecordProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).QueryUsuallyRecordProduct(ctx, req.(*RecommendProductQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_ManualQueryUsuallyRecordProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManualQueryUsuallyRecordProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).ManualQueryUsuallyRecordProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/ManualQueryUsuallyRecordProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).ManualQueryUsuallyRecordProduct(ctx, req.(*ManualQueryUsuallyRecordProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_AddAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddressInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).AddAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/AddAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).AddAddress(ctx, req.(*AddressInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UpdateAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddressInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UpdateAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UpdateAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UpdateAddress(ctx, req.(*AddressInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_GetAddressInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddressInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).GetAddressInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/GetAddressInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).GetAddressInfo(ctx, req.(*GetAddressInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_DelAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddressInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).DelAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/DelAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).DelAddress(ctx, req.(*GetAddressInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_GetAddressList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddressListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).GetAddressList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/GetAddressList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).GetAddressList(ctx, req.(*GetAddressListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UploadUserRequestLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserRequestLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UploadUserRequestLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UploadUserRequestLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UploadUserRequestLog(ctx, req.(*UserRequestLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_TagOperate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagOperateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).TagOperate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/TagOperate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).TagOperate(ctx, req.(*TagOperateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_QueryContentTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContentTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).QueryContentTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/QueryContentTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).QueryContentTag(ctx, req.(*ContentTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UserLevelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLevelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UserLevelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UserLevelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UserLevelList(ctx, req.(*UserLevelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UserLevelSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLevelSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UserLevelSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UserLevelSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UserLevelSet(ctx, req.(*UserLevelSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UserEditEquityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UserEditEquityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UserEditEquityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UserEditEquityList(ctx, req.(*EmptyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UserLevelEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLevelEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UserLevelEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UserLevelEdit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UserLevelEdit(ctx, req.(*UserLevelEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UserLevelDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLevelDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UserLevelDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UserLevelDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UserLevelDetail(ctx, req.(*UserLevelDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_RefreshUserLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).RefreshUserLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/RefreshUserLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).RefreshUserLevel(ctx, req.(*EmptyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_AllUserLevelEquityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).AllUserLevelEquityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/AllUserLevelEquityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).AllUserLevelEquityList(ctx, req.(*EmptyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_AddUserHealthVal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserHealthValReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).AddUserHealthVal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/AddUserHealthVal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).AddUserHealthVal(ctx, req.(*AddUserHealthValReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_SetNotificationState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNotificationStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).SetNotificationState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/SetNotificationState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).SetNotificationState(ctx, req.(*SetNotificationStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_AddNotificationMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNotificationMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).AddNotificationMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/AddNotificationMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).AddNotificationMessage(ctx, req.(*AddNotificationMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_CanSendWechatSubscribeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CanSendWechatSubscribeMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).CanSendWechatSubscribeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/CanSendWechatSubscribeMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).CanSendWechatSubscribeMessage(ctx, req.(*CanSendWechatSubscribeMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UserNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserNotificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UserNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UserNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UserNotification(ctx, req.(*UserNotificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_SendSubscribeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSubscribeMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).SendSubscribeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/SendSubscribeMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).SendSubscribeMessage(ctx, req.(*SendSubscribeMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_TaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).TaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/TaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).TaskList(ctx, req.(*TaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_TaskSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).TaskSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/TaskSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).TaskSave(ctx, req.(*TaskSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberEquityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberEquityListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberEquityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberEquityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberEquityList(ctx, req.(*MemberEquityListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberEquityDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberEquityDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberEquityDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberEquityDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberEquityDetail(ctx, req.(*MemberEquityDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberEquityEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberEquityEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberEquityEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberEquityEdit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberEquityEdit(ctx, req.(*MemberEquityEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UserLevelEquities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLevelEquitiesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UserLevelEquities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UserLevelEquities",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UserLevelEquities(ctx, req.(*UserLevelEquitiesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberEquitySet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberEquitySetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberEquitySet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberEquitySet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberEquitySet(ctx, req.(*MemberEquitySetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberTaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberTaskList(ctx, req.(*MemberTaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberHealthVal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberHealthValReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberHealthVal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberHealthVal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberHealthVal(ctx, req.(*MemberHealthValReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberHealthDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberHealthDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberHealthDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberHealthDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberHealthDetail(ctx, req.(*MemberHealthDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_UserEquity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserEquityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).UserEquity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/UserEquity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).UserEquity(ctx, req.(*UserEquityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_EquityGetcoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EquityGetcouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).EquityGetcoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/EquityGetcoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).EquityGetcoupon(ctx, req.(*EquityGetcouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_TaskFinish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskFinishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).TaskFinish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/TaskFinish",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).TaskFinish(ctx, req.(*TaskFinishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_EquityIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EquityIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).EquityIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/EquityIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).EquityIndex(ctx, req.(*EquityIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberProductDiscount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberProductDiscountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberProductDiscount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberProductDiscount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberProductDiscount(ctx, req.(*MemberProductDiscountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MessageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MessageInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MessageInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MessageInfo(ctx, req.(*MessageInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberCouponsMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberCouponsMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberCouponsMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberCouponsMessage(ctx, req.(*MessageInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_MemberNewMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberNewMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).MemberNewMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/MemberNewMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).MemberNewMessage(ctx, req.(*MemberNewMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_InfoUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InfoUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).InfoUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/InfoUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).InfoUpdate(ctx, req.(*InfoUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_PetInfoUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetInfoUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).PetInfoUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/PetInfoUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).PetInfoUpdate(ctx, req.(*PetInfoUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerCenterService_PetInfoAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PetInfoUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerCenterServiceServer).PetInfoAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.customerCenterService/PetInfoAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerCenterServiceServer).PetInfoAdd(ctx, req.(*PetInfoUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CustomerCenterService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.customerCenterService",
	HandlerType: (*CustomerCenterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryTags",
			Handler:    _CustomerCenterService_QueryTags_Handler,
		},
		{
			MethodName: "QueryPetTag",
			Handler:    _CustomerCenterService_QueryPetTag_Handler,
		},
		{
			MethodName: "GeneratePetTag",
			Handler:    _CustomerCenterService_GeneratePetTag_Handler,
		},
		{
			MethodName: "QueryUsuallyRecordProduct",
			Handler:    _CustomerCenterService_QueryUsuallyRecordProduct_Handler,
		},
		{
			MethodName: "ManualQueryUsuallyRecordProduct",
			Handler:    _CustomerCenterService_ManualQueryUsuallyRecordProduct_Handler,
		},
		{
			MethodName: "AddAddress",
			Handler:    _CustomerCenterService_AddAddress_Handler,
		},
		{
			MethodName: "UpdateAddress",
			Handler:    _CustomerCenterService_UpdateAddress_Handler,
		},
		{
			MethodName: "GetAddressInfo",
			Handler:    _CustomerCenterService_GetAddressInfo_Handler,
		},
		{
			MethodName: "DelAddress",
			Handler:    _CustomerCenterService_DelAddress_Handler,
		},
		{
			MethodName: "GetAddressList",
			Handler:    _CustomerCenterService_GetAddressList_Handler,
		},
		{
			MethodName: "UploadUserRequestLog",
			Handler:    _CustomerCenterService_UploadUserRequestLog_Handler,
		},
		{
			MethodName: "TagOperate",
			Handler:    _CustomerCenterService_TagOperate_Handler,
		},
		{
			MethodName: "QueryContentTag",
			Handler:    _CustomerCenterService_QueryContentTag_Handler,
		},
		{
			MethodName: "UserLevelList",
			Handler:    _CustomerCenterService_UserLevelList_Handler,
		},
		{
			MethodName: "UserLevelSet",
			Handler:    _CustomerCenterService_UserLevelSet_Handler,
		},
		{
			MethodName: "UserEditEquityList",
			Handler:    _CustomerCenterService_UserEditEquityList_Handler,
		},
		{
			MethodName: "UserLevelEdit",
			Handler:    _CustomerCenterService_UserLevelEdit_Handler,
		},
		{
			MethodName: "UserLevelDetail",
			Handler:    _CustomerCenterService_UserLevelDetail_Handler,
		},
		{
			MethodName: "RefreshUserLevel",
			Handler:    _CustomerCenterService_RefreshUserLevel_Handler,
		},
		{
			MethodName: "AllUserLevelEquityList",
			Handler:    _CustomerCenterService_AllUserLevelEquityList_Handler,
		},
		{
			MethodName: "AddUserHealthVal",
			Handler:    _CustomerCenterService_AddUserHealthVal_Handler,
		},
		{
			MethodName: "SetNotificationState",
			Handler:    _CustomerCenterService_SetNotificationState_Handler,
		},
		{
			MethodName: "AddNotificationMessage",
			Handler:    _CustomerCenterService_AddNotificationMessage_Handler,
		},
		{
			MethodName: "CanSendWechatSubscribeMessage",
			Handler:    _CustomerCenterService_CanSendWechatSubscribeMessage_Handler,
		},
		{
			MethodName: "UserNotification",
			Handler:    _CustomerCenterService_UserNotification_Handler,
		},
		{
			MethodName: "SendSubscribeMessage",
			Handler:    _CustomerCenterService_SendSubscribeMessage_Handler,
		},
		{
			MethodName: "TaskList",
			Handler:    _CustomerCenterService_TaskList_Handler,
		},
		{
			MethodName: "TaskSave",
			Handler:    _CustomerCenterService_TaskSave_Handler,
		},
		{
			MethodName: "MemberEquityList",
			Handler:    _CustomerCenterService_MemberEquityList_Handler,
		},
		{
			MethodName: "MemberEquityDetail",
			Handler:    _CustomerCenterService_MemberEquityDetail_Handler,
		},
		{
			MethodName: "MemberEquityEdit",
			Handler:    _CustomerCenterService_MemberEquityEdit_Handler,
		},
		{
			MethodName: "UserLevelEquities",
			Handler:    _CustomerCenterService_UserLevelEquities_Handler,
		},
		{
			MethodName: "MemberEquitySet",
			Handler:    _CustomerCenterService_MemberEquitySet_Handler,
		},
		{
			MethodName: "MemberTaskList",
			Handler:    _CustomerCenterService_MemberTaskList_Handler,
		},
		{
			MethodName: "MemberHealthVal",
			Handler:    _CustomerCenterService_MemberHealthVal_Handler,
		},
		{
			MethodName: "MemberHealthDetail",
			Handler:    _CustomerCenterService_MemberHealthDetail_Handler,
		},
		{
			MethodName: "UserEquity",
			Handler:    _CustomerCenterService_UserEquity_Handler,
		},
		{
			MethodName: "EquityGetcoupon",
			Handler:    _CustomerCenterService_EquityGetcoupon_Handler,
		},
		{
			MethodName: "TaskFinish",
			Handler:    _CustomerCenterService_TaskFinish_Handler,
		},
		{
			MethodName: "EquityIndex",
			Handler:    _CustomerCenterService_EquityIndex_Handler,
		},
		{
			MethodName: "MemberProductDiscount",
			Handler:    _CustomerCenterService_MemberProductDiscount_Handler,
		},
		{
			MethodName: "MessageInfo",
			Handler:    _CustomerCenterService_MessageInfo_Handler,
		},
		{
			MethodName: "MemberCouponsMessage",
			Handler:    _CustomerCenterService_MemberCouponsMessage_Handler,
		},
		{
			MethodName: "MemberNewMessage",
			Handler:    _CustomerCenterService_MemberNewMessage_Handler,
		},
		{
			MethodName: "InfoUpdate",
			Handler:    _CustomerCenterService_InfoUpdate_Handler,
		},
		{
			MethodName: "PetInfoUpdate",
			Handler:    _CustomerCenterService_PetInfoUpdate_Handler,
		},
		{
			MethodName: "PetInfoAdd",
			Handler:    _CustomerCenterService_PetInfoAdd_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/customerCenter.proto",
}
