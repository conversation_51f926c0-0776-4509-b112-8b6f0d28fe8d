package cc

import (
	"context"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	Lock sync.Mutex
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	// 通用基础服务
	RPC CustomerCenterServiceClient
	// 宠物贴士
	PetTips PetTipsServiceClient
	// 宠物便签
	PetNotes PetNotesServiceClient
	//搜索历史
	SearchHistory SearchServiceClient
	//用户反馈
	Feedback FeedbackServiceClient
	//用户
	User UserServiceClient

	Pet PetServiceClient
	//付费会员
	VipCard     VipCardServiceClient
	VirtualCard VirtualCardServiceClient
}

var client = &Client{}

func GetCustomerCenterClient() *Client {
	var client Client
	var err error
	url := config.GetString("grpc.customer-center")
	//url = "127.0.0.1:7059"
	if url == "" {
		url = "127.0.0.1:7059"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = NewCustomerCenterServiceClient(client.Conn)
		client.PetTips = NewPetTipsServiceClient(client.Conn)
		client.PetNotes = NewPetNotesServiceClient(client.Conn)
		client.SearchHistory = NewSearchServiceClient(client.Conn)
		client.Feedback = NewFeedbackServiceClient(client.Conn)
		client.User = NewUserServiceClient(client.Conn)
		client.Pet = NewPetServiceClient(client.Conn)
		client.VipCard = NewVipCardServiceClient(client.Conn)
		client.VirtualCard = NewVirtualCardServiceClient(client.Conn)
		client.Ctx, client.Cf = context.WithTimeout(context.Background(), time.Second*30)
		return &client
	}
}

func (c *Client) Close() {
	c.Conn.Close()
	c.Cf()
}

// GetCustomerCenterLongClient 获取gprc长连接，切记不要关闭
func GetCustomerCenterLongClient() *Client {
	client.Ctx, _ = context.WithTimeout(context.Background(), 5*time.Minute)
	if client.isAlive() {
		return client
	}
	client.Lock.Lock()
	defer client.Lock.Unlock()

	if client.isAlive() {
		return client
	}
	client = GetCustomerCenterClient()
	return client
}

func (c *Client) isAlive() bool {
	return c != nil && c.Conn != nil && c.Conn.GetState().String() != "SHUTDOWN"
}
