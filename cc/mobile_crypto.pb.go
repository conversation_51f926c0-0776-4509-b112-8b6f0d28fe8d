// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.4
// source: cc/mobile_crypto.proto

package cc

import (
	context "context"
	reflect "reflect"
	sync "sync"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 手机号解密请求
type MobileDecryptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ciphertext string `protobuf:"bytes,1,opt,name=ciphertext,proto3" json:"ciphertext"` // 加密的手机号
}

func (x *MobileDecryptRequest) Reset() {
	*x = MobileDecryptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cc_mobile_crypto_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobileDecryptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobileDecryptRequest) ProtoMessage() {}

func (x *MobileDecryptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cc_mobile_crypto_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobileDecryptRequest.ProtoReflect.Descriptor instead.
func (*MobileDecryptRequest) Descriptor() ([]byte, []int) {
	return file_cc_mobile_crypto_proto_rawDescGZIP(), []int{0}
}

func (x *MobileDecryptRequest) GetCiphertext() string {
	if x != nil {
		return x.Ciphertext
	}
	return ""
}

// 手机号解密响应
type MobileDecryptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"` // 解密后的手机号
	Error  string `protobuf:"bytes,2,opt,name=error,proto3" json:"error"`   // 错误信息
}

func (x *MobileDecryptResponse) Reset() {
	*x = MobileDecryptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cc_mobile_crypto_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobileDecryptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobileDecryptResponse) ProtoMessage() {}

func (x *MobileDecryptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cc_mobile_crypto_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobileDecryptResponse.ProtoReflect.Descriptor instead.
func (*MobileDecryptResponse) Descriptor() ([]byte, []int) {
	return file_cc_mobile_crypto_proto_rawDescGZIP(), []int{1}
}

func (x *MobileDecryptResponse) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *MobileDecryptResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// 手机号加密请求
type MobileEncryptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"` // 明文手机号
}

func (x *MobileEncryptRequest) Reset() {
	*x = MobileEncryptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cc_mobile_crypto_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobileEncryptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobileEncryptRequest) ProtoMessage() {}

func (x *MobileEncryptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cc_mobile_crypto_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobileEncryptRequest.ProtoReflect.Descriptor instead.
func (*MobileEncryptRequest) Descriptor() ([]byte, []int) {
	return file_cc_mobile_crypto_proto_rawDescGZIP(), []int{2}
}

func (x *MobileEncryptRequest) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

// 手机号加密响应
type MobileEncryptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ciphertext string `protobuf:"bytes,1,opt,name=ciphertext,proto3" json:"ciphertext"` // 加密后的手机号
	Error      string `protobuf:"bytes,2,opt,name=error,proto3" json:"error"`           // 错误信息
}

func (x *MobileEncryptResponse) Reset() {
	*x = MobileEncryptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cc_mobile_crypto_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobileEncryptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobileEncryptResponse) ProtoMessage() {}

func (x *MobileEncryptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cc_mobile_crypto_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobileEncryptResponse.ProtoReflect.Descriptor instead.
func (*MobileEncryptResponse) Descriptor() ([]byte, []int) {
	return file_cc_mobile_crypto_proto_rawDescGZIP(), []int{3}
}

func (x *MobileEncryptResponse) GetCiphertext() string {
	if x != nil {
		return x.Ciphertext
	}
	return ""
}

func (x *MobileEncryptResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

var File_cc_mobile_crypto_proto protoreflect.FileDescriptor

var file_cc_mobile_crypto_proto_rawDesc = []byte{
	0x0a, 0x16, 0x63, 0x63, 0x2f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x63, 0x63, 0x22, 0x36, 0x0a, 0x14,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x69, 0x70, 0x68, 0x65, 0x72, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x69, 0x70, 0x68, 0x65, 0x72,
	0x74, 0x65, 0x78, 0x74, 0x22, 0x43, 0x0a, 0x15, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x65,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x2e, 0x0a, 0x14, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x22, 0x4b, 0x0a, 0x15, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x69, 0x70, 0x68, 0x65, 0x72, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x69, 0x70, 0x68, 0x65, 0x72,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x32, 0xa4, 0x01, 0x0a, 0x12, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x46, 0x0a, 0x0d, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x12, 0x18, 0x2e, 0x63, 0x63, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44,
	0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e,
	0x63, 0x63, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x0d, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x12, 0x18, 0x2e, 0x63, 0x63,
	0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x63, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x42, 0x0a, 0x5a, 0x08, 0x5f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x63,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cc_mobile_crypto_proto_rawDescOnce sync.Once
	file_cc_mobile_crypto_proto_rawDescData = file_cc_mobile_crypto_proto_rawDesc
)

func file_cc_mobile_crypto_proto_rawDescGZIP() []byte {
	file_cc_mobile_crypto_proto_rawDescOnce.Do(func() {
		file_cc_mobile_crypto_proto_rawDescData = protoimpl.X.CompressGZIP(file_cc_mobile_crypto_proto_rawDescData)
	})
	return file_cc_mobile_crypto_proto_rawDescData
}

var file_cc_mobile_crypto_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_cc_mobile_crypto_proto_goTypes = []interface{}{
	(*MobileDecryptRequest)(nil),  // 0: cc.MobileDecryptRequest
	(*MobileDecryptResponse)(nil), // 1: cc.MobileDecryptResponse
	(*MobileEncryptRequest)(nil),  // 2: cc.MobileEncryptRequest
	(*MobileEncryptResponse)(nil), // 3: cc.MobileEncryptResponse
}
var file_cc_mobile_crypto_proto_depIdxs = []int32{
	0, // 0: cc.MobileCryptoService.MobileDecrypt:input_type -> cc.MobileDecryptRequest
	2, // 1: cc.MobileCryptoService.MobileEncrypt:input_type -> cc.MobileEncryptRequest
	1, // 2: cc.MobileCryptoService.MobileDecrypt:output_type -> cc.MobileDecryptResponse
	3, // 3: cc.MobileCryptoService.MobileEncrypt:output_type -> cc.MobileEncryptResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_cc_mobile_crypto_proto_init() }
func file_cc_mobile_crypto_proto_init() {
	if File_cc_mobile_crypto_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cc_mobile_crypto_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobileDecryptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cc_mobile_crypto_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobileDecryptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cc_mobile_crypto_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobileEncryptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cc_mobile_crypto_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobileEncryptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cc_mobile_crypto_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cc_mobile_crypto_proto_goTypes,
		DependencyIndexes: file_cc_mobile_crypto_proto_depIdxs,
		MessageInfos:      file_cc_mobile_crypto_proto_msgTypes,
	}.Build()
	File_cc_mobile_crypto_proto = out.File
	file_cc_mobile_crypto_proto_rawDesc = nil
	file_cc_mobile_crypto_proto_goTypes = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// MobileCryptoServiceClient is the client API for MobileCryptoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MobileCryptoServiceClient interface {
	// 手机号解密
	MobileDecrypt(ctx context.Context, in *MobileDecryptRequest, opts ...grpc.CallOption) (*MobileDecryptResponse, error)
	// 手机号加密
	MobileEncrypt(ctx context.Context, in *MobileEncryptRequest, opts ...grpc.CallOption) (*MobileEncryptResponse, error)
}

type mobileCryptoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMobileCryptoServiceClient(cc grpc.ClientConnInterface) MobileCryptoServiceClient {
	return &mobileCryptoServiceClient{cc}
}

func (c *mobileCryptoServiceClient) MobileDecrypt(ctx context.Context, in *MobileDecryptRequest, opts ...grpc.CallOption) (*MobileDecryptResponse, error) {
	out := new(MobileDecryptResponse)
	err := c.cc.Invoke(ctx, "/cc.MobileCryptoService/MobileDecrypt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mobileCryptoServiceClient) MobileEncrypt(ctx context.Context, in *MobileEncryptRequest, opts ...grpc.CallOption) (*MobileEncryptResponse, error) {
	out := new(MobileEncryptResponse)
	err := c.cc.Invoke(ctx, "/cc.MobileCryptoService/MobileEncrypt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MobileCryptoServiceServer is the server API for MobileCryptoService service.
type MobileCryptoServiceServer interface {
	// 手机号解密
	MobileDecrypt(context.Context, *MobileDecryptRequest) (*MobileDecryptResponse, error)
	// 手机号加密
	MobileEncrypt(context.Context, *MobileEncryptRequest) (*MobileEncryptResponse, error)
}

// UnimplementedMobileCryptoServiceServer can be embedded to have forward compatible implementations.
type UnimplementedMobileCryptoServiceServer struct {
}

func (*UnimplementedMobileCryptoServiceServer) MobileDecrypt(context.Context, *MobileDecryptRequest) (*MobileDecryptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MobileDecrypt not implemented")
}

func (*UnimplementedMobileCryptoServiceServer) MobileEncrypt(context.Context, *MobileEncryptRequest) (*MobileEncryptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MobileEncrypt not implemented")
}

func RegisterMobileCryptoServiceServer(s *grpc.Server, srv MobileCryptoServiceServer) {
	s.RegisterService(&_MobileCryptoService_serviceDesc, srv)
}

func _MobileCryptoService_MobileDecrypt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MobileDecryptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MobileCryptoServiceServer).MobileDecrypt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.MobileCryptoService/MobileDecrypt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MobileCryptoServiceServer).MobileDecrypt(ctx, req.(*MobileDecryptRequest))
	}
	return interceptor(ctx, info, handler)
}

func _MobileCryptoService_MobileEncrypt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MobileEncryptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MobileCryptoServiceServer).MobileEncrypt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.MobileCryptoService/MobileEncrypt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MobileCryptoServiceServer).MobileEncrypt(ctx, req.(*MobileEncryptRequest))
	}
	return interceptor(ctx, info, handler)
}

var _MobileCryptoService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.MobileCryptoService",
	HandlerType: (*MobileCryptoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MobileDecrypt",
			Handler:    _MobileCryptoService_MobileDecrypt_Handler,
		},
		{
			MethodName: "MobileEncrypt",
			Handler:    _MobileCryptoService_MobileEncrypt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/mobile_crypto.proto",
}
