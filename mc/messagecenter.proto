syntax = "proto3";
package mc;

service MessageService {
  //发送订阅消息
  rpc SubscribeMessage (SubscribeMessageRequest) returns (BaseResponse);
}


message BaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message SubscribeMessageRequest {
  string touser = 1;
  string template_id = 2;
  string page = 3;
  string miniprogram_state = 5;
  string lang = 6;
  string data = 7;
  //渠道(0-阿闻 1-阿闻、2-医生端等)
  int32 app_channel = 8;
  //是否极光推送
  int32 is_jpush = 9;
  string jpush_content = 10;
  //主体:1-阿闻，2-极宠家，3-福码购 6-宠物saas
  int32 org_id = 11;
}