package mc

import (
	"context"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	lock sync.Mutex
	Conn *grpc.ClientConn
	RPC  MessageServiceClient
	Ctx  context.Context
}

var grpcClient *Client

func init() {
	grpcClient = new(Client)
}

func GetMessageCenterClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 30*time.Second)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return newClient()
}

func newClient() *Client {
	var err error
	url := config.GetString("grpc.message-center")
	//url = "10.1.1.248:7078"
	if url == "" {
		url = "127.0.0.1:7078"
	}

	//url = "10.1.1.248:7078"
	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("message，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = NewMessageServiceClient(grpcClient.Conn)
		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
	//c.Cf()
}
