// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cc/mobile_crypto.proto

package cc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 手机号解密请求
type MobileDecryptRequest struct {
	Ciphertext           string   `protobuf:"bytes,1,opt,name=ciphertext,proto3" json:"ciphertext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MobileDecryptRequest) Reset()         { *m = MobileDecryptRequest{} }
func (m *MobileDecryptRequest) String() string { return proto.CompactTextString(m) }
func (*MobileDecryptRequest) ProtoMessage()    {}
func (*MobileDecryptRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_3529b626c39dc768, []int{0}
}

func (m *MobileDecryptRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MobileDecryptRequest.Unmarshal(m, b)
}
func (m *MobileDecryptRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MobileDecryptRequest.Marshal(b, m, deterministic)
}
func (m *MobileDecryptRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MobileDecryptRequest.Merge(m, src)
}
func (m *MobileDecryptRequest) XXX_Size() int {
	return xxx_messageInfo_MobileDecryptRequest.Size(m)
}
func (m *MobileDecryptRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MobileDecryptRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MobileDecryptRequest proto.InternalMessageInfo

func (m *MobileDecryptRequest) GetCiphertext() string {
	if m != nil {
		return m.Ciphertext
	}
	return ""
}

// 手机号解密响应
type MobileDecryptResponse struct {
	Mobile               string   `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Error                string   `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MobileDecryptResponse) Reset()         { *m = MobileDecryptResponse{} }
func (m *MobileDecryptResponse) String() string { return proto.CompactTextString(m) }
func (*MobileDecryptResponse) ProtoMessage()    {}
func (*MobileDecryptResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_3529b626c39dc768, []int{1}
}

func (m *MobileDecryptResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MobileDecryptResponse.Unmarshal(m, b)
}
func (m *MobileDecryptResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MobileDecryptResponse.Marshal(b, m, deterministic)
}
func (m *MobileDecryptResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MobileDecryptResponse.Merge(m, src)
}
func (m *MobileDecryptResponse) XXX_Size() int {
	return xxx_messageInfo_MobileDecryptResponse.Size(m)
}
func (m *MobileDecryptResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MobileDecryptResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MobileDecryptResponse proto.InternalMessageInfo

func (m *MobileDecryptResponse) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *MobileDecryptResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

// 手机号加密请求
type MobileEncryptRequest struct {
	Mobile               string   `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MobileEncryptRequest) Reset()         { *m = MobileEncryptRequest{} }
func (m *MobileEncryptRequest) String() string { return proto.CompactTextString(m) }
func (*MobileEncryptRequest) ProtoMessage()    {}
func (*MobileEncryptRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_3529b626c39dc768, []int{2}
}

func (m *MobileEncryptRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MobileEncryptRequest.Unmarshal(m, b)
}
func (m *MobileEncryptRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MobileEncryptRequest.Marshal(b, m, deterministic)
}
func (m *MobileEncryptRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MobileEncryptRequest.Merge(m, src)
}
func (m *MobileEncryptRequest) XXX_Size() int {
	return xxx_messageInfo_MobileEncryptRequest.Size(m)
}
func (m *MobileEncryptRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MobileEncryptRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MobileEncryptRequest proto.InternalMessageInfo

func (m *MobileEncryptRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

// 手机号加密响应
type MobileEncryptResponse struct {
	Ciphertext           string   `protobuf:"bytes,1,opt,name=ciphertext,proto3" json:"ciphertext,omitempty"`
	Error                string   `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MobileEncryptResponse) Reset()         { *m = MobileEncryptResponse{} }
func (m *MobileEncryptResponse) String() string { return proto.CompactTextString(m) }
func (*MobileEncryptResponse) ProtoMessage()    {}
func (*MobileEncryptResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_3529b626c39dc768, []int{3}
}

func (m *MobileEncryptResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MobileEncryptResponse.Unmarshal(m, b)
}
func (m *MobileEncryptResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MobileEncryptResponse.Marshal(b, m, deterministic)
}
func (m *MobileEncryptResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MobileEncryptResponse.Merge(m, src)
}
func (m *MobileEncryptResponse) XXX_Size() int {
	return xxx_messageInfo_MobileEncryptResponse.Size(m)
}
func (m *MobileEncryptResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MobileEncryptResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MobileEncryptResponse proto.InternalMessageInfo

func (m *MobileEncryptResponse) GetCiphertext() string {
	if m != nil {
		return m.Ciphertext
	}
	return ""
}

func (m *MobileEncryptResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func init() {
	proto.RegisterType((*MobileDecryptRequest)(nil), "cc.MobileDecryptRequest")
	proto.RegisterType((*MobileDecryptResponse)(nil), "cc.MobileDecryptResponse")
	proto.RegisterType((*MobileEncryptRequest)(nil), "cc.MobileEncryptRequest")
	proto.RegisterType((*MobileEncryptResponse)(nil), "cc.MobileEncryptResponse")
}

func init() { proto.RegisterFile("cc/mobile_crypto.proto", fileDescriptor_3529b626c39dc768) }

var fileDescriptor_3529b626c39dc768 = []byte{
	// 222 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x4b, 0x4e, 0xd6, 0xcf,
	0xcd, 0x4f, 0xca, 0xcc, 0x49, 0x8d, 0x4f, 0x2e, 0xaa, 0x2c, 0x28, 0xc9, 0xd7, 0x2b, 0x28, 0xca,
	0x2f, 0xc9, 0x17, 0x62, 0x4a, 0x4e, 0x56, 0x32, 0xe3, 0x12, 0xf1, 0x05, 0x4b, 0xb9, 0xa4, 0x82,
	0xe5, 0x82, 0x52, 0x0b, 0x4b, 0x53, 0x8b, 0x4b, 0x84, 0xe4, 0xb8, 0xb8, 0x92, 0x33, 0x0b, 0x32,
	0x52, 0x8b, 0x4a, 0x52, 0x2b, 0x4a, 0x24, 0x18, 0x15, 0x18, 0x35, 0x38, 0x83, 0x90, 0x44, 0x94,
	0x5c, 0xb9, 0x44, 0xd1, 0xf4, 0x15, 0x17, 0xe4, 0xe7, 0x15, 0xa7, 0x0a, 0x89, 0x71, 0xb1, 0x41,
	0xec, 0x82, 0x6a, 0x82, 0xf2, 0x84, 0x44, 0xb8, 0x58, 0x53, 0x8b, 0x8a, 0xf2, 0x8b, 0x24, 0x98,
	0xc0, 0xc2, 0x10, 0x8e, 0x92, 0x1e, 0xcc, 0x7a, 0xd7, 0x3c, 0x14, 0xeb, 0x71, 0x98, 0xa2, 0xe4,
	0x0b, 0xb3, 0x16, 0xae, 0x1e, 0x6a, 0x2d, 0x01, 0xf7, 0x62, 0xb7, 0xde, 0x68, 0x21, 0x23, 0x97,
	0x30, 0xc4, 0x3c, 0x67, 0x70, 0xc0, 0x04, 0xa7, 0x16, 0x95, 0x65, 0x26, 0xa7, 0x0a, 0xb9, 0x70,
	0xf1, 0xa2, 0xf8, 0x4e, 0x48, 0x42, 0x2f, 0x39, 0x59, 0x0f, 0x5b, 0x40, 0x49, 0x49, 0x62, 0x91,
	0x81, 0xba, 0x09, 0x6e, 0x0a, 0xd4, 0xb1, 0xc8, 0xa6, 0xa0, 0xfa, 0x17, 0xd9, 0x14, 0x34, 0x9f,
	0x39, 0xf1, 0x44, 0x71, 0xc5, 0xeb, 0x83, 0x23, 0x4c, 0x3f, 0x39, 0x39, 0x89, 0x0d, 0xcc, 0x32,
	0x06, 0x04, 0x00, 0x00, 0xff, 0xff, 0x43, 0x4c, 0xbf, 0xb7, 0xd4, 0x01, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MobileCryptoServiceClient is the client API for MobileCryptoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MobileCryptoServiceClient interface {
	// 手机号解密
	MobileDecrypt(ctx context.Context, in *MobileDecryptRequest, opts ...grpc.CallOption) (*MobileDecryptResponse, error)
	// 手机号加密
	MobileEncrypt(ctx context.Context, in *MobileEncryptRequest, opts ...grpc.CallOption) (*MobileEncryptResponse, error)
}

type mobileCryptoServiceClient struct {
	cc *grpc.ClientConn
}

func NewMobileCryptoServiceClient(cc *grpc.ClientConn) MobileCryptoServiceClient {
	return &mobileCryptoServiceClient{cc}
}

func (c *mobileCryptoServiceClient) MobileDecrypt(ctx context.Context, in *MobileDecryptRequest, opts ...grpc.CallOption) (*MobileDecryptResponse, error) {
	out := new(MobileDecryptResponse)
	err := c.cc.Invoke(ctx, "/cc.MobileCryptoService/MobileDecrypt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mobileCryptoServiceClient) MobileEncrypt(ctx context.Context, in *MobileEncryptRequest, opts ...grpc.CallOption) (*MobileEncryptResponse, error) {
	out := new(MobileEncryptResponse)
	err := c.cc.Invoke(ctx, "/cc.MobileCryptoService/MobileEncrypt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MobileCryptoServiceServer is the server API for MobileCryptoService service.
type MobileCryptoServiceServer interface {
	// 手机号解密
	MobileDecrypt(context.Context, *MobileDecryptRequest) (*MobileDecryptResponse, error)
	// 手机号加密
	MobileEncrypt(context.Context, *MobileEncryptRequest) (*MobileEncryptResponse, error)
}

// UnimplementedMobileCryptoServiceServer can be embedded to have forward compatible implementations.
type UnimplementedMobileCryptoServiceServer struct {
}

func (*UnimplementedMobileCryptoServiceServer) MobileDecrypt(ctx context.Context, req *MobileDecryptRequest) (*MobileDecryptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MobileDecrypt not implemented")
}
func (*UnimplementedMobileCryptoServiceServer) MobileEncrypt(ctx context.Context, req *MobileEncryptRequest) (*MobileEncryptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MobileEncrypt not implemented")
}

func RegisterMobileCryptoServiceServer(s *grpc.Server, srv MobileCryptoServiceServer) {
	s.RegisterService(&_MobileCryptoService_serviceDesc, srv)
}

func _MobileCryptoService_MobileDecrypt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MobileDecryptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MobileCryptoServiceServer).MobileDecrypt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.MobileCryptoService/MobileDecrypt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MobileCryptoServiceServer).MobileDecrypt(ctx, req.(*MobileDecryptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MobileCryptoService_MobileEncrypt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MobileEncryptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MobileCryptoServiceServer).MobileEncrypt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cc.MobileCryptoService/MobileEncrypt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MobileCryptoServiceServer).MobileEncrypt(ctx, req.(*MobileEncryptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MobileCryptoService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cc.MobileCryptoService",
	HandlerType: (*MobileCryptoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MobileDecrypt",
			Handler:    _MobileCryptoService_MobileDecrypt_Handler,
		},
		{
			MethodName: "MobileEncrypt",
			Handler:    _MobileCryptoService_MobileEncrypt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cc/mobile_crypto.proto",
}
