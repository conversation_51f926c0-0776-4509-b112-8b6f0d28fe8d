# 手机号加密解密 gRPC 服务

## 概述

本服务提供手机号的加密和解密功能，使用 RC4 算法进行加密，支持多个项目通过 gRPC 调用。

## 服务定义

### Proto 文件位置
- `proto/cc/mobile_crypto.proto`
- `proto/cc/mobile_crypto.pb.go` (生成的 Go 代码)

### 服务接口

#### MobileCryptoService

**方法列表:**

1. **MobileEncrypt** - 手机号加密
   - 请求: `MobileEncryptRequest`
   - 响应: `MobileEncryptResponse`

2. **MobileDecrypt** - 手机号解密
   - 请求: `MobileDecryptRequest`
   - 响应: `MobileDecryptResponse`

## 消息定义

### MobileEncryptRequest
```protobuf
message MobileEncryptRequest {
    string mobile = 1; // 明文手机号
}
```

### MobileEncryptResponse
```protobuf
message MobileEncryptResponse {
    string ciphertext = 1; // 加密后的手机号
    string error = 2;      // 错误信息
}
```

### MobileDecryptRequest
```protobuf
message MobileDecryptRequest {
    string ciphertext = 1; // 加密的手机号
}
```

### MobileDecryptResponse
```protobuf
message MobileDecryptResponse {
    string mobile = 1; // 解密后的手机号
    string error = 2;  // 错误信息
}
```

## 使用示例

### Go 客户端示例

```go
package main

import (
    "_/proto/cc"
    "context"
    "fmt"
    "log"
    "time"
    
    "google.golang.org/grpc"
)

func main() {
    // 连接到gRPC服务器
    conn, err := grpc.Dial("localhost:8080", grpc.WithInsecure())
    if err != nil {
        log.Fatalf("Failed to connect: %v", err)
    }
    defer conn.Close()

    // 创建客户端
    client := cc.NewMobileCryptoServiceClient(conn)
    ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
    defer cancel()

    // 加密手机号
    encryptResp, err := client.MobileEncrypt(ctx, &cc.MobileEncryptRequest{
        Mobile: "13800138000",
    })
    if err != nil {
        log.Fatalf("MobileEncrypt failed: %v", err)
    }
    
    fmt.Printf("加密后: %s\n", encryptResp.Ciphertext)

    // 解密手机号
    decryptResp, err := client.MobileDecrypt(ctx, &cc.MobileDecryptRequest{
        Ciphertext: encryptResp.Ciphertext,
    })
    if err != nil {
        log.Fatalf("MobileDecrypt failed: %v", err)
    }
    
    fmt.Printf("解密后: %s\n", decryptResp.Mobile)
}
```

### 其他语言客户端

可以使用 protoc 生成其他语言的客户端代码：

```bash
# Python
protoc --python_out=. --grpc_python_out=. cc/mobile_crypto.proto

# Java
protoc --java_out=. --grpc-java_out=. cc/mobile_crypto.proto

# C#
protoc --csharp_out=. --grpc_out=. cc/mobile_crypto.proto
```

## 部署和配置

### 服务注册

服务已在 `main.go` 中注册：

```go
cc.RegisterMobileCryptoServiceServer(micro.GrpcServer, &services.MobileCryptoService{})
```

### 依赖配置

服务依赖于 `utils.MobileEncrypt` 和 `utils.MobileDecrypt` 方法，这些方法需要：

1. 安全密钥配置 (`security_code`)
2. 数据安全服务地址 (`awen_url`)

确保配置文件中包含这些配置项。

## 测试

### 运行单元测试

```bash
go test ./services -v -run TestMobileCryptoService
```

### 运行客户端示例

```bash
# 确保服务已启动
go run examples/mobile_crypto_client.go
```

## 错误处理

服务会在以下情况返回错误：

1. **加密请求**：
   - `mobile` 参数为空：返回 "mobile cannot be empty"

2. **解密请求**：
   - `ciphertext` 参数为空：返回 "ciphertext cannot be empty"

## 安全注意事项

1. **密钥管理**：确保安全密钥的安全存储和传输
2. **网络安全**：在生产环境中使用 TLS 加密 gRPC 连接
3. **访问控制**：根据需要添加身份验证和授权机制
4. **日志安全**：避免在日志中记录敏感的手机号信息

## 性能考虑

1. **连接复用**：客户端应复用 gRPC 连接
2. **超时设置**：合理设置请求超时时间
3. **并发控制**：根据服务器性能调整并发请求数量

## 监控和日志

服务会记录以下日志：
- 请求参数（加密后的密文）
- 响应结果
- 错误信息

建议在生产环境中配置适当的日志级别和监控指标。
