syntax = "proto3";
import "google/protobuf/empty.proto";
import "google/protobuf/wrappers.proto";
package dac;

//数据中心服务相关
service DatacenterService {
  //查询员工列表
  rpc QueryStaff (QueryStaffRequest) returns (QueryStaffResponse);
  //可用渠道查询
  rpc QueryPlatformChannelUsable (selectType) returns (PlatformChannelResponse);
  //通过渠道id查询渠道信息
  rpc QueryPlatformChannelById (PlatformChannel) returns (PlatformChannelResponse);
  //门店信息查询
  rpc QueryStores (StoreRequest) returns (StoreResponse);
  //渠道门店ID查询1
  rpc QueryStoresChannelId (StoreRelationUserRequest) returns (StoreRelationUserResponse);
  //根据渠道门店id查询门店财务编码
  rpc GetFinanceCodeByStoresChannelId (StoreRelationUserRequest) returns (StoreRelationUserResponse);
  //查询渠道id下所有的门店id
  rpc GetStoreidsByChannelId (ChannelRequest) returns (StoreidsResponse);

  //查询与渠道关联的门店信息
  rpc QueryStoreInfo (StoreInfoRequest) returns (StoreInfoResponse);
  //查询与渠道关联的用户权限内的门店信息
  rpc QueryStoreInfoUserAuthority (StoreInfoRequest) returns (StoreInfoResponse);

  //消息创建
  rpc MessageCreate (MessageCreateRequest) returns (MessageCreateResponse);
  //消息创建修改读取状态
  rpc MessageUpdate (MessageUpdateRequest) returns (MessageUpdateResponse);
  //消息列表查询
  rpc MessageList (MessageListRequest) returns (MessageListResponse);
  //消息发送
  rpc MessageSend (MessageSendRequest) returns (MessageSendResponse);
  //通过门店Id发送消息
  rpc MessageSendWithShopId (MessageSendWithShopIdRequest) returns (MessageSendWithShopIdResponse);

  //创建配送门店状态修改
  rpc CreateShopStatus (CreateShopStatusRequest) returns (StoreToShopResponse);
  //创建门店
  rpc CreateShop (StoreToShopRequest) returns (StoreToShopResponse);
  //门店信息绑定
  rpc ShopTozilong (StoreToShopRequest) returns (StoreToShopResponse);
  //批量查询对应关系
  rpc SeachRelationList (StoreRelationRequest) returns (StoreRelationResponse);
  //门店信息远程批量查询，掉北京接口
  rpc StoreList (StoreListRequest) returns (StoreListResponse);
  //批量插入门店信息
  rpc StoreListAdd (StoreListAddRequest) returns (StoreListAddResponse);

  //设置添加
  rpc ShopSetAdd (ShopSetAddRequest) returns (ShopSetAddResponse);
  //设置获取单个
  rpc ShopSetGet (ShopSetGetRequest) returns (ShopSetGetResponse);
  //设置修改
  rpc ShopSetUpdate (ShopSetAddRequest) returns (ShopSetAddResponse);
  //设置获取列表，分页查询
  rpc ShopSetList (ShopSetListRequest) returns (ShopSetListResponse);
  //保存用户门店权限信息
  rpc SaveStoreUserAuthority (StoreUserAuthorityRequest) returns (BaseResponse);
  //根据财务编码/用户编号，查询拥有该门店权限的用户编号/财务编码
  rpc QueryStoreUserAuthority (StoreUserAuthorityRequest) returns (StoreUserAuthorityResponse);
  //查询门店位置信息（查询附近门店，查询是否在配送范围内）
  rpc QueryEsStorePointShape (EsSearchRequest) returns (EsSearchResponse);
  //店铺配送服务设置
  rpc ShopDeliveryServiceSet (ShopDeliveryServiceSetRequest) returns (ShopDeliveryServiceSetResponse);
  //店铺配送服务设置获取
  rpc ShopDeliveryServiceGet (ShopDeliveryServiceGetRequest) returns (ShopDeliveryServiceGetResponse);
  //获取店铺基本信息
  rpc ShopStoreGet (ShopStoreGetRequest) returns (ShopStoreGetResponse);
  //获取店铺主体id
  rpc GetStoreOrgId (GetStoreOrgIdReq) returns (GetStoreOrgIdResp);

  //店铺基本信息更新
  rpc ShopStoreUpdate (ShopStoreUpdateRequest) returns (BaseResponse);
  //获取店铺营业设置信息
  rpc ShopBusinessSetupGet (ShopBusinessSetupGetRequest) returns (ShopBusinessSetupGetResponse);
  //店铺营业设置更新
  rpc ShopBusinessSetupUpdate (ShopBusinessSetupUpdateRequest) returns (ShopBusinessSetupUpdateResponse);
  //店铺基本设置更新
  rpc ShopBasicSetupUpdate (ShopBasicSetupUpdateRequest) returns (ShopBasicSetupUpdateResponse);
  

  // 店铺自提更新
  rpc ShopSelfLiftingUpdate (ShopSelfLiftingUpdateRequest) returns (ShopSelfLiftingUpdateResponse);

  //获取员工列表
  rpc GetStaffList (GetStaffListRequest) returns (GetStaffListResponse);

  // 初始化配送范围
  rpc InitializationDeliveryarea (InitializationDeliveryareaRequest) returns (InitializationDeliveryareaResponse);

  //店铺品类和头像更新
  rpc ShopCateAndImageUpdate (ShopCateAndImageUpdateRequest) returns (ShopCateAndImageUpdateResponse);
  //店铺其它业务设置更新
  rpc ShopOtherUpdate (ShopOtherUpdateRequest) returns (ShopOtherUpdateResponse);
  //店铺Banner设置更新
  rpc ShopBannerUpdate (ShopBannerUpdateRequest) returns (ShopBannerUpdateResponse);
  //医院详情(前端项目使用)
  rpc HospitalInfoGet (HospitalInfoGetRequest) returns (HospitalInfoGetResponse);
  //城市列表
  rpc CitysGet (CitysGetRequest) returns (CitysGetResponse);
  //门店管理列表
  rpc ShopStoreList (ShopStoreListRequest) returns (ShopStoreListResponse);
  //店铺配送服务设置列表
  rpc ShopDeliveryServiceList (ShopDeliveryServiceListRequest) returns (ShopDeliveryServiceListResponse);
  //店铺配送服务设置明细获取
  rpc ShopDeliveryServiceDetail (ShopDeliveryServiceDetailRequest) returns (ShopDeliveryServiceDetailResponse);
  //店铺退货地址设置
  rpc ShopReturnAddressSet (ShopReturnAddressSetRequest) returns (BaseResponse);
  //V3.1版本需求：获取门店关系表
  rpc GetRelationList (StoreRelationRequest) returns (StoreRelationResponse);

  //获取用户权限内的门店医院列表
  rpc GetHospitalListByUserNo (GetHospitalListByUserNoRequest) returns (GetHospitalListByUserNoResponse);

  //根据用户id调用北京接口获取医院数据列表
  rpc GetDataByUserNo (GetHospitalListByUserNoRequest) returns (GetHospitalListByUserNoResponse);
  //根据用户id获取门店信息
  rpc GetOrgStoreList  (GetOrgStoreListRequest) returns (GetOrgStoreListResponse);
  //获取小程序码
  rpc GetShopWxAppCode (GetShopWxAppCodeRequest) returns (GetShopWxAppCodeResponse);
  //获取宠物saas 授权方小程序token
  rpc GetAuthorizerAccessToken (GetAuthorizerAccessTokenReq) returns (GetAuthorizerAccessTokenRes);
  //新增店铺管理-系统设置-订单提醒接口
  rpc RemindSetAdd (RemindSetAddRequest) returns (RemindSetResponse);
  //新增店铺管理-系统设置-自动接单设置接口
  rpc AutoSetAdd (AutoSetAddRequest) returns (AutoSetAddResponse);

  //订单导出任务——获取列表
  rpc GetOrderExportTaskList (GetOrderExportTaskListRequest) returns (GetOrderExportTaskListResponse);
  //订单导出任务——创建列表
  rpc CreateOrderExportTask (CreateOrderExportTaskListRequest) returns (CreateOrderExportTaskListResponse);
  //订单导出任务——更新列表
  rpc UpdateOrderExportTask (UpdateOrderExportTaskListRequest) returns (UpdateOrderExportTaskListResponse);

  //获取缓存里面的附近门店的门店、商品、优惠信息
  rpc GetProductShopByCache (GetProductShopByCacheRequest) returns (GetProductShopByCacheResponse);

  //新增广告
  rpc AdvertisementAdd (AdvertisementAddRequest) returns (BaseResponse);
  //获取广告详情
  rpc AdvertisementGet (AdvertisementGetRequest) returns (AdvertisementGetResponse);
  //获取广告列表
  rpc AdvertisementList (AdvertisementListRequest) returns (AdvertisementListResponse);
  //编辑广告
  rpc AdvertisementEdit (AdvertisementEditRequest) returns (BaseResponse);
  //获取播放广告
  rpc AdvertisementLaunchGet (AdvertisementLaunchGetRequest) returns (AdvertisementLaunchGetResponse);
  //获取北京门店结构
  rpc GetStoreTree (ShopTreeRequest) returns (ShopTreeResponse);
  //终止广告
  rpc AdvertisementStop (AdvertisementGetRequest) returns (BaseResponse);
  // 词库新增或编辑
  rpc NewAnalyzeToken (NewAnalyzeTokenRequest) returns (BaseResponse);
  // 分词查询(包括已删除的分词)
  rpc GetAnalyzeToken (GetAnalyzeTokenRequest) returns (GetAnalyzeTokenResponse);
  // 词库新增或编辑
  rpc DelAnalyzeToken (DelAnalyzeTokenRequest) returns (BaseResponse);

  rpc GetStoreInfoSyncEs (GetStoreInfoSyncEsRequest) returns (GetStoreInfoSyncEsResponse);
  rpc GetScrmHospitalInfoSyncEs (GetScrmHospitalInfoSyncEsRequest) returns (GetScrmHospitalInfoSyncEsResponse);

  //获取北京ACP员工信息
  rpc GetAcpUserInfo (BjAcpRequest) returns (BjAcpResponse);
  //添加北京ACP员工信息到本地
  rpc AddAcpUserInfo (BjAcpInfo) returns (ShopSetAddResponse);
  //修改本地ACP员工信息
  rpc UpdateAcpUserInfo (BjAcpInfo) returns (ShopSetAddResponse);
  //刪除本地ACP员工信息
  rpc DeleteAcpUserInfo (BjAcpInfo) returns (ShopSetAddResponse);
  //本地ACP员工信息List
  rpc SeleteAcpUserInfoList (BjAcpListRequest) returns (BjAcpListResponse);

  //添加更新提醒
  rpc AddRemind (AddRemindRequest) returns (ShopSetAddResponse);
  //删除更新提醒
  rpc DeleteRemind (AddRemindRequest) returns (ShopSetAddResponse);
  //编辑更新提醒
  rpc UpdateRemind (AddRemindRequest) returns (ShopSetAddResponse);
  //查询消息提醒列表分页
  rpc SelectRemindList (RemindListRequest) returns (RemindListResponse);
  //按用户编码查询没有度去过的更新提示
  rpc SelectRemindByUserNo (RemindUserNoRequest) returns (RemindListResponse);

  //查询Ehr的员工信息
  rpc GetEhrStaff (GetHerStaffRequest) returns (GetHerStaffResponse);
  //查询Ehr的组织机构信息
  rpc GetEhrStaffOrganization (GetHerStaffOrganizationRequest) returns (GetHerStaffOrganizationResponse);
  //查询Ehr的岗位信息
  rpc GetEhrStaffPost (GetHerStaffPostRequest) returns (GetHerStaffPostResponse);

  //查询scrm用户信息
  rpc GetScrmUser (GetScrmUserRequest) returns (GetScrmUserResponse);

  //查询店铺配送列表
  rpc GetStoreDeliveryList (GetStoreDeliveryListRequest) returns (GetStoreDeliveryListResponse);
  //新增/编辑店铺配送
  rpc SetStoreDelivery (StoreDelivery) returns (BaseResponse);
  //是否开启店铺配送
  rpc SetStoreDeliveryStatus (StoreDelivery) returns (BaseResponse);

  //查询 用户与外部关系 信息
  rpc GetMemberExternalRelation(GetMemberExternalRelationReq) returns (GetMemberExternalRelationRes);

  //新增 用户与外部关系 信息
  rpc AddMemberExternalRelation(AddMemberExternalRelationReq) returns (BaseResponse);

  rpc GetMemberInfo(GetMemberInfoReq) returns (GetMemberInfoRes);
  rpc GrantAuthorization(GetMemberInfoReq) returns (GetMemberInfoRes);
  rpc SearchAuthorization(GetMemberInfoReq) returns (GetMemberInfoRes);
  //根据宠物id获取SCRM的用户ID
  rpc GetSCRMUserIDByPetID (google.protobuf.StringValue) returns (BaseResponse);
  //查询店铺对应的渠道店铺关系
  rpc GetStoreChannelExternal(GetStoreChannelExternalReq)returns (GetStoreChannelExternalRes);
  // 更新用户的头像信息
  rpc UpdateUserInfo(UpdateUserInfoReq) returns (BaseResponse);

  // 根据财务编码获取appChannel
  rpc GetAppChannelByFinanceCode(GetAppChannelRequest)returns (GetAppChannelResponse);

  //添加APP版本信息
  rpc AddAppVersion(AddAppVersionRequest) returns (BaseResponse);
  //获取最新版本信息
  rpc GetAppVersion(GetAppVersionRequest) returns (GetAppVersionResponse);

  rpc LogOutMember(LogOutMemberRequest) returns(BaseResponse);

  rpc GetStoreMasterIDByAppId(GetStoreMasterIDRequest) returns(GetStoreMasterIDResponse);
  rpc GetStoreMasterInfo(GetStoreMasterInfoRequest) returns(GetStoreMasterInfoResponse);
  rpc CreateStoreMaster(CreateStoreMasterRequest) returns(CommonResponse);
  rpc UpdateStoreMaster(UpdateStoreMasterRequest) returns(CommonResponse);
  rpc DeleteStoreMaster(DeleteStoreMasterRequest) returns(CommonResponse);
  rpc GetStoreMasterList(GetStoreMasterListRequest) returns(GetStoreMasterListResponse);
  rpc GetStoreMasterIdByMerchantId(GetStoreMasterIdByMerchantIdRequest) returns(GetStoreMasterIdByMerchantIdResponse);
  //新增文件上传记录
  rpc AddFileUploadRecord(AddFileUploadRecordRequest) returns(BaseResponse);
  //获取文件上传记录
  rpc GetFileUploadRecords(GetFileUploadRecordsRequest) returns(GetFileUploadRecordsResponse);
  //更新文件上传记录
  rpc UpdateFileUploadRecordStatus(UpdateFileUploadRecordStatusRequest) returns(BaseResponse);
  //获取最新文件
  rpc GetFileUploadRecordNow(GetFileUploadRecordNowRequest) returns(GetFileUploadRecordNowResponse);


  //设置商详漂浮广告
  rpc SetAdGoodsDetail(SetAdGoodsDetailReq) returns (BaseResponse);
  //获取商详漂浮广告
  rpc GetAdGoodsDetail(GetAdGoodsDetailReq) returns (GetAdGoodsDetailRes);


  // 推送消息到百度小程序
  rpc SendTemplateMessage(TemplateMessageVo) returns (BaseResponse);
  // 获取门店的storeMaster
  rpc GetStoreMasterIdByFinanceCode(GetStoreMasterIdByFinanceCodeRequest) returns (GetStoreMasterIdByFinanceCodeResponse);

  // 获取首页金刚区配置 customer-api
  rpc NavigationBarList(NavigationBarListReq) returns (NavigationBarListRes);
  // 新增或修改首页金刚区配置
  rpc AddNavigationbar(AddNavigationbarReq) returns (BaseResponse);
  // 获取首页金刚区配置 boss
  rpc NavigationBarListByBoss(EmptyParam) returns (NavigationBarListRes);
  //金刚区配置上下架
  rpc NavigationbarUp(NavigationbarUpReq) returns (BaseResponse);

  //获取全国区域划分
  rpc QueryRegionalDivision(QueryRegionalDivisionRequest) returns (QueryRegionalDivisionResponse);
  //获取全国区域划分
  rpc MiniQueryRegionalDivision(QueryRegionalDivisionRequest) returns (QueryRegionalDivisionResponse);

  // 获取导入模板
  rpc ImportTemplate(ImportTemplateRequest) returns (ImportTemplateResponse);
  // 导入
  rpc Import(ImportRequest) returns(ImportResponse);
  // 导入历史
  rpc ImportHistories(ImportHistoryRequest) returns(ImportHistoryResponse);

  // 导出
  rpc ExportTaskCreate(ExportRequest)returns(ExportResponse);
  // 导出历史
  rpc ExportHistories(ExportHistoryRequest) returns(ExportHistoryResponse);

  // 获取店铺活动的状态
  rpc ShopActiveStateList(ShopActiveStateListRequest) returns(ShopActiveStateListResponse);

  // 发送短信
  rpc SendSms(SendSmsRequest) returns(SendSmsResponse);
  // 发送验证码短信
  rpc SendCode(SendCodeRequest) returns(SendCodeResponse);

  // 第三方渠道+阿闻渠道批量闭店开店
  rpc OpenOrCloseShop(ThirdChannelOpenOrCloseShopRequest) returns(ThirdChannelOpenOrCloseShopResponse);
  // 批量闭店开店任务
  rpc OpenOrCloseShopTask(OpenOrCloseShopTaskRequest) returns(OpenOrCloseShopTaskResponse);
  // 批量闭店开店任务错误信息
  rpc ExternalCallbackForBatchOpenCloseShop(ExternalCallbackForBatchOpenCloseShopRequest) returns(ThirdChannelOpenOrCloseShopResponse);
  // 仓库白名单列表
  rpc WarehouseWhiteList(WarehouseWhiteListReq) returns(WarehouseWhiteListRes);
  // 仓库白名单移除
  rpc WarehouseWhiteDel(WarehouseWhiteDelReq) returns(BaseResponse);
  // 仓库白名单批量导入
  rpc WarehouseWhiteImport(WarehouseWhiteImportReq) returns(BaseResponse);
  rpc QueryNearShop(NearShopRequest) returns (NearShopResponse);

  // 添加商城频道
  rpc AddMallChannel(AddMallChannelRequest) returns(Response);
  // 商城频道列表
  rpc GetMallChannelList(GetMallChannelListRequest) returns(GetMallChannelListResponse);
  // 编辑商城频道
  rpc EditMallChannel(EditMallChannelRequest) returns(Response);
  // 删除商城频道
  rpc DeleteMallChannel(DeleteMallChannelRequest) returns(Response);
  // 添加商城频道内容
  rpc AddMallChannelItem(AddMallChannelItemRequest) returns(Response);
  // 编辑商城频道内容列表
  rpc GetMallChannelItemList(GetMallChannelItemListRequest) returns(GetMallChannelItemListResponse);
  // 编辑商城频道内容
  rpc EditMallChannelItem(EditMallChannelItemRequest) returns(Response);
  // 删除商城频道内容
  rpc DeleteMallChannelItem(DeleteMallChannelItemRequest) returns(Response);
  // 获取商品关联的频道
  rpc GetGoodsChannels(GetGoodsChannelsRequest) returns(GetGoodsChannelsResponse);
  // 获取小程序前端频道专题内容
  rpc GetMiniMallChannelList(GetMiniMallChannelListRequest) returns(GetMiniMallChannelListResponse);
  // 下单步骤日志
  rpc OrderStepLog(OrderStepLogRequest) returns(Response);
  // 提交订单是否订阅日志
  rpc OrderSubscribeLog(OrderSubscribeLogRequest) returns(Response);
  // 链路值保存
  rpc OrderLinkStore(OrderLinkStoreReq) returns(Response);

  //店铺配送方式列表
  rpc StoreDeliveryMethodList(StoreDeliveryMethodListRequest)returns(StoreDeliveryMethodListResponse);
  //设置店铺配送方式
  rpc SetStoreDeliveryMethod(SetStoreDeliveryMethodRequest)returns(SetStoreDeliveryMethodResponse);
  //操作店铺日志列表
  rpc StoreOperateLog(StoreOperateLogRequest)returns(StoreOperateLogResponse);

  //获取店铺快递公司
  rpc StoreExpressList(StoreExpressListRequest) returns(StoreExpressListResponse);
  //获取店铺大区、城市列表信息
  rpc StoreRegionAndCityOptions(StoreRegionAndCityOptionsRequest) returns(StoreRegionAndCityOptionsResponse);
  rpc ContentSecurity(ContentSecurityRequest) returns(ContentSecurityResponse);
  // 获取会员等级信息
  rpc MemberLeverList(EmptyParam) returns(MemberLeverRes);
  // 初始化门店
  rpc EsInitStore(google.protobuf.Empty) returns(Response);
  // 初始化门店商品
  rpc EsInitStoreProduct(EsInitStoreProductReq) returns(Response);

  //会员卡 健康服务金权益 按月下发
  rpc InsuranceMonth(InsuranceMonthReq) returns(Response);

  //查询门店位置信息（查询附近门店，查询是否在配送范围内）
  rpc IsPointInPolygon (SaaSPolygonRequest) returns (SaaSPolygonResponse);

   //创建用户数据eshop.user_info数据
   rpc CreateEshopUserInfo (CreateEshopUserInfoRequest) returns (CreateEshopUserInfoResponse);
}
message CreateEshopUserInfoRequest  {
  string user_name = 1;
  string user_mobile = 2;
  int32 org_id = 3;
  string weixin_mini_openid = 4;
  string weixin_unionid = 5;
  int32 user_status = 6;
  int32 user_sex = 7;
}
message CreateEshopUserInfoResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

message GetOrgStoreListResponse{
  int32 code=1;
  int32 status=2;
  string message=3;
  repeated OrgStoreList data = 4;
}

message OrgStoreList{
  string id = 1;
  string name = 2;
  //机构名称
  string clinic_name = 3; 
  //机构编码
  string brand_code = 4; 
  //大区2级
	string area = 5;  
  string area_code = 6;  
  //城市
	string city=7; 
  string city_code = 8;     
  //深度
	int32  level=9;     
   //孩子
	repeated OrgStoreList children = 10; 

}
message GetOrgStoreListRequest{
  string user_no = 1; //用户编号
}
message InsuranceMonthReq{
  string begin_day = 1;
  string end_day = 2;
}
message Response {
  int64 code = 1;
  string message = 2;
}

message WarehouseWhiteImportReq {
  //文件URL
  string file_url = 1;
}

message WarehouseWhiteDelReq {
  int32 id = 1;
  //创建人ID（不用前端传）
  string create_id = 2;
  //创建人姓名（不用前端传）
  string create_name = 3;
  //创建人IP所属位置（不用前端传）
  string ip_location = 4;
  //创建人IP（前端不用传）
  string ip_addr = 5;
}

message WarehouseWhiteListReq{
  // 搜索字段
  string search = 1;
  // 页码
  int32 page_index = 2;
  // 每页数量
  int32 page_size = 3;
}

message WarehouseWhiteListRes {
  string msg = 1;
  int64 total = 2;
  repeated WarehouseWhiteList data = 3;
}

message WarehouseWhiteList {
  int32 id = 1;
  string warehouse_code = 2;
  string warehouse_name = 3;
  //创建时间
  string create_time = 4;
  //更新时间
  string update_time = 5;
  //创建人姓名
  string create_name = 6;
  //创建人ip
  string create_ip = 7;
  //ip所属位置
  string ip_location = 8;
  //更新人姓名
  string update_name = 9;
}


message NearShopRequest {
    float lat = 1;
    float lon = 2;
}
message NearShopResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated NearShop shops = 4;
}

message NearShop {
  //财务编码
  string finance_code = 1;
  //门店名称
  string name = 2;
  //门店地址
  string address = 3;
  //距离
  string  distance = 4;
  //营业时间
  string begin = 5;
  //闭店时间
  string end = 6;
  //门店图片
  string avatar = 7;
  //门店经纬度(纬度经度)
  string location = 8;
}

message NavigationBarListReq {
  //客户端类别 1微信小程序  2安卓  3IOS
  int32 client_type = 1;
}

message NavigationbarUpReq {
  //id
  int32 id = 1;
  //1上架  0下架
  int32 is_up = 2;
}
message GetStoreMasterIdByFinanceCodeRequest{
  repeated string finance_code = 1;
}

message GetStoreMasterIdByFinanceCodeResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated StoreMasterShop store_master_list = 4;
}
message StoreMasterShop {
  string finance_code = 1;
  int32 store_master_id = 2;
}
message CreateStoreMasterRequest{
  string name = 1;
  string elm_app_id = 2;
  string elm_app_secret = 3;
  string mt_app_id = 4;
  string mt_app_secret = 5;
  string jddj_app_id = 6;
  string jddj_app_secret = 7;
  string jddj_app_merchant_id = 8;
  string user_no = 9;
}

message UpdateStoreMasterRequest{
  StoreMasterInfo store_master_info = 1;
  string user_no = 2;
}

message DeleteStoreMasterRequest{
  int32 id = 1;
  string user_no = 2;
}

message GetStoreMasterListRequest{
  // 信息等级 1.只返回 id 与 name
  int32 info_level = 1;
  // 搜索字段
  string search = 2;
  // 页码
  int32 page_index = 3;
  // 每页数量
  int32 page_size = 4;
}

message GetStoreMasterIDRequest{
  string app_id = 1;
  int32 channel_id = 2 ;
}

message GetStoreMasterIDResponse{
  CommonResponse common = 1;
  int32 store_master_id = 2;
}

message GetStoreMasterIdByMerchantIdRequest{
  string jddj_app_merchant_id = 1;
}

message GetStoreMasterIdByMerchantIdResponse{
  CommonResponse common = 1;
  int32 id = 2;
}
message GetStoreMasterInfoRequest{
  int32 id = 1;
}

message GetStoreMasterListResponse{
  CommonResponse common = 1;
  int32 total = 2;
  repeated StoreMasterInfo data = 3;
}

message GetStoreMasterInfoResponse{
  CommonResponse common = 1;
  StoreMasterInfo data = 2;
}

message StoreMasterInfo{
  int32 id = 1;
  string name = 2;
  string elm_app_id = 3;
  string elm_app_secret = 4;
  string mt_app_id = 5;
  string mt_app_secret = 6;
  string jddj_app_id = 7;
  string jddj_app_secret = 8;
  string jddj_app_merchant_id = 9;
}

message GetAppChannelRequest{
  // 财务编码
  string finance_code = 1;
}
message GetStoreChannelExternalReq{
  //渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)
  int32 channel_id = 1;
  //渠道对应的店铺名称（例如：美团对应的店铺Id）
  string channel_store_id = 2;

}

message GetAppChannelResponse{
  int32 code = 1;
  string message = 2;
  string error = 3;
  // 1 瑞鹏自有， 2 tp代运营
  int32 app_channel = 4;
}

message GetMemberInfoRes{

  MemberInfoData result = 1 ;
}


message GetStoreChannelExternalRes {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated StoreChannelExternalData data = 4;
}

message StoreChannelExternalData{
  //财务编码
  string finance_code = 1;
  //渠道对应的店铺名称（例如：美团对应的店铺Id）
  string channel_store_id = 2;
  //1.阿闻自有,2.TP代运营
  int32  app_channel = 3;
}

message  GetMemberInfoReq {
  //用户手机号
  string userMobile = 1;
  string baiduOpenId = 2;
  string unionId = 3;
  int32 org_id = 4;
  string user_no = 5;
  string user_name = 6;
  string protocol_type = 7;
}

message MemberInfoData{
  int32 id = 1;
  string userId = 2;
  string userImId = 3;
  string userImToken = 4;
  string userName = 5;
  int32 userSex = 6;
  string userMobile = 7;
  int32 userSource = 8;
  int32 userStatus = 9;
  string userAvatar = 10;
  string userBirthday = 11;
  string firstRaisesPet = 12;
  string city = 13;
  string country = 14;
  string province = 15;
  string area = 16;
  string userRemark = 17;
  string createTime = 18;
  string updateTime = 19;
  string bigdataUserId = 20;
  int32 isReal = 21;
  string message = 22;
  string systemError = 23;
  string businessError = 24;
  int32 statusCode = 25;
  string extensions = 26;
  bool success = 27;
  // 微信openid
  string wechat_open_id = 29;
  repeated PetInfoData petList = 28;
  string UserInfoId = 30;
}

message PetInfoData{
  int32 id = 1;
  string petId = 2;
  string userId = 3;
  string petName = 4;
  int32 petSex = 5;
  int32 petKindof = 6;
  int32 petVariety = 7;
  int32 petNeutering = 8;
  int32 petVaccinated = 9;
  int32 petDeworming = 10;
  int32 petWeight = 11;
  int32 petLong = 12;
  int32 petHeight = 13;
  int32 petSource = 14;
  int32 petStatus = 15;
  string petAvatar = 16;
  string petBirthday = 17;
  string petHomeday = 18;
  string petRemark = 19;
  string createTime = 20;
  string updateTime = 21;
  string bigdataPetId = 22;
  string faceId = 23;
  string petCode = 24;
  string insuranceFaceId = 25;
  string dogLicenceCode = 26;
  string dogVaccinateCode = 27;
  string enterSource = 28;
  string chipCode = 29;
  string petFlower = 30;
  string flowerCode = 31;
  string sendTime = 32;
  string scrmPetPhoto = 33;
  string shopId = 34;
  string shopName = 35;
  string petVarietyName = 36;
  string petKindOfName = 37;
  string petInfoId = 38; // eshop.pet_info表的pet_info_id字段
}

message AddMemberExternalRelationReq{
  //用户手机号
  string member_mobile = 1;
  //scrm userId
  string scrm_user_id = 2;
  //百度openId
  string baidu_open_id = 3;
  //微信openId
  string wechat_open_id = 4;
  //用户名称
  string member_name = 5;
  //微信ID
  string union_id = 6;
}



message GetMemberExternalRelationReq {
  //用户手机号
  string member_mobile = 1;
  //scrm userId
  string scrm_user_id = 2;
  //百度openId
  string baidu_open_id = 3;
  //微信openId
  string wechat_open_id = 4;
  //微信ID
  string union_id = 5;
}
message GetMemberExternalRelationRes{
  int32 code = 1;
  string message = 2;
  string error = 3;
  MemberExternalRelationData data = 4;
}

message MemberExternalRelationData{
  //用户手机号
  string mobile = 1;
  //scrm userId
  string scrm_user_id = 2;
  //百度openId
  string baidu_open_id = 3;
  //微信openId
  string wechat_open_id = 4;
  //用户名称
  string member_name = 5;
  //0已注销 1正常
  int32 state = 6;
  //注销时间
  string cancel_time = 7;
  //注销原因
  string reason = 8;
  //微信UNID
  string union_id = 9;
}


message GetScrmUserRequest {
  //用户手机号
  string user_mobile = 1;
}

message GetScrmUserResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  //用户信息
  ScrmUserInfo data = 4;
}

message ScrmUserInfo {
  string user_id = 1;
  string user_name = 2;
  string user_mobile = 3;
}

message GetProductShopByCacheRequest {
  //店铺id集合
  repeated string shop_ids = 1;
  int32 channel_id = 2;
  // 是否显示推荐商品
  bool show_recommend = 3;
}
message GetProductShopByCacheResponse {
  //返回的json 字符串
  string product_shop_json = 1;
}

/**
    新增店铺管理-系统设置-订单提醒接口
**/
//Request
message RemindSetAddRequest {
  //店铺ID
  string shop_id = 1;
  //接单声音是否开启
  int32 is_getorder = 2;
  //提示音频率，1播放3次，2循环播放
  int32 getordertype = 3;
  //催单声音是否开启
  int32 is_reminder = 4;
  //提示音频率，1播放3次，2循环播放
  int32 remindertype = 5;
  //退单声音是否开启
  int32 is_backorder = 6;
  //提示音频率，1播放3次，2循环播放
  int32 backordertype = 7;
}
//Response
message RemindSetResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

message QueryStaffRequest {
  //页码
  int32 page_index = 1;
  //每页数量
  int32 page_size = 2;
  //手机号
  string mobile = 3;
  //员工工号
  string staff_no = 4;
  //员工姓名
  string staff_name = 5;
}

message QueryStaffResponse {
  repeated Staff data = 1;
  int64 total_count = 2;
}

message Staff {
  //ehr系统唯一id
  string unique_id = 1;
  //员工工号
  string staff_no = 2;
  //单位代码
  string unit_code = 3;
  //部门代码
  string department_code = 4;
  //职位代码
  string job_code = 5;
  //门店编码
  string finance_code = 6;
  //员工姓名
  string staff_name = 7;
  //性别，0未知，1男，2女
  string sex = 8;
  //出生年月日
  string birth = 9;
  //城市
  string city = 10;
  //手机号
  string mobile = 11;
  //身份证号
  string card_no = 12;
  //职位名称
  string job_name = 13;
  //职位路径
  string job_path = 14;
  //在职状态，1在职，0离职
  int32 duty_status = 15;
  //入职时间
  string hire_date = 16;
}

/**
    新增店铺管理-系统设置-自动接单设置接口
**/
//Request
message AutoSetAddRequest {
  //店铺ID
  string shop_id = 1;
  //是否自动接单1：是，0：否
  int32 is_aotu_order = 2;
}
//Response
message AutoSetAddResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

/**
    获取订单导出任务列表
**/
//order_export_task模型
message ExportTask {
  //主键id
  int32 id = 1;
  //任务内容:1.导出订单数据;2.导出(含商品明细)数据
  int32 task_content = 2;
  //任务状态:1:生成中;2:已完成;3.失败
  int32 task_status = 3;
  //结果文件路径
  string resulte_file_url = 4;
  //修改时间
  string modify_time = 5;
  //用户编码
  string create_id = 6;
  //创建时间
  string create_time = 7;
  //创建人姓名
  string create_name = 8;
  //ip
  string ip = 9;
  //ip归属地
  string ip_location = 10;
  // 任务数量
  int32 task_num = 11;
}

//Request
message GetOrderExportTaskListRequest {
  //用户编码
  string create_id = 1;
  //创建时间
  string createtime = 2;
  //任务内容:1.实物订单-导出订单数据;2.实物订单-导出(含商品明细)数据;3.退货退款管理-导出订单数据;（以逗号分隔开的字符串）
  string task_content = 3;
  //页大小
  int32 page_size = 4;
  //当前页
  int32 page_index = 5;
  //查找对象:1.自己;2.所有人
  int32 search_type = 6;
  //主体id
  int32 org_id = 7;
  //财务编码
  string financial_code = 8;
}

//Response
message GetOrderExportTaskListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  int32 TotalCount = 4;
  //任务列表
  repeated ExportTask details = 7;
}

/**
    订单导出任务——创建列表
**/
//Request
message CreateOrderExportTaskListRequest {
  //任务内容:1.导出订单数据;2.导出(含商品明细)数据
  int32 task_content = 1;
  //任务状态:1:生成中;2:已完成;3.失败
  int32 task_status = 2;
  //操作文件路径
  string operation_file_url = 3;
  //结果文件路径
  string resulte_file_url = 4;
  //修改时间
  string modify_time = 5;
  //用户编码
  string create_id = 6;
  //创建时间
  string create_time = 7;
  //任务名称（非必须）
  string task_name = 8;
  //创建人名字
  string create_name = 9;
  //创建时IP
  string ip = 10;
  //IP归属地址
  string ip_location = 11;
  //主体id
  int32 org_id = 12;
  //财务编码
  string financial_code = 13;
}

//Response
message CreateOrderExportTaskListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  int32 id = 4;
}

/**
    订单导出任务——更新列表
**/
//Request
message UpdateOrderExportTaskListRequest {
  //主键id
  int32 id = 1;
  //任务状态:1:生成中;2:已完成;3.失败
  int32 task_status = 2;
  //结果文件路径
  string resulte_file_url = 3;
  //导出任务数
  int32 task_num = 4;
}

//Response
message UpdateOrderExportTaskListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}


message GetStaffListRequest {
  //财务编码
  string finance_code = 1;
}
message GetStaffListResponse {
  //财务编码
  repeated StaffList data = 1;
}
message StaffList {
  //财务编码
  string staff_id = 1;
  string staff_name = 2;
  string finance_code = 3;
}
message ShopReturnAddressSetRequest {
  //财务编码
  string finance_Code = 1;
  //渠道ID，必填
  int32 channel_Id = 2;
  //退货地址
  string return_address = 3;
}
message ShopDeliveryServiceDetailRequest {
  //财务编码
  string finance_Code = 1;
  //渠道ID，必填
  int32 channel_Id = 2;
}
message ShopDeliveryServiceDetailResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  ShopDeliveryServiceDetail data = 4;
}
message ShopDeliveryServiceDetail {
  //财务编码
  string finance_Code = 1;
  //子龙门店id
  string zilong_id = 2;
  //渠道id
  int32 channel_Id = 3;
  //收费模式：0按距离收取配送费
  int32 chargetype = 4;
  //配送区域,经纬度坐标点
  string deliveryarea = 5;
  //基础起送价,分
  int32 basefee = 6;
  //基础配送价，分
  int32 deliveryfee = 7;
  //动态配送费用,距离加价
  repeated DeliveryDistance deliveryDistance = 8;
  //动态配送费用,重量加价
  repeated DeliveryWeight deliveryWeight = 9;
  //特殊时段配送费用
  repeated DeliverySpecialtimefee deliverySpecialtimefee = 10;
  //id，新增传0
  int32 id = 11;
  //店铺头像
  string image = 14;
  //营业日期
  repeated int32 Businessdate = 15;
  //营业开始时间
  int32 Business_opentime = 16;
  //营业结束时间
  int32 Business_closetime = 17;
  // 当前店铺是否开启了社区配送
  int32 enable_pickup = 18;
}
message DeliveryDistance {
  int32 id = 1;
  //财务ID
  string finance_Code = 2;
  //渠道id
  int32 channel_Id = 3;
  //起始距离
  float begin = 4;
  //结束距离）
  float end = 5;
  //价格，分
  int32 Fee = 6;
}
message DeliveryWeight {
  int32 id = 1;
  //财务ID
  string finance_Code = 2;
  //渠道id
  int32 channel_Id = 3;
  //起始重量
  float begin = 4;
  //结束重量
  float end = 5;
  //超出重量
  float weight = 6;
  //价格，分
  int32 Fee = 7;
}
message DeliverySpecialtimefee {
  int32 id = 1;
  //财务ID
  string finance_Code = 2;
  //渠道id
  int32 channel_Id = 3;
  //起始时间，小时
  int32 begin_hour = 4;
  //起始时间，分
  int32 begin_minute = 5;
  //结束时间，小时
  int32 end_hour = 6;
  //结束时间，分
  int32 end_minute = 7;
  //起送价，分
  int32 base_fee = 8;
  //配送费，分
  int32 delivery_fee = 9;
}
message ShopDeliveryServiceListRequest {
  //财务编码集合，以,分隔
  string finance_codes = 1;
  //渠道ID，非必填
  int32 channel_id = 2;
}
message ShopDeliveryServiceListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  int32 total_count = 4;
  //门店信息
  repeated ShopDeliveryServiceInfo DataList = 5;
}
message ShopStoreListRequest {
  //每页大小
  int32 pageSize = 1;
  //当前页
  int32 page = 2;
  //渠道ID
  string channelId = 3;
  //店铺名称模糊查询
  string shop_name = 4;
  //城市名称，可模糊搜索
  string city = 5;
}
message ShopStoreListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  int32 total_count = 4;
  //门店信息
  repeated ShopStore DataList = 5;
}
message ShopStore {
  //id
  int32 id = 1;
  //财务编码
  string finance_Code = 2;
  //渠道id
  int32 channel_Id = 3;
  //渠道门店id
  string channel_store_id = 4;
  //全渠道往来单位
  string custom_code = 5;
  //门店名称
  string shop_name = 6;
  //城市
  string city = 7;
  //门店地址
  string shop_address = 8;
  //营业时间
  string business_times = 9;
}
message ShopDeliveryServiceGetRequest {
  //财务编码
  string finance_Code = 1;
  //子龙门店id
  string zilong_id = 2;
  //渠道id
  int32 channel_Id = 3;
}
message ShopDeliveryServiceGetResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  //店铺营业设置信息
  ShopDeliveryServiceInfo data = 4;
}
message ShopDeliveryServiceInfo {
  //财务编码
  string finance_code = 1;
  //子龙门店id
  string zilong_id = 2;
  //渠道id
  int32 channel_id = 3;
  //收费模式：0按距离收取配送费
  int32 chargetype = 4;
  //配送区域,经纬度坐标点
  string deliveryarea = 5;
  //基础起送价,分
  int32 basefee = 6;
  //基础配送价，分
  int32 deliveryfee = 7;
  //动态配送费用,距离加价
  string distanceprice = 8;
  //动态配送费用,重量加价
  string weightprice = 9;
  //特殊时段配送费用
  string specialtimefee = 10;
  //id，新增传0
  int32 id = 11;
  //预订提示信息（例如：现在预订，8:30起送）
  string advanceorder_msg = 12;
  //店铺营业状态 1:正常营业 2:接受预定 3:店铺打烊
  int32 advanceorder_status = 13;
  //店铺头像
  string image = 14;
  //营业日期
  repeated int32 Businessdate = 15;
  //营业开始时间
  int32 Business_opentime = 16;
  //营业结束时间
  int32 Business_closetime = 17;
  //店铺电话
  string mobile = 18;
  //营业状态1:营业 2:闭店 3:打烊
  int32 business_status = 19;
  //预订单设置 0不可预订，1可预定
  int32 advanceorder = 20;
  //退货地址
  string return_address = 21;
  //根据条件计算的实际基础起送价,分
  int32 real_basefee = 22;
  //根据条件计算的实际基础配送价，分
  int32 real_deliveryfee = 23;
  //接受预定日期
  repeated int32 advancedates = 24;
  //是否同步美团配送范围 (true:同步 false:不同步)
  bool is_push_scope = 25;
  //是否支持自提(1:支持 0:不支持)
  bool is_self_lifting = 26;
  //备货时长 单位分钟 不能小于0
  int32 stock_up_time = 27;
  //店铺公告
  string notice = 28;
  //是否支持小程序自提(1:支持 0:不支持)
  bool is_self_lifting_app = 29;
  //小程序banner
  repeated MiniBanner mini_banner = 30;
  //app banner
  repeated AppBanner app_banner = 31;
  // 活动状态 0:正常店铺 1:配送站式拼团 2:团长拼团
  int32 activity_state = 32;
}
message CitysGetRequest {
  //财务编码
  string finance_code = 1;
  //渠道id
  int32 channel_id = 2;
  //城市名称，可模糊搜索
  string name = 3;
}
message CitysGetResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  //城市列表
  repeated City data = 4;
}
message City {
  //Id
  string id = 1;
  //名称
  string name = 2;
}
message HospitalInfoGetRequest {
  //财务编码
  string finance_code = 1;
  //渠道id
  int32 channel_id = 2;
}
message HospitalInfoGetResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  //医院详情实体
  HospitalInfo data = 4;
}
message HospitalInfo {
  //医院名称
  string name = 1;
  //服务项目，逗号分隔
  string service_items = 2;
  //营业时间段
  string business_times = 3;
  //是否提供停车服务
  bool p = 4;
  //地址
  string address = 5;
  //电话
  string mobile = 6;
}
message ShopOtherUpdateRequest {
  //财务编码
  string finance_Code = 1;
  //渠道id
  int32 channel_Id = 2;
  //是否支持开具发票
  bool invoice = 3;
  //是否自动打印
  bool auto_print = 4;
}
message ShopOtherUpdateResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

message ShopBannerUpdateRequest {
  //渠道id
  int32 channel_id = 1;
  //财务编码
  string finance_code = 2;
  //小程序banner
  repeated MiniBanner mini_banner = 3;
  //APP banner
  repeated AppBanner app_banner = 4;
}

message ShopBannerUpdateResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
}

message ShopCateAndImageUpdateRequest {
  //财务编码
  string finance_Code = 1;
  //渠道id
  int32 channel_Id = 2;
  //种类1
  string category1 = 3;
  //种类2
  string category2 = 4;
  //店铺头像
  string image = 5;
}
message ShopCateAndImageUpdateResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}
message ShopBasicSetupUpdateRequest {
  //财务编码
  string finance_Code = 1;
  //渠道id
  int32 channel_Id = 2;
  //店铺公告
  string notice = 3;
  //店铺电话
  string mobile = 4;
  //二维码
  string qrcode = 5;
  //微信小程序的Accesstoken
  string access_token = 6;
}


message ShopBasicSetupUpdateResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}


message ShopSelfLiftingUpdateRequest {
  //财务编码
  string finance_code = 1;
  //渠道id
  int32 channel_id = 2;
  //是否支持自提(true:支持 false:不支持)
  bool is_self_lifting = 3;
  //备货时长 单位分钟 不能小于0
  int32 stock_up_time = 4;
  //阿闻小程序是否支持自提(true:支持 false:不支持)
  bool is_self_lifting_app = 5;
}


message ShopSelfLiftingUpdateResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}


message InitializationDeliveryareaRequest {
}


message InitializationDeliveryareaResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}


message ShopBusinessSetupGetRequest {
  //财务编码
  string finance_Code = 1;
  //渠道id
  int32 channel_Id = 2;
}
message ShopBusinessSetupGetResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  //店铺基本业务设置实体
  ShopBusinessSetupInfo data = 4;
}

message MiniBanner {
  //图片地址
  string image_url = 1;
  //路径
  string url = 2;
}
message AppBanner {
  // 类型 0微页面 1小程序
  int32 type = 1;
  //图片地址
  string image_url = 2;
  //路径
  string url = 3;
}

message ShopBusinessSetupInfo {
  //id
  int32 id = 1;
  //财务编码
  string finance_Code = 2;
  //渠道id
  int32 channel_Id = 3;
  //营业日期（星期一**星期日分别为1**7）
  string business_date = 4;
  //营业时间段（json数组，例[["8:00","12:00"],["14:00","15:00"]]）
  string business_times = 5;
  //预订单设置（true:休息时间支持预订，false:休息时间不支持预订）
  bool advance_order = 6;
  //接受预定日期（json数组，例[0,1]）
  string advance_dates = 7;
  //预订单提醒：送到时间前xx分钟提醒备货
  int32 advance_remindtime = 8;
  //营业状态1：营业 2：闭店
  int32 business_status = 9;
  //店铺公告
  string notice = 10;
  //店铺电话
  string mobile = 11;
  //二维码
  string qrcode = 12;
  //经营种类1
  string category1 = 13;
  //经营种类2
  string category2 = 14;
  //店铺头像
  string image = 15;
  //业务系统
  string businesssystem = 16;
  //业务系统ID
  string businesssystemid = 17;
  //是否支持开具发票（true:支持;false:不支持）
  bool invoice = 18;
  //是否自动打印（true:是;false:否)
  bool auto_print = 19;
  //退货地址
  string return_address = 20;
  //是否支持自提(true:支持 false:不支持)
  bool is_self_lifting = 21;
  //备货时长 单位分钟 不能小于0
  int32 stock_up_time = 22;
  //店铺小程序码
  string miniappcode = 23;
  // 小程序是否支持自提
  bool is_self_lifting_app = 24;
  //小程序banner
  repeated MiniBanner mini_banner = 25;
  //APP banner
  repeated AppBanner app_banner = 26;
}
message ShopBusinessSetupUpdateRequest {
  //id(记录ID,新增传0)
  int32 id = 1;
  //财务编码
  string finance_Code = 2;
  //渠道id
  int32 channel_Id = 3;
  //营业日期（星期一**星期日分别为1**7）
  string business_date = 4;
  //营业时间段（json数组，例[["8:00","12:00"],["14:00","15:00"]]）
  string business_times = 5;
  //预订单设置（true:休息时间支持预订，false:休息时间不支持预订）
  bool advance_order = 6;
  //接受预定日期（json数组，例[0,1]）
  string advance_dates = 7;
  //预订单提醒：送到时间前xx分钟提醒备货
  int32 advance_remindtime = 8;
  //营业状态 1:营业 2:闭店
  int32 business_status = 9;
}
message ShopBusinessSetupUpdateResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}
message GetStoreOrgIdReq {
  //财务编码
  string finance_Code = 1;
}
message GetStoreOrgIdResp {
  // 主体id
  int32 org_id = 1;
}
message ShopStoreGetRequest {
  //财务编码
  string finance_Code = 1;
  //子龙门店id
  string zilong_id = 2;
  //渠道id
  int32 channel_Id = 3;
  //用户编号
  string user_No = 4;
}
message ShopStoreInfo {
  //店铺ID
  string shop_id = 1;
  //店铺编码
  string shop_code = 2;
  //店铺名称
  string shop_name = 3;
  //店铺简称
  string shop_shortname = 5;
  //财务编码
  string finance_Code = 6;
  //子龙门店id
  string zilong_id = 7;
  //渠道id
  int32 channel_Id = 8;
  //渠道门店id（美团ID）
  string channel_store_id = 9;
  //A8客户编码
  string customer_code = 10;
  //全渠道往来单位
  string custom_code = 11;
  //门店地址
  string shop_address = 12;
  //店铺简介
  string shop_desc = 13;
  //店铺定位信息经度（X）
  string point_x = 14;
  //店铺定位信息纬度（y）
  string point_y = 15;
  //饿了么店铺ID
  string ele_id = 16;
  //饿了么店铺配送方式 9：蜂鸟快送，11：星火众包
  int32 elm_delivery = 17;
  //京东到家门店ID
  string jddj_id = 18;
  //所在市
  string city = 19;
  //所在大区
  string big_region = 20;
  //店铺类型 1新瑞鹏  2TP代运营
  int32 app_channel = 21;
  //店铺仓库
  repeated Warehouse warehouse_list = 22;
  //美团取货信息展示
  string mt_label = 23;
  //饿了么取货信息展示
  string elm_label = 24;
  //京东到家取货信息展示
  string jd_label = 25;
  // 阿闻店铺取货信息展示
  string zl_label = 26;
  // 医疗互联网
  string yl_label = 27;

  // 竖屏自提
  string sp_label = 28;
  // 配送方式设置
  int32 delivery_method = 29;
  // 是否有资质售药
  int32 sell_drugs = 30;
  //药品上架渠道,多个用英文逗号隔开
  string drugs_channel_ids = 31;
  // 主体ID
  int32 org_id = 32;
}

message Warehouse {
  //仓库编号
  string code = 1;
  //仓库名称
  string name = 2;
  //仓库地址
  string address = 3;
  // 仓库类型 如：3
  int32 category = 4;
  // 仓库类型描述 如 门店仓
  string category_text = 5;
}

message ShopStoreGetResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  //店铺基本信息实体
  ShopStoreInfo data = 4;
}

message ShopStoreUpdateRequest {
  //财务编码
  string finance_Code = 1;
  //子龙门店id
  string zilong_id = 2;
  //渠道id
  int32 channel_Id = 3;
  //渠道门店id(美团)
  string channel_store_id = 4;
  //全渠道往来单位(A8)
  string custom_code = 5;
  //门店地址
  string shop_address = 6;
  //店铺简介
  string shop_desc = 7;
  //店铺定位信息经度（X）
  string point_x = 8;
  //店铺定位信息纬度（y）
  string point_y = 9;
  //饿了么门店ID
  string ele_id = 10;
  //饿了么门店配送方式 9：蜂鸟快送，11：星火众包
  int32 elm_delivery = 11;
  //京东到家门店ID
  string jddj_id = 12;
  //城市
  string city = 13;
  //大区
  string big_region = 14;
  //店铺类型
  int32 app_channel = 15;
  // 是否有资质售药
  int32 sell_drugs = 16;
  //药品上架渠道,多个用英文逗号隔开
  string drugs_channel_ids = 17;
}

message ShopDeliveryServiceSetRequest {
  //财务编码
  string finance_Code = 1;
  //子龙门店id
  string zilong_id = 2;
  //渠道id
  int32 channel_Id = 3;
  //收费模式：0按距离收取配送费
  int32 charge_type = 4;
  //配送区域,经纬度坐标点
  string delivery_area = 5;
  //基础起送价,分
  int32 basefee = 6;
  //基础配送价，分
  int32 deliveryfee = 7;
  //动态配送费用,距离加价
  string distanceprice = 8;
  //动态配送费用,重量加价
  string weightprice = 9;
  //特殊时段配送费用
  string specialtimefee = 10;
  //是否同步美团配送范围 (true:同步 false:不同步)
  bool is_push_scope = 11;
  //id，新增传0
  int32 id = 13;
}

message ShopDeliveryServiceSetResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

message StoreUserAuthorityResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated StoreUserAuthority details = 4;
  repeated string user_no = 5;
}

message GetFinanceCodeByStoresChannelIdRequest {
  //门店名称
  string name = 1;
  //渠道名称
  int32 channel_id = 2;
}

message StoreInfoResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated StoreInfo details = 4;
}
message StoreInfoRequest {
  //门店名称
  string name = 1;
  //渠道名称
  int32 channel_id = 2;
  //门店财务编码
  repeated string finance_code = 3;
  //第三方门店id
  repeated string store_code = 4;
}

message StoreUserAuthority {
  //用户编号
  string user_no = 1;
  //财务编码
  string finance_code = 2;
  //财务编码数量
  repeated string finance_codes = 3;
}

message BaseResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated string details = 4;
}

message StoreUserAuthorityRequest {
  repeated StoreUserAuthority list = 1;
  repeated string finance_code = 2;
  repeated string user_no = 3;
}

message PlatformChannelResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated PlatformChannel data = 4;
}
message PlatformChannel {
  //主键id
  int32 id = 1;
  //渠道名称
  string name = 2;
  //图标
  string icon = 3;
  //是否使用
  int32 is_use = 4;
}

message ChannelRequest {
  //渠道id
  int32 channel_id = 1;
}

message StoreidsResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //门店id
  repeated string data = 4;
}

message StoreRelationUserRequest {
  //页码
  int32 p = 1;
  //每页数量
  int32 psize = 2;
  //财务编码
  repeated string finance_code = 3;
  //渠道id
  int32 channel_id = 4;
  //用户编号
  repeated string user_no = 5;
  //渠道门店id
  repeated string channel_store_id = 6;
}

message StoreRelationUserResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated StoreRelationUser data = 4;
}

message StoreRelationUser {
  //财务编码
  string finance_code = 1;
  //渠道id
  int32 channel_id = 2;
  //渠道门店id
  string channel_store_id = 3;
  //往来单位
  string custom_code = 4;
  //是否创建了门店
  int32 is_create = 5;
  //用户编号
  string user_no = 6;
}

message StoreRequest {
  //页码
  int32 p = 1;
  //每页数量
  int32 psize = 2;
  //门店名称，支持模糊查询
  string name = 3;
  //渠道id
  int32 channel_id = 4;
  //城市名称，支持模糊查询
  string city = 5;
  // 主体id
  string org_id = 6;
}

message StoreResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Store data = 4;
}

message Store {
  //主键id
  int32 id = 1;
  //门店名称
  string name = 2;
  //财务编码
  string finance_code = 3;
  //子龙门店id
  string zilong_id = 4;
  //渠道id
  int32 channel_id = 5;
  //渠道门店id
  string channel_store_id = 6;
  //省份
  string province = 7;
  //城市
  string city = 8;
  //经度
  string longitude = 9;
  //纬度
  string latitude = 10;
  //店铺地址
  string address = 11;
  string shortname = 12;
}

//请求门店信息分页
message CreateShopStatusRequest {
  //门店ID
  string shopId = 1;
  //渠道ID
  string channelId = 2;
  //状态码 10-审核驳回、20-审核通过、30-创建成功、40-上线可发单
  int32 status = 3;
}

//请求门店信息分页
message StoreListRequest {
  //每页大小
  int32 page_size = 1;
  //当前页
  int32 page = 2;
  //查询北京接口的token
  string token = 3;
  //门店名称（模糊查询）
  string shop_name = 4;
  //渠道ID，1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提
  int32 channel_id = 5;
  //用户编码
  string user_code = 6;
  //城市（模糊查询）
  string city_name = 7;
  //仓库类型 3-门店仓，4-前置仓，5-前置仓虚拟仓
  int32 channel_type = 8;
  //是否有资质售药（-1全部， 0没有售药资质， 1有售药资质）
  int32 sell_drugs = 9;

}

//请求门信息返回结果
message StoreListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  int32 total_count = 4;
  //远程门店信息和本地门店信息组合起来的结果
  repeated StoreInfo DataList = 5;
}

//门店信息
message StoreInfo {
  //财务编码
  string finance_code = 1;
  //子龙门店id
  string zilong_id = 2;
  //渠道id
  int32 channel_id = 3;
  //渠道门店id
  string channel_store_id = 4;
  //全渠道往来单位
  string custom_code = 5;
  //门店名称
  string shop_name = 6;
  int32 is_create = 7;
  //门店联系人姓名
  string contact_name = 8;
  //联系电话
  string contact_phone = 9;
  //门店地址
  string shop_address = 10;
  //门店名称
  string name = 11;
  //所属大区
  string bigregion = 12;
  //省
  string province = 13;
  //市
  string city = 14;
  //门店ID
  int32 id = 15;
  //经度
  string pointx = 16;
  //维度
  string pointy = 17;

  string shortname = 18;
  //门店地址
  string address = 19;
  //  app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 20;
  // 配送方式
  int32 delivery_method = 21;

  // 美团的仓库
  string meituan_warehouse = 22;
  // 饿了么的仓库
  string elm_warehouse = 23;
  // 阿闻外卖的仓库
  string awen_warehouse = 24;
  //京东的仓库
  string jd_warehouse = 25;
  //医疗互联网的仓库
  string md_warehouse = 26;
  // 阿闻自提
  string awen_zt_warehouse = 27;
  // 是否有资质售药
  int32 sell_drugs = 28;
  //药品上架渠道,多个用英文逗号隔开
  string drugs_channel_ids = 29;

}

message StoreToShopRequest {
  //财务编码
  string finance_code = 1;
  //子龙门店id
  string zilong_id = 2;
  //渠道id
  int32 channel_id = 3;
  //渠道门店id
  string channel_store_id = 4;
  //全渠道往来单位
  string custom_code = 5;
  //门店联系人姓名
  string contact_name = 6;
  //联系电话
  string contact_phone = 7;
  //门店地址
  string shop_address = 8;
  //标识修改了哪个值 1-美团，2-A8
  int32 edit_type = 9;
  // 店铺主体Id
  int32 store_master_id = 10;
}

message StoreToShopResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

message StoreRelationRequest {
  //财务编码ID集合，用逗号分隔
  string finance_code = 1;
  //子龙门店idID集合，用逗号分隔
  string zilong_id = 2;
  //渠道id
  int32 channel_id = 3;
}

message StoreRelationResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  repeated StoreRelation list = 4;

}
//对于关系，财务ID对渠道门店ID（美团或者饿了么等），全渠道往来单位
message StoreRelation {
  //财务编码
  string finance_code = 1;
  //渠道id
  int32 channel_id = 2;
  //渠道门店id
  string channel_store_id = 3;
  //往来单位
  string custom_code = 4;
  //是否创建了门店
  int32 is_create = 5;
}


message ShopSetAddRequest {
  //店铺ID
  string shop_id = 1;
  //接单声音是否开启
  int32 is_getorder = 2;
  //提示音频率，1播放3次，2循环播放
  int32 getordertype = 3;
  //催单声音是否开启
  int32 is_reminder = 4;
  //提示音频率，1播放3次，2循环播放
  int32 remindertype = 5;
  //退单声音是否开启
  int32 is_backorder = 6;
  //提示音频率，1播放3次，2循环播放
  int32 backordertype = 7;
  //自动接单声音是否开启
  int32 is_aotu = 8;
  //提示音频率，1播放3次，2循环播放
  int32 aotutype = 9;
  //订单到达是否开启
  int32 is_ordercome = 10;
  //提示音频率，1播放3次，2循环播放
  int32 ordercometype = 11;
  //配送异常声音是否开启
  int32 is_exception = 12;
  //提示音频率，1播放3次，2循环播放
  int32 exceptiontype = 13;
  //催音量，最大100%
  int32 volume = 14;
  //是否自动接单1：是，0：否
  int32 is_aotu_order = 15;
  //打印长度mm
  int32 print_extent = 16;
}

message ShopSetAddResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

//门店设置模块
message ShopSetGetRequest {
  //店铺ID（财务编码）
  string shop_id = 1;
}

message ShopSetGetResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  ShopSetAddRequest retInfo = 4;
}

//请求门店信息系统设置LIST
message ShopSetListRequest {
  //每页大小
  int32 pageSize = 1;
  //当前页
  int32 page = 2;
  //门店Id,（财务编码）,查询设置列表不用填
  string shopId = 3;
  //店铺名称模糊查询
  string shop_name = 4;
  //城市名称，可模糊搜索
  string city = 5;
}
//返回门店信息系统设置List
message ShopSetListResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  int32 total_count = 4;
  repeated ShopSetList list = 5;
}

//返回门店信息系统设置List
message ShopSetList {
  //门店名称
  string shopName = 1;
  //财务编码
  string financeCode = 2;
  //是否自动接单0：否 1是
  int32 is_aotu_order = 3;
}


message StoreListAddRequest {
  repeated Store data = 1;
}

//返回门店信息系统设置List
message StoreListAddResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
}

//消息创建---请求参数
message MessageCreateRequest {
  //门店id--财务编码
  string shop_id = 1;
  //用户信息
  string member_main = 2;
  //消息内容
  string content = 3;
  //消息类别 1 接单通知 2 拣货订单
  int32 message_type = 4;
  //订单id
  string order_id = 5;
  //渠道id 1:阿闻 2：美团 3：饿了么
  int32 channel_id = 6;
}

//消息创建---响应参数
message MessageCreateResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

//消息创建修改读取状态--请求参数
message MessageUpdateRequest {
  //消息id
  string message_id = 1;
  //订单id
  string order_id = 2;
  //渠道id 1:阿闻 2：美团 3：饿了么
  int32 channel_id = 3;
  //消息关联id
  string message_relationship_id = 4;
}

//消息创建修改读取状态--响应参数
message MessageUpdateResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

//消息列表查询--请求参数
message MessageListRequest {
  //门店id--财务编码
  string shop_id = 1;
  //消息类别 1 接单通知 2 拣货订单
  int32 message_type = 2;
  //消息内容
  string content = 3;
  //开始时间
  string start_time = 4;
  //结束时间
  string end_time = 5;
  //消息id
  string message_id = 6;
  //是否已读1 未读 2 已读
  int32 is_read = 7;
  //接收消息主体（有用户和系统等）
  string member_main = 8;
  //渠道id 1:阿闻 2：美团 3：饿了么
  int32 channel_id = 9;
  // 页码
  int64 page_index = 10;
  // 每页条数
  int64 page_size = 11;
}

//消息列表查询--响应参数
message MessageListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated MessageInfo MessageInfoList = 4;
  int64 total = 5;
}

//消息列表查询--响应参数
message MessageInfo {
  //消息id
  string message_id = 1;
  //门店id--财务编码
  string shop_id = 2;
  //消息内容
  string content = 3;
  //消息类别 1 接单通知 2 拣货订单
  int32 message_type = 4;
  //创建时间
  string create_time = 5;
  //接收消息主体（有用户和系统等）
  string member_main = 6;
  //是否已读1 未读 2 已读
  int32 is_read = 7;
  // 消息关联id
  string message_relationship_id = 8;
}

//消息发送--请求参数
message MessageSendRequest {
  string objectId = 1;
  string msg = 2;
  int32 isUser = 3;
}

//消息发送--响应参数
message MessageSendResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

//通过门店Id发送消息请求
message MessageSendWithShopIdRequest {
  // 是否发送给只拥有该门店的用户
  bool isOnlyAuthorityUser = 2;
  // 门店Id
  string shopId = 3;
  //需要发送的消息内容
  string msg = 4;
}
//通过门店Id发送消息响应
message MessageSendWithShopIdResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  // 是否发送成功
  bool isSuccess = 4;
}

message SaaSPolygonRequest {
  //经纬度
  string  Lat  = 1;
  //经纬度
  string   Lng  = 2;
  // 门店财务编码
  string financial_code = 5;
}
message SaaSPolygonResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  // 是否在配送范围
  bool isIn = 4;
}

message EsSearchRequest {
  //经纬度
  string lon_lat = 1;
  //查询类型（geo_point 根据经纬度查询附近门店，geo_shape 根据经纬度查询是否在门店配送范围内）
  string type = 2;
  int32 page_index = 3;
  int32 page_size = 4;
  // 门店财务编码
  string financial_code = 5;
}
message EsSearchResponse {
  int32 took = 1;
  bool timed_out = 2;
  EsSearchShards _shards = 3;
  EsSearchHits hits = 4;
  int32 code = 5;
  string message = 6;
  string error = 7;
}
message EsSearchShards {
  int32 total = 1;
  int32 successful = 2;
  int32 skipped = 3;
  int32 failed = 4;
}

message EsSearchHits2 {
  string _index = 1;
  string _type = 2;
  string _id = 3;
  repeated double sort = 4;
  EsSearchStoreSource _source = 5;
}
message EsSearchStoreSource {
  //门店名称
  string name = 1;
  string location_point = 2;
  string financial_code = 3;

}

message EsSearchHits {
  EsSearchTotal total = 1;
  repeated EsSearchHits2 hits = 2;
}

message EsSearchTotal {
  int32 value = 1;
  string relation = 2;

}

//获取用户权限内的门店医院列表--请求参数
message GetHospitalListByUserNoRequest {
  //用户编号
  string user_no = 1;
  //渠道id
  int32 channel_id = 2;
  //总账号标识1--是，0--否
  int32 is_logo = 3;
  //仓库类型
  int32 category = 4;
  // 当 值为pickup时，需要过滤已经参加活动的门店
  string from = 5;
}

//获取用户权限内的门店医院列表--响应参数
message GetHospitalListByUserNoResponse {
  //响应状态码
  int32 code = 1;
  //响应状态码
  int32 status = 2;
  //返回信息
  string message = 3;
  //返回错误
  string error = 4;
  //返回数据
  repeated Hospital data = 5;
  //接口返回的原始数据
  bytes original_data = 6;
}

message Hospital {
  //系统id
  int32 id = 1;
  //对应的组织机构ID
  string clinic_id = 2;
  //对应的父级组织机构id
  int32 parent_id = 3;
  //医院名称
  string clinic_name = 4;
  //医院简称
  string clinic_shortname = 5;
  //品牌名称
  string brand = 6;
  //品牌名称id
  int32 brand_id = 7;
  //子品牌
  string sub_brand = 8;
  //品牌编码（财务编码）
  string brand_code = 9;
  //医院状态基础信息_医院状态：0未知，1营业，2暂停，3闭店，4交接，5筹备，99删除
  int32 clinic_status = 10;
  //医院类型
  string clinic_type = 11;
  //创院时间
  string create_time = 12;
  //地址：省份
  string province = 13;
  //地址：城市
  string city = 14;
  //地址：地址
  string address = 15;
  //院长
  string dean = 16;
  //院长电话
  string dean_number = 17;
  //第二联系人|运营经理
  string inner_contactor = 18;
  //第二联系人|运营经理电话
  string inner_contactor_phone = 19;
  //前台电话
  string hospital_phone = 20;
  //是否有医疗业务：1是
  int32 has_medcine = 21;
  //是否有美容业务：1是
  int32 has_meirong = 22;
  //是否有零售业务：1是
  int32 hasLingshou = 23;
  //关联ID_HIS系统    单选：小暖/林特/迅德/汉思/其他
  string system = 24;
  //小暖系统内部ID
  int32 system_id = 25;
  //营业执照名称
  string business_licenseName = 26;
  //经度
  string longitude = 27;
  //纬度
  string latitude = 28;
  //美团id
  string meituan_id = 29;
  //组织机构编码（财务编码）
  string struct_outer_code = 30;
  //添加时间
  string add_time = 31;
  //最后修改时间
  string last_modify = 32;
}

message GetShopWxAppCodeRequest {
  string finance_code = 1; //财务编码
  string access_token = 2; //微信小程序accesstoken
  //主页路径
  string page_path = 3;
}

message GetComponentAccessTokenReq {
  string component_appid =1; //第三方平台appid
  string  component_appsecret = 2; //第三方平台secret
  string  component_verify_ticket = 3;//第三方平台ticket
}
message GetComponentAccessTokenRes {
  string component_access_token = 1; //第三方平台token
  int64 expires_in = 2; // token有效期，一般为7200秒
}
message GetShopWxAppCodeResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  string qiniuUrl = 4;
}

message GetAuthorizerAccessTokenReq{
  string finance_code = 1;
  string component_appid =2;
  string authorizer_appid = 3;
  string authorizer_refresh_token=4;
  string ChainId = 5;

  
}
message GetAuthorizerAccessTokenRes{
  string authorizer_access_token = 1;
  int64 expires_in = 2;
  string authorizer_refresh_token = 3;
}

message AdvertisementAddRequest{
  int32 id = 1;
  //广告名称
  string advertisement_name = 2;
  //投放渠道,0：竖屏
  int32 placement_channel = 3;
  //投放位置 0：开屏
  int32  placement_position = 4;
  //投放形式 0：轮播广告
  int32  show_type = 5;
  //权重
  int32  weight = 6;
  //生效时间
  string  start_time = 7;
  //结束时间
  string  end_time = 8;
  //状态 1未进行 2进行中 3已结束 4已终止
  int32 status = 9;
  //创建时间
  string create_time = 10;
  //广告图片、视频信息
  repeated AdvertisementMaterial materials = 11;
  //广告关联店铺
  repeated AdvertisementShop shops = 12;
}
message AdvertisementEditRequest{
  int32 id = 1;
  //广告名称
  string advertisement_name = 2;
  //投放渠道,0：竖屏
  int32 placement_channel = 3;
  //投放位置 0：开屏
  int32  placement_position = 4;
  //投放形式 0：轮播广告
  int32  show_type = 5;
  //权重
  int32  weight = 6;
  //生效时间
  string  start_time = 7;
  //结束时间
  string  end_time = 8;
  //状态 1未进行 2进行中 3已结束 4已终止
  int32 status = 9;
  //创建时间
  string create_time = 10;
  //广告图片、视频信息
  repeated AdvertisementMaterial materials = 11;
  //广告关联店铺
  repeated AdvertisementShop shops = 12;
  bool disable_button=13;
}

message AdvertisementInfo{
  int32 id = 1;
  //广告名称
  string advertisement_name = 2;
  //投放渠道,0：竖屏
  int32 placement_channel = 3;
  //投放位置 0：开屏
  int32  placement_position = 4;
  //投放形式 0：轮播广告
  int32  show_type = 5;
  //权重
  int32  weight = 6;
  //生效时间
  string  start_time = 7;
  //结束时间
  string  end_time = 8;
  //状态 1未进行 2进行中 3已结束 4已终止
  int32 status = 9;
  //创建时间
  string create_time = 10;
  bool disable_button=11;
}
message AdvertisementDetails{
  int32 id = 1;
  //广告名称
  string advertisement_name = 2;
  //投放渠道,0：竖屏
  int32 placement_channel = 3;
  //投放位置 0：开屏
  int32  placement_position = 4;
  //投放形式 0：轮播广告
  int32  show_type = 5;
  //权重
  int32  weight = 6;
  //生效时间
  string  start_time = 7;
  //结束时间
  string  end_time = 8;
  //状态 1未进行 2进行中 3已结束 4已终止
  int32 status = 9;
  //创建时间
  string create_time = 10;
  //广告图片、视频信息
  repeated AdvertisementMaterial materials = 11;
  //广告关联店铺
  repeated AdvertisementShop shops = 12;
  bool disable_button=13;
}
message AdvertisementMaterial{
  int32 id = 1;
  //广告主Id
  int32 advertisement_id = 2;
  //素材类型 0：图片 1：视频
  int32 material_type = 3;
  //广告名称
  string material = 4;
  //素材封面
  string material_cover = 5;
  //广告链接
  string skip_link = 6;
  //链接类型 0：门店页 1：预约挂号 2：预约洗美 3：预约
  int32 link_type = 7;
  //禁用退出广告按钮
  bool disable_button=8;
}
message AdvertisementShop{
  int32 id = 1;
  //广告主Id
  int32 advertisement_id = 2;
  //财务编码
  string shop_id = 3;
  //门店名称
  string shop_name = 4;
}

message AdvertisementGetRequest{
  int32 advertisement_id = 1;
}
message AdvertisementGetResponse{
  int32 code = 1;
  string message = 2;
  AdvertisementDetails data = 3;
}

message AdvertisementListRequest{
  //每页大小
  int32 pageSize = 1;
  //当前页
  int32 page = 2;
  //广告名称
  string advertisement_name = 3;
  //状态 1未进行 2进行中 3已结束 4已终止
  int32 status = 4;
}
message AdvertisementListResponse{
  int32 code = 1;
  string message = 2;
  int32 total_count = 3;
  //广告主信息
  repeated AdvertisementInfo data = 4;
}

message AdvertisementLaunchGetRequest{
  //投放渠道,0：竖屏
  int32 placement_channel = 1;
  //投放位置 0：开屏
  int32 placement_position = 2;
  //财务编码
  string shop_id = 3;
}
message AdvertisementLaunchGetResponse{
  int32 code = 1;
  string message = 2;
  AdvertisementLaunch data = 3;

}
message AdvertisementLaunch{
  //广告图片、视频信息
  repeated AdvertisementMaterial materials = 1;
  bool disable_button=2;
}
message ShopTreeRequest{
  string user_no = 1;
}
message ShopTreeResponse{
  int32 code = 1;
  string message = 2;
  repeated ShopTree data = 3;
}
message ShopTree{
  int32 shop_id = 1;
  string finance_code = 2;
  int32 shop_type = 3;
  int32 parent_id = 4;
  string shop_name = 5;
  // 层级
  int32 level = 7;
  repeated ShopTree children = 6;
}
// 词库新增或编辑
message NewAnalyzeTokenRequest {
  // 分词id(编辑时传入)
  int32 id = 1;
  // 分词名
  string token = 2;
  // 类型
  string type = 3;
  // 分词图片
  string logo = 4;
}

// 分词查询请求参数
message GetAnalyzeTokenRequest {
  // 分词关键字
  string keyword = 1;
  int32 page_index = 2;
  int32 page_size = 3;
  // 更新时间
  string update_date = 4;
}

// 分词查询响应参数
message GetAnalyzeTokenResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  // 分词列表
  repeated AnalyzeToken data = 4;
}

// 分词列表
message AnalyzeToken {
  // 分词id
  int32 id = 1;
  // 分词名
  string token = 2;
  // 分词图片
  string logo = 3;
  // 分词类型
  string type = 4;
  // 创建时间
  string create_date = 5;
  // 更新时间
  string update_date = 6;
  // 删除时间
  string delete_date = 7;
}

// 分词删除
message DelAnalyzeTokenRequest {
  int32 id = 1;
}

message GetStoreInfoSyncEsRequest{
  //财务编码
  string finance_code = 1;
}

message StoreInfoSyncEs{
  //财务编码
  string finance_code = 1;
  //定位信息经度
  string pointX = 2;
  //定位信息纬度
  string pointY = 3;
  //配送区域,经纬度坐标点
  string deliveryarea = 4;
  //店铺电话
  string mobile = 5;
  //店铺名称
  string Name = 6;
}

message GetStoreInfoSyncEsResponse{
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated StoreInfoSyncEs data = 4;
}

message GetScrmHospitalInfoSyncEsRequest{
  //财务编码
  string finance_code = 1;
}

message ScrmHospitalInfoSyncEs{
  //医院编码
  string epp_code = 1;
  //医院名称
  string name = 2;
  //具体位置
  string hospital_address = 3;
  //医院封面图
  string hospital_avatar = 4;
  //1中心医院，2专科医院，3社区医院，4全科医院，5小型全科医院，6中型全科医院，7宠物店
  int32 hospital_type = 5;
  //状态 0正常 -1删除 1闭店 2暂停营业
  int32 hospital_status = 6;
  //所在城市
  string hospital_city = 7;
  //医院code
  string hospital_code = 8;
  //网络医院（虚拟）医院
  string hospital_internet_id = 9;
  //标签名称
  string tags = 10;
  //1=瑞鹏，2=其它宠物店
  int32 companySource = 11;
}

message GetScrmHospitalInfoSyncEsResponse{
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated ScrmHospitalInfoSyncEs data = 4;
}

message BjAcpRequest {
  string user_mobile = 1;
}
//查询北京ACP返回的员工信息
message BjAcpResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  BjAcpInfo user_info = 4;// 用户code

}
//查询北京ACP返回的员工信息
message BjAcpInfo {
  string user_no = 1;// 用户code
  string user_name = 2; // 用户名
  string staff_name_cn = 3;// 用户中文名
  string user_mobile = 4;// 用户手机号
  string last_date = 5; // 最后编辑时间
  string user_auth_str = 6;// 有权限的字符串集合用,分隔来源渠道id（datacenter.platform_channel表）1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店
}

//请求北京ACP员工信息LIST
message  BjAcpListRequest {
  //每页大小
  int32 pageSize = 1;
  //当前页
  int32 page = 2;
  //用户真实名称（模糊搜索）
  string staff_name_cn = 3;
  //用户手机号
  string user_mobile = 4;
  //用户编号
  string user_no = 5;
}

//请求北京ACP员工信息LIST
message  BjAcpListResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  int32 total_count = 4;
  repeated BjAcpInfo user_info_list = 5;// 用户code
}

//提醒信息数据
message AddRemindRequest {
  //主键
  int32 id = 1;
  //标题
  string title = 2;
  //内容
  string content = 3;
  //附件路径
  string file_url = 4;
  //创建时间
  string create_date = 5;
  //文件名称
  string file_name = 6;
}

//提醒信息数据List分页
message RemindListRequest {
  //每页大小
  int32 pageSize = 1;
  //当前页
  int32 page = 2;
}

//提醒信息数据LIST
message RemindListResponse {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  //总页数
  int32 total_count = 4;
  //提醒list
  repeated AddRemindRequest remind_list = 5;
}

//根据用户NO获取未读取的消息提醒
message RemindUserNoRequest {
  string user_no = 1;
}

message GetHerStaffRequest{
  //手机号
  string mobile = 1;
  //姓名
  string staff_name = 2;
  //组织编号
  string department_code = 3;
  //岗位编号
  string job_code = 4;
  //在职状态
  int32 duty_status = 5;
  //每页大小
  int32 pageSize = 6;
  //当前页
  int32 page = 7;
}
message GetHerStaffResponse{
  //响应状态码
  int32 code = 1;
  //返回信息
  string message = 2;
  //返回错误
  string error = 3;
  int32 total_count = 4;
  //返回数据
  repeated HerStaff data = 5;
}
message HerStaff{
  //员工工号
  string staff_no = 1;
  //单位代码
  string unit_code = 2;
  //组织编号
  string department_code = 3;
  //岗位编号
  string job_code = 4;
  //门店编码
  string finance_code = 5;
  //姓名
  string staff_name = 6;
  //性别，0未知，1男，2女
  int32 sex = 7;
  //生日
  string birth = 8;
  //城市
  string city = 9;
  //手机号
  string mobile = 10;
  //身份证号
  string card_no = 11;
  //职位名称
  string job_name = 12;
  //职位路径
  string job_path = 13;
  //在职状态
  int32 duty_status = 14;
  //入职时间
  string hire_date = 15;
}
message GetHerStaffOrganizationRequest{
  //组织编号
  string organization_no = 1;
  //组织名称
  string organization_desc = 2;
  //父级编号
  string parent_no = 3;
  //有效 1，无效-1
  int32 valid = 4;
  //每页大小
  int32 pageSize = 5;
  //当前页
  int32 page = 6;
}
message GetHerStaffOrganizationResponse{
  //响应状态码
  int32 code = 1;
  //返回信息
  string message = 2;
  //返回错误
  string error = 3;
  int32 total_count = 4;
  //返回数据
  repeated HerStaffOrganization data = 5;
}
message HerStaffOrganization{
  //机构编号
  string organization_no = 1;
  //机构编码
  string codeset_id = 2;
  //机构名称
  string organization_desc = 3;
  //父级编号
  string parent_no = 4;
  //父级名称
  string parent_desc = 5;
  //
  string origin_code_item_id = 6;
  //层级
  int32 grade = 7;
  //财务编码
  string finance_code = 8;
  //组织路径
  string organization_path = 9;
  //企业微信
  string wxqy = 10;
  //企业微信id
  string wxqy_id = 11;
  //有效 1，无效-1
  int32 valid = 12;
  //建立时间
  string build_time = 13;
}
message GetHerStaffPostRequest{
  //岗位编号
  string post_no = 1;
  //父级编号
  string parent_no = 2;
  //组织编号
  string organization_no = 3;
  //岗位名称
  string post_desc = 4;
  //有效 1，无效-1
  int32 valid = 5;
  //每页大小
  int32 pageSize = 6;
  //当前页
  int32 page = 7;
}
message GetHerStaffPostResponse{
  //响应状态码
  int32 code = 1;
  //返回信息
  string message = 2;
  //返回错误
  string error = 3;
  int32 total_count = 4;
  //返回数据
  repeated HerStaffPost data = 5;
}
message HerStaffPost{
  //岗位编号
  string post_no = 1;
  //岗位编码
  string codeset_id = 2;
  //岗位名称
  string post_desc = 3;
  //父级编号
  string parent_no = 4;
  //父级名称
  string parent_desc = 5;
  //
  string origin_code_item_id = 6;
  //层级
  int32 grade = 7;
  //财务编码
  string post_path = 8;
  //岗位名字
  string post_name = 9;
  //企业微信
  string wxqy = 10;
  //企业微信id
  string wxqy_id = 11;
  //有效 1，无效-1
  int32 valid = 12;
  //建立时间
  string build_time = 13;
}

message GetStoreDeliveryListRequest{
  //财务编码
  string finance_code = 1;
  //店铺名称
  string store_name = 2;
  //是否启用 1：不启用 2：启用
  int32 status = 3;
  //每页大小
  int32 page_size = 4;
  //当前页
  int32 page_index = 5;
}
message GetStoreDeliveryListResponse{
  //响应状态码
  int32 code = 1;
  //返回信息
  string message = 2;
  //返回错误
  string error = 3;
  int32 total_count = 4;
  //返回数据
  repeated StoreDelivery data = 5;
}
message StoreDelivery{
  //id
  int64 id = 1;
  //财务编码
  string finance_code = 2;
  //店铺名称
  string store_name = 3;
  //联系人
  string contact_person = 4;
  //联系方式
  string contact_information = 5;
  //城市
  string city = 6;
  //店铺地址
  string store_address = 7;
  //经度
  string lng = 8;
  //纬度
  string lat = 9;
  //配送方 1：闪送
  int32 delivery_type = 10;
  //是否启用 0 否 1 是
  int32 status = 11;
  //第三方配送ID
  string third_store_id = 12;
  //类型
  int32 good_type = 13;
  //火星纬度
  string spark_lat = 14;
  //火星经度
  string spark_lng = 15;
}

message UpdateUserInfoReq  {
  string staff_no = 1;
  string avatar = 2;
  string thumb_avatar = 3;
  //企业微信userid
  string WxUserId = 4;
}

message AddAppVersionRequest{
  //1：安卓 2：IOS
  int32 type = 1;
  //版本号
  string version_number = 2;
  //是否提醒升级 0 否 1是
  int32 is_remind = 3;
  //是否强制升级 0 否 1是
  int32 is_forced_upgrade = 4;
  //APK地址
  string apk_url = 5;
  //版本描述
  string version_description = 6;
}

message GetAppVersionRequest{
  int32 type = 1;
}

message GetAppVersionResponse{
  int32 code = 1;
  string message = 2;
  AppVersion data = 3;
}

message AppVersion{
  int64 id = 1;
  //1：安卓 2：IOS
  int32 type = 2;
  //版本号
  string version_number = 3;
  //是否提醒升级 0 否 1是
  int32 is_remind = 4;
  //是否强制升级 0 否 1是
  int32 is_forced_upgrade = 5;
  //APK地址
  string apk_url = 6;
  //版本描述
  string version_description = 7;
  //创建时间
  string create_time = 8;
  //是否生效
  int32 valid = 9;
}

message LogOutMemberRequest{
  //手机号
  string mobile = 1;
  //原因
  string reason = 2;
  //1：注销 2：取消注销
  int32 type = 3;
}

message AddFileUploadRecordRequest{
  //文件地址
  string file_url = 1;
  //文件类型 1 APP微页面
  int32 type = 2;
  //创建人ID
  string create_user_id = 3;
  //创建人
  string create_user_name = 4;
}
message CommonResponse {
  RetCode code = 1;
  string msg = 2;
}


message SetAdGoodsDetailReq {
  AdGoodsDetail ad_goods_detail = 1;
}

message GetAdGoodsDetailReq {
  int32 id = 1;
  int32 type = 2;
}

message GetAdGoodsDetailRes {
  //响应状态码
  int32 code = 1;
  //返回信息
  string message = 2;
  //返回错误
  string error = 3;
  //商详广告体
  AdGoodsDetail ad_goods_detail = 4;
}

message AdGoodsDetail {
  int32 id = 1;
  //商城ID
  int32 store_id = 2;
  //图片
  string image = 3;
  //路径
  string path = 4;
  //状态（0=>关闭，1=>开启）
  int32 status = 5;
  //广告类型 0：默认是商详广告，1：百度勃林格
  int32 type = 6;
}
enum RetCode {
  UNKNOWN = 0; // The first enum value must be zero in proto3
  SUCCESS = 200;
  ERROR = 400;
  InvalidParams = 1001;
  StoreMasterExisted = 1007;
  StoreMasterApplied = 1008;
  MerchantIdExisted = 1009;
  AppIdExisted = 1010;
}
message GetFileUploadRecordsRequest{
  //id
  int32 id = 1;
  //创建人ID
  string create_user_id = 2;
  //1 只查有效，0所有
  int32 valid = 3;
  //文件类型 1 APP微页面
  int32 type = 4;
  //当前页
  int32 page_index = 5;
  //页大小
  int32 page_size = 6;
}
message GetFileUploadRecordsResponse{
  int32 code = 1;
  string message = 2;
  int32 total_count = 3;
  repeated FileUploadRecord data = 4;
}
message FileUploadRecord{
  //id
  int64 id = 1 ;
  //创建人ID
  string create_user_id = 2;
  //创建人
  string create_user_name = 3;
  //文件路径
  string file_url = 4;
  //状态0 无效 1 有效
  int32 valid = 5;
  //文件类型 1 APP微页面
  int32 type = 6;
  //创建时间
  string create_time = 7;
}
message UpdateFileUploadRecordStatusRequest{
  int32 id = 1;
  int32 valid = 2;
}
message GetFileUploadRecordNowRequest{
  int32 type = 1;
}
message GetFileUploadRecordNowResponse{
  int32 code = 1;
  string message = 2;
  int32 total_count = 3;
  FileUploadRecord data = 4;
}


message TemplateMessageVo{
  string pet_name = 1;  // 宠物名称
  string hospital_name = 2; // 门店名称
  string appointment_name = 3;  // 预约项目
  string appointment_time = 4;  // 预约时间
  string appointment_doctor_name = 5; // 预约医生姓名
  string registration_fee = 6;  // 挂号费
  string order_id = 7;   // 订单号
  string telephone = 8;  // 电话
  string remark = 9;  // 备注
  string token = 10;
  string form_id = 11; //
}

message EmptyParam {

}

message NavigationBarListRes {
  repeated NavigationBarList data = 1;
}

message NavigationBarList {
  //id
  int32 id = 1;
  //图标
  string icon_img = 2;
  //名称
  string name = 3;
  //跳转链接、 小程序跳转路径
  string mini_url = 4;
  //小程序ID
  string mini_id = 5;
  //0:H5,1:原生，2：内部小程序 3:外部小程序
  int32 type = 6;
  //H5链接; type = 0:传路径
  string web_url = 7;
  //type = 1:传的类型   0：在线问诊，1：附近名医 2:病例简历， 3：养宠贴士 4：爪知
  int32 native_type = 8;
  //是否登录 1是 0否
  int32 is_login = 9;
  //排序
  int32 sort = 10;
  //1微信小程序  2安卓  3IOS
  int32 client_type = 11;
  //上下架  1上架 0下架
  int32 is_up = 12;
}

message AddNavigationbarReq {
  //id 新增传0，修改传对应的ID
  int32 id = 1;
  //图标
  string icon_img = 2;
  //名称
  string name = 3;
  //跳转链接、 小程序跳转路径
  string mini_url = 4;
  //小程序ID
  string mini_id = 5;
  //0:H5,1:原生，2：内部小程序 3:外部小程序
  int32 type = 6;
  //H5链接; type = 0:传路径
  string web_url = 7;
  //type = 1:传的类型   0：在线问诊，1：附近名医 2:病例简历， 3：养宠贴士 4：爪知
  int32 native_type = 8;
  //是否登录 1是 0否
  int32 is_login = 9;
  //排序
  int32 sort = 10;
  //1微信小程序  2安卓  3IOS
  int32 client_type = 11;
  //上下架  1上架 0下架
  int32 is_up = 12;
}
message QueryRegionalDivisionRequest{
    //父级id
    int64 parent_id = 2;
    //关键字查询
    string keyword =3;
    //传0则只针对关键字查询一层，传1则可以查询多层关键字，用小写,分割，按省市区查询
    int32 keyword_multistage = 4;
    //活动类型 1，宠友服务群 2，抗疫补给站
    int32 type = 5;
}
message QueryRegionalDivisionResponse{
    repeated RegionalDivision data = 1;
}
message RegionalDivision{
    int64 id = 1;
    //父级id
    int64 parent_id = 2;
    //名称
    string name = 3;
    //层级 0省 1市 2 区 3街道（不是硬性区分，有些区域是只有省市街道的）
    int32 level = 4;
    //子级集合
    repeated RegionalDivision child = 5;
}

message ImportTemplateRequest{
  //模板类型 0未支持的类型 1搜索词库 2电商仓商品价格  3-商城会员折扣 4-到家会员折扣;
  int32 type = 1;
}
message ImportTemplateResponse{
  // 状态码，200正常，>=400出错
  int32 code =1;
  // 消息
  string message=2;
  //文件名称
  string file_name = 3;
  // 文件字节流
  bytes template = 4;
}

message ImportRequest{
  // 导入类型 1搜索词库 2电商仓商品价格  3-商城会员折扣 4-到家会员折扣;
  int32 type = 1;
  //用户编码，前端不用传
  string user_no = 2;
  bytes file = 3;
  //用户名称，前端不用传
  string user_name = 4;
  //会员商品要传生效状态 1-立即生效 2-定时任务
  int32 effective_type =5;
  //类型4要传生效时间
  string effective_time =6;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 7;
}
message ImportResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
}

message ImportHistoryRequest{
  // 导入类型 1搜索词库 2电商仓商品价格 3-商城会员折扣 4-到家会员折扣
  int32 type = 1;
  // 页码，不传默认为1
  int32 page_index = 2;
  // 每页数量，不传默认10
  int32 page_size = 3;
  // 前端不用传
  string user_no = 4;
  //前端不用传
  string user_name =5;
}
message ImportHistoryResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message List {
    // 导入id
    int32 id = 1;
    // 导入时间
    string created_at = 2;
    // 导入结果
    string result = 3;
    // 导入结果url
    string result_url = 4;
    //商品数量
    int32 num =5;
    //状态:1-执行中 2-已完成 3-执行失败
    int32  status =6;
    //操作人编码
    string user_no =7;
    //生效时间
    string effective_time =8;
    //员工名称
    string user_name = 9;
  }
  repeated List data = 3;
  // 总数
  int32 total = 4;
}

message ExportRequest{
  // 导出类型 1搜索词库 2电商仓商品价格
  int32 type = 1;
  //查询的参数 传json字符串
  string payload = 3;
  //前端不用传
  string user_no = 2;
  //是否同步导出 默认不是  0否 1是
  int32 is_sync = 4;
}
message ExportResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  //同步导出文件路径，异步导出时为空
  string url = 3;
}

message ExportHistoryRequest{
  // 导出类型 1搜索词库 2电商仓商品价格
  int32 type = 1;
  // 页码，不传默认为1
  int32 page_index = 2;
  // 每页数量，不传默认10
  int32 page_size = 3;
  // 前端不用传
  string user_no = 4;
  // 任务类型 1个人任务 2全部任务
  string search_type = 5;
}
message ExportHistoryResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message List {
    // 导出id
    int32 id = 1;
    // 导出时间
    string created_at = 2;
    // 导出结果 0处理中 1成功 2失败
    int32 status = 3;
    // 导出结果url
    string result_url = 4;
    //任务类型
    string type_text = 5;
    //任务详情
    string result_desc = 6;
    //操作人名称
    string staff_name = 7;
    //操作人ip
    string ip = 8;
  }
  repeated List data = 3;
  // 总数
  int32 total = 4;
}

message ShopActiveStateListRequest{
  //财务编码集合，以,分隔
  string finance_codes = 1;
}

message ShopActiveStateListResponse{
  int32 code = 1;
  string message = 2;
  message List {
    //财务编码
    string finance_code = 1;
    //状态 0:正常店铺 1:配送站式拼团 2:团长拼团
    int32 activity_state = 2;
    //状态2时返回 前台显示 0否 1是
    int32 is_show = 3;
    //状态2时返回 待收方式 0团长决定是否代收 1团长必须代收
    int32 take_type = 4;
    //状态1时返回 分享海报
    string share_image = 5;
  }
  //列表
  repeated List data_list = 3;
}

message selectType {
  // 医疗互联网字段  1需要 0 不需要
  int32 include_medical = 1;
}
message SendSmsRequest {
  // 手机号，多个,分割
  string phone_numbers = 1;
  // 短信签名名称
  string sign_name = 2;
  // 短信模板CODE
  string template_code = 3;
  // 短信模板变量对应的实际值，JSON格式。支持传入多个参数，示例：{"name":"张三","number":"15038****76"}
  string template_param = 4;
}

message SendSmsResponse {
  int64 code = 1;
  string message = 2;
  AliyunSmsResponse data = 3;
}

message AliyunSmsResponse {
  string code = 1;
  string message = 2;
  string biz_id = 3;
  string request_id = 4;
}

message SendCodeRequest {
  // 手机号
  string mobile = 1;
 // 主体id : 7-小闻养宠助手
  int32 org_id = 2;
}

message SendCodeResponse {
  int64 code = 1;
  string message = 2;
}

message ExternalCallbackForBatchOpenCloseShopRequest {
  bytes data = 1;
  // shop_status_task表id
  int64 task_id = 2;
}

message ThirdChannelOpenOrCloseShopRequest {
  // 导入类型,1-阿闻，2-美团，3-饿了么，4-京东
  int32 type = 1;
  // 文件地址
  string file = 2;
  // 客户端ip
  string client_ip = 4;
  //后台用户信息，前端不用传
  string user_no = 5;
  string user_name = 6;
  bytes file_bytes = 3;
}

message ThirdChannelOpenOrCloseShopResponse {
  // 200:成功，400:失败
  int32 code = 1;
  // 提示信息
  string message = 2;
  // 错误信息
  string error = 3;
}

message OpenOrCloseShopTaskRequest{
  // 页码
  int32 page_index = 1;
  // 每页条数
  int32 page_size = 2;
  // 类型，默认0-个人，1-全部
  int32 type = 3;
  // 登录用户，前端不用传
  string user_no = 4;
}
message OpenOrCloseShopTaskResponse {
  // 200:成功，400:失败
  int32 code = 1;
  // 提示信息
  string message = 2;
  // 错误信息
  string error = 3;
  message taskData {
    // 开始时间
    string start_time = 1;
    // 任务类型
    string type_name = 2;
    // 门店数量
    int32 shop_num = 3;
    // 任务状态，0-未开始，1-已完成，2-进行中，3-已取消
    int32 status = 4;
    // 任务详情
    string description = 5;
    // 操作人
    string operation = 6;
    // ip地址
    string ip = 7;
    // 任务id
    int64 task_id = 8;
    // 下载地址
    string url = 9;
  }
  repeated taskData data = 4;
  // 总条数
  int64 total_count = 5;
}

message AddMallChannelRequest{
  int64 sort = 1;
  string title = 2;
  string sub_title = 3;
}

message GetMallChannelListRequest{
  int64 page_index = 1;
  int64 page_size = 2;
}

message GetMallChannelListResponse{
  int64 code = 1;
  string message = 2;
  Data data = 3;
  message Data {
    repeated List list = 1;
    int64 total = 2;
  };
  message List {
    // 表id
    int64 id = 1;
    string title = 2;
    string sub_title = 3;
    // 序号
    int64 sort = 4;
  };
}

message EditMallChannelRequest{
  int64 channel_id = 1;
  int64 sort = 2;
  string title = 3;
  string sub_title = 4;
}

message DeleteMallChannelRequest{
  int64 channel_id = 1;
}

message AddMallChannelItemRequest{
  // 频道id
  int64 channel_id = 1;
  // 缩略图
  string image = 2;
  // 跳转链接
  string link_url = 3;
  string skuid = 4;
  // 序号
  int64 sort = 5;
  // 缩略图
  string image1 = 6;
}

message GetMallChannelItemListRequest{
  int64 channel_id = 1;
  int64 page_index = 2;
  int64 page_size = 3;
}

message GetMallChannelItemListResponse{
  int64 code = 1;
  string message = 2;
  Data data = 3;
  message Data {
    repeated List list = 1;
    int64 total = 2;
  };
  message List {
    // 表id
    int64 id = 1;
    int64 channel_id = 2;
    string image = 3;
    string link_url = 4;
    string skuid = 5;
    // 序号
    int64 sort = 6;
    string updated_at = 7;
    string image1 = 8;
  };
}

message EditMallChannelItemRequest{
  // 频道内容id
  int64 channel_item_id = 1;
  // 缩略图
  string image = 2;
  // 跳转链接
  string link_url = 3;
  string skuid = 4;
  // 序号
  int64 sort = 5;
  // 缩略图
  string image1 = 6;
}

message DeleteMallChannelItemRequest{
  int64 channel_item_id = 1;
}

message GetGoodsChannelsRequest{
  int64 goods_id = 1;
}

message GetGoodsChannelsResponse{
  int64 code = 1;
  string message = 2;
  Data data = 3;
  message Data {
    repeated List list = 1;
    repeated string channel_name = 2;
  };
  message List {
    // 表id
    int64 id = 1;
    int64 channel_id = 2;
    string image = 3;
    string link_url = 4;
    string skuid = 5;
    // 序号
    int64 sort = 6;
    string updated_at = 7;
  };
}

message GetMiniMallChannelListRequest{
  int64 page_index = 1;
  int64 page_size = 2;
  int64 item_size = 3;
}

message GetMiniMallChannelListResponse{
  int64 code = 1;
  string message = 2;
  Data data = 3;
  message Data {
    repeated List list = 1;
  };
  message ItemList {
    int64 id = 1;
    int64 channel_id = 2;
    string image = 3;
    string link_url = 4;
    string skuid = 5;
    int64 sort = 6;
    string updated_at = 7;
  }
  message List {
    // 表id
    int64 id = 1;
    string title = 2;
    string sub_title = 3;
    // 序号
    int64 sort = 4;
    // 频道内容
    repeated ItemList item_list = 5;
  };
}

message OrderStepLogRequest {
  // 订单号
  string order_sn = 1;
  // 步骤json字符串
  string step = 2;
  // 1-微信下单，2-scrm用
  string type = 3;
}

message OrderSubscribeLogRequest {
  // 订单号
  string order_sn = 1;
  // 用户订阅状态，默认0，1-允许，2-拒绝
  int32 status = 2;
}

message StoreDeliveryMethodListRequest {
  // 页码
  int32 page_index = 1;
  // 每页数量
  int32 page_size = 2;
  //大区 全部传空字符串
  string bigregion = 3;
  //城市 全部传空字符串
  string city = 4;
  //配送方式 0全部 1发外卖配送 2 发物流配送 3不发配送(商家自配)
  int32 delivery_method = 5;
  //搜索关键词 财务编码或店铺名称
  string keyword = 6;
  //财务编码
  string finance_code= 7;
  //主体id
  int32 org_id = 8;
}

message StoreDeliveryMethodListResponse {
  //状态码 200成功 非200失败
  int32 code = 1;
  //错误信息
  string message = 2;
  //异常信息
  string error = 3;
  //列表信息
  repeated Item list = 4;
  //记录数量
  int32 total = 5;
  message Item {
    //财务编码
    string finance_code = 1;
    //门店名称
    string name = 2;
    //大区
    string  bigregion = 3;
    //城市
    string  city = 4;
    //配送方式 1发外卖配送 2 发物流配送 3不发配送(商家自配)
    int32 delivery_method = 5;
  }
}

message SetStoreDeliveryMethodRequest {
  //财务编码 多个时英文逗号分割
  string finance_code = 1;
  //配送方式 1发外卖配送 2 发物流配送 3不发配送(商家自配)
  int32 delivery_method = 2;
  //用户编码 前端不用传
  string user_no = 3;
  //用户名 前端不用传
  string user_name = 4;
  //创建人IP所属位置（不用前端传）
  string ip_location = 5;
  //创建人IP（前端不用传）
  string ip_addr = 6;
}

message SetStoreDeliveryMethodResponse {
  //状态码 200成功 非200失败
  int32 code = 1;
  //错误信息
  string message = 2;
  //异常信息
  string error = 3;
}

message StoreOperateLogRequest {
  // 页码
  int32 page_index = 1;
  // 每页数量
  int32 page_size = 2;
  //用户编码 前端不用传
  string user_no = 3;
  //搜索类型 0全部 1个人
  int32 search_all = 4;
  //日志类型 1：修改配送设置
  int32 type = 5;
  //搜索关键词 财务编码或店铺名称
  string keyword = 6;
}

message StoreOperateLogResponse {
  //状态码 200成功 非200失败
  int32 code = 1;
  //错误信息
  string message = 2;
  //异常信息
  string error = 3;
  //列表信息
  repeated Item list = 4;
  //记录数量
  int32 total = 5;
  message Item {
    //操作时间
    string create_time = 1;
    //操作类型
    int32 type = 2;
    //操作类型名称
    string type_text = 3;
    //门店编码
    string finance_code = 4;
    //操作详情
    string desc = 5;
    //创建人IP所属位置（不用前端传）
    string ip_location = 6;
    //创建人IP（前端不用传）
    string ip_addr = 7;
    //操作人
    string user_name = 8;
  }
}

// 获取店铺绑定的快递列表
message StoreExpressListRequest {
  //门店编码
  string finance_code = 1;
}
message StoreExpressListResponse{
  //状态码 200成功 非200失败
  int32 code = 1;
  //错误信息
  string message = 2;
  //列表信息
  repeated Item list = 4;
  message Item {
    //快递公司代码
    string code = 1;
    //快递公司名称
    string name = 2;
  }
}

message StoreRegionAndCityOptionsRequest{

}
message StoreRegionAndCityOptionsResponse{
  //状态码 200成功 非200失败
  int32 code = 1;
  //错误信息
  string message = 2;
  //异常信息
  string error = 3;
  //大区
  repeated string bigregion = 4;
  //城市
  repeated string city = 5;
}

// 新场景值
message OrderLinkStoreReq {
  // 微信场景值
  int32 scene = 1;
  // 订单号
  string order_sn = 2;
  // 商品类型，1实物、2虚拟、3到家
  int32 product_type = 3;
  // 订单金额，单位分
  int32 order_amount = 4;
  // 用户id，前端不需要传
  string scrm_id = 5;

  message Step {
    // 路径
    string path = 1;
    // 参数
    map<string, string> query = 2;
  }

  repeated Step steps = 6;
}

message ContentSecurityRequest{
  //1:文本  2:图片  3:音频  4:视频
  int32 type =1;
  //当type = 1,文本内容
  string content =2;
}
message ContentSecurityResponse{
  //状态码 200成功 非200失败
  int32 code =1;
  //信息
  string message =2;
}


message MemberLeverRes{
 int32 code = 1;
 string message = 2;

  repeated  MemberLevers data = 3;
}

message MemberLevers{
  int32 Id = 1;
//    等级ID
  int32 level_id =2;
//    等级名称
  string lever_name =3;
//    等级背景
  string background = 4;
//    个人中心背景
  string   center_background = 5;
//    健康值
 int32 health_val = 6;
//    状态 0停用 1启用
 int32 level_status = 7;

 // 权益id，多个,分割
 string    privilege_ids = 8;
//    商城升级券，多个,分割
 string goods_upgrade_vouchers = 9;
//    到店礼券升级券，子龙那边的券，多个,分割
 string store_upgrade_vouchers = 10;
//    到店礼券周特权券，子龙那边的券，多个,分割
 string store_week_vouchers = 11;

 // member_price 会员价，折扣
  float member_price = 12;

  // create_time
  string create_time = 13;
  string update_time = 14;
}

message EsInitStoreProductReq {
  // 财务编码
  string finance_code = 1;
}
