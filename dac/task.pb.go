// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dac/task.proto

package dac

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ChooseTaskReq struct {
	// 1 医保服务额度过期、2医保服务额度月度发放
	Data int32 `protobuf:"varint,1,opt,name=data,proto3" json:"data"`
	// 当data=2时，可以指定当前时间 如 2023-06-29 12:00:00
	Time                 string   `protobuf:"bytes,2,opt,name=time,proto3" json:"time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChooseTaskReq) Reset()         { *m = ChooseTaskReq{} }
func (m *ChooseTaskReq) String() string { return proto.CompactTextString(m) }
func (*ChooseTaskReq) ProtoMessage()    {}
func (*ChooseTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_b7948ab480552637, []int{0}
}

func (m *ChooseTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChooseTaskReq.Unmarshal(m, b)
}
func (m *ChooseTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChooseTaskReq.Marshal(b, m, deterministic)
}
func (m *ChooseTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChooseTaskReq.Merge(m, src)
}
func (m *ChooseTaskReq) XXX_Size() int {
	return xxx_messageInfo_ChooseTaskReq.Size(m)
}
func (m *ChooseTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChooseTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChooseTaskReq proto.InternalMessageInfo

func (m *ChooseTaskReq) GetData() int32 {
	if m != nil {
		return m.Data
	}
	return 0
}

func (m *ChooseTaskReq) GetTime() string {
	if m != nil {
		return m.Time
	}
	return ""
}

type TaskResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskResponse) Reset()         { *m = TaskResponse{} }
func (m *TaskResponse) String() string { return proto.CompactTextString(m) }
func (*TaskResponse) ProtoMessage()    {}
func (*TaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b7948ab480552637, []int{1}
}

func (m *TaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskResponse.Unmarshal(m, b)
}
func (m *TaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskResponse.Marshal(b, m, deterministic)
}
func (m *TaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskResponse.Merge(m, src)
}
func (m *TaskResponse) XXX_Size() int {
	return xxx_messageInfo_TaskResponse.Size(m)
}
func (m *TaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TaskResponse proto.InternalMessageInfo

func (m *TaskResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TaskResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func init() {
	proto.RegisterType((*ChooseTaskReq)(nil), "dac.ChooseTaskReq")
	proto.RegisterType((*TaskResponse)(nil), "dac.TaskResponse")
}

func init() { proto.RegisterFile("dac/task.proto", fileDescriptor_b7948ab480552637) }

var fileDescriptor_b7948ab480552637 = []byte{
	// 167 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0x4b, 0x49, 0x4c, 0xd6,
	0x2f, 0x49, 0x2c, 0xce, 0xd6, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x4e, 0x49, 0x4c, 0x56,
	0x32, 0xe7, 0xe2, 0x75, 0xce, 0xc8, 0xcf, 0x2f, 0x4e, 0x0d, 0x49, 0x2c, 0xce, 0x0e, 0x4a, 0x2d,
	0x14, 0x12, 0xe2, 0x62, 0x49, 0x49, 0x2c, 0x49, 0x94, 0x60, 0x54, 0x60, 0xd4, 0x60, 0x0d, 0x02,
	0xb3, 0x41, 0x62, 0x25, 0x99, 0xb9, 0xa9, 0x12, 0x4c, 0x0a, 0x8c, 0x1a, 0x9c, 0x41, 0x60, 0xb6,
	0x92, 0x0d, 0x17, 0x0f, 0x44, 0x4b, 0x71, 0x41, 0x7e, 0x5e, 0x71, 0x2a, 0x48, 0x4d, 0x72, 0x7e,
	0x4a, 0x2a, 0x4c, 0x1f, 0x88, 0x2d, 0x24, 0xc1, 0xc5, 0x9e, 0x9b, 0x5a, 0x5c, 0x9c, 0x98, 0x0e,
	0xd3, 0x0a, 0xe3, 0x1a, 0x59, 0x73, 0xb1, 0x80, 0x74, 0x0b, 0x19, 0x73, 0x71, 0x21, 0xac, 0x17,
	0x12, 0xd2, 0x4b, 0x49, 0x4c, 0xd6, 0x43, 0x71, 0x8f, 0x94, 0x20, 0x58, 0x0c, 0xd9, 0x2a, 0x27,
	0xd6, 0x28, 0x90, 0xd3, 0x93, 0xd8, 0xc0, 0xde, 0x30, 0x06, 0x04, 0x00, 0x00, 0xff, 0xff, 0x17,
	0x0f, 0x09, 0xda, 0xd8, 0x00, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TaskClient is the client API for Task service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TaskClient interface {
	//提供测试使用的，手动选择执行定时任务
	ChooseTask(ctx context.Context, in *ChooseTaskReq, opts ...grpc.CallOption) (*TaskResponse, error)
}

type taskClient struct {
	cc *grpc.ClientConn
}

func NewTaskClient(cc *grpc.ClientConn) TaskClient {
	return &taskClient{cc}
}

func (c *taskClient) ChooseTask(ctx context.Context, in *ChooseTaskReq, opts ...grpc.CallOption) (*TaskResponse, error) {
	out := new(TaskResponse)
	err := c.cc.Invoke(ctx, "/dac.Task/ChooseTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TaskServer is the server API for Task service.
type TaskServer interface {
	//提供测试使用的，手动选择执行定时任务
	ChooseTask(context.Context, *ChooseTaskReq) (*TaskResponse, error)
}

// UnimplementedTaskServer can be embedded to have forward compatible implementations.
type UnimplementedTaskServer struct {
}

func (*UnimplementedTaskServer) ChooseTask(ctx context.Context, req *ChooseTaskReq) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChooseTask not implemented")
}

func RegisterTaskServer(s *grpc.Server, srv TaskServer) {
	s.RegisterService(&_Task_serviceDesc, srv)
}

func _Task_ChooseTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChooseTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServer).ChooseTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dac.Task/ChooseTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServer).ChooseTask(ctx, req.(*ChooseTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Task_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dac.Task",
	HandlerType: (*TaskServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChooseTask",
			Handler:    _Task_ChooseTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dac/task.proto",
}
