package dac

import (
	"context"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"sync"
	"time"
)

type Client struct {
	lock sync.Mutex
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	RPC  DatacenterServiceClient
	PU   PickupServiceClient
	CG   CommunityGroupServiceClient
}

var grpcClient *Client
var grpcMessageClient *Client

func init() {
	grpcClient = &Client{
		Ctx: context.Background(),
	}

	grpcMessageClient = &Client{
		Ctx: context.Background(),
	}
}

func GetDataCenterClient() *Client {
	grpcClient.Ctx, _ = context.WithTimeout(context.Background(), 30*time.Second)

	if isAlive() {
		return grpcClient
	}

	grpcClient.lock.Lock()
	defer grpcClient.lock.Unlock()

	if isAlive() {
		return grpcClient
	}

	return newClient()
}

func newClient() *Client {
	var err error
	url := config.GetString("grpc.data-center")
	//url = "172.30.1.4:10032"
	if url == "" {
		url = "127.0.0.1:10032"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("datacenter，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = NewDatacenterServiceClient(grpcClient.Conn)
		grpcClient.PU = NewPickupServiceClient(grpcClient.Conn)
		grpcClient.CG = NewCommunityGroupServiceClient(grpcClient.Conn)

		return grpcClient
	}
}

func isAlive() bool {
	return grpcClient != nil && grpcClient.Conn != nil && grpcClient.Conn.GetState().String() != "SHUTDOWN"
}

func (c *Client) Close() {
	//c.Conn.Close()
	//c.Cf()
}

func isMessageClientAlive() bool {
	return grpcMessageClient != nil && grpcMessageClient.Conn != nil && grpcMessageClient.Conn.GetState().String() != "SHUTDOWN"
}

func GetDataCenterMessgeClient() *Client {
	grpcMessageClient.Ctx, _ = context.WithTimeout(context.Background(), 30*time.Second)

	if isMessageClientAlive() {
		return grpcMessageClient
	}

	grpcMessageClient.lock.Lock()
	defer grpcMessageClient.lock.Unlock()

	if isMessageClientAlive() {
		return grpcMessageClient
	}

	return newMessageClient()
}

func newMessageClient() *Client {
	var err error
	url := config.GetString("grpc.datacenter.msg")
	//url = "10.1.1.242:10032"
	if url == "" {
		url = "127.0.0.1:10032"
	}

	if grpcMessageClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("grpc.datacenter.msg，grpc连接失败，", err)
		return nil
	} else {
		grpcMessageClient.RPC = NewDatacenterServiceClient(grpcMessageClient.Conn)

		return grpcMessageClient
	}
}
