syntax = "proto3";
package dac;
option go_package = "dac";
import "google/protobuf/wrappers.proto";
import "google/protobuf/empty.proto";

// 社区团购服务相关
service PickupService {
  // 活动列表
  rpc List(PickupListReq)returns(PickupListResponse);
  // 新增/修改活动
  rpc Store(PickupStoreReq)returns(PickupResponse);
  // 撤销活动
  rpc Delete(PickupDeleteReq)returns(PickupResponse);
  // 自提站点列表
  rpc Stations(StationsReq)returns (StationsResponse);
  // 自提站点新增或保存
  rpc StationStore(StationStoreReq) returns(PickupResponse);
  // 自提站点禁用或启用
  rpc StationPatch(StationPatchReq) returns(PickupResponse);
  // 自提站点详情
  rpc StationDetail(StationDetailReq) returns(StationDetailResponse);
  // 自提点导入模板
  rpc StationImportTemplate(StationImportTemplateReq) returns (StationImportTemplateResponse);
  // 批量导入自提点
  rpc StationImport(StationImportReq) returns(StationImportResponse);
  // 自提点导入历史
  rpc StationImportHistories(StationImportHistoryListReq) returns(StationImportHistoryListResponse);

  // --------以下是小程序的接口-----------
  // 自提站点查询
  rpc AWStations(AWStationsReq) returns(AWStationsResponse);
  // 站点状态查询
  rpc AWStationState(AWStationStateReq) returns(AWStationStateResponse);
  // 最近的站点
  rpc AWStationNearest(AWStationNearestReq) returns(AWStationNearestResponse);

  //社区团购
  //活动列表
  rpc GroupActivityList(GroupActivityListReq)returns(GroupActivityListResponse);
  //活动详情
  rpc GroupActivityQuery(GroupActivityQueryReq)returns(GroupActivityQueryResponse);
  //活动新增编辑
  rpc GroupActivityEdit(GroupActivityEditReq)returns(google.protobuf.Empty);
  //活动状态更新
  rpc GroupActivityStatus(GroupActivityStatusReq)returns(google.protobuf.Empty);
  //佣金提现列表
  rpc CommissionCashoutList(CommissionCashoutListReq)returns(CommissionCashoutListResponse);
  //佣金提现列表审核\确认打款
  rpc CommissionCashoutAudit(CommissionCashoutAuditReq)returns(google.protobuf.Empty);
  //佣金提现列表查看详情
  rpc CommissionCashoutQuery(CommissionCashoutQueryReq)returns(CommissionCashoutQueryResponse);
  //佣金提现导入已打款记录
  rpc CommissionCashoutImport(CommissionCashoutImportReq)returns(google.protobuf.Empty);
  //佣金提现查看导入历史
  rpc CommissionCashoutImportList(CommissionCashoutImportListReq)returns(CommissionCashoutImportListResponse);
  //佣金信息
  rpc CommissionInfo(CommissionInfoReq)returns(CommissionInfoResponse);
  //佣金提现申请
  rpc CommissionCashout(CommissionCashoutReq)returns(google.protobuf.Empty);
}

message PickupResponse {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
}

// 自提活动列表
message PickupListReq {
  // 关联门店财务编码，多个用逗号分隔
  string shop_codes = 1;
  // 每页数量
  int32 page_size = 2;
  // 当前页码
  int32 page_index = 3;
}

message PickupListResponse {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message Pickup {
    // 活动id
    int32 id = 1;
    // 活动类型
    string type = 2;
    // 成团金额
    float station_daily_min =3;
    // 截止时间
    string end_time = 4;
    // 送货天数
    int32 deliver_days = 5;
    // 门店名称
    string shop_name = 6;
    // 财务编码
    string shop_codes = 7;
    // 分享海报
    string share_image = 8;
  }
  repeated Pickup data = 6;
  // 总数
  int32 total = 4;
}

// 自提活动列表
message PickupStoreReq {
  // 关联门店财务编码，多个用逗号分隔
  string shop_codes = 1;
  // 成团金额
  float station_daily_min = 2;
  // 截止时间
  string end_time = 3;
  // 送货天数
  int32 deliver_days = 4;
  // 操作人no，前端不需要传
  string user_no = 6;
  // 操作人名称，前端不需要传
  string user_name = 7;
  // 分享海报
  string share_image = 8;
}

message PickupDeleteReq {
  // 活动id
  int32 id = 1;
  // 操作人no，前端不需要传
  string user_no = 6;
  // 操作人名称，前端不需要传
  string user_name = 7;
}

message StationImportTemplateReq {}

message StationImportTemplateResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 文件字节流
  bytes template = 3;
}

message StationImportReq {
  // 文件字节流
  bytes file = 1;
}

message StationImportResponse {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
}

message StationImportHistoryListReq {
  // 页码，不传默认为1
  int32 page = 1;
  // 每页数量，不传默认10
  int32 page_size = 2;
}

message StationImportHistoryListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message List {
    // 导入id
    int32 id = 1;
    // 导入时间
    string created_at = 2;
    // 导入结果
    string result = 3;
    // 导入结果url
    string result_url = 4;
  }
  repeated List data = 3;
  // 总数
  int32 total = 4;
}

message StationsReq {
  //名称
  string name = 1;
  //状态 "0"禁用、"1"启用 其他全部
  string  status = 2;
  // 页码，不传默认为1
  int32 page = 3;
  // 每页数量，不传默认10
  int32 page_size = 4;
}

message StationsResponse {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;

  message List {
    int32 id = 1;
    //名称
    string name = 2;
    //详细地址
    string address = 3;
    //状态 0禁用、1启用
    int32  status = 4;
  }
  repeated List data = 3;
  // 总数
  int32 total = 4;
}

message StationStoreReq {
  int32 id = 1;
  //名称
  string name = 2;
  //详细地址
  string address = 3;
  //维度
  string  lng = 4;
  //纬度
  string lat = 5;
}



message StationPatchReq {
  int32 id = 1;
  // 状态 0禁用、1启用，不更新不要传
  google.protobuf.Int32Value status = 2;
}

message StationDetailReq {
  int32 id = 1;
}

message StationDetailResponse {
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  message Data {
    //id
    int32 id = 1;
    //名称
    string name = 2;
    //详细地址
    string address = 3;
    string  lng = 4;
    string lat = 5;
    //状态 0禁用、1启用
    int32  status = 6;
  }
  Data data = 3;
}

message AWStationsReq {
  // 地址经度
  string lng = 1;
  // 地址纬度
  string lat = 2;
  // 门店财务编码
  string finance_code = 3;
  // 搜索关键字
  string keyword = 4;
  // 每页数量
  int32 page_size = 5;
  // 当前页码
  int32 page_index = 6;
}

message AWStationsResponse {
  int32 code = 1;
  string message = 2;
  message Station {
    // 站点id
    int32 id = 1;
    // 站点名称
    string name = 2;
    // 站点地址
    string address = 3;
    // 距离，自带单位，如km
    string distance = 4;
  }
  repeated Station data = 3;
  // 总数
  int32 total = 4;
}

message AWStationStateReq{
  // 门店财务编码
  string finance_code = 1;
  // 站点id
  int32 station_id = 2;
}

message AWStationStateResponse {
  int32 code = 1;
  string message = 2;
  message State {
    // 站点id
    int32 id = 1;
    // 站点名称
    string name = 2;
    // 预计送达时间
    string expected_time = 3;
    // 预计送达时间描述，如成团当日
    string expected_desc = 4;
    // 进度提醒 已支付x单，16:00前还需成交y单才可成团
    string progress_notice = 5;
  }
  State data = 3;
}

message AWStationNearestReq {
  // 地址经度
  string lng = 1;
  // 地址纬度
  string lat = 2;
  // 门店财务编码
  string finance_code = 3;
}

message AWStationNearestResponse {
  int32 code = 1;
  string message = 2;
  message Nearest {
    // 站点id
    int32 id = 1;
    // 站点名称
    string name = 2;
    // 已支付订单计数
    int32 paid_count = 3;
    // 还需数量
    int32 remain = 4;
    // 预计送达时间
    string expected_time = 5;
    // 预计送达时间描述，如成团当日
    string expected_desc = 6;
    // 进度提醒 已支付x单，16:00前还需成交y单才可成团
    string progress_notice = 7;
    // 距离，自带单位，如km
    string distance = 8;
  }
  Nearest data = 3;
}

//社区团购
message GroupActivityListReq {
  //活动状态，0未开始 1进行中 2已结束 3已终止 -1全部
  int32 status = 1;
  //活动时间开始日期
  string start_time = 2;
  //活动时间结束日期
  string end_time = 3;
  //门店财务编码，多个财务编码用逗号分隔
  string finance_code = 4;
  //创建人
  string created_by = 5;
  //每页数量
  int32 page_size = 6;
  //当前页码
  int32 page_index = 7;
}

message GroupActivityListResponse {
  message Data {
    int32 id = 1;
    // 门店财务编码
    string store_finance_code = 13;
    // 门店名称
    string store_name = 14;
    //活动开始时间
    string start_time = 2;
    //活动结束时间
    string end_time = 3;
    //代收方式 0团长决定是否代收 1团长必须代收
    int32 delivery_mode = 4;
    //是否包邮 1是 0否
    int32 is_free = 5;
    //拼团区域是否显示 1是 0否
    int32 is_show = 6;
    //成团金额，单位分
    int32 group_money = 7;
    //拼团时间（小时）
    int32 group_day = 8;
    //送达时间（天）
    int32 delivery_day = 9;
    //活动状态，0未开始 1进行中 2已结束 3已终止
    int32 status = 10;
    //创建时间
    string created_at = 11;
    //创建人
    string created_by = 12;
  }
  int32 code = 1;
  string message = 2;
  repeated Data data = 3;
  //总数
  int32 total = 4;
}

message GroupActivityQueryReq {
  int32 id = 1;
}

message GroupActivityQueryResponse {
  int32 code = 1;
  string message = 2;
  message Data {
    int32 id = 1;
    //活动开始时间
    string start_time = 2;
    //活动结束时间
    string end_time = 3;
    //代收方式 0团长决定是否代收 1团长必须代收
    int32 delivery_mode = 4;
    //是否包邮 1是 0否
    int32 is_free = 5;
    //拼团区域是否显示 1是 0否
    int32 is_show = 6;
    //成团金额，单位分
    int32 group_money = 7;
    //拼团时间（小时）
    int32 group_day = 8;
    //送达时间（天）
    int32 delivery_day = 9;
    //活动状态，0未开始 1进行中 2已结束 3已终止
    int32 status = 10;
    //门店财务编码
    string finance_code = 11;
    // 店铺推广海报地址
    string poster_url = 12;
  }
  Data data = 3;
}

message GroupActivityEditReq {
  int32 id = 1;
  //活动开始时间
  string start_time = 2;
  //活动结束时间
  string end_time = 3;
  //代收方式 0团长决定是否代收 1团长必须代收
  int32 delivery_mode = 4;
  //是否包邮 1是 0否
  int32 is_free = 5;
  //拼团区域是否显示 1是 0否
  int32 is_show = 6;
  //成团金额，单位分
  int32 group_money = 7;
  //拼团时间（小时）
  int32 group_day = 8;
  //送达时间（天）
  int32 delivery_day = 9;
  //操作人
  string created_by = 10;
  //门店财务编码
  string finance_code = 11;
  // 店铺推广海报地址
  string poster_url = 12;
}

message GroupActivityStatusReq {
  int32 id = 1;
  //活动状态，0未开始 1进行中 2已结束 3已终止
  int32 status = 2;
  //操作人
  string created_by = 3;
}

message CommissionCashoutListReq {
  //状态 0已申请提现 1审核通过待打款 2已驳回 3已打款
  int32 status = 1;
  //分销员身份 1内部 2外部
  int32 identity = 2;
  //提现人信息 1收款人姓名 2收款账号 3提现编码 4提现人手机
  int32 search_type = 3;
  //提现人信息搜索关键字
  string search_keyword = 4;
  //申请开始时间
  string start_time = 5;
  //申请结束时间
  string end_time = 6;
  //每页数量
  int32 page_size = 7;
  //当前页码
  int32 page_index = 8;
  //会员ID
  string member_id = 9;
}

message CommissionCashoutListResponse {
  message Data {
    int32 id = 1;
    //提现编号
    string cash_out_bn = 2;
    //提现手机
    string cash_mobile = 3;
    //申请金额
    int32 cash_out = 4;
    //收款人
    string cash_name = 5;
    //佣金提现收款账户
    string cash_account = 6;
    //拼佣金提现开户行
    string cash_bank = 7;
    //佣金提现开户支行
    string cash_branch = 8;
    //分销员身份 1内部 2外部
    int32 identity = 9;
    //门店
    string store = 10;
    //财务编码
    string finance_code = 11;
    //品牌
    string brand = 12;
    //城市
    string city = 13;
    //大区
    string big_region = 14;
    //状态 0已申请提现 1审核通过待打款 2已驳回 3已打款
    int32 status = 15;
    //申请时间
    string create_time = 16;
    //审核人
    string audit_user = 17;
    //审核驳回的原因
    string memo = 18;
    //审核时间
    string update_time = 19;
  }
  int32 code = 1;
  string message = 2;
  repeated Data data = 3;
  //总数
  int32 total = 4;
}

message CommissionCashoutAuditReq {
  int32 id = 1;
  //状态 1审核通过待打款 2已驳回 3已打款
  int32 status = 2;
  //审核驳回的原因
  string memo = 3;
}

message CommissionCashoutQueryReq {
  int32 id = 1;
}

message CommissionCashoutQueryResponse {
  int32 code = 1;
  string message = 2;
  message Data {
    int32 id = 1;
    //提现编号
    string cash_out_bn = 2;
    //提现手机
    string cash_mobile = 3;
    //申请金额
    int32 cash_out = 4;
    //收款人
    string cash_name = 5;
    //佣金提现收款账户
    string cash_account = 6;
    //佣金提现开户行
    string cash_bank = 7;
    //佣金提现开户支行
    string cash_branch = 8;
    //分销员身份 1内部 2外部
    int32 identity = 9;
    //门店
    string store = 10;
    //财务编码
    string finance_code = 11;
    //品牌
    string brand = 12;
    //城市
    string city = 13;
    //大区
    string big_region = 14;
    //状态 0已申请提现 1审核通过待打款 2已驳回 3已打款
    int32 status = 15;
    //申请时间
    string create_time = 16;
    //审核人
    string audit_user = 17;
    //审核驳回的原因
    string memo = 18;
    //审核时间
    string update_time = 19;
  }
  Data data = 3;
}

message CommissionCashoutImportReq {
  string excel_url = 1;
}

message CommissionCashoutImportListReq {
  //每页数量
  int32 page_size = 1;
  //当前页码
  int32 page_index = 2;
}

message CommissionCashoutImportListResponse {
  message Data {
    int32 id = 1;
    //导入结果
    string import_result = 2;
    //导入结果文件下载地址
    string import_result_url = 3;
    //导入时间
    string create_time = 4;
  }
  int32 code = 1;
  string message = 2;
  repeated Data data = 3;
  //总数
  int32 total = 4;
}

message CommissionInfoReq {
  //会员ID
  string member_id = 1;
}

message CommissionInfoResponse {
  int32 code = 1;
  string message = 2;
  message Data {
    //可提现佣金(不包括冻结中)
    int32 commission_cash = 1;
    //已申请提现(冻结中)
    int32 commission_frozen = 2;
    //提现人
    string cash_name = 3;
    //佣金提现收款账户
    string cash_account = 4;
    //佣金提现开户行
    string cash_bank = 5;
    //佣金提现开户支行
    string cash_branch = 6;
  }
  Data data = 3;
}

message CommissionCashoutReq {
  //申请提现金额
  int32 cash_out = 1;
  //提现人
  string cash_name = 2;
  //佣金提现收款账户
  string cash_account = 3;
  //佣金提现开户行
  string cash_bank = 4;
  //佣金提现开户支行
  string cash_branch = 5;
  //会员ID
  string member_id = 6;
}