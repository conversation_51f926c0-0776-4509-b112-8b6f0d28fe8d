syntax = "proto3";
package dac;

// @Desc 社区团购
service CommunityGroupService {
    //  社区团购活动设置
    rpc GroupActivityGet(GroupActivityGetRequest) returns (GroupActivityGetResponse);
}


message GroupActivityGetRequest {
    // 店铺财务编码
    string store_finance_code = 1;
    // 状态 0未开始 1进行中 2已结束 3已终止
    int32 status = 2;
}

message GroupActivityGetResponse {
    int64 id = 1;
    // 创建人
    string user_no = 2;
    // 店铺财务编码
    string store_finance_code = 3;
    // 状态 0未开始 1进行中 2已结束 3已终止
    int32 status = 4;
    // 开始时间
    string start_at = 5;
    // 结束时间
    string end_at = 6;
    // 待收方式 0团长决定是否代收 1团长必须代收
    int32 take_type = 7;
    // 是否包邮 0否 1是
    int32 is_free_fee = 8;
    // 前台显示 0否 1是
    int32 is_show = 9;
    // 成团金额(单位:分)
    int64 min_amount = 10;
    // 开团后N小时内必须成团
    int32 must_hours = 11;
    // 最晚N日内送达
    int32 deliver_days = 12;
    string created_at = 13;
    string updated_at = 14;
}
