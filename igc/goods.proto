syntax = "proto3";
package proto;
option go_package = "igc";

import "google/protobuf/wrappers.proto";

service IntegralGoodsService {
  // 获取商品信息列表
  rpc List(GoodsListRequest) returns (GoodsListResponse) {}
  // 详情
  rpc Detail(GoodsIdRequest) returns (GoodsDetailResponse) {}
  // 编辑或增加
  rpc Store(GoodsDetail) returns (GoodsResponse) {}
  // 上下架、推荐 积分商品（部分更新）
  rpc Patch(GoodsPatchRequest) returns (GoodsResponse) {}

  //以下是阿闻小程序接口
  // 积分商品列表
  rpc AWList(AWGoodsListRequest) returns (AWGoodsListResponse) {}
  // 积分商品详情
  rpc AWDetail(GoodsIdRequest) returns (AWGoodsDetailResponse) {}
}

message GoodsIdRequest{
  // 商品id
  int32 id = 1;
  // 前端不需要传
  string scrm_id = 2;
}

message GoodsResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
}

message GoodsListRequest{
  // 上架 '0'表示下架 '1'表示上架，其他表示不过滤
  string show = 1;
  // 推荐 '0'不推荐 '1'表示推荐，其他表示不过滤
  string commend = 2;
  // 类型 '1'实物'2'虚拟'3'优惠券，其他表示不过滤
  string type = 3;
  // 名称
  string name = 4;
  // 每页数量
  int32 page_size = 5;
  // 当前页码
  int32 page_index = 6;

  //商品类型 1第三方商品 2自有商品3门店卷 4商城券 5:爱心币
  int32 good_type = 7;

}

message GoodsListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  repeated GoodsList data = 3;
  // 总数
  int32 total = 4;
}

message GoodsDetailResponse{
  int32 code = 1;
  string message = 2;
  string error = 3;
   GoodsDetail data = 4;
}

message AWGoodsDetailResponse{
  int32 code = 1;
  string message = 2;
  string error = 3;
  message Extra{
    //用户积分状态 0积分不足 1积分正常
    int32 integral_state = 1;
    //会员vip状态 0用户不是会员 1用户是会员
    int32 member_vip_state = 2;
    //状态 0结束 1即将开始 2进行中 3库存不足
    int32 goods_state = 3;
  }
  GoodsDetail data = 4;
  //额外计算好的信息
  Extra extra = 5;
}

message GoodsList{
  // 积分礼品主键
  int32 id = 11;
  // 积分礼品名称
  string name = 1;
  // 类型1实物2虚拟3优惠券
  int32 type = 16;
  // 积分礼品兑换所需积分
  int32 points = 3;
  // 积分礼品库存数
  int32 storage = 6;
  // 积分礼品上架 0表示下架 1表示上架
  int32 show = 7;
  // 积分礼品推荐 0不推荐 1表示推荐
  int32 commend = 8;
  // 售出数
  int32 sale_num = 9;
  // 创建时间
  string add_time = 10;
}

message GoodsDetail{
  // 积分礼品主键
  int32 id = 19;
  // 积分礼品名称
  string name = 1;
  // 积分礼品原价
  float price = 2;
  // 积分礼品兑换所需积分
  int32 points = 3;
  // 积分礼品默认封面图片
  string image = 4;
  // 积分礼品货号
  string serial = 5;
  // 积分礼品库存数
  int32 storage = 6;
  // 积分礼品上架 0表示下架 1表示上架
  int32 show = 7;
  // 积分礼品推荐 0不推荐 1推荐
  int32 commend = 8;
  // 积分礼品详细内容
  string body = 9;
  // 是否限制每会员兑换数量 0不限制 1限制
  int32 is_limit = 10;
  // 每会员限制兑换数量
  int32 limit_num = 11;
  // 是否限制兑换时间 0为不限制 1为限制
  int32 is_limit_time = 12;
  // 限制参与兑换的会员级别 0不限制 1限制
  int32 limit_mgrade = 13;
  // 兑换开始时间
  string start_time = 14;
  // 兑换结束时间
  string end_time = 15;
  // 类型1实物2虚拟3优惠券 当type=2时good_type=0
  int32 type = 16;
  // 虚拟商品sku或scrm优惠券IDgo
  int64 scrm_info = 17;
  // 商品类型 1第三方商品(属type=1) 2自有商品(属type=1) 3门店卷(属type=3) 4:商城券（属type=4）
  int32 good_type = 18;
  //兑换数量
  int32 sale_num = 20;
  // 会员等级数组
  repeated int32 user_level_ids = 21;
  // 新增的爱心币的数量
  int32 coin_num = 22;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 23;

}

message GoodsPatchRequest{
  // 商品id
  int32 id = 1;
  // 上架 '0'表示下架 '1'表示上架，不更新不要传
  google.protobuf.Int32Value show = 2;
  // 推荐 '0'不推荐 '1'表示推荐，不更新不要传
  google.protobuf.Int32Value commend = 3;
}

message AWGoodsListRequest{
  // 排序类型 0最新 1积分从低到高 2积分从高到低
  string sort = 1;
  // 推荐 '0'不推荐 '1'表示推荐
  string commend = 2;
  // 兑换类型 0全部兑换 1我能兑换
  string show_type = 3;
  // 关键词查询
  string keyword = 4;
  //积分区间开始
  string points_start = 5;
  //积分区间结束
  string points_end = 6;
  // 每页数量
  int32 page_size = 7;
  // 当前页码
  int32 page_index = 8;
  // 前端不需要传
  string scrm_id = 9;

  //商品类型 1第三方商品 2自有商品3门店卷 4商城券 5:爱心币
  int32 good_type = 10;
}

message AWGoodsListResponse{
  // 状态码，200正常，>=400出错
  int32 code = 1;
  // 消息
  string message = 2;
  // 总数
  int32 total = 3;
  repeated GoodsDetail data = 4;
}