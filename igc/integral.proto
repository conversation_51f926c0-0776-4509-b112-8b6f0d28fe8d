syntax = "proto3";
package igc;

service IntegralService {
  // 积分倒推重新计算
  rpc Recompute(IntegralRecomputeReq) returns(BaseResponse);
  // 分页查询用户积分明细
  rpc PageIntegralRecord(PageIntegralRecordReq) returns(PageIntegralRecordResp);
}

//积分服务定时任务启动
service TaskChoose {
  rpc ChooseTaskRun (TaskRunVo) returns (BaseResponse);
}
message TaskRunVo{
  // 启动类型 1：会员统计
  int32 data = 1;
}

// 通用返回
message BaseResponse {
  // 状态码
  int32 code = 1;
  // 消息
  string message = 2;
}

// 积分重新计算
message IntegralRecomputeReq {
  // scrmId，多个用逗号分隔
  string scrm_id = 1;
  // 起始用户id
  int32 start_id = 2;
  // 结束用户id
  int32 end_id = 3;
  // 请求的时间戳
  int32 timestamp = 4;
  // 是否记录日志
  int32 record_log = 5;
  // 签名
  string sign = 6;
}

message PageIntegralRecordReq {
  //分页
  int32 page_index = 1;
  //分页
  int32 page_size = 2;
  //积分类型
  int32 integral_type = 3;
  //用户id
  string member_id = 4;
  //主体ID
  int32 org_id = 5;
}

message PageIntegralRecordResp {
  // 状态码
  int32 code = 1;
  // 消息
  string message = 2;

  repeated PageIntegralRecordData data = 3;        //积分列表数据

  int64 total = 4;
}

message PageIntegralRecordData {
  // 积分创建时间
  string integral_date = 1;
  // 积分值
  int64 integral_count = 2;
  // 积分类型
  int32 integral_type = 3;
  // 业务类型
  string integral_name = 4;
  // 订单号
  string order_id = 5;
  // 积分添加原因，详细说明
  string integral_reason = 6;
  // 积分添加原因，详细说明
  string user_level_id = 7;
}