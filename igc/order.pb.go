// Code generated by protoc-gen-go. DO NOT EDIT.
// source: igc/order.proto

package igc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type IntegralChangeMessageRequest struct {
	MemberId string `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	//积分类型
	IntegralType int32 `protobuf:"varint,2,opt,name=integral_type,json=integralType,proto3" json:"integral_type"`
	//积分
	InsertIntegral int32 `protobuf:"varint,3,opt,name=insert_integral,json=insertIntegral,proto3" json:"insert_integral"`
	//积分
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IntegralChangeMessageRequest) Reset()         { *m = IntegralChangeMessageRequest{} }
func (m *IntegralChangeMessageRequest) String() string { return proto.CompactTextString(m) }
func (*IntegralChangeMessageRequest) ProtoMessage()    {}
func (*IntegralChangeMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{0}
}

func (m *IntegralChangeMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IntegralChangeMessageRequest.Unmarshal(m, b)
}
func (m *IntegralChangeMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IntegralChangeMessageRequest.Marshal(b, m, deterministic)
}
func (m *IntegralChangeMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IntegralChangeMessageRequest.Merge(m, src)
}
func (m *IntegralChangeMessageRequest) XXX_Size() int {
	return xxx_messageInfo_IntegralChangeMessageRequest.Size(m)
}
func (m *IntegralChangeMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IntegralChangeMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IntegralChangeMessageRequest proto.InternalMessageInfo

func (m *IntegralChangeMessageRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *IntegralChangeMessageRequest) GetIntegralType() int32 {
	if m != nil {
		return m.IntegralType
	}
	return 0
}

func (m *IntegralChangeMessageRequest) GetInsertIntegral() int32 {
	if m != nil {
		return m.InsertIntegral
	}
	return 0
}

func (m *IntegralChangeMessageRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type OrderEmptyRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderEmptyRequest) Reset()         { *m = OrderEmptyRequest{} }
func (m *OrderEmptyRequest) String() string { return proto.CompactTextString(m) }
func (*OrderEmptyRequest) ProtoMessage()    {}
func (*OrderEmptyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{1}
}

func (m *OrderEmptyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderEmptyRequest.Unmarshal(m, b)
}
func (m *OrderEmptyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderEmptyRequest.Marshal(b, m, deterministic)
}
func (m *OrderEmptyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderEmptyRequest.Merge(m, src)
}
func (m *OrderEmptyRequest) XXX_Size() int {
	return xxx_messageInfo_OrderEmptyRequest.Size(m)
}
func (m *OrderEmptyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderEmptyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderEmptyRequest proto.InternalMessageInfo

type OrderResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderResponse) Reset()         { *m = OrderResponse{} }
func (m *OrderResponse) String() string { return proto.CompactTextString(m) }
func (*OrderResponse) ProtoMessage()    {}
func (*OrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{2}
}

func (m *OrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderResponse.Unmarshal(m, b)
}
func (m *OrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderResponse.Marshal(b, m, deterministic)
}
func (m *OrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderResponse.Merge(m, src)
}
func (m *OrderResponse) XXX_Size() int {
	return xxx_messageInfo_OrderResponse.Size(m)
}
func (m *OrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderResponse proto.InternalMessageInfo

func (m *OrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *OrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type OrderExpressRequest struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 物流名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 物流单号
	Sn                   string   `protobuf:"bytes,3,opt,name=sn,proto3" json:"sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderExpressRequest) Reset()         { *m = OrderExpressRequest{} }
func (m *OrderExpressRequest) String() string { return proto.CompactTextString(m) }
func (*OrderExpressRequest) ProtoMessage()    {}
func (*OrderExpressRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{3}
}

func (m *OrderExpressRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderExpressRequest.Unmarshal(m, b)
}
func (m *OrderExpressRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderExpressRequest.Marshal(b, m, deterministic)
}
func (m *OrderExpressRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderExpressRequest.Merge(m, src)
}
func (m *OrderExpressRequest) XXX_Size() int {
	return xxx_messageInfo_OrderExpressRequest.Size(m)
}
func (m *OrderExpressRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderExpressRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderExpressRequest proto.InternalMessageInfo

func (m *OrderExpressRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OrderExpressRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *OrderExpressRequest) GetSn() string {
	if m != nil {
		return m.Sn
	}
	return ""
}

type OrderListRequest struct {
	// 订单编号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 手机号
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	// 兑换开始日期
	StartDate string `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date"`
	// 兑换结束日期
	EndDate string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	// 订单状态 20待发货、21未使用、30待收货、40已完成、41已使用、42已过期、50已取消
	State string `protobuf:"bytes,3,opt,name=state,proto3" json:"state"`
	// 商品分类 1实物2虚拟3优惠券
	Type string `protobuf:"bytes,6,opt,name=type,proto3" json:"type"`
	// 订单来源 1阿闻
	From string `protobuf:"bytes,7,opt,name=from,proto3" json:"from"`
	// 销售渠道 2小程序、3app
	Channel string `protobuf:"bytes,8,opt,name=channel,proto3" json:"channel"`
	// 商品名称
	GoodsName string `protobuf:"bytes,9,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	// 每页数量，订单导出时不需要传
	PageSize int32 `protobuf:"varint,10,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 当前页码，订单导出时不需要传
	PageIndex            int32    `protobuf:"varint,11,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListRequest) Reset()         { *m = OrderListRequest{} }
func (m *OrderListRequest) String() string { return proto.CompactTextString(m) }
func (*OrderListRequest) ProtoMessage()    {}
func (*OrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{4}
}

func (m *OrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListRequest.Unmarshal(m, b)
}
func (m *OrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListRequest.Marshal(b, m, deterministic)
}
func (m *OrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListRequest.Merge(m, src)
}
func (m *OrderListRequest) XXX_Size() int {
	return xxx_messageInfo_OrderListRequest.Size(m)
}
func (m *OrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListRequest proto.InternalMessageInfo

func (m *OrderListRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *OrderListRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *OrderListRequest) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *OrderListRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *OrderListRequest) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *OrderListRequest) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *OrderListRequest) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

func (m *OrderListRequest) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *OrderListRequest) GetGoodsName() string {
	if m != nil {
		return m.GoodsName
	}
	return ""
}

func (m *OrderListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *OrderListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type GoodsListData struct {
	// 对应兑换商品id
	ProductId int32 `protobuf:"varint,9,opt,name=product_id,json=productId,proto3" json:"product_id"`
	// scrm_info，实物、虚拟skuid，优惠券id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	// 商品名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 商品价格
	Price float32 `protobuf:"fixed32,3,opt,name=price,proto3" json:"price"`
	// 商品货号
	Serial string `protobuf:"bytes,4,opt,name=serial,proto3" json:"serial"`
	// 所需积分
	Integral int32 `protobuf:"varint,5,opt,name=integral,proto3" json:"integral"`
	// 兑换数量
	Qty int32 `protobuf:"varint,6,opt,name=qty,proto3" json:"qty"`
	// 图片
	Image string `protobuf:"bytes,7,opt,name=image,proto3" json:"image"`
	// 总计
	Total                int32    `protobuf:"varint,8,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsListData) Reset()         { *m = GoodsListData{} }
func (m *GoodsListData) String() string { return proto.CompactTextString(m) }
func (*GoodsListData) ProtoMessage()    {}
func (*GoodsListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{5}
}

func (m *GoodsListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsListData.Unmarshal(m, b)
}
func (m *GoodsListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsListData.Marshal(b, m, deterministic)
}
func (m *GoodsListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsListData.Merge(m, src)
}
func (m *GoodsListData) XXX_Size() int {
	return xxx_messageInfo_GoodsListData.Size(m)
}
func (m *GoodsListData) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsListData.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsListData proto.InternalMessageInfo

func (m *GoodsListData) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GoodsListData) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GoodsListData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GoodsListData) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GoodsListData) GetSerial() string {
	if m != nil {
		return m.Serial
	}
	return ""
}

func (m *GoodsListData) GetIntegral() int32 {
	if m != nil {
		return m.Integral
	}
	return 0
}

func (m *GoodsListData) GetQty() int32 {
	if m != nil {
		return m.Qty
	}
	return 0
}

func (m *GoodsListData) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *GoodsListData) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type OrderAddressData struct {
	// 收货人名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 手机号
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	// 加密手机号
	EncryptMobile string `protobuf:"bytes,9,opt,name=encrypt_mobile,json=encryptMobile,proto3" json:"encrypt_mobile"`
	// 省市区
	AreaInfo string `protobuf:"bytes,3,opt,name=area_info,json=areaInfo,proto3" json:"area_info"`
	// 地址
	Address string `protobuf:"bytes,4,opt,name=address,proto3" json:"address"`
	// 物流单号
	ShippingCode string `protobuf:"bytes,5,opt,name=shipping_code,json=shippingCode,proto3" json:"shipping_code"`
	// 发货时间
	ShippingTime string `protobuf:"bytes,6,opt,name=shipping_time,json=shippingTime,proto3" json:"shipping_time"`
	// 物流公司编码
	ShippingEcode string `protobuf:"bytes,7,opt,name=shipping_ecode,json=shippingEcode,proto3" json:"shipping_ecode"`
	// 物流公司名称
	ShippingEname        string   `protobuf:"bytes,8,opt,name=shipping_ename,json=shippingEname,proto3" json:"shipping_ename"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderAddressData) Reset()         { *m = OrderAddressData{} }
func (m *OrderAddressData) String() string { return proto.CompactTextString(m) }
func (*OrderAddressData) ProtoMessage()    {}
func (*OrderAddressData) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{6}
}

func (m *OrderAddressData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderAddressData.Unmarshal(m, b)
}
func (m *OrderAddressData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderAddressData.Marshal(b, m, deterministic)
}
func (m *OrderAddressData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderAddressData.Merge(m, src)
}
func (m *OrderAddressData) XXX_Size() int {
	return xxx_messageInfo_OrderAddressData.Size(m)
}
func (m *OrderAddressData) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderAddressData.DiscardUnknown(m)
}

var xxx_messageInfo_OrderAddressData proto.InternalMessageInfo

func (m *OrderAddressData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *OrderAddressData) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *OrderAddressData) GetEncryptMobile() string {
	if m != nil {
		return m.EncryptMobile
	}
	return ""
}

func (m *OrderAddressData) GetAreaInfo() string {
	if m != nil {
		return m.AreaInfo
	}
	return ""
}

func (m *OrderAddressData) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *OrderAddressData) GetShippingCode() string {
	if m != nil {
		return m.ShippingCode
	}
	return ""
}

func (m *OrderAddressData) GetShippingTime() string {
	if m != nil {
		return m.ShippingTime
	}
	return ""
}

func (m *OrderAddressData) GetShippingEcode() string {
	if m != nil {
		return m.ShippingEcode
	}
	return ""
}

func (m *OrderAddressData) GetShippingEname() string {
	if m != nil {
		return m.ShippingEname
	}
	return ""
}

type OrderListData struct {
	// 订单编号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 下单时间
	AddTime string `protobuf:"bytes,2,opt,name=add_time,json=addTime,proto3" json:"add_time"`
	// 订单来源 阿闻/App
	From string `protobuf:"bytes,3,opt,name=from,proto3" json:"from"`
	// 会员账号
	MemberName string `protobuf:"bytes,4,opt,name=member_name,json=memberName,proto3" json:"member_name"`
	// 会员手机号
	MemberMobile string `protobuf:"bytes,5,opt,name=member_mobile,json=memberMobile,proto3" json:"member_mobile"`
	// 加密手机号
	EncryptMobile string `protobuf:"bytes,16,opt,name=encrypt_mobile,json=encryptMobile,proto3" json:"encrypt_mobile"`
	// scrmId
	ScrmId string `protobuf:"bytes,6,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 备注
	Remark string `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark"`
	// 状态数字 20待发货、21未使用、30待收货、40已完成、41已使用、42已过期、50已取消
	State int32 `protobuf:"varint,8,opt,name=state,proto3" json:"state"`
	// 订单文本 20待发货、21未使用、30待收货、40已完成、41已使用、42已过期、50已取消
	StateText string `protobuf:"bytes,9,opt,name=state_text,json=stateText,proto3" json:"state_text"`
	// 订单类型 1实物2虚拟3优惠券
	Type int32 `protobuf:"varint,10,opt,name=type,proto3" json:"type"`
	// 商品信息
	Goods []*GoodsListData `protobuf:"bytes,11,rep,name=goods,proto3" json:"goods"`
	// 地址及物流信息
	Address *OrderAddressData `protobuf:"bytes,12,opt,name=address,proto3" json:"address"`
	// 总积分
	Total int32 `protobuf:"varint,13,opt,name=total,proto3" json:"total"`
	// 完成时间
	FinnshedTime string `protobuf:"bytes,14,opt,name=finnshed_time,json=finnshedTime,proto3" json:"finnshed_time"`
	// 商品类型 1第三方商品(属type=1) 2自有商品(属type=1) 3门店卷(属type=3) 4:商城券（属type=4）
	GoodType             int32    `protobuf:"varint,15,opt,name=good_type,json=goodType,proto3" json:"good_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListData) Reset()         { *m = OrderListData{} }
func (m *OrderListData) String() string { return proto.CompactTextString(m) }
func (*OrderListData) ProtoMessage()    {}
func (*OrderListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{7}
}

func (m *OrderListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListData.Unmarshal(m, b)
}
func (m *OrderListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListData.Marshal(b, m, deterministic)
}
func (m *OrderListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListData.Merge(m, src)
}
func (m *OrderListData) XXX_Size() int {
	return xxx_messageInfo_OrderListData.Size(m)
}
func (m *OrderListData) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListData.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListData proto.InternalMessageInfo

func (m *OrderListData) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *OrderListData) GetAddTime() string {
	if m != nil {
		return m.AddTime
	}
	return ""
}

func (m *OrderListData) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

func (m *OrderListData) GetMemberName() string {
	if m != nil {
		return m.MemberName
	}
	return ""
}

func (m *OrderListData) GetMemberMobile() string {
	if m != nil {
		return m.MemberMobile
	}
	return ""
}

func (m *OrderListData) GetEncryptMobile() string {
	if m != nil {
		return m.EncryptMobile
	}
	return ""
}

func (m *OrderListData) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *OrderListData) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *OrderListData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *OrderListData) GetStateText() string {
	if m != nil {
		return m.StateText
	}
	return ""
}

func (m *OrderListData) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *OrderListData) GetGoods() []*GoodsListData {
	if m != nil {
		return m.Goods
	}
	return nil
}

func (m *OrderListData) GetAddress() *OrderAddressData {
	if m != nil {
		return m.Address
	}
	return nil
}

func (m *OrderListData) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *OrderListData) GetFinnshedTime() string {
	if m != nil {
		return m.FinnshedTime
	}
	return ""
}

func (m *OrderListData) GetGoodType() int32 {
	if m != nil {
		return m.GoodType
	}
	return 0
}

type OrderListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*OrderListData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListResponse) Reset()         { *m = OrderListResponse{} }
func (m *OrderListResponse) String() string { return proto.CompactTextString(m) }
func (*OrderListResponse) ProtoMessage()    {}
func (*OrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{8}
}

func (m *OrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListResponse.Unmarshal(m, b)
}
func (m *OrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListResponse.Marshal(b, m, deterministic)
}
func (m *OrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListResponse.Merge(m, src)
}
func (m *OrderListResponse) XXX_Size() int {
	return xxx_messageInfo_OrderListResponse.Size(m)
}
func (m *OrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListResponse proto.InternalMessageInfo

func (m *OrderListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *OrderListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *OrderListResponse) GetData() []*OrderListData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *OrderListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type OrderCouponData struct {
	// 截至时间
	EndTime string `protobuf:"bytes,1,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 截至日期，精确到日
	EndDate string `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	// 兑换码
	Code                 string   `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderCouponData) Reset()         { *m = OrderCouponData{} }
func (m *OrderCouponData) String() string { return proto.CompactTextString(m) }
func (*OrderCouponData) ProtoMessage()    {}
func (*OrderCouponData) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{9}
}

func (m *OrderCouponData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderCouponData.Unmarshal(m, b)
}
func (m *OrderCouponData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderCouponData.Marshal(b, m, deterministic)
}
func (m *OrderCouponData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderCouponData.Merge(m, src)
}
func (m *OrderCouponData) XXX_Size() int {
	return xxx_messageInfo_OrderCouponData.Size(m)
}
func (m *OrderCouponData) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderCouponData.DiscardUnknown(m)
}

var xxx_messageInfo_OrderCouponData proto.InternalMessageInfo

func (m *OrderCouponData) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *OrderCouponData) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *OrderCouponData) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

type OrderVerifyCodeData struct {
	// 截至日期
	EndTime string `protobuf:"bytes,1,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 截至日期，精确到日
	EndDate string `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date"`
	// 核销码
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	// 当前状态 0:未使用 1:已使用 2:已过期
	State int32 `protobuf:"varint,4,opt,name=state,proto3" json:"state"`
	// 核销时间
	UseTime string `protobuf:"bytes,5,opt,name=use_time,json=useTime,proto3" json:"use_time"`
	// 核销门店
	ChainName            string   `protobuf:"bytes,6,opt,name=chain_name,json=chainName,proto3" json:"chain_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderVerifyCodeData) Reset()         { *m = OrderVerifyCodeData{} }
func (m *OrderVerifyCodeData) String() string { return proto.CompactTextString(m) }
func (*OrderVerifyCodeData) ProtoMessage()    {}
func (*OrderVerifyCodeData) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{10}
}

func (m *OrderVerifyCodeData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderVerifyCodeData.Unmarshal(m, b)
}
func (m *OrderVerifyCodeData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderVerifyCodeData.Marshal(b, m, deterministic)
}
func (m *OrderVerifyCodeData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderVerifyCodeData.Merge(m, src)
}
func (m *OrderVerifyCodeData) XXX_Size() int {
	return xxx_messageInfo_OrderVerifyCodeData.Size(m)
}
func (m *OrderVerifyCodeData) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderVerifyCodeData.DiscardUnknown(m)
}

var xxx_messageInfo_OrderVerifyCodeData proto.InternalMessageInfo

func (m *OrderVerifyCodeData) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *OrderVerifyCodeData) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *OrderVerifyCodeData) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *OrderVerifyCodeData) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *OrderVerifyCodeData) GetUseTime() string {
	if m != nil {
		return m.UseTime
	}
	return ""
}

func (m *OrderVerifyCodeData) GetChainName() string {
	if m != nil {
		return m.ChainName
	}
	return ""
}

type OrderStepData struct {
	// 时间
	Time string `protobuf:"bytes,1,opt,name=time,proto3" json:"time"`
	// 描述
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderStepData) Reset()         { *m = OrderStepData{} }
func (m *OrderStepData) String() string { return proto.CompactTextString(m) }
func (*OrderStepData) ProtoMessage()    {}
func (*OrderStepData) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{11}
}

func (m *OrderStepData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderStepData.Unmarshal(m, b)
}
func (m *OrderStepData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderStepData.Marshal(b, m, deterministic)
}
func (m *OrderStepData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderStepData.Merge(m, src)
}
func (m *OrderStepData) XXX_Size() int {
	return xxx_messageInfo_OrderStepData.Size(m)
}
func (m *OrderStepData) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderStepData.DiscardUnknown(m)
}

var xxx_messageInfo_OrderStepData proto.InternalMessageInfo

func (m *OrderStepData) GetTime() string {
	if m != nil {
		return m.Time
	}
	return ""
}

func (m *OrderStepData) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type OrderDetailResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 订单信息
	Order *OrderListData `protobuf:"bytes,3,opt,name=order,proto3" json:"order"`
	// 兑换信息，仅当type=3有效
	Coupon *OrderCouponData `protobuf:"bytes,4,opt,name=coupon,proto3" json:"coupon"`
	// 核销码，仅当type=2有效
	VerifyCode *OrderVerifyCodeData `protobuf:"bytes,5,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code"`
	// 脚印信息
	Steps                []*OrderStepData `protobuf:"bytes,6,rep,name=steps,proto3" json:"steps"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *OrderDetailResponse) Reset()         { *m = OrderDetailResponse{} }
func (m *OrderDetailResponse) String() string { return proto.CompactTextString(m) }
func (*OrderDetailResponse) ProtoMessage()    {}
func (*OrderDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{12}
}

func (m *OrderDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderDetailResponse.Unmarshal(m, b)
}
func (m *OrderDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderDetailResponse.Marshal(b, m, deterministic)
}
func (m *OrderDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderDetailResponse.Merge(m, src)
}
func (m *OrderDetailResponse) XXX_Size() int {
	return xxx_messageInfo_OrderDetailResponse.Size(m)
}
func (m *OrderDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderDetailResponse proto.InternalMessageInfo

func (m *OrderDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *OrderDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *OrderDetailResponse) GetOrder() *OrderListData {
	if m != nil {
		return m.Order
	}
	return nil
}

func (m *OrderDetailResponse) GetCoupon() *OrderCouponData {
	if m != nil {
		return m.Coupon
	}
	return nil
}

func (m *OrderDetailResponse) GetVerifyCode() *OrderVerifyCodeData {
	if m != nil {
		return m.VerifyCode
	}
	return nil
}

func (m *OrderDetailResponse) GetSteps() []*OrderStepData {
	if m != nil {
		return m.Steps
	}
	return nil
}

type OrderExportListRequest struct {
	// 页码，不传默认为1
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页数量，不传默认10
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderExportListRequest) Reset()         { *m = OrderExportListRequest{} }
func (m *OrderExportListRequest) String() string { return proto.CompactTextString(m) }
func (*OrderExportListRequest) ProtoMessage()    {}
func (*OrderExportListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{13}
}

func (m *OrderExportListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderExportListRequest.Unmarshal(m, b)
}
func (m *OrderExportListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderExportListRequest.Marshal(b, m, deterministic)
}
func (m *OrderExportListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderExportListRequest.Merge(m, src)
}
func (m *OrderExportListRequest) XXX_Size() int {
	return xxx_messageInfo_OrderExportListRequest.Size(m)
}
func (m *OrderExportListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderExportListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderExportListRequest proto.InternalMessageInfo

func (m *OrderExportListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *OrderExportListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type OrderExportListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                          `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*OrderExportListResponse_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderExportListResponse) Reset()         { *m = OrderExportListResponse{} }
func (m *OrderExportListResponse) String() string { return proto.CompactTextString(m) }
func (*OrderExportListResponse) ProtoMessage()    {}
func (*OrderExportListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{14}
}

func (m *OrderExportListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderExportListResponse.Unmarshal(m, b)
}
func (m *OrderExportListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderExportListResponse.Marshal(b, m, deterministic)
}
func (m *OrderExportListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderExportListResponse.Merge(m, src)
}
func (m *OrderExportListResponse) XXX_Size() int {
	return xxx_messageInfo_OrderExportListResponse.Size(m)
}
func (m *OrderExportListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderExportListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderExportListResponse proto.InternalMessageInfo

func (m *OrderExportListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *OrderExportListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *OrderExportListResponse) GetData() []*OrderExportListResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *OrderExportListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type OrderExportListResponse_List struct {
	// 操作时间
	CreatedAt string `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 状态 0处理中、1成功、2失败
	State int32 `protobuf:"varint,2,opt,name=state,proto3" json:"state"`
	// 状态文本
	StateText string `protobuf:"bytes,3,opt,name=state_text,json=stateText,proto3" json:"state_text"`
	// 当state = 1时返回链接
	Url string `protobuf:"bytes,4,opt,name=url,proto3" json:"url"`
	// 当state = 2返回失败原因
	Result               string   `protobuf:"bytes,5,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderExportListResponse_List) Reset()         { *m = OrderExportListResponse_List{} }
func (m *OrderExportListResponse_List) String() string { return proto.CompactTextString(m) }
func (*OrderExportListResponse_List) ProtoMessage()    {}
func (*OrderExportListResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{14, 0}
}

func (m *OrderExportListResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderExportListResponse_List.Unmarshal(m, b)
}
func (m *OrderExportListResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderExportListResponse_List.Marshal(b, m, deterministic)
}
func (m *OrderExportListResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderExportListResponse_List.Merge(m, src)
}
func (m *OrderExportListResponse_List) XXX_Size() int {
	return xxx_messageInfo_OrderExportListResponse_List.Size(m)
}
func (m *OrderExportListResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderExportListResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_OrderExportListResponse_List proto.InternalMessageInfo

func (m *OrderExportListResponse_List) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *OrderExportListResponse_List) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *OrderExportListResponse_List) GetStateText() string {
	if m != nil {
		return m.StateText
	}
	return ""
}

func (m *OrderExportListResponse_List) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *OrderExportListResponse_List) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

type ExpressCompaniesResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string                           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*ExpressCompaniesResponse_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *ExpressCompaniesResponse) Reset()         { *m = ExpressCompaniesResponse{} }
func (m *ExpressCompaniesResponse) String() string { return proto.CompactTextString(m) }
func (*ExpressCompaniesResponse) ProtoMessage()    {}
func (*ExpressCompaniesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{15}
}

func (m *ExpressCompaniesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressCompaniesResponse.Unmarshal(m, b)
}
func (m *ExpressCompaniesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressCompaniesResponse.Marshal(b, m, deterministic)
}
func (m *ExpressCompaniesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressCompaniesResponse.Merge(m, src)
}
func (m *ExpressCompaniesResponse) XXX_Size() int {
	return xxx_messageInfo_ExpressCompaniesResponse.Size(m)
}
func (m *ExpressCompaniesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressCompaniesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressCompaniesResponse proto.InternalMessageInfo

func (m *ExpressCompaniesResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ExpressCompaniesResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ExpressCompaniesResponse) GetData() []*ExpressCompaniesResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

type ExpressCompaniesResponse_List struct {
	// 快递公司代码
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	// 快递公司名称
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpressCompaniesResponse_List) Reset()         { *m = ExpressCompaniesResponse_List{} }
func (m *ExpressCompaniesResponse_List) String() string { return proto.CompactTextString(m) }
func (*ExpressCompaniesResponse_List) ProtoMessage()    {}
func (*ExpressCompaniesResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{15, 0}
}

func (m *ExpressCompaniesResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressCompaniesResponse_List.Unmarshal(m, b)
}
func (m *ExpressCompaniesResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressCompaniesResponse_List.Marshal(b, m, deterministic)
}
func (m *ExpressCompaniesResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressCompaniesResponse_List.Merge(m, src)
}
func (m *ExpressCompaniesResponse_List) XXX_Size() int {
	return xxx_messageInfo_ExpressCompaniesResponse_List.Size(m)
}
func (m *ExpressCompaniesResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressCompaniesResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressCompaniesResponse_List proto.InternalMessageInfo

func (m *ExpressCompaniesResponse_List) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *ExpressCompaniesResponse_List) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type ExpressStoreRequest struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 物流单号
	ShippingCode string `protobuf:"bytes,2,opt,name=shipping_code,json=shippingCode,proto3" json:"shipping_code"`
	// 物流公司编码
	ShippingEcode        string   `protobuf:"bytes,3,opt,name=shipping_ecode,json=shippingEcode,proto3" json:"shipping_ecode"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpressStoreRequest) Reset()         { *m = ExpressStoreRequest{} }
func (m *ExpressStoreRequest) String() string { return proto.CompactTextString(m) }
func (*ExpressStoreRequest) ProtoMessage()    {}
func (*ExpressStoreRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{16}
}

func (m *ExpressStoreRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressStoreRequest.Unmarshal(m, b)
}
func (m *ExpressStoreRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressStoreRequest.Marshal(b, m, deterministic)
}
func (m *ExpressStoreRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressStoreRequest.Merge(m, src)
}
func (m *ExpressStoreRequest) XXX_Size() int {
	return xxx_messageInfo_ExpressStoreRequest.Size(m)
}
func (m *ExpressStoreRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressStoreRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressStoreRequest proto.InternalMessageInfo

func (m *ExpressStoreRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *ExpressStoreRequest) GetShippingCode() string {
	if m != nil {
		return m.ShippingCode
	}
	return ""
}

func (m *ExpressStoreRequest) GetShippingEcode() string {
	if m != nil {
		return m.ShippingEcode
	}
	return ""
}

// 批量导入物流模板
type ExpressImportTemplateResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 文件字节流
	Template             []byte   `protobuf:"bytes,3,opt,name=template,proto3" json:"template"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpressImportTemplateResponse) Reset()         { *m = ExpressImportTemplateResponse{} }
func (m *ExpressImportTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*ExpressImportTemplateResponse) ProtoMessage()    {}
func (*ExpressImportTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{17}
}

func (m *ExpressImportTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressImportTemplateResponse.Unmarshal(m, b)
}
func (m *ExpressImportTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressImportTemplateResponse.Marshal(b, m, deterministic)
}
func (m *ExpressImportTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressImportTemplateResponse.Merge(m, src)
}
func (m *ExpressImportTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_ExpressImportTemplateResponse.Size(m)
}
func (m *ExpressImportTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressImportTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressImportTemplateResponse proto.InternalMessageInfo

func (m *ExpressImportTemplateResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ExpressImportTemplateResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ExpressImportTemplateResponse) GetTemplate() []byte {
	if m != nil {
		return m.Template
	}
	return nil
}

type ExpressImportRequest struct {
	// 文件字节流
	File                 []byte   `protobuf:"bytes,1,opt,name=file,proto3" json:"file"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpressImportRequest) Reset()         { *m = ExpressImportRequest{} }
func (m *ExpressImportRequest) String() string { return proto.CompactTextString(m) }
func (*ExpressImportRequest) ProtoMessage()    {}
func (*ExpressImportRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{18}
}

func (m *ExpressImportRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressImportRequest.Unmarshal(m, b)
}
func (m *ExpressImportRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressImportRequest.Marshal(b, m, deterministic)
}
func (m *ExpressImportRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressImportRequest.Merge(m, src)
}
func (m *ExpressImportRequest) XXX_Size() int {
	return xxx_messageInfo_ExpressImportRequest.Size(m)
}
func (m *ExpressImportRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressImportRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressImportRequest proto.InternalMessageInfo

func (m *ExpressImportRequest) GetFile() []byte {
	if m != nil {
		return m.File
	}
	return nil
}

type ExpressImportListRequest struct {
	// 页码，不传默认为1
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页数量，不传默认10
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpressImportListRequest) Reset()         { *m = ExpressImportListRequest{} }
func (m *ExpressImportListRequest) String() string { return proto.CompactTextString(m) }
func (*ExpressImportListRequest) ProtoMessage()    {}
func (*ExpressImportListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{19}
}

func (m *ExpressImportListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressImportListRequest.Unmarshal(m, b)
}
func (m *ExpressImportListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressImportListRequest.Marshal(b, m, deterministic)
}
func (m *ExpressImportListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressImportListRequest.Merge(m, src)
}
func (m *ExpressImportListRequest) XXX_Size() int {
	return xxx_messageInfo_ExpressImportListRequest.Size(m)
}
func (m *ExpressImportListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressImportListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressImportListRequest proto.InternalMessageInfo

func (m *ExpressImportListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ExpressImportListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ExpressImportListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                            `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*ExpressImportListResponse_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpressImportListResponse) Reset()         { *m = ExpressImportListResponse{} }
func (m *ExpressImportListResponse) String() string { return proto.CompactTextString(m) }
func (*ExpressImportListResponse) ProtoMessage()    {}
func (*ExpressImportListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{20}
}

func (m *ExpressImportListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressImportListResponse.Unmarshal(m, b)
}
func (m *ExpressImportListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressImportListResponse.Marshal(b, m, deterministic)
}
func (m *ExpressImportListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressImportListResponse.Merge(m, src)
}
func (m *ExpressImportListResponse) XXX_Size() int {
	return xxx_messageInfo_ExpressImportListResponse.Size(m)
}
func (m *ExpressImportListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressImportListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressImportListResponse proto.InternalMessageInfo

func (m *ExpressImportListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ExpressImportListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ExpressImportListResponse) GetData() []*ExpressImportListResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ExpressImportListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type ExpressImportListResponse_List struct {
	// 操作时间
	CreatedAt string `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 结果url
	Url string `protobuf:"bytes,4,opt,name=url,proto3" json:"url"`
	// 导入结果文本
	Result               string   `protobuf:"bytes,5,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpressImportListResponse_List) Reset()         { *m = ExpressImportListResponse_List{} }
func (m *ExpressImportListResponse_List) String() string { return proto.CompactTextString(m) }
func (*ExpressImportListResponse_List) ProtoMessage()    {}
func (*ExpressImportListResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{20, 0}
}

func (m *ExpressImportListResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressImportListResponse_List.Unmarshal(m, b)
}
func (m *ExpressImportListResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressImportListResponse_List.Marshal(b, m, deterministic)
}
func (m *ExpressImportListResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressImportListResponse_List.Merge(m, src)
}
func (m *ExpressImportListResponse_List) XXX_Size() int {
	return xxx_messageInfo_ExpressImportListResponse_List.Size(m)
}
func (m *ExpressImportListResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressImportListResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressImportListResponse_List proto.InternalMessageInfo

func (m *ExpressImportListResponse_List) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *ExpressImportListResponse_List) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ExpressImportListResponse_List) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

type AWOrderStoreRequest struct {
	// 商品id
	GoodsId int32 `protobuf:"varint,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	// 地址id，仅实物订单需要
	AddressId int32 `protobuf:"varint,2,opt,name=address_id,json=addressId,proto3" json:"address_id"`
	// 前端不需要传
	ScrmId string `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	//机构ID
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWOrderStoreRequest) Reset()         { *m = AWOrderStoreRequest{} }
func (m *AWOrderStoreRequest) String() string { return proto.CompactTextString(m) }
func (*AWOrderStoreRequest) ProtoMessage()    {}
func (*AWOrderStoreRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{21}
}

func (m *AWOrderStoreRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWOrderStoreRequest.Unmarshal(m, b)
}
func (m *AWOrderStoreRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWOrderStoreRequest.Marshal(b, m, deterministic)
}
func (m *AWOrderStoreRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWOrderStoreRequest.Merge(m, src)
}
func (m *AWOrderStoreRequest) XXX_Size() int {
	return xxx_messageInfo_AWOrderStoreRequest.Size(m)
}
func (m *AWOrderStoreRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AWOrderStoreRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AWOrderStoreRequest proto.InternalMessageInfo

func (m *AWOrderStoreRequest) GetGoodsId() int32 {
	if m != nil {
		return m.GoodsId
	}
	return 0
}

func (m *AWOrderStoreRequest) GetAddressId() int32 {
	if m != nil {
		return m.AddressId
	}
	return 0
}

func (m *AWOrderStoreRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *AWOrderStoreRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type AWOrderListRequest struct {
	// 商品分类 1实物、2虚拟、3优惠券
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type"`
	// 前端不需要传
	ScrmId string `protobuf:"bytes,5,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 每页数量
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 当前页码
	PageIndex            int32    `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWOrderListRequest) Reset()         { *m = AWOrderListRequest{} }
func (m *AWOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*AWOrderListRequest) ProtoMessage()    {}
func (*AWOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{22}
}

func (m *AWOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWOrderListRequest.Unmarshal(m, b)
}
func (m *AWOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWOrderListRequest.Marshal(b, m, deterministic)
}
func (m *AWOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWOrderListRequest.Merge(m, src)
}
func (m *AWOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_AWOrderListRequest.Size(m)
}
func (m *AWOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AWOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AWOrderListRequest proto.InternalMessageInfo

func (m *AWOrderListRequest) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *AWOrderListRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *AWOrderListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *AWOrderListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type AWOrderListResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 订单信息
	Data []*OrderListData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWOrderListResponse) Reset()         { *m = AWOrderListResponse{} }
func (m *AWOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*AWOrderListResponse) ProtoMessage()    {}
func (*AWOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{23}
}

func (m *AWOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWOrderListResponse.Unmarshal(m, b)
}
func (m *AWOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWOrderListResponse.Marshal(b, m, deterministic)
}
func (m *AWOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWOrderListResponse.Merge(m, src)
}
func (m *AWOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_AWOrderListResponse.Size(m)
}
func (m *AWOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AWOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AWOrderListResponse proto.InternalMessageInfo

func (m *AWOrderListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AWOrderListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AWOrderListResponse) GetData() []*OrderListData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *AWOrderListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type AWOrderDetailResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 订单信息
	Data *OrderListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	// 兑换信息，仅当type=3有效
	Coupon *OrderCouponData `protobuf:"bytes,4,opt,name=coupon,proto3" json:"coupon"`
	// 核销码，仅当type=2有效
	VerifyCode           *OrderVerifyCodeData `protobuf:"bytes,5,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AWOrderDetailResponse) Reset()         { *m = AWOrderDetailResponse{} }
func (m *AWOrderDetailResponse) String() string { return proto.CompactTextString(m) }
func (*AWOrderDetailResponse) ProtoMessage()    {}
func (*AWOrderDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{24}
}

func (m *AWOrderDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWOrderDetailResponse.Unmarshal(m, b)
}
func (m *AWOrderDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWOrderDetailResponse.Marshal(b, m, deterministic)
}
func (m *AWOrderDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWOrderDetailResponse.Merge(m, src)
}
func (m *AWOrderDetailResponse) XXX_Size() int {
	return xxx_messageInfo_AWOrderDetailResponse.Size(m)
}
func (m *AWOrderDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AWOrderDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AWOrderDetailResponse proto.InternalMessageInfo

func (m *AWOrderDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AWOrderDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AWOrderDetailResponse) GetData() *OrderListData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *AWOrderDetailResponse) GetCoupon() *OrderCouponData {
	if m != nil {
		return m.Coupon
	}
	return nil
}

func (m *AWOrderDetailResponse) GetVerifyCode() *OrderVerifyCodeData {
	if m != nil {
		return m.VerifyCode
	}
	return nil
}

type OrderRequest struct {
	// 订单编号
	OrderSn              string   `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderRequest) Reset()         { *m = OrderRequest{} }
func (m *OrderRequest) String() string { return proto.CompactTextString(m) }
func (*OrderRequest) ProtoMessage()    {}
func (*OrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{25}
}

func (m *OrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderRequest.Unmarshal(m, b)
}
func (m *OrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderRequest.Marshal(b, m, deterministic)
}
func (m *OrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderRequest.Merge(m, src)
}
func (m *OrderRequest) XXX_Size() int {
	return xxx_messageInfo_OrderRequest.Size(m)
}
func (m *OrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderRequest proto.InternalMessageInfo

func (m *OrderRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type AWOrderRequest struct {
	// 订单编号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 前端不需要传
	ScrmId               string   `protobuf:"bytes,2,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWOrderRequest) Reset()         { *m = AWOrderRequest{} }
func (m *AWOrderRequest) String() string { return proto.CompactTextString(m) }
func (*AWOrderRequest) ProtoMessage()    {}
func (*AWOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{26}
}

func (m *AWOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWOrderRequest.Unmarshal(m, b)
}
func (m *AWOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWOrderRequest.Marshal(b, m, deterministic)
}
func (m *AWOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWOrderRequest.Merge(m, src)
}
func (m *AWOrderRequest) XXX_Size() int {
	return xxx_messageInfo_AWOrderRequest.Size(m)
}
func (m *AWOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AWOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AWOrderRequest proto.InternalMessageInfo

func (m *AWOrderRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *AWOrderRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type AWDeliveryDetailResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 快递公司
	ExpressName string `protobuf:"bytes,3,opt,name=express_name,json=expressName,proto3" json:"express_name"`
	// 快递单号
	ShippingCode         string                           `protobuf:"bytes,4,opt,name=shipping_code,json=shippingCode,proto3" json:"shipping_code"`
	Status               string                           `protobuf:"bytes,5,opt,name=status,proto3" json:"status"`
	Msg                  string                           `protobuf:"bytes,6,opt,name=msg,proto3" json:"msg"`
	Result               *AWDeliveryDetailResponse_Result `protobuf:"bytes,7,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *AWDeliveryDetailResponse) Reset()         { *m = AWDeliveryDetailResponse{} }
func (m *AWDeliveryDetailResponse) String() string { return proto.CompactTextString(m) }
func (*AWDeliveryDetailResponse) ProtoMessage()    {}
func (*AWDeliveryDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{27}
}

func (m *AWDeliveryDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWDeliveryDetailResponse.Unmarshal(m, b)
}
func (m *AWDeliveryDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWDeliveryDetailResponse.Marshal(b, m, deterministic)
}
func (m *AWDeliveryDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWDeliveryDetailResponse.Merge(m, src)
}
func (m *AWDeliveryDetailResponse) XXX_Size() int {
	return xxx_messageInfo_AWDeliveryDetailResponse.Size(m)
}
func (m *AWDeliveryDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AWDeliveryDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AWDeliveryDetailResponse proto.InternalMessageInfo

func (m *AWDeliveryDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AWDeliveryDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AWDeliveryDetailResponse) GetExpressName() string {
	if m != nil {
		return m.ExpressName
	}
	return ""
}

func (m *AWDeliveryDetailResponse) GetShippingCode() string {
	if m != nil {
		return m.ShippingCode
	}
	return ""
}

func (m *AWDeliveryDetailResponse) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *AWDeliveryDetailResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *AWDeliveryDetailResponse) GetResult() *AWDeliveryDetailResponse_Result {
	if m != nil {
		return m.Result
	}
	return nil
}

type AWDeliveryDetailResponse_Node struct {
	// 发生时间
	Time string `protobuf:"bytes,1,opt,name=time,proto3" json:"time"`
	// 流转描述
	Status               string   `protobuf:"bytes,2,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWDeliveryDetailResponse_Node) Reset()         { *m = AWDeliveryDetailResponse_Node{} }
func (m *AWDeliveryDetailResponse_Node) String() string { return proto.CompactTextString(m) }
func (*AWDeliveryDetailResponse_Node) ProtoMessage()    {}
func (*AWDeliveryDetailResponse_Node) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{27, 0}
}

func (m *AWDeliveryDetailResponse_Node) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWDeliveryDetailResponse_Node.Unmarshal(m, b)
}
func (m *AWDeliveryDetailResponse_Node) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWDeliveryDetailResponse_Node.Marshal(b, m, deterministic)
}
func (m *AWDeliveryDetailResponse_Node) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWDeliveryDetailResponse_Node.Merge(m, src)
}
func (m *AWDeliveryDetailResponse_Node) XXX_Size() int {
	return xxx_messageInfo_AWDeliveryDetailResponse_Node.Size(m)
}
func (m *AWDeliveryDetailResponse_Node) XXX_DiscardUnknown() {
	xxx_messageInfo_AWDeliveryDetailResponse_Node.DiscardUnknown(m)
}

var xxx_messageInfo_AWDeliveryDetailResponse_Node proto.InternalMessageInfo

func (m *AWDeliveryDetailResponse_Node) GetTime() string {
	if m != nil {
		return m.Time
	}
	return ""
}

func (m *AWDeliveryDetailResponse_Node) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type AWDeliveryDetailResponse_Result struct {
	Type   string `protobuf:"bytes,3,opt,name=type,proto3" json:"type"`
	Number string `protobuf:"bytes,4,opt,name=number,proto3" json:"number"`
	//配送流程节点
	List []*AWDeliveryDetailResponse_Node `protobuf:"bytes,5,rep,name=list,proto3" json:"list"`
	//阿里云返回的物流名称
	ExpName              string   `protobuf:"bytes,6,opt,name=expName,proto3" json:"expName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AWDeliveryDetailResponse_Result) Reset()         { *m = AWDeliveryDetailResponse_Result{} }
func (m *AWDeliveryDetailResponse_Result) String() string { return proto.CompactTextString(m) }
func (*AWDeliveryDetailResponse_Result) ProtoMessage()    {}
func (*AWDeliveryDetailResponse_Result) Descriptor() ([]byte, []int) {
	return fileDescriptor_23af842d07abbb99, []int{27, 1}
}

func (m *AWDeliveryDetailResponse_Result) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AWDeliveryDetailResponse_Result.Unmarshal(m, b)
}
func (m *AWDeliveryDetailResponse_Result) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AWDeliveryDetailResponse_Result.Marshal(b, m, deterministic)
}
func (m *AWDeliveryDetailResponse_Result) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AWDeliveryDetailResponse_Result.Merge(m, src)
}
func (m *AWDeliveryDetailResponse_Result) XXX_Size() int {
	return xxx_messageInfo_AWDeliveryDetailResponse_Result.Size(m)
}
func (m *AWDeliveryDetailResponse_Result) XXX_DiscardUnknown() {
	xxx_messageInfo_AWDeliveryDetailResponse_Result.DiscardUnknown(m)
}

var xxx_messageInfo_AWDeliveryDetailResponse_Result proto.InternalMessageInfo

func (m *AWDeliveryDetailResponse_Result) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *AWDeliveryDetailResponse_Result) GetNumber() string {
	if m != nil {
		return m.Number
	}
	return ""
}

func (m *AWDeliveryDetailResponse_Result) GetList() []*AWDeliveryDetailResponse_Node {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *AWDeliveryDetailResponse_Result) GetExpName() string {
	if m != nil {
		return m.ExpName
	}
	return ""
}

func init() {
	proto.RegisterType((*IntegralChangeMessageRequest)(nil), "proto.IntegralChangeMessageRequest")
	proto.RegisterType((*OrderEmptyRequest)(nil), "proto.OrderEmptyRequest")
	proto.RegisterType((*OrderResponse)(nil), "proto.OrderResponse")
	proto.RegisterType((*OrderExpressRequest)(nil), "proto.OrderExpressRequest")
	proto.RegisterType((*OrderListRequest)(nil), "proto.OrderListRequest")
	proto.RegisterType((*GoodsListData)(nil), "proto.GoodsListData")
	proto.RegisterType((*OrderAddressData)(nil), "proto.OrderAddressData")
	proto.RegisterType((*OrderListData)(nil), "proto.OrderListData")
	proto.RegisterType((*OrderListResponse)(nil), "proto.OrderListResponse")
	proto.RegisterType((*OrderCouponData)(nil), "proto.OrderCouponData")
	proto.RegisterType((*OrderVerifyCodeData)(nil), "proto.OrderVerifyCodeData")
	proto.RegisterType((*OrderStepData)(nil), "proto.OrderStepData")
	proto.RegisterType((*OrderDetailResponse)(nil), "proto.OrderDetailResponse")
	proto.RegisterType((*OrderExportListRequest)(nil), "proto.OrderExportListRequest")
	proto.RegisterType((*OrderExportListResponse)(nil), "proto.OrderExportListResponse")
	proto.RegisterType((*OrderExportListResponse_List)(nil), "proto.OrderExportListResponse.List")
	proto.RegisterType((*ExpressCompaniesResponse)(nil), "proto.ExpressCompaniesResponse")
	proto.RegisterType((*ExpressCompaniesResponse_List)(nil), "proto.ExpressCompaniesResponse.List")
	proto.RegisterType((*ExpressStoreRequest)(nil), "proto.ExpressStoreRequest")
	proto.RegisterType((*ExpressImportTemplateResponse)(nil), "proto.ExpressImportTemplateResponse")
	proto.RegisterType((*ExpressImportRequest)(nil), "proto.ExpressImportRequest")
	proto.RegisterType((*ExpressImportListRequest)(nil), "proto.ExpressImportListRequest")
	proto.RegisterType((*ExpressImportListResponse)(nil), "proto.ExpressImportListResponse")
	proto.RegisterType((*ExpressImportListResponse_List)(nil), "proto.ExpressImportListResponse.List")
	proto.RegisterType((*AWOrderStoreRequest)(nil), "proto.AWOrderStoreRequest")
	proto.RegisterType((*AWOrderListRequest)(nil), "proto.AWOrderListRequest")
	proto.RegisterType((*AWOrderListResponse)(nil), "proto.AWOrderListResponse")
	proto.RegisterType((*AWOrderDetailResponse)(nil), "proto.AWOrderDetailResponse")
	proto.RegisterType((*OrderRequest)(nil), "proto.OrderRequest")
	proto.RegisterType((*AWOrderRequest)(nil), "proto.AWOrderRequest")
	proto.RegisterType((*AWDeliveryDetailResponse)(nil), "proto.AWDeliveryDetailResponse")
	proto.RegisterType((*AWDeliveryDetailResponse_Node)(nil), "proto.AWDeliveryDetailResponse.Node")
	proto.RegisterType((*AWDeliveryDetailResponse_Result)(nil), "proto.AWDeliveryDetailResponse.Result")
}

func init() { proto.RegisterFile("igc/order.proto", fileDescriptor_23af842d07abbb99) }

var fileDescriptor_23af842d07abbb99 = []byte{
	// 1774 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0xcd, 0x6e, 0xdb, 0xc6,
	0x16, 0x36, 0x29, 0x51, 0x96, 0x8e, 0xfc, 0x97, 0xf1, 0x9f, 0x2c, 0xc7, 0xd7, 0xbe, 0x4c, 0x72,
	0xaf, 0x6f, 0x16, 0xbe, 0xa8, 0xbb, 0x48, 0x03, 0xa3, 0x0d, 0x1c, 0x3b, 0x28, 0x54, 0xd4, 0x09,
	0x40, 0x1b, 0x31, 0xda, 0x2e, 0x04, 0x46, 0x1c, 0xcb, 0x44, 0xc5, 0x9f, 0x70, 0x46, 0x86, 0x1d,
	0xb4, 0x28, 0x8a, 0x76, 0x91, 0x97, 0x28, 0xd0, 0x37, 0x28, 0x10, 0x74, 0xd3, 0x17, 0xe8, 0x43,
	0x14, 0x7d, 0x83, 0x3e, 0x40, 0xb7, 0xc5, 0x9c, 0x99, 0xa1, 0x48, 0x49, 0xb4, 0x0d, 0x27, 0x68,
	0x57, 0x9a, 0x39, 0x3c, 0x73, 0xe6, 0xfc, 0x7c, 0xe7, 0x67, 0x04, 0xb3, 0x7e, 0xb7, 0xf3, 0xff,
	0x28, 0xf1, 0x68, 0xb2, 0x15, 0x27, 0x11, 0x8f, 0x88, 0x85, 0x3f, 0xf6, 0x8f, 0x06, 0xdc, 0x6e,
	0x85, 0x9c, 0x76, 0x13, 0xb7, 0xb7, 0x77, 0xea, 0x86, 0x5d, 0x7a, 0x40, 0x19, 0x73, 0xbb, 0xd4,
	0xa1, 0x2f, 0xfb, 0x94, 0x71, 0xb2, 0x0a, 0xb5, 0x80, 0x06, 0x2f, 0x68, 0xd2, 0xf6, 0xbd, 0x86,
	0xb1, 0x61, 0x6c, 0xd6, 0x9c, 0xaa, 0x24, 0xb4, 0x3c, 0x72, 0x07, 0xa6, 0x7d, 0x75, 0xb8, 0xcd,
	0x2f, 0x62, 0xda, 0x30, 0x37, 0x8c, 0x4d, 0xcb, 0x99, 0xd2, 0xc4, 0xa3, 0x8b, 0x98, 0x92, 0xff,
	0xc2, 0xac, 0x1f, 0x32, 0x9a, 0xf0, 0xb6, 0x26, 0x37, 0x4a, 0xc8, 0x36, 0x23, 0xc9, 0xfa, 0x7a,
	0xb2, 0x08, 0x95, 0x28, 0xe9, 0x8a, 0x7b, 0xca, 0xf8, 0xdd, 0x8a, 0x92, 0x6e, 0xcb, 0xb3, 0xe7,
	0xe1, 0xd6, 0x33, 0xa1, 0xf8, 0x93, 0x20, 0xe6, 0x17, 0x4a, 0x2d, 0xfb, 0x43, 0x98, 0x46, 0xa2,
	0x43, 0x59, 0x1c, 0x85, 0x8c, 0x12, 0x02, 0xe5, 0x4e, 0xe4, 0x51, 0x54, 0xd1, 0x72, 0x70, 0x4d,
	0x1a, 0x30, 0x19, 0x48, 0x6b, 0x50, 0xb1, 0x9a, 0xa3, 0xb7, 0x76, 0x0b, 0xe6, 0xa5, 0xcc, 0xf3,
	0x38, 0xa1, 0x8c, 0x69, 0x63, 0x67, 0xc0, 0x54, 0x56, 0x5a, 0x8e, 0xe9, 0x7b, 0x42, 0x68, 0xe8,
	0x06, 0xfa, 0x34, 0xae, 0x05, 0x0f, 0x0b, 0xd1, 0x82, 0x9a, 0x63, 0xb2, 0xd0, 0x7e, 0x63, 0xc2,
	0x1c, 0xca, 0xfa, 0xd4, 0x67, 0x5c, 0x0b, 0x5a, 0x81, 0x2a, 0x3a, 0xbb, 0xcd, 0x42, 0xe5, 0xb4,
	0x49, 0xdc, 0x1f, 0x86, 0x64, 0x09, 0x2a, 0x41, 0xf4, 0xc2, 0xef, 0x69, 0xa9, 0x6a, 0x47, 0xd6,
	0x00, 0x18, 0x77, 0x13, 0xde, 0xf6, 0x5c, 0x4e, 0xd1, 0x03, 0x35, 0xa7, 0x86, 0x94, 0x7d, 0x97,
	0x53, 0x21, 0x91, 0x86, 0x9e, 0xfc, 0x68, 0x49, 0x89, 0x34, 0xf4, 0xf0, 0xd3, 0x02, 0x58, 0x8c,
	0x0b, 0xba, 0x54, 0x4a, 0x6e, 0x84, 0xee, 0x18, 0x92, 0x8a, 0xd4, 0x5d, 0xac, 0x05, 0xed, 0x24,
	0x89, 0x82, 0xc6, 0xa4, 0xa4, 0x89, 0xb5, 0x70, 0x52, 0xe7, 0xd4, 0x0d, 0x43, 0xda, 0x6b, 0x54,
	0xa5, 0x5c, 0xb5, 0x15, 0x1a, 0x75, 0xa3, 0xc8, 0x63, 0x6d, 0xf4, 0x41, 0x4d, 0x6a, 0x84, 0x94,
	0xa7, 0xc2, 0x11, 0xab, 0x50, 0x8b, 0xdd, 0x2e, 0x6d, 0x33, 0xff, 0x15, 0x6d, 0x00, 0xfa, 0xac,
	0x2a, 0x08, 0x87, 0xfe, 0x2b, 0xb4, 0x06, 0x3f, 0xfa, 0xa1, 0x47, 0xcf, 0x1b, 0x75, 0xfc, 0x8a,
	0xec, 0x2d, 0x41, 0xb0, 0x7f, 0x33, 0x60, 0xfa, 0x63, 0x21, 0x49, 0x38, 0x6d, 0xdf, 0xe5, 0x2e,
	0x1e, 0x48, 0x22, 0xaf, 0xdf, 0xe1, 0x02, 0x00, 0x35, 0x75, 0x40, 0x52, 0x5a, 0x5e, 0x26, 0x32,
	0xb5, 0xc2, 0xc8, 0x2c, 0x80, 0x15, 0x27, 0x7e, 0x47, 0xfa, 0xc1, 0x74, 0xe4, 0x46, 0xf8, 0x9b,
	0xd1, 0xc4, 0x77, 0x7b, 0xca, 0xa7, 0x6a, 0x47, 0x9a, 0x50, 0x4d, 0xf1, 0x68, 0x49, 0xed, 0xf5,
	0x9e, 0xcc, 0x41, 0xe9, 0x25, 0xbf, 0x40, 0xd7, 0x59, 0x8e, 0x58, 0x0a, 0xd9, 0x7e, 0x20, 0x80,
	0x24, 0x5d, 0x27, 0x37, 0x82, 0xca, 0x23, 0xee, 0x4a, 0xcf, 0x59, 0x8e, 0xdc, 0xd8, 0x3f, 0x6b,
	0x44, 0xec, 0x7a, 0x9e, 0x40, 0x17, 0xda, 0xa7, 0x15, 0x36, 0x32, 0x0a, 0x17, 0x41, 0xe1, 0x1e,
	0xcc, 0xd0, 0xb0, 0x93, 0x5c, 0xc4, 0xbc, 0xad, 0xbe, 0x4b, 0xe7, 0x4f, 0x2b, 0xea, 0x81, 0x64,
	0x5b, 0x85, 0x9a, 0x9b, 0x50, 0xb7, 0xed, 0x87, 0x27, 0x91, 0x8a, 0x7d, 0x55, 0x10, 0x5a, 0xe1,
	0x49, 0x24, 0xc2, 0xea, 0xca, 0xeb, 0x95, 0xdd, 0x7a, 0x2b, 0x92, 0x96, 0x9d, 0xfa, 0x71, 0xec,
	0x87, 0xdd, 0x36, 0xa6, 0x8c, 0x84, 0xd3, 0x94, 0x26, 0xee, 0x89, 0xd4, 0xc9, 0x32, 0x71, 0x3f,
	0xd0, 0x30, 0x4a, 0x99, 0x8e, 0xfc, 0x00, 0xf5, 0x4c, 0x99, 0x28, 0x8a, 0x92, 0xde, 0x49, 0x8f,
	0x3e, 0xc1, 0x34, 0xcc, 0xb1, 0xa1, 0x13, 0xaa, 0x43, 0x6c, 0x82, 0x68, 0xff, 0x59, 0x52, 0x39,
	0x9d, 0x62, 0xe2, 0x92, 0x2c, 0x5a, 0x81, 0xaa, 0xeb, 0x79, 0x52, 0x35, 0x33, 0xb5, 0x0f, 0xb5,
	0xd2, 0x20, 0x2f, 0x65, 0x40, 0xbe, 0x0e, 0x75, 0x55, 0xc5, 0xf0, 0x7e, 0xe9, 0x11, 0x90, 0x24,
	0x04, 0xf3, 0x1d, 0x98, 0x56, 0x0c, 0xca, 0xe3, 0xca, 0x29, 0x92, 0x78, 0x50, 0x14, 0x97, 0xb9,
	0x71, 0x71, 0x59, 0x86, 0x49, 0xd6, 0x49, 0x02, 0x81, 0xe3, 0x8a, 0x82, 0x5c, 0x27, 0x09, 0x5a,
	0x9e, 0x88, 0x77, 0x42, 0x03, 0x37, 0xf9, 0x52, 0xf9, 0x49, 0xed, 0x06, 0x09, 0xac, 0x60, 0x24,
	0x13, 0x58, 0x16, 0x04, 0x4e, 0xdb, 0x9c, 0x9e, 0x73, 0x9d, 0x7e, 0x48, 0x39, 0xa2, 0xe7, 0x3c,
	0xcd, 0x6f, 0x99, 0x79, 0x32, 0xbf, 0xef, 0x83, 0x85, 0xf9, 0xd9, 0xa8, 0x6f, 0x94, 0x36, 0xeb,
	0xdb, 0x0b, 0xb2, 0xd6, 0x6f, 0xe5, 0x32, 0xcd, 0x91, 0x2c, 0xe4, 0xbd, 0x01, 0x40, 0xa6, 0x36,
	0x8c, 0xcd, 0xfa, 0xf6, 0xb2, 0xe2, 0x1e, 0x86, 0xee, 0x00, 0x39, 0x29, 0xdc, 0xa7, 0x33, 0x70,
	0x17, 0xae, 0x3b, 0xf1, 0xc3, 0x90, 0x9d, 0x52, 0x15, 0x8f, 0x19, 0xe9, 0x3a, 0x4d, 0xc4, 0xa0,
	0xac, 0x02, 0x56, 0x0e, 0xd9, 0x25, 0x66, 0x65, 0xba, 0x09, 0x82, 0xe8, 0x10, 0xf6, 0xb7, 0x86,
	0x2a, 0xf1, 0xb2, 0x84, 0xde, 0xa4, 0xa2, 0x93, 0x4d, 0x28, 0x7b, 0x2e, 0x77, 0x1b, 0xa5, 0x9c,
	0xe5, 0x39, 0x3c, 0x39, 0xc8, 0x31, 0xb0, 0xa2, 0x9c, 0x4d, 0xda, 0x2f, 0x60, 0x16, 0x99, 0xf7,
	0xa2, 0x7e, 0x1c, 0x85, 0x1a, 0x7e, 0xa2, 0xe4, 0xa2, 0x4d, 0x46, 0x5a, 0x72, 0xd1, 0x9c, 0x6c,
	0x35, 0x36, 0xf3, 0xd5, 0x58, 0xab, 0xad, 0xe0, 0x27, 0xd6, 0xf6, 0x4f, 0x86, 0xea, 0x37, 0xcf,
	0x69, 0xe2, 0x9f, 0x5c, 0x88, 0x0c, 0x7b, 0xb7, 0x37, 0x0c, 0x20, 0x54, 0xce, 0x42, 0x68, 0x05,
	0xaa, 0x7d, 0x46, 0xa5, 0x7c, 0xd5, 0x34, 0xfa, 0x8c, 0xa2, 0xfc, 0x35, 0x80, 0xce, 0xa9, 0xeb,
	0x87, 0x32, 0x21, 0x24, 0x4e, 0x6b, 0x48, 0x11, 0xf9, 0x60, 0x3f, 0x50, 0xb9, 0x78, 0xc8, 0x69,
	0xac, 0xeb, 0x57, 0x46, 0x4d, 0x5c, 0x0b, 0x9a, 0x47, 0x59, 0x47, 0x17, 0x61, 0xb1, 0xb6, 0x5f,
	0x9b, 0xca, 0xd4, 0x7d, 0xca, 0x5d, 0xbf, 0x77, 0xc3, 0x68, 0xde, 0x07, 0x0b, 0x33, 0x1d, 0x6d,
	0x2c, 0x0a, 0xa7, 0x64, 0x21, 0x5b, 0x50, 0xe9, 0x60, 0xd0, 0xd0, 0xf6, 0xfa, 0xf6, 0x52, 0x96,
	0x79, 0x10, 0x4e, 0x47, 0x71, 0x91, 0x1d, 0xa8, 0x9f, 0x61, 0x18, 0x06, 0xd5, 0xaf, 0xbe, 0xdd,
	0xcc, 0x1e, 0xca, 0x47, 0xc9, 0x81, 0xb3, 0x74, 0x2f, 0x14, 0x63, 0x9c, 0xc6, 0xac, 0x51, 0x19,
	0xc5, 0x99, 0xf6, 0x95, 0x23, 0x59, 0xec, 0x23, 0x58, 0xd2, 0x43, 0x46, 0x94, 0xf0, 0xec, 0x78,
	0x90, 0xef, 0x8e, 0xc6, 0x50, 0x77, 0xcc, 0x77, 0x56, 0x33, 0xdf, 0x59, 0xed, 0x1f, 0x4c, 0x58,
	0x1e, 0x11, 0x7b, 0x23, 0x27, 0x3f, 0xc8, 0xa5, 0xcc, 0x9d, 0xac, 0x29, 0xa3, 0xb2, 0xb7, 0x70,
	0x73, 0x49, 0x06, 0x35, 0xbf, 0x37, 0xa0, 0x2c, 0x98, 0x10, 0x5a, 0x09, 0x75, 0x39, 0xf5, 0xda,
	0x2e, 0x57, 0x80, 0xa9, 0x29, 0xca, 0x2e, 0x1f, 0x40, 0xd5, 0x2c, 0xae, 0x76, 0xa5, 0xe1, 0x6a,
	0x37, 0x07, 0xa5, 0x7e, 0xa2, 0x5b, 0xb8, 0x58, 0xca, 0x62, 0xca, 0xfa, 0x3d, 0xae, 0x90, 0xad,
	0x76, 0xf6, 0x1b, 0x03, 0x1a, 0x6a, 0xac, 0xdb, 0x8b, 0x82, 0xd8, 0x0d, 0x7d, 0xca, 0x6e, 0xe8,
	0xa0, 0x0f, 0x72, 0x0e, 0xba, 0xab, 0x1c, 0x54, 0x24, 0x3c, 0xe3, 0xa1, 0xe6, 0x96, 0x72, 0x45,
	0xf6, 0x3e, 0x9d, 0xaa, 0x63, 0x46, 0x17, 0xfb, 0x2b, 0x98, 0x57, 0x62, 0x0f, 0x79, 0x94, 0xd0,
	0x6b, 0x8c, 0x91, 0x23, 0x5d, 0xdc, 0x1c, 0xd3, 0xc5, 0x47, 0x1b, 0x74, 0x69, 0x4c, 0x83, 0xb6,
	0x7d, 0x58, 0x53, 0xb7, 0xb7, 0x02, 0x11, 0xf7, 0x23, 0x1a, 0xc4, 0x3d, 0x97, 0xd3, 0x1b, 0xba,
	0xad, 0x09, 0x55, 0xae, 0x24, 0xe0, 0x7d, 0x53, 0x4e, 0xba, 0xb7, 0xef, 0xc3, 0x42, 0xee, 0x2a,
	0x6d, 0xa9, 0x68, 0xda, 0xa2, 0xa1, 0x1a, 0xc8, 0x8f, 0x6b, 0xfb, 0x79, 0x1a, 0x48, 0xc9, 0xfb,
	0xae, 0x32, 0xe8, 0x0f, 0x03, 0x56, 0xc6, 0x08, 0xbe, 0x91, 0xad, 0x0f, 0x73, 0x10, 0xb9, 0x97,
	0x87, 0xc8, 0xa8, 0xf4, 0xab, 0xb3, 0xe8, 0xd9, 0xf5, 0x92, 0xe8, 0xfa, 0xf9, 0xf0, 0x0d, 0xcc,
	0xef, 0x1e, 0xab, 0xfa, 0x94, 0x87, 0x96, 0x1c, 0xee, 0xd3, 0x07, 0xcf, 0x24, 0xee, 0x5b, 0x9e,
	0xb8, 0x5a, 0x75, 0x7c, 0xf1, 0x51, 0x7a, 0xaf, 0xa6, 0x28, 0x2d, 0x2f, 0x3b, 0xde, 0x94, 0x72,
	0xe3, 0x4d, 0xc1, 0xfb, 0xed, 0x6b, 0x20, 0x4a, 0x81, 0x6c, 0x00, 0xf5, 0xf8, 0x62, 0x64, 0x9e,
	0x27, 0x19, 0xc9, 0x56, 0x4e, 0xf2, 0x65, 0xe1, 0x1c, 0x82, 0x42, 0x69, 0xf8, 0xa9, 0xf1, 0x9d,
	0x91, 0x3a, 0xe0, 0x1f, 0x1c, 0x2f, 0x7e, 0x37, 0x60, 0x51, 0x69, 0xf1, 0x56, 0x8d, 0x71, 0xa0,
	0x87, 0x71, 0x85, 0x1e, 0x7f, 0x67, 0x5b, 0xb4, 0xff, 0x07, 0x53, 0xea, 0x39, 0x7e, 0x55, 0xe1,
	0xb2, 0xf7, 0x61, 0x46, 0x39, 0xe2, 0x1a, 0x55, 0x2e, 0x83, 0x08, 0x33, 0x8b, 0x08, 0xfb, 0x97,
	0x12, 0x34, 0x76, 0x8f, 0xf7, 0x69, 0xcf, 0x3f, 0xa3, 0xc9, 0xc5, 0x5b, 0xb9, 0xf4, 0xdf, 0x30,
	0x45, 0x65, 0xbe, 0xca, 0x59, 0x48, 0x82, 0xba, 0xae, 0x68, 0xfa, 0x75, 0x90, 0x2f, 0xb6, 0xe5,
	0x31, 0xc5, 0x56, 0x3c, 0x34, 0xb9, 0xcb, 0xfb, 0x2c, 0x05, 0x2f, 0xee, 0x44, 0xaa, 0x06, 0xac,
	0xab, 0x46, 0x2c, 0xb1, 0x24, 0x1f, 0xa5, 0xa9, 0x3a, 0x89, 0x5e, 0xfe, 0x8f, 0xf2, 0x72, 0x91,
	0x41, 0x5b, 0x0e, 0x72, 0xeb, 0x94, 0x6e, 0x6e, 0x43, 0xf9, 0xa9, 0xea, 0x24, 0x23, 0x33, 0xd9,
	0x40, 0x0b, 0x33, 0xab, 0x45, 0xf3, 0xb5, 0x01, 0x15, 0x29, 0x26, 0x4d, 0xbd, 0x52, 0x26, 0xf5,
	0x96, 0xa0, 0x12, 0xf6, 0xc5, 0x53, 0x47, 0xbf, 0x92, 0xe5, 0x4e, 0xb4, 0xc0, 0x9e, 0xcf, 0x44,
	0x4d, 0xc9, 0xb6, 0xc0, 0x42, 0x45, 0x85, 0x5a, 0x0e, 0x9e, 0x10, 0x0e, 0xa7, 0xe7, 0xf1, 0xd3,
	0xc1, 0x74, 0xa9, 0xb7, 0xdb, 0xbf, 0x56, 0x61, 0x41, 0xff, 0xe9, 0x23, 0x0b, 0x13, 0x4d, 0xce,
	0xc4, 0x53, 0x7d, 0x47, 0xd5, 0xbe, 0xe5, 0x61, 0x58, 0x2b, 0xa4, 0x34, 0x1b, 0xa3, 0x1f, 0xe4,
	0xc5, 0xf6, 0x04, 0xd9, 0x81, 0x8a, 0x54, 0x86, 0xcc, 0x67, 0xb9, 0xf4, 0xd1, 0x1c, 0x92, 0xf3,
	0x5a, 0xdb, 0x13, 0xe4, 0x21, 0x54, 0xe4, 0xc8, 0x53, 0x7c, 0xf7, 0x42, 0x5e, 0x6a, 0x7a, 0xf4,
	0x00, 0x60, 0x30, 0x2d, 0x91, 0xb5, 0xa2, 0x29, 0x4a, 0x0a, 0xf9, 0xd7, 0xe5, 0x43, 0x96, 0x3d,
	0x41, 0x9e, 0xc1, 0xdc, 0xf0, 0x80, 0x41, 0x72, 0x66, 0x67, 0xff, 0x06, 0x6b, 0xae, 0x5f, 0x31,
	0x93, 0xd8, 0x13, 0xe4, 0x31, 0x4c, 0x65, 0x47, 0x0b, 0xd2, 0xcc, 0x1f, 0xc9, 0x36, 0x85, 0x42,
	0x1b, 0x3f, 0x83, 0xc5, 0xb1, 0x03, 0xc2, 0x25, 0x9a, 0xdd, 0x1d, 0xd7, 0x0a, 0x87, 0x07, 0x0b,
	0x54, 0x6f, 0x3a, 0xc7, 0x42, 0x56, 0xc7, 0x1d, 0xbc, 0x54, 0x41, 0xf2, 0x1c, 0x6e, 0x8d, 0x74,
	0x5c, 0xb2, 0x5e, 0xdc, 0x8b, 0xa5, 0xac, 0x8d, 0xab, 0x9a, 0x35, 0xd9, 0x81, 0xc9, 0xdd, 0xe3,
	0xbc, 0xd7, 0xc6, 0xb4, 0xd2, 0x02, 0xa5, 0x1e, 0x41, 0x65, 0xf7, 0x18, 0x35, 0x59, 0xc9, 0x9f,
	0xcd, 0xea, 0xd0, 0x1c, 0xf7, 0x29, 0x15, 0x50, 0x15, 0x79, 0x86, 0x90, 0x5e, 0xcc, 0xf3, 0xe9,
	0xe3, 0xb7, 0xf3, 0xe4, 0xa1, 0x32, 0xf8, 0x08, 0xe6, 0x76, 0x8f, 0xf7, 0xa2, 0xf0, 0xc4, 0x4f,
	0x02, 0x87, 0x76, 0xa8, 0x1f, 0xf3, 0x22, 0x41, 0xe3, 0x4d, 0xf8, 0x44, 0x08, 0xc8, 0x67, 0x7a,
	0x91, 0x80, 0xf5, 0x2b, 0x2a, 0x03, 0x71, 0x60, 0x71, 0xec, 0xff, 0xcc, 0x44, 0xbf, 0x3b, 0x2e,
	0xfb, 0x17, 0x7a, 0xbc, 0x7e, 0x8f, 0xad, 0xcf, 0x4b, 0x7e, 0xb7, 0xf3, 0xa2, 0x82, 0xdf, 0xde,
	0xff, 0x2b, 0x00, 0x00, 0xff, 0xff, 0x00, 0xbb, 0xc3, 0x4d, 0xe4, 0x16, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// IntegralOrderServiceClient is the client API for IntegralOrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type IntegralOrderServiceClient interface {
	// 积分订单列表
	List(ctx context.Context, in *OrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error)
	// 积分订单详情
	Detail(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderDetailResponse, error)
	// 积分订单导出
	Export(ctx context.Context, in *OrderListRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// 积分订单导出任务列表
	ExportList(ctx context.Context, in *OrderExportListRequest, opts ...grpc.CallOption) (*OrderExportListResponse, error)
	// 快递公司列表
	ExpressCompanies(ctx context.Context, in *OrderEmptyRequest, opts ...grpc.CallOption) (*ExpressCompaniesResponse, error)
	// 发货或者更新物流
	ExpressStore(ctx context.Context, in *ExpressStoreRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// 批量导入物流模板
	ExpressImportTemplate(ctx context.Context, in *OrderEmptyRequest, opts ...grpc.CallOption) (*ExpressImportTemplateResponse, error)
	// 批量导入物流
	ExpressImport(ctx context.Context, in *ExpressImportRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// 批量导入物流历史
	ExpressImportList(ctx context.Context, in *ExpressImportListRequest, opts ...grpc.CallOption) (*ExpressImportListResponse, error)
	// --------以下是小程序的接口-----------
	// 立即兑换
	AWStore(ctx context.Context, in *AWOrderStoreRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// 订单列表
	AWList(ctx context.Context, in *AWOrderListRequest, opts ...grpc.CallOption) (*AWOrderListResponse, error)
	// 订单详情
	AWDetail(ctx context.Context, in *AWOrderRequest, opts ...grpc.CallOption) (*AWOrderDetailResponse, error)
	// 确认收货
	AWConfirmReceipt(ctx context.Context, in *AWOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// 物流信息
	AWDeliveryDetail(ctx context.Context, in *AWOrderRequest, opts ...grpc.CallOption) (*AWDeliveryDetailResponse, error)
	IntegralChangeMessage(ctx context.Context, in *IntegralChangeMessageRequest, opts ...grpc.CallOption) (*OrderResponse, error)
}

type integralOrderServiceClient struct {
	cc *grpc.ClientConn
}

func NewIntegralOrderServiceClient(cc *grpc.ClientConn) IntegralOrderServiceClient {
	return &integralOrderServiceClient{cc}
}

func (c *integralOrderServiceClient) List(ctx context.Context, in *OrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error) {
	out := new(OrderListResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/List", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) Detail(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderDetailResponse, error) {
	out := new(OrderDetailResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/Detail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) Export(ctx context.Context, in *OrderListRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/Export", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) ExportList(ctx context.Context, in *OrderExportListRequest, opts ...grpc.CallOption) (*OrderExportListResponse, error) {
	out := new(OrderExportListResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/ExportList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) ExpressCompanies(ctx context.Context, in *OrderEmptyRequest, opts ...grpc.CallOption) (*ExpressCompaniesResponse, error) {
	out := new(ExpressCompaniesResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/ExpressCompanies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) ExpressStore(ctx context.Context, in *ExpressStoreRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/ExpressStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) ExpressImportTemplate(ctx context.Context, in *OrderEmptyRequest, opts ...grpc.CallOption) (*ExpressImportTemplateResponse, error) {
	out := new(ExpressImportTemplateResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/ExpressImportTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) ExpressImport(ctx context.Context, in *ExpressImportRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/ExpressImport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) ExpressImportList(ctx context.Context, in *ExpressImportListRequest, opts ...grpc.CallOption) (*ExpressImportListResponse, error) {
	out := new(ExpressImportListResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/ExpressImportList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) AWStore(ctx context.Context, in *AWOrderStoreRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/AWStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) AWList(ctx context.Context, in *AWOrderListRequest, opts ...grpc.CallOption) (*AWOrderListResponse, error) {
	out := new(AWOrderListResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/AWList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) AWDetail(ctx context.Context, in *AWOrderRequest, opts ...grpc.CallOption) (*AWOrderDetailResponse, error) {
	out := new(AWOrderDetailResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/AWDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) AWConfirmReceipt(ctx context.Context, in *AWOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/AWConfirmReceipt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) AWDeliveryDetail(ctx context.Context, in *AWOrderRequest, opts ...grpc.CallOption) (*AWDeliveryDetailResponse, error) {
	out := new(AWDeliveryDetailResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/AWDeliveryDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralOrderServiceClient) IntegralChangeMessage(ctx context.Context, in *IntegralChangeMessageRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, "/proto.IntegralOrderService/IntegralChangeMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IntegralOrderServiceServer is the server API for IntegralOrderService service.
type IntegralOrderServiceServer interface {
	// 积分订单列表
	List(context.Context, *OrderListRequest) (*OrderListResponse, error)
	// 积分订单详情
	Detail(context.Context, *OrderRequest) (*OrderDetailResponse, error)
	// 积分订单导出
	Export(context.Context, *OrderListRequest) (*OrderResponse, error)
	// 积分订单导出任务列表
	ExportList(context.Context, *OrderExportListRequest) (*OrderExportListResponse, error)
	// 快递公司列表
	ExpressCompanies(context.Context, *OrderEmptyRequest) (*ExpressCompaniesResponse, error)
	// 发货或者更新物流
	ExpressStore(context.Context, *ExpressStoreRequest) (*OrderResponse, error)
	// 批量导入物流模板
	ExpressImportTemplate(context.Context, *OrderEmptyRequest) (*ExpressImportTemplateResponse, error)
	// 批量导入物流
	ExpressImport(context.Context, *ExpressImportRequest) (*OrderResponse, error)
	// 批量导入物流历史
	ExpressImportList(context.Context, *ExpressImportListRequest) (*ExpressImportListResponse, error)
	// --------以下是小程序的接口-----------
	// 立即兑换
	AWStore(context.Context, *AWOrderStoreRequest) (*OrderResponse, error)
	// 订单列表
	AWList(context.Context, *AWOrderListRequest) (*AWOrderListResponse, error)
	// 订单详情
	AWDetail(context.Context, *AWOrderRequest) (*AWOrderDetailResponse, error)
	// 确认收货
	AWConfirmReceipt(context.Context, *AWOrderRequest) (*OrderResponse, error)
	// 物流信息
	AWDeliveryDetail(context.Context, *AWOrderRequest) (*AWDeliveryDetailResponse, error)
	IntegralChangeMessage(context.Context, *IntegralChangeMessageRequest) (*OrderResponse, error)
}

// UnimplementedIntegralOrderServiceServer can be embedded to have forward compatible implementations.
type UnimplementedIntegralOrderServiceServer struct {
}

func (*UnimplementedIntegralOrderServiceServer) List(ctx context.Context, req *OrderListRequest) (*OrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) Detail(ctx context.Context, req *OrderRequest) (*OrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Detail not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) Export(ctx context.Context, req *OrderListRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Export not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) ExportList(ctx context.Context, req *OrderExportListRequest) (*OrderExportListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportList not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) ExpressCompanies(ctx context.Context, req *OrderEmptyRequest) (*ExpressCompaniesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpressCompanies not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) ExpressStore(ctx context.Context, req *ExpressStoreRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpressStore not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) ExpressImportTemplate(ctx context.Context, req *OrderEmptyRequest) (*ExpressImportTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpressImportTemplate not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) ExpressImport(ctx context.Context, req *ExpressImportRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpressImport not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) ExpressImportList(ctx context.Context, req *ExpressImportListRequest) (*ExpressImportListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpressImportList not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) AWStore(ctx context.Context, req *AWOrderStoreRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWStore not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) AWList(ctx context.Context, req *AWOrderListRequest) (*AWOrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWList not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) AWDetail(ctx context.Context, req *AWOrderRequest) (*AWOrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWDetail not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) AWConfirmReceipt(ctx context.Context, req *AWOrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWConfirmReceipt not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) AWDeliveryDetail(ctx context.Context, req *AWOrderRequest) (*AWDeliveryDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AWDeliveryDetail not implemented")
}
func (*UnimplementedIntegralOrderServiceServer) IntegralChangeMessage(ctx context.Context, req *IntegralChangeMessageRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegralChangeMessage not implemented")
}

func RegisterIntegralOrderServiceServer(s *grpc.Server, srv IntegralOrderServiceServer) {
	s.RegisterService(&_IntegralOrderService_serviceDesc, srv)
}

func _IntegralOrderService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/List",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).List(ctx, req.(*OrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_Detail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).Detail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/Detail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).Detail(ctx, req.(*OrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_Export_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).Export(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/Export",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).Export(ctx, req.(*OrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_ExportList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderExportListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).ExportList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/ExportList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).ExportList(ctx, req.(*OrderExportListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_ExpressCompanies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderEmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).ExpressCompanies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/ExpressCompanies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).ExpressCompanies(ctx, req.(*OrderEmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_ExpressStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpressStoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).ExpressStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/ExpressStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).ExpressStore(ctx, req.(*ExpressStoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_ExpressImportTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderEmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).ExpressImportTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/ExpressImportTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).ExpressImportTemplate(ctx, req.(*OrderEmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_ExpressImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpressImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).ExpressImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/ExpressImport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).ExpressImport(ctx, req.(*ExpressImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_ExpressImportList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpressImportListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).ExpressImportList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/ExpressImportList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).ExpressImportList(ctx, req.(*ExpressImportListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_AWStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AWOrderStoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).AWStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/AWStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).AWStore(ctx, req.(*AWOrderStoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_AWList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AWOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).AWList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/AWList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).AWList(ctx, req.(*AWOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_AWDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AWOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).AWDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/AWDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).AWDetail(ctx, req.(*AWOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_AWConfirmReceipt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AWOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).AWConfirmReceipt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/AWConfirmReceipt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).AWConfirmReceipt(ctx, req.(*AWOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_AWDeliveryDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AWOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).AWDeliveryDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/AWDeliveryDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).AWDeliveryDetail(ctx, req.(*AWOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralOrderService_IntegralChangeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegralChangeMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralOrderServiceServer).IntegralChangeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.IntegralOrderService/IntegralChangeMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralOrderServiceServer).IntegralChangeMessage(ctx, req.(*IntegralChangeMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _IntegralOrderService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "proto.IntegralOrderService",
	HandlerType: (*IntegralOrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _IntegralOrderService_List_Handler,
		},
		{
			MethodName: "Detail",
			Handler:    _IntegralOrderService_Detail_Handler,
		},
		{
			MethodName: "Export",
			Handler:    _IntegralOrderService_Export_Handler,
		},
		{
			MethodName: "ExportList",
			Handler:    _IntegralOrderService_ExportList_Handler,
		},
		{
			MethodName: "ExpressCompanies",
			Handler:    _IntegralOrderService_ExpressCompanies_Handler,
		},
		{
			MethodName: "ExpressStore",
			Handler:    _IntegralOrderService_ExpressStore_Handler,
		},
		{
			MethodName: "ExpressImportTemplate",
			Handler:    _IntegralOrderService_ExpressImportTemplate_Handler,
		},
		{
			MethodName: "ExpressImport",
			Handler:    _IntegralOrderService_ExpressImport_Handler,
		},
		{
			MethodName: "ExpressImportList",
			Handler:    _IntegralOrderService_ExpressImportList_Handler,
		},
		{
			MethodName: "AWStore",
			Handler:    _IntegralOrderService_AWStore_Handler,
		},
		{
			MethodName: "AWList",
			Handler:    _IntegralOrderService_AWList_Handler,
		},
		{
			MethodName: "AWDetail",
			Handler:    _IntegralOrderService_AWDetail_Handler,
		},
		{
			MethodName: "AWConfirmReceipt",
			Handler:    _IntegralOrderService_AWConfirmReceipt_Handler,
		},
		{
			MethodName: "AWDeliveryDetail",
			Handler:    _IntegralOrderService_AWDeliveryDetail_Handler,
		},
		{
			MethodName: "IntegralChangeMessage",
			Handler:    _IntegralOrderService_IntegralChangeMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "igc/order.proto",
}
