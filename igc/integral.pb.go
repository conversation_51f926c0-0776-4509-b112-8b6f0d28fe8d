// Code generated by protoc-gen-go. DO NOT EDIT.
// source: igc/integral.proto

package igc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type TaskRunVo struct {
	// 启动类型 1：会员统计
	Data                 int32    `protobuf:"varint,1,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskRunVo) Reset()         { *m = TaskRunVo{} }
func (m *TaskRunVo) String() string { return proto.CompactTextString(m) }
func (*TaskRunVo) ProtoMessage()    {}
func (*TaskRunVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b776d33231f6905, []int{0}
}

func (m *TaskRunVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskRunVo.Unmarshal(m, b)
}
func (m *TaskRunVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskRunVo.Marshal(b, m, deterministic)
}
func (m *TaskRunVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskRunVo.Merge(m, src)
}
func (m *TaskRunVo) XXX_Size() int {
	return xxx_messageInfo_TaskRunVo.Size(m)
}
func (m *TaskRunVo) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskRunVo.DiscardUnknown(m)
}

var xxx_messageInfo_TaskRunVo proto.InternalMessageInfo

func (m *TaskRunVo) GetData() int32 {
	if m != nil {
		return m.Data
	}
	return 0
}

// 通用返回
type BaseResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b776d33231f6905, []int{1}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 积分重新计算
type IntegralRecomputeReq struct {
	// scrmId，多个用逗号分隔
	ScrmId string `protobuf:"bytes,1,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 起始用户id
	StartId int32 `protobuf:"varint,2,opt,name=start_id,json=startId,proto3" json:"start_id"`
	// 结束用户id
	EndId int32 `protobuf:"varint,3,opt,name=end_id,json=endId,proto3" json:"end_id"`
	// 请求的时间戳
	Timestamp int32 `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp"`
	// 是否记录日志
	RecordLog int32 `protobuf:"varint,5,opt,name=record_log,json=recordLog,proto3" json:"record_log"`
	// 签名
	Sign                 string   `protobuf:"bytes,6,opt,name=sign,proto3" json:"sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IntegralRecomputeReq) Reset()         { *m = IntegralRecomputeReq{} }
func (m *IntegralRecomputeReq) String() string { return proto.CompactTextString(m) }
func (*IntegralRecomputeReq) ProtoMessage()    {}
func (*IntegralRecomputeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b776d33231f6905, []int{2}
}

func (m *IntegralRecomputeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IntegralRecomputeReq.Unmarshal(m, b)
}
func (m *IntegralRecomputeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IntegralRecomputeReq.Marshal(b, m, deterministic)
}
func (m *IntegralRecomputeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IntegralRecomputeReq.Merge(m, src)
}
func (m *IntegralRecomputeReq) XXX_Size() int {
	return xxx_messageInfo_IntegralRecomputeReq.Size(m)
}
func (m *IntegralRecomputeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IntegralRecomputeReq.DiscardUnknown(m)
}

var xxx_messageInfo_IntegralRecomputeReq proto.InternalMessageInfo

func (m *IntegralRecomputeReq) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *IntegralRecomputeReq) GetStartId() int32 {
	if m != nil {
		return m.StartId
	}
	return 0
}

func (m *IntegralRecomputeReq) GetEndId() int32 {
	if m != nil {
		return m.EndId
	}
	return 0
}

func (m *IntegralRecomputeReq) GetTimestamp() int32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *IntegralRecomputeReq) GetRecordLog() int32 {
	if m != nil {
		return m.RecordLog
	}
	return 0
}

func (m *IntegralRecomputeReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type PageIntegralRecordReq struct {
	//分页
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//分页
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//积分类型
	IntegralType int32 `protobuf:"varint,3,opt,name=integral_type,json=integralType,proto3" json:"integral_type"`
	//用户id
	MemberId string `protobuf:"bytes,4,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	//主体ID
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PageIntegralRecordReq) Reset()         { *m = PageIntegralRecordReq{} }
func (m *PageIntegralRecordReq) String() string { return proto.CompactTextString(m) }
func (*PageIntegralRecordReq) ProtoMessage()    {}
func (*PageIntegralRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b776d33231f6905, []int{3}
}

func (m *PageIntegralRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PageIntegralRecordReq.Unmarshal(m, b)
}
func (m *PageIntegralRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PageIntegralRecordReq.Marshal(b, m, deterministic)
}
func (m *PageIntegralRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PageIntegralRecordReq.Merge(m, src)
}
func (m *PageIntegralRecordReq) XXX_Size() int {
	return xxx_messageInfo_PageIntegralRecordReq.Size(m)
}
func (m *PageIntegralRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PageIntegralRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_PageIntegralRecordReq proto.InternalMessageInfo

func (m *PageIntegralRecordReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *PageIntegralRecordReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *PageIntegralRecordReq) GetIntegralType() int32 {
	if m != nil {
		return m.IntegralType
	}
	return 0
}

func (m *PageIntegralRecordReq) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *PageIntegralRecordReq) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type PageIntegralRecordResp struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message              string                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*PageIntegralRecordData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	Total                int64                     `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *PageIntegralRecordResp) Reset()         { *m = PageIntegralRecordResp{} }
func (m *PageIntegralRecordResp) String() string { return proto.CompactTextString(m) }
func (*PageIntegralRecordResp) ProtoMessage()    {}
func (*PageIntegralRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b776d33231f6905, []int{4}
}

func (m *PageIntegralRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PageIntegralRecordResp.Unmarshal(m, b)
}
func (m *PageIntegralRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PageIntegralRecordResp.Marshal(b, m, deterministic)
}
func (m *PageIntegralRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PageIntegralRecordResp.Merge(m, src)
}
func (m *PageIntegralRecordResp) XXX_Size() int {
	return xxx_messageInfo_PageIntegralRecordResp.Size(m)
}
func (m *PageIntegralRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PageIntegralRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_PageIntegralRecordResp proto.InternalMessageInfo

func (m *PageIntegralRecordResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PageIntegralRecordResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *PageIntegralRecordResp) GetData() []*PageIntegralRecordData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PageIntegralRecordResp) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type PageIntegralRecordData struct {
	// 积分创建时间
	IntegralDate string `protobuf:"bytes,1,opt,name=integral_date,json=integralDate,proto3" json:"integral_date"`
	// 积分值
	IntegralCount int64 `protobuf:"varint,2,opt,name=integral_count,json=integralCount,proto3" json:"integral_count"`
	// 积分类型
	IntegralType int32 `protobuf:"varint,3,opt,name=integral_type,json=integralType,proto3" json:"integral_type"`
	// 业务类型
	IntegralName string `protobuf:"bytes,4,opt,name=integral_name,json=integralName,proto3" json:"integral_name"`
	// 订单号
	OrderId string `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 积分添加原因，详细说明
	IntegralReason string `protobuf:"bytes,6,opt,name=integral_reason,json=integralReason,proto3" json:"integral_reason"`
	// 积分添加原因，详细说明
	UserLevelId          string   `protobuf:"bytes,7,opt,name=user_level_id,json=userLevelId,proto3" json:"user_level_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PageIntegralRecordData) Reset()         { *m = PageIntegralRecordData{} }
func (m *PageIntegralRecordData) String() string { return proto.CompactTextString(m) }
func (*PageIntegralRecordData) ProtoMessage()    {}
func (*PageIntegralRecordData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b776d33231f6905, []int{5}
}

func (m *PageIntegralRecordData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PageIntegralRecordData.Unmarshal(m, b)
}
func (m *PageIntegralRecordData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PageIntegralRecordData.Marshal(b, m, deterministic)
}
func (m *PageIntegralRecordData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PageIntegralRecordData.Merge(m, src)
}
func (m *PageIntegralRecordData) XXX_Size() int {
	return xxx_messageInfo_PageIntegralRecordData.Size(m)
}
func (m *PageIntegralRecordData) XXX_DiscardUnknown() {
	xxx_messageInfo_PageIntegralRecordData.DiscardUnknown(m)
}

var xxx_messageInfo_PageIntegralRecordData proto.InternalMessageInfo

func (m *PageIntegralRecordData) GetIntegralDate() string {
	if m != nil {
		return m.IntegralDate
	}
	return ""
}

func (m *PageIntegralRecordData) GetIntegralCount() int64 {
	if m != nil {
		return m.IntegralCount
	}
	return 0
}

func (m *PageIntegralRecordData) GetIntegralType() int32 {
	if m != nil {
		return m.IntegralType
	}
	return 0
}

func (m *PageIntegralRecordData) GetIntegralName() string {
	if m != nil {
		return m.IntegralName
	}
	return ""
}

func (m *PageIntegralRecordData) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PageIntegralRecordData) GetIntegralReason() string {
	if m != nil {
		return m.IntegralReason
	}
	return ""
}

func (m *PageIntegralRecordData) GetUserLevelId() string {
	if m != nil {
		return m.UserLevelId
	}
	return ""
}

func init() {
	proto.RegisterType((*TaskRunVo)(nil), "igc.TaskRunVo")
	proto.RegisterType((*BaseResponse)(nil), "igc.BaseResponse")
	proto.RegisterType((*IntegralRecomputeReq)(nil), "igc.IntegralRecomputeReq")
	proto.RegisterType((*PageIntegralRecordReq)(nil), "igc.PageIntegralRecordReq")
	proto.RegisterType((*PageIntegralRecordResp)(nil), "igc.PageIntegralRecordResp")
	proto.RegisterType((*PageIntegralRecordData)(nil), "igc.PageIntegralRecordData")
}

func init() { proto.RegisterFile("igc/integral.proto", fileDescriptor_5b776d33231f6905) }

var fileDescriptor_5b776d33231f6905 = []byte{
	// 559 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0x4f, 0x6f, 0xd4, 0x3e,
	0x10, 0x55, 0x9a, 0x66, 0x77, 0x33, 0xed, 0xb6, 0xfa, 0x8d, 0xda, 0x1f, 0x69, 0x97, 0x8a, 0x55,
	0x10, 0x62, 0x4f, 0x5b, 0x69, 0x39, 0x21, 0x71, 0x40, 0xb4, 0x97, 0x48, 0x05, 0xa1, 0xb4, 0xe2,
	0x1a, 0xb9, 0xf1, 0xc8, 0x44, 0x6c, 0xe2, 0x60, 0x7b, 0xab, 0xb6, 0xdf, 0x81, 0x2b, 0x17, 0xbe,
	0x02, 0x27, 0x3e, 0x21, 0xb2, 0xf3, 0x87, 0x56, 0xdd, 0x4a, 0x70, 0xf3, 0xbc, 0x37, 0x1e, 0xbf,
	0x79, 0x63, 0x1b, 0xb0, 0x10, 0xf9, 0x71, 0x51, 0x19, 0x12, 0x8a, 0x2d, 0xe7, 0xb5, 0x92, 0x46,
	0xa2, 0x5f, 0x88, 0x3c, 0x7e, 0x06, 0xe1, 0x05, 0xd3, 0x5f, 0xd2, 0x55, 0xf5, 0x49, 0x22, 0xc2,
	0x26, 0x67, 0x86, 0x45, 0xde, 0xd4, 0x9b, 0x05, 0xa9, 0x5b, 0xc7, 0x6f, 0x60, 0xfb, 0x1d, 0xd3,
	0x94, 0x92, 0xae, 0x65, 0xa5, 0xc9, 0xe6, 0xe4, 0x92, 0x53, 0x97, 0x63, 0xd7, 0x18, 0xc1, 0xb0,
	0x24, 0xad, 0x99, 0xa0, 0x68, 0x63, 0xea, 0xcd, 0xc2, 0xb4, 0x0b, 0xe3, 0x5f, 0x1e, 0xec, 0x25,
	0xed, 0xb1, 0x29, 0xe5, 0xb2, 0xac, 0x57, 0x86, 0x52, 0xfa, 0x8a, 0x4f, 0x60, 0xa8, 0x73, 0x55,
	0x66, 0x05, 0x77, 0x95, 0xc2, 0x74, 0x60, 0xc3, 0x84, 0xe3, 0x01, 0x8c, 0xb4, 0x61, 0xca, 0x58,
	0x66, 0xc3, 0x9d, 0x31, 0x74, 0x71, 0xc2, 0x71, 0x1f, 0x06, 0x54, 0x71, 0x4b, 0xf8, 0x8e, 0x08,
	0xa8, 0xe2, 0x09, 0xc7, 0xa7, 0x10, 0x9a, 0xa2, 0x24, 0x6d, 0x58, 0x59, 0x47, 0x9b, 0x8e, 0xf9,
	0x03, 0xe0, 0x11, 0x80, 0xa2, 0x5c, 0x2a, 0x9e, 0x2d, 0xa5, 0x88, 0x82, 0x86, 0x6e, 0x90, 0x33,
	0x29, 0x6c, 0x3b, 0xba, 0x10, 0x55, 0x34, 0x70, 0x22, 0xdc, 0x3a, 0xfe, 0xe9, 0xc1, 0xfe, 0x47,
	0x26, 0xe8, 0xae, 0x70, 0xc5, 0xad, 0xea, 0x23, 0x80, 0x9a, 0x09, 0xca, 0x8a, 0x8a, 0xd3, 0x75,
	0x6b, 0x41, 0x58, 0xbb, 0x54, 0x4e, 0xd7, 0x38, 0x01, 0x17, 0x64, 0xba, 0xb8, 0xa5, 0x56, 0xfc,
	0xc8, 0x02, 0xe7, 0xc5, 0x2d, 0xe1, 0x73, 0x18, 0x77, 0x03, 0xc8, 0xcc, 0x4d, 0x4d, 0x6d, 0x13,
	0xdb, 0x1d, 0x78, 0x71, 0x53, 0x93, 0xad, 0x50, 0x52, 0x79, 0x49, 0xca, 0x76, 0xb9, 0xe9, 0x34,
	0x8d, 0x1a, 0xa0, 0xe9, 0x5f, 0x2a, 0x61, 0x99, 0xa6, 0x8d, 0x40, 0x2a, 0x91, 0xf0, 0xf8, 0x9b,
	0x07, 0xff, 0xaf, 0x93, 0xab, 0xeb, 0x7f, 0x1b, 0x16, 0x1e, 0xb7, 0xe3, 0xf7, 0xa7, 0xfe, 0x6c,
	0x6b, 0x31, 0x99, 0x17, 0x22, 0x9f, 0x3f, 0x2c, 0x7c, 0xca, 0x0c, 0x6b, 0xee, 0x06, 0xee, 0x41,
	0x60, 0xa4, 0x61, 0x4b, 0xa7, 0xd4, 0x4f, 0x9b, 0x20, 0xfe, 0xbe, 0xb1, 0x4e, 0x8f, 0xdd, 0x76,
	0xcf, 0x03, 0xce, 0x0c, 0xb5, 0xb3, 0xef, 0x3d, 0x38, 0x65, 0x86, 0xf0, 0x05, 0xec, 0xf4, 0x49,
	0xb9, 0x5c, 0x55, 0xc6, 0xe9, 0xf4, 0xd3, 0x7e, 0xeb, 0x89, 0x05, 0xff, 0xce, 0xcf, 0xbb, 0x49,
	0x15, 0x2b, 0xa9, 0xf5, 0xb4, 0x4f, 0xfa, 0xc0, 0x4a, 0xb2, 0x57, 0x4e, 0x2a, 0xde, 0x78, 0x1e,
	0x34, 0x96, 0xb8, 0x38, 0xe1, 0xf8, 0x12, 0x76, 0xfb, 0xfd, 0x8a, 0x98, 0x96, 0xdd, 0x4d, 0xe9,
	0x25, 0xa6, 0x0e, 0xc5, 0x18, 0xc6, 0x2b, 0x4d, 0x2a, 0x5b, 0xd2, 0x15, 0x2d, 0x6d, 0xa1, 0xa1,
	0x4b, 0xdb, 0xb2, 0xe0, 0x99, 0xc5, 0x12, 0xbe, 0xf8, 0xe1, 0xc1, 0x6e, 0x67, 0xca, 0x39, 0xa9,
	0xab, 0x22, 0x27, 0x7c, 0x0d, 0x61, 0xff, 0x2e, 0xf0, 0xc0, 0x59, 0xbe, 0xee, 0xbd, 0x1c, 0xfe,
	0xe7, 0xa8, 0x7b, 0x2f, 0xf1, 0x3d, 0xe0, 0x43, 0x9b, 0xf1, 0xf0, 0x91, 0xb1, 0xd9, 0x22, 0x93,
	0x47, 0x39, 0x5d, 0x2f, 0xde, 0x02, 0xd8, 0x9f, 0xe0, 0xe4, 0xb3, 0x94, 0x9a, 0x70, 0x01, 0xe3,
	0x66, 0xd5, 0xfe, 0x0e, 0xb8, 0xe3, 0xf6, 0xf6, 0x7f, 0xc5, 0x1a, 0x41, 0x97, 0x03, 0xf7, 0xaf,
	0xbc, 0xfa, 0x1d, 0x00, 0x00, 0xff, 0xff, 0xb7, 0x26, 0xe5, 0x78, 0x6d, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// IntegralServiceClient is the client API for IntegralService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type IntegralServiceClient interface {
	// 积分倒推重新计算
	Recompute(ctx context.Context, in *IntegralRecomputeReq, opts ...grpc.CallOption) (*BaseResponse, error)
	// 分页查询用户积分明细
	PageIntegralRecord(ctx context.Context, in *PageIntegralRecordReq, opts ...grpc.CallOption) (*PageIntegralRecordResp, error)
}

type integralServiceClient struct {
	cc *grpc.ClientConn
}

func NewIntegralServiceClient(cc *grpc.ClientConn) IntegralServiceClient {
	return &integralServiceClient{cc}
}

func (c *integralServiceClient) Recompute(ctx context.Context, in *IntegralRecomputeReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/igc.IntegralService/Recompute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integralServiceClient) PageIntegralRecord(ctx context.Context, in *PageIntegralRecordReq, opts ...grpc.CallOption) (*PageIntegralRecordResp, error) {
	out := new(PageIntegralRecordResp)
	err := c.cc.Invoke(ctx, "/igc.IntegralService/PageIntegralRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IntegralServiceServer is the server API for IntegralService service.
type IntegralServiceServer interface {
	// 积分倒推重新计算
	Recompute(context.Context, *IntegralRecomputeReq) (*BaseResponse, error)
	// 分页查询用户积分明细
	PageIntegralRecord(context.Context, *PageIntegralRecordReq) (*PageIntegralRecordResp, error)
}

// UnimplementedIntegralServiceServer can be embedded to have forward compatible implementations.
type UnimplementedIntegralServiceServer struct {
}

func (*UnimplementedIntegralServiceServer) Recompute(ctx context.Context, req *IntegralRecomputeReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Recompute not implemented")
}
func (*UnimplementedIntegralServiceServer) PageIntegralRecord(ctx context.Context, req *PageIntegralRecordReq) (*PageIntegralRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PageIntegralRecord not implemented")
}

func RegisterIntegralServiceServer(s *grpc.Server, srv IntegralServiceServer) {
	s.RegisterService(&_IntegralService_serviceDesc, srv)
}

func _IntegralService_Recompute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegralRecomputeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralServiceServer).Recompute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/igc.IntegralService/Recompute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralServiceServer).Recompute(ctx, req.(*IntegralRecomputeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _IntegralService_PageIntegralRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageIntegralRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegralServiceServer).PageIntegralRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/igc.IntegralService/PageIntegralRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegralServiceServer).PageIntegralRecord(ctx, req.(*PageIntegralRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _IntegralService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "igc.IntegralService",
	HandlerType: (*IntegralServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Recompute",
			Handler:    _IntegralService_Recompute_Handler,
		},
		{
			MethodName: "PageIntegralRecord",
			Handler:    _IntegralService_PageIntegralRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "igc/integral.proto",
}

// TaskChooseClient is the client API for TaskChoose service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TaskChooseClient interface {
	ChooseTaskRun(ctx context.Context, in *TaskRunVo, opts ...grpc.CallOption) (*BaseResponse, error)
}

type taskChooseClient struct {
	cc *grpc.ClientConn
}

func NewTaskChooseClient(cc *grpc.ClientConn) TaskChooseClient {
	return &taskChooseClient{cc}
}

func (c *taskChooseClient) ChooseTaskRun(ctx context.Context, in *TaskRunVo, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/igc.TaskChoose/ChooseTaskRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TaskChooseServer is the server API for TaskChoose service.
type TaskChooseServer interface {
	ChooseTaskRun(context.Context, *TaskRunVo) (*BaseResponse, error)
}

// UnimplementedTaskChooseServer can be embedded to have forward compatible implementations.
type UnimplementedTaskChooseServer struct {
}

func (*UnimplementedTaskChooseServer) ChooseTaskRun(ctx context.Context, req *TaskRunVo) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChooseTaskRun not implemented")
}

func RegisterTaskChooseServer(s *grpc.Server, srv TaskChooseServer) {
	s.RegisterService(&_TaskChoose_serviceDesc, srv)
}

func _TaskChoose_ChooseTaskRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRunVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskChooseServer).ChooseTaskRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/igc.TaskChoose/ChooseTaskRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskChooseServer).ChooseTaskRun(ctx, req.(*TaskRunVo))
	}
	return interceptor(ctx, in, info, handler)
}

var _TaskChoose_serviceDesc = grpc.ServiceDesc{
	ServiceName: "igc.TaskChoose",
	HandlerType: (*TaskChooseServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChooseTaskRun",
			Handler:    _TaskChoose_ChooseTaskRun_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "igc/integral.proto",
}
