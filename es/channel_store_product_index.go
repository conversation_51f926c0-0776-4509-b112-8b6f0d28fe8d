package es

import (
	"_/proto/models"
	"_/proto/pc"
	"context"
	"encoding/json"
	"fmt"
	"html"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/shopspring/decimal"
	kit "github.com/tricobbler/rp-kit"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
)

// 本地生活商品es索引
// _id命名规则：财务编码-渠道id-sku_id，（CX0013-1-1018477001）
// 目前只有阿闻渠道，写死1就行

type ChannelStoreProductIndex struct {
	//复用 es 链接对象
	client       *elastic.Client
	clientIniter sync.Once
	Index        string
}

// 根据financial_code查询渠道门店商品（查询_id）
func (c *ChannelStoreProductIndex) QueryProductByFinanceCode(finance_code string, product_size int) ([]*models.ChannelProductRequestEs, error) {
	boolQuery := elastic.NewBoolQuery().Must()
	boolQuerySlice := make([]elastic.Query, 0)
	//and匹配
	if len(finance_code) > 0 {
		financeCodeQuery := elastic.NewTermsQuery("finance_code.keyword", finance_code)
		boolQuerySlice = append(boolQuerySlice, financeCodeQuery)
	}

	boolQuery.Must(boolQuerySlice...)

	searchResult, err := c.Search(0, product_size, boolQuery).Do(context.Background())
	if err != nil {
		glog.Error(err)
		return nil, err
	}
	//添加排序
	productsRes := ConvertToChannelProductRequest(searchResult.Hits)
	return productsRes, nil
}

// 根据skuid查询渠道门店商品（查询_id）
func (c *ChannelStoreProductIndex) QueryTerms(finance_code string, channel_id int, sku_id []int32) ([]*models.ChannelProductRequestEs, error) {
	var ids []interface{}
	var skus []interface{}
	for _, v := range sku_id {
		id := fmt.Sprintf("%s-%d-%d", finance_code, channel_id, v)
		ids = append(ids, id)
		skus = append(skus, v)
	}

	// bool查询
	query := elastic.NewBoolQuery()
	query.Filter(elastic.NewTermsQuery("sku_info.sku_id", skus...))
	query.Must(elastic.NewTermsQuery("finance_code.keyword", finance_code))
	//q.Filter(elastic.NewTermsQuery("finance_code.keyword", finance_code))
	//q.Must(elastic.NewTermsQuery("sku_info.sku_id", 0))

	//query := elastic.NewTermsQuery("_id", ids...)
	searchResult, err := c.Search(0, len(sku_id), query).Pretty(true).Do(context.Background())
	if err != nil {
		glog.Error(err)
		return nil, err
	}
	//添加排序
	products := ConvertToChannelProductRequest(searchResult.Hits)
	productsMap := make(map[int32]*models.ChannelProductRequestEs)
	var productsRes []*models.ChannelProductRequestEs
	for _, v := range products {
		productsMap[v.SkuInfo[0].SkuId] = v
	}
	for _, v := range sku_id {
		if _, ok := productsMap[v]; ok {
			productsRes = append(productsRes, productsMap[v])
		}
	}
	return productsRes, nil
}

// 根据财务编码和渠道id随机查询商品
func (c *ChannelStoreProductIndex) QueryRandom(finance_code string, channel_id, from, size int) ([]*models.ChannelProductRequestEs, error) {
	//随机查询，from=0
	from = 0

	query := elastic.NewFunctionScoreQuery()
	query = query.Query(elastic.NewMatchQuery("finance_code.keyword", finance_code))
	query = query.Query(elastic.NewBoolQuery().Must(elastic.NewTermQuery("product.channel_id", channel_id)))
	query = query.Add(elastic.NewMatchAllQuery(), elastic.NewWeightFactorFunction(1))
	query = query.AddScoreFunc(elastic.NewRandomFunction())
	searchResult, err := c.Search(from, size, query).Do(context.Background())
	if err != nil {
		glog.Error(err)
		return nil, err
	}
	return ConvertToChannelProductRequest(searchResult.Hits), nil
}

func (c *ChannelStoreProductIndex) Search(from, size int, query ...elastic.Query) *elastic.SearchService {
	//client := NewEsClient()
	//search := client.Search().Index(IndexChannelStoreProduct)
	search := c.getEsClient().Search().Index(IndexChannelStoreProduct)
	for _, v := range query {
		search.Query(v)
	}
	return search.From(from).Size(size)
}

// 新增
func (c *ChannelStoreProductIndex) Add(id string, body interface{}) (bool, error) {
	_, err := c.getEsClient().Index().Index(IndexChannelStoreProduct).Type("_doc").Id(id).BodyJson(body).Do(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// 删除
func (c *ChannelStoreProductIndex) Delete(id string) (bool, error) {
	_, err := c.getEsClient().Delete().Index(IndexChannelStoreProduct).Type("_doc").Id(id).Do(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// 获取es 链接
func (c *ChannelStoreProductIndex) getEsClient() *elastic.Client {
	c.clientIniter.Do(func() {
		if c.client == nil {
			c.client = NewEsClient()
		}
	})
	return c.client
}

func ConvertToChannelProductRequest(hit *elastic.SearchHits) []*models.ChannelProductRequestEs {
	bbcImgPath := config.GetString("bbc_img_path")
	var cprs = make([]*models.ChannelProductRequestEs, 0)
	for _, v := range hit.Hits {
		var cpr models.ChannelProductRequestEs
		if err := json.Unmarshal(v.Source, &cpr); err != nil {
			glog.Error(err)
			return nil
		}

		if len(cpr.Product.Pic) > 0 {
			var pic string
			pics := cpr.Product.Pic
			ps := strings.Split(cpr.Product.Pic, ",")
			nowCount := 0
			for _, x := range ps {
				if strings.Contains(x, "/watermark") {
					nowCount++
				}
			}

			if nowCount > 1 {

				if len(ps) > 0 {
					pic = ps[0]
				}
				if strings.Contains(pic, "?x-oss-process=image/watermark") { //截取错误
					index := strings.Index(pics, "y_30,voffset_0")
					if index > 0 {
						pic = pics[0:index] + "y_30,voffset_0"
					}
				}
				cpr.Product.Pic = pic
			}

			if !strings.HasPrefix(cpr.Product.Pic, "http") {
				cpr.Product.Pic = bbcImgPath + cpr.Product.Pic
			}
		}
		cprs = append(cprs, &cpr)
	}
	return cprs
}

// 宠物类型（犬，猫）查询
func (c *ChannelStoreProductIndex) QueryByPetTags(petType, financeCode string, from, size int) []*models.ChannelProductRequestEs {
	esClient := NewEsClient()
	var queryString string
	if petType == "猫" || petType == "犬" {
		queryString = fmt.Sprintf("tags:%s AND finance_code.keyword:%s", petType, financeCode)
	} else {
		queryString = fmt.Sprintf("finance_code.keyword:%s", financeCode)
	}

	result, err := esClient.Search().Index(IndexChannelStoreProduct).Query(elastic.NewQueryStringQuery(queryString)).From(from).Size(size).Do(context.Background())
	if err != nil {
		glog.Error(err)
	}
	return ConvertToChannelProductRequest(result.Hits)
}

// 商品搜索 权重：(标题>标签>规格>类目>描述)>销量
func (c *ChannelStoreProductIndex) QueryProduct(keyWords string, isProductType, sort, from, size int, warehouseIdList []int32, financeCode ...string) []*models.ChannelProductRequestEs {
	keyWords = strings.ReplaceAll(keyWords, " ", "")

	client := NewEsClient()
	//client := NewEsClient("http://es-cn-m7r1uawz10006espz.public.elasticsearch.aliyuncs.com:9200", "elastic", "dk3aOf6U") // uat
	//client := NewEsClient("http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200", "chengcong", "ad9uUWN^QqK&luv1") // 线上

	var codes []interface{}
	for _, v := range financeCode {
		codes = append(codes, v)
	}
	var warehouses []interface{}
	for _, v := range warehouseIdList {
		warehouses = append(warehouses, v)
	}
	// bool查询
	q := elastic.NewBoolQuery()
	q.Filter(elastic.NewTermsQuery("finance_code.keyword", codes...))
	if len(warehouses) > 0 {
		q.Filter(elastic.NewTermsQuery("warehouse_id", warehouses...))
	} else {
		q.MustNot(elastic.NewTermsQuery("warehouse_category", 5))
	}
	if isProductType == 1 {
		q.Must(elastic.NewTermsQuery("product.is_virtual", 0))
	} else if isProductType == 2 {
		q.Must(elastic.NewTermsQuery("product.is_virtual", 1))
	}
	// 判断是否包含字母
	var hzRegexp = regexp.MustCompile("^[a-zA-Z]+$")
	if hzRegexp.MatchString(keyWords) { // 拼音，中英文搜索
		q.Should(
			// 不分词，短语匹配
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchPhraseQuery("product.name", keyWords),
			).TieBreaker(0.1).Boost(250),
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKS", keyWords).Analyzer("ik_smart_analyzer").Operator("and"),
			).TieBreaker(0.1).Boost(50),
			// 拼音分词
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKPY", keyWords).Analyzer("ik_pinyin_analyzer").Operator("and"),
			).TieBreaker(0.1).Boost(50),
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKPY", keyWords).Analyzer("ik_pinyin_analyzer"),
			).TieBreaker(0.1).Boost(10),

			//elastic.NewMatchQuery("product.name", keyWords).Boost(15),
			//elastic.NewMatchQuery("tags", keyWords),
			//elastic.NewMatchQuery("sku_info.skuv.spec_value_value", keyWords),
			//elastic.NewMatchQuery("product.channel_category_name", keyWords),
			//elastic.NewMatchQuery("product.selling_point", keyWords),
		)
	} else { // 中文，数字搜索
		q.Should(
			// 匹配整个词
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchPhraseQuery("product.name", keyWords),
			).TieBreaker(0.1).Boost(250),
			// ik_smart分词
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKS", keyWords).Analyzer("ik_smart_analyzer").Operator("and"),
			).TieBreaker(0.1).Boost(50),
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKS", keyWords).Analyzer("ik_smart_analyzer"),
			).TieBreaker(0.1).Boost(10),
			// ik_max_word分词
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKM", keyWords).Analyzer("ik_max_word_analyzer"),
			).TieBreaker(0.1),
		)
	}
	q.MinimumNumberShouldMatch(1)
	if c.Index == "" {
		c.Index = IndexChannelStoreProduct
		glog.Info("非体验版环境，索引为：", c.Index)
	}

	c.Index = IndexChannelStoreProduct
	//c.Index = "channel_store_product_ik"
	//c.Index = "channel_store_product_ik_test"

	search := client.Search(c.Index).Query(q).MinScore(1)

	// "排序方式: 1.综合排序;2.价格升序;3.价格降序;4.销量降序;"
	switch sort {
	case 2:
		search.SortBy(elastic.NewFieldSort("sku_info.market_price").Asc(), elastic.NewScoreSort())
	case 3:
		search.SortBy(elastic.NewFieldSort("sku_info.market_price").Desc(), elastic.NewScoreSort())
	case 4:
		search.SortBy(elastic.NewFieldSort("sku_info.sales_volume").Desc(), elastic.NewScoreSort())
	}

	if r, err := search.From(from).Size(size).Do(context.Background()); err != nil {

		glog.Error(err)
		return []*models.ChannelProductRequestEs{}
	} else {
		return ConvertToChannelProductRequest(r.Hits)
	}
}

type QueryProductReq struct {
	KeyWords      string  `query:"key_words" validate:"required" label:"关键词"`
	CityName      string  `query:"city_name" label:"城市名称"`
	PageIndex     int     `query:"page_index" validate:"required" label:"当前页"`
	Lon           float64 `query:"lon" label:"经度"`
	Lat           float64 `query:"lat" label:"纬度"`
	PageSize      int     `query:"page_size" validate:"required" label:"每页显示数据条数"`
	FinanceCode   string  `query:"finance_code" validate:"required" label:"财务编码，多个财务编码以逗号分隔，电商商城的财务编码为000000"`
	Sort          int     `query:"sort" validate:"required,min=1,max=4" label:"排序方式: 1.综合排序;2.价格升序;3.价格降序;4.销量降序;"`
	AdCode        int     `query:"adcode"  label:"城市行政编码"`
	IsProductType int     `query:"is_product_type" label:"商品类别"`
}

// 商品搜索 权重：(标题>标签>规格>类目>描述)>销量
func (c *ChannelStoreProductIndex) QueryProductByReq(in *QueryProductReq, warehouseIdList []int32) []*models.ChannelProductRequestEs {
	in.KeyWords = strings.ReplaceAll(in.KeyWords, " ", "")

	client := NewEsClient()
	//client := NewEsClient("http://es-cn-m7r1uawz10006espz.public.elasticsearch.aliyuncs.com:9200", "elastic", "dk3aOf6U") // uat
	//client := NewEsClient("http://es-cn-nif1zqxhb000cei3i.public.elasticsearch.aliyuncs.com:9200", "chengcong", "ad9uUWN^QqK&luv1") // 线上

	var codes []interface{}
	for _, v := range strings.Split(in.FinanceCode, ",") {
		codes = append(codes, v)
	}
	var warehouses []interface{}
	for _, v := range warehouseIdList {
		warehouses = append(warehouses, v)
	}
	// bool查询
	q := elastic.NewBoolQuery()
	q.Filter(elastic.NewTermsQuery("finance_code.keyword", codes...))

	if len(warehouses) > 0 {
		q.Filter(elastic.NewTermsQuery("warehouse_id", warehouses...))
	} else {
		q.MustNot(elastic.NewTermsQuery("warehouse_category", 5))
	}

	if in.IsProductType == 1 {
		q.Must(elastic.NewTermsQuery("product.is_virtual", 0))
	} else if in.IsProductType == 2 {
		q.Must(elastic.NewTermsQuery("product.is_virtual", 1))
	}

	// 虚拟商品按城市过滤
	if len(in.CityName) > 0 {
		cq := elastic.NewBoolQuery()
		cq.Should(
			elastic.NewBoolQuery().MustNot(elastic.NewTermQuery("product.channel_id", "5")), // 非电商不过滤
			elastic.NewTermQuery("product.is_virtual", 0),                                   // 实物商品没有城市
			elastic.NewTermQuery("region_id", 0),                                            // 全国的没有城市
			elastic.NewTermQuery("cities.keyword", in.CityName),                             // 包含城市的展示
		)
		cq.MinimumNumberShouldMatch(1)
		q.Must(cq)
	}

	// 判断是否包含字母
	var hzRegexp = regexp.MustCompile("^[a-zA-Z]+$")
	if hzRegexp.MatchString(in.KeyWords) { // 拼音，中英文搜索
		q.Should(
			// 不分词，短语匹配
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchPhraseQuery("product.name", in.KeyWords),
			).TieBreaker(0.1).Boost(250),
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKS", in.KeyWords).Analyzer("ik_smart_analyzer").Operator("and"),
			).TieBreaker(0.1).Boost(50),
			// 拼音分词
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKPY", in.KeyWords).Analyzer("ik_pinyin_analyzer").Operator("and"),
			).TieBreaker(0.1).Boost(50),
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKPY", in.KeyWords).Analyzer("ik_pinyin_analyzer"),
			).TieBreaker(0.1).Boost(10),

			//elastic.NewMatchQuery("product.name", in.KeyWords).Boost(15),
			//elastic.NewMatchQuery("tags", in.KeyWords),
			//elastic.NewMatchQuery("sku_info.skuv.spec_value_value", in.KeyWords),
			//elastic.NewMatchQuery("product.channel_category_name", in.KeyWords),
			//elastic.NewMatchQuery("product.selling_point", in.KeyWords),
		)
	} else { // 中文，数字搜索
		q.Should(
			// 匹配整个词
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchPhraseQuery("product.name", in.KeyWords),
			).TieBreaker(0.1).Boost(250),
			// ik_smart分词
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKS", in.KeyWords).Analyzer("ik_smart_analyzer").Operator("and"),
			).TieBreaker(0.1).Boost(50),
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKS", in.KeyWords).Analyzer("ik_smart_analyzer"),
			).TieBreaker(0.1).Boost(10),
			// ik_max_word分词
			elastic.NewDisMaxQuery().Query(
				elastic.NewMatchQuery("product.name.IKM", in.KeyWords).Analyzer("ik_max_word_analyzer"),
			).TieBreaker(0.1),
		)
	}
	q.MinimumNumberShouldMatch(1)
	if c.Index == "" {
		c.Index = IndexChannelStoreProduct
		glog.Info("非体验版环境，索引为：", c.Index)
	}

	//c.Index = IndexChannelStoreProduct
	//c.Index = "channel_store_product_ik"
	//c.Index = "channel_store_product_ik_test"

	search := client.Search(c.Index).Query(q).MinScore(1)

	// 优先显示VIP商品
	// "排序方式: 1.综合排序;2.价格升序;3.价格降序;4.销量降序;"
	switch in.Sort {
	case 1:
		search.SortBy(elastic.NewFieldSort("product.is_vip").Desc(), elastic.NewScoreSort())
	case 2:
		search.SortBy(elastic.NewFieldSort("product.is_vip").Desc(), elastic.NewFieldSort("sku_info.market_price").Asc(), elastic.NewScoreSort())
	case 3:
		search.SortBy(elastic.NewFieldSort("product.is_vip").Desc(), elastic.NewFieldSort("sku_info.market_price").Desc(), elastic.NewScoreSort())
	case 4:
		search.SortBy(elastic.NewFieldSort("product.is_vip").Desc(), elastic.NewFieldSort("sku_info.sales_volume").Desc(), elastic.NewScoreSort())
	}

	if r, err := search.From(in.PageIndex*in.PageSize - in.PageSize).Size(in.PageSize).Do(context.Background()); err != nil {
		glog.Error(err)
		return []*models.ChannelProductRequestEs{}
	} else {
		return ConvertToChannelProductRequest(r.Hits)
	}
}

func FormatChannelProductRequestEs(p *pc.ChannelProductEsBaseData) models.ChannelProductRequestEs {
	var jd pc.ChannelProductRequest
	json.Unmarshal([]byte(p.JsonData), &jd)

	//组合商品的sku明细
	var skuGroup []*models.SkuGroupEs
	for _, v := range jd.SkuInfo[0].SkuGroup {
		skuGroup = append(skuGroup, &models.SkuGroupEs{
			ProductId:      v.ProductId,
			SkuId:          v.SkuId,
			GroupProductId: v.GroupProductId,
			GroupSkuId:     v.GroupSkuId,
			Count:          v.Count,
			DiscountType:   v.DiscountType,
			DiscountValue:  v.DiscountValue,
			MarketPrice:    v.MarketPrice,
			ProductType:    v.ProductType,
		})
	}

	m := models.ChannelProductRequestEs{
		UpdateDate:        p.UpdateDate,
		FinanceCode:       p.FinanceCode,
		Tags:              p.Tags,
		WarehouseId:       p.WarehouseId,
		WarehouseCategory: p.WarehouseCategory,
		Product: &models.ChannelProductEs{
			Id:                  jd.Product.Id,
			Name:                jd.Product.Name,
			ShortName:           jd.Product.ShortName,
			Pic:                 jd.Product.Pic,
			SellingPoint:        jd.Product.SellingPoint,
			ChannelId:           jd.Product.ChannelId,
			ChannelCategoryId:   jd.Product.ChannelCategoryId,
			ChannelCategoryName: jd.Product.ChannelCategoryName,
			ProductType:         jd.Product.ProductType,
		},
		SkuInfo: []*models.SkuInfoEs{
			{
				SkuId:         jd.SkuInfo[0].SkuId,
				MarketPrice:   jd.SkuInfo[0].MarketPrice,
				SalesVolume:   p.SalesVolume,
				RetailPrice:   jd.SkuInfo[0].RetailPrice,
				WeightForUnit: jd.SkuInfo[0].WeightForUnit,
				SkuGroup:      skuGroup,
			},
		},
	}

	for _, v := range jd.SkuInfo[0].Skuv {
		m.SkuInfo[0].Skuv = append(m.SkuInfo[0].Skuv, &models.SkuvEs{SpecName: v.SpecName, SpecValueValue: v.SpecValueValue})
	}
	return m
}

func FormatUpetGoodsToChannelProductRequestEs(p models.UpetGoodsExt) models.ChannelProductRequestEs {
	var productType int32
	if p.IsVirtual == 1 {
		productType = 2
	} else if p.GoodsType == 0 {
		productType = 1
	} else {
		productType = 3
	}

	m := models.ChannelProductRequestEs{
		FinanceCode: "000000",
		RegionId:    p.RegionId,
		Tags:        p.Tags,
		Cities:      p.Cities,
		UpdateDate:  time.Unix(int64(p.GoodsEdittime), 0).In(time.Local).Format(kit.DATETIME_LAYOUT),
		Product: &models.ChannelProductEs{
			Id:                  p.GoodsCommonid,
			Name:                p.GoodsName,
			ShortName:           p.ShortName,
			Pic:                 p.GoodsImage,
			SellingPoint:        p.GoodsJingle,
			ChannelId:           "5", //电商渠道为5
			ChannelCategoryId:   p.GcId,
			ChannelCategoryName: p.ChannelCategoryName,
			IsVirtual:           p.IsVirtual,
			ProductType:         productType,
		},
		SkuInfo: []*models.SkuInfoEs{
			{
				SkuId:          p.GoodsId,
				MarketPrice:    DecimalToInt(p.GoodsPrice),
				MemberPrice:    DecimalToInt(p.MemberPrice1),
				SalesVolume:    p.GoodsSalenum,
				PromotionType:  p.GoodsPromotionType,
				PromotionPrice: DecimalToInt(p.GoodsPromotionPrice),
			},
		},
	}

	// 提取php序列化数组规格值
	reg, _ := regexp.Compile(`:\"(.*?)\";`)
	specNames := reg.FindAllStringSubmatch(p.SpecName, -1)
	specValues := reg.FindAllStringSubmatch(p.GoodsSpec, -1)
	for i, sn := range specNames {
		if len(specValues) > i {
			m.SkuInfo[0].Skuv = append(m.SkuInfo[0].Skuv, &models.SkuvEs{
				SpecName:       sn[1],
				SpecValueValue: html.UnescapeString(specValues[i][1]),
			})
		}
	}
	return m
}

func DecimalToInt(num float64) int32 {
	decimalValue := decimal.NewFromFloat(num)
	decimalValue = decimalValue.Mul(decimal.NewFromInt(100))
	res, _ := decimalValue.Float64()
	return int32(res)
}
