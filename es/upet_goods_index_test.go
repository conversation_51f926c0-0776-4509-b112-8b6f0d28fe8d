package es

import (
	"reflect"
	"testing"
)

func TestUpetGoodsIndex_QueryTerms(t *testing.T) {
	type args struct {
		from   int
		size   int
		sku_id []int32
	}
	tests := []struct {
		name    string
		c       *UpetGoodsIndex
		args    args
		want    []*UpetGoods
		wantErr bool
	}{
		{
			name: "查询电商在售商品",
			c:    &UpetGoodsIndex{},
			args: args{
				from:   0,
				size:   1,
				sku_id: []int32{100017},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryTerms(tt.args.sku_id)
			println(len(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("UpetGoodsIndex.QueryTerms() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpetGoodsIndex.QueryTerms() = %v, want %v", got, tt.want)
			}
		})
	}
}
func TestEs(t *testing.T) {
	/*q := elastic.NewQueryStringQuery("犬").Field("tags")
	q2 := elastic.NewQueryStringQuery("CX0013").Field("finance_code")

	esClient := NewEsClient()
	result, err := esClient.Search().Index("channel_store_product").Query(q).Query(q2).From(0).Size(10).Do(context.Background())
	if err != nil {
		t.Error(err)
	}

	upetGoods := ConvertToChannelProductRequest(result.Hits)
	for _, v := range upetGoods {
		println(v.Tags)
	}*/

}
