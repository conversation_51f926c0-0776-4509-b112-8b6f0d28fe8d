package es

import (
	"_/proto/models"
	"_/proto/pc"
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"sync"
	"testing"

	"github.com/olivere/elastic/v7"
)

func TestChannelStoreProductIndex_QueryTerms(t *testing.T) {
	type args struct {
		finance_code string
		channel_id   int
		from         int
		size         int
		sku_id       []int32
	}
	tests := []struct {
		name    string
		c       *ChannelStoreProductIndex
		args    args
		want    []*pc.ChannelProductRequest
		wantErr bool
	}{
		{
			name: "查询ES渠道门店商品",
			c:    &ChannelStoreProductIndex{},
			args: args{
				finance_code: "CX0004",
				channel_id:   1,
				from:         0,
				size:         1,
				sku_id:       []int32{1018661001},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryTerms(tt.args.finance_code, tt.args.channel_id, tt.args.sku_id)
			if (err != nil) != tt.wantErr {
				println(len(got))
				t.<PERSON>("ChannelStoreProductIndex.QueryTerms() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChannelStoreProductIndex.QueryTerms() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelStoreProductIndex_QueryRandom(t *testing.T) {
	type args struct {
		finance_code string
		channel_id   int
		from         int
		size         int
	}
	tests := []struct {
		name    string
		c       *ChannelStoreProductIndex
		args    args
		want    []*pc.ChannelProductRequest
		wantErr bool
	}{
		{
			name: "随机查询门店商品",
			c:    &ChannelStoreProductIndex{},
			args: args{
				finance_code: "CX0109",
				channel_id:   1,
				from:         0,
				size:         10,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryRandom(tt.args.finance_code, tt.args.channel_id, tt.args.from, tt.args.size)
			println(len(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("ChannelStoreProductIndex.QueryRandom() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChannelStoreProductIndex.QueryRandom() = %v, want %v", got, tt.want)
			}
		})
	}
}

// 测试删除
func TestChannelStoreProductIndex_Delete(t *testing.T) {
	var c = new(ChannelStoreProductIndex)
	var waitGroup sync.WaitGroup
	for i := 0; i < 100; i++ {
		waitGroup.Add(1)
		go func(index int) {
			defer func() {
				waitGroup.Done()
			}()
			_, err := c.Delete(strconv.Itoa(index))
			if err != nil {
				t.Error(err)
			}
		}(i)
	}
	waitGroup.Wait()

}

func TestCat(t *testing.T) {
	es := NewEsClient("http://es-cn-n6w1rdbxt0002d0jl.public.elasticsearch.aliyuncs.com:9200", "elastic", "kCC6y672Wexjw44a") // sit

	var m models.ChannelProductRequestEs
	jsonStr := `{"product":{"id":1023118,"name":"【新瑞鹏全国】爱宠健康订阅-标签测试4","short_name":"健康订阅-标签测试","pic":"http://file.vetscloud.com/047a9785594a0c234dfa6e18adf09a8d","selling_point":"","channel_id":"5","channel_category_id":1183,"channel_category_name":"","is_virtual":1,"product_type":2,"term_type":0,"term_value":0,"virtual_invalid_refund":0},"sku_info":[{"sku_id":1023118001,"market_price":990,"retail_price":0,"member_price":0,"sales_volume":32,"promotion_type":0,"promotion_price":990,"skuv":null,"weight_for_unit":0,"sku_group":null}],"finance_code":"000000","warehouse_id":0,"tags":"","region_id":9,"update_date":"","warehouse_category":0}`
	json.Unmarshal([]byte(jsonStr), &m)
	fmt.Println(fmt.Sprintf("%s-5-%d", m.FinanceCode, m.SkuInfo[0].SkuId))
	esRes, err := es.Index().Index(IndexChannelStoreProduct).Id(fmt.Sprintf("%s-5-%d", m.FinanceCode, m.SkuInfo[0].SkuId)).BodyJson(m).Do(context.Background())
	fmt.Println(esRes, err)
	//delRes, err := es.Delete().Index(IndexChannelStoreProduct).Id(fmt.Sprintf("%s-5-%d", m.FinanceCode, m.SkuInfo[0].SkuId)).Do(context.Background())
	//fmt.Println(delRes, err)

	q := elastic.NewBoolQuery()
	q.Must(elastic.NewMatchPhraseQuery("_id", "000000-5-1023118001"))
	r, _ := es.Search(IndexChannelStoreProduct).Query(q).Do(context.Background())
	res := ConvertToChannelProductRequest(r.Hits)
	for _, v := range res {
		fmt.Println(v.Product)
	}
}

type ChannelStoreProductIndexTest struct {
	//复用 es 链接对象
	client       *elastic.Client
	clientIniter sync.Once
}

func (c *ChannelStoreProductIndexTest) getEsClient() *elastic.Client {
	c.clientIniter.Do(func() {
		if c.client == nil {
			c.client = NewEsClient()
		}
	})
	return c.client
}

func TestChannelStoreProductIndex_QueryProduct(t *testing.T) {
	type args struct {
		keyWords        string
		sort            int
		from            int
		size            int
		is_product_type int
		financeCode     []string
	}
	tests := []struct {
		name string
		c    *ChannelStoreProductIndex
		args args
		want []*pc.ChannelProductRequest
	}{
		{
			name: "",
			c:    &ChannelStoreProductIndex{},
			args: args{
				//keyWords: "活性炭豆腐猫砂",
				//keyWords: "麦富迪犬用北",
				//keyWords: "猫罐头",
				//keyWords: "消费",
				//keyWords: "云uat周期购",
				keyWords: "驱虫",
				// "排序方式: 1.综合排序;2.价格升序;3.价格降序;4.销量降序;"
				sort:            4,
				from:            0,
				size:            1000,
				is_product_type: 2,
				//financeCode: []string{"RP0228", "000000"},
				financeCode: []string{"000000"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fmt.Println("搜索词：", tt.args.keyWords)
			res := tt.c.QueryProduct(tt.args.keyWords, tt.args.is_product_type, tt.args.sort, tt.args.from, tt.args.size, []int32{}, tt.args.financeCode...)
			if len(res) > 0 {
				for k, v := range res {
					fmt.Println(k, v.Product.Name, v.Product.IsVirtual, v.SkuInfo[0].SkuId, v.SkuInfo[0].PromotionPrice, v.FinanceCode)
					//fmt.Println(k, v.Product.Name, v.SkuInfo[0].PromotionPrice, "tag-"+v.Tags, "SpecValueValue-"+v.SkuInfo[0].Skuv[0].SpecValueValue, "channelName-"+v.Product.ChannelCategoryName, v.Product.SellingPoint)
				}
			}
			fmt.Println(res)
		})
	}
}

func TestChannelStoreProductIndex_QueryProductByReq(t *testing.T) {
	type fields struct {
		client       *elastic.Client
		clientIniter sync.Once
		Index        string
	}
	type args struct {
		in              *QueryProductReq
		warehouseIdList []int32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*models.ChannelProductRequestEs
	}{
		{
			name: "",
			args: args{
				in: &QueryProductReq{
					KeyWords:    "专享",
					CityName:    "上海市",
					PageIndex:   1,
					PageSize:    10000,
					FinanceCode: "000000,CX0003",
					//IsProductType: 2,
				},
				warehouseIdList: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ChannelStoreProductIndex{
				client:       tt.fields.client,
				clientIniter: tt.fields.clientIniter,
				Index:        tt.fields.Index,
			}
			got := c.QueryProductByReq(tt.args.in, tt.args.warehouseIdList)
			t.Log(got)
		})
	}
}
