package es

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"testing"

	"github.com/olivere/elastic/v7"
)

func TestStoreIndex_GeoDistanceQuery(t *testing.T) {
	type args struct {
		lat  float64
		lon  float64
		from int
		size int
	}
	tests := []struct {
		name    string
		c       *StoreIndex
		args    args
		want    []*StoreDoc
		wantErr bool
	}{
		{
			name: "查询附近门店",
			c:    &StoreIndex{},
			args: args{
				lat:  22.521361,
				lon:  114.036191,
				from: 0,
				size: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryGeoDistance(tt.args.lat, tt.args.lon, tt.args.from, tt.args.size)
			println(len(got))
			if (err != nil) != tt.wantErr {
				t.<PERSON><PERSON>("StoreIndex.QueryGeoDistance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StoreIndex.QueryGeoDistance() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestQueryStore(t *testing.T) {
	client := NewEsClient()
	q := elastic.NewBoolQuery()
	// q.Filter(elastic.newgeo)

	distance := elastic.NewGeoDistanceSort("location_point").Point(22.653315, 114.030649).Asc().Unit("km")

	s := client.Search(IndexStore).
		Query(q).
		From(0).
		Size(999).
		SortBy(distance)

	src, err := distance.Source()
	if err != nil {
		t.Fatal(err)
	}
	data, err := json.Marshal(src)
	if err != nil {
		t.Fatalf("marshaling to JSON failed: %v", err)
	}
	t.Log(string(data))

	r, err := s.Do(context.Background())
	if err != nil {
		t.Error(err)
	} else {
		doc := ConvertToStoreDoc(r.Hits)
		for _, v := range doc {
			fmt.Println(v.Name, v.Distance)
		}
	}
}

func TestStoreIndex_QueryStore(t *testing.T) {
	type args struct {
		w *StoreSearchWhere
	}
	tests := []struct {
		name string
		c    *StoreIndex
		args args
		want []*StoreDoc
	}{
		{
			name: "医院查询",
			c:    &StoreIndex{},
			args: args{
				w: &StoreSearchWhere{
					Keywords: "深圳景田",
					CityName: "深圳市",
					Lat:      22.526685,
					Lon:      114.055561,
					Type: 2,
					// Size: 5,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.c.QueryStore(tt.args.w)
			println(len(got))
			for _, v := range got {
				// fmt.Println(v.HospitalCity,v.Name, v.Tags, v.Distance)
				println(v.Name,v.Distance,"km")
			}
		})
	}
}
