package es

import (
	"context"
	"encoding/json"
	"strings"
	"sync"

	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
)

type ProductDict struct {
	//复用 es 链接对象
	client       *elastic.Client
	clientIniter sync.Once
}

//中文分词
func (p *ProductDict) IkAnalyze(keyword string) []elastic.AnalyzeToken {
	client := NewEsClient()
	if res, err := client.IndexAnalyze().Analyzer("aliws").Text(keyword).Do(context.Background()); err != nil {
		glog.Error(err)
		return []elastic.AnalyzeToken{}
	} else {
		return res.Tokens
	}
}

//拼音分词
func (p *ProductDict) PinyinAnalyze(keyword string) []elastic.AnalyzeToken {
	client := NewEsClient()
	if res, err := client.IndexAnalyze().Analyzer("pinyin_analyzer").Index(IndexProductDict).Text(keyword).Do(context.Background()); err != nil {
		glog.Error(err)
		return []elastic.AnalyzeToken{}
	} else {
		return res.Tokens
	}
}

//每两个邻近的单字（词）组在一起
func (p *ProductDict) RegroupDict(s []string) []string {
	var dict []string
	for i, v := range s {
		for i2, v2 := range s {
			if i2 > i {
				dict = append(dict, v+v2)
				break
			}
		}
	}
	return dict
}

//将分词后的token用指定分隔符串起来
func (p *ProductDict) JoinTokens(tokens []elastic.AnalyzeToken, sep string) string {
	return strings.Join(p.TokensToArray(tokens), sep)
}

//将分词后的token转成string数组
func (p *ProductDict) TokensToArray(tokens []elastic.AnalyzeToken) []string {
	var s []string
	for _, v := range tokens {
		s = append(s, v.Token)
	}
	return s
}

//比较两个词的相似度
func (p *ProductDict) CompareDict(dict []string, dict2 []string) int {
	var score int
	for _, v := range dict {
		for _, v2 := range dict2 {
			if v == v2 {
				score++
			}
		}
	}
	return score
}

//每两个中文组新词
func (p *ProductDict) RegroupDictIk(tokens []elastic.AnalyzeToken) []string {
	var dict []string
	for _, v := range tokens {
		if len(v.Token) >= 6 {
			for _, v2 := range tokens {
				if len(v2.Token) >= 6 && v2.Position > v.Position {
					dict = append(dict, v.Token+" "+v2.Token)
					break
				}
			}
		}
	}
	return dict
}

//根据关键词提示联想词列表
func (p *ProductDict) QueryWord(keyword, style string, from, size int) ([]*AssociationalWord, error) {
	keyword = strings.ReplaceAll(keyword, " ", "")
	bq := elastic.NewBoolQuery()
	if style != "" {
		bq.Filter(elastic.NewMatchQuery("type.keyword", style))
	}

	// bq.Should(
	// 	elastic.NewMatchQuery("token", keyword),
	// 	elastic.NewMatchQuery("token.pinyin", keyword),
	// )
	bq.Must(elastic.NewMatchQuery("token", keyword))

	if res, err := NewEsClient().
		Search(IndexProductDict).
		Query(bq).
		From(from).
		Size(size).
		MinScore(1).
		Do(context.Background()); err != nil {
		glog.Error(err)
		return nil, err
	} else {
		return ConvertToWord(res.Hits), nil
	}
}

// 获取es 链接
func (p *ProductDict) getEsClient() *elastic.Client {
	p.clientIniter.Do(func() {
		if p.client == nil {
			p.client = NewEsClient()
		}
	})
	return p.client
}

func ConvertToWord(hit *elastic.SearchHits) []*AssociationalWord {
	var cprs []*AssociationalWord
	for _, v := range hit.Hits {
		var cpr AssociationalWord
		if err := json.Unmarshal(v.Source, &cpr); err != nil {
			glog.Error(err)
			return nil
		}
		cprs = append(cprs, &cpr)
		// fmt.Println(cpr.Token, *v.Score)
	}
	return cprs
}

type AssociationalWord struct {
	Token string `json:"token"` //联想词
	Type  string `json:"type"`  //联想词类型
	Logo  string `json:"logo"`  //logo图片
}
