package es

import (
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
)

var (
	IndexStore               = "store"                 //医院
	IndexChannelStoreProduct = "channel_store_product" //商品
	IndexProductDict         = "product_dict"          //词库
)

func init() {
	productIndex := config.GetString("elasticsearch.ChannelStoreProductIndex")
	if productIndex != "" {
		IndexChannelStoreProduct = productIndex
	}
	glog.Info("当前环境商品索引 ", IndexChannelStoreProduct)
}

//哪个项目需要调用，需要在配置系统对应的项目，添加es相关的配置

func NewEsClient(url_name_pwd ...string) *elastic.Client {
	var esUrl, esUname, esPwd string
	if len(url_name_pwd) == 3 {
		esUrl = url_name_pwd[0]
		esUname = url_name_pwd[1]
		esPwd = url_name_pwd[2]
	} else {
		esUrl = config.GetString("elasticsearch")
		esUname = config.GetString("elasticsearch.LoginName")
		esPwd = config.GetString("elasticsearch.Password")
	}

	//// 测试es uat环境的配置
	//esUrl = "http://es-cn-m7r1uawz10006espz.public.elasticsearch.aliyuncs.com:9200"
	//esUname = "elastic"
	//esPwd = "dk3aOf6U"

	if es, err := elastic.NewClient(
		elastic.SetSniff(false),
		elastic.SetHealthcheck(false),
		elastic.SetURL(esUrl),
		elastic.SetBasicAuth(esUname, esPwd),
	); err != nil {
		glog.Error("es ============= client error: ", err)
		return nil
	} else {
		glog.Info("es ============= client successful")
		return es
	}
}
