// Code generated by protoc-gen-go. DO NOT EDIT.
// source: recommend/Recommend_rppet.proto

package recommend

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// The request message containing the user's name.
type TipRequest struct {
	Userid               string   `protobuf:"bytes,1,opt,name=userid,proto3" json:"userid"`
	Size                 int32    `protobuf:"varint,2,opt,name=size,proto3" json:"size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TipRequest) Reset()         { *m = TipRequest{} }
func (m *TipRequest) String() string { return proto.CompactTextString(m) }
func (*TipRequest) ProtoMessage()    {}
func (*TipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7f6e8a03c7b375d4, []int{0}
}

func (m *TipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TipRequest.Unmarshal(m, b)
}
func (m *TipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TipRequest.Marshal(b, m, deterministic)
}
func (m *TipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TipRequest.Merge(m, src)
}
func (m *TipRequest) XXX_Size() int {
	return xxx_messageInfo_TipRequest.Size(m)
}
func (m *TipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TipRequest proto.InternalMessageInfo

func (m *TipRequest) GetUserid() string {
	if m != nil {
		return m.Userid
	}
	return ""
}

func (m *TipRequest) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

// The response message containing the greetings
type RcmdTipsReply struct {
	Message              string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message"`
	Host                 string   `protobuf:"bytes,2,opt,name=host,proto3" json:"host"`
	Returncode           int32    `protobuf:"varint,3,opt,name=returncode,proto3" json:"returncode"`
	Tips                 string   `protobuf:"bytes,4,opt,name=tips,proto3" json:"tips"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RcmdTipsReply) Reset()         { *m = RcmdTipsReply{} }
func (m *RcmdTipsReply) String() string { return proto.CompactTextString(m) }
func (*RcmdTipsReply) ProtoMessage()    {}
func (*RcmdTipsReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_7f6e8a03c7b375d4, []int{1}
}

func (m *RcmdTipsReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdTipsReply.Unmarshal(m, b)
}
func (m *RcmdTipsReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdTipsReply.Marshal(b, m, deterministic)
}
func (m *RcmdTipsReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdTipsReply.Merge(m, src)
}
func (m *RcmdTipsReply) XXX_Size() int {
	return xxx_messageInfo_RcmdTipsReply.Size(m)
}
func (m *RcmdTipsReply) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdTipsReply.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdTipsReply proto.InternalMessageInfo

func (m *RcmdTipsReply) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RcmdTipsReply) GetHost() string {
	if m != nil {
		return m.Host
	}
	return ""
}

func (m *RcmdTipsReply) GetReturncode() int32 {
	if m != nil {
		return m.Returncode
	}
	return 0
}

func (m *RcmdTipsReply) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

type ProdRequest struct {
	Userid               string   `protobuf:"bytes,1,opt,name=userid,proto3" json:"userid"`
	Chainid              string   `protobuf:"bytes,2,opt,name=chainid,proto3" json:"chainid"`
	Size                 int32    `protobuf:"varint,3,opt,name=size,proto3" json:"size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProdRequest) Reset()         { *m = ProdRequest{} }
func (m *ProdRequest) String() string { return proto.CompactTextString(m) }
func (*ProdRequest) ProtoMessage()    {}
func (*ProdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7f6e8a03c7b375d4, []int{2}
}

func (m *ProdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProdRequest.Unmarshal(m, b)
}
func (m *ProdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProdRequest.Marshal(b, m, deterministic)
}
func (m *ProdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProdRequest.Merge(m, src)
}
func (m *ProdRequest) XXX_Size() int {
	return xxx_messageInfo_ProdRequest.Size(m)
}
func (m *ProdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ProdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ProdRequest proto.InternalMessageInfo

func (m *ProdRequest) GetUserid() string {
	if m != nil {
		return m.Userid
	}
	return ""
}

func (m *ProdRequest) GetChainid() string {
	if m != nil {
		return m.Chainid
	}
	return ""
}

func (m *ProdRequest) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

// The response message containing the greetings
type RcmdProdsReply struct {
	Message              string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message"`
	Host                 string   `protobuf:"bytes,2,opt,name=host,proto3" json:"host"`
	Returncode           int32    `protobuf:"varint,3,opt,name=returncode,proto3" json:"returncode"`
	Prods                string   `protobuf:"bytes,4,opt,name=prods,proto3" json:"prods"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RcmdProdsReply) Reset()         { *m = RcmdProdsReply{} }
func (m *RcmdProdsReply) String() string { return proto.CompactTextString(m) }
func (*RcmdProdsReply) ProtoMessage()    {}
func (*RcmdProdsReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_7f6e8a03c7b375d4, []int{3}
}

func (m *RcmdProdsReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdProdsReply.Unmarshal(m, b)
}
func (m *RcmdProdsReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdProdsReply.Marshal(b, m, deterministic)
}
func (m *RcmdProdsReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdProdsReply.Merge(m, src)
}
func (m *RcmdProdsReply) XXX_Size() int {
	return xxx_messageInfo_RcmdProdsReply.Size(m)
}
func (m *RcmdProdsReply) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdProdsReply.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdProdsReply proto.InternalMessageInfo

func (m *RcmdProdsReply) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RcmdProdsReply) GetHost() string {
	if m != nil {
		return m.Host
	}
	return ""
}

func (m *RcmdProdsReply) GetReturncode() int32 {
	if m != nil {
		return m.Returncode
	}
	return 0
}

func (m *RcmdProdsReply) GetProds() string {
	if m != nil {
		return m.Prods
	}
	return ""
}

func init() {
	proto.RegisterType((*TipRequest)(nil), "recommend.TipRequest")
	proto.RegisterType((*RcmdTipsReply)(nil), "recommend.RcmdTipsReply")
	proto.RegisterType((*ProdRequest)(nil), "recommend.ProdRequest")
	proto.RegisterType((*RcmdProdsReply)(nil), "recommend.RcmdProdsReply")
}

func init() { proto.RegisterFile("recommend/Recommend_rppet.proto", fileDescriptor_7f6e8a03c7b375d4) }

var fileDescriptor_7f6e8a03c7b375d4 = []byte{
	// 316 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x92, 0x3d, 0x4f, 0xf3, 0x30,
	0x14, 0x85, 0x9b, 0x7e, 0xaa, 0xf7, 0x7d, 0x5b, 0x09, 0x0b, 0x2a, 0xd3, 0x01, 0xaa, 0x4c, 0x1d,
	0x50, 0x90, 0xca, 0xc2, 0x86, 0xd4, 0xb2, 0x22, 0x45, 0xa6, 0xcc, 0xa8, 0xc4, 0x57, 0xa9, 0x25,
	0x12, 0xbb, 0xb6, 0x33, 0xc0, 0xcf, 0xe1, 0x97, 0x22, 0xbb, 0xf9, 0x2a, 0x0b, 0x13, 0xdb, 0x3d,
	0x96, 0xcf, 0x71, 0x9e, 0x73, 0x03, 0xd7, 0x1a, 0x13, 0x99, 0x65, 0x98, 0xf3, 0x5b, 0x56, 0x4d,
	0xaf, 0x5a, 0x29, 0xb4, 0x91, 0xd2, 0xd2, 0x4a, 0x32, 0xae, 0x2f, 0x84, 0xf7, 0x00, 0x5b, 0xa1,
	0x18, 0x1e, 0x0a, 0x34, 0x96, 0xcc, 0x60, 0x58, 0x18, 0xd4, 0x82, 0xd3, 0x60, 0x11, 0x2c, 0xc7,
	0xac, 0x54, 0x84, 0x40, 0xdf, 0x88, 0x4f, 0xa4, 0xdd, 0x45, 0xb0, 0x1c, 0x30, 0x3f, 0x87, 0x07,
	0x98, 0xb0, 0x24, 0xe3, 0x5b, 0xa1, 0x0c, 0x43, 0xf5, 0xfe, 0x41, 0x28, 0x8c, 0x32, 0x34, 0x66,
	0x97, 0x62, 0xe9, 0xae, 0xa4, 0xb3, 0xef, 0xa5, 0xb1, 0xde, 0x3e, 0x66, 0x7e, 0x26, 0x57, 0x00,
	0x1a, 0x6d, 0xa1, 0xf3, 0x44, 0x72, 0xa4, 0x3d, 0x1f, 0xdc, 0x3a, 0x71, 0x1e, 0x2b, 0x94, 0xa1,
	0xfd, 0xa3, 0xc7, 0xcd, 0xe1, 0x33, 0xfc, 0x8b, 0xb5, 0xe4, 0xbf, 0x7d, 0x2d, 0x85, 0x51, 0xb2,
	0xdf, 0x89, 0x5c, 0xf0, 0xf2, 0xc5, 0x4a, 0xd6, 0x1c, 0xbd, 0x16, 0x87, 0x85, 0xa9, 0xe3, 0x70,
	0xc1, 0x7f, 0x02, 0x72, 0x0e, 0x03, 0xe5, 0xb2, 0x4b, 0x92, 0xa3, 0x58, 0xc5, 0x30, 0xa9, 0x77,
	0xe3, 0x2a, 0x24, 0x0f, 0x00, 0x29, 0x5a, 0x96, 0x71, 0x47, 0x4a, 0x2e, 0xa2, 0x7a, 0x45, 0x51,
	0xb3, 0x9f, 0x39, 0x6d, 0x1d, 0x9f, 0x94, 0x1f, 0x76, 0x56, 0x2f, 0x30, 0xad, 0x13, 0x3d, 0x0c,
	0xd9, 0xc0, 0x7f, 0x17, 0x59, 0xc1, 0x91, 0x59, 0xcb, 0xdd, 0xea, 0x71, 0x7e, 0xf9, 0x23, 0xb5,
	0xa9, 0x22, 0xec, 0xac, 0x6f, 0xe0, 0x4c, 0xc8, 0x28, 0xd5, 0x2a, 0x69, 0x6e, 0xad, 0x4f, 0x5e,
	0xb2, 0x32, 0x0e, 0xbe, 0xba, 0x7d, 0xb6, 0x79, 0x7a, 0x7c, 0x1b, 0xfa, 0x1f, 0xec, 0xee, 0x3b,
	0x00, 0x00, 0xff, 0xff, 0x6c, 0x7e, 0x4f, 0xd3, 0x83, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RecommendTipsClient is the client API for RecommendTips service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RecommendTipsClient interface {
	// Sends a greeting
	GetRmdtips(ctx context.Context, in *TipRequest, opts ...grpc.CallOption) (*RcmdTipsReply, error)
}

type recommendTipsClient struct {
	cc *grpc.ClientConn
}

func NewRecommendTipsClient(cc *grpc.ClientConn) RecommendTipsClient {
	return &recommendTipsClient{cc}
}

func (c *recommendTipsClient) GetRmdtips(ctx context.Context, in *TipRequest, opts ...grpc.CallOption) (*RcmdTipsReply, error) {
	out := new(RcmdTipsReply)
	err := c.cc.Invoke(ctx, "/recommend.RecommendTips/getRmdtips", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RecommendTipsServer is the server API for RecommendTips service.
type RecommendTipsServer interface {
	// Sends a greeting
	GetRmdtips(context.Context, *TipRequest) (*RcmdTipsReply, error)
}

// UnimplementedRecommendTipsServer can be embedded to have forward compatible implementations.
type UnimplementedRecommendTipsServer struct {
}

func (*UnimplementedRecommendTipsServer) GetRmdtips(ctx context.Context, req *TipRequest) (*RcmdTipsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRmdtips not implemented")
}

func RegisterRecommendTipsServer(s *grpc.Server, srv RecommendTipsServer) {
	s.RegisterService(&_RecommendTips_serviceDesc, srv)
}

func _RecommendTips_GetRmdtips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecommendTipsServer).GetRmdtips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/recommend.RecommendTips/GetRmdtips",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecommendTipsServer).GetRmdtips(ctx, req.(*TipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _RecommendTips_serviceDesc = grpc.ServiceDesc{
	ServiceName: "recommend.RecommendTips",
	HandlerType: (*RecommendTipsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "getRmdtips",
			Handler:    _RecommendTips_GetRmdtips_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "recommend/Recommend_rppet.proto",
}

// RecommendProdsClient is the client API for RecommendProds service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RecommendProdsClient interface {
	// Sends a greeting
	GetRcmdProds(ctx context.Context, in *ProdRequest, opts ...grpc.CallOption) (*RcmdProdsReply, error)
}

type recommendProdsClient struct {
	cc *grpc.ClientConn
}

func NewRecommendProdsClient(cc *grpc.ClientConn) RecommendProdsClient {
	return &recommendProdsClient{cc}
}

func (c *recommendProdsClient) GetRcmdProds(ctx context.Context, in *ProdRequest, opts ...grpc.CallOption) (*RcmdProdsReply, error) {
	out := new(RcmdProdsReply)
	err := c.cc.Invoke(ctx, "/recommend.RecommendProds/getRcmdProds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RecommendProdsServer is the server API for RecommendProds service.
type RecommendProdsServer interface {
	// Sends a greeting
	GetRcmdProds(context.Context, *ProdRequest) (*RcmdProdsReply, error)
}

// UnimplementedRecommendProdsServer can be embedded to have forward compatible implementations.
type UnimplementedRecommendProdsServer struct {
}

func (*UnimplementedRecommendProdsServer) GetRcmdProds(ctx context.Context, req *ProdRequest) (*RcmdProdsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRcmdProds not implemented")
}

func RegisterRecommendProdsServer(s *grpc.Server, srv RecommendProdsServer) {
	s.RegisterService(&_RecommendProds_serviceDesc, srv)
}

func _RecommendProds_GetRcmdProds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecommendProdsServer).GetRcmdProds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/recommend.RecommendProds/GetRcmdProds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecommendProdsServer).GetRcmdProds(ctx, req.(*ProdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _RecommendProds_serviceDesc = grpc.ServiceDesc{
	ServiceName: "recommend.RecommendProds",
	HandlerType: (*RecommendProdsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "getRcmdProds",
			Handler:    _RecommendProds_GetRcmdProds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "recommend/Recommend_rppet.proto",
}
