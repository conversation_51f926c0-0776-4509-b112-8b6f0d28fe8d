package main

import (
	"_/proto/cc"
	"_/services"
	"_/tasks"
	"flag"
	"net/http"
	_ "net/http/pprof"
	"os"
	"strings"
	"time"

	"github.com/limitedlee/microservice/micro"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/reflection"
)

func init() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh

	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))

	if env != "production" && env != "pro" {
		kit.IsDebug = true
	}
}

func main() {
	//pprof性能分析
	go func() {
		err := http.ListenAndServe(":11123", nil)
		if err != nil {
			return
		}
	}()

	//日志命令行参数化处理，可以启用禁用控制台日志等，defer确认在程序退出时将所有缓冲日志写入es
	defer glog.Flush()
	flag.Parse()

	micro := micro.MicService{}
	micro.NewServer()

	//服务注册
	cc.RegisterSearchServiceServer(micro.GrpcServer, &services.SearchService{})
	cc.RegisterPetNotesServiceServer(micro.GrpcServer, &services.PetNotesService{})
	cc.RegisterPetTipsServiceServer(micro.GrpcServer, &services.PetTipsService{})
	cc.RegisterCustomerCenterServiceServer(micro.GrpcServer, &services.CustomerCenterService{})
	cc.RegisterUserServiceServer(micro.GrpcServer, &services.UserService{})
	cc.RegisterFeedbackServiceServer(micro.GrpcServer, &services.FeedbackService{})
	cc.RegisterPetServiceServer(micro.GrpcServer, &services.PetService{})
	cc.RegisterDcTaskCustomerServer(micro.GrpcServer, &tasks.TaskCustomer{})
	cc.RegisterVipCardServiceServer(micro.GrpcServer, &services.VipCardService{})
	cc.RegisterVirtualCardServiceServer(micro.GrpcServer, &services.VirtualCardService{})
	cc.RegisterMobileCryptoServiceServer(micro.GrpcServer, &services.MobileCryptoService{})
	//定时任务
	if kit.EnvCanCron() {
		glog.Info("task run...")
		services.InitTask()
		tasks.InitTimeTask()
	}
	tasks.InitMq()
	glog.Info("customer-center master 服务启动...")

	//服务反射，便于查看grpc的状态
	reflection.Register(micro.GrpcServer)
	micro.Start()

}
