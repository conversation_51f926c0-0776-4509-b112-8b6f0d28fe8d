package tasks

import (
	"_/services"
	"_/utils"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"time"
)

func initPtTask(c *cron.Cron) {
	//每天凌晨2点定时增加虚拟阅读量
	c.AddFunc("0 2 * * *", taskIncreaseVirtualReading)
	//c.AddFunc("@every 5s", taskIncreaseVirtualReading)
}

func taskIncreaseVirtualReading() {
	glog.Info("taskIncreaseVirtualReading start")
	//初始化redis
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()

	taskLock := "customercenter:task:increaseVirtualReadingLock"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), time.Minute*5).Val() {
		glog.Error("taskIncreaseVirtualReading already running")
		return
	}
	defer redisConn.Del(taskLock)
	//处理业务
	pt_service := new(services.PetTipsService)
	err := pt_service.IncreaseVirtualReading()
	if err != nil {
		glog.Error("update failed")
		return
	}
	glog.Info("taskIncreaseVirtualReading end")
	return
}
