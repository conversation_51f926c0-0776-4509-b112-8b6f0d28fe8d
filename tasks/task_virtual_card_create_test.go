package tasks

import (
	"_/models"
	"_/services"
	"_/utils"
	"github.com/google/uuid"
	"testing"
)

func TestCreateVirtualCardTask(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{name: "测试定时任务"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//CancelVirtualCardTask()
			//CreateVirtualCardTask()
			//ExpireVirtualCardTask()
			db := services.DataNewEngine()
			session := db.NewSession()
			session.Begin()
			itemMode := models.VipCardVirtual{}
			itemMode.OrgId = 1
			itemMode.OrgName = ""
			itemMode.BatchId = "-3"
			itemMode.TemplateId = 1
			itemMode.CardPass = utils.Get16MD5Encode(uuid.NewString())
			//存数据库需要加密
			_, err := session.Insert(&itemMode)
			if err != nil {

			}
			session.Commit()

		})
	}
}
