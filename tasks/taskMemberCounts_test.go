package tasks

import (
	"_/models"
	"_/services"
	"fmt"
	"testing"

	"github.com/spf13/cast"
)

func TestCounts(t *testing.T) {
	var out []*models.UserStatistics
	for j := int32(0); j < 6; j++ {
		for i := int32(1); i < 10; i++ {
			model := &models.UserStatistics{
				Type:             j,
				Level:            i,
				UserTotal:        j*i + 1,
				NewUserTotal:     j*i + 2,
				PayUserTotal:     j*i + 3,
				CouponUserTotal:  j*i + 4,
				CouponVerifyRate: 20,
				UserPayAmount:    cast.ToFloat64(j*i + 5),
				UserOrderTotal:   j * i,
				UserOrderPrice:   cast.ToFloat64(j*i+5) / cast.ToFloat64(j*i),
			}
			out = append(out, model)
		}
	}
	_, err := services.DataCenterEngine.Insert(out)
	if err != nil {
		fmt.Println(err)
	}
	//type args struct {
	//	level   int32
	//	dayType int32
	//}
	//tests := []struct {
	//	name    string
	//	args    args
	//	want    map[int32]*models.UserStatistics
	//	wantErr bool
	//}{
	//	// TODO: Add test cases.
	//}
	//for _, tt := range tests {
	//	t.Run(tt.name, func(t *testing.T) {
	//		got, err := Counts(tt.args.level, tt.args.dayType)
	//		if (err != nil) != tt.wantErr {
	//			t.Errorf("Counts() error = %v, wantErr %v", err, tt.wantErr)
	//			return
	//		}
	//		if !reflect.DeepEqual(got, tt.want) {
	//			t.Errorf("Counts() got = %v, want %v", got, tt.want)
	//		}
	//	})
	//}
}

func Test_userCounts(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{name: "统计"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := userCounts(); (err != nil) != tt.wantErr {
				t.Errorf("userCounts() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
