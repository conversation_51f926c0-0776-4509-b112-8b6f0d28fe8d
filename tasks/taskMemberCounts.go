package tasks

import (
	"_/models"
	"_/services"
	"_/utils"
	"errors"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"time"
)

func initMemberCountsTask(c *cron.Cron) {
	//每天凌晨2点半统计前一天的会员概况
	//c.AddFunc("30 2 * * *", taskMemberCounts)
}

func taskMemberCounts() {
	glog.Info("taskMemberCounts start")
	//初始化redis
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()

	taskLock := "customercenter:task:memberCountsLock"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), time.Minute*5).Val() {
		glog.Error("taskMemberCounts already running")
		return
	}
	defer redisConn.Del(taskLock)

	err := userCounts()
	if err != nil {
		glog.Error("用户概况统计异常：" + err.Error())
	}

}
func userCounts() error {
	var userStatistics []*models.UserStatistics
	for i := int32(0); i < 6; i++ {
		statistics, err := dayCounts(i)
		if err != nil {
			return err
		}
		for _, v := range statistics {
			userStatistics = append(userStatistics, v)
		}
	}

	db := services.DataCenterEngine
	_, err := db.Insert(userStatistics)
	if err != nil {
		return err
	}

	return nil

}

// 统计 等级 以及天数
func dayCounts(dayType int32) (map[int32]*models.UserStatistics, error) {
	out := make(map[int32]*models.UserStatistics)
	number, err := userTotalCounts()
	if err != nil {
		return nil, err
	}
	var totalNumber int32
	for i := int32(0); i < 9; i++ {
		out[i] = &models.UserStatistics{
			Type:  dayType,
			Level: i,
		}
		for _, n := range number {
			if n.Level == i {
				out[i].UserTotal = n.Num
				out[i].UserNum = n.Num
				totalNumber += n.Num
			}
		}
	}
	day := 0
	switch dayType {
	case 0:
		day = -1
	case 1:
		day = -7
	case 2:
		day = -30
	case 3:
		day = -60
	case 4:
		day = -90
	case 5:
		day = -180
	}
	dayTime := time.Now().AddDate(0, 0, day)
	dayStr := dayTime.Format("2006-01-02") + " 00:00:00"
	//startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", dayStr, time.Local)
	//dayUnix := startTime.Unix()

	//为了方便测试，在测试环境统计到执行的当前时间
	endStr := time.Now().Format("2006-01-02") + " 00:00:00"
	if kit.IsDebug {
		endStr = kit.GetTimeNow(time.Now())
	}
	//endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", endStr, time.Local)
	//endUnix := endTime.Unix()

	err = newUserTotalCounts(dayStr, endStr, out)
	if err != nil {
		return nil, err
	}
	userUpgradeNumTotal, userDowngradeNumTotal, err := userNumCounts(dayStr, endStr, out)
	if err != nil {
		return nil, err
	}

	err = payUserTotalCounts(dayStr, endStr, out)
	if err != nil {
		return nil, err
	}
	err = couponUserTotalCounts(dayStr, endStr, out)
	if err != nil {
		return nil, err
	}

	for i, _ := range out {
		if userUpgradeNumTotal > 0 {
			out[i].UserUpgradeNumRate = (cast.ToFloat64(out[i].UserUpgradeNum) / cast.ToFloat64(userUpgradeNumTotal)) * 100
		}
		if userDowngradeNumTotal > 0 {
			out[i].UserDowngradeNumRate = (cast.ToFloat64(out[i].UserDowngradeNum) / cast.ToFloat64(userDowngradeNumTotal)) * 100
		}
		if totalNumber > 0 {
			out[i].UserNumRate = (cast.ToFloat64(out[i].UserTotal) / cast.ToFloat64(totalNumber)) * 100
		}
		if out[i].CouponUserTotal > 0 {
			out[i].CouponVerifyRate = (out[i].CouponVerifyRate / cast.ToFloat64(out[i].CouponNum)) * 100
		}
		if out[i].PayUserTotal > 0 {
			out[i].UserOrderPrice = out[i].UserPayAmount / cast.ToFloat64(out[i].PayUserTotal)
		}
	}

	return out, nil
}
func userTotalCounts() ([]models.Counts, error) {
	var counts []models.Counts
	err := services.BbcEngine.Table("upet_member").Select("user_level_id level,count(member_id) num").GroupBy("user_level_id").Find(&counts)
	if err != nil {
		return nil, err
	}
	if len(counts) <= 0 {
		return nil, errors.New("查不到数据")
	}
	return counts, nil
}

// 新增会员数：自然日新增新注册会员数
func newUserTotalCounts(dayStr, endStr string, out map[int32]*models.UserStatistics) error {
	var counts []models.Counts

	err := services.BbcEngine.SQL("SELECT u.user_level_id level,count(u.scrm_user_id) num from (select max(id),um.scrm_user_id,um.user_level_id,if(ISNULL(ul.create_time),FROM_UNIXTIME(um.member_time),ul.create_time) create_time from `upet_member` AS `um` left JOIN upet_member_level_log ul ON um.scrm_user_id = ul.scrm_user_id and um.user_level_id = ul.new_user_level_id where um.scrm_user_id != '' and (um.user_level_id =0 and if(ISNULL(ul.create_time),FROM_UNIXTIME(um.member_time),ul.create_time) >= ? and if(um.user_level_id =0,FROM_UNIXTIME(um.member_time),ul.create_time) < ?) or (um.user_level_id>0 and ul.create_time >= ? and ul.create_time < ?) GROUP BY um.scrm_user_id) u GROUP BY u.user_level_id", dayStr, endStr, dayStr, endStr).Find(&counts)
	if err != nil {
		return err
	}
	if len(counts) > 0 {
		for _, i := range counts {
			out[i.Level].NewUserTotal = i.Num
		}
	}
	return nil
}

// 支付会员数 会员支付订单数 会员支付金额 会员客单价=会员支付金额/支付会员数
func payUserTotalCounts(starTime, endTime string, out map[int32]*models.UserStatistics) error {
	var payCounts []models.PayCounts
	err := services.OrderEngine.SQL("select um.user_level_id level_id ,count(DISTINCT(u.member_id)) pay_user_total, count(u.member_id) user_order_total,sum(pay_amount) total_pay_amount from (SELECT u.member_id,u.pay_amount,order_sn FROM(SELECT member_id,pay_amount,order_sn FROM order_main WHERE is_pay = 1 AND parent_order_sn = '' AND create_time > ? AND create_time < ? AND member_id != '' AND pay_amount > 0 UNION ALL SELECT user_id member_id,pay_price pay_amount,pin_order_sn order_sn FROM pin_order_group WHERE STATUS IN ( 20, 30, 40 ) AND create_time > ? AND create_time < ? AND user_id != '' AND pay_price > 0 UNION ALL SELECT scrm_user_id member_id,actual_amount pay_amount,order_sn FROM datacenter.pos_orders WHERE is_push IN ( 0, 2 ) AND created_time > ? AND created_time < ? AND scrm_user_id != '' AND actual_amount > 0 AND payment_type != 5 ) u GROUP BY u.order_sn) u INNER JOIN upetmart.upet_member um on um.scrm_user_id = u.member_id GROUP BY um.user_level_id ", starTime, endTime, starTime, endTime, starTime, endTime).Find(&payCounts)
	if err != nil {
		return err
	}
	for _, k := range payCounts {
		if _, ok := out[k.LevelId]; ok {
			out[k.LevelId].UserPayAmount += kit.FenToYuan(k.TotalPayAmount)
			out[k.LevelId].UserOrderTotal += k.UserOrderTotal
			out[k.LevelId].PayUserTotal += k.PayUserTotal
		}
	}

	return nil
}

func couponUserTotalCounts(starTime, endTime string, out map[int32]*models.UserStatistics) error {
	var couponCounts []models.CouponCounts
	err := services.DataCenterEngine.Table("user_coupon").Alias("u").
		Join("inner", "upetmart.upet_member um", "um.scrm_user_id = u.user_id").
		Select("um.user_level_id level_id,count(DISTINCT(u.user_id)) user_num,count(um.user_level_id) total_num").Where("u.create_time > ? and u.create_time < ? ", starTime, endTime).GroupBy("um.user_level_id").Find(&couponCounts)
	if err != nil {
		return err
	}
	if len(couponCounts) <= 0 {
		return nil
	}

	//商城券核销人数
	var couponWriteOff []models.CouponWriteOff
	err = services.DataCenterEngine.Table("user_coupon").Alias("u").
		Join("inner", "upetmart.upet_member um", "um.scrm_user_id = u.user_id").
		Join("inner", "upetmart.upet_voucher uv", "uv.voucher_id = u.voucher_id and date(u.create_time) = date(uv.write_off_date) and uv.voucher_state = 2").
		Where("u.create_time > ? and u.create_time < ? and u.coupon_type = 2", starTime, endTime).Select("um.user_level_id level_id,count(DISTINCT(u.user_id)),count(um.user_level_id) total_num").GroupBy("um.user_level_id").Find(&couponWriteOff)

	if err != nil {
		return err
	}

	for _, k := range couponCounts {
		if _, ok := out[k.LevelId]; ok {
			out[k.LevelId].CouponNum += k.TotalNum
		}
	}
	for _, k := range couponWriteOff {
		if _, ok := out[k.LevelId]; ok {
			out[k.LevelId].CouponVerifyRate += cast.ToFloat64(k.TotalNum)
		}
	}

	pageIndex, pageSize := 1, 1000
	for {
		var (
			memberCoupons []models.ZlMemberCoupon
			zlCouponCode  []string
		)
		memberCouponsMap := make(map[string]models.ZlMemberCoupon)

		err = services.DataCenterEngine.Table("user_coupon").Alias("u").
			Join("inner", "upetmart.upet_member um", "um.scrm_user_id = u.user_id").
			Select("um.user_level_id level_id,u.create_time,u.coupon_code").Where("u.coupon_type = 1 and u.create_time > ? and u.create_time < ? ", starTime, endTime).Limit(pageSize, (pageIndex*pageSize)-pageSize).Find(&memberCoupons)
		if err != nil {
			return err
		}
		if len(memberCoupons) <= 0 {
			break
		}
		for _, i := range memberCoupons {
			zlCouponCode = append(zlCouponCode, i.CouponCode)
		}

		for index, _ := range memberCoupons {
			memberCouponsMap[memberCoupons[index].CouponCode] = memberCoupons[index]
		}

		var zlCouponWriteOffs []models.ZlCouponWriteOffDate
		err = services.ZlHospitalNewEngine().Table("pos_payment_promotion").Select("create_date write_off_date,promotion_number").In("promotion_number", zlCouponCode).Find(&zlCouponWriteOffs)
		if err != nil {
			return err
		}

		for _, k := range zlCouponWriteOffs {
			if memberCouponsMap[k.PromotionNumber].CreateTime.Year() == k.WriteOffDate.Year() &&
				memberCouponsMap[k.PromotionNumber].CreateTime.Month() == k.WriteOffDate.Month() &&
				memberCouponsMap[k.PromotionNumber].CreateTime.Day() == k.WriteOffDate.Day() {
				if _, ok := memberCouponsMap[k.PromotionNumber]; ok {
					if _, ok1 := out[memberCouponsMap[k.PromotionNumber].LevelId]; ok1 {
						out[memberCouponsMap[k.PromotionNumber].LevelId].CouponVerifyRate++
					}
				}
			}
		}
		pageIndex++
	}

	return nil
}

func userNumCounts(starTime, endTime string, out map[int32]*models.UserStatistics) (userUpgradeNumTotal, userDowngradeNumTotal int32, err error) {

	var levelCounts []models.Counts
	err = services.BbcEngine.Table("upet_member_level_log").Select("`old_user_level_id` level,count(scrm_user_id) num,`lift_type` type").Where("create_time > ? and create_time < ?", starTime, endTime).GroupBy("lift_type,`old_user_level_id`").Find(&levelCounts)
	if err != nil {
		return userUpgradeNumTotal, userDowngradeNumTotal, err
	}
	if len(levelCounts) > 0 {
		for _, i := range levelCounts {
			if i.Type == 1 {
				out[i.Level].UserUpgradeNum = i.Num
				userUpgradeNumTotal += i.Num
			} else if i.Type == 2 {
				out[i.Level].UserDowngradeNum = i.Num
				userDowngradeNumTotal += i.Num
			}
		}
	}
	return userUpgradeNumTotal, userDowngradeNumTotal, nil
}
