package tasks

import (
	"_/models"
	"_/services"
	"_/utils"
	"fmt"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
)

func InitRefreshMemberLevelTask(c *cron.Cron) {
	//每天凌晨1点30定时刷新用户会员等级
	c.AddFunc("30 1 * * *", refreshMemberLevel)
}

type UserHealthVal struct {
	UserId       string `json:"user_id"`
	SumHealthVal int64  `json:"sum_health_val"`
}

// 刷新用户健康值表，计算用户的最新等级
// 这里只需要处理冻结的健康值，非冻结的健康值会自动算入用户总健康值
func refreshMemberLevel() {
	glog.Info("refreshMemberLevel task start")
	//初始化redis
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()
	taskLock := "customercenter:task:refreshMemberLevelLock"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 12*time.Hour).Val() {
		glog.Error("refreshMemberLevel task already running")
		return
	}
	defer redisConn.Del(taskLock)

	// 获取会员等级
	userLevelList, err := getUserLevelList()
	if err != nil {
		glog.Error("refreshMemberLevel failed, error: ", err)
		return
	}

	size := 100
	for {
		curTime := time.Now().Format("2006-01-02 15:04:05")
		userList := make([]UserHealthVal, 0)
		err := services.DataCenterEngine.Table("health_detail").Select("user_id,sum(health_val) sum_health_val").
			Where("`type`=3 and effect_time<=?", curTime).Limit(size).GroupBy("user_id").Find(&userList)
		if err != nil {
			glog.Error("refreshMemberLevel health_detail.Find error: ", err)
			return
		}
		if len(userList) == 0 {
			break
		}

		for _, v := range userList {
			//更新用户健康值
			if err := handleUserHealthVal(v.UserId, v.SumHealthVal, userLevelList); err != nil {
				glog.Error("refreshMemberLevel handleUserHealthVal error: ", err)
				return
			}

			//更新冻结的状态为收入
			if _, err := services.DataCenterEngine.Table("health_detail").Where("user_id=? AND `type`=3 and effect_time<=?",
				v.UserId, curTime).Update(map[string]interface{}{"`type`": 1}); err != nil {
				glog.Error("refreshMemberLevel health_detail Update: ", err)
				return
			}
		}

		time.Sleep(200 * time.Millisecond)
	}

	glog.Info("refreshMemberLevel task end.")
}

func handleUserHealthVal(userId string, sumHealthVal int64, userLevelList []*models.UserLevel) error {
	memberIntegralInfo := &models.MemberIntegralInfo{}
	b, err := services.DataCenterEngine.Table("member_integral_info").Where("memberid=?", userId).Get(memberIntegralInfo)
	if err != nil {
		glog.Error("member_integral_info.Get error: ", err)
		return err
	}
	if !b {
		// 新增用户
		memberIntegralInfo.MemberId = userId
		memberIntegralInfo.HealthVal = int32(sumHealthVal)
		if _, err := services.DataCenterEngine.Table("member_integral_info").Insert(memberIntegralInfo); err != nil {
			glog.Error("member_integral_info.insert error: ", err, userId)
			return err
		}
	} else {
		// 更新用户健康值
		if _, err = services.DataCenterEngine.Exec("UPDATE member_integral_info SET freeze_health_val=IF(freeze_health_val<?,0,freeze_health_val-?),health_val=health_val+? WHERE memberid=?",
			sumHealthVal, sumHealthVal, sumHealthVal, userId); err != nil {
			glog.Error("member_integral_info.update error: ", err, userId)
			return err
		}
	}

	member := &models.UpetMember{}
	b, err = services.BbcEngine.Table("upet_member").Cols("scrm_user_id,user_level_id,weixin_mini_openid").Where("scrm_user_id=?", userId).Get(member)
	if err != nil {
		glog.Error("upet_member.Get error: ", err)
		return err
	}
	if !b {
		glog.Info("用户记录未找到 scrm_user_id:", userId)
		// 没有用户记录也需要给用户加健康值，但不新增用户记录
		return nil
	}

	newHealthVal := int64(memberIntegralInfo.HealthVal) + sumHealthVal
	newLevelId := int64(0)
	oriLevelName, curLevelName := "V0闻卡会员", ""
	for k, v := range userLevelList {
		if v.LevelId == member.UserLevelId {
			oriLevelName = fmt.Sprintf("V%d%s", v.LevelId, v.LevelName)
		}
		if newHealthVal >= v.HealthVal &&
			(k == len(userLevelList)-1 || newHealthVal < userLevelList[k+1].HealthVal) {
			curLevelName = fmt.Sprintf("V%d%s", v.LevelId, v.LevelName)
			newLevelId = v.LevelId
		}
	}
	if newLevelId != member.UserLevelId {
		return resetMemberLevel(member, newLevelId, newHealthVal, oriLevelName, curLevelName)
	}

	return nil
}

// 重置用户会员等级
func resetMemberLevel(member *models.UpetMember, newLevelId int64, newHealthVal int64, oriLevelName, curLevelName string) error {
	updData := map[string]interface{}{
		"user_level_id":    newLevelId,
		"user_level_etime": time.Now().AddDate(1, 0, 0).Unix(),
	}
	if member.UserLevelStime == 0 {
		updData["user_level_stime"] = time.Now().Unix()
	}

	_, err := services.BbcEngine.Table("upet_member").Where("scrm_user_id=?", member.ScrmUserId).Update(updData)
	if err != nil {
		return err
	}
	_, err = services.BbcEngine.Table("upet_member_level_log").Insert(&models.UpetMemberLevelLog{
		ScrmUserId:     member.ScrmUserId,
		LiftType:       models.MemberLevelLiftTypeUp,
		OldUserLevelId: member.UserLevelId,
		NewUserLevelId: newLevelId,
		Content:        "解冻健康值，用户会员等级变更",
	})
	if err != nil {
		return err
	}

	// 推送会员等级变更通知
	go services.SendLevelChangeWxMessage(member, newLevelId, newHealthVal, oriLevelName, curLevelName)

	return nil
}

func getUserLevelList() ([]*models.UserLevel, error) {
	list := make([]*models.UserLevel, 0)
	err := services.DataCenterEngine.Table("user_level").Where("level_status=1").OrderBy("level_id asc").Find(&list)
	return list, err
}

//func initializationUserLevel() {
//	// 获取会员等级
//	userLevelList, err := getUserLevelList()
//	if err != nil {
//		glog.Error("initializationUserLevel failed, error: ", err)
//		return
//	}
//
//	page, size := 1, 100
//	for {
//		userList := make([]UserHealthVal, 0)
//		err = services.DataCenterEngine.Table("member_integral_info").Select("memberid user_id,health_val sum_health_val").Where("health_val > 0").Limit(size, (page-1)*size).Find(&userList)
//		if err != nil {
//			glog.Error("member_integral_info.Find error: ", err)
//			return
//		}
//		if len(userList) == 0 {
//			break
//		}
//
//		for _, v := range userList {
//			member := &models.UpetMember{}
//			b, err := services.BbcEngine.Table("upet_member").Cols("scrm_user_id,user_level_id,weixin_mini_openid").Where("scrm_user_id=?", v.UserId).Get(member)
//			if err != nil {
//				glog.Error("会员初始化失败：", v.UserId)
//				continue
//			}
//			if !b {
//				continue
//			}
//			newLevelId := int64(0)
//			for k, j := range userLevelList {
//				if v.SumHealthVal >= j.HealthVal {
//					if k == len(userLevelList)-1 || v.SumHealthVal < userLevelList[k+1].HealthVal {
//						newLevelId = j.LevelId
//					}
//				}
//			}
//			if newLevelId > 0 && newLevelId != member.UserLevelId {
//				updData := map[string]interface{}{
//					"user_level_id":    newLevelId,
//					"user_level_etime": time.Now().AddDate(1, 0, 0).Unix(),
//				}
//				if member.UserLevelStime == 0 {
//					updData["user_level_stime"] = time.Now().Unix()
//				}
//
//				_, err := services.BbcEngine.Table("upet_member").Where("scrm_user_id=?", member.ScrmUserId).Update(updData)
//				if err != nil {
//					glog.Error("会员初始化失败：", v.UserId)
//					continue
//				}
//			}
//		}
//
//		page++
//	}
//}
