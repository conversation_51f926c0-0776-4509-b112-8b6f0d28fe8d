package tasks

import (
	"_/proto/cc"
	"context"

	"github.com/maybgit/glog"

	"github.com/robfig/cron/v3"
)

// 定时任务
func InitTimeTask() {
	c := cron.New()

	//宠物贴士定时任务
	initPtTask(c)

	//会员等级失效扣减健康值
	InitHealthValExpireTask(c)

	//刷新用户会员等级、解冻健康值
	InitRefreshMemberLevelTask(c)

	//会员等级过期通知
	InitUserLevelExpireTask(c)

	initMemberCountsTask(c)

	//付费会员权益过期提醒
	initVipEquityExpireTask(c)

	//创建VIP虚拟卡卡密任务
	initVirtualCardCreateTask(c)

	//注销VIP虚拟卡卡券任务
	initVirtualCardCancelTask(c)

	InitSaveMoneyTask(c)

	//InitMq()

	c.Start()
}

func InitMq() {
	go ZlOrderBinLogTask()
}

type TaskCustomer struct {
}

// 调用定时任务的grpc方法，方便测试使用
func (t *TaskCustomer) ChooseTaskRun(ctx context.Context, in *cc.TaskRunVo) (*cc.BaseReq, error) {
	response := cc.BaseReq{
		Code:    200,
		Message: "",
	}
	mapData := make(map[int32]func(), 0)

	mapData[1] = taskMemberCounts    // 会员概况统计
	mapData[2] = refreshMemberLevel  //刷新用户等级
	mapData[3] = taskVipEquityExpire //会员权益过期通知订阅
	mapData[4] = SaveMoneyTask       //计算会员节约多少钱

	//同步子龙线下门店消费有积分的用户，同步数据时间为：2022-07-18-2022-07-28 23：59：59，后继不用，屏蔽掉
	//mapData[3] = syncUserIntegral   //同步用户积分
	//会员等级上线初始化健康值，上线执行，后继不用，屏蔽掉
	//mapData[20] = initializationUserLevel

	glog.Info("手动启动定时任务：", in.Data)
	if fun, ok := mapData[in.Data]; ok {
		fun()
	}

	return &response, nil
}
