package tasks

import (
	"_/models"
	"_/proto/mc"
	"_/services"
	"_/utils"
	"fmt"
	"time"

	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/robfig/cron/v3"
)

func InitUserLevelExpireTask(c *cron.Cron) {
	//每天6点定时检查会员等级过期
	c.AddFunc("0 6 * * *", userLevelExpireTask)
}

// 会员等级过期通知，过期前14天通知
func userLevelExpireTask() {
	glog.Info("healthTask start")
	//初始化redis
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()

	taskLock := "customercenter:task:userLevelExpireTaskLock"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 12*time.Hour).Val() {
		glog.Error("userLevelExpireTask already running")
		return
	}
	defer redisConn.Del(taskLock)

	userLevels, err := getUserLevels()
	if err != nil {
		glog.Error("userLevelExpireTask getUserLevels error: ", err)
		return
	}

	curTime := time.Now()
	st := time.Date(curTime.Year(), curTime.Month(), curTime.Day()+14, 0, 0, 0, 0, time.Local).Unix()
	et := time.Date(curTime.Year(), curTime.Month(), curTime.Day()+14, 23, 59, 59, 0, time.Local).Unix()
	startId, size := int64(0), 100
	for {
		list := make([]*models.UpetMember, 0)
		err := services.BbcEngine.Table("upet_member").Cols("member_id,scrm_user_id,user_level_id,user_level_stime,user_level_etime,weixin_mini_openid").
			Where("member_id>? AND user_level_etime>=? AND user_level_etime<=?", startId, st, et).Limit(size).Find(&list)
		if err != nil {
			glog.Error("userLevelExpireTask error:", err)
			return
		}
		if len(list) == 0 {
			break
		}

		for _, v := range list {
			startId = v.MemberId

			curLevelName := fmt.Sprintf(`V%d%s`, v.UserLevelId, userLevels[v.UserLevelId].LevelName)
			go sendLevelExpireWxMessage(v, curLevelName)
		}

		time.Sleep(200 * time.Millisecond)
	}

	glog.Info("userLevelExpireTask end")
}

// 发送微信订阅消息，会员等级过期
func sendLevelExpireWxMessage(member *models.UpetMember, curLevelName string) {
	subTemplate, err := getLevelExpireSubscribeMessageTemplate()
	if err != nil {
		glog.Error("sendLevelExpireWxMessage sendLevelExpireWxMessage error: ", err, member.ScrmUserId)
		return
	}
	if !services.CanSendWxMessage(member.ScrmUserId, "user_level", 1) {
		glog.Error("sendLevelExpireWxMessage CanSendWxMessage ", member.ScrmUserId)
		return
	}

	miniprogramState := ""
	if kit.EnvIsTest() {
		miniprogramState = "trial"
	}
	endTime := time.Unix(member.UserLevelEtime, 0).Format("2006年01月02日")
	subscribeMessageRequest := &mc.SubscribeMessageRequest{
		Touser:           member.WeixinMiniOpenid,
		TemplateId:       subTemplate.TemplateId,
		Page:             "/pages/tabs/member", // 小程序跳转页面
		MiniprogramState: miniprogramState,
		Data:             fmt.Sprintf(subTemplate.Content, curLevelName, endTime, "您的会员即将到期，跳转查看会员规则说明"),
		IsJpush:          0,
	}
	msgClient := mc.GetMessageCenterClient()
	re, err := msgClient.RPC.SubscribeMessage(msgClient.Ctx, subscribeMessageRequest)
	if err != nil {
		glog.Error("sendLevelExpireWxMessage sendLevelExpireWxMessage error: ", err, member.ScrmUserId, kit.JsonEncode(subscribeMessageRequest))
		return
	}
	if re.Code != 200 {
		glog.Error("sendLevelExpireWxMessage 发送会员等级过期通知失败，ScrmUserId：", member.ScrmUserId, kit.JsonEncode(re), kit.JsonEncode(subscribeMessageRequest))
		return
	}

	// 记录发送成功的记录
	msgRecord := &models.WechatSubscribeMessageRecord{
		ScrmUserId:    member.ScrmUserId,
		TemplateId:    subTemplate.TemplateId,
		MessageData:   subscribeMessageRequest.Data,
		MessageParams: "",
	}
	if _, err := services.DataCenterEngine.Table("wechat_subscribe_message_record").Insert(msgRecord); err != nil {
		glog.Error("sendLevelExpireWxMessage wechat_subscribe_message_record insert error: ", err, kit.JsonEncode(msgRecord))
		return
	}

	// 订阅数减一
	_, err = services.DataCenterEngine.Exec("UPDATE wechat_subscribe_message SET number=IF(number<1,0,number-1) WHERE scrm_user_id=? AND template_id=?", member.ScrmUserId, subTemplate.TemplateId)
	if err != nil {
		glog.Error("sendLevelExpireWxMessage 扣减用户订阅数失败，error: ", err, member.ScrmUserId)
		return
	}
	glog.Info("sendLevelExpireWxMessage 发送订阅消息成功: ", kit.JsonEncode(msgRecord))
}

// 会员等级变更订阅模板
var levelExpireSubscribeMessageTemplate *models.WechatSubscribeMessageTemplate

// 获取订阅消息模板
func getLevelExpireSubscribeMessageTemplate() (*models.WechatSubscribeMessageTemplate, error) {
	if levelExpireSubscribeMessageTemplate != nil {
		return levelExpireSubscribeMessageTemplate, nil
	}

	levelExpireSubscribeMessageTemplate = &models.WechatSubscribeMessageTemplate{}
	_, err := services.DataCenterEngine.Table("wechat_subscribe_message_template").Where("template_key=? AND store_id=1", "user-level-expire").Get(levelExpireSubscribeMessageTemplate)
	if err != nil {
		return nil, err
	}
	return levelExpireSubscribeMessageTemplate, nil
}
