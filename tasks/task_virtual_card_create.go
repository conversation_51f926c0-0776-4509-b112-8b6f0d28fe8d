package tasks

import (
	"_/models"
	"_/services"
	"_/utils"
	"fmt"
	"runtime"
	"time"

	"github.com/google/uuid"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
)

func initVirtualCardCreateTask(c *cron.Cron) {
	//2秒钟执行一次
	c.AddFunc("@every 2s", CreateVirtualCardTask) // 每2秒分钟同步一次库存
	//每天0点执行一次
	c.AddFunc("0 0 * * *", ExpireVirtualCardTask) // 每2秒分钟同步一次库存
}

//定时任务过期卡
func ExpireVirtualCardTask() {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("ExpireVirtualCardTask异常信息捕获：", err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER PWERROR] %v %s\n", err, stack[:length])

		}
	}()

	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()

	taskLock := "customer:task:ExpireVirtualCardTask"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 5*time.Minute).Val() {
		glog.Error("ExpireVirtualCardTask already running")
		return
	}
	defer redisConn.Del(taskLock)
	_, err := services.DataNewEngine().Exec("update vip_card_virtual set status=3 where status=0 and  expire_time<? ", time.Now().Format(kit.DATETIME_LAYOUT))
	if err != nil {
		glog.Error("ExpireVirtualCardTask异常信息捕获：", err)
	}

}

// 查询数据库，生成卡券
func CreateVirtualCardTask() {

	defer func() {
		if err := recover(); err != nil {
			glog.Error("CreateVirtualCardTask异常信息捕获：", err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER PWERROR] %v %s\n", err, stack[:length])

		}
	}()

	//glog.Info("CreateVirtualCardTask start")
	//初始化redis
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()

	taskLock := "customer:task:VirtualCardCreate"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 5*time.Minute).Val() {
		glog.Error("VirtualCardCreate already running")
		return
	}
	defer redisConn.Del(taskLock)
	//开始写逻辑

	db := services.DataNewEngine()
	task := models.VipCardVirtualCreate{}
	//查询是否有需要处理的任务
	isHave, err := db.Where("status=0").Desc("create_time").Get(&task)
	if err != nil {
		glog.Error("查询创建虚拟卡任务出错", err.Error())
		return
	}
	if !isHave {
		return
	}
	task.Status = 1
	//先修改状态
	_, err = db.ID(task.Id).Cols("status").Update(&task)
	if err != nil {
		glog.Error("修改创建卡任务出错", err.Error())
		return
	}

	f := excelize.NewFile()
	nameList := []interface{}{
		"卡号", "卡密",
	}
	//使用流式写入，会更节省内存
	writer, err := f.NewStreamWriter("Sheet1")
	_ = writer.SetRow("A1", nameList)

	session := db.NewSession()
	session.Begin()
	date := time.Now().Format(utils.DATE_LAYOUT)
	startDate := date + " 23:59:59"
	location, _ := time.ParseInLocation(utils.DATE_TIME_LAYOUT, startDate, time.Local)
	add := location.AddDate(1, 0, 0)
	add = add.AddDate(0, 0, -1)

	for i := 0; i < task.CardCount; i++ {

		strPass := "FY" + utils.Get16MD5Encode(uuid.NewString())
		itemMode := models.VipCardVirtual{}
		itemMode.OrgId = int32(task.OrgId)
		itemMode.OrgName = task.OrgName
		itemMode.BatchId = cast.ToString(task.Id)
		itemMode.TemplateId = int32(task.TemplateId)
		itemMode.SellType = int32(task.SellType)
		itemMode.ExpireTime = add
		//存数据库需要加密
		itemMode.CardPass = utils.MobileEncrypt(strPass)

		_, err = session.Insert(&itemMode)
		if err != nil {
			glog.Error("创建卡密插入数据库失败", err.Error())
			task.Status = 2
			//先修改状态
			_, err = db.ID(task.Id).Cols("status").Update(&task)
			session.Rollback()
			return
		}

		axis := fmt.Sprintf("A%d", i+2)
		_ = writer.SetRow(axis, []interface{}{
			"FY" + cast.ToString(itemMode.CardId), // 卡号
			strPass,                               // 卡密
		})

	}
	writer.Flush()
	//if len(insertModel) > 0 {
	//	_, err = db.Insert(&insertModel)
	//	if err != nil {
	//		glog.Error("创建卡密插入数据库失败", err.Error())
	//		task.Status = 2
	//		//先修改状态
	//		_, err = db.ID(task.Id).Cols("status").Update(&task)
	//		session.Rollback()
	//		return
	//	}
	//}

	fileName := fmt.Sprintf("卡密导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	url, err := utils.UploadExcelToQiNiu1(f, fileName)
	if err != nil {
		glog.Error("创建卡密上传云失败", err.Error())
		task.Status = 2
		//先修改状态
		_, err = db.ID(task.Id).Cols("status").Update(&task)
		session.Rollback()
		return
	}
	session.Commit()
	task.Status = 3
	task.FileUrl = url

	_, err = db.Exec("update vip_card_virtual_create set status=3,file_url=? where id=?", url, task.Id)
	if err != nil {
		glog.Error("修改创建虚拟卡任务失败", task.Id, err.Error(), url)
		return
	}
	//先修改状态
	//_, err = db.ID(task.Id).Cols("status", "file_url").Update(&task)

}
