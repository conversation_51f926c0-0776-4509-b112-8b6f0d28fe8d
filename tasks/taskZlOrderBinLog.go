package tasks

import (
	"_/models"
	"_/proto/cc"
	"_/services"
	"_/utils"
	"context"
	"encoding/json"
	"errors"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/streadway/amqp"
	kit "github.com/tricobbler/rp-kit"
)

var (
	logHead = "ZlOrderBinLog:"
)

// ZlOrderBinLogTask 子龙订单canal落地
// 订阅子龙订单支付和退款数据，并处理对应的健康值
func ZlOrderBinLogTask() {
	utils.Consume(utils.QueueZlOrderBinLog, "", utils.MQExchange, ZlOrderBinLog)
}

// ZlOrderBinLog 子龙订单canal落地
func ZlOrderBinLog(d amqp.Delivery) (response string, err error) {
	defer kit.CatchPanic()
	mqMessage := string(d.Body)
	glog.Info(logHead, "MQ:", mqMessage)
	model := new(models.MqZlOrderBinLogTask)
	err = json.Unmarshal(d.Body, model)

	if err != nil {
		glog.Error(logHead, "ZlOrderBinLog.json.Unmarshal:", err, ",", mqMessage)
		d.Ack(false)
		return
	}
	if model.Type == 1 {
		go zlOrderPay(model.Id)
	} else if model.Type == 2 {
		go zlOrderRefund(model.Id)
	}

	d.Ack(false)

	return
}

func zlOrderPay(id int) error {
	hospitalDb := services.ZlHospitalEngine
	scrmDb := services.ZlScrmEngine
	dataCenterDb := services.DataCenterEngine
	var posPaymentMaster models.PosPaymentMaster
	ok, err := hospitalDb.Table("pos_orders_master").Alias("ma").
		Join("inner", "pos_payment_method me", "ma.id = me.order_id").
		Join("left", "sys_hospital_info hi", "hi.system_id = ma.org_id").
		Where("me.id = ? and order_center_order_number is null", id).Select("ma.id,ma.order_number,ma.customer_id,ma.total_amount,ma.order_type,ma.actuly_payed,ma.present_payed_amount,ma.created_date,hi.clinic_name shop_name,hi.brand_code shop_id,me.payment_type").Get(&posPaymentMaster)
	if err != nil {
		glog.Error(logHead, "ZlOrderBinLog查询pos_orders_master数据异常:", err)
		return err
	}
	if ok {
		var posOrder models.PosOrders
		if ok, err = dataCenterDb.Table("pos_orders").Where("order_sn = ?", posPaymentMaster.OrderNumber).Get(&posOrder); err != nil {
			glog.Error(logHead, "ZlOrderBinLog查询pos_orders数据异常:", err)
			return err
		} else if ok {
			return err
		}
		var scrmId string
		if ok, err = scrmDb.Table("t_scrm_map").Select("scrm_id").Where("type= 1 and status=0 and target_id = ?", cast.ToString(posPaymentMaster.CustomerId)).Get(&scrmId); err != nil {
			glog.Error(logHead, "ZlOrderBinLog查询t_scrm_map数据异常:", err)
			return err
		}
		posOrder = models.PosOrders{
			OrderSn:             posPaymentMaster.OrderNumber,
			ZlCustomerId:        posPaymentMaster.CustomerId,
			ScrmUserId:          scrmId,
			TotalAmount:         kit.YuanToFen(posPaymentMaster.TotalAmount),
			ActualAmount:        kit.YuanToFen(posPaymentMaster.ActulyPayed),
			DiscountAmount:      kit.YuanToFen(posPaymentMaster.DiscountAmount),
			PresentPayedAmount:  kit.YuanToFen(posPaymentMaster.PresentPayedAmount),
			RefundAmount:        0,
			RefundPresentAmount: 0,
			CreatedTime:         posPaymentMaster.CreatedDate,
			ShopId:              posPaymentMaster.ShopId,
			ShopName:            posPaymentMaster.ShopName,
			PaymentType:         posPaymentMaster.PaymentType,
		}
		if len(scrmId) <= 0 || posOrder.ActualAmount <= 0 {
			posOrder.IsPush = 1
		}
		dataCenterDbSession := dataCenterDb.NewSession()
		defer dataCenterDbSession.Close()

		dataCenterDbSession.Begin()

		if posOrder.IsPush == 0 {
			cu := services.CustomerCenterService{}
			res, err := cu.AddUserHealthVal(context.Background(), &cc.AddUserHealthValReq{
				UserId:       scrmId,
				HealthType:   2,
				Type:         1,
				Title:        "线下消费",
				Content:      "线下消费-" + posOrder.OrderSn,
				OrderSn:      posOrder.OrderSn,
				PayAmount:    cast.ToString(kit.FenToYuan(cast.ToInt64(posOrder.ActualAmount))),
				RefundAmount: "0",
				ShopId:       posOrder.ShopId,
				ShopName:     posOrder.ShopName,
				PayTime:      kit.GetTimeNow(posPaymentMaster.CreatedDate),
			})
			if err != nil {
				dataCenterDbSession.Rollback()
				glog.Error("ZlOrderBinLog.pos_orders.AddUserHealthVal error: ", err)
				return err
			}
			if res.Code != 200 {
				dataCenterDbSession.Rollback()
				glog.Error("ZlOrderBinLog.pos_orders.AddUserHealthVal 失败: ", err)
				return errors.New("增加健康值失败")
			}
			posOrder.IsPush = 2
		}
		_, err = dataCenterDbSession.Table("pos_orders").Insert(posOrder)
		if err != nil {
			dataCenterDbSession.Rollback()
			glog.Error("ZlOrderBinLog.pos_orders.Insert error: ", err)
			return err
		}
		err = dataCenterDbSession.Commit()
		if err != nil {
			dataCenterDbSession.Rollback()
			return err
		}
	}
	return nil
}

func zlOrderRefund(id int) error {
	hospitalDb := services.ZlHospitalEngine
	dataCenterDb := services.DataCenterEngine
	var posRmaMaster models.PosRmaMaster
	ok, err := hospitalDb.Table("pos_rma_master").Alias("rm").
		Join("left", "sys_hospital_info hi", "hi.system_id = rm.org_id").
		Where("rm.id = ?", id).Select("rm.id,rm.org_id,rm.order_number,rm.rma_number,rm.customer_id,rm.rma_amount,rm.rma_status,rm.rma_total_amount,rm.rma_type,hi.clinic_name shop_name,hi.brand_code shop_id").Get(&posRmaMaster)
	if err != nil {
		glog.Error(logHead, "ZlOrderBinLog查询pos_orders_master数据异常:", err)
		return err
	}
	if ok {
		var posOrder models.PosOrders
		if ok, err = dataCenterDb.Table("pos_orders").Where("order_sn = ?", posRmaMaster.OrderNumber).Get(&posOrder); err != nil {
			glog.Error(logHead, "ZlOrderBinLog查询pos_orders数据异常:", err)
			return err
		} else if ok {

			refundAmount := kit.YuanToFen(posRmaMaster.RmaAmount)
			presentRmaAmount := kit.YuanToFen(posRmaMaster.PresentRmaAmount)

			if (posOrder.ActualAmount - (posOrder.RefundAmount + refundAmount)) < 0 {
				return nil
			}
			dataCenterDbSession := dataCenterDb.NewSession()
			defer dataCenterDbSession.Close()

			dataCenterDbSession.Begin()

			dataCenterDbSession.Exec("UPDATE pos_orders SET refund_amount=refund_amount+?,refund_present_amount=refund_present_amount+? WHERE order_sn=?", refundAmount, presentRmaAmount, posRmaMaster.OrderNumber)
			if len(posOrder.ScrmUserId) > 0 {
				healthDetail := &models.HealthDetail{}
				b, err := dataCenterDb.Table("health_detail").Where("user_id=? AND order_sn=?", posOrder.ScrmUserId, posOrder.OrderSn).Get(healthDetail)
				if err != nil {
					dataCenterDbSession.Rollback()
					glog.Error("ZlOrderBinLog查询health_detail异常: ", err)
					return err
				}
				if b {
					cu := services.CustomerCenterService{}
					res, err := cu.AddUserHealthVal(context.Background(), &cc.AddUserHealthValReq{
						UserId:       posOrder.ScrmUserId,
						HealthType:   5,
						Type:         2,
						Title:        "线下退款",
						Content:      "线下退款-" + posOrder.OrderSn,
						OrderSn:      posOrder.OrderSn,
						RefundAmount: cast.ToString(posRmaMaster.RmaAmount),
						ShopId:       posRmaMaster.ShopId,
						ShopName:     posRmaMaster.ShopName,
					})
					if err != nil {
						dataCenterDbSession.Rollback()
						glog.Error("ZlOrderBinLog.pos_orders.AddUserHealthVal error: ", err)
						return err
					}
					if res.Code != 200 {
						dataCenterDbSession.Rollback()
						glog.Error("ZlOrderBinLog.pos_orders.AddUserHealthVal 失败: ", err)
						return errors.New("退健康值失败")
					}
				}
			}
			err = dataCenterDbSession.Commit()
			if err != nil {
				dataCenterDbSession.Rollback()
				return err
			}
		}
	}
	return nil
}
