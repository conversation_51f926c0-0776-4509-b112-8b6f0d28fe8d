package tasks

import (
	"_/models"
	"_/proto/mc"
	"_/services"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/robfig/cron/v3"
)

func initVipEquityExpireTask(c *cron.Cron) {
	//每天9点提醒权益过期
	c.AddFunc("* 9 * * *", taskVipEquityExpire)

	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	if env != "production" && env != "pro" {
		c.AddFunc("@every 5m", taskVipEquityExpire)
	}
}

type UserVoucherCount struct {
	UserId int64 `json:"user_id"`
}

// 会员权益过期通知订阅模板内容
var voucherExpireSubscribeMessageTemplate *models.WechatSubscribeMessageTemplate

// 获取订阅消息模板
func getVoucherExpireSubscribeMessageTemplate() (*models.WechatSubscribeMessageTemplate, error) {
	if voucherExpireSubscribeMessageTemplate != nil {
		return voucherExpireSubscribeMessageTemplate, nil
	}
	voucherExpireSubscribeMessageTemplate = &models.WechatSubscribeMessageTemplate{}
	_, err := services.DataCenterEngine.Table("wechat_subscribe_message_template").Where("template_key=? AND store_id=1", "vip-card-expire").Get(voucherExpireSubscribeMessageTemplate)
	if err != nil {
		glog.Error("userLevelExpireTask user-level-change error: ", err)
		return nil, err
	}
	return voucherExpireSubscribeMessageTemplate, nil
}
func taskVipEquityExpire() {
	glog.Info("taskVipEquityExpire start")
	//初始化redis
	//redisConn := utils.ConnectClusterRedis()
	//defer redisConn.Close()
	//
	//taskLock := "upetcenter:task:taskVipEquityExpire"
	//if !redisConn.SetNX(taskLock, time.Now().Unix(), 12*time.Hour).Val() {
	//	glog.Error("taskVipEquityExpire already running")
	//	return
	//}
	//defer redisConn.Del(taskLock)
	//获取权益名称 5-开卡礼包
	var equityName string
	_, err := services.DataNewEngine().SQL("select equity_name from vip_card_equity e left join vip_card_equity_config " +
		"c on e.id = c.equity_id where c.card_tid=1 and e.equity_type=5;").
		Get(&equityName)
	if err != nil {
		glog.Error("taskVipEquityExpire error: ", err)
		return
	}
	if equityName == "" {
		glog.Error("taskVipEquityExpire 无相关权益信息")
		return
	}

	type user struct {
		UserId     string
		ExpiryDate string
	}
	var list []user

	today := time.Now().Local()
	// 在当前日期的基础上加上 15 天
	future := fmt.Sprintf("%s 23:59:59", today.AddDate(0, 0, 15).Format(kit.DATE_LAYOUT))
	// 格式化输出结果为 yyyy-MM-dd 格式
	err = services.DataNewEngine().SQL("select distinct o.user_id,o.expiry_date from datacenter.vip_card_order o "+
		"inner join datacenter.vip_card_equity_config c on c.card_tid = o.card_id inner join datacenter.vip_card_equity "+
		"e on e.id = c.equity_id and e.equity_type = 5 left join datacenter.vip_user_equity_record r on o.order_sn = "+
		"r.order_sn and r.equity_id = e.id where o.state = 10 and r.id is null and o.card_type = 1 and o.expiry_date = ?;", future).
		Find(&list)
	if err != nil {
		glog.Error("taskVipEquityExpire error: ", err)
		return
	}
	if len(list) > 0 {
		for _, v := range list {
			member := &models.UpetMember{}
			b, err := services.BbcEngine.Table("upet_member").Cols("member_id,scrm_user_id,weixin_mini_openid").
				Where("scrm_user_id = ?", v.UserId).Get(member)
			if err != nil {
				glog.Error("taskVipEquityExpire upet_member error: ", err)
				return
			}
			if !b {
				glog.Error("taskVipEquityExpire upet_member 无记录: ", v.UserId)
				continue
			}
			// 发送提醒
			equityTitle := "权益还有15天即将到期，请尽快使用"
			go sendVoucherExpireWxMessage(member, equityName, v.ExpiryDate, equityTitle)
		}
	}

	//宠物医保到期前1天、15天、30天提醒
	var MonthDay int32
	if has, err := services.DataNewEngine().SQL(`select e.expiry_day from vip_card_equity_config c 
    inner join vip_card_equity e on c.equity_id = e.id where c.or_id = 13 and e.equity_type = 7;`).Get(&MonthDay); err != nil {
		glog.Error("taskVipEquityExpire 保险有效期,error: ", err)
	} else if !has {
		return
	}
	var timeIds = []int32{1, 15, 30}
	wg := new(sync.WaitGroup)
	for _, v := range timeIds {
		wg.Add(1)
		go func(day, MonthDay int32) {
			var list1 []user
			err = services.DataNewEngine().SQL(`select distinct o.user_id,date_add(date_format(o.create_time,'%y%m%d'), interval ? month) expiry_date from datacenter.vip_card_order o 
inner join datacenter.vip_card_equity_config c on c.card_tid = o.card_id 
inner join datacenter.vip_card_equity e on e.id = c.equity_id and e.equity_type = 7 
left join datacenter.vip_user_equity_record r on o.order_sn = r.order_sn and r.equity_id = e.id 
where o.state = 10 and r.id is null and curdate() = date_sub(date_add(date_format(o.create_time,'%y%m%d'), interval ? month), interval ? day);`, MonthDay, MonthDay, day).
				Find(&list1)
			if err != nil {
				glog.Error("taskVipEquityExpire error: ", err)
				return
			}
			if len(list1) > 0 {
				for _, v := range list1 {
					member := &models.UpetMember{}
					b, err := services.BbcEngine.Table("upet_member").Cols("member_id,scrm_user_id,weixin_mini_openid").
						Where("scrm_user_id = ?", v.UserId).Get(member)
					if err != nil {
						glog.Error("taskVipEquityExpire upet_member error: ", err)
						return
					}
					if !b {
						glog.Error("taskVipEquityExpire upet_member 无记录: ", v.UserId)
						continue
					}
					// 发送提醒
					equityTitle := ""
					switch day {
					case 1:
						equityTitle = "权益明天天即将到期，请尽快使用"
					case 15:
						equityTitle = "权益还有15天即将到期，请尽快使用"
					case 30:
						equityTitle = "权益还有1个月即将到期，请尽快使用"
					}
					go sendVoucherExpireWxMessage(member, "宠物医保", v.ExpiryDate, equityTitle)
				}
			}
			wg.Done()
		}(v, MonthDay)
		wg.Wait()
	}
}

// 发送微信订阅消息
func sendVoucherExpireWxMessage(member *models.UpetMember, title, expireTime, equityName string) {
	subTemplate, err := getVoucherExpireSubscribeMessageTemplate()
	if err != nil {
		glog.Error("taskVipEquityExpire sendVoucherExpireWxMessage error: ", err, member.ScrmUserId)
		return
	}
	// 是否能推送订阅消息
	canRe := services.CanSendWxMessage(member.ScrmUserId, "vip_card_expire", 1)
	if !canRe {
		glog.Info("taskVipEquityExpire sendVoucherExpireWxMessage ", member.ScrmUserId, kit.JsonEncode(canRe))
		return
	}

	miniprogramState := ""
	if kit.EnvIsTest() {
		miniprogramState = "trial"
	}
	expireTimeArr := strings.Split(expireTime, " ")
	msgClient := mc.GetMessageCenterClient()
	req := &mc.SubscribeMessageRequest{
		Touser:           member.WeixinMiniOpenid,
		TemplateId:       subTemplate.TemplateId,
		Page:             subTemplate.Page, // 小程序跳转页面
		MiniprogramState: miniprogramState,
		Data:             fmt.Sprintf(subTemplate.Content, title, expireTimeArr[0], equityName),
		IsJpush:          0,
	}
	re, err := msgClient.RPC.SubscribeMessage(msgClient.Ctx, req)
	if err != nil {
		glog.Error("taskVipEquityExpire sendWxMessage error: ", err, member.ScrmUserId)
		return
	}
	if re.Code != 200 {
		glog.Errorf("taskVipEquityExpire 发送会员权益过期通知失败，%s,%s,%s", member.ScrmUserId, kit.JsonEncode(req.Data), kit.JsonEncode(re))
		return
	}

	// 订阅数减一
	_, err = services.DataCenterEngine.Exec("UPDATE wechat_subscribe_message SET number=IF(number<1,0,number-1) WHERE scrm_user_id=? AND template_id=?", member.ScrmUserId, subTemplate.TemplateId)
	if err != nil {
		glog.Error("taskVipEquityExpire 扣减用户订阅数失败，error: ", err, member.ScrmUserId)
		return
	}
}
