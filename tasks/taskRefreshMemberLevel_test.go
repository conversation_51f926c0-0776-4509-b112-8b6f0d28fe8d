package tasks

import (
	"_/models"
	"testing"
)

func Test_refreshMemberLevel(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "刷新用户等级",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			refreshMemberLevel()
		})
	}
}

func Test_handleUserHealthVal(t *testing.T) {
	type args struct {
		userId        string
		sumHealthVal  int64
		userLevelList []*models.UserLevel
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			args: args{
				userId: "000e80cd8445f296asadsdasdasd",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := handleUserHealthVal(tt.args.userId, tt.args.sumHealthVal, tt.args.userLevelList); (err != nil) != tt.wantErr {
				t.Errorf("handleUserHealthVal() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
