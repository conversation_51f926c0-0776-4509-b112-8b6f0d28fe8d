package tasks

import (
	"_/models"
	"_/services"
	"_/utils"
	"errors"
	"fmt"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"github.com/xuri/excelize/v2"
	"net/http"
	"runtime"
	"strconv"
	"time"
)

func initVirtualCardCancelTask(c *cron.Cron) {
	//2秒钟执行一次
	c.AddFunc("@every 2s", CancelVirtualCardTask) // 每2秒分钟执行一次 todo 待处理执行时间周期
}

func CancelVirtualCardTask() {
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()

	taskLock := "customer:task:CancelVirtualCardTask"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 5*time.Minute).Val() {
		glog.Error("CancelVirtualCardTask already running")
		return
	}
	defer redisConn.Del(taskLock)
	taskList := make([]*models.VipCardVirtualCancel, 0)
	db := services.DataCenterEngine
	if err := db.Table("vip_card_virtual_cancel").Where("cancel_status = ?", 1).Find(&taskList); err != nil {
		glog.Error("CancelVirtualCardTask-查询数据失败：", err)
		return
	}
	//if err := db.Table("vip_card_virtual_cancel").Where("id = ?", 50).Find(&taskList); err != nil {
	//	glog.Error("CancelVirtualCardTask-查询数据失败：", err)
	//	return
	//}
	for _, task := range taskList {
		CancelVirtualCard(task)
	}
	return
}

func CancelVirtualCard(task *models.VipCardVirtualCancel) (msg string) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("CancelVirtualCard异常信息捕获：", err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			glog.Errorf("[PANIC RECOVER PWERROR] %v %s\n", err, stack[:length])

		}
	}()
	errList, state, successCount, errorCount, err := DealData(task)
	if err != nil {
		glog.Error("ExecTask-ImportDoctorSchedule-err: ", err)
		msg = "系统错误，导入失败"
		UpdateTask(task.Id, successCount, errorCount, 3, "")
		return
	}
	if state == 0 { //全部失败
		msg = "全部失败"
		if len(errList) > 0 {
			f := importTask(errList)
			url, err := ExportFileToUrl(f)
			if err != nil {
				msg = "系统错误，导入失败"
				UpdateTask(task.Id, successCount, errorCount, 3, "")
				return
			}
			UpdateTask(task.Id, successCount, errorCount, 3, url)
		}
		return
	} else if state == 1 { //全部成功
		f := importTask(errList)
		url, err := ExportFileToUrl(f)
		if err != nil {
			msg = "系统错误，导入失败"
			UpdateTask(task.Id, successCount, errorCount, 3, "")
			return
		}
		msg = "全部成功"
		UpdateTask(task.Id, successCount, errorCount, 2, url)
		return
	} else if state == 2 { //部分成功
		msg = "部分成功"
		if len(errList) > 0 {
			f := importTask(errList)
			url, err := ExportFileToUrl(f)
			if err != nil {
				msg = "系统错误，导入失败"
				UpdateTask(task.Id, successCount, errorCount, 3, "")
				return
			}
			UpdateTask(task.Id, successCount, errorCount, 2, url)
		}
		return
	}
	return
}

// importTaskErr 导出记录
func importTask(rows [][]string) *excelize.File {
	f := excelize.NewFile()
	sheetWords := []string{
		"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
		"AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ",
	}
	for i, list := range rows {
		for j, e := range list {
			f.SetCellValue("Sheet1", sheetWords[j]+strconv.Itoa(i+1), e)
		}
	}
	err := f.Save()
	if err != nil {
		glog.Error("importTask 保存失败; err: ", err)
		return f
	}
	return f
}
func DealData(task *models.VipCardVirtualCancel) (errList [][]string, state, successCount int32, errorCount int32, err error) {
	rows, err := ReadFile(task.FileUrl)
	if err != nil {
		glog.Error("importDoctorSchedule, 读取excel失败, err: ", err)
		return nil, 0, 0, 0, err
	}
	if len(rows) <= 1 {
		var errMsg = "Excel未包含任何数据行."
		glog.Error("importDoctorSchedule, 处理excel失败, err: ", errMsg)
		return nil, 0, 0, 0, errors.New(errMsg)
	}

	// 处理excel数据
	var firstRow []string
	var scheduleErrList [][]string
	var scheduleSuccessList [][]string

	var CardIds []int64 //每一行的医生

	for i, row := range rows {
		var hasFailed = false //是否有错误
		//第一行是表头
		if i < 1 {
			continue
		}
		if len(row) == 0 {
			continue
		}
		CardId := ""

		if len(row) != 2 {
			hasFailed = true
			row = append(row, "模板不正确")
			scheduleErrList = append(scheduleErrList, row)
			continue
		}
		if len(row[1]) != 18 {
			hasFailed = true
			row = append(row, "券密格式错误")
			scheduleErrList = append(scheduleErrList, row)
			continue
		}
		CardId = utils.MobileEncrypt(row[1])
		////去掉前缀的卡号
		//if len(row[0]) >= 12 && strings.Contains(row[0], "FY") {
		//	CardId = row[0][2:len(row[0])]
		//} else {
		//	hasFailed = true
		//	row = append(row, "券号格式错误")
		//	scheduleErrList = append(scheduleErrList, row)
		//	continue
		//}
		vipCardVirtualFirst := make([]*models.VipCardVirtual, 0)
		db := services.DataCenterEngine
		if err := db.Table("vip_card_virtual").Where("card_pass=? ", CardId).Limit(1).Find(&vipCardVirtualFirst); err != nil {
			glog.Error("vip_card_virtual-查询数据失败：", err)
			hasFailed = true
			row = append(row, "查询出错")
			scheduleErrList = append(scheduleErrList, row)
			continue
		}
		if len(vipCardVirtualFirst) == 0 {
			hasFailed = true
			row = append(row, "卡密不存在")
			scheduleErrList = append(scheduleErrList, row)
			continue
		}
		vipCardVirtual := vipCardVirtualFirst[0]
		if vipCardVirtual.OrgId != task.OrgId {
			hasFailed = true
			row = append(row, "和组织名称不对应")
			scheduleErrList = append(scheduleErrList, row)
			continue
		}

		if vipCardVirtual.Status == 1 {
			hasFailed = true
			row = append(row, "已激活")
			scheduleErrList = append(scheduleErrList, row)
			continue
		}
		if vipCardVirtual.Status == 2 {
			hasFailed = true
			row = append(row, "已注销")
			scheduleErrList = append(scheduleErrList, row)
			continue
		}
		if vipCardVirtual.Status == 3 {
			hasFailed = true
			row = append(row, "已失效")
			scheduleErrList = append(scheduleErrList, row)
			continue
		}
		if !hasFailed {
			row = append(row, "")
			scheduleSuccessList = append(scheduleSuccessList, row)
			CardIds = append(CardIds, vipCardVirtual.CardId)
			continue
		}
	}
	//批量插入医生排班表
	if len(CardIds) > 0 {
		services.DataCenterEngine.Table("vip_card_virtual").
			In("card_id", CardIds).
			Update(&models.VipCardVirtual{Status: 2})
	}
	if len(scheduleSuccessList) == len(rows)-1 {
		state = 1 //全部成功
	} else if len(scheduleErrList) == len(rows)-1 {
		state = 0 //全部失败
	} else {
		state = 2 //部分成功
	}
	if len(scheduleErrList) > 0 || len(scheduleSuccessList) > 0 {
		firstRow = append(firstRow, "卡号", "卡密", "错误原因")
		errList = append([][]string{}, firstRow)
		errList = append(errList, scheduleErrList...)
		errList = append(errList, scheduleSuccessList...)
	}
	return errList, state, int32(len(scheduleSuccessList)), int32(len(scheduleErrList)), nil
}

// UpdateTask 更新任务
func UpdateTask(taskId int64, cancelNum int32, errorNum int32, taskStatus int32, url string) {
	task := &models.VipCardVirtualCancel{
		Id:           taskId,
		CancelNum:    cancelNum,
		CancelStatus: taskStatus,
		CancelResult: url,
		ErrorNum:     errorNum,
	}
	services.DataCenterEngine.ID(task.Id).Update(task)
}

// ReadFile 读取任务文件
func ReadFile(url string) ([][]string, error) {
	// 下载excel
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		glog.Error("ReadFile, 下载excel失败, err: ", err)
		return nil, err
	}
	resp, err := utils.HttpClient.Do(req)
	if err != nil {
		glog.Error("ReadFile, 下载excel失败, err: ", err)
		return nil, err
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		glog.Error("ReadFile, 读取excel失败, err: ", err)
		return nil, err
	}
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		glog.Error("ReadFile, GetRows失败, err: ", err)
		return nil, err
	}
	return rows, nil
}

// ExportFileToUrl 将文件上传阿里云
func ExportFileToUrl(f *excelize.File) (string, error) {
	//file, err := f.WriteToBuffer()
	//if err != nil {
	//	return "", err
	//}
	//bodyBuffer := &bytes.Buffer{}
	//bodyWriter := multipart.NewWriter(bodyBuffer)

	fileName := fmt.Sprintf("虚拟卡券注销(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)

	//fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)
	//_, err = io.Copy(fileWriter, file)
	//if err != nil {
	//	glog.Error("ExportFileToUrl io.Copy; err: ", err)
	//	return "", err
	//}

	return utils.UploadExcelToQiNiu1(f, fileName)

	//path := config.GetString("file-upload-url") + "/fss/uposs"
	//contentType := bodyWriter.FormDataContentType()
	//err = bodyWriter.Close()
	//if err != nil {
	//	glog.Error("ExportFileToUrl bodyWriter.Close; err: ", err)
	//	return "", err
	//}
	//// 上传文件
	//resp, err := http.Post(path, contentType, bodyBuffer)
	//if err != nil {
	//	glog.Error("ExportFileToUrl http.Post; err: ", err)
	//	return "", err
	//}
	//defer resp.Body.Close()
	//
	//respBody, err := io.ReadAll(resp.Body)
	//if err != nil {
	//	glog.Error("ExportFileToUrl io.ReadAll; err: ", err)
	//	return "", err
	//}
	//var result UploadResult
	//err = json.Unmarshal(respBody, &result)
	//if err != nil {
	//	glog.Error("ExportFileToUrl json.Unmarshal; err: ", err)
	//	return "", err
	//}
	//if len(result.Url) == 0 {
	//	return "", errors.New(result.Err)
	//}
	//if strings.Index(result.Url, "https") < 0 {
	//	result.Url = strings.Replace(result.Url, "http", "https", 1)
	//}
	//return result.Url, nil
}

// UploadResult 返回信息
type UploadResult struct {
	Url string `json:"url"`
	Err string `json:"error"`
}
