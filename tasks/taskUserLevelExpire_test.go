package tasks

import (
	"_/models"
	"testing"
)

func Test_userLevelExpireTask(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "会员等级过期通知",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			userLevelExpireTask()
		})
	}
}

func Test_sendLevelExpireWxMessage(t *testing.T) {
	type args struct {
		member       *models.UpetMember
		curLevelName string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			args: args{
				member:       &models.UpetMember{ScrmUserId: "14877d6e2f4f06e90d076cddbf53ba35"},
				curLevelName: "v2",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sendLevelExpireWxMessage(tt.args.member, tt.args.curLevelName)
		})
	}
}
