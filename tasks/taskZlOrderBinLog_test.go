package tasks

import (
	"testing"
)

func Test_zlOrderPay(t *testing.T) {
	type args struct {
		id int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{name: "测试binlog子龙下单",
			args: args{id: 100015891}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := zlOrderPay(tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("zlOrderPay() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
