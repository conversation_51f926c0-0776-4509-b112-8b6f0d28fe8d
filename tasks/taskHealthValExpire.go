package tasks

import (
	"_/models"
	"_/services"
	"_/utils"
	"fmt"
	"math"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
)

func InitHealthValExpireTask(c *cron.Cron) {
	//每天凌晨1点定时是否有过期的会员，有则扣减健康值
	c.AddFunc("0 1 * * *", healthValExpireTask)
}

// 用户健康值及等级变更说明：
// 若会员在等级过期前1-3个月内，通过消费或做任务获得过健康值，等级有效期过后，等级身份不会失效，
// 仍然是当前会员等级。等级过期1-3个月指，若过期时间是2022年5月16日，则1-3个月指2022年2月16日-5月16日之间。
//
// 若会员在等级过期前1-3个月，没有健康值的增长记录，则会员等级到期后，以往等级身份将立即失效，
// 健康值立即消减20%。在健康值消减后最晚T+1日，根据消减后的最新健康值，对应到相应会员等级上，即为会员的新会员等级。
// 若会员等级过期导致健康值消减20%后，会员又连续多年一直没有健康值的增长记录，则逐年消减10%健康值，直到健康值达到0为止。
func healthValExpireTask() {
	glog.Info("healthValExpireTask start")
	//初始化redis
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()

	taskLock := "customercenter:task:healthValExpireTaskLock"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 12*time.Hour).Val() {
		glog.Error("healthValExpireTask already running")
		return
	}
	defer redisConn.Del(taskLock)

	// 获取会员等级
	userLevels, err := getUserLevels()
	if err != nil {
		glog.Error("healthValExpireTask failed, error: ", err)
		return
	}

	curTime := time.Now()
	st := time.Date(curTime.Year(), curTime.Month(), curTime.Day()-1, 0, 0, 0, 0, time.Local).Unix()
	et := time.Date(curTime.Year(), curTime.Month(), curTime.Day()-1, 23, 59, 59, 0, time.Local).Unix()
	startId, size := int64(0), 100
	for {
		list := make([]*models.UpetMember, 0)
		// 查询昨天到期的会员
		err := services.BbcEngine.Table("upet_member").Select("member_id,scrm_user_id,user_level_id,user_level_id,user_level_stime,user_level_etime,weixin_mini_openid").
			Where("member_id>? AND user_level_etime>=? AND user_level_etime<=?", startId, st, et).Limit(size).Find(&list)
		if err != nil {
			glog.Error("healthValExpireTask.upet_member error: ", err)
			break
		}
		if len(list) == 0 {
			break
		}

		for _, v := range list {
			startId = v.MemberId

			err := handleUserLevel(v, userLevels)
			if err != nil {
				glog.Error("healthValExpireTask.handleUserLevel error: ", err)
				break
			}
		}
	}

	glog.Info("healthValExpireTask end")
	return
}

func handleUserLevel(member *models.UpetMember, userLevels map[int64]*models.UserLevel) error {
	// 查询用户前3月是否有获得过健康值，存在健康值记录，会员等级续期一年
	curTime := time.Now()
	st := time.Date(curTime.Year(), curTime.Month()-3, curTime.Day(), 0, 0, 0, 0, time.Local).Format("2006-01-02 15:04:05")
	cnt, err := services.DataCenterEngine.Table("health_detail").Where("user_id=? AND `type`=1 AND create_time>?",
		member.ScrmUserId, st).Count()
	if err != nil {
		return err
	}
	// 存在新记录，会员等级有效期延长一年；会员等级为V0，等级不变
	if cnt > 0 {
		return extendMemebrLevel(member.ScrmUserId)
	}

	// 若会员等级过期导致健康值消减20%后，会员又连续多年一直没有健康值的增长记录，则逐年消减10%健康值，直到健康值达到0为止。
	// 判断上一年是否有降级记录，有则扣减10%，无则扣减20%
	st1 := time.Date(curTime.Year()-1, curTime.Month(), curTime.Day(), 0, 0, 0, 0, time.Local).Format("2006-01-02 15:04:05")
	et1 := time.Date(curTime.Year()-1, curTime.Month(), curTime.Day(), 23, 59, 59, 0, time.Local).Format("2006-01-02 15:04:05")
	liftCnt, err := services.BbcEngine.Table("upet_member_level_log").Where("scrm_user_id=? AND lift_type=? AND create_time>=? AND create_time<=?",
		member.ScrmUserId, 2, st1, et1).Count()
	rate := 0.2
	if liftCnt > 0 {
		rate = 0.1
	}

	// 会员等级变更
	memberIntegralInfo := &models.MemberIntegralInfo{}
	_, err = services.DataCenterEngine.Table("member_integral_info").Where("memberid=?", member.ScrmUserId).Get(memberIntegralInfo)
	if err != nil {
		return err
	}
	// 扣减的健康值
	decHealthVal := int64(math.Round(rate * float64(memberIntegralInfo.HealthVal)))
	if decHealthVal <= 0 {
		decHealthVal = int64(memberIntegralInfo.HealthVal)
	}

	session := services.DataCenterEngine.NewSession()
	defer session.Close()

	session.Begin()
	_, err = session.Table("health_detail").Insert(&models.HealthDetail{
		UserId:          member.ScrmUserId,
		Type:            2,
		Title:           "会员等级失效",
		Content:         fmt.Sprintf("会员等级失效，扣减%.0f%%健康值", rate*100),
		HealthVal:       decHealthVal,
		PrevHealthCount: int64(memberIntegralInfo.HealthVal),
		CurrHealthCount: int64(memberIntegralInfo.HealthVal) - decHealthVal,
		HealthType:      4,
		EffectTime:      time.Now(),
		PayAmount:       "0.0",
		RefundAmount:    "0.0",
	})
	if err != nil {
		session.Rollback()
		return err
	}
	_, err = session.Exec("UPDATE member_integral_info SET health_val=IF(health_val < ?, 0, health_val-?) WHERE memberid=?", decHealthVal, decHealthVal, member.ScrmUserId)
	if err != nil {
		session.Rollback()
		return err
	}
	session.Commit()

	newUserLevelId := member.UserLevelId
	userCurlevel := userLevels[member.UserLevelId]
	// 判断会员等级是否需要变更
	// 当前健康值减掉仍大于当前会员等级需要的健康值，则会员等级不变，小于则降级
	newHealthVal := int64(memberIntegralInfo.HealthVal) - decHealthVal
	oriLevelName, curLevelName := "", "V0闻卡会员"
	if newHealthVal < userCurlevel.HealthVal {
		newUserLevelId = member.UserLevelId - 1
		oriLevelName = fmt.Sprintf("V%d%s", userCurlevel.LevelId, userCurlevel.LevelName)
		if newUserLevelId > 0 {
			curLevelName = fmt.Sprintf("V%d%s", userLevels[newUserLevelId].LevelId, userLevels[newUserLevelId].LevelName)
		}
	}
	_, err = services.BbcEngine.Table("upet_member").Where("scrm_user_id=?", member.ScrmUserId).Update(map[string]interface{}{
		"user_level_id":    newUserLevelId,
		"user_level_etime": time.Now().AddDate(1, 0, -1).Unix(), // 计算的是昨天过期的会员
	})
	if err != nil {
		glog.Error("upet_member.Update error: ", err)
		return err
	}

	// 会员等级变更
	if newUserLevelId != member.UserLevelId {
		_, err = services.BbcEngine.Table("upet_member_level_log").Insert(&models.UpetMemberLevelLog{
			ScrmUserId:     member.ScrmUserId,
			LiftType:       models.MemberLevelLiftTypeDown,
			OldUserLevelId: member.UserLevelId,
			NewUserLevelId: newUserLevelId,
			Content:        "会员等级到期后，变更会员等级",
		})
		if err != nil {
			return err
		}
		glog.Info(fmt.Sprintf("upet_member %s会员等级失效，old:%d, new:%d", member.ScrmUserId, member.UserLevelId, newUserLevelId))

		// 发送微信订阅通知
		go services.SendLevelChangeWxMessage(member, newUserLevelId, newHealthVal, oriLevelName, curLevelName)
	}

	return nil
}

func getUserLevels() (map[int64]*models.UserLevel, error) {
	data := make(map[int64]*models.UserLevel, 0)
	list := make([]*models.UserLevel, 0)
	err := services.DataCenterEngine.Table("user_level").Where("level_status=1").Find(&list)
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		data[v.LevelId] = v
	}
	return data, nil
}

// 延长会员有效期一年
func extendMemebrLevel(scrmUserId string) error {
	_, err := services.BbcEngine.Table("upet_member").Where("scrm_user_id=?", scrmUserId).Update(map[string]interface{}{
		"user_level_etime": time.Now().AddDate(1, 0, -1).Unix(),
	})
	return err
}
