package tasks

import (
	"_/proto/et"
	"_/services"
	"_/utils"
	"fmt"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	"sync"
	"time"
)

func InitSaveMoneyTask(c *cron.Cron) {
	//每天跑一次算所有购买了VIP卡的人节约多少钱
	c.AddFunc("0 */5 * * *", SaveMoneyTask)
}

//累积节省¥xxx的逻辑：
//包含开卡礼包、实物商品、服务金虚拟团单、宠物体检券、宠物医保
//1、开卡礼包，领取开卡礼包的订单生成并订单状态变成“已完成”后，计算节省金额，取开卡礼包的价值
//2、实物商品，购买商品的订单变成“已完成”状态后，算节省金额：商品市场价-会员价
//3、服务金虚拟团单，购买成功后算节省的金额：不需要等到核销，节省的金额：抵扣的服务金数据
//（如果后续退了这个团单的订单，节省金额再扣减回来）
//4、宠物体检券，领取后需要订单核销完成算节省金额，节省的金额：一个体检券算49元，两个体检券算98元，取子龙优惠券接口状态为“已核销”
//5、宠物医保：用户领取保险成功后，算节省200元
func SaveMoneyTask() {

	////初始化redis
	redisConn := utils.GetLongRedisConn()
	//
	taskLock := "customercenter:task:SaveMoneyTaskLock1"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 5*time.Hour).Val() {
		glog.Error("SaveMoneyTask already running")
		return
	}
	defer func() {
		redisConn1 := utils.GetLongRedisConn()
		redisConn1.Del(taskLock)
	}()

	glog.Info("SaveMoneyTask 开始")

	//先查询出要计算的所有用户
	db := services.DataCenterEngine
	bbcdb := services.BbcEngine
	userList := make([]string, 0)

	err := db.SQL("SELECT user_id FROM datacenter.vip_card_order GROUP BY user_id").Find(&userList)
	if err != nil {
		glog.Error("SaveMoneyTask 查询要处理的用户失败:", err.Error())
		return
	}

	//循环一个一个的处理用户的节约金额，存入redis
	for index, userId := range userList {
		//根据SCRMID转化为电商的用户ID
		memberId := 0

		glog.Info(fmt.Sprintf("SaveMoneyTask 共有:%d 个用户,当前处理第个 %d, 用户ID： %s", len(userList), index+1, userId))
		//userId = "a2f592b3eb634ee4adcda9bbae9e3391"
		//userId = "58fc60189fe645e9943f3e392e7eec8d"
		//统计开卡礼包节约金额
		var kklb float64
		//统计实物商品节约金额
		var swsp float64
		//服务金虚拟订单
		var fwj float64
		//保险节约的金额
		var bjje float64
		//体检券金额
		var tjj float64
		//家庭服务包
		var jtfwb float64

		_, err = bbcdb.SQL("SELECT member_id FROM upet_member where scrm_user_id=?", userId).Get(&memberId)
		if err != nil {
			glog.Error("SaveMoneyTask 根据scrmID转电商ID失败:", err.Error())
			return
		}

		wg := new(sync.WaitGroup)
		wg.Add(6)

		//宠物体检券
		go func() {
			defer wg.Done()
			//先查询出这个人所有的体检券，然后去调用子龙接口判断是否使用了
			//查询这个人领取的体检券
			cardList := make([]string, 0)
			err = db.SQL(" SELECT gift_order_sn FROM vip_user_equity_record a  "+
				"INNER JOIN vip_card_equity b ON a.equity_id=b.id "+
				"INNER JOIN  vip_card_order c ON a.`order_sn`=c.`order_sn` "+
				"WHERE b.`equity_short_name`='宠物体检券' and gift_order_sn!='' and c.user_id=? ", userId).Find(&cardList)
			if err != nil {
				glog.Error("SaveMoneyTask 体检券查询失败:", err.Error())
			}
			//如果有体检券，就去子龙查询状态
			if len(cardList) > 0 {
				client := et.GetExternalClient()
				glog.Info("SaveMoneyTask 用户ID："+userId+" 查询子龙请求：", cardList)
				resp, err := client.ZiLong.GetVerifyDetail(client.Ctx, &et.GetVerifyDetailReq{
					CouponCodes: cardList,
				})
				if err != nil {
					glog.Error("SaveMoneyTask 查询子龙券状态出错:", err.Error())
				}

				if len(resp.Data) == 0 {
					glog.Error("SaveMoneyTask 查询子龙券状态出错:")
				}
				glog.Info("SaveMoneyTask 用户ID："+userId+" 查询子龙返回：", resp)
				sumcCupon := float64(0)
				couponMap := make(map[string]int)
				for _, data := range resp.Data {
					//子龙会返回重复的数据，所以去重
					if data.Status == "2" {
						_, ok := couponMap[data.CouponCode]
						if ok {
							continue
						}
						sumcCupon++
						couponMap[data.CouponCode] = 1
					}
				}

				tjj = sumcCupon * 49
			}
		}()

		//家庭服务包
		go func() {

			defer wg.Done()
			_, err = db.SQL(" SELECT IFNULL(count(1)*99,0) FROM vip_user_equity_record a  "+
				"INNER JOIN vip_card_equity b ON a.equity_id=b.id "+
				"INNER JOIN  vip_card_order c ON a.`order_sn`=c.`order_sn` "+
				"WHERE b.`equity_type`=6 and c.state=10 and c.user_id=? ", userId).Get(&jtfwb)
			if err != nil {
				glog.Error("SaveMoneyTask 统计家庭服务包金额查询失败:", err.Error())
			}

		}()

		//统计开卡礼包
		go func() {

			defer wg.Done()
			_, err = bbcdb.SQL(`SELECT IFNULL(SUM(goods_price), 0) FROM upet_orders a 
                 INNER JOIN  upet_order_goods  b ON a.order_id=b.order_id 
                        WHERE order_type=19 AND order_state>10 and a.buyer_id=?`, memberId).Get(&kklb)
			if err != nil {
				glog.Error("SaveMoneyTask 统计开卡礼包节约金额查询失败:", err.Error())
			}
		}()

		//统计实物商品
		go func() {
			defer wg.Done()
			_, err = bbcdb.SQL(`SELECT IFNULL(SUM(b.goods_original_price-b.goods_price)*b.goods_num,0) FROM upet_orders a 
                 INNER JOIN  upet_order_goods  b ON a.order_id=b.order_id 
                 LEFT JOIN upet_refund_return c ON a.order_sn=c.order_sn AND b.goods_id=c.goods_id   AND c.refund_state=3
                        WHERE goods_type=16 AND order_state>10 and a.buyer_id=? AND c.buyer_name IS NULL`, memberId).Get(&swsp)
			if err != nil {
				glog.Error("SaveMoneyTask 统计实物商品节约金额查询失败:", err.Error())
			}
		}()

		//统计服务金虚拟订单
		go func() {
			defer wg.Done()
			_, err = db.SQL("SELECT IFNULL(SUM(generate_amount*-1),0) FROM member_property_guarantee_quota_detail   WHERE quota_type=2  AND link_order_number!=''  and member_id=?", userId).Get(&fwj)
			if err != nil {
				glog.Error("SaveMoneyTask 服务金虚拟订单节约金额查询失败:", err.Error())
			}
			if fwj != 0 {
				fwj = fwj / 100
			}

		}()

		//保险金额
		go func() {
			defer wg.Done()
			_, err = db.SQL(" SELECT IFNULL(COUNT(1)*200,0) FROM vip_user_equity_record a  INNER JOIN vip_card_equity b ON a.equity_id=b.id "+
				" INNER JOIN  vip_card_order c ON a.`order_sn`=c.`order_sn` "+
				" WHERE equity_type=7 and c.user_id=? and a.status=2 and c.state=10", userId).Get(&bjje)
			if err != nil {
				glog.Error("SaveMoneyTask 宠物体检券查询失败:", err.Error())
			}
		}()
		wg.Wait()
		SumMoney := kklb + swsp + fwj + bjje + tjj + jtfwb

		////统计开卡礼包节约金额
		//var kklb float64
		////统计实物商品节约金额
		//var swsp float64
		////服务金虚拟订单
		//var fwj float64
		////保险节约的金额
		//var bjje float64
		////体检券金额
		//var tjj float64

		glog.Info(fmt.Sprintf("SaveMoneyTask 用户ID： %s 开卡礼包金额：%f 实物商品：%f 服务金虚拟订单：%f 保险节约的金额：%f 体检券金额：%f 家庭服务包：%f", userId, kklb, swsp, fwj, bjje, tjj, jtfwb))
		//大于0才存到redis里面，读取的时候，没有读到都显示0
		redisConn = utils.GetLongRedisConn()
		formattedNum := fmt.Sprintf("%.2f", SumMoney)
		if SumMoney > 0 {
			//定时任务跑的数据，缓存redis
			redisConn.HSet("customercenter:SaveMoney", userId, formattedNum)
		} else {
			redisConn.HDel("customercenter:SaveMoney", userId)
		}

	}
	glog.Info("SaveMoneyTask 结束")

}
