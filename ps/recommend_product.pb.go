// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ps/recommend_product.proto

package ps

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/golang/protobuf/ptypes/struct"
	_ "github.com/golang/protobuf/ptypes/wrappers"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type RecommendProductRequest struct {
	//分页索引，0为随机获取指定page_size条数据
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//财务编码
	FinanceCode string `protobuf:"bytes,2,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//用户id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物id
	PetId string `protobuf:"bytes,4,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//分页大小
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//排序字段
	OrderBy              string   `protobuf:"bytes,6,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendProductRequest) Reset()         { *m = RecommendProductRequest{} }
func (m *RecommendProductRequest) String() string { return proto.CompactTextString(m) }
func (*RecommendProductRequest) ProtoMessage()    {}
func (*RecommendProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_445b6aa590913b14, []int{0}
}

func (m *RecommendProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendProductRequest.Unmarshal(m, b)
}
func (m *RecommendProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendProductRequest.Marshal(b, m, deterministic)
}
func (m *RecommendProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendProductRequest.Merge(m, src)
}
func (m *RecommendProductRequest) XXX_Size() int {
	return xxx_messageInfo_RecommendProductRequest.Size(m)
}
func (m *RecommendProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendProductRequest proto.InternalMessageInfo

func (m *RecommendProductRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *RecommendProductRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *RecommendProductRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *RecommendProductRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *RecommendProductRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *RecommendProductRequest) GetOrderBy() string {
	if m != nil {
		return m.OrderBy
	}
	return ""
}

type RecommendProductResponse struct {
	Code    int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string              `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details []*RecommendProduct `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//总记录数
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendProductResponse) Reset()         { *m = RecommendProductResponse{} }
func (m *RecommendProductResponse) String() string { return proto.CompactTextString(m) }
func (*RecommendProductResponse) ProtoMessage()    {}
func (*RecommendProductResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_445b6aa590913b14, []int{1}
}

func (m *RecommendProductResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendProductResponse.Unmarshal(m, b)
}
func (m *RecommendProductResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendProductResponse.Marshal(b, m, deterministic)
}
func (m *RecommendProductResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendProductResponse.Merge(m, src)
}
func (m *RecommendProductResponse) XXX_Size() int {
	return xxx_messageInfo_RecommendProductResponse.Size(m)
}
func (m *RecommendProductResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendProductResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendProductResponse proto.InternalMessageInfo

func (m *RecommendProductResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RecommendProductResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RecommendProductResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *RecommendProductResponse) GetDetails() []*RecommendProduct {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *RecommendProductResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type RecommendProduct struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	FinanceCode          string   `protobuf:"bytes,2,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	UserId               string   `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	PetId                string   `protobuf:"bytes,4,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	SkuId                int32    `protobuf:"varint,5,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	SortIndex            int32    `protobuf:"varint,6,opt,name=sort_index,json=sortIndex,proto3" json:"sort_index"`
	CreateDate           string   `protobuf:"bytes,7,opt,name=create_date,json=createDate,proto3" json:"create_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendProduct) Reset()         { *m = RecommendProduct{} }
func (m *RecommendProduct) String() string { return proto.CompactTextString(m) }
func (*RecommendProduct) ProtoMessage()    {}
func (*RecommendProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_445b6aa590913b14, []int{2}
}

func (m *RecommendProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendProduct.Unmarshal(m, b)
}
func (m *RecommendProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendProduct.Marshal(b, m, deterministic)
}
func (m *RecommendProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendProduct.Merge(m, src)
}
func (m *RecommendProduct) XXX_Size() int {
	return xxx_messageInfo_RecommendProduct.Size(m)
}
func (m *RecommendProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendProduct.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendProduct proto.InternalMessageInfo

func (m *RecommendProduct) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecommendProduct) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *RecommendProduct) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *RecommendProduct) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *RecommendProduct) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *RecommendProduct) GetSortIndex() int32 {
	if m != nil {
		return m.SortIndex
	}
	return 0
}

func (m *RecommendProduct) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

type RecommendTipsRequest struct {
	//分页索引
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//用户id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物id
	PetId string `protobuf:"bytes,4,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//分页大小
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//排序字段
	OrderBy              string   `protobuf:"bytes,6,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendTipsRequest) Reset()         { *m = RecommendTipsRequest{} }
func (m *RecommendTipsRequest) String() string { return proto.CompactTextString(m) }
func (*RecommendTipsRequest) ProtoMessage()    {}
func (*RecommendTipsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_445b6aa590913b14, []int{3}
}

func (m *RecommendTipsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendTipsRequest.Unmarshal(m, b)
}
func (m *RecommendTipsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendTipsRequest.Marshal(b, m, deterministic)
}
func (m *RecommendTipsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendTipsRequest.Merge(m, src)
}
func (m *RecommendTipsRequest) XXX_Size() int {
	return xxx_messageInfo_RecommendTipsRequest.Size(m)
}
func (m *RecommendTipsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendTipsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendTipsRequest proto.InternalMessageInfo

func (m *RecommendTipsRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *RecommendTipsRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *RecommendTipsRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *RecommendTipsRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *RecommendTipsRequest) GetOrderBy() string {
	if m != nil {
		return m.OrderBy
	}
	return ""
}

type RecommendTipsResponse struct {
	Code    int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string           `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details []*RecommendTips `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//总记录数
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendTipsResponse) Reset()         { *m = RecommendTipsResponse{} }
func (m *RecommendTipsResponse) String() string { return proto.CompactTextString(m) }
func (*RecommendTipsResponse) ProtoMessage()    {}
func (*RecommendTipsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_445b6aa590913b14, []int{4}
}

func (m *RecommendTipsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendTipsResponse.Unmarshal(m, b)
}
func (m *RecommendTipsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendTipsResponse.Marshal(b, m, deterministic)
}
func (m *RecommendTipsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendTipsResponse.Merge(m, src)
}
func (m *RecommendTipsResponse) XXX_Size() int {
	return xxx_messageInfo_RecommendTipsResponse.Size(m)
}
func (m *RecommendTipsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendTipsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendTipsResponse proto.InternalMessageInfo

func (m *RecommendTipsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RecommendTipsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RecommendTipsResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *RecommendTipsResponse) GetDetails() []*RecommendTips {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *RecommendTipsResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type RecommendTips struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	UserId               string   `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	PetId                string   `protobuf:"bytes,3,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	PetTipsId            int32    `protobuf:"varint,4,opt,name=pet_tips_id,json=petTipsId,proto3" json:"pet_tips_id"`
	SortIndex            int32    `protobuf:"varint,5,opt,name=sort_index,json=sortIndex,proto3" json:"sort_index"`
	CreateDate           string   `protobuf:"bytes,6,opt,name=create_date,json=createDate,proto3" json:"create_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendTips) Reset()         { *m = RecommendTips{} }
func (m *RecommendTips) String() string { return proto.CompactTextString(m) }
func (*RecommendTips) ProtoMessage()    {}
func (*RecommendTips) Descriptor() ([]byte, []int) {
	return fileDescriptor_445b6aa590913b14, []int{5}
}

func (m *RecommendTips) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendTips.Unmarshal(m, b)
}
func (m *RecommendTips) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendTips.Marshal(b, m, deterministic)
}
func (m *RecommendTips) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendTips.Merge(m, src)
}
func (m *RecommendTips) XXX_Size() int {
	return xxx_messageInfo_RecommendTips.Size(m)
}
func (m *RecommendTips) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendTips.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendTips proto.InternalMessageInfo

func (m *RecommendTips) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecommendTips) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *RecommendTips) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *RecommendTips) GetPetTipsId() int32 {
	if m != nil {
		return m.PetTipsId
	}
	return 0
}

func (m *RecommendTips) GetSortIndex() int32 {
	if m != nil {
		return m.SortIndex
	}
	return 0
}

func (m *RecommendTips) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

type RecommendTipsSpecialRequest struct {
	//分页索引
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//用户id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//宠物id
	PetId string `protobuf:"bytes,4,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//分页大小
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//排序字段
	OrderBy              string   `protobuf:"bytes,6,opt,name=order_by,json=orderBy,proto3" json:"order_by"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendTipsSpecialRequest) Reset()         { *m = RecommendTipsSpecialRequest{} }
func (m *RecommendTipsSpecialRequest) String() string { return proto.CompactTextString(m) }
func (*RecommendTipsSpecialRequest) ProtoMessage()    {}
func (*RecommendTipsSpecialRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_445b6aa590913b14, []int{6}
}

func (m *RecommendTipsSpecialRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendTipsSpecialRequest.Unmarshal(m, b)
}
func (m *RecommendTipsSpecialRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendTipsSpecialRequest.Marshal(b, m, deterministic)
}
func (m *RecommendTipsSpecialRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendTipsSpecialRequest.Merge(m, src)
}
func (m *RecommendTipsSpecialRequest) XXX_Size() int {
	return xxx_messageInfo_RecommendTipsSpecialRequest.Size(m)
}
func (m *RecommendTipsSpecialRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendTipsSpecialRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendTipsSpecialRequest proto.InternalMessageInfo

func (m *RecommendTipsSpecialRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *RecommendTipsSpecialRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *RecommendTipsSpecialRequest) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *RecommendTipsSpecialRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *RecommendTipsSpecialRequest) GetOrderBy() string {
	if m != nil {
		return m.OrderBy
	}
	return ""
}

type RecommendTipsSpecialResponse struct {
	Code    int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string           `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Details []*RecommendTips `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	//总记录数
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendTipsSpecialResponse) Reset()         { *m = RecommendTipsSpecialResponse{} }
func (m *RecommendTipsSpecialResponse) String() string { return proto.CompactTextString(m) }
func (*RecommendTipsSpecialResponse) ProtoMessage()    {}
func (*RecommendTipsSpecialResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_445b6aa590913b14, []int{7}
}

func (m *RecommendTipsSpecialResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendTipsSpecialResponse.Unmarshal(m, b)
}
func (m *RecommendTipsSpecialResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendTipsSpecialResponse.Marshal(b, m, deterministic)
}
func (m *RecommendTipsSpecialResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendTipsSpecialResponse.Merge(m, src)
}
func (m *RecommendTipsSpecialResponse) XXX_Size() int {
	return xxx_messageInfo_RecommendTipsSpecialResponse.Size(m)
}
func (m *RecommendTipsSpecialResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendTipsSpecialResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendTipsSpecialResponse proto.InternalMessageInfo

func (m *RecommendTipsSpecialResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RecommendTipsSpecialResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RecommendTipsSpecialResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *RecommendTipsSpecialResponse) GetDetails() []*RecommendTips {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *RecommendTipsSpecialResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type RecommendTipsSpecial struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	UserId               string   `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	PetId                string   `protobuf:"bytes,3,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	PetTipsId            int32    `protobuf:"varint,4,opt,name=pet_tips_id,json=petTipsId,proto3" json:"pet_tips_id"`
	SortIndex            int32    `protobuf:"varint,5,opt,name=sort_index,json=sortIndex,proto3" json:"sort_index"`
	CreateDate           string   `protobuf:"bytes,6,opt,name=create_date,json=createDate,proto3" json:"create_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendTipsSpecial) Reset()         { *m = RecommendTipsSpecial{} }
func (m *RecommendTipsSpecial) String() string { return proto.CompactTextString(m) }
func (*RecommendTipsSpecial) ProtoMessage()    {}
func (*RecommendTipsSpecial) Descriptor() ([]byte, []int) {
	return fileDescriptor_445b6aa590913b14, []int{8}
}

func (m *RecommendTipsSpecial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendTipsSpecial.Unmarshal(m, b)
}
func (m *RecommendTipsSpecial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendTipsSpecial.Marshal(b, m, deterministic)
}
func (m *RecommendTipsSpecial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendTipsSpecial.Merge(m, src)
}
func (m *RecommendTipsSpecial) XXX_Size() int {
	return xxx_messageInfo_RecommendTipsSpecial.Size(m)
}
func (m *RecommendTipsSpecial) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendTipsSpecial.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendTipsSpecial proto.InternalMessageInfo

func (m *RecommendTipsSpecial) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecommendTipsSpecial) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *RecommendTipsSpecial) GetPetId() string {
	if m != nil {
		return m.PetId
	}
	return ""
}

func (m *RecommendTipsSpecial) GetPetTipsId() int32 {
	if m != nil {
		return m.PetTipsId
	}
	return 0
}

func (m *RecommendTipsSpecial) GetSortIndex() int32 {
	if m != nil {
		return m.SortIndex
	}
	return 0
}

func (m *RecommendTipsSpecial) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

func init() {
	proto.RegisterType((*RecommendProductRequest)(nil), "ps.RecommendProductRequest")
	proto.RegisterType((*RecommendProductResponse)(nil), "ps.RecommendProductResponse")
	proto.RegisterType((*RecommendProduct)(nil), "ps.RecommendProduct")
	proto.RegisterType((*RecommendTipsRequest)(nil), "ps.RecommendTipsRequest")
	proto.RegisterType((*RecommendTipsResponse)(nil), "ps.RecommendTipsResponse")
	proto.RegisterType((*RecommendTips)(nil), "ps.RecommendTips")
	proto.RegisterType((*RecommendTipsSpecialRequest)(nil), "ps.RecommendTipsSpecialRequest")
	proto.RegisterType((*RecommendTipsSpecialResponse)(nil), "ps.RecommendTipsSpecialResponse")
	proto.RegisterType((*RecommendTipsSpecial)(nil), "ps.RecommendTipsSpecial")
}

func init() { proto.RegisterFile("ps/recommend_product.proto", fileDescriptor_445b6aa590913b14) }

var fileDescriptor_445b6aa590913b14 = []byte{
	// 591 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x56, 0xcd, 0x6e, 0xda, 0x4c,
	0x14, 0x95, 0x21, 0x36, 0xc9, 0xe5, 0xfb, 0xaa, 0x74, 0x0a, 0xc5, 0x01, 0x12, 0x28, 0x2b, 0xa4,
	0x4a, 0x20, 0xa5, 0x6f, 0x90, 0x54, 0x8a, 0xd8, 0x21, 0xe8, 0xaa, 0xaa, 0x64, 0x19, 0xcf, 0x0d,
	0xb2, 0x02, 0x9e, 0xe9, 0xcc, 0x58, 0x6d, 0xf2, 0x2e, 0xdd, 0x75, 0xd3, 0x55, 0xba, 0xea, 0xb2,
	0x9b, 0x6e, 0xfb, 0x50, 0xd5, 0x8c, 0xed, 0x08, 0x6c, 0x47, 0x8d, 0xfa, 0x27, 0x76, 0xcc, 0x99,
	0x99, 0x73, 0xcf, 0xbd, 0xe7, 0x8c, 0x05, 0xb4, 0xb9, 0x1c, 0x0b, 0x0c, 0xd8, 0x7a, 0x8d, 0x11,
	0xf5, 0xb8, 0x60, 0x34, 0x0e, 0xd4, 0x88, 0x0b, 0xa6, 0x18, 0xa9, 0x70, 0xd9, 0x3e, 0x59, 0x32,
	0xb6, 0x5c, 0xe1, 0xd8, 0x20, 0x8b, 0xf8, 0x72, 0xfc, 0x4e, 0xf8, 0x9c, 0xa3, 0x90, 0xc9, 0x99,
	0x76, 0x37, 0xbf, 0x2f, 0x95, 0xb8, 0x63, 0x18, 0x7c, 0xb3, 0xa0, 0x35, 0xcb, 0xd8, 0xa7, 0x09,
	0xf9, 0x0c, 0xdf, 0xc6, 0x28, 0x15, 0x39, 0x06, 0xe0, 0xfe, 0x12, 0xbd, 0x30, 0xa2, 0xf8, 0xde,
	0xb5, 0xfa, 0xd6, 0xd0, 0x9e, 0x1d, 0x68, 0x64, 0xa2, 0x01, 0xf2, 0x0c, 0xfe, 0xbb, 0x0c, 0x23,
	0x3f, 0x0a, 0xd0, 0x0b, 0x18, 0x45, 0xb7, 0xd2, 0xb7, 0x86, 0x07, 0xb3, 0x7a, 0x8a, 0x9d, 0x33,
	0x8a, 0xa4, 0x05, 0xb5, 0x58, 0xa2, 0xf0, 0x42, 0xea, 0x56, 0xcd, 0xae, 0xa3, 0x97, 0x13, 0x4a,
	0x9a, 0xe0, 0x70, 0x54, 0x1a, 0xdf, 0x33, 0xb8, 0xcd, 0x51, 0x4d, 0x28, 0xe9, 0x80, 0xe1, 0xf7,
	0x64, 0x78, 0x83, 0xae, 0x6d, 0x0a, 0xee, 0x6b, 0x60, 0x1e, 0xde, 0x20, 0x39, 0x82, 0x7d, 0x26,
	0x28, 0x0a, 0x6f, 0x71, 0xed, 0x3a, 0xe6, 0x56, 0xcd, 0xac, 0xcf, 0xae, 0x07, 0xb7, 0x16, 0xb8,
	0xc5, 0x2e, 0x24, 0x67, 0x91, 0x44, 0x42, 0x60, 0xcf, 0xe8, 0x4b, 0x1a, 0x30, 0xbf, 0x89, 0x0b,
	0xb5, 0x35, 0x4a, 0xe9, 0x2f, 0x33, 0xd9, 0xd9, 0x92, 0x34, 0xc0, 0x46, 0x21, 0x98, 0x48, 0x05,
	0x27, 0x0b, 0x32, 0x82, 0x1a, 0x45, 0xe5, 0x87, 0x2b, 0xe9, 0xee, 0xf5, 0xab, 0xc3, 0xfa, 0x69,
	0x63, 0xc4, 0xe5, 0xa8, 0x50, 0x32, 0x3b, 0x44, 0x7a, 0x50, 0x57, 0x4c, 0xf9, 0x2b, 0x2f, 0x60,
	0x71, 0xa4, 0xd2, 0x56, 0xc0, 0x40, 0xe7, 0x1a, 0x19, 0x7c, 0xb7, 0xe0, 0x30, 0x7f, 0x9d, 0x3c,
	0x82, 0x4a, 0x48, 0x53, 0x9d, 0x95, 0x90, 0xfe, 0x8d, 0x09, 0x37, 0xc1, 0x91, 0x57, 0xb1, 0x86,
	0x13, 0x4d, 0xb6, 0xbc, 0x8a, 0x27, 0x54, 0x5b, 0x2d, 0x99, 0x50, 0xa9, 0xd5, 0x4e, 0x62, 0xb5,
	0x46, 0x12, 0xab, 0x7b, 0x50, 0x0f, 0x04, 0xfa, 0x0a, 0x3d, 0xea, 0x2b, 0x74, 0x6b, 0x86, 0x11,
	0x12, 0xe8, 0xa5, 0xaf, 0x70, 0xf0, 0xc1, 0x82, 0xc6, 0x5d, 0x3b, 0xaf, 0x42, 0x2e, 0x1f, 0x98,
	0xa1, 0x7f, 0x15, 0x90, 0x4f, 0x16, 0x34, 0x73, 0xfa, 0xfe, 0x60, 0x3a, 0x9e, 0xe7, 0xd3, 0xf1,
	0x78, 0x2b, 0x1d, 0xa6, 0xde, 0xc3, 0xa3, 0x71, 0x6b, 0xc1, 0xff, 0x5b, 0x77, 0x0b, 0xb9, 0xd8,
	0x98, 0x5a, 0xe5, 0x9e, 0xa9, 0x55, 0x37, 0xa7, 0x76, 0x02, 0x75, 0x0d, 0xab, 0x90, 0xcb, 0x6c,
	0xa2, 0xda, 0x05, 0x54, 0x9a, 0xbd, 0xe0, 0xbe, 0xfd, 0x13, 0xf7, 0x9d, 0x82, 0xfb, 0x1f, 0x2d,
	0xe8, 0x6c, 0x29, 0x9e, 0x73, 0x0c, 0x42, 0x7f, 0xb5, 0x63, 0x21, 0xf8, 0x6c, 0x41, 0xb7, 0x5c,
	0xe6, 0xce, 0x66, 0xe1, 0x4b, 0xfe, 0x5d, 0xa5, 0x92, 0x77, 0x3d, 0x12, 0xa7, 0x5f, 0xab, 0x40,
	0xa6, 0xb2, 0xf0, 0x85, 0x9b, 0xc2, 0x93, 0x0b, 0x54, 0x05, 0xb8, 0x53, 0xfa, 0x35, 0x4d, 0xd2,
	0xd3, 0xee, 0x96, 0x6f, 0xa6, 0x9e, 0xcd, 0xe1, 0x69, 0x09, 0xe3, 0xd9, 0x22, 0xf8, 0x1d, 0xd2,
	0x0b, 0x38, 0xdc, 0x24, 0x35, 0x8f, 0xd0, 0x2d, 0xfa, 0x98, 0x72, 0x1d, 0x95, 0xec, 0xa4, 0x44,
	0x6f, 0xa0, 0x95, 0x27, 0xca, 0x1c, 0xec, 0x15, 0x6e, 0x6d, 0xbf, 0x9a, 0x76, 0xff, 0xfe, 0x03,
	0x29, 0xfb, 0x6b, 0x38, 0x2e, 0xef, 0x3d, 0xab, 0xf1, 0xeb, 0x23, 0x58, 0x38, 0xe6, 0xff, 0xc1,
	0x8b, 0x1f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x59, 0x6f, 0xfb, 0x47, 0x7f, 0x08, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PsRecommendProductClient is the client API for PsRecommendProduct service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PsRecommendProductClient interface {
	//获取阿闻到家商品推荐
	GetRecommendProduct(ctx context.Context, in *RecommendProductRequest, opts ...grpc.CallOption) (*RecommendProductResponse, error)
	//获取bbc电商商品推荐
	GetRecommendProductBbc(ctx context.Context, in *RecommendProductRequest, opts ...grpc.CallOption) (*RecommendProductResponse, error)
	//获取宠物贴士推荐
	GetRecommendTips(ctx context.Context, in *RecommendTipsRequest, opts ...grpc.CallOption) (*RecommendTipsResponse, error)
	//获取宠物贴士推荐
	GetRecommendTipsSpecial(ctx context.Context, in *RecommendTipsSpecialRequest, opts ...grpc.CallOption) (*RecommendTipsSpecialResponse, error)
	//获取bbc电商商品推荐（特殊时期）
	GetRecommendProductBbcSpecial(ctx context.Context, in *RecommendProductRequest, opts ...grpc.CallOption) (*RecommendProductResponse, error)
}

type psRecommendProductClient struct {
	cc *grpc.ClientConn
}

func NewPsRecommendProductClient(cc *grpc.ClientConn) PsRecommendProductClient {
	return &psRecommendProductClient{cc}
}

func (c *psRecommendProductClient) GetRecommendProduct(ctx context.Context, in *RecommendProductRequest, opts ...grpc.CallOption) (*RecommendProductResponse, error) {
	out := new(RecommendProductResponse)
	err := c.cc.Invoke(ctx, "/ps.PsRecommendProduct/GetRecommendProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psRecommendProductClient) GetRecommendProductBbc(ctx context.Context, in *RecommendProductRequest, opts ...grpc.CallOption) (*RecommendProductResponse, error) {
	out := new(RecommendProductResponse)
	err := c.cc.Invoke(ctx, "/ps.PsRecommendProduct/GetRecommendProductBbc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psRecommendProductClient) GetRecommendTips(ctx context.Context, in *RecommendTipsRequest, opts ...grpc.CallOption) (*RecommendTipsResponse, error) {
	out := new(RecommendTipsResponse)
	err := c.cc.Invoke(ctx, "/ps.PsRecommendProduct/GetRecommendTips", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psRecommendProductClient) GetRecommendTipsSpecial(ctx context.Context, in *RecommendTipsSpecialRequest, opts ...grpc.CallOption) (*RecommendTipsSpecialResponse, error) {
	out := new(RecommendTipsSpecialResponse)
	err := c.cc.Invoke(ctx, "/ps.PsRecommendProduct/GetRecommendTipsSpecial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psRecommendProductClient) GetRecommendProductBbcSpecial(ctx context.Context, in *RecommendProductRequest, opts ...grpc.CallOption) (*RecommendProductResponse, error) {
	out := new(RecommendProductResponse)
	err := c.cc.Invoke(ctx, "/ps.PsRecommendProduct/GetRecommendProductBbcSpecial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PsRecommendProductServer is the server API for PsRecommendProduct service.
type PsRecommendProductServer interface {
	//获取阿闻到家商品推荐
	GetRecommendProduct(context.Context, *RecommendProductRequest) (*RecommendProductResponse, error)
	//获取bbc电商商品推荐
	GetRecommendProductBbc(context.Context, *RecommendProductRequest) (*RecommendProductResponse, error)
	//获取宠物贴士推荐
	GetRecommendTips(context.Context, *RecommendTipsRequest) (*RecommendTipsResponse, error)
	//获取宠物贴士推荐
	GetRecommendTipsSpecial(context.Context, *RecommendTipsSpecialRequest) (*RecommendTipsSpecialResponse, error)
	//获取bbc电商商品推荐（特殊时期）
	GetRecommendProductBbcSpecial(context.Context, *RecommendProductRequest) (*RecommendProductResponse, error)
}

// UnimplementedPsRecommendProductServer can be embedded to have forward compatible implementations.
type UnimplementedPsRecommendProductServer struct {
}

func (*UnimplementedPsRecommendProductServer) GetRecommendProduct(ctx context.Context, req *RecommendProductRequest) (*RecommendProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendProduct not implemented")
}
func (*UnimplementedPsRecommendProductServer) GetRecommendProductBbc(ctx context.Context, req *RecommendProductRequest) (*RecommendProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendProductBbc not implemented")
}
func (*UnimplementedPsRecommendProductServer) GetRecommendTips(ctx context.Context, req *RecommendTipsRequest) (*RecommendTipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendTips not implemented")
}
func (*UnimplementedPsRecommendProductServer) GetRecommendTipsSpecial(ctx context.Context, req *RecommendTipsSpecialRequest) (*RecommendTipsSpecialResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendTipsSpecial not implemented")
}
func (*UnimplementedPsRecommendProductServer) GetRecommendProductBbcSpecial(ctx context.Context, req *RecommendProductRequest) (*RecommendProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendProductBbcSpecial not implemented")
}

func RegisterPsRecommendProductServer(s *grpc.Server, srv PsRecommendProductServer) {
	s.RegisterService(&_PsRecommendProduct_serviceDesc, srv)
}

func _PsRecommendProduct_GetRecommendProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsRecommendProductServer).GetRecommendProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ps.PsRecommendProduct/GetRecommendProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsRecommendProductServer).GetRecommendProduct(ctx, req.(*RecommendProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsRecommendProduct_GetRecommendProductBbc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsRecommendProductServer).GetRecommendProductBbc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ps.PsRecommendProduct/GetRecommendProductBbc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsRecommendProductServer).GetRecommendProductBbc(ctx, req.(*RecommendProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsRecommendProduct_GetRecommendTips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendTipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsRecommendProductServer).GetRecommendTips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ps.PsRecommendProduct/GetRecommendTips",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsRecommendProductServer).GetRecommendTips(ctx, req.(*RecommendTipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsRecommendProduct_GetRecommendTipsSpecial_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendTipsSpecialRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsRecommendProductServer).GetRecommendTipsSpecial(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ps.PsRecommendProduct/GetRecommendTipsSpecial",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsRecommendProductServer).GetRecommendTipsSpecial(ctx, req.(*RecommendTipsSpecialRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsRecommendProduct_GetRecommendProductBbcSpecial_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsRecommendProductServer).GetRecommendProductBbcSpecial(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ps.PsRecommendProduct/GetRecommendProductBbcSpecial",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsRecommendProductServer).GetRecommendProductBbcSpecial(ctx, req.(*RecommendProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PsRecommendProduct_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ps.PsRecommendProduct",
	HandlerType: (*PsRecommendProductServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecommendProduct",
			Handler:    _PsRecommendProduct_GetRecommendProduct_Handler,
		},
		{
			MethodName: "GetRecommendProductBbc",
			Handler:    _PsRecommendProduct_GetRecommendProductBbc_Handler,
		},
		{
			MethodName: "GetRecommendTips",
			Handler:    _PsRecommendProduct_GetRecommendTips_Handler,
		},
		{
			MethodName: "GetRecommendTipsSpecial",
			Handler:    _PsRecommendProduct_GetRecommendTipsSpecial_Handler,
		},
		{
			MethodName: "GetRecommendProductBbcSpecial",
			Handler:    _PsRecommendProduct_GetRecommendProductBbcSpecial_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ps/recommend_product.proto",
}
