syntax = "proto3";
import "google/protobuf/wrappers.proto";
import "google/protobuf/struct.proto";
package ps;

//商品推荐服务
service PsRecommendProduct{
    //获取阿闻到家商品推荐
    rpc GetRecommendProduct (RecommendProductRequest) returns (RecommendProductResponse);
    //获取bbc电商商品推荐
    rpc GetRecommendProductBbc (RecommendProductRequest) returns (RecommendProductResponse);
    //获取宠物贴士推荐
    rpc GetRecommendTips (RecommendTipsRequest) returns (RecommendTipsResponse);
    //获取宠物贴士推荐
    rpc GetRecommendTipsSpecial (RecommendTipsSpecialRequest) returns (RecommendTipsSpecialResponse);
    //获取bbc电商商品推荐（特殊时期）
    rpc  GetRecommendProductBbcSpecial (RecommendProductRequest) returns (RecommendProductResponse);
}

message RecommendProductRequest{
    //分页索引，0为随机获取指定page_size条数据
    int32 page_index = 1;
    //财务编码
    string finance_code = 2; 
    //用户id
    string user_id = 3;
    //宠物id
    string pet_id = 4;
    //分页大小
    int32 page_size = 5;
    //排序字段
    string order_by = 6;
}

message RecommendProductResponse{
    int32 code = 1;
    string message = 2;
    string error = 3;
    repeated RecommendProduct details = 4;
    //总记录数
    int32 total_count = 5;
}

message RecommendProduct{
    int32 id = 1;
    string finance_code = 2;
    string user_id = 3;
    string pet_id = 4;
    int32 sku_id = 5;
    int32 sort_index = 6;
    string create_date = 7;
}

message RecommendTipsRequest{
    //分页索引
    int32 page_index = 1;
    //用户id
    string user_id = 3;
    //宠物id
    string pet_id = 4;
    //分页大小
    int32 page_size = 5;
    //排序字段
    string order_by = 6;
}

message RecommendTipsResponse{
    int32 code = 1;
    string message = 2;
    string error = 3;
    repeated RecommendTips details = 4;
    //总记录数
    int32 total_count = 5;
}

message RecommendTips{
    int32 id = 1;
    string user_id = 2;
    string pet_id = 3;
    int32 pet_tips_id = 4;
    int32 sort_index = 5;
    string create_date = 6;
}

message RecommendTipsSpecialRequest{
    //分页索引
    int32 page_index = 1;
    //用户id
    string user_id = 3;
    //宠物id
    string pet_id = 4;
    //分页大小
    int32 page_size = 5;
    //排序字段
    string order_by = 6;
}

message RecommendTipsSpecialResponse{
    int32 code = 1;
    string message = 2;
    string error = 3;
    repeated RecommendTips details = 4;
    //总记录数
    int32 total_count = 5;
}

message RecommendTipsSpecial{
    int32 id = 1;
    string user_id = 2;
    string pet_id = 3;
    int32 pet_tips_id = 4;
    int32 sort_index = 5;
    string create_date = 6;
}
