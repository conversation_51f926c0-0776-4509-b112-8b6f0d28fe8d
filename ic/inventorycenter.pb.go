// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ic/inventorycenter.proto

package ic

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type TaskCommonRequest struct {
	// 1 SyncA8VirtualGoodsQtyForQZC; 2 SyncA8VirtualGoodsQtyForQZCVirtual; 3 ToProvideSyncStock; 0 all
	Type                 int32    `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskCommonRequest) Reset()         { *m = TaskCommonRequest{} }
func (m *TaskCommonRequest) String() string { return proto.CompactTextString(m) }
func (*TaskCommonRequest) ProtoMessage()    {}
func (*TaskCommonRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{0}
}

func (m *TaskCommonRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskCommonRequest.Unmarshal(m, b)
}
func (m *TaskCommonRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskCommonRequest.Marshal(b, m, deterministic)
}
func (m *TaskCommonRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskCommonRequest.Merge(m, src)
}
func (m *TaskCommonRequest) XXX_Size() int {
	return xxx_messageInfo_TaskCommonRequest.Size(m)
}
func (m *TaskCommonRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskCommonRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TaskCommonRequest proto.InternalMessageInfo

func (m *TaskCommonRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type TaskCommonResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskCommonResponse) Reset()         { *m = TaskCommonResponse{} }
func (m *TaskCommonResponse) String() string { return proto.CompactTextString(m) }
func (*TaskCommonResponse) ProtoMessage()    {}
func (*TaskCommonResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{1}
}

func (m *TaskCommonResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskCommonResponse.Unmarshal(m, b)
}
func (m *TaskCommonResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskCommonResponse.Marshal(b, m, deterministic)
}
func (m *TaskCommonResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskCommonResponse.Merge(m, src)
}
func (m *TaskCommonResponse) XXX_Size() int {
	return xxx_messageInfo_TaskCommonResponse.Size(m)
}
func (m *TaskCommonResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskCommonResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TaskCommonResponse proto.InternalMessageInfo

func (m *TaskCommonResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TaskCommonResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *TaskCommonResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type ManualStockRecordListRequest struct {
	CreateTime           string   `protobuf:"bytes,1,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	PageIndex            int32    `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualStockRecordListRequest) Reset()         { *m = ManualStockRecordListRequest{} }
func (m *ManualStockRecordListRequest) String() string { return proto.CompactTextString(m) }
func (*ManualStockRecordListRequest) ProtoMessage()    {}
func (*ManualStockRecordListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{2}
}

func (m *ManualStockRecordListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualStockRecordListRequest.Unmarshal(m, b)
}
func (m *ManualStockRecordListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualStockRecordListRequest.Marshal(b, m, deterministic)
}
func (m *ManualStockRecordListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualStockRecordListRequest.Merge(m, src)
}
func (m *ManualStockRecordListRequest) XXX_Size() int {
	return xxx_messageInfo_ManualStockRecordListRequest.Size(m)
}
func (m *ManualStockRecordListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualStockRecordListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ManualStockRecordListRequest proto.InternalMessageInfo

func (m *ManualStockRecordListRequest) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *ManualStockRecordListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ManualStockRecordListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ManualStockRecordListResponse struct {
	Msg                  string               `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	Total                int32                `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	Data                 []*ManualStockRecord `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ManualStockRecordListResponse) Reset()         { *m = ManualStockRecordListResponse{} }
func (m *ManualStockRecordListResponse) String() string { return proto.CompactTextString(m) }
func (*ManualStockRecordListResponse) ProtoMessage()    {}
func (*ManualStockRecordListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{3}
}

func (m *ManualStockRecordListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualStockRecordListResponse.Unmarshal(m, b)
}
func (m *ManualStockRecordListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualStockRecordListResponse.Marshal(b, m, deterministic)
}
func (m *ManualStockRecordListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualStockRecordListResponse.Merge(m, src)
}
func (m *ManualStockRecordListResponse) XXX_Size() int {
	return xxx_messageInfo_ManualStockRecordListResponse.Size(m)
}
func (m *ManualStockRecordListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualStockRecordListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ManualStockRecordListResponse proto.InternalMessageInfo

func (m *ManualStockRecordListResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ManualStockRecordListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ManualStockRecordListResponse) GetData() []*ManualStockRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type ManualStockRecord struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	OperateId            string   `protobuf:"bytes,2,opt,name=operate_id,json=operateId,proto3" json:"operate_id"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator"`
	FileUrl              string   `protobuf:"bytes,4,opt,name=file_url,json=fileUrl,proto3" json:"file_url"`
	CreateTime           string   `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualStockRecord) Reset()         { *m = ManualStockRecord{} }
func (m *ManualStockRecord) String() string { return proto.CompactTextString(m) }
func (*ManualStockRecord) ProtoMessage()    {}
func (*ManualStockRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{4}
}

func (m *ManualStockRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualStockRecord.Unmarshal(m, b)
}
func (m *ManualStockRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualStockRecord.Marshal(b, m, deterministic)
}
func (m *ManualStockRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualStockRecord.Merge(m, src)
}
func (m *ManualStockRecord) XXX_Size() int {
	return xxx_messageInfo_ManualStockRecord.Size(m)
}
func (m *ManualStockRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualStockRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ManualStockRecord proto.InternalMessageInfo

func (m *ManualStockRecord) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ManualStockRecord) GetOperateId() string {
	if m != nil {
		return m.OperateId
	}
	return ""
}

func (m *ManualStockRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ManualStockRecord) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *ManualStockRecord) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

// 手动 单仓拉库存 请求参数
type RunStockByManualRequest struct {
	// 仓库编号
	WarehouseCode string `protobuf:"bytes,1,opt,name=warehouse_code,json=warehouseCode,proto3" json:"warehouse_code"`
	// a8货号 ，多个用英文逗号隔开
	GoodsCode            string   `protobuf:"bytes,2,opt,name=goods_code,json=goodsCode,proto3" json:"goods_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RunStockByManualRequest) Reset()         { *m = RunStockByManualRequest{} }
func (m *RunStockByManualRequest) String() string { return proto.CompactTextString(m) }
func (*RunStockByManualRequest) ProtoMessage()    {}
func (*RunStockByManualRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{5}
}

func (m *RunStockByManualRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RunStockByManualRequest.Unmarshal(m, b)
}
func (m *RunStockByManualRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RunStockByManualRequest.Marshal(b, m, deterministic)
}
func (m *RunStockByManualRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunStockByManualRequest.Merge(m, src)
}
func (m *RunStockByManualRequest) XXX_Size() int {
	return xxx_messageInfo_RunStockByManualRequest.Size(m)
}
func (m *RunStockByManualRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RunStockByManualRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RunStockByManualRequest proto.InternalMessageInfo

func (m *RunStockByManualRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *RunStockByManualRequest) GetGoodsCode() string {
	if m != nil {
		return m.GoodsCode
	}
	return ""
}

type OmsSyncSotckRequest struct {
	//商品集合
	Data                 []*OmsSyncSotck `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *OmsSyncSotckRequest) Reset()         { *m = OmsSyncSotckRequest{} }
func (m *OmsSyncSotckRequest) String() string { return proto.CompactTextString(m) }
func (*OmsSyncSotckRequest) ProtoMessage()    {}
func (*OmsSyncSotckRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{6}
}

func (m *OmsSyncSotckRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OmsSyncSotckRequest.Unmarshal(m, b)
}
func (m *OmsSyncSotckRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OmsSyncSotckRequest.Marshal(b, m, deterministic)
}
func (m *OmsSyncSotckRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OmsSyncSotckRequest.Merge(m, src)
}
func (m *OmsSyncSotckRequest) XXX_Size() int {
	return xxx_messageInfo_OmsSyncSotckRequest.Size(m)
}
func (m *OmsSyncSotckRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OmsSyncSotckRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OmsSyncSotckRequest proto.InternalMessageInfo

func (m *OmsSyncSotckRequest) GetData() []*OmsSyncSotck {
	if m != nil {
		return m.Data
	}
	return nil
}

type OmsSyncSotck struct {
	//仓库代码
	WarehouseCode string `protobuf:"bytes,1,opt,name=warehouse_code,json=warehouseCode,proto3" json:"warehouse_code"`
	//商品集合
	Goodslist            []*OmsGoodsList `protobuf:"bytes,2,rep,name=goodslist,proto3" json:"goodslist"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *OmsSyncSotck) Reset()         { *m = OmsSyncSotck{} }
func (m *OmsSyncSotck) String() string { return proto.CompactTextString(m) }
func (*OmsSyncSotck) ProtoMessage()    {}
func (*OmsSyncSotck) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{7}
}

func (m *OmsSyncSotck) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OmsSyncSotck.Unmarshal(m, b)
}
func (m *OmsSyncSotck) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OmsSyncSotck.Marshal(b, m, deterministic)
}
func (m *OmsSyncSotck) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OmsSyncSotck.Merge(m, src)
}
func (m *OmsSyncSotck) XXX_Size() int {
	return xxx_messageInfo_OmsSyncSotck.Size(m)
}
func (m *OmsSyncSotck) XXX_DiscardUnknown() {
	xxx_messageInfo_OmsSyncSotck.DiscardUnknown(m)
}

var xxx_messageInfo_OmsSyncSotck proto.InternalMessageInfo

func (m *OmsSyncSotck) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *OmsSyncSotck) GetGoodslist() []*OmsGoodsList {
	if m != nil {
		return m.Goodslist
	}
	return nil
}

type OmsSyncSotckResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OmsSyncSotckResponse) Reset()         { *m = OmsSyncSotckResponse{} }
func (m *OmsSyncSotckResponse) String() string { return proto.CompactTextString(m) }
func (*OmsSyncSotckResponse) ProtoMessage()    {}
func (*OmsSyncSotckResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{8}
}

func (m *OmsSyncSotckResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OmsSyncSotckResponse.Unmarshal(m, b)
}
func (m *OmsSyncSotckResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OmsSyncSotckResponse.Marshal(b, m, deterministic)
}
func (m *OmsSyncSotckResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OmsSyncSotckResponse.Merge(m, src)
}
func (m *OmsSyncSotckResponse) XXX_Size() int {
	return xxx_messageInfo_OmsSyncSotckResponse.Size(m)
}
func (m *OmsSyncSotckResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OmsSyncSotckResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OmsSyncSotckResponse proto.InternalMessageInfo

func (m *OmsSyncSotckResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *OmsSyncSotckResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type OmsGoodsList struct {
	//商品spu码
	Spu string `protobuf:"bytes,1,opt,name=spu,proto3" json:"spu"`
	//商品sku码
	Sku string `protobuf:"bytes,2,opt,name=sku,proto3" json:"sku"`
	//可用库存
	Stock int32 `protobuf:"varint,3,opt,name=stock,proto3" json:"stock"`
	//仓库总库存
	Allstock int32 `protobuf:"varint,4,opt,name=allstock,proto3" json:"allstock"`
	//货号
	Thirdsku             string   `protobuf:"bytes,5,opt,name=thirdsku,proto3" json:"thirdsku"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OmsGoodsList) Reset()         { *m = OmsGoodsList{} }
func (m *OmsGoodsList) String() string { return proto.CompactTextString(m) }
func (*OmsGoodsList) ProtoMessage()    {}
func (*OmsGoodsList) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{9}
}

func (m *OmsGoodsList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OmsGoodsList.Unmarshal(m, b)
}
func (m *OmsGoodsList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OmsGoodsList.Marshal(b, m, deterministic)
}
func (m *OmsGoodsList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OmsGoodsList.Merge(m, src)
}
func (m *OmsGoodsList) XXX_Size() int {
	return xxx_messageInfo_OmsGoodsList.Size(m)
}
func (m *OmsGoodsList) XXX_DiscardUnknown() {
	xxx_messageInfo_OmsGoodsList.DiscardUnknown(m)
}

var xxx_messageInfo_OmsGoodsList proto.InternalMessageInfo

func (m *OmsGoodsList) GetSpu() string {
	if m != nil {
		return m.Spu
	}
	return ""
}

func (m *OmsGoodsList) GetSku() string {
	if m != nil {
		return m.Sku
	}
	return ""
}

func (m *OmsGoodsList) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *OmsGoodsList) GetAllstock() int32 {
	if m != nil {
		return m.Allstock
	}
	return 0
}

func (m *OmsGoodsList) GetThirdsku() string {
	if m != nil {
		return m.Thirdsku
	}
	return ""
}

type AddStockTaskRequest struct {
	Request              string   `protobuf:"bytes,1,opt,name=request,proto3" json:"request"`
	SerialNumber         string   `protobuf:"bytes,2,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number"`
	StockType            int32    `protobuf:"varint,3,opt,name=stock_type,json=stockType,proto3" json:"stock_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddStockTaskRequest) Reset()         { *m = AddStockTaskRequest{} }
func (m *AddStockTaskRequest) String() string { return proto.CompactTextString(m) }
func (*AddStockTaskRequest) ProtoMessage()    {}
func (*AddStockTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{10}
}

func (m *AddStockTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddStockTaskRequest.Unmarshal(m, b)
}
func (m *AddStockTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddStockTaskRequest.Marshal(b, m, deterministic)
}
func (m *AddStockTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddStockTaskRequest.Merge(m, src)
}
func (m *AddStockTaskRequest) XXX_Size() int {
	return xxx_messageInfo_AddStockTaskRequest.Size(m)
}
func (m *AddStockTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddStockTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddStockTaskRequest proto.InternalMessageInfo

func (m *AddStockTaskRequest) GetRequest() string {
	if m != nil {
		return m.Request
	}
	return ""
}

func (m *AddStockTaskRequest) GetSerialNumber() string {
	if m != nil {
		return m.SerialNumber
	}
	return ""
}

func (m *AddStockTaskRequest) GetStockType() int32 {
	if m != nil {
		return m.StockType
	}
	return 0
}

type AddStockTaskResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddStockTaskResponse) Reset()         { *m = AddStockTaskResponse{} }
func (m *AddStockTaskResponse) String() string { return proto.CompactTextString(m) }
func (*AddStockTaskResponse) ProtoMessage()    {}
func (*AddStockTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{11}
}

func (m *AddStockTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddStockTaskResponse.Unmarshal(m, b)
}
func (m *AddStockTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddStockTaskResponse.Marshal(b, m, deterministic)
}
func (m *AddStockTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddStockTaskResponse.Merge(m, src)
}
func (m *AddStockTaskResponse) XXX_Size() int {
	return xxx_messageInfo_AddStockTaskResponse.Size(m)
}
func (m *AddStockTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddStockTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddStockTaskResponse proto.InternalMessageInfo

func (m *AddStockTaskResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AddStockTaskResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AddStockTaskResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//请求参数
type QueryStockTaskRequest struct {
	SerialNumber         string   `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryStockTaskRequest) Reset()         { *m = QueryStockTaskRequest{} }
func (m *QueryStockTaskRequest) String() string { return proto.CompactTextString(m) }
func (*QueryStockTaskRequest) ProtoMessage()    {}
func (*QueryStockTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{12}
}

func (m *QueryStockTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryStockTaskRequest.Unmarshal(m, b)
}
func (m *QueryStockTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryStockTaskRequest.Marshal(b, m, deterministic)
}
func (m *QueryStockTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryStockTaskRequest.Merge(m, src)
}
func (m *QueryStockTaskRequest) XXX_Size() int {
	return xxx_messageInfo_QueryStockTaskRequest.Size(m)
}
func (m *QueryStockTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryStockTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryStockTaskRequest proto.InternalMessageInfo

func (m *QueryStockTaskRequest) GetSerialNumber() string {
	if m != nil {
		return m.SerialNumber
	}
	return ""
}

//响应结果
type QueryStockTaskResponse struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Request              string   `protobuf:"bytes,2,opt,name=request,proto3" json:"request"`
	SerialNumber         string   `protobuf:"bytes,3,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number"`
	Status               int32    `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	CreateDate           string   `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryStockTaskResponse) Reset()         { *m = QueryStockTaskResponse{} }
func (m *QueryStockTaskResponse) String() string { return proto.CompactTextString(m) }
func (*QueryStockTaskResponse) ProtoMessage()    {}
func (*QueryStockTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{13}
}

func (m *QueryStockTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryStockTaskResponse.Unmarshal(m, b)
}
func (m *QueryStockTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryStockTaskResponse.Marshal(b, m, deterministic)
}
func (m *QueryStockTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryStockTaskResponse.Merge(m, src)
}
func (m *QueryStockTaskResponse) XXX_Size() int {
	return xxx_messageInfo_QueryStockTaskResponse.Size(m)
}
func (m *QueryStockTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryStockTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryStockTaskResponse proto.InternalMessageInfo

func (m *QueryStockTaskResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *QueryStockTaskResponse) GetRequest() string {
	if m != nil {
		return m.Request
	}
	return ""
}

func (m *QueryStockTaskResponse) GetSerialNumber() string {
	if m != nil {
		return m.SerialNumber
	}
	return ""
}

func (m *QueryStockTaskResponse) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *QueryStockTaskResponse) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

type SkuCodeInfo struct {
	//财务编码
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	Sku         string `protobuf:"bytes,2,opt,name=sku,proto3" json:"sku"`
	Spu         string `protobuf:"bytes,3,opt,name=spu,proto3" json:"spu"`
	//子龙货号
	ThirdSkuid string `protobuf:"bytes,4,opt,name=third_skuid,json=thirdSkuid,proto3" json:"third_skuid"`
	//子龙门店id
	ZlId  int32 `protobuf:"varint,5,opt,name=zl_id,json=zlId,proto3" json:"zl_id"`
	ErpId int32 `protobuf:"varint,6,opt,name=erp_id,json=erpId,proto3" json:"erp_id"`
	//指定仓库查询，可以多个仓库，前置仓和前置仓虚拟仓
	StockWarehouse       []int32  `protobuf:"varint,7,rep,packed,name=stock_warehouse,json=stockWarehouse,proto3" json:"stock_warehouse"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuCodeInfo) Reset()         { *m = SkuCodeInfo{} }
func (m *SkuCodeInfo) String() string { return proto.CompactTextString(m) }
func (*SkuCodeInfo) ProtoMessage()    {}
func (*SkuCodeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{14}
}

func (m *SkuCodeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuCodeInfo.Unmarshal(m, b)
}
func (m *SkuCodeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuCodeInfo.Marshal(b, m, deterministic)
}
func (m *SkuCodeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuCodeInfo.Merge(m, src)
}
func (m *SkuCodeInfo) XXX_Size() int {
	return xxx_messageInfo_SkuCodeInfo.Size(m)
}
func (m *SkuCodeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuCodeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SkuCodeInfo proto.InternalMessageInfo

func (m *SkuCodeInfo) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *SkuCodeInfo) GetSku() string {
	if m != nil {
		return m.Sku
	}
	return ""
}

func (m *SkuCodeInfo) GetSpu() string {
	if m != nil {
		return m.Spu
	}
	return ""
}

func (m *SkuCodeInfo) GetThirdSkuid() string {
	if m != nil {
		return m.ThirdSkuid
	}
	return ""
}

func (m *SkuCodeInfo) GetZlId() int32 {
	if m != nil {
		return m.ZlId
	}
	return 0
}

func (m *SkuCodeInfo) GetErpId() int32 {
	if m != nil {
		return m.ErpId
	}
	return 0
}

func (m *SkuCodeInfo) GetStockWarehouse() []int32 {
	if m != nil {
		return m.StockWarehouse
	}
	return nil
}

//提供给子龙系统实时推送库存接口--请求参数
type ProvideSyncStockRequest struct {
	GoodsStock []*GoodsStockList `protobuf:"bytes,1,rep,name=GoodsStock,proto3" json:"GoodsStock"`
	//符合条件的仓库ID集合
	PassWarehouse        []*Warehouse `protobuf:"bytes,2,rep,name=PassWarehouse,proto3" json:"PassWarehouse"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ProvideSyncStockRequest) Reset()         { *m = ProvideSyncStockRequest{} }
func (m *ProvideSyncStockRequest) String() string { return proto.CompactTextString(m) }
func (*ProvideSyncStockRequest) ProtoMessage()    {}
func (*ProvideSyncStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{15}
}

func (m *ProvideSyncStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProvideSyncStockRequest.Unmarshal(m, b)
}
func (m *ProvideSyncStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProvideSyncStockRequest.Marshal(b, m, deterministic)
}
func (m *ProvideSyncStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProvideSyncStockRequest.Merge(m, src)
}
func (m *ProvideSyncStockRequest) XXX_Size() int {
	return xxx_messageInfo_ProvideSyncStockRequest.Size(m)
}
func (m *ProvideSyncStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ProvideSyncStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ProvideSyncStockRequest proto.InternalMessageInfo

func (m *ProvideSyncStockRequest) GetGoodsStock() []*GoodsStockList {
	if m != nil {
		return m.GoodsStock
	}
	return nil
}

func (m *ProvideSyncStockRequest) GetPassWarehouse() []*Warehouse {
	if m != nil {
		return m.PassWarehouse
	}
	return nil
}

type Warehouse struct {
	Id                   int32    `protobuf:"varint,1,opt,name=Id,proto3" json:"Id"`
	Code                 string   `protobuf:"bytes,2,opt,name=Code,proto3" json:"Code"`
	Category             int32    `protobuf:"varint,3,opt,name=Category,proto3" json:"Category"`
	Status               int32    `protobuf:"varint,4,opt,name=Status,proto3" json:"Status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Warehouse) Reset()         { *m = Warehouse{} }
func (m *Warehouse) String() string { return proto.CompactTextString(m) }
func (*Warehouse) ProtoMessage()    {}
func (*Warehouse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{16}
}

func (m *Warehouse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Warehouse.Unmarshal(m, b)
}
func (m *Warehouse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Warehouse.Marshal(b, m, deterministic)
}
func (m *Warehouse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Warehouse.Merge(m, src)
}
func (m *Warehouse) XXX_Size() int {
	return xxx_messageInfo_Warehouse.Size(m)
}
func (m *Warehouse) XXX_DiscardUnknown() {
	xxx_messageInfo_Warehouse.DiscardUnknown(m)
}

var xxx_messageInfo_Warehouse proto.InternalMessageInfo

func (m *Warehouse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Warehouse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *Warehouse) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *Warehouse) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GoodsStockList struct {
	//子龙门店id，systemid
	ZilongId string `protobuf:"bytes,1,opt,name=zilong_id,json=zilongId,proto3" json:"zilong_id"`
	//商品信息
	GoodsList            []*GoodsStock `protobuf:"bytes,2,rep,name=GoodsList,proto3" json:"GoodsList"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GoodsStockList) Reset()         { *m = GoodsStockList{} }
func (m *GoodsStockList) String() string { return proto.CompactTextString(m) }
func (*GoodsStockList) ProtoMessage()    {}
func (*GoodsStockList) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{17}
}

func (m *GoodsStockList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsStockList.Unmarshal(m, b)
}
func (m *GoodsStockList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsStockList.Marshal(b, m, deterministic)
}
func (m *GoodsStockList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsStockList.Merge(m, src)
}
func (m *GoodsStockList) XXX_Size() int {
	return xxx_messageInfo_GoodsStockList.Size(m)
}
func (m *GoodsStockList) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsStockList.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsStockList proto.InternalMessageInfo

func (m *GoodsStockList) GetZilongId() string {
	if m != nil {
		return m.ZilongId
	}
	return ""
}

func (m *GoodsStockList) GetGoodsList() []*GoodsStock {
	if m != nil {
		return m.GoodsList
	}
	return nil
}

type GoodsStock struct {
	//商品id
	GoodsId string `protobuf:"bytes,2,opt,name=GoodsId,proto3" json:"GoodsId"`
	//库存数量
	Stock                int32    `protobuf:"varint,3,opt,name=Stock,proto3" json:"Stock"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsStock) Reset()         { *m = GoodsStock{} }
func (m *GoodsStock) String() string { return proto.CompactTextString(m) }
func (*GoodsStock) ProtoMessage()    {}
func (*GoodsStock) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{18}
}

func (m *GoodsStock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsStock.Unmarshal(m, b)
}
func (m *GoodsStock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsStock.Marshal(b, m, deterministic)
}
func (m *GoodsStock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsStock.Merge(m, src)
}
func (m *GoodsStock) XXX_Size() int {
	return xxx_messageInfo_GoodsStock.Size(m)
}
func (m *GoodsStock) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsStock.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsStock proto.InternalMessageInfo

func (m *GoodsStock) GetGoodsId() string {
	if m != nil {
		return m.GoodsId
	}
	return ""
}

func (m *GoodsStock) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

//提供给子龙系统实时推送库存接口--响应参数
type ProvideSyncStockResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProvideSyncStockResponse) Reset()         { *m = ProvideSyncStockResponse{} }
func (m *ProvideSyncStockResponse) String() string { return proto.CompactTextString(m) }
func (*ProvideSyncStockResponse) ProtoMessage()    {}
func (*ProvideSyncStockResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{19}
}

func (m *ProvideSyncStockResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProvideSyncStockResponse.Unmarshal(m, b)
}
func (m *ProvideSyncStockResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProvideSyncStockResponse.Marshal(b, m, deterministic)
}
func (m *ProvideSyncStockResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProvideSyncStockResponse.Merge(m, src)
}
func (m *ProvideSyncStockResponse) XXX_Size() int {
	return xxx_messageInfo_ProvideSyncStockResponse.Size(m)
}
func (m *ProvideSyncStockResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ProvideSyncStockResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ProvideSyncStockResponse proto.InternalMessageInfo

func (m *ProvideSyncStockResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ProvideSyncStockResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ProvideSyncStockResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type ThirdInfoRequest struct {
	//仓库id
	WarehouseId          int32        `protobuf:"varint,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	Details              []*ThirdInfo `protobuf:"bytes,2,rep,name=details,proto3" json:"details"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ThirdInfoRequest) Reset()         { *m = ThirdInfoRequest{} }
func (m *ThirdInfoRequest) String() string { return proto.CompactTextString(m) }
func (*ThirdInfoRequest) ProtoMessage()    {}
func (*ThirdInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{20}
}

func (m *ThirdInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThirdInfoRequest.Unmarshal(m, b)
}
func (m *ThirdInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThirdInfoRequest.Marshal(b, m, deterministic)
}
func (m *ThirdInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThirdInfoRequest.Merge(m, src)
}
func (m *ThirdInfoRequest) XXX_Size() int {
	return xxx_messageInfo_ThirdInfoRequest.Size(m)
}
func (m *ThirdInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ThirdInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ThirdInfoRequest proto.InternalMessageInfo

func (m *ThirdInfoRequest) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *ThirdInfoRequest) GetDetails() []*ThirdInfo {
	if m != nil {
		return m.Details
	}
	return nil
}

type ThirdInfoResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ThirdInfoResponse) Reset()         { *m = ThirdInfoResponse{} }
func (m *ThirdInfoResponse) String() string { return proto.CompactTextString(m) }
func (*ThirdInfoResponse) ProtoMessage()    {}
func (*ThirdInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{21}
}

func (m *ThirdInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThirdInfoResponse.Unmarshal(m, b)
}
func (m *ThirdInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThirdInfoResponse.Marshal(b, m, deterministic)
}
func (m *ThirdInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThirdInfoResponse.Merge(m, src)
}
func (m *ThirdInfoResponse) XXX_Size() int {
	return xxx_messageInfo_ThirdInfoResponse.Size(m)
}
func (m *ThirdInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ThirdInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ThirdInfoResponse proto.InternalMessageInfo

func (m *ThirdInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ThirdInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ThirdInfoResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type ThirdInfo struct {
	//第三方spu/sku id
	ThirdSpuSkuId string `protobuf:"bytes,1,opt,name=third_spu_sku_id,json=thirdSpuSkuId,proto3" json:"third_spu_sku_id"`
	//库存
	Stock                int32    `protobuf:"varint,2,opt,name=stock,proto3" json:"stock"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ThirdInfo) Reset()         { *m = ThirdInfo{} }
func (m *ThirdInfo) String() string { return proto.CompactTextString(m) }
func (*ThirdInfo) ProtoMessage()    {}
func (*ThirdInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{22}
}

func (m *ThirdInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThirdInfo.Unmarshal(m, b)
}
func (m *ThirdInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThirdInfo.Marshal(b, m, deterministic)
}
func (m *ThirdInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThirdInfo.Merge(m, src)
}
func (m *ThirdInfo) XXX_Size() int {
	return xxx_messageInfo_ThirdInfo.Size(m)
}
func (m *ThirdInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ThirdInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ThirdInfo proto.InternalMessageInfo

func (m *ThirdInfo) GetThirdSpuSkuId() string {
	if m != nil {
		return m.ThirdSpuSkuId
	}
	return ""
}

func (m *ThirdInfo) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

//商品仓库库存信息
type GoodsList struct {
	//商品id
	Goodsid string `protobuf:"bytes,1,opt,name=goodsid,proto3" json:"goodsid"`
	//商品库存
	Stock int32 `protobuf:"varint,2,opt,name=stock,proto3" json:"stock"`
	//商品库存
	Newstock int32 `protobuf:"varint,3,opt,name=newstock,proto3" json:"newstock"`
	//是否足够
	Enough bool `protobuf:"varint,4,opt,name=enough,proto3" json:"enough"`
	//库存来源
	GoodsSkuType         int32    `protobuf:"varint,5,opt,name=goods_sku_type,json=goodsSkuType,proto3" json:"goods_sku_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsList) Reset()         { *m = GoodsList{} }
func (m *GoodsList) String() string { return proto.CompactTextString(m) }
func (*GoodsList) ProtoMessage()    {}
func (*GoodsList) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{23}
}

func (m *GoodsList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsList.Unmarshal(m, b)
}
func (m *GoodsList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsList.Marshal(b, m, deterministic)
}
func (m *GoodsList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsList.Merge(m, src)
}
func (m *GoodsList) XXX_Size() int {
	return xxx_messageInfo_GoodsList.Size(m)
}
func (m *GoodsList) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsList.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsList proto.InternalMessageInfo

func (m *GoodsList) GetGoodsid() string {
	if m != nil {
		return m.Goodsid
	}
	return ""
}

func (m *GoodsList) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *GoodsList) GetNewstock() int32 {
	if m != nil {
		return m.Newstock
	}
	return 0
}

func (m *GoodsList) GetEnough() bool {
	if m != nil {
		return m.Enough
	}
	return false
}

func (m *GoodsList) GetGoodsSkuType() int32 {
	if m != nil {
		return m.GoodsSkuType
	}
	return 0
}

//冻结库存请求参数
type FreezeRequest struct {
	//来源：1电商，2本地生活 3：互联网医疗
	Source int32 `protobuf:"varint,1,opt,name=source,proto3" json:"source"`
	//用户id
	MemberId string `protobuf:"bytes,2,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	//订单id
	OrderId string `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//商品id
	GoodsList            []*OrderGoodsInfo `protobuf:"bytes,4,rep,name=goods_list,json=goodsList,proto3" json:"goods_list"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *FreezeRequest) Reset()         { *m = FreezeRequest{} }
func (m *FreezeRequest) String() string { return proto.CompactTextString(m) }
func (*FreezeRequest) ProtoMessage()    {}
func (*FreezeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{24}
}

func (m *FreezeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeRequest.Unmarshal(m, b)
}
func (m *FreezeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeRequest.Marshal(b, m, deterministic)
}
func (m *FreezeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeRequest.Merge(m, src)
}
func (m *FreezeRequest) XXX_Size() int {
	return xxx_messageInfo_FreezeRequest.Size(m)
}
func (m *FreezeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeRequest proto.InternalMessageInfo

func (m *FreezeRequest) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *FreezeRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *FreezeRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FreezeRequest) GetGoodsList() []*OrderGoodsInfo {
	if m != nil {
		return m.GoodsList
	}
	return nil
}

//订单商品信息
type OrderGoodsInfo struct {
	//商品id
	GoodsId string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	//扣减库存数量
	Number int32 `protobuf:"varint,2,opt,name=number,proto3" json:"number"`
	//仓库id
	WarehouseId int32 `protobuf:"varint,3,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	//药品仓类型：0:默认否, 1:巨星药品仓
	WarehouseType        int32    `protobuf:"varint,4,opt,name=warehouse_type,json=warehouseType,proto3" json:"warehouse_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderGoodsInfo) Reset()         { *m = OrderGoodsInfo{} }
func (m *OrderGoodsInfo) String() string { return proto.CompactTextString(m) }
func (*OrderGoodsInfo) ProtoMessage()    {}
func (*OrderGoodsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{25}
}

func (m *OrderGoodsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderGoodsInfo.Unmarshal(m, b)
}
func (m *OrderGoodsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderGoodsInfo.Marshal(b, m, deterministic)
}
func (m *OrderGoodsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderGoodsInfo.Merge(m, src)
}
func (m *OrderGoodsInfo) XXX_Size() int {
	return xxx_messageInfo_OrderGoodsInfo.Size(m)
}
func (m *OrderGoodsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderGoodsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderGoodsInfo proto.InternalMessageInfo

func (m *OrderGoodsInfo) GetGoodsId() string {
	if m != nil {
		return m.GoodsId
	}
	return ""
}

func (m *OrderGoodsInfo) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *OrderGoodsInfo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *OrderGoodsInfo) GetWarehouseType() int32 {
	if m != nil {
		return m.WarehouseType
	}
	return 0
}

//冻结库存响应参数
type FreezeResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeResponse) Reset()         { *m = FreezeResponse{} }
func (m *FreezeResponse) String() string { return proto.CompactTextString(m) }
func (*FreezeResponse) ProtoMessage()    {}
func (*FreezeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{26}
}

func (m *FreezeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeResponse.Unmarshal(m, b)
}
func (m *FreezeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeResponse.Marshal(b, m, deterministic)
}
func (m *FreezeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeResponse.Merge(m, src)
}
func (m *FreezeResponse) XXX_Size() int {
	return xxx_messageInfo_FreezeResponse.Size(m)
}
func (m *FreezeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeResponse proto.InternalMessageInfo

func (m *FreezeResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *FreezeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FreezeResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//释放库存请求参数
type FreedStockRequest struct {
	//来源：1电商，2本地生活 3：互联网医疗
	Source int32 `protobuf:"varint,1,opt,name=source,proto3" json:"source"`
	//订单id
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//商品信息
	GoodsList []*OrderGoodsInfo `protobuf:"bytes,3,rep,name=goods_list,json=goodsList,proto3" json:"goods_list"`
	// repeated GoodsInfo goods_list=2;
	//订单状态 0：未支付 1：已支付
	Status               int32    `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreedStockRequest) Reset()         { *m = FreedStockRequest{} }
func (m *FreedStockRequest) String() string { return proto.CompactTextString(m) }
func (*FreedStockRequest) ProtoMessage()    {}
func (*FreedStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{27}
}

func (m *FreedStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreedStockRequest.Unmarshal(m, b)
}
func (m *FreedStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreedStockRequest.Marshal(b, m, deterministic)
}
func (m *FreedStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreedStockRequest.Merge(m, src)
}
func (m *FreedStockRequest) XXX_Size() int {
	return xxx_messageInfo_FreedStockRequest.Size(m)
}
func (m *FreedStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FreedStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FreedStockRequest proto.InternalMessageInfo

func (m *FreedStockRequest) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *FreedStockRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FreedStockRequest) GetGoodsList() []*OrderGoodsInfo {
	if m != nil {
		return m.GoodsList
	}
	return nil
}

func (m *FreedStockRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GoodsInfo struct {
	//商品id
	GoodsId string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	//扣减库存数量
	Number               int32    `protobuf:"varint,2,opt,name=number,proto3" json:"number"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoodsInfo) Reset()         { *m = GoodsInfo{} }
func (m *GoodsInfo) String() string { return proto.CompactTextString(m) }
func (*GoodsInfo) ProtoMessage()    {}
func (*GoodsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{28}
}

func (m *GoodsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoodsInfo.Unmarshal(m, b)
}
func (m *GoodsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoodsInfo.Marshal(b, m, deterministic)
}
func (m *GoodsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoodsInfo.Merge(m, src)
}
func (m *GoodsInfo) XXX_Size() int {
	return xxx_messageInfo_GoodsInfo.Size(m)
}
func (m *GoodsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GoodsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GoodsInfo proto.InternalMessageInfo

func (m *GoodsInfo) GetGoodsId() string {
	if m != nil {
		return m.GoodsId
	}
	return ""
}

func (m *GoodsInfo) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

//释放库存响应参数
type FreedStockResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreedStockResponse) Reset()         { *m = FreedStockResponse{} }
func (m *FreedStockResponse) String() string { return proto.CompactTextString(m) }
func (*FreedStockResponse) ProtoMessage()    {}
func (*FreedStockResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{29}
}

func (m *FreedStockResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreedStockResponse.Unmarshal(m, b)
}
func (m *FreedStockResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreedStockResponse.Marshal(b, m, deterministic)
}
func (m *FreedStockResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreedStockResponse.Merge(m, src)
}
func (m *FreedStockResponse) XXX_Size() int {
	return xxx_messageInfo_FreedStockResponse.Size(m)
}
func (m *FreedStockResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FreedStockResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FreedStockResponse proto.InternalMessageInfo

func (m *FreedStockResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *FreedStockResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FreedStockResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//商品和库存信息
type ProductsAndStock struct {
	//仓库id
	WarehouseId int32 `protobuf:"varint,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	//商品id
	ProductId            string   `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductsAndStock) Reset()         { *m = ProductsAndStock{} }
func (m *ProductsAndStock) String() string { return proto.CompactTextString(m) }
func (*ProductsAndStock) ProtoMessage()    {}
func (*ProductsAndStock) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{30}
}

func (m *ProductsAndStock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductsAndStock.Unmarshal(m, b)
}
func (m *ProductsAndStock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductsAndStock.Marshal(b, m, deterministic)
}
func (m *ProductsAndStock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductsAndStock.Merge(m, src)
}
func (m *ProductsAndStock) XXX_Size() int {
	return xxx_messageInfo_ProductsAndStock.Size(m)
}
func (m *ProductsAndStock) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductsAndStock.DiscardUnknown(m)
}

var xxx_messageInfo_ProductsAndStock proto.InternalMessageInfo

func (m *ProductsAndStock) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *ProductsAndStock) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

type GetStockInfoRequest struct {
	//来源：1电商，2本地生活 3：互联网医疗
	Source int32 `protobuf:"varint,1,opt,name=source,proto3" json:"source"`
	//是否需要拉取子龙库存，只针对子龙使用
	IsNeedPull   int32           `protobuf:"varint,2,opt,name=is_need_pull,json=isNeedPull,proto3" json:"is_need_pull"`
	ProductsInfo []*ProductsInfo `protobuf:"bytes,3,rep,name=ProductsInfo,proto3" json:"ProductsInfo"`
	//指定仓库查询，可以多个仓库，前置仓和前置仓虚拟仓
	Stockwarehouse       []int32  `protobuf:"varint,4,rep,packed,name=stockwarehouse,proto3" json:"stockwarehouse"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStockInfoRequest) Reset()         { *m = GetStockInfoRequest{} }
func (m *GetStockInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetStockInfoRequest) ProtoMessage()    {}
func (*GetStockInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{31}
}

func (m *GetStockInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStockInfoRequest.Unmarshal(m, b)
}
func (m *GetStockInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStockInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetStockInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStockInfoRequest.Merge(m, src)
}
func (m *GetStockInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetStockInfoRequest.Size(m)
}
func (m *GetStockInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStockInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetStockInfoRequest proto.InternalMessageInfo

func (m *GetStockInfoRequest) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *GetStockInfoRequest) GetIsNeedPull() int32 {
	if m != nil {
		return m.IsNeedPull
	}
	return 0
}

func (m *GetStockInfoRequest) GetProductsInfo() []*ProductsInfo {
	if m != nil {
		return m.ProductsInfo
	}
	return nil
}

func (m *GetStockInfoRequest) GetStockwarehouse() []int32 {
	if m != nil {
		return m.Stockwarehouse
	}
	return nil
}

type GetStockInfoResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//返回信息
	GoodsInfo *GetStockInfoRequest `protobuf:"bytes,4,opt,name=goods_info,json=goodsInfo,proto3" json:"goods_info"`
	//按仓库返回库存信息
	GoodsInWarehouse     []*ProductsInWarehouse `protobuf:"bytes,5,rep,name=goods_in_warehouse,json=goodsInWarehouse,proto3" json:"goods_in_warehouse"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetStockInfoResponse) Reset()         { *m = GetStockInfoResponse{} }
func (m *GetStockInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetStockInfoResponse) ProtoMessage()    {}
func (*GetStockInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{32}
}

func (m *GetStockInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStockInfoResponse.Unmarshal(m, b)
}
func (m *GetStockInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStockInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetStockInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStockInfoResponse.Merge(m, src)
}
func (m *GetStockInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetStockInfoResponse.Size(m)
}
func (m *GetStockInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStockInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetStockInfoResponse proto.InternalMessageInfo

func (m *GetStockInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetStockInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetStockInfoResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetStockInfoResponse) GetGoodsInfo() *GetStockInfoRequest {
	if m != nil {
		return m.GoodsInfo
	}
	return nil
}

func (m *GetStockInfoResponse) GetGoodsInWarehouse() []*ProductsInWarehouse {
	if m != nil {
		return m.GoodsInWarehouse
	}
	return nil
}

//分仓库返回的库存信息
type ProductsInWarehouse struct {
	//商品skuid
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//库存信息
	Stock int32 `protobuf:"varint,2,opt,name=stock,proto3" json:"stock"`
	//财务编码
	WarehouseId          int32    `protobuf:"varint,3,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductsInWarehouse) Reset()         { *m = ProductsInWarehouse{} }
func (m *ProductsInWarehouse) String() string { return proto.CompactTextString(m) }
func (*ProductsInWarehouse) ProtoMessage()    {}
func (*ProductsInWarehouse) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{33}
}

func (m *ProductsInWarehouse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductsInWarehouse.Unmarshal(m, b)
}
func (m *ProductsInWarehouse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductsInWarehouse.Marshal(b, m, deterministic)
}
func (m *ProductsInWarehouse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductsInWarehouse.Merge(m, src)
}
func (m *ProductsInWarehouse) XXX_Size() int {
	return xxx_messageInfo_ProductsInWarehouse.Size(m)
}
func (m *ProductsInWarehouse) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductsInWarehouse.DiscardUnknown(m)
}

var xxx_messageInfo_ProductsInWarehouse proto.InternalMessageInfo

func (m *ProductsInWarehouse) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ProductsInWarehouse) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *ProductsInWarehouse) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

//商品和库存信息
type ProductsInfo struct {
	//是否组合：1：组合，2：非组合
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	//商品skuid
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//是否全部虚拟商品 0:否 1:是
	IsAllVirtual int32 `protobuf:"varint,3,opt,name=is_all_virtual,json=isAllVirtual,proto3" json:"is_all_virtual"`
	//组合商品的商品信息
	ChildRen []*ChildRen `protobuf:"bytes,4,rep,name=child_ren,json=childRen,proto3" json:"child_ren"`
	//库存信息
	Stock int32 `protobuf:"varint,5,opt,name=stock,proto3" json:"stock"`
	//财务编码
	FinanceCode          []string `protobuf:"bytes,6,rep,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductsInfo) Reset()         { *m = ProductsInfo{} }
func (m *ProductsInfo) String() string { return proto.CompactTextString(m) }
func (*ProductsInfo) ProtoMessage()    {}
func (*ProductsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{34}
}

func (m *ProductsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductsInfo.Unmarshal(m, b)
}
func (m *ProductsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductsInfo.Marshal(b, m, deterministic)
}
func (m *ProductsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductsInfo.Merge(m, src)
}
func (m *ProductsInfo) XXX_Size() int {
	return xxx_messageInfo_ProductsInfo.Size(m)
}
func (m *ProductsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ProductsInfo proto.InternalMessageInfo

func (m *ProductsInfo) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ProductsInfo) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ProductsInfo) GetIsAllVirtual() int32 {
	if m != nil {
		return m.IsAllVirtual
	}
	return 0
}

func (m *ProductsInfo) GetChildRen() []*ChildRen {
	if m != nil {
		return m.ChildRen
	}
	return nil
}

func (m *ProductsInfo) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *ProductsInfo) GetFinanceCode() []string {
	if m != nil {
		return m.FinanceCode
	}
	return nil
}

type ChildRen struct {
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//规则
	RuleNum int32 `protobuf:"varint,2,opt,name=rule_num,json=ruleNum,proto3" json:"rule_num"`
	//是否为虚拟 0:不是 1：是虚拟
	IsVirtual int32 `protobuf:"varint,3,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	// 0不是药品仓 1药品仓
	Stockwarehouse       int32    `protobuf:"varint,7,opt,name=stockwarehouse,proto3" json:"stockwarehouse"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChildRen) Reset()         { *m = ChildRen{} }
func (m *ChildRen) String() string { return proto.CompactTextString(m) }
func (*ChildRen) ProtoMessage()    {}
func (*ChildRen) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{35}
}

func (m *ChildRen) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChildRen.Unmarshal(m, b)
}
func (m *ChildRen) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChildRen.Marshal(b, m, deterministic)
}
func (m *ChildRen) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChildRen.Merge(m, src)
}
func (m *ChildRen) XXX_Size() int {
	return xxx_messageInfo_ChildRen.Size(m)
}
func (m *ChildRen) XXX_DiscardUnknown() {
	xxx_messageInfo_ChildRen.DiscardUnknown(m)
}

var xxx_messageInfo_ChildRen proto.InternalMessageInfo

func (m *ChildRen) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ChildRen) GetRuleNum() int32 {
	if m != nil {
		return m.RuleNum
	}
	return 0
}

func (m *ChildRen) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *ChildRen) GetStockwarehouse() int32 {
	if m != nil {
		return m.Stockwarehouse
	}
	return 0
}

type IsOmsWarehouseVo struct {
	//    仓库的code
	Warehouse            string   `protobuf:"bytes,1,opt,name=Warehouse,proto3" json:"Warehouse"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsOmsWarehouseVo) Reset()         { *m = IsOmsWarehouseVo{} }
func (m *IsOmsWarehouseVo) String() string { return proto.CompactTextString(m) }
func (*IsOmsWarehouseVo) ProtoMessage()    {}
func (*IsOmsWarehouseVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{36}
}

func (m *IsOmsWarehouseVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsOmsWarehouseVo.Unmarshal(m, b)
}
func (m *IsOmsWarehouseVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsOmsWarehouseVo.Marshal(b, m, deterministic)
}
func (m *IsOmsWarehouseVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsOmsWarehouseVo.Merge(m, src)
}
func (m *IsOmsWarehouseVo) XXX_Size() int {
	return xxx_messageInfo_IsOmsWarehouseVo.Size(m)
}
func (m *IsOmsWarehouseVo) XXX_DiscardUnknown() {
	xxx_messageInfo_IsOmsWarehouseVo.DiscardUnknown(m)
}

var xxx_messageInfo_IsOmsWarehouseVo proto.InternalMessageInfo

func (m *IsOmsWarehouseVo) GetWarehouse() string {
	if m != nil {
		return m.Warehouse
	}
	return ""
}

type IsOmsWarehouseResp struct {
	//    是否是oms true是 false 否
	IsOms                bool     `protobuf:"varint,1,opt,name=IsOms,proto3" json:"IsOms"`
	Msg                  string   `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg"`
	Code                 int32    `protobuf:"varint,3,opt,name=Code,proto3" json:"Code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsOmsWarehouseResp) Reset()         { *m = IsOmsWarehouseResp{} }
func (m *IsOmsWarehouseResp) String() string { return proto.CompactTextString(m) }
func (*IsOmsWarehouseResp) ProtoMessage()    {}
func (*IsOmsWarehouseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{37}
}

func (m *IsOmsWarehouseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsOmsWarehouseResp.Unmarshal(m, b)
}
func (m *IsOmsWarehouseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsOmsWarehouseResp.Marshal(b, m, deterministic)
}
func (m *IsOmsWarehouseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsOmsWarehouseResp.Merge(m, src)
}
func (m *IsOmsWarehouseResp) XXX_Size() int {
	return xxx_messageInfo_IsOmsWarehouseResp.Size(m)
}
func (m *IsOmsWarehouseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsOmsWarehouseResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsOmsWarehouseResp proto.InternalMessageInfo

func (m *IsOmsWarehouseResp) GetIsOms() bool {
	if m != nil {
		return m.IsOms
	}
	return false
}

func (m *IsOmsWarehouseResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *IsOmsWarehouseResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

type SyncOmsStockRequest struct {
	//仓库编码
	WarehouseCode string `protobuf:"bytes,1,opt,name=WarehouseCode,proto3" json:"WarehouseCode"`
	// 需要同步的库存
	List                 []*SkuStock `protobuf:"bytes,2,rep,name=List,proto3" json:"List"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SyncOmsStockRequest) Reset()         { *m = SyncOmsStockRequest{} }
func (m *SyncOmsStockRequest) String() string { return proto.CompactTextString(m) }
func (*SyncOmsStockRequest) ProtoMessage()    {}
func (*SyncOmsStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{38}
}

func (m *SyncOmsStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncOmsStockRequest.Unmarshal(m, b)
}
func (m *SyncOmsStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncOmsStockRequest.Marshal(b, m, deterministic)
}
func (m *SyncOmsStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncOmsStockRequest.Merge(m, src)
}
func (m *SyncOmsStockRequest) XXX_Size() int {
	return xxx_messageInfo_SyncOmsStockRequest.Size(m)
}
func (m *SyncOmsStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncOmsStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncOmsStockRequest proto.InternalMessageInfo

func (m *SyncOmsStockRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *SyncOmsStockRequest) GetList() []*SkuStock {
	if m != nil {
		return m.List
	}
	return nil
}

type SkuStock struct {
	//第三方货号
	ThirdSkuId string `protobuf:"bytes,1,opt,name=ThirdSkuId,proto3" json:"ThirdSkuId"`
	//库存
	Stock                int32    `protobuf:"varint,2,opt,name=Stock,proto3" json:"Stock"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuStock) Reset()         { *m = SkuStock{} }
func (m *SkuStock) String() string { return proto.CompactTextString(m) }
func (*SkuStock) ProtoMessage()    {}
func (*SkuStock) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{39}
}

func (m *SkuStock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuStock.Unmarshal(m, b)
}
func (m *SkuStock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuStock.Marshal(b, m, deterministic)
}
func (m *SkuStock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuStock.Merge(m, src)
}
func (m *SkuStock) XXX_Size() int {
	return xxx_messageInfo_SkuStock.Size(m)
}
func (m *SkuStock) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuStock.DiscardUnknown(m)
}

var xxx_messageInfo_SkuStock proto.InternalMessageInfo

func (m *SkuStock) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *SkuStock) GetStock() int32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

type SyncOmsDiffStockRequest struct {
	//仓库编码
	WarehouseCode string `protobuf:"bytes,1,opt,name=WarehouseCode,proto3" json:"WarehouseCode"`
	// 需要同步的库存
	List                 []*SkuStock `protobuf:"bytes,2,rep,name=List,proto3" json:"List"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SyncOmsDiffStockRequest) Reset()         { *m = SyncOmsDiffStockRequest{} }
func (m *SyncOmsDiffStockRequest) String() string { return proto.CompactTextString(m) }
func (*SyncOmsDiffStockRequest) ProtoMessage()    {}
func (*SyncOmsDiffStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{40}
}

func (m *SyncOmsDiffStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncOmsDiffStockRequest.Unmarshal(m, b)
}
func (m *SyncOmsDiffStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncOmsDiffStockRequest.Marshal(b, m, deterministic)
}
func (m *SyncOmsDiffStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncOmsDiffStockRequest.Merge(m, src)
}
func (m *SyncOmsDiffStockRequest) XXX_Size() int {
	return xxx_messageInfo_SyncOmsDiffStockRequest.Size(m)
}
func (m *SyncOmsDiffStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncOmsDiffStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncOmsDiffStockRequest proto.InternalMessageInfo

func (m *SyncOmsDiffStockRequest) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *SyncOmsDiffStockRequest) GetList() []*SkuStock {
	if m != nil {
		return m.List
	}
	return nil
}

type QueryStockReq struct {
	// 商品sku
	SkuIds []int32 `protobuf:"varint,1,rep,packed,name=sku_ids,json=skuIds,proto3" json:"sku_ids"`
	// 渠道id 1-阿闻到家 2-美团 3-饿了么 4-京东到家 5-阿闻电商 6-门店 7-百度 8-H5, 9-互联网医疗 10-自提
	ChannelId int32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 门店财务编码，channel_id = 5 时不用传
	ShopId               string   `protobuf:"bytes,3,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryStockReq) Reset()         { *m = QueryStockReq{} }
func (m *QueryStockReq) String() string { return proto.CompactTextString(m) }
func (*QueryStockReq) ProtoMessage()    {}
func (*QueryStockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{41}
}

func (m *QueryStockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryStockReq.Unmarshal(m, b)
}
func (m *QueryStockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryStockReq.Marshal(b, m, deterministic)
}
func (m *QueryStockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryStockReq.Merge(m, src)
}
func (m *QueryStockReq) XXX_Size() int {
	return xxx_messageInfo_QueryStockReq.Size(m)
}
func (m *QueryStockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryStockReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryStockReq proto.InternalMessageInfo

func (m *QueryStockReq) GetSkuIds() []int32 {
	if m != nil {
		return m.SkuIds
	}
	return nil
}

func (m *QueryStockReq) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QueryStockReq) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

type QueryStockRes struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 库存，map，key为skuid，value为库存
	Stock                map[int32]int32 `protobuf:"bytes,3,rep,name=stock,proto3" json:"stock" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *QueryStockRes) Reset()         { *m = QueryStockRes{} }
func (m *QueryStockRes) String() string { return proto.CompactTextString(m) }
func (*QueryStockRes) ProtoMessage()    {}
func (*QueryStockRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_df3b006fd776210d, []int{42}
}

func (m *QueryStockRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryStockRes.Unmarshal(m, b)
}
func (m *QueryStockRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryStockRes.Marshal(b, m, deterministic)
}
func (m *QueryStockRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryStockRes.Merge(m, src)
}
func (m *QueryStockRes) XXX_Size() int {
	return xxx_messageInfo_QueryStockRes.Size(m)
}
func (m *QueryStockRes) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryStockRes.DiscardUnknown(m)
}

var xxx_messageInfo_QueryStockRes proto.InternalMessageInfo

func (m *QueryStockRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryStockRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryStockRes) GetStock() map[int32]int32 {
	if m != nil {
		return m.Stock
	}
	return nil
}

func init() {
	proto.RegisterType((*TaskCommonRequest)(nil), "proto.TaskCommonRequest")
	proto.RegisterType((*TaskCommonResponse)(nil), "proto.TaskCommonResponse")
	proto.RegisterType((*ManualStockRecordListRequest)(nil), "proto.ManualStockRecordListRequest")
	proto.RegisterType((*ManualStockRecordListResponse)(nil), "proto.ManualStockRecordListResponse")
	proto.RegisterType((*ManualStockRecord)(nil), "proto.ManualStockRecord")
	proto.RegisterType((*RunStockByManualRequest)(nil), "proto.RunStockByManualRequest")
	proto.RegisterType((*OmsSyncSotckRequest)(nil), "proto.OmsSyncSotckRequest")
	proto.RegisterType((*OmsSyncSotck)(nil), "proto.OmsSyncSotck")
	proto.RegisterType((*OmsSyncSotckResponse)(nil), "proto.OmsSyncSotckResponse")
	proto.RegisterType((*OmsGoodsList)(nil), "proto.OmsGoodsList")
	proto.RegisterType((*AddStockTaskRequest)(nil), "proto.AddStockTaskRequest")
	proto.RegisterType((*AddStockTaskResponse)(nil), "proto.AddStockTaskResponse")
	proto.RegisterType((*QueryStockTaskRequest)(nil), "proto.QueryStockTaskRequest")
	proto.RegisterType((*QueryStockTaskResponse)(nil), "proto.QueryStockTaskResponse")
	proto.RegisterType((*SkuCodeInfo)(nil), "proto.SkuCodeInfo")
	proto.RegisterType((*ProvideSyncStockRequest)(nil), "proto.ProvideSyncStockRequest")
	proto.RegisterType((*Warehouse)(nil), "proto.Warehouse")
	proto.RegisterType((*GoodsStockList)(nil), "proto.GoodsStockList")
	proto.RegisterType((*GoodsStock)(nil), "proto.GoodsStock")
	proto.RegisterType((*ProvideSyncStockResponse)(nil), "proto.ProvideSyncStockResponse")
	proto.RegisterType((*ThirdInfoRequest)(nil), "proto.ThirdInfoRequest")
	proto.RegisterType((*ThirdInfoResponse)(nil), "proto.ThirdInfoResponse")
	proto.RegisterType((*ThirdInfo)(nil), "proto.ThirdInfo")
	proto.RegisterType((*GoodsList)(nil), "proto.Goods_list")
	proto.RegisterType((*FreezeRequest)(nil), "proto.FreezeRequest")
	proto.RegisterType((*OrderGoodsInfo)(nil), "proto.OrderGoodsInfo")
	proto.RegisterType((*FreezeResponse)(nil), "proto.FreezeResponse")
	proto.RegisterType((*FreedStockRequest)(nil), "proto.FreedStockRequest")
	proto.RegisterType((*GoodsInfo)(nil), "proto.GoodsInfo")
	proto.RegisterType((*FreedStockResponse)(nil), "proto.FreedStockResponse")
	proto.RegisterType((*ProductsAndStock)(nil), "proto.ProductsAndStock")
	proto.RegisterType((*GetStockInfoRequest)(nil), "proto.GetStockInfoRequest")
	proto.RegisterType((*GetStockInfoResponse)(nil), "proto.GetStockInfoResponse")
	proto.RegisterType((*ProductsInWarehouse)(nil), "proto.ProductsInWarehouse")
	proto.RegisterType((*ProductsInfo)(nil), "proto.ProductsInfo")
	proto.RegisterType((*ChildRen)(nil), "proto.ChildRen")
	proto.RegisterType((*IsOmsWarehouseVo)(nil), "proto.IsOmsWarehouseVo")
	proto.RegisterType((*IsOmsWarehouseResp)(nil), "proto.IsOmsWarehouseResp")
	proto.RegisterType((*SyncOmsStockRequest)(nil), "proto.SyncOmsStockRequest")
	proto.RegisterType((*SkuStock)(nil), "proto.SkuStock")
	proto.RegisterType((*SyncOmsDiffStockRequest)(nil), "proto.SyncOmsDiffStockRequest")
	proto.RegisterType((*QueryStockReq)(nil), "proto.QueryStockReq")
	proto.RegisterType((*QueryStockRes)(nil), "proto.QueryStockRes")
	proto.RegisterMapType((map[int32]int32)(nil), "proto.QueryStockRes.StockEntry")
}

func init() { proto.RegisterFile("ic/inventorycenter.proto", fileDescriptor_df3b006fd776210d) }

var fileDescriptor_df3b006fd776210d = []byte{
	// 1947 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0x4f, 0x73, 0x1b, 0x49,
	0x15, 0xaf, 0x91, 0x2c, 0x5b, 0x7a, 0x96, 0xb5, 0x72, 0xdb, 0x8e, 0x15, 0x25, 0xd9, 0x78, 0x27,
	0x81, 0xa4, 0xa8, 0x2d, 0x07, 0x02, 0x0b, 0x61, 0x6b, 0x6b, 0x8b, 0xc4, 0x09, 0xc9, 0x6c, 0x91,
	0xc4, 0x8c, 0xcc, 0x86, 0xca, 0x61, 0xb5, 0x13, 0x75, 0x5b, 0x9e, 0xd2, 0x68, 0x66, 0x76, 0x7a,
	0xc6, 0x8b, 0xcc, 0x85, 0x2a, 0x2e, 0x5c, 0xb8, 0x01, 0x07, 0x4e, 0xdc, 0xb8, 0x51, 0x1c, 0xf8,
	0x0c, 0x1c, 0xf8, 0x12, 0x7c, 0x15, 0xea, 0xf5, 0x9f, 0x99, 0x9e, 0x19, 0x29, 0x84, 0x2d, 0x73,
	0xd2, 0xbc, 0xd7, 0xaf, 0x5f, 0xbf, 0x7e, 0xef, 0xf7, 0xfe, 0xa8, 0x61, 0xe0, 0x4f, 0xee, 0xf9,
	0xe1, 0x39, 0x0b, 0xd3, 0x28, 0x59, 0x4c, 0x58, 0x98, 0xb2, 0xe4, 0x30, 0x4e, 0xa2, 0x34, 0x22,
	0x2d, 0xf1, 0x33, 0xbc, 0x36, 0x8d, 0xa2, 0x69, 0xc0, 0xee, 0x09, 0xea, 0x4d, 0x76, 0x7a, 0x8f,
	0xcd, 0xe3, 0x74, 0x21, 0x65, 0xec, 0x3b, 0xb0, 0x7d, 0xe2, 0xf1, 0xd9, 0x51, 0x34, 0x9f, 0x47,
	0xa1, 0xcb, 0xbe, 0xca, 0x18, 0x4f, 0x09, 0x81, 0xb5, 0x74, 0x11, 0xb3, 0x81, 0x75, 0x60, 0xdd,
	0x6d, 0xb9, 0xe2, 0xdb, 0xfe, 0x25, 0x10, 0x53, 0x90, 0xc7, 0x51, 0xc8, 0x19, 0x4a, 0x4e, 0x22,
	0x9a, 0x4b, 0xe2, 0x37, 0x19, 0xc0, 0xc6, 0x9c, 0x71, 0xee, 0x4d, 0xd9, 0xa0, 0x71, 0x60, 0xdd,
	0xed, 0xb8, 0x9a, 0x24, 0xbb, 0xd0, 0x62, 0x49, 0x12, 0x25, 0x83, 0xa6, 0xe0, 0x4b, 0xc2, 0xfe,
	0x35, 0x5c, 0x7f, 0xee, 0x85, 0x99, 0x17, 0x8c, 0xd2, 0x68, 0x32, 0x73, 0xd9, 0x24, 0x4a, 0xe8,
	0xcf, 0x7c, 0x9e, 0x6a, 0x6b, 0x6e, 0xc2, 0xe6, 0x24, 0x61, 0x5e, 0xca, 0xc6, 0xa9, 0x3f, 0x97,
	0x47, 0x75, 0x5c, 0x90, 0xac, 0x13, 0x7f, 0xce, 0xc8, 0x0d, 0x80, 0xd8, 0x9b, 0xb2, 0xb1, 0x1f,
	0x52, 0xf6, 0x2b, 0x71, 0x66, 0xcb, 0xed, 0x20, 0xc7, 0x41, 0x06, 0xb9, 0x06, 0x82, 0x18, 0x73,
	0xff, 0x82, 0x89, 0x93, 0x5b, 0x6e, 0x1b, 0x19, 0x23, 0xff, 0x82, 0xd9, 0x19, 0xdc, 0x58, 0x71,
	0xb8, 0xba, 0x61, 0x1f, 0x9a, 0x73, 0x3e, 0x55, 0xa7, 0xe2, 0x27, 0xde, 0x22, 0x8d, 0x52, 0x2f,
	0x50, 0x27, 0x49, 0x82, 0x7c, 0x08, 0x6b, 0xd4, 0x4b, 0xbd, 0x41, 0xf3, 0xa0, 0x79, 0x77, 0xf3,
	0xfe, 0x40, 0xba, 0xf7, 0xb0, 0xa6, 0xdb, 0x15, 0x52, 0xf6, 0x9f, 0x2d, 0xd8, 0xae, 0xad, 0x91,
	0x1e, 0x34, 0x7c, 0xaa, 0x7c, 0xd9, 0xf0, 0x29, 0x5e, 0x2c, 0x8a, 0x59, 0x82, 0x57, 0xf7, 0xa9,
	0x72, 0x66, 0x47, 0x71, 0x1c, 0x4a, 0x86, 0xd0, 0x96, 0x44, 0xee, 0xd1, 0x9c, 0x26, 0x57, 0xa1,
	0x7d, 0xea, 0x07, 0x6c, 0x9c, 0x25, 0xc1, 0x60, 0x4d, 0x46, 0x01, 0xe9, 0x5f, 0x24, 0x41, 0xd5,
	0x9f, 0xad, 0xaa, 0x3f, 0xed, 0x31, 0xec, 0xbb, 0x59, 0x28, 0x0c, 0x7b, 0xb4, 0x90, 0x56, 0xea,
	0x58, 0x7c, 0x0b, 0x7a, 0x5f, 0x7b, 0x09, 0x3b, 0x8b, 0x32, 0xce, 0xc6, 0x79, 0xe4, 0x3b, 0xee,
	0x56, 0xce, 0x3d, 0x42, 0x08, 0xdc, 0x00, 0x98, 0x46, 0x11, 0xe5, 0x52, 0x44, 0x19, 0x2e, 0x38,
	0xb8, 0x6c, 0x7f, 0x0a, 0x3b, 0x2f, 0xe7, 0x7c, 0xb4, 0x08, 0x27, 0xa3, 0x28, 0xc5, 0xdb, 0x4b,
	0xe5, 0x77, 0x94, 0x0b, 0x2d, 0xe1, 0xc2, 0x1d, 0xe5, 0xc2, 0x92, 0xa4, 0xf4, 0xde, 0x19, 0x74,
	0x4d, 0xee, 0xbb, 0x5a, 0xf5, 0x3d, 0x90, 0x36, 0x04, 0x3e, 0x4f, 0x07, 0x8d, 0xea, 0x21, 0x4f,
	0x71, 0x49, 0x84, 0xbe, 0x90, 0xb2, 0x1f, 0xc3, 0x6e, 0xd9, 0xd2, 0x6f, 0x82, 0x7b, 0xfb, 0x37,
	0x96, 0x30, 0x38, 0x3f, 0x01, 0x41, 0xc5, 0xe3, 0x4c, 0x83, 0x8a, 0xc7, 0x99, 0xe0, 0xcc, 0x32,
	0xb5, 0x11, 0x3f, 0x11, 0x66, 0x1c, 0x43, 0xa0, 0x20, 0x2b, 0x09, 0x8c, 0xb9, 0x17, 0x04, 0x72,
	0x61, 0x4d, 0x62, 0x59, 0xd3, 0xb8, 0x96, 0x9e, 0xf9, 0x09, 0x45, 0x45, 0x32, 0xaa, 0x39, 0x6d,
	0x73, 0xd8, 0x79, 0x48, 0xa9, 0x88, 0x29, 0xa6, 0xb1, 0x76, 0xf9, 0x00, 0x36, 0x12, 0xf9, 0xa9,
	0x8c, 0xd1, 0x24, 0xb9, 0x05, 0x5b, 0x9c, 0x25, 0xbe, 0x17, 0x8c, 0xc3, 0x6c, 0xfe, 0x86, 0x25,
	0xca, 0xb4, 0xae, 0x64, 0xbe, 0x10, 0x3c, 0x8c, 0xb3, 0x38, 0x7a, 0x2c, 0xca, 0x85, 0x34, 0xb4,
	0x23, 0x38, 0x27, 0x58, 0x33, 0x5e, 0xc3, 0x6e, 0xf9, 0xd0, 0x4b, 0xac, 0x1a, 0x9f, 0xc0, 0xde,
	0xcf, 0x33, 0x96, 0x2c, 0x6a, 0x57, 0xaa, 0x19, 0x6e, 0xd5, 0x0d, 0xb7, 0xff, 0x62, 0xc1, 0x95,
	0xea, 0x76, 0x65, 0x5c, 0x35, 0x09, 0x0d, 0x17, 0x35, 0xfe, 0x8b, 0x8b, 0x9a, 0x4b, 0x5c, 0x74,
	0x05, 0xd6, 0x79, 0xea, 0xa5, 0x19, 0x57, 0xe1, 0x52, 0x94, 0x91, 0x85, 0xd4, 0x4b, 0x2b, 0x59,
	0xf8, 0xd8, 0x4b, 0x99, 0xfd, 0x2f, 0x0b, 0x36, 0x47, 0xb3, 0x0c, 0x91, 0xeb, 0x84, 0xa7, 0x11,
	0xf9, 0x00, 0xba, 0xa7, 0x7e, 0xe8, 0x85, 0x93, 0x12, 0xc4, 0x37, 0x15, 0x4f, 0x00, 0xbc, 0x0e,
	0x22, 0x05, 0xb4, 0x66, 0x01, 0xb4, 0x9b, 0xb0, 0x29, 0x40, 0x31, 0xe6, 0xb3, 0xcc, 0xa7, 0xaa,
	0x36, 0x80, 0x60, 0x8d, 0x90, 0x43, 0x76, 0xa0, 0x75, 0x11, 0x60, 0xbd, 0x69, 0xc9, 0xe8, 0x5c,
	0x04, 0x0e, 0x25, 0x7b, 0xb0, 0xce, 0x92, 0x18, 0xb9, 0xeb, 0x12, 0x8d, 0x2c, 0x89, 0x1d, 0x4a,
	0xee, 0xc0, 0x7b, 0x32, 0xfe, 0x79, 0xa2, 0x0d, 0x36, 0x0e, 0x9a, 0x77, 0x5b, 0x6e, 0x4f, 0xb0,
	0x5f, 0x69, 0xae, 0xfd, 0x3b, 0x0b, 0xf6, 0x8f, 0x93, 0xe8, 0xdc, 0xa7, 0x4c, 0x24, 0x93, 0x2c,
	0x7a, 0xd2, 0x8d, 0x1f, 0x01, 0x88, 0xcc, 0x10, 0x4c, 0x95, 0xfc, 0x7b, 0x2a, 0x2f, 0x8b, 0x05,
	0x91, 0x99, 0x86, 0x20, 0xf9, 0x21, 0x6c, 0x1d, 0x7b, 0x9c, 0xe7, 0x67, 0xa8, 0x8c, 0xee, 0xab,
	0x9d, 0x39, 0xdf, 0x2d, 0x8b, 0xd9, 0x13, 0xe8, 0xe4, 0x04, 0x06, 0xdb, 0xc9, 0x83, 0xed, 0x50,
	0x44, 0xe6, 0x51, 0x51, 0xb2, 0xc4, 0x37, 0xa6, 0xd5, 0x91, 0x97, 0xb2, 0x69, 0x94, 0x2c, 0x74,
	0xfb, 0xd0, 0x34, 0x46, 0x77, 0x54, 0x8a, 0xae, 0xa4, 0xec, 0x2f, 0xa0, 0x57, 0x36, 0x1d, 0xbb,
	0xd0, 0x85, 0x1f, 0x44, 0xe1, 0x74, 0xac, 0xd0, 0xd5, 0x71, 0xdb, 0x92, 0xe1, 0x50, 0x72, 0x0f,
	0x3a, 0x79, 0x71, 0x50, 0xf7, 0xd8, 0xae, 0x79, 0xc0, 0x2d, 0x64, 0xec, 0x4f, 0x4c, 0x9f, 0x21,
	0x44, 0x05, 0xe5, 0xe8, 0x26, 0xa1, 0x49, 0xcc, 0x9d, 0x91, 0x59, 0x44, 0x04, 0x61, 0x7f, 0x01,
	0x83, 0x7a, 0x30, 0x2e, 0x31, 0x37, 0x3d, 0xe8, 0x9f, 0x20, 0xa0, 0x10, 0xb7, 0x3a, 0xca, 0x1f,
	0x40, 0xb7, 0xa8, 0xd1, 0x79, 0x82, 0x6d, 0xe6, 0x3c, 0x87, 0x92, 0xef, 0xc0, 0x06, 0x65, 0xa9,
	0xe7, 0x07, 0xbc, 0x12, 0xcb, 0x42, 0x99, 0x16, 0xb0, 0x5f, 0xc1, 0xb6, 0x71, 0xc4, 0x25, 0xda,
	0xfe, 0x19, 0x74, 0x72, 0xc5, 0xe4, 0x0e, 0xf4, 0x55, 0xb2, 0xc4, 0x19, 0x26, 0x4c, 0x11, 0xbb,
	0x2d, 0x99, 0x31, 0x71, 0x36, 0x9a, 0x65, 0xd2, 0xcf, 0xb2, 0x26, 0x37, 0x8c, 0x62, 0x6d, 0xff,
	0xc9, 0x52, 0x61, 0x1a, 0x63, 0x33, 0x41, 0x53, 0x44, 0x67, 0xc9, 0x95, 0x68, 0x72, 0xf9, 0x76,
	0x04, 0x5e, 0xc8, 0xbe, 0x36, 0x9b, 0x40, 0x4e, 0x23, 0xf0, 0x58, 0x18, 0x65, 0xd3, 0x33, 0x01,
	0xbc, 0xb6, 0xab, 0x28, 0x72, 0x1b, 0x7a, 0xb2, 0xf3, 0xa2, 0xb5, 0xa2, 0x2a, 0xcb, 0x34, 0xee,
	0x0a, 0xee, 0x68, 0x96, 0x89, 0xc2, 0xfc, 0x47, 0x0b, 0xb6, 0x7e, 0x9a, 0x30, 0x76, 0xc1, 0x74,
	0x78, 0xb0, 0x4c, 0x45, 0x59, 0x32, 0xd1, 0xce, 0x53, 0x14, 0xc2, 0x76, 0xce, 0xb0, 0x90, 0x15,
	0x13, 0x48, 0x5b, 0x32, 0x1c, 0x8a, 0x43, 0x46, 0x94, 0x50, 0xb9, 0x26, 0x9d, 0xb8, 0x21, 0x68,
	0x87, 0x92, 0x1f, 0xe8, 0x09, 0x40, 0x34, 0xdb, 0xb5, 0x52, 0x52, 0xbf, 0x44, 0x19, 0x89, 0x50,
	0x8c, 0x9e, 0x6c, 0xb7, 0x02, 0xd6, 0xbf, 0xb7, 0xa0, 0x57, 0x5e, 0xc5, 0x33, 0xa4, 0xa2, 0x8a,
	0xd7, 0x1c, 0x8a, 0x36, 0x1b, 0xbd, 0xa9, 0xe5, 0x2a, 0xaa, 0x06, 0xb5, 0x66, 0x1d, 0x6a, 0xa5,
	0x89, 0x41, 0xb8, 0x49, 0xe6, 0x6f, 0x31, 0x31, 0x08, 0x3f, 0x9d, 0x40, 0x4f, 0xbb, 0xe9, 0x12,
	0x21, 0xf6, 0x07, 0x0b, 0xb6, 0x51, 0x2d, 0x2d, 0x95, 0xc1, 0x55, 0x11, 0x30, 0x9d, 0xdc, 0x78,
	0x9b, 0x93, 0x9b, 0xef, 0xe6, 0xe4, 0x55, 0x1d, 0xc9, 0xfe, 0x54, 0x15, 0xa1, 0x6f, 0xe8, 0x76,
	0xfc, 0x87, 0x60, 0xde, 0xea, 0x12, 0x1d, 0x76, 0x02, 0xfd, 0xe3, 0x24, 0xa2, 0xd9, 0x24, 0xe5,
	0x0f, 0x43, 0xa9, 0xff, 0x5d, 0xea, 0x09, 0xfe, 0x2f, 0x90, 0xdb, 0x8c, 0xf1, 0x59, 0x71, 0x1c,
	0x6a, 0xff, 0xdd, 0x82, 0x9d, 0xa7, 0x2c, 0x15, 0xea, 0xcc, 0x4a, 0xb5, 0x2a, 0x10, 0x07, 0xd0,
	0xf5, 0xf9, 0x38, 0x64, 0x8c, 0x8e, 0xe3, 0x2c, 0xd0, 0xe3, 0x3f, 0xf8, 0xfc, 0x05, 0x63, 0xf4,
	0x38, 0x0b, 0x02, 0xf2, 0x23, 0xe8, 0x6a, 0x3b, 0x51, 0xa1, 0x8a, 0x88, 0x9e, 0x31, 0xcd, 0x25,
	0xb7, 0x24, 0x48, 0xbe, 0x0d, 0xb2, 0x61, 0x16, 0x6d, 0x74, 0xcd, 0x68, 0xa3, 0x39, 0xd7, 0xfe,
	0xb7, 0x05, 0xbb, 0x65, 0x93, 0x2f, 0xcf, 0xcb, 0xe4, 0xc7, 0x1a, 0x4d, 0x3e, 0xda, 0x8e, 0xd8,
	0xd8, 0xbc, 0x3f, 0xd4, 0x5d, 0xa8, 0xee, 0x27, 0x05, 0x29, 0x61, 0xff, 0x33, 0x20, 0x7a, 0xab,
	0x31, 0x0a, 0xb4, 0xc4, 0xf5, 0x87, 0xb5, 0xeb, 0x17, 0xad, 0xb9, 0xaf, 0x54, 0x14, 0xdd, 0x99,
	0xc1, 0xce, 0x12, 0x41, 0x9c, 0x3f, 0x8c, 0xf2, 0x8b, 0x15, 0x72, 0x75, 0xd9, 0x7d, 0x87, 0xfc,
	0xb7, 0xff, 0x69, 0x95, 0x43, 0xb5, 0xec, 0x2f, 0xaf, 0x71, 0x68, 0xc3, 0x3c, 0xf4, 0x36, 0xf4,
	0x7c, 0x3e, 0xf6, 0x82, 0x60, 0x7c, 0xee, 0x27, 0x69, 0xe6, 0x05, 0xea, 0x80, 0xae, 0xcf, 0x1f,
	0x06, 0xc1, 0xe7, 0x92, 0x47, 0x3e, 0x84, 0xce, 0xe4, 0xcc, 0x0f, 0xe8, 0x38, 0x61, 0xa1, 0xaa,
	0x7f, 0xef, 0x29, 0x4f, 0x1c, 0x21, 0xdf, 0x65, 0xa1, 0xdb, 0x9e, 0xa8, 0xaf, 0xe2, 0x22, 0xad,
	0xca, 0x45, 0x4a, 0x23, 0xdf, 0xfa, 0x41, 0xb3, 0x32, 0xf2, 0xd9, 0xbf, 0xb5, 0xa0, 0xad, 0xf5,
	0xad, 0xf2, 0xd2, 0x55, 0x68, 0x27, 0x59, 0xc0, 0x70, 0x4a, 0x55, 0x37, 0xd9, 0x40, 0xfa, 0x45,
	0x36, 0xc7, 0x14, 0xf1, 0x79, 0xe5, 0x1e, 0x1d, 0x9f, 0xeb, 0x4b, 0xd4, 0x71, 0xb9, 0x21, 0x44,
	0xaa, 0xb8, 0xfc, 0x2e, 0xf4, 0x1d, 0xfe, 0x72, 0x5e, 0x4c, 0x59, 0x9f, 0x47, 0xe4, 0xba, 0x31,
	0x67, 0xa9, 0x12, 0x52, 0x30, 0xec, 0x63, 0x20, 0xe5, 0x1d, 0x08, 0x65, 0x74, 0x83, 0xe0, 0x0a,
	0xf9, 0xb6, 0x2b, 0x09, 0x1c, 0x62, 0x9f, 0xf3, 0xa9, 0x1e, 0x6b, 0x9f, 0xf3, 0x69, 0x3e, 0xa6,
	0x49, 0x83, 0xc5, 0xb7, 0xfd, 0x25, 0xec, 0xe0, 0x34, 0x83, 0x7f, 0xd7, 0xcc, 0xb2, 0x7a, 0x1b,
	0xb6, 0x5e, 0x99, 0xff, 0x02, 0x75, 0xff, 0x2e, 0x31, 0xc9, 0x2d, 0x58, 0x33, 0x66, 0x2f, 0x1d,
	0xa8, 0xd1, 0x2c, 0x93, 0xba, 0xc4, 0xa2, 0xfd, 0x13, 0x68, 0x6b, 0x0e, 0x79, 0x1f, 0xe0, 0x44,
	0xcd, 0xcc, 0x8e, 0xae, 0x90, 0x06, 0xa7, 0x18, 0xbc, 0x1a, 0xe6, 0xe0, 0x45, 0x61, 0x5f, 0xd9,
	0xf8, 0xd8, 0x3f, 0x3d, 0xfd, 0x7f, 0xd9, 0xf9, 0x25, 0x6c, 0x15, 0xff, 0x6d, 0x5c, 0xf6, 0x15,
	0xd9, 0x87, 0x0d, 0x89, 0x0b, 0x2e, 0xc6, 0x6b, 0x2c, 0x69, 0x68, 0x24, 0xc7, 0xf0, 0x4f, 0xce,
	0xbc, 0x30, 0x64, 0x41, 0x81, 0xf2, 0x8e, 0xe2, 0x38, 0x54, 0xec, 0x3b, 0x8b, 0xe2, 0xa2, 0xbd,
	0xaf, 0x23, 0xe9, 0x50, 0xfb, 0x6f, 0x56, 0xf9, 0x08, 0xfe, 0x3f, 0x16, 0xa0, 0x8f, 0x8a, 0xff,
	0xb6, 0x78, 0x8f, 0x9b, 0xea, 0x1e, 0x25, 0x95, 0x87, 0xe2, 0xe3, 0x49, 0x98, 0x26, 0x0b, 0x95,
	0x0f, 0xc3, 0x07, 0x00, 0x05, 0x13, 0x61, 0x31, 0x63, 0x0b, 0x75, 0x22, 0x7e, 0xa2, 0xd3, 0xcf,
	0xbd, 0x20, 0x63, 0xda, 0xe9, 0x82, 0xf8, 0xb8, 0xf1, 0xc0, 0xba, 0xff, 0x8f, 0x75, 0xe8, 0x3b,
	0xfa, 0x91, 0x6c, 0xc4, 0x92, 0x73, 0x7f, 0xc2, 0xc8, 0xc7, 0xb0, 0x29, 0xbb, 0xbb, 0x0c, 0xe9,
	0xae, 0xb2, 0xa2, 0x34, 0x18, 0x0d, 0xf7, 0x2a, 0x5c, 0x55, 0x70, 0x1f, 0x02, 0x14, 0xcd, 0x8e,
	0x0c, 0x0c, 0xa1, 0x52, 0x57, 0x1f, 0x5e, 0x5d, 0xb2, 0xa2, 0x54, 0x3c, 0x81, 0xbe, 0x33, 0x8f,
	0xa3, 0x24, 0x95, 0xb0, 0x11, 0x8a, 0xf6, 0x6b, 0x13, 0xaf, 0xd2, 0x33, 0xa8, 0x2f, 0x28, 0x35,
	0x4f, 0xa1, 0x6b, 0x56, 0x67, 0xf2, 0x96, 0x92, 0x3d, 0xbc, 0xb6, 0x74, 0x4d, 0x29, 0x7a, 0x09,
	0xfd, 0xea, 0xb3, 0x0f, 0x79, 0x5f, 0x6d, 0x58, 0xf1, 0x1e, 0x94, 0x2b, 0x5c, 0xfa, 0x48, 0xf2,
	0x06, 0xf6, 0x96, 0xbe, 0xad, 0x91, 0x5b, 0xab, 0x5e, 0xc7, 0x8c, 0x67, 0xbf, 0xe1, 0xed, 0xb7,
	0x0b, 0xa9, 0x33, 0x9e, 0x55, 0xeb, 0x88, 0x48, 0x13, 0xed, 0xc6, 0x6a, 0x51, 0xca, 0xc3, 0xb1,
	0xa4, 0xf6, 0x3c, 0x82, 0xae, 0x59, 0x3f, 0x72, 0x3f, 0x2e, 0x29, 0x2a, 0xc3, 0x2b, 0x87, 0xf2,
	0x4d, 0xf5, 0x50, 0xbf, 0xa9, 0x1e, 0x3e, 0x99, 0xc7, 0xe9, 0x82, 0x7c, 0x06, 0xfd, 0x6a, 0x7e,
	0xe7, 0x2e, 0x5c, 0x91, 0xf8, 0x2b, 0x75, 0x3d, 0x83, 0xed, 0x1c, 0xb1, 0xe2, 0xe5, 0xd5, 0x0b,
	0x82, 0x1c, 0x68, 0xb5, 0x37, 0xdb, 0xfc, 0x66, 0x4b, 0x1e, 0x69, 0x1f, 0x00, 0x14, 0x99, 0x95,
	0xc3, 0xbc, 0x54, 0x22, 0x86, 0xcb, 0xb8, 0xfc, 0xfe, 0x5f, 0x2d, 0xd8, 0x7a, 0x2d, 0xfe, 0xa4,
	0xea, 0x9c, 0x79, 0x0e, 0xbd, 0xf2, 0xbb, 0x09, 0xb9, 0x5e, 0xdb, 0x69, 0xbc, 0xc6, 0x0c, 0x6f,
	0xac, 0x58, 0x2d, 0xc0, 0x6b, 0xbe, 0x10, 0xe5, 0x4e, 0x5f, 0xf2, 0x56, 0x95, 0x63, 0x6d, 0xd9,
	0x93, 0xd2, 0xa3, 0xb5, 0xd7, 0x0d, 0x7f, 0xf2, 0x66, 0x5d, 0x48, 0x7c, 0xff, 0x3f, 0x01, 0x00,
	0x00, 0xff, 0xff, 0xe8, 0xd3, 0x6f, 0x69, 0x14, 0x17, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// InventoryServiceClient is the client API for InventoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type InventoryServiceClient interface {
	//冻结商品库存
	FreezeStock(ctx context.Context, in *FreezeRequest, opts ...grpc.CallOption) (*FreezeResponse, error)
	//释放商品库存
	FreedStock(ctx context.Context, in *FreedStockRequest, opts ...grpc.CallOption) (*FreedStockResponse, error)
	//导入第三方库存
	ImportThirdStock(ctx context.Context, in *ThirdInfoRequest, opts ...grpc.CallOption) (*ThirdInfoResponse, error)
	//根据商品id和库存id获取库存信息
	GetStockInfo(ctx context.Context, in *GetStockInfoRequest, opts ...grpc.CallOption) (*GetStockInfoResponse, error)
	//手动触发跑前置仓库存1111
	RunStockByManual(ctx context.Context, in *RunStockByManualRequest, opts ...grpc.CallOption) (*OmsSyncSotckResponse, error)
	// 获取 手动拉取 单仓库存 记录列表
	ManualStockRecordList(ctx context.Context, in *ManualStockRecordListRequest, opts ...grpc.CallOption) (*ManualStockRecordListResponse, error)
	IsOmsWarehouseCode(ctx context.Context, in *IsOmsWarehouseVo, opts ...grpc.CallOption) (*IsOmsWarehouseResp, error)
	//同步oms库存
	SyncOmsStock(ctx context.Context, in *SyncOmsStockRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	//差异同步oms库存
	SyncOmsDiffStock(ctx context.Context, in *SyncOmsDiffStockRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	InventoryTaskCall(ctx context.Context, in *TaskCommonRequest, opts ...grpc.CallOption) (*TaskCommonResponse, error)
	// 查询库存，组合商品不需要传子商品
	QueryStock(ctx context.Context, in *QueryStockReq, opts ...grpc.CallOption) (*QueryStockRes, error)
}

type inventoryServiceClient struct {
	cc *grpc.ClientConn
}

func NewInventoryServiceClient(cc *grpc.ClientConn) InventoryServiceClient {
	return &inventoryServiceClient{cc}
}

func (c *inventoryServiceClient) FreezeStock(ctx context.Context, in *FreezeRequest, opts ...grpc.CallOption) (*FreezeResponse, error) {
	out := new(FreezeResponse)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/FreezeStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) FreedStock(ctx context.Context, in *FreedStockRequest, opts ...grpc.CallOption) (*FreedStockResponse, error) {
	out := new(FreedStockResponse)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/FreedStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) ImportThirdStock(ctx context.Context, in *ThirdInfoRequest, opts ...grpc.CallOption) (*ThirdInfoResponse, error) {
	out := new(ThirdInfoResponse)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/ImportThirdStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) GetStockInfo(ctx context.Context, in *GetStockInfoRequest, opts ...grpc.CallOption) (*GetStockInfoResponse, error) {
	out := new(GetStockInfoResponse)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/GetStockInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) RunStockByManual(ctx context.Context, in *RunStockByManualRequest, opts ...grpc.CallOption) (*OmsSyncSotckResponse, error) {
	out := new(OmsSyncSotckResponse)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/RunStockByManual", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) ManualStockRecordList(ctx context.Context, in *ManualStockRecordListRequest, opts ...grpc.CallOption) (*ManualStockRecordListResponse, error) {
	out := new(ManualStockRecordListResponse)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/ManualStockRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) IsOmsWarehouseCode(ctx context.Context, in *IsOmsWarehouseVo, opts ...grpc.CallOption) (*IsOmsWarehouseResp, error) {
	out := new(IsOmsWarehouseResp)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/IsOmsWarehouseCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) SyncOmsStock(ctx context.Context, in *SyncOmsStockRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/SyncOmsStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) SyncOmsDiffStock(ctx context.Context, in *SyncOmsDiffStockRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/SyncOmsDiffStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) InventoryTaskCall(ctx context.Context, in *TaskCommonRequest, opts ...grpc.CallOption) (*TaskCommonResponse, error) {
	out := new(TaskCommonResponse)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/InventoryTaskCall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *inventoryServiceClient) QueryStock(ctx context.Context, in *QueryStockReq, opts ...grpc.CallOption) (*QueryStockRes, error) {
	out := new(QueryStockRes)
	err := c.cc.Invoke(ctx, "/proto.InventoryService/QueryStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InventoryServiceServer is the server API for InventoryService service.
type InventoryServiceServer interface {
	//冻结商品库存
	FreezeStock(context.Context, *FreezeRequest) (*FreezeResponse, error)
	//释放商品库存
	FreedStock(context.Context, *FreedStockRequest) (*FreedStockResponse, error)
	//导入第三方库存
	ImportThirdStock(context.Context, *ThirdInfoRequest) (*ThirdInfoResponse, error)
	//根据商品id和库存id获取库存信息
	GetStockInfo(context.Context, *GetStockInfoRequest) (*GetStockInfoResponse, error)
	//手动触发跑前置仓库存1111
	RunStockByManual(context.Context, *RunStockByManualRequest) (*OmsSyncSotckResponse, error)
	// 获取 手动拉取 单仓库存 记录列表
	ManualStockRecordList(context.Context, *ManualStockRecordListRequest) (*ManualStockRecordListResponse, error)
	IsOmsWarehouseCode(context.Context, *IsOmsWarehouseVo) (*IsOmsWarehouseResp, error)
	//同步oms库存
	SyncOmsStock(context.Context, *SyncOmsStockRequest) (*empty.Empty, error)
	//差异同步oms库存
	SyncOmsDiffStock(context.Context, *SyncOmsDiffStockRequest) (*empty.Empty, error)
	InventoryTaskCall(context.Context, *TaskCommonRequest) (*TaskCommonResponse, error)
	// 查询库存，组合商品不需要传子商品
	QueryStock(context.Context, *QueryStockReq) (*QueryStockRes, error)
}

// UnimplementedInventoryServiceServer can be embedded to have forward compatible implementations.
type UnimplementedInventoryServiceServer struct {
}

func (*UnimplementedInventoryServiceServer) FreezeStock(ctx context.Context, req *FreezeRequest) (*FreezeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FreezeStock not implemented")
}
func (*UnimplementedInventoryServiceServer) FreedStock(ctx context.Context, req *FreedStockRequest) (*FreedStockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FreedStock not implemented")
}
func (*UnimplementedInventoryServiceServer) ImportThirdStock(ctx context.Context, req *ThirdInfoRequest) (*ThirdInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportThirdStock not implemented")
}
func (*UnimplementedInventoryServiceServer) GetStockInfo(ctx context.Context, req *GetStockInfoRequest) (*GetStockInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStockInfo not implemented")
}
func (*UnimplementedInventoryServiceServer) RunStockByManual(ctx context.Context, req *RunStockByManualRequest) (*OmsSyncSotckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunStockByManual not implemented")
}
func (*UnimplementedInventoryServiceServer) ManualStockRecordList(ctx context.Context, req *ManualStockRecordListRequest) (*ManualStockRecordListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManualStockRecordList not implemented")
}
func (*UnimplementedInventoryServiceServer) IsOmsWarehouseCode(ctx context.Context, req *IsOmsWarehouseVo) (*IsOmsWarehouseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsOmsWarehouseCode not implemented")
}
func (*UnimplementedInventoryServiceServer) SyncOmsStock(ctx context.Context, req *SyncOmsStockRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncOmsStock not implemented")
}
func (*UnimplementedInventoryServiceServer) SyncOmsDiffStock(ctx context.Context, req *SyncOmsDiffStockRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncOmsDiffStock not implemented")
}
func (*UnimplementedInventoryServiceServer) InventoryTaskCall(ctx context.Context, req *TaskCommonRequest) (*TaskCommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InventoryTaskCall not implemented")
}
func (*UnimplementedInventoryServiceServer) QueryStock(ctx context.Context, req *QueryStockReq) (*QueryStockRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStock not implemented")
}

func RegisterInventoryServiceServer(s *grpc.Server, srv InventoryServiceServer) {
	s.RegisterService(&_InventoryService_serviceDesc, srv)
}

func _InventoryService_FreezeStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).FreezeStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/FreezeStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).FreezeStock(ctx, req.(*FreezeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_FreedStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreedStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).FreedStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/FreedStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).FreedStock(ctx, req.(*FreedStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_ImportThirdStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThirdInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).ImportThirdStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/ImportThirdStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).ImportThirdStock(ctx, req.(*ThirdInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_GetStockInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStockInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).GetStockInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/GetStockInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).GetStockInfo(ctx, req.(*GetStockInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_RunStockByManual_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunStockByManualRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).RunStockByManual(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/RunStockByManual",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).RunStockByManual(ctx, req.(*RunStockByManualRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_ManualStockRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManualStockRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).ManualStockRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/ManualStockRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).ManualStockRecordList(ctx, req.(*ManualStockRecordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_IsOmsWarehouseCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsOmsWarehouseVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).IsOmsWarehouseCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/IsOmsWarehouseCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).IsOmsWarehouseCode(ctx, req.(*IsOmsWarehouseVo))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_SyncOmsStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncOmsStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).SyncOmsStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/SyncOmsStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).SyncOmsStock(ctx, req.(*SyncOmsStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_SyncOmsDiffStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncOmsDiffStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).SyncOmsDiffStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/SyncOmsDiffStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).SyncOmsDiffStock(ctx, req.(*SyncOmsDiffStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_InventoryTaskCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskCommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).InventoryTaskCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/InventoryTaskCall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).InventoryTaskCall(ctx, req.(*TaskCommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InventoryService_QueryStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InventoryServiceServer).QueryStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.InventoryService/QueryStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InventoryServiceServer).QueryStock(ctx, req.(*QueryStockReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _InventoryService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "proto.InventoryService",
	HandlerType: (*InventoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FreezeStock",
			Handler:    _InventoryService_FreezeStock_Handler,
		},
		{
			MethodName: "FreedStock",
			Handler:    _InventoryService_FreedStock_Handler,
		},
		{
			MethodName: "ImportThirdStock",
			Handler:    _InventoryService_ImportThirdStock_Handler,
		},
		{
			MethodName: "GetStockInfo",
			Handler:    _InventoryService_GetStockInfo_Handler,
		},
		{
			MethodName: "RunStockByManual",
			Handler:    _InventoryService_RunStockByManual_Handler,
		},
		{
			MethodName: "ManualStockRecordList",
			Handler:    _InventoryService_ManualStockRecordList_Handler,
		},
		{
			MethodName: "IsOmsWarehouseCode",
			Handler:    _InventoryService_IsOmsWarehouseCode_Handler,
		},
		{
			MethodName: "SyncOmsStock",
			Handler:    _InventoryService_SyncOmsStock_Handler,
		},
		{
			MethodName: "SyncOmsDiffStock",
			Handler:    _InventoryService_SyncOmsDiffStock_Handler,
		},
		{
			MethodName: "InventoryTaskCall",
			Handler:    _InventoryService_InventoryTaskCall_Handler,
		},
		{
			MethodName: "QueryStock",
			Handler:    _InventoryService_QueryStock_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ic/inventorycenter.proto",
}

// ZilongServiceClient is the client API for ZilongService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ZilongServiceClient interface {
	//提供给子龙系统实时推送库存接口
	// rpc ProvideSyncStock (ProvideSyncStockRequest) returns
	// (ProvideSyncStockResponse);
	//根据流水号查询结果
	QueryStockTask(ctx context.Context, in *QueryStockTaskRequest, opts ...grpc.CallOption) (*QueryStockTaskResponse, error)
	//插入参数和流水号记录
	AddStockTask(ctx context.Context, in *AddStockTaskRequest, opts ...grpc.CallOption) (*AddStockTaskResponse, error)
}

type zilongServiceClient struct {
	cc *grpc.ClientConn
}

func NewZilongServiceClient(cc *grpc.ClientConn) ZilongServiceClient {
	return &zilongServiceClient{cc}
}

func (c *zilongServiceClient) QueryStockTask(ctx context.Context, in *QueryStockTaskRequest, opts ...grpc.CallOption) (*QueryStockTaskResponse, error) {
	out := new(QueryStockTaskResponse)
	err := c.cc.Invoke(ctx, "/proto.ZilongService/QueryStockTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *zilongServiceClient) AddStockTask(ctx context.Context, in *AddStockTaskRequest, opts ...grpc.CallOption) (*AddStockTaskResponse, error) {
	out := new(AddStockTaskResponse)
	err := c.cc.Invoke(ctx, "/proto.ZilongService/AddStockTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ZilongServiceServer is the server API for ZilongService service.
type ZilongServiceServer interface {
	//提供给子龙系统实时推送库存接口
	// rpc ProvideSyncStock (ProvideSyncStockRequest) returns
	// (ProvideSyncStockResponse);
	//根据流水号查询结果
	QueryStockTask(context.Context, *QueryStockTaskRequest) (*QueryStockTaskResponse, error)
	//插入参数和流水号记录
	AddStockTask(context.Context, *AddStockTaskRequest) (*AddStockTaskResponse, error)
}

// UnimplementedZilongServiceServer can be embedded to have forward compatible implementations.
type UnimplementedZilongServiceServer struct {
}

func (*UnimplementedZilongServiceServer) QueryStockTask(ctx context.Context, req *QueryStockTaskRequest) (*QueryStockTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStockTask not implemented")
}
func (*UnimplementedZilongServiceServer) AddStockTask(ctx context.Context, req *AddStockTaskRequest) (*AddStockTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddStockTask not implemented")
}

func RegisterZilongServiceServer(s *grpc.Server, srv ZilongServiceServer) {
	s.RegisterService(&_ZilongService_serviceDesc, srv)
}

func _ZilongService_QueryStockTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStockTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZilongServiceServer).QueryStockTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.ZilongService/QueryStockTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZilongServiceServer).QueryStockTask(ctx, req.(*QueryStockTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ZilongService_AddStockTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddStockTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZilongServiceServer).AddStockTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.ZilongService/AddStockTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZilongServiceServer).AddStockTask(ctx, req.(*AddStockTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ZilongService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "proto.ZilongService",
	HandlerType: (*ZilongServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryStockTask",
			Handler:    _ZilongService_QueryStockTask_Handler,
		},
		{
			MethodName: "AddStockTask",
			Handler:    _ZilongService_AddStockTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ic/inventorycenter.proto",
}
