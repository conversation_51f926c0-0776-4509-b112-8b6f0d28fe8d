syntax = "proto3";
import "google/protobuf/empty.proto";
package proto;
option go_package = "ic";

service InventoryService {
  //冻结商品库存
  rpc FreezeStock(FreezeRequest) returns (FreezeResponse);
  //释放商品库存
  rpc FreedStock(FreedStockRequest) returns (FreedStockResponse);
  //导入第三方库存
  rpc ImportThirdStock(ThirdInfoRequest) returns (ThirdInfoResponse);
  //根据商品id和库存id获取库存信息
  rpc GetStockInfo(GetStockInfoRequest) returns (GetStockInfoResponse);
  //手动触发跑前置仓库存1111
  rpc RunStockByManual(RunStockByManualRequest) returns (OmsSyncSotckResponse);
  // 获取 手动拉取 单仓库存 记录列表
  rpc ManualStockRecordList(ManualStockRecordListRequest)
      returns (ManualStockRecordListResponse);
  rpc IsOmsWarehouseCode(IsOmsWarehouseVo) returns (IsOmsWarehouseResp);
  //同步oms库存
  rpc SyncOmsStock(SyncOmsStockRequest) returns (google.protobuf.Empty);
  //差异同步oms库存
  rpc SyncOmsDiffStock(SyncOmsDiffStockRequest) returns (google.protobuf.Empty);

  rpc InventoryTaskCall(TaskCommonRequest) returns (TaskCommonResponse);
  // 查询库存，组合商品不需要传子商品
  rpc QueryStock(QueryStockReq) returns (QueryStockRes);
}

service ZilongService {
  //提供给子龙系统实时推送库存接口
  // rpc ProvideSyncStock (ProvideSyncStockRequest) returns
  // (ProvideSyncStockResponse);
  //根据流水号查询结果
  rpc QueryStockTask(QueryStockTaskRequest) returns (QueryStockTaskResponse);
  //插入参数和流水号记录
  rpc AddStockTask(AddStockTaskRequest) returns (AddStockTaskResponse);
}


message TaskCommonRequest {
  // 1 SyncA8VirtualGoodsQtyForQZC; 2 SyncA8VirtualGoodsQtyForQZCVirtual; 3 ToProvideSyncStock; 0 all
  int32 type = 1;
}

message TaskCommonResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message ManualStockRecordListRequest {
  string create_time = 1;
  int32 page_index = 2;
  int32 page_size = 3;
}
message ManualStockRecordListResponse {
  string msg = 1;
  int32 total = 2;
  repeated ManualStockRecord data = 3;
}

message ManualStockRecord {
  int32 id = 1;
  string operate_id = 2;
  string operator = 3;
  string file_url = 4;
  string create_time = 5;
}
// 手动 单仓拉库存 请求参数
message RunStockByManualRequest {
  // 仓库编号
  string warehouse_code = 1;
  // a8货号 ，多个用英文逗号隔开
  string goods_code = 2;
}
message OmsSyncSotckRequest {
  //商品集合
  repeated OmsSyncSotck data = 1;
}

message OmsSyncSotck {
  //仓库代码
  string warehouse_code = 1;
  //商品集合
  repeated OmsGoodsList goodslist = 2;
}

message OmsSyncSotckResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
}

message OmsGoodsList {
  //商品spu码
  string spu = 1;
  //商品sku码
  string sku = 2;
  //可用库存
  int32 stock = 3;
  //仓库总库存
  int32 allstock = 4;
  //货号
  string thirdsku = 5;
}

message AddStockTaskRequest {
  string request = 1;
  string serial_number = 2;
  int32 stock_type = 3;
}

message AddStockTaskResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

//请求参数
message QueryStockTaskRequest { string serial_number = 1; }
//响应结果
message QueryStockTaskResponse {
  int32 id = 1;
  string request = 2;
  string serial_number = 3;
  int32 status = 4;
  string create_date = 5;
}

// GetStockInfoBySkuCode--请求参数
/*message GetStockInfoBySkuCodeRequest{
    //操作类型
    int32 action_type = 1;
    repeated SkuCodeInfo paramsInfo=2;
}*/

message SkuCodeInfo {
  //财务编码
  string finance_code = 1;
  string sku = 2;
  string spu = 3;
  //子龙货号
  string third_skuid = 4;
  //子龙门店id
  int32 zl_id = 5;
  int32 erp_id = 6;
  //指定仓库查询，可以多个仓库，前置仓和前置仓虚拟仓
  repeated int32 stock_warehouse = 7;
}

// GetStockInfoBySkuCode--响应参数
/*message GetStockInfoBySkuCodeResponse {
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    //错误信息
    string error = 3;

    map<string, int32> result = 4;
}*/

//提供给子龙系统实时推送库存接口--请求参数
message ProvideSyncStockRequest {
  repeated GoodsStockList GoodsStock = 1;
  //符合条件的仓库ID集合
  repeated Warehouse PassWarehouse = 2;
}

message Warehouse {
  int32 Id = 1;
  string Code = 2;
  int32 Category = 3;
  int32 Status = 4;
}

message GoodsStockList {
  //子龙门店id，systemid
  string zilong_id = 1;
  //商品信息
  repeated GoodsStock GoodsList = 2;
}

message GoodsStock {
  //商品id
  string GoodsId = 2;
  //库存数量
  int32 Stock = 3;
}

//提供给子龙系统实时推送库存接口--响应参数
message ProvideSyncStockResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message ThirdInfoRequest {
  //仓库id
  int32 warehouse_id = 1;
  repeated ThirdInfo details = 2;
}

message ThirdInfoResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

message ThirdInfo {
  //第三方spu/sku id
  string third_spu_sku_id = 1;
  //库存
  int32 stock = 2;
}

//商品仓库库存信息
message Goods_list {
  //商品id
  string goodsid = 1;
  //商品库存
  int32 stock = 2;
  //商品库存
  int32 newstock = 3;
  //是否足够
  bool enough = 4;
  //库存来源
  int32 goods_sku_type = 5;
}

//冻结库存请求参数
message FreezeRequest {
  //来源：1电商，2本地生活 3：互联网医疗
  int32 source = 1;
  //用户id
  string member_id = 2;
  //订单id
  string order_id = 3;
  //商品id
  repeated OrderGoodsInfo goods_list = 4;
}

//订单商品信息
message OrderGoodsInfo {
  //商品id
  string goods_id = 1;
  //扣减库存数量
  int32 number = 2;
  //仓库id
  int32 warehouse_id = 3;
  //药品仓类型：0:默认否, 1:巨星药品仓
  int32 warehouse_type = 4;
}

//冻结库存响应参数
message FreezeResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

//释放库存请求参数
message FreedStockRequest {
  //来源：1电商，2本地生活 3：互联网医疗
  int32 source = 1;
  //订单id
  string order_id = 2;
  //商品信息
  repeated OrderGoodsInfo goods_list = 3;
  // repeated GoodsInfo goods_list=2;
  //订单状态 0：未支付 1：已支付
  int32 status = 4;
}

message GoodsInfo {
  //商品id
  string goods_id = 1;
  //扣减库存数量
  int32 number = 2;
}
//释放库存响应参数
message FreedStockResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

//商品和库存信息
message ProductsAndStock {
  //仓库id
  int32 warehouse_id = 1;
  //商品id
  string product_id = 2;
}
message GetStockInfoRequest {
  //来源：1电商，2本地生活 3：互联网医疗
  int32 source = 1;
  //是否需要拉取子龙库存，只针对子龙使用
  int32 is_need_pull = 2;
  repeated ProductsInfo ProductsInfo = 3;
  //指定仓库查询，可以多个仓库，前置仓和前置仓虚拟仓
  repeated int32 stockwarehouse = 4;
}

message GetStockInfoResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //返回信息
  GetStockInfoRequest goods_info = 4;
  //按仓库返回库存信息
  repeated ProductsInWarehouse goods_in_warehouse = 5;
}

//分仓库返回的库存信息
message ProductsInWarehouse {
  //商品skuid
  int32 sku_id = 1;
  //库存信息
  int32 stock = 2;
  //财务编码
  int32 warehouse_id = 3;
}

//商品和库存信息
message ProductsInfo {
  //是否组合：1：组合，2：非组合
  int32 type = 1;
  //商品skuid
  int32 sku_id = 2;
  //是否全部虚拟商品 0:否 1:是
  int32 is_all_virtual = 3;
  //组合商品的商品信息
  repeated ChildRen child_ren = 4;
  //库存信息
  int32 stock = 5;
  //财务编码
  repeated string finance_code = 6;
}

message ChildRen {
  int32 sku_id = 1;
  //规则
  int32 rule_num = 2;
  //是否为虚拟 0:不是 1：是虚拟
  int32 is_virtual = 3;
  // 0不是药品仓 1药品仓
  int32 stockwarehouse = 7;
}
message IsOmsWarehouseVo {

  //    仓库的code
  string Warehouse = 1;
}

message IsOmsWarehouseResp {

  //    是否是oms true是 false 否
  bool IsOms = 1;
  string Msg = 2;
  int32 Code = 3;
}

message SyncOmsStockRequest {
  //仓库编码
  string WarehouseCode = 1;
  // 需要同步的库存
  repeated SkuStock List = 2;
}
message SkuStock {
  //第三方货号
  string ThirdSkuId = 1;
  //库存
  int32 Stock = 2;
}

message SyncOmsDiffStockRequest {
  //仓库编码
  string WarehouseCode = 1;
  // 需要同步的库存
  repeated SkuStock List = 2;
}

message QueryStockReq {
  // 商品sku
  repeated int32 sku_ids = 1;
  // 渠道id 1-阿闻到家 2-美团 3-饿了么 4-京东到家 5-阿闻电商 6-门店 7-百度 8-H5, 9-互联网医疗 10-自提
  int32 channel_id = 2;
  // 门店财务编码，channel_id = 5 时不用传
  string shop_id = 3;
}

message QueryStockRes {
  int32 code = 1;
  string message = 2;
  // 库存，map，key为skuid，value为库存
  map<int32, int32> stock = 3;
}