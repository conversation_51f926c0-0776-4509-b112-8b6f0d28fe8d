// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mm/member.proto

package mm

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type MemberMergeRequest struct {
	MemberPhone          string   `protobuf:"bytes,1,opt,name=memberPhone,proto3" json:"memberPhone"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberMergeRequest) Reset()         { *m = MemberMergeRequest{} }
func (m *MemberMergeRequest) String() string { return proto.CompactTextString(m) }
func (*MemberMergeRequest) ProtoMessage()    {}
func (*MemberMergeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{0}
}

func (m *MemberMergeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberMergeRequest.Unmarshal(m, b)
}
func (m *MemberMergeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberMergeRequest.Marshal(b, m, deterministic)
}
func (m *MemberMergeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberMergeRequest.Merge(m, src)
}
func (m *MemberMergeRequest) XXX_Size() int {
	return xxx_messageInfo_MemberMergeRequest.Size(m)
}
func (m *MemberMergeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberMergeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MemberMergeRequest proto.InternalMessageInfo

func (m *MemberMergeRequest) GetMemberPhone() string {
	if m != nil {
		return m.MemberPhone
	}
	return ""
}

type StoreMemberGoodBrowseInfoRequest struct {
	MemberId             string   `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	SkuId                int32    `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	SpuId                int32    `protobuf:"varint,3,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	OrgId                int32    `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreMemberGoodBrowseInfoRequest) Reset()         { *m = StoreMemberGoodBrowseInfoRequest{} }
func (m *StoreMemberGoodBrowseInfoRequest) String() string { return proto.CompactTextString(m) }
func (*StoreMemberGoodBrowseInfoRequest) ProtoMessage()    {}
func (*StoreMemberGoodBrowseInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{1}
}

func (m *StoreMemberGoodBrowseInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreMemberGoodBrowseInfoRequest.Unmarshal(m, b)
}
func (m *StoreMemberGoodBrowseInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreMemberGoodBrowseInfoRequest.Marshal(b, m, deterministic)
}
func (m *StoreMemberGoodBrowseInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreMemberGoodBrowseInfoRequest.Merge(m, src)
}
func (m *StoreMemberGoodBrowseInfoRequest) XXX_Size() int {
	return xxx_messageInfo_StoreMemberGoodBrowseInfoRequest.Size(m)
}
func (m *StoreMemberGoodBrowseInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreMemberGoodBrowseInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StoreMemberGoodBrowseInfoRequest proto.InternalMessageInfo

func (m *StoreMemberGoodBrowseInfoRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *StoreMemberGoodBrowseInfoRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *StoreMemberGoodBrowseInfoRequest) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *StoreMemberGoodBrowseInfoRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type StoreMemberGoodBrowseInfoResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息提示
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreMemberGoodBrowseInfoResponse) Reset()         { *m = StoreMemberGoodBrowseInfoResponse{} }
func (m *StoreMemberGoodBrowseInfoResponse) String() string { return proto.CompactTextString(m) }
func (*StoreMemberGoodBrowseInfoResponse) ProtoMessage()    {}
func (*StoreMemberGoodBrowseInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{2}
}

func (m *StoreMemberGoodBrowseInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreMemberGoodBrowseInfoResponse.Unmarshal(m, b)
}
func (m *StoreMemberGoodBrowseInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreMemberGoodBrowseInfoResponse.Marshal(b, m, deterministic)
}
func (m *StoreMemberGoodBrowseInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreMemberGoodBrowseInfoResponse.Merge(m, src)
}
func (m *StoreMemberGoodBrowseInfoResponse) XXX_Size() int {
	return xxx_messageInfo_StoreMemberGoodBrowseInfoResponse.Size(m)
}
func (m *StoreMemberGoodBrowseInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreMemberGoodBrowseInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StoreMemberGoodBrowseInfoResponse proto.InternalMessageInfo

func (m *StoreMemberGoodBrowseInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StoreMemberGoodBrowseInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GetMemberGoodBrowseListRequest struct {
	MemberId             string   `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMemberGoodBrowseListRequest) Reset()         { *m = GetMemberGoodBrowseListRequest{} }
func (m *GetMemberGoodBrowseListRequest) String() string { return proto.CompactTextString(m) }
func (*GetMemberGoodBrowseListRequest) ProtoMessage()    {}
func (*GetMemberGoodBrowseListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{3}
}

func (m *GetMemberGoodBrowseListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberGoodBrowseListRequest.Unmarshal(m, b)
}
func (m *GetMemberGoodBrowseListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberGoodBrowseListRequest.Marshal(b, m, deterministic)
}
func (m *GetMemberGoodBrowseListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberGoodBrowseListRequest.Merge(m, src)
}
func (m *GetMemberGoodBrowseListRequest) XXX_Size() int {
	return xxx_messageInfo_GetMemberGoodBrowseListRequest.Size(m)
}
func (m *GetMemberGoodBrowseListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberGoodBrowseListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberGoodBrowseListRequest proto.InternalMessageInfo

func (m *GetMemberGoodBrowseListRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *GetMemberGoodBrowseListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type GetMemberGoodBrowseListResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息提示
	Message              string                  `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*MemberGoodBrowseInfo `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetMemberGoodBrowseListResponse) Reset()         { *m = GetMemberGoodBrowseListResponse{} }
func (m *GetMemberGoodBrowseListResponse) String() string { return proto.CompactTextString(m) }
func (*GetMemberGoodBrowseListResponse) ProtoMessage()    {}
func (*GetMemberGoodBrowseListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{4}
}

func (m *GetMemberGoodBrowseListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberGoodBrowseListResponse.Unmarshal(m, b)
}
func (m *GetMemberGoodBrowseListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberGoodBrowseListResponse.Marshal(b, m, deterministic)
}
func (m *GetMemberGoodBrowseListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberGoodBrowseListResponse.Merge(m, src)
}
func (m *GetMemberGoodBrowseListResponse) XXX_Size() int {
	return xxx_messageInfo_GetMemberGoodBrowseListResponse.Size(m)
}
func (m *GetMemberGoodBrowseListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberGoodBrowseListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberGoodBrowseListResponse proto.InternalMessageInfo

func (m *GetMemberGoodBrowseListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetMemberGoodBrowseListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetMemberGoodBrowseListResponse) GetData() []*MemberGoodBrowseInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type MemberGoodBrowseInfo struct {
	CreateTime  string `protobuf:"bytes,1,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	SkuId       int32  `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	SpuId       int32  `protobuf:"varint,3,opt,name=spu_id,json=spuId,proto3" json:"spu_id"`
	ProductName string `protobuf:"bytes,4,opt,name=product_name,json=productName,proto3" json:"product_name"`
	//商品价格 单位分
	Price int32  `protobuf:"varint,5,opt,name=price,proto3" json:"price"`
	Spec  string `protobuf:"bytes,6,opt,name=spec,proto3" json:"spec"`
	Pic   string `protobuf:"bytes,7,opt,name=pic,proto3" json:"pic"`
	//上下架状态
	UpDown              int32 `protobuf:"varint,8,opt,name=up_down,json=upDown,proto3" json:"up_down"`
	GoodsPromotionType  int32 `protobuf:"varint,9,opt,name=goods_promotion_type,json=goodsPromotionType,proto3" json:"goods_promotion_type"`
	GoodsPromotionPrice int32 `protobuf:"varint,10,opt,name=goods_promotion_price,json=goodsPromotionPrice,proto3" json:"goods_promotion_price"`
	// 会员价
	MemberPrice1 int32 `protobuf:"varint,11,opt,name=member_price1,json=memberPrice1,proto3" json:"member_price1"`
	// 新人专享价格
	NewPeoplePrice int32 `protobuf:"varint,12,opt,name=new_people_price,json=newPeoplePrice,proto3" json:"new_people_price"`
	// 会员价是否生效
	IsMemberPrice int32 `protobuf:"varint,13,opt,name=is_member_price,json=isMemberPrice,proto3" json:"is_member_price"`
	// 日期
	Date                 string   `protobuf:"bytes,14,opt,name=date,proto3" json:"date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberGoodBrowseInfo) Reset()         { *m = MemberGoodBrowseInfo{} }
func (m *MemberGoodBrowseInfo) String() string { return proto.CompactTextString(m) }
func (*MemberGoodBrowseInfo) ProtoMessage()    {}
func (*MemberGoodBrowseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{5}
}

func (m *MemberGoodBrowseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberGoodBrowseInfo.Unmarshal(m, b)
}
func (m *MemberGoodBrowseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberGoodBrowseInfo.Marshal(b, m, deterministic)
}
func (m *MemberGoodBrowseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberGoodBrowseInfo.Merge(m, src)
}
func (m *MemberGoodBrowseInfo) XXX_Size() int {
	return xxx_messageInfo_MemberGoodBrowseInfo.Size(m)
}
func (m *MemberGoodBrowseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberGoodBrowseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MemberGoodBrowseInfo proto.InternalMessageInfo

func (m *MemberGoodBrowseInfo) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *MemberGoodBrowseInfo) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *MemberGoodBrowseInfo) GetSpuId() int32 {
	if m != nil {
		return m.SpuId
	}
	return 0
}

func (m *MemberGoodBrowseInfo) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *MemberGoodBrowseInfo) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *MemberGoodBrowseInfo) GetSpec() string {
	if m != nil {
		return m.Spec
	}
	return ""
}

func (m *MemberGoodBrowseInfo) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *MemberGoodBrowseInfo) GetUpDown() int32 {
	if m != nil {
		return m.UpDown
	}
	return 0
}

func (m *MemberGoodBrowseInfo) GetGoodsPromotionType() int32 {
	if m != nil {
		return m.GoodsPromotionType
	}
	return 0
}

func (m *MemberGoodBrowseInfo) GetGoodsPromotionPrice() int32 {
	if m != nil {
		return m.GoodsPromotionPrice
	}
	return 0
}

func (m *MemberGoodBrowseInfo) GetMemberPrice1() int32 {
	if m != nil {
		return m.MemberPrice1
	}
	return 0
}

func (m *MemberGoodBrowseInfo) GetNewPeoplePrice() int32 {
	if m != nil {
		return m.NewPeoplePrice
	}
	return 0
}

func (m *MemberGoodBrowseInfo) GetIsMemberPrice() int32 {
	if m != nil {
		return m.IsMemberPrice
	}
	return 0
}

func (m *MemberGoodBrowseInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

type ClearMemberGoodBrowseRequest struct {
	//用户id
	MemberId             string   `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearMemberGoodBrowseRequest) Reset()         { *m = ClearMemberGoodBrowseRequest{} }
func (m *ClearMemberGoodBrowseRequest) String() string { return proto.CompactTextString(m) }
func (*ClearMemberGoodBrowseRequest) ProtoMessage()    {}
func (*ClearMemberGoodBrowseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{6}
}

func (m *ClearMemberGoodBrowseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearMemberGoodBrowseRequest.Unmarshal(m, b)
}
func (m *ClearMemberGoodBrowseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearMemberGoodBrowseRequest.Marshal(b, m, deterministic)
}
func (m *ClearMemberGoodBrowseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearMemberGoodBrowseRequest.Merge(m, src)
}
func (m *ClearMemberGoodBrowseRequest) XXX_Size() int {
	return xxx_messageInfo_ClearMemberGoodBrowseRequest.Size(m)
}
func (m *ClearMemberGoodBrowseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearMemberGoodBrowseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ClearMemberGoodBrowseRequest proto.InternalMessageInfo

func (m *ClearMemberGoodBrowseRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

type ClearMemberGoodBrowseResponse struct {
	//code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息提示
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearMemberGoodBrowseResponse) Reset()         { *m = ClearMemberGoodBrowseResponse{} }
func (m *ClearMemberGoodBrowseResponse) String() string { return proto.CompactTextString(m) }
func (*ClearMemberGoodBrowseResponse) ProtoMessage()    {}
func (*ClearMemberGoodBrowseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{7}
}

func (m *ClearMemberGoodBrowseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearMemberGoodBrowseResponse.Unmarshal(m, b)
}
func (m *ClearMemberGoodBrowseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearMemberGoodBrowseResponse.Marshal(b, m, deterministic)
}
func (m *ClearMemberGoodBrowseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearMemberGoodBrowseResponse.Merge(m, src)
}
func (m *ClearMemberGoodBrowseResponse) XXX_Size() int {
	return xxx_messageInfo_ClearMemberGoodBrowseResponse.Size(m)
}
func (m *ClearMemberGoodBrowseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearMemberGoodBrowseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ClearMemberGoodBrowseResponse proto.InternalMessageInfo

func (m *ClearMemberGoodBrowseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ClearMemberGoodBrowseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type MemberMergeErrorResponse struct {
	//code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息提示
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberMergeErrorResponse) Reset()         { *m = MemberMergeErrorResponse{} }
func (m *MemberMergeErrorResponse) String() string { return proto.CompactTextString(m) }
func (*MemberMergeErrorResponse) ProtoMessage()    {}
func (*MemberMergeErrorResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{8}
}

func (m *MemberMergeErrorResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberMergeErrorResponse.Unmarshal(m, b)
}
func (m *MemberMergeErrorResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberMergeErrorResponse.Marshal(b, m, deterministic)
}
func (m *MemberMergeErrorResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberMergeErrorResponse.Merge(m, src)
}
func (m *MemberMergeErrorResponse) XXX_Size() int {
	return xxx_messageInfo_MemberMergeErrorResponse.Size(m)
}
func (m *MemberMergeErrorResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberMergeErrorResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MemberMergeErrorResponse proto.InternalMessageInfo

func (m *MemberMergeErrorResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MemberMergeErrorResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type MemberMergeResponse struct {
	//code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息提示
	Message              string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*MemberMergelList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MemberMergeResponse) Reset()         { *m = MemberMergeResponse{} }
func (m *MemberMergeResponse) String() string { return proto.CompactTextString(m) }
func (*MemberMergeResponse) ProtoMessage()    {}
func (*MemberMergeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{9}
}

func (m *MemberMergeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberMergeResponse.Unmarshal(m, b)
}
func (m *MemberMergeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberMergeResponse.Marshal(b, m, deterministic)
}
func (m *MemberMergeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberMergeResponse.Merge(m, src)
}
func (m *MemberMergeResponse) XXX_Size() int {
	return xxx_messageInfo_MemberMergeResponse.Size(m)
}
func (m *MemberMergeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberMergeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MemberMergeResponse proto.InternalMessageInfo

func (m *MemberMergeResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MemberMergeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MemberMergeResponse) GetData() []*MemberMergelList {
	if m != nil {
		return m.Data
	}
	return nil
}

//用户合并记录列表
type MemberMergelList struct {
	//处理时间
	MmlDealtime int32 `protobuf:"varint,1,opt,name=mml_dealtime,json=mmlDealtime,proto3" json:"mml_dealtime"`
	//旧手机号
	MmlOldMobile string `protobuf:"bytes,2,opt,name=mml_old_mobile,json=mmlOldMobile,proto3" json:"mml_old_mobile"`
	//新手机号
	MmlNewMobile string `protobuf:"bytes,3,opt,name=mml_new_mobile,json=mmlNewMobile,proto3" json:"mml_new_mobile"`
	//处理信息
	MmlNotes string `protobuf:"bytes,4,opt,name=mml_notes,json=mmlNotes,proto3" json:"mml_notes"`
	//处理人
	CreateUser string `protobuf:"bytes,5,opt,name=create_user,json=createUser,proto3" json:"create_user"`
	//申请人
	Applicant string `protobuf:"bytes,6,opt,name=applicant,proto3" json:"applicant"`
	//部门
	Department string `protobuf:"bytes,7,opt,name=department,proto3" json:"department"`
	//申请时间
	ApplicationDate string `protobuf:"bytes,8,opt,name=application_date,json=applicationDate,proto3" json:"application_date"`
	//审批记录
	ApprovalRecord       string   `protobuf:"bytes,9,opt,name=approval_record,json=approvalRecord,proto3" json:"approval_record"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberMergelList) Reset()         { *m = MemberMergelList{} }
func (m *MemberMergelList) String() string { return proto.CompactTextString(m) }
func (*MemberMergelList) ProtoMessage()    {}
func (*MemberMergelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_41b1d3cfd92c3772, []int{10}
}

func (m *MemberMergelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberMergelList.Unmarshal(m, b)
}
func (m *MemberMergelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberMergelList.Marshal(b, m, deterministic)
}
func (m *MemberMergelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberMergelList.Merge(m, src)
}
func (m *MemberMergelList) XXX_Size() int {
	return xxx_messageInfo_MemberMergelList.Size(m)
}
func (m *MemberMergelList) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberMergelList.DiscardUnknown(m)
}

var xxx_messageInfo_MemberMergelList proto.InternalMessageInfo

func (m *MemberMergelList) GetMmlDealtime() int32 {
	if m != nil {
		return m.MmlDealtime
	}
	return 0
}

func (m *MemberMergelList) GetMmlOldMobile() string {
	if m != nil {
		return m.MmlOldMobile
	}
	return ""
}

func (m *MemberMergelList) GetMmlNewMobile() string {
	if m != nil {
		return m.MmlNewMobile
	}
	return ""
}

func (m *MemberMergelList) GetMmlNotes() string {
	if m != nil {
		return m.MmlNotes
	}
	return ""
}

func (m *MemberMergelList) GetCreateUser() string {
	if m != nil {
		return m.CreateUser
	}
	return ""
}

func (m *MemberMergelList) GetApplicant() string {
	if m != nil {
		return m.Applicant
	}
	return ""
}

func (m *MemberMergelList) GetDepartment() string {
	if m != nil {
		return m.Department
	}
	return ""
}

func (m *MemberMergelList) GetApplicationDate() string {
	if m != nil {
		return m.ApplicationDate
	}
	return ""
}

func (m *MemberMergelList) GetApprovalRecord() string {
	if m != nil {
		return m.ApprovalRecord
	}
	return ""
}

func init() {
	proto.RegisterType((*MemberMergeRequest)(nil), "mm.MemberMergeRequest")
	proto.RegisterType((*StoreMemberGoodBrowseInfoRequest)(nil), "mm.StoreMemberGoodBrowseInfoRequest")
	proto.RegisterType((*StoreMemberGoodBrowseInfoResponse)(nil), "mm.StoreMemberGoodBrowseInfoResponse")
	proto.RegisterType((*GetMemberGoodBrowseListRequest)(nil), "mm.GetMemberGoodBrowseListRequest")
	proto.RegisterType((*GetMemberGoodBrowseListResponse)(nil), "mm.GetMemberGoodBrowseListResponse")
	proto.RegisterType((*MemberGoodBrowseInfo)(nil), "mm.MemberGoodBrowseInfo")
	proto.RegisterType((*ClearMemberGoodBrowseRequest)(nil), "mm.ClearMemberGoodBrowseRequest")
	proto.RegisterType((*ClearMemberGoodBrowseResponse)(nil), "mm.ClearMemberGoodBrowseResponse")
	proto.RegisterType((*MemberMergeErrorResponse)(nil), "mm.MemberMergeErrorResponse")
	proto.RegisterType((*MemberMergeResponse)(nil), "mm.MemberMergeResponse")
	proto.RegisterType((*MemberMergelList)(nil), "mm.MemberMergelList")
}

func init() { proto.RegisterFile("mm/member.proto", fileDescriptor_41b1d3cfd92c3772) }

var fileDescriptor_41b1d3cfd92c3772 = []byte{
	// 787 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x55, 0x41, 0x6f, 0x1b, 0x45,
	0x14, 0x56, 0xec, 0xd8, 0xb1, 0x9f, 0x13, 0xc7, 0x9a, 0xda, 0x64, 0x31, 0xa1, 0x75, 0xb6, 0x05,
	0x8c, 0x84, 0x12, 0x08, 0x12, 0x17, 0x6e, 0x10, 0xd4, 0x46, 0xc2, 0xc5, 0x6c, 0xc3, 0x0d, 0x69,
	0x35, 0xde, 0x7d, 0x38, 0xab, 0xee, 0xec, 0x0c, 0x33, 0xe3, 0x5a, 0x3d, 0x70, 0xe3, 0xc4, 0x8f,
	0x43, 0xe2, 0x1f, 0xa1, 0x79, 0x3b, 0x4e, 0x6c, 0x13, 0xbb, 0xc5, 0xb7, 0xdd, 0x6f, 0xbe, 0xef,
	0xdb, 0xb7, 0xf3, 0xde, 0x37, 0x03, 0xc7, 0x42, 0x5c, 0x08, 0x14, 0x13, 0xd4, 0xe7, 0x4a, 0x4b,
	0x2b, 0x59, 0x45, 0x88, 0xfe, 0xe9, 0x54, 0xca, 0x69, 0x8e, 0x17, 0x5c, 0x65, 0x17, 0xbc, 0x28,
	0xa4, 0xe5, 0x36, 0x93, 0x85, 0x29, 0x19, 0xe1, 0x37, 0xc0, 0x46, 0xa4, 0x18, 0xa1, 0x9e, 0x62,
	0x84, 0xbf, 0xcf, 0xd0, 0x58, 0x36, 0x80, 0x56, 0xe9, 0x33, 0xbe, 0x95, 0x05, 0x06, 0x7b, 0x83,
	0xbd, 0x61, 0x33, 0x5a, 0x86, 0xc2, 0x3f, 0xf7, 0x60, 0xf0, 0xca, 0x4a, 0x8d, 0xa5, 0xfa, 0xb9,
	0x94, 0xe9, 0x77, 0x5a, 0xce, 0x0d, 0x5e, 0x17, 0xbf, 0xc9, 0x85, 0xcd, 0x47, 0xd0, 0x2c, 0x35,
	0x71, 0x96, 0x7a, 0x93, 0x46, 0x09, 0x5c, 0xa7, 0xac, 0x07, 0x75, 0xf3, 0x7a, 0xe6, 0x56, 0x2a,
	0x83, 0xbd, 0x61, 0x2d, 0xaa, 0x99, 0xd7, 0x33, 0x0f, 0x2b, 0x82, 0xab, 0x1e, 0x56, 0x1e, 0x96,
	0x7a, 0xea, 0xe0, 0xfd, 0x12, 0x96, 0x7a, 0x7a, 0x9d, 0x86, 0x3f, 0xc3, 0xd9, 0x96, 0x2a, 0x8c,
	0x92, 0x85, 0x41, 0xc6, 0x60, 0x3f, 0x91, 0x69, 0xf9, 0x1b, 0xb5, 0x88, 0x9e, 0x59, 0x00, 0x07,
	0x02, 0x8d, 0xe1, 0x53, 0xa4, 0xcf, 0x37, 0xa3, 0xc5, 0x6b, 0x78, 0x03, 0x8f, 0x9f, 0xa3, 0x5d,
	0x37, 0xfc, 0x31, 0x33, 0xf6, 0x7d, 0x7f, 0xcb, 0x17, 0x5a, 0x59, 0x2e, 0xf4, 0x0f, 0x78, 0xb2,
	0xd1, 0x75, 0x97, 0x32, 0xd9, 0x17, 0xb0, 0x9f, 0x72, 0xcb, 0x83, 0xea, 0xa0, 0x3a, 0x6c, 0x5d,
	0x06, 0xe7, 0x42, 0x9c, 0x3f, 0xb8, 0x09, 0xc4, 0x0a, 0xff, 0xae, 0x42, 0xf7, 0xa1, 0x65, 0xf6,
	0x04, 0x5a, 0x89, 0x46, 0x6e, 0x31, 0xb6, 0x99, 0x58, 0x74, 0x1a, 0x4a, 0xe8, 0x26, 0x13, 0xf8,
	0x3f, 0xdb, 0x74, 0x06, 0x87, 0x4a, 0xcb, 0x74, 0x96, 0xd8, 0xb8, 0xe0, 0x02, 0xa9, 0x59, 0xcd,
	0xa8, 0xe5, 0xb1, 0x97, 0x5c, 0x20, 0xeb, 0x42, 0x4d, 0xe9, 0x2c, 0xc1, 0xa0, 0x56, 0x0a, 0xe9,
	0xc5, 0xfd, 0xbc, 0x51, 0x98, 0x04, 0x75, 0x12, 0xd0, 0x33, 0xeb, 0x40, 0x55, 0x65, 0x49, 0x70,
	0x40, 0x90, 0x7b, 0x64, 0x27, 0x70, 0x30, 0x53, 0x71, 0x2a, 0xe7, 0x45, 0xd0, 0x20, 0x75, 0x7d,
	0xa6, 0xae, 0xe4, 0xbc, 0x60, 0x5f, 0x42, 0x77, 0x2a, 0x65, 0x6a, 0x62, 0xa5, 0xa5, 0x90, 0x6e,
	0xc0, 0x63, 0xfb, 0x56, 0x61, 0xd0, 0x24, 0x16, 0xa3, 0xb5, 0xf1, 0x62, 0xe9, 0xe6, 0xad, 0x42,
	0x76, 0x09, 0xbd, 0x75, 0x45, 0x59, 0x16, 0x90, 0xe4, 0xd1, 0xaa, 0x64, 0x4c, 0x45, 0x3e, 0x85,
	0x23, 0xdf, 0x78, 0xa2, 0x7e, 0x15, 0xb4, 0x88, 0x7b, 0xe8, 0x83, 0x41, 0x18, 0x1b, 0x42, 0xa7,
	0xc0, 0x79, 0xac, 0x50, 0xaa, 0x1c, 0xbd, 0xe7, 0x21, 0xf1, 0xda, 0x05, 0xce, 0xc7, 0x04, 0x97,
	0x76, 0x9f, 0xc2, 0x71, 0x66, 0xe2, 0x65, 0xc7, 0xe0, 0x88, 0x88, 0x47, 0x99, 0x19, 0xdd, 0x5b,
	0xba, 0xbd, 0x49, 0xb9, 0xc5, 0xa0, 0x5d, 0xee, 0x8d, 0x7b, 0x0e, 0xbf, 0x85, 0xd3, 0xef, 0x73,
	0xe4, 0x7a, 0xbd, 0xa9, 0xef, 0x33, 0xa3, 0xe1, 0x08, 0x3e, 0xde, 0x20, 0xde, 0x29, 0x31, 0x2f,
	0x20, 0x58, 0x3a, 0x43, 0x7e, 0xd0, 0x5a, 0xea, 0x1d, 0x9d, 0x04, 0x3c, 0x5a, 0x39, 0x8d, 0x76,
	0x4a, 0xc6, 0x70, 0x25, 0x19, 0xdd, 0xfb, 0x64, 0x90, 0x69, 0x4e, 0x99, 0x2b, 0x53, 0xf1, 0x4f,
	0x05, 0x3a, 0xeb, 0x4b, 0x6e, 0x84, 0x85, 0xc8, 0xe3, 0x14, 0x79, 0x7e, 0x17, 0x89, 0x5a, 0xd4,
	0x12, 0x22, 0xbf, 0xf2, 0x10, 0x7b, 0x06, 0x6d, 0x47, 0x91, 0x79, 0x1a, 0x0b, 0x39, 0xc9, 0xf2,
	0x45, 0x09, 0x4e, 0xf8, 0x53, 0x9e, 0x8e, 0x08, 0x5b, 0xb0, 0xdc, 0x30, 0x78, 0x56, 0xf5, 0x8e,
	0xf5, 0x12, 0xe7, 0x9e, 0xe5, 0x1a, 0xe5, 0x58, 0xd2, 0xa2, 0xf1, 0x71, 0x69, 0x38, 0x82, 0x7b,
	0x5f, 0x4a, 0xe7, 0xcc, 0xa0, 0xa6, 0xc4, 0xdc, 0xa5, 0xf3, 0x17, 0x83, 0x9a, 0x9d, 0x42, 0x93,
	0x2b, 0x95, 0x67, 0x09, 0x2f, 0xac, 0xcf, 0xce, 0x3d, 0xc0, 0x1e, 0x03, 0xa4, 0xa8, 0xb8, 0xb6,
	0x02, 0x0b, 0xeb, 0x73, 0xb4, 0x84, 0xb0, 0xcf, 0xa1, 0xe3, 0xc9, 0x34, 0xff, 0x34, 0x64, 0x0d,
	0x62, 0x1d, 0x2f, 0xe1, 0x57, 0xdc, 0x22, 0xfb, 0x0c, 0x1c, 0xa4, 0xe5, 0x1b, 0x9e, 0xc7, 0x1a,
	0x13, 0xa9, 0x53, 0xca, 0x56, 0x33, 0x6a, 0x2f, 0xe0, 0x88, 0xd0, 0xcb, 0xbf, 0xaa, 0x2b, 0x37,
	0xca, 0x2b, 0xd4, 0x6f, 0xdc, 0x0c, 0xbf, 0x80, 0xde, 0x0a, 0xca, 0x75, 0x72, 0x4b, 0xdb, 0xfd,
	0xc1, 0x5a, 0x7f, 0xfc, 0x00, 0xf7, 0x4f, 0xfe, 0x83, 0xfb, 0x61, 0x98, 0xc0, 0xc9, 0x86, 0x93,
	0x94, 0x85, 0x4e, 0xb3, 0xfd, 0xf0, 0xee, 0x3f, 0xdd, 0xca, 0xf1, 0xdf, 0xb8, 0x85, 0x0f, 0x37,
	0x5e, 0x2b, 0xec, 0x99, 0x73, 0x78, 0xd7, 0xdd, 0xd7, 0xff, 0xe4, 0x1d, 0x2c, 0xff, 0xa5, 0x5f,
	0xa1, 0xf7, 0x60, 0x14, 0xd9, 0xc0, 0xe9, 0xb7, 0x45, 0xbc, 0x7f, 0xb6, 0x85, 0x51, 0xba, 0x4f,
	0xea, 0x74, 0xc9, 0x7f, 0xfd, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf7, 0x38, 0xe8, 0x17, 0x19,
	0x08, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MemberMergeServiceClient is the client API for MemberMergeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MemberMergeServiceClient interface {
	MemberMergeSearchList(ctx context.Context, in *MemberMergeRequest, opts ...grpc.CallOption) (*MemberMergeResponse, error)
	GetMemberGoodBrowseList(ctx context.Context, in *GetMemberGoodBrowseListRequest, opts ...grpc.CallOption) (*GetMemberGoodBrowseListResponse, error)
	StoreMemberGoodBrowseInfo(ctx context.Context, in *StoreMemberGoodBrowseInfoRequest, opts ...grpc.CallOption) (*StoreMemberGoodBrowseInfoResponse, error)
	ClearMemberGoodBrowse(ctx context.Context, in *ClearMemberGoodBrowseRequest, opts ...grpc.CallOption) (*ClearMemberGoodBrowseResponse, error)
}

type memberMergeServiceClient struct {
	cc *grpc.ClientConn
}

func NewMemberMergeServiceClient(cc *grpc.ClientConn) MemberMergeServiceClient {
	return &memberMergeServiceClient{cc}
}

func (c *memberMergeServiceClient) MemberMergeSearchList(ctx context.Context, in *MemberMergeRequest, opts ...grpc.CallOption) (*MemberMergeResponse, error) {
	out := new(MemberMergeResponse)
	err := c.cc.Invoke(ctx, "/mm.MemberMergeService/MemberMergeSearchList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberMergeServiceClient) GetMemberGoodBrowseList(ctx context.Context, in *GetMemberGoodBrowseListRequest, opts ...grpc.CallOption) (*GetMemberGoodBrowseListResponse, error) {
	out := new(GetMemberGoodBrowseListResponse)
	err := c.cc.Invoke(ctx, "/mm.MemberMergeService/GetMemberGoodBrowseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberMergeServiceClient) StoreMemberGoodBrowseInfo(ctx context.Context, in *StoreMemberGoodBrowseInfoRequest, opts ...grpc.CallOption) (*StoreMemberGoodBrowseInfoResponse, error) {
	out := new(StoreMemberGoodBrowseInfoResponse)
	err := c.cc.Invoke(ctx, "/mm.MemberMergeService/StoreMemberGoodBrowseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberMergeServiceClient) ClearMemberGoodBrowse(ctx context.Context, in *ClearMemberGoodBrowseRequest, opts ...grpc.CallOption) (*ClearMemberGoodBrowseResponse, error) {
	out := new(ClearMemberGoodBrowseResponse)
	err := c.cc.Invoke(ctx, "/mm.MemberMergeService/ClearMemberGoodBrowse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MemberMergeServiceServer is the server API for MemberMergeService service.
type MemberMergeServiceServer interface {
	MemberMergeSearchList(context.Context, *MemberMergeRequest) (*MemberMergeResponse, error)
	GetMemberGoodBrowseList(context.Context, *GetMemberGoodBrowseListRequest) (*GetMemberGoodBrowseListResponse, error)
	StoreMemberGoodBrowseInfo(context.Context, *StoreMemberGoodBrowseInfoRequest) (*StoreMemberGoodBrowseInfoResponse, error)
	ClearMemberGoodBrowse(context.Context, *ClearMemberGoodBrowseRequest) (*ClearMemberGoodBrowseResponse, error)
}

// UnimplementedMemberMergeServiceServer can be embedded to have forward compatible implementations.
type UnimplementedMemberMergeServiceServer struct {
}

func (*UnimplementedMemberMergeServiceServer) MemberMergeSearchList(ctx context.Context, req *MemberMergeRequest) (*MemberMergeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MemberMergeSearchList not implemented")
}
func (*UnimplementedMemberMergeServiceServer) GetMemberGoodBrowseList(ctx context.Context, req *GetMemberGoodBrowseListRequest) (*GetMemberGoodBrowseListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberGoodBrowseList not implemented")
}
func (*UnimplementedMemberMergeServiceServer) StoreMemberGoodBrowseInfo(ctx context.Context, req *StoreMemberGoodBrowseInfoRequest) (*StoreMemberGoodBrowseInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreMemberGoodBrowseInfo not implemented")
}
func (*UnimplementedMemberMergeServiceServer) ClearMemberGoodBrowse(ctx context.Context, req *ClearMemberGoodBrowseRequest) (*ClearMemberGoodBrowseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearMemberGoodBrowse not implemented")
}

func RegisterMemberMergeServiceServer(s *grpc.Server, srv MemberMergeServiceServer) {
	s.RegisterService(&_MemberMergeService_serviceDesc, srv)
}

func _MemberMergeService_MemberMergeSearchList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberMergeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberMergeServiceServer).MemberMergeSearchList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mm.MemberMergeService/MemberMergeSearchList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberMergeServiceServer).MemberMergeSearchList(ctx, req.(*MemberMergeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberMergeService_GetMemberGoodBrowseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberGoodBrowseListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberMergeServiceServer).GetMemberGoodBrowseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mm.MemberMergeService/GetMemberGoodBrowseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberMergeServiceServer).GetMemberGoodBrowseList(ctx, req.(*GetMemberGoodBrowseListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberMergeService_StoreMemberGoodBrowseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreMemberGoodBrowseInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberMergeServiceServer).StoreMemberGoodBrowseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mm.MemberMergeService/StoreMemberGoodBrowseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberMergeServiceServer).StoreMemberGoodBrowseInfo(ctx, req.(*StoreMemberGoodBrowseInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberMergeService_ClearMemberGoodBrowse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearMemberGoodBrowseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberMergeServiceServer).ClearMemberGoodBrowse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mm.MemberMergeService/ClearMemberGoodBrowse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberMergeServiceServer).ClearMemberGoodBrowse(ctx, req.(*ClearMemberGoodBrowseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MemberMergeService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mm.MemberMergeService",
	HandlerType: (*MemberMergeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MemberMergeSearchList",
			Handler:    _MemberMergeService_MemberMergeSearchList_Handler,
		},
		{
			MethodName: "GetMemberGoodBrowseList",
			Handler:    _MemberMergeService_GetMemberGoodBrowseList_Handler,
		},
		{
			MethodName: "StoreMemberGoodBrowseInfo",
			Handler:    _MemberMergeService_StoreMemberGoodBrowseInfo_Handler,
		},
		{
			MethodName: "ClearMemberGoodBrowse",
			Handler:    _MemberMergeService_ClearMemberGoodBrowse_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mm/member.proto",
}
