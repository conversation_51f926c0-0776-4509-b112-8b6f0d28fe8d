module _

go 1.13

require (
	github.com/Shopify/sarama v1.19.0
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.6.0
	github.com/go-xorm/xorm v0.7.9
	github.com/gogf/gf v1.16.6
	github.com/golang-module/carbon/v2 v2.2.3
	github.com/golang/protobuf v1.5.0
	github.com/google/uuid v1.3.0
	github.com/jinzhu/copier v0.3.5
	github.com/labstack/echo/v4 v4.1.16
	github.com/limitedlee/microservice v0.1.0
	github.com/maybgit/glog v0.0.0-20210928064228-9506732eb074
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/olivere/elastic/v7 v7.0.22
	github.com/panjf2000/ants v1.3.0
	github.com/ppkg/kit v0.0.0-20210928070906-2e2b70f489af
	github.com/robfig/cron/v3 v3.0.1
	github.com/shopspring/decimal v1.2.0
	github.com/spf13/cast v1.3.1
	github.com/streadway/amqp v1.0.0
	github.com/tricobbler/rp-kit v0.0.0-20210126092201-6e60814e8b66
	github.com/xuri/excelize/v2 v2.7.1
	google.golang.org/genproto v0.0.0-20200526211855-cb27e3aa2013
	google.golang.org/grpc v1.37.0
	google.golang.org/protobuf v1.26.0
)

replace _ => ./
