package proto

import (
	"bufio"
	"bytes"
	"fmt"
	"github.com/maybgit/pbgo"
	"io/fs"
	"io/ioutil"
	"os"
	"os/exec"
	"strconv"
	"strings"
)

func BuildProto() error {
	err := pbgo.Generate()
	return err
}

type SmartBuild struct {
	IgnoreDir              []string         // 忽略的目录
	IgnoreReplaceOmitempty []string         // 忽略替换Omitempty的文件
	History                map[string]int64 // 构建历史，path=>上次构建文件修改时间
}

// 构建时会记录proto路径及修改时间，并缓存到本地文件中
// 下次构建时会通过修改时间对比是否需要重新生成
// 如果需要强制构建，可以删除 .smartbuild.history
func SmartBuildProto() {
	sb, err := initSmartBuild()

	if err != nil {
		fmt.Println("初始化出错：" + err.Error())
		return
	}

	sb.recursionBuildProto(".")

	history, err := os.OpenFile(".smartbuild.history", os.O_RDWR|os.O_CREATE, 0644)
	if err != nil {
		fmt.Println("写入生成记录出错：" + err.Error())
	}

	writer := bufio.NewWriter(history)
	for p, t := range sb.History {
		_, _ = writer.WriteString(fmt.Sprintf("%s,%d\n", p, t))
	}
	_ = writer.Flush()

	return
}

// 初始化构建
func initSmartBuild() (*SmartBuild, error) {
	sb := &SmartBuild{
		IgnoreDir:              []string{"google", ".git", ".idea"},
		IgnoreReplaceOmitempty: []string{},
		History:                make(map[string]int64),
	}

	history, err := os.OpenFile(".smartbuild.history", os.O_RDONLY, 0644)
	if err != nil {
		if os.IsNotExist(err) {
			return sb, nil
		}
		return nil, err
	}
	defer history.Close()

	scanner := bufio.NewScanner(history)
	for scanner.Scan() {
		lineArr := strings.Split(scanner.Text(), ",")
		if len(lineArr) != 2 {
			continue
		}
		v, err := strconv.ParseInt(lineArr[1], 0, 0)
		if err != nil {
			continue
		}
		sb.History[lineArr[0]] = v
	}

	return sb, nil
}

// 循环读取proto
func (sb *SmartBuild) recursionBuildProto(dirname string) {
	file, err := ioutil.ReadDir(dirname)
	if err != nil {
		fmt.Println(fmt.Sprintf("读取目录 %s 出错：%s", dirname, err.Error()))
		return
	}
	for _, v := range file {
		if v.IsDir() {
			if !isExistsArray(v.Name(), sb.IgnoreDir) {
				sb.recursionBuildProto(dirname + "\\" + v.Name())
			}
		} else {
			if strings.HasSuffix(v.Name(), ".proto") {
				sb.buildProto(dirname, v)
			}
		}
	}

	return
}

// 构建proto
func (sb *SmartBuild) buildProto(dirName string, file fs.FileInfo) {
	name := dirName + "\\" + file.Name()

	if history, has := sb.History[name]; has && history == file.ModTime().Unix() {
		fmt.Println(fmt.Sprintf("skip %s", name))
		return
	}

	cmd := exec.Command("cmd.exe", "/C", "protoc --go_out=plugins=grpc:. "+name)
	w := bytes.NewBuffer(nil)
	cmd.Stderr = w

	if err := cmd.Run(); err != nil {
		fmt.Println(fmt.Sprintf("generate %s pb.go file error，%s", name, err.Error()))
	} else {
		pbFile := strings.ReplaceAll(name, ".proto", ".pb.go")
		fmt.Println(fmt.Sprintf("success %s => %s", name, pbFile))

		// 替换omitempty
		if err := sb.replaceOmitempty(pbFile); err != nil {
			fmt.Println("replaceOmitempty出错：" + err.Error())
		}

		// 记录构建时间
		sb.History[name] = file.ModTime().Unix()
	}

	if len(w.Bytes()) > 0 {
		fmt.Println(string(w.Bytes()))
	}
	return
}

// 替换默认生成的Omitempty
func (sb *SmartBuild) replaceOmitempty(pbFile string) (err error) {
	if isExistsArray(pbFile, sb.IgnoreReplaceOmitempty) {
		return
	}
	data, err := ioutil.ReadFile(pbFile)
	if err != nil {
		return
	}
	data = bytes.Replace(data, []byte(",omitempty"), []byte(""), -1)
	return ioutil.WriteFile(pbFile, data, 0644)
}

// 在数组中是否存在判断
func isExistsArray(s string, arr []string) bool {
	for _, v := range arr {
		if s == v {
			return true
		}
	}
	return false
}
