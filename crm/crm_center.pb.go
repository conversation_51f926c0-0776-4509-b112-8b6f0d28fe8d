// Code generated by protoc-gen-go. DO NOT EDIT.
// source: crm/crm_center.proto

package crm

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//平台商品库--获取异步任务数据接口--请求参数
type GetTaskListRequest struct {
	//任务id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//任务内容:1:批量新建;2:批量删除;3:批量更新
	TaskContent int32 `protobuf:"varint,2,opt,name=task_content,json=taskContent,proto3" json:"task_content"`
	//任务状态:1:调度中;2:进行中;3:已完成
	TaskStatus int32 `protobuf:"varint,3,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	//状态:1:正常;2:冻结;
	Status int32 `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	//创建人id
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	//排序类型：createTimeDesc:根据创建时间倒序
	Sort string `protobuf:"bytes,6,opt,name=sort,proto3" json:"sort"`
	//创建时间
	Createtime string `protobuf:"bytes,7,opt,name=createtime,proto3" json:"createtime"`
	//当前页
	Page int32 `protobuf:"varint,8,opt,name=page,proto3" json:"page"`
	//每页显示数据条数
	PageSize int32 `protobuf:"varint,9,opt,name=pageSize,proto3" json:"pageSize"`
	//渠道id
	ChannelId            int32    `protobuf:"varint,10,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTaskListRequest) Reset()         { *m = GetTaskListRequest{} }
func (m *GetTaskListRequest) String() string { return proto.CompactTextString(m) }
func (*GetTaskListRequest) ProtoMessage()    {}
func (*GetTaskListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{0}
}

func (m *GetTaskListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTaskListRequest.Unmarshal(m, b)
}
func (m *GetTaskListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTaskListRequest.Marshal(b, m, deterministic)
}
func (m *GetTaskListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTaskListRequest.Merge(m, src)
}
func (m *GetTaskListRequest) XXX_Size() int {
	return xxx_messageInfo_GetTaskListRequest.Size(m)
}
func (m *GetTaskListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTaskListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTaskListRequest proto.InternalMessageInfo

func (m *GetTaskListRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetTaskListRequest) GetTaskContent() int32 {
	if m != nil {
		return m.TaskContent
	}
	return 0
}

func (m *GetTaskListRequest) GetTaskStatus() int32 {
	if m != nil {
		return m.TaskStatus
	}
	return 0
}

func (m *GetTaskListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetTaskListRequest) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *GetTaskListRequest) GetSort() string {
	if m != nil {
		return m.Sort
	}
	return ""
}

func (m *GetTaskListRequest) GetCreatetime() string {
	if m != nil {
		return m.Createtime
	}
	return ""
}

func (m *GetTaskListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetTaskListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetTaskListRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

//平台商品库--获取异步任务数据接口--响应参数
type GetTaskListResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//当前页
	Page int32 `protobuf:"varint,4,opt,name=page,proto3" json:"page"`
	//每页显示数据条数
	PageSize int32 `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize"`
	//总数
	Count int32 `protobuf:"varint,6,opt,name=count,proto3" json:"count"`
	//结果
	TaskList             []*TaskList `protobuf:"bytes,7,rep,name=task_list,json=taskList,proto3" json:"task_list"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetTaskListResponse) Reset()         { *m = GetTaskListResponse{} }
func (m *GetTaskListResponse) String() string { return proto.CompactTextString(m) }
func (*GetTaskListResponse) ProtoMessage()    {}
func (*GetTaskListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{1}
}

func (m *GetTaskListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTaskListResponse.Unmarshal(m, b)
}
func (m *GetTaskListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTaskListResponse.Marshal(b, m, deterministic)
}
func (m *GetTaskListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTaskListResponse.Merge(m, src)
}
func (m *GetTaskListResponse) XXX_Size() int {
	return xxx_messageInfo_GetTaskListResponse.Size(m)
}
func (m *GetTaskListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTaskListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTaskListResponse proto.InternalMessageInfo

func (m *GetTaskListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetTaskListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetTaskListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetTaskListResponse) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetTaskListResponse) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetTaskListResponse) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetTaskListResponse) GetTaskList() []*TaskList {
	if m != nil {
		return m.TaskList
	}
	return nil
}

//任务列表模型
type TaskList struct {
	//任务id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//任务内容:1:批量新建;2:批量删除;3:渠道--批量新建 4：渠道--批量上架
	TaskContent int32 `protobuf:"varint,2,opt,name=task_content,json=taskContent,proto3" json:"task_content"`
	//任务状态:1:调度中;2:进行中;3:已完成
	TaskStatus int32 `protobuf:"varint,3,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	//任务详情
	TaskDetail string `protobuf:"bytes,4,opt,name=task_detail,json=taskDetail,proto3" json:"task_detail"`
	//操作文件路径
	OperationFileUrl string `protobuf:"bytes,5,opt,name=operation_file_url,json=operationFileUrl,proto3" json:"operation_file_url"`
	//操作结果文件路径
	ResulteFileUrl string `protobuf:"bytes,6,opt,name=resulte_file_url,json=resulteFileUrl,proto3" json:"resulte_file_url"`
	//状态:1:正常;2:冻结;
	Status int32 `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	//创建人id
	ModifyId string `protobuf:"bytes,8,opt,name=modify_id,json=modifyId,proto3" json:"modify_id"`
	//创建时间
	ModifyTime string `protobuf:"bytes,9,opt,name=modify_time,json=modifyTime,proto3" json:"modify_time"`
	//创建人id
	CreateId string `protobuf:"bytes,10,opt,name=create_id,json=createId,proto3" json:"create_id"`
	//渠道id
	ChannelId int32 `protobuf:"varint,11,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//创建时间
	CreateTime string `protobuf:"bytes,12,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//操作请求的token值，类似userinfo
	RequestHeader        string   `protobuf:"bytes,13,opt,name=request_header,json=requestHeader,proto3" json:"request_header"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskList) Reset()         { *m = TaskList{} }
func (m *TaskList) String() string { return proto.CompactTextString(m) }
func (*TaskList) ProtoMessage()    {}
func (*TaskList) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{2}
}

func (m *TaskList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskList.Unmarshal(m, b)
}
func (m *TaskList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskList.Marshal(b, m, deterministic)
}
func (m *TaskList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskList.Merge(m, src)
}
func (m *TaskList) XXX_Size() int {
	return xxx_messageInfo_TaskList.Size(m)
}
func (m *TaskList) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskList.DiscardUnknown(m)
}

var xxx_messageInfo_TaskList proto.InternalMessageInfo

func (m *TaskList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TaskList) GetTaskContent() int32 {
	if m != nil {
		return m.TaskContent
	}
	return 0
}

func (m *TaskList) GetTaskStatus() int32 {
	if m != nil {
		return m.TaskStatus
	}
	return 0
}

func (m *TaskList) GetTaskDetail() string {
	if m != nil {
		return m.TaskDetail
	}
	return ""
}

func (m *TaskList) GetOperationFileUrl() string {
	if m != nil {
		return m.OperationFileUrl
	}
	return ""
}

func (m *TaskList) GetResulteFileUrl() string {
	if m != nil {
		return m.ResulteFileUrl
	}
	return ""
}

func (m *TaskList) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *TaskList) GetModifyId() string {
	if m != nil {
		return m.ModifyId
	}
	return ""
}

func (m *TaskList) GetModifyTime() string {
	if m != nil {
		return m.ModifyTime
	}
	return ""
}

func (m *TaskList) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *TaskList) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TaskList) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *TaskList) GetRequestHeader() string {
	if m != nil {
		return m.RequestHeader
	}
	return ""
}

//平台商品库--新增异步任务数据接口--请求参数
type CreateBatchTaskRequest struct {
	//任务内容:1:导入用户标签
	TaskContent int32 `protobuf:"varint,1,opt,name=task_content,json=taskContent,proto3" json:"task_content"`
	//操作文件路径
	OperationFileUrl string `protobuf:"bytes,2,opt,name=operation_file_url,json=operationFileUrl,proto3" json:"operation_file_url"`
	//创建人id
	CreateId string `protobuf:"bytes,3,opt,name=create_id,json=createId,proto3" json:"create_id"`
	//标签名称
	FlagName string `protobuf:"bytes,4,opt,name=flag_name,json=flagName,proto3" json:"flag_name"`
	//标签组名称
	GroupFlagName        string   `protobuf:"bytes,5,opt,name=group_flag_name,json=groupFlagName,proto3" json:"group_flag_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateBatchTaskRequest) Reset()         { *m = CreateBatchTaskRequest{} }
func (m *CreateBatchTaskRequest) String() string { return proto.CompactTextString(m) }
func (*CreateBatchTaskRequest) ProtoMessage()    {}
func (*CreateBatchTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{3}
}

func (m *CreateBatchTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateBatchTaskRequest.Unmarshal(m, b)
}
func (m *CreateBatchTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateBatchTaskRequest.Marshal(b, m, deterministic)
}
func (m *CreateBatchTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateBatchTaskRequest.Merge(m, src)
}
func (m *CreateBatchTaskRequest) XXX_Size() int {
	return xxx_messageInfo_CreateBatchTaskRequest.Size(m)
}
func (m *CreateBatchTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateBatchTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateBatchTaskRequest proto.InternalMessageInfo

func (m *CreateBatchTaskRequest) GetTaskContent() int32 {
	if m != nil {
		return m.TaskContent
	}
	return 0
}

func (m *CreateBatchTaskRequest) GetOperationFileUrl() string {
	if m != nil {
		return m.OperationFileUrl
	}
	return ""
}

func (m *CreateBatchTaskRequest) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *CreateBatchTaskRequest) GetFlagName() string {
	if m != nil {
		return m.FlagName
	}
	return ""
}

func (m *CreateBatchTaskRequest) GetGroupFlagName() string {
	if m != nil {
		return m.GroupFlagName
	}
	return ""
}

//平台商品库--新增异步任务数据接口--响应参数
type CreateBatchTaskResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateBatchTaskResponse) Reset()         { *m = CreateBatchTaskResponse{} }
func (m *CreateBatchTaskResponse) String() string { return proto.CompactTextString(m) }
func (*CreateBatchTaskResponse) ProtoMessage()    {}
func (*CreateBatchTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{4}
}

func (m *CreateBatchTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateBatchTaskResponse.Unmarshal(m, b)
}
func (m *CreateBatchTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateBatchTaskResponse.Marshal(b, m, deterministic)
}
func (m *CreateBatchTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateBatchTaskResponse.Merge(m, src)
}
func (m *CreateBatchTaskResponse) XXX_Size() int {
	return xxx_messageInfo_CreateBatchTaskResponse.Size(m)
}
func (m *CreateBatchTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateBatchTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateBatchTaskResponse proto.InternalMessageInfo

func (m *CreateBatchTaskResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CreateBatchTaskResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CreateBatchTaskResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

//客户详情接口
type CustomerRequest struct {
	//创建人id
	Userid string `protobuf:"bytes,1,opt,name=userid,proto3" json:"userid"`
	//外部用户id
	ExternalUserid string `protobuf:"bytes,2,opt,name=external_userid,json=externalUserid,proto3" json:"external_userid"`
	//用户昵称
	NickName string `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	//用户类型    全部给-1
	CustomerType int32 `protobuf:"varint,4,opt,name=customer_type,json=customerType,proto3" json:"customer_type"`
	//用户性别     部给-1
	CustomerSex int32 `protobuf:"varint,5,opt,name=customer_sex,json=customerSex,proto3" json:"customer_sex"`
	//备注电话
	RemarkMobile string `protobuf:"bytes,6,opt,name=remark_mobile,json=remarkMobile,proto3" json:"remark_mobile"`
	//分页页码
	PageIndex int32 `protobuf:"varint,7,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//分页页数
	PageSize int32 `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//是否导出标记
	IsExport             int32    `protobuf:"varint,9,opt,name=is_export,json=isExport,proto3" json:"is_export"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomerRequest) Reset()         { *m = CustomerRequest{} }
func (m *CustomerRequest) String() string { return proto.CompactTextString(m) }
func (*CustomerRequest) ProtoMessage()    {}
func (*CustomerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{5}
}

func (m *CustomerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomerRequest.Unmarshal(m, b)
}
func (m *CustomerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomerRequest.Marshal(b, m, deterministic)
}
func (m *CustomerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomerRequest.Merge(m, src)
}
func (m *CustomerRequest) XXX_Size() int {
	return xxx_messageInfo_CustomerRequest.Size(m)
}
func (m *CustomerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CustomerRequest proto.InternalMessageInfo

func (m *CustomerRequest) GetUserid() string {
	if m != nil {
		return m.Userid
	}
	return ""
}

func (m *CustomerRequest) GetExternalUserid() string {
	if m != nil {
		return m.ExternalUserid
	}
	return ""
}

func (m *CustomerRequest) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *CustomerRequest) GetCustomerType() int32 {
	if m != nil {
		return m.CustomerType
	}
	return 0
}

func (m *CustomerRequest) GetCustomerSex() int32 {
	if m != nil {
		return m.CustomerSex
	}
	return 0
}

func (m *CustomerRequest) GetRemarkMobile() string {
	if m != nil {
		return m.RemarkMobile
	}
	return ""
}

func (m *CustomerRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CustomerRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *CustomerRequest) GetIsExport() int32 {
	if m != nil {
		return m.IsExport
	}
	return 0
}

//标签查询返回标签信息
type FlagResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//标签信息
	FlagInfo             *FlagInfo `protobuf:"bytes,4,opt,name=flag_info,json=flagInfo,proto3" json:"flag_info"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *FlagResponse) Reset()         { *m = FlagResponse{} }
func (m *FlagResponse) String() string { return proto.CompactTextString(m) }
func (*FlagResponse) ProtoMessage()    {}
func (*FlagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{6}
}

func (m *FlagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlagResponse.Unmarshal(m, b)
}
func (m *FlagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlagResponse.Marshal(b, m, deterministic)
}
func (m *FlagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlagResponse.Merge(m, src)
}
func (m *FlagResponse) XXX_Size() int {
	return xxx_messageInfo_FlagResponse.Size(m)
}
func (m *FlagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FlagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FlagResponse proto.InternalMessageInfo

func (m *FlagResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *FlagResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FlagResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *FlagResponse) GetFlagInfo() *FlagInfo {
	if m != nil {
		return m.FlagInfo
	}
	return nil
}

//标签组list请求参数
type FlagGroupListRequest struct {
	//当前页 111
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	//每页大小
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//标签组名称
	GroupName            string   `protobuf:"bytes,3,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlagGroupListRequest) Reset()         { *m = FlagGroupListRequest{} }
func (m *FlagGroupListRequest) String() string { return proto.CompactTextString(m) }
func (*FlagGroupListRequest) ProtoMessage()    {}
func (*FlagGroupListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{7}
}

func (m *FlagGroupListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlagGroupListRequest.Unmarshal(m, b)
}
func (m *FlagGroupListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlagGroupListRequest.Marshal(b, m, deterministic)
}
func (m *FlagGroupListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlagGroupListRequest.Merge(m, src)
}
func (m *FlagGroupListRequest) XXX_Size() int {
	return xxx_messageInfo_FlagGroupListRequest.Size(m)
}
func (m *FlagGroupListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FlagGroupListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FlagGroupListRequest proto.InternalMessageInfo

func (m *FlagGroupListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *FlagGroupListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *FlagGroupListRequest) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

//标签组list返回参数
type FlagGroupListResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Total   int32  `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//标签信息列表
	FlagGroupList        []*FlagGroupInfo `protobuf:"bytes,5,rep,name=flag_group_list,json=flagGroupList,proto3" json:"flag_group_list"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *FlagGroupListResponse) Reset()         { *m = FlagGroupListResponse{} }
func (m *FlagGroupListResponse) String() string { return proto.CompactTextString(m) }
func (*FlagGroupListResponse) ProtoMessage()    {}
func (*FlagGroupListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{8}
}

func (m *FlagGroupListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlagGroupListResponse.Unmarshal(m, b)
}
func (m *FlagGroupListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlagGroupListResponse.Marshal(b, m, deterministic)
}
func (m *FlagGroupListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlagGroupListResponse.Merge(m, src)
}
func (m *FlagGroupListResponse) XXX_Size() int {
	return xxx_messageInfo_FlagGroupListResponse.Size(m)
}
func (m *FlagGroupListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FlagGroupListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FlagGroupListResponse proto.InternalMessageInfo

func (m *FlagGroupListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *FlagGroupListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FlagGroupListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *FlagGroupListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *FlagGroupListResponse) GetFlagGroupList() []*FlagGroupInfo {
	if m != nil {
		return m.FlagGroupList
	}
	return nil
}

//标签查询列表请求参数
type FlagListRequest struct {
	//当前页
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	//每页大小
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//标签类型 1:手动标签 2:自动标签 0全部
	FlagType int32 `protobuf:"varint,3,opt,name=flag_type,json=flagType,proto3" json:"flag_type"`
	//标签组名称
	GroupName string `protobuf:"bytes,4,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	//标签名称
	FlagName             string   `protobuf:"bytes,5,opt,name=flag_name,json=flagName,proto3" json:"flag_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlagListRequest) Reset()         { *m = FlagListRequest{} }
func (m *FlagListRequest) String() string { return proto.CompactTextString(m) }
func (*FlagListRequest) ProtoMessage()    {}
func (*FlagListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{9}
}

func (m *FlagListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlagListRequest.Unmarshal(m, b)
}
func (m *FlagListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlagListRequest.Marshal(b, m, deterministic)
}
func (m *FlagListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlagListRequest.Merge(m, src)
}
func (m *FlagListRequest) XXX_Size() int {
	return xxx_messageInfo_FlagListRequest.Size(m)
}
func (m *FlagListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FlagListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FlagListRequest proto.InternalMessageInfo

func (m *FlagListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *FlagListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *FlagListRequest) GetFlagType() int32 {
	if m != nil {
		return m.FlagType
	}
	return 0
}

func (m *FlagListRequest) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *FlagListRequest) GetFlagName() string {
	if m != nil {
		return m.FlagName
	}
	return ""
}

//标签查询列表返回
type FlagListResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error   string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Total   int32  `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	//标签信息列表
	FlagList             []*FlagInfo `protobuf:"bytes,5,rep,name=flag_list,json=flagList,proto3" json:"flag_list"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *FlagListResponse) Reset()         { *m = FlagListResponse{} }
func (m *FlagListResponse) String() string { return proto.CompactTextString(m) }
func (*FlagListResponse) ProtoMessage()    {}
func (*FlagListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{10}
}

func (m *FlagListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlagListResponse.Unmarshal(m, b)
}
func (m *FlagListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlagListResponse.Marshal(b, m, deterministic)
}
func (m *FlagListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlagListResponse.Merge(m, src)
}
func (m *FlagListResponse) XXX_Size() int {
	return xxx_messageInfo_FlagListResponse.Size(m)
}
func (m *FlagListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FlagListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FlagListResponse proto.InternalMessageInfo

func (m *FlagListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *FlagListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FlagListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *FlagListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *FlagListResponse) GetFlagList() []*FlagInfo {
	if m != nil {
		return m.FlagList
	}
	return nil
}

//标签信息
type FlagInfo struct {
	//标签类型 1:手动标签 2:自动标签
	FlagType int32 `protobuf:"varint,1,opt,name=flag_type,json=flagType,proto3" json:"flag_type"`
	//标签组名称
	GroupName string `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	//标签名称
	FlagName string `protobuf:"bytes,3,opt,name=flag_name,json=flagName,proto3" json:"flag_name"`
	//用户个数
	FlagSum int32 `protobuf:"varint,4,opt,name=flag_sum,json=flagSum,proto3" json:"flag_sum"`
	//标签标签ID，删除的时候只需要传ID
	FlagId int32 `protobuf:"varint,5,opt,name=flag_id,json=flagId,proto3" json:"flag_id"`
	//选取中的条件
	List []*ConditionInfo `protobuf:"bytes,6,rep,name=list,proto3" json:"list"`
	//企业微信的标签ID
	TagId string `protobuf:"bytes,7,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	//标签组ID
	GroupId int32 `protobuf:"varint,8,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	//判断类型，1：满足任意一个被选中的条件即可，2：必须满足所有被选中的条件
	CheckType            int32    `protobuf:"varint,9,opt,name=check_type,json=checkType,proto3" json:"check_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlagInfo) Reset()         { *m = FlagInfo{} }
func (m *FlagInfo) String() string { return proto.CompactTextString(m) }
func (*FlagInfo) ProtoMessage()    {}
func (*FlagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{11}
}

func (m *FlagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlagInfo.Unmarshal(m, b)
}
func (m *FlagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlagInfo.Marshal(b, m, deterministic)
}
func (m *FlagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlagInfo.Merge(m, src)
}
func (m *FlagInfo) XXX_Size() int {
	return xxx_messageInfo_FlagInfo.Size(m)
}
func (m *FlagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FlagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FlagInfo proto.InternalMessageInfo

func (m *FlagInfo) GetFlagType() int32 {
	if m != nil {
		return m.FlagType
	}
	return 0
}

func (m *FlagInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *FlagInfo) GetFlagName() string {
	if m != nil {
		return m.FlagName
	}
	return ""
}

func (m *FlagInfo) GetFlagSum() int32 {
	if m != nil {
		return m.FlagSum
	}
	return 0
}

func (m *FlagInfo) GetFlagId() int32 {
	if m != nil {
		return m.FlagId
	}
	return 0
}

func (m *FlagInfo) GetList() []*ConditionInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *FlagInfo) GetTagId() string {
	if m != nil {
		return m.TagId
	}
	return ""
}

func (m *FlagInfo) GetGroupId() int32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *FlagInfo) GetCheckType() int32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

//标签组
type FlagGroupInfo struct {
	//标签组名称
	GroupName string `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	//标签组主键
	Id int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	//标签组在企业微信的ID
	GroupId string `protobuf:"bytes,3,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	//标签名称列表
	FlagNameList []string `protobuf:"bytes,4,rep,name=flag_name_list,json=flagNameList,proto3" json:"flag_name_list"`
	//标签信息列表
	FlagList []*FlagInfo `protobuf:"bytes,5,rep,name=flag_list,json=flagList,proto3" json:"flag_list"`
	//操作人
	Operator             string   `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlagGroupInfo) Reset()         { *m = FlagGroupInfo{} }
func (m *FlagGroupInfo) String() string { return proto.CompactTextString(m) }
func (*FlagGroupInfo) ProtoMessage()    {}
func (*FlagGroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{12}
}

func (m *FlagGroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlagGroupInfo.Unmarshal(m, b)
}
func (m *FlagGroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlagGroupInfo.Marshal(b, m, deterministic)
}
func (m *FlagGroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlagGroupInfo.Merge(m, src)
}
func (m *FlagGroupInfo) XXX_Size() int {
	return xxx_messageInfo_FlagGroupInfo.Size(m)
}
func (m *FlagGroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FlagGroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FlagGroupInfo proto.InternalMessageInfo

func (m *FlagGroupInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *FlagGroupInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FlagGroupInfo) GetGroupId() string {
	if m != nil {
		return m.GroupId
	}
	return ""
}

func (m *FlagGroupInfo) GetFlagNameList() []string {
	if m != nil {
		return m.FlagNameList
	}
	return nil
}

func (m *FlagGroupInfo) GetFlagList() []*FlagInfo {
	if m != nil {
		return m.FlagList
	}
	return nil
}

func (m *FlagGroupInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

//创建或者修改标签，如果group_id=0则创建标签组和标签,如果flag_id=0创建标签，否则是修改
type CreateFlagRequest struct {
	//标签组名称
	GroupName string `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	//标签组ID 如果是创建传0
	GroupId int32 `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	//标签名称
	FlagName string `protobuf:"bytes,3,opt,name=flag_name,json=flagName,proto3" json:"flag_name"`
	//标签ID如果是创建传0
	FlagId int32 `protobuf:"varint,4,opt,name=flag_id,json=flagId,proto3" json:"flag_id"`
	//操作人
	Operator string `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator"`
	//判断类型，1：满足任意一个被选中的条件即可，2：必须满足所有被选中的条件
	CheckType int32 `protobuf:"varint,6,opt,name=check_type,json=checkType,proto3" json:"check_type"`
	//选取中的条件
	List []*ConditionInfo `protobuf:"bytes,7,rep,name=list,proto3" json:"list"`
	//1:手动标签 2:自动标签
	FlagType int32 `protobuf:"varint,8,opt,name=flag_type,json=flagType,proto3" json:"flag_type"`
	//标签组在企业微信的ID
	GroupWxId string `protobuf:"bytes,9,opt,name=group_wx_id,json=groupWxId,proto3" json:"group_wx_id"`
	//标签在企业微信的ID
	TagId                string   `protobuf:"bytes,10,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateFlagRequest) Reset()         { *m = CreateFlagRequest{} }
func (m *CreateFlagRequest) String() string { return proto.CompactTextString(m) }
func (*CreateFlagRequest) ProtoMessage()    {}
func (*CreateFlagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{13}
}

func (m *CreateFlagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateFlagRequest.Unmarshal(m, b)
}
func (m *CreateFlagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateFlagRequest.Marshal(b, m, deterministic)
}
func (m *CreateFlagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateFlagRequest.Merge(m, src)
}
func (m *CreateFlagRequest) XXX_Size() int {
	return xxx_messageInfo_CreateFlagRequest.Size(m)
}
func (m *CreateFlagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateFlagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateFlagRequest proto.InternalMessageInfo

func (m *CreateFlagRequest) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *CreateFlagRequest) GetGroupId() int32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *CreateFlagRequest) GetFlagName() string {
	if m != nil {
		return m.FlagName
	}
	return ""
}

func (m *CreateFlagRequest) GetFlagId() int32 {
	if m != nil {
		return m.FlagId
	}
	return 0
}

func (m *CreateFlagRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CreateFlagRequest) GetCheckType() int32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

func (m *CreateFlagRequest) GetList() []*ConditionInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *CreateFlagRequest) GetFlagType() int32 {
	if m != nil {
		return m.FlagType
	}
	return 0
}

func (m *CreateFlagRequest) GetGroupWxId() string {
	if m != nil {
		return m.GroupWxId
	}
	return ""
}

func (m *CreateFlagRequest) GetTagId() string {
	if m != nil {
		return m.TagId
	}
	return ""
}

//返回结果
type BaseResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	Data                 string   `protobuf:"bytes,5,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{14}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *BaseResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *BaseResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

//条件信息
type ConditionInfo struct {
	//条件ID
	ConditionId int32 `protobuf:"varint,1,opt,name=condition_id,json=conditionId,proto3" json:"condition_id"`
	//值1，如果是只有单个值的就会在这里
	Value1 string `protobuf:"bytes,2,opt,name=value1,proto3" json:"value1"`
	Value2 string `protobuf:"bytes,3,opt,name=value2,proto3" json:"value2"`
	//选中的条件
	SelType              string   `protobuf:"bytes,4,opt,name=sel_type,json=selType,proto3" json:"sel_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConditionInfo) Reset()         { *m = ConditionInfo{} }
func (m *ConditionInfo) String() string { return proto.CompactTextString(m) }
func (*ConditionInfo) ProtoMessage()    {}
func (*ConditionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{15}
}

func (m *ConditionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConditionInfo.Unmarshal(m, b)
}
func (m *ConditionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConditionInfo.Marshal(b, m, deterministic)
}
func (m *ConditionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConditionInfo.Merge(m, src)
}
func (m *ConditionInfo) XXX_Size() int {
	return xxx_messageInfo_ConditionInfo.Size(m)
}
func (m *ConditionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ConditionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ConditionInfo proto.InternalMessageInfo

func (m *ConditionInfo) GetConditionId() int32 {
	if m != nil {
		return m.ConditionId
	}
	return 0
}

func (m *ConditionInfo) GetValue1() string {
	if m != nil {
		return m.Value1
	}
	return ""
}

func (m *ConditionInfo) GetValue2() string {
	if m != nil {
		return m.Value2
	}
	return ""
}

func (m *ConditionInfo) GetSelType() string {
	if m != nil {
		return m.SelType
	}
	return ""
}

// 添加企业客户标签
type AddQWTagRequest struct {
	// 标签组id
	GroupId string `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	// 标签组名称，最长为30个字符
	GroupName string `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	// 标签组次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
	Order uint32 `protobuf:"varint,3,opt,name=order,proto3" json:"order"`
	//    // 授权方安装的应用agentid。仅旧的第三方多应用套件需要填此参数
	//    string agentid = 4;
	// 多个标签信息
	Tag                  []*TagRequest `protobuf:"bytes,4,rep,name=tag,proto3" json:"tag"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddQWTagRequest) Reset()         { *m = AddQWTagRequest{} }
func (m *AddQWTagRequest) String() string { return proto.CompactTextString(m) }
func (*AddQWTagRequest) ProtoMessage()    {}
func (*AddQWTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{16}
}

func (m *AddQWTagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddQWTagRequest.Unmarshal(m, b)
}
func (m *AddQWTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddQWTagRequest.Marshal(b, m, deterministic)
}
func (m *AddQWTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddQWTagRequest.Merge(m, src)
}
func (m *AddQWTagRequest) XXX_Size() int {
	return xxx_messageInfo_AddQWTagRequest.Size(m)
}
func (m *AddQWTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddQWTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddQWTagRequest proto.InternalMessageInfo

func (m *AddQWTagRequest) GetGroupId() string {
	if m != nil {
		return m.GroupId
	}
	return ""
}

func (m *AddQWTagRequest) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *AddQWTagRequest) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *AddQWTagRequest) GetTag() []*TagRequest {
	if m != nil {
		return m.Tag
	}
	return nil
}

type AddQWTagCTRequest struct {
	// 标签组id
	GroupId string `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	// 标签组名称，最长为30个字符
	GroupName string `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	// 标签组次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
	Order uint32 `protobuf:"varint,3,opt,name=order,proto3" json:"order"`
	//    // 授权方安装的应用agentid。仅旧的第三方多应用套件需要填此参数
	//    string agentid = 4;
	// 创建时间
	CreateTime int64 `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 多个标签信息
	Tag                  []*TagCTRequest `protobuf:"bytes,5,rep,name=tag,proto3" json:"tag"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddQWTagCTRequest) Reset()         { *m = AddQWTagCTRequest{} }
func (m *AddQWTagCTRequest) String() string { return proto.CompactTextString(m) }
func (*AddQWTagCTRequest) ProtoMessage()    {}
func (*AddQWTagCTRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{17}
}

func (m *AddQWTagCTRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddQWTagCTRequest.Unmarshal(m, b)
}
func (m *AddQWTagCTRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddQWTagCTRequest.Marshal(b, m, deterministic)
}
func (m *AddQWTagCTRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddQWTagCTRequest.Merge(m, src)
}
func (m *AddQWTagCTRequest) XXX_Size() int {
	return xxx_messageInfo_AddQWTagCTRequest.Size(m)
}
func (m *AddQWTagCTRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddQWTagCTRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddQWTagCTRequest proto.InternalMessageInfo

func (m *AddQWTagCTRequest) GetGroupId() string {
	if m != nil {
		return m.GroupId
	}
	return ""
}

func (m *AddQWTagCTRequest) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *AddQWTagCTRequest) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *AddQWTagCTRequest) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *AddQWTagCTRequest) GetTag() []*TagCTRequest {
	if m != nil {
		return m.Tag
	}
	return nil
}

type TagCTRequest struct {
	// 添加的标签名称，最长为30个字符
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 标签次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
	Order uint32 `protobuf:"varint,2,opt,name=order,proto3" json:"order"`
	// 创建时间
	CreateTime int64 `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 标签的id
	Id                   string   `protobuf:"bytes,4,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagCTRequest) Reset()         { *m = TagCTRequest{} }
func (m *TagCTRequest) String() string { return proto.CompactTextString(m) }
func (*TagCTRequest) ProtoMessage()    {}
func (*TagCTRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{18}
}

func (m *TagCTRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagCTRequest.Unmarshal(m, b)
}
func (m *TagCTRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagCTRequest.Marshal(b, m, deterministic)
}
func (m *TagCTRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagCTRequest.Merge(m, src)
}
func (m *TagCTRequest) XXX_Size() int {
	return xxx_messageInfo_TagCTRequest.Size(m)
}
func (m *TagCTRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TagCTRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TagCTRequest proto.InternalMessageInfo

func (m *TagCTRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TagCTRequest) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *TagCTRequest) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *TagCTRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type QWGroupsAllResponse struct {
	// 标签组id
	GroupId string `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	// 标签组名称，最长为30个字符
	GroupName string `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	// 标签组次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
	Order uint32 `protobuf:"varint,3,opt,name=order,proto3" json:"order"`
	// 授权方安装的应用agentid。仅旧的第三方多应用套件需要填此参数
	//string agentid = 4;
	// 创建时间
	CreateTime int64 `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 多个标签信息
	Tag []*QWTagsAllResponse `protobuf:"bytes,5,rep,name=tag,proto3" json:"tag"`
	// 	标签组是否已经被删除，只在指定tag_id进行查询时返回
	Deleted              bool     `protobuf:"varint,6,opt,name=deleted,proto3" json:"deleted"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QWGroupsAllResponse) Reset()         { *m = QWGroupsAllResponse{} }
func (m *QWGroupsAllResponse) String() string { return proto.CompactTextString(m) }
func (*QWGroupsAllResponse) ProtoMessage()    {}
func (*QWGroupsAllResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{19}
}

func (m *QWGroupsAllResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QWGroupsAllResponse.Unmarshal(m, b)
}
func (m *QWGroupsAllResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QWGroupsAllResponse.Marshal(b, m, deterministic)
}
func (m *QWGroupsAllResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QWGroupsAllResponse.Merge(m, src)
}
func (m *QWGroupsAllResponse) XXX_Size() int {
	return xxx_messageInfo_QWGroupsAllResponse.Size(m)
}
func (m *QWGroupsAllResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QWGroupsAllResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QWGroupsAllResponse proto.InternalMessageInfo

func (m *QWGroupsAllResponse) GetGroupId() string {
	if m != nil {
		return m.GroupId
	}
	return ""
}

func (m *QWGroupsAllResponse) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *QWGroupsAllResponse) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *QWGroupsAllResponse) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *QWGroupsAllResponse) GetTag() []*QWTagsAllResponse {
	if m != nil {
		return m.Tag
	}
	return nil
}

func (m *QWGroupsAllResponse) GetDeleted() bool {
	if m != nil {
		return m.Deleted
	}
	return false
}

type QWTagsAllResponse struct {
	// 添加的标签名称，最长为30个字符
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 标签次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
	Order uint32 `protobuf:"varint,2,opt,name=order,proto3" json:"order"`
	// 创建时间
	CreateTime int64 `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 标签的id
	Id string `protobuf:"bytes,4,opt,name=id,proto3" json:"id"`
	// 标签是否已经被删除，只在指定tag_id/group_id进行查询时返回
	Deleted              bool     `protobuf:"varint,5,opt,name=deleted,proto3" json:"deleted"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QWTagsAllResponse) Reset()         { *m = QWTagsAllResponse{} }
func (m *QWTagsAllResponse) String() string { return proto.CompactTextString(m) }
func (*QWTagsAllResponse) ProtoMessage()    {}
func (*QWTagsAllResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{20}
}

func (m *QWTagsAllResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QWTagsAllResponse.Unmarshal(m, b)
}
func (m *QWTagsAllResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QWTagsAllResponse.Marshal(b, m, deterministic)
}
func (m *QWTagsAllResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QWTagsAllResponse.Merge(m, src)
}
func (m *QWTagsAllResponse) XXX_Size() int {
	return xxx_messageInfo_QWTagsAllResponse.Size(m)
}
func (m *QWTagsAllResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QWTagsAllResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QWTagsAllResponse proto.InternalMessageInfo

func (m *QWTagsAllResponse) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *QWTagsAllResponse) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *QWTagsAllResponse) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *QWTagsAllResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *QWTagsAllResponse) GetDeleted() bool {
	if m != nil {
		return m.Deleted
	}
	return false
}

// 添加标签
type TagRequest struct {
	// 添加的标签名称，最长为30个字符
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 标签次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
	Order uint32 `protobuf:"varint,2,opt,name=order,proto3" json:"order"`
	// 标签的id
	Id                   string   `protobuf:"bytes,4,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagRequest) Reset()         { *m = TagRequest{} }
func (m *TagRequest) String() string { return proto.CompactTextString(m) }
func (*TagRequest) ProtoMessage()    {}
func (*TagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{21}
}

func (m *TagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagRequest.Unmarshal(m, b)
}
func (m *TagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagRequest.Marshal(b, m, deterministic)
}
func (m *TagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagRequest.Merge(m, src)
}
func (m *TagRequest) XXX_Size() int {
	return xxx_messageInfo_TagRequest.Size(m)
}
func (m *TagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TagRequest proto.InternalMessageInfo

func (m *TagRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TagRequest) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *TagRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

// 编辑标签的返回信息
type AddTagResponse struct {
	// 错误的code
	Errcode uint32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 错误的消息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	// 返回的信息data
	TagGroup             *AddQWTagCTRequest `protobuf:"bytes,3,opt,name=tag_group,json=tagGroup,proto3" json:"tag_group"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddTagResponse) Reset()         { *m = AddTagResponse{} }
func (m *AddTagResponse) String() string { return proto.CompactTextString(m) }
func (*AddTagResponse) ProtoMessage()    {}
func (*AddTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{22}
}

func (m *AddTagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTagResponse.Unmarshal(m, b)
}
func (m *AddTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTagResponse.Marshal(b, m, deterministic)
}
func (m *AddTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTagResponse.Merge(m, src)
}
func (m *AddTagResponse) XXX_Size() int {
	return xxx_messageInfo_AddTagResponse.Size(m)
}
func (m *AddTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddTagResponse proto.InternalMessageInfo

func (m *AddTagResponse) GetErrcode() uint32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *AddTagResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *AddTagResponse) GetTagGroup() *AddQWTagCTRequest {
	if m != nil {
		return m.TagGroup
	}
	return nil
}

// 编辑企业客户标签req
type UpdateQWTagRequest struct {
	// 标签或标签组的id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	// 新的标签或标签组名称，最长为30个字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 标签/标签组的次序值。order值大的排序靠前。有效的值范围是[0, 2^32)
	Order                uint32   `protobuf:"varint,3,opt,name=order,proto3" json:"order"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateQWTagRequest) Reset()         { *m = UpdateQWTagRequest{} }
func (m *UpdateQWTagRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateQWTagRequest) ProtoMessage()    {}
func (*UpdateQWTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{23}
}

func (m *UpdateQWTagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateQWTagRequest.Unmarshal(m, b)
}
func (m *UpdateQWTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateQWTagRequest.Marshal(b, m, deterministic)
}
func (m *UpdateQWTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateQWTagRequest.Merge(m, src)
}
func (m *UpdateQWTagRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateQWTagRequest.Size(m)
}
func (m *UpdateQWTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateQWTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateQWTagRequest proto.InternalMessageInfo

func (m *UpdateQWTagRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateQWTagRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateQWTagRequest) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

// 编辑企业客户标签resp
type QWTagResponse struct {
	// 返回码
	Errcode uint32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 对返回码的文本描述内容
	Errmsg               string   `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QWTagResponse) Reset()         { *m = QWTagResponse{} }
func (m *QWTagResponse) String() string { return proto.CompactTextString(m) }
func (*QWTagResponse) ProtoMessage()    {}
func (*QWTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{24}
}

func (m *QWTagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QWTagResponse.Unmarshal(m, b)
}
func (m *QWTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QWTagResponse.Marshal(b, m, deterministic)
}
func (m *QWTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QWTagResponse.Merge(m, src)
}
func (m *QWTagResponse) XXX_Size() int {
	return xxx_messageInfo_QWTagResponse.Size(m)
}
func (m *QWTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QWTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QWTagResponse proto.InternalMessageInfo

func (m *QWTagResponse) GetErrcode() uint32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *QWTagResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

// 删除标签的req
type DelQWFlagRequest struct {
	// 标签的id列表
	TagId []string `protobuf:"bytes,1,rep,name=tag_id,json=tagId,proto3" json:"tag_id"`
	// 标签组的id列表
	GroupId              []string `protobuf:"bytes,2,rep,name=group_id,json=groupId,proto3" json:"group_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelQWFlagRequest) Reset()         { *m = DelQWFlagRequest{} }
func (m *DelQWFlagRequest) String() string { return proto.CompactTextString(m) }
func (*DelQWFlagRequest) ProtoMessage()    {}
func (*DelQWFlagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{25}
}

func (m *DelQWFlagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelQWFlagRequest.Unmarshal(m, b)
}
func (m *DelQWFlagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelQWFlagRequest.Marshal(b, m, deterministic)
}
func (m *DelQWFlagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelQWFlagRequest.Merge(m, src)
}
func (m *DelQWFlagRequest) XXX_Size() int {
	return xxx_messageInfo_DelQWFlagRequest.Size(m)
}
func (m *DelQWFlagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelQWFlagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelQWFlagRequest proto.InternalMessageInfo

func (m *DelQWFlagRequest) GetTagId() []string {
	if m != nil {
		return m.TagId
	}
	return nil
}

func (m *DelQWFlagRequest) GetGroupId() []string {
	if m != nil {
		return m.GroupId
	}
	return nil
}

// 编辑客户企业标签
type EditQWRequest struct {
	// 添加外部联系人的userid
	Userid string `protobuf:"bytes,1,opt,name=userid,proto3" json:"userid"`
	// 外部联系人userid
	ExternalUserid string `protobuf:"bytes,2,opt,name=external_userid,json=externalUserid,proto3" json:"external_userid"`
	// 要标记的标签列表
	AddTag []string `protobuf:"bytes,3,rep,name=add_tag,json=addTag,proto3" json:"add_tag"`
	// 要移除的标签列表
	RemoveTag            []string `protobuf:"bytes,4,rep,name=remove_tag,json=removeTag,proto3" json:"remove_tag"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditQWRequest) Reset()         { *m = EditQWRequest{} }
func (m *EditQWRequest) String() string { return proto.CompactTextString(m) }
func (*EditQWRequest) ProtoMessage()    {}
func (*EditQWRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{26}
}

func (m *EditQWRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditQWRequest.Unmarshal(m, b)
}
func (m *EditQWRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditQWRequest.Marshal(b, m, deterministic)
}
func (m *EditQWRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditQWRequest.Merge(m, src)
}
func (m *EditQWRequest) XXX_Size() int {
	return xxx_messageInfo_EditQWRequest.Size(m)
}
func (m *EditQWRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EditQWRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EditQWRequest proto.InternalMessageInfo

func (m *EditQWRequest) GetUserid() string {
	if m != nil {
		return m.Userid
	}
	return ""
}

func (m *EditQWRequest) GetExternalUserid() string {
	if m != nil {
		return m.ExternalUserid
	}
	return ""
}

func (m *EditQWRequest) GetAddTag() []string {
	if m != nil {
		return m.AddTag
	}
	return nil
}

func (m *EditQWRequest) GetRemoveTag() []string {
	if m != nil {
		return m.RemoveTag
	}
	return nil
}

type GetQWFlagRequest struct {
	// 标签的id列表
	TagId []string `protobuf:"bytes,1,rep,name=tag_id,json=tagId,proto3" json:"tag_id"`
	// 标签组的id列表
	GroupId              []string `protobuf:"bytes,2,rep,name=group_id,json=groupId,proto3" json:"group_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQWFlagRequest) Reset()         { *m = GetQWFlagRequest{} }
func (m *GetQWFlagRequest) String() string { return proto.CompactTextString(m) }
func (*GetQWFlagRequest) ProtoMessage()    {}
func (*GetQWFlagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{27}
}

func (m *GetQWFlagRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQWFlagRequest.Unmarshal(m, b)
}
func (m *GetQWFlagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQWFlagRequest.Marshal(b, m, deterministic)
}
func (m *GetQWFlagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQWFlagRequest.Merge(m, src)
}
func (m *GetQWFlagRequest) XXX_Size() int {
	return xxx_messageInfo_GetQWFlagRequest.Size(m)
}
func (m *GetQWFlagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQWFlagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetQWFlagRequest proto.InternalMessageInfo

func (m *GetQWFlagRequest) GetTagId() []string {
	if m != nil {
		return m.TagId
	}
	return nil
}

func (m *GetQWFlagRequest) GetGroupId() []string {
	if m != nil {
		return m.GroupId
	}
	return nil
}

type GetQWtagResponse struct {
	Errcode              uint32                 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	Errmsg               string                 `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	TagGroup             []*QWGroupsAllResponse `protobuf:"bytes,3,rep,name=tag_group,json=tagGroup,proto3" json:"tag_group"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetQWtagResponse) Reset()         { *m = GetQWtagResponse{} }
func (m *GetQWtagResponse) String() string { return proto.CompactTextString(m) }
func (*GetQWtagResponse) ProtoMessage()    {}
func (*GetQWtagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5d3e7325a6e1ff7, []int{28}
}

func (m *GetQWtagResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQWtagResponse.Unmarshal(m, b)
}
func (m *GetQWtagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQWtagResponse.Marshal(b, m, deterministic)
}
func (m *GetQWtagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQWtagResponse.Merge(m, src)
}
func (m *GetQWtagResponse) XXX_Size() int {
	return xxx_messageInfo_GetQWtagResponse.Size(m)
}
func (m *GetQWtagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQWtagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetQWtagResponse proto.InternalMessageInfo

func (m *GetQWtagResponse) GetErrcode() uint32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *GetQWtagResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *GetQWtagResponse) GetTagGroup() []*QWGroupsAllResponse {
	if m != nil {
		return m.TagGroup
	}
	return nil
}

func init() {
	proto.RegisterType((*GetTaskListRequest)(nil), "crm.GetTaskListRequest")
	proto.RegisterType((*GetTaskListResponse)(nil), "crm.GetTaskListResponse")
	proto.RegisterType((*TaskList)(nil), "crm.TaskList")
	proto.RegisterType((*CreateBatchTaskRequest)(nil), "crm.CreateBatchTaskRequest")
	proto.RegisterType((*CreateBatchTaskResponse)(nil), "crm.CreateBatchTaskResponse")
	proto.RegisterType((*CustomerRequest)(nil), "crm.CustomerRequest")
	proto.RegisterType((*FlagResponse)(nil), "crm.FlagResponse")
	proto.RegisterType((*FlagGroupListRequest)(nil), "crm.FlagGroupListRequest")
	proto.RegisterType((*FlagGroupListResponse)(nil), "crm.FlagGroupListResponse")
	proto.RegisterType((*FlagListRequest)(nil), "crm.FlagListRequest")
	proto.RegisterType((*FlagListResponse)(nil), "crm.FlagListResponse")
	proto.RegisterType((*FlagInfo)(nil), "crm.FlagInfo")
	proto.RegisterType((*FlagGroupInfo)(nil), "crm.FlagGroupInfo")
	proto.RegisterType((*CreateFlagRequest)(nil), "crm.CreateFlagRequest")
	proto.RegisterType((*BaseResponse)(nil), "crm.BaseResponse")
	proto.RegisterType((*ConditionInfo)(nil), "crm.ConditionInfo")
	proto.RegisterType((*AddQWTagRequest)(nil), "crm.AddQWTagRequest")
	proto.RegisterType((*AddQWTagCTRequest)(nil), "crm.AddQWTagCTRequest")
	proto.RegisterType((*TagCTRequest)(nil), "crm.TagCTRequest")
	proto.RegisterType((*QWGroupsAllResponse)(nil), "crm.QWGroupsAllResponse")
	proto.RegisterType((*QWTagsAllResponse)(nil), "crm.QWTagsAllResponse")
	proto.RegisterType((*TagRequest)(nil), "crm.TagRequest")
	proto.RegisterType((*AddTagResponse)(nil), "crm.AddTagResponse")
	proto.RegisterType((*UpdateQWTagRequest)(nil), "crm.UpdateQWTagRequest")
	proto.RegisterType((*QWTagResponse)(nil), "crm.QWTagResponse")
	proto.RegisterType((*DelQWFlagRequest)(nil), "crm.DelQWFlagRequest")
	proto.RegisterType((*EditQWRequest)(nil), "crm.EditQWRequest")
	proto.RegisterType((*GetQWFlagRequest)(nil), "crm.GetQWFlagRequest")
	proto.RegisterType((*GetQWtagResponse)(nil), "crm.GetQWtagResponse")
}

func init() { proto.RegisterFile("crm/crm_center.proto", fileDescriptor_f5d3e7325a6e1ff7) }

var fileDescriptor_f5d3e7325a6e1ff7 = []byte{
	// 1769 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0x4b, 0x6f, 0xe3, 0xc8,
	0x11, 0x06, 0xa9, 0x27, 0x4b, 0x96, 0x65, 0xb7, 0x3d, 0xb6, 0x46, 0x4e, 0x36, 0x5e, 0x6e, 0xb2,
	0x31, 0x16, 0x81, 0x17, 0xf1, 0x62, 0xb1, 0x8b, 0xc9, 0x06, 0x88, 0xd7, 0x9e, 0x71, 0x04, 0x4c,
	0x06, 0xb0, 0xac, 0x81, 0x4f, 0x01, 0xc1, 0x21, 0xdb, 0x36, 0x61, 0x3e, 0x94, 0x66, 0x6b, 0xc6,
	0x9e, 0x3c, 0x4e, 0x39, 0xe4, 0x98, 0xdc, 0x02, 0xe4, 0x92, 0x6b, 0x0e, 0x39, 0xe4, 0x90, 0x4b,
	0xfe, 0x40, 0x80, 0xe4, 0x4f, 0x2d, 0xba, 0xba, 0x9b, 0x6a, 0x52, 0x1a, 0xcf, 0xcb, 0x9e, 0x93,
	0xd8, 0xd5, 0xdd, 0xea, 0xaf, 0xbe, 0xfa, 0xaa, 0xba, 0x48, 0x58, 0x0f, 0x58, 0xf2, 0x79, 0xc0,
	0x12, 0x2f, 0xa0, 0x29, 0xa7, 0x6c, 0x77, 0xc2, 0x32, 0x9e, 0x91, 0x5a, 0xc0, 0x12, 0xf7, 0xef,
	0x36, 0x90, 0x23, 0xca, 0xc7, 0x7e, 0x7e, 0xf9, 0x38, 0xca, 0xf9, 0x88, 0xfe, 0x66, 0x4a, 0x73,
	0x4e, 0x96, 0xc1, 0x8e, 0xc2, 0xbe, 0xb5, 0x6d, 0xed, 0x34, 0x46, 0x76, 0x14, 0x92, 0x8f, 0x61,
	0x89, 0xfb, 0xf9, 0xa5, 0x17, 0x64, 0x29, 0xa7, 0x29, 0xef, 0xdb, 0x38, 0xd3, 0x11, 0xb6, 0x03,
	0x69, 0x22, 0x3f, 0x00, 0x1c, 0x7a, 0x39, 0xf7, 0xf9, 0x34, 0xef, 0xd7, 0x70, 0x05, 0x08, 0xd3,
	0x09, 0x5a, 0xc8, 0x06, 0x34, 0xd5, 0x5c, 0x1d, 0xe7, 0xd4, 0x88, 0x6c, 0x81, 0x13, 0x30, 0xea,
	0x73, 0xea, 0x45, 0x61, 0xbf, 0xb1, 0x6d, 0xed, 0x38, 0xa3, 0xb6, 0x34, 0x0c, 0x43, 0x42, 0xa0,
	0x9e, 0x67, 0x8c, 0xf7, 0x9b, 0x68, 0xc7, 0x67, 0xf2, 0x11, 0x80, 0x9c, 0xe7, 0x51, 0x42, 0xfb,
	0x2d, 0x9c, 0x31, 0x2c, 0x62, 0xcf, 0xc4, 0x3f, 0xa7, 0xfd, 0x36, 0x1e, 0x83, 0xcf, 0x64, 0x00,
	0x6d, 0xf1, 0x7b, 0x12, 0xbd, 0xa4, 0x7d, 0x07, 0xed, 0xc5, 0x98, 0x7c, 0x1f, 0x20, 0xb8, 0xf0,
	0xd3, 0x94, 0xc6, 0x02, 0x01, 0xe0, 0xac, 0xa3, 0x2c, 0xc3, 0xd0, 0xfd, 0xbf, 0x05, 0x6b, 0x25,
	0x8a, 0xf2, 0x49, 0x96, 0xe6, 0x78, 0x4c, 0x90, 0x85, 0x54, 0xb1, 0x84, 0xcf, 0xa4, 0x0f, 0xad,
	0x84, 0xe6, 0xb9, 0x38, 0xdd, 0x46, 0x5c, 0x7a, 0x48, 0xd6, 0xa1, 0x41, 0x19, 0xcb, 0x18, 0x12,
	0xe3, 0x8c, 0xe4, 0xa0, 0x80, 0x5a, 0x7f, 0x05, 0xd4, 0x46, 0x05, 0xea, 0x3a, 0x34, 0x82, 0x6c,
	0x9a, 0x4a, 0x3e, 0x1a, 0x23, 0x39, 0x20, 0x9f, 0x81, 0x83, 0xd4, 0xc7, 0x51, 0xce, 0xfb, 0xad,
	0xed, 0xda, 0x4e, 0x67, 0xaf, 0xbb, 0x1b, 0xb0, 0x64, 0xb7, 0xc0, 0xdc, 0xe6, 0xea, 0xc9, 0xfd,
	0x57, 0x0d, 0xda, 0xda, 0x7c, 0x27, 0x61, 0xd6, 0x0b, 0x42, 0xca, 0xfd, 0x28, 0x46, 0xcf, 0x1c,
	0xb9, 0xe0, 0x10, 0x2d, 0xe4, 0x27, 0x40, 0xb2, 0x09, 0x65, 0x3e, 0x8f, 0xb2, 0xd4, 0x3b, 0x8b,
	0x62, 0xea, 0x4d, 0x59, 0xac, 0x02, 0xbf, 0x52, 0xcc, 0x3c, 0x8a, 0x62, 0xfa, 0x94, 0xc5, 0x64,
	0x07, 0x56, 0x18, 0xcd, 0xa7, 0x31, 0xa7, 0xb3, 0xb5, 0x52, 0x0c, 0xcb, 0xca, 0xae, 0x57, 0xce,
	0xf4, 0xd5, 0xaa, 0xea, 0x2b, 0xc9, 0xc2, 0xe8, 0xec, 0x5a, 0x44, 0xb7, 0x2d, 0xf5, 0x25, 0x0d,
	0xc3, 0x50, 0xa0, 0x55, 0x93, 0x28, 0x26, 0x47, 0xa2, 0x95, 0xa6, 0xb1, 0x10, 0x53, 0x49, 0x9d,
	0x50, 0x51, 0x67, 0x59, 0x39, 0x9d, 0x8a, 0x72, 0xc4, 0x9f, 0xab, 0xbd, 0xf8, 0xe7, 0x4b, 0xa6,
	0x52, 0xf1, 0xcf, 0x7f, 0x04, 0xcb, 0x4c, 0x66, 0x9c, 0x77, 0x41, 0xfd, 0x90, 0xb2, 0x7e, 0x17,
	0xd7, 0x74, 0x95, 0xf5, 0x97, 0x68, 0x14, 0x0a, 0xdc, 0x38, 0xc0, 0x5d, 0xdf, 0xfa, 0x3c, 0xb8,
	0x10, 0xe1, 0xd3, 0x89, 0x5a, 0x8d, 0x98, 0x35, 0x1f, 0xb1, 0xc5, 0x7c, 0xdb, 0xaf, 0xe0, 0xbb,
	0xe4, 0x6f, 0xad, 0xe2, 0xef, 0x16, 0x38, 0x67, 0xb1, 0x7f, 0xee, 0xa5, 0x7e, 0x42, 0x55, 0x64,
	0xdb, 0xc2, 0xf0, 0xc4, 0x4f, 0x28, 0xf9, 0x14, 0x7a, 0xe7, 0x2c, 0x9b, 0x4e, 0xbc, 0xd9, 0x12,
	0x19, 0xd4, 0x2e, 0x9a, 0x1f, 0xa9, 0x75, 0xee, 0xaf, 0x61, 0x73, 0xce, 0x99, 0xdb, 0x4b, 0x29,
	0xf7, 0xdf, 0x36, 0xf4, 0x0e, 0xa6, 0x39, 0xcf, 0x12, 0xca, 0x34, 0x4b, 0x1b, 0xd0, 0x9c, 0xe6,
	0x94, 0x29, 0xad, 0x3b, 0x23, 0x35, 0x22, 0x3f, 0x86, 0x1e, 0xbd, 0xe2, 0x94, 0xa5, 0x7e, 0xec,
	0xa9, 0x05, 0xf2, 0x8c, 0x65, 0x6d, 0x7e, 0x2a, 0x17, 0x6e, 0x81, 0x93, 0x46, 0xc1, 0xa5, 0xf4,
	0x4a, 0xb1, 0x22, 0x0c, 0xe8, 0xf8, 0x27, 0xd0, 0x0d, 0xd4, 0x81, 0x1e, 0xbf, 0x9e, 0xe8, 0x6c,
	0x5e, 0xd2, 0xc6, 0xf1, 0xf5, 0x84, 0x8a, 0x40, 0x15, 0x8b, 0x72, 0x7a, 0xa5, 0x32, 0xbb, 0xa3,
	0x6d, 0x27, 0xf4, 0x4a, 0xfc, 0x0f, 0xa3, 0x89, 0xcf, 0x2e, 0xbd, 0x24, 0x7b, 0x16, 0xc5, 0x54,
	0xe9, 0x7c, 0x49, 0x1a, 0x7f, 0x85, 0x36, 0x21, 0x39, 0x51, 0x0d, 0xbc, 0x28, 0x0d, 0xe9, 0x95,
	0x52, 0xba, 0x23, 0x2c, 0x43, 0x61, 0x10, 0x40, 0x71, 0x3a, 0x17, 0xd5, 0xa3, 0x5d, 0xa9, 0x1e,
	0x5b, 0xe0, 0x44, 0xb9, 0x47, 0xaf, 0x26, 0xa2, 0xa2, 0xaa, 0x2a, 0x18, 0xe5, 0x0f, 0x71, 0xec,
	0xfe, 0x01, 0x96, 0x44, 0x88, 0x6e, 0xb5, 0xbc, 0x7d, 0xa6, 0xf4, 0x12, 0xa5, 0x67, 0x19, 0xb2,
	0xa2, 0x0b, 0x93, 0x38, 0x69, 0x98, 0x9e, 0x65, 0x52, 0x3e, 0xe2, 0xc9, 0x3d, 0x83, 0x75, 0x61,
	0x3d, 0x12, 0x5a, 0x31, 0xaf, 0x22, 0x5d, 0x22, 0x2d, 0xa3, 0x44, 0x96, 0xbc, 0xb4, 0xe7, 0xcb,
	0xb9, 0xd4, 0xa1, 0x11, 0x2c, 0x07, 0x2d, 0x28, 0xbf, 0x7f, 0x5a, 0x70, 0xaf, 0x72, 0xd0, 0x2d,
	0x7a, 0xbc, 0x0e, 0x0d, 0x9e, 0x71, 0x3f, 0x56, 0x1a, 0x90, 0x03, 0xf2, 0x00, 0x7a, 0xc8, 0x83,
	0xc4, 0x85, 0x65, 0xba, 0x81, 0x65, 0x9a, 0x14, 0x6c, 0x20, 0x1c, 0xa4, 0xa4, 0x7b, 0x66, 0xa2,
	0x73, 0xff, 0x66, 0x41, 0x4f, 0x2c, 0x78, 0x2f, 0x4e, 0x74, 0xe2, 0xa2, 0x3c, 0x65, 0xcd, 0x46,
	0xe6, 0x51, 0x9a, 0x65, 0xc2, 0xea, 0x15, 0xc2, 0xca, 0x49, 0xdf, 0x28, 0x27, 0xbd, 0xfb, 0x57,
	0x0b, 0x56, 0x66, 0xe8, 0xee, 0x9c, 0x48, 0x2d, 0x28, 0x83, 0xc2, 0x45, 0x82, 0x42, 0xe2, 0xfe,
	0x6c, 0x43, 0x5b, 0x9b, 0xcb, 0x04, 0x58, 0x37, 0x12, 0x60, 0xdf, 0x48, 0x40, 0xad, 0x52, 0xf5,
	0xee, 0x03, 0x3e, 0x7b, 0xf9, 0x34, 0x51, 0x50, 0x5b, 0x62, 0x7c, 0x32, 0x4d, 0xc8, 0x26, 0xb4,
	0xa4, 0xfa, 0x43, 0x95, 0xed, 0x4d, 0x14, 0x7b, 0x48, 0x3e, 0x85, 0x3a, 0x3a, 0xd0, 0x34, 0x34,
	0x70, 0x90, 0xa5, 0x61, 0x24, 0x0a, 0x31, 0x7a, 0x81, 0xf3, 0xe4, 0x1e, 0x34, 0xb9, 0xdc, 0x2f,
	0x9b, 0x9c, 0x06, 0xc7, 0xed, 0xf7, 0xa1, 0x2d, 0xe1, 0xaa, 0xfb, 0xac, 0x31, 0x6a, 0xe1, 0x58,
	0x5f, 0x48, 0x34, 0xb8, 0x94, 0x7e, 0x3a, 0xfa, 0x42, 0xa2, 0xc1, 0xa5, 0x70, 0xd4, 0xfd, 0xaf,
	0x05, 0xdd, 0x92, 0xd8, 0x2a, 0xae, 0x5b, 0x55, 0xd7, 0x65, 0x83, 0x60, 0x17, 0x0d, 0x82, 0x79,
	0xb4, 0x64, 0xa2, 0x38, 0xfa, 0x87, 0xb0, 0x5c, 0xb0, 0x24, 0xe3, 0x53, 0xdf, 0xae, 0x89, 0xf2,
	0xa5, 0xa9, 0xc2, 0x8e, 0xe3, 0x2d, 0x02, 0x28, 0x1a, 0x21, 0x79, 0x3d, 0x65, 0x4c, 0x95, 0xc2,
	0x62, 0xec, 0xfe, 0xc7, 0x86, 0x55, 0x79, 0x8b, 0xc8, 0xa2, 0x25, 0xf3, 0xe2, 0x35, 0xde, 0x98,
	0xe8, 0xed, 0x32, 0x71, 0x37, 0xc6, 0xd8, 0x08, 0x64, 0xbd, 0x14, 0x48, 0x13, 0x61, 0xa3, 0x8c,
	0xb0, 0x12, 0x8a, 0x66, 0x25, 0x14, 0x85, 0x06, 0x5a, 0xaf, 0xd1, 0x40, 0x49, 0xb8, 0xed, 0x8a,
	0x70, 0x3f, 0x82, 0x8e, 0x74, 0xe8, 0xc5, 0x95, 0x00, 0xe7, 0x18, 0x0e, 0x9f, 0x5e, 0x0d, 0x43,
	0x43, 0x40, 0x60, 0x08, 0xc8, 0xfd, 0x1d, 0x2c, 0x7d, 0xeb, 0xe7, 0xf4, 0x03, 0xe4, 0x2b, 0x81,
	0x7a, 0xe8, 0x73, 0x5f, 0x91, 0x83, 0xcf, 0xee, 0xef, 0xa1, 0x5b, 0x72, 0x14, 0xaf, 0x46, 0x6d,
	0xf0, 0x8a, 0x7e, 0xb4, 0x53, 0xd8, 0x86, 0xa1, 0xb8, 0xc0, 0x9f, 0xfb, 0xf1, 0x94, 0xfe, 0x54,
	0x81, 0x51, 0xa3, 0xc2, 0xbe, 0xa7, 0xc0, 0xa8, 0x91, 0x88, 0x74, 0x4e, 0xe3, 0xd9, 0x6d, 0xec,
	0x8c, 0x5a, 0x39, 0x8d, 0x31, 0x07, 0xfe, 0x68, 0x41, 0x6f, 0x3f, 0x0c, 0x8f, 0x4f, 0xc7, 0x33,
	0xdd, 0x98, 0xc2, 0xb0, 0xca, 0xb2, 0x7e, 0x4d, 0x6d, 0x58, 0x87, 0x46, 0xc6, 0x44, 0xe3, 0x26,
	0xce, 0xef, 0x8e, 0xe4, 0x80, 0x7c, 0x0c, 0x35, 0xee, 0x9f, 0x63, 0x02, 0x74, 0xf6, 0x7a, 0xaa,
	0x15, 0xd7, 0xa7, 0x8d, 0xc4, 0x9c, 0xfb, 0x0f, 0x0b, 0x56, 0x35, 0x8c, 0x83, 0xf1, 0x5d, 0x01,
	0xa9, 0x74, 0xa0, 0x82, 0x8a, 0x5a, 0xa9, 0x03, 0xfd, 0x44, 0x22, 0x95, 0x99, 0xb8, 0xaa, 0x91,
	0x16, 0x80, 0x24, 0xd6, 0x08, 0x96, 0x4a, 0x28, 0x09, 0xd4, 0x8d, 0x04, 0xc3, 0xe7, 0xd9, 0xf9,
	0xf6, 0x0d, 0xe7, 0xd7, 0xe6, 0xce, 0x97, 0x05, 0x46, 0x86, 0xc8, 0x8e, 0x42, 0xf7, 0x7f, 0x16,
	0xac, 0x1d, 0x9f, 0x62, 0x7d, 0xca, 0xf7, 0xe3, 0xb8, 0x90, 0xe8, 0x07, 0x27, 0x66, 0xc7, 0x24,
	0x66, 0x03, 0x89, 0xc1, 0x58, 0x99, 0xa8, 0x90, 0x1d, 0x91, 0x29, 0x21, 0x8d, 0x29, 0xa7, 0x21,
	0x66, 0x79, 0x7b, 0xa4, 0x87, 0xee, 0x9f, 0x2c, 0x58, 0x9d, 0xdb, 0x74, 0x87, 0xec, 0x99, 0x50,
	0x1a, 0x65, 0x28, 0x8f, 0x00, 0x0c, 0xbd, 0xbf, 0x39, 0x84, 0x6a, 0x7c, 0x5e, 0xc0, 0xf2, 0x7e,
	0x18, 0x8e, 0x8d, 0x3e, 0xb1, 0x0f, 0x2d, 0xca, 0x58, 0x51, 0x3f, 0xba, 0x23, 0x3d, 0x14, 0xc9,
	0x49, 0x19, 0x4b, 0xf2, 0x73, 0x9d, 0xb4, 0x72, 0x44, 0xbe, 0x10, 0xaf, 0xab, 0xaa, 0x19, 0x42,
	0xa7, 0x34, 0xc1, 0x73, 0xf9, 0x20, 0xde, 0x5b, 0xe5, 0x65, 0xe5, 0x3e, 0x01, 0xf2, 0x74, 0x12,
	0xfa, 0x9c, 0x96, 0x12, 0x77, 0xf6, 0x02, 0x2b, 0x09, 0xd0, 0x8e, 0xd9, 0x8b, 0x1c, 0x33, 0x05,
	0xe0, 0xee, 0x43, 0x57, 0xfd, 0xd3, 0xbb, 0xfa, 0xe1, 0x1e, 0xc2, 0xca, 0x21, 0x8d, 0x8f, 0x4f,
	0xcd, 0x1b, 0x68, 0x56, 0x71, 0x2d, 0xbc, 0xfd, 0x16, 0x5c, 0xd9, 0x36, 0x4e, 0x68, 0xf9, 0x0a,
	0x91, 0x74, 0x1f, 0x86, 0x11, 0x3f, 0x3e, 0xbd, 0xb5, 0xb7, 0x95, 0x4d, 0x68, 0xf9, 0x61, 0xe8,
	0x09, 0xfd, 0xd6, 0xf0, 0xb0, 0xa6, 0x8f, 0x31, 0x13, 0xa9, 0xc2, 0x68, 0x92, 0x3d, 0xa7, 0x9e,
	0x2e, 0x4f, 0xce, 0xc8, 0x91, 0x96, 0xb1, 0x8f, 0x0e, 0x1d, 0x51, 0xfe, 0xbe, 0x0e, 0xfd, 0x56,
	0xfd, 0x0b, 0x7f, 0x2f, 0x91, 0x7c, 0x59, 0x16, 0x89, 0xc8, 0xc2, 0xbe, 0xca, 0xc2, 0xb9, 0xea,
	0x30, 0x93, 0xc9, 0xde, 0x5f, 0x1c, 0xe8, 0x08, 0xf8, 0x27, 0x94, 0x3d, 0x8f, 0x02, 0x4a, 0x86,
	0x08, 0xa6, 0xd4, 0xef, 0x93, 0xfb, 0xe5, 0xa6, 0xdb, 0x68, 0xac, 0x07, 0x83, 0x45, 0x53, 0xca,
	0x87, 0xaf, 0xa1, 0x27, 0x15, 0x58, 0x4c, 0x93, 0x05, 0xed, 0xfb, 0x40, 0x16, 0xd1, 0xd2, 0xfd,
	0xfa, 0x35, 0xf4, 0x0e, 0x31, 0x0f, 0xdf, 0x7a, 0xe7, 0x03, 0xe8, 0x28, 0xf8, 0x88, 0x7c, 0xbd,
	0xd8, 0x65, 0x82, 0xbe, 0x57, 0xb1, 0xaa, 0xbd, 0x5f, 0x01, 0xcc, 0x3a, 0x24, 0x22, 0x33, 0x6c,
	0xae, 0x65, 0x5a, 0x74, 0xe8, 0x2e, 0xc0, 0x0c, 0x2e, 0x29, 0xb7, 0x67, 0x8b, 0xd6, 0x7f, 0x05,
	0x6b, 0x27, 0xd7, 0x69, 0x70, 0xc1, 0xb2, 0x34, 0x7a, 0x49, 0xc7, 0xd9, 0x29, 0x3d, 0xb8, 0xf0,
	0xf9, 0x1b, 0x6c, 0xfc, 0xbc, 0xf0, 0x0e, 0xfb, 0x80, 0x85, 0x1b, 0x4a, 0xef, 0xa4, 0x0f, 0xa0,
	0x77, 0x44, 0xb9, 0x7e, 0xbb, 0x37, 0x28, 0xa9, 0xbc, 0xf0, 0x2f, 0x3a, 0xec, 0x1b, 0x58, 0x35,
	0xf6, 0xaa, 0x6f, 0x51, 0x6f, 0xbc, 0xfb, 0x08, 0xbf, 0x01, 0xea, 0x85, 0x95, 0x80, 0x54, 0xf7,
	0xdf, 0xa4, 0xa2, 0x9f, 0x03, 0x91, 0x2f, 0xdc, 0xef, 0xe6, 0xc5, 0x63, 0xe8, 0x55, 0x3e, 0x9e,
	0x90, 0x2d, 0x23, 0xb2, 0xd5, 0xef, 0x43, 0x83, 0xef, 0x2d, 0x9e, 0x54, 0xff, 0xf6, 0x0b, 0x0c,
	0x40, 0xf1, 0x39, 0x70, 0x13, 0x17, 0xcf, 0x7f, 0x0e, 0x1e, 0xf4, 0xe7, 0x27, 0x66, 0x22, 0xc3,
	0xaa, 0x2d, 0x9c, 0xcd, 0x95, 0x1b, 0x95, 0xee, 0x6a, 0xb0, 0xa6, 0xad, 0x66, 0xb9, 0xfd, 0x06,
	0xba, 0xba, 0x9e, 0xcb, 0xbd, 0xf2, 0xf0, 0xf9, 0x1a, 0x3f, 0x20, 0xb3, 0xcb, 0xd7, 0x3c, 0xb6,
	0x28, 0xbd, 0x39, 0x91, 0x09, 0x50, 0xad, 0xc5, 0x0b, 0x37, 0x7e, 0x09, 0x1d, 0x59, 0x6c, 0xe5,
	0x4e, 0xb9, 0xa4, 0x54, 0x7e, 0x17, 0x6e, 0xfb, 0x99, 0x22, 0xea, 0xdc, 0x3c, 0xb0, 0x5a, 0x2b,
	0x07, 0x86, 0xd9, 0x28, 0x7e, 0xcf, 0x9a, 0xf8, 0xbd, 0xfd, 0x8b, 0xef, 0x02, 0x00, 0x00, 0xff,
	0xff, 0x38, 0x62, 0xba, 0x3a, 0x87, 0x17, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// FlagServiceClient is the client API for FlagService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FlagServiceClient interface {
	//查询标签组列表
	GetFlagGroupList(ctx context.Context, in *FlagGroupListRequest, opts ...grpc.CallOption) (*FlagGroupListResponse, error)
	//修改标签组
	UpdateFlagGroup(ctx context.Context, in *FlagGroupInfo, opts ...grpc.CallOption) (*BaseResponse, error)
	//删除标签组
	DeleteFlagGroup(ctx context.Context, in *FlagGroupInfo, opts ...grpc.CallOption) (*BaseResponse, error)
	//查询标签列表
	GetFlagList(ctx context.Context, in *FlagListRequest, opts ...grpc.CallOption) (*FlagListResponse, error)
	//创建标签组和标签，修改标签
	CreateFlag(ctx context.Context, in *CreateFlagRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//删除标签
	DeleteFlag(ctx context.Context, in *FlagInfo, opts ...grpc.CallOption) (*BaseResponse, error)
	//同步标签到企业微信
	SynchronizeToWeChat(ctx context.Context, in *FlagInfo, opts ...grpc.CallOption) (*BaseResponse, error)
	//根据标签ID查询标签信息
	GetFlagInfo(ctx context.Context, in *FlagInfo, opts ...grpc.CallOption) (*FlagResponse, error)
	//根据条件获取客户列表数据
	GetCustomerList(ctx context.Context, in *CustomerRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//根据条件获取客户详情数据
	GetCustomerDetail(ctx context.Context, in *CustomerRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//根据用户信息获取用户的标签信息
	GetCustomerFlagList(ctx context.Context, in *CustomerRequest, opts ...grpc.CallOption) (*FlagGroupListResponse, error)
	//根据条件导出
	ExportCustomerList(ctx context.Context, in *CustomerRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//批量导入手动用户标签
	CreateBatchTask(ctx context.Context, in *CreateBatchTaskRequest, opts ...grpc.CallOption) (*CreateBatchTaskResponse, error)
	//导入用户标签
	GetTaskList(ctx context.Context, in *GetTaskListRequest, opts ...grpc.CallOption) (*GetTaskListResponse, error)
	// 提供给北京的rpc接口 创建企微标签
	AddQWFlags(ctx context.Context, in *AddQWTagRequest, opts ...grpc.CallOption) (*AddTagResponse, error)
	// 提供给北京的rpc接口 更新企微标签信息
	UpdateQWFlags(ctx context.Context, in *UpdateQWTagRequest, opts ...grpc.CallOption) (*QWTagResponse, error)
	//  提供给北京的rpc接口 删除企微标签信息
	DelQWFlags(ctx context.Context, in *DelQWFlagRequest, opts ...grpc.CallOption) (*QWTagResponse, error)
	// 提供给北京的rpc接口 编辑客户企业标签
	EditQWFlags(ctx context.Context, in *EditQWRequest, opts ...grpc.CallOption) (*QWTagResponse, error)
	// 提供给北京的rpc接口 获取企业标签库
	GetTagFlags(ctx context.Context, in *GetQWFlagRequest, opts ...grpc.CallOption) (*GetQWtagResponse, error)
}

type flagServiceClient struct {
	cc *grpc.ClientConn
}

func NewFlagServiceClient(cc *grpc.ClientConn) FlagServiceClient {
	return &flagServiceClient{cc}
}

func (c *flagServiceClient) GetFlagGroupList(ctx context.Context, in *FlagGroupListRequest, opts ...grpc.CallOption) (*FlagGroupListResponse, error) {
	out := new(FlagGroupListResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/GetFlagGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) UpdateFlagGroup(ctx context.Context, in *FlagGroupInfo, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/UpdateFlagGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) DeleteFlagGroup(ctx context.Context, in *FlagGroupInfo, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/DeleteFlagGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) GetFlagList(ctx context.Context, in *FlagListRequest, opts ...grpc.CallOption) (*FlagListResponse, error) {
	out := new(FlagListResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/GetFlagList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) CreateFlag(ctx context.Context, in *CreateFlagRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/CreateFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) DeleteFlag(ctx context.Context, in *FlagInfo, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/DeleteFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) SynchronizeToWeChat(ctx context.Context, in *FlagInfo, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/SynchronizeToWeChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) GetFlagInfo(ctx context.Context, in *FlagInfo, opts ...grpc.CallOption) (*FlagResponse, error) {
	out := new(FlagResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/GetFlagInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) GetCustomerList(ctx context.Context, in *CustomerRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/GetCustomerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) GetCustomerDetail(ctx context.Context, in *CustomerRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/GetCustomerDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) GetCustomerFlagList(ctx context.Context, in *CustomerRequest, opts ...grpc.CallOption) (*FlagGroupListResponse, error) {
	out := new(FlagGroupListResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/GetCustomerFlagList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) ExportCustomerList(ctx context.Context, in *CustomerRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/ExportCustomerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) CreateBatchTask(ctx context.Context, in *CreateBatchTaskRequest, opts ...grpc.CallOption) (*CreateBatchTaskResponse, error) {
	out := new(CreateBatchTaskResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/CreateBatchTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) GetTaskList(ctx context.Context, in *GetTaskListRequest, opts ...grpc.CallOption) (*GetTaskListResponse, error) {
	out := new(GetTaskListResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/GetTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) AddQWFlags(ctx context.Context, in *AddQWTagRequest, opts ...grpc.CallOption) (*AddTagResponse, error) {
	out := new(AddTagResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/AddQWFlags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) UpdateQWFlags(ctx context.Context, in *UpdateQWTagRequest, opts ...grpc.CallOption) (*QWTagResponse, error) {
	out := new(QWTagResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/UpdateQWFlags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) DelQWFlags(ctx context.Context, in *DelQWFlagRequest, opts ...grpc.CallOption) (*QWTagResponse, error) {
	out := new(QWTagResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/DelQWFlags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) EditQWFlags(ctx context.Context, in *EditQWRequest, opts ...grpc.CallOption) (*QWTagResponse, error) {
	out := new(QWTagResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/EditQWFlags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *flagServiceClient) GetTagFlags(ctx context.Context, in *GetQWFlagRequest, opts ...grpc.CallOption) (*GetQWtagResponse, error) {
	out := new(GetQWtagResponse)
	err := c.cc.Invoke(ctx, "/crm.FlagService/GetTagFlags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FlagServiceServer is the server API for FlagService service.
type FlagServiceServer interface {
	//查询标签组列表
	GetFlagGroupList(context.Context, *FlagGroupListRequest) (*FlagGroupListResponse, error)
	//修改标签组
	UpdateFlagGroup(context.Context, *FlagGroupInfo) (*BaseResponse, error)
	//删除标签组
	DeleteFlagGroup(context.Context, *FlagGroupInfo) (*BaseResponse, error)
	//查询标签列表
	GetFlagList(context.Context, *FlagListRequest) (*FlagListResponse, error)
	//创建标签组和标签，修改标签
	CreateFlag(context.Context, *CreateFlagRequest) (*BaseResponse, error)
	//删除标签
	DeleteFlag(context.Context, *FlagInfo) (*BaseResponse, error)
	//同步标签到企业微信
	SynchronizeToWeChat(context.Context, *FlagInfo) (*BaseResponse, error)
	//根据标签ID查询标签信息
	GetFlagInfo(context.Context, *FlagInfo) (*FlagResponse, error)
	//根据条件获取客户列表数据
	GetCustomerList(context.Context, *CustomerRequest) (*BaseResponse, error)
	//根据条件获取客户详情数据
	GetCustomerDetail(context.Context, *CustomerRequest) (*BaseResponse, error)
	//根据用户信息获取用户的标签信息
	GetCustomerFlagList(context.Context, *CustomerRequest) (*FlagGroupListResponse, error)
	//根据条件导出
	ExportCustomerList(context.Context, *CustomerRequest) (*BaseResponse, error)
	//批量导入手动用户标签
	CreateBatchTask(context.Context, *CreateBatchTaskRequest) (*CreateBatchTaskResponse, error)
	//导入用户标签
	GetTaskList(context.Context, *GetTaskListRequest) (*GetTaskListResponse, error)
	// 提供给北京的rpc接口 创建企微标签
	AddQWFlags(context.Context, *AddQWTagRequest) (*AddTagResponse, error)
	// 提供给北京的rpc接口 更新企微标签信息
	UpdateQWFlags(context.Context, *UpdateQWTagRequest) (*QWTagResponse, error)
	//  提供给北京的rpc接口 删除企微标签信息
	DelQWFlags(context.Context, *DelQWFlagRequest) (*QWTagResponse, error)
	// 提供给北京的rpc接口 编辑客户企业标签
	EditQWFlags(context.Context, *EditQWRequest) (*QWTagResponse, error)
	// 提供给北京的rpc接口 获取企业标签库
	GetTagFlags(context.Context, *GetQWFlagRequest) (*GetQWtagResponse, error)
}

// UnimplementedFlagServiceServer can be embedded to have forward compatible implementations.
type UnimplementedFlagServiceServer struct {
}

func (*UnimplementedFlagServiceServer) GetFlagGroupList(ctx context.Context, req *FlagGroupListRequest) (*FlagGroupListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFlagGroupList not implemented")
}
func (*UnimplementedFlagServiceServer) UpdateFlagGroup(ctx context.Context, req *FlagGroupInfo) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFlagGroup not implemented")
}
func (*UnimplementedFlagServiceServer) DeleteFlagGroup(ctx context.Context, req *FlagGroupInfo) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFlagGroup not implemented")
}
func (*UnimplementedFlagServiceServer) GetFlagList(ctx context.Context, req *FlagListRequest) (*FlagListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFlagList not implemented")
}
func (*UnimplementedFlagServiceServer) CreateFlag(ctx context.Context, req *CreateFlagRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFlag not implemented")
}
func (*UnimplementedFlagServiceServer) DeleteFlag(ctx context.Context, req *FlagInfo) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFlag not implemented")
}
func (*UnimplementedFlagServiceServer) SynchronizeToWeChat(ctx context.Context, req *FlagInfo) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SynchronizeToWeChat not implemented")
}
func (*UnimplementedFlagServiceServer) GetFlagInfo(ctx context.Context, req *FlagInfo) (*FlagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFlagInfo not implemented")
}
func (*UnimplementedFlagServiceServer) GetCustomerList(ctx context.Context, req *CustomerRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerList not implemented")
}
func (*UnimplementedFlagServiceServer) GetCustomerDetail(ctx context.Context, req *CustomerRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerDetail not implemented")
}
func (*UnimplementedFlagServiceServer) GetCustomerFlagList(ctx context.Context, req *CustomerRequest) (*FlagGroupListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerFlagList not implemented")
}
func (*UnimplementedFlagServiceServer) ExportCustomerList(ctx context.Context, req *CustomerRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportCustomerList not implemented")
}
func (*UnimplementedFlagServiceServer) CreateBatchTask(ctx context.Context, req *CreateBatchTaskRequest) (*CreateBatchTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBatchTask not implemented")
}
func (*UnimplementedFlagServiceServer) GetTaskList(ctx context.Context, req *GetTaskListRequest) (*GetTaskListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskList not implemented")
}
func (*UnimplementedFlagServiceServer) AddQWFlags(ctx context.Context, req *AddQWTagRequest) (*AddTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddQWFlags not implemented")
}
func (*UnimplementedFlagServiceServer) UpdateQWFlags(ctx context.Context, req *UpdateQWTagRequest) (*QWTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateQWFlags not implemented")
}
func (*UnimplementedFlagServiceServer) DelQWFlags(ctx context.Context, req *DelQWFlagRequest) (*QWTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelQWFlags not implemented")
}
func (*UnimplementedFlagServiceServer) EditQWFlags(ctx context.Context, req *EditQWRequest) (*QWTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditQWFlags not implemented")
}
func (*UnimplementedFlagServiceServer) GetTagFlags(ctx context.Context, req *GetQWFlagRequest) (*GetQWtagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTagFlags not implemented")
}

func RegisterFlagServiceServer(s *grpc.Server, srv FlagServiceServer) {
	s.RegisterService(&_FlagService_serviceDesc, srv)
}

func _FlagService_GetFlagGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlagGroupListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).GetFlagGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/GetFlagGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).GetFlagGroupList(ctx, req.(*FlagGroupListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_UpdateFlagGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlagGroupInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).UpdateFlagGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/UpdateFlagGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).UpdateFlagGroup(ctx, req.(*FlagGroupInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_DeleteFlagGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlagGroupInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).DeleteFlagGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/DeleteFlagGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).DeleteFlagGroup(ctx, req.(*FlagGroupInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_GetFlagList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlagListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).GetFlagList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/GetFlagList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).GetFlagList(ctx, req.(*FlagListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_CreateFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFlagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).CreateFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/CreateFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).CreateFlag(ctx, req.(*CreateFlagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_DeleteFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlagInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).DeleteFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/DeleteFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).DeleteFlag(ctx, req.(*FlagInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_SynchronizeToWeChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlagInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).SynchronizeToWeChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/SynchronizeToWeChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).SynchronizeToWeChat(ctx, req.(*FlagInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_GetFlagInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlagInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).GetFlagInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/GetFlagInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).GetFlagInfo(ctx, req.(*FlagInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_GetCustomerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).GetCustomerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/GetCustomerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).GetCustomerList(ctx, req.(*CustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_GetCustomerDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).GetCustomerDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/GetCustomerDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).GetCustomerDetail(ctx, req.(*CustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_GetCustomerFlagList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).GetCustomerFlagList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/GetCustomerFlagList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).GetCustomerFlagList(ctx, req.(*CustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_ExportCustomerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).ExportCustomerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/ExportCustomerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).ExportCustomerList(ctx, req.(*CustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_CreateBatchTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBatchTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).CreateBatchTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/CreateBatchTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).CreateBatchTask(ctx, req.(*CreateBatchTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_GetTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).GetTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/GetTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).GetTaskList(ctx, req.(*GetTaskListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_AddQWFlags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddQWTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).AddQWFlags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/AddQWFlags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).AddQWFlags(ctx, req.(*AddQWTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_UpdateQWFlags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateQWTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).UpdateQWFlags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/UpdateQWFlags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).UpdateQWFlags(ctx, req.(*UpdateQWTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_DelQWFlags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelQWFlagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).DelQWFlags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/DelQWFlags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).DelQWFlags(ctx, req.(*DelQWFlagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_EditQWFlags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditQWRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).EditQWFlags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/EditQWFlags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).EditQWFlags(ctx, req.(*EditQWRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FlagService_GetTagFlags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQWFlagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FlagServiceServer).GetTagFlags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crm.FlagService/GetTagFlags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FlagServiceServer).GetTagFlags(ctx, req.(*GetQWFlagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _FlagService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "crm.FlagService",
	HandlerType: (*FlagServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFlagGroupList",
			Handler:    _FlagService_GetFlagGroupList_Handler,
		},
		{
			MethodName: "UpdateFlagGroup",
			Handler:    _FlagService_UpdateFlagGroup_Handler,
		},
		{
			MethodName: "DeleteFlagGroup",
			Handler:    _FlagService_DeleteFlagGroup_Handler,
		},
		{
			MethodName: "GetFlagList",
			Handler:    _FlagService_GetFlagList_Handler,
		},
		{
			MethodName: "CreateFlag",
			Handler:    _FlagService_CreateFlag_Handler,
		},
		{
			MethodName: "DeleteFlag",
			Handler:    _FlagService_DeleteFlag_Handler,
		},
		{
			MethodName: "SynchronizeToWeChat",
			Handler:    _FlagService_SynchronizeToWeChat_Handler,
		},
		{
			MethodName: "GetFlagInfo",
			Handler:    _FlagService_GetFlagInfo_Handler,
		},
		{
			MethodName: "GetCustomerList",
			Handler:    _FlagService_GetCustomerList_Handler,
		},
		{
			MethodName: "GetCustomerDetail",
			Handler:    _FlagService_GetCustomerDetail_Handler,
		},
		{
			MethodName: "GetCustomerFlagList",
			Handler:    _FlagService_GetCustomerFlagList_Handler,
		},
		{
			MethodName: "ExportCustomerList",
			Handler:    _FlagService_ExportCustomerList_Handler,
		},
		{
			MethodName: "CreateBatchTask",
			Handler:    _FlagService_CreateBatchTask_Handler,
		},
		{
			MethodName: "GetTaskList",
			Handler:    _FlagService_GetTaskList_Handler,
		},
		{
			MethodName: "AddQWFlags",
			Handler:    _FlagService_AddQWFlags_Handler,
		},
		{
			MethodName: "UpdateQWFlags",
			Handler:    _FlagService_UpdateQWFlags_Handler,
		},
		{
			MethodName: "DelQWFlags",
			Handler:    _FlagService_DelQWFlags_Handler,
		},
		{
			MethodName: "EditQWFlags",
			Handler:    _FlagService_EditQWFlags_Handler,
		},
		{
			MethodName: "GetTagFlags",
			Handler:    _FlagService_GetTagFlags_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "crm/crm_center.proto",
}
