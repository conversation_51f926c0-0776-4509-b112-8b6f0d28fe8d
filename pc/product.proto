syntax = "proto3";
import "google/protobuf/wrappers.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
package pc;

//数据中心商品模块服务
service DcProduct {
  //新增、编辑品牌
  rpc NewBrand(Brand) returns (BaseResponse);
  //删除品牌
  rpc DelBrand(IdRequest) returns (BaseResponse);
  //品牌列表
  rpc QueryBrand(BrandRequest) returns (BrandResponse);
  //新增、编辑规格
  rpc NewSpec(Spec) returns (BaseResponse);
  //删除规格
  rpc DelSpec(IdRequest) returns (BaseResponse);
  //规格列表
  rpc QuerySpec(SpecRequest) returns (SpecResponse);
  //根据ID查询规格，支持多ID逗号分隔
  rpc QuerySpecSingle(IdRequest) returns (SpecResponse);
  //查询规格返回map类型
  rpc QuerySpecMap(IdRequest) returns (SpecResponse);
  //新增、编辑规格值
  rpc NewSpecValue(SpecValue) returns (BaseResponse);
  //删除规格值
  rpc DelSpecValue(IdRequest) returns (BaseResponse);
  //规格值列表
  rpc QuerySpecValue(IdRequest) returns (SpecValueResponse);
  // Map规格值
  rpc QuerySpecValueMap(IdRequest) returns (SpecValueResponse);
  //新增、编辑类型
  rpc NewType(Type) returns (BaseResponse);
  //删除类型
  rpc DelType(IdRequest) returns (BaseResponse);
  //类型列表
  rpc QueryType(TypeRequest) returns (TypeResponse);
  //新增、编辑属性
  rpc NewAttr(Attr) returns (BaseResponse);
  //删除属性
  rpc DelAttr(IdRequest) returns (BaseResponse);
  //根据ID查询属，多个ID以逗号分隔
  rpc QueryAttr(IdRequest) returns (AttrResponse);
  //新增、编辑属性值
  rpc NewAttrValue(AttrValue) returns (BaseResponse);
  //删除属性值
  rpc DelAttrValue(IdRequest) returns (BaseResponse);
  //属性值列表
  rpc QueryAttrValue(IdRequest) returns (AttrValueResponse);
  //新增、编辑商品分类
  rpc NewCategory(Category) returns (BaseResponse);
  //测试接口
  rpc NewTestService(Category) returns (BaseResponse);
  //删除商品分类
  rpc DelCategory(IdRequest) returns (BaseResponse);
  //获取商品分类列表
  rpc QueryCategory(CategoryRequest) returns (CategoryResponse);
  //根据ID递归查询分类
  rpc QueryCategoryRecursion(IdRequest) returns (CategoryResponse);
  //新增、编辑渠道商品分类
  rpc NewChannelCategory(Category) returns (NewChannelCategoryResponse);
  //删除渠道商品分类
  rpc DelChannelCategory(IdRequest) returns (BaseResponse);
  //排序渠道商品分类
  rpc SortChannelCategory(CategorySortRequest) returns (BaseResponse);
  //获取渠道商品分类列表
  rpc QueryChannelCategory(CategoryRequest) returns (CategoryResponse);
  // 获取渠道分类附加门店商品数量
  rpc ChannelCategoryWithCount(ChannelCategoryWithCountReq) returns (ChannelCategoryWithCountRes);
  //同步渠道分类
  rpc SyncChannelCategory(SyncChannelCategoryRequest) returns (BaseResponse);
  //新增、编辑分类归属
  rpc NewCategoryAttr(CategoryAttr) returns (BaseResponse);
  //删除分类归属
  rpc DelCategoryAttr(IdRequest) returns (BaseResponse);
  //获取分类归属列表
  rpc QueryCategoryAttr(CategoryAttrRequest) returns (CategoryAttrResponse);
  //新增、编辑分类导航
  rpc NewCategoryNav(CategoryNav) returns (BaseResponse);
  //获取分类导航列表
  rpc QueryCategoryNav(CategoryNavRequest) returns (CategoryNavResponse);
  //获取分类菜单
  rpc GetCategoryListTree(IdRequest) returns (CategoryListTreeResponse);
  //新增、编辑ERP
  rpc NewErp(Erp) returns (BaseResponse);
  //删除ERP
  rpc DelErp(IdRequest) returns (BaseResponse);
  //启用、停用ERP
  rpc ActiveErp(ActiveErpRequest) returns (BaseResponse);
  // ERP列表
  rpc QueryErp(ErpRequest) returns (ErpResponse);
  //新增、编辑商品Tag
  rpc NewTag(Tag) returns (BaseResponse);
  //删除商品分类
  rpc DelTag(IdRequest) returns (BaseResponse);
  //获取商品分类列表
  rpc QueryTag(TagRequest) returns (TagResponse);
  //新建商品库商品
  rpc NewProduct(ProductRequest) returns (BaseResponse);
  //编辑商品库商品
  rpc EditProduct(ProductRequest) returns (BaseResponse);
  //更新商品为已使用状态
  // rpc EditProductIsUse (ProductRequest) returns (BaseResponse);
  //删除商品库商品
  rpc DelProduct(ArrayIntValue) returns (BaseResponse);
  //分页条件查询商品库商品列表
  rpc QueryProduct(QueryProductRequest) returns (ProductResponse);
  // 分页查询sku商品信息
  rpc ListProductSku(ListProductSkuRequest) returns (ListProductSkuResponse);
  //根据一个或多个商品ID/SKUID查询商品信息（不包括SKU信息）
  rpc QueryProductOnly(OneofIdRequest) returns (ProductResponse);
  //查询渠道商品详情信息
  rpc QueryChannelProductDetail(OneofIdRequest)
returns (ChannelProductDetailResponse);
  //查询渠道商品信息（渠道）
  rpc QueryChannelProductOnly(OneofIdRequest) returns (ChannelProductResponse);
  //根据一个或多个商品ID/SKUID查询商品信息，包括SKU信息
  rpc QueryProductSku(OneofIdRequest) returns (ProductResponse);
  //根据商品ID查询商品属性
  rpc QueryProductAttr(IdRequest) returns (ProductAttrResponse);
  //根据商品ID查询渠道商品属性（渠道）
  rpc QueryChannelProductAttr(OneofIdRequest)
      returns (ChannelProductAttrResponse);
  //分页条件查询渠道商品库商品列表
  rpc QueryChannelProduct(QueryProductRequest) returns (ChannelProductResponse);
  //分页条件查询渠道上架的商品列表
  rpc QueryChannelProductUp(QueryProductRequest)
      returns (ChannelProductResponse);
  //分页条件查询渠道商品库商品列表导出
  rpc QueryChannelProductExport(QueryProductExportRequest)
      returns (ChannelProductExportResponse);
  //编辑商品库渠道商品
  rpc EditChannelProduct(ChannelProductRequest) returns (BaseResponse);
  // 渠道商品上架(支持阿闻,美团，饿了么，)
   rpc UpChannelProduct(UpDownChannelProductRequest) returns (BaseResponse);
  // 渠道商品下架(支持阿闻,美团，饿了么，京东)
  rpc DownChannelProduct(UpDownChannelProductRequest) returns (BaseResponse);
  // 渠道商品下架(支持阿闻,美团，饿了么，京东)
  rpc ThirdDownChannelProduct(ThirdDownChannelProductRequest) returns (BaseResponse);
  // 批量上架
  rpc BatchUpChannelProduct(UpDownChannelProductRequest) returns (BaseResponse);
  // 批量下架
  rpc BatchDownChannelProduct(UpDownChannelProductRequest)
      returns (BaseResponse);

  //保存美团任务id
  rpc NewMtTask(NewMtTaskRequest) returns (BaseResponse);
  //更新快照
  rpc UpdateSnapShot(UpdateSnapShotRequest) returns (BaseResponse);
  //更新快照
  rpc UpdateSnapShotNew(UpdateSnapShotRequest) returns (BaseResponse);
  //查询门店推荐商品
  rpc QueryStoreRecommendProduct(ChannelStoreProductRequest)
      returns (QueryStoreRecommendProductResponse);
  //查询门店最新上架商品
  rpc QueryStoreLatestProduct(ChannelStoreProductRequest)
      returns (QueryStoreRecommendProductResponse);
  // 查询宠物saas商品最新上架商品
 rpc QueryEshopStoreLatestProduct(ChannelStoreProductRequest)
  returns (QueryStoreRecommendProductResponse);
  //根据商品ID，查询SKU规格详细信息(系统默认)
  rpc QuerySkuValue(OneofIdRequest) returns (SkuValueResponse);
  //根据商品ID，查询SKU规格详细信息(系统默认)（渠道）
  rpc QueryChannelSkuValue(OneofIdRequest) returns (SkuValueResponse);
  //根据商品ID，查询第三方货号
  rpc QuerySkuThird(OneofIdRequest) returns (SkuThirdResponse);
  //根据商品ID，查询第三方货号（渠道）
  rpc QueryChannelSkuThird(OneofIdRequest) returns (SkuThirdResponse);
  //根据第三方系统类型，查询第三方货号列表
  rpc QuerySkuThirdList(SkuThirdListRequest) returns (SkuThirdListResponse);
  //根据商品ID，查询SKU信息，不包含商品主体信息
  rpc QuerySku(google.protobuf.Int32Value) returns (SkuResponse);
  //根据商品ID，查询渠道SKU信息，不包含商品主体信息
  rpc QueryChannelSku(OneofIdRequest) returns (SkuResponse);
  //查询组合商品的组合明细
  rpc QuerySkuGroup(OneofIdRequest) returns (SkuGroupResponse);
  // 查询渠道组合商品的组合明细
  rpc QueryChannelSkuGrouop(OneofIdRequest) returns (SkuGroupResponse);
  rpc GetThirdSpuSkuId(ArrayIntValue) returns (ThirdSpuSkuIdResponse);
  //从管家商品库认领商品到渠道
  rpc CopyGjProductToChannelProduct(CopyGjProductToChannelProductRequest)
      returns (BaseResponse);
  //图片、媒体库空间列表
  rpc MediaClassList(MediaClassListRequest) returns (MediaClassListResponse);
  //图片、视频列表
  rpc MediaItemList(MediaItemListRequest) returns (MediaItemListResponse);
  //媒体文件上传
  rpc MediaUpload(MediaUploadRequest) returns (MediaUploadResponse);
  //图片、视频删除
  rpc MediaItemDel(IdRequest) returns (BaseResponse);
  //相册、视频库删除
  rpc MediaClassDel(IdRequest) returns (BaseResponse);
  //根据商品thrid_sku_id查询sku_id
  rpc QuerySkuIddBySkuThird(NewSkuThirdRequest) returns (NewSkuThirdResponse);
  //根据商品sku_id查询thrid_sku_id
  rpc QuerySkuThirdQQd(NewSkuThirdResponse) returns (NewSkuThirdRequest);
  //获取渠道商品编辑快照（渠道）
  rpc QueryChannelProductSnapshot(ChannelProductSnapshotRequest)
      returns (ChannelProductSnapshotResponse);
  //新增一个商品快照（渠道）
  rpc NewChannelProductSnapshot(ChannelProductSnapshot) returns (BaseResponse);
  //获取商品在指定门店上架快照信息
  rpc GetChannelProductSnapshot(GetChannelProductSnapshotRequest)
      returns (ChannelProductDetailResponse);
  //获取美团商品信息
  rpc GetMtProductData(GetMtProductDataRequest) returns (BaseStringResponse);
  //门店关联的商品
  rpc NewChannelStoreProduct(NewChannelStoreProductRequest)
      returns (BaseResponse);
  //查询渠道门店商品
  rpc QueryChannelStoreProduct(ChannelStoreProductRequest)
      returns (ChannelStoreProductResponse);
  //查询渠道门店商品
  rpc QuerySaaSChannelStoreProduct(ChannelStoreProductRequest)
          returns (ChannelStoreProductResponse);
  //根据商品id分组查询权限范围内的渠道门店商品上架门店的数量（渠道）
  rpc QueryChannelStoreProductGroupByFinanceCodeCount(
      ChannelProductGroupByFinanceCodeCountRequest)
      returns (ChannelStoreProductResponse);
  // 查询商品库所有商品信息
  rpc ExportAllProduct(QueryProductRequest) returns (ExportProductResponse);
  //阿闻渠道批量新建
  rpc BatchCreateToAW(BatchCreateToAWRequest) returns (BatchBaseResponse);
  //阿闻渠道--批量更新
  rpc BatchUpdateToAW(BatchUpdateToAWRequest) returns (BatchUpdateToAWResponse);
  //平台商品库--新增异步任务数据接口
  rpc CreateBatchTask(CreateBatchTaskRequest) returns (CreateBatchTaskResponse);
  //平台商品库--删除异步任务数据接口
  rpc DeleteTask(DeleteTaskRequest) returns (DeleteTaskResponse);

  //平台商品库--获取异步任务数据接口
  rpc GetTaskList(GetTaskListRequest) returns (GetTaskListResponse);
  // 取消异步任务,只支持taskContent=67
  rpc CancelTask(CancelTaskRequest) returns (CancelTaskResponse);
  // 分布式调度器回调通知,更新任务状态及计算结果
  rpc SwitchWarehouseScheduleCallback(SwitchWarehouseScheduleCallbackRequest)
      returns (google.protobuf.Empty);
  // 切仓日志列表
  rpc ListSwitchWarehouseLog(ListSwitchWarehouseLogRequest)
      returns (ListSwitchWarehouseLogResponse);
  // 新增切仓日志
  rpc AddSwitchWarehouseLog(AddSwitchWarehouseLogRequest)
      returns (google.protobuf.Empty);

  //过期的虚拟商品自动下架任务
  rpc TestExpireProductDown(ProductTagsRequest) returns (BaseResponse);
  //阿闻渠道--批量上架
  rpc BatchOnTheShelfToAW(BatchOnTheShelfToAWRequest)
      returns (BatchOnTheShelfToAWResponse);

  //美团渠道--批量上架(v5.0向后兼容)
  rpc BatchOnTheShelfToMT(BatchOnTheShelfToMTRequest)
      returns (BatchOnTheShelfToMTResponse);
  //美团渠道--批量新建/更新
  rpc BatchToMT(BatchToMTRequest) returns (BatchBaseResponse);

  //饿了么渠道--批量新建/更新
  rpc BatchToElm(BatchToMTRequest) returns (BatchToMTResponse);
  //饿了么渠道--批量上架(v5.0向后兼容)
  rpc BatchOnTheShelfToElm(BatchOnTheShelfToMTRequest)
      returns (BatchOnTheShelfToMTResponse);
  //饿了么渠道--单门店上架单个商品(v5.0向后兼容)
  rpc SingleOnTheShelfToElm(BatchOnTheShelfToMTRequest)
      returns (BatchToMTResponse);

  //阿闻渠道--单个上架(v5.0向后兼容)
  rpc BatchOnTheShelfSingeToAW(BatchOnTheShelfToAWRequest)
      returns (BatchOnTheShelfToAWResponse);

  //查询当前渠道所有的下过架的数据并更新成上架
  rpc DealChannelProductUpsale(ChannelProductUnsaleRequest)
      returns (BatchBaseResponse);
  //根据商品id和渠道id获取该商品在哪些门店上架的信息
  rpc QueryStoreRecommendProductOnstatus(
      QueryStoreRecommendProductOnstatusRequest)
      returns (QueryStoreRecommendProductOnstatusResponse);
  //根据第三方货号批量查询
  rpc QuerySkuThirdzlCK(NewSkuThirdzlRequest) returns (SkuThirdResponse);

  /*阿闻管家*/
  //【管家商品库】编辑管家商品库商品
  rpc EditGjProduct(ProductRequest) returns (BaseResponse);
  //【管家商品库】根据一个或多个商品ID/SKUID查询商品信息（不包括SKU信息）
  rpc QueryGjProductOnly(OneofIdRequest) returns (ProductResponse);
  //【管家商品库】根据商品ID查询商品渠道信息
  rpc QueryGjProductChannel(IdRequest) returns (ProductChannelResponse);
  // 【管家商品库】保存商品渠道信息
  rpc SaveGjProductChannel(SaveGjProductChannelRequest) returns (BaseResponse);
  //【管家商品库】根据商品ID查询商品属性
  rpc QueryGjProductAttr(IdRequest) returns (ProductAttrResponse);
  //【管家商品库】根据商品ID，查询SKU信息，不包含商品主体信息
  rpc QueryGjSku(google.protobuf.Int32Value) returns (SkuResponse);
  //【管家商品库】根据商品ID，查询SKU规格详细信息(系统默认)
  rpc QueryGjSkuValue(OneofIdRequest) returns (SkuValueResponse);
  //【管家商品库】根据商品ID，查询第三方货号
  rpc QueryGjSkuThird(OneofIdRequest) returns (SkuThirdResponse);
  //根据skuId获取商品标签信息
  rpc GetProductTags(ProductTagsRequest) returns (ProductTagsResponse);
  //根据快照id新增到es
  rpc NewESData(NewESDataRequest) returns (BaseResponse);
  //根据门店和商品skuid或者spuid 获取快照信息，支持批量获取
  rpc GetChannelProductSnapshotBySpuOrSku(
      GetChannelProductSnapshotBySpuOrSkuRequest)
      returns (ChannelProductDetailResponse);
       //根据门店和商品skuid或者spuid 获取快照信息，支持批量获取
  rpc GetEshopProductSnapshotBySpuOrSku(
    GetChannelProductSnapshotBySpuOrSkuRequest)
    returns (ChannelProductDetailResponse);
  //城市编码，查询归属于同一城市的团餐商品id
  rpc QueryChainGoodsId(QueryChainGoodsReq) returns (ChainResponse);

  //价格同步：接收北京请求参数
  rpc AddProductPrice(AddProductRequest) returns (BaseResponse);

  /*京东到家*/
  //京东到家批量新建
  rpc BatchToJddj(BatchToJddjRequest) returns (BatchToJddjResponse);
  //京东到家同步上下架状态与库存(v5.0向后兼容)
  rpc SingleOnTheShelfToJddj(BatchOnTheShelfToJddjRequest)
      returns (BatchOnTheShelfToJddjResponse);
  //京东到家批量上架(v5.0向后兼容)
  rpc BatchOnTheShelfToJddj(BatchOnTheShelfToJddjRequest)
      returns (BatchOnTheShelfToJddjResponse);
  //京东到家批量上架(组合方法)(v5.0向后兼容)
  rpc JddjOnTheShelfMulti(BatchOnTheShelfToJddjRequest)
      returns (BatchOnTheShelfToJddjResponse);
  //京东到家同步门店价格
  rpc SyncJddjPrice(SyncJddjPriceRequest) returns (BaseResponse);
  //京东到家门店上架(v5.0向后兼容)
  rpc JddjBatchOnTheShelf(BatchOnTheShelfToJddjRequest)
      returns (BatchOnTheShelfToJddjResponse);
  //京东到家认领后更新快照
  rpc JddjNewChannelProductSnapshot(ChannelProductSnapshot)
      returns (BaseResponse);
  //根据商品分类ID和渠道ID查询第三方分类ID
  rpc GetThirdIdByPid(GetThirdIdByPidRequest) returns (GetThirdIdByPidResponse);
  //价格同步（供批量上架、上架、编辑上架）只需要更新快照表的价格信息,以及更新channel_store_product
  rpc UpdatePriceToChannel(UpdatePriceToChannelRequest) returns (BaseResponse);
  //查询北京价格数据
  rpc GetProductPriceByBJ(GetProductPriceByBJRequest)
      returns (GetProductPriceByBJResponse);
  //查询价格数据(本地没有查询北京)
  rpc GetProductPriceByLocalBJ(GetProductPriceByBJRequest)
      returns (GetProductPriceByBJResponse);

  // 下单增加门店商品销售量
  rpc UpdateStoreProductSalesVolume(UpdateSalesVolumeRequest)
      returns (BaseResponse);

  //获取同步到ES的商品数据
  rpc GetChannelProductEsBaseData(ChannelProductEsBaseDataRequest)
      returns (ChannelProductEsBaseDataResponse);
  //根据商品分类ID和渠道ID查询第三方分类ID
  rpc QueryAllChannelProduct(QueryAllChannelRequest)
      returns (QueryAllChannelResponse);

  //批量新建更新同步至饿了么
  rpc BatchToElmNew(BatchToMTRequest) returns (BatchBaseResponse);

  //饿了么单门店单商品价格修改
  rpc SingleUpdatePriceToElm(ChannelProductRequest) returns (BatchToMTResponse);

  //更新组合商品sku价格
  rpc UpdateGroupProductSkuPrice(GroupProductUpdatePriceRequest)
      returns (BaseResponse);
  //查询库存前端调用的，可以查询传参数给我们，不需要我们自己去查询
  rpc GetStockInfoBySkuCodeApi(GetStockInfoApiRequest)
      returns (GetStockInfoBySkuCodeResponse);
  // API内部调用，不是前段调用的
  rpc GetStockInfoBySkuCode(GetStockInfoRequest)
      returns (GetStockInfoBySkuCodeResponse);
  // 查询前置仓价格
  rpc GetPreposeWarehousePrice(GetPreposePriceRequest)
      returns (GetPreposePriceResponse);

  // 组合商品库存通知
  rpc GroupProductStockNotice(StockNoticeRequest) returns (BaseResponse);

  // 批量导入电商虚拟商品(脚本)SyncGoods
  rpc SyncGoods(ChannelStoreProduct) returns (BaseResponse);

  // 更新前置仓已经上架的商品价格，根据前置仓的价格表
  rpc SyncGoodsQzcPrice(PreposePriceUpInfo) returns (BaseResponse);

  // 获取标记为药品的spu
  rpc GetDrugsProductId(google.protobuf.Int32Value) returns (DrugsProductIdResponse);

  // 查询饿了么类目列表
  rpc GetElmCategoryList(CategoryListRequest) returns (ElmCategoryListResponse);
  // 更新饿了么类目列表
  rpc UpdateElmCategoryList(CategoryListRequest) returns (BaseResponse);

  // 导入前置仓价格信息
  rpc ImportA8Price(ImportA8PriceRequest) returns (ImportA8PriceResponse);

  // 查询操作记录表
  rpc QueryQzcOperationRecord(QueryA8PriceRecordRequest) returns (QueryA8PriceRecordResponse);

  // 查询前置仓价格表信息
  rpc QueryQzcPriceList(A8PriceInfoRequest) returns (A8PriceInfoResponse);

  // 编辑前置仓价格（需写入操作记录表）
  rpc UpdateQzcPrice(UpdateA8PriceRequest) returns (UpdateA8PriceResponse);

  // 删除前置仓价格信息
  rpc DelQzcPrice(UpdateA8PriceRequest) returns (UpdateA8PriceResponse);

  //根据传入的SKUID，过滤掉没有库存的返回
  rpc GetHasStockSkuIDs(HasStockSkuIDsRequest) returns (HasStockSkuIDsResponse);

  //根据传入的SKUID，过滤掉没有库存的返回
  rpc SearchWarehouse(SearchWarehouseRequest) returns (SearchWarehouseResponse);

  // 刷新旧的产品数据到es中，用完就不需要了
  rpc SyncProductToEs(IdRequest) returns (BaseResponse);

  // 查询本地视频号类目
  rpc GetWxVideoCategoryList(WxCategoryListRequest)
      returns (WxCategoryListResponse);
  // 保存微信视频号类目列表
  rpc SaveWxVideoCategoryList(CategoryListRequest) returns (BaseResponse);
  // 创建微信视频号商品
  rpc CreateWxVideoProductInfo(AddWxProductInfoRequest)
      returns (AddProductInfoListResponse);
  // 查询组合商品的子商品信息
  rpc QueryChildProducts(QueryChildProductsRequest)
      returns (ChildProductsResponse);

  // 获取用户最后一条进行中的任务
  rpc GetUserUnFinishedTask(GetUserUnFinishedTaskRequest) returns (TaskList);

  //门店绑定虚拟仓
  rpc ShopBindWarehouse(BindShopWarehouse) returns (BindShopWarehouseResponse);
  //获取并删除门店的分类
  rpc GetAndDeleteCategory(SyncCategoryRequest)
      returns (GetAndDeleteCategoryResponse);
  //门店绑定虚拟仓
  rpc RenewCategory(SyncCategoryRequest) returns (RenewCategoryResponse);
  //门店绑定虚拟仓
  rpc RelateCategoryAndProduct(SyncCategoryRequest)
      returns (RelateCategoryAndProductResponse);
  //导入分类同步任务
  rpc ImportSyncCategoryShop(ImportSyncCategoryShopRequest)
      returns (BaseResponse);
  // 分布式调度器回调通知,更新任务状态及计算结果
  rpc SyncChannelCategoryScheduleCallback(
      SyncChannelCategoryScheduleCallbackRequest)
      returns (google.protobuf.Empty);
  // 分布式调度处理美团任务
  rpc DealWithMeiTuanChannel(SyncCategoryScheduleRequest)
      returns (SyncCategoryScheduleResponse);
  // 分布式调度处理eleme任务
  rpc DealWithEleMeChannel(SyncCategoryScheduleRequest)
      returns (SyncCategoryScheduleResponse);
  // 分布式调度处理jddj任务
  rpc DealWithJDDJChannel(SyncCategoryScheduleRequest)
      returns (SyncCategoryScheduleResponse);
  // SyncCategorySchedulerCallBack
  rpc SyncCategorySchedulerCallBack(SyncCategoryScheduleCallBackRequest)
      returns (BaseResponse);
  // 取消分类同步异步任务
  rpc CancelSyncChannelCategoryTask(CancelTaskRequest)
      returns (CancelTaskResponse);
  // 任务重试
  rpc RollBackTask(DeleteTaskRequest) returns (BaseResponse);

  rpc CancelSyncChannelCategoryThirdTask(CancelTaskRequest)
      returns (CancelTaskResponse);

  // 下架子龙无效(已停用或不存在)商品
  rpc OffshelfZilongInvalidProduct(OffshelfZilongInvalidProductRequest)
      returns (OffshelfZilongInvalidProductResponse);

  rpc AddTaskProductFromOms(AddTaskProductFromOmsVo) returns (BaseResponse);

  // 获取渠道设置的仓库关系
  rpc GetFinanceCodeWarehouseRelation(GetFinanceCodeWarehouseRelationVO)
      returns (GetFinanceCodeWarehouseRelationResp);

  // 互联网医院商品价格导入
  rpc HospitalProductPriceImport(HospitalProductPriceImportReq)
      returns (BaseResponse);
  // 互联网医院商品价格列表
  rpc HospitalProductPriceList(HospitalProductPriceListReq)
      returns (HospitalProductPriceListRes);
  // 互联网医院商品价格移除
  rpc HospitalProductPriceDel(IdRequest) returns (BaseResponse);
  // 互联网医院商品价格编辑
  rpc HospitalProductPriceEdit(HospitalProductPriceEditReq)
      returns (BaseResponse);
  // 互联网医院商品价格编辑
  rpc OperateRecord(OperateRecordReq) returns (OperateRecordRes);

  // 电商仓商品价格列表
  rpc MallProductPriceList(MallProductPriceListReq)
      returns (MallProductPriceListRes);
  // 电商仓商品价格移除
  rpc MallProductPriceDel(IdRequest) returns (BaseResponse);
  // 电商仓商品价格编辑
  rpc MallProductPriceEdit(MallProductPriceEditReq) returns (BaseResponse);

  // 检测添加购物车
  rpc CheckAddCart(CheckAddCartReq) returns (CheckAddCartRes);

  //可上架商品列表
  rpc GetUploadSkuList(UploadSkuListRequest) returns (UploadSkuListResponse);

  //可上架商品移除
  rpc RemoveUploadSku(UploadSkuDeleteRequest) returns (UploadSkuDeleteResponse);

  // 获取代运营配置文件
  rpc GetAgencyConfig(google.protobuf.Empty) returns (GetAgencyConfigResp);

  // 导入可上架商品
  rpc ImportAgencyProduct(ImportAgencyProductVo)
      returns (ImportAgencyProductResponse);

  // /渠道商品上下架
  rpc  AddUpOrDownTask(CreateBatchTaskRequest) returns (BaseResponse);
  // 获取产品多级分类
  rpc  GetProductCategory(GetProductCategoryRequest) returns (GetProductCategoryResponse);

  // 移动分类商品数据
  rpc MoveCategoryProduct(MoveProductVo) returns (BaseResponse);

  // 切换仓库提供的批量编辑接口
  // 编辑到美团
  rpc BatchToMTNew(BatchToMTRequest) returns (BaseResponse);
  //  编辑到饿了么
  rpc BatchToELENew(BatchToMTRequest) returns (BaseResponse);
  //  编辑到京东
  rpc BatchToJDDJNew(BatchToMTRequest) returns (BaseResponse);
  //  编辑到京东
  rpc DelProductYJ(DelProductParameter) returns (BaseResponse);
  // 删除渠道商品
  rpc DeleteChannelProduct(DeleteProductVo) returns (BaseResponse);
  // 查询渠道商品记录
  rpc ChannelProductRecords(ChannelProductRecordsVo) returns (RecordChannelProductRes);
  // 查询平台商品记录
  rpc ProductRecords(ProductRecordsVo) returns (RecordProductRes);
  // R1价格同步
  rpc R1PriceSync(R1PriceSyncReq) returns(ProductBaseResponse);
  // R1价格同步到Sku
  rpc R1PriceSyncSku(R1PriceSyncSkuReq) returns(ProductBaseResponse);
  // 诊断病种列表
  rpc DiagnoseDic(ProductDiagnoseDicReq) returns(ProductDiagnoseDicRes);
  // 子龙商品处方药属性同步
  rpc ZiLongDrugSync(ZiLongDrugSyncReq) returns(ProductBaseResponse);
  // 处方药查询对应病症
  rpc QueryDisease(ProductQueryDiseaseReq) returns(ProductQueryDiseaseRes);
  // 查询商品药品属性
  rpc DrugInfo(ProductDrugInfoReq) returns(ProductDrugInfoRes);
  // 获取药品用量对应的宠物类型
  rpc PetType(google.protobuf.Empty) returns(ProductPetTypeRes);

  // 电商会员商品分类
  rpc ShopCateList(ShopCateReq) returns (ShopCateListResponse);

  // 电商会员商品分类-按等级筛选
  rpc ShopCateListByClassify(ShopCateListReq) returns (ShopCateListByClassifyResponse);

  //到家会员商品分类
  rpc AwenCateList(AwenCateListReq) returns (ShopCateListResponse);
  //到家会员商品列表
  rpc AwenVipGoodsList(VipGoodsRequest) returns (VipGoodsResponse);
  //电商会员商品列表
  rpc ShopVipGoodsList(VipGoodsRequest) returns (VipGoodsResponse);
  //会员商品明细
  rpc ShopVipCardGoodsList(ShopVipCardGoodsRequest) returns (ShopVipCardGoodsResponse);
  //删除付费会员商品
  rpc DelShopVipCardGoods(DelShopVipCardGoodsReq) returns (ProductBaseResponse);
  //电商竖屏实物商品列表
  rpc ShopGoodsList(GoodsRequest) returns (GoodsResponse);

  // 门店修改仓库
  rpc EditShopBindWarehouse(ShopBindingWarehouseReq) returns (BaseResponse);
}

message OperateRecordReq {
  // 0全部 1自己的
  int32 type = 1;
  // 35互联网医院价格  37仓库白名单
  int32 task_content = 2;
  int32 page_index = 3;
  int32 page_size = 4;
  //创建人（前端不用传）
  string create_id = 5;
}

message OperateRecordRes {
  string msg = 1;
  int32 total = 2;
  repeated OperateRecordData data = 3;
}

message OperateRecordData {
  int32 id = 1;
  //操作类型：1移除商品  2编辑商品 3移除仓库白名单
  int32 operate_type = 2;
  //操作详情
  string operate_detail = 3;
  //创建人姓名
  string create_name = 4;
  //创建人ip
  string create_ip = 5;
  // ip所属位置
  string ip_location = 6;
  //创建时间
  string create_time = 7;
}

message SearchWarehouseResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  int32 warehouse_ids = 4;
}
message SearchWarehouseRequest {
  //财务编码
  string finance_code = 1;
  //渠道
  int32 channel_id = 2;
  //来源
  int32 user_agent = 3;
  // 配送方式: 1自提 2非自提
  int32 ad_code = 4;
}

message DrugsProductIdResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated int32 product_id = 4;
}
message StockNoticeRequest {
  //门店库存信息（支持批量）
  repeated StockStore stock_info = 1;
}
message StoreSku {
  int32 sku_id = 1;
  // 库存数量（最小0）
  int32 stock = 2;
}

message StockStore {
  //财务编码
  string finance_code = 1;
  //当前门店下的sku库存明细
  repeated StoreSku skus = 2;
}

message GroupProductUpdatePriceRequest {
  //最后更新时间
  string update_date = 1;
  // 更新目标表的前缀（gj_=管家，channel_=渠道）,为空则更新平台的
  string table_prefix = 2;
}

message ChannelProductEsBaseDataResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated ChannelProductEsBaseData details = 4;
}
message ChannelProductEsBaseData {
  string json_data = 1;
  string finance_code = 2;
  string channel_id = 3;
  int32 sku_id = 4;
  int32 up_down_state = 5;
  int32 sales_volume = 6;
  string tags = 7;
  string update_date = 8;
  // 5.5.2版本上线成功后需要废弃的字段
  int32 has_stock = 9;
  // 5.5.2版本新增的仓库的id
  int32 warehouse_id = 10;
  int32 warehouse_category = 12;
}
message ChannelProductEsBaseDataRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  string update_time = 3;
  string finance_code = 4;
}

message UpdatePriceToChannelRequest {
  string finance_code = 1;
  int32 product_id = 2;
  int32 sku_id = 3;
  int32 channel_id = 4;
  string ProductCode = 5;
}

message GetProductPriceByBJRequest {
  //产品编码
  repeated string ProductCode = 1;
  //财务编码
  repeated string StructCode = 2;
}
message GetProductPriceByBJResponse {
  int32 Code = 1;
  string Msg = 2;
  repeated ProductPriceData Data = 3;
  int32 Status = 4;
}

message ProductPriceData {
  repeated string Struct_Code = 1;
  repeated NewProductInfo Product_Info = 2;
}

message NewProductInfo {
  string Product_Code = 1;
  string Sell_Price = 2;
}

//请求接口参数
message AddProductRequest { string response = 1; }

message GetThirdIdByPidRequest {
  string category_id = 1;
  string channel_id = 2;
}
message GetThirdIdByPidResponse {
  int32 code = 1;
  string message = 2;
  string third_id = 3;
}

message ChainResponse { map<int32, int32> goods_id = 1; }

//根据快照id新增es的请求id
message NewESDataRequest {
  //快照id
  int32 id = 1;
  // sku
  //商品skuId
  int32 sku_id = 2;
}

message ProductTagsRequest {
  //商品skuId
  int32 sku_id = 1;
}

message ProductTagsResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated ProductTagsModel data = 4;
}

message ProductTagsModel {
  //主键id
  int32 id = 1;
  //商品skuid
  int32 sku_id = 2;
  //商品类别（1-实物商品，2-虚拟商品）
  int32 product_type = 3;
  //物种
  string species = 4;
  //品种
  string varieties = 5;
  //性别
  string sex = 6;
  //体型
  string shape = 7;
  //年龄
  string age = 8;
  //特殊阶段
  string special_stage = 9;
  //是否绝育
  string is_sterilization = 10;
  //内容类型
  string content_type = 11;
  //状态
  string status = 12;
}

message SyncJddjPriceRequest {
  int32 outSkuId = 1;
  int32 storePrice = 2;
  int32 preposePrice = 3;
  string financeCode = 4;
}

message CopyGjProductToChannelProductRequest {
  repeated int32 product_id = 1;
  repeated int32 channel_id = 2;
  string updateFields = 3;
  // 是否是批量认领
  bool is_batch = 4;
}

message NewSkuThirdzlRequest {
  repeated string Sku_Third_List = 1;
  int32 ErpId = 2;
}

message GetChannelProductSnapshotRequest {
  int32 channel_id = 1;
  string finance_code = 2;
  int32 channel_category_id = 3;
  int32 up_down_state = 4;
  int32 page_index = 5;
  int32 page_size = 6;
  // product_id切片
  repeated int32 product_id = 7;
  // 1商品id列表外的数据 0商品id列表内的数据
  int32 product_type = 8;
  //置顶商品sku_id
  int32 top_sku_id = 9;
  // sku_id切片
  repeated int32 sku_id = 10;
  //是否有库存的 0 不判断  1 只查询有库存的
  int32 has_stock = 11;
  // 仓库id
  int32 warehouse_id = 12;
}

//根据spu或者sku获取商品信息的参数
message GetChannelProductSnapshotBySpuOrSkuRequest {
  int32 channel_id = 1;
  string finance_code = 2;
  repeated int32 product_id = 3;
  repeated int32 sku_id = 4;
}

message QueryStoreRecommendProductResponse {
  repeated StoreRecommendProduct data = 1;
}

message StoreRecommendProduct {
  //财务编码
  string finance_code = 1;
  repeated ChannelProductDetail details = 2;
}

message UpdateSnapShotRequest {
  //财务编码
  repeated string finance_code = 1;
  //渠道ID
  int32 channel_id = 2;
  //商品ID
  repeated int32 product_id = 3;
}

message SyncChannelCategoryRequest {
  string channel_store_id = 1;
  int32 channel_id = 2;
}

message NewMtTaskRequest {
  //任务ID
  int32 task_id = 1;
  //任务数据
  string task_data = 2;
}

message ChannelProductGroupByFinanceCodeCountRequest {
  //商品ID
  repeated int32 product_id = 1;
  //渠道ID
  int32 channel_id = 2;
  //财务编码
  repeated string finance_code = 3;
}
message BaseStringResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  string data = 4;
  string err_list =5;
}

message NewChannelStoreProductRequest { repeated ChannelStoreProduct info = 1; }

message ChannelStoreProductResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated ChannelStoreProduct details = 4;
  int64 total_count = 5;
}

message ChannelProductSnapshotRequest {
  int32 id = 1;
  //渠道id
  int32 channel_id = 2;
  //用户编号
  string user_no = 3;
  //商品id
  repeated int32 product_id = 4;
  repeated int32 ids = 5;
  //财务编码
  string finance_code = 6;
  // sku_id
  repeated int32 sku_id = 7;
}

message CopyProductRequest {
  //商品id
  string product_id = 1;
  //渠道id
  string channel_id = 2;
}

message OneofIdRequest {
  oneof id {
    ArrayIntValue sku_id = 1;
    ArrayIntValue product_id = 2;
    ArrayIntValue erp_id = 3;
  }
  int32 channel_id = 4;
}

message SkuGroupResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated SkuGroup details = 4;
}

//任务列表模型
message TaskList {
  //任务id
  int32 id = 1;
  //任务内容:1:批量新建;2:批量更新;3：渠道-批量新建 4：渠道-批量上架 5:批量认领
  // 6:认领全部商品 7:批量认领或全部认领 8:药品全部下架 9:药品恢复上架
  // 10:药品相关任务 11:渠道--商品导出 12:渠道--批量更新 13批量同步 15批量脚本
  // 16 商品分类同步 17 全量同步分类到指定门店 18 商品从渠道下架处理 19
  //批量导入前置仓价格信息 20 平台商品导出
  // 67:美团替换门店绑定前置仓为前置虚拟仓任务
  int32 task_content = 2;
  //任务状态:1:未开始;2:进行中;3:已完成 4:已取消
  int32 task_status = 3;
  //任务详情
  string task_detail = 4;
  //操作文件路径
  string operation_file_url = 5;
  //操作结果文件路径
  string resulte_file_url = 6;
  //状态:1:正常;2:冻结;
  int32 status = 7;
  //创建人id
  string modify_id = 8;
  //创建时间
  string modify_time = 9;
  //创建人id
  string create_id = 10;
  //渠道id
  int32 channel_id = 11;
  //创建时间
  string create_time = 12;
  //操作请求的token值，类似userinfo
  string request_header = 13;
  //创建人姓名
  string create_name = 14;
  //创建人手机号
  string create_mobile = 15;
  //创建人IP
  string create_ip = 16;
  // ip位置
  string ip_location = 17;
  //成功数量
  int32 success_num = 18;
  //失败数量
  int32 fail_num = 19;
  //任务名称扩展
  string extended_data = 20;
  // 操作门店数量
  int32 shop_num = 21;
  // 仓库类型 4-前置仓 5-虚拟前置仓
  int32 category = 22;
  // 上下文数据
  string context_data = 23;
  //任务类型名称
  string task_content_name = 24;
}

//阿闻渠道批量新建--响应参数
message BatchCreateToAWResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

message NewChannelCategoryResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  int32 category_id = 4;
}

message GetMtProductDataRequest {
  repeated int32 product_id = 1;
  int32 channel_id = 2;
  repeated string finance_code = 3;
  //数据类型：0单门店单商品，1单门店多商品，2多门店多商品
  int32 type = 4;
  //是否异步
  int32 is_async = 5;
  int32 category = 6;
}

message ChannelStoreProductRequest {
  //渠道id
  int32 channel_id = 1;
  //门店财务编码
  repeated string finance_code = 2;
  //商品id
  repeated int32 product_id = 3;
  int32 page_index = 4;
  int32 page_size = 5;
  //上下架状态（1上架，0下架，-1所有）
  int32 up_down_state = 6;
  // 排序
  string order_by = 7;
  //渠道分类id
  int32 channel_category_id = 8;
  //商品名称
  string name = 9;
  // sku
  repeated int32 sku_id = 10;
}

message ChannelProductSnapshotResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated ChannelProductSnapshot details = 4;
}

message ThirdSpuSkuIdResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  google.protobuf.Struct details = 4;
}

message NewSkuThirdRequest {
  string Sku_Third = 1;
  int32 ErpId = 2;
}

message NewSkuThirdResponse {
  string SkuId = 1;
  int32 ErpId = 2;
  string SpuId = 3;
}

message SkuThirdResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated SkuThird details = 4;
}

message SkuValueResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated SkuValue details = 4;
}

message SkuInfoResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated SkuInfo details = 4;
}

//商品库新增商品的SKU信息
message SkuInfo {
  //第三方SKU货号
  repeated SkuThird sku_third = 1;
  //规格信息
  repeated SkuValue skuv = 2;
  //建议价格
  int32 retail_price = 3;
  // SKU ID
  int32 sku_id = 4;
  //商品ID
  int32 product_id = 5;
  //市场价
  int32 market_price = 6;
  //组合商品（类型为组合商品才需要）
  repeated SkuGroup sku_group = 7;
  //商品条码
  string bar_code = 8;
  //渠道ID
  int32 channel_id = 9;
  //是否可用
  int32 is_use = 10;
  //重量
  double weight_for_unit = 11;
  //重量单位
  string weight_unit = 12;
  //最小购买数量
  int32 min_order_count = 13;
  //价格单位
  string price_unit = 14;
  //前置仓价格
  int32 prepose_price = 15;
  //门店仓价格
  int32 store_price = 16;
  //商品销量
  int32 sales_volume = 17;
  //会员价
  int32 member_price = 18;
  //促销类型 0无促销，1团购，2限时折扣
  int32 promotion_type = 19;
  //商品促销价格
  int32 promotion_price = 20;

  // 库位（宠物saas用）
  string location_code = 21;
}

//新建商品库商品请求内容
message ProductRequest {
  //商品主表
  Product product = 1;
  // SKU信息
  repeated SkuInfo sku_info = 2;
  //商品属性
  repeated ProductAttr product_attr = 3;
  //商品标签
  ProductTags product_tags = 4;
}

//商品标签
message ProductTags {
  //主键id(新建时不传，编辑时必传)
  int32 id = 1;
  //商品skuid
  int32 sku_id = 2;
  //商品id
  int32 product_id = 3;
  //商品类别（1-电商实物商品，2-电商虚拟商品，3-本地生活实物商品，4-本地生活虚拟商品）
  int32 product_type = 4;
  //物种
  string species = 5;
  //品种
  string varieties = 6;
  //性别
  string sex = 7;
  //体型
  string shape = 8;
  //年龄
  string age = 9;
  //特殊阶段
  string special_stage = 10;
  //是否绝育
  string is_sterilization = 11;
  //内容类型
  string content_type = 12;
  //状态
  string status = 13;
}

//新建商品库商品请求内容
message ChannelProductRequest {
  //商品主表
  ChannelProduct product = 1;
  // SKU信息
  repeated SkuInfo sku_info = 2;
  //商品属性
  repeated ChannelProductAttr product_attr = 3;
  //财务编码
  string finance_code = 4;
  //商品标签
  string tags = 5;
}

// 渠道商品上架
message UpDownChannelProductRequest {
  // 渠道Id
  int32 channelId = 1;
  // 财务编码
  repeated string financeCode = 2;
  // 商品Id
  repeated string productId = 3;
  // 当前操作人
  string userNo = 4;
  //创建人姓名
  string user_name = 5;
  //创建人电话
  string user_mobile = 6;
  //创建人ip
  string user_ip = 7;
  // ip所属位置
  string ip_location = 8;

  // 是否切换仓库
  bool IsSwitchingWarehouse = 9;

}

// 第三方回调渠道商品下架
message ThirdDownChannelProductRequest {
  // 渠道Id
  int32 channelId = 1;
  // 第三方需要下架的数据
  repeated ThirdDownChannelProductDetail detailThird = 2;
  // 我们数据库需要下架的数据
  repeated ThirdDownChannelProductDetail detail = 3;
  // 当前操作人
  string userNo = 4;
  //创建人姓名
  string user_name = 5;
}

// 渠道商品下架详情
message ThirdDownChannelProductDetail {
  // APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
   string appPoiCode = 1;
  // 商品Id
  repeated string productId = 2;
}


//商品的SKU列表
message SkuResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Sku details = 4;
}

//批量改价详情查询请求
message ProductBatchModifyDetailsRequest {
  int32 page_index = 1;
  int32 page_size = 2;
}

//编辑商品响应内容
message ProductOneResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //商品主表
  Product product = 4;
  // SKU信息
  repeated SkuInfo sku_info = 5;
  //商品属性
  repeated ProductAttr product_attr = 6;
}

//编辑渠道商品响应内容
message ChannelProductDetailResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated ChannelProductDetail details = 4;
  int64 total_count = 5;
}

message ChannelProductDetail {
  //商品主表
  ChannelProduct product = 1;
  // SKU信息
  repeated SkuInfo sku_info = 2;
  //商品属性
  repeated ChannelProductAttr product_attr = 3;
}

//编辑渠道商品响应内容
message ChannelProductOneResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //商品主表
  ChannelProduct product = 4;
  // SKU信息
  repeated SkuInfo sku_info = 5;
  //商品属性
  repeated ChannelProductAttr product_attr = 6;
}

//商品查询列表响应内容
message ProductResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Product details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

//渠道商品查询列表响应内容
message ChannelProductResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated ChannelProduct details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

//渠道商品查询列表响应内容
message ChannelProductExportResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //渠道门店商品详情
  repeated ChannelProducExportModel details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

//商品库列表查询条件
message QueryProductRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  //查询条件
  string where = 3;
  //查询条件的类型（商品筛选类型，为空则为综合筛选（name=商品名称，bar_code=商品条码，code=商品编码，code=商品ID））
  string where_type = 4;
  //分类id
  int32 category_id = 5;
  //品牌ID
  int32 brand_id = 6;
  //商品类别（0-所有，1-实物商品，2-虚拟商品, 3-组合商品）
  int32 product_type = 7;
  //是否为组合商品
  int32 is_group = 8;
  //商品是否删除条件（0:查询未删除/未停用的商品，1:查询已删除/已停用的商品）
  int32 is_del = 9;
  //渠道id
  int32 channel_id = 10;
  //上下架状态（-1查询所有，1上架，0下架）
  int32 up_down_state = 11;
  //是否是推荐商品,1是0否
  int32 is_recommend = 12;
  //门店财务编码
  string finance_code = 13;
  //是否为查询管家商品库
  int32 is_gj = 14;
  // 是否查询药品商品 0全部、1药品
  int32 is_drugs = 15;
  //商品应用范围（0全部, 1电商，2前置仓，3门店仓）
  int32 use_range = 16;
  //是否排除过期虚拟商品
  int32 is_exclude = 17;

  // int32 0: 全部商品 1：医疗互联网商品
  int32 select_type = 18;

  // int32 0：全部 1：OMS同步 2：后台新增 3：后台导入
  int32 source_type = 19;

  // 渠道商品库侧边栏分类id
  int32 store_category_id = 20;
  // 是否查询药品商品 0全部、1处方药
  int32 is_prescribed_drug = 21;
}

//商品库列表导出查询条件
message QueryProductExportRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  //查询条件
  string where = 3;
  //查询条件的类型（商品筛选类型，为空则为综合筛选（name=商品名称，bar_code=商品条码，code=商品编码，code=商品ID））
  string where_type = 4;
  //分类id
  int32 category_id = 5;
  //品牌ID
  int32 brand_id = 6;
  //商品类别（0-所有，1-实物商品，2-虚拟商品）
  int32 product_type = 7;
  //是否为组合商品
  int32 is_group = 8;
  //商品是否删除条件（0:查询未删除的商品，1:查询已删除的商品）
  int32 is_del = 9;
  //渠道id
  int32 channel_id = 10;
  //上下架状态（-1查询所有，1上架，0下架）
  int32 up_down_state = 11;
  //门店财务编码
  repeated string finance_code = 12;
}

//编辑商品属性响应内容
message ProductAttrResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //商品属性
  repeated ProductAttr details = 4;
}

//查询sku商品条件
message ListProductSkuRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  //查询条件
  string where = 3;
  //查询条件的类型（商品筛选类型，为空则为综合筛选（name=商品名称，bar_code=商品条码，code=商品编码，code=商品ID））
  string where_type = 4;
  //是否排除过期虚拟商品
  int32 is_exclude = 5;
  //是否为组合商品
  int32 is_group = 6;
}
message ListProductSkuResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated ProductSku details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}
// sku商品信息
message ProductSku {
  //商品ID
  int32 id = 1;
  // skuId
  int32 sku_id = 2;
  //分类id
  int32 category_id = 3;
  //品牌id
  int32 brand_id = 4;
  //渠道id
  string channel_id = 5;
  //商品名称
  string name = 6;
  //商品编号
  string code = 7;
  //商品类别（1-实物商品，2-虚拟商品, 3-组合商品）
  int32 product_type = 8;
  // 规格列表
  repeated SpecValue spec_list = 9;
  int32 term_type = 10;
  int32 term_value = 11;
}

// 管家商品渠道信息响应内容
message ProductChannelResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //渠道信息
  repeated ProductChannel details = 4;
}

// 保存管家商品渠道信息
message SaveGjProductChannelRequest {
  //渠道信息
  repeated ProductChannel list = 1;
}

// 管家渠道信息
message ProductChannel {
  //商品ID
  int32 product_id = 1;
  //渠道id
  int32 channel_id = 2;
  // 分类ID
  int32 category_id = 3;
  // 定制化属性
  repeated SimpleChannelAttr attr = 4;
}
// 渠道定制化属性
message SimpleChannelAttr {
  int32 id = 1;
  //属性ID
  int32 attr_id = 2;
  //属性值ID
  string attr_value_id = 3;
  //属性名称
  string attr_name = 4;
  //属性值
  string attr_value = 5;
}

//编辑商品属性响应内容
message ChannelProductAttrResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //商品属性
  repeated ChannelProductAttr details = 4;
}

//第三方sku货号列表查询条件
message SkuThirdListRequest {
  //第三方系统类型,多个之间用英文逗号分隔
  string systemid = 1;
  //起始修改时间
  string startmodified = 2;
  //结束修改时间
  string endmodified = 3;
  //商品名称
  string productname = 4;
  //第三方货号
  string thirdskuid = 5;

  int32 page_index = 6;
  int32 page_size = 7;
}

//第三方sku货号列表响应内容
message SkuThirdListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated SkuThirdProduct details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

message IdRequest {
  string id = 1;
  string ip_addr = 2;
  //创建人ID（不用前端传）
  string create_id = 3;
  //创建人姓名（不用前端传）
  string create_name = 4;
  //创建人IP所属位置（不用前端传）
  string ip_location = 5;
}

message ArrayStringValue { repeated string value = 1; }

message ArrayIntValue { repeated int32 value = 1; }

//分类列表查询条件
message CategoryRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  string data_type = 3;
  Category where = 4;
  //是否排除无商品分类，true是false否
  bool filter_empty = 5;
  string finance_code = 6;
  // 仓库id
  int32 warehouse_id = 7;
}

//商品分类响应内容
message CategoryResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Category details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

// ERP列表查询条件
message ErpRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  Erp where = 3;
}

//启用、停用ERP
message ActiveErpRequest {
  string id = 1;
  string status = 2;
}

// ERP响应内容
message ErpResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Erp details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

// Tag列表查询条件
message TagRequest {
  string type_name = 1;
  int32 page_index = 2;
  int32 page_size = 3;
  Tag where = 4;
}

// Tag响应内容
message TagResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Tag details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

//品牌列表查询条件
message BrandRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  Brand where = 3;
}

//商品品牌响应内容
message BrandResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Brand details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

//规格列表查询条件
message SpecRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  Spec where = 3;
}

//商品规格响应内容
message SpecResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Spec details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
  map<int32, Spec> spec = 6;
}

//规格值列表查询条件
message SpecValueRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  SpecValue where = 3;
}

//商品规格值响应内容
message SpecValueResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated SpecValue details = 4;
  map<int32, SpecValue> spec_value = 5;
}

//商品属性响应内容
message AttrResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Attr details = 4;
}

//商品属性值响应内容
message AttrValueResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated AttrValue details = 4;
}

//规格值列表查询条件
message TypeRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  Type where = 3;
  int32 spec_id = 4;
  string key_name = 5;
}

//商品规格值响应内容
message TypeResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Type details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

//分类归属列表查询条件
message CategoryAttrRequest {
  int32 page_index = 1;
  int32 page_size = 2;
  CategoryAttr where = 3;
}

//分类归属响应内容
message CategoryAttrResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated CategoryAttr details = 4;
  //返回总数，用于分页
  int32 total_count = 5;
}

//分类导航列表查询条件
message CategoryNavRequest { CategoryNav where = 1; }

//分类导航响应内容
message CategoryNavResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated CategoryNav details = 4;
}

//分类菜单列表相应
message CategoryListTreeResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated CategoryListTree details = 4;
}

message BaseResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated string details = 4;
  repeated UpDownERROR up_down_detail = 5;
}

message BatchBaseResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated string details = 4;
  // 成功个数
  int32 success_num = 5;
  // 失败个数
  int32 fail_num = 6;
  // 错误信息excel链接
  string qiniu_url = 7;
}

// Models
message Attr {

  int32 id = 1;
  //属性名称
  string name = 2;
  //属性排序
  int32 sort = 3;
  //属性显示，1显示 2不显示
  int32 is_show = 4;
}

message AttrValue {
  int32 id = 1;
  //属性值
  string value = 2;
  //属性ID
  int32 attr_id = 3;
  //属性名称
  string name = 4;
}

message Brand {
  int32 id = 1;
  //品牌名称
  string name = 2;
  //排序
  int32 sort = 3;
  // logo图片
  string logo = 4;
  //品牌描述
  string description = 5;

  string create_date = 6;
  //是否推荐
  int32 is_recommend = 7;
  //展现形式 1-图片，2-文字
  int32 show_type = 8;
  //首字母
  string initial = 9;

  int32 company_id = 10;
  //所属分类id
  int32 brand_category_id = 11;
  //所属分类名称
  string category_name = 12;
}

message Category {
  // uuid 平台分类id
  int32 id = 1;
  //分类名称
  string name = 2;
  //父类id
  int32 parent_id = 3;
  //排序
  int32 sort = 4;
  //创建时间
  string create_date = 5;
  //是否允许发布虚拟商品
  int32 is_invented = 6;
  //商品展展示方式（1-颜色，2-SPU）
  int32 product_show_type = 7;
  //关联的类型ID
  int32 type_id = 8;
  //分类的TAG标签，多个逗号分开
  string tag = 9;
  //父类名称
  string parent_name = 10;
  // Tag名称
  string tag_name = 11;
  //时候含有子菜单
  int32 has_sub = 12;
  //归属ID
  int32 category_arr_id = 13;
  //类别名称
  string type_name = 14;
  //类型关联子分类
  int32 connect_type = 15;
  //虚拟商品关联子分类
  int32 connect_invent = 16;
  //展示方式关联子分类
  int32 connect_show_type = 17;
  //图片(三级分类存)
  string img = 18;
  //渠道id
  int32 channel_id = 19;
  //是否可发布需要核销的虚拟商品
  int32 is_verify = 20;
  //是否可发布需要核销的虚拟商品关联子分类
  int32 connect_verify = 21;
  //是否可发布不需要核销的虚拟商品
  int32 is_noverify = 22;
  //是否可发布不需要核销的虚拟商品关联子分类
  int32 connect_noverify = 23;
  //修改时间
  string update_date = 24;
  //第三方分类id
  string third_category_id = 25;

  // 创建ip
  string ip_addr = 26;
  // 创建人
  string create_name = 27;
}

message CategorySortRequest {
  int32 id = 1;
  //排序动作
  string sort_action = 2;
}

message Erp {
  int32 id = 1;
  //系统名称
  string name = 2;
  //启用/停用
  int32 is_use = 3;
}

message ProductDrugDosage{
  // 投药单位字典code
  string dosing_unit = 22;
  // 投药单位，mg
  string dosing_unit_name = 23;
  // 投药方式code
  string dosing_way = 24;
  // 投药方式名称，iv
  string dosing_way_name = 25;
  // 投药频次
  string use_frequency = 26;
  // 投药频次，每日一次
  string use_frequency_name = 27;
  // 推荐用量
  message RecommendDosage {
    string code = 1;
    // 用量
    double value = 2;
    // 宠物类型
    string name = 3;
  }
  repeated RecommendDosage recommend_dosage = 29;
}

message Product {
  int32 id = 1;
  //分类id
  int32 category_id = 2;
  //品牌id
  int32 brand_id = 3;
  //商品名称
  string name = 4;
  //商品编号
  string code = 5;
  //商品条码
  string bar_code = 6;
  //商品添加日期
  string create_date = 7;
  //商品最后更新日期
  string update_date = 8;
  //是否删除
  int32 is_del = 9;
  //是否为组合商品
  int32 is_group = 10;
  //商品图片（多图）
  string pic = 11;
  //商品卖点
  string selling_point = 12;
  //商品视频地址
  string video = 13;
  //电脑端详情内容
  string content_pc = 14;
  //手机端详情内容
  string content_mobile = 15;
  //是否参与优惠折扣
  int32 is_discount = 16;
  //商品类别（1-实物商品，2-虚拟商品, 3-组合商品）
  int32 product_type = 17;
  //商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
  int32 is_use = 18;
  //分类名称
  string category_name = 19;
  // sku信息
  repeated Sku sku = 20;
  //商品属性
  repeated ProductAttr attr = 21;
  //渠道id
  string channel_id = 22;
  //品牌名称
  string brand_name = 23;
  //京东分类id
  int32 jd_category_id = 24;
  //是否为药品 0否，1是
  int32 is_drugs = 25;
  // 商品应用范围（1电商，2前置仓，3门店仓）
  string use_range = 26;
  // 只有虚拟商品才有值(1.有效期至多少 2.有效期天数)
  int32 term_type = 27;
  // 如果term_type=1 存：时间戳 如果term_type=2 存多少天
  int32 term_value = 28;
  // 组合类型(1:实实组合,2:虚虚组合,3.虚实组合)只有是组合商品才有值
  int32 group_type = 29;
  // 是否支持过期退款 1：是 0：否
  int32 virtual_invalid_refund = 30;
  // 药品仓类型 (0:否 1：巨星药品仓)
  int32 warehouse_type = 31;

  // 是否医疗商品（0:不是 1：是，不会同步到gj商品库）
  int32 is_intel_goods = 32;

  // 商品的启用禁用状态
  int32 disabled = 33;

  // 是否是oms的商品
  int32 from_oms = 34;

  // 1：OMS同步 2：后台新增 3：后台导入
  int32 source_type = 35;
  // 商品短标题
  string short_name = 36;
  // 投药天数
  int32 dosing_days = 37;
  // 药品用量
  ProductDrugDosage drug_dosage = 38;
  // 是否处方药 0否，1是
  int32 is_prescribed_drug = 39;
  // 对应病症
  repeated ProductDiagnoseDicList disease = 40;
}

message ChannelProduct {
  int32 id = 1;
  //分类id
  int32 category_id = 2;
  //品牌id
  int32 brand_id = 3;
  //商品名称
  string name = 4;
  //商品编号
  string code = 5;
  //商品条码
  string bar_code = 6;
  //商品添加日期
  string  create_date = 7;
  //商品最后更新日期
  string update_date = 8;
  //是否删除
  int32 is_del = 9;
  //是否为组合商品
  int32 is_group = 10;
  //商品图片（多图）
  string pic = 11;
  //商品卖点
  string selling_point = 12;
  //商品视频地址
  string video = 13;
  //电脑端详情内容
  string content_pc = 14;
  //手机端详情内容
  string content_mobile = 15;
  //是否参与优惠折扣
  int32 is_discount = 16;
  //商品类别（1-实物商品，2-虚拟商品, 3-组合商品）
  int32 product_type = 17;
  //商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
  int32 is_use = 18;
  //分类名称
  string category_name = 19;
  // sku信息
  repeated Sku sku = 20;
  //商品属性
  repeated ChannelProductAttr attr = 21;
  //渠道id
  string channel_id = 22;
  //渠道名称（美团，饿了么，阿闻，京东）
  string channel_name = 23;
  //渠道的分类id
  int32 channel_category_id = 24;
  //渠道商品类目id
  int32 channel_tag_id = 25;
  //最后编辑用户
  string last_edit_user = 26;
  //渠道的分类名称
  string channel_category_name = 27;
  //是否是推荐商品,1是0否
  int32 is_recommend = 28;
  //列表SKUID
  int32 sku_id = 29;
  //品牌名称
  string brand_name = 30;
  // 组合类型(1:实实组合,2:虚虚组合,3.虚实组合)
  int32 group_type = 31;
  // 只有虚拟商品才有值(1.有效期至多少 2.有效期天数)
  int32 term_type = 32;
  // 如果term_type=1 存：时间戳 如果term_type=2 存多少天
  int32 term_value = 33;
  // 商品应用范围（1电商，2前置仓，3门店仓）
  string use_range = 34;
  // 是否支持过期退款 1：是 0：否
  int32 virtual_invalid_refund = 35;
  //商品短标题
  string short_name = 36;
  //渠道的第三级分类名称
  string channel_last_category_name = 37;
}

message ChannelProducExportModel {
  int32 id = 1;
  //分类id
  int32 category_id = 2;
  //品牌id
  int32 brand_id = 3;
  //商品名称
  string name = 4;
  //商品编号
  string code = 5;
  //商品条码
  string bar_code = 6;
  //商品添加日期
  string create_date = 7;
  //商品最后更新日期
  string update_date = 8;
  //是否删除
  int32 is_del = 9;
  //是否为组合商品
  int32 is_group = 10;
  //商品图片（多图）
  string pic = 11;
  //商品卖点
  string selling_point = 12;
  //商品视频地址
  string video = 13;
  //电脑端详情内容
  string content_pc = 14;
  //手机端详情内容
  string content_mobile = 15;
  //是否参与优惠折扣
  int32 is_discount = 16;
  //商品类别（1-实物商品，2-虚拟商品）
  int32 product_type = 17;
  //商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
  int32 is_use = 18;
  //分类名称
  string category_name = 19;
  // sku信息
  repeated Sku sku = 20;
  //商品属性
  repeated ChannelProductAttr attr = 21;
  //渠道id
  string channel_id = 22;
  //渠道名称（美团，饿了么，阿闻，京东）
  string channel_name = 23;
  //渠道的分类id
  int32 channel_category_id = 24;
  //渠道商品类目id
  int32 channel_tag_id = 25;
  //最后编辑用户
  string last_edit_user = 26;
  //渠道的分类名称
  string channel_category_name = 27;

  //门店财务编码
  string finance_code = 28;
  //上下架状态1-上架0-下架
  int32 up_down_state = 29;
  //最后一次更新的快照id
  int32 snapshot_id = 30;
  //用户编号
  string user_no = 31;
  //快照json数据
  string json_data = 32;
  // skuid
  int32 sku_id = 33;
}

message ProductAttr {
  int32 id = 1;
  //商品ID
  int32 product_id = 2;
  //属性ID
  int32 attr_id = 3;
  //属性值ID
  int32 attr_value_id = 4;
}

message ChannelProductAttr {
  int32 id = 1;
  //商品ID
  int32 product_id = 2;
  //属性ID
  int32 attr_id = 3;
  //属性值ID
  string attr_value_id = 4;
  //属性名称
  string attr_name = 5;
  //属性值
  string attr_value = 6;
  //渠道id
  int32 channel_id = 7;
}

message Sku {
  // SKU ID
  int32 id = 1;
  //商品库中的商品ID
  int32 product_id = 2;
  //市场价
  int32 market_price = 3;
  //建议价格
  int32 retail_price = 4;
  // sku规格值组合
  repeated SkuValue sku_value = 5;
  //第三方货号
  repeated SkuThird sku_third = 6;
  //渠道id
  int32 channel_id = 7;
  //是否可用
  int32 is_use = 8;
  //商品条码
  string bar_code = 9;
  //重量
  double weight_for_unit = 10;
  //重量单位
  string weight_unit = 11;
  //价格单位
  string price_unit = 12;
  int32 min_order_count = 13;
  //前置仓价格
  int32 prepose_price = 14;
  //门店仓价格
  int32 store_price = 15;
  //组合商品信息
  repeated SkuGroup sku_group = 16;
  // 库位信息（宠物saas用）
  string  location_code = 17;
}

message SkuGroup {
  int32 id = 1;
  //商品ID
  int32 product_id = 2;
  // SKU
  int32 sku_id = 3;
  //组合的商品ID
  int32 group_product_id = 4;
  //组合的商品SKUID
  int32 group_sku_id = 5;
  //组合的商品数量
  int32 count = 6;
  //商品名称
  string product_name = 7;
  // 折扣类型(1-按折扣优惠，2-按固定价格优惠)
  int32 discount_type = 8;
  // 折扣值（当折扣类型为1时，存百分比。折扣类型为2时，存具体设置的价格。）
  int32 discount_value = 9;
  // 市场价
  int32 market_price = 10;
  // 渠道id
  int32 channel_id = 11;
  // 子商品快照信息
  ChannelProductRequest snapshot = 12;
  // 子商品类型
  int32 product_type = 13;
  // 规格信息(仅QuerySkuGroup有值)
  repeated SpecValue spec_list = 14;

  // term_type只是针对虚拟有效： 有效期类型 1标识有效期至多少，
  // 2标识购买后多少天失效
  int32 term_type = 16;
  // term_value 有效期的value
  int32 term_value = 17;
}

message SkuThird {
  int32 id = 1;
  //商品库SKU ID
  int32 sku_id = 2;
  //第三方SKUID
  string third_sku_id = 3;
  // ERP仓库ID
  int32 erp_id = 4;
  //商品ID
  int32 product_id = 5;
  //仓库名称
  string erp_name = 6;
  //第三方SPUID
  string third_spu_id = 7;
  //渠道id
  int32 channel_id = 8;
  //是否可用
  int32 is_use = 9;
}

//商品第三方货号组合类
message SkuThirdProduct {
  int32 id = 1;
  //商品库SKU ID
  int32 sku_id = 2;
  //第三方SKUID
  string third_sku_id = 3;
  // ERP仓库ID
  int32 erp_id = 4;
  //商品ID
  int32 product_id = 5;
  //仓库名称
  string erp_name = 6;
  //第三方SPUID
  string third_spu_id = 7;
  //商品名称
  string productname = 8;
  //商品条码
  string bar_code = 9;
}

message SkuValue {
  int32 id = 1;
  //规格ID
  int32 spec_id = 2;
  //规格值ID
  int32 spec_value_id = 3;
  // SKU ID
  int32 sku_id = 4;
  //商品ID
  int32 product_id = 5;
  //规格属性图片
  string pic = 6;
  //规格名称
  string spec_name = 7;
  //规格值
  string spec_value_value = 8;
  repeated SkuValue details = 9;
  //渠道id
  int32 channel_id = 10;
}

message Spec {
  int32 id = 1;
  //规格名称
  string name = 2;
  //排序
  int32 sort = 3;
  //该规格是否允许上传图片
  int32 has_pic = 4;
}

message SpecValue {
  int32 id = 1;
  //规格ID
  int32 spec_id = 2;
  //规格值
  string value = 3;
  //规格名
  string name = 4;
}

message Tag {
  int32 id = 1;
  //标签名称
  string name = 2;
  //排序
  int32 sort = 3;
  //标签类型（1-默认标签，2-活动标签）
  int32 type = 4;
  //是否显示
  int32 is_show = 5;
}

message Type {
  int32 id = 1;
  //类型名称
  string name = 2;
  //排序
  int32 sort = 3;
  //规格名称
  int32 category_id = 4;
  //类型名称
  repeated TypeSpec spec = 5;
  //类型名称
  repeated TypeBrand brand = 6;
  //类型名称
  repeated TypeAttr attr = 7;
}

//分类归属
message CategoryAttr {
  int32 id = 1;
  //分类归属名称
  string name = 2;
}

//分类导航
message CategoryNav {
  //分类ID
  int32 category_id = 1;
  //商品分类别名
  string alias = 2;
  //分类图片
  string pic = 3;
  //推荐子级分类
  string categoryids = 4;
  //推荐的品牌
  string brandids = 5;
  //广告图1
  string adv1 = 6;
  //广告图2
  string adv2 = 7;
  //广告1目标地址
  string adv1_aim = 8;
  //广告2目标地址
  string adv2_aim = 9;
}

//分类导航
message CategoryListTree {
  //分类ID
  int32 id = 1;
  //商品分类别名
  string Name = 2;
  //分类图片
  int32 parent_id = 3;
  //子级分类
  repeated CategoryListTree list = 4;
  //是否允许创建子商品
  int32 is_invented = 5;
}

message TypeList {
  int32 id = 1;
  //类型名称
  string name = 2;
  //排序
  int32 sort = 3;
  //规格，多个逗号分开
  repeated TypeSpec spec = 4;
  //品牌，多个
  repeated TypeBrand brand = 5;
  //属性，多个
  repeated TypeAttr attr = 6;
  //快捷定位分类ID
  int32 category_id = 7;
  //快捷定位分类名称
  string category_name = 8;
}

message TypeAttr {
  //类型ID
  int32 type_id = 1;
  //属性ID
  int32 attr_id = 2;
  //属性名称
  string name = 3;
  //属性值
  repeated AttrValue value = 4;
  //排序
  int32 sort = 5;
  //显示，1显示，2不显示
  int32 is_show = 6;
}

message TypeBrand {
  //类型ID
  int32 type_id = 1;
  //品牌ID
  int32 brand_id = 2;
  //品牌名称
  string brand_name = 3;
}

message TypeSpec {
  //类型ID
  int32 type_id = 1;
  //规格ID
  int32 spec_id = 2;
  //规格名称
  string spec_name = 3;
  //规格值
  repeated SpecValue spec_value = 4;
}

//图片、媒体库空间列表
message MediaClassListRequest {
  //搜索类型1id 2相册/媒体库名称 3店铺id
  int32 keyword_type = 1;
  //关键字
  string keyword = 2;
  //文件类型1图片 2媒体库
  int32 apic_type = 3;
  int32 page_index = 4;
  int32 page_size = 5;
}

//图片、媒体库空间列表返回
message MediaClassListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated MediaClass media_class = 4;
  int32 total_count = 5;
}

message MediaClass {
  //相册或媒体库id
  string aclass_id = 1;
  //相册或媒体库名称
  string aclass_name = 2;
  //店铺ID
  string store_id = 3;
  //店铺名称
  string store_name = 4;
  //数量
  int32 num = 5;
  //类型1图片相册  2视频库
  int32 apic_type = 6;
}

//图片、视频列表
message MediaItemListRequest {
  //文件类型1图片 2媒体库
  int32 media_type = 1;
  string aclass_id = 2;
  int32 page_index = 4;
  int32 page_size = 5;
}
//图片、视频列表返回
message MediaItemListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated MediaItem media_item_class = 4;
  int32 total_count = 5;
}

//图片、视频
message MediaItem {
  // id
  string apic_id = 1;
  //名称
  string apic_name = 2;
  //上传时间
  string upload_time = 3;
  //大小
  string apic_size = 4;
  //链接
  string apic_path = 5;
  //规格
  string apic_spec = 6;
  //店铺名称
  string store_name = 7;
}

//媒体上传
message MediaUploadRequest {
  //库id
  int32 aclass_id = 1;
  // 1图片 2视频
  int32 apic_type = 2;
  //图片/视频名称
  string apic_name = 3;
  //店铺id
  int32 store_id = 4;
  //店铺名称
  string store_name = 5;
  //大小
  int32 apic_size = 6;
  //链接
  string apic_path = 7;
  //规格， 宽x高， 示例：400x200
  string apic_spec = 8;
  //规格
  bytes file = 9;
}

//上传返回
message MediaUploadResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  string url = 4;
}

//渠道商品编辑快照
message ChannelProductSnapshot {
  int32 id = 1;
  //渠道id
  int32 channel_id = 2;
  //用户编号
  string user_no = 3;
  //商品id
  int32 product_id = 4;
  // json格式的快照数据
  string json_data = 5;
  //快照日期
  string create_date = 6;
  //财务编码
  string finance_code = 7;
  // 第三方商品id
  string product_third_id = 8;
  // 操作第三方时候的错信息
  string sync_error = 9;
}

//渠道门店商品
message ChannelStoreProduct {
  int32 id = 1;
  //渠道id
  int32 channel_id = 2;
  //门店财务编码
  string finance_code = 3;
  //商品id
  int32 product_id = 4;
  //上下架状态
  int32 up_down_state = 5;
  //创建时间
  string create_date = 6;
  //更新时间
  string update_date = 7;
  //分组统计的数量
  int32 group_count = 8;
  repeated ChannelStoreProduct details = 9;
  //快照id
  int32 snapshot_id = 10;
  //渠道分类id
  int32 channel_category_id = 11;
  //渠道分类名称
  string channel_category_name = 12;
  //商品名称
  string name = 13;
  // sku
  int32 sku_id = 14;
  //市场价
  int32 market_price = 15;
}

message ExportAllProductRequest {
  // 页码
  int32 page_index = 1;
  // 每页数据量
  int32 page_size = 2;
  // 商品类型 1事物商品 2虚拟商品
  int32 ProductType = 3;
}
message ExportProduct {
  // 商品ID
  int32 spu_id = 1;
  // sku id
  int32 sku_id = 2;
  // 商品名称
  string name = 3;
  // 分类名称
  string category_name = 4;
  // 分类id
  int32 category_id = 5;
  // 品牌名称
  string brand_name = 6;
  // 商品图片(多图片用逗号分隔)
  string pic = 7;
  // 商品卖点
  string selling_point = 8;
  // 商品视频
  string video = 9;
  // 商品详情
  string content_pc = 10;
  // 商品类别
  int32 product_type = 11;
  // 商品条码
  string bar_code = 12;
  // 规格名称
  string spec_name = 13;
  // 规格值
  string spec_value = 14;
  // 市场价
  int32 market_price = 15;
  // 建议零售价
  int32 retail_price = 16;
  // 瑞鹏ERP货号
  string third_spu_sku_id1 = 17;
  // A8货号
  string third_spu_sku_id2 = 18;
  // 管易货号
  string third_spu_sku_id3 = 19;
  // 子龙货号
  string third_spu_sku_id4 = 20;
  // 虚拟商品有效期类型 1.有效期至多少 2.有效期天数
  int32 term_type = 21;
  // 如果term_type=1 存：时间戳 如果term_type=2 存多少天
  int32 term_value = 22;
  // 是否支持过期退款, 0否 1是
  int32 VirtualInvalidRefund = 23;
  // 是否药品仓 0否 1是
  int32 warehouse_type = 24;

  // 是否药品
  int32 is_drugs = 25;
  // 是否处方药
  int32 is_prescribed_drug = 26;
  // 用量信息 json
  string drug_dosage = 27;
  // 对应病症 json
  string disease = 28;
  // 投药天数
  int32 dosing_days = 29;
}

message ExportProductResponse {
  int32 Code = 1;
  string message = 2;
  string error = 3;
  // 导出商品信息
  repeated ExportProduct details = 4;
}
//平台商品库--获取异步任务数据接口--请求参数
message GetTaskListRequest {
  //任务id
  int32 id = 1;
  //任务内容:1:批量新建;2:批量删除;3:批量更新
  int32 task_content = 2;
  //任务状态:1:调度中;2:进行中;3:已完成
  int32 task_status = 3;
  //状态:1:正常;2:冻结;
  int32 status = 4;
  //创建人id
  string create_id = 5;
  //排序类型：createTimeDesc:根据创建时间倒序
  string sort = 6;
  //创建时间
  string createtime = 7;
  //当前页
  int32 page = 8;
  //每页显示数据条数
  int32 pageSize = 9;
  //渠道id
  int32 channel_id = 10;
  //发起人 0:自己;1:全部;2:其他;
  int32 promoter = 11;
}

//平台商品库--获取异步任务数据接口--响应参数
message GetTaskListResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  //当前页
  int32 page = 4;
  //每页显示数据条数
  int32 pageSize = 5;
  //总数
  int32 count = 6;
  //结果
  repeated TaskList task_list = 7;
}

//阿闻渠道--批量上架请求参数
message BatchOnTheShelfToAWRequest {
  //商品ID，多个用逗号分隔
  string product_id = 1;
  //渠道ID
  int32 channel_id = 2;
  //上架门店财务编码，多个财务编码用英文逗号分隔
  string finance_code = 3;
  //是否为全部门店，1：是，0：否
  int32 is_all = 4;
  //用户编号
  string user_no = 5;
  //上下架状态（1-上架，0-下架）
  int32 up_down_state = 6;
  //门店类型 3:门店仓 4:前置仓
  int32 category = 7;
}

//阿闻渠道--批量上架响应参数
message BatchOnTheShelfToAWResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

//京东到家渠道--批量上架请求参数
message BatchOnTheShelfToJddjRequest {
  //商品ID，多个用逗号分隔
  string product_id = 1;
  //渠道ID
  int32 channel_id = 2;
  //当前财务编码
  string finance_code = 3;
  //批量操作财务编码，多个财务编码用英文,分割
  string finance_code_list = 4;
  //是否为全部门店，1：是，0：否
  int32 is_all = 5;
  //用户编号
  string user_no = 6;
  //批量操作类型，0上架;1下架
  int32 up_down_state = 7;
  //门店类型 3:门店仓 4:前置仓
  int32 category = 8;
}

//京东到家渠道--批量上架响应参数
message BatchOnTheShelfToJddjResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

//美团渠道--批量上架/下架请求参数
message BatchOnTheShelfToMTRequest {
  //商品ID，多个用逗号分隔
  string product_id = 1;
  //渠道ID
  int32 channel_id = 2;
  //当前财务编码
  string finance_code = 3;
  //批量操作财务编码，多个财务编码用英文,分割
  string finance_code_list = 4;
  //是否为全部门店，1：是，0：否
  int32 is_all = 5;
  //用户编号
  string user_no = 6;
  //批量操作类型，0上架;1下架
  int32 operate_type = 7;
  //门店属性
  int32 category = 8;

  // 店铺主体Id
  int32 store_master_id = 9;
}
//美团渠道--批量上架响应参数
message BatchOnTheShelfToMTResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}
//美团渠道--批量新建/更新--请求参数
message BatchToMTRequest {
  //商品ID，多个用逗号分隔,如果为空，则默认所有
  string product_id = 1;
  //渠道ID
  int32 channel_id = 2;
  //是否全部门店
  int32 is_all = 3;
  //用户编号
  string user_no = 4;
  //当前财务编码
  string finance_code = 5;
  //批量操作财务编码，多个财务编码用英文,分割
  string finance_code_list = 6;
  //批量操作类型，1创建2更新
  int32 operate_type = 7;
  //更新字段--商品名称
  int32 update_goods_name = 8;
  //更新字段--商品卖点
  int32 update_selling_point = 9;
  //更新字段--商品图片
  int32 update_pic = 10;
  //更新字段--商品视频
  int32 update_video = 11;
  //更新字段--重量
  int32 update_height = 12;
  //更新字段--货号
  int32 update_code = 13;
  //更新字段--市场价
  int32 update_market_price = 14;
  //更新字段--商品条码
  int32 update_bar_code = 15;
  //更新字段--图片详情
  int32 update_pic_details = 16;
  //更新字段--上架状态
  int32 update_status = 17;
  //更新字段，逗号分隔
  string update_field = 18;
  //门店属性
  int32 category = 19;

  // 是否切换仓库
  int32 is_cut_storehouse = 20;

  // 切换仓库第三方已经存在的productId的集合
  repeated int32 exist_product_id_third = 21;
}
//美团渠道--批量新增/更新--响应参数
message BatchToMTResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  string sync_error = 4;
}

//京东到家渠道-批量新建-请求
message BatchToJddjRequest {
  //商品ID，多个用逗号分隔,如果为空，则默认所有
  string product_id = 1;
  //渠道ID
  int32 channel_id = 2;
  //是否全部门店
  int32 is_all = 3;
  //用户编号
  string user_no = 4;
  //当前财务编码
  string finance_code = 5;
  //批量操作财务编码，多个财务编码用英文,分割
  string finance_code_list = 6;
  //批量操作类型，1创建2更新
  int32 operate_type = 7;
  //更新字段--商品名称
  int32 update_goods_name = 8;
  //更新字段--商品卖点
  int32 update_selling_point = 9;
  //更新字段--商品图片
  int32 update_pic = 10;
  //更新字段--商品视频
  int32 update_video = 11;
  //更新字段--重量
  int32 update_height = 12;
  //更新字段--货号
  int32 update_code = 13;
  //更新字段--市场价
  int32 update_market_price = 14;
  //更新字段--商品条码
  int32 update_bar_code = 15;
  //更新字段--图片详情
  int32 update_pic_details = 16;
  //更新字段--上架状态
  int32 update_status = 17;
  //更新字段，逗号分隔
  string update_field = 18;
}
//京东到家渠道-批量新增-响应
message BatchToJddjResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

//阿闻渠道--批量更新--请求参数
message BatchUpdateToAWRequest {
  //商品ID，多个用逗号分隔,如果为空，则默认所有
  string product_id = 1;
  //门店类型：1：前置门店仓 2：子龙系统门店
  int32 shop_type = 2;
  //更新字段--商品名称
  int32 update_goods_name = 3;
  //更新字段--商品卖点
  int32 update_selling_point = 4;
  //更新字段--商品图片
  int32 update_pic = 5;
  //更新字段--商品视频
  int32 update_video = 6;
  //更新字段--商品品牌
  int32 update_brand_id = 7;
  //更新字段--商品属性
  int32 update_attr = 8;
  //更新字段--货号
  int32 update_code = 9;
  //更新字段--市场价
  int32 update_market_price = 10;
  //更新字段--建议零售价
  int32 update_retail_price = 11;
  //更新字段--商品条码
  int32 update_bar_code = 12;
  //更新字段--图片详情
  int32 update_pic_details = 13;
  //更新字段--重量
  int32 update_height = 14;
  //更新字段类型 1：全部 0：部分
  int32 update_type = 15;
  //财务编码
  string finance_code = 16;
  //是否为全部门店，1：是，0：否
  int32 is_all = 17;
  //用户编号
  string user_no = 18;
  //渠道ID
  int32 channel_id = 19;
  //操作类型 1:新建，2:更新
  int32 operate_type = 20;
}

//阿闻渠道--批量更新--响应参数
message BatchUpdateToAWResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}
//平台商品库--删除异步任务数据接口--请求参数
message DeleteTaskRequest {
  //任务id
  int32 task_id = 1;
  //修改id
  string modify_id = 2;
  // 任务类型
  int32 task_content = 3;
}

//平台商品库--删除异步任务数据接口--响应参数
message DeleteTaskResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

//阿闻渠道批量新建--请求参数
message BatchCreateToAWRequest {
  //商品ID，多个用逗号分隔
  string product_id = 1;
  //渠道ID
  int32 channel_id = 2;
  //上架门店财务编码，多个财务编码用英文逗号分隔
  string finance_code = 3;
  //是否为全部门店，1：是，0：否
  int32 is_all = 4;
  //用户编号
  string user_no = 5;
  //仓库类型
  int32 category = 6;
}

//平台商品库--新增异步任务数据接口--请求参数
message CreateBatchTaskRequest {
  //任务内容:1:批量新建;2:批量删除;3:批量更新;
  int32 task_content = 1;
  //操作文件路径
  string operation_file_url = 2;
  //创建人id
  string create_id = 3;
  //渠道id
  int32 channel_id = 4;
  //请求头部
  string request_header = 5;
  //创建人姓名
  string create_name = 6;
  //创建人电话
  string create_mobile = 7;
  //创建人ip
  string create_ip = 8;
  // ip所属位置
  string ip_location = 9;
  //任务名称扩展
  string extended_data = 10;

  // 上架还是下架任务 1:上架 0下架
  int32 up_or_down = 11;
}

//平台商品库--新增异步任务数据接口--响应参数
message CreateBatchTaskResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

message ImportExcelProductResponse {
  // 200成功，400失败
  int32 code = 1;
  // 响应信息
  string message = 2;
  // 成功入库个数
  int32 success_num = 3;
  // 失败个数
  int32 fail_num = 4;
  // 错误信息excel链接
  string qiniu_url = 5;
}

message ChannelProductUnsaleRequest {
  //渠道id
  int32 channel_id = 1;
  //商品列表
  repeated string product_ids = 2;
  string finance_code_list = 3;
  //门店分类
  int32 category = 4;
  //用户编号
  string user_no = 5;
}

//根据商品id和渠道id获取该商品在哪些门店上架的信息--请求参数
message QueryStoreRecommendProductOnstatusRequest {
  //渠道id
  int32 channel_id = 1;
  //商品id
  string product_id = 2;
}
//根据商品id和渠道id获取该商品在哪些门店上架的信息--响应参数
message QueryStoreRecommendProductOnstatusResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated ChannelStoreProductModel details = 4;
}

//数据库表channel_store_product对应字段
message ChannelStoreProductModel {
  int32 id = 1;
  //渠道id
  int32 channel_id = 2;
  //门店财务编码
  string finance_code = 3;
  //商品id
  int32 product_id = 4;
  //分类id
  int32 channel_category_id = 5;
  //分类id
  int32 is_recommend = 6;
  //上下架状态
  int32 up_down_state = 7;
  //创建时间
  string create_date = 8;
  //更新时间
  string update_date = 9;
  //快照id
  int32 snapshot_id = 10;
}

message QueryAllChannelRequest {
  int32 erp_id = 1;
  string name = 2;
  int32 product_id = 3;
  int32 sku_id = 4;
  int32 page_index = 5;
  int32 page_size = 6;
}

message QueryAllChannel {
  string name = 1;
  int32 sku = 2;
  int32 spu = 3;
  string thirdsku = 4;
  string thirdspu = 5;
}

message QueryAllChannelResponse {
  int32 code = 1;
  string msg = 2;
  repeated QueryAllChannel data = 3;
}
message ExcelChannelProdcutRequest {
  //渠道id
  int32 channel_id = 1;
  //门店财务编码
  string finance_code = 2;
}

message ExcelChannelProdcutResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
  // repeated QueryExcelChannelProdcutInfo details = 4;
}

message QueryExcelChannelProdcutInfo {
  //门店财务编码
  string finance_name = 1;
  //财务编码
  string finance_code = 2;
  //商品名称
  string product_name = 3;
  //平台商品ID
  int32 product_id = 4;
  // SKUID
  int32 sku_id = 5;
  //店内分类
  string category_name = 6;
  //重量
  float weight = 7;
  //前置仓价格
  string prepose_price = 8;
  //门店仓价格
  string store_price = 9;
  // A8货号
  string a8_third = 10;
  //子龙货号
  string zl_third = 11;
  //商品条码
  string bar_code = 12;
  //上下架状态
  string up_down_state = 13;
}

message UpdateSalesVolumeRequest {
  // 渠道id
  int32 channel_id = 1;
  // 门店id
  string shopId = 2;
  // 商品数量
  repeated ProductNumber product_list = 3;
}

message ProductNumber {
  // 商品id
  int32 product_id = 1;
  // 商品数量
  int32 num = 2;
}

// 饿了么单门店单商品价格修改请求参数
message SingleUpdatePriceRequest {
  // 门店id
  string finance_code = 1;
  // 商品id
  int32 product_id = 2;
  // 门店仓价格
  int32 store_price = 3;
  // 前置仓价格
  int32 prepose_price = 4;
  // 饿了么id
  string store_id = 5;
}

message GetStockInfoBySkuCodeResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;

  map<string, int32> result = 4;
}

//查询库存请求参数
message GetStockInfoRequest {
  repeated ProductsInfo ProductsInfo = 1;
  // 1电商，2本地生活，3互联网医疗
  int32 source = 2;

  // 渠道id
  int32 channel_id = 3;
}

//前端请求库存参数
message GetStockInfoApiRequest {
  string finance_code = 1;
  repeated ProductsInfoApi productsInfo = 2;
}
//前端请商品库存结构体
message ProductsInfoApi {
  //商品skuid
  int32 sku_id = 1;
  int32 product_type = 2;
  repeated SkuGroup details = 4;
}

//商品和库存信息
message ProductsInfo {
  //商品skuid
  int32 sku_id = 3;
  //财务编码
  repeated string finance_code = 7;
  // 仓库的id
  int32 warehouse_id = 8;
}

//获取前置仓价格参数
message GetPreposePriceRequest {
  // 门店对应仓库id列表
  repeated PreposeWarehouseIdWithCode store_code = 1;
  // 商品货号
  repeated string product_code = 2;
}

// 前置仓仓库id对应门店
message PreposeWarehouseIdWithCode {
  // 仓库id
  int32 id = 1;
  // 门店id
  string code = 2;
}

//获取前置仓价格响应
message GetPreposePriceResponse {
  // 响应码
  int32 code = 1;
  // 响应信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 价格信息
  repeated PreposePriceInfo data = 4;
}

// 前置仓价格信息
message PreposePriceInfo {
  // 门店id
  string finance_code = 1;
  // 商品货号
  string product_code = 2;
  // 价格
  int32 price = 3;
  // 仓库id
  int32 warehouse_id = 4;
}

// 前置仓跑价格用
message PreposePriceUpInfo {
  // 门店id
  string finance_code = 1;
  // 渠道ID，包含的就会跑,如1,2,3
  string channel_ids = 2;
}

//获取饿了么门店品类列表
message ElmCategoryListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //品类集合
  repeated ElmStoreCategoryList data = 4;
}

message ElmStoreCategoryList {
  //品类ID
  string cat_id = 1;
  //品类名称
  string cat_name = 2;
  //品类层级
  int32 depth = 3;
  //父级品类ID
  int32 parent_id = 4;
  repeated ElmStoreCategoryList child = 5;
}

message CategoryListRequest {
  //搜索关键字
  string keyword = 1;
  // 父类id
  string parent_id = 2;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 3;
}

// 导入前置仓价格参数
message ImportA8PriceRequest {
  // 操作类型 1 增加，2 全部替换
  int32 handle_type = 1;
  // 文件七牛url
  string qiniu_url = 2;
  // 操作人ip地址
  string ip = 3;
}

// 导入前置仓价格响应
message ImportA8PriceResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

// 查询前置仓价格操作记录表请求参数
message QueryA8PriceRecordRequest {
  // 分页页码
  int32 page_index = 1;
  // 每页数据量
  int32 page_size = 2;
  // 开始时间
  string start_time = 3;
  // 结束时间
  string end_time = 4;
  // 0自己的，1全部的
  int32 promoter = 5;
  // 仓库名称或者编码
  string warehouse = 6;
  // 货号
  string sku = 7;
}

// 查询前置仓价格操作记录表响应
message QueryA8PriceRecordResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  // A8价格操作信息列表
  repeated A8PriceRecord data = 4;
  // 总数
  int32 total = 5;
}

// A8价格操作信息
message A8PriceRecord {
  // 操作时间
  string create_time = 1;
  // 操作类型
  string type = 2;
  // 仓库编码
  string warehouse_code = 3;
  // 仓库名称
  string warehouse_name = 4;
  // 操作内容
  string operation = 5;
  // 操作人
  string user_name = 6;
  // 操作人ip
  string user_ip = 7;
}

// 查询前置仓价格信息请求参数
message A8PriceInfoRequest {
  // 仓库名
  string warehouse_name = 1;
  // 仓库id
  int32 warehouse_id = 2;
  // A8货号
  string third_sku_id = 3;
  // 分页页码
  int32 page_index = 4;
  // 每页数据量
  int32 page_size = 5;
}

// 查询前置仓价格信息请求参数
message A8PriceInfoResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  // A8价格操作信息列表
  repeated A8PriceInfo data = 4;
  // 总数
  int32 total = 5;
}

// A8价格信息
message A8PriceInfo {
  // id
  int32 id = 1;
  // 前置仓id
  int32 warehouse_id = 2;
  // 前置仓名称
  string warehouse_name = 3;
  // A8货号
  string third_sku_id = 4;
  // 商品id
  int32 product_id = 5;
  // skuId
  int32 sku_id = 6;
  // 价格
  int32 price = 7;
  // 创建时间
  string create_time = 8;
  // 创建人
  string create_user_name = 9;
  // 最后更新时间
  string last_time = 10;
  // 更新人
  string update_user_name = 11;
}

// A8价格编辑参数
message UpdateA8PriceRequest {
  // id
  int32 id = 1;
  // 价格
  int32 price = 2;
  // 操作人ip地址
  string ip = 3;
}

// A8价格编辑响应
message UpdateA8PriceResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message HasStockSkuIDsResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  repeated int32 sku_id = 4;
}

message HasStockSkuIDsRequest {
  repeated int32 sku_id = 1;
  string finance_code = 2;
}

// 视频号列表请求参数
message WxCategoryListRequest {
  // 二级类目名称模糊查询
  string second_cat_name = 1;
  // 三级类目名称模糊查询
  string third_cat_name = 2;
  // 是否申请资质; 默认 0 全部, 1 不用申请资质或需要申请且已申请资质的
  int32 is_qualification = 3;
  // 类目资质类型,默认 -1 全部，0:不需要,1:必填,2:选填
  int32 qualification_type = 4;
  // 商品资质类型,默认 -1 全部，0:不需要,1:必填,2:选填
  int32 product_qualification_type = 5;
  // 三级类目id
  int32 third_cat_id = 6;
}

// 视频号列表响应
message WxCategoryListResponse {
  // 响应码
  int32 errcode = 1;
  // 响应信息
  string errmsg = 2;
  //  类目列表
  repeated WxCategoryList third_cat_list = 3;
}

// 视频号类目列表
message WxCategoryList {
  // 类目id
  int32 third_cat_id = 1;
  // 类目名称
  string third_cat_name = 2;
  // 类目资质
  string qualification = 3;
  // 类目资质类型,0:不需要,1:必填,2:选填
  int32 qualification_type = 4;
  // 商品资质
  string product_qualification = 5;
  // 商品资质类型,0:不需要,1:必填,2:选填
  int32 product_qualification_type = 6;
  // 二级类目ID
  int32 second_cat_id = 7;
  // 二级类目名称
  string second_cat_name = 8;
  // 一级类目ID
  int32 first_cat_id = 9;
  // 一级类目名称
  string first_cat_name = 10;
  // 是否申请资质; 0否,1是
  int32 is_qualification = 11;
  // 资质图片url(多个以逗号分隔)
  string qualification_url = 12;
}

// 创建微信视频号商品(多个)
message AddWxProductInfoRequest {
  repeated UploadProductInfoRequest params = 1;
}

// 创建商品至微信视频号请求参数
message UploadProductInfoRequest {
  // 商家自定义商品ID(是)
  string out_product_id = 1;
  // 标题(是)
  string title = 2;
  // 绑定的小程序商品路径(是)
  string path = 3;
  // 主图,多张,列表(是)
  repeated string head_img = 4;
  // 商品资质图片(否)
  repeated string qualification_pics = 5;
  // 商品详情图文(否)
  DescInfo desc_info = 6;
  // 第三级类目ID(是)
  int32 third_cat_id = 7;
  // 品牌id(是)
  int64 brand_id = 8;
  // 预留字段，用于版本控制(否)
  string info_version = 9;
  // sku数组(是)
  repeated WxVideoSkuInfo skus = 10;
}

// 微信商品详情信息
message DescInfo {
  // 商品详情图文(否)
  string desc = 1;
  // 商品详情图片(否)
  repeated string imgs = 2;
}

// 微信商品sku
message WxVideoSkuInfo {
  // 商家自定义商品ID(是)
  string out_product_id = 1;
  // 商家自定义skuID(是)
  string out_sku_id = 2;
  // sku小图(是)
  string thumb_img = 3;
  // 售卖价格,以分为单位(是)
  int32 sale_price = 4;
  // 市场价格,以分为单位(是)
  int32 market_price = 5;
  // 库存(是)
  int32 stock_num = 6;
  // 条形码(否)
  string barcode = 7;
  // 商品编码(否)
  string sku_code = 8;
  // 销售属性(是)
  repeated SkuAttrs sku_attrs = 9;
}

// 微信商品销售属性
message SkuAttrs {
  // 销售属性key（自定义）(是)
  string attr_key = 1;
  // 销售属性value（自定义）(是)
  string attr_value = 2;
}

// 批量微信添加商品响应
message AddProductInfoListResponse {
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;
  // 商品响应数据
  repeated AddProductInfoResponse data = 3;
}

// 微信添加商品响应
message AddProductInfoResponse {
  // 错误码
  int32 errcode = 1;
  // 错误信息
  string errmsg = 2;
  // 商品响应数据
  AddProductInfoResponseData data = 3;
}

// 微信添加商品响应数据
message AddProductInfoResponseData {
  // 交易组件平台内部商品ID
  int64 product_id = 1;
  // 商家自定义商品ID
  string out_product_id = 2;
  // 创建时间
  string create_time = 3;
  // sku数组
  repeated ProductInfoResponseSkus skus = 4;
}

// 微信商品响应sku
message ProductInfoResponseSkus {
  // 交易组件平台自定义skuID
  int64 sku_id = 1;
  // 商家自定义skuID
  string out_sku_id = 2;
}
//查询组合商品的的子商品信息请求参数
message QueryChildProductsRequest {
  //父商品的sku_id
  string parent_sku_id = 1;
  //父商品的product_id
  int32 parent_product_id = 2;
  //渠道id
  int32 channel_id = 3;
  // erp 2:A8 4：子龙
  int32 erp = 4;
  //门店财务编码
  string finance_code = 5;
}

//查询组合商品的的子商品信息请求参数
message ChildProductsResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  repeated ChildProduct products = 4;
}
//子商品字段
message ChildProduct {
  string id = 1;
  // sku
  string sku_id = 2;
  //商品id
  string product_id = 3;
  //商品名称
  string product_name = 4;
  //商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 product_type = 5;
  //组合商品组合类型（组合商品组合类型0-非组合31-实物实物32-实物虚拟33-虚拟虚拟）
  string bar_code = 6;
  //数量
  int32 number = 7;
  //规格
  string specs = 8;
  //原价
  int32 market_price = 9;
  //商品图片
  string image = 10;
  //只有虚拟商品才有值1有效期至多少2有效期天数
  int32 term_type = 11;
  //如果term_type=1存时间戳如果term_type=2存多少天
  int32 term_value = 12;
  //是否支持过期退款 1：是  0：否
  int32 virtual_invalid_refund = 13;
  //药品仓类型：0:默认否, 1:巨星药品仓
  int32 warehouse_type = 14;
  // 折扣值（当折扣类型为1时，存百分比。折扣类型为2时，存具体设置的价格。）(单位分)
  int32 discount_value = 15;
  //折扣类型（1按折扣优惠，2按固定价格优惠）
  int32 discount_type = 16;
  //组合商品父商品skuId
  string parent_sku_id = 17;
  //第三方skuId
  string third_sku_id = 18;
}

message GetUserUnFinishedTaskRequest {
  //用户编号
  string user_no = 1;
  //渠道id(0-所有平台,1-阿闻，2-美团，3-饿了么，4-京东)
  int32 channel_id = 2;
  //任务内容:1:批量新建;2:批量更新;3：渠道-批量新建 4：渠道-批量上架 5:批量认领
  // 6:认领全部商品 7:批量认领或全部认领 8:药品全部下架 9:药品恢复上架
  // 10:药品相关任务 11:渠道--商品导出 12:渠道--批量更新 13批量同步 15批量脚本
  // 16 商品分类同步 17 全量同步分类到指定门店 18 商品从渠道下架处理 19
  //批量导入前置仓价格信息 20 平台商品导出
  int32 task_content = 3;
  //操作文件路径或者参数
  string operation_data = 4;
}

// 门店绑定虚拟仓
message BindShopWarehouse {
  //操作渠道
  int32 channel_id = 1;
  // 文件七牛url
  string qiniu_url = 2;
  // 用户登录ip
  string ip = 3;
  // ip归属地
  string ip_location = 4;
  // 操作类型 1切电商仓 3切门店仓 4 导入前置仓， 5 导入前置虚拟仓
  int32 bind_type = 5;
}

// 门店绑定虚拟仓响应
message BindShopWarehouseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

// 上下架错误处理
message UpDownERROR {
  //状态码
  string product_id = 1;
  string sku_id = 2;
  //消息
  bool is_success = 3;
  //错误信息
  string message = 4;

  // 门店的财务编码
  string finance_code = 5;
}

message CancelTaskRequest {
  // 任务ID
  int64 Id = 1;

  // TaskContext 任务类型
  int64 task_content = 2;
}

message CancelTaskResponse {
  // 200:成功，400:失败
  int32 Code = 1;
  string Message = 2;
  string Error = 3;
}

message SwitchWarehouseScheduleCallbackRequest {
  // 定时任务ID
  int64 TaskId = 1;
  // 门店列表
  repeated SimpleStoreInfo StoreList = 2;
}
message SimpleStoreInfo {
  // 门店财务编码
  string FinanceCode = 1;
  // 渠道，1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;
  int32 ChannelId = 2;
  // 处理失败商品
  repeated SimpleProductInfo FailList = 3;
  // 系统错误
  string SystemError = 4;
}
message SimpleProductInfo {
  int64 Id = 1;
  int64 SkuId = 2;
  string Name = 3;
  // 商品类别（1-实物商品，2-虚拟商品，3-组合商品）
  int32 Type = 4;
  // 处理失败错误信息
  string Message = 5;
}

message ListSwitchWarehouseLogRequest {
  //当前页
  int32 page = 1;
  //每页显示数据条数
  int32 pageSize = 2;
  //渠道id
  int32 channel_id = 3;
  //搜索关键字
  string keyword = 4;
  //创建人id;
  string create_id = 5;
}
message ListSwitchWarehouseLogResponse {
  repeated SwitchWarehouseLog list = 1; // 日志列表数据
  int32 total = 2;                      // 总记录数
}
message SwitchWarehouseLog {
  int64 id = 1; // 主键id
  // 渠道id(0-所有平台,1-阿闻，2-美团，3-饿了么，4-京东到家，10-阿闻竖屏自提)
  int32 channel_id = 2;
  // 操作类型：1-导入门店，2-移除门店，3-仓库侧移除门店
  int32 action = 3;
  // 财务编码
  string finance_code = 4;
  // 门店名称
  string shop_name = 5;
  // 切仓前仓库编码
  string src_warehouse_code = 6;
  // 切仓前仓库名称
  string src_warehouse_name = 7;
  // 切仓前仓库类型1-电商仓3-门店仓4-前置仓5-虚拟前置仓
  int32 src_warehouse_category = 8;
  // 切仓后仓库编码
  string dst_warehouse_code = 9;
  // 切仓后仓库名称
  string dst_warehouse_name = 10;
  // 切仓后仓库类型1-电商仓3-门店仓4-前置仓5-虚拟前置仓
  int32 dst_warehouse_category = 11;
  // 操作详情
  string content = 12;
  // 创建人id
  string create_id = 13;
  // 创建时间
  string create_time = 14;
  // 创建人姓名
  string create_name = 15;
  // 创建人ip
  string create_ip = 17;
  // ip所属位置
  string ip_location = 18;
}

message AddSwitchWarehouseLogRequest {
  repeated SwitchWarehouseLog list = 1; // 日志列表数据
}

// todo  注释完善
message SyncCategoryRequest {
  //渠道
  int32 channel_id = 1;
  //门店财务编码
  string finance_code = 2;
  //第三方门店id
  string app_poi_code = 3;
  //门店渠道
  int32 store_master_id = 4;
}

message GetAndDeleteCategoryResponse {
  // 200:成功，400:失败
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated GetAndDeleteFailList fail_list = 4;
}
message RenewCategoryResponse {
  // 200:成功，400:失败
  int32 Code = 1;
  string Message = 2;
  string Error = 3;
  repeated RenewCategoryFailList fail_list = 4;
}

message RelateCategoryAndProductResponse {
  // 200:成功，400:失败
  int32 Code = 1;
  string Message = 2;
  string Error = 3;
  repeated RelateCategoryAndProductFailList fail_list = 4;
}
message GetAndDeleteFailList {
  int64 category_id = 1;
  string category_name = 2;
  string error = 3;
}
message RenewCategoryFailList {
  int64 category_id = 1;
  string category_name = 2;
  string error = 3;
}
message RelateCategoryAndProductFailList {
  string sku_id = 1;
  string product_id = 2;
  string category_id = 3;
  string category_name = 4;
  string error = 5;
}
//导入需要分类同步的门店
message ImportSyncCategoryShopRequest {
  // "渠道 0 所有 1 阿闻 2 美团 3饿了么 4京东 5商城 指定多个渠道用逗号分割
  // 比如同步饿了么与美团则传：2,3"
  string channel_id = 1;
  string qiniu_url = 2;
  string user_name = 3;
  string user_ip = 4;
}

message SyncChannelCategoryScheduleCallbackRequest {
  // 定时任务ID
  int64 task_id = 1;
  // 门店列表
  repeated SyncChannelCategoryScheduleCallbackData data = 2;
}

message SyncChannelCategoryScheduleCallbackData {
  // 财务代码
  string finance_code = 1;
  string shop_name = 2;
  bool is_success = 3;
  // 执行结果
  string message = 4;
  // 分类Id
  string category_id = 5;
  // 分类名称
  string category_name = 6;
  string category_post_err = 7;
  // 是否同步成功
  string product_id = 8;
  string sku_id = 9;
  string sku_post_err = 10;
  // SystemError 系统错误
  string system_error = 11;
}

message SyncCategoryScheduleCallBackRequest {
  int64 TaskId = 1;
  repeated SyncCategoryScheduleRequest vo = 2;
}

message SyncCategoryScheduleRequest {
  // 分类Id
  int64 CategoryId = 1;
  // 分类名称
  string CategoryName = 2;
  // 财务代码
  string FinanceCode = 3;
  // shopName 门店名称
  string ShopName = 4;
  // 是否同步成功
  bool IsSuccess = 5;
  // 执行结果
  string Message = 6;
  // channel_id
  int32 ChannelId = 7;
  // 操作类型 1新增 2 修改 3删除
  int32 SyncType = 8;
  // 第三方门店的id
  string ChannelStoreId = 9;
  // appChannel
  int32 AppChannel = 10;
  // 上级分类id-必须
  int32 ParentId = 11;
  // 排序-必须
  int32 Sort = 12;
  // 系统错误
  // SystemError 系统错误
  string SystemError = 13;
}

message SyncCategoryScheduleResponse {
  // 200:成功，400:失败
  int32 Code = 1;
  string Message = 2;
  string Error = 3;
  SyncCategoryScheduleRequest data = 4;
}

message OffshelfZilongInvalidProductRequest {
  // Excel文件地址
  string ExcelUrl = 1;
}

message OffshelfZilongInvalidProductResponse {
  // 200:成功，400:失败
  int32 Code = 1;
  string Message = 2;
  // 分布式jobId
  int64 JobId = 3;
}

message AddTaskProductFromOmsVo {
  // 同步的商品数据
  string data = 1;
}

message GetFinanceCodeWarehouseRelationVO {

  // 门店财务编码
  string finance_code = 1;
  //  渠道id
  int32 channel_id = 2;
}

message GetFinanceCodeWarehouseRelationResp {
  //仓库id
  repeated int32 warehouse_ids = 1;

  // 渠道id
  int32 channel_id = 2;
  // 是否同一个仓库 只针对阿闻和阿闻竖屏自提  true 是 false 否
  bool is_same = 3;

  // 仓库类型   3门店仓  4前置仓 5虚拟仓
  int32 category = 4;
}

message HospitalProductPriceImportReq {
  // Excel文件地址
  string excel_url = 1;
}

message HospitalProductPriceListReq {
  int32 page_index = 1;
  int32 page_size = 2;
  string skuid = 3;
}

message HospitalProductPriceListRes {
  message Data {
    int32 id = 1;
    // sku_id
    int32 sku_id = 2;
    //商品价格(分)
    int32 price = 3;
    //创建人
    string create_by = 4;
    //创建时间
    string create_date = 5;
    //编辑人
    string update_by = 6;
    //编辑时间
    string update_date = 7;
  }
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Data data = 4;
  int32 total = 5;
}

message HospitalProductPriceEditReq {
  int32 id = 1;
  //商品价格(分)
  int32 price = 2;
  //创建人ID（不用前端传）
  string create_id = 3;
  //创建人姓名（不用前端传）
  string create_name = 4;
  //创建人IP所属位置（不用前端传）
  string ip_location = 5;
  //创建人IP（前端不用传）
  string ip_addr = 6;
}

message ShowAgencyProductVo {
  // 应用配置的id
  int32 app_channel = 1;

  //  skuId
  int64 sku_id = 2;
}

message PageAgencyProductResp {
  // 分页数量
  int32 total = 1;
  // 异常消息
  string message = 2;

  //  商品数据
  repeated ShowAgencyProductResp data = 3;
}

message ShowAgencyProductResp {
  // 应用配置的id
  int32 app_channel = 1;

  //  skuId
  int64 sku_id = 2;

  // 商品名称
  string product_name = 3;

  // 创建人
  string create_by = 4;
  //  创建时间
  string create_time = 5;
}

message ImportAgencyProductVo {
  string OpenFileUrl = 1;
  string create_by = 2;
  int32 app_id = 3;
}

message ImportAgencyProductResponse {
  int32 code = 1;
  string message = 2;
}

message UploadSkuListRequest {
  //应用 id
  int32 app_id = 1;
  // 查找关键词
  string key = 2;

  int32 page_index = 3;

  int32 page_size = 4;
}

message UploadSkuListResponse {
  int32 code = 1;
  string message = 2;
  repeated UploadSku data = 3;
  int32 total = 4;
}

message UploadSku {
  int32 id = 1;
  int32 app_channel = 2;
  int32 sku_id = 3;
  string product_name = 4;
  string create_by = 5;
  string create_time = 6;
}

message UploadSkuDeleteRequest { int32 id = 1; }

message UploadSkuDeleteResponse {
  int32 code = 1;
  string message = 2;
}

message GetAgencyConfigResp {
  int32 code = 1;
  string message = 2;
  repeated int32 configData = 3;
}

message IsUpProductResp {
  int32 code = 1;
  string message = 2;
  map<string, int32> data = 3;
}

message MallProductPriceListReq {
  int32 page_index = 1;
  int32 page_size = 2;
  string skuid = 3;
}

message MallProductPriceListRes {
  message Data {
    int32 id = 1;
    // 仓库编码
    string warehouse_code = 2;
    // sku_id
    int32 sku_id = 3;
    //商品价格(分)
    int32 price = 4;
    //创建日期
    string create_date = 5;
    //更新日期
    string update_date = 6;
    //创建人
    string create_name = 7;
    //更新人
    string update_name = 8;
  }
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated Data data = 4;
  int32 total = 5;
}

message MallProductPriceEditReq {
  int32 id = 1;
  //商品价格(分)
  int32 price = 2;
  //创建人ID（不用前端传）
  string create_id = 3;
  //创建人姓名（不用前端传）
  string create_name = 4;
  //创建人IP所属位置（不用前端传）
  string ip_location = 5;
  //创建人IP（前端不用传）
  string ip_addr = 6;
}

// 检测添加购物车
message CheckAddCartReq {
  // 财务编码
  string finance_code = 1;
  // 渠道id
  int32 channel_id = 2;
  // 商品条码
  string bar_code = 3;
  // 商品skuid，与商品条码二选一
  int32 sku_id = 4;
}

message CheckAddCartRes {
  // 200正常，400错误
  int32 code = 1;
  // 错误信息
  string message = 2;
  // 当前库存
  int32 stock = 3;
  // 商品spu
  int32 product_id = 4;
  // 商品sku
  int32 sku_id = 5;
}

message GetProductCategoryRequest {
  repeated string product_ids = 1;
  repeated string sku_ids = 2;
}

message GetProductCategoryResponse {
  message Data {
    string product_id = 1;
    int32 category_first = 2;
    int32 category_second = 3;
    int32 category_third = 4;
  }
  repeated Data data = 1;
}

// 查询渠道分类，附加商品数量
message ChannelCategoryWithCountReq {
  // 渠道id
  int32 channel_id = 1;
  // 财务编码
  string finance_code = 2;
}

message ChannelCategoryWithCount {
  // 分类id
  int32 id = 1;
  // 分类名称
  string name = 2;
  // 子分类
  repeated ChannelCategoryWithCount children = 3;
  // 商品计数
  int32 product_count = 4;
}

// 查询渠道分类，附加商品数量
message ChannelCategoryWithCountRes {
  int32 code = 1;
  string message = 2;
  // 分类
  repeated ChannelCategoryWithCount data = 3;
}

message MoveProductVo {

  // 原来的分类
  int32 old_category_id = 1;

  // 移动到新的分类
  int32 new_category_id = 2;

  //创建人id
  string create_id = 3;

  //创建人姓名
  string create_name = 6;
  //创建人电话
  string create_mobile = 7;
  //创建人ip
  string create_ip = 8;
  // ip所属位置
  string ip_location = 9;
}

message DelProductParameter {
  repeated int32 product_id = 1;
  //开始时间
  string begin_time = 2;
  //结束时间
  string end_time = 3;
}

message ProductBaseResponse {
  // 200 成功，400失败
  int32 code = 1;
  // 失败时返回原因
  string message = 2;
}

// R1价格同步
message R1PriceSyncReq {
  message Price {
    // 物料编码（货号）
    string skuNo = 1;
    // 集采价
    float centralizedPurchasePrice = 2;
    // 建议零售价格
    float retailPrice = 3;
    // 批发价
    float tradePrice = 4;
  }
  repeated Price prices = 1;
}

// R1价格同步到sku
message R1PriceSyncSkuReq {
  // 限定更新的范围
  string search = 1;
  // 范围类型 1 sku_id、2 货号、3 组合商品id、9全量
  int32 type = 2;
}

message DeleteProductVo {
  // 商品的id
  int32 product_id= 1;

  //创建人id
  string create_id = 2;

  //创建人姓名
  string create_name = 3;
  //创建人电话
  string create_mobile = 4;
  //创建人ip
  string create_ip = 5;
  // ip所属位置
  string ip_location = 6;

  // 渠道id 9互联网医院
  int32 channel_id = 7;
  // erp_id
  int32 erp_id = 8;
  // third_sku_id 第三方货号
  string third_sku_id = 9;
}

message ChannelProductRecordsVo {
  // 商品的id
  int32 product_id= 1;

  //渠道id
  int32 channel_id = 2;

  //开始时间
  string start_time = 3;
  //结束时间
  string end_time = 4;
  //where条件 sku_id, product_name, third_sku_id
  string where = 5;
  // 查询的值
  string value = 6;
  // 门店财务编码
  string finance_code = 7;

  // 操作类型 1删除商品2上架3批量上架4下架 5批量下架6自动上架7自动下架
  int64 record_type = 8;

  // page
  int64 page_index = 9;
  //size
  int64 page_size = 10;
}


message RecordChannelProductRes {

  int64 code = 1;
  int64 total =2;
  string Error =3;
  repeated RecordChannelProduct data = 4;
}
message RecordChannelProduct {

  int64  id  = 1;
  int64 product_id   =2;
  int64 channel_id = 3;
  int64 sku_id = 4;
  string product_name = 5;
  string zilong = 6;
  string a8 =7;
  int64 record_type =8;
  string record_time = 9;
  string finance_code = 10;
  string  user_no = 11;
  string user_name = 12;
  string create_date = 13;
  string update_date = 14;
}

message RecordProductRes {

  int64 code = 1;
  int64 total =2;
  string Error =3;
  repeated RecordProduct data = 4;
}
message RecordProduct {

  int32  id  = 1;
  int32  record_type = 2;
  string  record_time = 3;
  string record_data = 4;
  string  user_no = 5;
  string user_name = 6;
  string upc = 7;
  string product_name = 8;

  int64 product_id   =9;
  int64 sku_id = 10;

  string zilong_new = 11;
  string a8_new =12;
  string zilong_old =13;
  string a8_old =14;

  string create_date = 15;
  string update_date = 16;
}

message ProductRecordsVo {
  // 商品的id
  int32 product_id= 1;

  // 操作类型 1修改货号 2删除商品
  int64 record_type = 2;
  //开始时间
  string start_time = 3;
  //结束时间
  string end_time = 4;
  //where条件 sku_id, product_name, third_sku_id,product_id,upc
  string where = 5;
  // 查询的值
  string value = 6;
  // page
  int64 page_index = 7;
  //size
  int64 page_size = 8;
}

message ProductDiagnoseDicReq {
  int32 page_index = 1;
  int32 page_size = 2;
  // 搜索关键字
  string keyword = 3;
}

message ProductDiagnoseDicList {
  // 病种编号
  string code = 1;
  // 名称
  string name = 2;
}

message ProductDiagnoseDicRes {
  int32 code = 1;
  string message = 2;
  // 病种
  repeated ProductDiagnoseDicList data = 3;
  // 返回总数，用于分页
  int32 total_count = 4;
}

message ZiLongDrugSyncReq {
  message Data {
    // 商品货号
    string product_code = 1;
    // 是否处方药,只药品有，1是、0否
    int32 is_prescribed_drug = 2;
    // 是否可销
    int32 can_sell = 3;
  }
  // 商品药品数据
  repeated Data data = 1;
}

message ProductQueryDiseaseReq {
  string finance_code = 1;
  message SkuNum {
    // 商品id（兼容组合商品）
    int32 sku_id = 1;
    // 商品数量
    int32 num = 2;
  }
  // 商品skuId
  repeated SkuNum skus = 2;
}

message ProductQueryDiseaseRes{
  int32 code = 1;
  string message = 2;

  message ProductQueryDisease {
    // 商品sku_id
    int32 sku_id = 1;
    // 商品数量
    int32 num = 2;
    // 商品名称
    string name = 3;
    // 商品图片链接
    string pic = 4;
    // 病症
    repeated ProductDiagnoseDicList disease = 5;
  }

  repeated ProductQueryDisease data = 3;
}

message ProductDrugInfoReq {
  // product_id 数组
  repeated int32 ids = 1;
}

message ProductDrugInfoRes {
  int32 code = 1;
  string message = 2;
  message DrugInfo {
    // 是否是药品
    int32 is_drugs = 1;
    // 是否是处方药
    int32 is_prescribed_drug = 2;
  }
  map<int32,DrugInfo> data = 3;
}

message ProductPetTypeRes {
  int32 code = 1;
  string message = 2;

  message Data {
    string code = 1;
    // 名称
    string name = 2;
  }

  repeated Data data = 3;
}

message ShopCateListResponse{
  int32 code = 1;
  string message = 2;
  repeated GoodCateData data = 3;
}

message ShopCateListByClassifyResponse{
  int32 code = 1;
  string message = 2;
  repeated ShopCateData data = 3;
}

message GoodCateData{
  int32 gc_id = 1;
  string gc_name = 2;
  int32  gc_parent_id = 3;
  int32 level =4;
  repeated GoodCateData children = 5;
}

message ShopCateData{
  // 分类id，英文逗号分隔（相同名称）
  string gc_ids = 1;
  string gc_name = 2;
}

message AwenCateListReq{
  string finance_code = 1;
}

message VipGoodsRequest{
  //分类id
  string gc_id =1;
  //价格排序 1-从高到底 2-从低到高
  int32 price_state = 2;
  //销量 1-从高到底 2-从低到高
  int32 sales_state = 3;
  //商品名称
  string good_name = 4;
  //到家的财务编码
  string finance_code = 5;
  //1-电商精品推荐 2-电商商城商品 3-混合上下分布
  int32 type =6;
  //用户id(前端不用传)
  string user_id =7;
  //页码
  int32 page_index = 8;
  //每页显示条数
  int32 page_size = 9;
}

message VipGoodsData{
  //商品sku_id
  int32 sku_id =1;
  //商品spu_id
  int32 product_id = 2;
  //短标题名称
  int32 short_name = 3;
  //商品名称
  string name = 4;
  //到家的财务编码
  string finance_code = 5;
  //市场价分
  int32 market_price=6;
  //会员价 分
  int32 member_price_1 = 7;
  //是否是会员价
  int32 is_member_price =8;
  //促销类型 0无促销，1团购，2限时折扣
  int32 goods_promotion_type = 9;
  //商品促销价格 分
  int32 goods_promotion_price =10;
  //商品图片
  string goods_image =11;
  //会员等级
  int32 user_level_id =12;
}

message VipGoodsResponse{
  int32 code = 1;
  string message = 2;
  int32 total = 3;
  repeated VipGoodsData data =4;
}

message ShopVipCardGoodsRequest{
  //页码
  int32 page_index = 1;
  //每页显示条数
  int32 page_size = 2;
  //商品skuid
  int32  sku_id = 3;
  //商品spuid
  int32 spu_id = 4;
  //商品名称
  string goods_name =5;
  //是否有免费会员价 1=是 2否
  int32 is_free =6;
  //是否导出 1-是
  int32 export = 7;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 8;
}

message ShopVipCardGoodsResponse{
  int32 code = 1;
  string message = 2;
  int32 total = 3;
  repeated VipGoodsList data =4;
}

message VipGoodsList{
  int32 id =1;
  //商品sku_id
  int32 sku_id =2;
  //商品spu_id
  int32 spu_id = 3;
  //商品名称
  string goods_name = 4;
  //销售价
  double goods_price = 5;
  //市场价元
  double market_price=6;
  //免费会员价
  double member_price = 7;
  //是否有免费会员价 1=有 2没有
  string is_member_text = 8;
  //促销类型 0无促销，1团购，2限时折扣
  int32 goods_promotion_type = 9;
  //活动名称
  string goods_promotion_name = 10;
  //商品促销价格-商城活动价
  double goods_promotion_price =11;
  //商品图片
  string goods_image =12;
  //免费会员价等级价格
  repeated member_lever_price lever_price = 13;
  //付费会员价
  double vip_member_price =14;
  //付费会员折扣
  double vip_discount =15;
  //参与付费会员折扣的价格
  double vip_discount_price = 16;
}

message member_lever_price {
  string lever_price_text = 1;
}

message DelShopVipCardGoodsReq{
  //商品sku_id
  int32  sku_id = 1;
  //员工编号
  string user_id = 2;
  //员工名称
  string user_name = 3;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 4;
}

message GoodsRequest{
  //页码
  int32 page_index = 1;
  //每页显示条数
  int32 page_size = 2;
  //商品名称
  string good_name = 3;
  //用户id(前端不用传)
  string user_id =4;
  //商品sku_id
  int32 goods_id =5;
  //搜索 1-是 默认0
  int32 search = 6;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 7;
}

message GoodsResponse{
  int32 code = 1;
  string message = 2;
  int32 total = 3;
  repeated GoodsData data =4;
}

message GoodsData{
  //商品sku_id
  int32 sku_id =1;
  //商品spu_id
  int32 product_id = 2;
  //短标题名称
  int32 short_name = 3;
  //商品名称
  string name = 4;
  //到家的财务编码
  string finance_code = 5;
  //市场价 元
  double market_price=6;
  //会员价 元
  double member_price_1 = 7;
  //是否是会员价
  int32 is_member_price =8;
  //促销类型 0无促销，1团购，2限时折扣
  int32 goods_promotion_type = 9;
  //商品促销价格 元
  double goods_promotion_price =10;
  //商品图片
  string goods_image =11;
  //会员等级
  int32 user_level_id =12;
}

message ShopCateListReq {
  // 分类等级：1-一类；2-二类；3-三类
  int32 classify = 1;
  // 商品类型：1-会员价商品分类；2-医保价商品分类
  int32 type = 2;
  //用户id(前端不用传)
  string user_id =3;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 4;
}

message ShopCateReq {
  //用户id(前端不用传)
  string user_id =1;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 2;
}

message ShopBindingWarehouseReq{
  //创建人ip
  string ip =1;
  //ip所属位置
  string ip_location =2;
  //门店切仓json数组
  repeated BindData data =3;
}

message BindData{
  //1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提
  int32 channel_id =1;
  //门店财务编码
  string shop_id =2;
  //门店名称
  string shop_name =3;
  //仓库id
  int32 warehouse_id =4;
  //是否跑数据 0-否 1-是
  int32 run_type = 5;
}

message QueryChainGoodsReq {
  int32 adcode = 1;
  //主体：1-阿闻，2-极宠家，3-福码购
  int32 org_id = 2;
}