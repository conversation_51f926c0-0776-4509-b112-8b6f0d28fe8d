syntax = "proto3";
import "google/protobuf/wrappers.proto";
import "google/protobuf/struct.proto";
package pc;

//数据中心渠道商品模块服务
service DcChannelProduct {
  //根据sku_id查找product_id（批量）
  rpc GetProductIdBySkuId (GetProductIdBySkuIdRequest) returns (GetProductIdBySkuIdResponse);
  // 渠道商品库列表
  rpc List(ChannelProductListReq) returns(ChannelProductListRes);
  // 渠道商品库列表商品统计
  rpc Count(ChannelProductCountReq) returns(ChannelProductCountRes);
  // 渠道商品库列表
  rpc ExceptionList(ChannelProductListReq) returns(ChannelProductExceptionListRes);
  // 去重的上架商品列表
  rpc StoreProductUpDistinct(StoreProductUpDistinctReq) returns(StoreProductUpDistinctRes);
  // 营销活动商品按价格统计
  rpc CountByPrice(ProductCountByPriceReq) returns(ProductCountByPriceRes);
  // 价格关联门店搜索
  rpc PriceStore(ProductPriceStoreReq) returns(ProductPriceStoreRes);
  // 美团商户端删除商品
  rpc MtDelProduct (MtDelProductReq) returns (ChannelProductDelResp);
  // 饿了么商户端删除商品
  rpc ElmDelProduct (ElmDelProductReq) returns (ChannelProductDelResp);
  // 饿了么商户端下架商品
  rpc ElmDownProduct (ElmDownProductReq) returns (ElmDownProductResp);
}

message GetProductIdBySkuIdRequest {
  repeated int32 sku_id = 1;
  int32 is_saas = 2;
}

message GetProductIdBySkuIdResponse {
  map<int32,int32> data = 1;
}


//数据中心渠道商品模块服务
service DcTaskProduct {
  rpc ChooseTaskRun (TaskRunVo) returns (TaskRunBaseResponse);
}


message TaskRunVo{
  // 启动类型 1：阿闻门店仓自动上架 2 阿闻前置仓上架 3： mt门店仓自动上架  4：mt前置仓自动上架  5：ele门店仓自动上架  6：ele前置仓自动上架
  // 7: 医疗门店仓自动上架 8：医疗前置仓自动上架 9:阿闻前置仓价格  10：mt前置仓价格 11：ele前置仓价格同步 12：jd前置仓价格同步
  int32 data = 1;
}



message TaskRunBaseResponse {
  int32 code = 1;
  string message = 2;
  string error = 3;
}

message ChannelProductListReq{
  int32 page_index = 1;
  int32 page_size = 2;
  // 查询条件
  string where = 3;
  // 查询条件的类型（商品筛选类型，为空则为综合筛选（name=商品名称，id=平台商品ID，third_spu_sku_id=商品货号，sku_id=SKUID，bar_code商品条码））
  string where_type = 4;
  // 分类id
  int32 category_id = 5;
  // 品牌ID
  int32 brand_id = 6;
  // 商品类别（0-所有，1-实物商品，2-虚拟商品，31实实组合，33虚实组合）
  int32 product_type = 7;
  // 渠道id
  int32 channel_id = 10;
  // 上下架状态（-1查询所有，0下架，1上架，2有库存，3有库存已下架）
  int32 up_down_state = 11;
  // 门店财务编码
  string finance_code = 13;
  // 是否药品
  int32 is_drugs = 14;
  //是否力荐商品
  int32 is_recommend = 15;
  //最小购买数量
  int32 min_order_count=16;
  //最大购买数量
  int32 max_order_count=17;
}

message ChannelProductList {
  // 商品id
  int32 id = 1;
  // 商品名称
  string name = 2;
  // 商品类别文本
  string type_text = 3;
  // 1-实物商品，2-虚拟商品，3-组合商品
  int32 product_type = 4;
  // 商品图片（多图）
  string pic = 5;
  // 渠道id
  int32 channel_id = 6;
  //商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
  int32 is_use = 7;
  // 店内分类
  int32 channel_category_id = 8;
  // 商品最后更新日期
  string update_date = 9;
  message Sku {
    // skuId
    int32 sku_id = 1;
    // a8货号
    string a8_id = 2;
    // 子龙货号
    string zilong_id = 3;
    message Stock {
      // 可售库存
      int32 qty = 1;
      // 锁定库存
      int32 lock_qty = 2;
    }
    // 库存数据，虚拟商品不返回，阿闻渠道返回2组数据
    repeated Stock stocks = 4;
    // 价格（分）
    int32 price = 5;
    // 会员价格（分）
    int32 vip_price = 11;
    // 规格值
    string spec_value = 6;
    // 商品条码
    string bar_code = 7;
    // 总量
    double weight_for_unit = 8;
    // 平台a8货号
    string a8_id_pt = 9;
    // 平台子龙货号
    string zilong_id_pt = 10;
  }
  // sku数据
  repeated Sku skus = 10;

  string channel_category_name =11;
  //是否力荐商品
  int32 is_recommend=12;
  //最小起购数
  int32 min_order_count=13;
  //是否药品
  int32 is_drugs=14;
  //第三方的商品ID
  string product_third_id=15;
  //操作第三方时候的错信息
  string sync_error=16;
}

message ChannelProductExceptionListRes{
  int32 code = 1;
  string message = 2;
  repeated ChannelProductExceptionList data = 3;
  // 返回总数，用于分页
  int32 total_count = 4;
}

message ChannelProductExceptionList{
  //重复的货号描述
  string third_sku_id= 1;
  repeated ChannelProductExceptionDetail data = 2;
  //重复的货号
  string third_sku_item= 3;
  //重复的货号
  int32 erp_id= 4;
}


message ChannelProductExceptionDetail {
  // 商品id
  int32 id = 1;
  // 商品名称
  string name = 2;
  // 商品类别文本
  string type_text = 3;
  // 1-实物商品，2-虚拟商品，3-组合商品
  int32 product_type = 4;
  // 商品图片（多图）
  string pic = 5;
  // 渠道id
  int32 channel_id = 6;
  //商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
  int32 is_use = 7;
  // 店内分类
  int32 channel_category_id = 8;
  // 商品最后更新日期
  string update_date = 10;
  //A8货号
  string a8_id = 11;
  // 子龙货号
  string zilong_id = 12;
  // 平台a8货号
  string a8_id_pt = 13;
  // 平台子龙货号
  string zilong_id_pt = 14;
  // skuid
  int32  sku_id= 15;
  //erp_id
  string erp_id = 16;

}

message ChannelProductListRes{
  int32 code = 1;
  string message = 2;
  repeated ChannelProductList data = 3;
  // 返回总数，用于分页
  int32 total_count = 4;
  // 仓库类型名称
  repeated string warehouse_type = 5;
}

// 渠道商品库列表商品计数
message ChannelProductCountReq {
  // 不传取请求头
  int32 channel_id = 1;
  // 门店财务编码，不传取请求头
  string finance_code = 2;
  // 分类id
  int32 category_id = 3;
}

message ChannelProductCountRes {
  int32 code = 1;
  string message = 2;
  // 全部
  int32 total = 3;
  // 上架
  int32 up = 4;
  // 下架
  int32 down = 5;
  // 有库存
  int32 has_stock = 6;
  // 有库存已下架
  int32 has_stock_and_down = 7;
}

message StoreProductUpDistinctReq {
  // 渠道id
  int32 channel_id = 1;
  // 财务编码
  repeated string finance_code = 2;
  // 商品id
  repeated int32 product_id = 3;
  // skuId
  repeated int32 sku_id= 4;
  // 商品名称
  string name = 5;

  int32 page_index = 6;
  int32 page_size = 7;
}

message StoreProductUpDistinctRes {
  int32 code = 1;
  string message = 2;
  message List {
    // 商品id
    int32 product_id = 2;
    // skuId
    int32 sku_id= 3;
    // 商品名称
    string name = 4;
  }
  repeated List data = 3;
  // 是否有下一页
  bool has_more = 4;
}

// 商品按价格统计
message ProductCountByPriceReq {
  // 渠道id
  int32 channel_id = 1;
  // 财务编码
  repeated string finance_code = 2;
  // 商品sku_id
  repeated int32 sku_id = 3;
}

message ProductCountByPriceRes {
  int32 code = 1;
  string message = 2;

  message Data {
    // 商品id
    int32 product_id = 1;
    // skuId
    int32 sku_id = 2;
    // 商品名称
    string name = 3;
    // 售价
    int32 market_price = 4;
    // 关联门店数量
    int32 count = 5;
    // 随机一家门店名称
    string shop_name = 6;
  }
  repeated Data data =3;
}

// 商品价格关联的门店
message ProductPriceStoreReq {
  // 渠道id
  int32 channel_id = 1;
  // 财务编码
  repeated string finance_code = 2;
  // 商品sku_id
  int32 sku_id = 3;
  // 商品价格
  int32 price = 4;
  // 搜索类型 1门店名称、2财务编码
  int32 search_type = 5;
  // 搜索关键字
  string search = 6;

  int32 page_index = 7;
  int32 page_size = 8;
}

message ProductPriceStoreRes {
  int32 code = 1;
  string message = 2;

  message Data {
    // 财务编码
    string finance_code = 1;
    // 门店名称
    string name = 2;
  }

  repeated Data data =3;
  // 返回总数，用于分页
  int32 total_count = 4;
}

message MtDelProductReq {
  // 商家系统的三方门店id
  string app_poi_code = 1;
  // 商品id
  repeated string app_spu_code = 2;
}

message ChannelProductDelResp {
  // 状态码
  int32 code = 1;
  // 消息
  string err_msg = 2;
}

message ElmDelProductReq {
  // 合作方门店ID
  string shop_id = 1;
  // 商品id
  string sku_id = 2;
}

message ElmDownProductReq {
  // 合作方门店ID
  string shop_id = 1;
  // 商品id
  string sku_id = 2;
  // 渠道
  int32 app_channel = 3;
  // 操作类型：0-上架，1-下架
  int32 operate_type = 4;
}

message ElmDownProductResp {
  // 状态码
  int32 code = 1;
  // 消息
  string err_msg = 2;
}