// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pc/channelProduct.proto

package pc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/golang/protobuf/ptypes/struct"
	_ "github.com/golang/protobuf/ptypes/wrappers"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GetProductIdBySkuIdRequest struct {
	SkuId                []int32  `protobuf:"varint,1,rep,packed,name=sku_id,json=skuId,proto3" json:"sku_id"`
	IsSaas               int32    `protobuf:"varint,2,opt,name=is_saas,json=isSaas,proto3" json:"is_saas"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProductIdBySkuIdRequest) Reset()         { *m = GetProductIdBySkuIdRequest{} }
func (m *GetProductIdBySkuIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetProductIdBySkuIdRequest) ProtoMessage()    {}
func (*GetProductIdBySkuIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{0}
}

func (m *GetProductIdBySkuIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductIdBySkuIdRequest.Unmarshal(m, b)
}
func (m *GetProductIdBySkuIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductIdBySkuIdRequest.Marshal(b, m, deterministic)
}
func (m *GetProductIdBySkuIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductIdBySkuIdRequest.Merge(m, src)
}
func (m *GetProductIdBySkuIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetProductIdBySkuIdRequest.Size(m)
}
func (m *GetProductIdBySkuIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductIdBySkuIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductIdBySkuIdRequest proto.InternalMessageInfo

func (m *GetProductIdBySkuIdRequest) GetSkuId() []int32 {
	if m != nil {
		return m.SkuId
	}
	return nil
}

func (m *GetProductIdBySkuIdRequest) GetIsSaas() int32 {
	if m != nil {
		return m.IsSaas
	}
	return 0
}

type GetProductIdBySkuIdResponse struct {
	Data                 map[int32]int32 `protobuf:"bytes,1,rep,name=data,proto3" json:"data" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetProductIdBySkuIdResponse) Reset()         { *m = GetProductIdBySkuIdResponse{} }
func (m *GetProductIdBySkuIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetProductIdBySkuIdResponse) ProtoMessage()    {}
func (*GetProductIdBySkuIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{1}
}

func (m *GetProductIdBySkuIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductIdBySkuIdResponse.Unmarshal(m, b)
}
func (m *GetProductIdBySkuIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductIdBySkuIdResponse.Marshal(b, m, deterministic)
}
func (m *GetProductIdBySkuIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductIdBySkuIdResponse.Merge(m, src)
}
func (m *GetProductIdBySkuIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetProductIdBySkuIdResponse.Size(m)
}
func (m *GetProductIdBySkuIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductIdBySkuIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductIdBySkuIdResponse proto.InternalMessageInfo

func (m *GetProductIdBySkuIdResponse) GetData() map[int32]int32 {
	if m != nil {
		return m.Data
	}
	return nil
}

type TaskRunVo struct {
	// 启动类型 1：阿闻门店仓自动上架 2 阿闻前置仓上架 3： mt门店仓自动上架  4：mt前置仓自动上架  5：ele门店仓自动上架  6：ele前置仓自动上架
	// 7: 医疗门店仓自动上架 8：医疗前置仓自动上架 9:阿闻前置仓价格  10：mt前置仓价格 11：ele前置仓价格同步 12：jd前置仓价格同步
	Data                 int32    `protobuf:"varint,1,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskRunVo) Reset()         { *m = TaskRunVo{} }
func (m *TaskRunVo) String() string { return proto.CompactTextString(m) }
func (*TaskRunVo) ProtoMessage()    {}
func (*TaskRunVo) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{2}
}

func (m *TaskRunVo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskRunVo.Unmarshal(m, b)
}
func (m *TaskRunVo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskRunVo.Marshal(b, m, deterministic)
}
func (m *TaskRunVo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskRunVo.Merge(m, src)
}
func (m *TaskRunVo) XXX_Size() int {
	return xxx_messageInfo_TaskRunVo.Size(m)
}
func (m *TaskRunVo) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskRunVo.DiscardUnknown(m)
}

var xxx_messageInfo_TaskRunVo proto.InternalMessageInfo

func (m *TaskRunVo) GetData() int32 {
	if m != nil {
		return m.Data
	}
	return 0
}

type TaskRunBaseResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskRunBaseResponse) Reset()         { *m = TaskRunBaseResponse{} }
func (m *TaskRunBaseResponse) String() string { return proto.CompactTextString(m) }
func (*TaskRunBaseResponse) ProtoMessage()    {}
func (*TaskRunBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{3}
}

func (m *TaskRunBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskRunBaseResponse.Unmarshal(m, b)
}
func (m *TaskRunBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskRunBaseResponse.Marshal(b, m, deterministic)
}
func (m *TaskRunBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskRunBaseResponse.Merge(m, src)
}
func (m *TaskRunBaseResponse) XXX_Size() int {
	return xxx_messageInfo_TaskRunBaseResponse.Size(m)
}
func (m *TaskRunBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskRunBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TaskRunBaseResponse proto.InternalMessageInfo

func (m *TaskRunBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TaskRunBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *TaskRunBaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type ChannelProductListReq struct {
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize  int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 查询条件
	Where string `protobuf:"bytes,3,opt,name=where,proto3" json:"where"`
	// 查询条件的类型（商品筛选类型，为空则为综合筛选（name=商品名称，id=平台商品ID，third_spu_sku_id=商品货号，sku_id=SKUID，bar_code商品条码））
	WhereType string `protobuf:"bytes,4,opt,name=where_type,json=whereType,proto3" json:"where_type"`
	// 分类id
	CategoryId int32 `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	// 品牌ID
	BrandId int32 `protobuf:"varint,6,opt,name=brand_id,json=brandId,proto3" json:"brand_id"`
	// 商品类别（0-所有，1-实物商品，2-虚拟商品，31实实组合，33虚实组合）
	ProductType int32 `protobuf:"varint,7,opt,name=product_type,json=productType,proto3" json:"product_type"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,10,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 上下架状态（-1查询所有，0下架，1上架，2有库存，3有库存已下架）
	UpDownState int32 `protobuf:"varint,11,opt,name=up_down_state,json=upDownState,proto3" json:"up_down_state"`
	// 门店财务编码
	FinanceCode string `protobuf:"bytes,13,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 是否药品
	IsDrugs int32 `protobuf:"varint,14,opt,name=is_drugs,json=isDrugs,proto3" json:"is_drugs"`
	//是否力荐商品
	IsRecommend int32 `protobuf:"varint,15,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend"`
	//最小购买数量
	MinOrderCount int32 `protobuf:"varint,16,opt,name=min_order_count,json=minOrderCount,proto3" json:"min_order_count"`
	//最大购买数量
	MaxOrderCount        int32    `protobuf:"varint,17,opt,name=max_order_count,json=maxOrderCount,proto3" json:"max_order_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductListReq) Reset()         { *m = ChannelProductListReq{} }
func (m *ChannelProductListReq) String() string { return proto.CompactTextString(m) }
func (*ChannelProductListReq) ProtoMessage()    {}
func (*ChannelProductListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{4}
}

func (m *ChannelProductListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductListReq.Unmarshal(m, b)
}
func (m *ChannelProductListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductListReq.Marshal(b, m, deterministic)
}
func (m *ChannelProductListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductListReq.Merge(m, src)
}
func (m *ChannelProductListReq) XXX_Size() int {
	return xxx_messageInfo_ChannelProductListReq.Size(m)
}
func (m *ChannelProductListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductListReq proto.InternalMessageInfo

func (m *ChannelProductListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ChannelProductListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ChannelProductListReq) GetWhere() string {
	if m != nil {
		return m.Where
	}
	return ""
}

func (m *ChannelProductListReq) GetWhereType() string {
	if m != nil {
		return m.WhereType
	}
	return ""
}

func (m *ChannelProductListReq) GetCategoryId() int32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *ChannelProductListReq) GetBrandId() int32 {
	if m != nil {
		return m.BrandId
	}
	return 0
}

func (m *ChannelProductListReq) GetProductType() int32 {
	if m != nil {
		return m.ProductType
	}
	return 0
}

func (m *ChannelProductListReq) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelProductListReq) GetUpDownState() int32 {
	if m != nil {
		return m.UpDownState
	}
	return 0
}

func (m *ChannelProductListReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *ChannelProductListReq) GetIsDrugs() int32 {
	if m != nil {
		return m.IsDrugs
	}
	return 0
}

func (m *ChannelProductListReq) GetIsRecommend() int32 {
	if m != nil {
		return m.IsRecommend
	}
	return 0
}

func (m *ChannelProductListReq) GetMinOrderCount() int32 {
	if m != nil {
		return m.MinOrderCount
	}
	return 0
}

func (m *ChannelProductListReq) GetMaxOrderCount() int32 {
	if m != nil {
		return m.MaxOrderCount
	}
	return 0
}

type ChannelProductList struct {
	// 商品id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 商品名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 商品类别文本
	TypeText string `protobuf:"bytes,3,opt,name=type_text,json=typeText,proto3" json:"type_text"`
	// 1-实物商品，2-虚拟商品，3-组合商品
	ProductType int32 `protobuf:"varint,4,opt,name=product_type,json=productType,proto3" json:"product_type"`
	// 商品图片（多图）
	Pic string `protobuf:"bytes,5,opt,name=pic,proto3" json:"pic"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
	IsUse int32 `protobuf:"varint,7,opt,name=is_use,json=isUse,proto3" json:"is_use"`
	// 店内分类
	ChannelCategoryId int32 `protobuf:"varint,8,opt,name=channel_category_id,json=channelCategoryId,proto3" json:"channel_category_id"`
	// 商品最后更新日期
	UpdateDate string `protobuf:"bytes,9,opt,name=update_date,json=updateDate,proto3" json:"update_date"`
	// sku数据
	Skus                []*ChannelProductList_Sku `protobuf:"bytes,10,rep,name=skus,proto3" json:"skus"`
	ChannelCategoryName string                    `protobuf:"bytes,11,opt,name=channel_category_name,json=channelCategoryName,proto3" json:"channel_category_name"`
	//是否力荐商品
	IsRecommend int32 `protobuf:"varint,12,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend"`
	//最小起购数
	MinOrderCount int32 `protobuf:"varint,13,opt,name=min_order_count,json=minOrderCount,proto3" json:"min_order_count"`
	//是否药品
	IsDrugs int32 `protobuf:"varint,14,opt,name=is_drugs,json=isDrugs,proto3" json:"is_drugs"`
	//第三方的商品ID
	ProductThirdId string `protobuf:"bytes,15,opt,name=product_third_id,json=productThirdId,proto3" json:"product_third_id"`
	//操作第三方时候的错信息
	SyncError            string   `protobuf:"bytes,16,opt,name=sync_error,json=syncError,proto3" json:"sync_error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductList) Reset()         { *m = ChannelProductList{} }
func (m *ChannelProductList) String() string { return proto.CompactTextString(m) }
func (*ChannelProductList) ProtoMessage()    {}
func (*ChannelProductList) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{5}
}

func (m *ChannelProductList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductList.Unmarshal(m, b)
}
func (m *ChannelProductList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductList.Marshal(b, m, deterministic)
}
func (m *ChannelProductList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductList.Merge(m, src)
}
func (m *ChannelProductList) XXX_Size() int {
	return xxx_messageInfo_ChannelProductList.Size(m)
}
func (m *ChannelProductList) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductList.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductList proto.InternalMessageInfo

func (m *ChannelProductList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChannelProductList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelProductList) GetTypeText() string {
	if m != nil {
		return m.TypeText
	}
	return ""
}

func (m *ChannelProductList) GetProductType() int32 {
	if m != nil {
		return m.ProductType
	}
	return 0
}

func (m *ChannelProductList) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *ChannelProductList) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelProductList) GetIsUse() int32 {
	if m != nil {
		return m.IsUse
	}
	return 0
}

func (m *ChannelProductList) GetChannelCategoryId() int32 {
	if m != nil {
		return m.ChannelCategoryId
	}
	return 0
}

func (m *ChannelProductList) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

func (m *ChannelProductList) GetSkus() []*ChannelProductList_Sku {
	if m != nil {
		return m.Skus
	}
	return nil
}

func (m *ChannelProductList) GetChannelCategoryName() string {
	if m != nil {
		return m.ChannelCategoryName
	}
	return ""
}

func (m *ChannelProductList) GetIsRecommend() int32 {
	if m != nil {
		return m.IsRecommend
	}
	return 0
}

func (m *ChannelProductList) GetMinOrderCount() int32 {
	if m != nil {
		return m.MinOrderCount
	}
	return 0
}

func (m *ChannelProductList) GetIsDrugs() int32 {
	if m != nil {
		return m.IsDrugs
	}
	return 0
}

func (m *ChannelProductList) GetProductThirdId() string {
	if m != nil {
		return m.ProductThirdId
	}
	return ""
}

func (m *ChannelProductList) GetSyncError() string {
	if m != nil {
		return m.SyncError
	}
	return ""
}

type ChannelProductList_Sku struct {
	// skuId
	SkuId int32 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// a8货号
	A8Id string `protobuf:"bytes,2,opt,name=a8_id,json=a8Id,proto3" json:"a8_id"`
	// 子龙货号
	ZilongId string `protobuf:"bytes,3,opt,name=zilong_id,json=zilongId,proto3" json:"zilong_id"`
	// 库存数据，虚拟商品不返回，阿闻渠道返回2组数据
	Stocks []*ChannelProductList_Sku_Stock `protobuf:"bytes,4,rep,name=stocks,proto3" json:"stocks"`
	// 价格（分）
	Price int32 `protobuf:"varint,5,opt,name=price,proto3" json:"price"`
	// 会员价格（分）
	VipPrice int32 `protobuf:"varint,11,opt,name=vip_price,json=vipPrice,proto3" json:"vip_price"`
	// 规格值
	SpecValue string `protobuf:"bytes,6,opt,name=spec_value,json=specValue,proto3" json:"spec_value"`
	// 商品条码
	BarCode string `protobuf:"bytes,7,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	// 总量
	WeightForUnit float64 `protobuf:"fixed64,8,opt,name=weight_for_unit,json=weightForUnit,proto3" json:"weight_for_unit"`
	// 平台a8货号
	A8IdPt string `protobuf:"bytes,9,opt,name=a8_id_pt,json=a8IdPt,proto3" json:"a8_id_pt"`
	// 平台子龙货号
	ZilongIdPt           string   `protobuf:"bytes,10,opt,name=zilong_id_pt,json=zilongIdPt,proto3" json:"zilong_id_pt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductList_Sku) Reset()         { *m = ChannelProductList_Sku{} }
func (m *ChannelProductList_Sku) String() string { return proto.CompactTextString(m) }
func (*ChannelProductList_Sku) ProtoMessage()    {}
func (*ChannelProductList_Sku) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{5, 0}
}

func (m *ChannelProductList_Sku) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductList_Sku.Unmarshal(m, b)
}
func (m *ChannelProductList_Sku) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductList_Sku.Marshal(b, m, deterministic)
}
func (m *ChannelProductList_Sku) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductList_Sku.Merge(m, src)
}
func (m *ChannelProductList_Sku) XXX_Size() int {
	return xxx_messageInfo_ChannelProductList_Sku.Size(m)
}
func (m *ChannelProductList_Sku) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductList_Sku.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductList_Sku proto.InternalMessageInfo

func (m *ChannelProductList_Sku) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ChannelProductList_Sku) GetA8Id() string {
	if m != nil {
		return m.A8Id
	}
	return ""
}

func (m *ChannelProductList_Sku) GetZilongId() string {
	if m != nil {
		return m.ZilongId
	}
	return ""
}

func (m *ChannelProductList_Sku) GetStocks() []*ChannelProductList_Sku_Stock {
	if m != nil {
		return m.Stocks
	}
	return nil
}

func (m *ChannelProductList_Sku) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ChannelProductList_Sku) GetVipPrice() int32 {
	if m != nil {
		return m.VipPrice
	}
	return 0
}

func (m *ChannelProductList_Sku) GetSpecValue() string {
	if m != nil {
		return m.SpecValue
	}
	return ""
}

func (m *ChannelProductList_Sku) GetBarCode() string {
	if m != nil {
		return m.BarCode
	}
	return ""
}

func (m *ChannelProductList_Sku) GetWeightForUnit() float64 {
	if m != nil {
		return m.WeightForUnit
	}
	return 0
}

func (m *ChannelProductList_Sku) GetA8IdPt() string {
	if m != nil {
		return m.A8IdPt
	}
	return ""
}

func (m *ChannelProductList_Sku) GetZilongIdPt() string {
	if m != nil {
		return m.ZilongIdPt
	}
	return ""
}

type ChannelProductList_Sku_Stock struct {
	// 可售库存
	Qty int32 `protobuf:"varint,1,opt,name=qty,proto3" json:"qty"`
	// 锁定库存
	LockQty              int32    `protobuf:"varint,2,opt,name=lock_qty,json=lockQty,proto3" json:"lock_qty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductList_Sku_Stock) Reset()         { *m = ChannelProductList_Sku_Stock{} }
func (m *ChannelProductList_Sku_Stock) String() string { return proto.CompactTextString(m) }
func (*ChannelProductList_Sku_Stock) ProtoMessage()    {}
func (*ChannelProductList_Sku_Stock) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{5, 0, 0}
}

func (m *ChannelProductList_Sku_Stock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductList_Sku_Stock.Unmarshal(m, b)
}
func (m *ChannelProductList_Sku_Stock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductList_Sku_Stock.Marshal(b, m, deterministic)
}
func (m *ChannelProductList_Sku_Stock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductList_Sku_Stock.Merge(m, src)
}
func (m *ChannelProductList_Sku_Stock) XXX_Size() int {
	return xxx_messageInfo_ChannelProductList_Sku_Stock.Size(m)
}
func (m *ChannelProductList_Sku_Stock) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductList_Sku_Stock.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductList_Sku_Stock proto.InternalMessageInfo

func (m *ChannelProductList_Sku_Stock) GetQty() int32 {
	if m != nil {
		return m.Qty
	}
	return 0
}

func (m *ChannelProductList_Sku_Stock) GetLockQty() int32 {
	if m != nil {
		return m.LockQty
	}
	return 0
}

type ChannelProductExceptionListRes struct {
	Code    int32                          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*ChannelProductExceptionList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 返回总数，用于分页
	TotalCount           int32    `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductExceptionListRes) Reset()         { *m = ChannelProductExceptionListRes{} }
func (m *ChannelProductExceptionListRes) String() string { return proto.CompactTextString(m) }
func (*ChannelProductExceptionListRes) ProtoMessage()    {}
func (*ChannelProductExceptionListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{6}
}

func (m *ChannelProductExceptionListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductExceptionListRes.Unmarshal(m, b)
}
func (m *ChannelProductExceptionListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductExceptionListRes.Marshal(b, m, deterministic)
}
func (m *ChannelProductExceptionListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductExceptionListRes.Merge(m, src)
}
func (m *ChannelProductExceptionListRes) XXX_Size() int {
	return xxx_messageInfo_ChannelProductExceptionListRes.Size(m)
}
func (m *ChannelProductExceptionListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductExceptionListRes.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductExceptionListRes proto.InternalMessageInfo

func (m *ChannelProductExceptionListRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChannelProductExceptionListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ChannelProductExceptionListRes) GetData() []*ChannelProductExceptionList {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ChannelProductExceptionListRes) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type ChannelProductExceptionList struct {
	//重复的货号描述
	ThirdSkuId string                           `protobuf:"bytes,1,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	Data       []*ChannelProductExceptionDetail `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	//重复的货号
	ThirdSkuItem string `protobuf:"bytes,3,opt,name=third_sku_item,json=thirdSkuItem,proto3" json:"third_sku_item"`
	//重复的货号
	ErpId                int32    `protobuf:"varint,4,opt,name=erp_id,json=erpId,proto3" json:"erp_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductExceptionList) Reset()         { *m = ChannelProductExceptionList{} }
func (m *ChannelProductExceptionList) String() string { return proto.CompactTextString(m) }
func (*ChannelProductExceptionList) ProtoMessage()    {}
func (*ChannelProductExceptionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{7}
}

func (m *ChannelProductExceptionList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductExceptionList.Unmarshal(m, b)
}
func (m *ChannelProductExceptionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductExceptionList.Marshal(b, m, deterministic)
}
func (m *ChannelProductExceptionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductExceptionList.Merge(m, src)
}
func (m *ChannelProductExceptionList) XXX_Size() int {
	return xxx_messageInfo_ChannelProductExceptionList.Size(m)
}
func (m *ChannelProductExceptionList) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductExceptionList.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductExceptionList proto.InternalMessageInfo

func (m *ChannelProductExceptionList) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *ChannelProductExceptionList) GetData() []*ChannelProductExceptionDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ChannelProductExceptionList) GetThirdSkuItem() string {
	if m != nil {
		return m.ThirdSkuItem
	}
	return ""
}

func (m *ChannelProductExceptionList) GetErpId() int32 {
	if m != nil {
		return m.ErpId
	}
	return 0
}

type ChannelProductExceptionDetail struct {
	// 商品id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 商品名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 商品类别文本
	TypeText string `protobuf:"bytes,3,opt,name=type_text,json=typeText,proto3" json:"type_text"`
	// 1-实物商品，2-虚拟商品，3-组合商品
	ProductType int32 `protobuf:"varint,4,opt,name=product_type,json=productType,proto3" json:"product_type"`
	// 商品图片（多图）
	Pic string `protobuf:"bytes,5,opt,name=pic,proto3" json:"pic"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
	IsUse int32 `protobuf:"varint,7,opt,name=is_use,json=isUse,proto3" json:"is_use"`
	// 店内分类
	ChannelCategoryId int32 `protobuf:"varint,8,opt,name=channel_category_id,json=channelCategoryId,proto3" json:"channel_category_id"`
	// 商品最后更新日期
	UpdateDate string `protobuf:"bytes,10,opt,name=update_date,json=updateDate,proto3" json:"update_date"`
	//A8货号
	A8Id string `protobuf:"bytes,11,opt,name=a8_id,json=a8Id,proto3" json:"a8_id"`
	// 子龙货号
	ZilongId string `protobuf:"bytes,12,opt,name=zilong_id,json=zilongId,proto3" json:"zilong_id"`
	// 平台a8货号
	A8IdPt string `protobuf:"bytes,13,opt,name=a8_id_pt,json=a8IdPt,proto3" json:"a8_id_pt"`
	// 平台子龙货号
	ZilongIdPt string `protobuf:"bytes,14,opt,name=zilong_id_pt,json=zilongIdPt,proto3" json:"zilong_id_pt"`
	// skuid
	SkuId int32 `protobuf:"varint,15,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//erp_id
	ErpId                string   `protobuf:"bytes,16,opt,name=erp_id,json=erpId,proto3" json:"erp_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductExceptionDetail) Reset()         { *m = ChannelProductExceptionDetail{} }
func (m *ChannelProductExceptionDetail) String() string { return proto.CompactTextString(m) }
func (*ChannelProductExceptionDetail) ProtoMessage()    {}
func (*ChannelProductExceptionDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{8}
}

func (m *ChannelProductExceptionDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductExceptionDetail.Unmarshal(m, b)
}
func (m *ChannelProductExceptionDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductExceptionDetail.Marshal(b, m, deterministic)
}
func (m *ChannelProductExceptionDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductExceptionDetail.Merge(m, src)
}
func (m *ChannelProductExceptionDetail) XXX_Size() int {
	return xxx_messageInfo_ChannelProductExceptionDetail.Size(m)
}
func (m *ChannelProductExceptionDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductExceptionDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductExceptionDetail proto.InternalMessageInfo

func (m *ChannelProductExceptionDetail) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChannelProductExceptionDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelProductExceptionDetail) GetTypeText() string {
	if m != nil {
		return m.TypeText
	}
	return ""
}

func (m *ChannelProductExceptionDetail) GetProductType() int32 {
	if m != nil {
		return m.ProductType
	}
	return 0
}

func (m *ChannelProductExceptionDetail) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *ChannelProductExceptionDetail) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelProductExceptionDetail) GetIsUse() int32 {
	if m != nil {
		return m.IsUse
	}
	return 0
}

func (m *ChannelProductExceptionDetail) GetChannelCategoryId() int32 {
	if m != nil {
		return m.ChannelCategoryId
	}
	return 0
}

func (m *ChannelProductExceptionDetail) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

func (m *ChannelProductExceptionDetail) GetA8Id() string {
	if m != nil {
		return m.A8Id
	}
	return ""
}

func (m *ChannelProductExceptionDetail) GetZilongId() string {
	if m != nil {
		return m.ZilongId
	}
	return ""
}

func (m *ChannelProductExceptionDetail) GetA8IdPt() string {
	if m != nil {
		return m.A8IdPt
	}
	return ""
}

func (m *ChannelProductExceptionDetail) GetZilongIdPt() string {
	if m != nil {
		return m.ZilongIdPt
	}
	return ""
}

func (m *ChannelProductExceptionDetail) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ChannelProductExceptionDetail) GetErpId() string {
	if m != nil {
		return m.ErpId
	}
	return ""
}

type ChannelProductListRes struct {
	Code    int32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*ChannelProductList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 返回总数，用于分页
	TotalCount int32 `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	// 仓库类型名称
	WarehouseType        []string `protobuf:"bytes,5,rep,name=warehouse_type,json=warehouseType,proto3" json:"warehouse_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductListRes) Reset()         { *m = ChannelProductListRes{} }
func (m *ChannelProductListRes) String() string { return proto.CompactTextString(m) }
func (*ChannelProductListRes) ProtoMessage()    {}
func (*ChannelProductListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{9}
}

func (m *ChannelProductListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductListRes.Unmarshal(m, b)
}
func (m *ChannelProductListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductListRes.Marshal(b, m, deterministic)
}
func (m *ChannelProductListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductListRes.Merge(m, src)
}
func (m *ChannelProductListRes) XXX_Size() int {
	return xxx_messageInfo_ChannelProductListRes.Size(m)
}
func (m *ChannelProductListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductListRes.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductListRes proto.InternalMessageInfo

func (m *ChannelProductListRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChannelProductListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ChannelProductListRes) GetData() []*ChannelProductList {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ChannelProductListRes) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *ChannelProductListRes) GetWarehouseType() []string {
	if m != nil {
		return m.WarehouseType
	}
	return nil
}

// 渠道商品库列表商品计数
type ChannelProductCountReq struct {
	// 不传取请求头
	ChannelId int32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 门店财务编码，不传取请求头
	FinanceCode string `protobuf:"bytes,2,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 分类id
	CategoryId           int32    `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductCountReq) Reset()         { *m = ChannelProductCountReq{} }
func (m *ChannelProductCountReq) String() string { return proto.CompactTextString(m) }
func (*ChannelProductCountReq) ProtoMessage()    {}
func (*ChannelProductCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{10}
}

func (m *ChannelProductCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductCountReq.Unmarshal(m, b)
}
func (m *ChannelProductCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductCountReq.Marshal(b, m, deterministic)
}
func (m *ChannelProductCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductCountReq.Merge(m, src)
}
func (m *ChannelProductCountReq) XXX_Size() int {
	return xxx_messageInfo_ChannelProductCountReq.Size(m)
}
func (m *ChannelProductCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductCountReq proto.InternalMessageInfo

func (m *ChannelProductCountReq) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelProductCountReq) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *ChannelProductCountReq) GetCategoryId() int32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

type ChannelProductCountRes struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 全部
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 上架
	Up int32 `protobuf:"varint,4,opt,name=up,proto3" json:"up"`
	// 下架
	Down int32 `protobuf:"varint,5,opt,name=down,proto3" json:"down"`
	// 有库存
	HasStock int32 `protobuf:"varint,6,opt,name=has_stock,json=hasStock,proto3" json:"has_stock"`
	// 有库存已下架
	HasStockAndDown      int32    `protobuf:"varint,7,opt,name=has_stock_and_down,json=hasStockAndDown,proto3" json:"has_stock_and_down"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductCountRes) Reset()         { *m = ChannelProductCountRes{} }
func (m *ChannelProductCountRes) String() string { return proto.CompactTextString(m) }
func (*ChannelProductCountRes) ProtoMessage()    {}
func (*ChannelProductCountRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{11}
}

func (m *ChannelProductCountRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductCountRes.Unmarshal(m, b)
}
func (m *ChannelProductCountRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductCountRes.Marshal(b, m, deterministic)
}
func (m *ChannelProductCountRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductCountRes.Merge(m, src)
}
func (m *ChannelProductCountRes) XXX_Size() int {
	return xxx_messageInfo_ChannelProductCountRes.Size(m)
}
func (m *ChannelProductCountRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductCountRes.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductCountRes proto.InternalMessageInfo

func (m *ChannelProductCountRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChannelProductCountRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ChannelProductCountRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ChannelProductCountRes) GetUp() int32 {
	if m != nil {
		return m.Up
	}
	return 0
}

func (m *ChannelProductCountRes) GetDown() int32 {
	if m != nil {
		return m.Down
	}
	return 0
}

func (m *ChannelProductCountRes) GetHasStock() int32 {
	if m != nil {
		return m.HasStock
	}
	return 0
}

func (m *ChannelProductCountRes) GetHasStockAndDown() int32 {
	if m != nil {
		return m.HasStockAndDown
	}
	return 0
}

type StoreProductUpDistinctReq struct {
	// 渠道id
	ChannelId int32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 财务编码
	FinanceCode []string `protobuf:"bytes,2,rep,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 商品id
	ProductId []int32 `protobuf:"varint,3,rep,packed,name=product_id,json=productId,proto3" json:"product_id"`
	// skuId
	SkuId []int32 `protobuf:"varint,4,rep,packed,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商品名称
	Name                 string   `protobuf:"bytes,5,opt,name=name,proto3" json:"name"`
	PageIndex            int32    `protobuf:"varint,6,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreProductUpDistinctReq) Reset()         { *m = StoreProductUpDistinctReq{} }
func (m *StoreProductUpDistinctReq) String() string { return proto.CompactTextString(m) }
func (*StoreProductUpDistinctReq) ProtoMessage()    {}
func (*StoreProductUpDistinctReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{12}
}

func (m *StoreProductUpDistinctReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreProductUpDistinctReq.Unmarshal(m, b)
}
func (m *StoreProductUpDistinctReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreProductUpDistinctReq.Marshal(b, m, deterministic)
}
func (m *StoreProductUpDistinctReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreProductUpDistinctReq.Merge(m, src)
}
func (m *StoreProductUpDistinctReq) XXX_Size() int {
	return xxx_messageInfo_StoreProductUpDistinctReq.Size(m)
}
func (m *StoreProductUpDistinctReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreProductUpDistinctReq.DiscardUnknown(m)
}

var xxx_messageInfo_StoreProductUpDistinctReq proto.InternalMessageInfo

func (m *StoreProductUpDistinctReq) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StoreProductUpDistinctReq) GetFinanceCode() []string {
	if m != nil {
		return m.FinanceCode
	}
	return nil
}

func (m *StoreProductUpDistinctReq) GetProductId() []int32 {
	if m != nil {
		return m.ProductId
	}
	return nil
}

func (m *StoreProductUpDistinctReq) GetSkuId() []int32 {
	if m != nil {
		return m.SkuId
	}
	return nil
}

func (m *StoreProductUpDistinctReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StoreProductUpDistinctReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *StoreProductUpDistinctReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type StoreProductUpDistinctRes struct {
	Code    int32                             `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                            `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*StoreProductUpDistinctRes_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 是否有下一页
	HasMore              bool     `protobuf:"varint,4,opt,name=has_more,json=hasMore,proto3" json:"has_more"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreProductUpDistinctRes) Reset()         { *m = StoreProductUpDistinctRes{} }
func (m *StoreProductUpDistinctRes) String() string { return proto.CompactTextString(m) }
func (*StoreProductUpDistinctRes) ProtoMessage()    {}
func (*StoreProductUpDistinctRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{13}
}

func (m *StoreProductUpDistinctRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreProductUpDistinctRes.Unmarshal(m, b)
}
func (m *StoreProductUpDistinctRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreProductUpDistinctRes.Marshal(b, m, deterministic)
}
func (m *StoreProductUpDistinctRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreProductUpDistinctRes.Merge(m, src)
}
func (m *StoreProductUpDistinctRes) XXX_Size() int {
	return xxx_messageInfo_StoreProductUpDistinctRes.Size(m)
}
func (m *StoreProductUpDistinctRes) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreProductUpDistinctRes.DiscardUnknown(m)
}

var xxx_messageInfo_StoreProductUpDistinctRes proto.InternalMessageInfo

func (m *StoreProductUpDistinctRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StoreProductUpDistinctRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *StoreProductUpDistinctRes) GetData() []*StoreProductUpDistinctRes_List {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *StoreProductUpDistinctRes) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type StoreProductUpDistinctRes_List struct {
	// 商品id
	ProductId int32 `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id"`
	// skuId
	SkuId int32 `protobuf:"varint,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商品名称
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreProductUpDistinctRes_List) Reset()         { *m = StoreProductUpDistinctRes_List{} }
func (m *StoreProductUpDistinctRes_List) String() string { return proto.CompactTextString(m) }
func (*StoreProductUpDistinctRes_List) ProtoMessage()    {}
func (*StoreProductUpDistinctRes_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{13, 0}
}

func (m *StoreProductUpDistinctRes_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreProductUpDistinctRes_List.Unmarshal(m, b)
}
func (m *StoreProductUpDistinctRes_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreProductUpDistinctRes_List.Marshal(b, m, deterministic)
}
func (m *StoreProductUpDistinctRes_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreProductUpDistinctRes_List.Merge(m, src)
}
func (m *StoreProductUpDistinctRes_List) XXX_Size() int {
	return xxx_messageInfo_StoreProductUpDistinctRes_List.Size(m)
}
func (m *StoreProductUpDistinctRes_List) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreProductUpDistinctRes_List.DiscardUnknown(m)
}

var xxx_messageInfo_StoreProductUpDistinctRes_List proto.InternalMessageInfo

func (m *StoreProductUpDistinctRes_List) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *StoreProductUpDistinctRes_List) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *StoreProductUpDistinctRes_List) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 商品按价格统计
type ProductCountByPriceReq struct {
	// 渠道id
	ChannelId int32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 财务编码
	FinanceCode []string `protobuf:"bytes,2,rep,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 商品sku_id
	SkuId                []int32  `protobuf:"varint,3,rep,packed,name=sku_id,json=skuId,proto3" json:"sku_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductCountByPriceReq) Reset()         { *m = ProductCountByPriceReq{} }
func (m *ProductCountByPriceReq) String() string { return proto.CompactTextString(m) }
func (*ProductCountByPriceReq) ProtoMessage()    {}
func (*ProductCountByPriceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{14}
}

func (m *ProductCountByPriceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductCountByPriceReq.Unmarshal(m, b)
}
func (m *ProductCountByPriceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductCountByPriceReq.Marshal(b, m, deterministic)
}
func (m *ProductCountByPriceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductCountByPriceReq.Merge(m, src)
}
func (m *ProductCountByPriceReq) XXX_Size() int {
	return xxx_messageInfo_ProductCountByPriceReq.Size(m)
}
func (m *ProductCountByPriceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductCountByPriceReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProductCountByPriceReq proto.InternalMessageInfo

func (m *ProductCountByPriceReq) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ProductCountByPriceReq) GetFinanceCode() []string {
	if m != nil {
		return m.FinanceCode
	}
	return nil
}

func (m *ProductCountByPriceReq) GetSkuId() []int32 {
	if m != nil {
		return m.SkuId
	}
	return nil
}

type ProductCountByPriceRes struct {
	Code                 int32                          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*ProductCountByPriceRes_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *ProductCountByPriceRes) Reset()         { *m = ProductCountByPriceRes{} }
func (m *ProductCountByPriceRes) String() string { return proto.CompactTextString(m) }
func (*ProductCountByPriceRes) ProtoMessage()    {}
func (*ProductCountByPriceRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{15}
}

func (m *ProductCountByPriceRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductCountByPriceRes.Unmarshal(m, b)
}
func (m *ProductCountByPriceRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductCountByPriceRes.Marshal(b, m, deterministic)
}
func (m *ProductCountByPriceRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductCountByPriceRes.Merge(m, src)
}
func (m *ProductCountByPriceRes) XXX_Size() int {
	return xxx_messageInfo_ProductCountByPriceRes.Size(m)
}
func (m *ProductCountByPriceRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductCountByPriceRes.DiscardUnknown(m)
}

var xxx_messageInfo_ProductCountByPriceRes proto.InternalMessageInfo

func (m *ProductCountByPriceRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ProductCountByPriceRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ProductCountByPriceRes) GetData() []*ProductCountByPriceRes_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type ProductCountByPriceRes_Data struct {
	// 商品id
	ProductId int32 `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id"`
	// skuId
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商品名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	// 售价
	MarketPrice int32 `protobuf:"varint,4,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	// 关联门店数量
	Count int32 `protobuf:"varint,5,opt,name=count,proto3" json:"count"`
	// 随机一家门店名称
	ShopName             string   `protobuf:"bytes,6,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductCountByPriceRes_Data) Reset()         { *m = ProductCountByPriceRes_Data{} }
func (m *ProductCountByPriceRes_Data) String() string { return proto.CompactTextString(m) }
func (*ProductCountByPriceRes_Data) ProtoMessage()    {}
func (*ProductCountByPriceRes_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{15, 0}
}

func (m *ProductCountByPriceRes_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductCountByPriceRes_Data.Unmarshal(m, b)
}
func (m *ProductCountByPriceRes_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductCountByPriceRes_Data.Marshal(b, m, deterministic)
}
func (m *ProductCountByPriceRes_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductCountByPriceRes_Data.Merge(m, src)
}
func (m *ProductCountByPriceRes_Data) XXX_Size() int {
	return xxx_messageInfo_ProductCountByPriceRes_Data.Size(m)
}
func (m *ProductCountByPriceRes_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductCountByPriceRes_Data.DiscardUnknown(m)
}

var xxx_messageInfo_ProductCountByPriceRes_Data proto.InternalMessageInfo

func (m *ProductCountByPriceRes_Data) GetProductId() int32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ProductCountByPriceRes_Data) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ProductCountByPriceRes_Data) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ProductCountByPriceRes_Data) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *ProductCountByPriceRes_Data) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ProductCountByPriceRes_Data) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

// 商品价格关联的门店
type ProductPriceStoreReq struct {
	// 渠道id
	ChannelId int32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 财务编码
	FinanceCode []string `protobuf:"bytes,2,rep,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 商品sku_id
	SkuId int32 `protobuf:"varint,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商品价格
	Price int32 `protobuf:"varint,4,opt,name=price,proto3" json:"price"`
	// 搜索类型 1门店名称、2财务编码
	SearchType int32 `protobuf:"varint,5,opt,name=search_type,json=searchType,proto3" json:"search_type"`
	// 搜索关键字
	Search               string   `protobuf:"bytes,6,opt,name=search,proto3" json:"search"`
	PageIndex            int32    `protobuf:"varint,7,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize             int32    `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductPriceStoreReq) Reset()         { *m = ProductPriceStoreReq{} }
func (m *ProductPriceStoreReq) String() string { return proto.CompactTextString(m) }
func (*ProductPriceStoreReq) ProtoMessage()    {}
func (*ProductPriceStoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{16}
}

func (m *ProductPriceStoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductPriceStoreReq.Unmarshal(m, b)
}
func (m *ProductPriceStoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductPriceStoreReq.Marshal(b, m, deterministic)
}
func (m *ProductPriceStoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductPriceStoreReq.Merge(m, src)
}
func (m *ProductPriceStoreReq) XXX_Size() int {
	return xxx_messageInfo_ProductPriceStoreReq.Size(m)
}
func (m *ProductPriceStoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductPriceStoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProductPriceStoreReq proto.InternalMessageInfo

func (m *ProductPriceStoreReq) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ProductPriceStoreReq) GetFinanceCode() []string {
	if m != nil {
		return m.FinanceCode
	}
	return nil
}

func (m *ProductPriceStoreReq) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ProductPriceStoreReq) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ProductPriceStoreReq) GetSearchType() int32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *ProductPriceStoreReq) GetSearch() string {
	if m != nil {
		return m.Search
	}
	return ""
}

func (m *ProductPriceStoreReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ProductPriceStoreReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ProductPriceStoreRes struct {
	Code    int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                       `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*ProductPriceStoreRes_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 返回总数，用于分页
	TotalCount           int32    `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductPriceStoreRes) Reset()         { *m = ProductPriceStoreRes{} }
func (m *ProductPriceStoreRes) String() string { return proto.CompactTextString(m) }
func (*ProductPriceStoreRes) ProtoMessage()    {}
func (*ProductPriceStoreRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{17}
}

func (m *ProductPriceStoreRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductPriceStoreRes.Unmarshal(m, b)
}
func (m *ProductPriceStoreRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductPriceStoreRes.Marshal(b, m, deterministic)
}
func (m *ProductPriceStoreRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductPriceStoreRes.Merge(m, src)
}
func (m *ProductPriceStoreRes) XXX_Size() int {
	return xxx_messageInfo_ProductPriceStoreRes.Size(m)
}
func (m *ProductPriceStoreRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductPriceStoreRes.DiscardUnknown(m)
}

var xxx_messageInfo_ProductPriceStoreRes proto.InternalMessageInfo

func (m *ProductPriceStoreRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ProductPriceStoreRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ProductPriceStoreRes) GetData() []*ProductPriceStoreRes_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ProductPriceStoreRes) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type ProductPriceStoreRes_Data struct {
	// 财务编码
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	// 门店名称
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductPriceStoreRes_Data) Reset()         { *m = ProductPriceStoreRes_Data{} }
func (m *ProductPriceStoreRes_Data) String() string { return proto.CompactTextString(m) }
func (*ProductPriceStoreRes_Data) ProtoMessage()    {}
func (*ProductPriceStoreRes_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{17, 0}
}

func (m *ProductPriceStoreRes_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductPriceStoreRes_Data.Unmarshal(m, b)
}
func (m *ProductPriceStoreRes_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductPriceStoreRes_Data.Marshal(b, m, deterministic)
}
func (m *ProductPriceStoreRes_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductPriceStoreRes_Data.Merge(m, src)
}
func (m *ProductPriceStoreRes_Data) XXX_Size() int {
	return xxx_messageInfo_ProductPriceStoreRes_Data.Size(m)
}
func (m *ProductPriceStoreRes_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductPriceStoreRes_Data.DiscardUnknown(m)
}

var xxx_messageInfo_ProductPriceStoreRes_Data proto.InternalMessageInfo

func (m *ProductPriceStoreRes_Data) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *ProductPriceStoreRes_Data) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type MtDelProductReq struct {
	// 商家系统的三方门店id
	AppPoiCode string `protobuf:"bytes,1,opt,name=app_poi_code,json=appPoiCode,proto3" json:"app_poi_code"`
	// 商品id
	AppSpuCode           []string `protobuf:"bytes,2,rep,name=app_spu_code,json=appSpuCode,proto3" json:"app_spu_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MtDelProductReq) Reset()         { *m = MtDelProductReq{} }
func (m *MtDelProductReq) String() string { return proto.CompactTextString(m) }
func (*MtDelProductReq) ProtoMessage()    {}
func (*MtDelProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{18}
}

func (m *MtDelProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MtDelProductReq.Unmarshal(m, b)
}
func (m *MtDelProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MtDelProductReq.Marshal(b, m, deterministic)
}
func (m *MtDelProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MtDelProductReq.Merge(m, src)
}
func (m *MtDelProductReq) XXX_Size() int {
	return xxx_messageInfo_MtDelProductReq.Size(m)
}
func (m *MtDelProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MtDelProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_MtDelProductReq proto.InternalMessageInfo

func (m *MtDelProductReq) GetAppPoiCode() string {
	if m != nil {
		return m.AppPoiCode
	}
	return ""
}

func (m *MtDelProductReq) GetAppSpuCode() []string {
	if m != nil {
		return m.AppSpuCode
	}
	return nil
}

type ChannelProductDelResp struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelProductDelResp) Reset()         { *m = ChannelProductDelResp{} }
func (m *ChannelProductDelResp) String() string { return proto.CompactTextString(m) }
func (*ChannelProductDelResp) ProtoMessage()    {}
func (*ChannelProductDelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{19}
}

func (m *ChannelProductDelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProductDelResp.Unmarshal(m, b)
}
func (m *ChannelProductDelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProductDelResp.Marshal(b, m, deterministic)
}
func (m *ChannelProductDelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProductDelResp.Merge(m, src)
}
func (m *ChannelProductDelResp) XXX_Size() int {
	return xxx_messageInfo_ChannelProductDelResp.Size(m)
}
func (m *ChannelProductDelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProductDelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProductDelResp proto.InternalMessageInfo

func (m *ChannelProductDelResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChannelProductDelResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type ElmDelProductReq struct {
	// 合作方门店ID
	ShopId string `protobuf:"bytes,1,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	// 商品id
	SkuId                string   `protobuf:"bytes,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ElmDelProductReq) Reset()         { *m = ElmDelProductReq{} }
func (m *ElmDelProductReq) String() string { return proto.CompactTextString(m) }
func (*ElmDelProductReq) ProtoMessage()    {}
func (*ElmDelProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{20}
}

func (m *ElmDelProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ElmDelProductReq.Unmarshal(m, b)
}
func (m *ElmDelProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ElmDelProductReq.Marshal(b, m, deterministic)
}
func (m *ElmDelProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ElmDelProductReq.Merge(m, src)
}
func (m *ElmDelProductReq) XXX_Size() int {
	return xxx_messageInfo_ElmDelProductReq.Size(m)
}
func (m *ElmDelProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ElmDelProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_ElmDelProductReq proto.InternalMessageInfo

func (m *ElmDelProductReq) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *ElmDelProductReq) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

type ElmDownProductReq struct {
	// 合作方门店ID
	ShopId string `protobuf:"bytes,1,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	// 商品id
	SkuId string `protobuf:"bytes,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 渠道
	AppChannel int32 `protobuf:"varint,3,opt,name=app_channel,json=appChannel,proto3" json:"app_channel"`
	// 操作类型：0-上架，1-下架
	OperateType          int32    `protobuf:"varint,4,opt,name=operate_type,json=operateType,proto3" json:"operate_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ElmDownProductReq) Reset()         { *m = ElmDownProductReq{} }
func (m *ElmDownProductReq) String() string { return proto.CompactTextString(m) }
func (*ElmDownProductReq) ProtoMessage()    {}
func (*ElmDownProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{21}
}

func (m *ElmDownProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ElmDownProductReq.Unmarshal(m, b)
}
func (m *ElmDownProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ElmDownProductReq.Marshal(b, m, deterministic)
}
func (m *ElmDownProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ElmDownProductReq.Merge(m, src)
}
func (m *ElmDownProductReq) XXX_Size() int {
	return xxx_messageInfo_ElmDownProductReq.Size(m)
}
func (m *ElmDownProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ElmDownProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_ElmDownProductReq proto.InternalMessageInfo

func (m *ElmDownProductReq) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *ElmDownProductReq) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *ElmDownProductReq) GetAppChannel() int32 {
	if m != nil {
		return m.AppChannel
	}
	return 0
}

func (m *ElmDownProductReq) GetOperateType() int32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

type ElmDownProductResp struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ElmDownProductResp) Reset()         { *m = ElmDownProductResp{} }
func (m *ElmDownProductResp) String() string { return proto.CompactTextString(m) }
func (*ElmDownProductResp) ProtoMessage()    {}
func (*ElmDownProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d22520ae0085036, []int{22}
}

func (m *ElmDownProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ElmDownProductResp.Unmarshal(m, b)
}
func (m *ElmDownProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ElmDownProductResp.Marshal(b, m, deterministic)
}
func (m *ElmDownProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ElmDownProductResp.Merge(m, src)
}
func (m *ElmDownProductResp) XXX_Size() int {
	return xxx_messageInfo_ElmDownProductResp.Size(m)
}
func (m *ElmDownProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ElmDownProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_ElmDownProductResp proto.InternalMessageInfo

func (m *ElmDownProductResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ElmDownProductResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func init() {
	proto.RegisterType((*GetProductIdBySkuIdRequest)(nil), "pc.GetProductIdBySkuIdRequest")
	proto.RegisterType((*GetProductIdBySkuIdResponse)(nil), "pc.GetProductIdBySkuIdResponse")
	proto.RegisterMapType((map[int32]int32)(nil), "pc.GetProductIdBySkuIdResponse.DataEntry")
	proto.RegisterType((*TaskRunVo)(nil), "pc.TaskRunVo")
	proto.RegisterType((*TaskRunBaseResponse)(nil), "pc.TaskRunBaseResponse")
	proto.RegisterType((*ChannelProductListReq)(nil), "pc.ChannelProductListReq")
	proto.RegisterType((*ChannelProductList)(nil), "pc.ChannelProductList")
	proto.RegisterType((*ChannelProductList_Sku)(nil), "pc.ChannelProductList.Sku")
	proto.RegisterType((*ChannelProductList_Sku_Stock)(nil), "pc.ChannelProductList.Sku.Stock")
	proto.RegisterType((*ChannelProductExceptionListRes)(nil), "pc.ChannelProductExceptionListRes")
	proto.RegisterType((*ChannelProductExceptionList)(nil), "pc.ChannelProductExceptionList")
	proto.RegisterType((*ChannelProductExceptionDetail)(nil), "pc.ChannelProductExceptionDetail")
	proto.RegisterType((*ChannelProductListRes)(nil), "pc.ChannelProductListRes")
	proto.RegisterType((*ChannelProductCountReq)(nil), "pc.ChannelProductCountReq")
	proto.RegisterType((*ChannelProductCountRes)(nil), "pc.ChannelProductCountRes")
	proto.RegisterType((*StoreProductUpDistinctReq)(nil), "pc.StoreProductUpDistinctReq")
	proto.RegisterType((*StoreProductUpDistinctRes)(nil), "pc.StoreProductUpDistinctRes")
	proto.RegisterType((*StoreProductUpDistinctRes_List)(nil), "pc.StoreProductUpDistinctRes.List")
	proto.RegisterType((*ProductCountByPriceReq)(nil), "pc.ProductCountByPriceReq")
	proto.RegisterType((*ProductCountByPriceRes)(nil), "pc.ProductCountByPriceRes")
	proto.RegisterType((*ProductCountByPriceRes_Data)(nil), "pc.ProductCountByPriceRes.Data")
	proto.RegisterType((*ProductPriceStoreReq)(nil), "pc.ProductPriceStoreReq")
	proto.RegisterType((*ProductPriceStoreRes)(nil), "pc.ProductPriceStoreRes")
	proto.RegisterType((*ProductPriceStoreRes_Data)(nil), "pc.ProductPriceStoreRes.Data")
	proto.RegisterType((*MtDelProductReq)(nil), "pc.MtDelProductReq")
	proto.RegisterType((*ChannelProductDelResp)(nil), "pc.ChannelProductDelResp")
	proto.RegisterType((*ElmDelProductReq)(nil), "pc.ElmDelProductReq")
	proto.RegisterType((*ElmDownProductReq)(nil), "pc.ElmDownProductReq")
	proto.RegisterType((*ElmDownProductResp)(nil), "pc.ElmDownProductResp")
}

func init() { proto.RegisterFile("pc/channelProduct.proto", fileDescriptor_9d22520ae0085036) }

var fileDescriptor_9d22520ae0085036 = []byte{
	// 1871 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x58, 0xcd, 0x6e, 0x1b, 0xc9,
	0x11, 0xc6, 0xf0, 0x9f, 0x25, 0x51, 0xd2, 0xb6, 0x2c, 0x89, 0xa2, 0x23, 0x4b, 0x1e, 0x24, 0x0b,
	0x27, 0x01, 0x68, 0xc4, 0x9b, 0x64, 0x8d, 0x20, 0x9b, 0xdd, 0xb5, 0xe8, 0x4d, 0x08, 0xac, 0x37,
	0x0a, 0x69, 0x1b, 0xc8, 0x69, 0xd0, 0x9e, 0x69, 0x93, 0x03, 0x92, 0x33, 0xad, 0xee, 0x1e, 0x4b,
	0x74, 0x1e, 0x20, 0xc7, 0x9c, 0xf2, 0x06, 0x8b, 0xbc, 0x40, 0x80, 0x20, 0x79, 0x85, 0xbc, 0x40,
	0x0e, 0x01, 0x72, 0xc9, 0x3d, 0x0f, 0x90, 0x53, 0x50, 0xdd, 0x3d, 0xd4, 0xcc, 0xf0, 0xc7, 0xab,
	0x85, 0x6f, 0xb9, 0x10, 0xec, 0xea, 0x9a, 0xea, 0xfa, 0xf9, 0xbe, 0xea, 0x9a, 0x81, 0x23, 0xee,
	0x3f, 0xf4, 0xc7, 0x34, 0x8a, 0xd8, 0xf4, 0x42, 0xc4, 0x41, 0xe2, 0xab, 0x2e, 0x17, 0xb1, 0x8a,
	0x49, 0x89, 0xfb, 0x9d, 0x7b, 0xa3, 0x38, 0x1e, 0x4d, 0xd9, 0x43, 0x2d, 0x79, 0x95, 0xbc, 0x7e,
	0x78, 0x25, 0x28, 0xe7, 0x4c, 0x48, 0xa3, 0xd3, 0xf9, 0x4e, 0x71, 0x5f, 0x2a, 0xb1, 0xb0, 0xe0,
	0x7e, 0x09, 0x9d, 0x5f, 0x32, 0x65, 0xad, 0xf6, 0x83, 0x27, 0xf3, 0xe1, 0x24, 0xe9, 0x07, 0x03,
	0x76, 0x99, 0x30, 0xa9, 0xc8, 0x01, 0xd4, 0xe4, 0x24, 0xf1, 0xc2, 0xa0, 0xed, 0x9c, 0x95, 0x1f,
	0x54, 0x07, 0x55, 0x89, 0xbb, 0xe4, 0x08, 0xea, 0xa1, 0xf4, 0x24, 0xa5, 0xb2, 0x5d, 0x3a, 0x73,
	0x1e, 0x54, 0x07, 0xb5, 0x50, 0x0e, 0x29, 0x95, 0xee, 0x1f, 0x1d, 0xb8, 0xbb, 0xd2, 0x9c, 0xe4,
	0x71, 0x24, 0x19, 0xf9, 0x04, 0x2a, 0x01, 0x55, 0x54, 0x5b, 0xdb, 0x7a, 0xf4, 0xfd, 0x2e, 0xf7,
	0xbb, 0x1b, 0xd4, 0xbb, 0x3d, 0xaa, 0xe8, 0xd3, 0x48, 0x89, 0xf9, 0x40, 0x3f, 0xd6, 0xf9, 0x18,
	0x9a, 0x0b, 0x11, 0xd9, 0x83, 0xf2, 0x84, 0xcd, 0xdb, 0x8e, 0x76, 0x00, 0xff, 0x92, 0x3b, 0x50,
	0x7d, 0x43, 0xa7, 0x09, 0xb3, 0x4e, 0x99, 0xc5, 0xcf, 0x4a, 0x8f, 0x1d, 0xf7, 0x14, 0x9a, 0xcf,
	0xa9, 0x9c, 0x0c, 0x92, 0xe8, 0x65, 0x4c, 0xc8, 0xc2, 0x09, 0xd4, 0xd2, 0xff, 0xdd, 0xdf, 0xc2,
	0xbe, 0x55, 0x78, 0x42, 0x25, 0x5b, 0xf8, 0x4b, 0xa0, 0xe2, 0xc7, 0x01, 0x4b, 0x55, 0xf1, 0x3f,
	0x69, 0x43, 0x7d, 0xc6, 0xa4, 0xa4, 0x23, 0x73, 0x4e, 0x73, 0x90, 0x2e, 0xf1, 0x7c, 0x26, 0x44,
	0x2c, 0xda, 0x65, 0x2d, 0x37, 0x0b, 0xf7, 0x9f, 0x65, 0x38, 0x38, 0xcf, 0x15, 0xef, 0xcb, 0x50,
	0xaa, 0x01, 0xbb, 0x24, 0x27, 0x00, 0x9c, 0x8e, 0x98, 0x17, 0x46, 0x01, 0xbb, 0xb6, 0x67, 0x34,
	0x51, 0xd2, 0x47, 0x01, 0xb9, 0x0b, 0x7a, 0xe1, 0xc9, 0xf0, 0x6d, 0x1a, 0x52, 0x03, 0x05, 0xc3,
	0xf0, 0xad, 0x3e, 0xeb, 0x6a, 0xcc, 0x04, 0x4b, 0xcf, 0xd2, 0x0b, 0xb4, 0xa8, 0xff, 0x78, 0x6a,
	0xce, 0x59, 0xbb, 0xa2, 0xb7, 0x9a, 0x5a, 0xf2, 0x7c, 0xce, 0x19, 0x39, 0x85, 0x2d, 0x9f, 0x2a,
	0x36, 0x8a, 0xc5, 0x1c, 0x6b, 0x5a, 0xd5, 0x36, 0x21, 0x15, 0xf5, 0x03, 0x72, 0x0c, 0x8d, 0x57,
	0x82, 0x46, 0x01, 0xee, 0xd6, 0xf4, 0x6e, 0x5d, 0xaf, 0xfb, 0x01, 0xb9, 0x0f, 0xdb, 0xdc, 0xb8,
	0x6f, 0x8c, 0xd7, 0xf5, 0xf6, 0x96, 0x95, 0x69, 0xf3, 0x27, 0x00, 0x16, 0xa5, 0xf8, 0x3c, 0x98,
	0x78, 0xac, 0xa4, 0x1f, 0x10, 0x17, 0x5a, 0x09, 0xf7, 0x82, 0xf8, 0x2a, 0xf2, 0xa4, 0xa2, 0x8a,
	0xb5, 0xb7, 0x8c, 0x89, 0x84, 0xf7, 0xe2, 0xab, 0x68, 0x88, 0x22, 0x3c, 0xe5, 0x75, 0x18, 0xd1,
	0xc8, 0x67, 0x9e, 0x4e, 0x7c, 0x4b, 0x87, 0xb0, 0x65, 0x65, 0xe7, 0x98, 0xff, 0x63, 0x68, 0x84,
	0xd2, 0x0b, 0x44, 0x32, 0x92, 0xed, 0x1d, 0xe3, 0x63, 0x28, 0x7b, 0xb8, 0xc4, 0xa7, 0x43, 0xe9,
	0x09, 0xe6, 0xc7, 0xb3, 0x19, 0x8b, 0x82, 0xf6, 0xae, 0x39, 0x20, 0x94, 0x83, 0x54, 0x44, 0x3e,
	0x84, 0xdd, 0x59, 0x18, 0x79, 0xb1, 0x08, 0x98, 0xf0, 0xfc, 0x38, 0x89, 0x54, 0x7b, 0x4f, 0x6b,
	0xb5, 0x66, 0x61, 0xf4, 0x6b, 0x94, 0x9e, 0xa3, 0x50, 0xeb, 0xd1, 0xeb, 0x9c, 0xde, 0x07, 0x56,
	0x8f, 0x5e, 0xdf, 0xe8, 0xb9, 0x7f, 0xa9, 0x03, 0x59, 0xae, 0x2e, 0xd9, 0x81, 0x92, 0x26, 0x0d,
	0x3e, 0x51, 0x0a, 0x03, 0x04, 0x52, 0x44, 0x67, 0x29, 0x62, 0xf4, 0x7f, 0xac, 0x2f, 0x66, 0xd2,
	0x53, 0xec, 0x5a, 0xd9, 0x32, 0x36, 0x50, 0xf0, 0x9c, 0x5d, 0xab, 0xa5, 0x74, 0x57, 0x96, 0xd3,
	0xbd, 0x07, 0x65, 0x1e, 0xfa, 0xba, 0x8a, 0xcd, 0x01, 0xfe, 0x2d, 0x14, 0xa0, 0x56, 0x2c, 0xc0,
	0x01, 0xd4, 0x42, 0xe9, 0x25, 0x32, 0x2d, 0x5e, 0x35, 0x94, 0x2f, 0x24, 0x23, 0x5d, 0xd8, 0x4f,
	0x9f, 0xca, 0xa2, 0xa3, 0xa1, 0x75, 0x3e, 0xb0, 0x5b, 0xe7, 0x37, 0x20, 0x39, 0x85, 0xad, 0x84,
	0x07, 0x54, 0x31, 0x0f, 0x7f, 0xda, 0x4d, 0x7d, 0x3e, 0x18, 0x51, 0x0f, 0x8b, 0xd8, 0x85, 0x8a,
	0x9c, 0x24, 0xb2, 0x0d, 0x9a, 0xe5, 0x1d, 0x64, 0xf9, 0x72, 0x8a, 0xba, 0xc3, 0x49, 0x32, 0xd0,
	0x7a, 0xe4, 0x11, 0x1c, 0x2c, 0x39, 0xa0, 0xb3, 0xb5, 0xa5, 0x4d, 0xef, 0x17, 0x5c, 0xf8, 0x0a,
	0x93, 0x57, 0x2c, 0xf5, 0xf6, 0x37, 0x2a, 0x75, 0x6b, 0x55, 0xa9, 0x37, 0x00, 0xea, 0x01, 0xec,
	0x2d, 0xaa, 0x30, 0x0e, 0x85, 0xe6, 0xc5, 0xae, 0x76, 0x6a, 0x27, 0xad, 0x04, 0x8a, 0xfb, 0x01,
	0xa6, 0x5e, 0xce, 0x23, 0xdf, 0x33, 0x0d, 0x60, 0xcf, 0x30, 0x0f, 0x25, 0x4f, 0x51, 0xd0, 0xf9,
	0x43, 0x19, 0xca, 0xc3, 0x49, 0x92, 0x6b, 0xa8, 0xce, 0x4d, 0x43, 0xdd, 0x87, 0x2a, 0x7d, 0x8c,
	0x52, 0x8b, 0x0f, 0xfa, 0xb8, 0x1f, 0x20, 0x3e, 0xde, 0x86, 0xd3, 0x38, 0x1a, 0xe1, 0x86, 0xc5,
	0x87, 0x11, 0xf4, 0x03, 0xf2, 0x18, 0x6a, 0x52, 0xc5, 0xfe, 0x44, 0xb6, 0x2b, 0x3a, 0xcb, 0x67,
	0xeb, 0xb3, 0xdc, 0x1d, 0xa2, 0xe2, 0xc0, 0xea, 0x63, 0xe7, 0xe0, 0x22, 0xf4, 0x99, 0xa5, 0xbf,
	0x59, 0xe0, 0x61, 0x6f, 0x42, 0xee, 0x99, 0x1d, 0x43, 0xcc, 0xc6, 0x9b, 0x90, 0x5f, 0xe8, 0x4d,
	0x0c, 0x8e, 0x33, 0xdf, 0x33, 0xdd, 0xb5, 0x66, 0x83, 0xe3, 0xcc, 0x7f, 0x89, 0x02, 0xdd, 0x35,
	0xa8, 0x30, 0x84, 0xad, 0x9b, 0x96, 0xf8, 0x8a, 0x0a, 0x4d, 0xd6, 0x0f, 0x61, 0xf7, 0x8a, 0x85,
	0xa3, 0xb1, 0xf2, 0x5e, 0xc7, 0xc2, 0x4b, 0xa2, 0x50, 0x69, 0x5c, 0x39, 0x83, 0x96, 0x11, 0x7f,
	0x11, 0x8b, 0x17, 0x51, 0xa8, 0x48, 0x1b, 0x1a, 0x3a, 0x01, 0x1e, 0x57, 0x16, 0x50, 0x35, 0xcc,
	0xc1, 0x85, 0x22, 0x67, 0xb0, 0xbd, 0xc8, 0x02, 0xee, 0x82, 0x81, 0x5b, 0x9a, 0x88, 0x0b, 0xd5,
	0xf9, 0x31, 0x54, 0x75, 0x84, 0x48, 0x88, 0x4b, 0xb5, 0xb8, 0x11, 0x2e, 0xd5, 0x1c, 0x3d, 0x9b,
	0xc6, 0xfe, 0xc4, 0x43, 0xb1, 0xe9, 0xa0, 0x75, 0x5c, 0xff, 0x46, 0xcd, 0xdd, 0xaf, 0x1d, 0xb8,
	0x97, 0xcf, 0xd7, 0xd3, 0x6b, 0x9f, 0x71, 0x15, 0xc6, 0x91, 0xe9, 0xcf, 0xf2, 0x96, 0xdd, 0xff,
	0x23, 0x7b, 0xad, 0x94, 0x75, 0x3d, 0x4e, 0x97, 0xeb, 0x91, 0xb7, 0xaf, 0x95, 0x91, 0x4b, 0x2a,
	0x56, 0x74, 0x6a, 0xf1, 0x69, 0x58, 0x0e, 0x5a, 0x64, 0xfa, 0xcb, 0x9f, 0x1d, 0xb8, 0xbb, 0xc1,
	0x0c, 0xa6, 0xc7, 0x20, 0x33, 0x03, 0xab, 0xe6, 0x00, 0xb4, 0x4c, 0x5f, 0xa6, 0xe4, 0x27, 0xd6,
	0xaf, 0x92, 0xf6, 0xeb, 0xfe, 0x06, 0xbf, 0x7a, 0x4c, 0xd1, 0x70, 0x6a, 0x3d, 0xfb, 0x2e, 0xec,
	0x64, 0x0c, 0x2b, 0x36, 0xb3, 0x10, 0xdc, 0x5e, 0x98, 0x56, 0x6c, 0x86, 0x78, 0x66, 0x82, 0xe3,
	0xc1, 0xc6, 0xf5, 0x2a, 0x13, 0xbc, 0x1f, 0xb8, 0x7f, 0x2d, 0xc3, 0xc9, 0xc6, 0x43, 0xfe, 0x9f,
	0x1a, 0x24, 0x2c, 0x35, 0xc8, 0x05, 0xdd, 0xb7, 0xd6, 0xd1, 0x7d, 0xbb, 0x40, 0xf7, 0x2c, 0x3f,
	0x5a, 0x1b, 0xf9, 0xb1, 0x53, 0xe4, 0x47, 0xa6, 0xe7, 0xec, 0x66, 0x7b, 0xce, 0x4d, 0xe9, 0xf6,
	0xd2, 0x71, 0x05, 0x4b, 0xf7, 0x37, 0x67, 0xf5, 0xb8, 0x72, 0x5b, 0x3a, 0xfc, 0x20, 0x47, 0x87,
	0xc3, 0xd5, 0xed, 0xe9, 0x1b, 0xb2, 0x80, 0x7c, 0x0f, 0x76, 0xae, 0xa8, 0x60, 0xe3, 0x38, 0x91,
	0x76, 0xb6, 0xa9, 0x9e, 0x95, 0x1f, 0x34, 0x07, 0xad, 0x85, 0x14, 0x0b, 0xee, 0xfe, 0x0e, 0x0e,
	0xf3, 0x67, 0xe8, 0xa7, 0xed, 0xa8, 0x95, 0x29, 0xbc, 0x53, 0x2c, 0x7c, 0x71, 0xec, 0x28, 0x2d,
	0x8f, 0x1d, 0x85, 0xd9, 0xa9, 0x5c, 0x9c, 0x9d, 0xdc, 0xbf, 0x3b, 0x6b, 0x4e, 0x97, 0xb7, 0x1f,
	0x23, 0x75, 0xe8, 0xf6, 0x0c, 0xb3, 0x40, 0xc2, 0x24, 0xdc, 0xa6, 0xa6, 0x94, 0x70, 0x3d, 0xc5,
	0xc6, 0x57, 0x91, 0xed, 0xe2, 0xfa, 0x3f, 0x42, 0x68, 0x4c, 0xa5, 0xa7, 0x1b, 0xbd, 0x45, 0x77,
	0x63, 0x4c, 0xa5, 0xe9, 0x8e, 0x3f, 0x04, 0xb2, 0xd8, 0xf4, 0x70, 0xc6, 0xd3, 0x8f, 0x1b, 0xa0,
	0xef, 0xa6, 0x5a, 0x9f, 0x47, 0x01, 0x0e, 0x63, 0xee, 0xbf, 0x1d, 0x38, 0x1e, 0xaa, 0x58, 0x30,
	0x1b, 0xca, 0x0b, 0xde, 0x0b, 0xa5, 0x0a, 0x23, 0xff, 0xdb, 0x65, 0xb3, 0x5c, 0xcc, 0x26, 0x8e,
	0xbe, 0x96, 0xbd, 0x3a, 0x99, 0x65, 0x3d, 0xfa, 0xa6, 0xef, 0x01, 0x19, 0xc8, 0x56, 0xb2, 0xef,
	0x1d, 0x69, 0x93, 0xa8, 0x66, 0x9a, 0x44, 0x7e, 0x88, 0xae, 0x6d, 0x1c, 0xa2, 0xeb, 0xf9, 0x21,
	0xda, 0xfd, 0xcf, 0x86, 0x28, 0x6f, 0x5b, 0xb5, 0x9f, 0xe6, 0xf0, 0xee, 0x22, 0xde, 0xd7, 0x9a,
	0xee, 0x66, 0xb0, 0x7f, 0x0c, 0x58, 0x22, 0x6f, 0x16, 0x0b, 0xd3, 0xc3, 0x1a, 0x83, 0xfa, 0x98,
	0xca, 0x67, 0xb1, 0x60, 0x9d, 0x0b, 0xa8, 0xe8, 0x1e, 0x9f, 0x4f, 0x56, 0xc9, 0x86, 0xb8, 0x22,
	0x59, 0xe5, 0x2c, 0xbf, 0xd3, 0x64, 0x55, 0x6e, 0x92, 0xe5, 0x5e, 0xc2, 0x61, 0x16, 0x9b, 0x4f,
	0xe6, 0xfa, 0x7e, 0x7f, 0x3f, 0x25, 0xcd, 0xba, 0x71, 0x53, 0x33, 0xf7, 0xeb, 0xd2, 0x9a, 0x33,
	0xdf, 0xc3, 0xfd, 0xba, 0xda, 0xae, 0x7e, 0x6d, 0xb4, 0x6f, 0x8c, 0x7f, 0x72, 0xa0, 0x82, 0xcb,
	0x42, 0x0e, 0x9d, 0xf5, 0x39, 0x2c, 0xad, 0xca, 0x61, 0x39, 0x03, 0xb8, 0xfb, 0xb0, 0x3d, 0xa3,
	0x62, 0xc2, 0x94, 0x1d, 0x96, 0xec, 0xc5, 0x63, 0x64, 0x66, 0x5e, 0xba, 0x03, 0x55, 0xd3, 0xc9,
	0xec, 0x88, 0xa5, 0x17, 0x08, 0x45, 0x39, 0x8e, 0xb9, 0x19, 0x6d, 0xcd, 0x10, 0xd5, 0x40, 0x01,
	0xce, 0xb3, 0xee, 0x7f, 0x1d, 0xb8, 0x63, 0xc3, 0xd1, 0x36, 0x34, 0x76, 0xde, 0x7f, 0x61, 0x32,
	0xb1, 0x2d, 0xe6, 0xc0, 0x4a, 0x76, 0x0e, 0x3c, 0x85, 0x2d, 0xc9, 0xa8, 0xf0, 0xc7, 0x69, 0x9b,
	0xd5, 0x6d, 0xce, 0x88, 0xf4, 0xa5, 0x7a, 0x08, 0x35, 0xb3, 0xb2, 0x21, 0xd8, 0x55, 0x81, 0x87,
	0xf5, 0x8d, 0x3c, 0x6c, 0x14, 0x78, 0xf8, 0x8f, 0xd5, 0xc1, 0xdf, 0x16, 0x21, 0x3f, 0xca, 0x21,
	0xe4, 0x24, 0x83, 0x90, 0x9c, 0xd5, 0x0c, 0x3e, 0xde, 0x79, 0xf3, 0x74, 0x3e, 0xb1, 0xf8, 0x29,
	0xe6, 0xd9, 0x59, 0xbe, 0x21, 0x56, 0x8c, 0x30, 0xee, 0x0b, 0xd8, 0x7d, 0xa6, 0x7a, 0x8b, 0x1b,
	0x01, 0x0b, 0x7a, 0x06, 0xdb, 0x94, 0x73, 0x8f, 0xc7, 0x61, 0xd6, 0x12, 0x50, 0xce, 0x2f, 0xe2,
	0x50, 0x1b, 0xb2, 0x1a, 0x92, 0x27, 0xd9, 0x9a, 0xa2, 0xc6, 0x90, 0x27, 0xa8, 0xe1, 0xf6, 0x8a,
	0x77, 0x74, 0x8f, 0x4d, 0x07, 0x4c, 0xf2, 0x95, 0x09, 0x3b, 0x82, 0x3a, 0x13, 0xc2, 0x9b, 0xc9,
	0x91, 0x75, 0xad, 0xc6, 0x84, 0x78, 0x26, 0x47, 0xee, 0x13, 0xd8, 0x7b, 0x3a, 0x9d, 0xe5, 0xbd,
	0x3b, 0x82, 0xba, 0x06, 0xe9, 0x62, 0x94, 0xac, 0xe1, 0x72, 0x89, 0x21, 0xcd, 0x94, 0xde, 0xbf,
	0x77, 0xe0, 0x03, 0x34, 0x12, 0x5f, 0x45, 0xdf, 0xde, 0x0a, 0x96, 0x01, 0x23, 0xb6, 0xb0, 0x4e,
	0x2f, 0x57, 0xca, 0xb9, 0x8d, 0x12, 0xd3, 0x1f, 0x73, 0x26, 0x70, 0xa6, 0xca, 0x4e, 0x7b, 0x56,
	0xa6, 0x2f, 0xff, 0xcf, 0x81, 0x14, 0x1d, 0xb9, 0x65, 0x42, 0x1e, 0xfd, 0xab, 0x0a, 0x7b, 0x3d,
	0x3f, 0x9f, 0x59, 0xf2, 0x12, 0xf6, 0x57, 0x7c, 0xa3, 0x22, 0xf7, 0xd6, 0x7e, 0xbc, 0xd2, 0x9f,
	0xce, 0x3a, 0xa7, 0xef, 0xf8, 0xb8, 0x45, 0x7e, 0x6e, 0xbb, 0xfb, 0xf1, 0x9a, 0xd1, 0x88, 0x5d,
	0x76, 0xd6, 0x6e, 0x49, 0xf2, 0x29, 0x54, 0xcd, 0x68, 0xb4, 0xe2, 0xf5, 0x3a, 0x9d, 0x7a, 0x3a,
	0xeb, 0xf7, 0x24, 0xf9, 0x0a, 0x5a, 0xf9, 0x37, 0x89, 0x0d, 0x7e, 0xb8, 0xef, 0x7a, 0x99, 0x61,
	0x92, 0xbc, 0x84, 0xc3, 0xd5, 0xf7, 0x1d, 0x39, 0xd9, 0x74, 0x17, 0x5e, 0x76, 0x36, 0x6e, 0x4b,
	0xf2, 0x05, 0x6c, 0x67, 0xfb, 0xbb, 0x89, 0x77, 0xf5, 0x25, 0xd6, 0x59, 0xbf, 0x27, 0xc9, 0x67,
	0x00, 0x37, 0x5d, 0x80, 0xb4, 0xd7, 0x34, 0x87, 0xcb, 0xce, 0xba, 0x1d, 0x49, 0x7e, 0x01, 0xdb,
	0x59, 0x2e, 0x93, 0x7d, 0xd4, 0x2c, 0xb0, 0x7b, 0x55, 0xc9, 0x52, 0x6e, 0x7e, 0x06, 0xad, 0x1c,
	0xdd, 0xc8, 0x1d, 0xd4, 0x2d, 0x32, 0x70, 0x93, 0x85, 0x4f, 0x61, 0x27, 0x0f, 0x71, 0x72, 0x90,
	0x9a, 0xc8, 0xf1, 0xaf, 0x73, 0xb8, 0x4a, 0x2c, 0xf9, 0xa3, 0x5f, 0x41, 0xab, 0xe7, 0x3f, 0xa7,
	0x72, 0x92, 0x3e, 0xff, 0x31, 0xb4, 0xce, 0xc7, 0x71, 0x2c, 0x99, 0xfd, 0xfa, 0x49, 0x5a, 0xf8,
	0xe4, 0xe2, 0x5b, 0x69, 0xe7, 0x28, 0xb3, 0xcc, 0x7e, 0x19, 0x7d, 0x55, 0xd3, 0x9f, 0x8f, 0x3f,
	0xfa, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x18, 0xce, 0xbe, 0x8e, 0x9b, 0x16, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DcChannelProductClient is the client API for DcChannelProduct service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DcChannelProductClient interface {
	//根据sku_id查找product_id（批量）
	GetProductIdBySkuId(ctx context.Context, in *GetProductIdBySkuIdRequest, opts ...grpc.CallOption) (*GetProductIdBySkuIdResponse, error)
	// 渠道商品库列表
	List(ctx context.Context, in *ChannelProductListReq, opts ...grpc.CallOption) (*ChannelProductListRes, error)
	// 渠道商品库列表商品统计
	Count(ctx context.Context, in *ChannelProductCountReq, opts ...grpc.CallOption) (*ChannelProductCountRes, error)
	// 渠道商品库列表
	ExceptionList(ctx context.Context, in *ChannelProductListReq, opts ...grpc.CallOption) (*ChannelProductExceptionListRes, error)
	// 去重的上架商品列表
	StoreProductUpDistinct(ctx context.Context, in *StoreProductUpDistinctReq, opts ...grpc.CallOption) (*StoreProductUpDistinctRes, error)
	// 营销活动商品按价格统计
	CountByPrice(ctx context.Context, in *ProductCountByPriceReq, opts ...grpc.CallOption) (*ProductCountByPriceRes, error)
	// 价格关联门店搜索
	PriceStore(ctx context.Context, in *ProductPriceStoreReq, opts ...grpc.CallOption) (*ProductPriceStoreRes, error)
	// 美团商户端删除商品
	MtDelProduct(ctx context.Context, in *MtDelProductReq, opts ...grpc.CallOption) (*ChannelProductDelResp, error)
	// 饿了么商户端删除商品
	ElmDelProduct(ctx context.Context, in *ElmDelProductReq, opts ...grpc.CallOption) (*ChannelProductDelResp, error)
	// 饿了么商户端下架商品
	ElmDownProduct(ctx context.Context, in *ElmDownProductReq, opts ...grpc.CallOption) (*ElmDownProductResp, error)
}

type dcChannelProductClient struct {
	cc *grpc.ClientConn
}

func NewDcChannelProductClient(cc *grpc.ClientConn) DcChannelProductClient {
	return &dcChannelProductClient{cc}
}

func (c *dcChannelProductClient) GetProductIdBySkuId(ctx context.Context, in *GetProductIdBySkuIdRequest, opts ...grpc.CallOption) (*GetProductIdBySkuIdResponse, error) {
	out := new(GetProductIdBySkuIdResponse)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/GetProductIdBySkuId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dcChannelProductClient) List(ctx context.Context, in *ChannelProductListReq, opts ...grpc.CallOption) (*ChannelProductListRes, error) {
	out := new(ChannelProductListRes)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/List", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dcChannelProductClient) Count(ctx context.Context, in *ChannelProductCountReq, opts ...grpc.CallOption) (*ChannelProductCountRes, error) {
	out := new(ChannelProductCountRes)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/Count", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dcChannelProductClient) ExceptionList(ctx context.Context, in *ChannelProductListReq, opts ...grpc.CallOption) (*ChannelProductExceptionListRes, error) {
	out := new(ChannelProductExceptionListRes)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/ExceptionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dcChannelProductClient) StoreProductUpDistinct(ctx context.Context, in *StoreProductUpDistinctReq, opts ...grpc.CallOption) (*StoreProductUpDistinctRes, error) {
	out := new(StoreProductUpDistinctRes)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/StoreProductUpDistinct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dcChannelProductClient) CountByPrice(ctx context.Context, in *ProductCountByPriceReq, opts ...grpc.CallOption) (*ProductCountByPriceRes, error) {
	out := new(ProductCountByPriceRes)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/CountByPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dcChannelProductClient) PriceStore(ctx context.Context, in *ProductPriceStoreReq, opts ...grpc.CallOption) (*ProductPriceStoreRes, error) {
	out := new(ProductPriceStoreRes)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/PriceStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dcChannelProductClient) MtDelProduct(ctx context.Context, in *MtDelProductReq, opts ...grpc.CallOption) (*ChannelProductDelResp, error) {
	out := new(ChannelProductDelResp)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/MtDelProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dcChannelProductClient) ElmDelProduct(ctx context.Context, in *ElmDelProductReq, opts ...grpc.CallOption) (*ChannelProductDelResp, error) {
	out := new(ChannelProductDelResp)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/ElmDelProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dcChannelProductClient) ElmDownProduct(ctx context.Context, in *ElmDownProductReq, opts ...grpc.CallOption) (*ElmDownProductResp, error) {
	out := new(ElmDownProductResp)
	err := c.cc.Invoke(ctx, "/pc.DcChannelProduct/ElmDownProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DcChannelProductServer is the server API for DcChannelProduct service.
type DcChannelProductServer interface {
	//根据sku_id查找product_id（批量）
	GetProductIdBySkuId(context.Context, *GetProductIdBySkuIdRequest) (*GetProductIdBySkuIdResponse, error)
	// 渠道商品库列表
	List(context.Context, *ChannelProductListReq) (*ChannelProductListRes, error)
	// 渠道商品库列表商品统计
	Count(context.Context, *ChannelProductCountReq) (*ChannelProductCountRes, error)
	// 渠道商品库列表
	ExceptionList(context.Context, *ChannelProductListReq) (*ChannelProductExceptionListRes, error)
	// 去重的上架商品列表
	StoreProductUpDistinct(context.Context, *StoreProductUpDistinctReq) (*StoreProductUpDistinctRes, error)
	// 营销活动商品按价格统计
	CountByPrice(context.Context, *ProductCountByPriceReq) (*ProductCountByPriceRes, error)
	// 价格关联门店搜索
	PriceStore(context.Context, *ProductPriceStoreReq) (*ProductPriceStoreRes, error)
	// 美团商户端删除商品
	MtDelProduct(context.Context, *MtDelProductReq) (*ChannelProductDelResp, error)
	// 饿了么商户端删除商品
	ElmDelProduct(context.Context, *ElmDelProductReq) (*ChannelProductDelResp, error)
	// 饿了么商户端下架商品
	ElmDownProduct(context.Context, *ElmDownProductReq) (*ElmDownProductResp, error)
}

// UnimplementedDcChannelProductServer can be embedded to have forward compatible implementations.
type UnimplementedDcChannelProductServer struct {
}

func (*UnimplementedDcChannelProductServer) GetProductIdBySkuId(ctx context.Context, req *GetProductIdBySkuIdRequest) (*GetProductIdBySkuIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProductIdBySkuId not implemented")
}
func (*UnimplementedDcChannelProductServer) List(ctx context.Context, req *ChannelProductListReq) (*ChannelProductListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (*UnimplementedDcChannelProductServer) Count(ctx context.Context, req *ChannelProductCountReq) (*ChannelProductCountRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Count not implemented")
}
func (*UnimplementedDcChannelProductServer) ExceptionList(ctx context.Context, req *ChannelProductListReq) (*ChannelProductExceptionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExceptionList not implemented")
}
func (*UnimplementedDcChannelProductServer) StoreProductUpDistinct(ctx context.Context, req *StoreProductUpDistinctReq) (*StoreProductUpDistinctRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreProductUpDistinct not implemented")
}
func (*UnimplementedDcChannelProductServer) CountByPrice(ctx context.Context, req *ProductCountByPriceReq) (*ProductCountByPriceRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountByPrice not implemented")
}
func (*UnimplementedDcChannelProductServer) PriceStore(ctx context.Context, req *ProductPriceStoreReq) (*ProductPriceStoreRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PriceStore not implemented")
}
func (*UnimplementedDcChannelProductServer) MtDelProduct(ctx context.Context, req *MtDelProductReq) (*ChannelProductDelResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MtDelProduct not implemented")
}
func (*UnimplementedDcChannelProductServer) ElmDelProduct(ctx context.Context, req *ElmDelProductReq) (*ChannelProductDelResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ElmDelProduct not implemented")
}
func (*UnimplementedDcChannelProductServer) ElmDownProduct(ctx context.Context, req *ElmDownProductReq) (*ElmDownProductResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ElmDownProduct not implemented")
}

func RegisterDcChannelProductServer(s *grpc.Server, srv DcChannelProductServer) {
	s.RegisterService(&_DcChannelProduct_serviceDesc, srv)
}

func _DcChannelProduct_GetProductIdBySkuId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProductIdBySkuIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).GetProductIdBySkuId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/GetProductIdBySkuId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).GetProductIdBySkuId(ctx, req.(*GetProductIdBySkuIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DcChannelProduct_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelProductListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/List",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).List(ctx, req.(*ChannelProductListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DcChannelProduct_Count_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelProductCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).Count(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/Count",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).Count(ctx, req.(*ChannelProductCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DcChannelProduct_ExceptionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelProductListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).ExceptionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/ExceptionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).ExceptionList(ctx, req.(*ChannelProductListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DcChannelProduct_StoreProductUpDistinct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreProductUpDistinctReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).StoreProductUpDistinct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/StoreProductUpDistinct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).StoreProductUpDistinct(ctx, req.(*StoreProductUpDistinctReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DcChannelProduct_CountByPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductCountByPriceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).CountByPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/CountByPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).CountByPrice(ctx, req.(*ProductCountByPriceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DcChannelProduct_PriceStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductPriceStoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).PriceStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/PriceStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).PriceStore(ctx, req.(*ProductPriceStoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DcChannelProduct_MtDelProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MtDelProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).MtDelProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/MtDelProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).MtDelProduct(ctx, req.(*MtDelProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DcChannelProduct_ElmDelProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ElmDelProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).ElmDelProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/ElmDelProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).ElmDelProduct(ctx, req.(*ElmDelProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DcChannelProduct_ElmDownProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ElmDownProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcChannelProductServer).ElmDownProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcChannelProduct/ElmDownProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcChannelProductServer).ElmDownProduct(ctx, req.(*ElmDownProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _DcChannelProduct_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pc.DcChannelProduct",
	HandlerType: (*DcChannelProductServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProductIdBySkuId",
			Handler:    _DcChannelProduct_GetProductIdBySkuId_Handler,
		},
		{
			MethodName: "List",
			Handler:    _DcChannelProduct_List_Handler,
		},
		{
			MethodName: "Count",
			Handler:    _DcChannelProduct_Count_Handler,
		},
		{
			MethodName: "ExceptionList",
			Handler:    _DcChannelProduct_ExceptionList_Handler,
		},
		{
			MethodName: "StoreProductUpDistinct",
			Handler:    _DcChannelProduct_StoreProductUpDistinct_Handler,
		},
		{
			MethodName: "CountByPrice",
			Handler:    _DcChannelProduct_CountByPrice_Handler,
		},
		{
			MethodName: "PriceStore",
			Handler:    _DcChannelProduct_PriceStore_Handler,
		},
		{
			MethodName: "MtDelProduct",
			Handler:    _DcChannelProduct_MtDelProduct_Handler,
		},
		{
			MethodName: "ElmDelProduct",
			Handler:    _DcChannelProduct_ElmDelProduct_Handler,
		},
		{
			MethodName: "ElmDownProduct",
			Handler:    _DcChannelProduct_ElmDownProduct_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pc/channelProduct.proto",
}

// DcTaskProductClient is the client API for DcTaskProduct service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DcTaskProductClient interface {
	ChooseTaskRun(ctx context.Context, in *TaskRunVo, opts ...grpc.CallOption) (*TaskRunBaseResponse, error)
}

type dcTaskProductClient struct {
	cc *grpc.ClientConn
}

func NewDcTaskProductClient(cc *grpc.ClientConn) DcTaskProductClient {
	return &dcTaskProductClient{cc}
}

func (c *dcTaskProductClient) ChooseTaskRun(ctx context.Context, in *TaskRunVo, opts ...grpc.CallOption) (*TaskRunBaseResponse, error) {
	out := new(TaskRunBaseResponse)
	err := c.cc.Invoke(ctx, "/pc.DcTaskProduct/ChooseTaskRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DcTaskProductServer is the server API for DcTaskProduct service.
type DcTaskProductServer interface {
	ChooseTaskRun(context.Context, *TaskRunVo) (*TaskRunBaseResponse, error)
}

// UnimplementedDcTaskProductServer can be embedded to have forward compatible implementations.
type UnimplementedDcTaskProductServer struct {
}

func (*UnimplementedDcTaskProductServer) ChooseTaskRun(ctx context.Context, req *TaskRunVo) (*TaskRunBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChooseTaskRun not implemented")
}

func RegisterDcTaskProductServer(s *grpc.Server, srv DcTaskProductServer) {
	s.RegisterService(&_DcTaskProduct_serviceDesc, srv)
}

func _DcTaskProduct_ChooseTaskRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRunVo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DcTaskProductServer).ChooseTaskRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pc.DcTaskProduct/ChooseTaskRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DcTaskProductServer).ChooseTaskRun(ctx, req.(*TaskRunVo))
	}
	return interceptor(ctx, in, info, handler)
}

var _DcTaskProduct_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pc.DcTaskProduct",
	HandlerType: (*DcTaskProductServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChooseTaskRun",
			Handler:    _DcTaskProduct_ChooseTaskRun_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pc/channelProduct.proto",
}
