package pc

import (
	"context"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"time"
)

type Client struct {
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
	RPC  DcProductClient
}

func GetDcProductClient(c ...echo.Context) *Client {
	var client Client
	var err error
	url := config.GetString("grpc.product-center")
	//url = "10.1.1.248:11003"
	if url == "" {
		url = "127.0.0.1:11003"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure(), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(950000000))); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = NewDcProductClient(client.Conn)
		//client.Ctx = AppendToOutgoingContextLoginUserInfo(context.Background(), c...)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Minute*30)
		return &client
	}
}

//关闭链接
func (d *Client) Close() {
	d.Conn.Close()
	d.Cf()
}
