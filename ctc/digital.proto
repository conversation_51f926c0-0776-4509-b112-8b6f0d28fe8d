syntax = "proto3";

package ctc;

option go_package ="./ctc";

// 数字藏品服务
service DigitalService {
  //用户实名注册
  rpc RealRegister(RealRegisterRequest) returns(RealRegisterResponse);
  //素材上传致信链
  rpc DigitalImageUpload(DigitalImageUploadRequest) returns(DigitalImageUploadResponse);
  //发行数字藏品
  rpc NftPublishDigital(NftPublishDigitalRequest) returns(NftPublishDigitalResponse);
  //发行数字藏品系列
  rpc NftSeriesClaim(NftSeriesClaimRequest) returns(NftSeriesClaimResponse);
  //查询数字藏品列表(系列查询)
  rpc NftSeriesList(NftSeriesListRequest) returns(NftSeriesListResponse);
  //查询数字藏品详细信息
  rpc NftInfo(NftInfoRequest) returns(NftInfoResponse);
  //查询各类结果接口
  rpc NftSearchResult(NftSearchResultRequest) returns(NftSearchResultResponse);
  //nft积分查询接口
  rpc NftPointQuery(NftPointQueryRequest) returns(NftPointQueryResponse);
  //nft购买
  rpc NftBuy(NftBuyRequest) returns(NftBuyResponse);
  //nft销售状态变更
  rpc NftStatus(NftStatusRequest) returns(NftStatusResponse);
  //新增数字藏品到瑞鹏平台数据
  rpc NftCreate(NftCreateRequest) returns(NftResponse);
  //更新瑞鹏平台数据藏品
  rpc NftUpdate(NftUpdateRequest) returns(NftResponse);
  //删除瑞鹏平台数据藏品
  rpc NftDelete(NftDeleteRequest) returns(NftResponse);
  //展示瑞鹏平台数据藏品
  rpc NftRuiPengList(NftRuiPengListRequest) returns(NftRuiPengListResponse);
  //瑞鹏平台数据藏品详情数据
  rpc NftRuiPengDetail(NftRuiPengDetailRequest) returns(NftRuiPengDetailResponse);
  //判断用户是否获取openid
  rpc UserOpenid(UserOpenidRequest) returns(UserOpenidResponse);
  //获取openid
  rpc UserAuthOpenid(UserAuthOpenidRequest) returns(UserAuthOpenidResponse);
  //用户购买或免费领取数字藏品
  rpc UserBuyNft(UserBuyNftRequest) returns(UserBuyNftResponse);
  //数字藏品订单支付
  rpc UserNftPay(UserNftPayRequest) returns(UserNftPayResponse);
  //支付结果领取nft
  rpc NftPayResultPick(NftPayResultPickRequest) returns(NftResponse);
}

// 用户实名注册入参
message RealRegisterRequest {
  string person_name = 1;
  string mobile = 2;
  string id_card = 3;
  string openid = 4;
  string scrm_user_id = 5;
}

// 用户实名注册返回值
message RealRegisterResponse {
  int32 code = 1;
  string message = 2;
  string user_identification = 3;
}

// 素材上传参数
message DigitalImageUploadRequest {
  string file_path = 1;
  string user_identification = 2;
}

// 素材上传返回值
message DigitalImageUploadResponse {
  int32 code = 1;
  string message = 2;
  string image_url = 3;
}

// 发行数字藏品参数
message NftPublishDigitalRequest {
  string author = 1;
  string name = 2;
  string url = 3;
  string display_url = 4;
  string desc = 5;
  string flag = 6;
  string user_identification = 7;
  int32 publish_count = 8;
  string series_id = 9;
  int32 series_begin_index = 10;
  int32 sell_status = 11;
  int32 sell_count = 12;
  string package_type = 13;
}

//发行数字藏品返回值
message NftPublishDigitalResponse {
  int32 code = 1;
  string message = 2;
  string task_id = 3;
}

//声明系列入参
message NftSeriesClaimRequest {
  string series_name = 1;
  int32 total_count = 2;
  string cover_url = 3;
  string desc = 4;
  string user_identification = 5;
}

//声明系列返回值
message NftSeriesClaimResponse {
  int32 code = 1;
  string message = 2;
  string task_id = 3;
}

//nft数字藏品列表藏品入参
message NftSeriesListRequest {
  int64 offset = 1;
  int64 limit = 2;
  string series_id = 3;
  string user_identification = 4;
}

message NftInfo {
  string nft_id = 1;
  string flag = 2;
  string author = 3;
  string name = 4;
  string url = 5;
  string display_url = 6;
  string desc = 7;
  string series_name = 8;
  string series_id = 9;
  int32 sell_status = 10;
  int32 series_total_num = 11;
  int32 series_index_id = 12;
  int32 sell_count = 13;
}

//nft数字藏品列表返回值
message NftSeriesListResponse {
  int32 code = 1;
  string message = 2;
  int32 total = 3;
  repeated NftInfo nft_info = 4;
}

//查询各类结果入参
message NftSearchResultRequest {
  string task_id = 1;
  string method = 2;
}

//查询各类结果返回值
message NftSearchResultResponse {
  int32 code = 1;
  string message = 2;
  string data = 3;
}

//积分查询入参
message NftPointQueryRequest {
  string user_identification = 1;
}

//积分查询返回值
message NftPointQueryResponse {
  int32 code = 1;
  string message = 2;
  int32 count = 3;
}

//数字藏品详细信息参数
message NftInfoRequest {
  string nft_id = 1;
}

//数字藏品详细信息参数返回值
message NftInfoResponse {
  int32 code = 1;
  string message = 2;
  NftInfo nft_info = 3;
}

//nft购买参数
message NftBuyRequest {
  string nft_id = 1;
  string user_identification = 2;
  int32 apply_score = 3;
  int32 offer_count = 4;
}

//数字藏品购买返回值
message NftBuyResponse {
  int32 code = 1;
  string message = 2;
  string task_id = 3;
}

//nft销售状态变更参数
message NftStatusRequest {
  string nft_id = 1;
  string user_identification = 2;
  int32  trans_status = 3;
  int32 trans_price = 4;
}

//nft销售状态变更返回值
message NftStatusResponse {
  int32 code = 1;
  string message = 2;
  string task_id = 3;
}

//判断获取openid
message UserOpenidRequest {
  string user_identification = 1;
}

message UserOpenidResponse {
  int32 code = 1;
  string message = 2;
  string url = 3;
}

//创建平台数字藏品参数
message NftCreateRequest {
  string name = 1;
  string image_url = 2;
  string desc = 3;
  string user_identification = 4;
  float price = 5;
  string series_id = 6;
  string detail = 7;
  int32 digital_type = 8;
}

//平台数字藏品返回值
message NftResponse {
  int32 code = 1;
  string message = 2;
}

//更新数字藏品信息
message NftUpdateRequest {
  int32 id = 1;
  string name = 2;
  string image_url = 3;
  string desc = 4;
  string user_identification = 5;
  float price = 6;
  int32 status = 7;
  string series_id = 8;
  string detail = 9;
  int32 digital_type = 10;
}

message NftDeleteRequest {
  int32 id = 1;
}

message NftRuiPengListRequest {}

//瑞鹏数据藏品返回值
message NftRuiPengListResponse {
  int32 code = 1;
  string message = 2;
  repeated RuiPengList data = 3;
}

//瑞鹏数据藏品列表数据展示
message RuiPengList {
    string name = 1;
    string image_url = 2;
    string desc = 3;
    float price = 4;
    string series_id = 5;
    string publish_identification = 6;
    string detail = 7;
    int32 digital_type = 8;
}

//购买nft入惨
message UserBuyNftRequest {
  string user_identification = 1;
  string publish_identification = 2;
  string series_id = 3;
  float price = 4;
  int32 sell_type = 5;
}

//购买nft返回值
message UserBuyNftResponse {
  int32 code = 1;
  string message = 2;
  string order_sn = 3;
}

//瑞鹏平台详情页参数
message NftRuiPengDetailRequest {
  string series_id = 1;
  string user_identification = 2;
}

message NftDetail {
  string detail = 1;
  float price = 2;
  string publish_identification = 3;
  string series_id = 4;
  int32 flag = 5;
  string name = 6;
  string desc = 7;
  int32 digital_type = 8;
}

//瑞鹏平台详情页返回值
message NftRuiPengDetailResponse {
  int32 code = 1;
  string message = 2;
  NftDetail data = 3;
}

//数字藏品订单支付参数
message UserNftPayRequest {
  string user_identification = 1;
  string order_sn = 2;
}

//数字藏品订单支付返回值
message UserNftPayResponse {
  int32 code = 1;
  string message = 2;
  string data = 3;
}

//数字藏品购买领取
message NftPayResultPickRequest {
  string user_identification = 1;
  string nft_id = 2;
  float price = 3;
}

//获取auth openid
message UserAuthOpenidRequest {
  string code = 1;
}

//数字藏品订单支付返回值
message UserAuthOpenidResponse {
  int32 code = 1;
  string message = 2;
  string openid = 3;
}

//用户转换参数
message ChangeUserIdRequest {
  string scrm_user_id = 1;
}

//用户转换返回值
message ChangeUserIdResponse {
  int32 code = 1;
  string message = 2;
  string user_identification = 3;
}

