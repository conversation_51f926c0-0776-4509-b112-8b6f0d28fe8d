syntax = "proto3";

//option go_package = "./ctc";

package ctc;

// 通用基础服务
service ContentCenterService {
    // 文章新增或修改
    rpc ArticleSave(ArticleSaveRequest) returns(ContentResponse);
    // 文章列表
    rpc ArticleList(ArticleListRequest) returns(ArticleListResponse);
    // 文章小程序过滤数据
    rpc ArticleMiniFilter(EmptyRequest) returns(ArticleMiniFilterResponse);
    // 文章列表-小程序
    rpc ArticleListMini(ArticleListRequest) returns(ArticleListMiniResponse);
    // 文章详情
    rpc ArticleDetail(ArticleDetailRequest) returns(ArticleDetailResponse);
    // 文章搜索条件
    rpc ArticleSearchCondition(ArticleSearchConditionRequest) returns(ArticleSearchConditionResponse);
    // 文章发布、下架
    rpc ArticleStatus(ArticleStatusRequest) returns(ContentResponse);

    //查询文章操作记录
    rpc ArticleEditHistoryList(ArticleEditHistoryListRequest) returns(ArticleEditHistoryListResponse);
    //推送文章概要
    rpc ArticleRecommend(ArticleRecommendRequest) returns(ArticleRecommendResponse);
    // 类目新增或修改
    rpc ArticleCategorySave(ArticleCategorySaveRequest) returns(ArticleCategorySaveResponse);
    // 类目删除
    rpc ArticleCategoryDelete(ArticleCategoryDeleteRequest) returns(ArticleCategoryDeleteResponse);
    // 类目查询
    rpc ArticleCategoryList(ArticleCategoryListRequest) returns(ArticleCategoryListResponse);

    // 医生列表查询
    rpc QueryDoctorList(QueryDoctorListRequest) returns(QueryDoctorListResponse);

    // 获取类目
    rpc CategoryBar(CategoryBarRequest) returns (CategoryBarResponse);

    // 获取二级类目以及文章统计数据
    rpc GetStatistics(OrgIdRequest) returns (GetStatisticsResponse);

    // 搜索词列表
    rpc WordList(WordRequest) returns (WordResponse);
    // 搜索词新增或修改
    rpc WordOperate(JsonStrRequest) returns (ContentResponse);
    // 搜索词隐藏
    rpc WordDelete(WordDeleteRequest) returns (ContentResponse);
    // 首页热门搜索词
    rpc IndexHotWord(IndexWordRequest) returns (IndexHotWordResponse);
    // 首页默认搜索词
    rpc IndexDefaultWord(IndexWordRequest) returns (IndexDefaultWordResponse);

    //搜索词库列表
    rpc Keywords(KeywordsRequest) returns(KeywordsResponse);
    //热搜词添加
    rpc KeywordStore(KeywordStoreRequest) returns(ContentResponse);
    //单个热搜词删除
    rpc KeywordDelete(KeywordDeleteRequest) returns(ContentResponse);
}

service CensusService {
    //添加访问记录
    rpc AddVisitRecord(AddVisitRecordRequest) returns(ContentResponse);
    //添加勃林格访问记录
    rpc AddBoehringereChickRecord(BoehringereChickRecord) returns(ContentResponse);
    // 文章统计
    rpc ArticleCensus(ArticleCensusRequest) returns(ArticleCensusResponse);
    // 勃林格统计
    rpc BoehringereCensus(BoehringereCensusRequest) returns(BoehringereCensusResponse);
}

service BiliAdService {
    // 添加B站广告点击记录
    rpc AddClickRecord(AddClickRecordRequest) returns(ContentResponse);
    // 查找B站广告点击记录
    rpc FindClickRecords(FindClickRecordsRequest) returns(FindClickRecordsResponse);
    // 保存推送B站广告归因记录
    rpc SaveConversion(SaveConversionRequest) returns(ContentResponse);
}

message ContentResponse {
    int32 code = 1;
    string message = 2;
}


// 文章保存
message ArticleSaveRequest {
    // 模板id
    int32 template_id = 1;
    // 一级分类id
    int32 category_first = 2;
    // 二级分类id
    int32 category_second = 3;
    // 三级分类id
    int32 category_third = 4;
    // 视频地址
    string video_url = 5;
    // 封面地址
    string cover_url = 6;
    // 标题
    string title = 7;
    // 内容
    string content = 8;
    // 医生code，接口中doctor_code
    string doctor_code = 9;
    // 执业证书编号
    string doctor_cert_no = 10;
    // 标签集合
    string tag_json = 11;
    // 分发渠道，1-百度小程序
    repeated int32 dis_channel = 12;
    // 是否显示广告，1-显示，0-不显示
    int32 is_show_ads = 13;
    // 在线问诊入口图片地址
    string online_ask_url = 14;
    // 文章类型：1-图文，2-视频
    int32 article_type = 16;
    // 文章id
    int32 article_id = 17;
    // 状态，1-保存发布，2-保存草稿
    int32 status = 18;
    // 主体id
    int32 orgId = 19;
}
message ArticleTagData {

}


// 文章列表-后台
message ArticleListRequest {
    string title = 1;
    string doctor_name = 2;
    int32 article_type = 3;
    int32 category_first = 4;
    int32 category_second = 5;
    int32 category_third = 6;
    int32 dis_channel = 7;
    int32 page_index = 8;
    int32 page_size = 9;
    int32 status = 10;
    // 标签数据过滤，json字符串 [{"name":"年龄","tags":"幼年"}]
    string tags = 11;
    //随机值
    int32 randNum = 12;
    // 主体id
    int32 orgId = 13;
}
message ArticleListResponse {
    // 响应码
    int32 code = 1;
    // 返回信息
    string message = 2;
    repeated ArticleListData data = 3;
    int64 total_count = 4;
}
message ArticleListData {
    // 文章id
    int32 article_id = 1;
    // 标题
    string title = 2;
    // 文章分类，1-图文，2-视频
    int32 article_type = 3;
    // 医生姓名
    string doctor_name = 4;
    // 创建时间
    string created_at = 5;
    // 最后发布时间
    string last_publish_time = 6;
    // 最后更新时间
    string updated_at = 7;
    // 最后操作人
    string last_operator = 8;
    // 文章状态，0-未发布，1-已发布，2-下架
    string status = 9;
}
message ArticleSearchConditionRequest {
    string from = 1;
    // 主体id
    int32 orgId = 2;
}
message ArticleSearchConditionResponse {
    // 响应码
    int32 code = 1;
    // 返回信息
    string message = 2;
    repeated ArticleArr article_type = 3;
    repeated ArticleArr dis_channel = 4;
    repeated ArticleArr status = 5;
    repeated FirstArticleCategory article_category = 6;
}
message ArticleArr {
    int32 id = 1;
    string name = 2;
}


// 小程序列表
message ArticleListMiniResponse {
    // 响应码
    int32 code = 1;
    // 返回信息
    string message = 2;
    repeated ArticleListMiniData data = 3;
    int64 total_count = 4;
}
message ArticleListMiniData {
    // 文章id
    int64 article_id = 1;
    // 标题
    string title = 2;
    // 文章分类，1-图文，2-视频
    int32 article_type = 3;
    // 医生姓名
    string doctor_name = 4;
    // 医生职称
    string doctor_position_name = 5;
    // 医生所属医院
    string hospital = 6;
    // 医生执业证书编号
    string doctor_cert_no = 7;
    // 视频地址
    string video_url = 8;
    // 封面地址
    string cover_url = 9;
    // 医生头像
    string head_img = 10;
    // 最后发布日期
    string publish_time = 11;
    //更新时间
    string updated_at = 12;
}

// 文章详情
message ArticleDetailRequest {
    int32 article_id = 1;
    string from = 2;
}
message ArticleDetailResponse {
    // 响应码
    int32 code = 1;
    // 返回信息
    string message = 2;
    // 文章详情信息
    ArticleDetail detail = 3;
}
message ArticleDetail {
    // 模板id
    int32 template_id = 1;
    // 一级分类id
    int32 category_first = 2;
    // 二级分类id
    int32 category_second = 3;
    // 三级分类id
    int32 category_third = 4;
    // 视频地址
    string video_url = 5;
    // 封面地址
    string cover_url = 6;
    // 标题
    string title = 7;
    // 内容
    string content = 8;
    // 医生code，接口中doctor_code
    string doctor_code = 9;
    // 执业证书编号
    string doctor_cert_no = 10;
    // 标签集合
    string tag_json = 11;
    // 分发渠道，1-百度小程序,2-阿闻小程序，4-阿闻app
    int32 dis_channel = 12;
    repeated int32 dis_channel_arr = 24;
    // 是否显示广告，1-显示，0-不显示
    int32 is_show_ads = 13;
    // 在线问诊入口图片地址
    string online_ask_url = 14;
    // 文章类型：1-图文，2-视频
    int32 article_type = 16;
    // 文章id
    int32 article_id = 17;
    // 最后更新时间
    string updated_at = 18;
    //医生名称
    string doctor_name =20;
    //医生称号（职级、岗位）
    string doctor_level =21;
    //医生头像
    string doctor_img =22;
    //医院名称
    string hospital_name = 23;
}


// 文章发布、下架
message ArticleStatusRequest {
    int32 article_id = 1;
    int32 status = 2;
}

// 文章操作历史查询
message ArticleEditHistoryListRequest {
    // 文章id
    int32 article_id = 1;
    // 操作类型 默认0,1- 创建并保存草稿，2-编辑并保存草稿，3-发布，4-编辑并发布，5-下架
    int32 type = 2;
    // 创建人id
    string create_id = 3;
    // 页
    int32 page_index = 4;
    // 页大小
    int32 page_size = 5;
}
message ArticleEditHistoryListResponse {
    int32 code = 1;
    string message = 2;
    repeated ArticleEditRecord data = 3;
    // 总条数
    int32 total_count = 4;
}

message ArticleEditRecord {
    int32 id = 1;
    // 文章id
    int32 article_id = 2;
    // 操作类型 默认0,1- 创建并保存草稿，2-编辑并保存草稿，3-发布，4-编辑并发布，5-下架
    int32 type = 3;
    // 操作内容
    string operation_data = 4;
    // 操作人编号
    string create_id = 5;
    // 操作人名称
    string create_name = 6;
    // 操作时间
    string created_at = 7;
}

// 文章推荐
message ArticleRecommendRequest {
    // 文章id
    int64   article_id  = 1;
}
message ArticleRecommendResponse {
    int32       code = 1;
    string      message = 2;
    // 总数
    int32       total_count   = 3;
    repeated    ArticleRecommend data = 5;
}
message ArticleRecommend {
    // 文章id
    int32   id    = 1;
    // 文章标题
    string  title    = 2;
    // 文章封面
    string  cover   = 3;
    // 医生编码
    string  doctor_code = 4;
    // 医生名称
    string  doctor_name   = 5;
    // 证书
    string  certificate = 6;
    // 医院名称
    string  hospital_name = 7;
    // 医生职称
    string  doctor_level   = 8;
    // 医生头像
    string  doctor_img    = 9;
    // 文章类型 1-图文，2-视频
    int32   article_type   = 10;
}

// 类目新增编辑
message ArticleCategorySaveRequest {
    // 类目id
    int32 id = 1;
    // 类目名称
    string category_name = 2;
    // 上级id
    int32 parent_id = 3;
    // 是否可用 0 不可 , 1可用
    int32 valid = 4;
    // 创建人id
    string create_id = 5;
    // 创建人名字
    string create_name = 6;
    // 类目层级 1 一级 ， 2 二级 ， 3 三级
    int32 level = 7;
    // 主体id
    int32 orgId = 8;
}
message ArticleCategorySaveResponse {
    int32 code = 1;
    string message = 2;
    int32 data = 3;
}

// 类目删除
message ArticleCategoryDeleteRequest {
    // 类目id
    int32 category_id = 1;
    int32 level = 2;
}
message ArticleCategoryDeleteResponse {
    int32 code = 1;
    string message = 2;
}

// 类目查询
message ArticleCategoryListRequest {
    // 主体id
    int32 orgId = 1;
}
message ArticleCategoryListResponse {
    int32 code = 1;
    string message = 2;
    repeated FirstArticleCategory data = 3;
}

// 一级类目
message FirstArticleCategory {
    // 类目id
    int32 id = 1;
    // 类目名称
    string category_name = 2;
    // 上级id
    int32 parent_id = 3;
    // 是否可用
    int32 valid = 4;
    // 创建人id
    string create_id = 5;
    // 创建人姓名
    string create_name = 6;
    // 类目层级
    int32 level = 7;
    // 子类目
    repeated SecondArticleCategory child = 8;
}

// 二级类目
message SecondArticleCategory {
    int32 id = 1;
    string category_name = 2;
    int32 parent_id = 3;
    int32 valid = 4;
    string create_id = 5;
    string create_name = 6;
    int32 level = 7;
    repeated ThirdArticleCategory child = 8;
}

// 三级类目
message ThirdArticleCategory {
    int32 id = 1;
    string category_name = 2;
    int32 parent_id = 3;
    int32 valid = 4;
    string create_id = 5;
    string create_name = 6;
    int32 level = 7;
}
message QueryDoctorListRequest{
    //关键字搜索，现只支持医生名称
    string keyword = 1;
}
message QueryDoctorListResponse{
    int32 code = 1;
    string message = 2;
    repeated ScrmDoctor data = 3;
}
message ScrmDoctor{
    //医生编号
    string doctor_code = 1;
    //医生名称
	string doctor_name =2;
	//医生称号（职级、岗位）
	string doctor_level =3;
	//医生性别
	string doctor_sex =4;
	//医生头像
	string doctor_img =5;
	//医院编号
	string hospital_code = 6;
	//医院名称
	string hospital_name = 7;
}

message ArticleCensusRequest{
    //开始时间
    string begin_time = 1;
    //结束时间
    string end_time = 2;
    //访问渠道 1：阿闻小程序 2：百度小程序  3：安卓 4：IOS
    int32 channel = 3;
    //页码
    int32 page_index = 4;
    //每页条数，默认20
    int32 page_size = 5;
    //关键字 现支持文章标题
    string keyword = 6;
    // 主体id
    int32 orgId = 7;
}
message ArticleCensusResponse{
    int32 code = 1;
    string message = 2;
    //总条数
    int32 total_count = 3;
    //文章访问统计
    repeated ArticleCensus data = 4;
}
message ArticleCensus{
    //文章id
    int64 article_id = 1;
    //文章标题
    string article_title = 2;
    //独立访问量
    int32 unique_visitor = 3;
    //页面访问量
    int32 page_view = 4;
    //视频播放量
    int32 video_view = 5;
}

message BoehringereCensusRequest{
    //开始时间
    string begin_time = 1;
    //结束时间
    string end_time = 2;
}
message BoehringereCensusResponse{
    int32 code = 1;
    string message = 2;
    //文章访问统计
    repeated BoehringereCensus data = 3;
}
message BoehringereCensus{
    //类型：1:banner 2:内容区 3:产品区 4:问诊区 5:产品介绍区一 6:产品介绍区二 7:产品介绍区三 8:产品介绍区四
    int32 type = 1;
    //类型子级（0为标题，1为内容一，以此类推）
    int32 type_child = 2;
    //访问渠道 1：阿闻小程序 2：百度小程序  3：安卓 4：IOS
    int32 channel = 3;
    //点击量
    int32 total_count = 5;
}
message AddVisitRecordRequest{
    //访问内容id（type=1时为文章id）
	int32 content_id = 1;
    // 类型 1：文章 2：视频
	int32 type = 2;
	// 访问渠道 1：阿闻小程序 2：百度小程序  3：安卓 4：IOS
	int32 channel = 3;
	// 用户编号或openId
	string user_no = 4;
	// IP
	string ip = 5;
}
message BoehringereChickRecord{
    // 类型：1:banner 2:内容区 3:产品区 4:问诊区 5:产品介绍区一 6:产品介绍区二 7:产品介绍区三 8:产品介绍区四
	int32 type = 1;
	// 类型子级（0为标题，1为内容一，以此类推）
	int32 type_child = 2;
	// 用户编号
	string user_no = 3;
	// IP
	string ip = 4;
}

message CategoryBarRequest{
    //0 默认查一级分类 1 二级分类
    int32 type = 1;
    string tags = 2;
    // 主体id，默认1-阿闻
    int32 org_id = 3;
}

message CategoryBarResponse{
    int32 code = 1;
    string message = 2;
    repeated Category   data = 3;
}

message Category {
    int32 id = 1;
    string category_name = 2;
    int32 parent_id = 3;
    int32 valid = 4;
    string create_id = 5;
    string create_name = 6;
    int32 level = 7;
}

message EmptyRequest{

}

message OrgIdRequest{
    // 主体id，默认1-阿闻
    int32 org_id = 1;
}

message ArticleTagGroup {
    // 标签组名称
    string name = 1;
    // 多个标签，用逗号分割
    string tags = 2;
}

message ArticleMiniFilterResponse {
    // 200正常，400错误
    int32 code = 1;
    string message = 2;
    // 标签组数据
    repeated ArticleTagGroup tag_groups = 3;
}

message GetStatisticsResponse{
    //文章条数
    int32 articleCount =1;
    //类目条数
    int32 categoryCount = 2;
}

message AddClickRecordRequest{
    string track_id = 1;
    string account_id = 2;
    string campaign_id = 3;
    string unit_id = 4;
    string creative_id = 5;
    int64 os = 6;
    string imei = 7;
    string callback_url = 8;
    string mac1 = 9;
    string idfa = 10;
    string caid = 11;
    string aaid = 12;
    string android_id = 13;
    string oaid = 14;
    string ip = 15;
    string ua = 16;
    string model = 17;
    int64 ts = 18;
    int64 shop_id = 19;
    int64 up_mid = 20;
}

message FindClickRecordsRequest{
    string imei = 1;
    int64 os = 2;
    string idfa = 3;
    string oaid = 4;
    string ip = 5;
    string ua = 6;
    string created_at = 7;
}

message BiliAdClickRecord{
    int64 id = 1;
    string track_id = 2;
    string account_id = 3;
    string campaign_id = 4;
    string unit_id = 5;
    string creative_id = 6;
    int64 os = 7;
    string imei = 8;
    string mac1 = 9;
    string idfa = 11;
    string caid = 12;
    string aaid = 13;
    string android_id = 14;
    string oaid = 15;
    string ip = 16;
    string ua = 17;
    int64 ts = 18;
    int64 shop_id = 19;
    int64 up_mid = 20;
}

message FindClickRecordsResponse{
    // 响应码
    int32 code = 1;
    // 返回信息
    string message = 2;
    repeated BiliAdClickRecord list = 3;
}

message SaveConversionRequest{
    int64 bili_ad_click_id = 1;
    string conv_type = 2;
    int64 conv_time = 3;
    int64 conv_value = 4;
    int64 conv_count = 5;
    string imei = 6;
    string idfa = 7;
    string oaid = 8;
    string mac = 9;
    string client_ip = 10;
    string model = 11;
    string track_id = 12;
    string ua = 13;
    int64 result_code = 14;
    string result_message = 15;
}

message JsonStrRequest{
    // json字节
    bytes json_byte =1;
    // 类型，1-热词，2-默认词
    int32 type = 2;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =3;
}

// ********* 搜索词相关接口  ****************
// 前端接口
message IndexWordRequest {
    // 渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它，7-竖屏
    string user_agent = 1;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =2;
}
message IndexHotWordResponse{
    // 200正常，400错误
    int32 code = 1;
    string message = 2;
    repeated IndexHotWordData data = 3;
    message IndexHotWordData {
        // 热词名称
        string name = 1;
        // 是否推荐，1-推荐（显示火的图标），2-不推荐
        int32 is_recom = 2;
    }
}

message IndexDefaultWordResponse{
    // 200正常，400错误
    int32 code = 1;
    string message = 2;
    repeated IndexDefaultWordData data = 3;
    message IndexDefaultWordData {
        // 默认词名称
        string name = 1;
        // 实际搜索词
        string real_name = 2;
        // 落地页路径
        string path_url = 3;
    }
}

// 后台接口
message WordData {
    // id
    int64 id = 1;
    // 词名称
    string name = 2;
    // 实际搜索词
    string real_name = 4;
    // 渠道，1-微信小程序，2-app，4-百度
    int32 channel = 5;
    // 是否推荐，1-推荐，2-不推荐
    int32 is_recom = 3;
    // 是否显示，1-显示，2-不显示
    int32 is_show = 6;
    // 落地页路径
    string path_url = 7;
    // 最后更新时间
    string updated_at = 8;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =9;
}
message WordResponse {
    int32 code = 1;
    string message = 2;
    repeated WordData data = 3;
    int64 total_count = 4;
}

message WordRequest {
    // 默认搜索词
    string name = 1;
    // 实际搜索词
    string real_name = 2;
    // 搜索渠道
    int32 channel = 3;
    // 类型，1-热词，2-默认词
    int32 type = 4;
    // 页码
    int32 page_index = 5;
    // 每页条数
    int32 page_size = 6;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =7;
}

message HotWordRequest {
    // 搜索词
    string name = 1;
    // 页码
    int32 page_index = 5;
    // 每页条数
    int32 page_size = 6;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =7;
}

message WordDeleteRequest {
    // 列表返回的id
    int64 id = 1;
    // 不用传，后端会处理
    int32 type = 4;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =5;
}

message HotWordOperateRequest {
    // id，有值表示修改，默认新增
    int64 id = 1;
    // 是否推荐，1-推荐，2-不推荐
    int32 is_recom = 3;
    // 词名称
    string name = 4;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =5;
}

message WordOperateRequest{
    // id，有值表示修改，默认新增
    int64 id = 1;
    // 词名称
    string name = 2;
    // 实际搜索词
    string real_name = 3;
    // 搜索渠道
    int32 channel = 4;
    // 落地页路径
    string path_url = 5;
    // 是否显示，1-显示，2-不显示
    int32 is_show = 6;
    // 是否推荐，1-推荐，2-不推荐
    int32 is_recom = 7;
    // 类型，1-热词，2-默认词
    int32 type = 8;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =9;
}

message DefaultWordRequest{
    // 默认搜索词
    string name = 1;
    // 实际搜索词
    string real_name = 2;
    // 搜索渠道
    int32 channel = 3;
    // 页码
    int32 page_index = 5;
    // 每页条数
    int32 page_size = 6;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =7;
}

message DefaultWordOperateRequest {
    // id，有值表示修改，默认新增
    int64 id = 1;
    // 词名称
    string name = 2;
    // 实际搜索词
    string real_name = 3;
    // 搜索渠道
    int32 channel = 4;
    // 落地页路径
    string path_url = 5;
    // 是否显示，1-显示，2-不显示
    int32 is_show = 6;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =7;
}

message KeywordsRequest {
    //搜索关键词
    string name = 1;
    //页码
    int32 page_index = 3;
    // 每页数量，不传默认10
    int32 page_size = 4;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =5;
}
message KeywordsResponse {
    // 状态码，200正常，>=400出错
    int32 code = 1;
    // 消息
    string message = 2;

    message List {
        int32 id = 1;
        //名称
        string name = 2;
        //状态 0未生效、1已生效 2待删除 4已删除
        int32  status = 4;
    }
    repeated List data = 3;
    // 总数
    int32 total = 4;
}

message KeywordStoreRequest {
    //热搜词
    string name = 1;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =2;
}

message KeywordDeleteRequest {
    int32 id = 1;
    //主体：1-阿闻，2-极宠家 3.润合云店
    int32 org_id =2;
}

// ********* 搜索词相关接口结束  ****************









