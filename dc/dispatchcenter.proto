syntax = "proto3";
import "google/protobuf/empty.proto";
package dc;

//区域信息
service BaseAreaService {
  //区域数据
  rpc BaseArea(BaseAreaRequest) returns (BaseAreaResponse);
  //注释
  //根据仓库ID获取仓库配送区域GetWarehouseInfoByFanceCodeRequest
  rpc GetAreaByWarehouseId(GetAreaByWarehouseIdRequest)
      returns (GetAreaByWarehouseIdResponse);
}

//仓库模块
service WarehouseService {
  //仓库列表
  rpc WarehouseList(WarehouseListRequest) returns (WarehouseListResponse);
  //变更仓库状态
  rpc UpdateWarehouseStatus(UpdateWarehouseStatusRequest)
      returns (BaseResponse);

  //根据仓库id查询仓库详情
  rpc GetWarehouseById(GetWarehouseByIdRequest)
      returns (GetWarehouseByIdResponse);

  // 新增仓库
  rpc AddWarehouse(AddWarehouseRequest) returns (BaseResponse);

  // 编辑仓库
  rpc EditWarehouse(EditWarehouseRequest) returns (BaseResponse);

  // 店铺绑定的所有仓库(门店仓、前置仓、虚拟仓)
  rpc ShopWarehouseList(ShopWarehouseListRequest) returns (WarehouseListResponse);

  //新增仓库配送区域
  rpc AddWarehouseArea(AddWarehouseAreaRequest) returns (BaseResponse);
  //获取仓库类型
  rpc GetWarehouseType(Empty) returns (GetWarehouseTypeResponse);

  //获取仓库级别
  rpc GetWarehouseLevel(Empty) returns (GetWarehouseLevelResponse);

  //根据区域获取仓库列表
  rpc GetWarehouseByArea(WarehouseByAreaRequest)
      returns (WarehouseByAreaResponse);

  //仓库(前置仓)关联门店
  rpc WarehouseRelation(WarehouseRelationRequest) returns (BaseResponse);

  //根据仓库id获取已绑定的门店信息列表
  rpc GetWarehouseRelationList(WarehouseRelationListRequest)
      returns (WarehouseRelationListResponse);

  //根据财务编码获取仓库信息
  rpc GetWarehouseInfoByFanceCode(GetWarehouseInfoByFanceCodeRequest)
      returns (GetWarehouseInfoByFanceCodeResponse);
  //根据财务编码批量获取仓库信息
  rpc GetWarehouseInfoByFanceCodes(GetWarehouseInfoByFanceCodesRequest)
      returns (GetWarehouseInfoByFanceCodesResponse);
  //根据条件获取仓库信息（V3.1版本的需要）
  rpc GetWarehouseInfoByCondition(GetWarehouseInfoByConditionRequest)
      returns (GetWarehouseInfoByConditionResponse);
  //根据分类获取门店列表
  rpc GetStoreListByCategory(GetStoreListByCategoryRequest)
      returns (GetStoreListByCategoryResponse);
  //前置仓关联的门店信息
  rpc GetWarehouseRelation(Empty) returns (GetWarehouseRelationResponse);
  //仓库操作日志
  rpc WarehouseLog(WarehouseLogRequest) returns (WarehouseLogResponse);

  // 区域仓关联前置仓
  rpc PreposeWarehouseRelation(PreposeWarehouseRelationRequest)
      returns (BaseResponse);
  // 根据id查询区域仓关联的前置仓信息列表
  rpc GetPreposeWarehouseRelationList(WarehouseRelationListRequest)
      returns (RegionRelationListRespon);
  // 查询前置仓关联的所有城市列表
  rpc PreposeWarehouseCityList(Empty) returns (PreposeCiytListResponse);

  // 查询门店是否绑定了前置虚拟仓
  rpc GetShopWarehouseInfoByFinanceCode(
      GetShopWarehouseInfoByFinanceCodeRequest)
      returns (GetShopWarehouseInfoByFinanceCodeResponse);

  // 门店绑定信息列表
  rpc ShopBindInfoList(ShopBindInfoRequest) returns (ShopBindInfoRespond);

  // 门店仓库管理列表
  rpc WarehouseRelationShopList(ShopBindWarehouseReq) returns (ShopBindWarehouseRes);

  // 使用店铺id获取其绑定仓库信息
  rpc ShopBindInfoListByShopId(ShopBindInfoByShopIdRequest)
      returns (ShopBindInfoByShopIdRespond);

  //获取绑定了仓库的门店数量
  rpc BindShops(BindShopsRequest) returns (BindShopsRespond);

  //插入美团渠道绑定仓库数据
  rpc StoreWarehouseRelationShop(StoreWarehouseRelationShopRequest)
      returns (StoreWarehouseRelationShopRespond);

  // 同步仓库信息列表
  rpc GetA8WareHouseList(GetA8WareHouseListRequest)
      returns (GetA8WareHouseListResponse);

  // 初始化店铺绑定数据
  rpc InitBindShopData(InitShopDataRequest) returns (InitShopDataRespond);
  // 修复初始化店铺绑定数据
  rpc InitBindMTShopData(InitShopDataRequest) returns (InitShopDataRespond);
  // 用code取仓库信息
  rpc GetWarehouseInfoByCode(GetWarehouseInfoByCodeRequest)
      returns (GetWarehouseInfoByCodeResponse);

  //同步oms仓库
  rpc SyncOmsWarehouse(SyncOmsWarehouseRequest) returns (google.protobuf.Empty);

  //移除渠道绑定关系
  rpc RemoveShop(RemoveShopRequest) returns (RemoveShopRespond);
}

//移除渠道绑定关系
message RemoveShopRequest {
  //渠道
  int32  channel_id = 1;
  //门店编码
  string shop_id = 2;
}

message RemoveShopRespond {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

// 初始化店铺绑定数据

message InitShopDataRequest {
  //1 初始渠道关系; 2启用全部门店仓
  int32 step = 1;
}

message InitShopDataRespond {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  // 成功的信息
  repeated SWInfo info = 4;
}

message SWInfo {
  string shop_id = 1;
  int32 ware_house_id = 2;
  string shop_name = 3;
  string warehouse_name = 4;
  int32 channel_id = 5;
}

// 查询门店是否绑定了前置虚拟仓
message GetShopWarehouseInfoByFinanceCodeRequest {
  int32 channel_id = 1;
  string finance_code = 2;
}

message GetShopWarehouseInfoByFinanceCodeResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //仓库信息
  WarehouseList Warehouseinfo = 4;
}

//根据分类获取门店列表-- 请求参数
message GetStoreListByCategoryRequest {
  // 3门店仓 4前置（虚拟）仓
  int32 category = 1;
  // 渠道id，1-阿闻到家 2-美团 3-饿了么 4-京东到家
  int32 channel_id = 2;
}

//根据分类获取门店列表-- 响应参数
message GetStoreListByCategoryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //仓库信息
  repeated string finance_code = 4;
}

message GetWarehouseInfoByFanceCodesRequest {
  repeated string finance_code = 1;
  //1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提
  int32 channel_id = 3;
}

message GetWarehouseInfoByFanceCodesResponse {
  repeated WarehouseList data = 1;
}

//根据财务编码获取仓库信息-- 请求参数
message GetWarehouseInfoByFanceCodeRequest {
  string finance_code = 1;
  int32 warehouse_id = 2; // 仓库的id
  //1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提
  int32 channel_id = 3;
}

//根据财务编码获取仓库信息-- 响应参数
message GetWarehouseInfoByFanceCodeResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //仓库信息
  WarehouseList Warehouseinfo = 4;
}

//拆单模块
service DemolitionOrderService {
  //    rpc DemolitionOrder (DemolitionOrderRequest)
  //    returns(DemolitionOrderResponse);

  rpc SetDemolitionOrderTestData(DataRequest) returns (BaseResponse);
}

///仓库(前置仓)关联门店 -- 请求参数
message WarehouseRelationRequest {
  //前置仓id
  int32 warehouse_id = 1;

  //门店信息数组
  repeated ShopInfo ShopInfoList = 2;
}

//门店信息
message ShopInfo {
  //门店的财务编码
  string shop_id = 1;
  //门店的名称
  string shop_name = 2;
}

// 区域仓关联前置仓
message PreposeWarehouseRelationRequest {
  // 区域仓id
  int32 RegionId = 1;
  // 前置仓id列表
  repeated int32 PreposeIdList = 2;
}

//根据仓库id获取已绑定的门店信息列表--请求参数
message WarehouseRelationListRequest {
  //仓库id
  int32 warehouse_id = 1;
}

//根据仓库id获取已绑定的门店信息列表--响应参数
message WarehouseRelationListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //门店信息数组
  repeated ShopInfo ShopInfoList = 4;
}

// 根据仓库id获取已关联的前置仓信息列表响应
message RegionRelationListRespon {
  int32 code = 1;
  // 消息
  string msg = 2;
  // 数据
  repeated PreposeWarehouseInfo data = 3;
}

// 前置仓信息
message PreposeWarehouseInfo {
  // 仓库id
  int32 warehouse_id = 1;
  // 仓库编码
  string warehouse_code = 2;
  // 仓库名称
  string warehouse_name = 3;
}

message Empty {}

// 前置仓关联的城市列表
message PreposeCiytListResponse {
  int32 code = 1;
  // 消息
  string msg = 2;
  // 数据
  repeated string data = 3;
}

//通用返回
message BaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message DemolitionOrderResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;

  //仓库数组
  repeated WarehouseToGoods WarehouseToGoodsList = 4;
}

//商品对应的仓库集合
message WarehouseToGoods {
  //仓库id
  int32 warehouseid = 1;
  //订单id
  string orderid = 2;
  //商品数量
  int32 Quantity = 3;
  //商品Id
  string goodsid = 4;

  //第三方仓库id  例如A8Id ,管易Id
  string thirdid = 5;
  //第三方仓库code  例如A8 code,管易 code
  string warehousecode = 6;
}

message DataRequest { repeated TestData DataList = 1; }

message TestData {
  //仓库id
  int32 warehouseid = 1;
  //库存数量
  int32 store = 2;
  //商品Id
  string goodsid = 3;
}

//新增仓库
message AddWarehouseRequest {
  //仓库编号
  string code = 1;

  string thirdid = 2;

  //仓库名称
  string name = 3;

  //仓库归属(1-A8,2-管易)
  int32 comefrom = 4;

  //仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
  int32 level = 5;

  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
  int32 category = 6;

  //仓库地址
  string address = 7;

  //仓库联系人
  string contacts = 8;

  //仓库联系方式
  string tel = 9;

  //所属系统 0:默认,1:ERP,2:子龙
  int32 subsystem = 10;

  int32 ratio = 11;
  //仓库经度
  int64 lng = 12;
  //仓库纬度
  int64 lat = 13;

  // 所属区域
  string region = 14;
  // 所属城市
  string city = 15;
  // 是否有资质售药 1有、0否
  //int32 sell_drugs = 18; v6.27.2 
  repeated WarehouseDelivery  warehouse_delivery =19;
}

message WarehouseByAreaRequest {
  //省份
  int32 province = 1;
  //仓库归属(1-A8,2-管易)
  int32 comefrom = 2;
}

message WarehouseByAreaResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //仓库数组
  repeated WarehouseList WarehouseAarray = 4;
}

//编辑仓库
message EditWarehouseRequest {

  int32 id = 1;
  //仓库编号
  string code = 2;

  //仓库名称
  string name = 3;

  //仓库归属(1-A8,2-管易)
  int32 comefrom = 4;

  //仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
  int32 level = 5;

  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
  int32 category = 6;

  //仓库地址
  string address = 7;

  //仓库联系人
  string contacts = 8;

  //仓库联系方式
  string tel = 9;

  //所属系统 0:默认,1:ERP,2:子龙
  int32 subsystem = 10;
  //仓库比例
  int32 ratio = 11;

  //仓库比例
  int32 status = 12;

  //第三方仓库ID
  string thirdid = 13;

  //仓库经度
  int64 lng = 14;

  //仓库纬度
  int64 lat = 15;

  // 所属区域
  string region = 16;
  // 所属城市
  string city = 17;
  // 是否有资质售药 1有、0否
  // int32 sell_drugs = 18;
  repeated WarehouseDelivery  warehouse_delivery =19;

}

//新增仓库配送区域
message WarehouseDelivery {
  //仓库选择绑定了的配送主键
  int32  delivery_id =1;
  //配送的门店编码
  string  shop_no =2;
}

//新增仓库配送区域
message AddWarehouseAreaRequest {

  repeated WarehouseArea WarehouseAreaList = 1;
  //仓库id
  int32 warehouseid = 2;
}

message WarehouseArea {
  //
  int32 id = 1;

  //区域id
  int32 areaid = 2;

  //仓库id
  int32 warehouseid = 3;

  //区域等级(1-一级，2-二级，3-3级)
  int32 level = 4;
}

//获取仓库类型
message GetWarehouseTypeResponse {
  repeated WarehouseType warehouseTypes = 1;
  //状态码
  int32 code = 2;
}
//仓库类型
message WarehouseType {

  int32 id = 1;
  string name = 2;
}

//获取仓库等级
message GetWarehouseLevelResponse {
  repeated WarehouseLevel warehouseLevels = 1;
  //状态码
  int32 code = 2;
}

//仓库等级
message WarehouseLevel {

  int32 id = 1;
  string name = 2;
}

//仓库列表请求参数
message WarehouseListRequest {
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
  string category = 1;
  //页码
  int32 pageindex = 2;
  //每页行数
  int32 pagesize = 3;
  //搜索关键词
  string keyWord = 4;
  // 所属城市
  string city = 5;
  // 是否查询前置仓和前置虚拟仓, 默认 0 否，1 是
  int32 prepose_category = 6;
  // 启用状态
  int32 status = 7;
  // 主体ID
  int32 org_id =8;
}

//仓库列表返回数据
message WarehouseListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //总行数
  int32 totalCount = 4;
  //仓库数组
  repeated WarehouseList WarehouseAarray = 5;
}

//仓库列表数据集
message WarehouseList {
  // ID
  int32 id = 1;
  //第三方仓库ID 例如a8id,管易ID
  string thirdid = 2;
  //仓库编号
  string code = 3;
  //仓库名称
  string name = 4;
  //仓库归属(1-A8,2-管易)
  int32 comefrom = 5;
  //仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
  int32 level = 6;
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
  int32 category = 7;
  //仓库地址
  string address = 8;
  //仓库联系人
  string contacts = 9;
  //仓库联系方式
  string tel = 10;
  //仓库状态（0-禁用，1-启用）
  int32 status = 11;
  //创建时间
  string createdate = 12;
  //最后更新时间
  string lastdate = 13;
  //所属系统 0:默认,1:ERP,2:子龙
  int32 subsystem = 14;
  //仓库比例
  int32 ratio = 15;
  //关联门店信息
  repeated string warehouse_info = 16;
  //仓库经度
  int64 lng = 17;
  //仓库纬度
  int64 lat = 18;
  // 所属区域
  string region = 19;
  // 所属城市
  string city = 20;
  //关联门店数量
  int32 number = 21;
}

//选择区域请求
message BaseAreaRequest {
  //地区深度,传1获取大区和省数据，传2获取大区省市数据，传3获取大区省市县数据
  int32 areadeep = 1;
}
//选择区域返回
message BaseAreaResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;

  //大区集合
  repeated RegionList regionarray = 4;
}

//大区集合
message RegionList {
  //大区名称
  string arearegion = 1;
  //省集合
  repeated ProvinceList children = 2;
}
//大区直属省，直辖市集合
message ProvinceList {
  // ID
  int32 areaid = 1;
  //地区名称
  string areaname = 2;
  //父ID
  int32 areaparentid = 3;
  //排序
  int32 areasort = 4;
  //地区深度，从1开始
  int32 areadeep = 5;
  //省直属市，区集合
  repeated CityList children = 6;
}

//省直属市，区集合
message CityList {
  // ID
  int32 areaid = 1;
  //地区名称
  string areaname = 2;
  //父ID
  int32 areaparentid = 3;
  //排序
  int32 areasort = 4;
  //地区深度，从1开始
  int32 areadeep = 5;
  //市直属县，区集合
  repeated CountyList children = 6;
}

//市直属县，区集合
message CountyList {
  // ID
  int32 areaid = 1;
  //地区名称
  string areaname = 2;
  //父ID
  int32 areaparentid = 3;
  //排序
  int32 areasort = 4;
  //地区深度，从1开始
  int32 areadeep = 5;
}

//变更仓库状态请求参数
message UpdateWarehouseStatusRequest {
  //仓库id
  int32 id = 1;
  //仓库状态（0-禁用，1-启用）
  string status = 2;
}

//根据仓库ID查询仓库详情请求参数
message GetWarehouseByIdRequest {
  //仓库id
  int32 id = 1;
}

//根据仓库ID查询仓库详情返回数据
message GetWarehouseByIdResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //仓库详情
  // WarehouseList Warehouse  =4;
  // ID
  int32 id = 4;
  //仓库编号
  string warehousecode = 5;
  //仓库名称
  string name = 6;
  //仓库归属(1-A8,2-管易)
  int32 comefrom = 7;
  //仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
  int32 level = 8;
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
  int32 category = 9;
  //仓库地址
  string address = 10;
  //仓库联系人
  string contacts = 11;
  //仓库联系方式
  string tel = 12;
  //仓库状态（0-禁用，1-启用）
  int32 status = 13;
  //创建时间
  string createdate = 14;
  //第三方仓库ID 例如a8id,管易ID
  string thirdid = 15;
  //所属系统 0:默认,1:ERP,2:子龙
  int32 subsystem = 16;
  //仓库比例
  int32 ratio = 17;
  //仓库经度
  int64 lng = 18;
  //仓库纬度
  int64 lat = 19;
  // 区域
  string region = 20;
  // 城市
  string city = 21;
  // 是否有资质售药 1有、0否
  int32 sell_drugs = 22;
  // 仓库关联配送配置信息
  repeated WarehouseDeliveryInfo warehouse_delivery_list = 23;
  //仓库关联门店及渠道
  repeated WarehouseRelationShopList relation_shop_list = 24;
}

message WarehouseDeliveryInfo {
  //配送配置主键ID
  int32 id = 1;
  //配送类型名称
  string  delivery_name=2;
  string  app_key=3;
  string  app_secret=4;
  string  scoure_id=5;
  //配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风
  int32  delivery_type=6;
  //配送门店编码
  string  shop_no=7;
}

message WarehouseRelationShopList{
  string shop_name=1;
  string channel_names = 2;
}

//根据仓库ID获取仓库配送区域请求参数
message GetAreaByWarehouseIdRequest {
  //仓库id
  int32 warehouseid = 1;
}

//根据仓库ID获取仓库配送区域返回数据
message GetAreaByWarehouseIdResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //区域信息
  repeated WarehouseAreaDto WarehouseArea = 4;
}

message WarehouseAreaDto {
  //区域名称
  string areaname = 1;
  //区域等级 1级 2级 3级 最多3级
  int32 level = 2;
  //区域ID
  int32 areaid = 3;
}

//拆单请求model
message DemolitionOrderRequest {
  int64 id = 1;
  //主键
  string orderid = 2;
  //用户编号
  string memberid = 3;
  //订单金额 单位（分）
  int32 ordermoney = 4;
  // 1 咨询订单2 门店订单3 预约订单4 电商订单5 积分订单6 拼团订单
  int32 ordertype = 5;
  //订单类型详细的子类型
  int32 ordertypedetail = 6;
  //订单状态：1-未支付，2-已支付，3-已退款，4-已取消，5-退款中
  int32 orderstate = 7;
  //
  int32 useragent = 8;
  //订单所属平台
  int32 platformid = 9;
  //订单的所属分院
  string belonghospitalid = 10;
  //创建时间
  string createtime = 11;
  //最后修改时间
  string lasttime = 12;
  //订单子状态：
  //第一位：主状态，第2、3位：活动类型{01：抽奖拼团，02：非抽奖拼团}，第4、5位：活动状态类型{01：待发货，02：待收货，03：交易完成，04:拼团中，05：拼团成功，06：拼团失败
  //}
  int32 orderchildenstate = 13;
  //是否评价：0-未评价，1-已评价
  int32 isevaluate = 14;
  //是否推送电商(0-否，1-是)
  int32 ispostupet = 15;
  //是否发送5分钟通知(0-否，1-是)
  int32 isnotify = 16;
  // 1.A8 ,2管易 ,3门店
  int32 ordersource = 17;
  // 0.初始化
  int32 status = 18;
  //订单相信信息
  repeated OrderGoodsDetail OrderGoodsDetails = 19;
  //订单类型详细的子类型
  string orderdetail = 20;

  //收货地址城市
  string province = 21;
  //收货地址城市
  string city = 22;
  //收货地址区域
  string region = 23;
}

message OrderGoodsDetail {
  string id = 1;
  //订单编号(guid)
  string orderid = 2;
  //商品编号(货号)
  string goodsid = 3;
  //商品主编码
  string barcode = 4;
  //商品名称
  string name = 5;
  //商品单价单位（分）
  int32 univalence = 6;
  //商品售价单位（分）
  int32 sellprice = 7;
  //商品数量
  int32 quantity = 8;
  //商品销售单位
  string unit = 9;
  //商品适用分院（0--所有分院，其他对应分院编号）
  string applyhospitalid = 10;
  // 1-不用核销 2-需要核销 3-已核销
  int32 chargeoff = 11;
  //核销码
  string chargeoffcode = 12;
  //核销对象-分院编号
  string chargeoffhospitalId = 13;
  //核销时间
  string chargeofftime = 14;
  //创建时间
  string createtime = 15;
  //最后操作时间
  string lasttime = 16;
  //来源 从请求头获取
  int32 source = 17;
  // UA 从请求头获取
  int32 useragent = 18;
  //核销对象-用户id
  string chargeoffmemberid = 19;
  //商品图片文件
  string goodsimage = 20;
}

//根据条件获取仓库信息（V3.1版本的需要）请求参数
message GetWarehouseInfoByConditionRequest {
  //财务编码
  string finance_code = 1;
  //仓库类型  3-门店仓，4-前置仓
  int32 category = 2;
}
//根据条件获取仓库信息（V3.1版本的需要）响应参数
message GetWarehouseInfoByConditionResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //仓库信息
  repeated WarehouseList Warehouseinfo = 4;
}

//前置仓关联的门店信息 响应参数
message GetWarehouseRelationResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //总行数
  int32 totalCount = 4;
  //仓库信息
  repeated WarehouseRelationList WarehouseRelationAarray = 5;
}

message WarehouseRelationList {
  //仓库ID
  int32 id = 1;
  //仓库名称
  string name = 2;
  //门店ID
  string shop_id = 3;
}

//仓库操作日志请求参数
message WarehouseLogRequest {
  //开始时间
  string starttime = 1;
  // 结束时间
  string endtime = 2;
  //页码
  int32 pageindex = 3;
  //每页行数
  int32 pagesize = 4;
}

//仓库操作日志列表返回数据
message WarehouseLogResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //总行数
  int32 totalCount = 4;
  //仓库数组
  repeated WarehouseLog WarehouseLogAarray = 5;
}

//仓库操作日志列表数据集
message WarehouseLog {
  // ID
  int32 id = 1;
  //操作内容
  string content = 2;
  // 操作人
  string name = 3;
  // 操作时间
  string createdate = 4;
}

// 获取门店绑定信息
message ShopBindInfoRequest {
  //渠道 : 1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
  int32 channel_id = 1;
  //绑定仓库类型 : 3门店仓 4前置仓 5前置虚拟仓
  int32 bind_type = 2;
  //门店名称 or 门店财务编码
  string search = 3;
  //页
  int32 page_index = 4;
  //页大小
  int32 page_size = 5;
}

message ShopBindInfoRespond {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //总行数
  int32 totalCount = 4;
  //仓库详细
  repeated ShopBindInfo info = 5;
}

message ShopBindInfo {
  // ID
  int32 id = 1;
  //渠道 : 2美团
  int32 channel_id = 2;
  //门店ID
  string shop_id = 3;
  //门店名称
  string shop_name = 4;
  //仓库id
  int32 warehouse_id = 5;
  //仓库名称
  string warehouse_name = 6;
  //仓库编码
  string code = 7;
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓 5-前置虚拟仓)
  int32 category = 8;
  //A8客户编码
  string custom_code =9;
}

// 获取门店仓库信息
message ShopBindWarehouseReq {
  //渠道 : 1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提
  int32 channel_id = 1;
  //绑定仓库类型 : (1-电商仓，2-区域仓，3-门店仓，4-前置仓 5-前置虚拟仓 6-门店前置仓)
  int32 bind_type = 2;
  //门店名称 or 门店财务编码
  string shop_search = 3;
  //仓库名称 or 仓库编码
  string warehouse_search = 4;
  //页
  int32 page_index = 5;
  //页大小
  int32 page_size = 6;
}

message ShopBindWarehouseRes {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //总行数
  int32 totalCount = 4;
  //仓库详细
  repeated ShopBindWarehouseInfo info = 5;
}

message ShopBindWarehouseInfo {
  // ID
  int32 id = 1;
  //渠道 : 2美团
  int32 channel_id = 2;
  //门店ID
  string shop_id = 3;
  //门店名称
  string shop_name = 4;
  //仓库id
  int32 warehouse_id = 5;
  //仓库名称
  string warehouse_name = 6;
  //仓库编码
  string code = 7;
  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓 5-前置虚拟仓)
  int32 category = 8;
  //A8客户编码
  string custom_code =9;
  //阿闻外卖
  string awen_delivery_warehourse =10;
  //阿闻自提
  string awen_pickup_warehourse =11;
  //美团
  string mt_warehourse =12;
  //饿了么
  string ele_warehourse =13;
  //京东到家
  string jd_warehourse =14;
  //互联网医院
  string hospitals_warehourse =15;
}

message warehouse_info{
  int32 channel_id =1;
  string name =2;
  string code =3;
  int32 category =4;
  string shop_id =5;
  int32 warehouse_id =6;
}

// 使用店铺id获取其绑定仓库信息
message ShopBindInfoByShopIdRequest {
  //门店id
  repeated string shop_id = 1;
  //渠道 1美团
  int32 channel_id = 2;
}

message ShopBindInfoByShopIdRespond {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;

  repeated ShopBindInfo info = 4;
}

message BindShopsRequest {
  //渠道 1美团
  int32 channel_id = 1;
}

message BindShopsRespond {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;

  repeated BindInfo info = 4;
}

message BindInfo {
  int32 channel = 1;
  //绑定门店仓的门店数量
  int32 shop_num = 2;
  //绑定前置仓的门店数量
  int32 shop_num_front = 3;
  //绑定前置虚拟仓的门店数量
  int32 shop_num_virtual = 4;
  // 绑定电商仓门店数量
  int32 shop_num_ds = 5;
}

message StoreWarehouseRelationShopRequest {
  //渠道 : 2美团
  int32 channel_id = 1;
  // 店铺绑定仓库信息
  repeated WarehouseRelationShop wrs = 2;
}

message WarehouseRelationShop {
  //门店ID
  string shop_id = 1;
  //门店名称
  string shop_name = 2;
  //仓库id
  int32 warehouse_id = 3;
  //仓库名称
  string warehouse_name = 4;
  //仓库类型 4前置仓 5前置虚拟仓
  int32 category = 5;
  //仓库编码
  string code = 6;
}

message StoreWarehouseRelationShopRespond {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  // 失败的shop_id
  repeated string shop_ids = 4;
}

message GetA8WareHouseListRequest {
  string BeginTime = 1;
  string EndTime = 2;
  int32 PageIndex = 3;
  int32 PageSize = 4;
  string Condition = 5;
  int32 IsAll = 6;
}

message GetA8WareHouseListResponse {
  int32 totalCount = 1;
  repeated A8WareHouse data = 2;
}
message A8WareHouse {
  int32 kid = 1;
  string typeId = 2;
  string usercode = 3;
  string fullnameStype = 4;
  string fullnameStock = 5;
  string address = 6;
}

//根据code取仓库信息-- 请求参数
message GetWarehouseInfoByCodeRequest {
  string code = 1;
  int32 category = 2;
}

//根据财务编码获取仓库信息-- 响应参数
message GetWarehouseInfoByCodeResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //仓库信息
  WarehouseList Warehouseinfo = 4;
}

message SyncOmsWarehouseRequest {
  //仓库编号
  string code = 1;

  //仓库名称
  string name = 2;

  //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
  int32 category = 3;

  //仓库地址
  string address = 4;

  //仓库联系人
  string contacts = 5;

  //仓库联系方式
  string tel = 6;

  //第三方仓库ID
  string thirdid = 7;
}

//店铺关联所有仓库列表请求参数
message ShopWarehouseListRequest {
  //财务编码
  string ShopId = 1;
}
