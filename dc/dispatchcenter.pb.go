// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dc/dispatchcenter.proto

package dc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//移除渠道绑定关系
type RemoveShopRequest struct {
	//渠道
	ChannelId int32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//门店编码
	ShopId               string   `protobuf:"bytes,2,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveShopRequest) Reset()         { *m = RemoveShopRequest{} }
func (m *RemoveShopRequest) String() string { return proto.CompactTextString(m) }
func (*RemoveShopRequest) ProtoMessage()    {}
func (*RemoveShopRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{0}
}

func (m *RemoveShopRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveShopRequest.Unmarshal(m, b)
}
func (m *RemoveShopRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveShopRequest.Marshal(b, m, deterministic)
}
func (m *RemoveShopRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveShopRequest.Merge(m, src)
}
func (m *RemoveShopRequest) XXX_Size() int {
	return xxx_messageInfo_RemoveShopRequest.Size(m)
}
func (m *RemoveShopRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveShopRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveShopRequest proto.InternalMessageInfo

func (m *RemoveShopRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveShopRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

type RemoveShopRespond struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveShopRespond) Reset()         { *m = RemoveShopRespond{} }
func (m *RemoveShopRespond) String() string { return proto.CompactTextString(m) }
func (*RemoveShopRespond) ProtoMessage()    {}
func (*RemoveShopRespond) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{1}
}

func (m *RemoveShopRespond) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveShopRespond.Unmarshal(m, b)
}
func (m *RemoveShopRespond) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveShopRespond.Marshal(b, m, deterministic)
}
func (m *RemoveShopRespond) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveShopRespond.Merge(m, src)
}
func (m *RemoveShopRespond) XXX_Size() int {
	return xxx_messageInfo_RemoveShopRespond.Size(m)
}
func (m *RemoveShopRespond) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveShopRespond.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveShopRespond proto.InternalMessageInfo

func (m *RemoveShopRespond) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RemoveShopRespond) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RemoveShopRespond) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type InitShopDataRequest struct {
	//1 初始渠道关系; 2启用全部门店仓
	Step                 int32    `protobuf:"varint,1,opt,name=step,proto3" json:"step"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InitShopDataRequest) Reset()         { *m = InitShopDataRequest{} }
func (m *InitShopDataRequest) String() string { return proto.CompactTextString(m) }
func (*InitShopDataRequest) ProtoMessage()    {}
func (*InitShopDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{2}
}

func (m *InitShopDataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitShopDataRequest.Unmarshal(m, b)
}
func (m *InitShopDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitShopDataRequest.Marshal(b, m, deterministic)
}
func (m *InitShopDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitShopDataRequest.Merge(m, src)
}
func (m *InitShopDataRequest) XXX_Size() int {
	return xxx_messageInfo_InitShopDataRequest.Size(m)
}
func (m *InitShopDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InitShopDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InitShopDataRequest proto.InternalMessageInfo

func (m *InitShopDataRequest) GetStep() int32 {
	if m != nil {
		return m.Step
	}
	return 0
}

type InitShopDataRespond struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	// 成功的信息
	Info                 []*SWInfo `protobuf:"bytes,4,rep,name=info,proto3" json:"info"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *InitShopDataRespond) Reset()         { *m = InitShopDataRespond{} }
func (m *InitShopDataRespond) String() string { return proto.CompactTextString(m) }
func (*InitShopDataRespond) ProtoMessage()    {}
func (*InitShopDataRespond) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{3}
}

func (m *InitShopDataRespond) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitShopDataRespond.Unmarshal(m, b)
}
func (m *InitShopDataRespond) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitShopDataRespond.Marshal(b, m, deterministic)
}
func (m *InitShopDataRespond) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitShopDataRespond.Merge(m, src)
}
func (m *InitShopDataRespond) XXX_Size() int {
	return xxx_messageInfo_InitShopDataRespond.Size(m)
}
func (m *InitShopDataRespond) XXX_DiscardUnknown() {
	xxx_messageInfo_InitShopDataRespond.DiscardUnknown(m)
}

var xxx_messageInfo_InitShopDataRespond proto.InternalMessageInfo

func (m *InitShopDataRespond) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *InitShopDataRespond) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *InitShopDataRespond) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *InitShopDataRespond) GetInfo() []*SWInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SWInfo struct {
	ShopId               string   `protobuf:"bytes,1,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	WareHouseId          int32    `protobuf:"varint,2,opt,name=ware_house_id,json=wareHouseId,proto3" json:"ware_house_id"`
	ShopName             string   `protobuf:"bytes,3,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	WarehouseName        string   `protobuf:"bytes,4,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	ChannelId            int32    `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SWInfo) Reset()         { *m = SWInfo{} }
func (m *SWInfo) String() string { return proto.CompactTextString(m) }
func (*SWInfo) ProtoMessage()    {}
func (*SWInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{4}
}

func (m *SWInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SWInfo.Unmarshal(m, b)
}
func (m *SWInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SWInfo.Marshal(b, m, deterministic)
}
func (m *SWInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SWInfo.Merge(m, src)
}
func (m *SWInfo) XXX_Size() int {
	return xxx_messageInfo_SWInfo.Size(m)
}
func (m *SWInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SWInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SWInfo proto.InternalMessageInfo

func (m *SWInfo) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *SWInfo) GetWareHouseId() int32 {
	if m != nil {
		return m.WareHouseId
	}
	return 0
}

func (m *SWInfo) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *SWInfo) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

func (m *SWInfo) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 查询门店是否绑定了前置虚拟仓
type GetShopWarehouseInfoByFinanceCodeRequest struct {
	ChannelId            int32    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	FinanceCode          string   `protobuf:"bytes,2,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShopWarehouseInfoByFinanceCodeRequest) Reset() {
	*m = GetShopWarehouseInfoByFinanceCodeRequest{}
}
func (m *GetShopWarehouseInfoByFinanceCodeRequest) String() string { return proto.CompactTextString(m) }
func (*GetShopWarehouseInfoByFinanceCodeRequest) ProtoMessage()    {}
func (*GetShopWarehouseInfoByFinanceCodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{5}
}

func (m *GetShopWarehouseInfoByFinanceCodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeRequest.Unmarshal(m, b)
}
func (m *GetShopWarehouseInfoByFinanceCodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeRequest.Marshal(b, m, deterministic)
}
func (m *GetShopWarehouseInfoByFinanceCodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeRequest.Merge(m, src)
}
func (m *GetShopWarehouseInfoByFinanceCodeRequest) XXX_Size() int {
	return xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeRequest.Size(m)
}
func (m *GetShopWarehouseInfoByFinanceCodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeRequest proto.InternalMessageInfo

func (m *GetShopWarehouseInfoByFinanceCodeRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetShopWarehouseInfoByFinanceCodeRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

type GetShopWarehouseInfoByFinanceCodeResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//仓库信息
	Warehouseinfo        *WarehouseList `protobuf:"bytes,4,opt,name=Warehouseinfo,proto3" json:"Warehouseinfo"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetShopWarehouseInfoByFinanceCodeResponse) Reset() {
	*m = GetShopWarehouseInfoByFinanceCodeResponse{}
}
func (m *GetShopWarehouseInfoByFinanceCodeResponse) String() string { return proto.CompactTextString(m) }
func (*GetShopWarehouseInfoByFinanceCodeResponse) ProtoMessage()    {}
func (*GetShopWarehouseInfoByFinanceCodeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{6}
}

func (m *GetShopWarehouseInfoByFinanceCodeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeResponse.Unmarshal(m, b)
}
func (m *GetShopWarehouseInfoByFinanceCodeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeResponse.Marshal(b, m, deterministic)
}
func (m *GetShopWarehouseInfoByFinanceCodeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeResponse.Merge(m, src)
}
func (m *GetShopWarehouseInfoByFinanceCodeResponse) XXX_Size() int {
	return xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeResponse.Size(m)
}
func (m *GetShopWarehouseInfoByFinanceCodeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShopWarehouseInfoByFinanceCodeResponse proto.InternalMessageInfo

func (m *GetShopWarehouseInfoByFinanceCodeResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetShopWarehouseInfoByFinanceCodeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetShopWarehouseInfoByFinanceCodeResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetShopWarehouseInfoByFinanceCodeResponse) GetWarehouseinfo() *WarehouseList {
	if m != nil {
		return m.Warehouseinfo
	}
	return nil
}

//根据分类获取门店列表-- 请求参数
type GetStoreListByCategoryRequest struct {
	// 3门店仓 4前置（虚拟）仓
	Category int32 `protobuf:"varint,1,opt,name=category,proto3" json:"category"`
	// 渠道id，1-阿闻到家 2-美团 3-饿了么 4-京东到家
	ChannelId            int32    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoreListByCategoryRequest) Reset()         { *m = GetStoreListByCategoryRequest{} }
func (m *GetStoreListByCategoryRequest) String() string { return proto.CompactTextString(m) }
func (*GetStoreListByCategoryRequest) ProtoMessage()    {}
func (*GetStoreListByCategoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{7}
}

func (m *GetStoreListByCategoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoreListByCategoryRequest.Unmarshal(m, b)
}
func (m *GetStoreListByCategoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoreListByCategoryRequest.Marshal(b, m, deterministic)
}
func (m *GetStoreListByCategoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoreListByCategoryRequest.Merge(m, src)
}
func (m *GetStoreListByCategoryRequest) XXX_Size() int {
	return xxx_messageInfo_GetStoreListByCategoryRequest.Size(m)
}
func (m *GetStoreListByCategoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoreListByCategoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoreListByCategoryRequest proto.InternalMessageInfo

func (m *GetStoreListByCategoryRequest) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *GetStoreListByCategoryRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

//根据分类获取门店列表-- 响应参数
type GetStoreListByCategoryResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//仓库信息
	FinanceCode          []string `protobuf:"bytes,4,rep,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoreListByCategoryResponse) Reset()         { *m = GetStoreListByCategoryResponse{} }
func (m *GetStoreListByCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*GetStoreListByCategoryResponse) ProtoMessage()    {}
func (*GetStoreListByCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{8}
}

func (m *GetStoreListByCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoreListByCategoryResponse.Unmarshal(m, b)
}
func (m *GetStoreListByCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoreListByCategoryResponse.Marshal(b, m, deterministic)
}
func (m *GetStoreListByCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoreListByCategoryResponse.Merge(m, src)
}
func (m *GetStoreListByCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_GetStoreListByCategoryResponse.Size(m)
}
func (m *GetStoreListByCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoreListByCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoreListByCategoryResponse proto.InternalMessageInfo

func (m *GetStoreListByCategoryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetStoreListByCategoryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetStoreListByCategoryResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetStoreListByCategoryResponse) GetFinanceCode() []string {
	if m != nil {
		return m.FinanceCode
	}
	return nil
}

type GetWarehouseInfoByFanceCodesRequest struct {
	FinanceCode []string `protobuf:"bytes,1,rep,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提
	ChannelId            int32    `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarehouseInfoByFanceCodesRequest) Reset()         { *m = GetWarehouseInfoByFanceCodesRequest{} }
func (m *GetWarehouseInfoByFanceCodesRequest) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseInfoByFanceCodesRequest) ProtoMessage()    {}
func (*GetWarehouseInfoByFanceCodesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{9}
}

func (m *GetWarehouseInfoByFanceCodesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodesRequest.Unmarshal(m, b)
}
func (m *GetWarehouseInfoByFanceCodesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodesRequest.Marshal(b, m, deterministic)
}
func (m *GetWarehouseInfoByFanceCodesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseInfoByFanceCodesRequest.Merge(m, src)
}
func (m *GetWarehouseInfoByFanceCodesRequest) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodesRequest.Size(m)
}
func (m *GetWarehouseInfoByFanceCodesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseInfoByFanceCodesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseInfoByFanceCodesRequest proto.InternalMessageInfo

func (m *GetWarehouseInfoByFanceCodesRequest) GetFinanceCode() []string {
	if m != nil {
		return m.FinanceCode
	}
	return nil
}

func (m *GetWarehouseInfoByFanceCodesRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetWarehouseInfoByFanceCodesResponse struct {
	Data                 []*WarehouseList `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetWarehouseInfoByFanceCodesResponse) Reset()         { *m = GetWarehouseInfoByFanceCodesResponse{} }
func (m *GetWarehouseInfoByFanceCodesResponse) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseInfoByFanceCodesResponse) ProtoMessage()    {}
func (*GetWarehouseInfoByFanceCodesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{10}
}

func (m *GetWarehouseInfoByFanceCodesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodesResponse.Unmarshal(m, b)
}
func (m *GetWarehouseInfoByFanceCodesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodesResponse.Marshal(b, m, deterministic)
}
func (m *GetWarehouseInfoByFanceCodesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseInfoByFanceCodesResponse.Merge(m, src)
}
func (m *GetWarehouseInfoByFanceCodesResponse) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodesResponse.Size(m)
}
func (m *GetWarehouseInfoByFanceCodesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseInfoByFanceCodesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseInfoByFanceCodesResponse proto.InternalMessageInfo

func (m *GetWarehouseInfoByFanceCodesResponse) GetData() []*WarehouseList {
	if m != nil {
		return m.Data
	}
	return nil
}

//根据财务编码获取仓库信息-- 请求参数
type GetWarehouseInfoByFanceCodeRequest struct {
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	WarehouseId int32  `protobuf:"varint,2,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	//1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提
	ChannelId            int32    `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarehouseInfoByFanceCodeRequest) Reset()         { *m = GetWarehouseInfoByFanceCodeRequest{} }
func (m *GetWarehouseInfoByFanceCodeRequest) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseInfoByFanceCodeRequest) ProtoMessage()    {}
func (*GetWarehouseInfoByFanceCodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{11}
}

func (m *GetWarehouseInfoByFanceCodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodeRequest.Unmarshal(m, b)
}
func (m *GetWarehouseInfoByFanceCodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodeRequest.Marshal(b, m, deterministic)
}
func (m *GetWarehouseInfoByFanceCodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseInfoByFanceCodeRequest.Merge(m, src)
}
func (m *GetWarehouseInfoByFanceCodeRequest) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodeRequest.Size(m)
}
func (m *GetWarehouseInfoByFanceCodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseInfoByFanceCodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseInfoByFanceCodeRequest proto.InternalMessageInfo

func (m *GetWarehouseInfoByFanceCodeRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GetWarehouseInfoByFanceCodeRequest) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *GetWarehouseInfoByFanceCodeRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

//根据财务编码获取仓库信息-- 响应参数
type GetWarehouseInfoByFanceCodeResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//仓库信息
	Warehouseinfo        *WarehouseList `protobuf:"bytes,4,opt,name=Warehouseinfo,proto3" json:"Warehouseinfo"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetWarehouseInfoByFanceCodeResponse) Reset()         { *m = GetWarehouseInfoByFanceCodeResponse{} }
func (m *GetWarehouseInfoByFanceCodeResponse) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseInfoByFanceCodeResponse) ProtoMessage()    {}
func (*GetWarehouseInfoByFanceCodeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{12}
}

func (m *GetWarehouseInfoByFanceCodeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodeResponse.Unmarshal(m, b)
}
func (m *GetWarehouseInfoByFanceCodeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodeResponse.Marshal(b, m, deterministic)
}
func (m *GetWarehouseInfoByFanceCodeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseInfoByFanceCodeResponse.Merge(m, src)
}
func (m *GetWarehouseInfoByFanceCodeResponse) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseInfoByFanceCodeResponse.Size(m)
}
func (m *GetWarehouseInfoByFanceCodeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseInfoByFanceCodeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseInfoByFanceCodeResponse proto.InternalMessageInfo

func (m *GetWarehouseInfoByFanceCodeResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetWarehouseInfoByFanceCodeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetWarehouseInfoByFanceCodeResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetWarehouseInfoByFanceCodeResponse) GetWarehouseinfo() *WarehouseList {
	if m != nil {
		return m.Warehouseinfo
	}
	return nil
}

///仓库(前置仓)关联门店 -- 请求参数
type WarehouseRelationRequest struct {
	//前置仓id
	WarehouseId int32 `protobuf:"varint,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	//门店信息数组
	ShopInfoList         []*ShopInfo `protobuf:"bytes,2,rep,name=ShopInfoList,proto3" json:"ShopInfoList"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *WarehouseRelationRequest) Reset()         { *m = WarehouseRelationRequest{} }
func (m *WarehouseRelationRequest) String() string { return proto.CompactTextString(m) }
func (*WarehouseRelationRequest) ProtoMessage()    {}
func (*WarehouseRelationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{13}
}

func (m *WarehouseRelationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseRelationRequest.Unmarshal(m, b)
}
func (m *WarehouseRelationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseRelationRequest.Marshal(b, m, deterministic)
}
func (m *WarehouseRelationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseRelationRequest.Merge(m, src)
}
func (m *WarehouseRelationRequest) XXX_Size() int {
	return xxx_messageInfo_WarehouseRelationRequest.Size(m)
}
func (m *WarehouseRelationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseRelationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseRelationRequest proto.InternalMessageInfo

func (m *WarehouseRelationRequest) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *WarehouseRelationRequest) GetShopInfoList() []*ShopInfo {
	if m != nil {
		return m.ShopInfoList
	}
	return nil
}

//门店信息
type ShopInfo struct {
	//门店的财务编码
	ShopId string `protobuf:"bytes,1,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//门店的名称
	ShopName             string   `protobuf:"bytes,2,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopInfo) Reset()         { *m = ShopInfo{} }
func (m *ShopInfo) String() string { return proto.CompactTextString(m) }
func (*ShopInfo) ProtoMessage()    {}
func (*ShopInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{14}
}

func (m *ShopInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopInfo.Unmarshal(m, b)
}
func (m *ShopInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopInfo.Marshal(b, m, deterministic)
}
func (m *ShopInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopInfo.Merge(m, src)
}
func (m *ShopInfo) XXX_Size() int {
	return xxx_messageInfo_ShopInfo.Size(m)
}
func (m *ShopInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShopInfo proto.InternalMessageInfo

func (m *ShopInfo) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *ShopInfo) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

// 区域仓关联前置仓
type PreposeWarehouseRelationRequest struct {
	// 区域仓id
	RegionId int32 `protobuf:"varint,1,opt,name=RegionId,proto3" json:"RegionId"`
	// 前置仓id列表
	PreposeIdList        []int32  `protobuf:"varint,2,rep,packed,name=PreposeIdList,proto3" json:"PreposeIdList"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreposeWarehouseRelationRequest) Reset()         { *m = PreposeWarehouseRelationRequest{} }
func (m *PreposeWarehouseRelationRequest) String() string { return proto.CompactTextString(m) }
func (*PreposeWarehouseRelationRequest) ProtoMessage()    {}
func (*PreposeWarehouseRelationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{15}
}

func (m *PreposeWarehouseRelationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreposeWarehouseRelationRequest.Unmarshal(m, b)
}
func (m *PreposeWarehouseRelationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreposeWarehouseRelationRequest.Marshal(b, m, deterministic)
}
func (m *PreposeWarehouseRelationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreposeWarehouseRelationRequest.Merge(m, src)
}
func (m *PreposeWarehouseRelationRequest) XXX_Size() int {
	return xxx_messageInfo_PreposeWarehouseRelationRequest.Size(m)
}
func (m *PreposeWarehouseRelationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PreposeWarehouseRelationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PreposeWarehouseRelationRequest proto.InternalMessageInfo

func (m *PreposeWarehouseRelationRequest) GetRegionId() int32 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *PreposeWarehouseRelationRequest) GetPreposeIdList() []int32 {
	if m != nil {
		return m.PreposeIdList
	}
	return nil
}

//根据仓库id获取已绑定的门店信息列表--请求参数
type WarehouseRelationListRequest struct {
	//仓库id
	WarehouseId          int32    `protobuf:"varint,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseRelationListRequest) Reset()         { *m = WarehouseRelationListRequest{} }
func (m *WarehouseRelationListRequest) String() string { return proto.CompactTextString(m) }
func (*WarehouseRelationListRequest) ProtoMessage()    {}
func (*WarehouseRelationListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{16}
}

func (m *WarehouseRelationListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseRelationListRequest.Unmarshal(m, b)
}
func (m *WarehouseRelationListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseRelationListRequest.Marshal(b, m, deterministic)
}
func (m *WarehouseRelationListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseRelationListRequest.Merge(m, src)
}
func (m *WarehouseRelationListRequest) XXX_Size() int {
	return xxx_messageInfo_WarehouseRelationListRequest.Size(m)
}
func (m *WarehouseRelationListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseRelationListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseRelationListRequest proto.InternalMessageInfo

func (m *WarehouseRelationListRequest) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

//根据仓库id获取已绑定的门店信息列表--响应参数
type WarehouseRelationListResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//门店信息数组
	ShopInfoList         []*ShopInfo `protobuf:"bytes,4,rep,name=ShopInfoList,proto3" json:"ShopInfoList"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *WarehouseRelationListResponse) Reset()         { *m = WarehouseRelationListResponse{} }
func (m *WarehouseRelationListResponse) String() string { return proto.CompactTextString(m) }
func (*WarehouseRelationListResponse) ProtoMessage()    {}
func (*WarehouseRelationListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{17}
}

func (m *WarehouseRelationListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseRelationListResponse.Unmarshal(m, b)
}
func (m *WarehouseRelationListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseRelationListResponse.Marshal(b, m, deterministic)
}
func (m *WarehouseRelationListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseRelationListResponse.Merge(m, src)
}
func (m *WarehouseRelationListResponse) XXX_Size() int {
	return xxx_messageInfo_WarehouseRelationListResponse.Size(m)
}
func (m *WarehouseRelationListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseRelationListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseRelationListResponse proto.InternalMessageInfo

func (m *WarehouseRelationListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *WarehouseRelationListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *WarehouseRelationListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *WarehouseRelationListResponse) GetShopInfoList() []*ShopInfo {
	if m != nil {
		return m.ShopInfoList
	}
	return nil
}

// 根据仓库id获取已关联的前置仓信息列表响应
type RegionRelationListRespon struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 数据
	Data                 []*PreposeWarehouseInfo `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *RegionRelationListRespon) Reset()         { *m = RegionRelationListRespon{} }
func (m *RegionRelationListRespon) String() string { return proto.CompactTextString(m) }
func (*RegionRelationListRespon) ProtoMessage()    {}
func (*RegionRelationListRespon) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{18}
}

func (m *RegionRelationListRespon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegionRelationListRespon.Unmarshal(m, b)
}
func (m *RegionRelationListRespon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegionRelationListRespon.Marshal(b, m, deterministic)
}
func (m *RegionRelationListRespon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionRelationListRespon.Merge(m, src)
}
func (m *RegionRelationListRespon) XXX_Size() int {
	return xxx_messageInfo_RegionRelationListRespon.Size(m)
}
func (m *RegionRelationListRespon) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionRelationListRespon.DiscardUnknown(m)
}

var xxx_messageInfo_RegionRelationListRespon proto.InternalMessageInfo

func (m *RegionRelationListRespon) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RegionRelationListRespon) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *RegionRelationListRespon) GetData() []*PreposeWarehouseInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

// 前置仓信息
type PreposeWarehouseInfo struct {
	// 仓库id
	WarehouseId int32 `protobuf:"varint,1,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	// 仓库编码
	WarehouseCode string `protobuf:"bytes,2,opt,name=warehouse_code,json=warehouseCode,proto3" json:"warehouse_code"`
	// 仓库名称
	WarehouseName        string   `protobuf:"bytes,3,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreposeWarehouseInfo) Reset()         { *m = PreposeWarehouseInfo{} }
func (m *PreposeWarehouseInfo) String() string { return proto.CompactTextString(m) }
func (*PreposeWarehouseInfo) ProtoMessage()    {}
func (*PreposeWarehouseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{19}
}

func (m *PreposeWarehouseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreposeWarehouseInfo.Unmarshal(m, b)
}
func (m *PreposeWarehouseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreposeWarehouseInfo.Marshal(b, m, deterministic)
}
func (m *PreposeWarehouseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreposeWarehouseInfo.Merge(m, src)
}
func (m *PreposeWarehouseInfo) XXX_Size() int {
	return xxx_messageInfo_PreposeWarehouseInfo.Size(m)
}
func (m *PreposeWarehouseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PreposeWarehouseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PreposeWarehouseInfo proto.InternalMessageInfo

func (m *PreposeWarehouseInfo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *PreposeWarehouseInfo) GetWarehouseCode() string {
	if m != nil {
		return m.WarehouseCode
	}
	return ""
}

func (m *PreposeWarehouseInfo) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

type Empty struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Empty) Reset()         { *m = Empty{} }
func (m *Empty) String() string { return proto.CompactTextString(m) }
func (*Empty) ProtoMessage()    {}
func (*Empty) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{20}
}

func (m *Empty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Empty.Unmarshal(m, b)
}
func (m *Empty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Empty.Marshal(b, m, deterministic)
}
func (m *Empty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Empty.Merge(m, src)
}
func (m *Empty) XXX_Size() int {
	return xxx_messageInfo_Empty.Size(m)
}
func (m *Empty) XXX_DiscardUnknown() {
	xxx_messageInfo_Empty.DiscardUnknown(m)
}

var xxx_messageInfo_Empty proto.InternalMessageInfo

// 前置仓关联的城市列表
type PreposeCiytListResponse struct {
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 数据
	Data                 []string `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreposeCiytListResponse) Reset()         { *m = PreposeCiytListResponse{} }
func (m *PreposeCiytListResponse) String() string { return proto.CompactTextString(m) }
func (*PreposeCiytListResponse) ProtoMessage()    {}
func (*PreposeCiytListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{21}
}

func (m *PreposeCiytListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreposeCiytListResponse.Unmarshal(m, b)
}
func (m *PreposeCiytListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreposeCiytListResponse.Marshal(b, m, deterministic)
}
func (m *PreposeCiytListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreposeCiytListResponse.Merge(m, src)
}
func (m *PreposeCiytListResponse) XXX_Size() int {
	return xxx_messageInfo_PreposeCiytListResponse.Size(m)
}
func (m *PreposeCiytListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PreposeCiytListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PreposeCiytListResponse proto.InternalMessageInfo

func (m *PreposeCiytListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PreposeCiytListResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *PreposeCiytListResponse) GetData() []string {
	if m != nil {
		return m.Data
	}
	return nil
}

//通用返回
type BaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{22}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type DemolitionOrderResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//仓库数组
	WarehouseToGoodsList []*WarehouseToGoods `protobuf:"bytes,4,rep,name=WarehouseToGoodsList,proto3" json:"WarehouseToGoodsList"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *DemolitionOrderResponse) Reset()         { *m = DemolitionOrderResponse{} }
func (m *DemolitionOrderResponse) String() string { return proto.CompactTextString(m) }
func (*DemolitionOrderResponse) ProtoMessage()    {}
func (*DemolitionOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{23}
}

func (m *DemolitionOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DemolitionOrderResponse.Unmarshal(m, b)
}
func (m *DemolitionOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DemolitionOrderResponse.Marshal(b, m, deterministic)
}
func (m *DemolitionOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DemolitionOrderResponse.Merge(m, src)
}
func (m *DemolitionOrderResponse) XXX_Size() int {
	return xxx_messageInfo_DemolitionOrderResponse.Size(m)
}
func (m *DemolitionOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DemolitionOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DemolitionOrderResponse proto.InternalMessageInfo

func (m *DemolitionOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DemolitionOrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DemolitionOrderResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *DemolitionOrderResponse) GetWarehouseToGoodsList() []*WarehouseToGoods {
	if m != nil {
		return m.WarehouseToGoodsList
	}
	return nil
}

//商品对应的仓库集合
type WarehouseToGoods struct {
	//仓库id
	Warehouseid int32 `protobuf:"varint,1,opt,name=warehouseid,proto3" json:"warehouseid"`
	//订单id
	Orderid string `protobuf:"bytes,2,opt,name=orderid,proto3" json:"orderid"`
	//商品数量
	Quantity int32 `protobuf:"varint,3,opt,name=Quantity,proto3" json:"Quantity"`
	//商品Id
	Goodsid string `protobuf:"bytes,4,opt,name=goodsid,proto3" json:"goodsid"`
	//第三方仓库id  例如A8Id ,管易Id
	Thirdid string `protobuf:"bytes,5,opt,name=thirdid,proto3" json:"thirdid"`
	//第三方仓库code  例如A8 code,管易 code
	Warehousecode        string   `protobuf:"bytes,6,opt,name=warehousecode,proto3" json:"warehousecode"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseToGoods) Reset()         { *m = WarehouseToGoods{} }
func (m *WarehouseToGoods) String() string { return proto.CompactTextString(m) }
func (*WarehouseToGoods) ProtoMessage()    {}
func (*WarehouseToGoods) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{24}
}

func (m *WarehouseToGoods) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseToGoods.Unmarshal(m, b)
}
func (m *WarehouseToGoods) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseToGoods.Marshal(b, m, deterministic)
}
func (m *WarehouseToGoods) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseToGoods.Merge(m, src)
}
func (m *WarehouseToGoods) XXX_Size() int {
	return xxx_messageInfo_WarehouseToGoods.Size(m)
}
func (m *WarehouseToGoods) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseToGoods.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseToGoods proto.InternalMessageInfo

func (m *WarehouseToGoods) GetWarehouseid() int32 {
	if m != nil {
		return m.Warehouseid
	}
	return 0
}

func (m *WarehouseToGoods) GetOrderid() string {
	if m != nil {
		return m.Orderid
	}
	return ""
}

func (m *WarehouseToGoods) GetQuantity() int32 {
	if m != nil {
		return m.Quantity
	}
	return 0
}

func (m *WarehouseToGoods) GetGoodsid() string {
	if m != nil {
		return m.Goodsid
	}
	return ""
}

func (m *WarehouseToGoods) GetThirdid() string {
	if m != nil {
		return m.Thirdid
	}
	return ""
}

func (m *WarehouseToGoods) GetWarehousecode() string {
	if m != nil {
		return m.Warehousecode
	}
	return ""
}

type DataRequest struct {
	DataList             []*TestData `protobuf:"bytes,1,rep,name=DataList,proto3" json:"DataList"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DataRequest) Reset()         { *m = DataRequest{} }
func (m *DataRequest) String() string { return proto.CompactTextString(m) }
func (*DataRequest) ProtoMessage()    {}
func (*DataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{25}
}

func (m *DataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataRequest.Unmarshal(m, b)
}
func (m *DataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataRequest.Marshal(b, m, deterministic)
}
func (m *DataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataRequest.Merge(m, src)
}
func (m *DataRequest) XXX_Size() int {
	return xxx_messageInfo_DataRequest.Size(m)
}
func (m *DataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DataRequest proto.InternalMessageInfo

func (m *DataRequest) GetDataList() []*TestData {
	if m != nil {
		return m.DataList
	}
	return nil
}

type TestData struct {
	//仓库id
	Warehouseid int32 `protobuf:"varint,1,opt,name=warehouseid,proto3" json:"warehouseid"`
	//库存数量
	Store int32 `protobuf:"varint,2,opt,name=store,proto3" json:"store"`
	//商品Id
	Goodsid              string   `protobuf:"bytes,3,opt,name=goodsid,proto3" json:"goodsid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestData) Reset()         { *m = TestData{} }
func (m *TestData) String() string { return proto.CompactTextString(m) }
func (*TestData) ProtoMessage()    {}
func (*TestData) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{26}
}

func (m *TestData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestData.Unmarshal(m, b)
}
func (m *TestData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestData.Marshal(b, m, deterministic)
}
func (m *TestData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestData.Merge(m, src)
}
func (m *TestData) XXX_Size() int {
	return xxx_messageInfo_TestData.Size(m)
}
func (m *TestData) XXX_DiscardUnknown() {
	xxx_messageInfo_TestData.DiscardUnknown(m)
}

var xxx_messageInfo_TestData proto.InternalMessageInfo

func (m *TestData) GetWarehouseid() int32 {
	if m != nil {
		return m.Warehouseid
	}
	return 0
}

func (m *TestData) GetStore() int32 {
	if m != nil {
		return m.Store
	}
	return 0
}

func (m *TestData) GetGoodsid() string {
	if m != nil {
		return m.Goodsid
	}
	return ""
}

//新增仓库
type AddWarehouseRequest struct {
	//仓库编号
	Code    string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Thirdid string `protobuf:"bytes,2,opt,name=thirdid,proto3" json:"thirdid"`
	//仓库名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	//仓库归属(1-A8,2-管易)
	Comefrom int32 `protobuf:"varint,4,opt,name=comefrom,proto3" json:"comefrom"`
	//仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
	Level int32 `protobuf:"varint,5,opt,name=level,proto3" json:"level"`
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
	Category int32 `protobuf:"varint,6,opt,name=category,proto3" json:"category"`
	//仓库地址
	Address string `protobuf:"bytes,7,opt,name=address,proto3" json:"address"`
	//仓库联系人
	Contacts string `protobuf:"bytes,8,opt,name=contacts,proto3" json:"contacts"`
	//仓库联系方式
	Tel string `protobuf:"bytes,9,opt,name=tel,proto3" json:"tel"`
	//所属系统 0:默认,1:ERP,2:子龙
	Subsystem int32 `protobuf:"varint,10,opt,name=subsystem,proto3" json:"subsystem"`
	Ratio     int32 `protobuf:"varint,11,opt,name=ratio,proto3" json:"ratio"`
	//仓库经度
	Lng int64 `protobuf:"varint,12,opt,name=lng,proto3" json:"lng"`
	//仓库纬度
	Lat int64 `protobuf:"varint,13,opt,name=lat,proto3" json:"lat"`
	// 所属区域
	Region string `protobuf:"bytes,14,opt,name=region,proto3" json:"region"`
	// 所属城市
	City string `protobuf:"bytes,15,opt,name=city,proto3" json:"city"`
	// 是否有资质售药 1有、0否
	//int32 sell_drugs = 18; v6.27.2
	WarehouseDelivery    []*WarehouseDelivery `protobuf:"bytes,19,rep,name=warehouse_delivery,json=warehouseDelivery,proto3" json:"warehouse_delivery"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddWarehouseRequest) Reset()         { *m = AddWarehouseRequest{} }
func (m *AddWarehouseRequest) String() string { return proto.CompactTextString(m) }
func (*AddWarehouseRequest) ProtoMessage()    {}
func (*AddWarehouseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{27}
}

func (m *AddWarehouseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWarehouseRequest.Unmarshal(m, b)
}
func (m *AddWarehouseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWarehouseRequest.Marshal(b, m, deterministic)
}
func (m *AddWarehouseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWarehouseRequest.Merge(m, src)
}
func (m *AddWarehouseRequest) XXX_Size() int {
	return xxx_messageInfo_AddWarehouseRequest.Size(m)
}
func (m *AddWarehouseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWarehouseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddWarehouseRequest proto.InternalMessageInfo

func (m *AddWarehouseRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *AddWarehouseRequest) GetThirdid() string {
	if m != nil {
		return m.Thirdid
	}
	return ""
}

func (m *AddWarehouseRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddWarehouseRequest) GetComefrom() int32 {
	if m != nil {
		return m.Comefrom
	}
	return 0
}

func (m *AddWarehouseRequest) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *AddWarehouseRequest) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *AddWarehouseRequest) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *AddWarehouseRequest) GetContacts() string {
	if m != nil {
		return m.Contacts
	}
	return ""
}

func (m *AddWarehouseRequest) GetTel() string {
	if m != nil {
		return m.Tel
	}
	return ""
}

func (m *AddWarehouseRequest) GetSubsystem() int32 {
	if m != nil {
		return m.Subsystem
	}
	return 0
}

func (m *AddWarehouseRequest) GetRatio() int32 {
	if m != nil {
		return m.Ratio
	}
	return 0
}

func (m *AddWarehouseRequest) GetLng() int64 {
	if m != nil {
		return m.Lng
	}
	return 0
}

func (m *AddWarehouseRequest) GetLat() int64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *AddWarehouseRequest) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *AddWarehouseRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *AddWarehouseRequest) GetWarehouseDelivery() []*WarehouseDelivery {
	if m != nil {
		return m.WarehouseDelivery
	}
	return nil
}

type WarehouseByAreaRequest struct {
	//省份
	Province int32 `protobuf:"varint,1,opt,name=province,proto3" json:"province"`
	//仓库归属(1-A8,2-管易)
	Comefrom             int32    `protobuf:"varint,2,opt,name=comefrom,proto3" json:"comefrom"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseByAreaRequest) Reset()         { *m = WarehouseByAreaRequest{} }
func (m *WarehouseByAreaRequest) String() string { return proto.CompactTextString(m) }
func (*WarehouseByAreaRequest) ProtoMessage()    {}
func (*WarehouseByAreaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{28}
}

func (m *WarehouseByAreaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseByAreaRequest.Unmarshal(m, b)
}
func (m *WarehouseByAreaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseByAreaRequest.Marshal(b, m, deterministic)
}
func (m *WarehouseByAreaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseByAreaRequest.Merge(m, src)
}
func (m *WarehouseByAreaRequest) XXX_Size() int {
	return xxx_messageInfo_WarehouseByAreaRequest.Size(m)
}
func (m *WarehouseByAreaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseByAreaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseByAreaRequest proto.InternalMessageInfo

func (m *WarehouseByAreaRequest) GetProvince() int32 {
	if m != nil {
		return m.Province
	}
	return 0
}

func (m *WarehouseByAreaRequest) GetComefrom() int32 {
	if m != nil {
		return m.Comefrom
	}
	return 0
}

type WarehouseByAreaResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//仓库数组
	WarehouseAarray      []*WarehouseList `protobuf:"bytes,4,rep,name=WarehouseAarray,proto3" json:"WarehouseAarray"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WarehouseByAreaResponse) Reset()         { *m = WarehouseByAreaResponse{} }
func (m *WarehouseByAreaResponse) String() string { return proto.CompactTextString(m) }
func (*WarehouseByAreaResponse) ProtoMessage()    {}
func (*WarehouseByAreaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{29}
}

func (m *WarehouseByAreaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseByAreaResponse.Unmarshal(m, b)
}
func (m *WarehouseByAreaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseByAreaResponse.Marshal(b, m, deterministic)
}
func (m *WarehouseByAreaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseByAreaResponse.Merge(m, src)
}
func (m *WarehouseByAreaResponse) XXX_Size() int {
	return xxx_messageInfo_WarehouseByAreaResponse.Size(m)
}
func (m *WarehouseByAreaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseByAreaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseByAreaResponse proto.InternalMessageInfo

func (m *WarehouseByAreaResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *WarehouseByAreaResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *WarehouseByAreaResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *WarehouseByAreaResponse) GetWarehouseAarray() []*WarehouseList {
	if m != nil {
		return m.WarehouseAarray
	}
	return nil
}

//编辑仓库
type EditWarehouseRequest struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//仓库编号
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
	//仓库名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	//仓库归属(1-A8,2-管易)
	Comefrom int32 `protobuf:"varint,4,opt,name=comefrom,proto3" json:"comefrom"`
	//仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
	Level int32 `protobuf:"varint,5,opt,name=level,proto3" json:"level"`
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
	Category int32 `protobuf:"varint,6,opt,name=category,proto3" json:"category"`
	//仓库地址
	Address string `protobuf:"bytes,7,opt,name=address,proto3" json:"address"`
	//仓库联系人
	Contacts string `protobuf:"bytes,8,opt,name=contacts,proto3" json:"contacts"`
	//仓库联系方式
	Tel string `protobuf:"bytes,9,opt,name=tel,proto3" json:"tel"`
	//所属系统 0:默认,1:ERP,2:子龙
	Subsystem int32 `protobuf:"varint,10,opt,name=subsystem,proto3" json:"subsystem"`
	//仓库比例
	Ratio int32 `protobuf:"varint,11,opt,name=ratio,proto3" json:"ratio"`
	//仓库比例
	Status int32 `protobuf:"varint,12,opt,name=status,proto3" json:"status"`
	//第三方仓库ID
	Thirdid string `protobuf:"bytes,13,opt,name=thirdid,proto3" json:"thirdid"`
	//仓库经度
	Lng int64 `protobuf:"varint,14,opt,name=lng,proto3" json:"lng"`
	//仓库纬度
	Lat int64 `protobuf:"varint,15,opt,name=lat,proto3" json:"lat"`
	// 所属区域
	Region string `protobuf:"bytes,16,opt,name=region,proto3" json:"region"`
	// 所属城市
	City string `protobuf:"bytes,17,opt,name=city,proto3" json:"city"`
	// 是否有资质售药 1有、0否
	// int32 sell_drugs = 18;
	WarehouseDelivery    []*WarehouseDelivery `protobuf:"bytes,19,rep,name=warehouse_delivery,json=warehouseDelivery,proto3" json:"warehouse_delivery"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *EditWarehouseRequest) Reset()         { *m = EditWarehouseRequest{} }
func (m *EditWarehouseRequest) String() string { return proto.CompactTextString(m) }
func (*EditWarehouseRequest) ProtoMessage()    {}
func (*EditWarehouseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{30}
}

func (m *EditWarehouseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditWarehouseRequest.Unmarshal(m, b)
}
func (m *EditWarehouseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditWarehouseRequest.Marshal(b, m, deterministic)
}
func (m *EditWarehouseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditWarehouseRequest.Merge(m, src)
}
func (m *EditWarehouseRequest) XXX_Size() int {
	return xxx_messageInfo_EditWarehouseRequest.Size(m)
}
func (m *EditWarehouseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EditWarehouseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EditWarehouseRequest proto.InternalMessageInfo

func (m *EditWarehouseRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *EditWarehouseRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *EditWarehouseRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *EditWarehouseRequest) GetComefrom() int32 {
	if m != nil {
		return m.Comefrom
	}
	return 0
}

func (m *EditWarehouseRequest) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *EditWarehouseRequest) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *EditWarehouseRequest) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *EditWarehouseRequest) GetContacts() string {
	if m != nil {
		return m.Contacts
	}
	return ""
}

func (m *EditWarehouseRequest) GetTel() string {
	if m != nil {
		return m.Tel
	}
	return ""
}

func (m *EditWarehouseRequest) GetSubsystem() int32 {
	if m != nil {
		return m.Subsystem
	}
	return 0
}

func (m *EditWarehouseRequest) GetRatio() int32 {
	if m != nil {
		return m.Ratio
	}
	return 0
}

func (m *EditWarehouseRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *EditWarehouseRequest) GetThirdid() string {
	if m != nil {
		return m.Thirdid
	}
	return ""
}

func (m *EditWarehouseRequest) GetLng() int64 {
	if m != nil {
		return m.Lng
	}
	return 0
}

func (m *EditWarehouseRequest) GetLat() int64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *EditWarehouseRequest) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *EditWarehouseRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *EditWarehouseRequest) GetWarehouseDelivery() []*WarehouseDelivery {
	if m != nil {
		return m.WarehouseDelivery
	}
	return nil
}

//新增仓库配送区域
type WarehouseDelivery struct {
	//仓库选择绑定了的配送主键
	DeliveryId int32 `protobuf:"varint,1,opt,name=delivery_id,json=deliveryId,proto3" json:"delivery_id"`
	//配送的门店编码
	ShopNo               string   `protobuf:"bytes,2,opt,name=shop_no,json=shopNo,proto3" json:"shop_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseDelivery) Reset()         { *m = WarehouseDelivery{} }
func (m *WarehouseDelivery) String() string { return proto.CompactTextString(m) }
func (*WarehouseDelivery) ProtoMessage()    {}
func (*WarehouseDelivery) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{31}
}

func (m *WarehouseDelivery) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseDelivery.Unmarshal(m, b)
}
func (m *WarehouseDelivery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseDelivery.Marshal(b, m, deterministic)
}
func (m *WarehouseDelivery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseDelivery.Merge(m, src)
}
func (m *WarehouseDelivery) XXX_Size() int {
	return xxx_messageInfo_WarehouseDelivery.Size(m)
}
func (m *WarehouseDelivery) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseDelivery.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseDelivery proto.InternalMessageInfo

func (m *WarehouseDelivery) GetDeliveryId() int32 {
	if m != nil {
		return m.DeliveryId
	}
	return 0
}

func (m *WarehouseDelivery) GetShopNo() string {
	if m != nil {
		return m.ShopNo
	}
	return ""
}

//新增仓库配送区域
type AddWarehouseAreaRequest struct {
	WarehouseAreaList []*WarehouseArea `protobuf:"bytes,1,rep,name=WarehouseAreaList,proto3" json:"WarehouseAreaList"`
	//仓库id
	Warehouseid          int32    `protobuf:"varint,2,opt,name=warehouseid,proto3" json:"warehouseid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddWarehouseAreaRequest) Reset()         { *m = AddWarehouseAreaRequest{} }
func (m *AddWarehouseAreaRequest) String() string { return proto.CompactTextString(m) }
func (*AddWarehouseAreaRequest) ProtoMessage()    {}
func (*AddWarehouseAreaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{32}
}

func (m *AddWarehouseAreaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWarehouseAreaRequest.Unmarshal(m, b)
}
func (m *AddWarehouseAreaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWarehouseAreaRequest.Marshal(b, m, deterministic)
}
func (m *AddWarehouseAreaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWarehouseAreaRequest.Merge(m, src)
}
func (m *AddWarehouseAreaRequest) XXX_Size() int {
	return xxx_messageInfo_AddWarehouseAreaRequest.Size(m)
}
func (m *AddWarehouseAreaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWarehouseAreaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddWarehouseAreaRequest proto.InternalMessageInfo

func (m *AddWarehouseAreaRequest) GetWarehouseAreaList() []*WarehouseArea {
	if m != nil {
		return m.WarehouseAreaList
	}
	return nil
}

func (m *AddWarehouseAreaRequest) GetWarehouseid() int32 {
	if m != nil {
		return m.Warehouseid
	}
	return 0
}

type WarehouseArea struct {
	//
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//区域id
	Areaid int32 `protobuf:"varint,2,opt,name=areaid,proto3" json:"areaid"`
	//仓库id
	Warehouseid int32 `protobuf:"varint,3,opt,name=warehouseid,proto3" json:"warehouseid"`
	//区域等级(1-一级，2-二级，3-3级)
	Level                int32    `protobuf:"varint,4,opt,name=level,proto3" json:"level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseArea) Reset()         { *m = WarehouseArea{} }
func (m *WarehouseArea) String() string { return proto.CompactTextString(m) }
func (*WarehouseArea) ProtoMessage()    {}
func (*WarehouseArea) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{33}
}

func (m *WarehouseArea) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseArea.Unmarshal(m, b)
}
func (m *WarehouseArea) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseArea.Marshal(b, m, deterministic)
}
func (m *WarehouseArea) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseArea.Merge(m, src)
}
func (m *WarehouseArea) XXX_Size() int {
	return xxx_messageInfo_WarehouseArea.Size(m)
}
func (m *WarehouseArea) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseArea.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseArea proto.InternalMessageInfo

func (m *WarehouseArea) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseArea) GetAreaid() int32 {
	if m != nil {
		return m.Areaid
	}
	return 0
}

func (m *WarehouseArea) GetWarehouseid() int32 {
	if m != nil {
		return m.Warehouseid
	}
	return 0
}

func (m *WarehouseArea) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

//获取仓库类型
type GetWarehouseTypeResponse struct {
	WarehouseTypes []*WarehouseType `protobuf:"bytes,1,rep,name=warehouseTypes,proto3" json:"warehouseTypes"`
	//状态码
	Code                 int32    `protobuf:"varint,2,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarehouseTypeResponse) Reset()         { *m = GetWarehouseTypeResponse{} }
func (m *GetWarehouseTypeResponse) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseTypeResponse) ProtoMessage()    {}
func (*GetWarehouseTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{34}
}

func (m *GetWarehouseTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseTypeResponse.Unmarshal(m, b)
}
func (m *GetWarehouseTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseTypeResponse.Marshal(b, m, deterministic)
}
func (m *GetWarehouseTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseTypeResponse.Merge(m, src)
}
func (m *GetWarehouseTypeResponse) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseTypeResponse.Size(m)
}
func (m *GetWarehouseTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseTypeResponse proto.InternalMessageInfo

func (m *GetWarehouseTypeResponse) GetWarehouseTypes() []*WarehouseType {
	if m != nil {
		return m.WarehouseTypes
	}
	return nil
}

func (m *GetWarehouseTypeResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

//仓库类型
type WarehouseType struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseType) Reset()         { *m = WarehouseType{} }
func (m *WarehouseType) String() string { return proto.CompactTextString(m) }
func (*WarehouseType) ProtoMessage()    {}
func (*WarehouseType) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{35}
}

func (m *WarehouseType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseType.Unmarshal(m, b)
}
func (m *WarehouseType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseType.Marshal(b, m, deterministic)
}
func (m *WarehouseType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseType.Merge(m, src)
}
func (m *WarehouseType) XXX_Size() int {
	return xxx_messageInfo_WarehouseType.Size(m)
}
func (m *WarehouseType) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseType.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseType proto.InternalMessageInfo

func (m *WarehouseType) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseType) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

//获取仓库等级
type GetWarehouseLevelResponse struct {
	WarehouseLevels []*WarehouseLevel `protobuf:"bytes,1,rep,name=warehouseLevels,proto3" json:"warehouseLevels"`
	//状态码
	Code                 int32    `protobuf:"varint,2,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarehouseLevelResponse) Reset()         { *m = GetWarehouseLevelResponse{} }
func (m *GetWarehouseLevelResponse) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseLevelResponse) ProtoMessage()    {}
func (*GetWarehouseLevelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{36}
}

func (m *GetWarehouseLevelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseLevelResponse.Unmarshal(m, b)
}
func (m *GetWarehouseLevelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseLevelResponse.Marshal(b, m, deterministic)
}
func (m *GetWarehouseLevelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseLevelResponse.Merge(m, src)
}
func (m *GetWarehouseLevelResponse) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseLevelResponse.Size(m)
}
func (m *GetWarehouseLevelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseLevelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseLevelResponse proto.InternalMessageInfo

func (m *GetWarehouseLevelResponse) GetWarehouseLevels() []*WarehouseLevel {
	if m != nil {
		return m.WarehouseLevels
	}
	return nil
}

func (m *GetWarehouseLevelResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

//仓库等级
type WarehouseLevel struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseLevel) Reset()         { *m = WarehouseLevel{} }
func (m *WarehouseLevel) String() string { return proto.CompactTextString(m) }
func (*WarehouseLevel) ProtoMessage()    {}
func (*WarehouseLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{37}
}

func (m *WarehouseLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseLevel.Unmarshal(m, b)
}
func (m *WarehouseLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseLevel.Marshal(b, m, deterministic)
}
func (m *WarehouseLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseLevel.Merge(m, src)
}
func (m *WarehouseLevel) XXX_Size() int {
	return xxx_messageInfo_WarehouseLevel.Size(m)
}
func (m *WarehouseLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseLevel.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseLevel proto.InternalMessageInfo

func (m *WarehouseLevel) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseLevel) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

//仓库列表请求参数
type WarehouseListRequest struct {
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
	Category string `protobuf:"bytes,1,opt,name=category,proto3" json:"category"`
	//页码
	Pageindex int32 `protobuf:"varint,2,opt,name=pageindex,proto3" json:"pageindex"`
	//每页行数
	Pagesize int32 `protobuf:"varint,3,opt,name=pagesize,proto3" json:"pagesize"`
	//搜索关键词
	KeyWord string `protobuf:"bytes,4,opt,name=keyWord,proto3" json:"keyWord"`
	// 所属城市
	City string `protobuf:"bytes,5,opt,name=city,proto3" json:"city"`
	// 是否查询前置仓和前置虚拟仓, 默认 0 否，1 是
	PreposeCategory int32 `protobuf:"varint,6,opt,name=prepose_category,json=preposeCategory,proto3" json:"prepose_category"`
	// 启用状态
	Status int32 `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	// 主体ID
	OrgId                int32    `protobuf:"varint,8,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseListRequest) Reset()         { *m = WarehouseListRequest{} }
func (m *WarehouseListRequest) String() string { return proto.CompactTextString(m) }
func (*WarehouseListRequest) ProtoMessage()    {}
func (*WarehouseListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{38}
}

func (m *WarehouseListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseListRequest.Unmarshal(m, b)
}
func (m *WarehouseListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseListRequest.Marshal(b, m, deterministic)
}
func (m *WarehouseListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseListRequest.Merge(m, src)
}
func (m *WarehouseListRequest) XXX_Size() int {
	return xxx_messageInfo_WarehouseListRequest.Size(m)
}
func (m *WarehouseListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseListRequest proto.InternalMessageInfo

func (m *WarehouseListRequest) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *WarehouseListRequest) GetPageindex() int32 {
	if m != nil {
		return m.Pageindex
	}
	return 0
}

func (m *WarehouseListRequest) GetPagesize() int32 {
	if m != nil {
		return m.Pagesize
	}
	return 0
}

func (m *WarehouseListRequest) GetKeyWord() string {
	if m != nil {
		return m.KeyWord
	}
	return ""
}

func (m *WarehouseListRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *WarehouseListRequest) GetPreposeCategory() int32 {
	if m != nil {
		return m.PreposeCategory
	}
	return 0
}

func (m *WarehouseListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WarehouseListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

//仓库列表返回数据
type WarehouseListResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总行数
	TotalCount int32 `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	//仓库数组
	WarehouseAarray      []*WarehouseList `protobuf:"bytes,5,rep,name=WarehouseAarray,proto3" json:"WarehouseAarray"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WarehouseListResponse) Reset()         { *m = WarehouseListResponse{} }
func (m *WarehouseListResponse) String() string { return proto.CompactTextString(m) }
func (*WarehouseListResponse) ProtoMessage()    {}
func (*WarehouseListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{39}
}

func (m *WarehouseListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseListResponse.Unmarshal(m, b)
}
func (m *WarehouseListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseListResponse.Marshal(b, m, deterministic)
}
func (m *WarehouseListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseListResponse.Merge(m, src)
}
func (m *WarehouseListResponse) XXX_Size() int {
	return xxx_messageInfo_WarehouseListResponse.Size(m)
}
func (m *WarehouseListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseListResponse proto.InternalMessageInfo

func (m *WarehouseListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *WarehouseListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *WarehouseListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *WarehouseListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *WarehouseListResponse) GetWarehouseAarray() []*WarehouseList {
	if m != nil {
		return m.WarehouseAarray
	}
	return nil
}

//仓库列表数据集
type WarehouseList struct {
	// ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//第三方仓库ID 例如a8id,管易ID
	Thirdid string `protobuf:"bytes,2,opt,name=thirdid,proto3" json:"thirdid"`
	//仓库编号
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	//仓库名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	//仓库归属(1-A8,2-管易)
	Comefrom int32 `protobuf:"varint,5,opt,name=comefrom,proto3" json:"comefrom"`
	//仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
	Level int32 `protobuf:"varint,6,opt,name=level,proto3" json:"level"`
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
	Category int32 `protobuf:"varint,7,opt,name=category,proto3" json:"category"`
	//仓库地址
	Address string `protobuf:"bytes,8,opt,name=address,proto3" json:"address"`
	//仓库联系人
	Contacts string `protobuf:"bytes,9,opt,name=contacts,proto3" json:"contacts"`
	//仓库联系方式
	Tel string `protobuf:"bytes,10,opt,name=tel,proto3" json:"tel"`
	//仓库状态（0-禁用，1-启用）
	Status int32 `protobuf:"varint,11,opt,name=status,proto3" json:"status"`
	//创建时间
	Createdate string `protobuf:"bytes,12,opt,name=createdate,proto3" json:"createdate"`
	//最后更新时间
	Lastdate string `protobuf:"bytes,13,opt,name=lastdate,proto3" json:"lastdate"`
	//所属系统 0:默认,1:ERP,2:子龙
	Subsystem int32 `protobuf:"varint,14,opt,name=subsystem,proto3" json:"subsystem"`
	//仓库比例
	Ratio int32 `protobuf:"varint,15,opt,name=ratio,proto3" json:"ratio"`
	//关联门店信息
	WarehouseInfo []string `protobuf:"bytes,16,rep,name=warehouse_info,json=warehouseInfo,proto3" json:"warehouse_info"`
	//仓库经度
	Lng int64 `protobuf:"varint,17,opt,name=lng,proto3" json:"lng"`
	//仓库纬度
	Lat int64 `protobuf:"varint,18,opt,name=lat,proto3" json:"lat"`
	// 所属区域
	Region string `protobuf:"bytes,19,opt,name=region,proto3" json:"region"`
	// 所属城市
	City string `protobuf:"bytes,20,opt,name=city,proto3" json:"city"`
	//关联门店数量
	Number               int32    `protobuf:"varint,21,opt,name=number,proto3" json:"number"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseList) Reset()         { *m = WarehouseList{} }
func (m *WarehouseList) String() string { return proto.CompactTextString(m) }
func (*WarehouseList) ProtoMessage()    {}
func (*WarehouseList) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{40}
}

func (m *WarehouseList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseList.Unmarshal(m, b)
}
func (m *WarehouseList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseList.Marshal(b, m, deterministic)
}
func (m *WarehouseList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseList.Merge(m, src)
}
func (m *WarehouseList) XXX_Size() int {
	return xxx_messageInfo_WarehouseList.Size(m)
}
func (m *WarehouseList) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseList.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseList proto.InternalMessageInfo

func (m *WarehouseList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseList) GetThirdid() string {
	if m != nil {
		return m.Thirdid
	}
	return ""
}

func (m *WarehouseList) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *WarehouseList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WarehouseList) GetComefrom() int32 {
	if m != nil {
		return m.Comefrom
	}
	return 0
}

func (m *WarehouseList) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WarehouseList) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *WarehouseList) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *WarehouseList) GetContacts() string {
	if m != nil {
		return m.Contacts
	}
	return ""
}

func (m *WarehouseList) GetTel() string {
	if m != nil {
		return m.Tel
	}
	return ""
}

func (m *WarehouseList) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WarehouseList) GetCreatedate() string {
	if m != nil {
		return m.Createdate
	}
	return ""
}

func (m *WarehouseList) GetLastdate() string {
	if m != nil {
		return m.Lastdate
	}
	return ""
}

func (m *WarehouseList) GetSubsystem() int32 {
	if m != nil {
		return m.Subsystem
	}
	return 0
}

func (m *WarehouseList) GetRatio() int32 {
	if m != nil {
		return m.Ratio
	}
	return 0
}

func (m *WarehouseList) GetWarehouseInfo() []string {
	if m != nil {
		return m.WarehouseInfo
	}
	return nil
}

func (m *WarehouseList) GetLng() int64 {
	if m != nil {
		return m.Lng
	}
	return 0
}

func (m *WarehouseList) GetLat() int64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *WarehouseList) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *WarehouseList) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *WarehouseList) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

//选择区域请求
type BaseAreaRequest struct {
	//地区深度,传1获取大区和省数据，传2获取大区省市数据，传3获取大区省市县数据
	Areadeep             int32    `protobuf:"varint,1,opt,name=areadeep,proto3" json:"areadeep"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseAreaRequest) Reset()         { *m = BaseAreaRequest{} }
func (m *BaseAreaRequest) String() string { return proto.CompactTextString(m) }
func (*BaseAreaRequest) ProtoMessage()    {}
func (*BaseAreaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{41}
}

func (m *BaseAreaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseAreaRequest.Unmarshal(m, b)
}
func (m *BaseAreaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseAreaRequest.Marshal(b, m, deterministic)
}
func (m *BaseAreaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseAreaRequest.Merge(m, src)
}
func (m *BaseAreaRequest) XXX_Size() int {
	return xxx_messageInfo_BaseAreaRequest.Size(m)
}
func (m *BaseAreaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseAreaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseAreaRequest proto.InternalMessageInfo

func (m *BaseAreaRequest) GetAreadeep() int32 {
	if m != nil {
		return m.Areadeep
	}
	return 0
}

//选择区域返回
type BaseAreaResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//大区集合
	Regionarray          []*RegionList `protobuf:"bytes,4,rep,name=regionarray,proto3" json:"regionarray"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BaseAreaResponse) Reset()         { *m = BaseAreaResponse{} }
func (m *BaseAreaResponse) String() string { return proto.CompactTextString(m) }
func (*BaseAreaResponse) ProtoMessage()    {}
func (*BaseAreaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{42}
}

func (m *BaseAreaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseAreaResponse.Unmarshal(m, b)
}
func (m *BaseAreaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseAreaResponse.Marshal(b, m, deterministic)
}
func (m *BaseAreaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseAreaResponse.Merge(m, src)
}
func (m *BaseAreaResponse) XXX_Size() int {
	return xxx_messageInfo_BaseAreaResponse.Size(m)
}
func (m *BaseAreaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseAreaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseAreaResponse proto.InternalMessageInfo

func (m *BaseAreaResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseAreaResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseAreaResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *BaseAreaResponse) GetRegionarray() []*RegionList {
	if m != nil {
		return m.Regionarray
	}
	return nil
}

//大区集合
type RegionList struct {
	//大区名称
	Arearegion string `protobuf:"bytes,1,opt,name=arearegion,proto3" json:"arearegion"`
	//省集合
	Children             []*ProvinceList `protobuf:"bytes,2,rep,name=children,proto3" json:"children"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RegionList) Reset()         { *m = RegionList{} }
func (m *RegionList) String() string { return proto.CompactTextString(m) }
func (*RegionList) ProtoMessage()    {}
func (*RegionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{43}
}

func (m *RegionList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegionList.Unmarshal(m, b)
}
func (m *RegionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegionList.Marshal(b, m, deterministic)
}
func (m *RegionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionList.Merge(m, src)
}
func (m *RegionList) XXX_Size() int {
	return xxx_messageInfo_RegionList.Size(m)
}
func (m *RegionList) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionList.DiscardUnknown(m)
}

var xxx_messageInfo_RegionList proto.InternalMessageInfo

func (m *RegionList) GetArearegion() string {
	if m != nil {
		return m.Arearegion
	}
	return ""
}

func (m *RegionList) GetChildren() []*ProvinceList {
	if m != nil {
		return m.Children
	}
	return nil
}

//大区直属省，直辖市集合
type ProvinceList struct {
	// ID
	Areaid int32 `protobuf:"varint,1,opt,name=areaid,proto3" json:"areaid"`
	//地区名称
	Areaname string `protobuf:"bytes,2,opt,name=areaname,proto3" json:"areaname"`
	//父ID
	Areaparentid int32 `protobuf:"varint,3,opt,name=areaparentid,proto3" json:"areaparentid"`
	//排序
	Areasort int32 `protobuf:"varint,4,opt,name=areasort,proto3" json:"areasort"`
	//地区深度，从1开始
	Areadeep int32 `protobuf:"varint,5,opt,name=areadeep,proto3" json:"areadeep"`
	//省直属市，区集合
	Children             []*CityList `protobuf:"bytes,6,rep,name=children,proto3" json:"children"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ProvinceList) Reset()         { *m = ProvinceList{} }
func (m *ProvinceList) String() string { return proto.CompactTextString(m) }
func (*ProvinceList) ProtoMessage()    {}
func (*ProvinceList) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{44}
}

func (m *ProvinceList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProvinceList.Unmarshal(m, b)
}
func (m *ProvinceList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProvinceList.Marshal(b, m, deterministic)
}
func (m *ProvinceList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProvinceList.Merge(m, src)
}
func (m *ProvinceList) XXX_Size() int {
	return xxx_messageInfo_ProvinceList.Size(m)
}
func (m *ProvinceList) XXX_DiscardUnknown() {
	xxx_messageInfo_ProvinceList.DiscardUnknown(m)
}

var xxx_messageInfo_ProvinceList proto.InternalMessageInfo

func (m *ProvinceList) GetAreaid() int32 {
	if m != nil {
		return m.Areaid
	}
	return 0
}

func (m *ProvinceList) GetAreaname() string {
	if m != nil {
		return m.Areaname
	}
	return ""
}

func (m *ProvinceList) GetAreaparentid() int32 {
	if m != nil {
		return m.Areaparentid
	}
	return 0
}

func (m *ProvinceList) GetAreasort() int32 {
	if m != nil {
		return m.Areasort
	}
	return 0
}

func (m *ProvinceList) GetAreadeep() int32 {
	if m != nil {
		return m.Areadeep
	}
	return 0
}

func (m *ProvinceList) GetChildren() []*CityList {
	if m != nil {
		return m.Children
	}
	return nil
}

//省直属市，区集合
type CityList struct {
	// ID
	Areaid int32 `protobuf:"varint,1,opt,name=areaid,proto3" json:"areaid"`
	//地区名称
	Areaname string `protobuf:"bytes,2,opt,name=areaname,proto3" json:"areaname"`
	//父ID
	Areaparentid int32 `protobuf:"varint,3,opt,name=areaparentid,proto3" json:"areaparentid"`
	//排序
	Areasort int32 `protobuf:"varint,4,opt,name=areasort,proto3" json:"areasort"`
	//地区深度，从1开始
	Areadeep int32 `protobuf:"varint,5,opt,name=areadeep,proto3" json:"areadeep"`
	//市直属县，区集合
	Children             []*CountyList `protobuf:"bytes,6,rep,name=children,proto3" json:"children"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CityList) Reset()         { *m = CityList{} }
func (m *CityList) String() string { return proto.CompactTextString(m) }
func (*CityList) ProtoMessage()    {}
func (*CityList) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{45}
}

func (m *CityList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CityList.Unmarshal(m, b)
}
func (m *CityList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CityList.Marshal(b, m, deterministic)
}
func (m *CityList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CityList.Merge(m, src)
}
func (m *CityList) XXX_Size() int {
	return xxx_messageInfo_CityList.Size(m)
}
func (m *CityList) XXX_DiscardUnknown() {
	xxx_messageInfo_CityList.DiscardUnknown(m)
}

var xxx_messageInfo_CityList proto.InternalMessageInfo

func (m *CityList) GetAreaid() int32 {
	if m != nil {
		return m.Areaid
	}
	return 0
}

func (m *CityList) GetAreaname() string {
	if m != nil {
		return m.Areaname
	}
	return ""
}

func (m *CityList) GetAreaparentid() int32 {
	if m != nil {
		return m.Areaparentid
	}
	return 0
}

func (m *CityList) GetAreasort() int32 {
	if m != nil {
		return m.Areasort
	}
	return 0
}

func (m *CityList) GetAreadeep() int32 {
	if m != nil {
		return m.Areadeep
	}
	return 0
}

func (m *CityList) GetChildren() []*CountyList {
	if m != nil {
		return m.Children
	}
	return nil
}

//市直属县，区集合
type CountyList struct {
	// ID
	Areaid int32 `protobuf:"varint,1,opt,name=areaid,proto3" json:"areaid"`
	//地区名称
	Areaname string `protobuf:"bytes,2,opt,name=areaname,proto3" json:"areaname"`
	//父ID
	Areaparentid int32 `protobuf:"varint,3,opt,name=areaparentid,proto3" json:"areaparentid"`
	//排序
	Areasort int32 `protobuf:"varint,4,opt,name=areasort,proto3" json:"areasort"`
	//地区深度，从1开始
	Areadeep             int32    `protobuf:"varint,5,opt,name=areadeep,proto3" json:"areadeep"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountyList) Reset()         { *m = CountyList{} }
func (m *CountyList) String() string { return proto.CompactTextString(m) }
func (*CountyList) ProtoMessage()    {}
func (*CountyList) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{46}
}

func (m *CountyList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountyList.Unmarshal(m, b)
}
func (m *CountyList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountyList.Marshal(b, m, deterministic)
}
func (m *CountyList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountyList.Merge(m, src)
}
func (m *CountyList) XXX_Size() int {
	return xxx_messageInfo_CountyList.Size(m)
}
func (m *CountyList) XXX_DiscardUnknown() {
	xxx_messageInfo_CountyList.DiscardUnknown(m)
}

var xxx_messageInfo_CountyList proto.InternalMessageInfo

func (m *CountyList) GetAreaid() int32 {
	if m != nil {
		return m.Areaid
	}
	return 0
}

func (m *CountyList) GetAreaname() string {
	if m != nil {
		return m.Areaname
	}
	return ""
}

func (m *CountyList) GetAreaparentid() int32 {
	if m != nil {
		return m.Areaparentid
	}
	return 0
}

func (m *CountyList) GetAreasort() int32 {
	if m != nil {
		return m.Areasort
	}
	return 0
}

func (m *CountyList) GetAreadeep() int32 {
	if m != nil {
		return m.Areadeep
	}
	return 0
}

//变更仓库状态请求参数
type UpdateWarehouseStatusRequest struct {
	//仓库id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//仓库状态（0-禁用，1-启用）
	Status               string   `protobuf:"bytes,2,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateWarehouseStatusRequest) Reset()         { *m = UpdateWarehouseStatusRequest{} }
func (m *UpdateWarehouseStatusRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateWarehouseStatusRequest) ProtoMessage()    {}
func (*UpdateWarehouseStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{47}
}

func (m *UpdateWarehouseStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWarehouseStatusRequest.Unmarshal(m, b)
}
func (m *UpdateWarehouseStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWarehouseStatusRequest.Marshal(b, m, deterministic)
}
func (m *UpdateWarehouseStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWarehouseStatusRequest.Merge(m, src)
}
func (m *UpdateWarehouseStatusRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateWarehouseStatusRequest.Size(m)
}
func (m *UpdateWarehouseStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWarehouseStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWarehouseStatusRequest proto.InternalMessageInfo

func (m *UpdateWarehouseStatusRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateWarehouseStatusRequest) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

//根据仓库ID查询仓库详情请求参数
type GetWarehouseByIdRequest struct {
	//仓库id
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarehouseByIdRequest) Reset()         { *m = GetWarehouseByIdRequest{} }
func (m *GetWarehouseByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseByIdRequest) ProtoMessage()    {}
func (*GetWarehouseByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{48}
}

func (m *GetWarehouseByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseByIdRequest.Unmarshal(m, b)
}
func (m *GetWarehouseByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseByIdRequest.Marshal(b, m, deterministic)
}
func (m *GetWarehouseByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseByIdRequest.Merge(m, src)
}
func (m *GetWarehouseByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseByIdRequest.Size(m)
}
func (m *GetWarehouseByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseByIdRequest proto.InternalMessageInfo

func (m *GetWarehouseByIdRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

//根据仓库ID查询仓库详情返回数据
type GetWarehouseByIdResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//仓库详情
	// WarehouseList Warehouse  =4;
	// ID
	Id int32 `protobuf:"varint,4,opt,name=id,proto3" json:"id"`
	//仓库编号
	Warehousecode string `protobuf:"bytes,5,opt,name=warehousecode,proto3" json:"warehousecode"`
	//仓库名称
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name"`
	//仓库归属(1-A8,2-管易)
	Comefrom int32 `protobuf:"varint,7,opt,name=comefrom,proto3" json:"comefrom"`
	//仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)
	Level int32 `protobuf:"varint,8,opt,name=level,proto3" json:"level"`
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)
	Category int32 `protobuf:"varint,9,opt,name=category,proto3" json:"category"`
	//仓库地址
	Address string `protobuf:"bytes,10,opt,name=address,proto3" json:"address"`
	//仓库联系人
	Contacts string `protobuf:"bytes,11,opt,name=contacts,proto3" json:"contacts"`
	//仓库联系方式
	Tel string `protobuf:"bytes,12,opt,name=tel,proto3" json:"tel"`
	//仓库状态（0-禁用，1-启用）
	Status int32 `protobuf:"varint,13,opt,name=status,proto3" json:"status"`
	//创建时间
	Createdate string `protobuf:"bytes,14,opt,name=createdate,proto3" json:"createdate"`
	//第三方仓库ID 例如a8id,管易ID
	Thirdid string `protobuf:"bytes,15,opt,name=thirdid,proto3" json:"thirdid"`
	//所属系统 0:默认,1:ERP,2:子龙
	Subsystem int32 `protobuf:"varint,16,opt,name=subsystem,proto3" json:"subsystem"`
	//仓库比例
	Ratio int32 `protobuf:"varint,17,opt,name=ratio,proto3" json:"ratio"`
	//仓库经度
	Lng int64 `protobuf:"varint,18,opt,name=lng,proto3" json:"lng"`
	//仓库纬度
	Lat int64 `protobuf:"varint,19,opt,name=lat,proto3" json:"lat"`
	// 区域
	Region string `protobuf:"bytes,20,opt,name=region,proto3" json:"region"`
	// 城市
	City string `protobuf:"bytes,21,opt,name=city,proto3" json:"city"`
	// 是否有资质售药 1有、0否
	SellDrugs int32 `protobuf:"varint,22,opt,name=sell_drugs,json=sellDrugs,proto3" json:"sell_drugs"`
	// 仓库关联配送配置信息
	WarehouseDeliveryList []*WarehouseDeliveryInfo `protobuf:"bytes,23,rep,name=warehouse_delivery_list,json=warehouseDeliveryList,proto3" json:"warehouse_delivery_list"`
	//仓库关联门店及渠道
	RelationShopList     []*WarehouseRelationShopList `protobuf:"bytes,24,rep,name=relation_shop_list,json=relationShopList,proto3" json:"relation_shop_list"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetWarehouseByIdResponse) Reset()         { *m = GetWarehouseByIdResponse{} }
func (m *GetWarehouseByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseByIdResponse) ProtoMessage()    {}
func (*GetWarehouseByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{49}
}

func (m *GetWarehouseByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseByIdResponse.Unmarshal(m, b)
}
func (m *GetWarehouseByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseByIdResponse.Marshal(b, m, deterministic)
}
func (m *GetWarehouseByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseByIdResponse.Merge(m, src)
}
func (m *GetWarehouseByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseByIdResponse.Size(m)
}
func (m *GetWarehouseByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseByIdResponse proto.InternalMessageInfo

func (m *GetWarehouseByIdResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetWarehousecode() string {
	if m != nil {
		return m.Warehousecode
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetComefrom() int32 {
	if m != nil {
		return m.Comefrom
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetContacts() string {
	if m != nil {
		return m.Contacts
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetTel() string {
	if m != nil {
		return m.Tel
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetCreatedate() string {
	if m != nil {
		return m.Createdate
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetThirdid() string {
	if m != nil {
		return m.Thirdid
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetSubsystem() int32 {
	if m != nil {
		return m.Subsystem
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetRatio() int32 {
	if m != nil {
		return m.Ratio
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetLng() int64 {
	if m != nil {
		return m.Lng
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetLat() int64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *GetWarehouseByIdResponse) GetSellDrugs() int32 {
	if m != nil {
		return m.SellDrugs
	}
	return 0
}

func (m *GetWarehouseByIdResponse) GetWarehouseDeliveryList() []*WarehouseDeliveryInfo {
	if m != nil {
		return m.WarehouseDeliveryList
	}
	return nil
}

func (m *GetWarehouseByIdResponse) GetRelationShopList() []*WarehouseRelationShopList {
	if m != nil {
		return m.RelationShopList
	}
	return nil
}

type WarehouseDeliveryInfo struct {
	//配送配置主键ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//配送类型名称
	DeliveryName string `protobuf:"bytes,2,opt,name=delivery_name,json=deliveryName,proto3" json:"delivery_name"`
	AppKey       string `protobuf:"bytes,3,opt,name=app_key,json=appKey,proto3" json:"app_key"`
	AppSecret    string `protobuf:"bytes,4,opt,name=app_secret,json=appSecret,proto3" json:"app_secret"`
	ScoureId     string `protobuf:"bytes,5,opt,name=scoure_id,json=scoureId,proto3" json:"scoure_id"`
	//配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风
	DeliveryType int32 `protobuf:"varint,6,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type"`
	//配送门店编码
	ShopNo               string   `protobuf:"bytes,7,opt,name=shop_no,json=shopNo,proto3" json:"shop_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseDeliveryInfo) Reset()         { *m = WarehouseDeliveryInfo{} }
func (m *WarehouseDeliveryInfo) String() string { return proto.CompactTextString(m) }
func (*WarehouseDeliveryInfo) ProtoMessage()    {}
func (*WarehouseDeliveryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{50}
}

func (m *WarehouseDeliveryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseDeliveryInfo.Unmarshal(m, b)
}
func (m *WarehouseDeliveryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseDeliveryInfo.Marshal(b, m, deterministic)
}
func (m *WarehouseDeliveryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseDeliveryInfo.Merge(m, src)
}
func (m *WarehouseDeliveryInfo) XXX_Size() int {
	return xxx_messageInfo_WarehouseDeliveryInfo.Size(m)
}
func (m *WarehouseDeliveryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseDeliveryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseDeliveryInfo proto.InternalMessageInfo

func (m *WarehouseDeliveryInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseDeliveryInfo) GetDeliveryName() string {
	if m != nil {
		return m.DeliveryName
	}
	return ""
}

func (m *WarehouseDeliveryInfo) GetAppKey() string {
	if m != nil {
		return m.AppKey
	}
	return ""
}

func (m *WarehouseDeliveryInfo) GetAppSecret() string {
	if m != nil {
		return m.AppSecret
	}
	return ""
}

func (m *WarehouseDeliveryInfo) GetScoureId() string {
	if m != nil {
		return m.ScoureId
	}
	return ""
}

func (m *WarehouseDeliveryInfo) GetDeliveryType() int32 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

func (m *WarehouseDeliveryInfo) GetShopNo() string {
	if m != nil {
		return m.ShopNo
	}
	return ""
}

type WarehouseRelationShopList struct {
	ShopName             string   `protobuf:"bytes,1,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	ChannelNames         string   `protobuf:"bytes,2,opt,name=channel_names,json=channelNames,proto3" json:"channel_names"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseRelationShopList) Reset()         { *m = WarehouseRelationShopList{} }
func (m *WarehouseRelationShopList) String() string { return proto.CompactTextString(m) }
func (*WarehouseRelationShopList) ProtoMessage()    {}
func (*WarehouseRelationShopList) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{51}
}

func (m *WarehouseRelationShopList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseRelationShopList.Unmarshal(m, b)
}
func (m *WarehouseRelationShopList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseRelationShopList.Marshal(b, m, deterministic)
}
func (m *WarehouseRelationShopList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseRelationShopList.Merge(m, src)
}
func (m *WarehouseRelationShopList) XXX_Size() int {
	return xxx_messageInfo_WarehouseRelationShopList.Size(m)
}
func (m *WarehouseRelationShopList) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseRelationShopList.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseRelationShopList proto.InternalMessageInfo

func (m *WarehouseRelationShopList) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *WarehouseRelationShopList) GetChannelNames() string {
	if m != nil {
		return m.ChannelNames
	}
	return ""
}

//根据仓库ID获取仓库配送区域请求参数
type GetAreaByWarehouseIdRequest struct {
	//仓库id
	Warehouseid          int32    `protobuf:"varint,1,opt,name=warehouseid,proto3" json:"warehouseid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAreaByWarehouseIdRequest) Reset()         { *m = GetAreaByWarehouseIdRequest{} }
func (m *GetAreaByWarehouseIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetAreaByWarehouseIdRequest) ProtoMessage()    {}
func (*GetAreaByWarehouseIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{52}
}

func (m *GetAreaByWarehouseIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAreaByWarehouseIdRequest.Unmarshal(m, b)
}
func (m *GetAreaByWarehouseIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAreaByWarehouseIdRequest.Marshal(b, m, deterministic)
}
func (m *GetAreaByWarehouseIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAreaByWarehouseIdRequest.Merge(m, src)
}
func (m *GetAreaByWarehouseIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetAreaByWarehouseIdRequest.Size(m)
}
func (m *GetAreaByWarehouseIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAreaByWarehouseIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAreaByWarehouseIdRequest proto.InternalMessageInfo

func (m *GetAreaByWarehouseIdRequest) GetWarehouseid() int32 {
	if m != nil {
		return m.Warehouseid
	}
	return 0
}

//根据仓库ID获取仓库配送区域返回数据
type GetAreaByWarehouseIdResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//区域信息
	WarehouseArea        []*WarehouseAreaDto `protobuf:"bytes,4,rep,name=WarehouseArea,proto3" json:"WarehouseArea"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAreaByWarehouseIdResponse) Reset()         { *m = GetAreaByWarehouseIdResponse{} }
func (m *GetAreaByWarehouseIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetAreaByWarehouseIdResponse) ProtoMessage()    {}
func (*GetAreaByWarehouseIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{53}
}

func (m *GetAreaByWarehouseIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAreaByWarehouseIdResponse.Unmarshal(m, b)
}
func (m *GetAreaByWarehouseIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAreaByWarehouseIdResponse.Marshal(b, m, deterministic)
}
func (m *GetAreaByWarehouseIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAreaByWarehouseIdResponse.Merge(m, src)
}
func (m *GetAreaByWarehouseIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetAreaByWarehouseIdResponse.Size(m)
}
func (m *GetAreaByWarehouseIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAreaByWarehouseIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAreaByWarehouseIdResponse proto.InternalMessageInfo

func (m *GetAreaByWarehouseIdResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetAreaByWarehouseIdResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetAreaByWarehouseIdResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetAreaByWarehouseIdResponse) GetWarehouseArea() []*WarehouseAreaDto {
	if m != nil {
		return m.WarehouseArea
	}
	return nil
}

type WarehouseAreaDto struct {
	//区域名称
	Areaname string `protobuf:"bytes,1,opt,name=areaname,proto3" json:"areaname"`
	//区域等级 1级 2级 3级 最多3级
	Level int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level"`
	//区域ID
	Areaid               int32    `protobuf:"varint,3,opt,name=areaid,proto3" json:"areaid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseAreaDto) Reset()         { *m = WarehouseAreaDto{} }
func (m *WarehouseAreaDto) String() string { return proto.CompactTextString(m) }
func (*WarehouseAreaDto) ProtoMessage()    {}
func (*WarehouseAreaDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{54}
}

func (m *WarehouseAreaDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseAreaDto.Unmarshal(m, b)
}
func (m *WarehouseAreaDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseAreaDto.Marshal(b, m, deterministic)
}
func (m *WarehouseAreaDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseAreaDto.Merge(m, src)
}
func (m *WarehouseAreaDto) XXX_Size() int {
	return xxx_messageInfo_WarehouseAreaDto.Size(m)
}
func (m *WarehouseAreaDto) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseAreaDto.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseAreaDto proto.InternalMessageInfo

func (m *WarehouseAreaDto) GetAreaname() string {
	if m != nil {
		return m.Areaname
	}
	return ""
}

func (m *WarehouseAreaDto) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WarehouseAreaDto) GetAreaid() int32 {
	if m != nil {
		return m.Areaid
	}
	return 0
}

//拆单请求model
type DemolitionOrderRequest struct {
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//主键
	Orderid string `protobuf:"bytes,2,opt,name=orderid,proto3" json:"orderid"`
	//用户编号
	Memberid string `protobuf:"bytes,3,opt,name=memberid,proto3" json:"memberid"`
	//订单金额 单位（分）
	Ordermoney int32 `protobuf:"varint,4,opt,name=ordermoney,proto3" json:"ordermoney"`
	// 1 咨询订单2 门店订单3 预约订单4 电商订单5 积分订单6 拼团订单
	Ordertype int32 `protobuf:"varint,5,opt,name=ordertype,proto3" json:"ordertype"`
	//订单类型详细的子类型
	Ordertypedetail int32 `protobuf:"varint,6,opt,name=ordertypedetail,proto3" json:"ordertypedetail"`
	//订单状态：1-未支付，2-已支付，3-已退款，4-已取消，5-退款中
	Orderstate int32 `protobuf:"varint,7,opt,name=orderstate,proto3" json:"orderstate"`
	//
	Useragent int32 `protobuf:"varint,8,opt,name=useragent,proto3" json:"useragent"`
	//订单所属平台
	Platformid int32 `protobuf:"varint,9,opt,name=platformid,proto3" json:"platformid"`
	//订单的所属分院
	Belonghospitalid string `protobuf:"bytes,10,opt,name=belonghospitalid,proto3" json:"belonghospitalid"`
	//创建时间
	Createtime string `protobuf:"bytes,11,opt,name=createtime,proto3" json:"createtime"`
	//最后修改时间
	Lasttime string `protobuf:"bytes,12,opt,name=lasttime,proto3" json:"lasttime"`
	//订单子状态：
	//第一位：主状态，第2、3位：活动类型{01：抽奖拼团，02：非抽奖拼团}，第4、5位：活动状态类型{01：待发货，02：待收货，03：交易完成，04:拼团中，05：拼团成功，06：拼团失败
	//}
	Orderchildenstate int32 `protobuf:"varint,13,opt,name=orderchildenstate,proto3" json:"orderchildenstate"`
	//是否评价：0-未评价，1-已评价
	Isevaluate int32 `protobuf:"varint,14,opt,name=isevaluate,proto3" json:"isevaluate"`
	//是否推送电商(0-否，1-是)
	Ispostupet int32 `protobuf:"varint,15,opt,name=ispostupet,proto3" json:"ispostupet"`
	//是否发送5分钟通知(0-否，1-是)
	Isnotify int32 `protobuf:"varint,16,opt,name=isnotify,proto3" json:"isnotify"`
	// 1.A8 ,2管易 ,3门店
	Ordersource int32 `protobuf:"varint,17,opt,name=ordersource,proto3" json:"ordersource"`
	// 0.初始化
	Status int32 `protobuf:"varint,18,opt,name=status,proto3" json:"status"`
	//订单相信信息
	OrderGoodsDetails []*OrderGoodsDetail `protobuf:"bytes,19,rep,name=OrderGoodsDetails,proto3" json:"OrderGoodsDetails"`
	//订单类型详细的子类型
	Orderdetail string `protobuf:"bytes,20,opt,name=orderdetail,proto3" json:"orderdetail"`
	//收货地址城市
	Province string `protobuf:"bytes,21,opt,name=province,proto3" json:"province"`
	//收货地址城市
	City string `protobuf:"bytes,22,opt,name=city,proto3" json:"city"`
	//收货地址区域
	Region               string   `protobuf:"bytes,23,opt,name=region,proto3" json:"region"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DemolitionOrderRequest) Reset()         { *m = DemolitionOrderRequest{} }
func (m *DemolitionOrderRequest) String() string { return proto.CompactTextString(m) }
func (*DemolitionOrderRequest) ProtoMessage()    {}
func (*DemolitionOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{55}
}

func (m *DemolitionOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DemolitionOrderRequest.Unmarshal(m, b)
}
func (m *DemolitionOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DemolitionOrderRequest.Marshal(b, m, deterministic)
}
func (m *DemolitionOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DemolitionOrderRequest.Merge(m, src)
}
func (m *DemolitionOrderRequest) XXX_Size() int {
	return xxx_messageInfo_DemolitionOrderRequest.Size(m)
}
func (m *DemolitionOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DemolitionOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DemolitionOrderRequest proto.InternalMessageInfo

func (m *DemolitionOrderRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DemolitionOrderRequest) GetOrderid() string {
	if m != nil {
		return m.Orderid
	}
	return ""
}

func (m *DemolitionOrderRequest) GetMemberid() string {
	if m != nil {
		return m.Memberid
	}
	return ""
}

func (m *DemolitionOrderRequest) GetOrdermoney() int32 {
	if m != nil {
		return m.Ordermoney
	}
	return 0
}

func (m *DemolitionOrderRequest) GetOrdertype() int32 {
	if m != nil {
		return m.Ordertype
	}
	return 0
}

func (m *DemolitionOrderRequest) GetOrdertypedetail() int32 {
	if m != nil {
		return m.Ordertypedetail
	}
	return 0
}

func (m *DemolitionOrderRequest) GetOrderstate() int32 {
	if m != nil {
		return m.Orderstate
	}
	return 0
}

func (m *DemolitionOrderRequest) GetUseragent() int32 {
	if m != nil {
		return m.Useragent
	}
	return 0
}

func (m *DemolitionOrderRequest) GetPlatformid() int32 {
	if m != nil {
		return m.Platformid
	}
	return 0
}

func (m *DemolitionOrderRequest) GetBelonghospitalid() string {
	if m != nil {
		return m.Belonghospitalid
	}
	return ""
}

func (m *DemolitionOrderRequest) GetCreatetime() string {
	if m != nil {
		return m.Createtime
	}
	return ""
}

func (m *DemolitionOrderRequest) GetLasttime() string {
	if m != nil {
		return m.Lasttime
	}
	return ""
}

func (m *DemolitionOrderRequest) GetOrderchildenstate() int32 {
	if m != nil {
		return m.Orderchildenstate
	}
	return 0
}

func (m *DemolitionOrderRequest) GetIsevaluate() int32 {
	if m != nil {
		return m.Isevaluate
	}
	return 0
}

func (m *DemolitionOrderRequest) GetIspostupet() int32 {
	if m != nil {
		return m.Ispostupet
	}
	return 0
}

func (m *DemolitionOrderRequest) GetIsnotify() int32 {
	if m != nil {
		return m.Isnotify
	}
	return 0
}

func (m *DemolitionOrderRequest) GetOrdersource() int32 {
	if m != nil {
		return m.Ordersource
	}
	return 0
}

func (m *DemolitionOrderRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DemolitionOrderRequest) GetOrderGoodsDetails() []*OrderGoodsDetail {
	if m != nil {
		return m.OrderGoodsDetails
	}
	return nil
}

func (m *DemolitionOrderRequest) GetOrderdetail() string {
	if m != nil {
		return m.Orderdetail
	}
	return ""
}

func (m *DemolitionOrderRequest) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *DemolitionOrderRequest) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *DemolitionOrderRequest) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

type OrderGoodsDetail struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	//订单编号(guid)
	Orderid string `protobuf:"bytes,2,opt,name=orderid,proto3" json:"orderid"`
	//商品编号(货号)
	Goodsid string `protobuf:"bytes,3,opt,name=goodsid,proto3" json:"goodsid"`
	//商品主编码
	Barcode string `protobuf:"bytes,4,opt,name=barcode,proto3" json:"barcode"`
	//商品名称
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name"`
	//商品单价单位（分）
	Univalence int32 `protobuf:"varint,6,opt,name=univalence,proto3" json:"univalence"`
	//商品售价单位（分）
	Sellprice int32 `protobuf:"varint,7,opt,name=sellprice,proto3" json:"sellprice"`
	//商品数量
	Quantity int32 `protobuf:"varint,8,opt,name=quantity,proto3" json:"quantity"`
	//商品销售单位
	Unit string `protobuf:"bytes,9,opt,name=unit,proto3" json:"unit"`
	//商品适用分院（0--所有分院，其他对应分院编号）
	Applyhospitalid string `protobuf:"bytes,10,opt,name=applyhospitalid,proto3" json:"applyhospitalid"`
	// 1-不用核销 2-需要核销 3-已核销
	Chargeoff int32 `protobuf:"varint,11,opt,name=chargeoff,proto3" json:"chargeoff"`
	//核销码
	Chargeoffcode string `protobuf:"bytes,12,opt,name=chargeoffcode,proto3" json:"chargeoffcode"`
	//核销对象-分院编号
	ChargeoffhospitalId string `protobuf:"bytes,13,opt,name=chargeoffhospitalId,proto3" json:"chargeoffhospitalId"`
	//核销时间
	Chargeofftime string `protobuf:"bytes,14,opt,name=chargeofftime,proto3" json:"chargeofftime"`
	//创建时间
	Createtime string `protobuf:"bytes,15,opt,name=createtime,proto3" json:"createtime"`
	//最后操作时间
	Lasttime string `protobuf:"bytes,16,opt,name=lasttime,proto3" json:"lasttime"`
	//来源 从请求头获取
	Source int32 `protobuf:"varint,17,opt,name=source,proto3" json:"source"`
	// UA 从请求头获取
	Useragent int32 `protobuf:"varint,18,opt,name=useragent,proto3" json:"useragent"`
	//核销对象-用户id
	Chargeoffmemberid string `protobuf:"bytes,19,opt,name=chargeoffmemberid,proto3" json:"chargeoffmemberid"`
	//商品图片文件
	Goodsimage           string   `protobuf:"bytes,20,opt,name=goodsimage,proto3" json:"goodsimage"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderGoodsDetail) Reset()         { *m = OrderGoodsDetail{} }
func (m *OrderGoodsDetail) String() string { return proto.CompactTextString(m) }
func (*OrderGoodsDetail) ProtoMessage()    {}
func (*OrderGoodsDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{56}
}

func (m *OrderGoodsDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderGoodsDetail.Unmarshal(m, b)
}
func (m *OrderGoodsDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderGoodsDetail.Marshal(b, m, deterministic)
}
func (m *OrderGoodsDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderGoodsDetail.Merge(m, src)
}
func (m *OrderGoodsDetail) XXX_Size() int {
	return xxx_messageInfo_OrderGoodsDetail.Size(m)
}
func (m *OrderGoodsDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderGoodsDetail.DiscardUnknown(m)
}

var xxx_messageInfo_OrderGoodsDetail proto.InternalMessageInfo

func (m *OrderGoodsDetail) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *OrderGoodsDetail) GetOrderid() string {
	if m != nil {
		return m.Orderid
	}
	return ""
}

func (m *OrderGoodsDetail) GetGoodsid() string {
	if m != nil {
		return m.Goodsid
	}
	return ""
}

func (m *OrderGoodsDetail) GetBarcode() string {
	if m != nil {
		return m.Barcode
	}
	return ""
}

func (m *OrderGoodsDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *OrderGoodsDetail) GetUnivalence() int32 {
	if m != nil {
		return m.Univalence
	}
	return 0
}

func (m *OrderGoodsDetail) GetSellprice() int32 {
	if m != nil {
		return m.Sellprice
	}
	return 0
}

func (m *OrderGoodsDetail) GetQuantity() int32 {
	if m != nil {
		return m.Quantity
	}
	return 0
}

func (m *OrderGoodsDetail) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *OrderGoodsDetail) GetApplyhospitalid() string {
	if m != nil {
		return m.Applyhospitalid
	}
	return ""
}

func (m *OrderGoodsDetail) GetChargeoff() int32 {
	if m != nil {
		return m.Chargeoff
	}
	return 0
}

func (m *OrderGoodsDetail) GetChargeoffcode() string {
	if m != nil {
		return m.Chargeoffcode
	}
	return ""
}

func (m *OrderGoodsDetail) GetChargeoffhospitalId() string {
	if m != nil {
		return m.ChargeoffhospitalId
	}
	return ""
}

func (m *OrderGoodsDetail) GetChargeofftime() string {
	if m != nil {
		return m.Chargeofftime
	}
	return ""
}

func (m *OrderGoodsDetail) GetCreatetime() string {
	if m != nil {
		return m.Createtime
	}
	return ""
}

func (m *OrderGoodsDetail) GetLasttime() string {
	if m != nil {
		return m.Lasttime
	}
	return ""
}

func (m *OrderGoodsDetail) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *OrderGoodsDetail) GetUseragent() int32 {
	if m != nil {
		return m.Useragent
	}
	return 0
}

func (m *OrderGoodsDetail) GetChargeoffmemberid() string {
	if m != nil {
		return m.Chargeoffmemberid
	}
	return ""
}

func (m *OrderGoodsDetail) GetGoodsimage() string {
	if m != nil {
		return m.Goodsimage
	}
	return ""
}

//根据条件获取仓库信息（V3.1版本的需要）请求参数
type GetWarehouseInfoByConditionRequest struct {
	//财务编码
	FinanceCode string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//仓库类型  3-门店仓，4-前置仓
	Category             int32    `protobuf:"varint,2,opt,name=category,proto3" json:"category"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarehouseInfoByConditionRequest) Reset()         { *m = GetWarehouseInfoByConditionRequest{} }
func (m *GetWarehouseInfoByConditionRequest) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseInfoByConditionRequest) ProtoMessage()    {}
func (*GetWarehouseInfoByConditionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{57}
}

func (m *GetWarehouseInfoByConditionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseInfoByConditionRequest.Unmarshal(m, b)
}
func (m *GetWarehouseInfoByConditionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseInfoByConditionRequest.Marshal(b, m, deterministic)
}
func (m *GetWarehouseInfoByConditionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseInfoByConditionRequest.Merge(m, src)
}
func (m *GetWarehouseInfoByConditionRequest) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseInfoByConditionRequest.Size(m)
}
func (m *GetWarehouseInfoByConditionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseInfoByConditionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseInfoByConditionRequest proto.InternalMessageInfo

func (m *GetWarehouseInfoByConditionRequest) GetFinanceCode() string {
	if m != nil {
		return m.FinanceCode
	}
	return ""
}

func (m *GetWarehouseInfoByConditionRequest) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

//根据条件获取仓库信息（V3.1版本的需要）响应参数
type GetWarehouseInfoByConditionResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//仓库信息
	Warehouseinfo        []*WarehouseList `protobuf:"bytes,4,rep,name=Warehouseinfo,proto3" json:"Warehouseinfo"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetWarehouseInfoByConditionResponse) Reset()         { *m = GetWarehouseInfoByConditionResponse{} }
func (m *GetWarehouseInfoByConditionResponse) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseInfoByConditionResponse) ProtoMessage()    {}
func (*GetWarehouseInfoByConditionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{58}
}

func (m *GetWarehouseInfoByConditionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseInfoByConditionResponse.Unmarshal(m, b)
}
func (m *GetWarehouseInfoByConditionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseInfoByConditionResponse.Marshal(b, m, deterministic)
}
func (m *GetWarehouseInfoByConditionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseInfoByConditionResponse.Merge(m, src)
}
func (m *GetWarehouseInfoByConditionResponse) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseInfoByConditionResponse.Size(m)
}
func (m *GetWarehouseInfoByConditionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseInfoByConditionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseInfoByConditionResponse proto.InternalMessageInfo

func (m *GetWarehouseInfoByConditionResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetWarehouseInfoByConditionResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetWarehouseInfoByConditionResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetWarehouseInfoByConditionResponse) GetWarehouseinfo() []*WarehouseList {
	if m != nil {
		return m.Warehouseinfo
	}
	return nil
}

//前置仓关联的门店信息 响应参数
type GetWarehouseRelationResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总行数
	TotalCount int32 `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	//仓库信息
	WarehouseRelationAarray []*WarehouseRelationList `protobuf:"bytes,5,rep,name=WarehouseRelationAarray,proto3" json:"WarehouseRelationAarray"`
	XXX_NoUnkeyedLiteral    struct{}                 `json:"-"`
	XXX_unrecognized        []byte                   `json:"-"`
	XXX_sizecache           int32                    `json:"-"`
}

func (m *GetWarehouseRelationResponse) Reset()         { *m = GetWarehouseRelationResponse{} }
func (m *GetWarehouseRelationResponse) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseRelationResponse) ProtoMessage()    {}
func (*GetWarehouseRelationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{59}
}

func (m *GetWarehouseRelationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseRelationResponse.Unmarshal(m, b)
}
func (m *GetWarehouseRelationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseRelationResponse.Marshal(b, m, deterministic)
}
func (m *GetWarehouseRelationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseRelationResponse.Merge(m, src)
}
func (m *GetWarehouseRelationResponse) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseRelationResponse.Size(m)
}
func (m *GetWarehouseRelationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseRelationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseRelationResponse proto.InternalMessageInfo

func (m *GetWarehouseRelationResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetWarehouseRelationResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetWarehouseRelationResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetWarehouseRelationResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetWarehouseRelationResponse) GetWarehouseRelationAarray() []*WarehouseRelationList {
	if m != nil {
		return m.WarehouseRelationAarray
	}
	return nil
}

type WarehouseRelationList struct {
	//仓库ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//仓库名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//门店ID
	ShopId               string   `protobuf:"bytes,3,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseRelationList) Reset()         { *m = WarehouseRelationList{} }
func (m *WarehouseRelationList) String() string { return proto.CompactTextString(m) }
func (*WarehouseRelationList) ProtoMessage()    {}
func (*WarehouseRelationList) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{60}
}

func (m *WarehouseRelationList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseRelationList.Unmarshal(m, b)
}
func (m *WarehouseRelationList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseRelationList.Marshal(b, m, deterministic)
}
func (m *WarehouseRelationList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseRelationList.Merge(m, src)
}
func (m *WarehouseRelationList) XXX_Size() int {
	return xxx_messageInfo_WarehouseRelationList.Size(m)
}
func (m *WarehouseRelationList) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseRelationList.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseRelationList proto.InternalMessageInfo

func (m *WarehouseRelationList) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseRelationList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WarehouseRelationList) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

//仓库操作日志请求参数
type WarehouseLogRequest struct {
	//开始时间
	Starttime string `protobuf:"bytes,1,opt,name=starttime,proto3" json:"starttime"`
	// 结束时间
	Endtime string `protobuf:"bytes,2,opt,name=endtime,proto3" json:"endtime"`
	//页码
	Pageindex int32 `protobuf:"varint,3,opt,name=pageindex,proto3" json:"pageindex"`
	//每页行数
	Pagesize             int32    `protobuf:"varint,4,opt,name=pagesize,proto3" json:"pagesize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseLogRequest) Reset()         { *m = WarehouseLogRequest{} }
func (m *WarehouseLogRequest) String() string { return proto.CompactTextString(m) }
func (*WarehouseLogRequest) ProtoMessage()    {}
func (*WarehouseLogRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{61}
}

func (m *WarehouseLogRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseLogRequest.Unmarshal(m, b)
}
func (m *WarehouseLogRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseLogRequest.Marshal(b, m, deterministic)
}
func (m *WarehouseLogRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseLogRequest.Merge(m, src)
}
func (m *WarehouseLogRequest) XXX_Size() int {
	return xxx_messageInfo_WarehouseLogRequest.Size(m)
}
func (m *WarehouseLogRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseLogRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseLogRequest proto.InternalMessageInfo

func (m *WarehouseLogRequest) GetStarttime() string {
	if m != nil {
		return m.Starttime
	}
	return ""
}

func (m *WarehouseLogRequest) GetEndtime() string {
	if m != nil {
		return m.Endtime
	}
	return ""
}

func (m *WarehouseLogRequest) GetPageindex() int32 {
	if m != nil {
		return m.Pageindex
	}
	return 0
}

func (m *WarehouseLogRequest) GetPagesize() int32 {
	if m != nil {
		return m.Pagesize
	}
	return 0
}

//仓库操作日志列表返回数据
type WarehouseLogResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总行数
	TotalCount int32 `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	//仓库数组
	WarehouseLogAarray   []*WarehouseLog `protobuf:"bytes,5,rep,name=WarehouseLogAarray,proto3" json:"WarehouseLogAarray"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *WarehouseLogResponse) Reset()         { *m = WarehouseLogResponse{} }
func (m *WarehouseLogResponse) String() string { return proto.CompactTextString(m) }
func (*WarehouseLogResponse) ProtoMessage()    {}
func (*WarehouseLogResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{62}
}

func (m *WarehouseLogResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseLogResponse.Unmarshal(m, b)
}
func (m *WarehouseLogResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseLogResponse.Marshal(b, m, deterministic)
}
func (m *WarehouseLogResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseLogResponse.Merge(m, src)
}
func (m *WarehouseLogResponse) XXX_Size() int {
	return xxx_messageInfo_WarehouseLogResponse.Size(m)
}
func (m *WarehouseLogResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseLogResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseLogResponse proto.InternalMessageInfo

func (m *WarehouseLogResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *WarehouseLogResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *WarehouseLogResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *WarehouseLogResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *WarehouseLogResponse) GetWarehouseLogAarray() []*WarehouseLog {
	if m != nil {
		return m.WarehouseLogAarray
	}
	return nil
}

//仓库操作日志列表数据集
type WarehouseLog struct {
	// ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//操作内容
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	// 操作人
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	// 操作时间
	Createdate           string   `protobuf:"bytes,4,opt,name=createdate,proto3" json:"createdate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseLog) Reset()         { *m = WarehouseLog{} }
func (m *WarehouseLog) String() string { return proto.CompactTextString(m) }
func (*WarehouseLog) ProtoMessage()    {}
func (*WarehouseLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{63}
}

func (m *WarehouseLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseLog.Unmarshal(m, b)
}
func (m *WarehouseLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseLog.Marshal(b, m, deterministic)
}
func (m *WarehouseLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseLog.Merge(m, src)
}
func (m *WarehouseLog) XXX_Size() int {
	return xxx_messageInfo_WarehouseLog.Size(m)
}
func (m *WarehouseLog) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseLog.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseLog proto.InternalMessageInfo

func (m *WarehouseLog) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WarehouseLog) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *WarehouseLog) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WarehouseLog) GetCreatedate() string {
	if m != nil {
		return m.Createdate
	}
	return ""
}

// 获取门店绑定信息
type ShopBindInfoRequest struct {
	//渠道 : 1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
	ChannelId int32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//绑定仓库类型 : 3门店仓 4前置仓 5前置虚拟仓
	BindType int32 `protobuf:"varint,2,opt,name=bind_type,json=bindType,proto3" json:"bind_type"`
	//门店名称 or 门店财务编码
	Search string `protobuf:"bytes,3,opt,name=search,proto3" json:"search"`
	//页
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//页大小
	PageSize             int32    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopBindInfoRequest) Reset()         { *m = ShopBindInfoRequest{} }
func (m *ShopBindInfoRequest) String() string { return proto.CompactTextString(m) }
func (*ShopBindInfoRequest) ProtoMessage()    {}
func (*ShopBindInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{64}
}

func (m *ShopBindInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopBindInfoRequest.Unmarshal(m, b)
}
func (m *ShopBindInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopBindInfoRequest.Marshal(b, m, deterministic)
}
func (m *ShopBindInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopBindInfoRequest.Merge(m, src)
}
func (m *ShopBindInfoRequest) XXX_Size() int {
	return xxx_messageInfo_ShopBindInfoRequest.Size(m)
}
func (m *ShopBindInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopBindInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShopBindInfoRequest proto.InternalMessageInfo

func (m *ShopBindInfoRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ShopBindInfoRequest) GetBindType() int32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *ShopBindInfoRequest) GetSearch() string {
	if m != nil {
		return m.Search
	}
	return ""
}

func (m *ShopBindInfoRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ShopBindInfoRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ShopBindInfoRespond struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总行数
	TotalCount int32 `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	//仓库详细
	Info                 []*ShopBindInfo `protobuf:"bytes,5,rep,name=info,proto3" json:"info"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ShopBindInfoRespond) Reset()         { *m = ShopBindInfoRespond{} }
func (m *ShopBindInfoRespond) String() string { return proto.CompactTextString(m) }
func (*ShopBindInfoRespond) ProtoMessage()    {}
func (*ShopBindInfoRespond) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{65}
}

func (m *ShopBindInfoRespond) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopBindInfoRespond.Unmarshal(m, b)
}
func (m *ShopBindInfoRespond) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopBindInfoRespond.Marshal(b, m, deterministic)
}
func (m *ShopBindInfoRespond) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopBindInfoRespond.Merge(m, src)
}
func (m *ShopBindInfoRespond) XXX_Size() int {
	return xxx_messageInfo_ShopBindInfoRespond.Size(m)
}
func (m *ShopBindInfoRespond) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopBindInfoRespond.DiscardUnknown(m)
}

var xxx_messageInfo_ShopBindInfoRespond proto.InternalMessageInfo

func (m *ShopBindInfoRespond) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ShopBindInfoRespond) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ShopBindInfoRespond) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *ShopBindInfoRespond) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *ShopBindInfoRespond) GetInfo() []*ShopBindInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type ShopBindInfo struct {
	// ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//渠道 : 2美团
	ChannelId int32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//门店ID
	ShopId string `protobuf:"bytes,3,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//门店名称
	ShopName string `protobuf:"bytes,4,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	//仓库id
	WarehouseId int32 `protobuf:"varint,5,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	//仓库名称
	WarehouseName string `protobuf:"bytes,6,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	//仓库编码
	Code string `protobuf:"bytes,7,opt,name=code,proto3" json:"code"`
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓 5-前置虚拟仓)
	Category int32 `protobuf:"varint,8,opt,name=category,proto3" json:"category"`
	//A8客户编码
	CustomCode           string   `protobuf:"bytes,9,opt,name=custom_code,json=customCode,proto3" json:"custom_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopBindInfo) Reset()         { *m = ShopBindInfo{} }
func (m *ShopBindInfo) String() string { return proto.CompactTextString(m) }
func (*ShopBindInfo) ProtoMessage()    {}
func (*ShopBindInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{66}
}

func (m *ShopBindInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopBindInfo.Unmarshal(m, b)
}
func (m *ShopBindInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopBindInfo.Marshal(b, m, deterministic)
}
func (m *ShopBindInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopBindInfo.Merge(m, src)
}
func (m *ShopBindInfo) XXX_Size() int {
	return xxx_messageInfo_ShopBindInfo.Size(m)
}
func (m *ShopBindInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopBindInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShopBindInfo proto.InternalMessageInfo

func (m *ShopBindInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ShopBindInfo) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ShopBindInfo) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *ShopBindInfo) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *ShopBindInfo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *ShopBindInfo) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

func (m *ShopBindInfo) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *ShopBindInfo) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *ShopBindInfo) GetCustomCode() string {
	if m != nil {
		return m.CustomCode
	}
	return ""
}

// 获取门店仓库信息
type ShopBindWarehouseReq struct {
	//渠道 : 1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提
	ChannelId int32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//绑定仓库类型 : (1-电商仓，2-区域仓，3-门店仓，4-前置仓 5-前置虚拟仓 6-门店前置仓)
	BindType int32 `protobuf:"varint,2,opt,name=bind_type,json=bindType,proto3" json:"bind_type"`
	//门店名称 or 门店财务编码
	ShopSearch string `protobuf:"bytes,3,opt,name=shop_search,json=shopSearch,proto3" json:"shop_search"`
	//仓库名称 or 仓库编码
	WarehouseSearch string `protobuf:"bytes,4,opt,name=warehouse_search,json=warehouseSearch,proto3" json:"warehouse_search"`
	//页
	PageIndex int32 `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//页大小
	PageSize             int32    `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopBindWarehouseReq) Reset()         { *m = ShopBindWarehouseReq{} }
func (m *ShopBindWarehouseReq) String() string { return proto.CompactTextString(m) }
func (*ShopBindWarehouseReq) ProtoMessage()    {}
func (*ShopBindWarehouseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{67}
}

func (m *ShopBindWarehouseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopBindWarehouseReq.Unmarshal(m, b)
}
func (m *ShopBindWarehouseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopBindWarehouseReq.Marshal(b, m, deterministic)
}
func (m *ShopBindWarehouseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopBindWarehouseReq.Merge(m, src)
}
func (m *ShopBindWarehouseReq) XXX_Size() int {
	return xxx_messageInfo_ShopBindWarehouseReq.Size(m)
}
func (m *ShopBindWarehouseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopBindWarehouseReq.DiscardUnknown(m)
}

var xxx_messageInfo_ShopBindWarehouseReq proto.InternalMessageInfo

func (m *ShopBindWarehouseReq) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ShopBindWarehouseReq) GetBindType() int32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *ShopBindWarehouseReq) GetShopSearch() string {
	if m != nil {
		return m.ShopSearch
	}
	return ""
}

func (m *ShopBindWarehouseReq) GetWarehouseSearch() string {
	if m != nil {
		return m.WarehouseSearch
	}
	return ""
}

func (m *ShopBindWarehouseReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ShopBindWarehouseReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ShopBindWarehouseRes struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//总行数
	TotalCount int32 `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	//仓库详细
	Info                 []*ShopBindWarehouseInfo `protobuf:"bytes,5,rep,name=info,proto3" json:"info"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ShopBindWarehouseRes) Reset()         { *m = ShopBindWarehouseRes{} }
func (m *ShopBindWarehouseRes) String() string { return proto.CompactTextString(m) }
func (*ShopBindWarehouseRes) ProtoMessage()    {}
func (*ShopBindWarehouseRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{68}
}

func (m *ShopBindWarehouseRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopBindWarehouseRes.Unmarshal(m, b)
}
func (m *ShopBindWarehouseRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopBindWarehouseRes.Marshal(b, m, deterministic)
}
func (m *ShopBindWarehouseRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopBindWarehouseRes.Merge(m, src)
}
func (m *ShopBindWarehouseRes) XXX_Size() int {
	return xxx_messageInfo_ShopBindWarehouseRes.Size(m)
}
func (m *ShopBindWarehouseRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopBindWarehouseRes.DiscardUnknown(m)
}

var xxx_messageInfo_ShopBindWarehouseRes proto.InternalMessageInfo

func (m *ShopBindWarehouseRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ShopBindWarehouseRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ShopBindWarehouseRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *ShopBindWarehouseRes) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *ShopBindWarehouseRes) GetInfo() []*ShopBindWarehouseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type ShopBindWarehouseInfo struct {
	// ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//渠道 : 2美团
	ChannelId int32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//门店ID
	ShopId string `protobuf:"bytes,3,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//门店名称
	ShopName string `protobuf:"bytes,4,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	//仓库id
	WarehouseId int32 `protobuf:"varint,5,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	//仓库名称
	WarehouseName string `protobuf:"bytes,6,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	//仓库编码
	Code string `protobuf:"bytes,7,opt,name=code,proto3" json:"code"`
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓 5-前置虚拟仓)
	Category int32 `protobuf:"varint,8,opt,name=category,proto3" json:"category"`
	//A8客户编码
	CustomCode string `protobuf:"bytes,9,opt,name=custom_code,json=customCode,proto3" json:"custom_code"`
	//阿闻外卖
	AwenDeliveryWarehourse string `protobuf:"bytes,10,opt,name=awen_delivery_warehourse,json=awenDeliveryWarehourse,proto3" json:"awen_delivery_warehourse"`
	//阿闻自提
	AwenPickupWarehourse string `protobuf:"bytes,11,opt,name=awen_pickup_warehourse,json=awenPickupWarehourse,proto3" json:"awen_pickup_warehourse"`
	//美团
	MtWarehourse string `protobuf:"bytes,12,opt,name=mt_warehourse,json=mtWarehourse,proto3" json:"mt_warehourse"`
	//饿了么
	EleWarehourse string `protobuf:"bytes,13,opt,name=ele_warehourse,json=eleWarehourse,proto3" json:"ele_warehourse"`
	//京东到家
	JdWarehourse string `protobuf:"bytes,14,opt,name=jd_warehourse,json=jdWarehourse,proto3" json:"jd_warehourse"`
	//互联网医院
	HospitalsWarehourse  string   `protobuf:"bytes,15,opt,name=hospitals_warehourse,json=hospitalsWarehourse,proto3" json:"hospitals_warehourse"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopBindWarehouseInfo) Reset()         { *m = ShopBindWarehouseInfo{} }
func (m *ShopBindWarehouseInfo) String() string { return proto.CompactTextString(m) }
func (*ShopBindWarehouseInfo) ProtoMessage()    {}
func (*ShopBindWarehouseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{69}
}

func (m *ShopBindWarehouseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopBindWarehouseInfo.Unmarshal(m, b)
}
func (m *ShopBindWarehouseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopBindWarehouseInfo.Marshal(b, m, deterministic)
}
func (m *ShopBindWarehouseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopBindWarehouseInfo.Merge(m, src)
}
func (m *ShopBindWarehouseInfo) XXX_Size() int {
	return xxx_messageInfo_ShopBindWarehouseInfo.Size(m)
}
func (m *ShopBindWarehouseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopBindWarehouseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShopBindWarehouseInfo proto.InternalMessageInfo

func (m *ShopBindWarehouseInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ShopBindWarehouseInfo) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ShopBindWarehouseInfo) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *ShopBindWarehouseInfo) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *ShopBindWarehouseInfo) GetCustomCode() string {
	if m != nil {
		return m.CustomCode
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetAwenDeliveryWarehourse() string {
	if m != nil {
		return m.AwenDeliveryWarehourse
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetAwenPickupWarehourse() string {
	if m != nil {
		return m.AwenPickupWarehourse
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetMtWarehourse() string {
	if m != nil {
		return m.MtWarehourse
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetEleWarehourse() string {
	if m != nil {
		return m.EleWarehourse
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetJdWarehourse() string {
	if m != nil {
		return m.JdWarehourse
	}
	return ""
}

func (m *ShopBindWarehouseInfo) GetHospitalsWarehourse() string {
	if m != nil {
		return m.HospitalsWarehourse
	}
	return ""
}

type WarehouseInfo struct {
	ChannelId            int32    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Code                 string   `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	Category             int32    `protobuf:"varint,4,opt,name=category,proto3" json:"category"`
	ShopId               string   `protobuf:"bytes,5,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	WarehouseId          int32    `protobuf:"varint,6,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseInfo) Reset()         { *m = WarehouseInfo{} }
func (m *WarehouseInfo) String() string { return proto.CompactTextString(m) }
func (*WarehouseInfo) ProtoMessage()    {}
func (*WarehouseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{70}
}

func (m *WarehouseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseInfo.Unmarshal(m, b)
}
func (m *WarehouseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseInfo.Marshal(b, m, deterministic)
}
func (m *WarehouseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseInfo.Merge(m, src)
}
func (m *WarehouseInfo) XXX_Size() int {
	return xxx_messageInfo_WarehouseInfo.Size(m)
}
func (m *WarehouseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseInfo proto.InternalMessageInfo

func (m *WarehouseInfo) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WarehouseInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WarehouseInfo) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *WarehouseInfo) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *WarehouseInfo) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *WarehouseInfo) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

// 使用店铺id获取其绑定仓库信息
type ShopBindInfoByShopIdRequest struct {
	//门店id
	ShopId []string `protobuf:"bytes,1,rep,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//渠道 1美团
	ChannelId            int32    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopBindInfoByShopIdRequest) Reset()         { *m = ShopBindInfoByShopIdRequest{} }
func (m *ShopBindInfoByShopIdRequest) String() string { return proto.CompactTextString(m) }
func (*ShopBindInfoByShopIdRequest) ProtoMessage()    {}
func (*ShopBindInfoByShopIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{71}
}

func (m *ShopBindInfoByShopIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopBindInfoByShopIdRequest.Unmarshal(m, b)
}
func (m *ShopBindInfoByShopIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopBindInfoByShopIdRequest.Marshal(b, m, deterministic)
}
func (m *ShopBindInfoByShopIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopBindInfoByShopIdRequest.Merge(m, src)
}
func (m *ShopBindInfoByShopIdRequest) XXX_Size() int {
	return xxx_messageInfo_ShopBindInfoByShopIdRequest.Size(m)
}
func (m *ShopBindInfoByShopIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopBindInfoByShopIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShopBindInfoByShopIdRequest proto.InternalMessageInfo

func (m *ShopBindInfoByShopIdRequest) GetShopId() []string {
	if m != nil {
		return m.ShopId
	}
	return nil
}

func (m *ShopBindInfoByShopIdRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ShopBindInfoByShopIdRespond struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string          `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Info                 []*ShopBindInfo `protobuf:"bytes,4,rep,name=info,proto3" json:"info"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ShopBindInfoByShopIdRespond) Reset()         { *m = ShopBindInfoByShopIdRespond{} }
func (m *ShopBindInfoByShopIdRespond) String() string { return proto.CompactTextString(m) }
func (*ShopBindInfoByShopIdRespond) ProtoMessage()    {}
func (*ShopBindInfoByShopIdRespond) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{72}
}

func (m *ShopBindInfoByShopIdRespond) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopBindInfoByShopIdRespond.Unmarshal(m, b)
}
func (m *ShopBindInfoByShopIdRespond) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopBindInfoByShopIdRespond.Marshal(b, m, deterministic)
}
func (m *ShopBindInfoByShopIdRespond) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopBindInfoByShopIdRespond.Merge(m, src)
}
func (m *ShopBindInfoByShopIdRespond) XXX_Size() int {
	return xxx_messageInfo_ShopBindInfoByShopIdRespond.Size(m)
}
func (m *ShopBindInfoByShopIdRespond) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopBindInfoByShopIdRespond.DiscardUnknown(m)
}

var xxx_messageInfo_ShopBindInfoByShopIdRespond proto.InternalMessageInfo

func (m *ShopBindInfoByShopIdRespond) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ShopBindInfoByShopIdRespond) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ShopBindInfoByShopIdRespond) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *ShopBindInfoByShopIdRespond) GetInfo() []*ShopBindInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BindShopsRequest struct {
	//渠道 1美团
	ChannelId            int32    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindShopsRequest) Reset()         { *m = BindShopsRequest{} }
func (m *BindShopsRequest) String() string { return proto.CompactTextString(m) }
func (*BindShopsRequest) ProtoMessage()    {}
func (*BindShopsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{73}
}

func (m *BindShopsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindShopsRequest.Unmarshal(m, b)
}
func (m *BindShopsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindShopsRequest.Marshal(b, m, deterministic)
}
func (m *BindShopsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindShopsRequest.Merge(m, src)
}
func (m *BindShopsRequest) XXX_Size() int {
	return xxx_messageInfo_BindShopsRequest.Size(m)
}
func (m *BindShopsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BindShopsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BindShopsRequest proto.InternalMessageInfo

func (m *BindShopsRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type BindShopsRespond struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string      `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Info                 []*BindInfo `protobuf:"bytes,4,rep,name=info,proto3" json:"info"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BindShopsRespond) Reset()         { *m = BindShopsRespond{} }
func (m *BindShopsRespond) String() string { return proto.CompactTextString(m) }
func (*BindShopsRespond) ProtoMessage()    {}
func (*BindShopsRespond) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{74}
}

func (m *BindShopsRespond) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindShopsRespond.Unmarshal(m, b)
}
func (m *BindShopsRespond) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindShopsRespond.Marshal(b, m, deterministic)
}
func (m *BindShopsRespond) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindShopsRespond.Merge(m, src)
}
func (m *BindShopsRespond) XXX_Size() int {
	return xxx_messageInfo_BindShopsRespond.Size(m)
}
func (m *BindShopsRespond) XXX_DiscardUnknown() {
	xxx_messageInfo_BindShopsRespond.DiscardUnknown(m)
}

var xxx_messageInfo_BindShopsRespond proto.InternalMessageInfo

func (m *BindShopsRespond) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BindShopsRespond) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BindShopsRespond) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *BindShopsRespond) GetInfo() []*BindInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BindInfo struct {
	Channel int32 `protobuf:"varint,1,opt,name=channel,proto3" json:"channel"`
	//绑定门店仓的门店数量
	ShopNum int32 `protobuf:"varint,2,opt,name=shop_num,json=shopNum,proto3" json:"shop_num"`
	//绑定前置仓的门店数量
	ShopNumFront int32 `protobuf:"varint,3,opt,name=shop_num_front,json=shopNumFront,proto3" json:"shop_num_front"`
	//绑定前置虚拟仓的门店数量
	ShopNumVirtual int32 `protobuf:"varint,4,opt,name=shop_num_virtual,json=shopNumVirtual,proto3" json:"shop_num_virtual"`
	// 绑定电商仓门店数量
	ShopNumDs            int32    `protobuf:"varint,5,opt,name=shop_num_ds,json=shopNumDs,proto3" json:"shop_num_ds"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindInfo) Reset()         { *m = BindInfo{} }
func (m *BindInfo) String() string { return proto.CompactTextString(m) }
func (*BindInfo) ProtoMessage()    {}
func (*BindInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{75}
}

func (m *BindInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindInfo.Unmarshal(m, b)
}
func (m *BindInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindInfo.Marshal(b, m, deterministic)
}
func (m *BindInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindInfo.Merge(m, src)
}
func (m *BindInfo) XXX_Size() int {
	return xxx_messageInfo_BindInfo.Size(m)
}
func (m *BindInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BindInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BindInfo proto.InternalMessageInfo

func (m *BindInfo) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *BindInfo) GetShopNum() int32 {
	if m != nil {
		return m.ShopNum
	}
	return 0
}

func (m *BindInfo) GetShopNumFront() int32 {
	if m != nil {
		return m.ShopNumFront
	}
	return 0
}

func (m *BindInfo) GetShopNumVirtual() int32 {
	if m != nil {
		return m.ShopNumVirtual
	}
	return 0
}

func (m *BindInfo) GetShopNumDs() int32 {
	if m != nil {
		return m.ShopNumDs
	}
	return 0
}

type StoreWarehouseRelationShopRequest struct {
	//渠道 : 2美团
	ChannelId int32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 店铺绑定仓库信息
	Wrs                  []*WarehouseRelationShop `protobuf:"bytes,2,rep,name=wrs,proto3" json:"wrs"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *StoreWarehouseRelationShopRequest) Reset()         { *m = StoreWarehouseRelationShopRequest{} }
func (m *StoreWarehouseRelationShopRequest) String() string { return proto.CompactTextString(m) }
func (*StoreWarehouseRelationShopRequest) ProtoMessage()    {}
func (*StoreWarehouseRelationShopRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{76}
}

func (m *StoreWarehouseRelationShopRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreWarehouseRelationShopRequest.Unmarshal(m, b)
}
func (m *StoreWarehouseRelationShopRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreWarehouseRelationShopRequest.Marshal(b, m, deterministic)
}
func (m *StoreWarehouseRelationShopRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreWarehouseRelationShopRequest.Merge(m, src)
}
func (m *StoreWarehouseRelationShopRequest) XXX_Size() int {
	return xxx_messageInfo_StoreWarehouseRelationShopRequest.Size(m)
}
func (m *StoreWarehouseRelationShopRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreWarehouseRelationShopRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StoreWarehouseRelationShopRequest proto.InternalMessageInfo

func (m *StoreWarehouseRelationShopRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StoreWarehouseRelationShopRequest) GetWrs() []*WarehouseRelationShop {
	if m != nil {
		return m.Wrs
	}
	return nil
}

type WarehouseRelationShop struct {
	//门店ID
	ShopId string `protobuf:"bytes,1,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//门店名称
	ShopName string `protobuf:"bytes,2,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	//仓库id
	WarehouseId int32 `protobuf:"varint,3,opt,name=warehouse_id,json=warehouseId,proto3" json:"warehouse_id"`
	//仓库名称
	WarehouseName string `protobuf:"bytes,4,opt,name=warehouse_name,json=warehouseName,proto3" json:"warehouse_name"`
	//仓库类型 4前置仓 5前置虚拟仓
	Category int32 `protobuf:"varint,5,opt,name=category,proto3" json:"category"`
	//仓库编码
	Code                 string   `protobuf:"bytes,6,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarehouseRelationShop) Reset()         { *m = WarehouseRelationShop{} }
func (m *WarehouseRelationShop) String() string { return proto.CompactTextString(m) }
func (*WarehouseRelationShop) ProtoMessage()    {}
func (*WarehouseRelationShop) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{77}
}

func (m *WarehouseRelationShop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarehouseRelationShop.Unmarshal(m, b)
}
func (m *WarehouseRelationShop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarehouseRelationShop.Marshal(b, m, deterministic)
}
func (m *WarehouseRelationShop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarehouseRelationShop.Merge(m, src)
}
func (m *WarehouseRelationShop) XXX_Size() int {
	return xxx_messageInfo_WarehouseRelationShop.Size(m)
}
func (m *WarehouseRelationShop) XXX_DiscardUnknown() {
	xxx_messageInfo_WarehouseRelationShop.DiscardUnknown(m)
}

var xxx_messageInfo_WarehouseRelationShop proto.InternalMessageInfo

func (m *WarehouseRelationShop) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *WarehouseRelationShop) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *WarehouseRelationShop) GetWarehouseId() int32 {
	if m != nil {
		return m.WarehouseId
	}
	return 0
}

func (m *WarehouseRelationShop) GetWarehouseName() string {
	if m != nil {
		return m.WarehouseName
	}
	return ""
}

func (m *WarehouseRelationShop) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *WarehouseRelationShop) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

type StoreWarehouseRelationShopRespond struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	// 失败的shop_id
	ShopIds              []string `protobuf:"bytes,4,rep,name=shop_ids,json=shopIds,proto3" json:"shop_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreWarehouseRelationShopRespond) Reset()         { *m = StoreWarehouseRelationShopRespond{} }
func (m *StoreWarehouseRelationShopRespond) String() string { return proto.CompactTextString(m) }
func (*StoreWarehouseRelationShopRespond) ProtoMessage()    {}
func (*StoreWarehouseRelationShopRespond) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{78}
}

func (m *StoreWarehouseRelationShopRespond) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreWarehouseRelationShopRespond.Unmarshal(m, b)
}
func (m *StoreWarehouseRelationShopRespond) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreWarehouseRelationShopRespond.Marshal(b, m, deterministic)
}
func (m *StoreWarehouseRelationShopRespond) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreWarehouseRelationShopRespond.Merge(m, src)
}
func (m *StoreWarehouseRelationShopRespond) XXX_Size() int {
	return xxx_messageInfo_StoreWarehouseRelationShopRespond.Size(m)
}
func (m *StoreWarehouseRelationShopRespond) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreWarehouseRelationShopRespond.DiscardUnknown(m)
}

var xxx_messageInfo_StoreWarehouseRelationShopRespond proto.InternalMessageInfo

func (m *StoreWarehouseRelationShopRespond) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StoreWarehouseRelationShopRespond) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *StoreWarehouseRelationShopRespond) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *StoreWarehouseRelationShopRespond) GetShopIds() []string {
	if m != nil {
		return m.ShopIds
	}
	return nil
}

type GetA8WareHouseListRequest struct {
	BeginTime            string   `protobuf:"bytes,1,opt,name=BeginTime,proto3" json:"BeginTime"`
	EndTime              string   `protobuf:"bytes,2,opt,name=EndTime,proto3" json:"EndTime"`
	PageIndex            int32    `protobuf:"varint,3,opt,name=PageIndex,proto3" json:"PageIndex"`
	PageSize             int32    `protobuf:"varint,4,opt,name=PageSize,proto3" json:"PageSize"`
	Condition            string   `protobuf:"bytes,5,opt,name=Condition,proto3" json:"Condition"`
	IsAll                int32    `protobuf:"varint,6,opt,name=IsAll,proto3" json:"IsAll"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetA8WareHouseListRequest) Reset()         { *m = GetA8WareHouseListRequest{} }
func (m *GetA8WareHouseListRequest) String() string { return proto.CompactTextString(m) }
func (*GetA8WareHouseListRequest) ProtoMessage()    {}
func (*GetA8WareHouseListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{79}
}

func (m *GetA8WareHouseListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetA8WareHouseListRequest.Unmarshal(m, b)
}
func (m *GetA8WareHouseListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetA8WareHouseListRequest.Marshal(b, m, deterministic)
}
func (m *GetA8WareHouseListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetA8WareHouseListRequest.Merge(m, src)
}
func (m *GetA8WareHouseListRequest) XXX_Size() int {
	return xxx_messageInfo_GetA8WareHouseListRequest.Size(m)
}
func (m *GetA8WareHouseListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetA8WareHouseListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetA8WareHouseListRequest proto.InternalMessageInfo

func (m *GetA8WareHouseListRequest) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *GetA8WareHouseListRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GetA8WareHouseListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetA8WareHouseListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetA8WareHouseListRequest) GetCondition() string {
	if m != nil {
		return m.Condition
	}
	return ""
}

func (m *GetA8WareHouseListRequest) GetIsAll() int32 {
	if m != nil {
		return m.IsAll
	}
	return 0
}

type GetA8WareHouseListResponse struct {
	TotalCount           int32          `protobuf:"varint,1,opt,name=totalCount,proto3" json:"totalCount"`
	Data                 []*A8WareHouse `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetA8WareHouseListResponse) Reset()         { *m = GetA8WareHouseListResponse{} }
func (m *GetA8WareHouseListResponse) String() string { return proto.CompactTextString(m) }
func (*GetA8WareHouseListResponse) ProtoMessage()    {}
func (*GetA8WareHouseListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{80}
}

func (m *GetA8WareHouseListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetA8WareHouseListResponse.Unmarshal(m, b)
}
func (m *GetA8WareHouseListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetA8WareHouseListResponse.Marshal(b, m, deterministic)
}
func (m *GetA8WareHouseListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetA8WareHouseListResponse.Merge(m, src)
}
func (m *GetA8WareHouseListResponse) XXX_Size() int {
	return xxx_messageInfo_GetA8WareHouseListResponse.Size(m)
}
func (m *GetA8WareHouseListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetA8WareHouseListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetA8WareHouseListResponse proto.InternalMessageInfo

func (m *GetA8WareHouseListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetA8WareHouseListResponse) GetData() []*A8WareHouse {
	if m != nil {
		return m.Data
	}
	return nil
}

type A8WareHouse struct {
	Kid                  int32    `protobuf:"varint,1,opt,name=kid,proto3" json:"kid"`
	TypeId               string   `protobuf:"bytes,2,opt,name=typeId,proto3" json:"typeId"`
	Usercode             string   `protobuf:"bytes,3,opt,name=usercode,proto3" json:"usercode"`
	FullnameStype        string   `protobuf:"bytes,4,opt,name=fullnameStype,proto3" json:"fullnameStype"`
	FullnameStock        string   `protobuf:"bytes,5,opt,name=fullnameStock,proto3" json:"fullnameStock"`
	Address              string   `protobuf:"bytes,6,opt,name=address,proto3" json:"address"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *A8WareHouse) Reset()         { *m = A8WareHouse{} }
func (m *A8WareHouse) String() string { return proto.CompactTextString(m) }
func (*A8WareHouse) ProtoMessage()    {}
func (*A8WareHouse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{81}
}

func (m *A8WareHouse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_A8WareHouse.Unmarshal(m, b)
}
func (m *A8WareHouse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_A8WareHouse.Marshal(b, m, deterministic)
}
func (m *A8WareHouse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_A8WareHouse.Merge(m, src)
}
func (m *A8WareHouse) XXX_Size() int {
	return xxx_messageInfo_A8WareHouse.Size(m)
}
func (m *A8WareHouse) XXX_DiscardUnknown() {
	xxx_messageInfo_A8WareHouse.DiscardUnknown(m)
}

var xxx_messageInfo_A8WareHouse proto.InternalMessageInfo

func (m *A8WareHouse) GetKid() int32 {
	if m != nil {
		return m.Kid
	}
	return 0
}

func (m *A8WareHouse) GetTypeId() string {
	if m != nil {
		return m.TypeId
	}
	return ""
}

func (m *A8WareHouse) GetUsercode() string {
	if m != nil {
		return m.Usercode
	}
	return ""
}

func (m *A8WareHouse) GetFullnameStype() string {
	if m != nil {
		return m.FullnameStype
	}
	return ""
}

func (m *A8WareHouse) GetFullnameStock() string {
	if m != nil {
		return m.FullnameStock
	}
	return ""
}

func (m *A8WareHouse) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

//根据code取仓库信息-- 请求参数
type GetWarehouseInfoByCodeRequest struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Category             int32    `protobuf:"varint,2,opt,name=category,proto3" json:"category"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarehouseInfoByCodeRequest) Reset()         { *m = GetWarehouseInfoByCodeRequest{} }
func (m *GetWarehouseInfoByCodeRequest) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseInfoByCodeRequest) ProtoMessage()    {}
func (*GetWarehouseInfoByCodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{82}
}

func (m *GetWarehouseInfoByCodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseInfoByCodeRequest.Unmarshal(m, b)
}
func (m *GetWarehouseInfoByCodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseInfoByCodeRequest.Marshal(b, m, deterministic)
}
func (m *GetWarehouseInfoByCodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseInfoByCodeRequest.Merge(m, src)
}
func (m *GetWarehouseInfoByCodeRequest) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseInfoByCodeRequest.Size(m)
}
func (m *GetWarehouseInfoByCodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseInfoByCodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseInfoByCodeRequest proto.InternalMessageInfo

func (m *GetWarehouseInfoByCodeRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *GetWarehouseInfoByCodeRequest) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

//根据财务编码获取仓库信息-- 响应参数
type GetWarehouseInfoByCodeResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//仓库信息
	Warehouseinfo        *WarehouseList `protobuf:"bytes,4,opt,name=Warehouseinfo,proto3" json:"Warehouseinfo"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetWarehouseInfoByCodeResponse) Reset()         { *m = GetWarehouseInfoByCodeResponse{} }
func (m *GetWarehouseInfoByCodeResponse) String() string { return proto.CompactTextString(m) }
func (*GetWarehouseInfoByCodeResponse) ProtoMessage()    {}
func (*GetWarehouseInfoByCodeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{83}
}

func (m *GetWarehouseInfoByCodeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarehouseInfoByCodeResponse.Unmarshal(m, b)
}
func (m *GetWarehouseInfoByCodeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarehouseInfoByCodeResponse.Marshal(b, m, deterministic)
}
func (m *GetWarehouseInfoByCodeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarehouseInfoByCodeResponse.Merge(m, src)
}
func (m *GetWarehouseInfoByCodeResponse) XXX_Size() int {
	return xxx_messageInfo_GetWarehouseInfoByCodeResponse.Size(m)
}
func (m *GetWarehouseInfoByCodeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarehouseInfoByCodeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarehouseInfoByCodeResponse proto.InternalMessageInfo

func (m *GetWarehouseInfoByCodeResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetWarehouseInfoByCodeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetWarehouseInfoByCodeResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetWarehouseInfoByCodeResponse) GetWarehouseinfo() *WarehouseList {
	if m != nil {
		return m.Warehouseinfo
	}
	return nil
}

type SyncOmsWarehouseRequest struct {
	//仓库编号
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	//仓库名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)
	Category int32 `protobuf:"varint,3,opt,name=category,proto3" json:"category"`
	//仓库地址
	Address string `protobuf:"bytes,4,opt,name=address,proto3" json:"address"`
	//仓库联系人
	Contacts string `protobuf:"bytes,5,opt,name=contacts,proto3" json:"contacts"`
	//仓库联系方式
	Tel string `protobuf:"bytes,6,opt,name=tel,proto3" json:"tel"`
	//第三方仓库ID
	Thirdid              string   `protobuf:"bytes,7,opt,name=thirdid,proto3" json:"thirdid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SyncOmsWarehouseRequest) Reset()         { *m = SyncOmsWarehouseRequest{} }
func (m *SyncOmsWarehouseRequest) String() string { return proto.CompactTextString(m) }
func (*SyncOmsWarehouseRequest) ProtoMessage()    {}
func (*SyncOmsWarehouseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{84}
}

func (m *SyncOmsWarehouseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncOmsWarehouseRequest.Unmarshal(m, b)
}
func (m *SyncOmsWarehouseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncOmsWarehouseRequest.Marshal(b, m, deterministic)
}
func (m *SyncOmsWarehouseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncOmsWarehouseRequest.Merge(m, src)
}
func (m *SyncOmsWarehouseRequest) XXX_Size() int {
	return xxx_messageInfo_SyncOmsWarehouseRequest.Size(m)
}
func (m *SyncOmsWarehouseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncOmsWarehouseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncOmsWarehouseRequest proto.InternalMessageInfo

func (m *SyncOmsWarehouseRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *SyncOmsWarehouseRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SyncOmsWarehouseRequest) GetCategory() int32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *SyncOmsWarehouseRequest) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *SyncOmsWarehouseRequest) GetContacts() string {
	if m != nil {
		return m.Contacts
	}
	return ""
}

func (m *SyncOmsWarehouseRequest) GetTel() string {
	if m != nil {
		return m.Tel
	}
	return ""
}

func (m *SyncOmsWarehouseRequest) GetThirdid() string {
	if m != nil {
		return m.Thirdid
	}
	return ""
}

//店铺关联所有仓库列表请求参数
type ShopWarehouseListRequest struct {
	//财务编码
	ShopId               string   `protobuf:"bytes,1,opt,name=ShopId,proto3" json:"ShopId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopWarehouseListRequest) Reset()         { *m = ShopWarehouseListRequest{} }
func (m *ShopWarehouseListRequest) String() string { return proto.CompactTextString(m) }
func (*ShopWarehouseListRequest) ProtoMessage()    {}
func (*ShopWarehouseListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_87e14ea68acb18a5, []int{85}
}

func (m *ShopWarehouseListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopWarehouseListRequest.Unmarshal(m, b)
}
func (m *ShopWarehouseListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopWarehouseListRequest.Marshal(b, m, deterministic)
}
func (m *ShopWarehouseListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopWarehouseListRequest.Merge(m, src)
}
func (m *ShopWarehouseListRequest) XXX_Size() int {
	return xxx_messageInfo_ShopWarehouseListRequest.Size(m)
}
func (m *ShopWarehouseListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopWarehouseListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShopWarehouseListRequest proto.InternalMessageInfo

func (m *ShopWarehouseListRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func init() {
	proto.RegisterType((*RemoveShopRequest)(nil), "dc.RemoveShopRequest")
	proto.RegisterType((*RemoveShopRespond)(nil), "dc.RemoveShopRespond")
	proto.RegisterType((*InitShopDataRequest)(nil), "dc.InitShopDataRequest")
	proto.RegisterType((*InitShopDataRespond)(nil), "dc.InitShopDataRespond")
	proto.RegisterType((*SWInfo)(nil), "dc.SWInfo")
	proto.RegisterType((*GetShopWarehouseInfoByFinanceCodeRequest)(nil), "dc.GetShopWarehouseInfoByFinanceCodeRequest")
	proto.RegisterType((*GetShopWarehouseInfoByFinanceCodeResponse)(nil), "dc.GetShopWarehouseInfoByFinanceCodeResponse")
	proto.RegisterType((*GetStoreListByCategoryRequest)(nil), "dc.GetStoreListByCategoryRequest")
	proto.RegisterType((*GetStoreListByCategoryResponse)(nil), "dc.GetStoreListByCategoryResponse")
	proto.RegisterType((*GetWarehouseInfoByFanceCodesRequest)(nil), "dc.GetWarehouseInfoByFanceCodesRequest")
	proto.RegisterType((*GetWarehouseInfoByFanceCodesResponse)(nil), "dc.GetWarehouseInfoByFanceCodesResponse")
	proto.RegisterType((*GetWarehouseInfoByFanceCodeRequest)(nil), "dc.GetWarehouseInfoByFanceCodeRequest")
	proto.RegisterType((*GetWarehouseInfoByFanceCodeResponse)(nil), "dc.GetWarehouseInfoByFanceCodeResponse")
	proto.RegisterType((*WarehouseRelationRequest)(nil), "dc.WarehouseRelationRequest")
	proto.RegisterType((*ShopInfo)(nil), "dc.ShopInfo")
	proto.RegisterType((*PreposeWarehouseRelationRequest)(nil), "dc.PreposeWarehouseRelationRequest")
	proto.RegisterType((*WarehouseRelationListRequest)(nil), "dc.WarehouseRelationListRequest")
	proto.RegisterType((*WarehouseRelationListResponse)(nil), "dc.WarehouseRelationListResponse")
	proto.RegisterType((*RegionRelationListRespon)(nil), "dc.RegionRelationListRespon")
	proto.RegisterType((*PreposeWarehouseInfo)(nil), "dc.PreposeWarehouseInfo")
	proto.RegisterType((*Empty)(nil), "dc.Empty")
	proto.RegisterType((*PreposeCiytListResponse)(nil), "dc.PreposeCiytListResponse")
	proto.RegisterType((*BaseResponse)(nil), "dc.BaseResponse")
	proto.RegisterType((*DemolitionOrderResponse)(nil), "dc.DemolitionOrderResponse")
	proto.RegisterType((*WarehouseToGoods)(nil), "dc.WarehouseToGoods")
	proto.RegisterType((*DataRequest)(nil), "dc.DataRequest")
	proto.RegisterType((*TestData)(nil), "dc.TestData")
	proto.RegisterType((*AddWarehouseRequest)(nil), "dc.AddWarehouseRequest")
	proto.RegisterType((*WarehouseByAreaRequest)(nil), "dc.WarehouseByAreaRequest")
	proto.RegisterType((*WarehouseByAreaResponse)(nil), "dc.WarehouseByAreaResponse")
	proto.RegisterType((*EditWarehouseRequest)(nil), "dc.EditWarehouseRequest")
	proto.RegisterType((*WarehouseDelivery)(nil), "dc.WarehouseDelivery")
	proto.RegisterType((*AddWarehouseAreaRequest)(nil), "dc.AddWarehouseAreaRequest")
	proto.RegisterType((*WarehouseArea)(nil), "dc.WarehouseArea")
	proto.RegisterType((*GetWarehouseTypeResponse)(nil), "dc.GetWarehouseTypeResponse")
	proto.RegisterType((*WarehouseType)(nil), "dc.WarehouseType")
	proto.RegisterType((*GetWarehouseLevelResponse)(nil), "dc.GetWarehouseLevelResponse")
	proto.RegisterType((*WarehouseLevel)(nil), "dc.WarehouseLevel")
	proto.RegisterType((*WarehouseListRequest)(nil), "dc.WarehouseListRequest")
	proto.RegisterType((*WarehouseListResponse)(nil), "dc.WarehouseListResponse")
	proto.RegisterType((*WarehouseList)(nil), "dc.WarehouseList")
	proto.RegisterType((*BaseAreaRequest)(nil), "dc.BaseAreaRequest")
	proto.RegisterType((*BaseAreaResponse)(nil), "dc.BaseAreaResponse")
	proto.RegisterType((*RegionList)(nil), "dc.RegionList")
	proto.RegisterType((*ProvinceList)(nil), "dc.ProvinceList")
	proto.RegisterType((*CityList)(nil), "dc.CityList")
	proto.RegisterType((*CountyList)(nil), "dc.CountyList")
	proto.RegisterType((*UpdateWarehouseStatusRequest)(nil), "dc.UpdateWarehouseStatusRequest")
	proto.RegisterType((*GetWarehouseByIdRequest)(nil), "dc.GetWarehouseByIdRequest")
	proto.RegisterType((*GetWarehouseByIdResponse)(nil), "dc.GetWarehouseByIdResponse")
	proto.RegisterType((*WarehouseDeliveryInfo)(nil), "dc.WarehouseDeliveryInfo")
	proto.RegisterType((*WarehouseRelationShopList)(nil), "dc.WarehouseRelationShopList")
	proto.RegisterType((*GetAreaByWarehouseIdRequest)(nil), "dc.GetAreaByWarehouseIdRequest")
	proto.RegisterType((*GetAreaByWarehouseIdResponse)(nil), "dc.GetAreaByWarehouseIdResponse")
	proto.RegisterType((*WarehouseAreaDto)(nil), "dc.WarehouseAreaDto")
	proto.RegisterType((*DemolitionOrderRequest)(nil), "dc.DemolitionOrderRequest")
	proto.RegisterType((*OrderGoodsDetail)(nil), "dc.OrderGoodsDetail")
	proto.RegisterType((*GetWarehouseInfoByConditionRequest)(nil), "dc.GetWarehouseInfoByConditionRequest")
	proto.RegisterType((*GetWarehouseInfoByConditionResponse)(nil), "dc.GetWarehouseInfoByConditionResponse")
	proto.RegisterType((*GetWarehouseRelationResponse)(nil), "dc.GetWarehouseRelationResponse")
	proto.RegisterType((*WarehouseRelationList)(nil), "dc.WarehouseRelationList")
	proto.RegisterType((*WarehouseLogRequest)(nil), "dc.WarehouseLogRequest")
	proto.RegisterType((*WarehouseLogResponse)(nil), "dc.WarehouseLogResponse")
	proto.RegisterType((*WarehouseLog)(nil), "dc.WarehouseLog")
	proto.RegisterType((*ShopBindInfoRequest)(nil), "dc.ShopBindInfoRequest")
	proto.RegisterType((*ShopBindInfoRespond)(nil), "dc.ShopBindInfoRespond")
	proto.RegisterType((*ShopBindInfo)(nil), "dc.ShopBindInfo")
	proto.RegisterType((*ShopBindWarehouseReq)(nil), "dc.ShopBindWarehouseReq")
	proto.RegisterType((*ShopBindWarehouseRes)(nil), "dc.ShopBindWarehouseRes")
	proto.RegisterType((*ShopBindWarehouseInfo)(nil), "dc.ShopBindWarehouseInfo")
	proto.RegisterType((*WarehouseInfo)(nil), "dc.warehouse_info")
	proto.RegisterType((*ShopBindInfoByShopIdRequest)(nil), "dc.ShopBindInfoByShopIdRequest")
	proto.RegisterType((*ShopBindInfoByShopIdRespond)(nil), "dc.ShopBindInfoByShopIdRespond")
	proto.RegisterType((*BindShopsRequest)(nil), "dc.BindShopsRequest")
	proto.RegisterType((*BindShopsRespond)(nil), "dc.BindShopsRespond")
	proto.RegisterType((*BindInfo)(nil), "dc.BindInfo")
	proto.RegisterType((*StoreWarehouseRelationShopRequest)(nil), "dc.StoreWarehouseRelationShopRequest")
	proto.RegisterType((*WarehouseRelationShop)(nil), "dc.WarehouseRelationShop")
	proto.RegisterType((*StoreWarehouseRelationShopRespond)(nil), "dc.StoreWarehouseRelationShopRespond")
	proto.RegisterType((*GetA8WareHouseListRequest)(nil), "dc.GetA8WareHouseListRequest")
	proto.RegisterType((*GetA8WareHouseListResponse)(nil), "dc.GetA8WareHouseListResponse")
	proto.RegisterType((*A8WareHouse)(nil), "dc.A8WareHouse")
	proto.RegisterType((*GetWarehouseInfoByCodeRequest)(nil), "dc.GetWarehouseInfoByCodeRequest")
	proto.RegisterType((*GetWarehouseInfoByCodeResponse)(nil), "dc.GetWarehouseInfoByCodeResponse")
	proto.RegisterType((*SyncOmsWarehouseRequest)(nil), "dc.SyncOmsWarehouseRequest")
	proto.RegisterType((*ShopWarehouseListRequest)(nil), "dc.ShopWarehouseListRequest")
}

func init() { proto.RegisterFile("dc/dispatchcenter.proto", fileDescriptor_87e14ea68acb18a5) }

var fileDescriptor_87e14ea68acb18a5 = []byte{
	// 4135 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x3c, 0xcb, 0x8f, 0x1c, 0xc7,
	0x5b, 0xea, 0x9d, 0xc7, 0xce, 0x7c, 0xfb, 0x9a, 0xed, 0x7d, 0xcd, 0xce, 0xae, 0xed, 0x75, 0xdb,
	0x86, 0xf5, 0x0f, 0xc7, 0x49, 0x9c, 0xa0, 0x84, 0x04, 0x94, 0xec, 0x7a, 0x63, 0x67, 0xe5, 0x38,
	0x76, 0x66, 0x1d, 0xac, 0x44, 0x41, 0xab, 0xf6, 0x74, 0xed, 0x6c, 0xc7, 0x3d, 0xdd, 0xed, 0xee,
	0x9e, 0x75, 0x06, 0x71, 0x40, 0x5c, 0x02, 0x1c, 0x72, 0xe3, 0x82, 0x78, 0x48, 0x20, 0x04, 0x88,
	0x43, 0x8e, 0x80, 0x84, 0x84, 0x90, 0x10, 0x70, 0xe7, 0x80, 0xc4, 0x05, 0xfe, 0x08, 0x0e, 0x9c,
	0x10, 0xaa, 0x67, 0x57, 0x55, 0x57, 0xcf, 0x0c, 0x64, 0x88, 0x2c, 0x7e, 0xb7, 0xa9, 0xaf, 0x5e,
	0x5f, 0xd5, 0xf7, 0xfe, 0xea, 0xeb, 0x81, 0x2d, 0xaf, 0xf7, 0xba, 0xe7, 0xa7, 0xb1, 0x9b, 0xf5,
	0xce, 0x7b, 0x28, 0xcc, 0x50, 0x72, 0x3b, 0x4e, 0xa2, 0x2c, 0xb2, 0xe7, 0xbc, 0x5e, 0x67, 0xa7,
	0x1f, 0x45, 0xfd, 0x00, 0xbd, 0x4e, 0x20, 0xcf, 0x86, 0x67, 0xaf, 0xa3, 0x41, 0x9c, 0x8d, 0xe8,
	0x00, 0xe7, 0x01, 0xac, 0x76, 0xd1, 0x20, 0xba, 0x40, 0x27, 0xe7, 0x51, 0xdc, 0x45, 0x2f, 0x86,
	0x28, 0xcd, 0xec, 0x4b, 0x00, 0xbd, 0x73, 0x37, 0x0c, 0x51, 0x70, 0xea, 0x7b, 0x6d, 0x6b, 0xcf,
	0xda, 0xaf, 0x75, 0x9b, 0x0c, 0x72, 0xec, 0xd9, 0x5b, 0x30, 0x9f, 0x9e, 0x47, 0x31, 0xee, 0x9b,
	0xdb, 0xb3, 0xf6, 0x9b, 0xdd, 0x3a, 0x6e, 0x1e, 0x7b, 0xce, 0x53, 0x75, 0xb1, 0x34, 0x8e, 0x42,
	0xcf, 0xb6, 0xa1, 0xda, 0x8b, 0x3c, 0xc4, 0x96, 0x21, 0xbf, 0xed, 0x36, 0xcc, 0x0f, 0x50, 0x9a,
	0xba, 0x7d, 0xc4, 0x56, 0xe0, 0x4d, 0x7b, 0x1d, 0x6a, 0x28, 0x49, 0xa2, 0xa4, 0x5d, 0x21, 0x70,
	0xda, 0x70, 0x6e, 0xc2, 0xda, 0x71, 0xe8, 0x67, 0x78, 0xd9, 0x23, 0x37, 0x73, 0x39, 0x9e, 0x36,
	0x54, 0xd3, 0x0c, 0xc5, 0x7c, 0x69, 0xfc, 0xdb, 0x19, 0xe9, 0x43, 0x67, 0x86, 0x85, 0x7d, 0x19,
	0xaa, 0x7e, 0x78, 0x16, 0xb5, 0xab, 0x7b, 0x95, 0xfd, 0x85, 0x3b, 0x70, 0xdb, 0xeb, 0xdd, 0x3e,
	0x79, 0x7a, 0x1c, 0x9e, 0x45, 0x5d, 0x02, 0x77, 0xfe, 0xcc, 0x82, 0x3a, 0x05, 0xc8, 0x57, 0x64,
	0xc9, 0x57, 0x64, 0x3b, 0xb0, 0xf4, 0xd2, 0x4d, 0xd0, 0xe9, 0x79, 0x34, 0x4c, 0x11, 0xbf, 0xc1,
	0x5a, 0x77, 0x01, 0x03, 0x3f, 0xc6, 0xb0, 0x63, 0xcf, 0xde, 0x81, 0x26, 0x99, 0x1c, 0xba, 0x03,
	0xc4, 0x30, 0x68, 0x60, 0xc0, 0xa7, 0xee, 0x00, 0xd9, 0x37, 0x60, 0x19, 0x8f, 0xa5, 0xf3, 0xc9,
	0x88, 0x2a, 0x19, 0xb1, 0x24, 0xa0, 0x64, 0x98, 0x4a, 0xc2, 0x9a, 0x46, 0x42, 0x27, 0x80, 0xfd,
	0xfb, 0x88, 0x5c, 0xd2, 0x53, 0x3e, 0x0d, 0xe3, 0x7d, 0x38, 0xba, 0xe7, 0x87, 0x6e, 0xd8, 0x43,
	0x77, 0x23, 0x0f, 0x4d, 0xc9, 0x0d, 0x57, 0x61, 0xf1, 0x8c, 0x4e, 0x3a, 0x25, 0x37, 0x4c, 0xaf,
	0x72, 0xe1, 0x2c, 0x5f, 0x08, 0x5f, 0xcc, 0xcd, 0x29, 0xb6, 0xc3, 0x94, 0x4a, 0xd1, 0x4c, 0x48,
	0xf5, 0x0e, 0x2c, 0x89, 0x9d, 0x18, 0xcd, 0xac, 0xfd, 0x85, 0x3b, 0xab, 0x98, 0x66, 0xa2, 0xe3,
	0x13, 0x3f, 0xcd, 0xba, 0xea, 0x38, 0xe7, 0x4b, 0xb8, 0x84, 0x31, 0xcd, 0xa2, 0x84, 0x74, 0x1f,
	0x8e, 0xee, 0xba, 0x19, 0xea, 0x47, 0xc9, 0x88, 0xdf, 0x46, 0x07, 0x1a, 0x3d, 0x06, 0x62, 0x18,
	0x8a, 0xb6, 0x76, 0x53, 0x73, 0xfa, 0xa5, 0x7f, 0x6b, 0xc1, 0xe5, 0xb2, 0xc5, 0x67, 0x78, 0x76,
	0x9d, 0x20, 0x98, 0x5d, 0x35, 0x82, 0xf4, 0xe1, 0xda, 0x7d, 0x94, 0xe9, 0xb4, 0xe0, 0xdd, 0x29,
	0x3f, 0xab, 0xbe, 0x92, 0x55, 0x58, 0x49, 0x3b, 0x72, 0x45, 0x3f, 0xf2, 0x43, 0xb8, 0x3e, 0x7e,
	0x23, 0x76, 0xee, 0x1b, 0x50, 0xf5, 0xdc, 0xcc, 0x25, 0x3b, 0x18, 0xc9, 0x44, 0xba, 0x9d, 0xdf,
	0xb2, 0xc0, 0x19, 0xb3, 0x5e, 0x39, 0xde, 0x3a, 0x4b, 0xe2, 0x21, 0xb9, 0x18, 0xa9, 0x62, 0x78,
	0xce, 0xc4, 0x70, 0xc2, 0xd1, 0xfe, 0xc8, 0x1a, 0x7b, 0x89, 0xaf, 0x06, 0x3b, 0x47, 0xd0, 0x16,
	0x80, 0x2e, 0x0a, 0xdc, 0xcc, 0x8f, 0x42, 0xe9, 0x96, 0x94, 0x2b, 0xb0, 0x8a, 0x57, 0xf0, 0x06,
	0x2c, 0x62, 0xa1, 0xc5, 0x47, 0xc3, 0xab, 0xb7, 0xe7, 0x08, 0x79, 0x16, 0x89, 0xe6, 0x63, 0xf0,
	0xae, 0x32, 0xc2, 0xf9, 0x10, 0x1a, 0xbc, 0x5d, 0xae, 0x04, 0x15, 0x05, 0x37, 0xa7, 0x2a, 0x38,
	0xa7, 0x07, 0x57, 0x1e, 0x27, 0x28, 0x8e, 0x52, 0x54, 0x8a, 0x79, 0x07, 0x1a, 0x5d, 0xd4, 0xf7,
	0xa3, 0xf0, 0x98, 0x63, 0x2d, 0xda, 0xf6, 0x75, 0x58, 0x62, 0xd3, 0x8f, 0x3d, 0x81, 0x73, 0xad,
	0xab, 0x02, 0x9d, 0x03, 0xd8, 0x2d, 0xac, 0x4e, 0xee, 0x6f, 0xea, 0xbb, 0x71, 0x7e, 0xc7, 0x82,
	0x4b, 0x25, 0x6b, 0xcc, 0x90, 0xf2, 0x3a, 0x05, 0xaa, 0x13, 0x29, 0x10, 0x42, 0x9b, 0x5e, 0x46,
	0x11, 0x27, 0x23, 0x46, 0x2d, 0xa8, 0x0c, 0xd2, 0x3e, 0xc3, 0x06, 0xff, 0xb4, 0x6f, 0x31, 0x61,
	0xac, 0x90, 0xbd, 0xda, 0x78, 0x2f, 0x9d, 0x22, 0xd4, 0xea, 0x11, 0x99, 0xfc, 0xd6, 0x82, 0x75,
	0x53, 0xf7, 0x34, 0xfc, 0xa5, 0x18, 0x33, 0xc9, 0x7a, 0xe4, 0xc6, 0x8c, 0x08, 0x6b, 0xd1, 0xe6,
	0x55, 0x0c, 0x36, 0xcf, 0x99, 0x87, 0xda, 0x47, 0xd8, 0xb5, 0x71, 0x4e, 0x60, 0x8b, 0x61, 0x74,
	0xd7, 0x1f, 0x65, 0x13, 0x69, 0x52, 0xbc, 0x01, 0x5b, 0xba, 0x81, 0x26, 0x3b, 0x67, 0x17, 0x16,
	0x0f, 0xdd, 0x74, 0xa6, 0x72, 0xed, 0xfc, 0xa9, 0x05, 0x5b, 0x47, 0x68, 0x10, 0x05, 0x3e, 0x26,
	0xd4, 0xa3, 0xc4, 0x43, 0xc9, 0x4c, 0xb9, 0xe7, 0x63, 0x58, 0x17, 0x34, 0x79, 0x12, 0xdd, 0x8f,
	0x22, 0x2f, 0x95, 0xb8, 0x68, 0x5d, 0x51, 0x1f, 0xac, 0xbf, 0x6b, 0x9c, 0xe1, 0xfc, 0xbd, 0x05,
	0x2d, 0xbd, 0xc3, 0xde, 0x83, 0x9c, 0x9a, 0x06, 0x02, 0xfb, 0x1e, 0x46, 0x38, 0xc2, 0xa7, 0x12,
	0xae, 0x22, 0x6f, 0x62, 0x19, 0xfe, 0x6c, 0xe8, 0x86, 0x99, 0x9f, 0x8d, 0x98, 0x6e, 0x15, 0x6d,
	0x3c, 0xab, 0x8f, 0x37, 0xf0, 0x3d, 0xe6, 0xdc, 0xf0, 0x26, 0xee, 0xc9, 0xce, 0xfd, 0xc4, 0x63,
	0x3e, 0x4d, 0xb3, 0xcb, 0x9b, 0x58, 0xee, 0xc5, 0xc6, 0xe4, 0xde, 0xea, 0x1a, 0x8b, 0x60, 0xa0,
	0xf3, 0x0e, 0x2c, 0xc8, 0x0e, 0xe4, 0x3e, 0x34, 0x70, 0x93, 0xdc, 0x89, 0x95, 0x4b, 0xd6, 0x13,
	0x94, 0x66, 0x64, 0x98, 0xe8, 0x75, 0xbe, 0x82, 0x06, 0x87, 0x4e, 0x71, 0xec, 0x75, 0xa8, 0xa5,
	0xd8, 0xca, 0x33, 0xb3, 0x42, 0x1b, 0xf2, 0xb1, 0x2a, 0xca, 0xb1, 0x9c, 0xbf, 0xac, 0xc0, 0xda,
	0x81, 0xe7, 0x49, 0xea, 0x44, 0x38, 0xb8, 0x92, 0x01, 0x13, 0x3c, 0xc0, 0xaf, 0x60, 0x4e, 0xbd,
	0x02, 0x1b, 0xaa, 0x92, 0x70, 0x90, 0xdf, 0xc4, 0x5d, 0x89, 0x06, 0xe8, 0x2c, 0x89, 0x06, 0xe4,
	0x2e, 0xb1, 0xbb, 0xc2, 0xda, 0x18, 0xcb, 0x00, 0x5d, 0xa0, 0x80, 0xb9, 0x87, 0xb4, 0xa1, 0x38,
	0x38, 0x75, 0xcd, 0xc1, 0x69, 0xc3, 0xbc, 0xeb, 0x79, 0x09, 0x4a, 0xd3, 0xf6, 0x3c, 0xdd, 0x9b,
	0x35, 0xe9, 0x3e, 0x61, 0xe6, 0xf6, 0xb2, 0xb4, 0xdd, 0xa0, 0x1a, 0x9d, 0xb7, 0xb1, 0x7c, 0x65,
	0x28, 0x68, 0x37, 0xa9, 0x7c, 0x65, 0x28, 0xb0, 0x77, 0xa1, 0x99, 0x0e, 0x9f, 0xa5, 0xa3, 0x34,
	0x43, 0x83, 0x36, 0x50, 0xcb, 0x2a, 0x00, 0x18, 0xaf, 0x04, 0x6b, 0xae, 0xf6, 0x02, 0xc5, 0x8b,
	0x34, 0xf0, 0x2a, 0x41, 0xd8, 0x6f, 0x2f, 0xee, 0x59, 0xfb, 0x95, 0x2e, 0xfe, 0x49, 0x20, 0x6e,
	0xd6, 0x5e, 0x62, 0x10, 0x37, 0xb3, 0x37, 0xa1, 0x9e, 0x10, 0xdd, 0xd7, 0x5e, 0xa6, 0x06, 0x87,
	0xb6, 0xc8, 0x3d, 0x62, 0x46, 0x5b, 0x61, 0xf7, 0x88, 0x99, 0xec, 0x08, 0xec, 0x5c, 0xa9, 0x78,
	0x28, 0xf0, 0x2f, 0x50, 0x32, 0x6a, 0xaf, 0x11, 0x2e, 0xd8, 0x50, 0x24, 0xe3, 0x88, 0x75, 0x76,
	0x57, 0x5f, 0xea, 0x20, 0xe7, 0x31, 0x6c, 0x8a, 0x71, 0x87, 0xa3, 0x83, 0x04, 0xb9, 0x92, 0x91,
	0x8a, 0x93, 0xe8, 0xc2, 0x0f, 0x7b, 0x5c, 0x86, 0x45, 0x5b, 0xa1, 0xca, 0x9c, 0x4a, 0x15, 0xe7,
	0x77, 0x2d, 0xd8, 0x2a, 0x2c, 0x39, 0x43, 0x9d, 0xf0, 0x3e, 0xac, 0x88, 0xe5, 0x0f, 0xdc, 0x24,
	0x71, 0x47, 0x4c, 0x1d, 0x18, 0xbc, 0x09, 0x7d, 0xa4, 0xf3, 0xef, 0x15, 0x58, 0xff, 0xc8, 0xf3,
	0xb3, 0x02, 0xa7, 0x2e, 0xc3, 0x9c, 0x10, 0x85, 0x39, 0x3f, 0x8f, 0xb7, 0xe6, 0x24, 0xce, 0xfd,
	0xff, 0xcd, 0x9f, 0x9b, 0x50, 0x4f, 0x33, 0x37, 0x1b, 0xa6, 0x84, 0x45, 0x6b, 0x5d, 0xd6, 0x92,
	0xe5, 0x75, 0x49, 0x95, 0x57, 0xc6, 0xd1, 0xcb, 0x05, 0x8e, 0x5e, 0x31, 0x71, 0x74, 0xcb, 0xc8,
	0xd1, 0xab, 0x33, 0xe7, 0xe8, 0x87, 0xb0, 0x5a, 0x18, 0x67, 0x5f, 0x81, 0x05, 0xbe, 0x60, 0x6e,
	0xca, 0x81, 0x83, 0xa4, 0x9c, 0x40, 0x18, 0xc9, 0x39, 0x81, 0x4f, 0x23, 0xe7, 0xd7, 0x60, 0x4b,
	0xd6, 0x6c, 0xb2, 0x84, 0x7c, 0x20, 0xed, 0x84, 0xe1, 0x92, 0x1a, 0x56, 0x79, 0x91, 0x4c, 0x2a,
	0x8e, 0xd5, 0x15, 0xf1, 0x5c, 0x41, 0x11, 0x3b, 0x91, 0xe4, 0x38, 0xe3, 0x69, 0x05, 0x3e, 0xdd,
	0x84, 0xba, 0x9b, 0x20, 0x57, 0xcc, 0x66, 0x2d, 0x7d, 0xe9, 0x8a, 0x51, 0xc7, 0x53, 0xee, 0xac,
	0x4a, 0xdc, 0xe9, 0xf8, 0xd0, 0x96, 0x83, 0x82, 0x27, 0xa3, 0x38, 0xf7, 0x18, 0x7e, 0x41, 0x72,
	0x63, 0x70, 0x47, 0x6a, 0x3c, 0x2c, 0x99, 0xa2, 0x0d, 0x54, 0xc4, 0x89, 0x09, 0xbe, 0xf3, 0x96,
	0x74, 0x36, 0x3c, 0xca, 0x24, 0x83, 0x92, 0x87, 0x4d, 0x7e, 0x3b, 0x03, 0xd8, 0x96, 0xf1, 0xfb,
	0x04, 0x23, 0x2d, 0x10, 0xfc, 0x45, 0x58, 0x79, 0xa9, 0xf4, 0x70, 0x0c, 0x6d, 0x55, 0x35, 0x90,
	0x49, 0xfa, 0x50, 0x23, 0x8e, 0x6f, 0xc3, 0xb2, 0x3a, 0x6d, 0x2a, 0x24, 0xff, 0xc3, 0x92, 0xfc,
	0x16, 0xd9, 0x2d, 0xd7, 0x83, 0xef, 0xa6, 0x24, 0xfb, 0xbb, 0xd0, 0x8c, 0xdd, 0x3e, 0xf2, 0x43,
	0x0f, 0x7d, 0xc3, 0x63, 0x6f, 0x01, 0x20, 0xda, 0xd8, 0xed, 0xa3, 0xd4, 0xff, 0x55, 0xc4, 0xdd,
	0x0d, 0xde, 0xc6, 0x12, 0xfa, 0x1c, 0x8d, 0x9e, 0x46, 0x89, 0x70, 0x37, 0x58, 0x53, 0x48, 0x59,
	0x4d, 0x92, 0xb2, 0x9b, 0xd0, 0x8a, 0xa9, 0x73, 0x79, 0xaa, 0xe9, 0xa1, 0x15, 0x06, 0xe7, 0x51,
	0xbd, 0xa4, 0x12, 0xe6, 0x15, 0x95, 0xb0, 0x01, 0xf5, 0x28, 0xe9, 0x63, 0x41, 0x6a, 0x50, 0xde,
	0x89, 0x92, 0xfe, 0xb1, 0xe7, 0xfc, 0x95, 0x05, 0x1b, 0xda, 0xb1, 0x67, 0xa8, 0xf7, 0x2f, 0x03,
	0x64, 0x51, 0xe6, 0x06, 0x77, 0xa3, 0x61, 0x98, 0x31, 0xa6, 0x95, 0x20, 0x26, 0xbb, 0x50, 0x9b,
	0xda, 0x2e, 0x7c, 0x57, 0x95, 0x98, 0x91, 0xc8, 0xa6, 0x4e, 0xe7, 0xb1, 0x6e, 0x0b, 0x39, 0x5c,
	0xc5, 0x60, 0x2a, 0xaa, 0x25, 0xa6, 0xa2, 0x56, 0x66, 0x2a, 0xea, 0x65, 0xa6, 0x62, 0xbe, 0xdc,
	0x54, 0x34, 0xca, 0x4d, 0x45, 0xd3, 0x6c, 0x2a, 0x20, 0x37, 0x15, 0x39, 0x8d, 0x17, 0x14, 0x1a,
	0x5f, 0x06, 0xe8, 0x25, 0xc8, 0xcd, 0x90, 0xe7, 0x66, 0x88, 0x98, 0x84, 0x66, 0x57, 0x82, 0xe0,
	0x5d, 0x02, 0x37, 0xcd, 0x48, 0x2f, 0xb5, 0x0b, 0xa2, 0xad, 0x9a, 0x9f, 0xe5, 0x52, 0xf3, 0xb3,
	0x22, 0x9b, 0x1f, 0x25, 0x46, 0x22, 0x39, 0x82, 0x16, 0x09, 0x5e, 0x72, 0x07, 0x98, 0x04, 0x65,
	0xcc, 0xe6, 0xac, 0x16, 0x6c, 0x8e, 0x6d, 0xb2, 0x39, 0x6b, 0x46, 0x9b, 0xb3, 0x2e, 0x49, 0xc3,
	0x26, 0xd4, 0xc3, 0xe1, 0xe0, 0x19, 0x4a, 0xda, 0x1b, 0xf4, 0xf8, 0xb4, 0xe5, 0xbc, 0x06, 0x2b,
	0x38, 0x5a, 0xd2, 0x1c, 0x22, 0xac, 0x5c, 0x3d, 0x24, 0x32, 0xb6, 0xa2, 0xed, 0xfc, 0xa6, 0x05,
	0xad, 0x7c, 0xfc, 0x4c, 0xe3, 0xe7, 0x05, 0x8a, 0xbd, 0xec, 0xe9, 0x2c, 0x63, 0x8e, 0xa6, 0x41,
	0x32, 0x61, 0x67, 0x79, 0x88, 0xf3, 0x25, 0x40, 0xde, 0x85, 0xc9, 0x88, 0x91, 0x64, 0xf7, 0x41,
	0x75, 0x8e, 0x04, 0xb1, 0x6f, 0x41, 0xa3, 0x77, 0xee, 0x07, 0x5e, 0x82, 0x42, 0x96, 0x1d, 0x69,
	0xd1, 0x78, 0x99, 0x7a, 0x7a, 0x64, 0x79, 0x31, 0xc2, 0xf9, 0x27, 0x0b, 0x16, 0xe5, 0x2e, 0xc9,
	0xfc, 0x58, 0x8a, 0xf9, 0x61, 0x77, 0x25, 0x27, 0x48, 0x78, 0xdb, 0x76, 0x60, 0x11, 0xff, 0x8e,
	0xdd, 0x04, 0x85, 0x99, 0xb0, 0x4d, 0x0a, 0x8c, 0xcf, 0x4f, 0xa3, 0x84, 0x8b, 0xba, 0x68, 0x2b,
	0x74, 0xa8, 0xa9, 0x74, 0xc0, 0x01, 0x91, 0x38, 0x4e, 0x3d, 0x0f, 0x88, 0xee, 0xfa, 0xd9, 0x48,
	0x3b, 0xca, 0x3f, 0x58, 0xd0, 0xe0, 0xe0, 0x57, 0xee, 0x18, 0x3f, 0x29, 0x1c, 0x83, 0x90, 0x9c,
	0x28, 0x3a, 0xfd, 0x20, 0xbf, 0x67, 0x01, 0xe4, 0x1d, 0xaf, 0xda, 0x51, 0x9c, 0x7b, 0xb0, 0xfb,
	0x79, 0x8c, 0xb5, 0x82, 0x50, 0xaf, 0x27, 0x44, 0xc1, 0x94, 0x39, 0xde, 0xb9, 0x3e, 0xe2, 0x7e,
	0x18, 0x69, 0x39, 0x37, 0x61, 0x4b, 0x36, 0xfc, 0x87, 0xa3, 0x63, 0xaf, 0x64, 0x09, 0xe7, 0x9f,
	0x6b, 0xaa, 0x13, 0x43, 0xc7, 0xce, 0x50, 0x28, 0xe9, 0x86, 0x55, 0x81, 0x73, 0x21, 0x76, 0xaf,
	0x19, 0x62, 0x77, 0x61, 0x13, 0xea, 0x25, 0x36, 0x61, 0xbe, 0xcc, 0x26, 0x34, 0xca, 0x6c, 0x42,
	0xb3, 0xdc, 0x26, 0x40, 0xb9, 0x4d, 0x58, 0x30, 0xdb, 0x84, 0x45, 0x93, 0x4d, 0x58, 0x1a, 0x63,
	0x13, 0x96, 0x0b, 0x36, 0x41, 0xb2, 0x91, 0x2b, 0xaa, 0x8d, 0x54, 0x2c, 0x42, 0xab, 0xd4, 0x22,
	0xac, 0x1a, 0x02, 0x66, 0xbb, 0xa0, 0xea, 0xd7, 0x4c, 0xaa, 0x7e, 0xdd, 0xa8, 0xea, 0x37, 0x24,
	0x55, 0x7f, 0x09, 0x20, 0x45, 0x41, 0x70, 0xea, 0x25, 0xc3, 0x7e, 0xda, 0xde, 0x64, 0x48, 0xa0,
	0x20, 0x38, 0xc2, 0x00, 0xfb, 0x33, 0xd8, 0x2a, 0x46, 0x1f, 0xa7, 0x01, 0xf6, 0xe9, 0xb7, 0x88,
	0x08, 0x6e, 0x1b, 0x43, 0x10, 0x92, 0x49, 0xdc, 0x28, 0x84, 0x21, 0x44, 0x16, 0x1f, 0x80, 0x9d,
	0xb0, 0x24, 0xe6, 0x29, 0x89, 0x2e, 0xc8, 0x6a, 0x6d, 0xb2, 0xda, 0x25, 0x65, 0x35, 0x9e, 0xeb,
	0x3c, 0x39, 0x8f, 0x62, 0x22, 0xdf, 0xad, 0x44, 0x83, 0x38, 0xff, 0x26, 0x7b, 0x57, 0xf2, 0xee,
	0x05, 0x11, 0xba, 0x06, 0x4b, 0x02, 0x7f, 0x49, 0xde, 0x17, 0x39, 0x90, 0x3c, 0xb0, 0x6d, 0xc1,
	0xbc, 0x1b, 0xc7, 0xa7, 0xcf, 0xd1, 0x88, 0xf1, 0x76, 0xdd, 0x8d, 0xe3, 0x07, 0x88, 0x5c, 0x13,
	0xee, 0x48, 0x51, 0x2f, 0x41, 0x19, 0x73, 0x60, 0x9a, 0x6e, 0x1c, 0x9f, 0x10, 0x00, 0xc9, 0x7d,
	0xf7, 0xa2, 0x61, 0x82, 0x4e, 0x45, 0x0e, 0xab, 0x41, 0x01, 0xc7, 0xea, 0xce, 0xd9, 0x28, 0x46,
	0xcc, 0x9d, 0x11, 0x3b, 0x13, 0x37, 0x5f, 0x0a, 0xb5, 0xe6, 0x95, 0x50, 0xeb, 0x57, 0x60, 0xbb,
	0xf4, 0x42, 0xd4, 0x9c, 0xbb, 0xa5, 0x3d, 0x2a, 0x5e, 0x83, 0x25, 0xfe, 0xd4, 0x81, 0xfb, 0xb9,
	0xee, 0x58, 0x64, 0x40, 0x3c, 0x26, 0x75, 0x3e, 0x80, 0x9d, 0xfb, 0x28, 0xc3, 0x16, 0xfa, 0x70,
	0x94, 0x67, 0x7a, 0x85, 0x16, 0x99, 0x98, 0x15, 0x73, 0xfe, 0xc0, 0x82, 0x5d, 0xf3, 0x0a, 0x33,
	0xd4, 0x2d, 0xef, 0x69, 0x11, 0x9f, 0x31, 0xd7, 0x89, 0x3b, 0x8e, 0xb2, 0xa8, 0xab, 0x0e, 0x75,
	0xbe, 0x92, 0x72, 0x9c, 0x6c, 0x88, 0xa2, 0xf7, 0x2d, 0x4d, 0xef, 0x0b, 0x0d, 0x33, 0x27, 0x6b,
	0x98, 0xdc, 0x82, 0x54, 0x64, 0x0b, 0xe2, 0xfc, 0x57, 0x0d, 0x36, 0x0b, 0xc9, 0x5e, 0x5d, 0x03,
	0x57, 0xb8, 0xb3, 0x5c, 0x9e, 0x36, 0x1d, 0x20, 0xec, 0x61, 0x89, 0x24, 0xa2, 0x68, 0x63, 0xf5,
	0x42, 0x86, 0x0d, 0xa2, 0x10, 0x8d, 0xb8, 0x87, 0x9f, 0x43, 0xb0, 0x12, 0x21, 0x2d, 0xc2, 0x59,
	0xec, 0x49, 0x58, 0x00, 0xec, 0x7d, 0x58, 0x11, 0x0d, 0x0f, 0x65, 0xae, 0xcf, 0x9d, 0x69, 0x1d,
	0x2c, 0xf6, 0xc1, 0x5a, 0x0d, 0x31, 0xb5, 0x2b, 0x41, 0xf0, 0x3e, 0xc3, 0x14, 0x25, 0x6e, 0x1f,
	0x85, 0x19, 0x53, 0xbe, 0x39, 0x00, 0xcf, 0x8e, 0x03, 0x37, 0x3b, 0x8b, 0x92, 0x81, 0xef, 0x31,
	0x15, 0x2c, 0x41, 0xec, 0x9f, 0x40, 0xeb, 0x19, 0x0a, 0xa2, 0xb0, 0x7f, 0x1e, 0xa5, 0xb1, 0x9f,
	0xb9, 0x81, 0xef, 0x31, 0x6d, 0x5c, 0x80, 0xe7, 0x0a, 0x35, 0xf3, 0x07, 0x88, 0x29, 0x66, 0x09,
	0xc2, 0x9d, 0x6c, 0xd2, 0xbb, 0x98, 0x3b, 0xd9, 0xa4, 0xef, 0x16, 0xac, 0x12, 0x9c, 0x89, 0x23,
	0x80, 0x42, 0x7a, 0x18, 0xaa, 0xaf, 0x8b, 0x1d, 0x78, 0x27, 0x3f, 0x45, 0x17, 0x6e, 0x30, 0xe4,
	0xaa, 0xbb, 0xd6, 0x95, 0x20, 0xb4, 0x3f, 0x8e, 0xd2, 0x6c, 0x18, 0xa3, 0x8c, 0x79, 0xe6, 0x12,
	0x04, 0x63, 0xe2, 0xa7, 0x61, 0x94, 0xf9, 0x67, 0x23, 0xa6, 0xbf, 0x45, 0x1b, 0x4b, 0x0e, 0xbd,
	0xbd, 0x68, 0x98, 0xf4, 0x10, 0x53, 0xe2, 0x32, 0x48, 0x32, 0x28, 0xb6, 0x62, 0x50, 0x0e, 0x61,
	0x95, 0xf0, 0x11, 0x49, 0xc7, 0x1f, 0x11, 0xea, 0xa4, 0x2c, 0xe1, 0x43, 0x18, 0x5e, 0xef, 0xec,
	0x16, 0x87, 0x8b, 0xdd, 0x19, 0xcd, 0xa9, 0x1d, 0x90, 0x41, 0x4a, 0x26, 0x93, 0x1a, 0x84, 0x3c,
	0x93, 0xc9, 0x0d, 0xc5, 0xa6, 0x1a, 0x13, 0x30, 0xa3, 0xb2, 0x25, 0x1b, 0x15, 0xe7, 0x3f, 0xab,
	0xd0, 0xd2, 0xf7, 0x97, 0x58, 0xbf, 0x39, 0x81, 0xf5, 0x4b, 0xd3, 0xe7, 0xb8, 0xe7, 0x99, 0x9b,
	0xb0, 0xc7, 0x6e, 0xd2, 0xc3, 0x9a, 0xc2, 0x67, 0xa8, 0x49, 0x3e, 0xc3, 0x65, 0x80, 0x61, 0xe8,
	0x5f, 0xb8, 0x01, 0xc2, 0x07, 0xa2, 0x3c, 0x2e, 0x41, 0x88, 0xad, 0x45, 0x41, 0x10, 0x27, 0x7e,
	0x8f, 0x73, 0x77, 0x0e, 0xc0, 0x97, 0xf1, 0x82, 0xbf, 0x5b, 0x50, 0xde, 0x16, 0x6d, 0xbc, 0xdb,
	0x30, 0xf4, 0x33, 0x16, 0x35, 0x92, 0xdf, 0x58, 0xac, 0xdc, 0x38, 0x0e, 0x46, 0x05, 0x6e, 0xd6,
	0xc1, 0x78, 0xdf, 0xde, 0xb9, 0x9b, 0xf4, 0x51, 0x74, 0x76, 0xc6, 0x82, 0xc9, 0x1c, 0x80, 0x7d,
	0x24, 0xd1, 0x20, 0x27, 0xa5, 0xfc, 0xac, 0x02, 0xed, 0x37, 0x60, 0x4d, 0x00, 0xf8, 0xd2, 0xc7,
	0x3c, 0xf1, 0x68, 0xea, 0x52, 0xd6, 0x25, 0x72, 0xb2, 0xac, 0xad, 0x4b, 0x84, 0x45, 0x15, 0xb4,
	0x95, 0xb1, 0x82, 0xd6, 0xd2, 0x04, 0x0d, 0x33, 0xaf, 0xcc, 0xd9, 0xac, 0xa5, 0xaa, 0x09, 0x5b,
	0x57, 0x13, 0xb7, 0x60, 0x55, 0xa0, 0x20, 0x34, 0x1e, 0x8d, 0x47, 0x8b, 0x1d, 0x18, 0x3f, 0xca,
	0x0c, 0x03, 0x6c, 0x28, 0x28, 0x0f, 0x4b, 0x10, 0xa7, 0x67, 0xaa, 0x1b, 0xb8, 0x1b, 0x85, 0x9e,
	0xaf, 0xbd, 0x88, 0x4f, 0xaa, 0x1b, 0x90, 0xdd, 0xc7, 0x39, 0xd5, 0x7d, 0x2c, 0xa9, 0x08, 0x90,
	0x76, 0xf9, 0xbf, 0xad, 0x08, 0xa8, 0x4c, 0x55, 0x11, 0xf0, 0x2f, 0xd4, 0x08, 0x1b, 0xde, 0xd6,
	0x7f, 0xc4, 0x5c, 0xd3, 0x89, 0xf4, 0xc4, 0xc1, 0x11, 0x50, 0x72, 0x4e, 0xdb, 0x46, 0xef, 0x8e,
	0x9c, 0xa7, 0x6c, 0xa6, 0xf3, 0x44, 0xf2, 0xef, 0xe4, 0x19, 0xd3, 0xa4, 0x1c, 0xe5, 0x5a, 0x85,
	0x8a, 0x52, 0xd3, 0xf6, 0xad, 0x05, 0x6b, 0xf9, 0x85, 0x46, 0x7d, 0xce, 0x2b, 0x58, 0x4b, 0x64,
	0x6e, 0x42, 0x59, 0x9e, 0x32, 0x4a, 0x0e, 0xc0, 0x17, 0x86, 0x42, 0x8f, 0xf4, 0xb1, 0x0b, 0x63,
	0x4d, 0x35, 0x4d, 0x59, 0x19, 0x97, 0xa6, 0xac, 0xaa, 0x69, 0x4a, 0xe7, 0x6f, 0x94, 0xac, 0x28,
	0xc6, 0xe4, 0x47, 0xa4, 0xd8, 0x87, 0x60, 0xcb, 0x7b, 0x2b, 0xc4, 0x6a, 0xa9, 0x4c, 0x17, 0xf5,
	0xbb, 0x86, 0xb1, 0x4e, 0x00, 0x8b, 0x32, 0xd4, 0x94, 0x20, 0xc4, 0x21, 0x15, 0x56, 0x06, 0x0c,
	0x63, 0xd6, 0x34, 0xbe, 0x1b, 0xa9, 0xa1, 0x54, 0x55, 0x0f, 0xa5, 0x9c, 0x3f, 0xb1, 0x60, 0x0d,
	0xfb, 0xbe, 0x87, 0x7e, 0xe8, 0x91, 0x10, 0x63, 0xba, 0x62, 0xb6, 0x1d, 0x68, 0x3e, 0xf3, 0x43,
	0x8f, 0x3a, 0xdf, 0x4c, 0xbe, 0x31, 0x80, 0x38, 0xde, 0x58, 0x91, 0x21, 0x37, 0xe9, 0x9d, 0x0b,
	0x16, 0x21, 0x2d, 0xbc, 0x26, 0x26, 0xd2, 0x29, 0xa5, 0x69, 0x35, 0xa7, 0xe9, 0x31, 0xa1, 0xe9,
	0x0e, 0xa5, 0xf8, 0x29, 0x21, 0x6a, 0x2d, 0x27, 0xea, 0x09, 0x26, 0xea, 0x1f, 0x16, 0xf0, 0x9c,
	0x65, 0xbd, 0xe2, 0x78, 0x9a, 0x5e, 0x67, 0xf5, 0x8c, 0x12, 0x15, 0x15, 0x54, 0x68, 0x55, 0xe3,
	0x77, 0x73, 0xb4, 0x04, 0x85, 0x83, 0x0b, 0x84, 0x1b, 0x5f, 0xf5, 0x56, 0x2a, 0x59, 0x6a, 0x44,
	0x52, 0xd5, 0x22, 0x12, 0xbd, 0x78, 0xa4, 0x36, 0xa1, 0x78, 0x44, 0xca, 0x0c, 0x68, 0x95, 0x90,
	0xfc, 0x26, 0xe7, 0xa5, 0xf4, 0xb2, 0xac, 0xc5, 0x1b, 0x5a, 0x12, 0xe0, 0x0a, 0x2c, 0xf4, 0x86,
	0x69, 0x16, 0x0d, 0xa8, 0x0d, 0x68, 0x32, 0xd6, 0x22, 0x20, 0x52, 0x3c, 0xf7, 0xaf, 0x16, 0xac,
	0xf3, 0x0b, 0x91, 0xdf, 0x41, 0x7f, 0x10, 0x6f, 0x5d, 0x81, 0x05, 0x72, 0x19, 0x0a, 0x83, 0x01,
	0x06, 0x9d, 0x50, 0x26, 0xbb, 0x09, 0xad, 0xfc, 0xb4, 0x6c, 0x14, 0xbd, 0xb4, 0xfc, 0x21, 0xe6,
	0xc4, 0xc4, 0x8f, 0xb5, 0xb1, 0xfc, 0x58, 0xd7, 0xf8, 0xf1, 0xcf, 0xcd, 0x87, 0x4b, 0x7f, 0x14,
	0x86, 0x7c, 0x4d, 0x61, 0xc8, 0x6d, 0x99, 0x21, 0xb5, 0xca, 0x23, 0xc2, 0x99, 0x7f, 0x51, 0x85,
	0x0d, 0x63, 0xff, 0x4f, 0x3b, 0x8b, 0xda, 0xef, 0x42, 0xdb, 0x7d, 0x89, 0xc2, 0x3c, 0x0d, 0xc3,
	0xf6, 0x4b, 0x52, 0xc4, 0xbc, 0xcf, 0x4d, 0xdc, 0xcf, 0xb3, 0x20, 0x4f, 0x45, 0xaf, 0xfd, 0x36,
	0x90, 0x9e, 0xd3, 0xd8, 0xef, 0x3d, 0x1f, 0xc6, 0xf2, 0x3c, 0x1a, 0x5d, 0xad, 0xe3, 0xde, 0xc7,
	0xa4, 0x53, 0x9a, 0x75, 0x0d, 0x96, 0x06, 0x99, 0x3c, 0x98, 0x3a, 0xa7, 0x8b, 0x83, 0x4c, 0x1a,
	0x74, 0x03, 0x96, 0x51, 0x80, 0xe4, 0x51, 0xd4, 0x2d, 0x5d, 0x42, 0x01, 0x52, 0xd7, 0xfa, 0xda,
	0x93, 0x47, 0x51, 0x87, 0x74, 0xf1, 0x6b, 0x4f, 0x1a, 0xf4, 0x26, 0xac, 0x73, 0x1f, 0x36, 0x95,
	0xc7, 0x52, 0xcf, 0x74, 0x4d, 0xf4, 0xe5, 0x53, 0x9c, 0xef, 0x2d, 0xfd, 0x85, 0x64, 0x92, 0xc0,
	0x9a, 0xfc, 0x04, 0xd3, 0x63, 0x95, 0x4c, 0xaa, 0xaa, 0x46, 0x2a, 0x89, 0xb5, 0x6a, 0x0a, 0x6b,
	0xe9, 0xdc, 0x53, 0x2f, 0x56, 0x18, 0x7e, 0x0e, 0x3b, 0xb2, 0xe2, 0x3d, 0x1c, 0x91, 0x3a, 0x3f,
	0x91, 0x70, 0x51, 0xca, 0x2b, 0x2b, 0xd2, 0xd2, 0x93, 0xcb, 0x90, 0x4b, 0xd6, 0x9d, 0x9d, 0xe9,
	0xb9, 0xae, 0x94, 0xca, 0x97, 0x99, 0x96, 0x37, 0xa1, 0x85, 0x21, 0xb8, 0x27, 0x9d, 0xce, 0x40,
	0x3b, 0xdf, 0x28, 0x53, 0x66, 0x87, 0xf0, 0x9e, 0x82, 0x30, 0x79, 0xf4, 0xd0, 0x90, 0xfd, 0xde,
	0x82, 0x86, 0xb0, 0x81, 0xd8, 0x59, 0xa1, 0x38, 0xb1, 0x5d, 0x79, 0xd3, 0xde, 0x86, 0x06, 0x55,
	0x19, 0x43, 0x5e, 0xda, 0x43, 0xa8, 0xf4, 0xe9, 0x70, 0x60, 0x5f, 0x87, 0x65, 0xde, 0x75, 0x7a,
	0x96, 0x44, 0x61, 0xc6, 0x1f, 0x0a, 0xd8, 0x80, 0x7b, 0x18, 0x66, 0xef, 0x43, 0x4b, 0x8c, 0xba,
	0xf0, 0x93, 0x6c, 0xe8, 0xf2, 0x12, 0x83, 0x65, 0x36, 0xee, 0x97, 0x29, 0xd4, 0xbe, 0xcc, 0x6c,
	0x06, 0x1e, 0xe9, 0xa5, 0x5c, 0xd1, 0xb3, 0x41, 0x47, 0xa9, 0x13, 0xc1, 0x55, 0x52, 0x6b, 0x6e,
	0x4c, 0x0a, 0x4e, 0xe9, 0x10, 0xfd, 0x1c, 0x54, 0x5e, 0x26, 0x29, 0x7b, 0xda, 0xda, 0x2e, 0xcd,
	0xb9, 0x76, 0xf1, 0x28, 0xe7, 0x1f, 0x2d, 0x83, 0x0b, 0x8e, 0xbb, 0xff, 0x77, 0xa5, 0xc0, 0x05,
	0x19, 0xa9, 0x4c, 0xa3, 0x61, 0x8d, 0x9f, 0x43, 0xc8, 0x22, 0x5a, 0xd3, 0x44, 0x94, 0xb3, 0x4f,
	0x3d, 0x17, 0x69, 0xe7, 0x37, 0xac, 0xf1, 0x77, 0x37, 0x3b, 0xc6, 0xe3, 0xfc, 0xe2, 0x7b, 0x29,
	0xab, 0xd4, 0x9f, 0xa7, 0x57, 0x93, 0x3a, 0x7f, 0x67, 0x91, 0x62, 0x8d, 0x83, 0x77, 0x9f, 0xf2,
	0x8f, 0x43, 0xe4, 0x5a, 0x88, 0x5d, 0x68, 0x1e, 0xa2, 0xbe, 0x1f, 0x3e, 0x91, 0x02, 0x10, 0x01,
	0xc0, 0x68, 0x7c, 0x14, 0x7a, 0x4f, 0xa4, 0x00, 0x84, 0x35, 0xf1, 0xbc, 0xc7, 0xdc, 0x17, 0xe0,
	0x01, 0x88, 0x00, 0xe0, 0x8b, 0x7a, 0xcc, 0x7c, 0x01, 0xae, 0xcb, 0x78, 0x1b, 0xcf, 0x14, 0xc1,
	0x2c, 0xd3, 0x66, 0x39, 0x00, 0x1f, 0xef, 0x38, 0x3d, 0x08, 0xc4, 0x13, 0x3c, 0x69, 0x38, 0x2e,
	0x74, 0x4c, 0x47, 0x60, 0x91, 0x8b, 0xea, 0x10, 0x58, 0x05, 0x87, 0xe0, 0x1a, 0xab, 0xc3, 0xa5,
	0xec, 0xb7, 0x82, 0xd9, 0x4f, 0x5a, 0x8a, 0x15, 0xe6, 0xfe, 0xb5, 0x05, 0x0b, 0x12, 0xd4, 0x6e,
	0x41, 0xe5, 0xb9, 0x60, 0x65, 0xfc, 0x13, 0x3b, 0xee, 0xd8, 0xe9, 0x3a, 0x16, 0xdf, 0x2b, 0xd1,
	0x16, 0x3e, 0xec, 0x30, 0x45, 0x89, 0xa4, 0xd0, 0x45, 0xdb, 0xbe, 0x0e, 0x4b, 0x67, 0xc3, 0x20,
	0xc0, 0x2c, 0x75, 0x42, 0x3c, 0x36, 0xc6, 0x57, 0x0a, 0x50, 0x1d, 0x15, 0xf5, 0x9e, 0xf3, 0x97,
	0x2b, 0x05, 0x28, 0xbf, 0x2b, 0xd5, 0x95, 0x77, 0x25, 0xe7, 0x11, 0xf9, 0xdc, 0xa4, 0x90, 0x31,
	0xf0, 0xc6, 0x56, 0x80, 0x8e, 0xcb, 0x41, 0xfc, 0x3e, 0xfd, 0xc6, 0xc4, 0xb8, 0xe2, 0xab, 0xf0,
	0x41, 0xc2, 0xdf, 0x5a, 0xb0, 0x75, 0x32, 0x0a, 0x7b, 0x8f, 0x06, 0xe9, 0x54, 0xd5, 0xae, 0x26,
	0x1b, 0x2c, 0x9f, 0xbf, 0x52, 0xfe, 0x84, 0x57, 0x2d, 0x7f, 0xc2, 0xab, 0x99, 0x9f, 0xf0, 0xea,
	0xf9, 0x13, 0x9e, 0xf4, 0x14, 0x37, 0xaf, 0x3c, 0xc5, 0x39, 0x77, 0xa0, 0xad, 0x7c, 0xc8, 0x24,
	0xcb, 0xe4, 0x26, 0xd4, 0xa9, 0x2d, 0xe5, 0x5a, 0x8e, 0xb6, 0xee, 0xfc, 0xb1, 0x95, 0x97, 0x43,
	0x9c, 0xa0, 0xe4, 0xc2, 0xef, 0x21, 0xfb, 0xe7, 0xa1, 0xc1, 0x41, 0xf6, 0x1a, 0xb1, 0x37, 0x6a,
	0xbd, 0x44, 0x67, 0x5d, 0x05, 0x32, 0xea, 0x7d, 0x01, 0xeb, 0xa6, 0x37, 0x14, 0xfb, 0x0a, 0x1e,
	0x3d, 0xe6, 0x7d, 0xa6, 0xb3, 0x57, 0x3e, 0x80, 0x2e, 0x7d, 0xe7, 0xb7, 0x37, 0xa4, 0xf7, 0x0f,
	0x8e, 0xe6, 0x91, 0x5e, 0xd8, 0xd3, 0x2e, 0xd2, 0x98, 0xed, 0xb0, 0x6d, 0xe8, 0x61, 0x58, 0x3f,
	0x80, 0x0d, 0xe3, 0x2b, 0xb6, 0x4d, 0xb0, 0x1a, 0xf7, 0xc0, 0xdd, 0x69, 0xf1, 0x6b, 0x10, 0x8b,
	0x3d, 0x84, 0x96, 0xfe, 0x3c, 0x6d, 0xef, 0xb0, 0xd3, 0x99, 0x1e, 0xb8, 0x3b, 0xbb, 0xe6, 0x4e,
	0x51, 0x96, 0xb7, 0x28, 0x57, 0x28, 0xda, 0x5b, 0x44, 0xcd, 0x14, 0xab, 0xb1, 0x0d, 0x98, 0xbc,
	0x0f, 0x4b, 0x4a, 0x35, 0x2c, 0xbd, 0x1c, 0x53, 0x81, 0xac, 0x61, 0xf2, 0x27, 0xb0, 0x5a, 0x60,
	0x24, 0x7b, 0x97, 0xbb, 0x4a, 0xff, 0xd3, 0x1b, 0x3e, 0x80, 0x96, 0x5e, 0x67, 0x49, 0x2f, 0xa5,
	0xa4, 0xfa, 0xd2, 0x78, 0x9a, 0x96, 0x5e, 0xbb, 0x68, 0x37, 0xc9, 0x81, 0x06, 0x71, 0x36, 0x2a,
	0xde, 0xa2, 0x52, 0xdc, 0xf8, 0x4b, 0xb0, 0x5a, 0x28, 0x2c, 0x94, 0x67, 0x5f, 0xd2, 0x67, 0xab,
	0xa5, 0x87, 0x0f, 0xc1, 0x56, 0x09, 0x44, 0x0e, 0xd0, 0x51, 0xce, 0xab, 0xd4, 0x57, 0x77, 0x76,
	0x8c, 0x7d, 0x6c, 0xb9, 0xbb, 0x52, 0x69, 0x29, 0xb7, 0xdc, 0xf4, 0x6e, 0xcb, 0x3e, 0x2a, 0x32,
	0xdc, 0xc7, 0xa9, 0x5a, 0x06, 0xa1, 0xe4, 0x14, 0xf7, 0xca, 0x13, 0x94, 0x6c, 0xbd, 0xab, 0x63,
	0x46, 0xb0, 0x0d, 0x42, 0xf2, 0xa2, 0x5a, 0xf6, 0x05, 0x99, 0xfd, 0x33, 0xfa, 0x95, 0x99, 0x3f,
	0x77, 0xeb, 0xfc, 0xec, 0xc4, 0x71, 0x6c, 0xbf, 0x17, 0x6a, 0xea, 0x57, 0xff, 0x1a, 0xcf, 0x9e,
	0xb4, 0x90, 0x90, 0xc9, 0xfd, 0xc9, 0x03, 0xc7, 0x1d, 0x31, 0x77, 0x1a, 0x4a, 0x8e, 0xa8, 0x67,
	0xe6, 0xcb, 0x8e, 0x58, 0xcc, 0xad, 0x9f, 0xc2, 0xa6, 0xf9, 0x13, 0x4b, 0xfb, 0x2a, 0x5b, 0xa2,
	0xfc, 0xdb, 0xce, 0x8e, 0x33, 0x6e, 0x88, 0xe0, 0xac, 0x75, 0x13, 0x53, 0xc8, 0xac, 0xbe, 0xa7,
	0x23, 0x5b, 0xc8, 0xb1, 0x1f, 0x68, 0xb9, 0xd0, 0xad, 0x42, 0x06, 0x95, 0x61, 0xd4, 0x2e, 0x76,
	0xb0, 0x25, 0x3e, 0x83, 0x76, 0xd9, 0x67, 0x72, 0xf6, 0x35, 0xd3, 0x27, 0x5b, 0x93, 0xf9, 0xdd,
	0x85, 0x2b, 0xf7, 0x51, 0x56, 0x36, 0x6f, 0x4a, 0xb6, 0xdf, 0xcd, 0x6b, 0xeb, 0x0c, 0x1f, 0xa0,
	0x1d, 0x16, 0xb1, 0x16, 0x45, 0x64, 0xd2, 0x0d, 0xee, 0x48, 0x07, 0x28, 0x7c, 0xc2, 0xf5, 0xeb,
	0x16, 0x5c, 0x9d, 0xf8, 0x35, 0xb1, 0x7d, 0x8b, 0xd3, 0x72, 0x9a, 0x6f, 0x9c, 0x3b, 0xaf, 0x4d,
	0x39, 0x9a, 0xa1, 0x70, 0x04, 0x2d, 0x39, 0x9c, 0x25, 0xe8, 0x6f, 0x15, 0x82, 0x5c, 0xb6, 0xb6,
	0xa1, 0x83, 0x86, 0x0f, 0x8f, 0xc6, 0xd5, 0x6b, 0xb4, 0x8d, 0xd9, 0xaf, 0x2e, 0x7a, 0xd1, 0x29,
	0xeb, 0x49, 0xed, 0xaf, 0xa8, 0x6b, 0x22, 0xa3, 0xc5, 0x83, 0x7b, 0xea, 0x1f, 0x8c, 0x49, 0x27,
	0x74, 0xc6, 0x0c, 0xa0, 0xe8, 0xbe, 0x03, 0x4d, 0x11, 0x7a, 0xdb, 0xeb, 0x3c, 0x42, 0x96, 0x83,
	0xf7, 0x8e, 0x0e, 0xa5, 0x13, 0xbf, 0x86, 0x4e, 0x79, 0x2c, 0x65, 0xdf, 0x20, 0xfb, 0x4e, 0x8a,
	0x53, 0x3b, 0x13, 0x87, 0xd1, 0xbd, 0x4e, 0x88, 0x1d, 0xd1, 0xe2, 0x0d, 0x9b, 0x1b, 0x1f, 0x73,
	0x28, 0xd5, 0xb9, 0x5c, 0xd6, 0x9d, 0x93, 0xfb, 0x38, 0xf4, 0x33, 0x7e, 0x30, 0xf2, 0x11, 0x18,
	0xa1, 0xaa, 0xe1, 0x4f, 0x09, 0x3a, 0x86, 0x0e, 0x8a, 0xda, 0x3d, 0xb0, 0xf9, 0x2a, 0x0f, 0x9f,
	0xfc, 0x80, 0x75, 0xa8, 0x8a, 0x33, 0x78, 0xf8, 0x42, 0xc5, 0x95, 0xc7, 0x13, 0x42, 0xc5, 0x8d,
	0x0b, 0x10, 0xee, 0x43, 0x4b, 0x77, 0xd1, 0xa9, 0x2b, 0x51, 0xe2, 0xb8, 0x77, 0x36, 0x6f, 0xd3,
	0xbf, 0x98, 0xb8, 0xcd, 0xff, 0x62, 0x82, 0x8a, 0xb1, 0xfd, 0x1e, 0x40, 0xfe, 0x7f, 0x10, 0xf6,
	0x06, 0xd5, 0x0c, 0xda, 0x9f, 0x4d, 0x74, 0x0a, 0x60, 0x72, 0xca, 0x3b, 0x5f, 0x14, 0x8a, 0x65,
	0xb8, 0x47, 0xfa, 0x01, 0x74, 0x4e, 0x50, 0xa6, 0x75, 0x8a, 0x8f, 0xf3, 0x48, 0x90, 0x28, 0xdf,
	0x63, 0x41, 0xcf, 0x3d, 0xab, 0x13, 0x34, 0xdf, 0xfa, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0x4f,
	0x50, 0x66, 0x3e, 0x36, 0x43, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BaseAreaServiceClient is the client API for BaseAreaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BaseAreaServiceClient interface {
	//区域数据
	BaseArea(ctx context.Context, in *BaseAreaRequest, opts ...grpc.CallOption) (*BaseAreaResponse, error)
	//注释
	//根据仓库ID获取仓库配送区域GetWarehouseInfoByFanceCodeRequest
	GetAreaByWarehouseId(ctx context.Context, in *GetAreaByWarehouseIdRequest, opts ...grpc.CallOption) (*GetAreaByWarehouseIdResponse, error)
}

type baseAreaServiceClient struct {
	cc *grpc.ClientConn
}

func NewBaseAreaServiceClient(cc *grpc.ClientConn) BaseAreaServiceClient {
	return &baseAreaServiceClient{cc}
}

func (c *baseAreaServiceClient) BaseArea(ctx context.Context, in *BaseAreaRequest, opts ...grpc.CallOption) (*BaseAreaResponse, error) {
	out := new(BaseAreaResponse)
	err := c.cc.Invoke(ctx, "/dc.BaseAreaService/BaseArea", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseAreaServiceClient) GetAreaByWarehouseId(ctx context.Context, in *GetAreaByWarehouseIdRequest, opts ...grpc.CallOption) (*GetAreaByWarehouseIdResponse, error) {
	out := new(GetAreaByWarehouseIdResponse)
	err := c.cc.Invoke(ctx, "/dc.BaseAreaService/GetAreaByWarehouseId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BaseAreaServiceServer is the server API for BaseAreaService service.
type BaseAreaServiceServer interface {
	//区域数据
	BaseArea(context.Context, *BaseAreaRequest) (*BaseAreaResponse, error)
	//注释
	//根据仓库ID获取仓库配送区域GetWarehouseInfoByFanceCodeRequest
	GetAreaByWarehouseId(context.Context, *GetAreaByWarehouseIdRequest) (*GetAreaByWarehouseIdResponse, error)
}

// UnimplementedBaseAreaServiceServer can be embedded to have forward compatible implementations.
type UnimplementedBaseAreaServiceServer struct {
}

func (*UnimplementedBaseAreaServiceServer) BaseArea(ctx context.Context, req *BaseAreaRequest) (*BaseAreaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BaseArea not implemented")
}
func (*UnimplementedBaseAreaServiceServer) GetAreaByWarehouseId(ctx context.Context, req *GetAreaByWarehouseIdRequest) (*GetAreaByWarehouseIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAreaByWarehouseId not implemented")
}

func RegisterBaseAreaServiceServer(s *grpc.Server, srv BaseAreaServiceServer) {
	s.RegisterService(&_BaseAreaService_serviceDesc, srv)
}

func _BaseAreaService_BaseArea_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseAreaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseAreaServiceServer).BaseArea(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.BaseAreaService/BaseArea",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseAreaServiceServer).BaseArea(ctx, req.(*BaseAreaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseAreaService_GetAreaByWarehouseId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAreaByWarehouseIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseAreaServiceServer).GetAreaByWarehouseId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.BaseAreaService/GetAreaByWarehouseId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseAreaServiceServer).GetAreaByWarehouseId(ctx, req.(*GetAreaByWarehouseIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BaseAreaService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dc.BaseAreaService",
	HandlerType: (*BaseAreaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BaseArea",
			Handler:    _BaseAreaService_BaseArea_Handler,
		},
		{
			MethodName: "GetAreaByWarehouseId",
			Handler:    _BaseAreaService_GetAreaByWarehouseId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dc/dispatchcenter.proto",
}

// WarehouseServiceClient is the client API for WarehouseService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type WarehouseServiceClient interface {
	//仓库列表
	WarehouseList(ctx context.Context, in *WarehouseListRequest, opts ...grpc.CallOption) (*WarehouseListResponse, error)
	//变更仓库状态
	UpdateWarehouseStatus(ctx context.Context, in *UpdateWarehouseStatusRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//根据仓库id查询仓库详情
	GetWarehouseById(ctx context.Context, in *GetWarehouseByIdRequest, opts ...grpc.CallOption) (*GetWarehouseByIdResponse, error)
	// 新增仓库
	AddWarehouse(ctx context.Context, in *AddWarehouseRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 编辑仓库
	EditWarehouse(ctx context.Context, in *EditWarehouseRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 店铺绑定的所有仓库(门店仓、前置仓、虚拟仓)
	ShopWarehouseList(ctx context.Context, in *ShopWarehouseListRequest, opts ...grpc.CallOption) (*WarehouseListResponse, error)
	//新增仓库配送区域
	AddWarehouseArea(ctx context.Context, in *AddWarehouseAreaRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取仓库类型
	GetWarehouseType(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetWarehouseTypeResponse, error)
	//获取仓库级别
	GetWarehouseLevel(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetWarehouseLevelResponse, error)
	//根据区域获取仓库列表
	GetWarehouseByArea(ctx context.Context, in *WarehouseByAreaRequest, opts ...grpc.CallOption) (*WarehouseByAreaResponse, error)
	//仓库(前置仓)关联门店
	WarehouseRelation(ctx context.Context, in *WarehouseRelationRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//根据仓库id获取已绑定的门店信息列表
	GetWarehouseRelationList(ctx context.Context, in *WarehouseRelationListRequest, opts ...grpc.CallOption) (*WarehouseRelationListResponse, error)
	//根据财务编码获取仓库信息
	GetWarehouseInfoByFanceCode(ctx context.Context, in *GetWarehouseInfoByFanceCodeRequest, opts ...grpc.CallOption) (*GetWarehouseInfoByFanceCodeResponse, error)
	//根据财务编码批量获取仓库信息
	GetWarehouseInfoByFanceCodes(ctx context.Context, in *GetWarehouseInfoByFanceCodesRequest, opts ...grpc.CallOption) (*GetWarehouseInfoByFanceCodesResponse, error)
	//根据条件获取仓库信息（V3.1版本的需要）
	GetWarehouseInfoByCondition(ctx context.Context, in *GetWarehouseInfoByConditionRequest, opts ...grpc.CallOption) (*GetWarehouseInfoByConditionResponse, error)
	//根据分类获取门店列表
	GetStoreListByCategory(ctx context.Context, in *GetStoreListByCategoryRequest, opts ...grpc.CallOption) (*GetStoreListByCategoryResponse, error)
	//前置仓关联的门店信息
	GetWarehouseRelation(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetWarehouseRelationResponse, error)
	//仓库操作日志
	WarehouseLog(ctx context.Context, in *WarehouseLogRequest, opts ...grpc.CallOption) (*WarehouseLogResponse, error)
	// 区域仓关联前置仓
	PreposeWarehouseRelation(ctx context.Context, in *PreposeWarehouseRelationRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 根据id查询区域仓关联的前置仓信息列表
	GetPreposeWarehouseRelationList(ctx context.Context, in *WarehouseRelationListRequest, opts ...grpc.CallOption) (*RegionRelationListRespon, error)
	// 查询前置仓关联的所有城市列表
	PreposeWarehouseCityList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*PreposeCiytListResponse, error)
	// 查询门店是否绑定了前置虚拟仓
	GetShopWarehouseInfoByFinanceCode(ctx context.Context, in *GetShopWarehouseInfoByFinanceCodeRequest, opts ...grpc.CallOption) (*GetShopWarehouseInfoByFinanceCodeResponse, error)
	// 门店绑定信息列表
	ShopBindInfoList(ctx context.Context, in *ShopBindInfoRequest, opts ...grpc.CallOption) (*ShopBindInfoRespond, error)
	// 门店仓库管理列表
	WarehouseRelationShopList(ctx context.Context, in *ShopBindWarehouseReq, opts ...grpc.CallOption) (*ShopBindWarehouseRes, error)
	// 使用店铺id获取其绑定仓库信息
	ShopBindInfoListByShopId(ctx context.Context, in *ShopBindInfoByShopIdRequest, opts ...grpc.CallOption) (*ShopBindInfoByShopIdRespond, error)
	//获取绑定了仓库的门店数量
	BindShops(ctx context.Context, in *BindShopsRequest, opts ...grpc.CallOption) (*BindShopsRespond, error)
	//插入美团渠道绑定仓库数据
	StoreWarehouseRelationShop(ctx context.Context, in *StoreWarehouseRelationShopRequest, opts ...grpc.CallOption) (*StoreWarehouseRelationShopRespond, error)
	// 同步仓库信息列表
	GetA8WareHouseList(ctx context.Context, in *GetA8WareHouseListRequest, opts ...grpc.CallOption) (*GetA8WareHouseListResponse, error)
	// 初始化店铺绑定数据
	InitBindShopData(ctx context.Context, in *InitShopDataRequest, opts ...grpc.CallOption) (*InitShopDataRespond, error)
	// 修复初始化店铺绑定数据
	InitBindMTShopData(ctx context.Context, in *InitShopDataRequest, opts ...grpc.CallOption) (*InitShopDataRespond, error)
	// 用code取仓库信息
	GetWarehouseInfoByCode(ctx context.Context, in *GetWarehouseInfoByCodeRequest, opts ...grpc.CallOption) (*GetWarehouseInfoByCodeResponse, error)
	//同步oms仓库
	SyncOmsWarehouse(ctx context.Context, in *SyncOmsWarehouseRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	//移除渠道绑定关系
	RemoveShop(ctx context.Context, in *RemoveShopRequest, opts ...grpc.CallOption) (*RemoveShopRespond, error)
}

type warehouseServiceClient struct {
	cc *grpc.ClientConn
}

func NewWarehouseServiceClient(cc *grpc.ClientConn) WarehouseServiceClient {
	return &warehouseServiceClient{cc}
}

func (c *warehouseServiceClient) WarehouseList(ctx context.Context, in *WarehouseListRequest, opts ...grpc.CallOption) (*WarehouseListResponse, error) {
	out := new(WarehouseListResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/WarehouseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) UpdateWarehouseStatus(ctx context.Context, in *UpdateWarehouseStatusRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/UpdateWarehouseStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseById(ctx context.Context, in *GetWarehouseByIdRequest, opts ...grpc.CallOption) (*GetWarehouseByIdResponse, error) {
	out := new(GetWarehouseByIdResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) AddWarehouse(ctx context.Context, in *AddWarehouseRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/AddWarehouse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) EditWarehouse(ctx context.Context, in *EditWarehouseRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/EditWarehouse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) ShopWarehouseList(ctx context.Context, in *ShopWarehouseListRequest, opts ...grpc.CallOption) (*WarehouseListResponse, error) {
	out := new(WarehouseListResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/ShopWarehouseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) AddWarehouseArea(ctx context.Context, in *AddWarehouseAreaRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/AddWarehouseArea", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseType(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetWarehouseTypeResponse, error) {
	out := new(GetWarehouseTypeResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseLevel(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetWarehouseLevelResponse, error) {
	out := new(GetWarehouseLevelResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseByArea(ctx context.Context, in *WarehouseByAreaRequest, opts ...grpc.CallOption) (*WarehouseByAreaResponse, error) {
	out := new(WarehouseByAreaResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseByArea", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) WarehouseRelation(ctx context.Context, in *WarehouseRelationRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/WarehouseRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseRelationList(ctx context.Context, in *WarehouseRelationListRequest, opts ...grpc.CallOption) (*WarehouseRelationListResponse, error) {
	out := new(WarehouseRelationListResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseRelationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseInfoByFanceCode(ctx context.Context, in *GetWarehouseInfoByFanceCodeRequest, opts ...grpc.CallOption) (*GetWarehouseInfoByFanceCodeResponse, error) {
	out := new(GetWarehouseInfoByFanceCodeResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseInfoByFanceCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseInfoByFanceCodes(ctx context.Context, in *GetWarehouseInfoByFanceCodesRequest, opts ...grpc.CallOption) (*GetWarehouseInfoByFanceCodesResponse, error) {
	out := new(GetWarehouseInfoByFanceCodesResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseInfoByFanceCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseInfoByCondition(ctx context.Context, in *GetWarehouseInfoByConditionRequest, opts ...grpc.CallOption) (*GetWarehouseInfoByConditionResponse, error) {
	out := new(GetWarehouseInfoByConditionResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseInfoByCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetStoreListByCategory(ctx context.Context, in *GetStoreListByCategoryRequest, opts ...grpc.CallOption) (*GetStoreListByCategoryResponse, error) {
	out := new(GetStoreListByCategoryResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetStoreListByCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseRelation(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetWarehouseRelationResponse, error) {
	out := new(GetWarehouseRelationResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) WarehouseLog(ctx context.Context, in *WarehouseLogRequest, opts ...grpc.CallOption) (*WarehouseLogResponse, error) {
	out := new(WarehouseLogResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/WarehouseLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) PreposeWarehouseRelation(ctx context.Context, in *PreposeWarehouseRelationRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/PreposeWarehouseRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetPreposeWarehouseRelationList(ctx context.Context, in *WarehouseRelationListRequest, opts ...grpc.CallOption) (*RegionRelationListRespon, error) {
	out := new(RegionRelationListRespon)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetPreposeWarehouseRelationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) PreposeWarehouseCityList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*PreposeCiytListResponse, error) {
	out := new(PreposeCiytListResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/PreposeWarehouseCityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetShopWarehouseInfoByFinanceCode(ctx context.Context, in *GetShopWarehouseInfoByFinanceCodeRequest, opts ...grpc.CallOption) (*GetShopWarehouseInfoByFinanceCodeResponse, error) {
	out := new(GetShopWarehouseInfoByFinanceCodeResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetShopWarehouseInfoByFinanceCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) ShopBindInfoList(ctx context.Context, in *ShopBindInfoRequest, opts ...grpc.CallOption) (*ShopBindInfoRespond, error) {
	out := new(ShopBindInfoRespond)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/ShopBindInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) WarehouseRelationShopList(ctx context.Context, in *ShopBindWarehouseReq, opts ...grpc.CallOption) (*ShopBindWarehouseRes, error) {
	out := new(ShopBindWarehouseRes)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/WarehouseRelationShopList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) ShopBindInfoListByShopId(ctx context.Context, in *ShopBindInfoByShopIdRequest, opts ...grpc.CallOption) (*ShopBindInfoByShopIdRespond, error) {
	out := new(ShopBindInfoByShopIdRespond)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/ShopBindInfoListByShopId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) BindShops(ctx context.Context, in *BindShopsRequest, opts ...grpc.CallOption) (*BindShopsRespond, error) {
	out := new(BindShopsRespond)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/BindShops", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) StoreWarehouseRelationShop(ctx context.Context, in *StoreWarehouseRelationShopRequest, opts ...grpc.CallOption) (*StoreWarehouseRelationShopRespond, error) {
	out := new(StoreWarehouseRelationShopRespond)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/StoreWarehouseRelationShop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetA8WareHouseList(ctx context.Context, in *GetA8WareHouseListRequest, opts ...grpc.CallOption) (*GetA8WareHouseListResponse, error) {
	out := new(GetA8WareHouseListResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetA8WareHouseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) InitBindShopData(ctx context.Context, in *InitShopDataRequest, opts ...grpc.CallOption) (*InitShopDataRespond, error) {
	out := new(InitShopDataRespond)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/InitBindShopData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) InitBindMTShopData(ctx context.Context, in *InitShopDataRequest, opts ...grpc.CallOption) (*InitShopDataRespond, error) {
	out := new(InitShopDataRespond)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/InitBindMTShopData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) GetWarehouseInfoByCode(ctx context.Context, in *GetWarehouseInfoByCodeRequest, opts ...grpc.CallOption) (*GetWarehouseInfoByCodeResponse, error) {
	out := new(GetWarehouseInfoByCodeResponse)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/GetWarehouseInfoByCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) SyncOmsWarehouse(ctx context.Context, in *SyncOmsWarehouseRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/SyncOmsWarehouse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *warehouseServiceClient) RemoveShop(ctx context.Context, in *RemoveShopRequest, opts ...grpc.CallOption) (*RemoveShopRespond, error) {
	out := new(RemoveShopRespond)
	err := c.cc.Invoke(ctx, "/dc.WarehouseService/RemoveShop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WarehouseServiceServer is the server API for WarehouseService service.
type WarehouseServiceServer interface {
	//仓库列表
	WarehouseList(context.Context, *WarehouseListRequest) (*WarehouseListResponse, error)
	//变更仓库状态
	UpdateWarehouseStatus(context.Context, *UpdateWarehouseStatusRequest) (*BaseResponse, error)
	//根据仓库id查询仓库详情
	GetWarehouseById(context.Context, *GetWarehouseByIdRequest) (*GetWarehouseByIdResponse, error)
	// 新增仓库
	AddWarehouse(context.Context, *AddWarehouseRequest) (*BaseResponse, error)
	// 编辑仓库
	EditWarehouse(context.Context, *EditWarehouseRequest) (*BaseResponse, error)
	// 店铺绑定的所有仓库(门店仓、前置仓、虚拟仓)
	ShopWarehouseList(context.Context, *ShopWarehouseListRequest) (*WarehouseListResponse, error)
	//新增仓库配送区域
	AddWarehouseArea(context.Context, *AddWarehouseAreaRequest) (*BaseResponse, error)
	//获取仓库类型
	GetWarehouseType(context.Context, *Empty) (*GetWarehouseTypeResponse, error)
	//获取仓库级别
	GetWarehouseLevel(context.Context, *Empty) (*GetWarehouseLevelResponse, error)
	//根据区域获取仓库列表
	GetWarehouseByArea(context.Context, *WarehouseByAreaRequest) (*WarehouseByAreaResponse, error)
	//仓库(前置仓)关联门店
	WarehouseRelation(context.Context, *WarehouseRelationRequest) (*BaseResponse, error)
	//根据仓库id获取已绑定的门店信息列表
	GetWarehouseRelationList(context.Context, *WarehouseRelationListRequest) (*WarehouseRelationListResponse, error)
	//根据财务编码获取仓库信息
	GetWarehouseInfoByFanceCode(context.Context, *GetWarehouseInfoByFanceCodeRequest) (*GetWarehouseInfoByFanceCodeResponse, error)
	//根据财务编码批量获取仓库信息
	GetWarehouseInfoByFanceCodes(context.Context, *GetWarehouseInfoByFanceCodesRequest) (*GetWarehouseInfoByFanceCodesResponse, error)
	//根据条件获取仓库信息（V3.1版本的需要）
	GetWarehouseInfoByCondition(context.Context, *GetWarehouseInfoByConditionRequest) (*GetWarehouseInfoByConditionResponse, error)
	//根据分类获取门店列表
	GetStoreListByCategory(context.Context, *GetStoreListByCategoryRequest) (*GetStoreListByCategoryResponse, error)
	//前置仓关联的门店信息
	GetWarehouseRelation(context.Context, *Empty) (*GetWarehouseRelationResponse, error)
	//仓库操作日志
	WarehouseLog(context.Context, *WarehouseLogRequest) (*WarehouseLogResponse, error)
	// 区域仓关联前置仓
	PreposeWarehouseRelation(context.Context, *PreposeWarehouseRelationRequest) (*BaseResponse, error)
	// 根据id查询区域仓关联的前置仓信息列表
	GetPreposeWarehouseRelationList(context.Context, *WarehouseRelationListRequest) (*RegionRelationListRespon, error)
	// 查询前置仓关联的所有城市列表
	PreposeWarehouseCityList(context.Context, *Empty) (*PreposeCiytListResponse, error)
	// 查询门店是否绑定了前置虚拟仓
	GetShopWarehouseInfoByFinanceCode(context.Context, *GetShopWarehouseInfoByFinanceCodeRequest) (*GetShopWarehouseInfoByFinanceCodeResponse, error)
	// 门店绑定信息列表
	ShopBindInfoList(context.Context, *ShopBindInfoRequest) (*ShopBindInfoRespond, error)
	// 门店仓库管理列表
	WarehouseRelationShopList(context.Context, *ShopBindWarehouseReq) (*ShopBindWarehouseRes, error)
	// 使用店铺id获取其绑定仓库信息
	ShopBindInfoListByShopId(context.Context, *ShopBindInfoByShopIdRequest) (*ShopBindInfoByShopIdRespond, error)
	//获取绑定了仓库的门店数量
	BindShops(context.Context, *BindShopsRequest) (*BindShopsRespond, error)
	//插入美团渠道绑定仓库数据
	StoreWarehouseRelationShop(context.Context, *StoreWarehouseRelationShopRequest) (*StoreWarehouseRelationShopRespond, error)
	// 同步仓库信息列表
	GetA8WareHouseList(context.Context, *GetA8WareHouseListRequest) (*GetA8WareHouseListResponse, error)
	// 初始化店铺绑定数据
	InitBindShopData(context.Context, *InitShopDataRequest) (*InitShopDataRespond, error)
	// 修复初始化店铺绑定数据
	InitBindMTShopData(context.Context, *InitShopDataRequest) (*InitShopDataRespond, error)
	// 用code取仓库信息
	GetWarehouseInfoByCode(context.Context, *GetWarehouseInfoByCodeRequest) (*GetWarehouseInfoByCodeResponse, error)
	//同步oms仓库
	SyncOmsWarehouse(context.Context, *SyncOmsWarehouseRequest) (*empty.Empty, error)
	//移除渠道绑定关系
	RemoveShop(context.Context, *RemoveShopRequest) (*RemoveShopRespond, error)
}

// UnimplementedWarehouseServiceServer can be embedded to have forward compatible implementations.
type UnimplementedWarehouseServiceServer struct {
}

func (*UnimplementedWarehouseServiceServer) WarehouseList(ctx context.Context, req *WarehouseListRequest) (*WarehouseListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WarehouseList not implemented")
}
func (*UnimplementedWarehouseServiceServer) UpdateWarehouseStatus(ctx context.Context, req *UpdateWarehouseStatusRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWarehouseStatus not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseById(ctx context.Context, req *GetWarehouseByIdRequest) (*GetWarehouseByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseById not implemented")
}
func (*UnimplementedWarehouseServiceServer) AddWarehouse(ctx context.Context, req *AddWarehouseRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddWarehouse not implemented")
}
func (*UnimplementedWarehouseServiceServer) EditWarehouse(ctx context.Context, req *EditWarehouseRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditWarehouse not implemented")
}
func (*UnimplementedWarehouseServiceServer) ShopWarehouseList(ctx context.Context, req *ShopWarehouseListRequest) (*WarehouseListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopWarehouseList not implemented")
}
func (*UnimplementedWarehouseServiceServer) AddWarehouseArea(ctx context.Context, req *AddWarehouseAreaRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddWarehouseArea not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseType(ctx context.Context, req *Empty) (*GetWarehouseTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseType not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseLevel(ctx context.Context, req *Empty) (*GetWarehouseLevelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseLevel not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseByArea(ctx context.Context, req *WarehouseByAreaRequest) (*WarehouseByAreaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseByArea not implemented")
}
func (*UnimplementedWarehouseServiceServer) WarehouseRelation(ctx context.Context, req *WarehouseRelationRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WarehouseRelation not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseRelationList(ctx context.Context, req *WarehouseRelationListRequest) (*WarehouseRelationListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseRelationList not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseInfoByFanceCode(ctx context.Context, req *GetWarehouseInfoByFanceCodeRequest) (*GetWarehouseInfoByFanceCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseInfoByFanceCode not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseInfoByFanceCodes(ctx context.Context, req *GetWarehouseInfoByFanceCodesRequest) (*GetWarehouseInfoByFanceCodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseInfoByFanceCodes not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseInfoByCondition(ctx context.Context, req *GetWarehouseInfoByConditionRequest) (*GetWarehouseInfoByConditionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseInfoByCondition not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetStoreListByCategory(ctx context.Context, req *GetStoreListByCategoryRequest) (*GetStoreListByCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStoreListByCategory not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseRelation(ctx context.Context, req *Empty) (*GetWarehouseRelationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseRelation not implemented")
}
func (*UnimplementedWarehouseServiceServer) WarehouseLog(ctx context.Context, req *WarehouseLogRequest) (*WarehouseLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WarehouseLog not implemented")
}
func (*UnimplementedWarehouseServiceServer) PreposeWarehouseRelation(ctx context.Context, req *PreposeWarehouseRelationRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreposeWarehouseRelation not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetPreposeWarehouseRelationList(ctx context.Context, req *WarehouseRelationListRequest) (*RegionRelationListRespon, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPreposeWarehouseRelationList not implemented")
}
func (*UnimplementedWarehouseServiceServer) PreposeWarehouseCityList(ctx context.Context, req *Empty) (*PreposeCiytListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreposeWarehouseCityList not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetShopWarehouseInfoByFinanceCode(ctx context.Context, req *GetShopWarehouseInfoByFinanceCodeRequest) (*GetShopWarehouseInfoByFinanceCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetShopWarehouseInfoByFinanceCode not implemented")
}
func (*UnimplementedWarehouseServiceServer) ShopBindInfoList(ctx context.Context, req *ShopBindInfoRequest) (*ShopBindInfoRespond, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopBindInfoList not implemented")
}
func (*UnimplementedWarehouseServiceServer) WarehouseRelationShopList(ctx context.Context, req *ShopBindWarehouseReq) (*ShopBindWarehouseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WarehouseRelationShopList not implemented")
}
func (*UnimplementedWarehouseServiceServer) ShopBindInfoListByShopId(ctx context.Context, req *ShopBindInfoByShopIdRequest) (*ShopBindInfoByShopIdRespond, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopBindInfoListByShopId not implemented")
}
func (*UnimplementedWarehouseServiceServer) BindShops(ctx context.Context, req *BindShopsRequest) (*BindShopsRespond, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindShops not implemented")
}
func (*UnimplementedWarehouseServiceServer) StoreWarehouseRelationShop(ctx context.Context, req *StoreWarehouseRelationShopRequest) (*StoreWarehouseRelationShopRespond, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreWarehouseRelationShop not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetA8WareHouseList(ctx context.Context, req *GetA8WareHouseListRequest) (*GetA8WareHouseListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetA8WareHouseList not implemented")
}
func (*UnimplementedWarehouseServiceServer) InitBindShopData(ctx context.Context, req *InitShopDataRequest) (*InitShopDataRespond, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitBindShopData not implemented")
}
func (*UnimplementedWarehouseServiceServer) InitBindMTShopData(ctx context.Context, req *InitShopDataRequest) (*InitShopDataRespond, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitBindMTShopData not implemented")
}
func (*UnimplementedWarehouseServiceServer) GetWarehouseInfoByCode(ctx context.Context, req *GetWarehouseInfoByCodeRequest) (*GetWarehouseInfoByCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWarehouseInfoByCode not implemented")
}
func (*UnimplementedWarehouseServiceServer) SyncOmsWarehouse(ctx context.Context, req *SyncOmsWarehouseRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncOmsWarehouse not implemented")
}
func (*UnimplementedWarehouseServiceServer) RemoveShop(ctx context.Context, req *RemoveShopRequest) (*RemoveShopRespond, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveShop not implemented")
}

func RegisterWarehouseServiceServer(s *grpc.Server, srv WarehouseServiceServer) {
	s.RegisterService(&_WarehouseService_serviceDesc, srv)
}

func _WarehouseService_WarehouseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).WarehouseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/WarehouseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).WarehouseList(ctx, req.(*WarehouseListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_UpdateWarehouseStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWarehouseStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).UpdateWarehouseStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/UpdateWarehouseStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).UpdateWarehouseStatus(ctx, req.(*UpdateWarehouseStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWarehouseByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseById(ctx, req.(*GetWarehouseByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_AddWarehouse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWarehouseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).AddWarehouse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/AddWarehouse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).AddWarehouse(ctx, req.(*AddWarehouseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_EditWarehouse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditWarehouseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).EditWarehouse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/EditWarehouse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).EditWarehouse(ctx, req.(*EditWarehouseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_ShopWarehouseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShopWarehouseListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).ShopWarehouseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/ShopWarehouseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).ShopWarehouseList(ctx, req.(*ShopWarehouseListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_AddWarehouseArea_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWarehouseAreaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).AddWarehouseArea(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/AddWarehouseArea",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).AddWarehouseArea(ctx, req.(*AddWarehouseAreaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseType(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseLevel(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseByArea_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseByAreaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseByArea(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseByArea",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseByArea(ctx, req.(*WarehouseByAreaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_WarehouseRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).WarehouseRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/WarehouseRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).WarehouseRelation(ctx, req.(*WarehouseRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseRelationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseRelationListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseRelationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseRelationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseRelationList(ctx, req.(*WarehouseRelationListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseInfoByFanceCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWarehouseInfoByFanceCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseInfoByFanceCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseInfoByFanceCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseInfoByFanceCode(ctx, req.(*GetWarehouseInfoByFanceCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseInfoByFanceCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWarehouseInfoByFanceCodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseInfoByFanceCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseInfoByFanceCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseInfoByFanceCodes(ctx, req.(*GetWarehouseInfoByFanceCodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseInfoByCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWarehouseInfoByConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseInfoByCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseInfoByCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseInfoByCondition(ctx, req.(*GetWarehouseInfoByConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetStoreListByCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoreListByCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetStoreListByCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetStoreListByCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetStoreListByCategory(ctx, req.(*GetStoreListByCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseRelation(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_WarehouseLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).WarehouseLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/WarehouseLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).WarehouseLog(ctx, req.(*WarehouseLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_PreposeWarehouseRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreposeWarehouseRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).PreposeWarehouseRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/PreposeWarehouseRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).PreposeWarehouseRelation(ctx, req.(*PreposeWarehouseRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetPreposeWarehouseRelationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WarehouseRelationListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetPreposeWarehouseRelationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetPreposeWarehouseRelationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetPreposeWarehouseRelationList(ctx, req.(*WarehouseRelationListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_PreposeWarehouseCityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).PreposeWarehouseCityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/PreposeWarehouseCityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).PreposeWarehouseCityList(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetShopWarehouseInfoByFinanceCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShopWarehouseInfoByFinanceCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetShopWarehouseInfoByFinanceCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetShopWarehouseInfoByFinanceCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetShopWarehouseInfoByFinanceCode(ctx, req.(*GetShopWarehouseInfoByFinanceCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_ShopBindInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShopBindInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).ShopBindInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/ShopBindInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).ShopBindInfoList(ctx, req.(*ShopBindInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_WarehouseRelationShopList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShopBindWarehouseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).WarehouseRelationShopList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/WarehouseRelationShopList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).WarehouseRelationShopList(ctx, req.(*ShopBindWarehouseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_ShopBindInfoListByShopId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShopBindInfoByShopIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).ShopBindInfoListByShopId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/ShopBindInfoListByShopId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).ShopBindInfoListByShopId(ctx, req.(*ShopBindInfoByShopIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_BindShops_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindShopsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).BindShops(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/BindShops",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).BindShops(ctx, req.(*BindShopsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_StoreWarehouseRelationShop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreWarehouseRelationShopRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).StoreWarehouseRelationShop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/StoreWarehouseRelationShop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).StoreWarehouseRelationShop(ctx, req.(*StoreWarehouseRelationShopRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetA8WareHouseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetA8WareHouseListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetA8WareHouseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetA8WareHouseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetA8WareHouseList(ctx, req.(*GetA8WareHouseListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_InitBindShopData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitShopDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).InitBindShopData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/InitBindShopData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).InitBindShopData(ctx, req.(*InitShopDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_InitBindMTShopData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitShopDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).InitBindMTShopData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/InitBindMTShopData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).InitBindMTShopData(ctx, req.(*InitShopDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_GetWarehouseInfoByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWarehouseInfoByCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).GetWarehouseInfoByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/GetWarehouseInfoByCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).GetWarehouseInfoByCode(ctx, req.(*GetWarehouseInfoByCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_SyncOmsWarehouse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncOmsWarehouseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).SyncOmsWarehouse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/SyncOmsWarehouse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).SyncOmsWarehouse(ctx, req.(*SyncOmsWarehouseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WarehouseService_RemoveShop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveShopRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WarehouseServiceServer).RemoveShop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.WarehouseService/RemoveShop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WarehouseServiceServer).RemoveShop(ctx, req.(*RemoveShopRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _WarehouseService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dc.WarehouseService",
	HandlerType: (*WarehouseServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WarehouseList",
			Handler:    _WarehouseService_WarehouseList_Handler,
		},
		{
			MethodName: "UpdateWarehouseStatus",
			Handler:    _WarehouseService_UpdateWarehouseStatus_Handler,
		},
		{
			MethodName: "GetWarehouseById",
			Handler:    _WarehouseService_GetWarehouseById_Handler,
		},
		{
			MethodName: "AddWarehouse",
			Handler:    _WarehouseService_AddWarehouse_Handler,
		},
		{
			MethodName: "EditWarehouse",
			Handler:    _WarehouseService_EditWarehouse_Handler,
		},
		{
			MethodName: "ShopWarehouseList",
			Handler:    _WarehouseService_ShopWarehouseList_Handler,
		},
		{
			MethodName: "AddWarehouseArea",
			Handler:    _WarehouseService_AddWarehouseArea_Handler,
		},
		{
			MethodName: "GetWarehouseType",
			Handler:    _WarehouseService_GetWarehouseType_Handler,
		},
		{
			MethodName: "GetWarehouseLevel",
			Handler:    _WarehouseService_GetWarehouseLevel_Handler,
		},
		{
			MethodName: "GetWarehouseByArea",
			Handler:    _WarehouseService_GetWarehouseByArea_Handler,
		},
		{
			MethodName: "WarehouseRelation",
			Handler:    _WarehouseService_WarehouseRelation_Handler,
		},
		{
			MethodName: "GetWarehouseRelationList",
			Handler:    _WarehouseService_GetWarehouseRelationList_Handler,
		},
		{
			MethodName: "GetWarehouseInfoByFanceCode",
			Handler:    _WarehouseService_GetWarehouseInfoByFanceCode_Handler,
		},
		{
			MethodName: "GetWarehouseInfoByFanceCodes",
			Handler:    _WarehouseService_GetWarehouseInfoByFanceCodes_Handler,
		},
		{
			MethodName: "GetWarehouseInfoByCondition",
			Handler:    _WarehouseService_GetWarehouseInfoByCondition_Handler,
		},
		{
			MethodName: "GetStoreListByCategory",
			Handler:    _WarehouseService_GetStoreListByCategory_Handler,
		},
		{
			MethodName: "GetWarehouseRelation",
			Handler:    _WarehouseService_GetWarehouseRelation_Handler,
		},
		{
			MethodName: "WarehouseLog",
			Handler:    _WarehouseService_WarehouseLog_Handler,
		},
		{
			MethodName: "PreposeWarehouseRelation",
			Handler:    _WarehouseService_PreposeWarehouseRelation_Handler,
		},
		{
			MethodName: "GetPreposeWarehouseRelationList",
			Handler:    _WarehouseService_GetPreposeWarehouseRelationList_Handler,
		},
		{
			MethodName: "PreposeWarehouseCityList",
			Handler:    _WarehouseService_PreposeWarehouseCityList_Handler,
		},
		{
			MethodName: "GetShopWarehouseInfoByFinanceCode",
			Handler:    _WarehouseService_GetShopWarehouseInfoByFinanceCode_Handler,
		},
		{
			MethodName: "ShopBindInfoList",
			Handler:    _WarehouseService_ShopBindInfoList_Handler,
		},
		{
			MethodName: "WarehouseRelationShopList",
			Handler:    _WarehouseService_WarehouseRelationShopList_Handler,
		},
		{
			MethodName: "ShopBindInfoListByShopId",
			Handler:    _WarehouseService_ShopBindInfoListByShopId_Handler,
		},
		{
			MethodName: "BindShops",
			Handler:    _WarehouseService_BindShops_Handler,
		},
		{
			MethodName: "StoreWarehouseRelationShop",
			Handler:    _WarehouseService_StoreWarehouseRelationShop_Handler,
		},
		{
			MethodName: "GetA8WareHouseList",
			Handler:    _WarehouseService_GetA8WareHouseList_Handler,
		},
		{
			MethodName: "InitBindShopData",
			Handler:    _WarehouseService_InitBindShopData_Handler,
		},
		{
			MethodName: "InitBindMTShopData",
			Handler:    _WarehouseService_InitBindMTShopData_Handler,
		},
		{
			MethodName: "GetWarehouseInfoByCode",
			Handler:    _WarehouseService_GetWarehouseInfoByCode_Handler,
		},
		{
			MethodName: "SyncOmsWarehouse",
			Handler:    _WarehouseService_SyncOmsWarehouse_Handler,
		},
		{
			MethodName: "RemoveShop",
			Handler:    _WarehouseService_RemoveShop_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dc/dispatchcenter.proto",
}

// DemolitionOrderServiceClient is the client API for DemolitionOrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DemolitionOrderServiceClient interface {
	SetDemolitionOrderTestData(ctx context.Context, in *DataRequest, opts ...grpc.CallOption) (*BaseResponse, error)
}

type demolitionOrderServiceClient struct {
	cc *grpc.ClientConn
}

func NewDemolitionOrderServiceClient(cc *grpc.ClientConn) DemolitionOrderServiceClient {
	return &demolitionOrderServiceClient{cc}
}

func (c *demolitionOrderServiceClient) SetDemolitionOrderTestData(ctx context.Context, in *DataRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/dc.DemolitionOrderService/SetDemolitionOrderTestData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DemolitionOrderServiceServer is the server API for DemolitionOrderService service.
type DemolitionOrderServiceServer interface {
	SetDemolitionOrderTestData(context.Context, *DataRequest) (*BaseResponse, error)
}

// UnimplementedDemolitionOrderServiceServer can be embedded to have forward compatible implementations.
type UnimplementedDemolitionOrderServiceServer struct {
}

func (*UnimplementedDemolitionOrderServiceServer) SetDemolitionOrderTestData(ctx context.Context, req *DataRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDemolitionOrderTestData not implemented")
}

func RegisterDemolitionOrderServiceServer(s *grpc.Server, srv DemolitionOrderServiceServer) {
	s.RegisterService(&_DemolitionOrderService_serviceDesc, srv)
}

func _DemolitionOrderService_SetDemolitionOrderTestData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DemolitionOrderServiceServer).SetDemolitionOrderTestData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dc.DemolitionOrderService/SetDemolitionOrderTestData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DemolitionOrderServiceServer).SetDemolitionOrderTestData(ctx, req.(*DataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DemolitionOrderService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "dc.DemolitionOrderService",
	HandlerType: (*DemolitionOrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetDemolitionOrderTestData",
			Handler:    _DemolitionOrderService_SetDemolitionOrderTestData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dc/dispatchcenter.proto",
}
