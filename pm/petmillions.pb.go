// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pm/petmillions.proto

package pm

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GetPetBreedsReq struct {
	PetDictParentIds     []string `protobuf:"bytes,1,rep,name=pet_dict_parent_ids,json=petDictParentIds,proto3" json:"pet_dict_parent_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetBreedsReq) Reset()         { *m = GetPetBreedsReq{} }
func (m *GetPetBreedsReq) String() string { return proto.CompactTextString(m) }
func (*GetPetBreedsReq) ProtoMessage()    {}
func (*GetPetBreedsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{0}
}

func (m *GetPetBreedsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetBreedsReq.Unmarshal(m, b)
}
func (m *GetPetBreedsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetBreedsReq.Marshal(b, m, deterministic)
}
func (m *GetPetBreedsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetBreedsReq.Merge(m, src)
}
func (m *GetPetBreedsReq) XXX_Size() int {
	return xxx_messageInfo_GetPetBreedsReq.Size(m)
}
func (m *GetPetBreedsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetBreedsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetBreedsReq proto.InternalMessageInfo

func (m *GetPetBreedsReq) GetPetDictParentIds() []string {
	if m != nil {
		return m.PetDictParentIds
	}
	return nil
}

type GetBaseCityReq struct {
	Citynames            []string `protobuf:"bytes,1,rep,name=citynames,proto3" json:"citynames"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBaseCityReq) Reset()         { *m = GetBaseCityReq{} }
func (m *GetBaseCityReq) String() string { return proto.CompactTextString(m) }
func (*GetBaseCityReq) ProtoMessage()    {}
func (*GetBaseCityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{1}
}

func (m *GetBaseCityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBaseCityReq.Unmarshal(m, b)
}
func (m *GetBaseCityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBaseCityReq.Marshal(b, m, deterministic)
}
func (m *GetBaseCityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBaseCityReq.Merge(m, src)
}
func (m *GetBaseCityReq) XXX_Size() int {
	return xxx_messageInfo_GetBaseCityReq.Size(m)
}
func (m *GetBaseCityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBaseCityReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBaseCityReq proto.InternalMessageInfo

func (m *GetBaseCityReq) GetCitynames() []string {
	if m != nil {
		return m.Citynames
	}
	return nil
}

type BaseRes struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//数据条数
	DataCount int64 `protobuf:"varint,3,opt,name=data_count,json=dataCount,proto3" json:"data_count"`
	//结果
	Data                 string   `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRes) Reset()         { *m = BaseRes{} }
func (m *BaseRes) String() string { return proto.CompactTextString(m) }
func (*BaseRes) ProtoMessage()    {}
func (*BaseRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{2}
}

func (m *BaseRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRes.Unmarshal(m, b)
}
func (m *BaseRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRes.Marshal(b, m, deterministic)
}
func (m *BaseRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRes.Merge(m, src)
}
func (m *BaseRes) XXX_Size() int {
	return xxx_messageInfo_BaseRes.Size(m)
}
func (m *BaseRes) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRes.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRes proto.InternalMessageInfo

func (m *BaseRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseRes) GetDataCount() int64 {
	if m != nil {
		return m.DataCount
	}
	return 0
}

func (m *BaseRes) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

//创建疫苗信息
type CreateVaccineReq struct {
	//疫苗id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//宠物类型
	PetType int32 `protobuf:"varint,2,opt,name=pet_type,json=petType,proto3" json:"pet_type"`
	//生产商
	Supplier string `protobuf:"bytes,3,opt,name=supplier,proto3" json:"supplier"`
	//疫苗名称
	VaccineName string `protobuf:"bytes,4,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name"`
	//预防疾病
	VaccineDesc string `protobuf:"bytes,5,opt,name=vaccine_desc,json=vaccineDesc,proto3" json:"vaccine_desc"`
	//疫苗状态
	IsEnabled int32 `protobuf:"varint,6,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled"`
	//分页、页码
	PageIndex int32 `protobuf:"varint,7,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//分页、每页个数
	PageSize             int32    `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateVaccineReq) Reset()         { *m = CreateVaccineReq{} }
func (m *CreateVaccineReq) String() string { return proto.CompactTextString(m) }
func (*CreateVaccineReq) ProtoMessage()    {}
func (*CreateVaccineReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{3}
}

func (m *CreateVaccineReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateVaccineReq.Unmarshal(m, b)
}
func (m *CreateVaccineReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateVaccineReq.Marshal(b, m, deterministic)
}
func (m *CreateVaccineReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateVaccineReq.Merge(m, src)
}
func (m *CreateVaccineReq) XXX_Size() int {
	return xxx_messageInfo_CreateVaccineReq.Size(m)
}
func (m *CreateVaccineReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateVaccineReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateVaccineReq proto.InternalMessageInfo

func (m *CreateVaccineReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CreateVaccineReq) GetPetType() int32 {
	if m != nil {
		return m.PetType
	}
	return 0
}

func (m *CreateVaccineReq) GetSupplier() string {
	if m != nil {
		return m.Supplier
	}
	return ""
}

func (m *CreateVaccineReq) GetVaccineName() string {
	if m != nil {
		return m.VaccineName
	}
	return ""
}

func (m *CreateVaccineReq) GetVaccineDesc() string {
	if m != nil {
		return m.VaccineDesc
	}
	return ""
}

func (m *CreateVaccineReq) GetIsEnabled() int32 {
	if m != nil {
		return m.IsEnabled
	}
	return 0
}

func (m *CreateVaccineReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *CreateVaccineReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetBaseMarchentReq struct {
	//商户id
	Marchentid int32 `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	// 页索引(公用属性，根据业务需求可以不传)
	PageIndex int32 `protobuf:"varint,2,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小(公用属性，根据业务需求可以不传)
	PageSize             int32    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBaseMarchentReq) Reset()         { *m = GetBaseMarchentReq{} }
func (m *GetBaseMarchentReq) String() string { return proto.CompactTextString(m) }
func (*GetBaseMarchentReq) ProtoMessage()    {}
func (*GetBaseMarchentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{4}
}

func (m *GetBaseMarchentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBaseMarchentReq.Unmarshal(m, b)
}
func (m *GetBaseMarchentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBaseMarchentReq.Marshal(b, m, deterministic)
}
func (m *GetBaseMarchentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBaseMarchentReq.Merge(m, src)
}
func (m *GetBaseMarchentReq) XXX_Size() int {
	return xxx_messageInfo_GetBaseMarchentReq.Size(m)
}
func (m *GetBaseMarchentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBaseMarchentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBaseMarchentReq proto.InternalMessageInfo

func (m *GetBaseMarchentReq) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *GetBaseMarchentReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetBaseMarchentReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetMarchentListReq struct {
	//省id
	ProvinceId int32 `protobuf:"varint,1,opt,name=province_id,json=provinceId,proto3" json:"province_id"`
	//城市id
	CityId int32 `protobuf:"varint,2,opt,name=city_id,json=cityId,proto3" json:"city_id"`
	//合作状态
	IsEnabled int32 `protobuf:"varint,3,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled"`
	//经营品类
	PetType int32 `protobuf:"varint,4,opt,name=pet_type,json=petType,proto3" json:"pet_type"`
	//店铺名称
	ShopName string `protobuf:"bytes,5,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	//分页信息
	PageIndex int32 `protobuf:"varint,6,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//分页总数
	PageSize int32 `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//区域id
	AreaId               int32    `protobuf:"varint,8,opt,name=area_id,json=areaId,proto3" json:"area_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMarchentListReq) Reset()         { *m = GetMarchentListReq{} }
func (m *GetMarchentListReq) String() string { return proto.CompactTextString(m) }
func (*GetMarchentListReq) ProtoMessage()    {}
func (*GetMarchentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{5}
}

func (m *GetMarchentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMarchentListReq.Unmarshal(m, b)
}
func (m *GetMarchentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMarchentListReq.Marshal(b, m, deterministic)
}
func (m *GetMarchentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMarchentListReq.Merge(m, src)
}
func (m *GetMarchentListReq) XXX_Size() int {
	return xxx_messageInfo_GetMarchentListReq.Size(m)
}
func (m *GetMarchentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMarchentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMarchentListReq proto.InternalMessageInfo

func (m *GetMarchentListReq) GetProvinceId() int32 {
	if m != nil {
		return m.ProvinceId
	}
	return 0
}

func (m *GetMarchentListReq) GetCityId() int32 {
	if m != nil {
		return m.CityId
	}
	return 0
}

func (m *GetMarchentListReq) GetIsEnabled() int32 {
	if m != nil {
		return m.IsEnabled
	}
	return 0
}

func (m *GetMarchentListReq) GetPetType() int32 {
	if m != nil {
		return m.PetType
	}
	return 0
}

func (m *GetMarchentListReq) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *GetMarchentListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetMarchentListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetMarchentListReq) GetAreaId() int32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

type CreateMarchentReq struct {
	//商户id
	Marchentid int32 `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	//商户名称(商户账户|主账户)
	MerchantName string `protobuf:"bytes,2,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name"`
	//商户名称
	Username string `protobuf:"bytes,3,opt,name=username,proto3" json:"username"`
	//商户手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	//店铺名称
	ShopName string `protobuf:"bytes,5,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	//店铺类型
	ShopType int32 `protobuf:"varint,6,opt,name=shop_type,json=shopType,proto3" json:"shop_type"`
	//业态类型
	BussinessFormatType int32 `protobuf:"varint,7,opt,name=bussiness_format_type,json=bussinessFormatType,proto3" json:"bussiness_format_type"`
	//省_id
	ProvinceId int32 `protobuf:"varint,8,opt,name=province_id,json=provinceId,proto3" json:"province_id"`
	//省_文本
	Province string `protobuf:"bytes,9,opt,name=province,proto3" json:"province"`
	//市_id
	CityId int32 `protobuf:"varint,10,opt,name=city_id,json=cityId,proto3" json:"city_id"`
	//市_文本
	City string `protobuf:"bytes,11,opt,name=city,proto3" json:"city"`
	//详细地址
	Address string `protobuf:"bytes,12,opt,name=address,proto3" json:"address"`
	//财务负责人
	Treasurer string `protobuf:"bytes,13,opt,name=treasurer,proto3" json:"treasurer"`
	//财务手机号
	TreasurerMobile string `protobuf:"bytes,14,opt,name=treasurer_mobile,json=treasurerMobile,proto3" json:"treasurer_mobile"`
	//商户负责人-总部运营负责人
	Leader string `protobuf:"bytes,15,opt,name=leader,proto3" json:"leader"`
	//区_id
	AreaId int32 `protobuf:"varint,16,opt,name=area_id,json=areaId,proto3" json:"area_id"`
	//区_文本
	AreaName             string   `protobuf:"bytes,17,opt,name=area_name,json=areaName,proto3" json:"area_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateMarchentReq) Reset()         { *m = CreateMarchentReq{} }
func (m *CreateMarchentReq) String() string { return proto.CompactTextString(m) }
func (*CreateMarchentReq) ProtoMessage()    {}
func (*CreateMarchentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{6}
}

func (m *CreateMarchentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateMarchentReq.Unmarshal(m, b)
}
func (m *CreateMarchentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateMarchentReq.Marshal(b, m, deterministic)
}
func (m *CreateMarchentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateMarchentReq.Merge(m, src)
}
func (m *CreateMarchentReq) XXX_Size() int {
	return xxx_messageInfo_CreateMarchentReq.Size(m)
}
func (m *CreateMarchentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateMarchentReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateMarchentReq proto.InternalMessageInfo

func (m *CreateMarchentReq) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *CreateMarchentReq) GetMerchantName() string {
	if m != nil {
		return m.MerchantName
	}
	return ""
}

func (m *CreateMarchentReq) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *CreateMarchentReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CreateMarchentReq) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *CreateMarchentReq) GetShopType() int32 {
	if m != nil {
		return m.ShopType
	}
	return 0
}

func (m *CreateMarchentReq) GetBussinessFormatType() int32 {
	if m != nil {
		return m.BussinessFormatType
	}
	return 0
}

func (m *CreateMarchentReq) GetProvinceId() int32 {
	if m != nil {
		return m.ProvinceId
	}
	return 0
}

func (m *CreateMarchentReq) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *CreateMarchentReq) GetCityId() int32 {
	if m != nil {
		return m.CityId
	}
	return 0
}

func (m *CreateMarchentReq) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *CreateMarchentReq) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *CreateMarchentReq) GetTreasurer() string {
	if m != nil {
		return m.Treasurer
	}
	return ""
}

func (m *CreateMarchentReq) GetTreasurerMobile() string {
	if m != nil {
		return m.TreasurerMobile
	}
	return ""
}

func (m *CreateMarchentReq) GetLeader() string {
	if m != nil {
		return m.Leader
	}
	return ""
}

func (m *CreateMarchentReq) GetAreaId() int32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *CreateMarchentReq) GetAreaName() string {
	if m != nil {
		return m.AreaName
	}
	return ""
}

// 导入商户数据请求
type MarchentImportReq struct {
	// 需要导入的商户列表
	Marchents            []*MarchentImportDto `protobuf:"bytes,1,rep,name=marchents,proto3" json:"marchents"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *MarchentImportReq) Reset()         { *m = MarchentImportReq{} }
func (m *MarchentImportReq) String() string { return proto.CompactTextString(m) }
func (*MarchentImportReq) ProtoMessage()    {}
func (*MarchentImportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{7}
}

func (m *MarchentImportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentImportReq.Unmarshal(m, b)
}
func (m *MarchentImportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentImportReq.Marshal(b, m, deterministic)
}
func (m *MarchentImportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentImportReq.Merge(m, src)
}
func (m *MarchentImportReq) XXX_Size() int {
	return xxx_messageInfo_MarchentImportReq.Size(m)
}
func (m *MarchentImportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentImportReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentImportReq proto.InternalMessageInfo

func (m *MarchentImportReq) GetMarchents() []*MarchentImportDto {
	if m != nil {
		return m.Marchents
	}
	return nil
}

type MarchentReq struct {
	//商户id
	Marchentid int32 `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	//商户名称(商户账户|主账户)
	MerchantName string `protobuf:"bytes,2,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name"`
	//商户手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	//商户名称
	Username string `protobuf:"bytes,4,opt,name=username,proto3" json:"username"`
	//身份证号
	IdNumber string `protobuf:"bytes,5,opt,name=id_number,json=idNumber,proto3" json:"id_number"`
	//身份证图片
	IdPic string `protobuf:"bytes,6,opt,name=id_pic,json=idPic,proto3" json:"id_pic"`
	//邮箱
	Email string `protobuf:"bytes,7,opt,name=email,proto3" json:"email"`
	//生日
	Birthday string `protobuf:"bytes,8,opt,name=birthday,proto3" json:"birthday"`
	//性别(1-男，2-女)
	Sex                  int32    `protobuf:"varint,9,opt,name=sex,proto3" json:"sex"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentReq) Reset()         { *m = MarchentReq{} }
func (m *MarchentReq) String() string { return proto.CompactTextString(m) }
func (*MarchentReq) ProtoMessage()    {}
func (*MarchentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{8}
}

func (m *MarchentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentReq.Unmarshal(m, b)
}
func (m *MarchentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentReq.Marshal(b, m, deterministic)
}
func (m *MarchentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentReq.Merge(m, src)
}
func (m *MarchentReq) XXX_Size() int {
	return xxx_messageInfo_MarchentReq.Size(m)
}
func (m *MarchentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentReq proto.InternalMessageInfo

func (m *MarchentReq) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *MarchentReq) GetMerchantName() string {
	if m != nil {
		return m.MerchantName
	}
	return ""
}

func (m *MarchentReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *MarchentReq) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *MarchentReq) GetIdNumber() string {
	if m != nil {
		return m.IdNumber
	}
	return ""
}

func (m *MarchentReq) GetIdPic() string {
	if m != nil {
		return m.IdPic
	}
	return ""
}

func (m *MarchentReq) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *MarchentReq) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *MarchentReq) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type MarchentStateReq struct {
	//商户id
	Marchentid int32 `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	//商户合作状态
	IsEnabled            int32    `protobuf:"varint,2,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentStateReq) Reset()         { *m = MarchentStateReq{} }
func (m *MarchentStateReq) String() string { return proto.CompactTextString(m) }
func (*MarchentStateReq) ProtoMessage()    {}
func (*MarchentStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{9}
}

func (m *MarchentStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentStateReq.Unmarshal(m, b)
}
func (m *MarchentStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentStateReq.Marshal(b, m, deterministic)
}
func (m *MarchentStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentStateReq.Merge(m, src)
}
func (m *MarchentStateReq) XXX_Size() int {
	return xxx_messageInfo_MarchentStateReq.Size(m)
}
func (m *MarchentStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentStateReq proto.InternalMessageInfo

func (m *MarchentStateReq) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *MarchentStateReq) GetIsEnabled() int32 {
	if m != nil {
		return m.IsEnabled
	}
	return 0
}

type MarchentQualifyReq struct {
	//商户id
	Marchentid int32 `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	//企业名称
	QualifyName string `protobuf:"bytes,2,opt,name=qualify_name,json=qualifyName,proto3" json:"qualify_name"`
	//企业营业执照
	LicensePic string `protobuf:"bytes,3,opt,name=license_pic,json=licensePic,proto3" json:"license_pic"`
	//社会统一信用代码
	CreditCode string `protobuf:"bytes,4,opt,name=credit_code,json=creditCode,proto3" json:"credit_code"`
	//企业法人代表
	QualifyOwner string `protobuf:"bytes,5,opt,name=qualify_owner,json=qualifyOwner,proto3" json:"qualify_owner"`
	//企业成立时间
	QualifyEstablish string `protobuf:"bytes,6,opt,name=qualify_establish,json=qualifyEstablish,proto3" json:"qualify_establish"`
	//特殊许可证件图
	SpecialPic string `protobuf:"bytes,7,opt,name=special_pic,json=specialPic,proto3" json:"special_pic"`
	//防疫许可证件图
	AntiepidemicPic string `protobuf:"bytes,8,opt,name=antiepidemic_pic,json=antiepidemicPic,proto3" json:"antiepidemic_pic"`
	//犬协会认证
	DogAssociation string `protobuf:"bytes,9,opt,name=dog_association,json=dogAssociation,proto3" json:"dog_association"`
	//猫协会认证
	CatAssociation string `protobuf:"bytes,10,opt,name=cat_association,json=catAssociation,proto3" json:"cat_association"`
	//认证时间
	AssociationDate      string   `protobuf:"bytes,11,opt,name=association_date,json=associationDate,proto3" json:"association_date"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentQualifyReq) Reset()         { *m = MarchentQualifyReq{} }
func (m *MarchentQualifyReq) String() string { return proto.CompactTextString(m) }
func (*MarchentQualifyReq) ProtoMessage()    {}
func (*MarchentQualifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{10}
}

func (m *MarchentQualifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentQualifyReq.Unmarshal(m, b)
}
func (m *MarchentQualifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentQualifyReq.Marshal(b, m, deterministic)
}
func (m *MarchentQualifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentQualifyReq.Merge(m, src)
}
func (m *MarchentQualifyReq) XXX_Size() int {
	return xxx_messageInfo_MarchentQualifyReq.Size(m)
}
func (m *MarchentQualifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentQualifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentQualifyReq proto.InternalMessageInfo

func (m *MarchentQualifyReq) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *MarchentQualifyReq) GetQualifyName() string {
	if m != nil {
		return m.QualifyName
	}
	return ""
}

func (m *MarchentQualifyReq) GetLicensePic() string {
	if m != nil {
		return m.LicensePic
	}
	return ""
}

func (m *MarchentQualifyReq) GetCreditCode() string {
	if m != nil {
		return m.CreditCode
	}
	return ""
}

func (m *MarchentQualifyReq) GetQualifyOwner() string {
	if m != nil {
		return m.QualifyOwner
	}
	return ""
}

func (m *MarchentQualifyReq) GetQualifyEstablish() string {
	if m != nil {
		return m.QualifyEstablish
	}
	return ""
}

func (m *MarchentQualifyReq) GetSpecialPic() string {
	if m != nil {
		return m.SpecialPic
	}
	return ""
}

func (m *MarchentQualifyReq) GetAntiepidemicPic() string {
	if m != nil {
		return m.AntiepidemicPic
	}
	return ""
}

func (m *MarchentQualifyReq) GetDogAssociation() string {
	if m != nil {
		return m.DogAssociation
	}
	return ""
}

func (m *MarchentQualifyReq) GetCatAssociation() string {
	if m != nil {
		return m.CatAssociation
	}
	return ""
}

func (m *MarchentQualifyReq) GetAssociationDate() string {
	if m != nil {
		return m.AssociationDate
	}
	return ""
}

type MarchentManagementReq struct {
	//商户id
	Marchentid int32 `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	//店铺名称
	ShopName string `protobuf:"bytes,2,opt,name=shop_name,json=shopName,proto3" json:"shop_name"`
	//店铺类型
	ShopType int32 `protobuf:"varint,3,opt,name=shop_type,json=shopType,proto3" json:"shop_type"`
	//业态类型
	BussinessFormatType int32 `protobuf:"varint,4,opt,name=bussiness_format_type,json=bussinessFormatType,proto3" json:"bussiness_format_type"`
	//业务种类
	BussinessType string `protobuf:"bytes,5,opt,name=bussiness_type,json=bussinessType,proto3" json:"bussiness_type"`
	//活体品类
	PetType int32 `protobuf:"varint,6,opt,name=pet_type,json=petType,proto3" json:"pet_type"`
	//活体品种
	PetVariety string `protobuf:"bytes,7,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//繁衍规模(1-小型规模(10以内)，2-中型规模(介于10和20之间)，3-大型规模(大于20))
	ScaleReproduction int32 `protobuf:"varint,8,opt,name=scale_reproduction,json=scaleReproduction,proto3" json:"scale_reproduction"`
	//门头照片
	ShopPic string `protobuf:"bytes,9,opt,name=shop_pic,json=shopPic,proto3" json:"shop_pic"`
	//省_id
	ProvinceId int32 `protobuf:"varint,10,opt,name=province_id,json=provinceId,proto3" json:"province_id"`
	//省_文本
	Province string `protobuf:"bytes,11,opt,name=province,proto3" json:"province"`
	//市_id
	CityId int32 `protobuf:"varint,12,opt,name=city_id,json=cityId,proto3" json:"city_id"`
	//市_文本
	City string `protobuf:"bytes,13,opt,name=city,proto3" json:"city"`
	//详细地址
	Address string `protobuf:"bytes,14,opt,name=address,proto3" json:"address"`
	//客服电话
	Telephone string `protobuf:"bytes,15,opt,name=telephone,proto3" json:"telephone"`
	//是否连锁(0-否，1-是)
	Islinked int32 `protobuf:"varint,16,opt,name=islinked,proto3" json:"islinked"`
	//logo
	ShopLogo string `protobuf:"bytes,17,opt,name=shop_logo,json=shopLogo,proto3" json:"shop_logo"`
	//店铺环境图片
	EnviromentPic string `protobuf:"bytes,18,opt,name=enviroment_pic,json=enviromentPic,proto3" json:"enviroment_pic"`
	//店铺介绍
	ShopDesc string `protobuf:"bytes,19,opt,name=shop_desc,json=shopDesc,proto3" json:"shop_desc"`
	//营业面积
	ShopMeasure string `protobuf:"bytes,20,opt,name=shop_measure,json=shopMeasure,proto3" json:"shop_measure"`
	//雇员数量
	EmployeeNum int32 `protobuf:"varint,21,opt,name=employee_num,json=employeeNum,proto3" json:"employee_num"`
	//区_id
	AreaId int32 `protobuf:"varint,22,opt,name=area_id,json=areaId,proto3" json:"area_id"`
	//区_文本
	AreaName             string   `protobuf:"bytes,23,opt,name=area_name,json=areaName,proto3" json:"area_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentManagementReq) Reset()         { *m = MarchentManagementReq{} }
func (m *MarchentManagementReq) String() string { return proto.CompactTextString(m) }
func (*MarchentManagementReq) ProtoMessage()    {}
func (*MarchentManagementReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{11}
}

func (m *MarchentManagementReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentManagementReq.Unmarshal(m, b)
}
func (m *MarchentManagementReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentManagementReq.Marshal(b, m, deterministic)
}
func (m *MarchentManagementReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentManagementReq.Merge(m, src)
}
func (m *MarchentManagementReq) XXX_Size() int {
	return xxx_messageInfo_MarchentManagementReq.Size(m)
}
func (m *MarchentManagementReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentManagementReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentManagementReq proto.InternalMessageInfo

func (m *MarchentManagementReq) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *MarchentManagementReq) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *MarchentManagementReq) GetShopType() int32 {
	if m != nil {
		return m.ShopType
	}
	return 0
}

func (m *MarchentManagementReq) GetBussinessFormatType() int32 {
	if m != nil {
		return m.BussinessFormatType
	}
	return 0
}

func (m *MarchentManagementReq) GetBussinessType() string {
	if m != nil {
		return m.BussinessType
	}
	return ""
}

func (m *MarchentManagementReq) GetPetType() int32 {
	if m != nil {
		return m.PetType
	}
	return 0
}

func (m *MarchentManagementReq) GetPetVariety() string {
	if m != nil {
		return m.PetVariety
	}
	return ""
}

func (m *MarchentManagementReq) GetScaleReproduction() int32 {
	if m != nil {
		return m.ScaleReproduction
	}
	return 0
}

func (m *MarchentManagementReq) GetShopPic() string {
	if m != nil {
		return m.ShopPic
	}
	return ""
}

func (m *MarchentManagementReq) GetProvinceId() int32 {
	if m != nil {
		return m.ProvinceId
	}
	return 0
}

func (m *MarchentManagementReq) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *MarchentManagementReq) GetCityId() int32 {
	if m != nil {
		return m.CityId
	}
	return 0
}

func (m *MarchentManagementReq) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *MarchentManagementReq) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *MarchentManagementReq) GetTelephone() string {
	if m != nil {
		return m.Telephone
	}
	return ""
}

func (m *MarchentManagementReq) GetIslinked() int32 {
	if m != nil {
		return m.Islinked
	}
	return 0
}

func (m *MarchentManagementReq) GetShopLogo() string {
	if m != nil {
		return m.ShopLogo
	}
	return ""
}

func (m *MarchentManagementReq) GetEnviromentPic() string {
	if m != nil {
		return m.EnviromentPic
	}
	return ""
}

func (m *MarchentManagementReq) GetShopDesc() string {
	if m != nil {
		return m.ShopDesc
	}
	return ""
}

func (m *MarchentManagementReq) GetShopMeasure() string {
	if m != nil {
		return m.ShopMeasure
	}
	return ""
}

func (m *MarchentManagementReq) GetEmployeeNum() int32 {
	if m != nil {
		return m.EmployeeNum
	}
	return 0
}

func (m *MarchentManagementReq) GetAreaId() int32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *MarchentManagementReq) GetAreaName() string {
	if m != nil {
		return m.AreaName
	}
	return ""
}

type MarchentOperateReq struct {
	//商户id
	Marchentid int32 `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	//抖音号
	DouyinNum string `protobuf:"bytes,2,opt,name=douyin_num,json=douyinNum,proto3" json:"douyin_num"`
	//快手号
	KuaishouNum string `protobuf:"bytes,3,opt,name=kuaishou_num,json=kuaishouNum,proto3" json:"kuaishou_num"`
	//公众号
	GongzhongNum string `protobuf:"bytes,4,opt,name=gongzhong_num,json=gongzhongNum,proto3" json:"gongzhong_num"`
	//视频号
	ShipingNum string `protobuf:"bytes,5,opt,name=shiping_num,json=shipingNum,proto3" json:"shiping_num"`
	//微信号
	WechatNum string `protobuf:"bytes,6,opt,name=wechat_num,json=wechatNum,proto3" json:"wechat_num"`
	//京东
	JdNum string `protobuf:"bytes,7,opt,name=jd_num,json=jdNum,proto3" json:"jd_num"`
	//淘宝
	TaobaoNum string `protobuf:"bytes,8,opt,name=taobao_num,json=taobaoNum,proto3" json:"taobao_num"`
	//其他
	OtherNum             string   `protobuf:"bytes,9,opt,name=other_num,json=otherNum,proto3" json:"other_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentOperateReq) Reset()         { *m = MarchentOperateReq{} }
func (m *MarchentOperateReq) String() string { return proto.CompactTextString(m) }
func (*MarchentOperateReq) ProtoMessage()    {}
func (*MarchentOperateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{12}
}

func (m *MarchentOperateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentOperateReq.Unmarshal(m, b)
}
func (m *MarchentOperateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentOperateReq.Marshal(b, m, deterministic)
}
func (m *MarchentOperateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentOperateReq.Merge(m, src)
}
func (m *MarchentOperateReq) XXX_Size() int {
	return xxx_messageInfo_MarchentOperateReq.Size(m)
}
func (m *MarchentOperateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentOperateReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentOperateReq proto.InternalMessageInfo

func (m *MarchentOperateReq) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *MarchentOperateReq) GetDouyinNum() string {
	if m != nil {
		return m.DouyinNum
	}
	return ""
}

func (m *MarchentOperateReq) GetKuaishouNum() string {
	if m != nil {
		return m.KuaishouNum
	}
	return ""
}

func (m *MarchentOperateReq) GetGongzhongNum() string {
	if m != nil {
		return m.GongzhongNum
	}
	return ""
}

func (m *MarchentOperateReq) GetShipingNum() string {
	if m != nil {
		return m.ShipingNum
	}
	return ""
}

func (m *MarchentOperateReq) GetWechatNum() string {
	if m != nil {
		return m.WechatNum
	}
	return ""
}

func (m *MarchentOperateReq) GetJdNum() string {
	if m != nil {
		return m.JdNum
	}
	return ""
}

func (m *MarchentOperateReq) GetTaobaoNum() string {
	if m != nil {
		return m.TaobaoNum
	}
	return ""
}

func (m *MarchentOperateReq) GetOtherNum() string {
	if m != nil {
		return m.OtherNum
	}
	return ""
}

type MarchentFinanceReq struct {
	//商户id
	Marchentid int32 `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	//开户行
	BankDeposit string `protobuf:"bytes,2,opt,name=bank_deposit,json=bankDeposit,proto3" json:"bank_deposit"`
	//户名
	DepositName string `protobuf:"bytes,3,opt,name=deposit_name,json=depositName,proto3" json:"deposit_name"`
	//账户
	DepositNum string `protobuf:"bytes,4,opt,name=deposit_num,json=depositNum,proto3" json:"deposit_num"`
	//私人开户行
	SelfBankDeposit string `protobuf:"bytes,5,opt,name=self_bank_deposit,json=selfBankDeposit,proto3" json:"self_bank_deposit"`
	//私人户名
	SelfDepositName string `protobuf:"bytes,6,opt,name=self_deposit_name,json=selfDepositName,proto3" json:"self_deposit_name"`
	//私人账户
	SelfDepositNum       string   `protobuf:"bytes,7,opt,name=self_deposit_num,json=selfDepositNum,proto3" json:"self_deposit_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentFinanceReq) Reset()         { *m = MarchentFinanceReq{} }
func (m *MarchentFinanceReq) String() string { return proto.CompactTextString(m) }
func (*MarchentFinanceReq) ProtoMessage()    {}
func (*MarchentFinanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{13}
}

func (m *MarchentFinanceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentFinanceReq.Unmarshal(m, b)
}
func (m *MarchentFinanceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentFinanceReq.Marshal(b, m, deterministic)
}
func (m *MarchentFinanceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentFinanceReq.Merge(m, src)
}
func (m *MarchentFinanceReq) XXX_Size() int {
	return xxx_messageInfo_MarchentFinanceReq.Size(m)
}
func (m *MarchentFinanceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentFinanceReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentFinanceReq proto.InternalMessageInfo

func (m *MarchentFinanceReq) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *MarchentFinanceReq) GetBankDeposit() string {
	if m != nil {
		return m.BankDeposit
	}
	return ""
}

func (m *MarchentFinanceReq) GetDepositName() string {
	if m != nil {
		return m.DepositName
	}
	return ""
}

func (m *MarchentFinanceReq) GetDepositNum() string {
	if m != nil {
		return m.DepositNum
	}
	return ""
}

func (m *MarchentFinanceReq) GetSelfBankDeposit() string {
	if m != nil {
		return m.SelfBankDeposit
	}
	return ""
}

func (m *MarchentFinanceReq) GetSelfDepositName() string {
	if m != nil {
		return m.SelfDepositName
	}
	return ""
}

func (m *MarchentFinanceReq) GetSelfDepositNum() string {
	if m != nil {
		return m.SelfDepositNum
	}
	return ""
}

type MarchentMemberReq struct {
	//商户id
	Marchentid           int32             `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	Member               []*MarchentMember `protobuf:"bytes,2,rep,name=Member,proto3" json:"Member"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MarchentMemberReq) Reset()         { *m = MarchentMemberReq{} }
func (m *MarchentMemberReq) String() string { return proto.CompactTextString(m) }
func (*MarchentMemberReq) ProtoMessage()    {}
func (*MarchentMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{14}
}

func (m *MarchentMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentMemberReq.Unmarshal(m, b)
}
func (m *MarchentMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentMemberReq.Marshal(b, m, deterministic)
}
func (m *MarchentMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentMemberReq.Merge(m, src)
}
func (m *MarchentMemberReq) XXX_Size() int {
	return xxx_messageInfo_MarchentMemberReq.Size(m)
}
func (m *MarchentMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentMemberReq proto.InternalMessageInfo

func (m *MarchentMemberReq) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *MarchentMemberReq) GetMember() []*MarchentMember {
	if m != nil {
		return m.Member
	}
	return nil
}

type MarchentMember struct {
	//商户id
	Marchentid int32 `protobuf:"varint,1,opt,name=marchentid,proto3" json:"marchentid"`
	//商户名称
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username"`
	//商户手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	//是否是老板账号
	Isboss               int32    `protobuf:"varint,4,opt,name=isboss,proto3" json:"isboss"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentMember) Reset()         { *m = MarchentMember{} }
func (m *MarchentMember) String() string { return proto.CompactTextString(m) }
func (*MarchentMember) ProtoMessage()    {}
func (*MarchentMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{15}
}

func (m *MarchentMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentMember.Unmarshal(m, b)
}
func (m *MarchentMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentMember.Marshal(b, m, deterministic)
}
func (m *MarchentMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentMember.Merge(m, src)
}
func (m *MarchentMember) XXX_Size() int {
	return xxx_messageInfo_MarchentMember.Size(m)
}
func (m *MarchentMember) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentMember.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentMember proto.InternalMessageInfo

func (m *MarchentMember) GetMarchentid() int32 {
	if m != nil {
		return m.Marchentid
	}
	return 0
}

func (m *MarchentMember) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *MarchentMember) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *MarchentMember) GetIsboss() int32 {
	if m != nil {
		return m.Isboss
	}
	return 0
}

// 导入商户信息Dto
type MarchentImportDto struct {
	// 行号
	RowIndex int32 `protobuf:"varint,3,opt,name=rowIndex,proto3" json:"rowIndex"`
	//*商家主账户名
	MerchantName string `protobuf:"bytes,1,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name"`
	//*业务负责人-姓名
	MemberName string `protobuf:"bytes,20,opt,name=member_name,json=memberName,proto3" json:"member_name"`
	//*业务负责人-手机号
	MemberPhone string `protobuf:"bytes,21,opt,name=member_phone,json=memberPhone,proto3" json:"member_phone"`
	//业务负责人-邮箱
	MemeberEmail string `protobuf:"bytes,22,opt,name=memeber_email,json=memeberEmail,proto3" json:"memeber_email"`
	//业务负责人-出生日期
	MemberBirthday string `protobuf:"bytes,23,opt,name=member_birthday,json=memberBirthday,proto3" json:"member_birthday"`
	//业务负责人-性别
	MemberSex int32 `protobuf:"varint,24,opt,name=member_sex,json=memberSex,proto3" json:"member_sex"`
	// 工商信息-企业名称
	QualifyName string `protobuf:"bytes,30,opt,name=qualify_name,json=qualifyName,proto3" json:"qualify_name"`
	// 社会统一信用代码
	QualifyCreditCode string `protobuf:"bytes,31,opt,name=qualify_creditCode,json=qualifyCreditCode,proto3" json:"qualify_creditCode"`
	// 法人代表
	QualifyOwner string `protobuf:"bytes,32,opt,name=qualify_owner,json=qualifyOwner,proto3" json:"qualify_owner"`
	// 成立时间
	QualifyEstablish string `protobuf:"bytes,33,opt,name=qualify_establish,json=qualifyEstablish,proto3" json:"qualify_establish"`
	//*店铺名称
	ManagementName string `protobuf:"bytes,40,opt,name=management_name,json=managementName,proto3" json:"management_name"`
	// *城市
	ManagementCity string `protobuf:"bytes,41,opt,name=management_city,json=managementCity,proto3" json:"management_city"`
	// *区域
	ManagementArea string `protobuf:"bytes,78,opt,name=management_area,json=managementArea,proto3" json:"management_area"`
	// *地址
	ManagementAddress string `protobuf:"bytes,42,opt,name=management_address,json=managementAddress,proto3" json:"management_address"`
	// 店铺类型
	ManagementType int32 `protobuf:"varint,43,opt,name=management_type,json=managementType,proto3" json:"management_type"`
	// *业态类型
	ManagementBussinessFormatType int32 `protobuf:"varint,44,opt,name=management_bussinessFormatType,json=managementBussinessFormatType,proto3" json:"management_bussinessFormatType"`
	// 业务种类
	ManagementBussinessType string `protobuf:"bytes,45,opt,name=management_bussinessType,json=managementBussinessType,proto3" json:"management_bussinessType"`
	// *活体品类
	ManagementPetType int32 `protobuf:"varint,46,opt,name=management_petType,json=managementPetType,proto3" json:"management_petType"`
	// *活体品种
	ManagementPetVarietyType string `protobuf:"bytes,47,opt,name=management_petVarietyType,json=managementPetVarietyType,proto3" json:"management_petVarietyType"`
	// 客服电话
	ManagementTelephone string `protobuf:"bytes,48,opt,name=management_telephone,json=managementTelephone,proto3" json:"management_telephone"`
	// 是否连锁
	ManagementIsLinked bool `protobuf:"varint,49,opt,name=management_isLinked,json=managementIsLinked,proto3" json:"management_isLinked"`
	// 店铺简介
	ManagementDesc string `protobuf:"bytes,50,opt,name=management_desc,json=managementDesc,proto3" json:"management_desc"`
	// 营业面积
	ManagementMeasure string `protobuf:"bytes,51,opt,name=management_measure,json=managementMeasure,proto3" json:"management_measure"`
	// 雇员数量
	ManagementEmployeeNum int32 `protobuf:"varint,52,opt,name=management_employeeNum,json=managementEmployeeNum,proto3" json:"management_employeeNum"`
	// 抖音号
	OperateDouyin string `protobuf:"bytes,60,opt,name=operate_douyin,json=operateDouyin,proto3" json:"operate_douyin"`
	//快手号
	OperateKuaishou string `protobuf:"bytes,61,opt,name=operate_kuaishou,json=operateKuaishou,proto3" json:"operate_kuaishou"`
	//公众号
	OperateGongzhong string `protobuf:"bytes,62,opt,name=operate_gongzhong,json=operateGongzhong,proto3" json:"operate_gongzhong"`
	// 视频号
	OperateShiping string `protobuf:"bytes,63,opt,name=operate_shiping,json=operateShiping,proto3" json:"operate_shiping"`
	// 微信号
	OperateWechat string `protobuf:"bytes,64,opt,name=operate_wechat,json=operateWechat,proto3" json:"operate_wechat"`
	// 京东
	OperateJd string `protobuf:"bytes,65,opt,name=operate_jd,json=operateJd,proto3" json:"operate_jd"`
	// 淘宝
	OperateTaobao string `protobuf:"bytes,66,opt,name=operate_taobao,json=operateTaobao,proto3" json:"operate_taobao"`
	// 企业账户-开户行
	FinanceDeposit string `protobuf:"bytes,70,opt,name=finance_deposit,json=financeDeposit,proto3" json:"finance_deposit"`
	// 企业账户-户名
	FinanceName string `protobuf:"bytes,71,opt,name=finance_name,json=financeName,proto3" json:"finance_name"`
	// 企业账户-账号
	FinanceNum string `protobuf:"bytes,72,opt,name=finance_num,json=financeNum,proto3" json:"finance_num"`
	// 私人账户-开户行
	FinanceSelfDeposit string `protobuf:"bytes,73,opt,name=finance_self_deposit,json=financeSelfDeposit,proto3" json:"finance_self_deposit"`
	// 私人账户-户名
	FinanceSelfName string `protobuf:"bytes,74,opt,name=finance_self_name,json=financeSelfName,proto3" json:"finance_self_name"`
	//私人账户-账号
	FinanceSelfNum string `protobuf:"bytes,75,opt,name=finance_self_num,json=financeSelfNum,proto3" json:"finance_self_num"`
	// 财务负责人-姓名
	FinanceUserName string `protobuf:"bytes,76,opt,name=finance_user_name,json=financeUserName,proto3" json:"finance_user_name"`
	// 财务负责人-手机号
	FinanceUserMobile string `protobuf:"bytes,77,opt,name=finance_user_mobile,json=financeUserMobile,proto3" json:"finance_user_mobile"`
	//*总部运营负责人
	MerchantLeader       string   `protobuf:"bytes,2,opt,name=merchant_leader,json=merchantLeader,proto3" json:"merchant_leader"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentImportDto) Reset()         { *m = MarchentImportDto{} }
func (m *MarchentImportDto) String() string { return proto.CompactTextString(m) }
func (*MarchentImportDto) ProtoMessage()    {}
func (*MarchentImportDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{16}
}

func (m *MarchentImportDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentImportDto.Unmarshal(m, b)
}
func (m *MarchentImportDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentImportDto.Marshal(b, m, deterministic)
}
func (m *MarchentImportDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentImportDto.Merge(m, src)
}
func (m *MarchentImportDto) XXX_Size() int {
	return xxx_messageInfo_MarchentImportDto.Size(m)
}
func (m *MarchentImportDto) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentImportDto.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentImportDto proto.InternalMessageInfo

func (m *MarchentImportDto) GetRowIndex() int32 {
	if m != nil {
		return m.RowIndex
	}
	return 0
}

func (m *MarchentImportDto) GetMerchantName() string {
	if m != nil {
		return m.MerchantName
	}
	return ""
}

func (m *MarchentImportDto) GetMemberName() string {
	if m != nil {
		return m.MemberName
	}
	return ""
}

func (m *MarchentImportDto) GetMemberPhone() string {
	if m != nil {
		return m.MemberPhone
	}
	return ""
}

func (m *MarchentImportDto) GetMemeberEmail() string {
	if m != nil {
		return m.MemeberEmail
	}
	return ""
}

func (m *MarchentImportDto) GetMemberBirthday() string {
	if m != nil {
		return m.MemberBirthday
	}
	return ""
}

func (m *MarchentImportDto) GetMemberSex() int32 {
	if m != nil {
		return m.MemberSex
	}
	return 0
}

func (m *MarchentImportDto) GetQualifyName() string {
	if m != nil {
		return m.QualifyName
	}
	return ""
}

func (m *MarchentImportDto) GetQualifyCreditCode() string {
	if m != nil {
		return m.QualifyCreditCode
	}
	return ""
}

func (m *MarchentImportDto) GetQualifyOwner() string {
	if m != nil {
		return m.QualifyOwner
	}
	return ""
}

func (m *MarchentImportDto) GetQualifyEstablish() string {
	if m != nil {
		return m.QualifyEstablish
	}
	return ""
}

func (m *MarchentImportDto) GetManagementName() string {
	if m != nil {
		return m.ManagementName
	}
	return ""
}

func (m *MarchentImportDto) GetManagementCity() string {
	if m != nil {
		return m.ManagementCity
	}
	return ""
}

func (m *MarchentImportDto) GetManagementArea() string {
	if m != nil {
		return m.ManagementArea
	}
	return ""
}

func (m *MarchentImportDto) GetManagementAddress() string {
	if m != nil {
		return m.ManagementAddress
	}
	return ""
}

func (m *MarchentImportDto) GetManagementType() int32 {
	if m != nil {
		return m.ManagementType
	}
	return 0
}

func (m *MarchentImportDto) GetManagementBussinessFormatType() int32 {
	if m != nil {
		return m.ManagementBussinessFormatType
	}
	return 0
}

func (m *MarchentImportDto) GetManagementBussinessType() string {
	if m != nil {
		return m.ManagementBussinessType
	}
	return ""
}

func (m *MarchentImportDto) GetManagementPetType() int32 {
	if m != nil {
		return m.ManagementPetType
	}
	return 0
}

func (m *MarchentImportDto) GetManagementPetVarietyType() string {
	if m != nil {
		return m.ManagementPetVarietyType
	}
	return ""
}

func (m *MarchentImportDto) GetManagementTelephone() string {
	if m != nil {
		return m.ManagementTelephone
	}
	return ""
}

func (m *MarchentImportDto) GetManagementIsLinked() bool {
	if m != nil {
		return m.ManagementIsLinked
	}
	return false
}

func (m *MarchentImportDto) GetManagementDesc() string {
	if m != nil {
		return m.ManagementDesc
	}
	return ""
}

func (m *MarchentImportDto) GetManagementMeasure() string {
	if m != nil {
		return m.ManagementMeasure
	}
	return ""
}

func (m *MarchentImportDto) GetManagementEmployeeNum() int32 {
	if m != nil {
		return m.ManagementEmployeeNum
	}
	return 0
}

func (m *MarchentImportDto) GetOperateDouyin() string {
	if m != nil {
		return m.OperateDouyin
	}
	return ""
}

func (m *MarchentImportDto) GetOperateKuaishou() string {
	if m != nil {
		return m.OperateKuaishou
	}
	return ""
}

func (m *MarchentImportDto) GetOperateGongzhong() string {
	if m != nil {
		return m.OperateGongzhong
	}
	return ""
}

func (m *MarchentImportDto) GetOperateShiping() string {
	if m != nil {
		return m.OperateShiping
	}
	return ""
}

func (m *MarchentImportDto) GetOperateWechat() string {
	if m != nil {
		return m.OperateWechat
	}
	return ""
}

func (m *MarchentImportDto) GetOperateJd() string {
	if m != nil {
		return m.OperateJd
	}
	return ""
}

func (m *MarchentImportDto) GetOperateTaobao() string {
	if m != nil {
		return m.OperateTaobao
	}
	return ""
}

func (m *MarchentImportDto) GetFinanceDeposit() string {
	if m != nil {
		return m.FinanceDeposit
	}
	return ""
}

func (m *MarchentImportDto) GetFinanceName() string {
	if m != nil {
		return m.FinanceName
	}
	return ""
}

func (m *MarchentImportDto) GetFinanceNum() string {
	if m != nil {
		return m.FinanceNum
	}
	return ""
}

func (m *MarchentImportDto) GetFinanceSelfDeposit() string {
	if m != nil {
		return m.FinanceSelfDeposit
	}
	return ""
}

func (m *MarchentImportDto) GetFinanceSelfName() string {
	if m != nil {
		return m.FinanceSelfName
	}
	return ""
}

func (m *MarchentImportDto) GetFinanceSelfNum() string {
	if m != nil {
		return m.FinanceSelfNum
	}
	return ""
}

func (m *MarchentImportDto) GetFinanceUserName() string {
	if m != nil {
		return m.FinanceUserName
	}
	return ""
}

func (m *MarchentImportDto) GetFinanceUserMobile() string {
	if m != nil {
		return m.FinanceUserMobile
	}
	return ""
}

func (m *MarchentImportDto) GetMerchantLeader() string {
	if m != nil {
		return m.MerchantLeader
	}
	return ""
}

// 商户详情->商户宠物
type MarchentPetListDto struct {
	// 宠物id
	PetId int32 `protobuf:"varint,14,opt,name=petId,proto3" json:"petId"`
	// 窝代码
	NestCode string `protobuf:"bytes,1,opt,name=nestCode,proto3" json:"nestCode"`
	//窝名称
	NestName string `protobuf:"bytes,2,opt,name=nestName,proto3" json:"nestName"`
	// 品种
	PetBreed string `protobuf:"bytes,3,opt,name=petBreed,proto3" json:"petBreed"`
	// 性别
	PetSex string `protobuf:"bytes,4,opt,name=petSex,proto3" json:"petSex"`
	// 宠物身份证编码
	PetCardId string `protobuf:"bytes,5,opt,name=petCardId,proto3" json:"petCardId"`
	// 宠物芯片号码
	PetCode string `protobuf:"bytes,6,opt,name=petCode,proto3" json:"petCode"`
	// 首免时间
	PlanRecordFirst string `protobuf:"bytes,7,opt,name=planRecordFirst,proto3" json:"planRecordFirst"`
	// 二免时间
	PlanRecordSecond string `protobuf:"bytes,8,opt,name=planRecordSecond,proto3" json:"planRecordSecond"`
	// 三免时间
	PlanRecordThird string `protobuf:"bytes,9,opt,name=planRecordThird,proto3" json:"planRecordThird"`
	// 首次驱虫时间
	DewormingFirst string `protobuf:"bytes,10,opt,name=dewormingFirst,proto3" json:"dewormingFirst"`
	// 二次驱虫时间
	DewormingSecond string `protobuf:"bytes,15,opt,name=dewormingSecond,proto3" json:"dewormingSecond"`
	// 三次驱虫时间
	DewormingThird string `protobuf:"bytes,16,opt,name=dewormingThird,proto3" json:"dewormingThird"`
	// 交付状态
	DeliveryState string `protobuf:"bytes,11,opt,name=deliveryState,proto3" json:"deliveryState"`
	// 交付时间
	DeliveryDate string `protobuf:"bytes,12,opt,name=deliveryDate,proto3" json:"deliveryDate"`
	// 客户手机号码
	DeliveryMobile       string   `protobuf:"bytes,13,opt,name=deliveryMobile,proto3" json:"deliveryMobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentPetListDto) Reset()         { *m = MarchentPetListDto{} }
func (m *MarchentPetListDto) String() string { return proto.CompactTextString(m) }
func (*MarchentPetListDto) ProtoMessage()    {}
func (*MarchentPetListDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{17}
}

func (m *MarchentPetListDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentPetListDto.Unmarshal(m, b)
}
func (m *MarchentPetListDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentPetListDto.Marshal(b, m, deterministic)
}
func (m *MarchentPetListDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentPetListDto.Merge(m, src)
}
func (m *MarchentPetListDto) XXX_Size() int {
	return xxx_messageInfo_MarchentPetListDto.Size(m)
}
func (m *MarchentPetListDto) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentPetListDto.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentPetListDto proto.InternalMessageInfo

func (m *MarchentPetListDto) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

func (m *MarchentPetListDto) GetNestCode() string {
	if m != nil {
		return m.NestCode
	}
	return ""
}

func (m *MarchentPetListDto) GetNestName() string {
	if m != nil {
		return m.NestName
	}
	return ""
}

func (m *MarchentPetListDto) GetPetBreed() string {
	if m != nil {
		return m.PetBreed
	}
	return ""
}

func (m *MarchentPetListDto) GetPetSex() string {
	if m != nil {
		return m.PetSex
	}
	return ""
}

func (m *MarchentPetListDto) GetPetCardId() string {
	if m != nil {
		return m.PetCardId
	}
	return ""
}

func (m *MarchentPetListDto) GetPetCode() string {
	if m != nil {
		return m.PetCode
	}
	return ""
}

func (m *MarchentPetListDto) GetPlanRecordFirst() string {
	if m != nil {
		return m.PlanRecordFirst
	}
	return ""
}

func (m *MarchentPetListDto) GetPlanRecordSecond() string {
	if m != nil {
		return m.PlanRecordSecond
	}
	return ""
}

func (m *MarchentPetListDto) GetPlanRecordThird() string {
	if m != nil {
		return m.PlanRecordThird
	}
	return ""
}

func (m *MarchentPetListDto) GetDewormingFirst() string {
	if m != nil {
		return m.DewormingFirst
	}
	return ""
}

func (m *MarchentPetListDto) GetDewormingSecond() string {
	if m != nil {
		return m.DewormingSecond
	}
	return ""
}

func (m *MarchentPetListDto) GetDewormingThird() string {
	if m != nil {
		return m.DewormingThird
	}
	return ""
}

func (m *MarchentPetListDto) GetDeliveryState() string {
	if m != nil {
		return m.DeliveryState
	}
	return ""
}

func (m *MarchentPetListDto) GetDeliveryDate() string {
	if m != nil {
		return m.DeliveryDate
	}
	return ""
}

func (m *MarchentPetListDto) GetDeliveryMobile() string {
	if m != nil {
		return m.DeliveryMobile
	}
	return ""
}

// 商户详情->商户宠物交付记录(客户列表)
type MarchentDeliveryRecordListDto struct {
	// 客户手机号码
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	// 交付日期
	DateDay string `protobuf:"bytes,2,opt,name=dateDay,proto3" json:"dateDay"`
	// 宠物身份证
	PetCardId string `protobuf:"bytes,3,opt,name=petCardId,proto3" json:"petCardId"`
	// 城市
	City                 string   `protobuf:"bytes,4,opt,name=city,proto3" json:"city"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentDeliveryRecordListDto) Reset()         { *m = MarchentDeliveryRecordListDto{} }
func (m *MarchentDeliveryRecordListDto) String() string { return proto.CompactTextString(m) }
func (*MarchentDeliveryRecordListDto) ProtoMessage()    {}
func (*MarchentDeliveryRecordListDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{18}
}

func (m *MarchentDeliveryRecordListDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentDeliveryRecordListDto.Unmarshal(m, b)
}
func (m *MarchentDeliveryRecordListDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentDeliveryRecordListDto.Marshal(b, m, deterministic)
}
func (m *MarchentDeliveryRecordListDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentDeliveryRecordListDto.Merge(m, src)
}
func (m *MarchentDeliveryRecordListDto) XXX_Size() int {
	return xxx_messageInfo_MarchentDeliveryRecordListDto.Size(m)
}
func (m *MarchentDeliveryRecordListDto) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentDeliveryRecordListDto.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentDeliveryRecordListDto proto.InternalMessageInfo

func (m *MarchentDeliveryRecordListDto) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *MarchentDeliveryRecordListDto) GetDateDay() string {
	if m != nil {
		return m.DateDay
	}
	return ""
}

func (m *MarchentDeliveryRecordListDto) GetPetCardId() string {
	if m != nil {
		return m.PetCardId
	}
	return ""
}

func (m *MarchentDeliveryRecordListDto) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

type GetMarchentMarketingOutcomeReq struct {
	//商户id
	MarchentId           int32    `protobuf:"varint,1,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMarchentMarketingOutcomeReq) Reset()         { *m = GetMarchentMarketingOutcomeReq{} }
func (m *GetMarchentMarketingOutcomeReq) String() string { return proto.CompactTextString(m) }
func (*GetMarchentMarketingOutcomeReq) ProtoMessage()    {}
func (*GetMarchentMarketingOutcomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{19}
}

func (m *GetMarchentMarketingOutcomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMarchentMarketingOutcomeReq.Unmarshal(m, b)
}
func (m *GetMarchentMarketingOutcomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMarchentMarketingOutcomeReq.Marshal(b, m, deterministic)
}
func (m *GetMarchentMarketingOutcomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMarchentMarketingOutcomeReq.Merge(m, src)
}
func (m *GetMarchentMarketingOutcomeReq) XXX_Size() int {
	return xxx_messageInfo_GetMarchentMarketingOutcomeReq.Size(m)
}
func (m *GetMarchentMarketingOutcomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMarchentMarketingOutcomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMarchentMarketingOutcomeReq proto.InternalMessageInfo

func (m *GetMarchentMarketingOutcomeReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

type GetMarchentMarketingOutcomeRes struct {
	//当前商家关联用户的总转诊次数
	AllNum int32 `protobuf:"varint,1,opt,name=all_num,json=allNum,proto3" json:"all_num"`
	//当前商家关联用户的累计消费金额
	AllAmount float64 `protobuf:"fixed64,2,opt,name=all_amount,json=allAmount,proto3" json:"all_amount"`
	//当前商家关联用户的累计消费金额
	PrevNum int32 `protobuf:"varint,3,opt,name=prev_num,json=prevNum,proto3" json:"prev_num"`
	//当前商家关联用户的昨日到店消费金额
	PrevAmount           float64  `protobuf:"fixed64,4,opt,name=prev_amount,json=prevAmount,proto3" json:"prev_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMarchentMarketingOutcomeRes) Reset()         { *m = GetMarchentMarketingOutcomeRes{} }
func (m *GetMarchentMarketingOutcomeRes) String() string { return proto.CompactTextString(m) }
func (*GetMarchentMarketingOutcomeRes) ProtoMessage()    {}
func (*GetMarchentMarketingOutcomeRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_39d7f31115229404, []int{20}
}

func (m *GetMarchentMarketingOutcomeRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMarchentMarketingOutcomeRes.Unmarshal(m, b)
}
func (m *GetMarchentMarketingOutcomeRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMarchentMarketingOutcomeRes.Marshal(b, m, deterministic)
}
func (m *GetMarchentMarketingOutcomeRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMarchentMarketingOutcomeRes.Merge(m, src)
}
func (m *GetMarchentMarketingOutcomeRes) XXX_Size() int {
	return xxx_messageInfo_GetMarchentMarketingOutcomeRes.Size(m)
}
func (m *GetMarchentMarketingOutcomeRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMarchentMarketingOutcomeRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetMarchentMarketingOutcomeRes proto.InternalMessageInfo

func (m *GetMarchentMarketingOutcomeRes) GetAllNum() int32 {
	if m != nil {
		return m.AllNum
	}
	return 0
}

func (m *GetMarchentMarketingOutcomeRes) GetAllAmount() float64 {
	if m != nil {
		return m.AllAmount
	}
	return 0
}

func (m *GetMarchentMarketingOutcomeRes) GetPrevNum() int32 {
	if m != nil {
		return m.PrevNum
	}
	return 0
}

func (m *GetMarchentMarketingOutcomeRes) GetPrevAmount() float64 {
	if m != nil {
		return m.PrevAmount
	}
	return 0
}

func init() {
	proto.RegisterType((*GetPetBreedsReq)(nil), "pm.GetPetBreedsReq")
	proto.RegisterType((*GetBaseCityReq)(nil), "pm.GetBaseCityReq")
	proto.RegisterType((*BaseRes)(nil), "pm.BaseRes")
	proto.RegisterType((*CreateVaccineReq)(nil), "pm.CreateVaccineReq")
	proto.RegisterType((*GetBaseMarchentReq)(nil), "pm.GetBaseMarchentReq")
	proto.RegisterType((*GetMarchentListReq)(nil), "pm.GetMarchentListReq")
	proto.RegisterType((*CreateMarchentReq)(nil), "pm.CreateMarchentReq")
	proto.RegisterType((*MarchentImportReq)(nil), "pm.MarchentImportReq")
	proto.RegisterType((*MarchentReq)(nil), "pm.MarchentReq")
	proto.RegisterType((*MarchentStateReq)(nil), "pm.MarchentStateReq")
	proto.RegisterType((*MarchentQualifyReq)(nil), "pm.MarchentQualifyReq")
	proto.RegisterType((*MarchentManagementReq)(nil), "pm.MarchentManagementReq")
	proto.RegisterType((*MarchentOperateReq)(nil), "pm.MarchentOperateReq")
	proto.RegisterType((*MarchentFinanceReq)(nil), "pm.MarchentFinanceReq")
	proto.RegisterType((*MarchentMemberReq)(nil), "pm.MarchentMemberReq")
	proto.RegisterType((*MarchentMember)(nil), "pm.MarchentMember")
	proto.RegisterType((*MarchentImportDto)(nil), "pm.MarchentImportDto")
	proto.RegisterType((*MarchentPetListDto)(nil), "pm.MarchentPetListDto")
	proto.RegisterType((*MarchentDeliveryRecordListDto)(nil), "pm.MarchentDeliveryRecordListDto")
	proto.RegisterType((*GetMarchentMarketingOutcomeReq)(nil), "pm.GetMarchentMarketingOutcomeReq")
	proto.RegisterType((*GetMarchentMarketingOutcomeRes)(nil), "pm.GetMarchentMarketingOutcomeRes")
}

func init() { proto.RegisterFile("pm/petmillions.proto", fileDescriptor_39d7f31115229404) }

var fileDescriptor_39d7f31115229404 = []byte{
	// 2698 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x5a, 0x4b, 0x73, 0x1c, 0xb7,
	0x11, 0xae, 0xe5, 0x7b, 0x7b, 0xc9, 0x25, 0x39, 0x7c, 0x78, 0x24, 0x45, 0xaf, 0x75, 0x1c, 0xd3,
	0x72, 0x44, 0xd9, 0x92, 0xed, 0xd8, 0x89, 0x93, 0x58, 0x24, 0x25, 0x99, 0xb6, 0x28, 0xd3, 0x4b,
	0xd9, 0x39, 0x4e, 0x0d, 0x67, 0x5a, 0x5c, 0x48, 0xf3, 0xf2, 0x0c, 0x96, 0x12, 0x5d, 0xb9, 0xe5,
	0x2f, 0x24, 0xc7, 0xdc, 0x73, 0xca, 0x29, 0xa7, 0xfc, 0xa6, 0xe4, 0x0f, 0xf8, 0x90, 0x4a, 0xa1,
	0x1b, 0x98, 0x01, 0x96, 0x14, 0xb9, 0x4a, 0xe5, 0x46, 0x7c, 0xfd, 0x35, 0x1a, 0x68, 0xf4, 0x76,
	0x37, 0x30, 0x84, 0xd5, 0x22, 0xbd, 0x53, 0xa0, 0x4c, 0x45, 0x92, 0x88, 0x3c, 0xab, 0x36, 0x8b,
	0x32, 0x97, 0xb9, 0x37, 0x51, 0xa4, 0xbd, 0x2f, 0x60, 0xf1, 0x11, 0xca, 0x7d, 0x94, 0x5b, 0x25,
	0x62, 0x5c, 0xf5, 0xf1, 0x07, 0xef, 0x36, 0xac, 0x14, 0x28, 0x83, 0x58, 0x44, 0x32, 0x28, 0xc2,
	0x12, 0x33, 0x19, 0x88, 0xb8, 0xf2, 0x5b, 0x37, 0x26, 0x37, 0xda, 0xfd, 0xa5, 0x02, 0xe5, 0x8e,
	0x88, 0xe4, 0x3e, 0x09, 0x76, 0xe3, 0xaa, 0xb7, 0x09, 0xdd, 0x47, 0x28, 0xb7, 0xc2, 0x0a, 0xb7,
	0x85, 0x3c, 0x51, 0x13, 0xfc, 0x0c, 0xda, 0x91, 0x90, 0x27, 0x59, 0x98, 0xa2, 0x51, 0x6b, 0x80,
	0xde, 0x73, 0x98, 0x55, 0xe4, 0x3e, 0x56, 0x9e, 0x07, 0x53, 0x51, 0x1e, 0xa3, 0xdf, 0xba, 0xd1,
	0xda, 0x98, 0xee, 0xd3, 0xdf, 0x9e, 0x0f, 0xb3, 0x29, 0x56, 0x55, 0x78, 0x84, 0xfe, 0xc4, 0x8d,
	0xd6, 0x46, 0xbb, 0x6f, 0x86, 0xde, 0x55, 0x80, 0x38, 0x94, 0x61, 0x10, 0xe5, 0xc3, 0x4c, 0xfa,
	0x93, 0x37, 0x5a, 0x1b, 0x93, 0xfd, 0xb6, 0x42, 0xb6, 0x15, 0xa0, 0x26, 0x53, 0x03, 0x7f, 0x8a,
	0xb4, 0xe8, 0xef, 0xde, 0x4f, 0x2d, 0x58, 0xda, 0x2e, 0x31, 0x94, 0xf8, 0x7d, 0x18, 0x45, 0x22,
	0x43, 0xb5, 0xbc, 0x2e, 0x4c, 0x88, 0x58, 0xdb, 0x9c, 0x10, 0xb1, 0x77, 0x09, 0xe6, 0xd4, 0x7e,
	0xe5, 0x49, 0xc1, 0x26, 0xa7, 0xfb, 0xb3, 0x05, 0xca, 0xa7, 0x27, 0x05, 0x7a, 0x97, 0x61, 0xae,
	0x1a, 0x16, 0x45, 0x22, 0xb0, 0x24, 0x83, 0xed, 0x7e, 0x3d, 0xf6, 0x6e, 0xc2, 0xfc, 0x31, 0x4f,
	0x1a, 0xa8, 0x8d, 0x69, 0xbb, 0x1d, 0x8d, 0x3d, 0x09, 0x53, 0xb4, 0x29, 0x31, 0x56, 0x91, 0x3f,
	0xed, 0x50, 0x76, 0xb0, 0x8a, 0xd4, 0xa6, 0x44, 0x15, 0x60, 0x16, 0x1e, 0x26, 0x18, 0xfb, 0x33,
	0x64, 0xbe, 0x2d, 0xaa, 0x07, 0x0c, 0x28, 0x71, 0x11, 0x1e, 0x61, 0x20, 0xb2, 0x18, 0x5f, 0xf9,
	0xb3, 0x2c, 0x56, 0xc8, 0xae, 0x02, 0xbc, 0x2b, 0x40, 0x83, 0xa0, 0x12, 0x3f, 0xa2, 0x3f, 0x47,
	0xd2, 0x39, 0x05, 0x1c, 0x88, 0x1f, 0xb1, 0x97, 0x81, 0xa7, 0x0f, 0x66, 0x2f, 0x2c, 0xa3, 0x01,
	0x66, 0x52, 0xed, 0xfe, 0x1a, 0x40, 0xaa, 0x87, 0xb5, 0x17, 0x2c, 0x44, 0x1d, 0x5e, 0x3d, 0xbf,
	0x76, 0x87, 0x65, 0xf0, 0x32, 0xd4, 0xf3, 0x93, 0x43, 0x6c, 0x7b, 0x3f, 0xb5, 0xc8, 0xa0, 0x31,
	0xf6, 0x58, 0x54, 0x64, 0xf0, 0x3a, 0x74, 0x8a, 0x32, 0x3f, 0x16, 0x59, 0x84, 0x41, 0x63, 0xd1,
	0x40, 0xbb, 0xb1, 0xf7, 0x16, 0xcc, 0xaa, 0xe8, 0x50, 0x42, 0xb6, 0x37, 0xa3, 0x86, 0xbb, 0xf1,
	0x88, 0x6f, 0x26, 0x47, 0x7d, 0x63, 0x9f, 0xdb, 0x94, 0x7b, 0x6e, 0x57, 0xa0, 0x5d, 0x0d, 0xf2,
	0x82, 0x0f, 0x66, 0x5a, 0x1f, 0xdc, 0x20, 0x2f, 0xe8, 0x54, 0x5c, 0x9f, 0xce, 0x9c, 0xeb, 0xd3,
	0x59, 0x77, 0x8f, 0x6a, 0xad, 0x61, 0x89, 0xa1, 0x5a, 0x2b, 0xbb, 0x7b, 0x46, 0x0d, 0x77, 0xe3,
	0xde, 0x5f, 0xa6, 0x60, 0x99, 0x23, 0xed, 0x4d, 0x9c, 0xfd, 0x36, 0x2c, 0xa4, 0x58, 0x46, 0x83,
	0x30, 0x93, 0xbc, 0x56, 0x0e, 0xf9, 0x79, 0x03, 0xd2, 0x7a, 0x2f, 0xc3, 0xdc, 0xb0, 0xc2, 0x92,
	0xe4, 0x3a, 0x08, 0xcd, 0xd8, 0x5b, 0x87, 0x99, 0x34, 0x3f, 0x14, 0x89, 0x09, 0x3f, 0x3d, 0x3a,
	0xdf, 0x01, 0x46, 0x48, 0x9e, 0xe3, 0xfd, 0x93, 0x90, 0x5c, 0x77, 0x17, 0xd6, 0x0e, 0x87, 0x55,
	0x25, 0x32, 0xac, 0xaa, 0xe0, 0x59, 0x5e, 0xa6, 0xa1, 0x76, 0x31, 0xbb, 0x62, 0xa5, 0x16, 0x3e,
	0x24, 0x19, 0xe9, 0x8c, 0x1c, 0xf1, 0xdc, 0xa9, 0x23, 0x56, 0x61, 0xa3, 0x47, 0x7e, 0x9b, 0x57,
	0x63, 0xc6, 0xf6, 0xf1, 0x83, 0x73, 0xfc, 0x2a, 0x3b, 0x08, 0x79, 0xe2, 0x77, 0xf8, 0x07, 0xad,
	0xfe, 0x56, 0xd9, 0x21, 0x8c, 0xe3, 0x12, 0xab, 0xca, 0x9f, 0xe7, 0xec, 0xa0, 0x87, 0x2a, 0x6e,
	0x65, 0x89, 0x61, 0x35, 0x2c, 0xb1, 0xf4, 0x17, 0x48, 0xd6, 0x00, 0xde, 0x7b, 0xb0, 0x54, 0x0f,
	0x02, 0xed, 0xb1, 0x2e, 0x91, 0x16, 0x6b, 0x7c, 0x8f, 0x5d, 0xb7, 0x0e, 0x33, 0x09, 0x86, 0x31,
	0x96, 0xfe, 0x22, 0xbb, 0x94, 0x47, 0xf6, 0xd1, 0x2f, 0xd9, 0x47, 0xaf, 0xdc, 0x49, 0x02, 0xf2,
	0xf5, 0x32, 0xef, 0x4e, 0x01, 0xca, 0xd7, 0xbd, 0x2f, 0x61, 0xd9, 0x04, 0xc4, 0x6e, 0x5a, 0xe4,
	0x25, 0x85, 0xc5, 0x3d, 0x68, 0x9b, 0x20, 0xe0, 0x04, 0xd9, 0xb9, 0xbb, 0xb6, 0x59, 0xa4, 0x9b,
	0x2e, 0x73, 0x47, 0xe6, 0xfd, 0x86, 0xd7, 0xfb, 0x4f, 0x0b, 0x3a, 0xff, 0xf7, 0xd8, 0x6a, 0xe2,
	0x67, 0xd2, 0x89, 0x1f, 0x3b, 0xe6, 0xa6, 0x46, 0x62, 0xee, 0x0a, 0xb4, 0x45, 0x1c, 0x64, 0xc3,
	0xf4, 0x10, 0x4b, 0x13, 0x5b, 0x22, 0x7e, 0x42, 0x63, 0x6f, 0x0d, 0x66, 0x44, 0x1c, 0x14, 0x22,
	0xa2, 0xc0, 0x6a, 0xf7, 0xa7, 0x45, 0xbc, 0x2f, 0x22, 0x6f, 0x15, 0xa6, 0x31, 0x0d, 0x45, 0x42,
	0x51, 0xd4, 0xee, 0xf3, 0x40, 0x59, 0x39, 0x14, 0xa5, 0x1c, 0xc4, 0xe1, 0x09, 0x05, 0x4d, 0xbb,
	0x5f, 0x8f, 0xbd, 0x25, 0x98, 0xac, 0xf0, 0x15, 0x45, 0xcb, 0x74, 0x5f, 0xfd, 0xd9, 0xfb, 0x16,
	0x96, 0xcc, 0xfe, 0x0f, 0x64, 0x28, 0x71, 0x1c, 0x27, 0xb8, 0x29, 0x64, 0x62, 0x24, 0x85, 0xf4,
	0xfe, 0x31, 0x09, 0x9e, 0x99, 0xf3, 0xdb, 0x61, 0x98, 0x88, 0x67, 0x27, 0xe3, 0xcc, 0x7a, 0x13,
	0xe6, 0x7f, 0x60, 0xb6, 0xed, 0xd9, 0x8e, 0xc6, 0xc8, 0xb1, 0xd7, 0xa1, 0x93, 0x88, 0x08, 0xb3,
	0x0a, 0xc9, 0x19, 0xec, 0x5d, 0xd0, 0x90, 0xf2, 0xc8, 0x75, 0xe8, 0x44, 0x25, 0xc6, 0x42, 0x06,
	0x54, 0x02, 0xd9, 0xc9, 0xc0, 0xd0, 0xb6, 0x2a, 0x84, 0x6f, 0xc3, 0x82, 0x31, 0x92, 0xbf, 0xcc,
	0x6a, 0x57, 0x1b, 0xcb, 0xdf, 0x28, 0xcc, 0x7b, 0x1f, 0x96, 0x0d, 0x09, 0x2b, 0x19, 0x1e, 0x26,
	0xa2, 0x1a, 0x68, 0xcf, 0x2f, 0x69, 0xc1, 0x03, 0x83, 0x2b, 0x93, 0x55, 0x81, 0x91, 0x08, 0x13,
	0x5a, 0x13, 0x1f, 0x05, 0x68, 0x48, 0xad, 0xe9, 0x3d, 0x58, 0x0a, 0x33, 0x29, 0xb0, 0x10, 0x31,
	0xa6, 0x22, 0x22, 0x16, 0x9f, 0xcb, 0xa2, 0x8d, 0x2b, 0xea, 0xbb, 0xb0, 0x18, 0xe7, 0x47, 0x41,
	0x58, 0x55, 0x79, 0x24, 0x42, 0x29, 0xf2, 0x4c, 0xff, 0xb0, 0xbb, 0x71, 0x7e, 0x74, 0xbf, 0x41,
	0x15, 0x31, 0x0a, 0xa5, 0x43, 0x04, 0x26, 0x46, 0xa1, 0xb4, 0x89, 0xca, 0x78, 0x33, 0x0c, 0xe2,
	0x50, 0xa2, 0xfe, 0xe9, 0x2f, 0x5a, 0xf8, 0x4e, 0x28, 0xb1, 0xf7, 0xef, 0x69, 0x58, 0x33, 0xc7,
	0xb6, 0x17, 0x66, 0xe1, 0x11, 0xa6, 0x63, 0xfe, 0x28, 0x9c, 0xbc, 0x38, 0x71, 0x5e, 0x5e, 0x9c,
	0x1c, 0x37, 0x2f, 0x4e, 0xbd, 0x3e, 0x2f, 0xbe, 0x03, 0xdd, 0x46, 0x87, 0xc8, 0x7c, 0x86, 0x0b,
	0x35, 0x4a, 0x34, 0xbb, 0x90, 0xcd, 0xb8, 0x85, 0x4c, 0x65, 0x56, 0x94, 0xc1, 0x71, 0x58, 0x0a,
	0x94, 0x27, 0xe6, 0xc8, 0x0a, 0x94, 0xdf, 0x33, 0xe2, 0xdd, 0x06, 0xaf, 0x8a, 0xc2, 0x04, 0x83,
	0x12, 0x8b, 0x32, 0x8f, 0x87, 0x11, 0x79, 0x98, 0x33, 0xf0, 0x32, 0x49, 0xfa, 0x96, 0x40, 0x99,
	0xa2, 0x2d, 0xaa, 0x93, 0xe5, 0xf3, 0x9a, 0x55, 0x63, 0x1d, 0x90, 0x76, 0x12, 0x87, 0x73, 0x93,
	0x78, 0xe7, 0xf5, 0x49, 0x7c, 0xfe, 0xcc, 0x24, 0xbe, 0x70, 0x76, 0x12, 0xef, 0x9e, 0x4e, 0xe2,
	0x98, 0x60, 0x31, 0xc8, 0x33, 0xd4, 0xe9, 0xb7, 0x01, 0xd4, 0x02, 0x44, 0x95, 0x88, 0xec, 0x05,
	0x9a, 0x14, 0x5c, 0x8f, 0xeb, 0xb3, 0x4b, 0xf2, 0xa3, 0xdc, 0x24, 0x61, 0x05, 0x3c, 0xce, 0x8f,
	0x72, 0x75, 0x0e, 0x98, 0x1d, 0x8b, 0x32, 0x57, 0x61, 0x42, 0x7b, 0xf7, 0xf8, 0x1c, 0x1a, 0x54,
	0x79, 0xc0, 0xcc, 0x41, 0xbd, 0xda, 0x4a, 0x33, 0x07, 0x35, 0x6a, 0x37, 0x61, 0x9e, 0x84, 0x29,
	0x57, 0x0b, 0x7f, 0x95, 0x7f, 0xf3, 0x0a, 0xdb, 0x63, 0x48, 0x51, 0x30, 0x2d, 0x92, 0xfc, 0x04,
	0x51, 0xa5, 0x47, 0x7f, 0x8d, 0xd6, 0xd8, 0x31, 0xd8, 0x93, 0x61, 0x6a, 0x17, 0x91, 0xf5, 0xd7,
	0x17, 0x91, 0xb7, 0x46, 0x8a, 0xc8, 0xdf, 0x27, 0x9a, 0x34, 0xf5, 0x4d, 0x81, 0xe5, 0xf8, 0xc9,
	0x2f, 0xce, 0x87, 0x27, 0x22, 0xa3, 0xd5, 0x70, 0xb4, 0xb7, 0x19, 0x51, 0x6b, 0xb9, 0x09, 0xf3,
	0x2f, 0x86, 0xa1, 0xa8, 0x06, 0xf9, 0x90, 0x08, 0x9c, 0xa3, 0x3a, 0x06, 0x53, 0x94, 0xb7, 0x61,
	0xe1, 0x28, 0xcf, 0x8e, 0x7e, 0x1c, 0xe4, 0xd9, 0x11, 0x71, 0x38, 0x4d, 0xcd, 0xd7, 0xa0, 0x22,
	0xa9, 0xb4, 0x32, 0x10, 0x85, 0xd0, 0x94, 0x69, 0x9d, 0x56, 0x18, 0x52, 0x84, 0xab, 0x00, 0x2f,
	0x31, 0x1a, 0x84, 0x92, 0xe4, 0x9c, 0x9d, 0xda, 0x8c, 0x28, 0xf1, 0x1a, 0xcc, 0x3c, 0xa7, 0x7a,
	0x62, 0x8a, 0xc3, 0xf3, 0x58, 0x6b, 0xc9, 0x30, 0x3f, 0x0c, 0x73, 0x12, 0xcd, 0xe9, 0x60, 0x20,
	0x44, 0x89, 0xaf, 0x40, 0x3b, 0x97, 0x03, 0x2c, 0x49, 0xaa, 0x7b, 0x0a, 0x02, 0x9e, 0x0c, 0xd3,
	0xde, 0x5f, 0x2d, 0x87, 0x3d, 0x14, 0x59, 0x98, 0x45, 0x38, 0x66, 0x5e, 0x3f, 0x0c, 0xb3, 0x17,
	0x41, 0x8c, 0x45, 0x5e, 0x09, 0x69, 0xf2, 0xba, 0xc2, 0x76, 0x18, 0x52, 0x14, 0x2d, 0x0d, 0xac,
	0x86, 0xac, 0xa3, 0x31, 0x93, 0xfa, 0x6b, 0x4a, 0xed, 0x32, 0x30, 0x8c, 0x61, 0xea, 0xdd, 0x82,
	0xe5, 0x0a, 0x93, 0x67, 0x81, 0x63, 0x8b, 0xdd, 0xb6, 0xa8, 0x04, 0x5b, 0x96, 0x3d, 0xc3, 0x75,
	0x8c, 0xce, 0x34, 0xdc, 0x1d, 0xcb, 0xf0, 0x06, 0x2c, 0xb9, 0xdc, 0xda, 0xa5, 0x5d, 0x9b, 0x3a,
	0x4c, 0x7b, 0x41, 0xd3, 0x95, 0xec, 0xa1, 0xaa, 0xdb, 0xe3, 0x78, 0xe7, 0x16, 0xcc, 0x30, 0xd9,
	0x9f, 0xa0, 0x96, 0xc5, 0xb3, 0x5b, 0x16, 0x3d, 0x8d, 0x66, 0xf4, 0xfe, 0x08, 0x5d, 0x57, 0x72,
	0xe1, 0xec, 0x76, 0xc7, 0x31, 0xf1, 0xda, 0x2e, 0xd7, 0xed, 0x52, 0xd6, 0x61, 0x46, 0x54, 0x87,
	0x79, 0x55, 0xe9, 0x24, 0xac, 0x47, 0xbd, 0x7f, 0x2d, 0x8c, 0x76, 0x5d, 0x3b, 0x32, 0x57, 0x16,
	0xca, 0xfc, 0x25, 0x5f, 0x6c, 0x74, 0x76, 0x37, 0xe3, 0xd3, 0xcd, 0x52, 0xeb, 0x8c, 0x66, 0xe9,
	0x3a, 0x74, 0x52, 0xda, 0x0c, 0x53, 0x38, 0x03, 0x00, 0x43, 0xe6, 0xbe, 0xa7, 0x09, 0x9c, 0xc1,
	0xd6, 0x38, 0x38, 0x18, 0xdb, 0xa7, 0x1c, 0x46, 0x86, 0x52, 0x54, 0x1c, 0x6e, 0x88, 0xd6, 0x8d,
	0x21, 0x02, 0x1f, 0x50, 0x5f, 0xf4, 0x2e, 0x2c, 0xea, 0x79, 0xea, 0xf6, 0x88, 0x53, 0x42, 0x97,
	0xe1, 0x2d, 0xd3, 0x24, 0x5d, 0x05, 0x6d, 0x3e, 0x50, 0xbd, 0x92, 0xcf, 0xed, 0x0d, 0x23, 0x07,
	0xf8, 0xea, 0x54, 0x9f, 0x72, 0xed, 0x74, 0x9f, 0x72, 0x1b, 0x3c, 0x43, 0x69, 0x7a, 0x0f, 0xff,
	0x3a, 0x11, 0x4d, 0x6b, 0xb1, 0x7d, 0x4e, 0x53, 0x72, 0x63, 0xdc, 0xa6, 0xe4, 0xe6, 0x6b, 0x9a,
	0x12, 0xb5, 0xd7, 0xba, 0x84, 0xf3, 0x32, 0x37, 0xf4, 0x5e, 0x6b, 0x98, 0x56, 0xea, 0x12, 0xa9,
	0xa8, 0xbc, 0x37, 0x4a, 0xdc, 0x56, 0xe5, 0xc5, 0x25, 0xaa, 0x24, 0xea, 0x3f, 0x19, 0x25, 0xde,
	0x2f, 0x31, 0x54, 0x7b, 0xb7, 0x89, 0xba, 0x24, 0xdd, 0xe2, 0xbd, 0x5b, 0x5c, 0x5d, 0x9c, 0xdc,
	0x79, 0xa9, 0x5a, 0xbf, 0x4f, 0x1e, 0xb7, 0xe6, 0xa5, 0xa2, 0xfd, 0x00, 0xae, 0x59, 0xc4, 0x33,
	0x1a, 0x03, 0xff, 0x97, 0xa4, 0x77, 0xb5, 0x61, 0x6d, 0x9d, 0xd1, 0x3d, 0x7c, 0x06, 0xfe, 0x59,
	0xd3, 0xd0, 0x04, 0xb7, 0x69, 0x91, 0x6f, 0x9d, 0x31, 0x01, 0xa9, 0xba, 0x3b, 0xd3, 0xcd, 0x84,
	0xbf, 0xc9, 0x5d, 0x41, 0x23, 0xd9, 0xd7, 0x5d, 0xc6, 0x6f, 0xe0, 0x92, 0x4b, 0xd7, 0xdd, 0x05,
	0x69, 0xdd, 0x21, 0x53, 0xbe, 0xa3, 0x65, 0xc9, 0xbd, 0x0f, 0x61, 0xd5, 0x76, 0x4b, 0x5d, 0xbe,
	0x3f, 0x20, 0xbd, 0x15, 0xcb, 0x37, 0x75, 0x21, 0xbf, 0x03, 0x16, 0x1c, 0x88, 0xea, 0x31, 0xd7,
	0xf4, 0x0f, 0x6f, 0xb4, 0x36, 0xe6, 0xfa, 0xd6, 0xca, 0x77, 0xb5, 0x64, 0xc4, 0xf5, 0x54, 0x9f,
	0xef, 0x8e, 0x1e, 0x29, 0x55, 0x69, 0x77, 0xe3, 0xa6, 0x56, 0xdf, 0x1b, 0x3d, 0x52, 0x53, 0xb1,
	0x3f, 0x86, 0x75, 0x8b, 0x6e, 0x15, 0x6a, 0xff, 0x23, 0xf2, 0xd5, 0x5a, 0x23, 0x7d, 0x60, 0x55,
	0xf1, 0x77, 0xa0, 0x9b, 0x73, 0x19, 0x0e, 0xb8, 0x9c, 0xfa, 0x9f, 0x73, 0x3f, 0xa1, 0xd1, 0x1d,
	0x02, 0x55, 0x47, 0x6b, 0x68, 0xa6, 0xa8, 0xfa, 0xbf, 0xe5, 0xd4, 0xad, 0xf1, 0xaf, 0x35, 0xac,
	0x7e, 0x32, 0x86, 0x5a, 0xd7, 0x56, 0xff, 0x77, 0xfc, 0x93, 0xd1, 0x82, 0x47, 0x06, 0x57, 0xde,
	0x30, 0x64, 0x5d, 0x65, 0xfd, 0xdf, 0xb3, 0x37, 0x34, 0x7c, 0xc0, 0xa8, 0xbd, 0x4e, 0x2e, 0xb7,
	0xfe, 0x17, 0xce, 0x3a, 0xff, 0x40, 0xa0, 0xca, 0x22, 0x86, 0xf6, 0x3c, 0xf6, 0xef, 0x73, 0xa5,
	0xd5, 0xc8, 0x57, 0xb1, 0x3d, 0x0b, 0x97, 0x5f, 0x7f, 0xcb, 0x99, 0xe5, 0x29, 0x81, 0x6a, 0x55,
	0xcf, 0xb8, 0xd4, 0xd6, 0x35, 0xed, 0x21, 0xaf, 0x4a, 0xc3, 0x56, 0x09, 0x35, 0x44, 0xfa, 0xb9,
	0x3f, 0xe2, 0xac, 0xa4, 0x31, 0x93, 0x69, 0x6b, 0xca, 0x30, 0xf5, 0xbf, 0xe4, 0x4c, 0x6b, 0x18,
	0xc3, 0xd4, 0xfb, 0x00, 0x56, 0x0d, 0xc1, 0x2e, 0x79, 0xfe, 0x2e, 0x31, 0x3d, 0x2d, 0x3b, 0x68,
	0xaa, 0x9e, 0x2a, 0xa4, 0x8e, 0x06, 0x99, 0xfe, 0x8a, 0x4f, 0xc3, 0xa2, 0x9b, 0x42, 0xea, 0x72,
	0x87, 0xa9, 0xff, 0xb5, 0xb3, 0x17, 0xa2, 0x72, 0x29, 0x37, 0x4c, 0x55, 0xad, 0x78, 0xd6, 0xc7,
	0xce, 0xac, 0xdf, 0x55, 0xba, 0x3a, 0x6c, 0xc2, 0x8a, 0xc3, 0xd5, 0x25, 0x6d, 0x8f, 0x83, 0xd3,
	0x62, 0xeb, 0x87, 0x08, 0xaa, 0x02, 0xba, 0x26, 0xe9, 0x17, 0x89, 0x09, 0x53, 0x05, 0x18, 0x7e,
	0x4c, 0x68, 0xef, 0x9f, 0x53, 0x4d, 0xb7, 0xb3, 0x8f, 0xf4, 0xf0, 0xa6, 0xea, 0xdd, 0x2a, 0x4c,
	0x17, 0x28, 0x77, 0x63, 0x6a, 0xb2, 0xa7, 0xfb, 0x3c, 0x50, 0x55, 0x30, 0xc3, 0x8a, 0xd3, 0x3c,
	0x17, 0xb9, 0x7a, 0x6c, 0x64, 0x4f, 0xac, 0x1a, 0x6c, 0xc6, 0xd4, 0xfd, 0xeb, 0x57, 0x62, 0xf3,
	0x0a, 0x65, 0xc6, 0xaa, 0x0e, 0x17, 0x28, 0x0f, 0xf0, 0x95, 0x79, 0x85, 0xe2, 0x11, 0xbd, 0x25,
	0xa2, 0xdc, 0x0e, 0xcb, 0x78, 0x37, 0xd6, 0x0d, 0x4e, 0x03, 0xa8, 0x6b, 0x80, 0x1a, 0xa8, 0x85,
	0x70, 0x43, 0x63, 0x86, 0xde, 0x06, 0x2c, 0x16, 0x49, 0x98, 0xf5, 0x31, 0xca, 0xcb, 0xf8, 0xa1,
	0x28, 0x2b, 0xa9, 0xfb, 0x98, 0x51, 0xd8, 0xbb, 0x05, 0x4b, 0x0d, 0x74, 0x80, 0x51, 0x9e, 0xc5,
	0xba, 0x55, 0x3c, 0x85, 0xbb, 0xb3, 0x3e, 0x1d, 0x88, 0x32, 0xd6, 0x7d, 0xe3, 0x28, 0xec, 0xfd,
	0x02, 0xba, 0x31, 0xbe, 0xcc, 0xcb, 0x54, 0x64, 0x47, 0x6c, 0x5e, 0x5f, 0x59, 0x5d, 0x54, 0xcd,
	0x58, 0x23, 0xda, 0x38, 0x5f, 0x5a, 0x46, 0x61, 0x67, 0x46, 0x36, 0xbd, 0x34, 0x32, 0x23, 0x5b,
	0xfe, 0x39, 0x2c, 0xc4, 0x98, 0x88, 0x63, 0x2c, 0x4f, 0xe8, 0x8d, 0x43, 0x5f, 0xb4, 0x5c, 0xd0,
	0xeb, 0xa9, 0x26, 0x94, 0x01, 0x75, 0x1f, 0xd6, 0x4f, 0x61, 0x0e, 0xc6, 0x16, 0x79, 0xcc, 0xf1,
	0xa4, 0xaf, 0x60, 0x23, 0x68, 0xef, 0x4f, 0x2d, 0xb8, 0x6a, 0x82, 0x67, 0x47, 0x8b, 0xd8, 0x17,
	0x26, 0x8e, 0x9a, 0xee, 0xab, 0xe5, 0x74, 0x5f, 0x3e, 0xcc, 0xaa, 0x4b, 0xfa, 0x4e, 0x78, 0x62,
	0x5e, 0xea, 0xf5, 0xd0, 0x3d, 0xf7, 0xc9, 0xd1, 0x73, 0x37, 0x57, 0xc2, 0xa9, 0xe6, 0x4a, 0xd8,
	0xbb, 0x0f, 0xd7, 0xac, 0xa7, 0xe3, 0xbd, 0xb0, 0x7c, 0x81, 0x52, 0x64, 0x47, 0xdf, 0x0c, 0x65,
	0x94, 0xa7, 0xa8, 0x9f, 0x91, 0x4d, 0xb7, 0x18, 0x9c, 0x6e, 0x20, 0x77, 0xe3, 0xde, 0x9f, 0x5b,
	0x17, 0xcc, 0x51, 0xd1, 0xed, 0x2b, 0x49, 0xe8, 0xe7, 0xdc, 0xd2, 0xb7, 0xaf, 0x24, 0xd1, 0x77,
	0x0d, 0x25, 0x08, 0x53, 0xfa, 0xb4, 0xa0, 0x76, 0xd3, 0xea, 0xb7, 0xc3, 0x24, 0xb9, 0x4f, 0x00,
	0x5d, 0xd0, 0x4b, 0x3c, 0xae, 0x6f, 0x49, 0xea, 0x82, 0x5e, 0xe2, 0xb1, 0xbe, 0xfc, 0x90, 0x48,
	0xab, 0x4e, 0x91, 0x2a, 0x28, 0x88, 0x75, 0xef, 0xfe, 0xad, 0x0b, 0xde, 0x3e, 0xca, 0x3d, 0xfd,
	0xe9, 0xe5, 0x00, 0xcb, 0x63, 0x11, 0xa1, 0x77, 0x17, 0x16, 0x9c, 0x0f, 0x13, 0xde, 0xaa, 0xea,
	0xa6, 0x47, 0xbf, 0x55, 0x5c, 0xee, 0x28, 0xd4, 0x7c, 0x2e, 0xb9, 0x0b, 0x0b, 0xdf, 0x15, 0xf1,
	0x9b, 0xe9, 0xfc, 0x0a, 0x3c, 0x47, 0x87, 0x03, 0x68, 0x0c, 0xc5, 0x3b, 0x00, 0x8f, 0x54, 0xa1,
	0x1f, 0xdb, 0xd2, 0x3d, 0xfa, 0x0e, 0xa4, 0xa5, 0x2a, 0x76, 0xc6, 0x51, 0xfa, 0x0c, 0x56, 0x1f,
	0xa1, 0x7c, 0xf0, 0x4a, 0xf5, 0xe8, 0x6f, 0xa8, 0xfa, 0x11, 0x74, 0xdd, 0x07, 0x77, 0x6f, 0xad,
	0x51, 0xb2, 0x1e, 0x4a, 0x5d, 0xad, 0x4f, 0x8d, 0x3f, 0xec, 0xef, 0x22, 0x63, 0x69, 0xde, 0xa1,
	0x2f, 0x65, 0x8e, 0xda, 0xa2, 0x7d, 0x03, 0x3a, 0xa5, 0xb0, 0x09, 0x5d, 0x36, 0x35, 0x26, 0xff,
	0x53, 0x58, 0x71, 0xf9, 0xd6, 0x59, 0x8d, 0x3e, 0x7c, 0xba, 0x9a, 0x9f, 0xc3, 0x9a, 0xab, 0xa9,
	0xdf, 0x32, 0xbd, 0x75, 0x5b, 0xb7, 0x79, 0xe0, 0x74, 0xb5, 0xb7, 0xc0, 0x77, 0xb5, 0x9b, 0x27,
	0x35, 0xef, 0x92, 0x73, 0xc7, 0xb3, 0x9f, 0xda, 0x2e, 0x58, 0x81, 0x7e, 0xa6, 0x70, 0x57, 0xd0,
	0xbc, 0x5d, 0x5c, 0xa0, 0xad, 0xef, 0xec, 0xae, 0x76, 0x73, 0x91, 0x77, 0xb5, 0x7f, 0x0d, 0xab,
	0x23, 0xeb, 0xe7, 0x1b, 0xe7, 0xda, 0x19, 0xf7, 0xd3, 0xd3, 0x3f, 0xa9, 0x8e, 0x95, 0x33, 0xd8,
	0xde, 0xe9, 0x8f, 0x66, 0xa3, 0x31, 0x6b, 0x7f, 0xe6, 0x72, 0x5c, 0x7d, 0x91, 0xea, 0xe7, 0xb0,
	0xe6, 0xa4, 0xa8, 0xda, 0xcf, 0xff, 0x83, 0x61, 0xc7, 0xc3, 0x6f, 0xa6, 0xea, 0xb8, 0xf7, 0x22,
	0xd5, 0x4f, 0x61, 0xd9, 0x5e, 0x33, 0xfb, 0x76, 0x2c, 0xcd, 0x8f, 0x29, 0x23, 0x58, 0x9d, 0xc9,
	0x78, 0x6a, 0x5f, 0xc0, 0x25, 0x4b, 0xcd, 0xad, 0x49, 0xe3, 0xcd, 0x80, 0x70, 0xe5, 0x9c, 0x4a,
	0xe0, 0xf5, 0xf4, 0x1c, 0xe7, 0x94, 0x9b, 0xcb, 0x17, 0x73, 0x2a, 0xef, 0x13, 0xca, 0x08, 0xf6,
	0xf7, 0xce, 0x7a, 0x79, 0x23, 0x1f, 0x41, 0xcf, 0x8a, 0x02, 0x4e, 0x7a, 0x6f, 0xae, 0xfd, 0x11,
	0x74, 0xf9, 0x4d, 0xc3, 0xcd, 0x5e, 0xa7, 0xbe, 0x32, 0x9d, 0x77, 0x8a, 0x3b, 0x28, 0x43, 0x91,
	0x8c, 0xe7, 0xcc, 0x4f, 0x60, 0xc5, 0xfa, 0xbe, 0xff, 0xf4, 0x65, 0xfe, 0x18, 0x8f, 0x31, 0xf1,
	0x3c, 0x4b, 0x57, 0x7f, 0xf8, 0x77, 0xf5, 0x3e, 0x80, 0x79, 0xfb, 0x3f, 0x0b, 0xbc, 0x15, 0xad,
	0x60, 0xff, 0xaf, 0x81, 0xa3, 0x71, 0x38, 0x43, 0xff, 0x96, 0x70, 0xef, 0xbf, 0x01, 0x00, 0x00,
	0xff, 0xff, 0x95, 0xd9, 0x41, 0x28, 0xae, 0x20, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PetMillionsServiceClient is the client API for PetMillionsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PetMillionsServiceClient interface {
	//创建疫苗信息
	CreateVaccine(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新疫苗信息
	UpdateVaccine(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新疫苗状态
	UpdateVaccineState(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取当个疫苗信息
	GetVaccine(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取疫苗列表
	GetVaccineList(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取所有的疫苗列表
	GetExportVaccineList(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error)
	//创建商户信息
	CreateMarchent(ctx context.Context, in *CreateMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新主账号信息
	UpdateBaseMarchent(ctx context.Context, in *CreateMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新主账号信息
	GetBaseMarchent(ctx context.Context, in *MarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新主账号信息
	UpdateMarchent(ctx context.Context, in *MarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新主账号信息
	UpdateMarchentState(ctx context.Context, in *MarchentStateReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新资质认证信息
	UpdateMarchentQualify(ctx context.Context, in *MarchentQualifyReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新经营信息信息
	UpdateMarchentManagement(ctx context.Context, in *MarchentManagementReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新运营信息
	UpdateMarchentOperate(ctx context.Context, in *MarchentOperateReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新商户财务信息
	UpdateMarchentFinance(ctx context.Context, in *MarchentFinanceReq, opts ...grpc.CallOption) (*BaseRes, error)
	//更新账户信息
	UpdateMarchentMember(ctx context.Context, in *MarchentMemberReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取主账户信息
	GetMarchent(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取资质认证信息
	GetMarchentQualify(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取经营信息信息
	GetMarchentManagement(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取运营信息
	GetMarchentOperate(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取商户财务信息
	GetMarchentFinance(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取账户信息
	GetMarchentMember(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	// 获取宠物信息
	GetMarchentPet(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	// 获取客户信息
	GetMarchentDeliveryRecord(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取商户营销成果(到店、消费)
	GetMarchentMarketingOutcome(ctx context.Context, in *GetMarchentMarketingOutcomeReq, opts ...grpc.CallOption) (*GetMarchentMarketingOutcomeRes, error)
	//获取所有的商家信息列表
	GetMarchentList(ctx context.Context, in *GetMarchentListReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取所有的商家信息列表-导出
	GetExportMarchentList(ctx context.Context, in *GetMarchentListReq, opts ...grpc.CallOption) (*BaseRes, error)
	//导入商家信息
	ImportMarchent(ctx context.Context, in *MarchentImportReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取商家详情信息-组合detail
	GetMarchentDetail(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error)
	//根据城市信息获取门店信息
	GetBaseCityTwoLevel(ctx context.Context, in *GetBaseCityReq, opts ...grpc.CallOption) (*BaseRes, error)
	//获取宠物品种 例如（阿富汗猎犬）
	GetPetBreeds(ctx context.Context, in *GetPetBreedsReq, opts ...grpc.CallOption) (*BaseRes, error)
}

type petMillionsServiceClient struct {
	cc *grpc.ClientConn
}

func NewPetMillionsServiceClient(cc *grpc.ClientConn) PetMillionsServiceClient {
	return &petMillionsServiceClient{cc}
}

func (c *petMillionsServiceClient) CreateVaccine(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/CreateVaccine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateVaccine(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateVaccine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateVaccineState(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateVaccineState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetVaccine(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetVaccine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetVaccineList(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetVaccineList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetExportVaccineList(ctx context.Context, in *CreateVaccineReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetExportVaccineList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) CreateMarchent(ctx context.Context, in *CreateMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/CreateMarchent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateBaseMarchent(ctx context.Context, in *CreateMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateBaseMarchent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetBaseMarchent(ctx context.Context, in *MarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetBaseMarchent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateMarchent(ctx context.Context, in *MarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateMarchent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateMarchentState(ctx context.Context, in *MarchentStateReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateMarchentState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateMarchentQualify(ctx context.Context, in *MarchentQualifyReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateMarchentQualify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateMarchentManagement(ctx context.Context, in *MarchentManagementReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateMarchentManagement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateMarchentOperate(ctx context.Context, in *MarchentOperateReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateMarchentOperate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateMarchentFinance(ctx context.Context, in *MarchentFinanceReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateMarchentFinance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) UpdateMarchentMember(ctx context.Context, in *MarchentMemberReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/UpdateMarchentMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchent(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentQualify(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentQualify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentManagement(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentManagement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentOperate(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentOperate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentFinance(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentFinance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentMember(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentPet(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentDeliveryRecord(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentDeliveryRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentMarketingOutcome(ctx context.Context, in *GetMarchentMarketingOutcomeReq, opts ...grpc.CallOption) (*GetMarchentMarketingOutcomeRes, error) {
	out := new(GetMarchentMarketingOutcomeRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentMarketingOutcome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentList(ctx context.Context, in *GetMarchentListReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetExportMarchentList(ctx context.Context, in *GetMarchentListReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetExportMarchentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) ImportMarchent(ctx context.Context, in *MarchentImportReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/ImportMarchent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetMarchentDetail(ctx context.Context, in *GetBaseMarchentReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetMarchentDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetBaseCityTwoLevel(ctx context.Context, in *GetBaseCityReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetBaseCityTwoLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsServiceClient) GetPetBreeds(ctx context.Context, in *GetPetBreedsReq, opts ...grpc.CallOption) (*BaseRes, error) {
	out := new(BaseRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsService/GetPetBreeds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetMillionsServiceServer is the server API for PetMillionsService service.
type PetMillionsServiceServer interface {
	//创建疫苗信息
	CreateVaccine(context.Context, *CreateVaccineReq) (*BaseRes, error)
	//更新疫苗信息
	UpdateVaccine(context.Context, *CreateVaccineReq) (*BaseRes, error)
	//更新疫苗状态
	UpdateVaccineState(context.Context, *CreateVaccineReq) (*BaseRes, error)
	//获取当个疫苗信息
	GetVaccine(context.Context, *CreateVaccineReq) (*BaseRes, error)
	//获取疫苗列表
	GetVaccineList(context.Context, *CreateVaccineReq) (*BaseRes, error)
	//获取所有的疫苗列表
	GetExportVaccineList(context.Context, *CreateVaccineReq) (*BaseRes, error)
	//创建商户信息
	CreateMarchent(context.Context, *CreateMarchentReq) (*BaseRes, error)
	//更新主账号信息
	UpdateBaseMarchent(context.Context, *CreateMarchentReq) (*BaseRes, error)
	//更新主账号信息
	GetBaseMarchent(context.Context, *MarchentReq) (*BaseRes, error)
	//更新主账号信息
	UpdateMarchent(context.Context, *MarchentReq) (*BaseRes, error)
	//更新主账号信息
	UpdateMarchentState(context.Context, *MarchentStateReq) (*BaseRes, error)
	//更新资质认证信息
	UpdateMarchentQualify(context.Context, *MarchentQualifyReq) (*BaseRes, error)
	//更新经营信息信息
	UpdateMarchentManagement(context.Context, *MarchentManagementReq) (*BaseRes, error)
	//更新运营信息
	UpdateMarchentOperate(context.Context, *MarchentOperateReq) (*BaseRes, error)
	//更新商户财务信息
	UpdateMarchentFinance(context.Context, *MarchentFinanceReq) (*BaseRes, error)
	//更新账户信息
	UpdateMarchentMember(context.Context, *MarchentMemberReq) (*BaseRes, error)
	//获取主账户信息
	GetMarchent(context.Context, *GetBaseMarchentReq) (*BaseRes, error)
	//获取资质认证信息
	GetMarchentQualify(context.Context, *GetBaseMarchentReq) (*BaseRes, error)
	//获取经营信息信息
	GetMarchentManagement(context.Context, *GetBaseMarchentReq) (*BaseRes, error)
	//获取运营信息
	GetMarchentOperate(context.Context, *GetBaseMarchentReq) (*BaseRes, error)
	//获取商户财务信息
	GetMarchentFinance(context.Context, *GetBaseMarchentReq) (*BaseRes, error)
	//获取账户信息
	GetMarchentMember(context.Context, *GetBaseMarchentReq) (*BaseRes, error)
	// 获取宠物信息
	GetMarchentPet(context.Context, *GetBaseMarchentReq) (*BaseRes, error)
	// 获取客户信息
	GetMarchentDeliveryRecord(context.Context, *GetBaseMarchentReq) (*BaseRes, error)
	//获取商户营销成果(到店、消费)
	GetMarchentMarketingOutcome(context.Context, *GetMarchentMarketingOutcomeReq) (*GetMarchentMarketingOutcomeRes, error)
	//获取所有的商家信息列表
	GetMarchentList(context.Context, *GetMarchentListReq) (*BaseRes, error)
	//获取所有的商家信息列表-导出
	GetExportMarchentList(context.Context, *GetMarchentListReq) (*BaseRes, error)
	//导入商家信息
	ImportMarchent(context.Context, *MarchentImportReq) (*BaseRes, error)
	//获取商家详情信息-组合detail
	GetMarchentDetail(context.Context, *GetBaseMarchentReq) (*BaseRes, error)
	//根据城市信息获取门店信息
	GetBaseCityTwoLevel(context.Context, *GetBaseCityReq) (*BaseRes, error)
	//获取宠物品种 例如（阿富汗猎犬）
	GetPetBreeds(context.Context, *GetPetBreedsReq) (*BaseRes, error)
}

// UnimplementedPetMillionsServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPetMillionsServiceServer struct {
}

func (*UnimplementedPetMillionsServiceServer) CreateVaccine(ctx context.Context, req *CreateVaccineReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVaccine not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateVaccine(ctx context.Context, req *CreateVaccineReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVaccine not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateVaccineState(ctx context.Context, req *CreateVaccineReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVaccineState not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetVaccine(ctx context.Context, req *CreateVaccineReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVaccine not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetVaccineList(ctx context.Context, req *CreateVaccineReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVaccineList not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetExportVaccineList(ctx context.Context, req *CreateVaccineReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExportVaccineList not implemented")
}
func (*UnimplementedPetMillionsServiceServer) CreateMarchent(ctx context.Context, req *CreateMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMarchent not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateBaseMarchent(ctx context.Context, req *CreateMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBaseMarchent not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetBaseMarchent(ctx context.Context, req *MarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBaseMarchent not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateMarchent(ctx context.Context, req *MarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarchent not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateMarchentState(ctx context.Context, req *MarchentStateReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarchentState not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateMarchentQualify(ctx context.Context, req *MarchentQualifyReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarchentQualify not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateMarchentManagement(ctx context.Context, req *MarchentManagementReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarchentManagement not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateMarchentOperate(ctx context.Context, req *MarchentOperateReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarchentOperate not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateMarchentFinance(ctx context.Context, req *MarchentFinanceReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarchentFinance not implemented")
}
func (*UnimplementedPetMillionsServiceServer) UpdateMarchentMember(ctx context.Context, req *MarchentMemberReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarchentMember not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchent(ctx context.Context, req *GetBaseMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchent not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentQualify(ctx context.Context, req *GetBaseMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentQualify not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentManagement(ctx context.Context, req *GetBaseMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentManagement not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentOperate(ctx context.Context, req *GetBaseMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentOperate not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentFinance(ctx context.Context, req *GetBaseMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentFinance not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentMember(ctx context.Context, req *GetBaseMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentMember not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentPet(ctx context.Context, req *GetBaseMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentPet not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentDeliveryRecord(ctx context.Context, req *GetBaseMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentDeliveryRecord not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentMarketingOutcome(ctx context.Context, req *GetMarchentMarketingOutcomeReq) (*GetMarchentMarketingOutcomeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentMarketingOutcome not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentList(ctx context.Context, req *GetMarchentListReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentList not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetExportMarchentList(ctx context.Context, req *GetMarchentListReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExportMarchentList not implemented")
}
func (*UnimplementedPetMillionsServiceServer) ImportMarchent(ctx context.Context, req *MarchentImportReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportMarchent not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetMarchentDetail(ctx context.Context, req *GetBaseMarchentReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentDetail not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetBaseCityTwoLevel(ctx context.Context, req *GetBaseCityReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBaseCityTwoLevel not implemented")
}
func (*UnimplementedPetMillionsServiceServer) GetPetBreeds(ctx context.Context, req *GetPetBreedsReq) (*BaseRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetBreeds not implemented")
}

func RegisterPetMillionsServiceServer(s *grpc.Server, srv PetMillionsServiceServer) {
	s.RegisterService(&_PetMillionsService_serviceDesc, srv)
}

func _PetMillionsService_CreateVaccine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVaccineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).CreateVaccine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/CreateVaccine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).CreateVaccine(ctx, req.(*CreateVaccineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateVaccine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVaccineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateVaccine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateVaccine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateVaccine(ctx, req.(*CreateVaccineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateVaccineState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVaccineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateVaccineState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateVaccineState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateVaccineState(ctx, req.(*CreateVaccineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetVaccine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVaccineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetVaccine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetVaccine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetVaccine(ctx, req.(*CreateVaccineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetVaccineList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVaccineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetVaccineList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetVaccineList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetVaccineList(ctx, req.(*CreateVaccineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetExportVaccineList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVaccineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetExportVaccineList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetExportVaccineList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetExportVaccineList(ctx, req.(*CreateVaccineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_CreateMarchent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).CreateMarchent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/CreateMarchent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).CreateMarchent(ctx, req.(*CreateMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateBaseMarchent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateBaseMarchent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateBaseMarchent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateBaseMarchent(ctx, req.(*CreateMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetBaseMarchent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetBaseMarchent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetBaseMarchent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetBaseMarchent(ctx, req.(*MarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateMarchent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateMarchent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateMarchent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateMarchent(ctx, req.(*MarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateMarchentState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateMarchentState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateMarchentState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateMarchentState(ctx, req.(*MarchentStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateMarchentQualify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentQualifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateMarchentQualify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateMarchentQualify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateMarchentQualify(ctx, req.(*MarchentQualifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateMarchentManagement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentManagementReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateMarchentManagement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateMarchentManagement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateMarchentManagement(ctx, req.(*MarchentManagementReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateMarchentOperate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentOperateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateMarchentOperate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateMarchentOperate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateMarchentOperate(ctx, req.(*MarchentOperateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateMarchentFinance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentFinanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateMarchentFinance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateMarchentFinance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateMarchentFinance(ctx, req.(*MarchentFinanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_UpdateMarchentMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).UpdateMarchentMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/UpdateMarchentMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).UpdateMarchentMember(ctx, req.(*MarchentMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchent(ctx, req.(*GetBaseMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentQualify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentQualify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentQualify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentQualify(ctx, req.(*GetBaseMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentManagement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentManagement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentManagement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentManagement(ctx, req.(*GetBaseMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentOperate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentOperate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentOperate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentOperate(ctx, req.(*GetBaseMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentFinance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentFinance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentFinance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentFinance(ctx, req.(*GetBaseMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentMember(ctx, req.(*GetBaseMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentPet(ctx, req.(*GetBaseMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentDeliveryRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentDeliveryRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentDeliveryRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentDeliveryRecord(ctx, req.(*GetBaseMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentMarketingOutcome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMarchentMarketingOutcomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentMarketingOutcome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentMarketingOutcome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentMarketingOutcome(ctx, req.(*GetMarchentMarketingOutcomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMarchentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentList(ctx, req.(*GetMarchentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetExportMarchentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMarchentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetExportMarchentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetExportMarchentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetExportMarchentList(ctx, req.(*GetMarchentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_ImportMarchent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentImportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).ImportMarchent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/ImportMarchent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).ImportMarchent(ctx, req.(*MarchentImportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetMarchentDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseMarchentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetMarchentDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetMarchentDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetMarchentDetail(ctx, req.(*GetBaseMarchentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetBaseCityTwoLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseCityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetBaseCityTwoLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetBaseCityTwoLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetBaseCityTwoLevel(ctx, req.(*GetBaseCityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsService_GetPetBreeds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetBreedsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsServiceServer).GetPetBreeds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsService/GetPetBreeds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsServiceServer).GetPetBreeds(ctx, req.(*GetPetBreedsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PetMillionsService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pm.PetMillionsService",
	HandlerType: (*PetMillionsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVaccine",
			Handler:    _PetMillionsService_CreateVaccine_Handler,
		},
		{
			MethodName: "UpdateVaccine",
			Handler:    _PetMillionsService_UpdateVaccine_Handler,
		},
		{
			MethodName: "UpdateVaccineState",
			Handler:    _PetMillionsService_UpdateVaccineState_Handler,
		},
		{
			MethodName: "GetVaccine",
			Handler:    _PetMillionsService_GetVaccine_Handler,
		},
		{
			MethodName: "GetVaccineList",
			Handler:    _PetMillionsService_GetVaccineList_Handler,
		},
		{
			MethodName: "GetExportVaccineList",
			Handler:    _PetMillionsService_GetExportVaccineList_Handler,
		},
		{
			MethodName: "CreateMarchent",
			Handler:    _PetMillionsService_CreateMarchent_Handler,
		},
		{
			MethodName: "UpdateBaseMarchent",
			Handler:    _PetMillionsService_UpdateBaseMarchent_Handler,
		},
		{
			MethodName: "GetBaseMarchent",
			Handler:    _PetMillionsService_GetBaseMarchent_Handler,
		},
		{
			MethodName: "UpdateMarchent",
			Handler:    _PetMillionsService_UpdateMarchent_Handler,
		},
		{
			MethodName: "UpdateMarchentState",
			Handler:    _PetMillionsService_UpdateMarchentState_Handler,
		},
		{
			MethodName: "UpdateMarchentQualify",
			Handler:    _PetMillionsService_UpdateMarchentQualify_Handler,
		},
		{
			MethodName: "UpdateMarchentManagement",
			Handler:    _PetMillionsService_UpdateMarchentManagement_Handler,
		},
		{
			MethodName: "UpdateMarchentOperate",
			Handler:    _PetMillionsService_UpdateMarchentOperate_Handler,
		},
		{
			MethodName: "UpdateMarchentFinance",
			Handler:    _PetMillionsService_UpdateMarchentFinance_Handler,
		},
		{
			MethodName: "UpdateMarchentMember",
			Handler:    _PetMillionsService_UpdateMarchentMember_Handler,
		},
		{
			MethodName: "GetMarchent",
			Handler:    _PetMillionsService_GetMarchent_Handler,
		},
		{
			MethodName: "GetMarchentQualify",
			Handler:    _PetMillionsService_GetMarchentQualify_Handler,
		},
		{
			MethodName: "GetMarchentManagement",
			Handler:    _PetMillionsService_GetMarchentManagement_Handler,
		},
		{
			MethodName: "GetMarchentOperate",
			Handler:    _PetMillionsService_GetMarchentOperate_Handler,
		},
		{
			MethodName: "GetMarchentFinance",
			Handler:    _PetMillionsService_GetMarchentFinance_Handler,
		},
		{
			MethodName: "GetMarchentMember",
			Handler:    _PetMillionsService_GetMarchentMember_Handler,
		},
		{
			MethodName: "GetMarchentPet",
			Handler:    _PetMillionsService_GetMarchentPet_Handler,
		},
		{
			MethodName: "GetMarchentDeliveryRecord",
			Handler:    _PetMillionsService_GetMarchentDeliveryRecord_Handler,
		},
		{
			MethodName: "GetMarchentMarketingOutcome",
			Handler:    _PetMillionsService_GetMarchentMarketingOutcome_Handler,
		},
		{
			MethodName: "GetMarchentList",
			Handler:    _PetMillionsService_GetMarchentList_Handler,
		},
		{
			MethodName: "GetExportMarchentList",
			Handler:    _PetMillionsService_GetExportMarchentList_Handler,
		},
		{
			MethodName: "ImportMarchent",
			Handler:    _PetMillionsService_ImportMarchent_Handler,
		},
		{
			MethodName: "GetMarchentDetail",
			Handler:    _PetMillionsService_GetMarchentDetail_Handler,
		},
		{
			MethodName: "GetBaseCityTwoLevel",
			Handler:    _PetMillionsService_GetBaseCityTwoLevel_Handler,
		},
		{
			MethodName: "GetPetBreeds",
			Handler:    _PetMillionsService_GetPetBreeds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pm/petmillions.proto",
}
