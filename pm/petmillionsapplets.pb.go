// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pm/petmillionsapplets.proto

package pm

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	_struct "github.com/golang/protobuf/ptypes/struct"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//nest
type CreateNestReq struct {
	//窝名称
	NestName string `protobuf:"bytes,2,opt,name=nest_name,json=nestName,proto3" json:"nest_name"`
	//窝code
	NestCode string `protobuf:"bytes,3,opt,name=nest_code,json=nestCode,proto3" json:"nest_code"`
	//宠物出生日期
	PetBirthday string `protobuf:"bytes,4,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	//宠物种类
	PetSpecies string `protobuf:"bytes,5,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	//宠物品种
	PetBreed string `protobuf:"bytes,6,opt,name=pet_breed,json=petBreed,proto3" json:"pet_breed"`
	//数量(公)
	MaleNumber int32 `protobuf:"varint,7,opt,name=male_number,json=maleNumber,proto3" json:"male_number"`
	//数量(母)
	FemaleNumber int32 `protobuf:"varint,8,opt,name=female_number,json=femaleNumber,proto3" json:"female_number"`
	//关联商户Id
	MarchentId int32 `protobuf:"varint,9,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//免疫计划模板id
	PlanId int32 `protobuf:"varint,13,opt,name=plan_id,json=planId,proto3" json:"plan_id"`
	//第一次驱虫时间
	FirstDate string `protobuf:"bytes,10,opt,name=first_date,json=firstDate,proto3" json:"first_date"`
	//第二次驱虫时间
	TwoDate string `protobuf:"bytes,11,opt,name=two_date,json=twoDate,proto3" json:"two_date"`
	//第三次驱虫时间
	ThreeDate string `protobuf:"bytes,12,opt,name=three_date,json=threeDate,proto3" json:"three_date"`
	//宠物品种Id
	PetVariety int32 `protobuf:"varint,15,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//宠物种类Id-1未知 1000猫 1001狗 1002其他
	PetKindof            int32    `protobuf:"varint,14,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateNestReq) Reset()         { *m = CreateNestReq{} }
func (m *CreateNestReq) String() string { return proto.CompactTextString(m) }
func (*CreateNestReq) ProtoMessage()    {}
func (*CreateNestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{0}
}

func (m *CreateNestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateNestReq.Unmarshal(m, b)
}
func (m *CreateNestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateNestReq.Marshal(b, m, deterministic)
}
func (m *CreateNestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateNestReq.Merge(m, src)
}
func (m *CreateNestReq) XXX_Size() int {
	return xxx_messageInfo_CreateNestReq.Size(m)
}
func (m *CreateNestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateNestReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateNestReq proto.InternalMessageInfo

func (m *CreateNestReq) GetNestName() string {
	if m != nil {
		return m.NestName
	}
	return ""
}

func (m *CreateNestReq) GetNestCode() string {
	if m != nil {
		return m.NestCode
	}
	return ""
}

func (m *CreateNestReq) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *CreateNestReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *CreateNestReq) GetPetBreed() string {
	if m != nil {
		return m.PetBreed
	}
	return ""
}

func (m *CreateNestReq) GetMaleNumber() int32 {
	if m != nil {
		return m.MaleNumber
	}
	return 0
}

func (m *CreateNestReq) GetFemaleNumber() int32 {
	if m != nil {
		return m.FemaleNumber
	}
	return 0
}

func (m *CreateNestReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *CreateNestReq) GetPlanId() int32 {
	if m != nil {
		return m.PlanId
	}
	return 0
}

func (m *CreateNestReq) GetFirstDate() string {
	if m != nil {
		return m.FirstDate
	}
	return ""
}

func (m *CreateNestReq) GetTwoDate() string {
	if m != nil {
		return m.TwoDate
	}
	return ""
}

func (m *CreateNestReq) GetThreeDate() string {
	if m != nil {
		return m.ThreeDate
	}
	return ""
}

func (m *CreateNestReq) GetPetVariety() int32 {
	if m != nil {
		return m.PetVariety
	}
	return 0
}

func (m *CreateNestReq) GetPetKindof() int32 {
	if m != nil {
		return m.PetKindof
	}
	return 0
}

type EditNestNameReq struct {
	//窝id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//窝名称
	NestName             string   `protobuf:"bytes,2,opt,name=nest_name,json=nestName,proto3" json:"nest_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditNestNameReq) Reset()         { *m = EditNestNameReq{} }
func (m *EditNestNameReq) String() string { return proto.CompactTextString(m) }
func (*EditNestNameReq) ProtoMessage()    {}
func (*EditNestNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{1}
}

func (m *EditNestNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditNestNameReq.Unmarshal(m, b)
}
func (m *EditNestNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditNestNameReq.Marshal(b, m, deterministic)
}
func (m *EditNestNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditNestNameReq.Merge(m, src)
}
func (m *EditNestNameReq) XXX_Size() int {
	return xxx_messageInfo_EditNestNameReq.Size(m)
}
func (m *EditNestNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EditNestNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_EditNestNameReq proto.InternalMessageInfo

func (m *EditNestNameReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *EditNestNameReq) GetNestName() string {
	if m != nil {
		return m.NestName
	}
	return ""
}

type GetNestReq struct {
	//窝id
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNestReq) Reset()         { *m = GetNestReq{} }
func (m *GetNestReq) String() string { return proto.CompactTextString(m) }
func (*GetNestReq) ProtoMessage()    {}
func (*GetNestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{2}
}

func (m *GetNestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNestReq.Unmarshal(m, b)
}
func (m *GetNestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNestReq.Marshal(b, m, deterministic)
}
func (m *GetNestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNestReq.Merge(m, src)
}
func (m *GetNestReq) XXX_Size() int {
	return xxx_messageInfo_GetNestReq.Size(m)
}
func (m *GetNestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNestReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNestReq proto.InternalMessageInfo

func (m *GetNestReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetNestListReq struct {
	//窝名称
	NestName string `protobuf:"bytes,1,opt,name=nest_name,json=nestName,proto3" json:"nest_name"`
	//分页、页码
	PageIndex int32 `protobuf:"varint,2,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//分页、每页个数
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//关联商户Id
	MarchentId           int32    `protobuf:"varint,4,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNestListReq) Reset()         { *m = GetNestListReq{} }
func (m *GetNestListReq) String() string { return proto.CompactTextString(m) }
func (*GetNestListReq) ProtoMessage()    {}
func (*GetNestListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{3}
}

func (m *GetNestListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNestListReq.Unmarshal(m, b)
}
func (m *GetNestListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNestListReq.Marshal(b, m, deterministic)
}
func (m *GetNestListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNestListReq.Merge(m, src)
}
func (m *GetNestListReq) XXX_Size() int {
	return xxx_messageInfo_GetNestListReq.Size(m)
}
func (m *GetNestListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNestListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNestListReq proto.InternalMessageInfo

func (m *GetNestListReq) GetNestName() string {
	if m != nil {
		return m.NestName
	}
	return ""
}

func (m *GetNestListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetNestListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetNestListReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

type CreatePlanReq struct {
	//宠物种类,例如(猫)
	PetSpecies string `protobuf:"bytes,1,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	//关联商户Id
	MarchentId int32 `protobuf:"varint,2,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//计划模板名称
	PlanName string `protobuf:"bytes,3,opt,name=plan_name,json=planName,proto3" json:"plan_name"`
	//首次疫苗品牌
	FirstVaccineBrand string `protobuf:"bytes,4,opt,name=first_vaccine_brand,json=firstVaccineBrand,proto3" json:"first_vaccine_brand"`
	//首次疫苗名称
	FirstVaccineName string `protobuf:"bytes,5,opt,name=first_vaccine_name,json=firstVaccineName,proto3" json:"first_vaccine_name"`
	//首次疫苗间隔天数
	FirstIntervalDays int32 `protobuf:"varint,6,opt,name=first_interval_days,json=firstIntervalDays,proto3" json:"first_interval_days"`
	//第二次疫苗品牌
	TwoVaccineBrand string `protobuf:"bytes,7,opt,name=two_vaccine_brand,json=twoVaccineBrand,proto3" json:"two_vaccine_brand"`
	//第二次疫苗名称
	TwoVaccineName string `protobuf:"bytes,8,opt,name=two_vaccine_name,json=twoVaccineName,proto3" json:"two_vaccine_name"`
	//第二次疫苗间隔天数
	TwoIntervalDays int32 `protobuf:"varint,9,opt,name=two_interval_days,json=twoIntervalDays,proto3" json:"two_interval_days"`
	//第三次疫苗品牌
	ThreeVaccineBrand string `protobuf:"bytes,10,opt,name=three_vaccine_brand,json=threeVaccineBrand,proto3" json:"three_vaccine_brand"`
	//第三次疫苗名称
	ThreeVaccineName string `protobuf:"bytes,11,opt,name=three_vaccine_name,json=threeVaccineName,proto3" json:"three_vaccine_name"`
	//第三次疫苗间隔天数
	ThreeVaccineDays int32 `protobuf:"varint,12,opt,name=three_vaccine_days,json=threeVaccineDays,proto3" json:"three_vaccine_days"`
	//宠物种类Id-1未知 1000猫 1001狗 1002其他
	PetKindof            int32    `protobuf:"varint,13,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePlanReq) Reset()         { *m = CreatePlanReq{} }
func (m *CreatePlanReq) String() string { return proto.CompactTextString(m) }
func (*CreatePlanReq) ProtoMessage()    {}
func (*CreatePlanReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{4}
}

func (m *CreatePlanReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePlanReq.Unmarshal(m, b)
}
func (m *CreatePlanReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePlanReq.Marshal(b, m, deterministic)
}
func (m *CreatePlanReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePlanReq.Merge(m, src)
}
func (m *CreatePlanReq) XXX_Size() int {
	return xxx_messageInfo_CreatePlanReq.Size(m)
}
func (m *CreatePlanReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePlanReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePlanReq proto.InternalMessageInfo

func (m *CreatePlanReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *CreatePlanReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *CreatePlanReq) GetPlanName() string {
	if m != nil {
		return m.PlanName
	}
	return ""
}

func (m *CreatePlanReq) GetFirstVaccineBrand() string {
	if m != nil {
		return m.FirstVaccineBrand
	}
	return ""
}

func (m *CreatePlanReq) GetFirstVaccineName() string {
	if m != nil {
		return m.FirstVaccineName
	}
	return ""
}

func (m *CreatePlanReq) GetFirstIntervalDays() int32 {
	if m != nil {
		return m.FirstIntervalDays
	}
	return 0
}

func (m *CreatePlanReq) GetTwoVaccineBrand() string {
	if m != nil {
		return m.TwoVaccineBrand
	}
	return ""
}

func (m *CreatePlanReq) GetTwoVaccineName() string {
	if m != nil {
		return m.TwoVaccineName
	}
	return ""
}

func (m *CreatePlanReq) GetTwoIntervalDays() int32 {
	if m != nil {
		return m.TwoIntervalDays
	}
	return 0
}

func (m *CreatePlanReq) GetThreeVaccineBrand() string {
	if m != nil {
		return m.ThreeVaccineBrand
	}
	return ""
}

func (m *CreatePlanReq) GetThreeVaccineName() string {
	if m != nil {
		return m.ThreeVaccineName
	}
	return ""
}

func (m *CreatePlanReq) GetThreeVaccineDays() int32 {
	if m != nil {
		return m.ThreeVaccineDays
	}
	return 0
}

func (m *CreatePlanReq) GetPetKindof() int32 {
	if m != nil {
		return m.PetKindof
	}
	return 0
}

type EditPlanReq struct {
	//宠物种类,例如(猫)
	PetSpecies string `protobuf:"bytes,1,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	//关联商户Id
	MarchentId int32 `protobuf:"varint,2,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//计划模板名称
	PlanName string `protobuf:"bytes,3,opt,name=plan_name,json=planName,proto3" json:"plan_name"`
	//首次疫苗品牌
	FirstVaccineBrand string `protobuf:"bytes,4,opt,name=first_vaccine_brand,json=firstVaccineBrand,proto3" json:"first_vaccine_brand"`
	//首次疫苗名称
	FirstVaccineName string `protobuf:"bytes,5,opt,name=first_vaccine_name,json=firstVaccineName,proto3" json:"first_vaccine_name"`
	//首次疫苗间隔天数
	FirstIntervalDays int32 `protobuf:"varint,6,opt,name=first_interval_days,json=firstIntervalDays,proto3" json:"first_interval_days"`
	//第二次疫苗品牌
	TwoVaccineBrand string `protobuf:"bytes,7,opt,name=two_vaccine_brand,json=twoVaccineBrand,proto3" json:"two_vaccine_brand"`
	//第二次疫苗名称
	TwoVaccineName string `protobuf:"bytes,8,opt,name=two_vaccine_name,json=twoVaccineName,proto3" json:"two_vaccine_name"`
	//第二次疫苗间隔天数
	TwoIntervalDays int32 `protobuf:"varint,9,opt,name=two_interval_days,json=twoIntervalDays,proto3" json:"two_interval_days"`
	//第三次疫苗品牌
	ThreeVaccineBrand string `protobuf:"bytes,10,opt,name=three_vaccine_brand,json=threeVaccineBrand,proto3" json:"three_vaccine_brand"`
	//第三次疫苗名称
	ThreeVaccineName string `protobuf:"bytes,11,opt,name=three_vaccine_name,json=threeVaccineName,proto3" json:"three_vaccine_name"`
	//第三次疫苗间隔天数
	ThreeVaccineDays int32 `protobuf:"varint,12,opt,name=three_vaccine_days,json=threeVaccineDays,proto3" json:"three_vaccine_days"`
	//免疫计划Id
	Id                   int32    `protobuf:"varint,13,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditPlanReq) Reset()         { *m = EditPlanReq{} }
func (m *EditPlanReq) String() string { return proto.CompactTextString(m) }
func (*EditPlanReq) ProtoMessage()    {}
func (*EditPlanReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{5}
}

func (m *EditPlanReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditPlanReq.Unmarshal(m, b)
}
func (m *EditPlanReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditPlanReq.Marshal(b, m, deterministic)
}
func (m *EditPlanReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditPlanReq.Merge(m, src)
}
func (m *EditPlanReq) XXX_Size() int {
	return xxx_messageInfo_EditPlanReq.Size(m)
}
func (m *EditPlanReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EditPlanReq.DiscardUnknown(m)
}

var xxx_messageInfo_EditPlanReq proto.InternalMessageInfo

func (m *EditPlanReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *EditPlanReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *EditPlanReq) GetPlanName() string {
	if m != nil {
		return m.PlanName
	}
	return ""
}

func (m *EditPlanReq) GetFirstVaccineBrand() string {
	if m != nil {
		return m.FirstVaccineBrand
	}
	return ""
}

func (m *EditPlanReq) GetFirstVaccineName() string {
	if m != nil {
		return m.FirstVaccineName
	}
	return ""
}

func (m *EditPlanReq) GetFirstIntervalDays() int32 {
	if m != nil {
		return m.FirstIntervalDays
	}
	return 0
}

func (m *EditPlanReq) GetTwoVaccineBrand() string {
	if m != nil {
		return m.TwoVaccineBrand
	}
	return ""
}

func (m *EditPlanReq) GetTwoVaccineName() string {
	if m != nil {
		return m.TwoVaccineName
	}
	return ""
}

func (m *EditPlanReq) GetTwoIntervalDays() int32 {
	if m != nil {
		return m.TwoIntervalDays
	}
	return 0
}

func (m *EditPlanReq) GetThreeVaccineBrand() string {
	if m != nil {
		return m.ThreeVaccineBrand
	}
	return ""
}

func (m *EditPlanReq) GetThreeVaccineName() string {
	if m != nil {
		return m.ThreeVaccineName
	}
	return ""
}

func (m *EditPlanReq) GetThreeVaccineDays() int32 {
	if m != nil {
		return m.ThreeVaccineDays
	}
	return 0
}

func (m *EditPlanReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type CreatePlanRecordReq struct {
	//宠物种类
	PetSpecies string `protobuf:"bytes,1,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	//首次免疫时间
	FirstVaccineDate string `protobuf:"bytes,2,opt,name=first_vaccine_date,json=firstVaccineDate,proto3" json:"first_vaccine_date"`
	//首次免疫使用的品牌
	FirstVaccineBrand string `protobuf:"bytes,3,opt,name=first_vaccine_brand,json=firstVaccineBrand,proto3" json:"first_vaccine_brand"`
	//首次免疫使用的名称
	FirstVaccineName string `protobuf:"bytes,4,opt,name=first_vaccine_name,json=firstVaccineName,proto3" json:"first_vaccine_name"`
	//首次关联疫苗库Id
	//int32 first_vaccine_id = 5;
	//第二免疫时间
	TwoVaccineDate string `protobuf:"bytes,6,opt,name=two_vaccine_date,json=twoVaccineDate,proto3" json:"two_vaccine_date"`
	//第二免疫使用的品牌
	TwoVaccineBrand string `protobuf:"bytes,7,opt,name=two_vaccine_brand,json=twoVaccineBrand,proto3" json:"two_vaccine_brand"`
	//第二次免疫使用的名称
	TwoVaccineName string `protobuf:"bytes,8,opt,name=two_vaccine_name,json=twoVaccineName,proto3" json:"two_vaccine_name"`
	//第二次关联疫苗库Id
	//int32 two_vaccine_id = 9;
	//第三免疫时间
	ThreeVaccineDate string `protobuf:"bytes,10,opt,name=three_vaccine_date,json=threeVaccineDate,proto3" json:"three_vaccine_date"`
	//第三免疫使用的品牌
	ThreeVaccineBrand string `protobuf:"bytes,11,opt,name=three_vaccine_brand,json=threeVaccineBrand,proto3" json:"three_vaccine_brand"`
	//第三免疫使用的名称
	ThreeVaccineName string `protobuf:"bytes,12,opt,name=three_vaccine_name,json=threeVaccineName,proto3" json:"three_vaccine_name"`
	//关联商户Id
	MarchentId int32 `protobuf:"varint,14,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//宠物种类Id-1未知 1000猫 1001狗 1002其他
	PetKindof            int32    `protobuf:"varint,15,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePlanRecordReq) Reset()         { *m = CreatePlanRecordReq{} }
func (m *CreatePlanRecordReq) String() string { return proto.CompactTextString(m) }
func (*CreatePlanRecordReq) ProtoMessage()    {}
func (*CreatePlanRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{6}
}

func (m *CreatePlanRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePlanRecordReq.Unmarshal(m, b)
}
func (m *CreatePlanRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePlanRecordReq.Marshal(b, m, deterministic)
}
func (m *CreatePlanRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePlanRecordReq.Merge(m, src)
}
func (m *CreatePlanRecordReq) XXX_Size() int {
	return xxx_messageInfo_CreatePlanRecordReq.Size(m)
}
func (m *CreatePlanRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePlanRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePlanRecordReq proto.InternalMessageInfo

func (m *CreatePlanRecordReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *CreatePlanRecordReq) GetFirstVaccineDate() string {
	if m != nil {
		return m.FirstVaccineDate
	}
	return ""
}

func (m *CreatePlanRecordReq) GetFirstVaccineBrand() string {
	if m != nil {
		return m.FirstVaccineBrand
	}
	return ""
}

func (m *CreatePlanRecordReq) GetFirstVaccineName() string {
	if m != nil {
		return m.FirstVaccineName
	}
	return ""
}

func (m *CreatePlanRecordReq) GetTwoVaccineDate() string {
	if m != nil {
		return m.TwoVaccineDate
	}
	return ""
}

func (m *CreatePlanRecordReq) GetTwoVaccineBrand() string {
	if m != nil {
		return m.TwoVaccineBrand
	}
	return ""
}

func (m *CreatePlanRecordReq) GetTwoVaccineName() string {
	if m != nil {
		return m.TwoVaccineName
	}
	return ""
}

func (m *CreatePlanRecordReq) GetThreeVaccineDate() string {
	if m != nil {
		return m.ThreeVaccineDate
	}
	return ""
}

func (m *CreatePlanRecordReq) GetThreeVaccineBrand() string {
	if m != nil {
		return m.ThreeVaccineBrand
	}
	return ""
}

func (m *CreatePlanRecordReq) GetThreeVaccineName() string {
	if m != nil {
		return m.ThreeVaccineName
	}
	return ""
}

func (m *CreatePlanRecordReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *CreatePlanRecordReq) GetPetKindof() int32 {
	if m != nil {
		return m.PetKindof
	}
	return 0
}

type EditPlanRecordReq struct {
	//宠物种类
	PetSpecies string `protobuf:"bytes,1,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	//首次免疫时间
	FirstVaccineDate string `protobuf:"bytes,2,opt,name=first_vaccine_date,json=firstVaccineDate,proto3" json:"first_vaccine_date"`
	//首次免疫使用的品牌
	FirstVaccineBrand string `protobuf:"bytes,3,opt,name=first_vaccine_brand,json=firstVaccineBrand,proto3" json:"first_vaccine_brand"`
	//首次免疫使用的名称
	FirstVaccineName string `protobuf:"bytes,4,opt,name=first_vaccine_name,json=firstVaccineName,proto3" json:"first_vaccine_name"`
	//首次关联疫苗库Id
	//int32 first_vaccine_id = 5;
	//第二免疫时间
	TwoVaccineDate string `protobuf:"bytes,6,opt,name=two_vaccine_date,json=twoVaccineDate,proto3" json:"two_vaccine_date"`
	//第二免疫使用的品牌
	TwoVaccineBrand string `protobuf:"bytes,7,opt,name=two_vaccine_brand,json=twoVaccineBrand,proto3" json:"two_vaccine_brand"`
	//第二次免疫使用的名称
	TwoVaccineName string `protobuf:"bytes,8,opt,name=two_vaccine_name,json=twoVaccineName,proto3" json:"two_vaccine_name"`
	//第二次关联疫苗库Id
	//int32 two_vaccine_id = 9;
	//第三免疫时间
	ThreeVaccineDate string `protobuf:"bytes,10,opt,name=three_vaccine_date,json=threeVaccineDate,proto3" json:"three_vaccine_date"`
	//第三免疫使用的品牌
	ThreeVaccineBrand string `protobuf:"bytes,11,opt,name=three_vaccine_brand,json=threeVaccineBrand,proto3" json:"three_vaccine_brand"`
	//第三免疫使用的名称
	ThreeVaccineName string `protobuf:"bytes,12,opt,name=three_vaccine_name,json=threeVaccineName,proto3" json:"three_vaccine_name"`
	//第三次关联疫苗库Id
	//int32 three_vaccine_id = 13;
	//关联商户Id
	MarchentId int32 `protobuf:"varint,14,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//id
	Id                   int32    `protobuf:"varint,15,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditPlanRecordReq) Reset()         { *m = EditPlanRecordReq{} }
func (m *EditPlanRecordReq) String() string { return proto.CompactTextString(m) }
func (*EditPlanRecordReq) ProtoMessage()    {}
func (*EditPlanRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{7}
}

func (m *EditPlanRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditPlanRecordReq.Unmarshal(m, b)
}
func (m *EditPlanRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditPlanRecordReq.Marshal(b, m, deterministic)
}
func (m *EditPlanRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditPlanRecordReq.Merge(m, src)
}
func (m *EditPlanRecordReq) XXX_Size() int {
	return xxx_messageInfo_EditPlanRecordReq.Size(m)
}
func (m *EditPlanRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EditPlanRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_EditPlanRecordReq proto.InternalMessageInfo

func (m *EditPlanRecordReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *EditPlanRecordReq) GetFirstVaccineDate() string {
	if m != nil {
		return m.FirstVaccineDate
	}
	return ""
}

func (m *EditPlanRecordReq) GetFirstVaccineBrand() string {
	if m != nil {
		return m.FirstVaccineBrand
	}
	return ""
}

func (m *EditPlanRecordReq) GetFirstVaccineName() string {
	if m != nil {
		return m.FirstVaccineName
	}
	return ""
}

func (m *EditPlanRecordReq) GetTwoVaccineDate() string {
	if m != nil {
		return m.TwoVaccineDate
	}
	return ""
}

func (m *EditPlanRecordReq) GetTwoVaccineBrand() string {
	if m != nil {
		return m.TwoVaccineBrand
	}
	return ""
}

func (m *EditPlanRecordReq) GetTwoVaccineName() string {
	if m != nil {
		return m.TwoVaccineName
	}
	return ""
}

func (m *EditPlanRecordReq) GetThreeVaccineDate() string {
	if m != nil {
		return m.ThreeVaccineDate
	}
	return ""
}

func (m *EditPlanRecordReq) GetThreeVaccineBrand() string {
	if m != nil {
		return m.ThreeVaccineBrand
	}
	return ""
}

func (m *EditPlanRecordReq) GetThreeVaccineName() string {
	if m != nil {
		return m.ThreeVaccineName
	}
	return ""
}

func (m *EditPlanRecordReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *EditPlanRecordReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetPlanReq struct {
	//免疫计划Id
	PlanId               int32    `protobuf:"varint,1,opt,name=plan_id,json=planId,proto3" json:"plan_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlanReq) Reset()         { *m = GetPlanReq{} }
func (m *GetPlanReq) String() string { return proto.CompactTextString(m) }
func (*GetPlanReq) ProtoMessage()    {}
func (*GetPlanReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{8}
}

func (m *GetPlanReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlanReq.Unmarshal(m, b)
}
func (m *GetPlanReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlanReq.Marshal(b, m, deterministic)
}
func (m *GetPlanReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlanReq.Merge(m, src)
}
func (m *GetPlanReq) XXX_Size() int {
	return xxx_messageInfo_GetPlanReq.Size(m)
}
func (m *GetPlanReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlanReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlanReq proto.InternalMessageInfo

func (m *GetPlanReq) GetPlanId() int32 {
	if m != nil {
		return m.PlanId
	}
	return 0
}

type GetPlanRecordReq struct {
	//免疫计划记录Id
	PlanRecordsId        int32    `protobuf:"varint,1,opt,name=plan_records_id,json=planRecordsId,proto3" json:"plan_records_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlanRecordReq) Reset()         { *m = GetPlanRecordReq{} }
func (m *GetPlanRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetPlanRecordReq) ProtoMessage()    {}
func (*GetPlanRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{9}
}

func (m *GetPlanRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlanRecordReq.Unmarshal(m, b)
}
func (m *GetPlanRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlanRecordReq.Marshal(b, m, deterministic)
}
func (m *GetPlanRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlanRecordReq.Merge(m, src)
}
func (m *GetPlanRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetPlanRecordReq.Size(m)
}
func (m *GetPlanRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlanRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlanRecordReq proto.InternalMessageInfo

func (m *GetPlanRecordReq) GetPlanRecordsId() int32 {
	if m != nil {
		return m.PlanRecordsId
	}
	return 0
}

type GetPlanListReq struct {
	//关联商户Id
	MarchentId int32 `protobuf:"varint,1,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//宠物种类
	PetSpecies string `protobuf:"bytes,2,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	//宠物种类Id
	PetKindof            int32    `protobuf:"varint,3,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlanListReq) Reset()         { *m = GetPlanListReq{} }
func (m *GetPlanListReq) String() string { return proto.CompactTextString(m) }
func (*GetPlanListReq) ProtoMessage()    {}
func (*GetPlanListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{10}
}

func (m *GetPlanListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlanListReq.Unmarshal(m, b)
}
func (m *GetPlanListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlanListReq.Marshal(b, m, deterministic)
}
func (m *GetPlanListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlanListReq.Merge(m, src)
}
func (m *GetPlanListReq) XXX_Size() int {
	return xxx_messageInfo_GetPlanListReq.Size(m)
}
func (m *GetPlanListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlanListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlanListReq proto.InternalMessageInfo

func (m *GetPlanListReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *GetPlanListReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *GetPlanListReq) GetPetKindof() int32 {
	if m != nil {
		return m.PetKindof
	}
	return 0
}

type GetPlanRecordListReq struct {
	//关联商户Id
	MarchentId int32 `protobuf:"varint,1,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//宠物种类
	PetSpecies string `protobuf:"bytes,2,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	//宠物种类Id
	PetKindof            int32    `protobuf:"varint,3,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlanRecordListReq) Reset()         { *m = GetPlanRecordListReq{} }
func (m *GetPlanRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetPlanRecordListReq) ProtoMessage()    {}
func (*GetPlanRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{11}
}

func (m *GetPlanRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlanRecordListReq.Unmarshal(m, b)
}
func (m *GetPlanRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlanRecordListReq.Marshal(b, m, deterministic)
}
func (m *GetPlanRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlanRecordListReq.Merge(m, src)
}
func (m *GetPlanRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetPlanRecordListReq.Size(m)
}
func (m *GetPlanRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlanRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlanRecordListReq proto.InternalMessageInfo

func (m *GetPlanRecordListReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *GetPlanRecordListReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *GetPlanRecordListReq) GetPetKindof() int32 {
	if m != nil {
		return m.PetKindof
	}
	return 0
}

//pet
type GetPetListReq struct {
	//窝Id
	NestId               int32    `protobuf:"varint,1,opt,name=nest_id,json=nestId,proto3" json:"nest_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetListReq) Reset()         { *m = GetPetListReq{} }
func (m *GetPetListReq) String() string { return proto.CompactTextString(m) }
func (*GetPetListReq) ProtoMessage()    {}
func (*GetPetListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{12}
}

func (m *GetPetListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetListReq.Unmarshal(m, b)
}
func (m *GetPetListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetListReq.Marshal(b, m, deterministic)
}
func (m *GetPetListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetListReq.Merge(m, src)
}
func (m *GetPetListReq) XXX_Size() int {
	return xxx_messageInfo_GetPetListReq.Size(m)
}
func (m *GetPetListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetListReq proto.InternalMessageInfo

func (m *GetPetListReq) GetNestId() int32 {
	if m != nil {
		return m.NestId
	}
	return 0
}

type GetPetReq struct {
	//宠物Id
	PetId                int32    `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetReq) Reset()         { *m = GetPetReq{} }
func (m *GetPetReq) String() string { return proto.CompactTextString(m) }
func (*GetPetReq) ProtoMessage()    {}
func (*GetPetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{13}
}

func (m *GetPetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetReq.Unmarshal(m, b)
}
func (m *GetPetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetReq.Marshal(b, m, deterministic)
}
func (m *GetPetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetReq.Merge(m, src)
}
func (m *GetPetReq) XXX_Size() int {
	return xxx_messageInfo_GetPetReq.Size(m)
}
func (m *GetPetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetReq proto.InternalMessageInfo

func (m *GetPetReq) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

type EditPetReq struct {
	//宠物Id
	PetId int32 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//宠物花色
	NCount string `protobuf:"bytes,2,opt,name=n_count,json=nCount,proto3" json:"n_count"`
	//宠物芯片号(需要唯一)
	PetCode string `protobuf:"bytes,3,opt,name=pet_code,json=petCode,proto3" json:"pet_code"`
	//首次驱虫时间
	FirstDate string `protobuf:"bytes,4,opt,name=first_date,json=firstDate,proto3" json:"first_date"`
	//第二次驱虫时间
	TwoDate string `protobuf:"bytes,5,opt,name=two_date,json=twoDate,proto3" json:"two_date"`
	//第二次驱虫时间
	ThreeDate string `protobuf:"bytes,6,opt,name=three_date,json=threeDate,proto3" json:"three_date"`
	//第三次驱虫时间
	Remarks string `protobuf:"bytes,7,opt,name=remarks,proto3" json:"remarks"`
	//免疫计划记录详情
	PlanRecords *PlanRecords `protobuf:"bytes,8,opt,name=plan_records,json=planRecords,proto3" json:"plan_records"`
	//性别(0:其他,1:公,2母)
	Sex                  int32    `protobuf:"varint,9,opt,name=sex,proto3" json:"sex"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditPetReq) Reset()         { *m = EditPetReq{} }
func (m *EditPetReq) String() string { return proto.CompactTextString(m) }
func (*EditPetReq) ProtoMessage()    {}
func (*EditPetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{14}
}

func (m *EditPetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditPetReq.Unmarshal(m, b)
}
func (m *EditPetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditPetReq.Marshal(b, m, deterministic)
}
func (m *EditPetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditPetReq.Merge(m, src)
}
func (m *EditPetReq) XXX_Size() int {
	return xxx_messageInfo_EditPetReq.Size(m)
}
func (m *EditPetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EditPetReq.DiscardUnknown(m)
}

var xxx_messageInfo_EditPetReq proto.InternalMessageInfo

func (m *EditPetReq) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

func (m *EditPetReq) GetNCount() string {
	if m != nil {
		return m.NCount
	}
	return ""
}

func (m *EditPetReq) GetPetCode() string {
	if m != nil {
		return m.PetCode
	}
	return ""
}

func (m *EditPetReq) GetFirstDate() string {
	if m != nil {
		return m.FirstDate
	}
	return ""
}

func (m *EditPetReq) GetTwoDate() string {
	if m != nil {
		return m.TwoDate
	}
	return ""
}

func (m *EditPetReq) GetThreeDate() string {
	if m != nil {
		return m.ThreeDate
	}
	return ""
}

func (m *EditPetReq) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *EditPetReq) GetPlanRecords() *PlanRecords {
	if m != nil {
		return m.PlanRecords
	}
	return nil
}

func (m *EditPetReq) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type BatchEditPetReq struct {
	//窝id
	NestId int32 `protobuf:"varint,1,opt,name=nest_id,json=nestId,proto3" json:"nest_id"`
	//首次驱虫时间
	FirstDate string `protobuf:"bytes,2,opt,name=first_date,json=firstDate,proto3" json:"first_date"`
	//第二次驱虫时间
	TwoDate string `protobuf:"bytes,3,opt,name=two_date,json=twoDate,proto3" json:"two_date"`
	//第三次驱虫时间
	ThreeDate string `protobuf:"bytes,5,opt,name=three_date,json=threeDate,proto3" json:"three_date"`
	//repeated  list 在前面加
	//窝的免疫计划记录
	PlanRecordsId int32 `protobuf:"varint,6,opt,name=plan_records_id,json=planRecordsId,proto3" json:"plan_records_id"`
	//免疫计划记录详情
	PlanRecords          *PlanRecords `protobuf:"bytes,8,opt,name=plan_records,json=planRecords,proto3" json:"plan_records"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchEditPetReq) Reset()         { *m = BatchEditPetReq{} }
func (m *BatchEditPetReq) String() string { return proto.CompactTextString(m) }
func (*BatchEditPetReq) ProtoMessage()    {}
func (*BatchEditPetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{15}
}

func (m *BatchEditPetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchEditPetReq.Unmarshal(m, b)
}
func (m *BatchEditPetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchEditPetReq.Marshal(b, m, deterministic)
}
func (m *BatchEditPetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchEditPetReq.Merge(m, src)
}
func (m *BatchEditPetReq) XXX_Size() int {
	return xxx_messageInfo_BatchEditPetReq.Size(m)
}
func (m *BatchEditPetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchEditPetReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchEditPetReq proto.InternalMessageInfo

func (m *BatchEditPetReq) GetNestId() int32 {
	if m != nil {
		return m.NestId
	}
	return 0
}

func (m *BatchEditPetReq) GetFirstDate() string {
	if m != nil {
		return m.FirstDate
	}
	return ""
}

func (m *BatchEditPetReq) GetTwoDate() string {
	if m != nil {
		return m.TwoDate
	}
	return ""
}

func (m *BatchEditPetReq) GetThreeDate() string {
	if m != nil {
		return m.ThreeDate
	}
	return ""
}

func (m *BatchEditPetReq) GetPlanRecordsId() int32 {
	if m != nil {
		return m.PlanRecordsId
	}
	return 0
}

func (m *BatchEditPetReq) GetPlanRecords() *PlanRecords {
	if m != nil {
		return m.PlanRecords
	}
	return nil
}

type CreatePetImgReq struct {
	//宠物Id
	PetId int32 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//默认头像（第一张鼻纹图片地址）
	DefaultImg string `protobuf:"bytes,2,opt,name=default_img,json=defaultImg,proto3" json:"default_img"`
	//图片集合
	ImgData              []string `protobuf:"bytes,3,rep,name=img_data,json=imgData,proto3" json:"img_data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePetImgReq) Reset()         { *m = CreatePetImgReq{} }
func (m *CreatePetImgReq) String() string { return proto.CompactTextString(m) }
func (*CreatePetImgReq) ProtoMessage()    {}
func (*CreatePetImgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{16}
}

func (m *CreatePetImgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePetImgReq.Unmarshal(m, b)
}
func (m *CreatePetImgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePetImgReq.Marshal(b, m, deterministic)
}
func (m *CreatePetImgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePetImgReq.Merge(m, src)
}
func (m *CreatePetImgReq) XXX_Size() int {
	return xxx_messageInfo_CreatePetImgReq.Size(m)
}
func (m *CreatePetImgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePetImgReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePetImgReq proto.InternalMessageInfo

func (m *CreatePetImgReq) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

func (m *CreatePetImgReq) GetDefaultImg() string {
	if m != nil {
		return m.DefaultImg
	}
	return ""
}

func (m *CreatePetImgReq) GetImgData() []string {
	if m != nil {
		return m.ImgData
	}
	return nil
}

//plan
type PlanRecords struct {
	//宠物Id
	PetId int32 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//首次免疫时间
	MarchentId int32 `protobuf:"varint,2,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//宠物芯片号(需要唯一)
	FirstVaccineDate string `protobuf:"bytes,3,opt,name=first_vaccine_date,json=firstVaccineDate,proto3" json:"first_vaccine_date"`
	//首次免疫使用的品牌
	FirstVaccineBrand string `protobuf:"bytes,4,opt,name=first_vaccine_brand,json=firstVaccineBrand,proto3" json:"first_vaccine_brand"`
	//首次免疫使用的名称
	FirstVaccineName string `protobuf:"bytes,5,opt,name=first_vaccine_name,json=firstVaccineName,proto3" json:"first_vaccine_name"`
	//第二免疫时间
	TwoVaccineDate string `protobuf:"bytes,6,opt,name=two_vaccine_date,json=twoVaccineDate,proto3" json:"two_vaccine_date"`
	//第二免疫使用的品牌
	TwoVaccineBrand string `protobuf:"bytes,7,opt,name=two_vaccine_brand,json=twoVaccineBrand,proto3" json:"two_vaccine_brand"`
	//第二次免疫使用的名称
	TwoVaccineName string `protobuf:"bytes,8,opt,name=two_vaccine_name,json=twoVaccineName,proto3" json:"two_vaccine_name"`
	//第三免疫时间
	ThreeVaccineDate string `protobuf:"bytes,9,opt,name=three_vaccine_date,json=threeVaccineDate,proto3" json:"three_vaccine_date"`
	//第三免疫使用的品牌
	ThreeVaccineBrand string `protobuf:"bytes,10,opt,name=three_vaccine_brand,json=threeVaccineBrand,proto3" json:"three_vaccine_brand"`
	//第三免疫使用的名称
	ThreeVaccineName string `protobuf:"bytes,11,opt,name=three_vaccine_name,json=threeVaccineName,proto3" json:"three_vaccine_name"`
	//免疫计划记录Id
	PlanRecordsId        int32    `protobuf:"varint,12,opt,name=plan_records_id,json=planRecordsId,proto3" json:"plan_records_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlanRecords) Reset()         { *m = PlanRecords{} }
func (m *PlanRecords) String() string { return proto.CompactTextString(m) }
func (*PlanRecords) ProtoMessage()    {}
func (*PlanRecords) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{17}
}

func (m *PlanRecords) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlanRecords.Unmarshal(m, b)
}
func (m *PlanRecords) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlanRecords.Marshal(b, m, deterministic)
}
func (m *PlanRecords) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlanRecords.Merge(m, src)
}
func (m *PlanRecords) XXX_Size() int {
	return xxx_messageInfo_PlanRecords.Size(m)
}
func (m *PlanRecords) XXX_DiscardUnknown() {
	xxx_messageInfo_PlanRecords.DiscardUnknown(m)
}

var xxx_messageInfo_PlanRecords proto.InternalMessageInfo

func (m *PlanRecords) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

func (m *PlanRecords) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *PlanRecords) GetFirstVaccineDate() string {
	if m != nil {
		return m.FirstVaccineDate
	}
	return ""
}

func (m *PlanRecords) GetFirstVaccineBrand() string {
	if m != nil {
		return m.FirstVaccineBrand
	}
	return ""
}

func (m *PlanRecords) GetFirstVaccineName() string {
	if m != nil {
		return m.FirstVaccineName
	}
	return ""
}

func (m *PlanRecords) GetTwoVaccineDate() string {
	if m != nil {
		return m.TwoVaccineDate
	}
	return ""
}

func (m *PlanRecords) GetTwoVaccineBrand() string {
	if m != nil {
		return m.TwoVaccineBrand
	}
	return ""
}

func (m *PlanRecords) GetTwoVaccineName() string {
	if m != nil {
		return m.TwoVaccineName
	}
	return ""
}

func (m *PlanRecords) GetThreeVaccineDate() string {
	if m != nil {
		return m.ThreeVaccineDate
	}
	return ""
}

func (m *PlanRecords) GetThreeVaccineBrand() string {
	if m != nil {
		return m.ThreeVaccineBrand
	}
	return ""
}

func (m *PlanRecords) GetThreeVaccineName() string {
	if m != nil {
		return m.ThreeVaccineName
	}
	return ""
}

func (m *PlanRecords) GetPlanRecordsId() int32 {
	if m != nil {
		return m.PlanRecordsId
	}
	return 0
}

//deliveryRecordReq
type CreateDeliveryRecordReq struct {
	//宠物id
	PetId int32 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id"`
	//商户id
	MarchentId int32 `protobuf:"varint,3,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//客户姓名
	PayName string `protobuf:"bytes,4,opt,name=pay_name,json=payName,proto3" json:"pay_name"`
	//交易金额(单位元，保留整数)
	PayAmount int32 `protobuf:"varint,5,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//客户手机号码
	Mobile string `protobuf:"bytes,6,opt,name=mobile,proto3" json:"mobile"`
	//验证码
	VerificationCode     string   `protobuf:"bytes,7,opt,name=VerificationCode,proto3" json:"VerificationCode"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateDeliveryRecordReq) Reset()         { *m = CreateDeliveryRecordReq{} }
func (m *CreateDeliveryRecordReq) String() string { return proto.CompactTextString(m) }
func (*CreateDeliveryRecordReq) ProtoMessage()    {}
func (*CreateDeliveryRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{18}
}

func (m *CreateDeliveryRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDeliveryRecordReq.Unmarshal(m, b)
}
func (m *CreateDeliveryRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDeliveryRecordReq.Marshal(b, m, deterministic)
}
func (m *CreateDeliveryRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDeliveryRecordReq.Merge(m, src)
}
func (m *CreateDeliveryRecordReq) XXX_Size() int {
	return xxx_messageInfo_CreateDeliveryRecordReq.Size(m)
}
func (m *CreateDeliveryRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDeliveryRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDeliveryRecordReq proto.InternalMessageInfo

func (m *CreateDeliveryRecordReq) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

func (m *CreateDeliveryRecordReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *CreateDeliveryRecordReq) GetPayName() string {
	if m != nil {
		return m.PayName
	}
	return ""
}

func (m *CreateDeliveryRecordReq) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *CreateDeliveryRecordReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CreateDeliveryRecordReq) GetVerificationCode() string {
	if m != nil {
		return m.VerificationCode
	}
	return ""
}

type BaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//数据条数
	DataCount int64 `protobuf:"varint,3,opt,name=data_count,json=dataCount,proto3" json:"data_count"`
	//结果
	Data                 string   `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{19}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetDataCount() int64 {
	if m != nil {
		return m.DataCount
	}
	return 0
}

func (m *BaseResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

// 请求
type MarchentGetByMobileRequest struct {
	// 手机号码
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	// 微信用户的openId
	OpenId               string   `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentGetByMobileRequest) Reset()         { *m = MarchentGetByMobileRequest{} }
func (m *MarchentGetByMobileRequest) String() string { return proto.CompactTextString(m) }
func (*MarchentGetByMobileRequest) ProtoMessage()    {}
func (*MarchentGetByMobileRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{20}
}

func (m *MarchentGetByMobileRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentGetByMobileRequest.Unmarshal(m, b)
}
func (m *MarchentGetByMobileRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentGetByMobileRequest.Marshal(b, m, deterministic)
}
func (m *MarchentGetByMobileRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentGetByMobileRequest.Merge(m, src)
}
func (m *MarchentGetByMobileRequest) XXX_Size() int {
	return xxx_messageInfo_MarchentGetByMobileRequest.Size(m)
}
func (m *MarchentGetByMobileRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentGetByMobileRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentGetByMobileRequest proto.InternalMessageInfo

func (m *MarchentGetByMobileRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *MarchentGetByMobileRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

// 商户数据模型
type MarchentGetByMobileDto struct {
	// 商户id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 商户名称
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 合作状态(0-暂停合作，1-正常合作，2-待审核)
	State int32 `protobuf:"varint,3,opt,name=state,proto3" json:"state"`
	// 已经创建的窝数量
	NestCount            int32    `protobuf:"varint,4,opt,name=nestCount,proto3" json:"nestCount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentGetByMobileDto) Reset()         { *m = MarchentGetByMobileDto{} }
func (m *MarchentGetByMobileDto) String() string { return proto.CompactTextString(m) }
func (*MarchentGetByMobileDto) ProtoMessage()    {}
func (*MarchentGetByMobileDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{21}
}

func (m *MarchentGetByMobileDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentGetByMobileDto.Unmarshal(m, b)
}
func (m *MarchentGetByMobileDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentGetByMobileDto.Marshal(b, m, deterministic)
}
func (m *MarchentGetByMobileDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentGetByMobileDto.Merge(m, src)
}
func (m *MarchentGetByMobileDto) XXX_Size() int {
	return xxx_messageInfo_MarchentGetByMobileDto.Size(m)
}
func (m *MarchentGetByMobileDto) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentGetByMobileDto.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentGetByMobileDto proto.InternalMessageInfo

func (m *MarchentGetByMobileDto) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MarchentGetByMobileDto) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MarchentGetByMobileDto) GetState() int32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *MarchentGetByMobileDto) GetNestCount() int32 {
	if m != nil {
		return m.NestCount
	}
	return 0
}

// 响应
type MarchentGetByMobileResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 查询到的商户列表
	Data                 []*MarchentGetByMobileDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *MarchentGetByMobileResponse) Reset()         { *m = MarchentGetByMobileResponse{} }
func (m *MarchentGetByMobileResponse) String() string { return proto.CompactTextString(m) }
func (*MarchentGetByMobileResponse) ProtoMessage()    {}
func (*MarchentGetByMobileResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{22}
}

func (m *MarchentGetByMobileResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentGetByMobileResponse.Unmarshal(m, b)
}
func (m *MarchentGetByMobileResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentGetByMobileResponse.Marshal(b, m, deterministic)
}
func (m *MarchentGetByMobileResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentGetByMobileResponse.Merge(m, src)
}
func (m *MarchentGetByMobileResponse) XXX_Size() int {
	return xxx_messageInfo_MarchentGetByMobileResponse.Size(m)
}
func (m *MarchentGetByMobileResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentGetByMobileResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentGetByMobileResponse proto.InternalMessageInfo

func (m *MarchentGetByMobileResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MarchentGetByMobileResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MarchentGetByMobileResponse) GetData() []*MarchentGetByMobileDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// 获取商户信息
type MarchentGetInfoRequest struct {
	// 商户id
	MarchentId           int32    `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentGetInfoRequest) Reset()         { *m = MarchentGetInfoRequest{} }
func (m *MarchentGetInfoRequest) String() string { return proto.CompactTextString(m) }
func (*MarchentGetInfoRequest) ProtoMessage()    {}
func (*MarchentGetInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{23}
}

func (m *MarchentGetInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentGetInfoRequest.Unmarshal(m, b)
}
func (m *MarchentGetInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentGetInfoRequest.Marshal(b, m, deterministic)
}
func (m *MarchentGetInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentGetInfoRequest.Merge(m, src)
}
func (m *MarchentGetInfoRequest) XXX_Size() int {
	return xxx_messageInfo_MarchentGetInfoRequest.Size(m)
}
func (m *MarchentGetInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentGetInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentGetInfoRequest proto.InternalMessageInfo

func (m *MarchentGetInfoRequest) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

// 商户详细信息
type MarchentGetInfoDto struct {
	// 名称
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	// 店铺logo
	Logo string `protobuf:"bytes,2,opt,name=logo,proto3" json:"logo"`
	// 店铺地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	// 联系方式
	Telephone string `protobuf:"bytes,4,opt,name=telephone,proto3" json:"telephone"`
	// 店铺介绍
	Remarks              string   `protobuf:"bytes,5,opt,name=remarks,proto3" json:"remarks"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentGetInfoDto) Reset()         { *m = MarchentGetInfoDto{} }
func (m *MarchentGetInfoDto) String() string { return proto.CompactTextString(m) }
func (*MarchentGetInfoDto) ProtoMessage()    {}
func (*MarchentGetInfoDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{24}
}

func (m *MarchentGetInfoDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentGetInfoDto.Unmarshal(m, b)
}
func (m *MarchentGetInfoDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentGetInfoDto.Marshal(b, m, deterministic)
}
func (m *MarchentGetInfoDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentGetInfoDto.Merge(m, src)
}
func (m *MarchentGetInfoDto) XXX_Size() int {
	return xxx_messageInfo_MarchentGetInfoDto.Size(m)
}
func (m *MarchentGetInfoDto) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentGetInfoDto.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentGetInfoDto proto.InternalMessageInfo

func (m *MarchentGetInfoDto) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MarchentGetInfoDto) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *MarchentGetInfoDto) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *MarchentGetInfoDto) GetTelephone() string {
	if m != nil {
		return m.Telephone
	}
	return ""
}

func (m *MarchentGetInfoDto) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

// 获取商户信息返回
type MarchentGetInfoResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 数据
	Data                 *MarchentGetInfoDto `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MarchentGetInfoResponse) Reset()         { *m = MarchentGetInfoResponse{} }
func (m *MarchentGetInfoResponse) String() string { return proto.CompactTextString(m) }
func (*MarchentGetInfoResponse) ProtoMessage()    {}
func (*MarchentGetInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{25}
}

func (m *MarchentGetInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentGetInfoResponse.Unmarshal(m, b)
}
func (m *MarchentGetInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentGetInfoResponse.Marshal(b, m, deterministic)
}
func (m *MarchentGetInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentGetInfoResponse.Merge(m, src)
}
func (m *MarchentGetInfoResponse) XXX_Size() int {
	return xxx_messageInfo_MarchentGetInfoResponse.Size(m)
}
func (m *MarchentGetInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentGetInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentGetInfoResponse proto.InternalMessageInfo

func (m *MarchentGetInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MarchentGetInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MarchentGetInfoResponse) GetData() *MarchentGetInfoDto {
	if m != nil {
		return m.Data
	}
	return nil
}

type MarchentDeliveryRecordGetRequest struct {
	// 商户id
	MarchentId int32 `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	// 分页索引
	PageIndex int32 `protobuf:"varint,2,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 分页大小
	PageSize             int32    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentDeliveryRecordGetRequest) Reset()         { *m = MarchentDeliveryRecordGetRequest{} }
func (m *MarchentDeliveryRecordGetRequest) String() string { return proto.CompactTextString(m) }
func (*MarchentDeliveryRecordGetRequest) ProtoMessage()    {}
func (*MarchentDeliveryRecordGetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{26}
}

func (m *MarchentDeliveryRecordGetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentDeliveryRecordGetRequest.Unmarshal(m, b)
}
func (m *MarchentDeliveryRecordGetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentDeliveryRecordGetRequest.Marshal(b, m, deterministic)
}
func (m *MarchentDeliveryRecordGetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentDeliveryRecordGetRequest.Merge(m, src)
}
func (m *MarchentDeliveryRecordGetRequest) XXX_Size() int {
	return xxx_messageInfo_MarchentDeliveryRecordGetRequest.Size(m)
}
func (m *MarchentDeliveryRecordGetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentDeliveryRecordGetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentDeliveryRecordGetRequest proto.InternalMessageInfo

func (m *MarchentDeliveryRecordGetRequest) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *MarchentDeliveryRecordGetRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *MarchentDeliveryRecordGetRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 商户客户信息数据模型
type MarchentDeliveryRecordDto struct {
	// 宠物出生日期
	PetBirthday string `protobuf:"bytes,1,opt,name=petBirthday,proto3" json:"petBirthday"`
	// 宠物种类
	PetSpecies string `protobuf:"bytes,10,opt,name=petSpecies,proto3" json:"petSpecies"`
	// 宠物品种
	PetBreed string `protobuf:"bytes,2,opt,name=petBreed,proto3" json:"petBreed"`
	// 宠物头像
	PetPortrait string `protobuf:"bytes,3,opt,name=petPortrait,proto3" json:"petPortrait"`
	// 宠物性别 0:公,1母
	PetSex string `protobuf:"bytes,4,opt,name=petSex,proto3" json:"petSex"`
	// 宠物备注
	PetRemarks string `protobuf:"bytes,5,opt,name=petRemarks,proto3" json:"petRemarks"`
	// 客户姓名
	CustomerName string `protobuf:"bytes,6,opt,name=customerName,proto3" json:"customerName"`
	// 客户手机号码
	CustomerMobile string `protobuf:"bytes,7,opt,name=customerMobile,proto3" json:"customerMobile"`
	// 支付金额
	PayMoney float64 `protobuf:"fixed64,8,opt,name=payMoney,proto3" json:"payMoney"`
	// 支付日期
	PayDate              string   `protobuf:"bytes,9,opt,name=payDate,proto3" json:"payDate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentDeliveryRecordDto) Reset()         { *m = MarchentDeliveryRecordDto{} }
func (m *MarchentDeliveryRecordDto) String() string { return proto.CompactTextString(m) }
func (*MarchentDeliveryRecordDto) ProtoMessage()    {}
func (*MarchentDeliveryRecordDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{27}
}

func (m *MarchentDeliveryRecordDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentDeliveryRecordDto.Unmarshal(m, b)
}
func (m *MarchentDeliveryRecordDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentDeliveryRecordDto.Marshal(b, m, deterministic)
}
func (m *MarchentDeliveryRecordDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentDeliveryRecordDto.Merge(m, src)
}
func (m *MarchentDeliveryRecordDto) XXX_Size() int {
	return xxx_messageInfo_MarchentDeliveryRecordDto.Size(m)
}
func (m *MarchentDeliveryRecordDto) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentDeliveryRecordDto.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentDeliveryRecordDto proto.InternalMessageInfo

func (m *MarchentDeliveryRecordDto) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *MarchentDeliveryRecordDto) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *MarchentDeliveryRecordDto) GetPetBreed() string {
	if m != nil {
		return m.PetBreed
	}
	return ""
}

func (m *MarchentDeliveryRecordDto) GetPetPortrait() string {
	if m != nil {
		return m.PetPortrait
	}
	return ""
}

func (m *MarchentDeliveryRecordDto) GetPetSex() string {
	if m != nil {
		return m.PetSex
	}
	return ""
}

func (m *MarchentDeliveryRecordDto) GetPetRemarks() string {
	if m != nil {
		return m.PetRemarks
	}
	return ""
}

func (m *MarchentDeliveryRecordDto) GetCustomerName() string {
	if m != nil {
		return m.CustomerName
	}
	return ""
}

func (m *MarchentDeliveryRecordDto) GetCustomerMobile() string {
	if m != nil {
		return m.CustomerMobile
	}
	return ""
}

func (m *MarchentDeliveryRecordDto) GetPayMoney() float64 {
	if m != nil {
		return m.PayMoney
	}
	return 0
}

func (m *MarchentDeliveryRecordDto) GetPayDate() string {
	if m != nil {
		return m.PayDate
	}
	return ""
}

type MarchentDeliveryRecordGetResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 数据
	Data []*MarchentDeliveryRecordDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总记录条数
	Total                int64    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarchentDeliveryRecordGetResponse) Reset()         { *m = MarchentDeliveryRecordGetResponse{} }
func (m *MarchentDeliveryRecordGetResponse) String() string { return proto.CompactTextString(m) }
func (*MarchentDeliveryRecordGetResponse) ProtoMessage()    {}
func (*MarchentDeliveryRecordGetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{28}
}

func (m *MarchentDeliveryRecordGetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarchentDeliveryRecordGetResponse.Unmarshal(m, b)
}
func (m *MarchentDeliveryRecordGetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarchentDeliveryRecordGetResponse.Marshal(b, m, deterministic)
}
func (m *MarchentDeliveryRecordGetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarchentDeliveryRecordGetResponse.Merge(m, src)
}
func (m *MarchentDeliveryRecordGetResponse) XXX_Size() int {
	return xxx_messageInfo_MarchentDeliveryRecordGetResponse.Size(m)
}
func (m *MarchentDeliveryRecordGetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MarchentDeliveryRecordGetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MarchentDeliveryRecordGetResponse proto.InternalMessageInfo

func (m *MarchentDeliveryRecordGetResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MarchentDeliveryRecordGetResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MarchentDeliveryRecordGetResponse) GetData() []*MarchentDeliveryRecordDto {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *MarchentDeliveryRecordGetResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 调用小程序的api
type MiniAppPostApiRequest struct {
	// 小程序的api名称,参照 https://developers.weixin.qq.com/miniprogram/dev/api-backend/ 例如 auth.code2Session 代表 登录凭证校验
	ApiName string `protobuf:"bytes,1,opt,name=apiName,proto3" json:"apiName"`
	// 调用api需要的参数
	Params               *_struct.Struct `protobuf:"bytes,2,opt,name=params,proto3" json:"params"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MiniAppPostApiRequest) Reset()         { *m = MiniAppPostApiRequest{} }
func (m *MiniAppPostApiRequest) String() string { return proto.CompactTextString(m) }
func (*MiniAppPostApiRequest) ProtoMessage()    {}
func (*MiniAppPostApiRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{29}
}

func (m *MiniAppPostApiRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniAppPostApiRequest.Unmarshal(m, b)
}
func (m *MiniAppPostApiRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniAppPostApiRequest.Marshal(b, m, deterministic)
}
func (m *MiniAppPostApiRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniAppPostApiRequest.Merge(m, src)
}
func (m *MiniAppPostApiRequest) XXX_Size() int {
	return xxx_messageInfo_MiniAppPostApiRequest.Size(m)
}
func (m *MiniAppPostApiRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniAppPostApiRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MiniAppPostApiRequest proto.InternalMessageInfo

func (m *MiniAppPostApiRequest) GetApiName() string {
	if m != nil {
		return m.ApiName
	}
	return ""
}

func (m *MiniAppPostApiRequest) GetParams() *_struct.Struct {
	if m != nil {
		return m.Params
	}
	return nil
}

type MiniAppPostApiResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 微信接口返回的数据接口,参照 https://developers.weixin.qq.com/miniprogram/dev/api-backend/ 对应接口返回数据模型
	Data                 *_struct.Struct `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MiniAppPostApiResponse) Reset()         { *m = MiniAppPostApiResponse{} }
func (m *MiniAppPostApiResponse) String() string { return proto.CompactTextString(m) }
func (*MiniAppPostApiResponse) ProtoMessage()    {}
func (*MiniAppPostApiResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{30}
}

func (m *MiniAppPostApiResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniAppPostApiResponse.Unmarshal(m, b)
}
func (m *MiniAppPostApiResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniAppPostApiResponse.Marshal(b, m, deterministic)
}
func (m *MiniAppPostApiResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniAppPostApiResponse.Merge(m, src)
}
func (m *MiniAppPostApiResponse) XXX_Size() int {
	return xxx_messageInfo_MiniAppPostApiResponse.Size(m)
}
func (m *MiniAppPostApiResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniAppPostApiResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MiniAppPostApiResponse proto.InternalMessageInfo

func (m *MiniAppPostApiResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MiniAppPostApiResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MiniAppPostApiResponse) GetData() *_struct.Struct {
	if m != nil {
		return m.Data
	}
	return nil
}

// 查询未读消息请求
type MessageGetListRequest struct {
	// 商户Id
	MarchentId           int32    `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageGetListRequest) Reset()         { *m = MessageGetListRequest{} }
func (m *MessageGetListRequest) String() string { return proto.CompactTextString(m) }
func (*MessageGetListRequest) ProtoMessage()    {}
func (*MessageGetListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{31}
}

func (m *MessageGetListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageGetListRequest.Unmarshal(m, b)
}
func (m *MessageGetListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageGetListRequest.Marshal(b, m, deterministic)
}
func (m *MessageGetListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageGetListRequest.Merge(m, src)
}
func (m *MessageGetListRequest) XXX_Size() int {
	return xxx_messageInfo_MessageGetListRequest.Size(m)
}
func (m *MessageGetListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageGetListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MessageGetListRequest proto.InternalMessageInfo

func (m *MessageGetListRequest) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

// 未读消息数据模型
type MessageGetListDataDto struct {
	// 窝代码
	NestCode string `protobuf:"bytes,1,opt,name=nestCode,proto3" json:"nestCode"`
	// 标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 窝Id
	NestId int64 `protobuf:"varint,3,opt,name=nestId,proto3" json:"nestId"`
	// 疫苗时间
	CreateDate string `protobuf:"bytes,4,opt,name=createDate,proto3" json:"createDate"`
	// 未读数量
	UnReadCount          int32    `protobuf:"varint,5,opt,name=unReadCount,proto3" json:"unReadCount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageGetListDataDto) Reset()         { *m = MessageGetListDataDto{} }
func (m *MessageGetListDataDto) String() string { return proto.CompactTextString(m) }
func (*MessageGetListDataDto) ProtoMessage()    {}
func (*MessageGetListDataDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{32}
}

func (m *MessageGetListDataDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageGetListDataDto.Unmarshal(m, b)
}
func (m *MessageGetListDataDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageGetListDataDto.Marshal(b, m, deterministic)
}
func (m *MessageGetListDataDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageGetListDataDto.Merge(m, src)
}
func (m *MessageGetListDataDto) XXX_Size() int {
	return xxx_messageInfo_MessageGetListDataDto.Size(m)
}
func (m *MessageGetListDataDto) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageGetListDataDto.DiscardUnknown(m)
}

var xxx_messageInfo_MessageGetListDataDto proto.InternalMessageInfo

func (m *MessageGetListDataDto) GetNestCode() string {
	if m != nil {
		return m.NestCode
	}
	return ""
}

func (m *MessageGetListDataDto) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MessageGetListDataDto) GetNestId() int64 {
	if m != nil {
		return m.NestId
	}
	return 0
}

func (m *MessageGetListDataDto) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

func (m *MessageGetListDataDto) GetUnReadCount() int32 {
	if m != nil {
		return m.UnReadCount
	}
	return 0
}

// 查询未读消息响应
type MessageGetListResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 分页数据
	Data                 []*MessageGetListDataDto `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *MessageGetListResponse) Reset()         { *m = MessageGetListResponse{} }
func (m *MessageGetListResponse) String() string { return proto.CompactTextString(m) }
func (*MessageGetListResponse) ProtoMessage()    {}
func (*MessageGetListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{33}
}

func (m *MessageGetListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageGetListResponse.Unmarshal(m, b)
}
func (m *MessageGetListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageGetListResponse.Marshal(b, m, deterministic)
}
func (m *MessageGetListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageGetListResponse.Merge(m, src)
}
func (m *MessageGetListResponse) XXX_Size() int {
	return xxx_messageInfo_MessageGetListResponse.Size(m)
}
func (m *MessageGetListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageGetListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MessageGetListResponse proto.InternalMessageInfo

func (m *MessageGetListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MessageGetListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MessageGetListResponse) GetData() []*MessageGetListDataDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// 根据消息Id查询消息明细
type MessageGetDetailRequest struct {
	// 窝Id
	NestId               int32    `protobuf:"varint,1,opt,name=nestId,proto3" json:"nestId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageGetDetailRequest) Reset()         { *m = MessageGetDetailRequest{} }
func (m *MessageGetDetailRequest) String() string { return proto.CompactTextString(m) }
func (*MessageGetDetailRequest) ProtoMessage()    {}
func (*MessageGetDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{34}
}

func (m *MessageGetDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageGetDetailRequest.Unmarshal(m, b)
}
func (m *MessageGetDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageGetDetailRequest.Marshal(b, m, deterministic)
}
func (m *MessageGetDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageGetDetailRequest.Merge(m, src)
}
func (m *MessageGetDetailRequest) XXX_Size() int {
	return xxx_messageInfo_MessageGetDetailRequest.Size(m)
}
func (m *MessageGetDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageGetDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MessageGetDetailRequest proto.InternalMessageInfo

func (m *MessageGetDetailRequest) GetNestId() int32 {
	if m != nil {
		return m.NestId
	}
	return 0
}

// 消息明细dto
type MessageGetDetailDto struct {
	// 宠物Id
	PetId int32 `protobuf:"varint,1,opt,name=petId,proto3" json:"petId"`
	// 宠物头像
	PetPortrait string `protobuf:"bytes,2,opt,name=petPortrait,proto3" json:"petPortrait"`
	// 宠物性别 0:公,1母
	PetSex int32 `protobuf:"varint,3,opt,name=petSex,proto3" json:"petSex"`
	// 宠物备注
	PetRemarks string `protobuf:"bytes,4,opt,name=petRemarks,proto3" json:"petRemarks"`
	// 疫苗时间
	CreateDate string `protobuf:"bytes,5,opt,name=createDate,proto3" json:"createDate"`
	// 是否已读 0 未读 1 已读
	IsRead               int32    `protobuf:"varint,6,opt,name=isRead,proto3" json:"isRead"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MessageGetDetailDto) Reset()         { *m = MessageGetDetailDto{} }
func (m *MessageGetDetailDto) String() string { return proto.CompactTextString(m) }
func (*MessageGetDetailDto) ProtoMessage()    {}
func (*MessageGetDetailDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{35}
}

func (m *MessageGetDetailDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageGetDetailDto.Unmarshal(m, b)
}
func (m *MessageGetDetailDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageGetDetailDto.Marshal(b, m, deterministic)
}
func (m *MessageGetDetailDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageGetDetailDto.Merge(m, src)
}
func (m *MessageGetDetailDto) XXX_Size() int {
	return xxx_messageInfo_MessageGetDetailDto.Size(m)
}
func (m *MessageGetDetailDto) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageGetDetailDto.DiscardUnknown(m)
}

var xxx_messageInfo_MessageGetDetailDto proto.InternalMessageInfo

func (m *MessageGetDetailDto) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

func (m *MessageGetDetailDto) GetPetPortrait() string {
	if m != nil {
		return m.PetPortrait
	}
	return ""
}

func (m *MessageGetDetailDto) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *MessageGetDetailDto) GetPetRemarks() string {
	if m != nil {
		return m.PetRemarks
	}
	return ""
}

func (m *MessageGetDetailDto) GetCreateDate() string {
	if m != nil {
		return m.CreateDate
	}
	return ""
}

func (m *MessageGetDetailDto) GetIsRead() int32 {
	if m != nil {
		return m.IsRead
	}
	return 0
}

// 根据消息Id查询消息明细
type MessageGetDetailResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 数据模型
	Data                 []*MessageGetDetailDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *MessageGetDetailResponse) Reset()         { *m = MessageGetDetailResponse{} }
func (m *MessageGetDetailResponse) String() string { return proto.CompactTextString(m) }
func (*MessageGetDetailResponse) ProtoMessage()    {}
func (*MessageGetDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{36}
}

func (m *MessageGetDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageGetDetailResponse.Unmarshal(m, b)
}
func (m *MessageGetDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageGetDetailResponse.Marshal(b, m, deterministic)
}
func (m *MessageGetDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageGetDetailResponse.Merge(m, src)
}
func (m *MessageGetDetailResponse) XXX_Size() int {
	return xxx_messageInfo_MessageGetDetailResponse.Size(m)
}
func (m *MessageGetDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageGetDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MessageGetDetailResponse proto.InternalMessageInfo

func (m *MessageGetDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *MessageGetDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *MessageGetDetailResponse) GetData() []*MessageGetDetailDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// 更新消息状态
type AllMessageReadRequest struct {
	// 商户Id
	MarchentId           int32    `protobuf:"varint,2,opt,name=marchentId,proto3" json:"marchentId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AllMessageReadRequest) Reset()         { *m = AllMessageReadRequest{} }
func (m *AllMessageReadRequest) String() string { return proto.CompactTextString(m) }
func (*AllMessageReadRequest) ProtoMessage()    {}
func (*AllMessageReadRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{37}
}

func (m *AllMessageReadRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllMessageReadRequest.Unmarshal(m, b)
}
func (m *AllMessageReadRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllMessageReadRequest.Marshal(b, m, deterministic)
}
func (m *AllMessageReadRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllMessageReadRequest.Merge(m, src)
}
func (m *AllMessageReadRequest) XXX_Size() int {
	return xxx_messageInfo_AllMessageReadRequest.Size(m)
}
func (m *AllMessageReadRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AllMessageReadRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AllMessageReadRequest proto.InternalMessageInfo

func (m *AllMessageReadRequest) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

// 推送微信订阅消息数据请求
type SubscribeMessageSendRequest struct {
	// 商户Id，不传递则发送所有的商户信息
	MarchentId int32 `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	// 窝id，不传递则发送所有窝
	NestId int32 `protobuf:"varint,2,opt,name=nestId,proto3" json:"nestId"`
	// 宠物id，不传递则发送所有宠物
	PetId                int32    `protobuf:"varint,3,opt,name=petId,proto3" json:"petId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeMessageSendRequest) Reset()         { *m = SubscribeMessageSendRequest{} }
func (m *SubscribeMessageSendRequest) String() string { return proto.CompactTextString(m) }
func (*SubscribeMessageSendRequest) ProtoMessage()    {}
func (*SubscribeMessageSendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{38}
}

func (m *SubscribeMessageSendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeMessageSendRequest.Unmarshal(m, b)
}
func (m *SubscribeMessageSendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeMessageSendRequest.Marshal(b, m, deterministic)
}
func (m *SubscribeMessageSendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeMessageSendRequest.Merge(m, src)
}
func (m *SubscribeMessageSendRequest) XXX_Size() int {
	return xxx_messageInfo_SubscribeMessageSendRequest.Size(m)
}
func (m *SubscribeMessageSendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeMessageSendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeMessageSendRequest proto.InternalMessageInfo

func (m *SubscribeMessageSendRequest) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *SubscribeMessageSendRequest) GetNestId() int32 {
	if m != nil {
		return m.NestId
	}
	return 0
}

func (m *SubscribeMessageSendRequest) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

// 推送微信订阅消息数据响应
type SubscribeMessageSendResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总消息数量
	Total int64 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 发送成功数量
	Success              int64    `protobuf:"varint,4,opt,name=success,proto3" json:"success"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeMessageSendResponse) Reset()         { *m = SubscribeMessageSendResponse{} }
func (m *SubscribeMessageSendResponse) String() string { return proto.CompactTextString(m) }
func (*SubscribeMessageSendResponse) ProtoMessage()    {}
func (*SubscribeMessageSendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{39}
}

func (m *SubscribeMessageSendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeMessageSendResponse.Unmarshal(m, b)
}
func (m *SubscribeMessageSendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeMessageSendResponse.Marshal(b, m, deterministic)
}
func (m *SubscribeMessageSendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeMessageSendResponse.Merge(m, src)
}
func (m *SubscribeMessageSendResponse) XXX_Size() int {
	return xxx_messageInfo_SubscribeMessageSendResponse.Size(m)
}
func (m *SubscribeMessageSendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeMessageSendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeMessageSendResponse proto.InternalMessageInfo

func (m *SubscribeMessageSendResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SubscribeMessageSendResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SubscribeMessageSendResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *SubscribeMessageSendResponse) GetSuccess() int64 {
	if m != nil {
		return m.Success
	}
	return 0
}

// 推送微信订阅消息数据请求
type ObsoleteMessageDeleteRequest struct {
	// 商户Id，不传递则发送所有的商户信息
	MarchentId int32 `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	// 窝id，不传递则发送所有窝
	NestId int32 `protobuf:"varint,2,opt,name=nestId,proto3" json:"nestId"`
	// 宠物id，不传递则发送所有宠物
	PetId                int32    `protobuf:"varint,3,opt,name=petId,proto3" json:"petId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ObsoleteMessageDeleteRequest) Reset()         { *m = ObsoleteMessageDeleteRequest{} }
func (m *ObsoleteMessageDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*ObsoleteMessageDeleteRequest) ProtoMessage()    {}
func (*ObsoleteMessageDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{40}
}

func (m *ObsoleteMessageDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ObsoleteMessageDeleteRequest.Unmarshal(m, b)
}
func (m *ObsoleteMessageDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ObsoleteMessageDeleteRequest.Marshal(b, m, deterministic)
}
func (m *ObsoleteMessageDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ObsoleteMessageDeleteRequest.Merge(m, src)
}
func (m *ObsoleteMessageDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_ObsoleteMessageDeleteRequest.Size(m)
}
func (m *ObsoleteMessageDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ObsoleteMessageDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ObsoleteMessageDeleteRequest proto.InternalMessageInfo

func (m *ObsoleteMessageDeleteRequest) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *ObsoleteMessageDeleteRequest) GetNestId() int32 {
	if m != nil {
		return m.NestId
	}
	return 0
}

func (m *ObsoleteMessageDeleteRequest) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

// 推送微信订阅消息数据响应
type ObsoleteMessageDeleteResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总消息数量
	Total int64 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 发送成功数量
	Success              int64    `protobuf:"varint,4,opt,name=success,proto3" json:"success"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ObsoleteMessageDeleteResponse) Reset()         { *m = ObsoleteMessageDeleteResponse{} }
func (m *ObsoleteMessageDeleteResponse) String() string { return proto.CompactTextString(m) }
func (*ObsoleteMessageDeleteResponse) ProtoMessage()    {}
func (*ObsoleteMessageDeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{41}
}

func (m *ObsoleteMessageDeleteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ObsoleteMessageDeleteResponse.Unmarshal(m, b)
}
func (m *ObsoleteMessageDeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ObsoleteMessageDeleteResponse.Marshal(b, m, deterministic)
}
func (m *ObsoleteMessageDeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ObsoleteMessageDeleteResponse.Merge(m, src)
}
func (m *ObsoleteMessageDeleteResponse) XXX_Size() int {
	return xxx_messageInfo_ObsoleteMessageDeleteResponse.Size(m)
}
func (m *ObsoleteMessageDeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ObsoleteMessageDeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ObsoleteMessageDeleteResponse proto.InternalMessageInfo

func (m *ObsoleteMessageDeleteResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ObsoleteMessageDeleteResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ObsoleteMessageDeleteResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ObsoleteMessageDeleteResponse) GetSuccess() int64 {
	if m != nil {
		return m.Success
	}
	return 0
}

type GetVaccineBrandReq struct {
	//宠物种类 例如(猫,犬)
	PetSpecies           string   `protobuf:"bytes,1,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVaccineBrandReq) Reset()         { *m = GetVaccineBrandReq{} }
func (m *GetVaccineBrandReq) String() string { return proto.CompactTextString(m) }
func (*GetVaccineBrandReq) ProtoMessage()    {}
func (*GetVaccineBrandReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{42}
}

func (m *GetVaccineBrandReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVaccineBrandReq.Unmarshal(m, b)
}
func (m *GetVaccineBrandReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVaccineBrandReq.Marshal(b, m, deterministic)
}
func (m *GetVaccineBrandReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVaccineBrandReq.Merge(m, src)
}
func (m *GetVaccineBrandReq) XXX_Size() int {
	return xxx_messageInfo_GetVaccineBrandReq.Size(m)
}
func (m *GetVaccineBrandReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVaccineBrandReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVaccineBrandReq proto.InternalMessageInfo

func (m *GetVaccineBrandReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

type GetVaccineNameReq struct {
	//宠物种类 例如(猫,犬)
	PetSpecies string `protobuf:"bytes,1,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	//疫苗品牌
	VaccineBrand         string   `protobuf:"bytes,2,opt,name=vaccine_brand,json=vaccineBrand,proto3" json:"vaccine_brand"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVaccineNameReq) Reset()         { *m = GetVaccineNameReq{} }
func (m *GetVaccineNameReq) String() string { return proto.CompactTextString(m) }
func (*GetVaccineNameReq) ProtoMessage()    {}
func (*GetVaccineNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{43}
}

func (m *GetVaccineNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVaccineNameReq.Unmarshal(m, b)
}
func (m *GetVaccineNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVaccineNameReq.Marshal(b, m, deterministic)
}
func (m *GetVaccineNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVaccineNameReq.Merge(m, src)
}
func (m *GetVaccineNameReq) XXX_Size() int {
	return xxx_messageInfo_GetVaccineNameReq.Size(m)
}
func (m *GetVaccineNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVaccineNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVaccineNameReq proto.InternalMessageInfo

func (m *GetVaccineNameReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *GetVaccineNameReq) GetVaccineBrand() string {
	if m != nil {
		return m.VaccineBrand
	}
	return ""
}

type GetPetBreedReq struct {
	PetDictParentIds     []string `protobuf:"bytes,1,rep,name=pet_dict_parent_ids,json=petDictParentIds,proto3" json:"pet_dict_parent_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetBreedReq) Reset()         { *m = GetPetBreedReq{} }
func (m *GetPetBreedReq) String() string { return proto.CompactTextString(m) }
func (*GetPetBreedReq) ProtoMessage()    {}
func (*GetPetBreedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{44}
}

func (m *GetPetBreedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetBreedReq.Unmarshal(m, b)
}
func (m *GetPetBreedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetBreedReq.Marshal(b, m, deterministic)
}
func (m *GetPetBreedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetBreedReq.Merge(m, src)
}
func (m *GetPetBreedReq) XXX_Size() int {
	return xxx_messageInfo_GetPetBreedReq.Size(m)
}
func (m *GetPetBreedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetBreedReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetBreedReq proto.InternalMessageInfo

func (m *GetPetBreedReq) GetPetDictParentIds() []string {
	if m != nil {
		return m.PetDictParentIds
	}
	return nil
}

type GetPetNCountReq struct {
	//宠物种类 例如(猫,犬)
	PetSpecies string `protobuf:"bytes,1,opt,name=pet_species,json=petSpecies,proto3" json:"pet_species"`
	//宠物品种 例如(阿富汗猎犬)
	PetBreed             string   `protobuf:"bytes,2,opt,name=pet_breed,json=petBreed,proto3" json:"pet_breed"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetNCountReq) Reset()         { *m = GetPetNCountReq{} }
func (m *GetPetNCountReq) String() string { return proto.CompactTextString(m) }
func (*GetPetNCountReq) ProtoMessage()    {}
func (*GetPetNCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{45}
}

func (m *GetPetNCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetNCountReq.Unmarshal(m, b)
}
func (m *GetPetNCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetNCountReq.Marshal(b, m, deterministic)
}
func (m *GetPetNCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetNCountReq.Merge(m, src)
}
func (m *GetPetNCountReq) XXX_Size() int {
	return xxx_messageInfo_GetPetNCountReq.Size(m)
}
func (m *GetPetNCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetNCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetNCountReq proto.InternalMessageInfo

func (m *GetPetNCountReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

func (m *GetPetNCountReq) GetPetBreed() string {
	if m != nil {
		return m.PetBreed
	}
	return ""
}

type PayCustomerListReq struct {
	// 商户Id，不传递则发送所有的商户信息
	MarchentId int32 `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	// 交付人手机号码 or 客户姓名
	SearchKey string `protobuf:"bytes,2,opt,name=searchKey,proto3" json:"searchKey"`
	//交付时间 例如传值（2020-02-01）
	PayTime string `protobuf:"bytes,3,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//分页、每页个数
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//分页、页码
	PageIndex            int32    `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayCustomerListReq) Reset()         { *m = PayCustomerListReq{} }
func (m *PayCustomerListReq) String() string { return proto.CompactTextString(m) }
func (*PayCustomerListReq) ProtoMessage()    {}
func (*PayCustomerListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{46}
}

func (m *PayCustomerListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayCustomerListReq.Unmarshal(m, b)
}
func (m *PayCustomerListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayCustomerListReq.Marshal(b, m, deterministic)
}
func (m *PayCustomerListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayCustomerListReq.Merge(m, src)
}
func (m *PayCustomerListReq) XXX_Size() int {
	return xxx_messageInfo_PayCustomerListReq.Size(m)
}
func (m *PayCustomerListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PayCustomerListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PayCustomerListReq proto.InternalMessageInfo

func (m *PayCustomerListReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *PayCustomerListReq) GetSearchKey() string {
	if m != nil {
		return m.SearchKey
	}
	return ""
}

func (m *PayCustomerListReq) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *PayCustomerListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *PayCustomerListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

type ReferralRecordsReq struct {
	// 商户Id，不传递则发送所有的商户信息
	MarchentId int32 `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	// 交付人手机号码
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	//宠物Id
	PetId                int32    `protobuf:"varint,3,opt,name=petId,proto3" json:"petId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReferralRecordsReq) Reset()         { *m = ReferralRecordsReq{} }
func (m *ReferralRecordsReq) String() string { return proto.CompactTextString(m) }
func (*ReferralRecordsReq) ProtoMessage()    {}
func (*ReferralRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{47}
}

func (m *ReferralRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReferralRecordsReq.Unmarshal(m, b)
}
func (m *ReferralRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReferralRecordsReq.Marshal(b, m, deterministic)
}
func (m *ReferralRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReferralRecordsReq.Merge(m, src)
}
func (m *ReferralRecordsReq) XXX_Size() int {
	return xxx_messageInfo_ReferralRecordsReq.Size(m)
}
func (m *ReferralRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReferralRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReferralRecordsReq proto.InternalMessageInfo

func (m *ReferralRecordsReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *ReferralRecordsReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *ReferralRecordsReq) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

type CustomerDataReq struct {
	// 商户Id，不传递则发送所有的商户信息
	MarchentId           int32    `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomerDataReq) Reset()         { *m = CustomerDataReq{} }
func (m *CustomerDataReq) String() string { return proto.CompactTextString(m) }
func (*CustomerDataReq) ProtoMessage()    {}
func (*CustomerDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{48}
}

func (m *CustomerDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomerDataReq.Unmarshal(m, b)
}
func (m *CustomerDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomerDataReq.Marshal(b, m, deterministic)
}
func (m *CustomerDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomerDataReq.Merge(m, src)
}
func (m *CustomerDataReq) XXX_Size() int {
	return xxx_messageInfo_CustomerDataReq.Size(m)
}
func (m *CustomerDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomerDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_CustomerDataReq proto.InternalMessageInfo

func (m *CustomerDataReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

type GetScrmPetsReq struct {
	// 客户手机号码
	Mobile               string   `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScrmPetsReq) Reset()         { *m = GetScrmPetsReq{} }
func (m *GetScrmPetsReq) String() string { return proto.CompactTextString(m) }
func (*GetScrmPetsReq) ProtoMessage()    {}
func (*GetScrmPetsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{49}
}

func (m *GetScrmPetsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScrmPetsReq.Unmarshal(m, b)
}
func (m *GetScrmPetsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScrmPetsReq.Marshal(b, m, deterministic)
}
func (m *GetScrmPetsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScrmPetsReq.Merge(m, src)
}
func (m *GetScrmPetsReq) XXX_Size() int {
	return xxx_messageInfo_GetScrmPetsReq.Size(m)
}
func (m *GetScrmPetsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScrmPetsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScrmPetsReq proto.InternalMessageInfo

func (m *GetScrmPetsReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

type GetCommonPetColorRes struct {
	Data                 []string `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCommonPetColorRes) Reset()         { *m = GetCommonPetColorRes{} }
func (m *GetCommonPetColorRes) String() string { return proto.CompactTextString(m) }
func (*GetCommonPetColorRes) ProtoMessage()    {}
func (*GetCommonPetColorRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{50}
}

func (m *GetCommonPetColorRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommonPetColorRes.Unmarshal(m, b)
}
func (m *GetCommonPetColorRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommonPetColorRes.Marshal(b, m, deterministic)
}
func (m *GetCommonPetColorRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommonPetColorRes.Merge(m, src)
}
func (m *GetCommonPetColorRes) XXX_Size() int {
	return xxx_messageInfo_GetCommonPetColorRes.Size(m)
}
func (m *GetCommonPetColorRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommonPetColorRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommonPetColorRes proto.InternalMessageInfo

func (m *GetCommonPetColorRes) GetData() []string {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetPetReferralRecordsReq struct {
	//商户id
	MarchentId int32 `protobuf:"varint,1,opt,name=marchent_id,json=marchentId,proto3" json:"marchent_id"`
	//宠物id
	ScrmPetId string `protobuf:"bytes,2,opt,name=scrm_pet_id,json=scrmPetId,proto3" json:"scrm_pet_id"`
	//手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile"`
	//页码
	PageNo int32 `protobuf:"varint,4,opt,name=page_no,json=pageNo,proto3" json:"page_no"`
	//页量
	PageSize             int32    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetReferralRecordsReq) Reset()         { *m = GetPetReferralRecordsReq{} }
func (m *GetPetReferralRecordsReq) String() string { return proto.CompactTextString(m) }
func (*GetPetReferralRecordsReq) ProtoMessage()    {}
func (*GetPetReferralRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{51}
}

func (m *GetPetReferralRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetReferralRecordsReq.Unmarshal(m, b)
}
func (m *GetPetReferralRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetReferralRecordsReq.Marshal(b, m, deterministic)
}
func (m *GetPetReferralRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetReferralRecordsReq.Merge(m, src)
}
func (m *GetPetReferralRecordsReq) XXX_Size() int {
	return xxx_messageInfo_GetPetReferralRecordsReq.Size(m)
}
func (m *GetPetReferralRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetReferralRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetReferralRecordsReq proto.InternalMessageInfo

func (m *GetPetReferralRecordsReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *GetPetReferralRecordsReq) GetScrmPetId() string {
	if m != nil {
		return m.ScrmPetId
	}
	return ""
}

func (m *GetPetReferralRecordsReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *GetPetReferralRecordsReq) GetPageNo() int32 {
	if m != nil {
		return m.PageNo
	}
	return 0
}

func (m *GetPetReferralRecordsReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetPetReferralRecordsRes struct {
	Data                 []*GetPetReferralRecordsResUnit `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetPetReferralRecordsRes) Reset()         { *m = GetPetReferralRecordsRes{} }
func (m *GetPetReferralRecordsRes) String() string { return proto.CompactTextString(m) }
func (*GetPetReferralRecordsRes) ProtoMessage()    {}
func (*GetPetReferralRecordsRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{52}
}

func (m *GetPetReferralRecordsRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetReferralRecordsRes.Unmarshal(m, b)
}
func (m *GetPetReferralRecordsRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetReferralRecordsRes.Marshal(b, m, deterministic)
}
func (m *GetPetReferralRecordsRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetReferralRecordsRes.Merge(m, src)
}
func (m *GetPetReferralRecordsRes) XXX_Size() int {
	return xxx_messageInfo_GetPetReferralRecordsRes.Size(m)
}
func (m *GetPetReferralRecordsRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetReferralRecordsRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetReferralRecordsRes proto.InternalMessageInfo

func (m *GetPetReferralRecordsRes) GetData() []*GetPetReferralRecordsResUnit {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetPetReferralRecordsResUnit struct {
	//外部转诊编码
	ApplyCode string `protobuf:"bytes,1,opt,name=apply_code,json=applyCode,proto3" json:"apply_code"`
	//病例结束日期
	ApplyEndTime string `protobuf:"bytes,2,opt,name=apply_end_time,json=applyEndTime,proto3" json:"apply_end_time"`
	//接收分院编码
	BrandCode string `protobuf:"bytes,3,opt,name=brand_code,json=brandCode,proto3" json:"brand_code"`
	//接收分院名称
	ClinicName string `protobuf:"bytes,4,opt,name=clinic_name,json=clinicName,proto3" json:"clinic_name"`
	//接收医生姓名
	ApplyPhysicianName string `protobuf:"bytes,5,opt,name=apply_physician_name,json=applyPhysicianName,proto3" json:"apply_physician_name"`
	//转诊机构ID
	TargetId string `protobuf:"bytes,6,opt,name=target_id,json=targetId,proto3" json:"target_id"`
	//外部转诊机构名称
	TargetOrgname string `protobuf:"bytes,7,opt,name=target_orgname,json=targetOrgname,proto3" json:"target_orgname"`
	//客户姓名
	CusName string `protobuf:"bytes,8,opt,name=cus_name,json=cusName,proto3" json:"cus_name"`
	//手机号码
	Cellphone string `protobuf:"bytes,9,opt,name=cellphone,proto3" json:"cellphone"`
	//宠物ID
	PetLogicid int32 `protobuf:"varint,10,opt,name=pet_logicid,json=petLogicid,proto3" json:"pet_logicid"`
	//宠物姓名
	PetName string `protobuf:"bytes,11,opt,name=pet_name,json=petName,proto3" json:"pet_name"`
	//宠物种类
	PetKindof string `protobuf:"bytes,12,opt,name=pet_kindof,json=petKindof,proto3" json:"pet_kindof"`
	//宠物品种
	PetVariety string `protobuf:"bytes,13,opt,name=pet_variety,json=petVariety,proto3" json:"pet_variety"`
	//宠物出生日期
	PetBirthday string `protobuf:"bytes,14,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday"`
	//宠物年龄
	PetAge string `protobuf:"bytes,15,opt,name=pet_age,json=petAge,proto3" json:"pet_age"`
	//病情诊断
	MainSymptom string `protobuf:"bytes,16,opt,name=main_symptom,json=mainSymptom,proto3" json:"main_symptom"`
	//结算金额
	ActualAmount         float64  `protobuf:"fixed64,17,opt,name=actual_amount,json=actualAmount,proto3" json:"actual_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetReferralRecordsResUnit) Reset()         { *m = GetPetReferralRecordsResUnit{} }
func (m *GetPetReferralRecordsResUnit) String() string { return proto.CompactTextString(m) }
func (*GetPetReferralRecordsResUnit) ProtoMessage()    {}
func (*GetPetReferralRecordsResUnit) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{52, 0}
}

func (m *GetPetReferralRecordsResUnit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetReferralRecordsResUnit.Unmarshal(m, b)
}
func (m *GetPetReferralRecordsResUnit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetReferralRecordsResUnit.Marshal(b, m, deterministic)
}
func (m *GetPetReferralRecordsResUnit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetReferralRecordsResUnit.Merge(m, src)
}
func (m *GetPetReferralRecordsResUnit) XXX_Size() int {
	return xxx_messageInfo_GetPetReferralRecordsResUnit.Size(m)
}
func (m *GetPetReferralRecordsResUnit) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetReferralRecordsResUnit.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetReferralRecordsResUnit proto.InternalMessageInfo

func (m *GetPetReferralRecordsResUnit) GetApplyCode() string {
	if m != nil {
		return m.ApplyCode
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetApplyEndTime() string {
	if m != nil {
		return m.ApplyEndTime
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetBrandCode() string {
	if m != nil {
		return m.BrandCode
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetClinicName() string {
	if m != nil {
		return m.ClinicName
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetApplyPhysicianName() string {
	if m != nil {
		return m.ApplyPhysicianName
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetTargetId() string {
	if m != nil {
		return m.TargetId
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetTargetOrgname() string {
	if m != nil {
		return m.TargetOrgname
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetCusName() string {
	if m != nil {
		return m.CusName
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetCellphone() string {
	if m != nil {
		return m.Cellphone
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetPetLogicid() int32 {
	if m != nil {
		return m.PetLogicid
	}
	return 0
}

func (m *GetPetReferralRecordsResUnit) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetPetKindof() string {
	if m != nil {
		return m.PetKindof
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetPetVariety() string {
	if m != nil {
		return m.PetVariety
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetPetAge() string {
	if m != nil {
		return m.PetAge
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetMainSymptom() string {
	if m != nil {
		return m.MainSymptom
	}
	return ""
}

func (m *GetPetReferralRecordsResUnit) GetActualAmount() float64 {
	if m != nil {
		return m.ActualAmount
	}
	return 0
}

type AddCustomerAndPetsReq struct {
	// 商户Id，不传递则发送所有的商户信息
	MarchentId int32 `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	// 客户手机号码
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	//交付人姓名
	PayName string `protobuf:"bytes,3,opt,name=payName,proto3" json:"payName"`
	//用户scrm userId
	UserId string `protobuf:"bytes,4,opt,name=userId,proto3" json:"userId"`
	//Scrm petId
	ScrmPetId string `protobuf:"bytes,5,opt,name=scrmPetId,proto3" json:"scrmPetId"`
	//宠物名称
	PetName string `protobuf:"bytes,6,opt,name=petName,proto3" json:"petName"`
	//宠物生日
	PetBirthday string `protobuf:"bytes,7,opt,name=petBirthday,proto3" json:"petBirthday"`
	//宠物性别
	PetSex int32 `protobuf:"varint,8,opt,name=petSex,proto3" json:"petSex"`
	//宠物种类Id-1未知 1000猫 1001狗 1002其他
	PetKindof int32 `protobuf:"varint,9,opt,name=petKindof,proto3" json:"petKindof"`
	//宠物品种Id
	PetVariety int32 `protobuf:"varint,10,opt,name=petVariety,proto3" json:"petVariety"`
	//头像
	PetAvatar string `protobuf:"bytes,11,opt,name=petAvatar,proto3" json:"petAvatar"`
	//新宠petId
	PetId int32 `protobuf:"varint,12,opt,name=petId,proto3" json:"petId"`
	//商户名称
	ShopName string `protobuf:"bytes,13,opt,name=shopName,proto3" json:"shopName"`
	//芯片号
	ChipCode string `protobuf:"bytes,14,opt,name=chipCode,proto3" json:"chipCode"`
	//花色
	PetFlower string `protobuf:"bytes,15,opt,name=petFlower,proto3" json:"petFlower"`
	//支付金额
	PayAmount int32 `protobuf:"varint,16,opt,name=payAmount,proto3" json:"payAmount"`
	//交付日期
	DeliveryDate string `protobuf:"bytes,17,opt,name=deliveryDate,proto3" json:"deliveryDate"`
	//宠物品种
	PetBreed string `protobuf:"bytes,18,opt,name=petBreed,proto3" json:"petBreed"`
	//宠物种类
	PetSpecies           string   `protobuf:"bytes,19,opt,name=petSpecies,proto3" json:"petSpecies"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCustomerAndPetsReq) Reset()         { *m = AddCustomerAndPetsReq{} }
func (m *AddCustomerAndPetsReq) String() string { return proto.CompactTextString(m) }
func (*AddCustomerAndPetsReq) ProtoMessage()    {}
func (*AddCustomerAndPetsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{53}
}

func (m *AddCustomerAndPetsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCustomerAndPetsReq.Unmarshal(m, b)
}
func (m *AddCustomerAndPetsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCustomerAndPetsReq.Marshal(b, m, deterministic)
}
func (m *AddCustomerAndPetsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCustomerAndPetsReq.Merge(m, src)
}
func (m *AddCustomerAndPetsReq) XXX_Size() int {
	return xxx_messageInfo_AddCustomerAndPetsReq.Size(m)
}
func (m *AddCustomerAndPetsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCustomerAndPetsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCustomerAndPetsReq proto.InternalMessageInfo

func (m *AddCustomerAndPetsReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *AddCustomerAndPetsReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetPayName() string {
	if m != nil {
		return m.PayName
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetScrmPetId() string {
	if m != nil {
		return m.ScrmPetId
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetPetBirthday() string {
	if m != nil {
		return m.PetBirthday
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetPetSex() int32 {
	if m != nil {
		return m.PetSex
	}
	return 0
}

func (m *AddCustomerAndPetsReq) GetPetKindof() int32 {
	if m != nil {
		return m.PetKindof
	}
	return 0
}

func (m *AddCustomerAndPetsReq) GetPetVariety() int32 {
	if m != nil {
		return m.PetVariety
	}
	return 0
}

func (m *AddCustomerAndPetsReq) GetPetAvatar() string {
	if m != nil {
		return m.PetAvatar
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetPetId() int32 {
	if m != nil {
		return m.PetId
	}
	return 0
}

func (m *AddCustomerAndPetsReq) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetChipCode() string {
	if m != nil {
		return m.ChipCode
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetPetFlower() string {
	if m != nil {
		return m.PetFlower
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *AddCustomerAndPetsReq) GetDeliveryDate() string {
	if m != nil {
		return m.DeliveryDate
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetPetBreed() string {
	if m != nil {
		return m.PetBreed
	}
	return ""
}

func (m *AddCustomerAndPetsReq) GetPetSpecies() string {
	if m != nil {
		return m.PetSpecies
	}
	return ""
}

type GetCustomerDetailsReq struct {
	//交付表Id
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCustomerDetailsReq) Reset()         { *m = GetCustomerDetailsReq{} }
func (m *GetCustomerDetailsReq) String() string { return proto.CompactTextString(m) }
func (*GetCustomerDetailsReq) ProtoMessage()    {}
func (*GetCustomerDetailsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{54}
}

func (m *GetCustomerDetailsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomerDetailsReq.Unmarshal(m, b)
}
func (m *GetCustomerDetailsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomerDetailsReq.Marshal(b, m, deterministic)
}
func (m *GetCustomerDetailsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomerDetailsReq.Merge(m, src)
}
func (m *GetCustomerDetailsReq) XXX_Size() int {
	return xxx_messageInfo_GetCustomerDetailsReq.Size(m)
}
func (m *GetCustomerDetailsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomerDetailsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomerDetailsReq proto.InternalMessageInfo

func (m *GetCustomerDetailsReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type CustomerConfirmReq struct {
	//交付表Id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 客户手机号码
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	//验证码
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	//宠物名称
	PetName              string   `protobuf:"bytes,4,opt,name=petName,proto3" json:"petName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomerConfirmReq) Reset()         { *m = CustomerConfirmReq{} }
func (m *CustomerConfirmReq) String() string { return proto.CompactTextString(m) }
func (*CustomerConfirmReq) ProtoMessage()    {}
func (*CustomerConfirmReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{55}
}

func (m *CustomerConfirmReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomerConfirmReq.Unmarshal(m, b)
}
func (m *CustomerConfirmReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomerConfirmReq.Marshal(b, m, deterministic)
}
func (m *CustomerConfirmReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomerConfirmReq.Merge(m, src)
}
func (m *CustomerConfirmReq) XXX_Size() int {
	return xxx_messageInfo_CustomerConfirmReq.Size(m)
}
func (m *CustomerConfirmReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomerConfirmReq.DiscardUnknown(m)
}

var xxx_messageInfo_CustomerConfirmReq proto.InternalMessageInfo

func (m *CustomerConfirmReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CustomerConfirmReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CustomerConfirmReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *CustomerConfirmReq) GetPetName() string {
	if m != nil {
		return m.PetName
	}
	return ""
}

type AddCustomerReq struct {
	// 商户Id，不传递则发送所有的商户信息
	MarchentId int32 `protobuf:"varint,1,opt,name=marchentId,proto3" json:"marchentId"`
	// 客户手机号码
	Mobile               string   `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCustomerReq) Reset()         { *m = AddCustomerReq{} }
func (m *AddCustomerReq) String() string { return proto.CompactTextString(m) }
func (*AddCustomerReq) ProtoMessage()    {}
func (*AddCustomerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{56}
}

func (m *AddCustomerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCustomerReq.Unmarshal(m, b)
}
func (m *AddCustomerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCustomerReq.Marshal(b, m, deterministic)
}
func (m *AddCustomerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCustomerReq.Merge(m, src)
}
func (m *AddCustomerReq) XXX_Size() int {
	return xxx_messageInfo_AddCustomerReq.Size(m)
}
func (m *AddCustomerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCustomerReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCustomerReq proto.InternalMessageInfo

func (m *AddCustomerReq) GetMarchentId() int32 {
	if m != nil {
		return m.MarchentId
	}
	return 0
}

func (m *AddCustomerReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

type ExistCustomerReq struct {
	// 客户手机号码
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile"`
	//客户姓名
	PayName              string   `protobuf:"bytes,2,opt,name=payName,proto3" json:"payName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExistCustomerReq) Reset()         { *m = ExistCustomerReq{} }
func (m *ExistCustomerReq) String() string { return proto.CompactTextString(m) }
func (*ExistCustomerReq) ProtoMessage()    {}
func (*ExistCustomerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_8677034dafa80624, []int{57}
}

func (m *ExistCustomerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExistCustomerReq.Unmarshal(m, b)
}
func (m *ExistCustomerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExistCustomerReq.Marshal(b, m, deterministic)
}
func (m *ExistCustomerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExistCustomerReq.Merge(m, src)
}
func (m *ExistCustomerReq) XXX_Size() int {
	return xxx_messageInfo_ExistCustomerReq.Size(m)
}
func (m *ExistCustomerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExistCustomerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExistCustomerReq proto.InternalMessageInfo

func (m *ExistCustomerReq) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *ExistCustomerReq) GetPayName() string {
	if m != nil {
		return m.PayName
	}
	return ""
}

func init() {
	proto.RegisterType((*CreateNestReq)(nil), "pm.CreateNestReq")
	proto.RegisterType((*EditNestNameReq)(nil), "pm.EditNestNameReq")
	proto.RegisterType((*GetNestReq)(nil), "pm.GetNestReq")
	proto.RegisterType((*GetNestListReq)(nil), "pm.GetNestListReq")
	proto.RegisterType((*CreatePlanReq)(nil), "pm.CreatePlanReq")
	proto.RegisterType((*EditPlanReq)(nil), "pm.EditPlanReq")
	proto.RegisterType((*CreatePlanRecordReq)(nil), "pm.CreatePlanRecordReq")
	proto.RegisterType((*EditPlanRecordReq)(nil), "pm.EditPlanRecordReq")
	proto.RegisterType((*GetPlanReq)(nil), "pm.GetPlanReq")
	proto.RegisterType((*GetPlanRecordReq)(nil), "pm.GetPlanRecordReq")
	proto.RegisterType((*GetPlanListReq)(nil), "pm.GetPlanListReq")
	proto.RegisterType((*GetPlanRecordListReq)(nil), "pm.GetPlanRecordListReq")
	proto.RegisterType((*GetPetListReq)(nil), "pm.GetPetListReq")
	proto.RegisterType((*GetPetReq)(nil), "pm.GetPetReq")
	proto.RegisterType((*EditPetReq)(nil), "pm.EditPetReq")
	proto.RegisterType((*BatchEditPetReq)(nil), "pm.BatchEditPetReq")
	proto.RegisterType((*CreatePetImgReq)(nil), "pm.CreatePetImgReq")
	proto.RegisterType((*PlanRecords)(nil), "pm.PlanRecords")
	proto.RegisterType((*CreateDeliveryRecordReq)(nil), "pm.CreateDeliveryRecordReq")
	proto.RegisterType((*BaseResponse)(nil), "pm.BaseResponse")
	proto.RegisterType((*MarchentGetByMobileRequest)(nil), "pm.MarchentGetByMobileRequest")
	proto.RegisterType((*MarchentGetByMobileDto)(nil), "pm.MarchentGetByMobileDto")
	proto.RegisterType((*MarchentGetByMobileResponse)(nil), "pm.MarchentGetByMobileResponse")
	proto.RegisterType((*MarchentGetInfoRequest)(nil), "pm.MarchentGetInfoRequest")
	proto.RegisterType((*MarchentGetInfoDto)(nil), "pm.MarchentGetInfoDto")
	proto.RegisterType((*MarchentGetInfoResponse)(nil), "pm.MarchentGetInfoResponse")
	proto.RegisterType((*MarchentDeliveryRecordGetRequest)(nil), "pm.MarchentDeliveryRecordGetRequest")
	proto.RegisterType((*MarchentDeliveryRecordDto)(nil), "pm.MarchentDeliveryRecordDto")
	proto.RegisterType((*MarchentDeliveryRecordGetResponse)(nil), "pm.MarchentDeliveryRecordGetResponse")
	proto.RegisterType((*MiniAppPostApiRequest)(nil), "pm.MiniAppPostApiRequest")
	proto.RegisterType((*MiniAppPostApiResponse)(nil), "pm.MiniAppPostApiResponse")
	proto.RegisterType((*MessageGetListRequest)(nil), "pm.MessageGetListRequest")
	proto.RegisterType((*MessageGetListDataDto)(nil), "pm.MessageGetListDataDto")
	proto.RegisterType((*MessageGetListResponse)(nil), "pm.MessageGetListResponse")
	proto.RegisterType((*MessageGetDetailRequest)(nil), "pm.MessageGetDetailRequest")
	proto.RegisterType((*MessageGetDetailDto)(nil), "pm.MessageGetDetailDto")
	proto.RegisterType((*MessageGetDetailResponse)(nil), "pm.MessageGetDetailResponse")
	proto.RegisterType((*AllMessageReadRequest)(nil), "pm.AllMessageReadRequest")
	proto.RegisterType((*SubscribeMessageSendRequest)(nil), "pm.SubscribeMessageSendRequest")
	proto.RegisterType((*SubscribeMessageSendResponse)(nil), "pm.SubscribeMessageSendResponse")
	proto.RegisterType((*ObsoleteMessageDeleteRequest)(nil), "pm.ObsoleteMessageDeleteRequest")
	proto.RegisterType((*ObsoleteMessageDeleteResponse)(nil), "pm.ObsoleteMessageDeleteResponse")
	proto.RegisterType((*GetVaccineBrandReq)(nil), "pm.GetVaccineBrandReq")
	proto.RegisterType((*GetVaccineNameReq)(nil), "pm.GetVaccineNameReq")
	proto.RegisterType((*GetPetBreedReq)(nil), "pm.GetPetBreedReq")
	proto.RegisterType((*GetPetNCountReq)(nil), "pm.GetPetNCountReq")
	proto.RegisterType((*PayCustomerListReq)(nil), "pm.PayCustomerListReq")
	proto.RegisterType((*ReferralRecordsReq)(nil), "pm.ReferralRecordsReq")
	proto.RegisterType((*CustomerDataReq)(nil), "pm.CustomerDataReq")
	proto.RegisterType((*GetScrmPetsReq)(nil), "pm.GetScrmPetsReq")
	proto.RegisterType((*GetCommonPetColorRes)(nil), "pm.GetCommonPetColorRes")
	proto.RegisterType((*GetPetReferralRecordsReq)(nil), "pm.GetPetReferralRecordsReq")
	proto.RegisterType((*GetPetReferralRecordsRes)(nil), "pm.GetPetReferralRecordsRes")
	proto.RegisterType((*GetPetReferralRecordsResUnit)(nil), "pm.GetPetReferralRecordsRes.unit")
	proto.RegisterType((*AddCustomerAndPetsReq)(nil), "pm.AddCustomerAndPetsReq")
	proto.RegisterType((*GetCustomerDetailsReq)(nil), "pm.GetCustomerDetailsReq")
	proto.RegisterType((*CustomerConfirmReq)(nil), "pm.CustomerConfirmReq")
	proto.RegisterType((*AddCustomerReq)(nil), "pm.AddCustomerReq")
	proto.RegisterType((*ExistCustomerReq)(nil), "pm.ExistCustomerReq")
}

func init() { proto.RegisterFile("pm/petmillionsapplets.proto", fileDescriptor_8677034dafa80624) }

var fileDescriptor_8677034dafa80624 = []byte{
	// 3226 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0x49, 0x6f, 0x1c, 0xc7,
	0x15, 0xc6, 0xac, 0x24, 0xdf, 0x0c, 0x87, 0x64, 0x51, 0x22, 0x47, 0x23, 0x6a, 0x6b, 0x2f, 0x51,
	0xe4, 0x98, 0x8a, 0x64, 0x18, 0x76, 0x62, 0xd8, 0x06, 0x45, 0x2a, 0xcc, 0xc4, 0x26, 0x4d, 0x0c,
	0x03, 0x05, 0x01, 0x02, 0x0c, 0x8a, 0xdd, 0xc5, 0x61, 0xc7, 0xbd, 0xa9, 0xbb, 0x86, 0xd2, 0x38,
	0xce, 0x31, 0x97, 0x1c, 0x0c, 0x1f, 0x83, 0x5c, 0x73, 0xf2, 0x4f, 0x48, 0x80, 0x9c, 0xf2, 0x03,
	0x72, 0xca, 0x39, 0x97, 0xfc, 0x8d, 0x00, 0x41, 0x6d, 0xdd, 0x55, 0xbd, 0x8c, 0x29, 0x7a, 0x41,
	0x90, 0xf8, 0xd6, 0xf5, 0x6a, 0x7b, 0xcb, 0x57, 0xaf, 0xde, 0x7b, 0xd5, 0x70, 0x3d, 0xf2, 0xef,
	0x47, 0x84, 0xfa, 0xae, 0xe7, 0xb9, 0x61, 0x90, 0xe0, 0x28, 0xf2, 0x08, 0x4d, 0xb6, 0xa3, 0x38,
	0xa4, 0x21, 0xaa, 0x47, 0xfe, 0x60, 0x6b, 0x12, 0x86, 0x13, 0x8f, 0xdc, 0xe7, 0x94, 0x93, 0xe9,
	0xe9, 0xfd, 0x84, 0xc6, 0x53, 0x9b, 0x8a, 0x11, 0x83, 0xeb, 0xf9, 0x5e, 0xe2, 0x47, 0x74, 0x26,
	0x3a, 0xad, 0xbf, 0x35, 0x60, 0x79, 0x37, 0x26, 0x98, 0x92, 0x43, 0x92, 0xd0, 0x11, 0x79, 0x8a,
	0xae, 0xc3, 0x52, 0x40, 0x12, 0x3a, 0x0e, 0xb0, 0x4f, 0xfa, 0xf5, 0xdb, 0xb5, 0xbb, 0x4b, 0xa3,
	0x45, 0x46, 0x38, 0xc4, 0x3e, 0x49, 0x3b, 0xed, 0xd0, 0x21, 0xfd, 0x46, 0xd6, 0xb9, 0x1b, 0x3a,
	0x04, 0xdd, 0x81, 0x6e, 0x44, 0xe8, 0xf8, 0xc4, 0x8d, 0xe9, 0x99, 0x83, 0x67, 0xfd, 0x26, 0xef,
	0xef, 0x44, 0x84, 0x3e, 0x92, 0x24, 0x74, 0x0b, 0x58, 0x73, 0x9c, 0x44, 0xc4, 0x76, 0x49, 0xd2,
	0x6f, 0xf1, 0x11, 0x10, 0x11, 0x7a, 0x2c, 0x28, 0x6c, 0x03, 0xbe, 0x46, 0x4c, 0x88, 0xd3, 0x6f,
	0x8b, 0x0d, 0xd8, 0x02, 0xac, 0xcd, 0x66, 0xfb, 0xd8, 0x23, 0xe3, 0x60, 0xea, 0x9f, 0x90, 0xb8,
	0xbf, 0x70, 0xbb, 0x76, 0xb7, 0x35, 0x02, 0x46, 0x3a, 0xe4, 0x14, 0xf4, 0x12, 0x2c, 0x9f, 0x12,
	0x7d, 0xc8, 0x22, 0x1f, 0xd2, 0x15, 0x44, 0x39, 0x88, 0xaf, 0x12, 0xdb, 0x67, 0x24, 0xa0, 0x63,
	0xd7, 0xe9, 0x2f, 0xa9, 0x55, 0x04, 0x69, 0xe8, 0xa0, 0x4d, 0x58, 0x88, 0x3c, 0x1c, 0xb0, 0xce,
	0x65, 0xde, 0xd9, 0x66, 0xcd, 0xa1, 0x83, 0x6e, 0x00, 0x9c, 0xba, 0x71, 0x42, 0xc7, 0x0e, 0xa6,
	0xa4, 0x0f, 0x9c, 0xbb, 0x25, 0x4e, 0xd9, 0xc3, 0x94, 0xa0, 0x6b, 0xb0, 0x48, 0x9f, 0x85, 0xa2,
	0xb3, 0xc3, 0x3b, 0x17, 0xe8, 0xb3, 0x90, 0x77, 0xdd, 0x00, 0xa0, 0x67, 0x31, 0x21, 0xa2, 0xb3,
	0x2b, 0x66, 0x72, 0x0a, 0xef, 0x96, 0x6a, 0x39, 0xc7, 0xb1, 0x4b, 0xe8, 0xac, 0xbf, 0x22, 0x58,
	0x8a, 0x08, 0x7d, 0x22, 0x28, 0x6c, 0x3e, 0x1b, 0xf0, 0xb1, 0x1b, 0x38, 0xe1, 0x69, 0xbf, 0xc7,
	0xfb, 0x99, 0xa2, 0x3e, 0xe0, 0x04, 0xeb, 0x3d, 0x58, 0x79, 0xec, 0xb8, 0xf4, 0x50, 0x9a, 0x89,
	0x99, 0xb1, 0x07, 0x75, 0xd7, 0xe9, 0xd7, 0xf8, 0xc8, 0xba, 0xeb, 0xcc, 0x35, 0xab, 0xb5, 0x05,
	0xb0, 0x4f, 0xa8, 0x42, 0x40, 0x6e, 0xaa, 0xf5, 0xfb, 0x1a, 0xf4, 0x64, 0xf7, 0x87, 0x6e, 0x09,
	0x48, 0x6a, 0x39, 0x90, 0x30, 0x66, 0xf1, 0x84, 0x8c, 0xdd, 0xc0, 0x21, 0xcf, 0xf9, 0x5e, 0x8c,
	0x59, 0x3c, 0x21, 0x43, 0x46, 0xe0, 0x26, 0x66, 0xdd, 0x89, 0xfb, 0x89, 0xc0, 0x50, 0x6b, 0xb4,
	0xc8, 0x08, 0xc7, 0xee, 0x27, 0x24, 0x6f, 0x9c, 0x66, 0xde, 0x38, 0xd6, 0x17, 0x4d, 0x05, 0xd8,
	0x23, 0x0f, 0x07, 0x8c, 0x97, 0x1c, 0xa6, 0x6a, 0x05, 0x4c, 0xe5, 0xd6, 0xac, 0x17, 0x0c, 0xce,
	0x38, 0x62, 0x06, 0xe7, 0xd2, 0x48, 0x54, 0x33, 0x02, 0x97, 0x66, 0x1b, 0xd6, 0x85, 0xd1, 0xcf,
	0xb1, 0x6d, 0xbb, 0x01, 0x19, 0x9f, 0xc4, 0x38, 0x70, 0x24, 0xb8, 0xd7, 0x78, 0xd7, 0x13, 0xd1,
	0xf3, 0x88, 0x75, 0xa0, 0x1f, 0x00, 0x32, 0xc7, 0xf3, 0x55, 0x05, 0xd2, 0x57, 0xf5, 0xe1, 0xe6,
	0xea, 0x6e, 0x40, 0x49, 0x7c, 0x8e, 0xbd, 0xb1, 0x83, 0x67, 0x09, 0x47, 0x7e, 0x4b, 0xae, 0x3e,
	0x94, 0x3d, 0x7b, 0x78, 0x96, 0xa0, 0x7b, 0xb0, 0xc6, 0x30, 0x66, 0xf2, 0xb2, 0xc0, 0x17, 0x5f,
	0xa1, 0xcf, 0x42, 0x83, 0x93, 0xbb, 0xb0, 0xaa, 0x8f, 0xe5, 0x7c, 0x2c, 0xf2, 0xa1, 0xbd, 0x6c,
	0x28, 0xe7, 0x42, 0xae, 0x6a, 0xf2, 0x20, 0x0e, 0x06, 0x5b, 0xd5, 0xe0, 0x60, 0x1b, 0xd6, 0x05,
	0x94, 0x4d, 0x1e, 0xc4, 0x69, 0x58, 0xe3, 0x5d, 0x79, 0x7d, 0x98, 0xe3, 0x39, 0x1f, 0xe2, 0x7c,
	0xac, 0xea, 0xc3, 0x39, 0x27, 0x85, 0xd1, 0x9c, 0x95, 0x2e, 0x67, 0xc5, 0x18, 0xcd, 0x79, 0x31,
	0x8f, 0xc5, 0x72, 0xfe, 0x58, 0xfc, 0xa1, 0x09, 0x1d, 0x76, 0x2e, 0xbe, 0x43, 0xca, 0xff, 0x3d,
	0x52, 0x84, 0x4f, 0x5b, 0x4e, 0x7d, 0xda, 0x67, 0x4d, 0x58, 0xd7, 0xdd, 0x88, 0x1d, 0xc6, 0xce,
	0x85, 0x20, 0x52, 0x30, 0x1a, 0xf7, 0xe8, 0xf5, 0xa2, 0xd1, 0xb8, 0x63, 0xaf, 0x80, 0x44, 0xe3,
	0xc5, 0x20, 0xd1, 0xac, 0x80, 0x44, 0xce, 0x6c, 0x9c, 0x93, 0x76, 0xde, 0x6c, 0x9c, 0x8f, 0x6f,
	0x06, 0x0c, 0x25, 0x26, 0x48, 0xef, 0xc5, 0x9c, 0x09, 0x84, 0x2e, 0xca, 0xe0, 0xd0, 0x79, 0x31,
	0x38, 0x74, 0x2b, 0xe0, 0x90, 0x3b, 0xba, 0xbd, 0xc2, 0xd1, 0x35, 0x7d, 0xc5, 0x4a, 0xde, 0x57,
	0xfc, 0xbb, 0x01, 0x6b, 0x99, 0xaf, 0xf8, 0x0e, 0x0e, 0xff, 0xeb, 0x70, 0x10, 0x0e, 0x61, 0x25,
	0x75, 0x08, 0xaf, 0xf0, 0x10, 0x48, 0xdd, 0x14, 0x5a, 0x08, 0x58, 0xd3, 0x43, 0x40, 0xeb, 0xc7,
	0xb0, 0x9a, 0x0e, 0x53, 0x20, 0x79, 0x15, 0x56, 0xf8, 0xe0, 0x98, 0x53, 0x92, 0x6c, 0xd2, 0x72,
	0x94, 0x8e, 0x4b, 0x86, 0x8e, 0xf5, 0x94, 0x87, 0x51, 0x6c, 0xae, 0x0a, 0xa3, 0x72, 0x5c, 0xd6,
	0x0a, 0x5c, 0xe6, 0xf0, 0x57, 0x2f, 0xe0, 0xcf, 0x44, 0x75, 0x23, 0x8f, 0xea, 0x67, 0x70, 0xc5,
	0x60, 0xf7, 0x5b, 0xdb, 0xf8, 0x2e, 0x2c, 0xb3, 0x8d, 0x49, 0x1a, 0x31, 0x6e, 0xc2, 0x02, 0x8f,
	0x18, 0x33, 0x8d, 0xb2, 0xe6, 0xd0, 0xb1, 0x2c, 0x58, 0x12, 0x23, 0xd9, 0xa8, 0xab, 0xd0, 0x66,
	0xab, 0xa6, 0x83, 0x5a, 0x11, 0x61, 0x63, 0x3e, 0xaf, 0x03, 0xf0, 0xc3, 0x39, 0x6f, 0x14, 0xdf,
	0x62, 0x6c, 0x87, 0xd3, 0x80, 0x4a, 0x7e, 0xdb, 0xc1, 0x2e, 0x6b, 0xb1, 0xc0, 0x9c, 0x8d, 0xd7,
	0x92, 0x96, 0x85, 0x88, 0x88, 0x9c, 0xc5, 0x0c, 0xe9, 0x9b, 0xf3, 0x42, 0xfa, 0xd6, 0xbc, 0x90,
	0xbe, 0x9d, 0x0f, 0xe9, 0xfb, 0xb0, 0x10, 0x13, 0x1f, 0xc7, 0x1f, 0x27, 0xf2, 0x60, 0xa9, 0x26,
	0x7a, 0x08, 0x5d, 0x1d, 0x2e, 0xfc, 0x30, 0x75, 0x1e, 0xae, 0x6c, 0x47, 0xfe, 0x76, 0x66, 0xa8,
	0x64, 0xd4, 0xd1, 0xc0, 0x83, 0x56, 0xa1, 0x91, 0x90, 0xe7, 0xf2, 0xa2, 0x65, 0x9f, 0xd6, 0x3f,
	0x6b, 0xb0, 0xf2, 0x08, 0x53, 0xfb, 0x4c, 0xd3, 0x4b, 0x95, 0x8e, 0x73, 0x52, 0xd6, 0xe7, 0x49,
	0xd9, 0x98, 0x27, 0x65, 0x2b, 0x2f, 0x65, 0x09, 0xf4, 0xdb, 0x25, 0xd0, 0xbf, 0x8c, 0xcc, 0x96,
	0x03, 0x2b, 0xf2, 0x86, 0x26, 0x74, 0xe8, 0x4f, 0xe6, 0x18, 0xfe, 0x16, 0x74, 0x1c, 0x72, 0x8a,
	0xa7, 0x1e, 0x1d, 0xbb, 0xfe, 0x44, 0x81, 0x55, 0x92, 0x86, 0xfe, 0x84, 0x09, 0xe8, 0xfa, 0x13,
	0x26, 0x03, 0xee, 0x37, 0x6e, 0x37, 0x98, 0x80, 0xae, 0x3f, 0xd9, 0xc3, 0x14, 0x5b, 0xbf, 0x6b,
	0x42, 0x47, 0x63, 0x61, 0xce, 0x16, 0xf3, 0x23, 0xc3, 0xf2, 0x8b, 0xa0, 0xf1, 0x62, 0x17, 0xc1,
	0xd7, 0x14, 0x2a, 0xfe, 0x77, 0x5e, 0x04, 0x4b, 0x2f, 0x76, 0x11, 0x7c, 0x4d, 0x61, 0x62, 0x09,
	0x42, 0xbb, 0x65, 0xce, 0xf9, 0xef, 0x35, 0xd8, 0x14, 0x70, 0xdb, 0x23, 0x9e, 0x7b, 0x4e, 0xe2,
	0x59, 0xe6, 0xe0, 0x33, 0x4c, 0xd4, 0xe7, 0x60, 0xa2, 0x51, 0xc0, 0x04, 0xf3, 0x3b, 0x78, 0xa6,
	0x5f, 0xda, 0x0b, 0x11, 0x9e, 0x65, 0x39, 0xf2, 0x6c, 0x8c, 0x7d, 0xee, 0xae, 0x5a, 0x2a, 0x47,
	0x9e, 0xed, 0x70, 0x02, 0xda, 0x80, 0xb6, 0x1f, 0x9e, 0xb8, 0x9e, 0xb2, 0x9b, 0x6c, 0xa1, 0x7b,
	0xb0, 0xfa, 0x84, 0xc4, 0xee, 0xa9, 0x6b, 0x63, 0xea, 0x86, 0x01, 0x73, 0x61, 0xd2, 0x5c, 0x05,
	0xba, 0x15, 0x42, 0xf7, 0x11, 0x4e, 0xc8, 0x88, 0x24, 0x51, 0x18, 0x24, 0x04, 0x21, 0x68, 0x72,
	0x0f, 0x28, 0x70, 0xcd, 0xbf, 0x99, 0x97, 0xf2, 0x49, 0x92, 0xe0, 0x89, 0xf2, 0x0a, 0xaa, 0xc9,
	0x18, 0x64, 0xc7, 0x45, 0xfa, 0x53, 0x26, 0x5b, 0x63, 0xb4, 0xc4, 0x28, 0xc2, 0xa5, 0x22, 0x68,
	0xf2, 0xd3, 0x24, 0xc4, 0xe2, 0xdf, 0xd6, 0x87, 0x30, 0x38, 0x90, 0xc2, 0xef, 0x13, 0xfa, 0x68,
	0x76, 0xc0, 0x79, 0x1e, 0x91, 0xa7, 0x53, 0x92, 0xe8, 0x22, 0xd5, 0x0c, 0x91, 0x36, 0xa0, 0x1d,
	0x46, 0x24, 0x18, 0x3a, 0xca, 0x69, 0x8b, 0x96, 0x15, 0xc3, 0x46, 0xc9, 0x6a, 0x7b, 0x34, 0x2c,
	0x94, 0x36, 0xae, 0x40, 0x8b, 0xba, 0xd4, 0x53, 0x22, 0x88, 0x06, 0xa3, 0x26, 0x54, 0x9d, 0xc1,
	0xd6, 0x48, 0x34, 0xd0, 0x96, 0x28, 0x5c, 0x70, 0x21, 0x64, 0x75, 0x21, 0x23, 0x58, 0xbf, 0x81,
	0xeb, 0xa5, 0x12, 0x5c, 0x4a, 0x83, 0xdb, 0x52, 0x45, 0xcc, 0xe1, 0x74, 0x1e, 0x0e, 0x98, 0xaf,
	0x2b, 0x17, 0x48, 0xaa, 0xef, 0x6d, 0x43, 0xe0, 0x61, 0x70, 0x1a, 0x2a, 0xd5, 0xdd, 0x04, 0x0d,
	0x55, 0xc5, 0xcb, 0xda, 0xfa, 0xbc, 0x06, 0x28, 0x37, 0x95, 0xe9, 0x29, 0xd5, 0x4b, 0x4d, 0xd7,
	0x0b, 0x82, 0xa6, 0x17, 0x4e, 0x42, 0xc9, 0x2d, 0xff, 0x66, 0x42, 0x60, 0xc7, 0x89, 0x49, 0x92,
	0x28, 0xff, 0x2f, 0x9b, 0x4c, 0x5f, 0x94, 0x78, 0x24, 0x3a, 0x0b, 0x83, 0xf4, 0x7a, 0x4c, 0x09,
	0xfa, 0x25, 0xd7, 0x32, 0x2e, 0x39, 0x2b, 0x81, 0xcd, 0x82, 0x30, 0x97, 0xd2, 0xe2, 0xbd, 0x54,
	0x8b, 0xec, 0xc6, 0xd8, 0xc8, 0x69, 0x51, 0x8a, 0x2a, 0x35, 0xf8, 0x29, 0xdc, 0x56, 0x7d, 0xe6,
	0x21, 0xde, 0xe7, 0xf7, 0xe3, 0x45, 0x74, 0xc9, 0x04, 0x4e, 0x4b, 0x55, 0xc5, 0xda, 0xd5, 0x00,
	0xd2, 0x52, 0x55, 0xbe, 0x74, 0x65, 0xfd, 0xa3, 0x0e, 0xd7, 0xca, 0xb7, 0x67, 0xc6, 0xb8, 0x0d,
	0x7a, 0x21, 0x54, 0x9a, 0xc4, 0xa8, 0x8d, 0xde, 0x04, 0x2d, 0xbe, 0x92, 0xee, 0x4f, 0x8f, 0xb8,
	0x06, 0x90, 0x56, 0x42, 0x55, 0x01, 0x2f, 0xad, 0x8c, 0x8a, 0xd5, 0x8f, 0xc2, 0x98, 0xc6, 0xd8,
	0xa5, 0xd2, 0x88, 0x3a, 0x89, 0x1d, 0x33, 0xb6, 0x16, 0x79, 0x2e, 0xad, 0x28, 0x5b, 0x72, 0xd7,
	0x91, 0x61, 0x45, 0x8d, 0x82, 0x2c, 0xe8, 0xda, 0xd3, 0x84, 0x86, 0x3e, 0x89, 0x99, 0xe3, 0x92,
	0xfe, 0xc8, 0xa0, 0xa1, 0x57, 0xa1, 0xa7, 0xda, 0x02, 0xd4, 0xd2, 0x27, 0xe5, 0xa8, 0x42, 0x7b,
	0xb3, 0x83, 0x30, 0x20, 0x33, 0x7e, 0x73, 0xd4, 0x46, 0x69, 0x9b, 0x21, 0x20, 0xc2, 0xb3, 0xbd,
	0xec, 0xa2, 0x50, 0x4d, 0xeb, 0x8f, 0x35, 0xb8, 0x33, 0xc7, 0xac, 0x97, 0x42, 0xd5, 0x03, 0xe3,
	0x6c, 0xde, 0xd0, 0x51, 0x55, 0x30, 0x9d, 0x00, 0x17, 0x3f, 0x4d, 0x21, 0xc5, 0x1e, 0xd7, 0x5f,
	0x63, 0x24, 0x1a, 0xd6, 0x09, 0x5c, 0x3d, 0x70, 0x03, 0x77, 0x27, 0x8a, 0x8e, 0xc2, 0x84, 0xee,
	0x44, 0xae, 0xc2, 0x19, 0x3b, 0x52, 0x91, 0x7b, 0x98, 0xd5, 0x47, 0x55, 0x13, 0xdd, 0x87, 0x76,
	0x84, 0x63, 0xec, 0x8b, 0xa8, 0xba, 0xf3, 0x70, 0x73, 0x5b, 0x14, 0xe8, 0xb7, 0x55, 0x81, 0x7e,
	0xfb, 0x98, 0x97, 0xef, 0x47, 0x72, 0x98, 0x95, 0xc0, 0x46, 0x7e, 0x8f, 0x4b, 0x09, 0xfd, 0x9a,
	0x71, 0x94, 0x2a, 0xb7, 0x15, 0x67, 0xe9, 0x2d, 0xb8, 0x7a, 0x20, 0xe6, 0xed, 0xa7, 0x41, 0xfc,
	0x85, 0x9c, 0xd1, 0x9f, 0x6a, 0xf9, 0x99, 0x2c, 0xce, 0x62, 0x47, 0x60, 0x00, 0xe9, 0x5b, 0x81,
	0x5e, 0x33, 0xe6, 0x71, 0x78, 0xb9, 0x0f, 0xdf, 0x00, 0x19, 0xc1, 0xca, 0x0b, 0x48, 0xc5, 0xb3,
	0x37, 0x01, 0x6c, 0x71, 0x57, 0x67, 0x51, 0xbb, 0x46, 0x61, 0xc7, 0x61, 0x1a, 0x8c, 0x08, 0x76,
	0x76, 0xb5, 0xeb, 0x55, 0x27, 0x59, 0x53, 0xd8, 0xc8, 0x8b, 0x77, 0x29, 0x9d, 0xbe, 0x9e, 0xde,
	0x83, 0x0c, 0x48, 0xd7, 0x38, 0x90, 0xca, 0x84, 0x97, 0x5a, 0x7d, 0x00, 0x9b, 0x59, 0xf7, 0x1e,
	0xa1, 0xd8, 0xf5, 0xb4, 0xfb, 0x51, 0xca, 0x6a, 0xe6, 0x47, 0x7f, 0xad, 0xc1, 0x7a, 0x7e, 0x8e,
	0xf4, 0xee, 0x3c, 0x0c, 0x31, 0xe3, 0xd4, 0x9c, 0x23, 0xa8, 0xcf, 0x73, 0x04, 0x0d, 0x99, 0xd9,
	0x96, 0x39, 0x82, 0x66, 0xc1, 0x11, 0x98, 0x3a, 0x6f, 0x15, 0x74, 0xbe, 0x01, 0x6d, 0x37, 0x61,
	0x0a, 0x96, 0x19, 0x80, 0x6c, 0x59, 0x53, 0xe8, 0x17, 0x45, 0xfe, 0x8a, 0xf8, 0x6d, 0x70, 0xfc,
	0x1a, 0xba, 0x4e, 0x15, 0x93, 0xe1, 0x77, 0xc7, 0xf3, 0x64, 0x3f, 0x63, 0xa4, 0x1c, 0xbf, 0x85,
	0x40, 0xde, 0xfa, 0x18, 0xae, 0x1f, 0x4f, 0x4f, 0x12, 0x3b, 0x76, 0x4f, 0x88, 0x9c, 0x7e, 0x4c,
	0x02, 0xe7, 0xa2, 0xf7, 0x47, 0x66, 0xc6, 0xba, 0x91, 0x82, 0xa5, 0xe6, 0x6a, 0xe8, 0x89, 0xed,
	0xa7, 0xb0, 0x55, 0xbe, 0xd9, 0xa5, 0x14, 0x94, 0xba, 0xa8, 0x86, 0xe6, 0xa2, 0xd8, 0xf8, 0x64,
	0x6a, 0xdb, 0xec, 0x72, 0x17, 0xae, 0x4b, 0x35, 0x2d, 0x0f, 0xb6, 0x3e, 0x3a, 0x49, 0x42, 0x8f,
	0x50, 0xb5, 0xf9, 0x1e, 0x61, 0x8d, 0x6f, 0x46, 0xd6, 0xdf, 0xc2, 0x8d, 0x8a, 0xdd, 0xbe, 0x15,
	0x61, 0xdf, 0x04, 0xb4, 0x4f, 0x8c, 0xac, 0xea, 0x22, 0x05, 0x3e, 0xeb, 0x97, 0xb0, 0x96, 0x4d,
	0x53, 0x8f, 0x6b, 0x5f, 0x5a, 0x16, 0x7c, 0x09, 0x96, 0xcd, 0x6c, 0x46, 0x30, 0xdf, 0x3d, 0xd7,
	0xb6, 0xb7, 0xde, 0x17, 0xf5, 0x20, 0x79, 0x87, 0xb3, 0x75, 0x5f, 0x87, 0x75, 0xb6, 0xae, 0xe3,
	0xda, 0x74, 0x1c, 0xe1, 0x58, 0x24, 0x16, 0x6c, 0x7d, 0x96, 0xb2, 0xae, 0x46, 0x84, 0xee, 0xb9,
	0x36, 0x3d, 0xe2, 0x1d, 0x43, 0x27, 0xb1, 0x3e, 0x82, 0x15, 0xb1, 0xc0, 0x21, 0x77, 0x6a, 0x17,
	0xe2, 0xcc, 0x78, 0x60, 0xcd, 0x85, 0x11, 0xd6, 0x17, 0x35, 0x40, 0x47, 0x78, 0xb6, 0x2b, 0xaf,
	0x6d, 0x55, 0xbb, 0xb9, 0x40, 0xcc, 0x94, 0x10, 0xd6, 0xfc, 0x80, 0xcc, 0x54, 0x75, 0x21, 0x25,
	0xa8, 0x2c, 0x88, 0xba, 0x7e, 0x56, 0x7d, 0xc1, 0xb3, 0x9f, 0xbb, 0xe2, 0x39, 0x39, 0x7b, 0x0a,
	0x6c, 0xe6, 0x9e, 0x02, 0xcd, 0x67, 0xc4, 0x56, 0x2e, 0x14, 0xb3, 0x4e, 0x00, 0x8d, 0xc8, 0x29,
	0x89, 0x63, 0xec, 0xa9, 0xf2, 0xc1, 0x05, 0x58, 0xcd, 0xb2, 0x90, 0xba, 0x91, 0x85, 0x94, 0x43,
	0xf6, 0x01, 0xac, 0x28, 0x5d, 0x30, 0x3f, 0x7e, 0x81, 0x0d, 0xac, 0xbb, 0xdc, 0xa8, 0xc7, 0x76,
	0xec, 0x1f, 0x11, 0xca, 0x59, 0xaa, 0x48, 0x7c, 0xac, 0x7b, 0xbc, 0x36, 0xb7, 0x1b, 0xfa, 0x7e,
	0x18, 0x1c, 0xb1, 0x0f, 0x2f, 0x8c, 0x47, 0x24, 0x49, 0x53, 0x2b, 0x61, 0x75, 0xe1, 0xcd, 0xbe,
	0xa8, 0x41, 0x5f, 0x55, 0xc9, 0x0a, 0x32, 0x7f, 0x69, 0x31, 0xef, 0x26, 0x74, 0x12, 0x3b, 0xf6,
	0xc7, 0x5a, 0x12, 0xcb, 0x2c, 0x24, 0x78, 0x34, 0x94, 0xd2, 0x30, 0x94, 0xb2, 0xc9, 0x62, 0xb2,
	0x09, 0x19, 0x07, 0xa1, 0x34, 0x4e, 0x9b, 0x35, 0x0f, 0x43, 0xd3, 0x6e, 0xad, 0x5c, 0x1c, 0xfc,
	0x59, 0xab, 0x92, 0xd7, 0x04, 0xbd, 0xa9, 0x09, 0xd7, 0x79, 0x78, 0x87, 0xf9, 0xf0, 0xaa, 0xb1,
	0xdb, 0xd3, 0xc0, 0x95, 0xd1, 0xc8, 0xe0, 0x2f, 0x4d, 0x68, 0xb2, 0x26, 0x03, 0x05, 0x8e, 0x22,
	0x6f, 0x36, 0xb6, 0xb3, 0x28, 0x62, 0x89, 0x53, 0x78, 0x18, 0xf1, 0x32, 0xf4, 0x44, 0x37, 0x09,
	0x1c, 0x81, 0x38, 0x79, 0xf0, 0x38, 0xf5, 0x71, 0xe0, 0x70, 0xd8, 0xdd, 0x00, 0xe0, 0xa7, 0x52,
	0xaf, 0x08, 0x2e, 0x71, 0x0a, 0x5f, 0xe4, 0x16, 0x74, 0x6c, 0xcf, 0x0d, 0x5c, 0x5b, 0xcf, 0xdc,
	0x41, 0x90, 0x78, 0x04, 0xf7, 0x43, 0xb8, 0x22, 0x76, 0x89, 0xce, 0x66, 0x89, 0x6b, 0xbb, 0xea,
	0x41, 0x50, 0x5c, 0x8a, 0x88, 0xf7, 0x1d, 0xa9, 0x2e, 0xf5, 0xdf, 0x04, 0xc5, 0xf1, 0x44, 0xe8,
	0x5f, 0xfe, 0xd6, 0x20, 0x08, 0x43, 0x07, 0xbd, 0x02, 0x3d, 0xd9, 0x19, 0xc6, 0x13, 0xbe, 0x90,
	0x08, 0x9f, 0x97, 0x05, 0xf5, 0x23, 0x41, 0x64, 0xe7, 0xc8, 0x9e, 0x26, 0x7a, 0xdd, 0x65, 0xc1,
	0x9e, 0x26, 0x7c, 0xf9, 0x2d, 0x58, 0xb2, 0x89, 0xe7, 0x89, 0x2c, 0x4d, 0x84, 0xcf, 0x19, 0x41,
	0xf9, 0x04, 0x2f, 0x9c, 0xb8, 0xb6, 0x2b, 0x0a, 0x2b, 0xe2, 0xef, 0x82, 0x0f, 0x05, 0x45, 0xd5,
	0x47, 0xb5, 0x3a, 0xca, 0x42, 0x44, 0xb2, 0xb7, 0xfc, 0xac, 0xcc, 0x2b, 0x7f, 0x5c, 0x48, 0xcb,
	0xbc, 0xf9, 0x1f, 0x17, 0x96, 0x53, 0x77, 0xa3, 0x7e, 0x5c, 0xc8, 0xff, 0x13, 0xd2, 0x2b, 0xe6,
	0x3d, 0x0c, 0x65, 0x84, 0x8e, 0x99, 0x8b, 0x5f, 0x49, 0x53, 0x93, 0x9d, 0x09, 0xff, 0x9f, 0xc4,
	0xc7, 0x6e, 0x30, 0x4e, 0x66, 0x7e, 0x44, 0x43, 0xbf, 0xbf, 0x2a, 0xe6, 0x32, 0xda, 0xb1, 0x20,
	0x31, 0x3f, 0x8b, 0x6d, 0x3a, 0xc5, 0x9e, 0xaa, 0xa4, 0xac, 0xf1, 0xb4, 0xa2, 0x2b, 0x88, 0xa2,
	0x98, 0x62, 0xfd, 0xb9, 0x09, 0x57, 0x77, 0x1c, 0x47, 0x9d, 0xe4, 0x9d, 0xc0, 0x51, 0x47, 0xf3,
	0xb2, 0xde, 0x42, 0x24, 0x2b, 0x87, 0xd8, 0xf0, 0x68, 0x5c, 0x5f, 0x1b, 0xd0, 0x9e, 0x26, 0x24,
	0x1e, 0xaa, 0x5a, 0x9e, 0x6c, 0x71, 0x17, 0xa9, 0xce, 0x9b, 0x2a, 0xa3, 0x66, 0x07, 0xb0, 0x0f,
	0x4a, 0xe1, 0x12, 0x1c, 0xa9, 0xfe, 0x73, 0x69, 0xe3, 0x42, 0x51, 0x7d, 0x59, 0x3c, 0xb7, 0x68,
	0xc4, 0x73, 0x5b, 0x90, 0xd9, 0x49, 0x16, 0x8e, 0x35, 0xc3, 0x89, 0x68, 0x4f, 0x5a, 0x49, 0x83,
	0x84, 0xb2, 0x9b, 0x98, 0xbd, 0x73, 0x8e, 0x29, 0x8e, 0x25, 0x26, 0x32, 0x42, 0xe6, 0x2d, 0xbb,
	0x7a, 0xec, 0x39, 0x80, 0xc5, 0xe4, 0x2c, 0x8c, 0xb8, 0x18, 0x02, 0x09, 0x69, 0x9b, 0xf5, 0xd9,
	0x67, 0x6e, 0xc4, 0x63, 0x7f, 0x81, 0x81, 0xb4, 0x2d, 0xf7, 0xfa, 0x89, 0x17, 0x3e, 0x23, 0xb1,
	0x84, 0x40, 0x46, 0x10, 0x09, 0xb9, 0xac, 0x8b, 0x71, 0x08, 0x18, 0x85, 0x32, 0x0b, 0xba, 0x8e,
	0x4c, 0xd8, 0x78, 0x5c, 0xba, 0x26, 0x8e, 0xbb, 0x4e, 0x33, 0x12, 0x67, 0x94, 0x4b, 0x9c, 0xcd,
	0xa4, 0x7b, 0xbd, 0x70, 0xfd, 0x7f, 0x0f, 0xae, 0x32, 0x27, 0xad, 0x2e, 0x01, 0x1e, 0x64, 0x26,
	0x65, 0x3f, 0xc9, 0xfc, 0x1a, 0x90, 0x1a, 0xb5, 0x1b, 0x06, 0xa7, 0x6e, 0xec, 0x97, 0xfd, 0x85,
	0x53, 0x05, 0x28, 0x15, 0xfa, 0x08, 0x34, 0xa5, 0xa1, 0x8f, 0x02, 0x45, 0xd3, 0x00, 0x85, 0xf5,
	0x53, 0xe8, 0x69, 0x78, 0xfe, 0x0a, 0x40, 0xb6, 0xf6, 0x60, 0xf5, 0xf1, 0x73, 0x37, 0xa1, 0xfa,
	0x5a, 0x55, 0x85, 0x3a, 0x0d, 0xf4, 0x75, 0x03, 0xf4, 0x0f, 0xff, 0x75, 0x05, 0xae, 0x1d, 0x11,
	0x7a, 0x20, 0x7f, 0x50, 0xdb, 0x11, 0x3f, 0xa8, 0x1d, 0x93, 0xf8, 0xdc, 0xb5, 0x59, 0xae, 0x0d,
	0xd9, 0x1f, 0x66, 0x68, 0x8d, 0xb9, 0x7c, 0xe3, 0x8f, 0xb3, 0xc1, 0x2a, 0x23, 0x19, 0xa5, 0xca,
	0x37, 0xa1, 0xab, 0xff, 0xcf, 0x84, 0xd6, 0xd9, 0x88, 0xdc, 0x1f, 0x4e, 0x25, 0xd3, 0x5e, 0x83,
	0x05, 0xf9, 0x9f, 0x12, 0xea, 0xc9, 0x9b, 0xa5, 0x7a, 0x8f, 0x37, 0xa0, 0xa3, 0xfd, 0xd4, 0x84,
	0x90, 0x36, 0x41, 0xc6, 0x3d, 0x25, 0x93, 0x52, 0x59, 0x8e, 0x3c, 0x1c, 0xe8, 0xb2, 0xc8, 0x87,
	0xc3, 0xca, 0x7d, 0xd4, 0xab, 0x5f, 0xba, 0x8f, 0xf6, 0x0c, 0x58, 0x29, 0x09, 0xdf, 0xa4, 0xa7,
	0x4d, 0x28, 0x1f, 0xfc, 0x3a, 0x2c, 0xaa, 0x97, 0x6b, 0xb4, 0xa2, 0x34, 0x55, 0x3d, 0xfc, 0x5d,
	0x58, 0xcd, 0xff, 0xf9, 0x80, 0x36, 0xf3, 0x92, 0xc8, 0xd2, 0x77, 0xc9, 0xf4, 0xb7, 0xc4, 0xcb,
	0x5e, 0x36, 0xf7, 0x8a, 0xc1, 0x60, 0xf5, 0xc4, 0xf7, 0x79, 0x24, 0x6d, 0xbe, 0x45, 0xa2, 0x7e,
	0x61, 0x72, 0xb5, 0x52, 0x7e, 0x04, 0x3d, 0xf3, 0x85, 0x1e, 0x5d, 0x35, 0xa5, 0xad, 0xde, 0xfb,
	0x81, 0x78, 0xdd, 0x15, 0x29, 0xb9, 0xb0, 0x9b, 0xf1, 0x3c, 0x59, 0x32, 0xe5, 0xfb, 0xd0, 0x16,
	0x43, 0xd0, 0xb2, 0x1e, 0xa5, 0x54, 0x58, 0x4b, 0xbe, 0xc2, 0x09, 0x6b, 0x65, 0x4f, 0x72, 0x25,
	0x83, 0x8f, 0xb9, 0x47, 0x29, 0x46, 0x3c, 0x68, 0x6b, 0x4e, 0x30, 0xf4, 0x74, 0x30, 0xaf, 0x97,
	0x85, 0x55, 0x5d, 0xfd, 0x31, 0x50, 0x1c, 0x98, 0xdc, 0xf3, 0x60, 0xf9, 0x39, 0xd3, 0x9f, 0xd8,
	0xc4, 0xb4, 0xdc, 0xa3, 0x5b, 0xc9, 0xb4, 0xc7, 0x70, 0xa5, 0xec, 0xa9, 0x04, 0x5d, 0xcf, 0xa6,
	0x17, 0x1e, 0x51, 0x4a, 0x96, 0x79, 0x02, 0xeb, 0xfb, 0x84, 0xaa, 0xba, 0x9b, 0x2a, 0x88, 0xa3,
	0x9b, 0x15, 0x95, 0x72, 0x99, 0x95, 0x0e, 0x6e, 0x55, 0xf6, 0xcb, 0x75, 0x7f, 0xc6, 0xd3, 0x22,
	0x35, 0x62, 0x18, 0x9c, 0x86, 0x68, 0x50, 0x52, 0x37, 0x56, 0xeb, 0x5d, 0x2f, 0xed, 0x93, 0x6b,
	0x9d, 0xc1, 0x35, 0x6d, 0xad, 0x9c, 0xbc, 0x2f, 0x57, 0xd7, 0x0d, 0xb3, 0x8a, 0xf3, 0xe0, 0x95,
	0x2f, 0x19, 0x25, 0x77, 0xda, 0x87, 0xde, 0x51, 0x98, 0x50, 0x59, 0xe9, 0xdb, 0x89, 0x5c, 0x24,
	0xaa, 0x49, 0x65, 0xd5, 0xc5, 0xc1, 0xa0, 0xac, 0x2b, 0x5b, 0x88, 0xb1, 0x2c, 0xd2, 0x64, 0x8e,
	0xf7, 0x92, 0xb2, 0x94, 0xb9, 0x50, 0x79, 0x25, 0xec, 0x80, 0xff, 0xeb, 0x90, 0xe6, 0xea, 0xec,
	0xea, 0x13, 0x26, 0xae, 0x28, 0x61, 0x09, 0x8c, 0x56, 0x16, 0x7b, 0xde, 0x85, 0xde, 0x88, 0x60,
	0x27, 0xab, 0xca, 0x08, 0xbe, 0x4a, 0xab, 0x34, 0x25, 0x68, 0xf9, 0x05, 0xa0, 0x63, 0x12, 0x38,
	0x32, 0x13, 0x57, 0x4b, 0x70, 0x30, 0xcc, 0xa9, 0xd7, 0x0c, 0x6e, 0x57, 0x0f, 0x90, 0x0b, 0xff,
	0x0a, 0xae, 0x8a, 0x42, 0x44, 0xae, 0x3a, 0x81, 0xf8, 0xd4, 0x79, 0x05, 0x92, 0xc1, 0x9d, 0x39,
	0x23, 0xe4, 0xea, 0xef, 0x70, 0x30, 0x1a, 0x0f, 0x98, 0x1b, 0xf2, 0x28, 0xe7, 0x6a, 0x11, 0xe5,
	0x1e, 0xcf, 0x2c, 0x3e, 0x08, 0x8f, 0x57, 0x28, 0x48, 0x54, 0x5f, 0x3b, 0x2a, 0xce, 0x41, 0x99,
	0xfb, 0x50, 0xd5, 0x86, 0x72, 0x7f, 0xa0, 0x17, 0x14, 0x84, 0x3f, 0xc8, 0x95, 0x18, 0x4a, 0xfd,
	0xc1, 0x5a, 0x21, 0x93, 0x45, 0x1b, 0x85, 0xfa, 0xf2, 0x63, 0x3f, 0xa2, 0xb3, 0x81, 0xf2, 0xf8,
	0xc5, 0xc4, 0xf7, 0x3d, 0x5e, 0xa1, 0xc9, 0xd5, 0x1f, 0x84, 0xb6, 0x8a, 0x45, 0x89, 0x12, 0x36,
	0xde, 0xe6, 0xaa, 0xd6, 0x13, 0x76, 0xe9, 0xd0, 0xcc, 0x14, 0xbe, 0x64, 0xa6, 0xd8, 0x39, 0xef,
	0x90, 0xf9, 0xce, 0x25, 0xae, 0xb8, 0x4a, 0xd9, 0x2a, 0xe9, 0x4f, 0x95, 0xad, 0x55, 0x01, 0x4a,
	0x26, 0xed, 0x00, 0x2a, 0x66, 0x25, 0xf2, 0x4c, 0x94, 0x65, 0x2b, 0xe5, 0x4b, 0x14, 0xa3, 0x53,
	0xb1, 0x44, 0x69, 0xd4, 0x5a, 0xb2, 0xc4, 0x3b, 0x59, 0x89, 0x43, 0xc6, 0xad, 0x42, 0xee, 0x62,
	0x30, 0x5b, 0x2e, 0xb7, 0xc6, 0xaa, 0x90, 0xdb, 0x8c, 0x4c, 0xcb, 0x03, 0x08, 0x23, 0xe6, 0x14,
	0x01, 0x44, 0x3e, 0x0c, 0x2d, 0x4e, 0x3c, 0x69, 0x73, 0x24, 0xbd, 0xf1, 0x9f, 0x00, 0x00, 0x00,
	0xff, 0xff, 0x39, 0xf5, 0x09, 0x04, 0x10, 0x31, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PetMillionsAppletsServiceClient is the client API for PetMillionsAppletsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PetMillionsAppletsServiceClient interface {
	//创建窝信息信息
	CreateNest(ctx context.Context, in *CreateNestReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//修改窝的名称
	EditNestName(ctx context.Context, in *EditNestNameReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//根据窝Id获取窝信息
	GetNest(ctx context.Context, in *GetNestReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取窝的列表 名称支持模糊搜索
	GetNestList(ctx context.Context, in *GetNestListReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//新增免疫计划
	CreatePlan(ctx context.Context, in *CreatePlanReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取所有免疫计划（不需要分页一个用户每个宠物种类最多5个，查询宠物种类必传）
	GetPlanList(ctx context.Context, in *GetPlanListReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//新增免疫计划记录
	GetPlan(ctx context.Context, in *GetPlanReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//编辑免疫计划
	EditPlan(ctx context.Context, in *EditPlanReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//新增免疫计划记录
	CreatePlanRecord(ctx context.Context, in *CreatePlanRecordReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取单个免疫计划内容
	GetPlanRecord(ctx context.Context, in *GetPlanRecordReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取所有免疫计划记录 （废弃）
	GetPlanRecordList(ctx context.Context, in *GetPlanRecordListReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//编辑免疫计划记录
	EditPlanRecord(ctx context.Context, in *EditPlanRecordReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取宠物列表.
	GetPetList(ctx context.Context, in *GetPetListReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取宠物详情
	GetPet(ctx context.Context, in *GetPetReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//修改宠物
	EditPet(ctx context.Context, in *EditPetReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//宠物的就诊记录
	GetPetReferralRecords(ctx context.Context, in *GetPetReferralRecordsReq, opts ...grpc.CallOption) (*GetPetReferralRecordsRes, error)
	//批量更新宠物疫苗驱虫信息
	BatchEditPet(ctx context.Context, in *BatchEditPetReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//录入鼻纹信息
	CreatePetImg(ctx context.Context, in *CreatePetImgReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//新增交易信息
	CreateDeliveryRecord(ctx context.Context, in *CreateDeliveryRecordReq, opts ...grpc.CallOption) (*BaseResponse, error)
	// 通过手机号码查询其绑定的商户列表
	GetMarchentByMobile(ctx context.Context, in *MarchentGetByMobileRequest, opts ...grpc.CallOption) (*MarchentGetByMobileResponse, error)
	// 获取商户详细信息
	GetMarchentInfo(ctx context.Context, in *MarchentGetInfoRequest, opts ...grpc.CallOption) (*MarchentGetInfoResponse, error)
	// 通过商户信息查看客户信息
	GetMarchentDeliveryRecord(ctx context.Context, in *MarchentDeliveryRecordGetRequest, opts ...grpc.CallOption) (*MarchentDeliveryRecordGetResponse, error)
	// 调用小程序api
	PostMiniAppApi(ctx context.Context, in *MiniAppPostApiRequest, opts ...grpc.CallOption) (*MiniAppPostApiResponse, error)
	// 查询未读消息列表
	GetMessageList(ctx context.Context, in *MessageGetListRequest, opts ...grpc.CallOption) (*MessageGetListResponse, error)
	// 查询未读消息明细
	GetMessageDetail(ctx context.Context, in *MessageGetDetailRequest, opts ...grpc.CallOption) (*MessageGetDetailResponse, error)
	// 批量更新消息为已读
	ReadAllMessage(ctx context.Context, in *AllMessageReadRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 推送微信疫苗订阅消息
	SendVaccineMessage(ctx context.Context, in *SubscribeMessageSendRequest, opts ...grpc.CallOption) (*SubscribeMessageSendResponse, error)
	// 删除过时的微信订阅消息
	DeleteObsoleteMessage(ctx context.Context, in *ObsoleteMessageDeleteRequest, opts ...grpc.CallOption) (*ObsoleteMessageDeleteResponse, error)
	// 获取疫苗库品牌 集合
	GetVaccineBrand(ctx context.Context, in *GetVaccineBrandReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取疫苗名称 集合
	GetVaccineName(ctx context.Context, in *GetVaccineNameReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取宠物品种 例如（阿富汗猎犬）
	GetPetBreed(ctx context.Context, in *GetPetBreedReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取宠物花色 例如（黑色）
	GetPetNCount(ctx context.Context, in *GetPetNCountReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取宠物公共花色
	GetCommonPetColor(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*GetCommonPetColorRes, error)
	//客户宠物列表
	GetPayCustomerList(ctx context.Context, in *PayCustomerListReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取客户关联宠物累计数据
	GetCustomerData(ctx context.Context, in *CustomerDataReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//转诊记录
	GetReferralRecords(ctx context.Context, in *ReferralRecordsReq, opts ...grpc.CallOption) (*BaseResponse, error)
	// 获取子龙宠物列表
	GetScrmPets(ctx context.Context, in *GetScrmPetsReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//客户建档
	AddCustomerAndPets(ctx context.Context, in *AddCustomerAndPetsReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//获取客户建档详情
	GetCustomerDetails(ctx context.Context, in *GetCustomerDetailsReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//交付客户确认
	CustomerConfirm(ctx context.Context, in *CustomerConfirmReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//新增客户档案（不包含宠物信息）
	AddCustomer(ctx context.Context, in *AddCustomerReq, opts ...grpc.CallOption) (*BaseResponse, error)
	//是否存在客户档案
	ExistCustomer(ctx context.Context, in *ExistCustomerReq, opts ...grpc.CallOption) (*BaseResponse, error)
}

type petMillionsAppletsServiceClient struct {
	cc *grpc.ClientConn
}

func NewPetMillionsAppletsServiceClient(cc *grpc.ClientConn) PetMillionsAppletsServiceClient {
	return &petMillionsAppletsServiceClient{cc}
}

func (c *petMillionsAppletsServiceClient) CreateNest(ctx context.Context, in *CreateNestReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/CreateNest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) EditNestName(ctx context.Context, in *EditNestNameReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/EditNestName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetNest(ctx context.Context, in *GetNestReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetNest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetNestList(ctx context.Context, in *GetNestListReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetNestList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) CreatePlan(ctx context.Context, in *CreatePlanReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/CreatePlan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPlanList(ctx context.Context, in *GetPlanListReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPlanList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPlan(ctx context.Context, in *GetPlanReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPlan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) EditPlan(ctx context.Context, in *EditPlanReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/EditPlan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) CreatePlanRecord(ctx context.Context, in *CreatePlanRecordReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/CreatePlanRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPlanRecord(ctx context.Context, in *GetPlanRecordReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPlanRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPlanRecordList(ctx context.Context, in *GetPlanRecordListReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPlanRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) EditPlanRecord(ctx context.Context, in *EditPlanRecordReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/EditPlanRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPetList(ctx context.Context, in *GetPetListReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPetList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPet(ctx context.Context, in *GetPetReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) EditPet(ctx context.Context, in *EditPetReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/EditPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPetReferralRecords(ctx context.Context, in *GetPetReferralRecordsReq, opts ...grpc.CallOption) (*GetPetReferralRecordsRes, error) {
	out := new(GetPetReferralRecordsRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPetReferralRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) BatchEditPet(ctx context.Context, in *BatchEditPetReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/BatchEditPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) CreatePetImg(ctx context.Context, in *CreatePetImgReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/CreatePetImg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) CreateDeliveryRecord(ctx context.Context, in *CreateDeliveryRecordReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/CreateDeliveryRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetMarchentByMobile(ctx context.Context, in *MarchentGetByMobileRequest, opts ...grpc.CallOption) (*MarchentGetByMobileResponse, error) {
	out := new(MarchentGetByMobileResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetMarchentByMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetMarchentInfo(ctx context.Context, in *MarchentGetInfoRequest, opts ...grpc.CallOption) (*MarchentGetInfoResponse, error) {
	out := new(MarchentGetInfoResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetMarchentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetMarchentDeliveryRecord(ctx context.Context, in *MarchentDeliveryRecordGetRequest, opts ...grpc.CallOption) (*MarchentDeliveryRecordGetResponse, error) {
	out := new(MarchentDeliveryRecordGetResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetMarchentDeliveryRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) PostMiniAppApi(ctx context.Context, in *MiniAppPostApiRequest, opts ...grpc.CallOption) (*MiniAppPostApiResponse, error) {
	out := new(MiniAppPostApiResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/PostMiniAppApi", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetMessageList(ctx context.Context, in *MessageGetListRequest, opts ...grpc.CallOption) (*MessageGetListResponse, error) {
	out := new(MessageGetListResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetMessageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetMessageDetail(ctx context.Context, in *MessageGetDetailRequest, opts ...grpc.CallOption) (*MessageGetDetailResponse, error) {
	out := new(MessageGetDetailResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetMessageDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) ReadAllMessage(ctx context.Context, in *AllMessageReadRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/ReadAllMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) SendVaccineMessage(ctx context.Context, in *SubscribeMessageSendRequest, opts ...grpc.CallOption) (*SubscribeMessageSendResponse, error) {
	out := new(SubscribeMessageSendResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/SendVaccineMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) DeleteObsoleteMessage(ctx context.Context, in *ObsoleteMessageDeleteRequest, opts ...grpc.CallOption) (*ObsoleteMessageDeleteResponse, error) {
	out := new(ObsoleteMessageDeleteResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/DeleteObsoleteMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetVaccineBrand(ctx context.Context, in *GetVaccineBrandReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetVaccineBrand", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetVaccineName(ctx context.Context, in *GetVaccineNameReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetVaccineName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPetBreed(ctx context.Context, in *GetPetBreedReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPetNCount(ctx context.Context, in *GetPetNCountReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPetNCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetCommonPetColor(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*GetCommonPetColorRes, error) {
	out := new(GetCommonPetColorRes)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetCommonPetColor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetPayCustomerList(ctx context.Context, in *PayCustomerListReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetPayCustomerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetCustomerData(ctx context.Context, in *CustomerDataReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetCustomerData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetReferralRecords(ctx context.Context, in *ReferralRecordsReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetReferralRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetScrmPets(ctx context.Context, in *GetScrmPetsReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetScrmPets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) AddCustomerAndPets(ctx context.Context, in *AddCustomerAndPetsReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/AddCustomerAndPets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) GetCustomerDetails(ctx context.Context, in *GetCustomerDetailsReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/GetCustomerDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) CustomerConfirm(ctx context.Context, in *CustomerConfirmReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/CustomerConfirm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) AddCustomer(ctx context.Context, in *AddCustomerReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/AddCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petMillionsAppletsServiceClient) ExistCustomer(ctx context.Context, in *ExistCustomerReq, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/pm.PetMillionsAppletsService/ExistCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetMillionsAppletsServiceServer is the server API for PetMillionsAppletsService service.
type PetMillionsAppletsServiceServer interface {
	//创建窝信息信息
	CreateNest(context.Context, *CreateNestReq) (*BaseResponse, error)
	//修改窝的名称
	EditNestName(context.Context, *EditNestNameReq) (*BaseResponse, error)
	//根据窝Id获取窝信息
	GetNest(context.Context, *GetNestReq) (*BaseResponse, error)
	//获取窝的列表 名称支持模糊搜索
	GetNestList(context.Context, *GetNestListReq) (*BaseResponse, error)
	//新增免疫计划
	CreatePlan(context.Context, *CreatePlanReq) (*BaseResponse, error)
	//获取所有免疫计划（不需要分页一个用户每个宠物种类最多5个，查询宠物种类必传）
	GetPlanList(context.Context, *GetPlanListReq) (*BaseResponse, error)
	//新增免疫计划记录
	GetPlan(context.Context, *GetPlanReq) (*BaseResponse, error)
	//编辑免疫计划
	EditPlan(context.Context, *EditPlanReq) (*BaseResponse, error)
	//新增免疫计划记录
	CreatePlanRecord(context.Context, *CreatePlanRecordReq) (*BaseResponse, error)
	//获取单个免疫计划内容
	GetPlanRecord(context.Context, *GetPlanRecordReq) (*BaseResponse, error)
	//获取所有免疫计划记录 （废弃）
	GetPlanRecordList(context.Context, *GetPlanRecordListReq) (*BaseResponse, error)
	//编辑免疫计划记录
	EditPlanRecord(context.Context, *EditPlanRecordReq) (*BaseResponse, error)
	//获取宠物列表.
	GetPetList(context.Context, *GetPetListReq) (*BaseResponse, error)
	//获取宠物详情
	GetPet(context.Context, *GetPetReq) (*BaseResponse, error)
	//修改宠物
	EditPet(context.Context, *EditPetReq) (*BaseResponse, error)
	//宠物的就诊记录
	GetPetReferralRecords(context.Context, *GetPetReferralRecordsReq) (*GetPetReferralRecordsRes, error)
	//批量更新宠物疫苗驱虫信息
	BatchEditPet(context.Context, *BatchEditPetReq) (*BaseResponse, error)
	//录入鼻纹信息
	CreatePetImg(context.Context, *CreatePetImgReq) (*BaseResponse, error)
	//新增交易信息
	CreateDeliveryRecord(context.Context, *CreateDeliveryRecordReq) (*BaseResponse, error)
	// 通过手机号码查询其绑定的商户列表
	GetMarchentByMobile(context.Context, *MarchentGetByMobileRequest) (*MarchentGetByMobileResponse, error)
	// 获取商户详细信息
	GetMarchentInfo(context.Context, *MarchentGetInfoRequest) (*MarchentGetInfoResponse, error)
	// 通过商户信息查看客户信息
	GetMarchentDeliveryRecord(context.Context, *MarchentDeliveryRecordGetRequest) (*MarchentDeliveryRecordGetResponse, error)
	// 调用小程序api
	PostMiniAppApi(context.Context, *MiniAppPostApiRequest) (*MiniAppPostApiResponse, error)
	// 查询未读消息列表
	GetMessageList(context.Context, *MessageGetListRequest) (*MessageGetListResponse, error)
	// 查询未读消息明细
	GetMessageDetail(context.Context, *MessageGetDetailRequest) (*MessageGetDetailResponse, error)
	// 批量更新消息为已读
	ReadAllMessage(context.Context, *AllMessageReadRequest) (*BaseResponse, error)
	// 推送微信疫苗订阅消息
	SendVaccineMessage(context.Context, *SubscribeMessageSendRequest) (*SubscribeMessageSendResponse, error)
	// 删除过时的微信订阅消息
	DeleteObsoleteMessage(context.Context, *ObsoleteMessageDeleteRequest) (*ObsoleteMessageDeleteResponse, error)
	// 获取疫苗库品牌 集合
	GetVaccineBrand(context.Context, *GetVaccineBrandReq) (*BaseResponse, error)
	//获取疫苗名称 集合
	GetVaccineName(context.Context, *GetVaccineNameReq) (*BaseResponse, error)
	//获取宠物品种 例如（阿富汗猎犬）
	GetPetBreed(context.Context, *GetPetBreedReq) (*BaseResponse, error)
	//获取宠物花色 例如（黑色）
	GetPetNCount(context.Context, *GetPetNCountReq) (*BaseResponse, error)
	//获取宠物公共花色
	GetCommonPetColor(context.Context, *empty.Empty) (*GetCommonPetColorRes, error)
	//客户宠物列表
	GetPayCustomerList(context.Context, *PayCustomerListReq) (*BaseResponse, error)
	//获取客户关联宠物累计数据
	GetCustomerData(context.Context, *CustomerDataReq) (*BaseResponse, error)
	//转诊记录
	GetReferralRecords(context.Context, *ReferralRecordsReq) (*BaseResponse, error)
	// 获取子龙宠物列表
	GetScrmPets(context.Context, *GetScrmPetsReq) (*BaseResponse, error)
	//客户建档
	AddCustomerAndPets(context.Context, *AddCustomerAndPetsReq) (*BaseResponse, error)
	//获取客户建档详情
	GetCustomerDetails(context.Context, *GetCustomerDetailsReq) (*BaseResponse, error)
	//交付客户确认
	CustomerConfirm(context.Context, *CustomerConfirmReq) (*BaseResponse, error)
	//新增客户档案（不包含宠物信息）
	AddCustomer(context.Context, *AddCustomerReq) (*BaseResponse, error)
	//是否存在客户档案
	ExistCustomer(context.Context, *ExistCustomerReq) (*BaseResponse, error)
}

// UnimplementedPetMillionsAppletsServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPetMillionsAppletsServiceServer struct {
}

func (*UnimplementedPetMillionsAppletsServiceServer) CreateNest(ctx context.Context, req *CreateNestReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNest not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) EditNestName(ctx context.Context, req *EditNestNameReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditNestName not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetNest(ctx context.Context, req *GetNestReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNest not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetNestList(ctx context.Context, req *GetNestListReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNestList not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) CreatePlan(ctx context.Context, req *CreatePlanReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlan not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPlanList(ctx context.Context, req *GetPlanListReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanList not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPlan(ctx context.Context, req *GetPlanReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlan not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) EditPlan(ctx context.Context, req *EditPlanReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditPlan not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) CreatePlanRecord(ctx context.Context, req *CreatePlanRecordReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlanRecord not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPlanRecord(ctx context.Context, req *GetPlanRecordReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanRecord not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPlanRecordList(ctx context.Context, req *GetPlanRecordListReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanRecordList not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) EditPlanRecord(ctx context.Context, req *EditPlanRecordReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditPlanRecord not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPetList(ctx context.Context, req *GetPetListReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetList not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPet(ctx context.Context, req *GetPetReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPet not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) EditPet(ctx context.Context, req *EditPetReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditPet not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPetReferralRecords(ctx context.Context, req *GetPetReferralRecordsReq) (*GetPetReferralRecordsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetReferralRecords not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) BatchEditPet(ctx context.Context, req *BatchEditPetReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchEditPet not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) CreatePetImg(ctx context.Context, req *CreatePetImgReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetImg not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) CreateDeliveryRecord(ctx context.Context, req *CreateDeliveryRecordReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDeliveryRecord not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetMarchentByMobile(ctx context.Context, req *MarchentGetByMobileRequest) (*MarchentGetByMobileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentByMobile not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetMarchentInfo(ctx context.Context, req *MarchentGetInfoRequest) (*MarchentGetInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentInfo not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetMarchentDeliveryRecord(ctx context.Context, req *MarchentDeliveryRecordGetRequest) (*MarchentDeliveryRecordGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarchentDeliveryRecord not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) PostMiniAppApi(ctx context.Context, req *MiniAppPostApiRequest) (*MiniAppPostApiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PostMiniAppApi not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetMessageList(ctx context.Context, req *MessageGetListRequest) (*MessageGetListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMessageList not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetMessageDetail(ctx context.Context, req *MessageGetDetailRequest) (*MessageGetDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMessageDetail not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) ReadAllMessage(ctx context.Context, req *AllMessageReadRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadAllMessage not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) SendVaccineMessage(ctx context.Context, req *SubscribeMessageSendRequest) (*SubscribeMessageSendResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendVaccineMessage not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) DeleteObsoleteMessage(ctx context.Context, req *ObsoleteMessageDeleteRequest) (*ObsoleteMessageDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteObsoleteMessage not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetVaccineBrand(ctx context.Context, req *GetVaccineBrandReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVaccineBrand not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetVaccineName(ctx context.Context, req *GetVaccineNameReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVaccineName not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPetBreed(ctx context.Context, req *GetPetBreedReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetBreed not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPetNCount(ctx context.Context, req *GetPetNCountReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetNCount not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetCommonPetColor(ctx context.Context, req *empty.Empty) (*GetCommonPetColorRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommonPetColor not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetPayCustomerList(ctx context.Context, req *PayCustomerListReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayCustomerList not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetCustomerData(ctx context.Context, req *CustomerDataReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerData not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetReferralRecords(ctx context.Context, req *ReferralRecordsReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReferralRecords not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetScrmPets(ctx context.Context, req *GetScrmPetsReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScrmPets not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) AddCustomerAndPets(ctx context.Context, req *AddCustomerAndPetsReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCustomerAndPets not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) GetCustomerDetails(ctx context.Context, req *GetCustomerDetailsReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerDetails not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) CustomerConfirm(ctx context.Context, req *CustomerConfirmReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CustomerConfirm not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) AddCustomer(ctx context.Context, req *AddCustomerReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCustomer not implemented")
}
func (*UnimplementedPetMillionsAppletsServiceServer) ExistCustomer(ctx context.Context, req *ExistCustomerReq) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExistCustomer not implemented")
}

func RegisterPetMillionsAppletsServiceServer(s *grpc.Server, srv PetMillionsAppletsServiceServer) {
	s.RegisterService(&_PetMillionsAppletsService_serviceDesc, srv)
}

func _PetMillionsAppletsService_CreateNest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).CreateNest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/CreateNest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).CreateNest(ctx, req.(*CreateNestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_EditNestName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditNestNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).EditNestName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/EditNestName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).EditNestName(ctx, req.(*EditNestNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetNest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetNest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetNest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetNest(ctx, req.(*GetNestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetNestList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNestListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetNestList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetNestList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetNestList(ctx, req.(*GetNestListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_CreatePlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).CreatePlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/CreatePlan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).CreatePlan(ctx, req.(*CreatePlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPlanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPlanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPlanList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPlanList(ctx, req.(*GetPlanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPlan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPlan(ctx, req.(*GetPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_EditPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).EditPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/EditPlan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).EditPlan(ctx, req.(*EditPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_CreatePlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).CreatePlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/CreatePlanRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).CreatePlanRecord(ctx, req.(*CreatePlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPlanRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPlanRecord(ctx, req.(*GetPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPlanRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlanRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPlanRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPlanRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPlanRecordList(ctx, req.(*GetPlanRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_EditPlanRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditPlanRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).EditPlanRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/EditPlanRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).EditPlanRecord(ctx, req.(*EditPlanRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPetList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPetList(ctx, req.(*GetPetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPet(ctx, req.(*GetPetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_EditPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditPetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).EditPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/EditPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).EditPet(ctx, req.(*EditPetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPetReferralRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetReferralRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPetReferralRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPetReferralRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPetReferralRecords(ctx, req.(*GetPetReferralRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_BatchEditPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchEditPetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).BatchEditPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/BatchEditPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).BatchEditPet(ctx, req.(*BatchEditPetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_CreatePetImg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetImgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).CreatePetImg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/CreatePetImg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).CreatePetImg(ctx, req.(*CreatePetImgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_CreateDeliveryRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDeliveryRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).CreateDeliveryRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/CreateDeliveryRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).CreateDeliveryRecord(ctx, req.(*CreateDeliveryRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetMarchentByMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentGetByMobileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetMarchentByMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetMarchentByMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetMarchentByMobile(ctx, req.(*MarchentGetByMobileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetMarchentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentGetInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetMarchentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetMarchentInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetMarchentInfo(ctx, req.(*MarchentGetInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetMarchentDeliveryRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarchentDeliveryRecordGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetMarchentDeliveryRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetMarchentDeliveryRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetMarchentDeliveryRecord(ctx, req.(*MarchentDeliveryRecordGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_PostMiniAppApi_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MiniAppPostApiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).PostMiniAppApi(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/PostMiniAppApi",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).PostMiniAppApi(ctx, req.(*MiniAppPostApiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageGetListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetMessageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetMessageList(ctx, req.(*MessageGetListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetMessageDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageGetDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetMessageDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetMessageDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetMessageDetail(ctx, req.(*MessageGetDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_ReadAllMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllMessageReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).ReadAllMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/ReadAllMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).ReadAllMessage(ctx, req.(*AllMessageReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_SendVaccineMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeMessageSendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).SendVaccineMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/SendVaccineMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).SendVaccineMessage(ctx, req.(*SubscribeMessageSendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_DeleteObsoleteMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ObsoleteMessageDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).DeleteObsoleteMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/DeleteObsoleteMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).DeleteObsoleteMessage(ctx, req.(*ObsoleteMessageDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetVaccineBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVaccineBrandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetVaccineBrand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetVaccineBrand",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetVaccineBrand(ctx, req.(*GetVaccineBrandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetVaccineName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVaccineNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetVaccineName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetVaccineName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetVaccineName(ctx, req.(*GetVaccineNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetBreedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPetBreed(ctx, req.(*GetPetBreedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPetNCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetNCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPetNCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPetNCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPetNCount(ctx, req.(*GetPetNCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetCommonPetColor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(empty.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetCommonPetColor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetCommonPetColor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetCommonPetColor(ctx, req.(*empty.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetPayCustomerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCustomerListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetPayCustomerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetPayCustomerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetPayCustomerList(ctx, req.(*PayCustomerListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetCustomerData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomerDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetCustomerData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetCustomerData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetCustomerData(ctx, req.(*CustomerDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetReferralRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReferralRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetReferralRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetReferralRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetReferralRecords(ctx, req.(*ReferralRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetScrmPets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScrmPetsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetScrmPets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetScrmPets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetScrmPets(ctx, req.(*GetScrmPetsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_AddCustomerAndPets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCustomerAndPetsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).AddCustomerAndPets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/AddCustomerAndPets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).AddCustomerAndPets(ctx, req.(*AddCustomerAndPetsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_GetCustomerDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerDetailsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).GetCustomerDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/GetCustomerDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).GetCustomerDetails(ctx, req.(*GetCustomerDetailsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_CustomerConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomerConfirmReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).CustomerConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/CustomerConfirm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).CustomerConfirm(ctx, req.(*CustomerConfirmReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_AddCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCustomerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).AddCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/AddCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).AddCustomer(ctx, req.(*AddCustomerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetMillionsAppletsService_ExistCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExistCustomerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMillionsAppletsServiceServer).ExistCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pm.PetMillionsAppletsService/ExistCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMillionsAppletsServiceServer).ExistCustomer(ctx, req.(*ExistCustomerReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PetMillionsAppletsService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pm.PetMillionsAppletsService",
	HandlerType: (*PetMillionsAppletsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateNest",
			Handler:    _PetMillionsAppletsService_CreateNest_Handler,
		},
		{
			MethodName: "EditNestName",
			Handler:    _PetMillionsAppletsService_EditNestName_Handler,
		},
		{
			MethodName: "GetNest",
			Handler:    _PetMillionsAppletsService_GetNest_Handler,
		},
		{
			MethodName: "GetNestList",
			Handler:    _PetMillionsAppletsService_GetNestList_Handler,
		},
		{
			MethodName: "CreatePlan",
			Handler:    _PetMillionsAppletsService_CreatePlan_Handler,
		},
		{
			MethodName: "GetPlanList",
			Handler:    _PetMillionsAppletsService_GetPlanList_Handler,
		},
		{
			MethodName: "GetPlan",
			Handler:    _PetMillionsAppletsService_GetPlan_Handler,
		},
		{
			MethodName: "EditPlan",
			Handler:    _PetMillionsAppletsService_EditPlan_Handler,
		},
		{
			MethodName: "CreatePlanRecord",
			Handler:    _PetMillionsAppletsService_CreatePlanRecord_Handler,
		},
		{
			MethodName: "GetPlanRecord",
			Handler:    _PetMillionsAppletsService_GetPlanRecord_Handler,
		},
		{
			MethodName: "GetPlanRecordList",
			Handler:    _PetMillionsAppletsService_GetPlanRecordList_Handler,
		},
		{
			MethodName: "EditPlanRecord",
			Handler:    _PetMillionsAppletsService_EditPlanRecord_Handler,
		},
		{
			MethodName: "GetPetList",
			Handler:    _PetMillionsAppletsService_GetPetList_Handler,
		},
		{
			MethodName: "GetPet",
			Handler:    _PetMillionsAppletsService_GetPet_Handler,
		},
		{
			MethodName: "EditPet",
			Handler:    _PetMillionsAppletsService_EditPet_Handler,
		},
		{
			MethodName: "GetPetReferralRecords",
			Handler:    _PetMillionsAppletsService_GetPetReferralRecords_Handler,
		},
		{
			MethodName: "BatchEditPet",
			Handler:    _PetMillionsAppletsService_BatchEditPet_Handler,
		},
		{
			MethodName: "CreatePetImg",
			Handler:    _PetMillionsAppletsService_CreatePetImg_Handler,
		},
		{
			MethodName: "CreateDeliveryRecord",
			Handler:    _PetMillionsAppletsService_CreateDeliveryRecord_Handler,
		},
		{
			MethodName: "GetMarchentByMobile",
			Handler:    _PetMillionsAppletsService_GetMarchentByMobile_Handler,
		},
		{
			MethodName: "GetMarchentInfo",
			Handler:    _PetMillionsAppletsService_GetMarchentInfo_Handler,
		},
		{
			MethodName: "GetMarchentDeliveryRecord",
			Handler:    _PetMillionsAppletsService_GetMarchentDeliveryRecord_Handler,
		},
		{
			MethodName: "PostMiniAppApi",
			Handler:    _PetMillionsAppletsService_PostMiniAppApi_Handler,
		},
		{
			MethodName: "GetMessageList",
			Handler:    _PetMillionsAppletsService_GetMessageList_Handler,
		},
		{
			MethodName: "GetMessageDetail",
			Handler:    _PetMillionsAppletsService_GetMessageDetail_Handler,
		},
		{
			MethodName: "ReadAllMessage",
			Handler:    _PetMillionsAppletsService_ReadAllMessage_Handler,
		},
		{
			MethodName: "SendVaccineMessage",
			Handler:    _PetMillionsAppletsService_SendVaccineMessage_Handler,
		},
		{
			MethodName: "DeleteObsoleteMessage",
			Handler:    _PetMillionsAppletsService_DeleteObsoleteMessage_Handler,
		},
		{
			MethodName: "GetVaccineBrand",
			Handler:    _PetMillionsAppletsService_GetVaccineBrand_Handler,
		},
		{
			MethodName: "GetVaccineName",
			Handler:    _PetMillionsAppletsService_GetVaccineName_Handler,
		},
		{
			MethodName: "GetPetBreed",
			Handler:    _PetMillionsAppletsService_GetPetBreed_Handler,
		},
		{
			MethodName: "GetPetNCount",
			Handler:    _PetMillionsAppletsService_GetPetNCount_Handler,
		},
		{
			MethodName: "GetCommonPetColor",
			Handler:    _PetMillionsAppletsService_GetCommonPetColor_Handler,
		},
		{
			MethodName: "GetPayCustomerList",
			Handler:    _PetMillionsAppletsService_GetPayCustomerList_Handler,
		},
		{
			MethodName: "GetCustomerData",
			Handler:    _PetMillionsAppletsService_GetCustomerData_Handler,
		},
		{
			MethodName: "GetReferralRecords",
			Handler:    _PetMillionsAppletsService_GetReferralRecords_Handler,
		},
		{
			MethodName: "GetScrmPets",
			Handler:    _PetMillionsAppletsService_GetScrmPets_Handler,
		},
		{
			MethodName: "AddCustomerAndPets",
			Handler:    _PetMillionsAppletsService_AddCustomerAndPets_Handler,
		},
		{
			MethodName: "GetCustomerDetails",
			Handler:    _PetMillionsAppletsService_GetCustomerDetails_Handler,
		},
		{
			MethodName: "CustomerConfirm",
			Handler:    _PetMillionsAppletsService_CustomerConfirm_Handler,
		},
		{
			MethodName: "AddCustomer",
			Handler:    _PetMillionsAppletsService_AddCustomer_Handler,
		},
		{
			MethodName: "ExistCustomer",
			Handler:    _PetMillionsAppletsService_ExistCustomer_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pm/petmillionsapplets.proto",
}
