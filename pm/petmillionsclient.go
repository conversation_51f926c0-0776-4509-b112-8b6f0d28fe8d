package pm

import (
	"context"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type PetMillionsClient struct {
	Conn               *grpc.ClientConn
	Ctx                context.Context
	Cf                 context.CancelFunc
	RPC                PetMillionsAppletsServiceClient
	PetMillionsService PetMillionsServiceClient
}

func GetPetMillionsAppletsClient(c ...echo.Context) *PetMillionsClient {
	var client PetMillionsClient
	var err error
	url := config.GetString("grpc.petmillions")
	//url = "10.1.1.248:7067"
	if url == "" {
		url = "127.0.0.1:7067"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = NewPetMillionsAppletsServiceClient(client.Conn)
		client.PetMillionsService = NewPetMillionsServiceClient(client.Conn)
		client.Ctx = context.Background()
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*500)
		return &client
	}
}

func (c *PetMillionsClient) Close() {
	c.Conn.Close()
	c.Cf()
}
