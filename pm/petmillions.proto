syntax = "proto3";
package pm;

service PetMillionsService {
    //创建疫苗信息
    rpc CreateVaccine (CreateVaccineReq) returns (BaseRes);
    //更新疫苗信息
    rpc UpdateVaccine (CreateVaccineReq) returns (BaseRes);
    //更新疫苗状态
    rpc UpdateVaccineState (CreateVaccineReq) returns (BaseRes);
    //获取当个疫苗信息
    rpc GetVaccine (CreateVaccineReq) returns (BaseRes);
    //获取疫苗列表
    rpc GetVaccineList (CreateVaccineReq) returns (BaseRes);
    //获取所有的疫苗列表
    rpc GetExportVaccineList (CreateVaccineReq) returns (BaseRes);

    //创建商户信息
    rpc CreateMarchent (CreateMarchentReq) returns (BaseRes);
    //更新主账号信息
    rpc UpdateBaseMarchent (CreateMarchentReq) returns (BaseRes);
    //更新主账号信息
    rpc GetBaseMarchent (MarchentReq) returns (BaseRes);
    //更新主账号信息
    rpc UpdateMarchent (MarchentReq) returns (BaseRes);
    //更新主账号信息
    rpc UpdateMarchentState (MarchentStateReq) returns (BaseRes);
    //更新资质认证信息
    rpc UpdateMarchentQualify (MarchentQualifyReq) returns (BaseRes);
    //更新经营信息信息
    rpc UpdateMarchentManagement (MarchentManagementReq) returns (BaseRes);
    //更新运营信息
    rpc UpdateMarchentOperate (MarchentOperateReq) returns (BaseRes);
    //更新商户财务信息
    rpc UpdateMarchentFinance (MarchentFinanceReq) returns (BaseRes);
    //更新账户信息
    rpc UpdateMarchentMember (MarchentMemberReq) returns (BaseRes);

    //获取主账户信息
    rpc GetMarchent (GetBaseMarchentReq) returns (BaseRes);
    //获取资质认证信息
    rpc GetMarchentQualify (GetBaseMarchentReq) returns (BaseRes);
    //获取经营信息信息
    rpc GetMarchentManagement (GetBaseMarchentReq) returns (BaseRes);
    //获取运营信息
    rpc GetMarchentOperate (GetBaseMarchentReq) returns (BaseRes);
    //获取商户财务信息
    rpc GetMarchentFinance (GetBaseMarchentReq) returns (BaseRes);
    //获取账户信息
    rpc GetMarchentMember (GetBaseMarchentReq) returns (BaseRes);
    // 获取宠物信息
    rpc GetMarchentPet(GetBaseMarchentReq) returns (BaseRes);
    // 获取客户信息
    rpc GetMarchentDeliveryRecord(GetBaseMarchentReq) returns (BaseRes);
    //获取商户营销成果(到店、消费)
    rpc GetMarchentMarketingOutcome(GetMarchentMarketingOutcomeReq) returns (GetMarchentMarketingOutcomeRes);

    //获取所有的商家信息列表
    rpc GetMarchentList (GetMarchentListReq) returns (BaseRes);
    //获取所有的商家信息列表-导出
    rpc GetExportMarchentList (GetMarchentListReq) returns (BaseRes);
    //导入商家信息
    rpc ImportMarchent (MarchentImportReq) returns (BaseRes);
    //获取商家详情信息-组合detail
    rpc GetMarchentDetail (GetBaseMarchentReq) returns (BaseRes);
    //根据城市信息获取门店信息
    rpc GetBaseCityTwoLevel (GetBaseCityReq) returns (BaseRes);

    //获取宠物品种 例如（阿富汗猎犬）
    rpc GetPetBreeds(GetPetBreedsReq)  returns (BaseRes);
}

message GetPetBreedsReq {
    repeated string pet_dict_parent_ids = 1;
}

message GetBaseCityReq {
    repeated string citynames = 1;
}
message BaseRes {
    //状态码
    int32 code = 1;
    //错误信息
    string message = 2;
    //数据条数
    int64 data_count = 3;
    //结果
    string data = 4;
}
//创建疫苗信息
message CreateVaccineReq {
    //疫苗id
    int32 id = 1;
    //宠物类型
    int32 pet_type = 2;
    //生产商
    string supplier = 3;
    //疫苗名称
    string vaccine_name = 4;
    //预防疾病
    string vaccine_desc = 5;
    //疫苗状态
    int32 is_enabled = 6;
    //分页、页码
    int32 page_index = 7;
    //分页、每页个数
    int32 page_size = 8;
}

message GetBaseMarchentReq {
    //商户id
    int32 marchentid = 1;
    // 页索引(公用属性，根据业务需求可以不传)
    int32 pageIndex=2;
    // 页大小(公用属性，根据业务需求可以不传)
    int32 pageSize=3;
}

message GetMarchentListReq {
    //省id
    int32 province_id = 1;
    //城市id
    int32 city_id = 2;
    //合作状态
    int32 is_enabled = 3;
    //经营品类
    int32 pet_type = 4;
    //店铺名称
    string shop_name = 5;
    //分页信息
    int32 page_index = 6;
    //分页总数
    int32 page_size = 7;
    //区域id
    int32 area_id = 8;
}

message CreateMarchentReq {
    //商户id
    int32 marchentid = 1;
    //商户名称(商户账户|主账户)
    string merchant_name = 2;
    //商户名称
    string username = 3;
    //商户手机号
    string mobile = 4;
    //店铺名称
    string shop_name = 5;
    //店铺类型
    int32 shop_type = 6;
    //业态类型
    int32 bussiness_format_type = 7;
    //省_id
    int32 province_id = 8;
    //省_文本
    string province = 9;
    //市_id
    int32 city_id = 10;
    //市_文本
    string city = 11;
    //详细地址
    string address = 12;
    //财务负责人
    string treasurer = 13;
    //财务手机号
    string treasurer_mobile = 14;
    //商户负责人-总部运营负责人
    string leader = 15;
     //区_id
    int32 area_id = 16;
    //区_文本
    string area_name = 17;
}

// 导入商户数据请求
message MarchentImportReq {
    // 需要导入的商户列表
    repeated MarchentImportDto marchents=1;
}

message MarchentReq {
    //商户id
    int32 marchentid = 1;
    //商户名称(商户账户|主账户)
    string merchant_name = 2;
    //商户手机号
    string mobile = 3;
    //商户名称
    string username = 4;
    //身份证号
    string id_number = 5;
    //身份证图片
    string id_pic = 6;
    //邮箱
    string email = 7;
    //生日
    string birthday = 8;
    //性别(1-男，2-女)
    int32 sex = 9;
}

message MarchentStateReq {
    //商户id
    int32 marchentid = 1;
    //商户合作状态
    int32 is_enabled = 2;
}

message MarchentQualifyReq {
    //商户id
    int32 marchentid = 1;
    //企业名称
    string qualify_name = 2;
    //企业营业执照
    string license_pic = 3;
    //社会统一信用代码
    string credit_code = 4;
    //企业法人代表
    string qualify_owner = 5;
    //企业成立时间
    string qualify_establish = 6;
    //特殊许可证件图
    string special_pic = 7;
    //防疫许可证件图
    string antiepidemic_pic = 8;
    //犬协会认证
    string dog_association = 9;
    //猫协会认证
    string cat_association = 10;
    //认证时间
    string association_date = 11;

}

message MarchentManagementReq {
    //商户id
    int32 marchentid = 1;
    //店铺名称
    string shop_name = 2;
    //店铺类型
    int32 shop_type = 3;
    //业态类型
    int32 bussiness_format_type = 4;
    //业务种类
    string bussiness_type = 5;
    //活体品类
    int32 pet_type = 6;
    //活体品种
    string pet_variety = 7;
    //繁衍规模(1-小型规模(10以内)，2-中型规模(介于10和20之间)，3-大型规模(大于20))
    int32 scale_reproduction = 8;
    //门头照片
    string shop_pic = 9;
    //省_id
    int32 province_id = 10;
    //省_文本
    string province = 11;
    //市_id
    int32 city_id = 12;
    //市_文本
    string city = 13;
    //详细地址
    string address = 14;
    //客服电话
    string telephone = 15;
    //是否连锁(0-否，1-是)
    int32 islinked = 16;
    //logo
    string shop_logo = 17;
    //店铺环境图片
    string enviroment_pic = 18;
    //店铺介绍
    string shop_desc = 19;
    //营业面积
    string shop_measure = 20;
    //雇员数量
    int32 employee_num = 21;
    //区_id
    int32 area_id = 22;
    //区_文本
    string area_name = 23;
}

message MarchentOperateReq {
    //商户id
    int32 marchentid = 1;
    //抖音号
    string douyin_num = 2;
    //快手号
    string kuaishou_num = 3;
    //公众号
    string gongzhong_num = 4;
    //视频号
    string shiping_num = 5;
    //微信号
    string wechat_num = 6;
    //京东
    string jd_num = 7;
    //淘宝
    string taobao_num = 8;
    //其他
    string other_num = 9;
}

message MarchentFinanceReq {
    //商户id
    int32 marchentid = 1;
    //开户行
    string bank_deposit = 2;
    //户名
    string deposit_name = 3;
    //账户
    string deposit_num = 4;
    //私人开户行
    string self_bank_deposit = 5;
    //私人户名
    string self_deposit_name = 6;
    //私人账户
    string self_deposit_num = 7;
}

message MarchentMemberReq {
    //商户id
    int32 marchentid = 1;
    repeated MarchentMember Member = 2;
}

message MarchentMember {
    //商户id
    int32 marchentid = 1;
    //商户名称
    string username = 2;
    //商户手机号
    string mobile = 3;
    //是否是老板账号
    int32 isboss = 4;
}

// 导入商户信息Dto
message MarchentImportDto {

    // 行号
    int32 rowIndex=3;
    //*商家主账户名
    string merchant_name = 1;
 
    //*业务负责人-姓名
    string member_name=20;
    //*业务负责人-手机号
    string member_phone=21;
    //业务负责人-邮箱
    string memeber_email=22;
    //业务负责人-出生日期
    string member_birthday=23;
    //业务负责人-性别
    int32 member_sex=24;
 
    // 工商信息-企业名称
    string qualify_name=30;
    // 社会统一信用代码
    string qualify_creditCode = 31;
    // 法人代表
    string qualify_owner = 32;
    // 成立时间
    string qualify_establish = 33;

    //*店铺名称
    string management_name = 40;
    // *城市    
    string management_city = 41;
    // *区域
    string management_area = 78;
    // *地址
    string management_address = 42;
    // 店铺类型
    int32 management_type = 43;
    // *业态类型
    int32 management_bussinessFormatType = 44;
    // 业务种类
    string management_bussinessType = 45;
    // *活体品类
    int32 management_petType = 46;
    // *活体品种
    string management_petVarietyType=47;
    // 客服电话
    string management_telephone=48;
    // 是否连锁
    bool management_isLinked=49;
    // 店铺简介
    string management_desc=50;
    // 营业面积
    string management_measure=51;
    // 雇员数量
    int32 management_employeeNum=52;
    
    // 抖音号
    string operate_douyin=60;
    //快手号	
    string operate_kuaishou=61;
    //公众号
    string operate_gongzhong=62;
    // 视频号
    string operate_shiping=63;
    // 微信号
    string operate_wechat=64;
    // 京东
    string operate_jd=65;
    // 淘宝
    string operate_taobao=66;
 
    // 企业账户-开户行
    string finance_deposit=70;
    // 企业账户-户名
    string finance_name=71;
    // 企业账户-账号
    string finance_num=72;
    // 私人账户-开户行
    string finance_self_deposit=73;
    // 私人账户-户名
    string finance_self_name=74;
    //私人账户-账号
    string finance_self_num=75;
    // 财务负责人-姓名
    string finance_user_name=76;
    // 财务负责人-手机号
    string finance_user_mobile=77;
    //*总部运营负责人
    string merchant_leader=2;
 }

// 商户详情->商户宠物
message MarchentPetListDto {
    // 宠物id
    int32 petId=14;
    // 窝代码
    string nestCode=1;
    //窝名称
    string nestName =2;
    // 品种
    string petBreed =3;
    // 性别
    string petSex =4;
    // 宠物身份证编码
    string petCardId=5;
    // 宠物芯片号码
    string petCode=6;
    // 首免时间
    string planRecordFirst=7;
    // 二免时间
    string planRecordSecond=8;
    // 三免时间
    string planRecordThird=9;
    // 首次驱虫时间
    string dewormingFirst=10;
    // 二次驱虫时间
    string dewormingSecond=15;
    // 三次驱虫时间
    string dewormingThird=16;
    // 交付状态
    string deliveryState=11;
    // 交付时间
    string deliveryDate=12;
    // 客户手机号码
    string deliveryMobile=13;
}
// 商户详情->商户宠物交付记录(客户列表)
message MarchentDeliveryRecordListDto{
    // 客户手机号码
    string mobile=1;
    // 交付日期
    string dateDay=2;
    // 宠物身份证
    string petCardId=3;
    // 城市
    string city=4;
}

message GetMarchentMarketingOutcomeReq{
    //商户id
    int32 marchent_id = 1;
}

message GetMarchentMarketingOutcomeRes{
    //当前商家关联用户的总转诊次数
    int32 all_num=1;
    //当前商家关联用户的累计消费金额
    double all_amount=2;
    //当前商家关联用户的累计消费金额
    int32 prev_num=3;
    //当前商家关联用户的昨日到店消费金额
    double prev_amount=4;
}