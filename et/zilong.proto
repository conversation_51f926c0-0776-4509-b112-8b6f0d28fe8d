syntax = "proto3";
package et;

service ZiLongService{
  // 子龙商品列表
  rpc ProductList(ZiLongProductListReq) returns (ZiLongProductListRes);
  // 发送门店券
  rpc SendCoupon(SendCouponReq) returns (SendCouponRes);
  // 一卡通开通折扣卡
  rpc DiscountCardNew(DiscountCardNewReq) returns(DiscountCardNewRes);
  // 折扣卡退款
  rpc DiscountCardRefund(DiscountCardRefundReq) returns(ZiLongResponse);
  // 作废门店券
  rpc  WasteCoupon(WasteCouponReq) returns(ZiLongResponse);
  // 获取优惠券使用明细
  rpc GetVerifyDetail(GetVerifyDetailReq) returns(GetVerifyDetailResp);
  // 批量查询门店券使用状态
  rpc GetCouponStatus(GetCouponStatusReq) returns(GetCouponStatusResp);

}

message ZiLongResponse{
  int32 code = 1;
  string message = 2;
}

message ZiLongProductListReq{
  // 公司编码：新瑞鹏RPX0001，瑞云腾龙ZILONG，阿闻宠物店集团CWDJT，测试-阿闻宠物店集团awcs001
  string company_code = 1;
  // 总部产品编码
  repeated string product_code = 2;
  // 产品类型：1001药品，1002消耗品，1010商品....
  repeated int32 product_type = 3;
  // 页码，默认1
  int32 number = 4;
  // 页长，默认20
  int32 size = 5;
  // 条形码
  string bar_code = 6;
  //商品名称
  string product_name = 7;
}

message ZiLongProductList {
  // 商品id
  string id = 1;
  // 条形码
  string bar_code = 2;
  // 是否可sell，1是，0否
  string can_sell = 3;
  // 是否可预定，1是，0否
  string can_order = 4;
  // 目录ID
  string category_id = 5;
  // 目录名称 商品>用品类>厕卫类
  string category_name_path = 6;
  // 是否停用，1是，0否
  string disabled = 7;
  // 包装规格
  string pack_specific = 8;
  // 产品编码
  string product_code = 9;
  // 产品名称
  string product_name = 10;
  // 产品类型：1001药品，1002消耗品，1010商品....
  string product_type = 11;
  // 产品类型名称
  string product_type_name = 12;
  // 入库含税参考价
  string ref_price = 13;
  // 销售单价
  string sell_price = 14;
  // 出入库单位ID
  string store_unit = 16;
  // 出入库单位名称，袋
  string store_unit_name = 17;
  // 税收编码
  string tax_code = 21;
  // 投药单位字典code
  string dosing_unit = 22;
  // 投药单位，mg
  string dosing_unit_name = 23;
  // 投药方式code
  string dosing_way = 24;
  // 投药方式名称，iv
  string dosing_way_name = 25;
  // 投药频次
  string use_frequency = 26;
  // 投药频次，每日一次
  string use_frequency_name = 27;
  // 是否处方药,只药品有，1是。0否
  string is_prescribed_drug = 28;

  message RecommendDosage {
    string code = 1;
    // 用量
    double value = 2;
    // 宠物类型
    string name = 3;
  }

  repeated RecommendDosage recommend_dosage = 29;
}

message ZiLongProductListRes{
  int32 code = 1;
  string message = 2;
  repeated ZiLongProductList data = 3;
  // 总数
  int32 total_count = 4;
}

message SendCouponReq {
  // 模版id数组
  repeated int32 templateIdArr = 1;
  // 手机号数组
  repeated string phoneArr = 2;
  // 张数
  int32 number = 3;
  // 应用ID
  string appId = 4;
  // 来源
  string source = 5;
}

message SendCouponRes {
  int32 code = 1;
  string message = 2;
  repeated SendCouponResData data = 3;
}

message SendCouponResData {
  int32 couponTemplateId = 1;
  string couponName = 2;
  string couponNotice = 3;
  string settlementPrice = 4;
  string category = 5;
  string channel = 6;
  string periodValidityBeginTime = 7;
  string periodValidityEndTime = 8;
  string minConsumption = 9;
  string templateValue = 10;
  message List {
    int32 couponId = 1;
    string couponCode = 2;
    string userPhone = 3;
    string uuid = 4;
  }
  repeated List couponList = 11;
}

message GetVerifyDetailReq {
  // 券编号集合
  repeated string couponCodes = 1;
}

message GetVerifyDetailResp {
  int32 code = 1;
  string message = 2;
  repeated GetVerifyDetailRespData data = 3;
}

message GetVerifyDetailRespData {
  string couponId = 1;
  string couponCode = 2;
  // 状态: 1 未使用, 2 已使用, 3 已过期，4 已废弃'
  string status = 3;
  string couponUseTime = 4;
  string structName = 5;
  string structCode = 6;
  string orderNo = 7;
}

message DiscountCardNewReq {
  // 财务编码
  string eppCode = 1;
  // scrmId
  string customerId = 2;
  // scrm卡类别code
  string scrmCardsCategoryCode = 3;
  // 创建来源（0:子龙 1:小暖 2:瑞鹏 3:新宠 4:保险 5:阿闻 6:众嘉 7:考拉）
  int32 createSource = 4;
  // 卡开始时间
  string startTime = 5;
  // 卡结束时间
  string endTime = 6;
}

message DiscountCardNewRes {
  int32 code = 1;
  string message = 2;
  // 会员卡号
  string data = 3;
}

message DiscountCardRefundReq {
  // 卡号
  string ensureCode = 1;
  // scrmId
  string customerId = 2;
  // 创建来源（0:子龙 1:小暖 2:瑞鹏 3:新宠 4:保险 5:阿闻 6:众嘉 7:考拉）
  int32 createSource = 4;
  // 卡结束时间,如果没填，算关联的所有储值卡效期，如果效期都<当前时间，则默认当前时间；  如果储值卡效期大于传值，则按储值卡效期；储值卡效期小于传值，取传值日期
  string endTime = 5;
}

message WasteCouponReq {
  // 模版id数组
  repeated string ids = 1;
  //操作人
   string operator = 2;
  // 操作人id/编码
  string operatorId = 3;
  //优惠券code，数组
  repeated string couponCodes = 4;
}

message GetCouponStatusReq {
  // 门店券id，数组
  repeated string usercouponIds = 1;
  // 门店券code，数组
  repeated string usercouponCodes = 2;
}

message GetCouponStatusResp {
  // 状态码
  int32 code = 1;
  // 报错信息
  string msg = 2;
  // 业务数据
  repeated GetCouponStatusData data = 3;
  // 请求状态码
  int32 status = 4;
}

message GetCouponStatusData {
  // 券id
  int32 coupon_id = 1;
  // 券码
  string coupon_code = 2;
  //状态: 1 未使用, 2 已使用, 3 已过期，4 已废弃
  int32 status = 3;
  //是否退单，1是，0否
  int32 is_refund = 4;
}