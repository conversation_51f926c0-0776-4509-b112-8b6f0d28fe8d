syntax = "proto3";
package et;

service IShanSongService{
  // 订单计费
  //关于寄件人/收件人联系方式的说明：
  //fromMobile字段toMobile字段都支持座机号，正常手机号。toMbile字段支持隐私号，座机号，手机号
  //1：隐私号格式 18701012345#001
  //2：座机号格式 010-12345678 或者010-12345678-123
  //3：正常手机号格式 11位
  //关于物品来源的说明：
  //orderingSourceNo字段orderingSourceType字段是为了方便骑手店里取餐方便提供的字段。
  //1：两个字段非必传。
  //2：两个字段必须成对出现。
  //3：orderingSourceType的枚举参考开发指南"物品来源枚举"
  //4：如果填写该字段会在闪送客户端展示订单取号标记。比如美团#001号，饿了么#002号。如果不传递此参数，那么默认为闪送。
  rpc OrderCalculate (IssOrderCalculateRequest) returns (IssAddOrderResponse);
  // 提交订单
  //issOrderNo是计费接口返回的闪送订单编号
  rpc OrderPlace (IssOrderNoRequest) returns (IssAddOrderResponse);
  // 失新增\修改门店
  rpc StoreOperation (IssStoreOperationRequest) returns (IssStoreOperationResponse);
  // 取消订单
  rpc AbortOrder (IssOrderNoRequest) returns (IssAbortOrderResponse);
  // 查询闪送员位置
  rpc CourierInfo (IssOrderNoRequest) returns (IssCourierInfoResponse);
  // 确认物品送回接口
  rpc ConfirmGoodsReturn (IssOrderNoRequest) returns (IssBaseResponse);
}

//只需要传闪送的订单号的请求参数
message IssOrderNoRequest {
  //required 闪送订单号 闪送订单号在订单计费接口生成并返回
  string issOrderNo = 1;
}
//订单计费请求参数
message IssOrderCalculateRequest {
  // required 城市名称 取闪送开通城市列表即可，格式：xx市
  string cityName = 1;
  //required 预约类型 0立即单，1预约单
  int32 appointType = 2;
  //预约时间 指的是预约取件时间 如：2020-02-02 22:00,
  string appointmentDate = 3;
  //店铺ID 对应闪送门店ID
  string storeId = 4;
  //可指定的交通工具方式 0未指定; 2摩托车; 8汽车；默认为0；指定交通方式会产生额外费用
  int32 travelWay = 5;
  //帮我取 帮我送 1.帮我送 2.帮我取 ；默认为1
  int32 deliveryType = 6;
  //required 订单发送信息
  IssOrderCalculateSender sender = 7;
  //订单收单信息
  repeated IssOrderCalculateReceiver receiverList = 8;
}
//订单发送信息
message  IssOrderCalculateSender {
  //required 寄件地址
  string fromAddress = 1;
  //required 寄件人姓名
  string fromSenderName = 2;
  //required 寄件联系人
  string fromMobile = 3;
  //required 寄件纬度 只支持百度坐标系
  string fromLatitude = 4;
  //required 寄件经度  只支持百度坐标系
  string fromLongitude = 5;
  //寄件详细地址
  string fromAddressDetail = 6;
}
//订单收单信息
message  IssOrderCalculateReceiver {
  //required 第三方平台流水号 也即我们平台的订单号
  string orderNo = 1;
  //required 收件地址
  string toAddress = 2;
  //收件详细地址
  string toAddressDetail = 3;
  //required 收件纬度 只支持百度坐标系
  string toLatitude = 4;
  // required 收件经度 只支持百度坐标系
  string toLongitude = 5;
  // required 收件人姓名
  string toReceiverName = 6;
  //required 收件联系人
  string toMobile = 7;
  // required 物品类型 枚举值
  //1:文件广告 3:电子产品 5:蛋糕 6:快餐水果 7:鲜花绿植 8:海鲜水产 9:汽车配件 10:其他 11:宠物 12:母婴 13:医药健康 14:教育
  int32 goodType = 8;
  //required 物品重量 重量为0会报错 必须大于0； 如果是浮点则上取整整数，例如：6.7kg传7；单位为kg；重量在5kg以内默认按照5kg收费，最大重量不超过50kg
  int32 weight = 9;
  //备注
  string remarks = 10;
  //小费 单位为分，能被100整除
  int32 additionFee = 11;
  //保险费用 对应保单产品接口的amount
  int32 insurance = 12;
  //保险产品ID 对应保单产品的productId
  string insuranceProId = 13;
  //物品来源 对应商家版取号来源,支持美团,饿了么；不传时默认为“闪送”，传参数时必须和orderingSourceNo成对出现 如有值，则值不能为0
  int32 orderingSourceType = 14;
  //物品来源流水号 对应orderingSourceType流水号，传参数时必须和orderingSourceType成对出现 如有值，则值不能为""
  string orderingSourceNo = 15;
}

//失新增\修改门店 请求参数
message IssStoreOperationRequest {
  // required 店铺名称
  string storeName = 1;
  // required 店铺所在城市名称 取闪送开通城市列表即可，格式：xx市
  string cityName = 2;
  // required 店铺地址
  string address = 3;
  // required 店铺详细地址
  string addressDetail = 4;
  // required 店铺纬度 只支持百度坐标系，更新后实时显示
  string latitude = 5;
  // required 店铺经度 只支持百度坐标系，更新后实时显示
  string longitude = 6;
  // required 店铺联系人手机号/座机 支持手机号，支持座机号：格式 区号-座机号-分机号 如：010-1234567-123，分机号可省略
  string phone = 7;
  // required 店铺业务类型
  //1:文件广告 3:电子产品 5:蛋糕 6:快餐水果 7:鲜花绿植 8:海鲜水产 9:汽车配件 10:其他 11:宠物 12:母婴 13:医药健康 14:教育
  int32 goodType = 8;
  //操作类型:新增或更新 1.保存 2.更新，默认为1
  int32 operationType = 9;
  // 闪送店铺id 当operationType =2 即更新时 storeId不能为空
  string storeId = 10;
}

// 订单计费 与提交订单 返回
message IssBaseResponse {
  //状态码 200 成功 400 失败
  int32 code = 1;
  //消息
  string msg = 2;
  //错误信息
  string error = 3;
}

// 订单计费 与提交订单 返回
message IssAddOrderResponse {
  //状态码 200 成功 400 失败
  int32 code = 1;
  //消息
  string msg = 2;
  //错误信息
  string error = 3;
  IssAddOrderData data = 4;
}

//计费与提交结果详情
message IssAddOrderData{
  //总距离，单位米
  int32 totalDistance = 1;
  //总续重，单位kg
  int32  totalWeight = 2;
  //未优惠需要支付的费用
  int32 totalAmount = 3;
  //优惠的额度
  int32   couponSaveFee = 4;
  //实际支付的费用
  int32 totalFeeAfterSave = 5;
  //闪送订单号
  string   orderNumber = 6;
  //费用明细
  repeated IssFeeInfo feeInfoList = 7;
}
//计费明细
message IssFeeInfo{
  int32 fee = 1;
  string  des = 2;
  int32 type = 3;
}

//失新增\修改门店 返回数据
message IssStoreOperationResponse {
  //状态码 200 成功 400 失败
  int32 code = 1;
  //消息
  string msg = 2;
  //错误信息
  string error = 3;
  //门店id
  int32 storeId = 4;
}

//取消订单返回
message IssAbortOrderResponse {
  //状态码 200 成功 400 失败
  int32 code = 1;
  //消息
  string msg = 2;
  //错误信息
  string error = 3;
  IssAbortOrderData data = 4;
}
//去掉订单的数据详细信息
message IssAbortOrderData{
  //扣款金额
  int32 deductAmount = 1;
  //取消类型 1：因客户取消 实际情况 商品取消也会返回1 ；3：因闪送员取消 10：闪送系统自动取消
  int32 abortType = 2;
  //取消原因
  string  abortReason = 3;
}

//查询闪送员位置的返回数据结构
message IssCourierInfoResponse {
  //状态码 200 成功 400 失败
  int32 code = 1;
  //消息
  string msg = 2;
  //错误信息
  string error = 3;
  //详细信息
  IssCourierInfoData data = 4;
}

//闪送员位置数据
message IssCourierInfoData {
  //闪送员位置纬度
  string latitude = 1;
  //闪送员位置经度
  string longitude = 2;
  //闪送员姓名
  string name = 3;
  //闪送员手机号
  string mobile = 4;
  //闪送员位置所处的当前时间
  string time = 5;
  //骑手类型 固定值1，代表众包。可忽略该值
  int32 type = 6;
  //闪送员服务总次数
  int32 orderCount = 7;
  //闪送员头像
  string headIcon = 8;
  //骑手id
  string id = 9;
  //固定值0，代表未拉黑。可忽略该值
  int32 blacklisted = 10;
  //预计送达时间文案
  string estimateDeliveryTimeTip = 11;
  //  配送过程轨迹列表
  repeated IssDeliveryProcessTrail deliveryProcessTrail = 12;
}
//送货员轨迹数据
message IssDeliveryProcessTrail {
  //闪送员位置经度
  float latitude = 1;
  //闪送员位置纬度
  float longitude = 2;
  //闪送员位置所处的当前时间
  string datetime = 3;
}
