// Code generated by protoc-gen-go. DO NOT EDIT.
// source: et/zilong.proto

package et

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ZiLongResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ZiLongResponse) Reset()         { *m = ZiLongResponse{} }
func (m *ZiLongResponse) String() string { return proto.CompactTextString(m) }
func (*ZiLongResponse) ProtoMessage()    {}
func (*ZiLongResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{0}
}

func (m *ZiLongResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZiLongResponse.Unmarshal(m, b)
}
func (m *ZiLongResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZiLongResponse.Marshal(b, m, deterministic)
}
func (m *ZiLongResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZiLongResponse.Merge(m, src)
}
func (m *ZiLongResponse) XXX_Size() int {
	return xxx_messageInfo_ZiLongResponse.Size(m)
}
func (m *ZiLongResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ZiLongResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ZiLongResponse proto.InternalMessageInfo

func (m *ZiLongResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ZiLongResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type ZiLongProductListReq struct {
	// 公司编码：新瑞鹏RPX0001，瑞云腾龙ZILONG，阿闻宠物店集团CWDJT，测试-阿闻宠物店集团awcs001
	CompanyCode string `protobuf:"bytes,1,opt,name=company_code,json=companyCode,proto3" json:"company_code"`
	// 总部产品编码
	ProductCode []string `protobuf:"bytes,2,rep,name=product_code,json=productCode,proto3" json:"product_code"`
	// 产品类型：1001药品，1002消耗品，1010商品....
	ProductType []int32 `protobuf:"varint,3,rep,packed,name=product_type,json=productType,proto3" json:"product_type"`
	// 页码，默认1
	Number int32 `protobuf:"varint,4,opt,name=number,proto3" json:"number"`
	// 页长，默认20
	Size int32 `protobuf:"varint,5,opt,name=size,proto3" json:"size"`
	// 条形码
	BarCode string `protobuf:"bytes,6,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	//商品名称
	ProductName          string   `protobuf:"bytes,7,opt,name=product_name,json=productName,proto3" json:"product_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ZiLongProductListReq) Reset()         { *m = ZiLongProductListReq{} }
func (m *ZiLongProductListReq) String() string { return proto.CompactTextString(m) }
func (*ZiLongProductListReq) ProtoMessage()    {}
func (*ZiLongProductListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{1}
}

func (m *ZiLongProductListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZiLongProductListReq.Unmarshal(m, b)
}
func (m *ZiLongProductListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZiLongProductListReq.Marshal(b, m, deterministic)
}
func (m *ZiLongProductListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZiLongProductListReq.Merge(m, src)
}
func (m *ZiLongProductListReq) XXX_Size() int {
	return xxx_messageInfo_ZiLongProductListReq.Size(m)
}
func (m *ZiLongProductListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ZiLongProductListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ZiLongProductListReq proto.InternalMessageInfo

func (m *ZiLongProductListReq) GetCompanyCode() string {
	if m != nil {
		return m.CompanyCode
	}
	return ""
}

func (m *ZiLongProductListReq) GetProductCode() []string {
	if m != nil {
		return m.ProductCode
	}
	return nil
}

func (m *ZiLongProductListReq) GetProductType() []int32 {
	if m != nil {
		return m.ProductType
	}
	return nil
}

func (m *ZiLongProductListReq) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *ZiLongProductListReq) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *ZiLongProductListReq) GetBarCode() string {
	if m != nil {
		return m.BarCode
	}
	return ""
}

func (m *ZiLongProductListReq) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

type ZiLongProductList struct {
	// 商品id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	// 条形码
	BarCode string `protobuf:"bytes,2,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	// 是否可sell，1是，0否
	CanSell string `protobuf:"bytes,3,opt,name=can_sell,json=canSell,proto3" json:"can_sell"`
	// 是否可预定，1是，0否
	CanOrder string `protobuf:"bytes,4,opt,name=can_order,json=canOrder,proto3" json:"can_order"`
	// 目录ID
	CategoryId string `protobuf:"bytes,5,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	// 目录名称 商品>用品类>厕卫类
	CategoryNamePath string `protobuf:"bytes,6,opt,name=category_name_path,json=categoryNamePath,proto3" json:"category_name_path"`
	// 是否停用，1是，0否
	Disabled string `protobuf:"bytes,7,opt,name=disabled,proto3" json:"disabled"`
	// 包装规格
	PackSpecific string `protobuf:"bytes,8,opt,name=pack_specific,json=packSpecific,proto3" json:"pack_specific"`
	// 产品编码
	ProductCode string `protobuf:"bytes,9,opt,name=product_code,json=productCode,proto3" json:"product_code"`
	// 产品名称
	ProductName string `protobuf:"bytes,10,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 产品类型：1001药品，1002消耗品，1010商品....
	ProductType string `protobuf:"bytes,11,opt,name=product_type,json=productType,proto3" json:"product_type"`
	// 产品类型名称
	ProductTypeName string `protobuf:"bytes,12,opt,name=product_type_name,json=productTypeName,proto3" json:"product_type_name"`
	// 入库含税参考价
	RefPrice string `protobuf:"bytes,13,opt,name=ref_price,json=refPrice,proto3" json:"ref_price"`
	// 销售单价
	SellPrice string `protobuf:"bytes,14,opt,name=sell_price,json=sellPrice,proto3" json:"sell_price"`
	// 出入库单位ID
	StoreUnit string `protobuf:"bytes,16,opt,name=store_unit,json=storeUnit,proto3" json:"store_unit"`
	// 出入库单位名称，袋
	StoreUnitName string `protobuf:"bytes,17,opt,name=store_unit_name,json=storeUnitName,proto3" json:"store_unit_name"`
	// 税收编码
	TaxCode string `protobuf:"bytes,21,opt,name=tax_code,json=taxCode,proto3" json:"tax_code"`
	// 投药单位字典code
	DosingUnit string `protobuf:"bytes,22,opt,name=dosing_unit,json=dosingUnit,proto3" json:"dosing_unit"`
	// 投药单位，mg
	DosingUnitName string `protobuf:"bytes,23,opt,name=dosing_unit_name,json=dosingUnitName,proto3" json:"dosing_unit_name"`
	// 投药方式code
	DosingWay string `protobuf:"bytes,24,opt,name=dosing_way,json=dosingWay,proto3" json:"dosing_way"`
	// 投药方式名称，iv
	DosingWayName string `protobuf:"bytes,25,opt,name=dosing_way_name,json=dosingWayName,proto3" json:"dosing_way_name"`
	// 投药频次
	UseFrequency string `protobuf:"bytes,26,opt,name=use_frequency,json=useFrequency,proto3" json:"use_frequency"`
	// 投药频次，每日一次
	UseFrequencyName string `protobuf:"bytes,27,opt,name=use_frequency_name,json=useFrequencyName,proto3" json:"use_frequency_name"`
	// 是否处方药,只药品有，1是。0否
	IsPrescribedDrug     string                               `protobuf:"bytes,28,opt,name=is_prescribed_drug,json=isPrescribedDrug,proto3" json:"is_prescribed_drug"`
	RecommendDosage      []*ZiLongProductList_RecommendDosage `protobuf:"bytes,29,rep,name=recommend_dosage,json=recommendDosage,proto3" json:"recommend_dosage"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *ZiLongProductList) Reset()         { *m = ZiLongProductList{} }
func (m *ZiLongProductList) String() string { return proto.CompactTextString(m) }
func (*ZiLongProductList) ProtoMessage()    {}
func (*ZiLongProductList) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{2}
}

func (m *ZiLongProductList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZiLongProductList.Unmarshal(m, b)
}
func (m *ZiLongProductList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZiLongProductList.Marshal(b, m, deterministic)
}
func (m *ZiLongProductList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZiLongProductList.Merge(m, src)
}
func (m *ZiLongProductList) XXX_Size() int {
	return xxx_messageInfo_ZiLongProductList.Size(m)
}
func (m *ZiLongProductList) XXX_DiscardUnknown() {
	xxx_messageInfo_ZiLongProductList.DiscardUnknown(m)
}

var xxx_messageInfo_ZiLongProductList proto.InternalMessageInfo

func (m *ZiLongProductList) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ZiLongProductList) GetBarCode() string {
	if m != nil {
		return m.BarCode
	}
	return ""
}

func (m *ZiLongProductList) GetCanSell() string {
	if m != nil {
		return m.CanSell
	}
	return ""
}

func (m *ZiLongProductList) GetCanOrder() string {
	if m != nil {
		return m.CanOrder
	}
	return ""
}

func (m *ZiLongProductList) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *ZiLongProductList) GetCategoryNamePath() string {
	if m != nil {
		return m.CategoryNamePath
	}
	return ""
}

func (m *ZiLongProductList) GetDisabled() string {
	if m != nil {
		return m.Disabled
	}
	return ""
}

func (m *ZiLongProductList) GetPackSpecific() string {
	if m != nil {
		return m.PackSpecific
	}
	return ""
}

func (m *ZiLongProductList) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *ZiLongProductList) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *ZiLongProductList) GetProductType() string {
	if m != nil {
		return m.ProductType
	}
	return ""
}

func (m *ZiLongProductList) GetProductTypeName() string {
	if m != nil {
		return m.ProductTypeName
	}
	return ""
}

func (m *ZiLongProductList) GetRefPrice() string {
	if m != nil {
		return m.RefPrice
	}
	return ""
}

func (m *ZiLongProductList) GetSellPrice() string {
	if m != nil {
		return m.SellPrice
	}
	return ""
}

func (m *ZiLongProductList) GetStoreUnit() string {
	if m != nil {
		return m.StoreUnit
	}
	return ""
}

func (m *ZiLongProductList) GetStoreUnitName() string {
	if m != nil {
		return m.StoreUnitName
	}
	return ""
}

func (m *ZiLongProductList) GetTaxCode() string {
	if m != nil {
		return m.TaxCode
	}
	return ""
}

func (m *ZiLongProductList) GetDosingUnit() string {
	if m != nil {
		return m.DosingUnit
	}
	return ""
}

func (m *ZiLongProductList) GetDosingUnitName() string {
	if m != nil {
		return m.DosingUnitName
	}
	return ""
}

func (m *ZiLongProductList) GetDosingWay() string {
	if m != nil {
		return m.DosingWay
	}
	return ""
}

func (m *ZiLongProductList) GetDosingWayName() string {
	if m != nil {
		return m.DosingWayName
	}
	return ""
}

func (m *ZiLongProductList) GetUseFrequency() string {
	if m != nil {
		return m.UseFrequency
	}
	return ""
}

func (m *ZiLongProductList) GetUseFrequencyName() string {
	if m != nil {
		return m.UseFrequencyName
	}
	return ""
}

func (m *ZiLongProductList) GetIsPrescribedDrug() string {
	if m != nil {
		return m.IsPrescribedDrug
	}
	return ""
}

func (m *ZiLongProductList) GetRecommendDosage() []*ZiLongProductList_RecommendDosage {
	if m != nil {
		return m.RecommendDosage
	}
	return nil
}

type ZiLongProductList_RecommendDosage struct {
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	// 用量
	Value float64 `protobuf:"fixed64,2,opt,name=value,proto3" json:"value"`
	// 宠物类型
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ZiLongProductList_RecommendDosage) Reset()         { *m = ZiLongProductList_RecommendDosage{} }
func (m *ZiLongProductList_RecommendDosage) String() string { return proto.CompactTextString(m) }
func (*ZiLongProductList_RecommendDosage) ProtoMessage()    {}
func (*ZiLongProductList_RecommendDosage) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{2, 0}
}

func (m *ZiLongProductList_RecommendDosage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZiLongProductList_RecommendDosage.Unmarshal(m, b)
}
func (m *ZiLongProductList_RecommendDosage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZiLongProductList_RecommendDosage.Marshal(b, m, deterministic)
}
func (m *ZiLongProductList_RecommendDosage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZiLongProductList_RecommendDosage.Merge(m, src)
}
func (m *ZiLongProductList_RecommendDosage) XXX_Size() int {
	return xxx_messageInfo_ZiLongProductList_RecommendDosage.Size(m)
}
func (m *ZiLongProductList_RecommendDosage) XXX_DiscardUnknown() {
	xxx_messageInfo_ZiLongProductList_RecommendDosage.DiscardUnknown(m)
}

var xxx_messageInfo_ZiLongProductList_RecommendDosage proto.InternalMessageInfo

func (m *ZiLongProductList_RecommendDosage) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *ZiLongProductList_RecommendDosage) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *ZiLongProductList_RecommendDosage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type ZiLongProductListRes struct {
	Code    int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*ZiLongProductList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	TotalCount           int32    `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ZiLongProductListRes) Reset()         { *m = ZiLongProductListRes{} }
func (m *ZiLongProductListRes) String() string { return proto.CompactTextString(m) }
func (*ZiLongProductListRes) ProtoMessage()    {}
func (*ZiLongProductListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{3}
}

func (m *ZiLongProductListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZiLongProductListRes.Unmarshal(m, b)
}
func (m *ZiLongProductListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZiLongProductListRes.Marshal(b, m, deterministic)
}
func (m *ZiLongProductListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZiLongProductListRes.Merge(m, src)
}
func (m *ZiLongProductListRes) XXX_Size() int {
	return xxx_messageInfo_ZiLongProductListRes.Size(m)
}
func (m *ZiLongProductListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ZiLongProductListRes.DiscardUnknown(m)
}

var xxx_messageInfo_ZiLongProductListRes proto.InternalMessageInfo

func (m *ZiLongProductListRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ZiLongProductListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ZiLongProductListRes) GetData() []*ZiLongProductList {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ZiLongProductListRes) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type SendCouponReq struct {
	// 模版id数组
	TemplateIdArr []int32 `protobuf:"varint,1,rep,packed,name=templateIdArr,proto3" json:"templateIdArr"`
	// 手机号数组
	PhoneArr []string `protobuf:"bytes,2,rep,name=phoneArr,proto3" json:"phoneArr"`
	// 张数
	Number int32 `protobuf:"varint,3,opt,name=number,proto3" json:"number"`
	// 应用ID
	AppId string `protobuf:"bytes,4,opt,name=appId,proto3" json:"appId"`
	// 来源
	Source               string   `protobuf:"bytes,5,opt,name=source,proto3" json:"source"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendCouponReq) Reset()         { *m = SendCouponReq{} }
func (m *SendCouponReq) String() string { return proto.CompactTextString(m) }
func (*SendCouponReq) ProtoMessage()    {}
func (*SendCouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{4}
}

func (m *SendCouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCouponReq.Unmarshal(m, b)
}
func (m *SendCouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCouponReq.Marshal(b, m, deterministic)
}
func (m *SendCouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCouponReq.Merge(m, src)
}
func (m *SendCouponReq) XXX_Size() int {
	return xxx_messageInfo_SendCouponReq.Size(m)
}
func (m *SendCouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendCouponReq proto.InternalMessageInfo

func (m *SendCouponReq) GetTemplateIdArr() []int32 {
	if m != nil {
		return m.TemplateIdArr
	}
	return nil
}

func (m *SendCouponReq) GetPhoneArr() []string {
	if m != nil {
		return m.PhoneArr
	}
	return nil
}

func (m *SendCouponReq) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *SendCouponReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *SendCouponReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type SendCouponRes struct {
	Code                 int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*SendCouponResData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SendCouponRes) Reset()         { *m = SendCouponRes{} }
func (m *SendCouponRes) String() string { return proto.CompactTextString(m) }
func (*SendCouponRes) ProtoMessage()    {}
func (*SendCouponRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{5}
}

func (m *SendCouponRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCouponRes.Unmarshal(m, b)
}
func (m *SendCouponRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCouponRes.Marshal(b, m, deterministic)
}
func (m *SendCouponRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCouponRes.Merge(m, src)
}
func (m *SendCouponRes) XXX_Size() int {
	return xxx_messageInfo_SendCouponRes.Size(m)
}
func (m *SendCouponRes) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCouponRes.DiscardUnknown(m)
}

var xxx_messageInfo_SendCouponRes proto.InternalMessageInfo

func (m *SendCouponRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SendCouponRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SendCouponRes) GetData() []*SendCouponResData {
	if m != nil {
		return m.Data
	}
	return nil
}

type SendCouponResData struct {
	CouponTemplateId        int32                     `protobuf:"varint,1,opt,name=couponTemplateId,proto3" json:"couponTemplateId"`
	CouponName              string                    `protobuf:"bytes,2,opt,name=couponName,proto3" json:"couponName"`
	CouponNotice            string                    `protobuf:"bytes,3,opt,name=couponNotice,proto3" json:"couponNotice"`
	SettlementPrice         string                    `protobuf:"bytes,4,opt,name=settlementPrice,proto3" json:"settlementPrice"`
	Category                string                    `protobuf:"bytes,5,opt,name=category,proto3" json:"category"`
	Channel                 string                    `protobuf:"bytes,6,opt,name=channel,proto3" json:"channel"`
	PeriodValidityBeginTime string                    `protobuf:"bytes,7,opt,name=periodValidityBeginTime,proto3" json:"periodValidityBeginTime"`
	PeriodValidityEndTime   string                    `protobuf:"bytes,8,opt,name=periodValidityEndTime,proto3" json:"periodValidityEndTime"`
	MinConsumption          string                    `protobuf:"bytes,9,opt,name=minConsumption,proto3" json:"minConsumption"`
	TemplateValue           string                    `protobuf:"bytes,10,opt,name=templateValue,proto3" json:"templateValue"`
	CouponList              []*SendCouponResData_List `protobuf:"bytes,11,rep,name=couponList,proto3" json:"couponList"`
	XXX_NoUnkeyedLiteral    struct{}                  `json:"-"`
	XXX_unrecognized        []byte                    `json:"-"`
	XXX_sizecache           int32                     `json:"-"`
}

func (m *SendCouponResData) Reset()         { *m = SendCouponResData{} }
func (m *SendCouponResData) String() string { return proto.CompactTextString(m) }
func (*SendCouponResData) ProtoMessage()    {}
func (*SendCouponResData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{6}
}

func (m *SendCouponResData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCouponResData.Unmarshal(m, b)
}
func (m *SendCouponResData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCouponResData.Marshal(b, m, deterministic)
}
func (m *SendCouponResData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCouponResData.Merge(m, src)
}
func (m *SendCouponResData) XXX_Size() int {
	return xxx_messageInfo_SendCouponResData.Size(m)
}
func (m *SendCouponResData) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCouponResData.DiscardUnknown(m)
}

var xxx_messageInfo_SendCouponResData proto.InternalMessageInfo

func (m *SendCouponResData) GetCouponTemplateId() int32 {
	if m != nil {
		return m.CouponTemplateId
	}
	return 0
}

func (m *SendCouponResData) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *SendCouponResData) GetCouponNotice() string {
	if m != nil {
		return m.CouponNotice
	}
	return ""
}

func (m *SendCouponResData) GetSettlementPrice() string {
	if m != nil {
		return m.SettlementPrice
	}
	return ""
}

func (m *SendCouponResData) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *SendCouponResData) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *SendCouponResData) GetPeriodValidityBeginTime() string {
	if m != nil {
		return m.PeriodValidityBeginTime
	}
	return ""
}

func (m *SendCouponResData) GetPeriodValidityEndTime() string {
	if m != nil {
		return m.PeriodValidityEndTime
	}
	return ""
}

func (m *SendCouponResData) GetMinConsumption() string {
	if m != nil {
		return m.MinConsumption
	}
	return ""
}

func (m *SendCouponResData) GetTemplateValue() string {
	if m != nil {
		return m.TemplateValue
	}
	return ""
}

func (m *SendCouponResData) GetCouponList() []*SendCouponResData_List {
	if m != nil {
		return m.CouponList
	}
	return nil
}

type SendCouponResData_List struct {
	CouponId             int32    `protobuf:"varint,1,opt,name=couponId,proto3" json:"couponId"`
	CouponCode           string   `protobuf:"bytes,2,opt,name=couponCode,proto3" json:"couponCode"`
	UserPhone            string   `protobuf:"bytes,3,opt,name=userPhone,proto3" json:"userPhone"`
	Uuid                 string   `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendCouponResData_List) Reset()         { *m = SendCouponResData_List{} }
func (m *SendCouponResData_List) String() string { return proto.CompactTextString(m) }
func (*SendCouponResData_List) ProtoMessage()    {}
func (*SendCouponResData_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{6, 0}
}

func (m *SendCouponResData_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCouponResData_List.Unmarshal(m, b)
}
func (m *SendCouponResData_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCouponResData_List.Marshal(b, m, deterministic)
}
func (m *SendCouponResData_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCouponResData_List.Merge(m, src)
}
func (m *SendCouponResData_List) XXX_Size() int {
	return xxx_messageInfo_SendCouponResData_List.Size(m)
}
func (m *SendCouponResData_List) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCouponResData_List.DiscardUnknown(m)
}

var xxx_messageInfo_SendCouponResData_List proto.InternalMessageInfo

func (m *SendCouponResData_List) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *SendCouponResData_List) GetCouponCode() string {
	if m != nil {
		return m.CouponCode
	}
	return ""
}

func (m *SendCouponResData_List) GetUserPhone() string {
	if m != nil {
		return m.UserPhone
	}
	return ""
}

func (m *SendCouponResData_List) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

type GetVerifyDetailReq struct {
	// 券编号集合
	CouponCodes          []string `protobuf:"bytes,1,rep,name=couponCodes,proto3" json:"couponCodes"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVerifyDetailReq) Reset()         { *m = GetVerifyDetailReq{} }
func (m *GetVerifyDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetVerifyDetailReq) ProtoMessage()    {}
func (*GetVerifyDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{7}
}

func (m *GetVerifyDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVerifyDetailReq.Unmarshal(m, b)
}
func (m *GetVerifyDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVerifyDetailReq.Marshal(b, m, deterministic)
}
func (m *GetVerifyDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVerifyDetailReq.Merge(m, src)
}
func (m *GetVerifyDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetVerifyDetailReq.Size(m)
}
func (m *GetVerifyDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVerifyDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVerifyDetailReq proto.InternalMessageInfo

func (m *GetVerifyDetailReq) GetCouponCodes() []string {
	if m != nil {
		return m.CouponCodes
	}
	return nil
}

type GetVerifyDetailResp struct {
	Code                 int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*GetVerifyDetailRespData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetVerifyDetailResp) Reset()         { *m = GetVerifyDetailResp{} }
func (m *GetVerifyDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetVerifyDetailResp) ProtoMessage()    {}
func (*GetVerifyDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{8}
}

func (m *GetVerifyDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVerifyDetailResp.Unmarshal(m, b)
}
func (m *GetVerifyDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVerifyDetailResp.Marshal(b, m, deterministic)
}
func (m *GetVerifyDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVerifyDetailResp.Merge(m, src)
}
func (m *GetVerifyDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetVerifyDetailResp.Size(m)
}
func (m *GetVerifyDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVerifyDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVerifyDetailResp proto.InternalMessageInfo

func (m *GetVerifyDetailResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetVerifyDetailResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetVerifyDetailResp) GetData() []*GetVerifyDetailRespData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetVerifyDetailRespData struct {
	CouponId   string `protobuf:"bytes,1,opt,name=couponId,proto3" json:"couponId"`
	CouponCode string `protobuf:"bytes,2,opt,name=couponCode,proto3" json:"couponCode"`
	// 状态: 1 未使用, 2 已使用, 3 已过期，4 已废弃'
	Status               string   `protobuf:"bytes,3,opt,name=status,proto3" json:"status"`
	CouponUseTime        string   `protobuf:"bytes,4,opt,name=couponUseTime,proto3" json:"couponUseTime"`
	StructName           string   `protobuf:"bytes,5,opt,name=structName,proto3" json:"structName"`
	StructCode           string   `protobuf:"bytes,6,opt,name=structCode,proto3" json:"structCode"`
	OrderNo              string   `protobuf:"bytes,7,opt,name=orderNo,proto3" json:"orderNo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVerifyDetailRespData) Reset()         { *m = GetVerifyDetailRespData{} }
func (m *GetVerifyDetailRespData) String() string { return proto.CompactTextString(m) }
func (*GetVerifyDetailRespData) ProtoMessage()    {}
func (*GetVerifyDetailRespData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{9}
}

func (m *GetVerifyDetailRespData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVerifyDetailRespData.Unmarshal(m, b)
}
func (m *GetVerifyDetailRespData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVerifyDetailRespData.Marshal(b, m, deterministic)
}
func (m *GetVerifyDetailRespData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVerifyDetailRespData.Merge(m, src)
}
func (m *GetVerifyDetailRespData) XXX_Size() int {
	return xxx_messageInfo_GetVerifyDetailRespData.Size(m)
}
func (m *GetVerifyDetailRespData) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVerifyDetailRespData.DiscardUnknown(m)
}

var xxx_messageInfo_GetVerifyDetailRespData proto.InternalMessageInfo

func (m *GetVerifyDetailRespData) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *GetVerifyDetailRespData) GetCouponCode() string {
	if m != nil {
		return m.CouponCode
	}
	return ""
}

func (m *GetVerifyDetailRespData) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *GetVerifyDetailRespData) GetCouponUseTime() string {
	if m != nil {
		return m.CouponUseTime
	}
	return ""
}

func (m *GetVerifyDetailRespData) GetStructName() string {
	if m != nil {
		return m.StructName
	}
	return ""
}

func (m *GetVerifyDetailRespData) GetStructCode() string {
	if m != nil {
		return m.StructCode
	}
	return ""
}

func (m *GetVerifyDetailRespData) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

type DiscountCardNewReq struct {
	// 财务编码
	EppCode string `protobuf:"bytes,1,opt,name=eppCode,proto3" json:"eppCode"`
	// scrmId
	CustomerId string `protobuf:"bytes,2,opt,name=customerId,proto3" json:"customerId"`
	// scrm卡类别code
	ScrmCardsCategoryCode string `protobuf:"bytes,3,opt,name=scrmCardsCategoryCode,proto3" json:"scrmCardsCategoryCode"`
	// 创建来源（0:子龙 1:小暖 2:瑞鹏 3:新宠 4:保险 5:阿闻 6:众嘉 7:考拉）
	CreateSource int32 `protobuf:"varint,4,opt,name=createSource,proto3" json:"createSource"`
	// 卡开始时间
	StartTime string `protobuf:"bytes,5,opt,name=startTime,proto3" json:"startTime"`
	// 卡结束时间
	EndTime              string   `protobuf:"bytes,6,opt,name=endTime,proto3" json:"endTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiscountCardNewReq) Reset()         { *m = DiscountCardNewReq{} }
func (m *DiscountCardNewReq) String() string { return proto.CompactTextString(m) }
func (*DiscountCardNewReq) ProtoMessage()    {}
func (*DiscountCardNewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{10}
}

func (m *DiscountCardNewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscountCardNewReq.Unmarshal(m, b)
}
func (m *DiscountCardNewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscountCardNewReq.Marshal(b, m, deterministic)
}
func (m *DiscountCardNewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscountCardNewReq.Merge(m, src)
}
func (m *DiscountCardNewReq) XXX_Size() int {
	return xxx_messageInfo_DiscountCardNewReq.Size(m)
}
func (m *DiscountCardNewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscountCardNewReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscountCardNewReq proto.InternalMessageInfo

func (m *DiscountCardNewReq) GetEppCode() string {
	if m != nil {
		return m.EppCode
	}
	return ""
}

func (m *DiscountCardNewReq) GetCustomerId() string {
	if m != nil {
		return m.CustomerId
	}
	return ""
}

func (m *DiscountCardNewReq) GetScrmCardsCategoryCode() string {
	if m != nil {
		return m.ScrmCardsCategoryCode
	}
	return ""
}

func (m *DiscountCardNewReq) GetCreateSource() int32 {
	if m != nil {
		return m.CreateSource
	}
	return 0
}

func (m *DiscountCardNewReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *DiscountCardNewReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type DiscountCardNewRes struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 会员卡号
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiscountCardNewRes) Reset()         { *m = DiscountCardNewRes{} }
func (m *DiscountCardNewRes) String() string { return proto.CompactTextString(m) }
func (*DiscountCardNewRes) ProtoMessage()    {}
func (*DiscountCardNewRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{11}
}

func (m *DiscountCardNewRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscountCardNewRes.Unmarshal(m, b)
}
func (m *DiscountCardNewRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscountCardNewRes.Marshal(b, m, deterministic)
}
func (m *DiscountCardNewRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscountCardNewRes.Merge(m, src)
}
func (m *DiscountCardNewRes) XXX_Size() int {
	return xxx_messageInfo_DiscountCardNewRes.Size(m)
}
func (m *DiscountCardNewRes) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscountCardNewRes.DiscardUnknown(m)
}

var xxx_messageInfo_DiscountCardNewRes proto.InternalMessageInfo

func (m *DiscountCardNewRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DiscountCardNewRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DiscountCardNewRes) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type DiscountCardRefundReq struct {
	// 卡号
	EnsureCode string `protobuf:"bytes,1,opt,name=ensureCode,proto3" json:"ensureCode"`
	// scrmId
	CustomerId string `protobuf:"bytes,2,opt,name=customerId,proto3" json:"customerId"`
	// 创建来源（0:子龙 1:小暖 2:瑞鹏 3:新宠 4:保险 5:阿闻 6:众嘉 7:考拉）
	CreateSource int32 `protobuf:"varint,4,opt,name=createSource,proto3" json:"createSource"`
	// 卡结束时间,如果没填，算关联的所有储值卡效期，如果效期都<当前时间，则默认当前时间；  如果储值卡效期大于传值，则按储值卡效期；储值卡效期小于传值，取传值日期
	EndTime              string   `protobuf:"bytes,5,opt,name=endTime,proto3" json:"endTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiscountCardRefundReq) Reset()         { *m = DiscountCardRefundReq{} }
func (m *DiscountCardRefundReq) String() string { return proto.CompactTextString(m) }
func (*DiscountCardRefundReq) ProtoMessage()    {}
func (*DiscountCardRefundReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{12}
}

func (m *DiscountCardRefundReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscountCardRefundReq.Unmarshal(m, b)
}
func (m *DiscountCardRefundReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscountCardRefundReq.Marshal(b, m, deterministic)
}
func (m *DiscountCardRefundReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscountCardRefundReq.Merge(m, src)
}
func (m *DiscountCardRefundReq) XXX_Size() int {
	return xxx_messageInfo_DiscountCardRefundReq.Size(m)
}
func (m *DiscountCardRefundReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscountCardRefundReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscountCardRefundReq proto.InternalMessageInfo

func (m *DiscountCardRefundReq) GetEnsureCode() string {
	if m != nil {
		return m.EnsureCode
	}
	return ""
}

func (m *DiscountCardRefundReq) GetCustomerId() string {
	if m != nil {
		return m.CustomerId
	}
	return ""
}

func (m *DiscountCardRefundReq) GetCreateSource() int32 {
	if m != nil {
		return m.CreateSource
	}
	return 0
}

func (m *DiscountCardRefundReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type WasteCouponReq struct {
	// 模版id数组
	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids"`
	//操作人
	Operator string `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator"`
	// 操作人id/编码
	OperatorId string `protobuf:"bytes,3,opt,name=operatorId,proto3" json:"operatorId"`
	//优惠券code，数组
	CouponCodes          []string `protobuf:"bytes,4,rep,name=couponCodes,proto3" json:"couponCodes"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WasteCouponReq) Reset()         { *m = WasteCouponReq{} }
func (m *WasteCouponReq) String() string { return proto.CompactTextString(m) }
func (*WasteCouponReq) ProtoMessage()    {}
func (*WasteCouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{13}
}

func (m *WasteCouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WasteCouponReq.Unmarshal(m, b)
}
func (m *WasteCouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WasteCouponReq.Marshal(b, m, deterministic)
}
func (m *WasteCouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WasteCouponReq.Merge(m, src)
}
func (m *WasteCouponReq) XXX_Size() int {
	return xxx_messageInfo_WasteCouponReq.Size(m)
}
func (m *WasteCouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_WasteCouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_WasteCouponReq proto.InternalMessageInfo

func (m *WasteCouponReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *WasteCouponReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *WasteCouponReq) GetOperatorId() string {
	if m != nil {
		return m.OperatorId
	}
	return ""
}

func (m *WasteCouponReq) GetCouponCodes() []string {
	if m != nil {
		return m.CouponCodes
	}
	return nil
}

type GetCouponStatusReq struct {
	// 门店券id，数组
	UsercouponIds []string `protobuf:"bytes,1,rep,name=usercouponIds,proto3" json:"usercouponIds"`
	// 门店券code，数组
	UsercouponCodes      []string `protobuf:"bytes,2,rep,name=usercouponCodes,proto3" json:"usercouponCodes"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponStatusReq) Reset()         { *m = GetCouponStatusReq{} }
func (m *GetCouponStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetCouponStatusReq) ProtoMessage()    {}
func (*GetCouponStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{14}
}

func (m *GetCouponStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponStatusReq.Unmarshal(m, b)
}
func (m *GetCouponStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponStatusReq.Marshal(b, m, deterministic)
}
func (m *GetCouponStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponStatusReq.Merge(m, src)
}
func (m *GetCouponStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetCouponStatusReq.Size(m)
}
func (m *GetCouponStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponStatusReq proto.InternalMessageInfo

func (m *GetCouponStatusReq) GetUsercouponIds() []string {
	if m != nil {
		return m.UsercouponIds
	}
	return nil
}

func (m *GetCouponStatusReq) GetUsercouponCodes() []string {
	if m != nil {
		return m.UsercouponCodes
	}
	return nil
}

type GetCouponStatusResp struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 报错信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 业务数据
	Data []*GetCouponStatusData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 请求状态码
	Status               int32    `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponStatusResp) Reset()         { *m = GetCouponStatusResp{} }
func (m *GetCouponStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetCouponStatusResp) ProtoMessage()    {}
func (*GetCouponStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{15}
}

func (m *GetCouponStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponStatusResp.Unmarshal(m, b)
}
func (m *GetCouponStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponStatusResp.Marshal(b, m, deterministic)
}
func (m *GetCouponStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponStatusResp.Merge(m, src)
}
func (m *GetCouponStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetCouponStatusResp.Size(m)
}
func (m *GetCouponStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponStatusResp proto.InternalMessageInfo

func (m *GetCouponStatusResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCouponStatusResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *GetCouponStatusResp) GetData() []*GetCouponStatusData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GetCouponStatusResp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetCouponStatusData struct {
	// 券id
	CouponId int32 `protobuf:"varint,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	// 券码
	CouponCode string `protobuf:"bytes,2,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code"`
	//状态: 1 未使用, 2 已使用, 3 已过期，4 已废弃
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	//是否退单，1是，0否
	IsRefund             int32    `protobuf:"varint,4,opt,name=is_refund,json=isRefund,proto3" json:"is_refund"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponStatusData) Reset()         { *m = GetCouponStatusData{} }
func (m *GetCouponStatusData) String() string { return proto.CompactTextString(m) }
func (*GetCouponStatusData) ProtoMessage()    {}
func (*GetCouponStatusData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5984d635abb44789, []int{16}
}

func (m *GetCouponStatusData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponStatusData.Unmarshal(m, b)
}
func (m *GetCouponStatusData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponStatusData.Marshal(b, m, deterministic)
}
func (m *GetCouponStatusData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponStatusData.Merge(m, src)
}
func (m *GetCouponStatusData) XXX_Size() int {
	return xxx_messageInfo_GetCouponStatusData.Size(m)
}
func (m *GetCouponStatusData) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponStatusData.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponStatusData proto.InternalMessageInfo

func (m *GetCouponStatusData) GetCouponId() int32 {
	if m != nil {
		return m.CouponId
	}
	return 0
}

func (m *GetCouponStatusData) GetCouponCode() string {
	if m != nil {
		return m.CouponCode
	}
	return ""
}

func (m *GetCouponStatusData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetCouponStatusData) GetIsRefund() int32 {
	if m != nil {
		return m.IsRefund
	}
	return 0
}

func init() {
	proto.RegisterType((*ZiLongResponse)(nil), "et.ZiLongResponse")
	proto.RegisterType((*ZiLongProductListReq)(nil), "et.ZiLongProductListReq")
	proto.RegisterType((*ZiLongProductList)(nil), "et.ZiLongProductList")
	proto.RegisterType((*ZiLongProductList_RecommendDosage)(nil), "et.ZiLongProductList.RecommendDosage")
	proto.RegisterType((*ZiLongProductListRes)(nil), "et.ZiLongProductListRes")
	proto.RegisterType((*SendCouponReq)(nil), "et.SendCouponReq")
	proto.RegisterType((*SendCouponRes)(nil), "et.SendCouponRes")
	proto.RegisterType((*SendCouponResData)(nil), "et.SendCouponResData")
	proto.RegisterType((*SendCouponResData_List)(nil), "et.SendCouponResData.List")
	proto.RegisterType((*GetVerifyDetailReq)(nil), "et.GetVerifyDetailReq")
	proto.RegisterType((*GetVerifyDetailResp)(nil), "et.GetVerifyDetailResp")
	proto.RegisterType((*GetVerifyDetailRespData)(nil), "et.GetVerifyDetailRespData")
	proto.RegisterType((*DiscountCardNewReq)(nil), "et.DiscountCardNewReq")
	proto.RegisterType((*DiscountCardNewRes)(nil), "et.DiscountCardNewRes")
	proto.RegisterType((*DiscountCardRefundReq)(nil), "et.DiscountCardRefundReq")
	proto.RegisterType((*WasteCouponReq)(nil), "et.WasteCouponReq")
	proto.RegisterType((*GetCouponStatusReq)(nil), "et.GetCouponStatusReq")
	proto.RegisterType((*GetCouponStatusResp)(nil), "et.GetCouponStatusResp")
	proto.RegisterType((*GetCouponStatusData)(nil), "et.GetCouponStatusData")
}

func init() { proto.RegisterFile("et/zilong.proto", fileDescriptor_5984d635abb44789) }

var fileDescriptor_5984d635abb44789 = []byte{
	// 1503 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xcd, 0x6e, 0x1b, 0x47,
	0x12, 0x06, 0x45, 0xd1, 0x12, 0x8b, 0xa2, 0x48, 0xb5, 0x2d, 0x6b, 0x44, 0xf9, 0x47, 0xa6, 0x77,
	0x0d, 0xad, 0xd7, 0x90, 0x01, 0xed, 0x26, 0x08, 0x72, 0x08, 0x60, 0x53, 0x49, 0x20, 0xc0, 0xb0,
	0x05, 0xca, 0x96, 0x81, 0x5c, 0x06, 0xad, 0x99, 0x12, 0xd5, 0x08, 0x67, 0x7a, 0xd4, 0xdd, 0x63,
	0x9b, 0x3e, 0xe5, 0x98, 0x63, 0x2e, 0x41, 0x9e, 0x2a, 0xa7, 0xbc, 0x40, 0xae, 0x39, 0x04, 0x79,
	0x85, 0xa0, 0x7f, 0xe6, 0x8f, 0x43, 0x21, 0x11, 0x90, 0xdb, 0xd4, 0xd7, 0x1f, 0xab, 0xab, 0xaa,
	0xab, 0xbe, 0x9e, 0x21, 0xf4, 0x50, 0x3d, 0xfd, 0xc8, 0xa6, 0x3c, 0x9e, 0xec, 0x27, 0x82, 0x2b,
	0x4e, 0x96, 0x50, 0x0d, 0xbf, 0x80, 0xf5, 0x6f, 0xd8, 0x0b, 0x1e, 0x4f, 0xc6, 0x28, 0x13, 0x1e,
	0x4b, 0x24, 0x04, 0x96, 0x03, 0x1e, 0xa2, 0xd7, 0xd8, 0x6d, 0xec, 0xb5, 0xc6, 0xe6, 0x99, 0x78,
	0xb0, 0x12, 0xa1, 0x94, 0x74, 0x82, 0xde, 0xd2, 0x6e, 0x63, 0xaf, 0x3d, 0xce, 0xcc, 0xe1, 0x6f,
	0x0d, 0xb8, 0x65, 0x1d, 0x1c, 0x0b, 0x1e, 0xa6, 0x81, 0x7a, 0xc1, 0xa4, 0x1a, 0xe3, 0x25, 0x79,
	0x00, 0x6b, 0x01, 0x8f, 0x12, 0x1a, 0xcf, 0xfc, 0xdc, 0x5d, 0x7b, 0xdc, 0x71, 0xd8, 0x48, 0x7b,
	0x7d, 0x00, 0x6b, 0x89, 0xfd, 0x91, 0xa5, 0x2c, 0xed, 0x36, 0x35, 0xc5, 0x61, 0xf3, 0x14, 0x35,
	0x4b, 0xd0, 0x6b, 0xee, 0x36, 0xf7, 0x5a, 0x39, 0xe5, 0xf5, 0x2c, 0x41, 0x72, 0x1b, 0x6e, 0xc4,
	0x69, 0x74, 0x86, 0xc2, 0x5b, 0x36, 0x11, 0x3b, 0x4b, 0xe7, 0x21, 0xd9, 0x47, 0xf4, 0x5a, 0x36,
	0x0f, 0xfd, 0x4c, 0xb6, 0x61, 0xf5, 0x8c, 0x0a, 0xbb, 0xdb, 0x0d, 0x9b, 0xc8, 0x19, 0x15, 0xf3,
	0x3b, 0xc5, 0x34, 0x42, 0x6f, 0xc5, 0xc6, 0xeb, 0xb0, 0x97, 0x34, 0xc2, 0xe1, 0x1f, 0x2b, 0xb0,
	0x51, 0xcb, 0x95, 0xac, 0xc3, 0x12, 0x0b, 0x5d, 0x7a, 0x4b, 0x2c, 0xac, 0xec, 0xb1, 0x54, 0xdd,
	0x63, 0x1b, 0x56, 0x03, 0x1a, 0xfb, 0x12, 0xa7, 0x53, 0xaf, 0x69, 0x97, 0x02, 0x1a, 0x9f, 0xe0,
	0x74, 0x4a, 0x76, 0xa0, 0xad, 0x97, 0xb8, 0x08, 0x5d, 0x22, 0xed, 0xb1, 0xe6, 0xbe, 0xd2, 0x36,
	0xb9, 0x0f, 0x9d, 0x80, 0x2a, 0x9c, 0x70, 0x31, 0xf3, 0x59, 0x68, 0x32, 0x6a, 0x8f, 0x21, 0x83,
	0x8e, 0x42, 0xf2, 0x04, 0x48, 0x4e, 0xd0, 0xd1, 0xfb, 0x09, 0x55, 0x17, 0x2e, 0xc3, 0x7e, 0xb6,
	0xa2, 0x73, 0x38, 0xa6, 0xea, 0x82, 0x0c, 0x60, 0x35, 0x64, 0x92, 0x9e, 0x4d, 0x31, 0x74, 0x69,
	0xe6, 0x36, 0x79, 0x08, 0xdd, 0x84, 0x06, 0xdf, 0xfa, 0x32, 0xc1, 0x80, 0x9d, 0xb3, 0xc0, 0x5b,
	0x35, 0x84, 0x35, 0x0d, 0x9e, 0x38, 0xac, 0x76, 0x70, 0xed, 0x4a, 0xad, 0x16, 0x96, 0x13, 0x6a,
	0xe5, 0xac, 0x9d, 0x6d, 0xa7, 0x42, 0x31, 0x67, 0xfb, 0x18, 0x36, 0xca, 0x14, 0xeb, 0x6a, 0xcd,
	0xf0, 0x7a, 0x25, 0x9e, 0x71, 0xb7, 0x03, 0x6d, 0x81, 0xe7, 0x7e, 0x22, 0x58, 0x80, 0x5e, 0xd7,
	0xa6, 0x25, 0xf0, 0xfc, 0x58, 0xdb, 0xe4, 0x2e, 0x80, 0xae, 0xba, 0x5b, 0x5d, 0x37, 0xab, 0x6d,
	0x8d, 0x14, 0xcb, 0x8a, 0x0b, 0xf4, 0xd3, 0x98, 0x29, 0xaf, 0xef, 0x96, 0x35, 0xf2, 0x26, 0x66,
	0x8a, 0x3c, 0x82, 0x5e, 0xb1, 0x6c, 0x83, 0xd8, 0x30, 0x9c, 0x6e, 0xce, 0x31, 0x21, 0x6c, 0xc3,
	0xaa, 0xa2, 0x1f, 0x6c, 0x4d, 0x36, 0xed, 0xf9, 0x2a, 0xfa, 0xc1, 0xd4, 0xe3, 0x3e, 0x74, 0x42,
	0x2e, 0x59, 0x3c, 0xb1, 0x5b, 0xdc, 0xb6, 0x47, 0x68, 0x21, 0xb3, 0xc7, 0x1e, 0xf4, 0x4b, 0x04,
	0xbb, 0xc9, 0x96, 0x61, 0xad, 0x17, 0x2c, 0xb3, 0xcb, 0x5d, 0x70, 0xbf, 0xf3, 0xdf, 0xd3, 0x99,
	0xe7, 0xd9, 0x60, 0x2d, 0xf2, 0x96, 0xce, 0x74, 0xb0, 0xc5, 0xb2, 0xf5, 0xb3, 0x6d, 0x83, 0xcd,
	0x39, 0xc6, 0xcd, 0x43, 0xe8, 0xa6, 0x12, 0xfd, 0x73, 0x81, 0x97, 0x29, 0xc6, 0xc1, 0xcc, 0x1b,
	0xd8, 0x93, 0x4e, 0x25, 0x7e, 0x95, 0x61, 0xba, 0xb1, 0x2a, 0x24, 0xeb, 0x6f, 0xc7, 0x36, 0x56,
	0x99, 0x69, 0x5c, 0x3e, 0x01, 0xc2, 0xa4, 0x9f, 0x08, 0x94, 0x81, 0x60, 0x67, 0x18, 0xfa, 0xa1,
	0x48, 0x27, 0xde, 0x1d, 0xcb, 0x66, 0xf2, 0x38, 0x5f, 0x38, 0x14, 0xe9, 0x84, 0x1c, 0x43, 0x5f,
	0x60, 0xc0, 0xa3, 0x08, 0xe3, 0xd0, 0x0f, 0xb9, 0x51, 0x97, 0xbb, 0xbb, 0xcd, 0xbd, 0xce, 0xc1,
	0xbf, 0xf7, 0x51, 0xed, 0xd7, 0x26, 0x6d, 0x7f, 0x9c, 0xb1, 0x0f, 0x0d, 0x79, 0xdc, 0x13, 0x55,
	0x60, 0xf0, 0x0a, 0x7a, 0x73, 0x9c, 0x8a, 0x9a, 0xb5, 0x9d, 0x9a, 0xdd, 0x82, 0xd6, 0x3b, 0x3a,
	0x4d, 0xed, 0x78, 0x36, 0xc6, 0xd6, 0xd0, 0x4c, 0x93, 0x9c, 0x1d, 0x4c, 0xf3, 0x3c, 0xfc, 0x61,
	0xb1, 0xba, 0xc9, 0xeb, 0x89, 0x24, 0xf9, 0x0f, 0x2c, 0x87, 0x54, 0x51, 0xa3, 0x5e, 0x9d, 0x83,
	0xcd, 0x85, 0xd9, 0x8d, 0x0d, 0x45, 0xf7, 0x89, 0xe2, 0x8a, 0x4e, 0xfd, 0x80, 0xa7, 0xb1, 0x72,
	0x92, 0x06, 0x06, 0x1a, 0x69, 0x64, 0xf8, 0x53, 0x03, 0xba, 0x27, 0x18, 0x87, 0x23, 0x9e, 0x26,
	0x3c, 0xd6, 0x4a, 0xfb, 0x2f, 0xe8, 0x2a, 0x8c, 0x92, 0x29, 0x55, 0x78, 0x14, 0x3e, 0x13, 0xc2,
	0x6b, 0x18, 0x91, 0xac, 0x82, 0x7a, 0xe8, 0x93, 0x0b, 0x1e, 0xa3, 0x26, 0x58, 0xa1, 0xcd, 0xed,
	0x92, 0x84, 0x36, 0x2b, 0x12, 0x7a, 0x0b, 0x5a, 0x34, 0x49, 0x8e, 0x42, 0x27, 0x48, 0xd6, 0xd0,
	0x6c, 0xc9, 0x53, 0x11, 0xa0, 0x13, 0x22, 0x67, 0x0d, 0x2f, 0xaa, 0x81, 0xfd, 0x03, 0x45, 0xaa,
	0xb8, 0x3b, 0xa4, 0x8a, 0xda, 0x22, 0x0d, 0x7f, 0x59, 0x86, 0x8d, 0xda, 0x1a, 0x79, 0x0c, 0xfd,
	0xc0, 0x00, 0xaf, 0xf3, 0xc4, 0xdd, 0xd6, 0x35, 0x9c, 0xdc, 0x03, 0xb0, 0x98, 0xee, 0x5b, 0x17,
	0x49, 0x09, 0x21, 0x43, 0x7d, 0x7b, 0x19, 0x8b, 0x2b, 0xad, 0x18, 0xb6, 0x29, 0x2a, 0x18, 0xd9,
	0x83, 0x9e, 0x44, 0xa5, 0xa6, 0x18, 0x61, 0xac, 0x8c, 0x8e, 0xb8, 0x3a, 0xcd, 0xc3, 0xba, 0xf6,
	0x99, 0x08, 0xbb, 0x9a, 0xe5, 0xb6, 0x2e, 0x48, 0x70, 0x41, 0xe3, 0x18, 0xa7, 0xd9, 0x8d, 0xe4,
	0x4c, 0xf2, 0x19, 0x6c, 0x25, 0x28, 0x18, 0x0f, 0x4f, 0xe9, 0x94, 0x85, 0x4c, 0xcd, 0x9e, 0xe3,
	0x84, 0xc5, 0xaf, 0x59, 0x7e, 0x39, 0x5d, 0xb5, 0x4c, 0xfe, 0x0f, 0x9b, 0xd5, 0xa5, 0x2f, 0xe3,
	0xd0, 0xfc, 0xce, 0x8a, 0xf9, 0xe2, 0x45, 0xf2, 0x08, 0xd6, 0x23, 0x16, 0x8f, 0x78, 0x2c, 0xd3,
	0x28, 0x51, 0x8c, 0xc7, 0x4e, 0xd7, 0xe7, 0xd0, 0x72, 0xbf, 0x9d, 0x9a, 0x31, 0xb2, 0xda, 0x5e,
	0x05, 0xc9, 0xe7, 0x59, 0x85, 0x75, 0x73, 0x7b, 0x1d, 0x73, 0xa8, 0x83, 0x85, 0x87, 0xba, 0x6f,
	0xda, 0xbf, 0xc4, 0x1e, 0x28, 0x58, 0x36, 0x57, 0xab, 0xae, 0x9b, 0x41, 0xf3, 0x93, 0xcc, 0xed,
	0xe2, 0x04, 0x47, 0xc5, 0x45, 0x5b, 0x42, 0xc8, 0x1d, 0x68, 0xa7, 0x12, 0xc5, 0xb1, 0xee, 0x71,
	0x77, 0x7c, 0x05, 0xa0, 0x5b, 0x33, 0x4d, 0x59, 0xd6, 0xd8, 0xe6, 0x79, 0xf8, 0x29, 0x90, 0xaf,
	0x51, 0x9d, 0xa2, 0x60, 0xe7, 0xb3, 0x43, 0x54, 0x94, 0x4d, 0xf5, 0x74, 0xed, 0x42, 0xa7, 0xf0,
	0x2a, 0xcd, 0x6c, 0x99, 0xd7, 0x98, 0x1c, 0x1a, 0x2a, 0xb8, 0x59, 0xfb, 0x9d, 0x4c, 0xae, 0xd9,
	0xfd, 0x4f, 0x2b, 0xdd, 0xbf, 0xa3, 0x0b, 0xb5, 0xc0, 0x69, 0x69, 0x06, 0x7e, 0x6f, 0xc0, 0xd6,
	0x15, 0x8c, 0x5a, 0xdd, 0xda, 0xd7, 0xa8, 0x9b, 0x9e, 0x6e, 0x45, 0x55, 0x2a, 0x5d, 0xd1, 0x9c,
	0xa5, 0x4f, 0xdd, 0xb2, 0xde, 0x48, 0x34, 0xbd, 0x64, 0x4b, 0x57, 0x05, 0xb5, 0x77, 0xa9, 0x84,
	0xbb, 0xe1, 0xb3, 0x17, 0x95, 0x02, 0x29, 0xd6, 0x47, 0xc5, 0x2b, 0x58, 0x09, 0xd1, 0x05, 0x32,
	0xaf, 0x40, 0x2f, 0xb9, 0xeb, 0xf1, 0xcc, 0x1c, 0xfe, 0xda, 0x00, 0x72, 0xc8, 0xa4, 0x91, 0xc5,
	0x11, 0x15, 0xe1, 0x4b, 0x7c, 0xaf, 0x8f, 0xc7, 0x83, 0x15, 0x4c, 0x92, 0x51, 0x21, 0xf1, 0x99,
	0x69, 0x12, 0x4d, 0xa5, 0xe2, 0x11, 0x8a, 0xa3, 0x30, 0x4f, 0x34, 0x47, 0xf4, 0x90, 0xc8, 0x40,
	0x44, 0xda, 0x97, 0x1c, 0xb9, 0x69, 0x34, 0x7e, 0x6c, 0xde, 0x8b, 0x17, 0x8d, 0x30, 0x08, 0xa4,
	0x0a, 0x4f, 0xac, 0x04, 0x5a, 0x81, 0xae, 0x60, 0xba, 0xf5, 0xa4, 0xa2, 0x42, 0x99, 0x32, 0xb5,
	0xb2, 0x97, 0x09, 0x07, 0x98, 0x88, 0xdd, 0x38, 0xba, 0x81, 0x77, 0xe6, 0xf0, 0x74, 0x41, 0x86,
	0xd7, 0x55, 0x51, 0x92, 0xf7, 0x91, 0x69, 0x6c, 0xd3, 0x2a, 0x3f, 0x36, 0x60, 0xb3, 0xec, 0x78,
	0x8c, 0xe7, 0x69, 0x1c, 0xea, 0xea, 0xdd, 0x03, 0xc0, 0x58, 0xa6, 0x02, 0x4b, 0x05, 0x2c, 0x21,
	0x7f, 0x59, 0xc3, 0xbf, 0x53, 0x8d, 0x52, 0xbe, 0xad, 0x6a, 0xbe, 0xdf, 0x35, 0x60, 0xfd, 0x2d,
	0x95, 0x0a, 0x8b, 0xbb, 0xac, 0x0f, 0x4d, 0x16, 0x66, 0x53, 0xa6, 0x1f, 0x75, 0x2f, 0xf3, 0x04,
	0x05, 0x55, 0x5c, 0xb8, 0x00, 0x72, 0x5b, 0x87, 0x97, 0x3d, 0x1f, 0x85, 0x2e, 0xe5, 0x12, 0x32,
	0x3f, 0xbb, 0xcb, 0xf5, 0xd9, 0x0d, 0xcd, 0xcc, 0xdb, 0xfd, 0x4f, 0x4c, 0xa3, 0xbb, 0x1b, 0x55,
	0x4b, 0x45, 0x36, 0x33, 0x59, 0x3c, 0x55, 0x50, 0xeb, 0x7f, 0x01, 0xd8, 0x1d, 0xec, 0xc5, 0x3a,
	0x0f, 0xeb, 0x44, 0x6f, 0xd6, 0xb6, 0xb9, 0x42, 0x22, 0xfa, 0xd0, 0x8c, 0xe4, 0xc4, 0xa5, 0xaa,
	0x1f, 0xc9, 0x7f, 0x2b, 0xd2, 0xb0, 0xe5, 0xa4, 0xa1, 0xec, 0xac, 0x90, 0x85, 0xd2, 0xf8, 0xba,
	0xaf, 0x21, 0x6b, 0x0d, 0xbf, 0xaf, 0x87, 0x60, 0xa4, 0x42, 0x7f, 0x77, 0x18, 0xcc, 0x67, 0x75,
	0x8d, 0xbd, 0x9f, 0xd5, 0xaf, 0xfc, 0x35, 0x73, 0xb5, 0x58, 0xe4, 0xbb, 0x69, 0xaf, 0x4c, 0xfa,
	0xc2, 0xf4, 0x99, 0x0b, 0x64, 0x95, 0x49, 0xdb, 0x77, 0x07, 0x3f, 0x37, 0xa1, 0x6b, 0x5f, 0x7f,
	0x4e, 0x50, 0xbc, 0xd3, 0xf7, 0xe3, 0x33, 0xe8, 0x94, 0xbf, 0xa8, 0xbc, 0xc5, 0x2f, 0x48, 0x78,
	0x39, 0xb8, 0x6a, 0x45, 0x92, 0x03, 0x80, 0xe2, 0x62, 0x21, 0x1b, 0xf3, 0x17, 0xcd, 0xe5, 0xa0,
	0x06, 0x49, 0xf2, 0x0c, 0x7a, 0x73, 0xf3, 0x46, 0x6e, 0x6b, 0x56, 0x5d, 0x66, 0x06, 0x8b, 0x71,
	0x49, 0x46, 0xd5, 0x91, 0xb5, 0x19, 0x92, 0xed, 0x79, 0x76, 0x3e, 0x71, 0x03, 0x52, 0x64, 0x90,
	0x7f, 0x71, 0x7f, 0x02, 0x9d, 0xd2, 0x18, 0x10, 0x43, 0xa9, 0xce, 0xc5, 0xc2, 0x9f, 0x3d, 0x87,
	0xde, 0xdc, 0x05, 0x60, 0xc3, 0xaf, 0x5f, 0x62, 0x83, 0xad, 0x2b, 0xee, 0x13, 0xe7, 0xa3, 0xdc,
	0x15, 0xb9, 0x8f, 0xb9, 0xa1, 0x18, 0x6c, 0x2d, 0xc4, 0x65, 0x72, 0x76, 0xc3, 0xfc, 0x9b, 0xf0,
	0xbf, 0x3f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x0e, 0x29, 0x73, 0x43, 0x60, 0x10, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ZiLongServiceClient is the client API for ZiLongService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ZiLongServiceClient interface {
	// 子龙商品列表
	ProductList(ctx context.Context, in *ZiLongProductListReq, opts ...grpc.CallOption) (*ZiLongProductListRes, error)
	// 发送门店券
	SendCoupon(ctx context.Context, in *SendCouponReq, opts ...grpc.CallOption) (*SendCouponRes, error)
	// 一卡通开通折扣卡
	DiscountCardNew(ctx context.Context, in *DiscountCardNewReq, opts ...grpc.CallOption) (*DiscountCardNewRes, error)
	// 折扣卡退款
	DiscountCardRefund(ctx context.Context, in *DiscountCardRefundReq, opts ...grpc.CallOption) (*ZiLongResponse, error)
	// 作废门店券
	WasteCoupon(ctx context.Context, in *WasteCouponReq, opts ...grpc.CallOption) (*ZiLongResponse, error)
	// 获取优惠券使用明细
	GetVerifyDetail(ctx context.Context, in *GetVerifyDetailReq, opts ...grpc.CallOption) (*GetVerifyDetailResp, error)
	// 批量查询门店券使用状态
	GetCouponStatus(ctx context.Context, in *GetCouponStatusReq, opts ...grpc.CallOption) (*GetCouponStatusResp, error)
}

type ziLongServiceClient struct {
	cc *grpc.ClientConn
}

func NewZiLongServiceClient(cc *grpc.ClientConn) ZiLongServiceClient {
	return &ziLongServiceClient{cc}
}

func (c *ziLongServiceClient) ProductList(ctx context.Context, in *ZiLongProductListReq, opts ...grpc.CallOption) (*ZiLongProductListRes, error) {
	out := new(ZiLongProductListRes)
	err := c.cc.Invoke(ctx, "/et.ZiLongService/ProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ziLongServiceClient) SendCoupon(ctx context.Context, in *SendCouponReq, opts ...grpc.CallOption) (*SendCouponRes, error) {
	out := new(SendCouponRes)
	err := c.cc.Invoke(ctx, "/et.ZiLongService/SendCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ziLongServiceClient) DiscountCardNew(ctx context.Context, in *DiscountCardNewReq, opts ...grpc.CallOption) (*DiscountCardNewRes, error) {
	out := new(DiscountCardNewRes)
	err := c.cc.Invoke(ctx, "/et.ZiLongService/DiscountCardNew", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ziLongServiceClient) DiscountCardRefund(ctx context.Context, in *DiscountCardRefundReq, opts ...grpc.CallOption) (*ZiLongResponse, error) {
	out := new(ZiLongResponse)
	err := c.cc.Invoke(ctx, "/et.ZiLongService/DiscountCardRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ziLongServiceClient) WasteCoupon(ctx context.Context, in *WasteCouponReq, opts ...grpc.CallOption) (*ZiLongResponse, error) {
	out := new(ZiLongResponse)
	err := c.cc.Invoke(ctx, "/et.ZiLongService/WasteCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ziLongServiceClient) GetVerifyDetail(ctx context.Context, in *GetVerifyDetailReq, opts ...grpc.CallOption) (*GetVerifyDetailResp, error) {
	out := new(GetVerifyDetailResp)
	err := c.cc.Invoke(ctx, "/et.ZiLongService/GetVerifyDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ziLongServiceClient) GetCouponStatus(ctx context.Context, in *GetCouponStatusReq, opts ...grpc.CallOption) (*GetCouponStatusResp, error) {
	out := new(GetCouponStatusResp)
	err := c.cc.Invoke(ctx, "/et.ZiLongService/GetCouponStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ZiLongServiceServer is the server API for ZiLongService service.
type ZiLongServiceServer interface {
	// 子龙商品列表
	ProductList(context.Context, *ZiLongProductListReq) (*ZiLongProductListRes, error)
	// 发送门店券
	SendCoupon(context.Context, *SendCouponReq) (*SendCouponRes, error)
	// 一卡通开通折扣卡
	DiscountCardNew(context.Context, *DiscountCardNewReq) (*DiscountCardNewRes, error)
	// 折扣卡退款
	DiscountCardRefund(context.Context, *DiscountCardRefundReq) (*ZiLongResponse, error)
	// 作废门店券
	WasteCoupon(context.Context, *WasteCouponReq) (*ZiLongResponse, error)
	// 获取优惠券使用明细
	GetVerifyDetail(context.Context, *GetVerifyDetailReq) (*GetVerifyDetailResp, error)
	// 批量查询门店券使用状态
	GetCouponStatus(context.Context, *GetCouponStatusReq) (*GetCouponStatusResp, error)
}

// UnimplementedZiLongServiceServer can be embedded to have forward compatible implementations.
type UnimplementedZiLongServiceServer struct {
}

func (*UnimplementedZiLongServiceServer) ProductList(ctx context.Context, req *ZiLongProductListReq) (*ZiLongProductListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductList not implemented")
}
func (*UnimplementedZiLongServiceServer) SendCoupon(ctx context.Context, req *SendCouponReq) (*SendCouponRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCoupon not implemented")
}
func (*UnimplementedZiLongServiceServer) DiscountCardNew(ctx context.Context, req *DiscountCardNewReq) (*DiscountCardNewRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiscountCardNew not implemented")
}
func (*UnimplementedZiLongServiceServer) DiscountCardRefund(ctx context.Context, req *DiscountCardRefundReq) (*ZiLongResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiscountCardRefund not implemented")
}
func (*UnimplementedZiLongServiceServer) WasteCoupon(ctx context.Context, req *WasteCouponReq) (*ZiLongResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WasteCoupon not implemented")
}
func (*UnimplementedZiLongServiceServer) GetVerifyDetail(ctx context.Context, req *GetVerifyDetailReq) (*GetVerifyDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVerifyDetail not implemented")
}
func (*UnimplementedZiLongServiceServer) GetCouponStatus(ctx context.Context, req *GetCouponStatusReq) (*GetCouponStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCouponStatus not implemented")
}

func RegisterZiLongServiceServer(s *grpc.Server, srv ZiLongServiceServer) {
	s.RegisterService(&_ZiLongService_serviceDesc, srv)
}

func _ZiLongService_ProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ZiLongProductListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZiLongServiceServer).ProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.ZiLongService/ProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZiLongServiceServer).ProductList(ctx, req.(*ZiLongProductListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ZiLongService_SendCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZiLongServiceServer).SendCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.ZiLongService/SendCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZiLongServiceServer).SendCoupon(ctx, req.(*SendCouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ZiLongService_DiscountCardNew_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscountCardNewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZiLongServiceServer).DiscountCardNew(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.ZiLongService/DiscountCardNew",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZiLongServiceServer).DiscountCardNew(ctx, req.(*DiscountCardNewReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ZiLongService_DiscountCardRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscountCardRefundReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZiLongServiceServer).DiscountCardRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.ZiLongService/DiscountCardRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZiLongServiceServer).DiscountCardRefund(ctx, req.(*DiscountCardRefundReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ZiLongService_WasteCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WasteCouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZiLongServiceServer).WasteCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.ZiLongService/WasteCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZiLongServiceServer).WasteCoupon(ctx, req.(*WasteCouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ZiLongService_GetVerifyDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVerifyDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZiLongServiceServer).GetVerifyDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.ZiLongService/GetVerifyDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZiLongServiceServer).GetVerifyDetail(ctx, req.(*GetVerifyDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ZiLongService_GetCouponStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCouponStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZiLongServiceServer).GetCouponStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.ZiLongService/GetCouponStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZiLongServiceServer).GetCouponStatus(ctx, req.(*GetCouponStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ZiLongService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.ZiLongService",
	HandlerType: (*ZiLongServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProductList",
			Handler:    _ZiLongService_ProductList_Handler,
		},
		{
			MethodName: "SendCoupon",
			Handler:    _ZiLongService_SendCoupon_Handler,
		},
		{
			MethodName: "DiscountCardNew",
			Handler:    _ZiLongService_DiscountCardNew_Handler,
		},
		{
			MethodName: "DiscountCardRefund",
			Handler:    _ZiLongService_DiscountCardRefund_Handler,
		},
		{
			MethodName: "WasteCoupon",
			Handler:    _ZiLongService_WasteCoupon_Handler,
		},
		{
			MethodName: "GetVerifyDetail",
			Handler:    _ZiLongService_GetVerifyDetail_Handler,
		},
		{
			MethodName: "GetCouponStatus",
			Handler:    _ZiLongService_GetCouponStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/zilong.proto",
}
