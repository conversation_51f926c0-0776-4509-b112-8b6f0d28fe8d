syntax = "proto3";
package et;

service BlgService{
  //获取token
  rpc GenerateToken(BlgTokenReq) returns (BlgTokenResp);
  //核销
  rpc Verification(BlgVerifyReq) returns (BlgVerifyResp);
}

message BlgTokenReq {
  // 用户名
  string username=1;
  // 密码
  string password=2;
}

message BlgTokenResp{
  //是否成功
  bool success=1;
  //信息
  string message=2;
  //小程序登录code
  int32 code=3;
  //返回对象
  BlgTokenData result=4;
  //服务器时间戳
  int64 timestamp=5;
}

message BlgTokenData{
  // 过期时间，单位：秒
  int32 expired=1;
  // token
  string token=2;
}

message BlgVerifyReq{
  //订单号
  string orderNo=1;
  //门店名称
  string store=2;
  //核销时间
  string verificationTime=3;
  //核销skuid
  string skuNo=4;
  //产品名称
  string skuName=5;
  //用户id
  string userId=6;
}

message BlgVerifyResp{
  //是否成功
  bool success=1;
  //信息
  string message=2;
  //小程序登录code
  int32 code=3;
  //服务器时间戳
  int64 timestamp=4;
}