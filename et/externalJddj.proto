syntax = "proto3";
import "google/protobuf/empty.proto";
package et;

//商品类API
service JddjProductService{
    //新增分类
    rpc JddjAddShopCategory (JddjAddShopCategoryRequest) returns (JddjAddShopCategoryResponse);
    //新版新增商品信息接口
    rpc AddSku (AddSkuRequest) returns (JddjBaseResponse);
    //更新京东商品信息
    rpc JddjUpdateGoodsList (JddjUpdateGoodsListRequest) returns (JddjBaseResponse);
    //修改分类
    rpc JddjUpdateShopCategory (JddjUpdateShopCategoryRequest) returns (JddjUpdateShopCategoryResponse);
    //删除分类
    rpc JddjDeleteShopCategory (JddjDeleteShopCategoryRequest) returns (JddjDeleteShopCategoryResponse);
    //排序分类
    rpc JddjSortShopCategory (JddjSortShopCategoryRequest) returns (JddjSortShopCategoryResponse);
    //获取京东到家后台商品类目
    rpc QueryChildCategoriesForOP (QueryChildCategoriesForOPRequest) returns (QueryChildCategoriesForOPResponse);
    //获取京东到家品牌信息
    rpc QueryPageBrandInfo (QueryPageBrandInfoRequest) returns (QueryPageBrandInfoResponse);
    //根据商家商品编码和商家门店编码批量修改现货库存接口
    rpc BatchUpdateCurrentQtys (BatchUpdateCurrentQtysRequest) returns (BatchUpdateCurrentQtysResponse);
    //新增分类
    rpc AddShopCategory (AddShopCategoryRequest) returns (AddShopCategoryResponse);
    //根据商家商品编码和商家门店编码批量修改门店价格接口
    rpc UpdateStationPrice (UpdateStationPriceRequest) returns (UpdateStationPriceResponse);
    //根据商家商品编码和门店编码批量修改门店商品可售状态接口
    rpc BatchUpdateVendibility (BatchUpdateVendibilityRequest) returns (BatchUpdateVendibilityResponse);
    //根据商家商品编码和门店编码更新现货库存接口
    rpc UpdateStock(UpdateStockRequest)returns(BatchUpdateVendibilityResponse);

    //根据商家商品编码和商家门店编码修改门店价格接口
    rpc UpdatePriceOne(UpdatePriceOneRequest)returns(JddjBaseResponse);

    // 获取jddj的分类
    rpc GetJddjCategoryList(QueryChildCategoriesForOPRequest)returns (QueryChildCategoriesForOPResponse);


    //根据商家商品编码和门店编码更新现货库存接口单个的
    rpc UpdateOneStock(UpdateStockOneRequest)returns(JddjBaseResponse);

    //查询jddj分类信息
    rpc JddjGetShopCategory (JddjGetShopCategoryRequest) returns (JddjGetShopCategoryResponse);

    // 查询jd到家的品牌库商品信息
    rpc QuerySkuInfos (QuerySkuInfosRequest) returns (QuerySkuInfosResponse);

}

//订单类API
service JddjOrderService{
    // 订单取消并退款
    rpc JddjOrderCancelAndRefund (JddjOrderCancelRequest) returns (JddjBaseResponse);
    // 订单自提码核验
    rpc JddjCheckSelfPickCode (JddjCheckSelfPickCodeRequest) returns (JddjBaseResponse);
    // 拣货完成且顾客自提接口
    rpc JddjOrderSelfMention (JddjOrderSelfMentionRequest) returns (JddjBaseResponse);
    // 查询售后单详情接口
    rpc GetJddjAfsService (JddjAfsServiceRequest) returns (JddjBaseResponse);
    // 查询订单可售后商品金额接口
    rpc GetJddjOrderCalcMoney (JddjOrderCalcMoneyRequest) returns (JddjBaseResponse);
    // 商家自主发起售后接口
    rpc JddjMerchantInitiateAfterSale (JddjMerchantInitiateAfterSaleRequest) returns (JddjBaseResponse);
    // 售后单确认收货接口
    rpc JddjConfirmReceipt (JddjConfirmReceiptRequest) returns (JddjBaseResponse);
    // 商家审核用户取消申请（订单未完成取消订单）
    rpc JddjOrderCancelOperate (JddjOrderCancelOperateRequest) returns (JddjBaseResponse);
    // 申请售后单审核接口（订单完成发起的售后单）
    rpc JddjAfsOpenApprove (AfsOpenApproveRequest) returns (JddjBaseResponse);
    // 商家审核配送员取货失败接口
    rpc JddjReceiveFailedAudit (JddjOrderCancelOperateRequest) returns (JddjBaseResponse);
    // 根据订单号查询所有的售后单信息接口
    rpc JddjGetAfsSeriveOrderList (JddjOrderDetailRequest) returns (JddjBaseResponse);


    //获取京东到家订单详情
    rpc GetJddjOrderDetail (JddjOrderDetailRequest) returns (JddjOrderDetailResponse);
    //商家确认接单接口
    rpc JddjOrderAcceptOperate (JddjOrderConfirmRequest) returns (JddjOrderConfirmlResponse);
    // 拣货完成且商家自送接口
    rpc JddjOrderSerllerDelivery (JddjOrderSerllerDeliveryRequest) returns (JddjBaseResponse);
    // 拣货完成且众包配送接口
    rpc JddjOrderJDZBDelivery (JddjOrderSerllerDeliveryRequest) returns (JddjBaseResponse);
    // 订单妥投接口
    rpc JddjDeliveryEndOrder (JddjOrderSerllerDeliveryRequest) returns (JddjBaseResponse);
    // 订单调整接口
    rpc JddjAdjustOrder (JddjAdjustOrderRequest) returns(JddjAdjustOrderResponse);

    rpc  JddjOrderShoudSettlementService  (JddjOrderSerllerDeliveryRequest) returns (JddjOrderShoudSettlementResponse);
    // 分页查询对账单接口
    rpc JddjGetBalanceBillList (JddjGetBalanceBillListReq) returns (JddjGetBalanceBillListRes);
}

message JddjGetBalanceBillListReq{
    // 店铺主体Id
    int32 store_master_id = 1;
    // 订单状态 1:已完成
    int32 orderStatus = 2;
    // 门店id
    repeated int64 shopIds = 3;
    // 订单号
    repeated int64 orderIds = 4;
    // 订单开始时间(开始) 2016-05-05 00:00:00
    string orderStartTime = 5;
    // 订单开始时间(结束) 2016-05-08 23:00:00
    string orderEndTime = 6;
    // 订单开始时间(开始) 2016-05-05 00:00:00
    string finishStartTime = 7;
    // 订单开始时间(结束) 2016-05-08 23:00:00
    string finishEndTime = 8;
}
message JddjGetBalanceBillListRes{
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    //错误信息
    string error = 3;
    //数据集合
    BalanceBillList data = 4;
}
message BalanceBillList {
    int64 pageNum = 1;
    int32 pageSize = 2;  //每页条数
    int64 total = 3;  //总条数
    int32 startRow = 4;//	开始行
    int32 endRow = 5;//	结束行
    int32 pages = 6; //总页数
    repeated  BalanceBill result = 7;//		对账单分页列表
}
message BalanceBill {
    int32 id = 1; //对账单id
    int64 orgCode = 2;//商家id
    string orgName = 3;//商家名称
    int64 stationId = 4;//门店ID
    string stationName = 5;//门店名称
    string businessStartTime = 6;//下单时间
    string businessFinishTime = 7;//完成时间
    int64 orderId = 8;//订单号
    int64 srcOrderId = 9;//源订单号
    string accountTime = 10;//账期时间 （如 ******** 00:00~******** 00:00）
    string settleFinishTime = 11; //结算完成时间
}
//财务类API
service JddjBussService{
    rpc JddjQueryOassBussMoney(JddjQueryOassBussMoneyRequest) returns(JddjQueryOassBussMoneyResponse);
}

//平台类API
service JddjPlatformService{
    //token更新确认接口
    rpc VerificationUpdateToken (VerificationUpdateTokenRequest) returns (google.protobuf.Empty);
}

// 门店类API
service JddjStoreService {
    // 订单调整接口
    rpc SyncJddjStoreStatus (SyncJddjStoreStatusRequest) returns(SyncJddjStoreStatusResponse);
}

//批量同步门店营业状态
message SyncJddjStoreStatusRequest {
    message reqData {
        //平台门店ID
        string baidu_shop_id = 1;
        //门店ID
        string shop_id = 2;
        // 状态，0-暂停营业，1-营业
        int32 status = 3;
        // app渠道 1.阿闻自有,2.TP代运营
        int32 app_channel = 4;
    }
    repeated reqData data = 1;
    // shop_status_task表id
    int64 task_id = 2;
}
message SyncJddjStoreStatusResponse {
    message outData {
        //门店ID
        string shop_id = 1;
        // 状态,1-成功
        int32 status = 2;
        // 错误信息
        string msg = 3;
    }
    repeated outData data = 1;
}

message UpdateStockRequest{
    //商家商品编码
    string outStationNo = 1;
    //京东门店编号
    string stationNo=2;
    //操作人
    string userPin = 3;
    //
    repeated skuStockList skuStockList =4;
    // 店铺主体Id
    int32 store_master_id = 5;  
}

message UpdateStockOneRequest{
    //商家商品编码
    string skuId = 1;
    //京东门店编号
    string stationNo=2;
    // 库存数
    int32 currentQty = 3;
    // 店铺主体Id
    int32 store_master_id = 4;
}

message skuStockList{
    string outSkuId = 1;
    int32 stockQty = 2;
}
message BatchUpdateVendibilityRequest{
    //商家门店编码(outerStationNo和stationNo不能同时为空)
    string outStationNo = 1;
    //到家门店编码
    string stationNo = 2;
    //参数实体列表，单次最多50个商品
    repeated JddjStockVendibility stockVendibilityList = 3;
    //操作人
    string userPin  = 4;
    // 店铺主体Id
    int32 store_master_id = 5;   
}

message JddjStockVendibility{
    //商家商品编码
    string outSkuId = 1;
    //可售状态（true:可售，false:不可售）
    bool doSale = 2;
}

message BatchUpdateVendibilityResponse{
    //状态
    bool ret = 1;
    //编码
    string retCode  = 2;
    //成功或失败信息
    string retMsg = 3;
    repeated UpdateVendibilityResponse data = 4;
}

message UpdateVendibilityResponse{
    //0：修改成功，1：修改时发生错误，2：修改失败，回滚失败，3：修改失败，回滚成功
    int32 code = 1;
    //操作描述信息
    string msg = 2;
    //商家商品编码
    string outSkuId = 3;
}

message VerificationUpdateTokenRequest{
    //旧Token
    string oldToken  = 1;
    //新Token
    string newToken = 2;
    // 店铺主体Id
    int32 store_master_id = 3;  
}

// 商家审核用户取消申请（订单未完成取消订单）参数
message JddjOrderCancelOperateRequest{
    // 订单号
    string orderId      = 1;
    // 操作类型 true同意 false驳回
    bool   isAgreed     = 2;
    // 操作人
    string operator     = 3;
    // 取消备注	操作备注(isAgreed=false时此字段必填，isAgreed=true时可不填)
    string remark       = 4;
    // 店铺主体Id
    int32 store_master_id = 5;   
}

// 申请售后单审核接口（订单完成发起的售后单）
message AfsOpenApproveRequest{
    // 售后服务单号
    string serviceOrder = 1;
    // 审核结果类型（1：退款 2：退货 3：驳回）
    int32 approveType = 2;
    // 驳回原因(审核为驳回时必须，审核为退货或退款时不传)
    string rejectReason = 3;
    // 操作人
    string optPin = 4;
    // 店铺主体Id
    int32 store_master_id = 5;   
}
message JddjCategoryInfo{
    //类目ID
    int32 id = 1;
    //父类目ID(一级类目ID为0)
    int32 pid = 2;
    //类目名称
    string categoryName = 3;
    //类目等级(0：一级分类，1：二级分类，2：三级分类)
    int32 categoryLevel = 4;
    //类目状态(0：禁用，1：启用)
    int32 categoryStatus = 5;
    //类目层级映射关系
    string fullPath = 6;
    // 子分类
    repeated JddjCategoryInfo child = 7;
    // 店铺主体Id
    int32 store_master_id = 8;   
}

// 订单取消请求参数
message JddjOrderCancelRequest{
    // 订单号
    int64  orderId      = 1;
    // 操作人
    string operPin      = 2;
    // 操作备注
    string operRemark   = 3;
    // 操作时间
    string operTime     = 4;
    // 店铺主体Id
    int32 store_master_id = 5;
}
// 订单自提码核验参数
message JddjCheckSelfPickCodeRequest{
    // 自提码
    string selfPickCode = 1;
    // 订单号
    string orderId      = 2;
    // 操作人
    string operPin      = 3;
    // 店铺主体Id
    int32 store_master_id = 4;  
}
// 拣货完成且顾客自提接口
message JddjOrderSelfMentionRequest{
    // 订单号
    string orderId      = 1;
    // 操作人
    string operator     = 2;
    // 店铺主体Id
    int32 store_master_id = 3;  
}
// 查询售后单详情接口
message JddjAfsServiceRequest{
    // 售后单号
    string afsServiceOrder  = 1;
    // 店铺主体Id
    int32 store_master_id = 2;  
}
// 售后单确认收货接口
message JddjConfirmReceiptRequest{
    // 售后单号
    string afsServiceOrder  = 1;
    // 操作人
    string pin              = 2;
    // 店铺主体Id
    int32 store_master_id = 3; 
}
// 商家自主发起售后接口
message JddjMerchantInitiateAfterSaleRequest{
    //订单号
    string orderId              = 1;
    //操作人pin
    string pin                  = 2;
    //售后原因id（201:商品质量问题，202:送错货，203:缺件少件，501:全部商品未收到，208:包装脏污有破损，207:缺斤少两，210:商家通知我缺货，303:实物与原图不符，502:未在时效内送达）
    string questionTypeCode     = 3;
    //问题描述
    string questionDesc         = 4;
    //上传图片url逗号隔开
    string questionPic          = 5;
    //客户名称
    string customerName         = 6;
    //客户电话
    string customerMobilePhone  = 7;
    //客户详细地址
    string address              = 8;
    // 商品信息集合
    repeated JddVenderAfsSkuDto skuList  = 9;
    // 店铺主体Id
    int32 store_master_id = 10; 
}
// 商家自主发起售后接口（商品信息集合）
message JddVenderAfsSkuDto{
    // 到家商品id
    int64 skuId         = 1;
    // 商品数量
    int32 skuCount      = 2;
    // 商品促销类型（1正品，2秒杀促销，3单品直降促销，4限时抢购促销，6买赠，7新人专享，8第二件N折，9拼团购，1202加价购促销，1203满赠促销，8001轻松购会员价，9996组合购,9997捆绑除不尽的类型，9998捆绑参与其他单品，9999捆绑，10009单品预售，9000称重品最小促销类型，9040称重品会员价起始类型，9050称重品最大促销类型）
    int32 promotionType = 3;
}
// 查询订单可售后商品金额接口
message JddjOrderCalcMoneyRequest{
    // 订单ID
    string      orderId                     = 1;
    // 商品信息集合
    repeated    JddVenderAfsSkuDto skuList  = 2;
    // 店铺主体Id
    int32 store_master_id = 3;  
}
// 基础响应结果
message JddjBaseResponse{
    // 状态码
    int32  code       = 1;
    // 结果描述
    string message    = 2;
    // 返回值
    string data       = 3;
}

message UpdateStationPriceRequest{
    //商家门店编码
    string outStationNo = 1;
    //到家门店编码
    string stationNo = 2;
    //商品信息，最多50条
    repeated JddjSkuPriceInfo skuPriceInfoList = 3;
    // 店铺主体Id
    int32 store_master_id = 4;   
}

message JddjSkuPriceInfo{
    //商家商品编码
    string outSkuId = 1;
    //商品价格(单位 分)
    int64 price = 2;
}

message UpdateStationPriceResponse{
    //状态码
    string code = 1;
    //返回描述信息
    string msg = 2;
    //结果数据，只返回更新失败的记录，价格无变化的不会更新，同时商家中心商品价格更新日志流水中也不会有相应记录。
    repeated UpdateStationPriceResult result = 3;
}

message UpdateStationPriceResult{
    //错误码(190003:参数异常,190004:系统错误,190005部分处理失败！,19000C:没有对应京东到家门店,0:成功)
    string errorCode = 1;
    //错误信息
    string errorMessage = 2;
    //商家商品编码
    string outSkuId = 3;
}

message ChangeShopCategoryOrderRequest{
    //父级店内分类编码；如果调整一级店内分类顺序，父级分类传0即可。
    int64 pid = 1;
    //子级店内分类集合；按照填写的分类编号顺序对分类顺序进行调整；该父分类下所有子分类都必须传入。
    repeated int64 childIds = 2;
}

message AddShopCategoryRequest{
    //若增加一级店内分类，其父级分类ID为0；若在已有一级店内分类下增加二级店内分类，则pid为该一级店内分类id；若在已有二级店内分类下增加三级店内分类，则pid为该二级店内分类id。
    int64 pid = 1;
    //店内分类名称，12个字符以内
    string shopCategoryName = 2;
    //店内分类等级
    int32 shopCategoryLevel = 3;
    //创建人
    string createPin = 4;
    // 店铺主体Id
    int32 store_master_id = 5;   
}

message AddShopCategoryResponse{
    //店内分类编号
    string id = 1;
}

//message JddjBatchAddSkuRequest{
//    //请求唯一编码
//    string traceId = 1;
//    //商品参数集合
//    repeated openBatchAddSkuRequest openBatchAddSkuRequests=2;
//
//}
//
//
//message openBatchAddSkuRequest{
//    //商家skuId编码（商家skuId）
//    string outSkuId =1;
//    //商品唯一编码（对应商家的 UPC）
//    string upc =2;
//    //商家商品价格(单位：分)，用于初始商品门店价格，所有的商品门店价格都会初始化成该值。后续修改商品门店价格需要通过价格类接口修改
//    int64 jdPrice =3;
//    //商家店内分类编码
//    int64 shopCategoryId = 4;
//    //门店商品可售状态，true为可售，false为不可售，默认为可售。
//    bool isSale =5;
//}

//message JddjBatchAddSkuResponse{
//    //到家SKU编码
//    int32 skuId = 1;
//    //商家SKU编码
//    int32 outSkuId = 2;
//    //返回结果状态码
//    string resultCode = 3;
//    //返回结果状态消息
//    string resultMsg = 4;
//}

message AddSkuRequest{
    //请求唯一编码
    string traceId = 1;
    //商家skuId编码（商家skuId）
    string outSkuId = 2;
    //商家店内分类编号列表，商家店内分类分两级，一个商品可以绑定多个店内分类（上传的店内分类需为最末级分类， 即二级店内分类或没有子分类的一级店内分类），
    //店内分类编号通过查询商家店内分类信息接口获取
    repeated int64 shopCategories = 3;
    //到家类目编号，需传入到家的第三级分类（通过查询到家类目信息接口获取）
    int64 categoryId = 4;
    //到家品牌编号（通过分页查询商品品牌信息接口获取）
    int64 brandId = 5;
    //商品名称（格式：名称+规格）, 校验字符数（1-45字符），不能包含js代码
    string skuName = 6;
    //商家商品价格（商品价格不能大于10W）(单位：分)，用于初始商品门店价格，所有的商品门店价格都会初始化成该值。后续修改商品门店价格需要通过价格类接口修改。
    int64 skuPrice = 7;
    //重量（单位：公斤/KG），小数点后最多保留3位
    float weight = 8;
    //UPC编码（商品条码），限1-35个字符，包装类的商品要求UPC编码必填，且要符合条码编写的校验，否则商品会不予通过，接口返回错误状态码code为10059
    string upc = 9;
    //商品图片地址，图片数组中的顺序即图片顺序，默认第一张主图；图片要求：800*800 ，后缀格式只支持png或者jpg，最大不要超过1M；
    //利用该接口上传图片时，图片为异步处理，请调用“查询商品图片处理结果接口”查看图片是否上传成功。图片不超过六张
    repeated string images = 10;
    //商品描述。支持图片，图片大小规格：750px宽，小于1M，和文字，若有文字描述，则不能少于10字符。限制为源码 不超过“30000”。
    string productDesc = 11;
    //商品描述是否在app端展示（0展示 1不展示）
    int32 ifViewDesc = 12;
    //广告词, 校验字符数1-45字符
    string slogan = 13;
    //广告词生效时间，当广告词字段有值时，该字段必填
    string sloganStartTime = 14;
    //广告词失效时间，当广告词字段有值时，该字段必填。必须设置具体时间，时间长短无限制。
    string sloganEndTime = 15;
    //前缀编码
    string prefixKeyId = 16;
    //前缀内容
    string prefixKey = 17;
    //前缀开始时间，前缀编码有值时，该字段必填
    string preKeyStartTime = 18;
    //前缀结束时间，前缀编码有值时，该字段必填。必须设置具体时间，时间长短无限制
    string preKeyEndTime = 19;
    //长(mm)
    int32 length = 20;
    //宽(mm)
    int32 width = 21;
    //高(mm)
    int32 height = 22;
    //储藏方式(0常温,1冷藏,2冷冻)
    string transportAttribute = 23;
    //是否液体(0是,1否)
    string liquidStatue = 24;
    //是否处方药(1是,2否)
    string prescripition = 25;
    //是否高单值(1否，2是)
    string highSingularValue = 26;
    //是否易碎(0是,1否)
    string isBreakable = 27;
    //商家商品上下架状态：1.上架 2.下架
    int32 fixedStatus = 28;
    //门店商品可售状态(true/false)；新建商品时，如果为true，门店商品可售状态初始为可售，如果为false， 门店商品可售状态初始为不可售。
    //后续修改各个门店商品可售状态时，请使用：库存类“根据到家商品编码和到家门店编码批量修改门店商品可售状态接口”。
    bool isSale = 29;
    //城市ID，0为全国
    repeated int32 sellCities = 30;
    // 店铺主体Id
    int32 store_master_id = 31;   
}

message AddSkuResponse{
    //到家SKU编码
    int64 skuId = 1;
    //商家SKU编码
    string outSkuId = 2;
    //返回结果状态码
    string code = 3;
    //返回结果状态消息
    string resultMsg = 4;
    //
    string orgCode = 5;
    //
    string failedDetail = 6;
    //
    bool success = 7;
    //
    string detail = 8;

}

message BatchUpdateCurrentQtysRequest{
    //商家门店编码
    string outStationNo = 1;
    //到家门店编码
    string stationNo = 2;
    //操作人pin
    string userPin = 3;
    //商品信息，最多50条
    repeated JddjSkuStock skuStockList = 4;
    // 店铺主体Id
    int32 store_master_id = 5; 
}

message JddjSkuStock{
    //商家商品编码
    string outSkuId = 1;
    //现货库存（数量不能小于0）：线上商品门店库存
    int32 stockQty = 2;
}

message BatchUpdateCurrentQtysResponse{
    //状态
    bool ret = 1;
    //false,10025,非库存同步时间;flase,10026,门店编号错误;false,1,失败;false,0,请求参数不能为空;false,11,orgCode不能为空;false,19,外部门店编号和到家门店编号不能同时为空;
    //false,20,操作人userPin不能为空;false,21,请求的商品列表不能为空;false,24,商品数量不能超过50个;true,0,成功；false,23,未获取到到家门店编号;false,22,调用门店系统异常;
    string retCode  = 2;
    //返回描述信息
    string retMsg = 3;
    repeated UpdateStockResponse data = 4;
}

message UpdateStockResponse{
    //更新状态（0此sku更新成功，1更新失败）
    int32 code = 1;
    //更新状态描述
    string msg = 2;
    //外部sku编号
    string outSkuId = 3;
}


message JddjUpdateGoodsListRequest{
    //	商家商品编码，商家系统中唯一编码，限1-35字符，与京东到家商品编码一对一对应。
    string outSkuId=1;
    //到家类目编号，需传入到家的第三级分类（通过查询到家类目信息接口获取）
    int64 categoryId =2;
    //商家店内分类编号列表，商家店内分类分两级，一个商品可以绑定多个店内分类（上传的店内分类需为最末级分类，即二级店内分类或没有子分类的一级店内分类），店内分类编号通过查询商家店内分类信息接口获取。
    repeated string shopCategories=3;
    //到家品牌编号（通过分页查询商品品牌信息接口获取）。
    int64 brandId=4;
    //商品名称（格式：名称+规格）, 校验字符数（1-45字符），不能包含js代码
    string skuName=5;
    //重量（单位：公斤/KG），小数点后最多保留3位。
    float weight=6;
    //UPC编码（商品条码），限1-35个字符，包装类的商品要求UPC编码必填，且要符合条码编写的校验，否则商品会不予通过，接口返回错误状态码code为10059。
    string upcCode=7;
    //商家商品上下架状态 1.上架 2.下架，4.删除；注：已经运营下架状态的商品不能设置上架，如需上架，请联系到家运营处理。
    int32 fixedStatus=8;
    //商品图片地址，图片数组中的顺序即图片顺序，默认第一张主图；图片要求：800*800 ，后缀格式只支持png或者jpg，最大不要超过1M；利用该接口上传图片时，图片为异步处理，请调用“查询商品图片处理结果接口”查看图片是否上传成功
    repeated string images=9;
    //商品描述。支持图片和文字，若有文字描述，则不能少于10字符。（包含图片的，图片要求750px宽，小于1M）
    string productDesc=10;
    //商品描述是否在app端展示（0展示 1不展示）
    int32 ifViewDesc=11;
    //长(mm)
    int32 length=12;
    //宽(mm)
    int32 width=13;
    //高(mm)
    string height=14;
    //广告词, 校验字符数1-45字符。需清空广告词，则该字段传空值
    string slogan=15;
    //广告词生效时间，当广告词字段有值时，该字段必填。
    string sloganStartTime=16;
    //广告词失效时间，当广告词字段有值时，该字段必填。
    string sloganEndTime=17;
    //前缀编码，需清空前缀，则该字段传空值
    string prefixKeyId=18;
    //前缀内容
    string prefixKey=19;
    //前缀开始时间，前缀编码有值时，该字段必填
    string preKeyStartTime=20;
    //前缀结束时间，前缀编码有值时，该字段必填
    string preKeyEndTime=21;
    //储藏方式(0常温,1冷藏,2冷冻)
    string transportAttribute=22;
    //是否液体(0是,1否)
    string liquidStatue=23;
    //是否处方药(1是,2否)
    string prescripition=24;
    //是否高单值(1否,2是)
    string highSingularValue=25;
    //是否易碎(0否,1是)
    string isBreakable=26;
    //城市ID，0为全国，其他城市ID需调用获取所有城市信息列表接口查询，如果不传该参数默认为全国。
    repeated string sellCities=27;
    //请求唯一编码
    string traceId = 28;
    //商品价格
    int32 skuPrice = 29;
    // 店铺主体Id
    int32 store_master_id = 30;   
}


message JddjUpdateGoodsListResponse{
    string code =1;
    string msg = 2;
    Result result =3;
    string succcess = 4;
    string detail = 5;
}

message Result{
    string orgCode =1;
    string resultCode = 2;
    string outSkuId =3;
    string failedDetail = 4;
    int32 skuId =5;
    string resultMsg = 6;
}

message JddjAddShopCategoryRequest{
    int64 pid =1;
    string shopCategoryName =2;
    int32 shopCategoryLevel = 3;
    string createPin = 4;
    // 店铺主体Id
    int32 store_master_id = 5;  
}

message JddjAddShopCategoryResponse{
    ShopCategoryPartnerResponse result = 1;
    string code = 2;
    string msg = 3;
    string detail  =4;
}

message ShopCategoryPartnerResponse{
    string id = 1;
}


message JddjUpdateShopCategoryRequest{
    //店内分类编号
    int64 id = 1;
    //店内分类名称
    string shopCategoryName = 2;
    // 店铺主体Id
    int32 store_master_id = 3;   
}

message JddjUpdateShopCategoryResponse{
    string code =1;
    string msg = 2;
}

message JddjDeleteShopCategoryRequest{
    //店内分类编码
    int64 id =1;
    // 店铺主体Id
    int32 store_master_id = 2; 
}
message JddjDeleteShopCategoryResponse{
    string code =1;
    string msg = 2;
}
message JddjSortShopCategoryRequest{
    //父级店内分类编码；如果调整一级店内分类顺序，父级分类传0即可。
    int64 pid = 1;
    //子级店内分类集合；按照填写的分类编号顺序对分类顺序进行调整；该父分类下所有子分类都必须传入。
    string childIds=2;
    // 店铺主体Id
    int32 store_master_id = 3; 
}
message JddjSortShopCategoryResponse{
    string code =1;
    string msg = 2;
}


message QueryPageBrandInfoRequest{
    //品牌名称(模糊查询);不传代表查询所有
    string brandName = 1;
    //到家品牌编码
    int32 brandId = 2;
    //定义需要返回的字段列表；所有字段列表：BRAND_ID--品牌编号,BRAND_NAME--品牌名称,BRAND_STATUS--品牌状态(1已禁用，2启用中)
    repeated string fields = 3;
    //页码
    int32 pageNo = 4;
    //每页查询数量(最大值50)
    int32 pageSize = 5;
    // 店铺主体Id
    int32 store_master_id = 6;   
}

message QueryPageBrandInfoResponse{
    //总数量
    int32 count = 1;
    repeated JddjBrandInfo result = 2;
}

message JddjBrandInfo{
    //品牌编号
    int32 id = 1;
    //品牌名称
    string brandName = 2;
    //品牌状态：-1:删除状态1：待审核,2：审核通过，3：驳回
    int32 brandStatus = 3;
}

message QueryChildCategoriesForOPRequest{
    int32 id = 1;
    string fields = 2;
    // 店铺主体Id
    int32 store_master_id = 3;   
}

message QueryChildCategoriesForOPResponse{
    repeated JddjCategoryInfo result = 2;
}

//zhang start
message JddjOrderConfirmRequest{
    //订单ID
    string orderId=1;
    bool isAgreed=2;
    // 店铺主体Id
    int32 store_master_id = 3;   
}


message JddjOrderConfirmlResponse{
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    //错误信息
    string error = 3;
    //结果
    string data = 4;
}

message JddjOrderSerllerDeliveryRequest{
    //订单ID
    string orderId=1;
    // 店铺主体Id
    int32 store_master_id = 2;  
}
message JddjOrderDetailRequest{
    // 订单ID
    string orderId = 1;
    // 商家门店编码
    string deliveryStationNoIsv = 2;
    // 店铺主体Id
    int32 store_master_id = 3;
    // 订单开始时间(开始) 2016-05-05 00:00:00
    string orderStartTime_begin = 4;
    // 订单开始时间(结束) 2016-05-08 23:00:00
    string orderStartTime_end = 5;
}

//获取饿了么订单详情返回参数
message JddjOrderDetailResponse{
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    //错误信息
    string error = 3;
    //饿了么订单详情
    JddjData JddjData = 4;
}

message JddjData{
    //状态码
    string  code = 1;
    string msg = 2;
    JddjResult JddjResult = 3;
}
message JddjResult{
    int32 pageNo = 1;
    int32 pageSize = 2;
    int32 maxPageSize = 3;
    int32 totalCount = 4;
    repeated ResultList JddjResultList = 5;
}

message ResultList{
    //订单号
    int64 orderId=1;
    //31000:等待付款，31020:已付款，41000:待处理，32000:等待出库，33040:配送中，33060:已妥投，90000:订单完成）
    int32 orderStatus=2;

    //预计送达开始时间
    string  orderPreStartDeliveryTime=3;
    //预计送达结束时间
    string orderPreEndDeliveryTime=4;

    //商家编码
    string  orgCode=5;

    //收货人名称
    string buyerFullName=7;
    //收货人地址
    string buyerFullAddress=8;
    //收货人电话
    string buyerTelephone=9;
    //收货人手机号
    string buyerMobile=10;
    //到家配送门店编码
    string deliveryStationNo=11;
    //商家门店编码
    string  deliveryStationNoIsv=12;
    //配送门店名称
    string deliveryStationName=13;
    //承运商编号(9966:京东众包;2938:商家自送;1130:达达同城送;9999:到店自提)
    string deliveryCarrierNo=14;
    //承运商名称
    string deliveryCarrierName=15;

    //包裹重量（单位：kg）
    double  deliveryPackageWeight=16;
    //妥投时间
    string deliveryConfirmTime=17;
    //订单支付类型(1：货到付款，4:在线支付;)
    int32 orderPayType=18;
    //订单支付渠道，8001：微信支付；8002：微信免密代扣；8003：微信找人代付；9000：
    //京东支付（无法判断具体京东支付子类型）；9002：京东银行卡支付；9004：京东白条支付；9012：京东余额支付；9022：京东小金库支付
    int32 payChannel=19;
    //订单商品销售价总金额，等于sum（京东到家销售价skuJdPrice*商品下单数量skuCount）
    int32 orderTotalMoney=20;
    //订单级别优惠商品金额：(不含单品促销类优惠金额及运费相关优惠金额)，
    //等于OrderDiscountlist表中，除优惠类型7，8，12，15，16外的优惠金额discountPrice累加和
    int32  orderDiscountMoney=21;
    //用户支付的实际订单运费：订单应收运费（orderReceivableFreight）-运费优惠（OrderDiscountlist表中，
    //优惠类型7，8，12，15的优惠金额。运费优惠大于应收运费时，实际支付为0
    int32 orderFreightMoney=22;
    //达达同城送运费(单位：分)
    int32 localDeliveryMoney=23;
    //商家支付远距离运费(单位：分)。达达配送默认只能服务半径2公里内的用户，
    //商家可与到家运营沟通开通远距离服务，超过2公里后每1公里加收2元运费。费用承担方为商家
    int32 merchantPaymentDistanceFreightMoney=24;
    //订单应收运费：用户应该支付的订单运费，即未优惠前应付运费(不计满免运费，运费优惠券，VIP免基础运费等优惠)，
    //包含用户小费。订单对应门店配送方式为商家自送，则订单应收运费为设置的门店自送运费；订单对应门店配送方式为达达配送，则订单应收运费为用户支付给达达的配送费（平台规则统一设置，如基础运费、重量阶梯运费、距离阶梯运费、夜间或天气等因素的附加运费）
    int32 orderReceivableFreight=25;
    //用户积分抵扣金额
    int32 platformPointsDeductionMoney=26;
    //用户应付金额（单位为分）=商品销售价总金额orderTotalMoney -订单优惠总金额 orderDiscountMoney+
    //实际订单运费orderFreightMoney +包装金额packagingMoney -用户积分抵扣金额platformPointsDeductionMoney
    int32 orderBuyerPayableMoney=27;
    //包装金额
    int32 packagingMoney=28;
    //收货人地址腾讯坐标经度
    double buyerLng=29;
    //收货人地址腾讯坐标纬度
    double buyerLat=30;
    //收货人市名称
    string buyerCityName=31;
    //收货人县(区)名称
    string buyerCountryName=32;
    //订单买家备注
    string orderBuyerRemark=33;
    //订单开发票标识（1.开发票；2.不开发票）
    int32 orderInvoiceOpenMark = 34;

    JddjOrderInvoice JddjOrderInvoice = 35;

    repeated  JddjProductList JddjProductList = 36;

    repeated  JddjDiscount JddjDiscount = 37;
    //下单时间
    string  orderStartTime = 38;

    string  businessTag = 39;
    // 订单来源系统
    int32 srcOrderType = 40;
    //订单取消时间
    string  orderCancelTime = 41;
}

message  JddjOrderInvoice{
    //发票抬头
    string invoiceTitle=1;
    //发票税号
    string invoiceDutyNo=2;
}

message  JddjProductList{
    //订单号
    int64 orderId=1;
    //商品规格，多规格之间用英文分号;分隔
    string skuCostumeProperty=2;
    //调整单记录id（0:原单商品明细;非0:调整单id 或者 确认单id)
    int64 adjustId=3;
    //到家商品编码
    int64 skuId=4;
    //商品的名称
    string skuName=5;
    //商家商品编码
    string skuIdIsv=6;
    //到家商品销售价
    int32 skuJdPrice=7;
    //下单数量
    int32 skuCount=8;
    //调整方式(0:默认值，没调整，原订单明细;1:新增;2:删除;3:修改数量)
    int32 adjustMode=9;
    //商品upc码
    string upcCode=10;
    //到家商品门店价
    int32 skuStorePrice=11;
    //到家商品成本价
    int32 skuCostPrice=12;
    //商品级别促销类型(1、无优惠;2、秒杀(已经下线);3、单品直降;4、限时抢购;1202、
    //加价购;1203、满赠(标识商品);6、买赠(买A送B，标识B);9999、表示一个普通商品参与捆绑促销，
    //设置的捆绑类型;9998、表示一个商品参与了捆绑促销，并且还参与了其他促销类型;9997、
    //表示一个商品参与了捆绑促销，但是金额拆分不尽,9996:组合购,8001:商家会员价,
    //8:第二件N折,9:拼团促销)
    int32 promotionType=13;
    //税率
    string skuTaxRate=14;
    //促销活动编码
    int32 promotionId=15;
    //赠品关联的主商品信息，多个商品之间英文逗号分隔
    string relatedSkus=16;
    //商品维度的单个商品重量（千克）
    float skuWeight=17;
    //餐盒费（商品的总餐盒费）
    int32 canteenMoney=18;

}
message  JddjDiscount{
    //订单主表订单id
    int64 OrderID=1;
    //调整单记录id（0:原单商品明细;非0:调整单id 或者 确认单id)
    int64 AdjustID =2;
    //记录参加活动的sku数组
    string SkuIds=3;
    //优惠类型(1:优惠码;3:优惠劵;4:满减;5:满折;6:首单优惠;7:VIP免运费;
    //8:商家满免运费;10:满件减;11:满件折;12:首单地推满免运费;15:运费券;16:单品免运)
    int32 DiscountType=4;
    //小优惠类型(优惠码(1:满减;2:立减;3:满折);优惠券(1:满减;2:立减;5满折);
    //满减(1:满减);商家满免运费(null或0：平台承担。1：商家全免运费（包括商家促销免运、商家会员免运），商家承担补贴。
    //2：商家免基础运费（包括商家促销免运），商家承担补贴。
    //3：商家会员免基础运费（包括商家会员免运），商家承担补贴。
    //4：拼团免运。);满件减(1206:满件减);满件折(1207:满件折);运费券（1：满减，2：立减）)
    int32 DiscountDetailType=5;
    //用户领券编号，用户领取优惠券的标识，返回值中，-和#中间的字符串为对应优惠券实际编码，不同用户领券标识不同
    string  DiscountCode=6;
    //优惠金额
    int32 DiscountPrice =7;
    //商家承担金额，有运费券（discountType=15）或单品免运（discountType=16）时才会有值，其他优惠无值
    int32 VenderPayMoney =8;
    //平台承担金额，有运费券（discountType=15）或单品免运（discountType=16）时才会有值，其他优惠无值
    int32 PlatPayMoney =9;

}

message JddjOrderShoudSettlementResponse{
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    //错误信息
    string error = 3;

    OrderShoudSettlement  OrderShoudSettlement = 4;
}
message OrderShoudSettlement{
    //状态码
    int64 OrderId=1;
    int64 SettlementAmount=2;
    int64 GoodsCommission=3;
    int64 FreightCommission=4;
    int64 PckageCommission=5;
    int64 GuaranteedCommission =6;
}

//zhang end

message JddjAdjustOrderRequest{
    //外部订单号
    string orderId =1;
    //操作人
    string operPin = 2;
    //操作备注
    string remark = 3;
    //订单调整后的所有商品明细（含未调整的商品），如果某商品数量调整为0时，商品明细中不能包含该商品；如果全部商品数量调整为0时，需联系顾客沟通确认后，由顾客取消订单。
    repeated JddjOAOSAdjust AdjustList =4;
    //订单id
    string awOrder_id = 5;
    // 店铺主体Id
    int32 store_master_id = 6;  

}
message JddjOAOSAdjust{
    //商家商品编码(skuId与outSkuId至少填一个)
    string outSkuId = 1;
    //商品数量
    int32 skuCount = 2;
}
message JddjAdjustOrderResponse{
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    //错误信息
    string error = 3;
    //订单号(外部订单号)
    int64 orderId = 4;
    //缺货商品总数
    int32 adjustCount = 5;
    //门店编码
    string ProduceStationNo = 6;
    //调整后的订单总金额
    int64 orderTotalMoney =7;
    //调整后的订单优惠总金额
    int64 orderDiscountMoney= 8;
    //调整后的订单运费总金额
    int64 orderFreightMoney = 9;
    //调整后用户应付金额
    int64 orderBuyerPayableMoney = 10;
    //商品集合
    repeated JddjAdjust oaList = 11;
    //订单号
    string order_sn = 12;
}
message JddjAdjust{
    //调整后的到家商品编号
    int64 skuId=1;
    //调整后的商品数量
    int32 skuCount=2;
    //调整后的商品名称
    string skuName=3;
    //到家商品销售价格
    int64 skuJdPrice=4;
    //商家商品编码
    string outSkuId=5;
    //商品upc码
    string upc=6;
}

message JddjQueryOassBussMoneyRequest{
    //订单ID
    int64 orderId = 1;
    //用户pin （非必填）
    string userPin = 2;
    // 店铺主体Id
    int32 store_master_id = 3;   
}

message JddjQueryOassBussMoneyResponse{
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    //错误信息
    string error = 3;
    repeated OassBussinessSkus data = 4;
}

message OassBussinessSkus{
    int64 orderId= 1;
    int64 skuId = 2;
    int32 skuCount = 3;
    int32 promotionPrice = 4;
    int32 pdjPrice = 5;
    int32 costPrice = 6;
    int64 skuPayMoney = 7;
    int32 costRadio = 8;
    int32 saleRadio = 9;
    int64 costMoney = 10;
    int64 saleMoney = 11;
    int64 platformIntegralDeductMoney = 12;
    int32 promotionType = 13;
    string outActivityId = 14;
    repeated OrderBussiDiscountMoney discountlist = 15;

}
message OrderBussiDiscountMoney{
    int32 promotionType= 1;
    int32 promotionDetailType = 2;
    int64 skuDiscountMoney = 3;
    int32 saleRadio = 4;
    int64 costMoney = 5;
    int64 saleMoney = 6;
    string promotionCode = 7;
    string outActivityId = 8;
}

message UpdatePriceOneRequest {
    //商家门店编码
    string outStationNo = 1;
    //到家门店编码（与outStationNo不可同时为空）
    string stationNo = 2;
    //商家商品编码
    string outSkuId = 3;
    //价格(必填且不能小于等于0，单位 分)
    int32 price = 4;
    //uuid（填写随机生成唯一的标识串）
    string serviceNo = 5;
    // 店铺主体Id
    int32 store_master_id = 6;   
}




message JddjGetShopCategoryRequest{
    // 店铺主体Id
    int32 store_master_id = 1;
    // fields查询字段
    string fields = 2;
}

message JddjGetShopCategoryResponse{
    string code = 1;
    string msg = 2;
    string data  =3;
    repeated JDCategoryResult result = 4;
    bool success = 5;
}


message JDCategoryResult {
    string shopCategoryName =1;
    int32  pid =2;
    int32 id = 3;
    int32 sort = 4;
}


message QuerySkuInfosRequest {
    // 商品名称(支持模糊查询)
    string skuName = 1;
    // 商品UPC编码  6921041426579
    string upcCode = 2;
    // 到家商品编码
    int64 skuId = 3;
    //当前页
    int64 pageNo = 4;
    // 接口建议值：20 最大值50否则会报错
    int64 pageSize = 5;
    // 是否查询出已删除的上传商品(0代表不查已删除商品,不填则查出全部商品)
    string isFilterDel = 6;
    // 门店的appChannel
    int64 StoreMasterId = 7;
    // 查询递进skuId 说明：pageNo=1时，searchAfterSkuId非必填；pageNo!=1时，必填，取上页调用结果返回的searchAfterSkuId值
    int64 searchAfterSkuId = 8;
}


message QuerySkuInfosResponse{
    // 0表示成功，其他均为失败。
    string code = 1;
    //    结果描述
    string msg = 2;
    // 返回值，该属性值为字符串型JSON数据，请先按照字符串取值，再解析转换成JSON对象。
    string data  =3;
    // 解析的json返回
    SkuQueryResponse result = 4;
}

message SkuQueryResponse {
    // 返回的数量
    int64 count = 1;
    // 返回的string类型的数据
    string result = 2;
    // 解析的json格式返回
    repeated SkuMain jsonData = 3;
    // 查询递进skuId 说明: 用作下次查询的searchAfterSkuId入参，当返回为空时则查询结束
    int64 searchAfterSkuId = 4;
}

message SkuMain {
//    到家商品编码
    int64 skuId = 1;
//    商家商品编码
    string outSkuId =2;

//    到家类目编码，到家类目分三级，该字段展示最末级类目编码
    int64 categoryId = 3;
//    到家品牌编码
    int64 brandId = 4;
    // 商品名称
    string skuName = 5;
//    重量(公斤) 单位:KG
    float weight = 6;
//    商品UPC编码
    string upcCode = 7;

//    商家商品上下架状态(1:上架;2:下架;4:删除;)
    int64 fixedStatus = 8;
//    商家店内分类编码,店内分类分两级，该字段展示最末级分类编码；当一个商品绑定了多个店内分类时，该字段展示用逗号分隔的各个分类编码。
    repeated int64 shopCategories = 9;
//    广告词
    string slogan = 10;

}



message   QueryListBySkuIdsDto {
//    京东到家商品编码列表,单次最大25个商品
    repeated  int64   skuIds = 1;
//    图片类型 1：商品图片 2：商品描述图片
    int64 imgType =2;
//    处理状态,0:待处理,1: 成功,4:连接异常,5:IO异常,6:外部服务器异常,9:上传文件失败,10:图片格式错误,11:图片生成错误,100:其他错误
    repeated int64 handleStatus = 3;

    int64 storeMasterId = 4;
}