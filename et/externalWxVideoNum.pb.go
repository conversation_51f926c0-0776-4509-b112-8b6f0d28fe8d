// Code generated by protoc-gen-go. DO NOT EDIT.
// source: et/externalWxVideoNum.proto

package et

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type Empty struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Empty) Reset()         { *m = Empty{} }
func (m *Empty) String() string { return proto.CompactTextString(m) }
func (*Empty) ProtoMessage()    {}
func (*Empty) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{0}
}

func (m *Empty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Empty.Unmarshal(m, b)
}
func (m *Empty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Empty.Marshal(b, m, deterministic)
}
func (m *Empty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Empty.Merge(m, src)
}
func (m *Empty) XXX_Size() int {
	return xxx_messageInfo_Empty.Size(m)
}
func (m *Empty) XXX_DiscardUnknown() {
	xxx_messageInfo_Empty.DiscardUnknown(m)
}

var xxx_messageInfo_Empty proto.InternalMessageInfo

type WxBaseResponse struct {
	// 响应码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 响应信息
	Errmsg               string   `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WxBaseResponse) Reset()         { *m = WxBaseResponse{} }
func (m *WxBaseResponse) String() string { return proto.CompactTextString(m) }
func (*WxBaseResponse) ProtoMessage()    {}
func (*WxBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{1}
}

func (m *WxBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WxBaseResponse.Unmarshal(m, b)
}
func (m *WxBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WxBaseResponse.Marshal(b, m, deterministic)
}
func (m *WxBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WxBaseResponse.Merge(m, src)
}
func (m *WxBaseResponse) XXX_Size() int {
	return xxx_messageInfo_WxBaseResponse.Size(m)
}
func (m *WxBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WxBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WxBaseResponse proto.InternalMessageInfo

func (m *WxBaseResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *WxBaseResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

// 商品通用请求参数
type ProductBaseRequest struct {
	// 商家自定义商品ID
	OutProductId         string   `protobuf:"bytes,1,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductBaseRequest) Reset()         { *m = ProductBaseRequest{} }
func (m *ProductBaseRequest) String() string { return proto.CompactTextString(m) }
func (*ProductBaseRequest) ProtoMessage()    {}
func (*ProductBaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{2}
}

func (m *ProductBaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductBaseRequest.Unmarshal(m, b)
}
func (m *ProductBaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductBaseRequest.Marshal(b, m, deterministic)
}
func (m *ProductBaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductBaseRequest.Merge(m, src)
}
func (m *ProductBaseRequest) XXX_Size() int {
	return xxx_messageInfo_ProductBaseRequest.Size(m)
}
func (m *ProductBaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductBaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ProductBaseRequest proto.InternalMessageInfo

func (m *ProductBaseRequest) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

// 视频号类目详情响应
type WxCategoryListResponse struct {
	// 响应码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 响应信息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	//  类目列表
	ThirdCatList         []*WxCategoryList `protobuf:"bytes,3,rep,name=third_cat_list,json=thirdCatList,proto3" json:"third_cat_list"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *WxCategoryListResponse) Reset()         { *m = WxCategoryListResponse{} }
func (m *WxCategoryListResponse) String() string { return proto.CompactTextString(m) }
func (*WxCategoryListResponse) ProtoMessage()    {}
func (*WxCategoryListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{3}
}

func (m *WxCategoryListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WxCategoryListResponse.Unmarshal(m, b)
}
func (m *WxCategoryListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WxCategoryListResponse.Marshal(b, m, deterministic)
}
func (m *WxCategoryListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WxCategoryListResponse.Merge(m, src)
}
func (m *WxCategoryListResponse) XXX_Size() int {
	return xxx_messageInfo_WxCategoryListResponse.Size(m)
}
func (m *WxCategoryListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WxCategoryListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WxCategoryListResponse proto.InternalMessageInfo

func (m *WxCategoryListResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *WxCategoryListResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *WxCategoryListResponse) GetThirdCatList() []*WxCategoryList {
	if m != nil {
		return m.ThirdCatList
	}
	return nil
}

// 视频号类目列表
type WxCategoryList struct {
	// 类目id
	ThirdCatId int32 `protobuf:"varint,1,opt,name=third_cat_id,json=thirdCatId,proto3" json:"third_cat_id"`
	// 类目名称
	ThirdCatName string `protobuf:"bytes,2,opt,name=third_cat_name,json=thirdCatName,proto3" json:"third_cat_name"`
	// 类目资质
	Qualification string `protobuf:"bytes,3,opt,name=qualification,proto3" json:"qualification"`
	// 类目资质类型,0:不需要,1:必填,2:选填
	QualificationType int32 `protobuf:"varint,4,opt,name=qualification_type,json=qualificationType,proto3" json:"qualification_type"`
	// 商品资质
	ProductQualification string `protobuf:"bytes,5,opt,name=product_qualification,json=productQualification,proto3" json:"product_qualification"`
	// 商品资质类型,0:不需要,1:必填,2:选填
	ProductQualificationType int32 `protobuf:"varint,6,opt,name=product_qualification_type,json=productQualificationType,proto3" json:"product_qualification_type"`
	// 二级类目ID
	SecondCatId int32 `protobuf:"varint,7,opt,name=second_cat_id,json=secondCatId,proto3" json:"second_cat_id"`
	// 二级类目名称
	SecondCatName string `protobuf:"bytes,8,opt,name=second_cat_name,json=secondCatName,proto3" json:"second_cat_name"`
	// 一级类目ID
	FirstCatId int32 `protobuf:"varint,9,opt,name=first_cat_id,json=firstCatId,proto3" json:"first_cat_id"`
	// 一级类目名称
	FirstCatName         string   `protobuf:"bytes,10,opt,name=first_cat_name,json=firstCatName,proto3" json:"first_cat_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WxCategoryList) Reset()         { *m = WxCategoryList{} }
func (m *WxCategoryList) String() string { return proto.CompactTextString(m) }
func (*WxCategoryList) ProtoMessage()    {}
func (*WxCategoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{4}
}

func (m *WxCategoryList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WxCategoryList.Unmarshal(m, b)
}
func (m *WxCategoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WxCategoryList.Marshal(b, m, deterministic)
}
func (m *WxCategoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WxCategoryList.Merge(m, src)
}
func (m *WxCategoryList) XXX_Size() int {
	return xxx_messageInfo_WxCategoryList.Size(m)
}
func (m *WxCategoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_WxCategoryList.DiscardUnknown(m)
}

var xxx_messageInfo_WxCategoryList proto.InternalMessageInfo

func (m *WxCategoryList) GetThirdCatId() int32 {
	if m != nil {
		return m.ThirdCatId
	}
	return 0
}

func (m *WxCategoryList) GetThirdCatName() string {
	if m != nil {
		return m.ThirdCatName
	}
	return ""
}

func (m *WxCategoryList) GetQualification() string {
	if m != nil {
		return m.Qualification
	}
	return ""
}

func (m *WxCategoryList) GetQualificationType() int32 {
	if m != nil {
		return m.QualificationType
	}
	return 0
}

func (m *WxCategoryList) GetProductQualification() string {
	if m != nil {
		return m.ProductQualification
	}
	return ""
}

func (m *WxCategoryList) GetProductQualificationType() int32 {
	if m != nil {
		return m.ProductQualificationType
	}
	return 0
}

func (m *WxCategoryList) GetSecondCatId() int32 {
	if m != nil {
		return m.SecondCatId
	}
	return 0
}

func (m *WxCategoryList) GetSecondCatName() string {
	if m != nil {
		return m.SecondCatName
	}
	return ""
}

func (m *WxCategoryList) GetFirstCatId() int32 {
	if m != nil {
		return m.FirstCatId
	}
	return 0
}

func (m *WxCategoryList) GetFirstCatName() string {
	if m != nil {
		return m.FirstCatName
	}
	return ""
}

// 上传图片至微信
type UploadPicRequest struct {
	// 图片链接
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	// 0:此参数返回media_id，目前只用于品牌申请品牌和类目，推荐使用1：返回临时链接
	RespType             int32    `protobuf:"varint,2,opt,name=resp_type,json=respType,proto3" json:"resp_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadPicRequest) Reset()         { *m = UploadPicRequest{} }
func (m *UploadPicRequest) String() string { return proto.CompactTextString(m) }
func (*UploadPicRequest) ProtoMessage()    {}
func (*UploadPicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{5}
}

func (m *UploadPicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadPicRequest.Unmarshal(m, b)
}
func (m *UploadPicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadPicRequest.Marshal(b, m, deterministic)
}
func (m *UploadPicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadPicRequest.Merge(m, src)
}
func (m *UploadPicRequest) XXX_Size() int {
	return xxx_messageInfo_UploadPicRequest.Size(m)
}
func (m *UploadPicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadPicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UploadPicRequest proto.InternalMessageInfo

func (m *UploadPicRequest) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *UploadPicRequest) GetRespType() int32 {
	if m != nil {
		return m.RespType
	}
	return 0
}

// 图片上传微信响应
type UploadPicResponse struct {
	// 响应码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 响应信息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	// 图片信息
	ImgInfo              *UploadPicData `protobuf:"bytes,3,opt,name=img_info,json=imgInfo,proto3" json:"img_info"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UploadPicResponse) Reset()         { *m = UploadPicResponse{} }
func (m *UploadPicResponse) String() string { return proto.CompactTextString(m) }
func (*UploadPicResponse) ProtoMessage()    {}
func (*UploadPicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{6}
}

func (m *UploadPicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadPicResponse.Unmarshal(m, b)
}
func (m *UploadPicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadPicResponse.Marshal(b, m, deterministic)
}
func (m *UploadPicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadPicResponse.Merge(m, src)
}
func (m *UploadPicResponse) XXX_Size() int {
	return xxx_messageInfo_UploadPicResponse.Size(m)
}
func (m *UploadPicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadPicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UploadPicResponse proto.InternalMessageInfo

func (m *UploadPicResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *UploadPicResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *UploadPicResponse) GetImgInfo() *UploadPicData {
	if m != nil {
		return m.ImgInfo
	}
	return nil
}

// 返回的图片信息
type UploadPicData struct {
	// 图片链接
	TempImgUrl string `protobuf:"bytes,1,opt,name=temp_img_url,json=tempImgUrl,proto3" json:"temp_img_url"`
	// 图片id
	MediaId              string   `protobuf:"bytes,2,opt,name=media_id,json=mediaId,proto3" json:"media_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadPicData) Reset()         { *m = UploadPicData{} }
func (m *UploadPicData) String() string { return proto.CompactTextString(m) }
func (*UploadPicData) ProtoMessage()    {}
func (*UploadPicData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{7}
}

func (m *UploadPicData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadPicData.Unmarshal(m, b)
}
func (m *UploadPicData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadPicData.Marshal(b, m, deterministic)
}
func (m *UploadPicData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadPicData.Merge(m, src)
}
func (m *UploadPicData) XXX_Size() int {
	return xxx_messageInfo_UploadPicData.Size(m)
}
func (m *UploadPicData) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadPicData.DiscardUnknown(m)
}

var xxx_messageInfo_UploadPicData proto.InternalMessageInfo

func (m *UploadPicData) GetTempImgUrl() string {
	if m != nil {
		return m.TempImgUrl
	}
	return ""
}

func (m *UploadPicData) GetMediaId() string {
	if m != nil {
		return m.MediaId
	}
	return ""
}

// 上传品牌信息请求参数
type UploadBrandInfoRequest struct {
	// 请求
	AuditReq             *LicenseAndBrandInfo `protobuf:"bytes,1,opt,name=audit_req,json=auditReq,proto3" json:"audit_req"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UploadBrandInfoRequest) Reset()         { *m = UploadBrandInfoRequest{} }
func (m *UploadBrandInfoRequest) String() string { return proto.CompactTextString(m) }
func (*UploadBrandInfoRequest) ProtoMessage()    {}
func (*UploadBrandInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{8}
}

func (m *UploadBrandInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadBrandInfoRequest.Unmarshal(m, b)
}
func (m *UploadBrandInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadBrandInfoRequest.Marshal(b, m, deterministic)
}
func (m *UploadBrandInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadBrandInfoRequest.Merge(m, src)
}
func (m *UploadBrandInfoRequest) XXX_Size() int {
	return xxx_messageInfo_UploadBrandInfoRequest.Size(m)
}
func (m *UploadBrandInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadBrandInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UploadBrandInfoRequest proto.InternalMessageInfo

func (m *UploadBrandInfoRequest) GetAuditReq() *LicenseAndBrandInfo {
	if m != nil {
		return m.AuditReq
	}
	return nil
}

// 营业执照及品牌信息
type LicenseAndBrandInfo struct {
	// 营业执照图片
	License []string `protobuf:"bytes,1,rep,name=license,proto3" json:"license"`
	// 品牌信息
	BrandInfo            *BrandInfo `protobuf:"bytes,2,opt,name=brand_info,json=brandInfo,proto3" json:"brand_info"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *LicenseAndBrandInfo) Reset()         { *m = LicenseAndBrandInfo{} }
func (m *LicenseAndBrandInfo) String() string { return proto.CompactTextString(m) }
func (*LicenseAndBrandInfo) ProtoMessage()    {}
func (*LicenseAndBrandInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{9}
}

func (m *LicenseAndBrandInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LicenseAndBrandInfo.Unmarshal(m, b)
}
func (m *LicenseAndBrandInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LicenseAndBrandInfo.Marshal(b, m, deterministic)
}
func (m *LicenseAndBrandInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LicenseAndBrandInfo.Merge(m, src)
}
func (m *LicenseAndBrandInfo) XXX_Size() int {
	return xxx_messageInfo_LicenseAndBrandInfo.Size(m)
}
func (m *LicenseAndBrandInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LicenseAndBrandInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LicenseAndBrandInfo proto.InternalMessageInfo

func (m *LicenseAndBrandInfo) GetLicense() []string {
	if m != nil {
		return m.License
	}
	return nil
}

func (m *LicenseAndBrandInfo) GetBrandInfo() *BrandInfo {
	if m != nil {
		return m.BrandInfo
	}
	return nil
}

// 品牌信息
type BrandInfo struct {
	// 认证审核类型
	BrandAuditType int32 `protobuf:"varint,1,opt,name=brand_audit_type,json=brandAuditType,proto3" json:"brand_audit_type"`
	// 商标分类
	TrademarkType int32 `protobuf:"varint,2,opt,name=trademark_type,json=trademarkType,proto3" json:"trademark_type"`
	// 选择品牌经营类型
	BrandManagementType int32 `protobuf:"varint,3,opt,name=brand_management_type,json=brandManagementType,proto3" json:"brand_management_type"`
	// 商品产地是否进口
	CommodityOriginType int32 `protobuf:"varint,4,opt,name=commodity_origin_type,json=commodityOriginType,proto3" json:"commodity_origin_type"`
	// 商标/品牌词
	BrandWording string `protobuf:"bytes,5,opt,name=brand_wording,json=brandWording,proto3" json:"brand_wording"`
	// 销售授权书（如商持人为自然人，还需提供有其签名的身份证正反面扫描件)，图片url/media_id
	SaleAuthorization []string `protobuf:"bytes,6,rep,name=sale_authorization,json=saleAuthorization,proto3" json:"sale_authorization"`
	// 商标注册证书，图片url/media_id
	TrademarkRegistrationCertificate []string `protobuf:"bytes,7,rep,name=trademark_registration_certificate,json=trademarkRegistrationCertificate,proto3" json:"trademark_registration_certificate"`
	// 商标变更证明，图片url/media_id
	TrademarkChangeCertificate []string `protobuf:"bytes,8,rep,name=trademark_change_certificate,json=trademarkChangeCertificate,proto3" json:"trademark_change_certificate"`
	// 	商标注册人姓名
	TrademarkRegistrant string `protobuf:"bytes,9,opt,name=trademark_registrant,json=trademarkRegistrant,proto3" json:"trademark_registrant"`
	// 商标注册号/申请号
	TrademarkRegistrantNu string `protobuf:"bytes,10,opt,name=trademark_registrant_nu,json=trademarkRegistrantNu,proto3" json:"trademark_registrant_nu"`
	// 商标有效期，yyyy-MM-dd HH:mm:ss
	TrademarkAuthorizationPeriod string `protobuf:"bytes,11,opt,name=trademark_authorization_period,json=trademarkAuthorizationPeriod,proto3" json:"trademark_authorization_period"`
	// 商标注册申请受理通知书，图片url/media_id
	TrademarkRegistrationApplication []string `protobuf:"bytes,12,rep,name=trademark_registration_application,json=trademarkRegistrationApplication,proto3" json:"trademark_registration_application"`
	// 商标申请人姓名
	TrademarkApplicant string `protobuf:"bytes,13,opt,name=trademark_applicant,json=trademarkApplicant,proto3" json:"trademark_applicant"`
	// 商标申请时间, yyyy-MM-dd HH:mm:ss
	TrademarkApplicationTime string `protobuf:"bytes,14,opt,name=trademark_application_time,json=trademarkApplicationTime,proto3" json:"trademark_application_time"`
	// 中华人民共和国海关进口货物报关单，图片url/media_id
	ImportedGoodsForm    []string `protobuf:"bytes,15,rep,name=imported_goods_form,json=importedGoodsForm,proto3" json:"imported_goods_form"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandInfo) Reset()         { *m = BrandInfo{} }
func (m *BrandInfo) String() string { return proto.CompactTextString(m) }
func (*BrandInfo) ProtoMessage()    {}
func (*BrandInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{10}
}

func (m *BrandInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandInfo.Unmarshal(m, b)
}
func (m *BrandInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandInfo.Marshal(b, m, deterministic)
}
func (m *BrandInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandInfo.Merge(m, src)
}
func (m *BrandInfo) XXX_Size() int {
	return xxx_messageInfo_BrandInfo.Size(m)
}
func (m *BrandInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandInfo proto.InternalMessageInfo

func (m *BrandInfo) GetBrandAuditType() int32 {
	if m != nil {
		return m.BrandAuditType
	}
	return 0
}

func (m *BrandInfo) GetTrademarkType() int32 {
	if m != nil {
		return m.TrademarkType
	}
	return 0
}

func (m *BrandInfo) GetBrandManagementType() int32 {
	if m != nil {
		return m.BrandManagementType
	}
	return 0
}

func (m *BrandInfo) GetCommodityOriginType() int32 {
	if m != nil {
		return m.CommodityOriginType
	}
	return 0
}

func (m *BrandInfo) GetBrandWording() string {
	if m != nil {
		return m.BrandWording
	}
	return ""
}

func (m *BrandInfo) GetSaleAuthorization() []string {
	if m != nil {
		return m.SaleAuthorization
	}
	return nil
}

func (m *BrandInfo) GetTrademarkRegistrationCertificate() []string {
	if m != nil {
		return m.TrademarkRegistrationCertificate
	}
	return nil
}

func (m *BrandInfo) GetTrademarkChangeCertificate() []string {
	if m != nil {
		return m.TrademarkChangeCertificate
	}
	return nil
}

func (m *BrandInfo) GetTrademarkRegistrant() string {
	if m != nil {
		return m.TrademarkRegistrant
	}
	return ""
}

func (m *BrandInfo) GetTrademarkRegistrantNu() string {
	if m != nil {
		return m.TrademarkRegistrantNu
	}
	return ""
}

func (m *BrandInfo) GetTrademarkAuthorizationPeriod() string {
	if m != nil {
		return m.TrademarkAuthorizationPeriod
	}
	return ""
}

func (m *BrandInfo) GetTrademarkRegistrationApplication() []string {
	if m != nil {
		return m.TrademarkRegistrationApplication
	}
	return nil
}

func (m *BrandInfo) GetTrademarkApplicant() string {
	if m != nil {
		return m.TrademarkApplicant
	}
	return ""
}

func (m *BrandInfo) GetTrademarkApplicationTime() string {
	if m != nil {
		return m.TrademarkApplicationTime
	}
	return ""
}

func (m *BrandInfo) GetImportedGoodsForm() []string {
	if m != nil {
		return m.ImportedGoodsForm
	}
	return nil
}

// 上传品牌信息响应
type UploadBrandInfoResponse struct {
	// 错误码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 错误信息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	// 审核单id
	AuditId              string   `protobuf:"bytes,3,opt,name=audit_id,json=auditId,proto3" json:"audit_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadBrandInfoResponse) Reset()         { *m = UploadBrandInfoResponse{} }
func (m *UploadBrandInfoResponse) String() string { return proto.CompactTextString(m) }
func (*UploadBrandInfoResponse) ProtoMessage()    {}
func (*UploadBrandInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{11}
}

func (m *UploadBrandInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadBrandInfoResponse.Unmarshal(m, b)
}
func (m *UploadBrandInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadBrandInfoResponse.Marshal(b, m, deterministic)
}
func (m *UploadBrandInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadBrandInfoResponse.Merge(m, src)
}
func (m *UploadBrandInfoResponse) XXX_Size() int {
	return xxx_messageInfo_UploadBrandInfoResponse.Size(m)
}
func (m *UploadBrandInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadBrandInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UploadBrandInfoResponse proto.InternalMessageInfo

func (m *UploadBrandInfoResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *UploadBrandInfoResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *UploadBrandInfoResponse) GetAuditId() string {
	if m != nil {
		return m.AuditId
	}
	return ""
}

// 上传(创建与更新)商品至微信视频号请求参数 (是否必填，以下填是或否)
type UploadProductInfoRequest struct {
	// 商家自定义商品ID(是)
	OutProductId string `protobuf:"bytes,1,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	// 标题(是)
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 绑定的小程序商品路径(是)
	Path string `protobuf:"bytes,3,opt,name=path,proto3" json:"path"`
	// 主图,多张,列表(是)
	HeadImg []string `protobuf:"bytes,4,rep,name=head_img,json=headImg,proto3" json:"head_img"`
	// 商品资质图片(否)
	QualificationPics []string `protobuf:"bytes,5,rep,name=qualification_pics,json=qualificationPics,proto3" json:"qualification_pics"`
	// 商品详情图文(否)
	DescInfo *DescInfo `protobuf:"bytes,6,opt,name=desc_info,json=descInfo,proto3" json:"desc_info"`
	// 第三级类目ID(是)
	ThirdCatId int32 `protobuf:"varint,7,opt,name=third_cat_id,json=thirdCatId,proto3" json:"third_cat_id"`
	// 品牌id(是)
	BrandId int64 `protobuf:"varint,8,opt,name=brand_id,json=brandId,proto3" json:"brand_id"`
	// 预留字段，用于版本控制(否)
	InfoVersion string `protobuf:"bytes,9,opt,name=info_version,json=infoVersion,proto3" json:"info_version"`
	// sku数组(是)
	Skus                 []*SkuInfo `protobuf:"bytes,10,rep,name=skus,proto3" json:"skus"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UploadProductInfoRequest) Reset()         { *m = UploadProductInfoRequest{} }
func (m *UploadProductInfoRequest) String() string { return proto.CompactTextString(m) }
func (*UploadProductInfoRequest) ProtoMessage()    {}
func (*UploadProductInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{12}
}

func (m *UploadProductInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadProductInfoRequest.Unmarshal(m, b)
}
func (m *UploadProductInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadProductInfoRequest.Marshal(b, m, deterministic)
}
func (m *UploadProductInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadProductInfoRequest.Merge(m, src)
}
func (m *UploadProductInfoRequest) XXX_Size() int {
	return xxx_messageInfo_UploadProductInfoRequest.Size(m)
}
func (m *UploadProductInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadProductInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UploadProductInfoRequest proto.InternalMessageInfo

func (m *UploadProductInfoRequest) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

func (m *UploadProductInfoRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UploadProductInfoRequest) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *UploadProductInfoRequest) GetHeadImg() []string {
	if m != nil {
		return m.HeadImg
	}
	return nil
}

func (m *UploadProductInfoRequest) GetQualificationPics() []string {
	if m != nil {
		return m.QualificationPics
	}
	return nil
}

func (m *UploadProductInfoRequest) GetDescInfo() *DescInfo {
	if m != nil {
		return m.DescInfo
	}
	return nil
}

func (m *UploadProductInfoRequest) GetThirdCatId() int32 {
	if m != nil {
		return m.ThirdCatId
	}
	return 0
}

func (m *UploadProductInfoRequest) GetBrandId() int64 {
	if m != nil {
		return m.BrandId
	}
	return 0
}

func (m *UploadProductInfoRequest) GetInfoVersion() string {
	if m != nil {
		return m.InfoVersion
	}
	return ""
}

func (m *UploadProductInfoRequest) GetSkus() []*SkuInfo {
	if m != nil {
		return m.Skus
	}
	return nil
}

// 商品详情信息
type DescInfo struct {
	// 商品详情图文(否)
	Desc string `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc"`
	// 商品详情图片(否)
	Imgs                 []string `protobuf:"bytes,2,rep,name=imgs,proto3" json:"imgs"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DescInfo) Reset()         { *m = DescInfo{} }
func (m *DescInfo) String() string { return proto.CompactTextString(m) }
func (*DescInfo) ProtoMessage()    {}
func (*DescInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{13}
}

func (m *DescInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DescInfo.Unmarshal(m, b)
}
func (m *DescInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DescInfo.Marshal(b, m, deterministic)
}
func (m *DescInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescInfo.Merge(m, src)
}
func (m *DescInfo) XXX_Size() int {
	return xxx_messageInfo_DescInfo.Size(m)
}
func (m *DescInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DescInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DescInfo proto.InternalMessageInfo

func (m *DescInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *DescInfo) GetImgs() []string {
	if m != nil {
		return m.Imgs
	}
	return nil
}

// 商品sku
type SkuInfo struct {
	// 商家自定义商品ID(是)
	OutProductId string `protobuf:"bytes,1,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	// 商家自定义skuID(是)
	OutSkuId string `protobuf:"bytes,2,opt,name=out_sku_id,json=outSkuId,proto3" json:"out_sku_id"`
	// sku小图(是)
	ThumbImg string `protobuf:"bytes,3,opt,name=thumb_img,json=thumbImg,proto3" json:"thumb_img"`
	// 售卖价格,以分为单位(是)
	SalePrice int32 `protobuf:"varint,4,opt,name=sale_price,json=salePrice,proto3" json:"sale_price"`
	// 市场价格,以分为单位(是)
	MarketPrice int32 `protobuf:"varint,5,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	// 库存(是)
	StockNum int32 `protobuf:"varint,6,opt,name=stock_num,json=stockNum,proto3" json:"stock_num"`
	// 条形码(否)
	Barcode string `protobuf:"bytes,7,opt,name=barcode,proto3" json:"barcode"`
	// 商品编码(否)
	SkuCode string `protobuf:"bytes,8,opt,name=sku_code,json=skuCode,proto3" json:"sku_code"`
	// 销售属性(是)
	SkuAttrs             []*SkuAttrs `protobuf:"bytes,9,rep,name=sku_attrs,json=skuAttrs,proto3" json:"sku_attrs"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SkuInfo) Reset()         { *m = SkuInfo{} }
func (m *SkuInfo) String() string { return proto.CompactTextString(m) }
func (*SkuInfo) ProtoMessage()    {}
func (*SkuInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{14}
}

func (m *SkuInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuInfo.Unmarshal(m, b)
}
func (m *SkuInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuInfo.Marshal(b, m, deterministic)
}
func (m *SkuInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuInfo.Merge(m, src)
}
func (m *SkuInfo) XXX_Size() int {
	return xxx_messageInfo_SkuInfo.Size(m)
}
func (m *SkuInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SkuInfo proto.InternalMessageInfo

func (m *SkuInfo) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

func (m *SkuInfo) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *SkuInfo) GetThumbImg() string {
	if m != nil {
		return m.ThumbImg
	}
	return ""
}

func (m *SkuInfo) GetSalePrice() int32 {
	if m != nil {
		return m.SalePrice
	}
	return 0
}

func (m *SkuInfo) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *SkuInfo) GetStockNum() int32 {
	if m != nil {
		return m.StockNum
	}
	return 0
}

func (m *SkuInfo) GetBarcode() string {
	if m != nil {
		return m.Barcode
	}
	return ""
}

func (m *SkuInfo) GetSkuCode() string {
	if m != nil {
		return m.SkuCode
	}
	return ""
}

func (m *SkuInfo) GetSkuAttrs() []*SkuAttrs {
	if m != nil {
		return m.SkuAttrs
	}
	return nil
}

// 商品销售属性
type SkuAttrs struct {
	// 销售属性key（自定义）(是)
	AttrKey string `protobuf:"bytes,1,opt,name=attr_key,json=attrKey,proto3" json:"attr_key"`
	// 销售属性value（自定义）(是)
	AttrValue            string   `protobuf:"bytes,2,opt,name=attr_value,json=attrValue,proto3" json:"attr_value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuAttrs) Reset()         { *m = SkuAttrs{} }
func (m *SkuAttrs) String() string { return proto.CompactTextString(m) }
func (*SkuAttrs) ProtoMessage()    {}
func (*SkuAttrs) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{15}
}

func (m *SkuAttrs) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuAttrs.Unmarshal(m, b)
}
func (m *SkuAttrs) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuAttrs.Marshal(b, m, deterministic)
}
func (m *SkuAttrs) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuAttrs.Merge(m, src)
}
func (m *SkuAttrs) XXX_Size() int {
	return xxx_messageInfo_SkuAttrs.Size(m)
}
func (m *SkuAttrs) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuAttrs.DiscardUnknown(m)
}

var xxx_messageInfo_SkuAttrs proto.InternalMessageInfo

func (m *SkuAttrs) GetAttrKey() string {
	if m != nil {
		return m.AttrKey
	}
	return ""
}

func (m *SkuAttrs) GetAttrValue() string {
	if m != nil {
		return m.AttrValue
	}
	return ""
}

// 添加商品响应
type AddProductInfoResponse struct {
	// 错误码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 错误信息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	// 商品响应数据
	Data                 *AddProductInfoResponseData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *AddProductInfoResponse) Reset()         { *m = AddProductInfoResponse{} }
func (m *AddProductInfoResponse) String() string { return proto.CompactTextString(m) }
func (*AddProductInfoResponse) ProtoMessage()    {}
func (*AddProductInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{16}
}

func (m *AddProductInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddProductInfoResponse.Unmarshal(m, b)
}
func (m *AddProductInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddProductInfoResponse.Marshal(b, m, deterministic)
}
func (m *AddProductInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddProductInfoResponse.Merge(m, src)
}
func (m *AddProductInfoResponse) XXX_Size() int {
	return xxx_messageInfo_AddProductInfoResponse.Size(m)
}
func (m *AddProductInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddProductInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddProductInfoResponse proto.InternalMessageInfo

func (m *AddProductInfoResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *AddProductInfoResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *AddProductInfoResponse) GetData() *AddProductInfoResponseData {
	if m != nil {
		return m.Data
	}
	return nil
}

// 添加商品响应数据
type AddProductInfoResponseData struct {
	// 交易组件平台内部商品ID
	ProductId int64 `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id"`
	// 商家自定义商品ID
	OutProductId string `protobuf:"bytes,2,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	// 创建时间
	CreateTime string `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// sku数组
	Skus                 []*ProductInfoResponseSkus `protobuf:"bytes,4,rep,name=skus,proto3" json:"skus"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *AddProductInfoResponseData) Reset()         { *m = AddProductInfoResponseData{} }
func (m *AddProductInfoResponseData) String() string { return proto.CompactTextString(m) }
func (*AddProductInfoResponseData) ProtoMessage()    {}
func (*AddProductInfoResponseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{17}
}

func (m *AddProductInfoResponseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddProductInfoResponseData.Unmarshal(m, b)
}
func (m *AddProductInfoResponseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddProductInfoResponseData.Marshal(b, m, deterministic)
}
func (m *AddProductInfoResponseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddProductInfoResponseData.Merge(m, src)
}
func (m *AddProductInfoResponseData) XXX_Size() int {
	return xxx_messageInfo_AddProductInfoResponseData.Size(m)
}
func (m *AddProductInfoResponseData) XXX_DiscardUnknown() {
	xxx_messageInfo_AddProductInfoResponseData.DiscardUnknown(m)
}

var xxx_messageInfo_AddProductInfoResponseData proto.InternalMessageInfo

func (m *AddProductInfoResponseData) GetProductId() int64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *AddProductInfoResponseData) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

func (m *AddProductInfoResponseData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *AddProductInfoResponseData) GetSkus() []*ProductInfoResponseSkus {
	if m != nil {
		return m.Skus
	}
	return nil
}

// 商品响应sku
type ProductInfoResponseSkus struct {
	// 交易组件平台自定义skuID
	SkuId int64 `protobuf:"varint,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商家自定义skuID
	OutSkuId             string   `protobuf:"bytes,2,opt,name=out_sku_id,json=outSkuId,proto3" json:"out_sku_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductInfoResponseSkus) Reset()         { *m = ProductInfoResponseSkus{} }
func (m *ProductInfoResponseSkus) String() string { return proto.CompactTextString(m) }
func (*ProductInfoResponseSkus) ProtoMessage()    {}
func (*ProductInfoResponseSkus) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{18}
}

func (m *ProductInfoResponseSkus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductInfoResponseSkus.Unmarshal(m, b)
}
func (m *ProductInfoResponseSkus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductInfoResponseSkus.Marshal(b, m, deterministic)
}
func (m *ProductInfoResponseSkus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductInfoResponseSkus.Merge(m, src)
}
func (m *ProductInfoResponseSkus) XXX_Size() int {
	return xxx_messageInfo_ProductInfoResponseSkus.Size(m)
}
func (m *ProductInfoResponseSkus) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductInfoResponseSkus.DiscardUnknown(m)
}

var xxx_messageInfo_ProductInfoResponseSkus proto.InternalMessageInfo

func (m *ProductInfoResponseSkus) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *ProductInfoResponseSkus) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

// 商品列表请求参数
type ProductInfoListRequest struct {
	// 选填，不填时获取所有状态商品
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	// 创建开始时间 选填，与end_create_time成对 , yyyy-MM-dd HH:mm:ss
	StartCreateTime string `protobuf:"bytes,2,opt,name=start_create_time,json=startCreateTime,proto3" json:"start_create_time"`
	// 创建结束时间 选填，与start_create_time成对 , yyyy-MM-dd HH:mm:ss
	EndCreateTime string `protobuf:"bytes,3,opt,name=end_create_time,json=endCreateTime,proto3" json:"end_create_time"`
	// 更新开始时间 选填，与end_update_time成对 , yyyy-MM-dd HH:mm:ss
	StartUpdateTime string `protobuf:"bytes,4,opt,name=start_update_time,json=startUpdateTime,proto3" json:"start_update_time"`
	// 更新结束时间 选填，与start_update_time成对 , yyyy-MM-dd HH:mm:ss
	EndUpdateTime string `protobuf:"bytes,5,opt,name=end_update_time,json=endUpdateTime,proto3" json:"end_update_time"`
	// 页码
	Page int32 `protobuf:"varint,6,opt,name=page,proto3" json:"page"`
	// 每页数据量，不超过100
	PageSize int32 `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 默认0:获取线上数据, 1:获取草稿数据
	NeedEditSpu          int32    `protobuf:"varint,8,opt,name=need_edit_spu,json=needEditSpu,proto3" json:"need_edit_spu"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductInfoListRequest) Reset()         { *m = ProductInfoListRequest{} }
func (m *ProductInfoListRequest) String() string { return proto.CompactTextString(m) }
func (*ProductInfoListRequest) ProtoMessage()    {}
func (*ProductInfoListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{19}
}

func (m *ProductInfoListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductInfoListRequest.Unmarshal(m, b)
}
func (m *ProductInfoListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductInfoListRequest.Marshal(b, m, deterministic)
}
func (m *ProductInfoListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductInfoListRequest.Merge(m, src)
}
func (m *ProductInfoListRequest) XXX_Size() int {
	return xxx_messageInfo_ProductInfoListRequest.Size(m)
}
func (m *ProductInfoListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductInfoListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ProductInfoListRequest proto.InternalMessageInfo

func (m *ProductInfoListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ProductInfoListRequest) GetStartCreateTime() string {
	if m != nil {
		return m.StartCreateTime
	}
	return ""
}

func (m *ProductInfoListRequest) GetEndCreateTime() string {
	if m != nil {
		return m.EndCreateTime
	}
	return ""
}

func (m *ProductInfoListRequest) GetStartUpdateTime() string {
	if m != nil {
		return m.StartUpdateTime
	}
	return ""
}

func (m *ProductInfoListRequest) GetEndUpdateTime() string {
	if m != nil {
		return m.EndUpdateTime
	}
	return ""
}

func (m *ProductInfoListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ProductInfoListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ProductInfoListRequest) GetNeedEditSpu() int32 {
	if m != nil {
		return m.NeedEditSpu
	}
	return 0
}

// 商品列表响应
type ProductInfoListResponse struct {
	// 错误码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 错误信息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	// 总数
	TotalNum int32 `protobuf:"varint,3,opt,name=total_num,json=totalNum,proto3" json:"total_num"`
	// 商品信息列表
	Spus                 []*ProductInfo `protobuf:"bytes,4,rep,name=spus,proto3" json:"spus"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ProductInfoListResponse) Reset()         { *m = ProductInfoListResponse{} }
func (m *ProductInfoListResponse) String() string { return proto.CompactTextString(m) }
func (*ProductInfoListResponse) ProtoMessage()    {}
func (*ProductInfoListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{20}
}

func (m *ProductInfoListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductInfoListResponse.Unmarshal(m, b)
}
func (m *ProductInfoListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductInfoListResponse.Marshal(b, m, deterministic)
}
func (m *ProductInfoListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductInfoListResponse.Merge(m, src)
}
func (m *ProductInfoListResponse) XXX_Size() int {
	return xxx_messageInfo_ProductInfoListResponse.Size(m)
}
func (m *ProductInfoListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductInfoListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ProductInfoListResponse proto.InternalMessageInfo

func (m *ProductInfoListResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *ProductInfoListResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *ProductInfoListResponse) GetTotalNum() int32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

func (m *ProductInfoListResponse) GetSpus() []*ProductInfo {
	if m != nil {
		return m.Spus
	}
	return nil
}

// 商品数据
type ProductInfo struct {
	// 商家自定义商品ID
	OutProductId string `protobuf:"bytes,1,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	// 标题(是)
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 绑定的小程序商品路径
	Path string `protobuf:"bytes,3,opt,name=path,proto3" json:"path"`
	// 主图,多张,列表
	HeadImg []string `protobuf:"bytes,4,rep,name=head_img,json=headImg,proto3" json:"head_img"`
	// 商品审核信息，可能为空
	AuditInfo *AuditInfo `protobuf:"bytes,5,opt,name=audit_info,json=auditInfo,proto3" json:"audit_info"`
	// 商品详情图文
	DescInfo *DescInfo `protobuf:"bytes,6,opt,name=desc_info,json=descInfo,proto3" json:"desc_info"`
	// 第三级类目ID
	ThirdCatId int32 `protobuf:"varint,7,opt,name=third_cat_id,json=thirdCatId,proto3" json:"third_cat_id"`
	// 品牌id
	BrandId int64 `protobuf:"varint,8,opt,name=brand_id,json=brandId,proto3" json:"brand_id"`
	// 预留字段，用于版本控制
	InfoVersion string `protobuf:"bytes,9,opt,name=info_version,json=infoVersion,proto3" json:"info_version"`
	// sku数组(是)
	Skus []*SkuInfo `protobuf:"bytes,10,rep,name=skus,proto3" json:"skus"`
	// 商品线上状态 0 初始值，5 上架，11 自主下架，13 违规下架/风控系统下架
	Status int32 `protobuf:"varint,11,opt,name=status,proto3" json:"status"`
	// 商品草稿状态  0 初始值，1 编辑中，2 审核中，3 审核失败，4 审核成功
	EditStatus int32 `protobuf:"varint,12,opt,name=edit_status,json=editStatus,proto3" json:"edit_status"`
	// 创建时间
	CreateTime string `protobuf:"bytes,13,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 更新时间
	UpdateTime           string   `protobuf:"bytes,14,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductInfo) Reset()         { *m = ProductInfo{} }
func (m *ProductInfo) String() string { return proto.CompactTextString(m) }
func (*ProductInfo) ProtoMessage()    {}
func (*ProductInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{21}
}

func (m *ProductInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductInfo.Unmarshal(m, b)
}
func (m *ProductInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductInfo.Marshal(b, m, deterministic)
}
func (m *ProductInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductInfo.Merge(m, src)
}
func (m *ProductInfo) XXX_Size() int {
	return xxx_messageInfo_ProductInfo.Size(m)
}
func (m *ProductInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ProductInfo proto.InternalMessageInfo

func (m *ProductInfo) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

func (m *ProductInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ProductInfo) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *ProductInfo) GetHeadImg() []string {
	if m != nil {
		return m.HeadImg
	}
	return nil
}

func (m *ProductInfo) GetAuditInfo() *AuditInfo {
	if m != nil {
		return m.AuditInfo
	}
	return nil
}

func (m *ProductInfo) GetDescInfo() *DescInfo {
	if m != nil {
		return m.DescInfo
	}
	return nil
}

func (m *ProductInfo) GetThirdCatId() int32 {
	if m != nil {
		return m.ThirdCatId
	}
	return 0
}

func (m *ProductInfo) GetBrandId() int64 {
	if m != nil {
		return m.BrandId
	}
	return 0
}

func (m *ProductInfo) GetInfoVersion() string {
	if m != nil {
		return m.InfoVersion
	}
	return ""
}

func (m *ProductInfo) GetSkus() []*SkuInfo {
	if m != nil {
		return m.Skus
	}
	return nil
}

func (m *ProductInfo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ProductInfo) GetEditStatus() int32 {
	if m != nil {
		return m.EditStatus
	}
	return 0
}

func (m *ProductInfo) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *ProductInfo) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

// 商品审核信息
type AuditInfo struct {
	// 上一次审核时间, yyyy-MM-dd HH:mm:ss
	AuditTime string `protobuf:"bytes,1,opt,name=audit_time,json=auditTime,proto3" json:"audit_time"`
	// 拒绝理由
	RejectReason         string   `protobuf:"bytes,2,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuditInfo) Reset()         { *m = AuditInfo{} }
func (m *AuditInfo) String() string { return proto.CompactTextString(m) }
func (*AuditInfo) ProtoMessage()    {}
func (*AuditInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{22}
}

func (m *AuditInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuditInfo.Unmarshal(m, b)
}
func (m *AuditInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuditInfo.Marshal(b, m, deterministic)
}
func (m *AuditInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuditInfo.Merge(m, src)
}
func (m *AuditInfo) XXX_Size() int {
	return xxx_messageInfo_AuditInfo.Size(m)
}
func (m *AuditInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AuditInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AuditInfo proto.InternalMessageInfo

func (m *AuditInfo) GetAuditTime() string {
	if m != nil {
		return m.AuditTime
	}
	return ""
}

func (m *AuditInfo) GetRejectReason() string {
	if m != nil {
		return m.RejectReason
	}
	return ""
}

// 更新商品响应
type UpdateProductInfoResponse struct {
	// 错误码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 错误信息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	// 更新商品响应
	Data                 *UpdateProductInfoResponseData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *UpdateProductInfoResponse) Reset()         { *m = UpdateProductInfoResponse{} }
func (m *UpdateProductInfoResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateProductInfoResponse) ProtoMessage()    {}
func (*UpdateProductInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{23}
}

func (m *UpdateProductInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateProductInfoResponse.Unmarshal(m, b)
}
func (m *UpdateProductInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateProductInfoResponse.Marshal(b, m, deterministic)
}
func (m *UpdateProductInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateProductInfoResponse.Merge(m, src)
}
func (m *UpdateProductInfoResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateProductInfoResponse.Size(m)
}
func (m *UpdateProductInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateProductInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateProductInfoResponse proto.InternalMessageInfo

func (m *UpdateProductInfoResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *UpdateProductInfoResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *UpdateProductInfoResponse) GetData() *UpdateProductInfoResponseData {
	if m != nil {
		return m.Data
	}
	return nil
}

// 更新商品响应数据
type UpdateProductInfoResponseData struct {
	// 交易组件平台内部商品ID
	ProductId int64 `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id"`
	// 商家自定义商品ID
	OutProductId string `protobuf:"bytes,2,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	// 创建时间
	UpdateTime string `protobuf:"bytes,3,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	// sku数组
	Skus                 []*ProductInfoResponseSkus `protobuf:"bytes,4,rep,name=skus,proto3" json:"skus"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UpdateProductInfoResponseData) Reset()         { *m = UpdateProductInfoResponseData{} }
func (m *UpdateProductInfoResponseData) String() string { return proto.CompactTextString(m) }
func (*UpdateProductInfoResponseData) ProtoMessage()    {}
func (*UpdateProductInfoResponseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{24}
}

func (m *UpdateProductInfoResponseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateProductInfoResponseData.Unmarshal(m, b)
}
func (m *UpdateProductInfoResponseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateProductInfoResponseData.Marshal(b, m, deterministic)
}
func (m *UpdateProductInfoResponseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateProductInfoResponseData.Merge(m, src)
}
func (m *UpdateProductInfoResponseData) XXX_Size() int {
	return xxx_messageInfo_UpdateProductInfoResponseData.Size(m)
}
func (m *UpdateProductInfoResponseData) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateProductInfoResponseData.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateProductInfoResponseData proto.InternalMessageInfo

func (m *UpdateProductInfoResponseData) GetProductId() int64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *UpdateProductInfoResponseData) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

func (m *UpdateProductInfoResponseData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *UpdateProductInfoResponseData) GetSkus() []*ProductInfoResponseSkus {
	if m != nil {
		return m.Skus
	}
	return nil
}

// 免审核更新商品请求参数
type FreeUpdateProductInfoRequest struct {
	// 商家自定义商品ID，与product_id二选一
	OutProductId string `protobuf:"bytes,1,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	// 绑定的小程序商品路径
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path"`
	// sku数组
	Skus                 []*FreeUpdateProductInfoSku `protobuf:"bytes,3,rep,name=skus,proto3" json:"skus"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *FreeUpdateProductInfoRequest) Reset()         { *m = FreeUpdateProductInfoRequest{} }
func (m *FreeUpdateProductInfoRequest) String() string { return proto.CompactTextString(m) }
func (*FreeUpdateProductInfoRequest) ProtoMessage()    {}
func (*FreeUpdateProductInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{25}
}

func (m *FreeUpdateProductInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreeUpdateProductInfoRequest.Unmarshal(m, b)
}
func (m *FreeUpdateProductInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreeUpdateProductInfoRequest.Marshal(b, m, deterministic)
}
func (m *FreeUpdateProductInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreeUpdateProductInfoRequest.Merge(m, src)
}
func (m *FreeUpdateProductInfoRequest) XXX_Size() int {
	return xxx_messageInfo_FreeUpdateProductInfoRequest.Size(m)
}
func (m *FreeUpdateProductInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FreeUpdateProductInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FreeUpdateProductInfoRequest proto.InternalMessageInfo

func (m *FreeUpdateProductInfoRequest) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

func (m *FreeUpdateProductInfoRequest) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *FreeUpdateProductInfoRequest) GetSkus() []*FreeUpdateProductInfoSku {
	if m != nil {
		return m.Skus
	}
	return nil
}

// 免审核商品sku
type FreeUpdateProductInfoSku struct {
	// 商家自定义skuID
	OutSkuId string `protobuf:"bytes,1,opt,name=out_sku_id,json=outSkuId,proto3" json:"out_sku_id"`
	// 售卖价格,以分为单位
	SalePrice int32 `protobuf:"varint,2,opt,name=sale_price,json=salePrice,proto3" json:"sale_price"`
	// 市场价格,以分为单位
	MarketPrice int32 `protobuf:"varint,3,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	// 库存
	StockNum int32 `protobuf:"varint,4,opt,name=stock_num,json=stockNum,proto3" json:"stock_num"`
	// 条形码
	Barcode string `protobuf:"bytes,5,opt,name=barcode,proto3" json:"barcode"`
	// 商品编码
	SkuCode              string   `protobuf:"bytes,6,opt,name=sku_code,json=skuCode,proto3" json:"sku_code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreeUpdateProductInfoSku) Reset()         { *m = FreeUpdateProductInfoSku{} }
func (m *FreeUpdateProductInfoSku) String() string { return proto.CompactTextString(m) }
func (*FreeUpdateProductInfoSku) ProtoMessage()    {}
func (*FreeUpdateProductInfoSku) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{26}
}

func (m *FreeUpdateProductInfoSku) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreeUpdateProductInfoSku.Unmarshal(m, b)
}
func (m *FreeUpdateProductInfoSku) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreeUpdateProductInfoSku.Marshal(b, m, deterministic)
}
func (m *FreeUpdateProductInfoSku) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreeUpdateProductInfoSku.Merge(m, src)
}
func (m *FreeUpdateProductInfoSku) XXX_Size() int {
	return xxx_messageInfo_FreeUpdateProductInfoSku.Size(m)
}
func (m *FreeUpdateProductInfoSku) XXX_DiscardUnknown() {
	xxx_messageInfo_FreeUpdateProductInfoSku.DiscardUnknown(m)
}

var xxx_messageInfo_FreeUpdateProductInfoSku proto.InternalMessageInfo

func (m *FreeUpdateProductInfoSku) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *FreeUpdateProductInfoSku) GetSalePrice() int32 {
	if m != nil {
		return m.SalePrice
	}
	return 0
}

func (m *FreeUpdateProductInfoSku) GetMarketPrice() int32 {
	if m != nil {
		return m.MarketPrice
	}
	return 0
}

func (m *FreeUpdateProductInfoSku) GetStockNum() int32 {
	if m != nil {
		return m.StockNum
	}
	return 0
}

func (m *FreeUpdateProductInfoSku) GetBarcode() string {
	if m != nil {
		return m.Barcode
	}
	return ""
}

func (m *FreeUpdateProductInfoSku) GetSkuCode() string {
	if m != nil {
		return m.SkuCode
	}
	return ""
}

// 生成订单并获取ticket,文档地址：https://developers.weixin.qq.com/miniprogram/dev/framework/ministore/minishopopencomponent2/API/order/add_order.html
type OrderAddRequest struct {
	// 创建时间
	CreateTime string `protobuf:"bytes,1,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 商家自定义订单id，即我们自己平台订单号
	OutOrderId string `protobuf:"bytes,2,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	// 用户openid
	Openid string `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	// 商家小程序该订单的页面path，用于微信侧订单中心跳转
	Path string `protobuf:"bytes,4,opt,name=path,proto3" json:"path"`
	// 我们自己平台用户id，非必填
	OutUserId string `protobuf:"bytes,6,opt,name=out_user_id,json=outUserId,proto3" json:"out_user_id"`
	// 订单类型：0，普通单，1，二级商户单
	FundType int32 `protobuf:"varint,7,opt,name=fund_type,json=fundType,proto3" json:"fund_type"`
	// unix秒级时间戳，订单超时时间，取值：[15min, 1d]
	ExpireTime int32 `protobuf:"varint,8,opt,name=expire_time,json=expireTime,proto3" json:"expire_time"`
	// 取值范围，[7，3 * 365]，单位：天
	AftersaleDuration int32 `protobuf:"varint,9,opt,name=aftersale_duration,json=aftersaleDuration,proto3" json:"aftersale_duration"`
	// 会影响主播归因、分享员归因等，从下单前置检查获取
	TraceId string `protobuf:"bytes,10,opt,name=trace_id,json=traceId,proto3" json:"trace_id"`
	// 订单详情
	OrderDetail *OrderDetail `protobuf:"bytes,11,opt,name=order_detail,json=orderDetail,proto3" json:"order_detail"`
	// 配送方式信息
	DeliveryDetail *DeliveryDetail `protobuf:"bytes,12,opt,name=delivery_detail,json=deliveryDetail,proto3" json:"delivery_detail"`
	// 地址信息，delivery_type = 2 无需设置, delivery_type = 4 填写自提门店地址
	AddressInfo *AddressInfo `protobuf:"bytes,13,opt,name=address_info,json=addressInfo,proto3" json:"address_info"`
	// 默认退货地址，退货售后超时时，会让用户将货物寄往此地址。
	DefaultReceivingAddress *AddressInfo `protobuf:"bytes,14,opt,name=default_receiving_address,json=defaultReceivingAddress,proto3" json:"default_receiving_address"`
	XXX_NoUnkeyedLiteral    struct{}     `json:"-"`
	XXX_unrecognized        []byte       `json:"-"`
	XXX_sizecache           int32        `json:"-"`
}

func (m *OrderAddRequest) Reset()         { *m = OrderAddRequest{} }
func (m *OrderAddRequest) String() string { return proto.CompactTextString(m) }
func (*OrderAddRequest) ProtoMessage()    {}
func (*OrderAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{27}
}

func (m *OrderAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderAddRequest.Unmarshal(m, b)
}
func (m *OrderAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderAddRequest.Marshal(b, m, deterministic)
}
func (m *OrderAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderAddRequest.Merge(m, src)
}
func (m *OrderAddRequest) XXX_Size() int {
	return xxx_messageInfo_OrderAddRequest.Size(m)
}
func (m *OrderAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderAddRequest proto.InternalMessageInfo

func (m *OrderAddRequest) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *OrderAddRequest) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *OrderAddRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *OrderAddRequest) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *OrderAddRequest) GetOutUserId() string {
	if m != nil {
		return m.OutUserId
	}
	return ""
}

func (m *OrderAddRequest) GetFundType() int32 {
	if m != nil {
		return m.FundType
	}
	return 0
}

func (m *OrderAddRequest) GetExpireTime() int32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *OrderAddRequest) GetAftersaleDuration() int32 {
	if m != nil {
		return m.AftersaleDuration
	}
	return 0
}

func (m *OrderAddRequest) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *OrderAddRequest) GetOrderDetail() *OrderDetail {
	if m != nil {
		return m.OrderDetail
	}
	return nil
}

func (m *OrderAddRequest) GetDeliveryDetail() *DeliveryDetail {
	if m != nil {
		return m.DeliveryDetail
	}
	return nil
}

func (m *OrderAddRequest) GetAddressInfo() *AddressInfo {
	if m != nil {
		return m.AddressInfo
	}
	return nil
}

func (m *OrderAddRequest) GetDefaultReceivingAddress() *AddressInfo {
	if m != nil {
		return m.DefaultReceivingAddress
	}
	return nil
}

// 订单详情
type OrderDetail struct {
	ProductInfos         []*ProductInfos `protobuf:"bytes,1,rep,name=product_infos,json=productInfos,proto3" json:"product_infos"`
	PayInfo              *PayInfo        `protobuf:"bytes,2,opt,name=pay_info,json=payInfo,proto3" json:"pay_info"`
	PriceInfo            *PriceInfo      `protobuf:"bytes,3,opt,name=price_info,json=priceInfo,proto3" json:"price_info"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *OrderDetail) Reset()         { *m = OrderDetail{} }
func (m *OrderDetail) String() string { return proto.CompactTextString(m) }
func (*OrderDetail) ProtoMessage()    {}
func (*OrderDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{28}
}

func (m *OrderDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderDetail.Unmarshal(m, b)
}
func (m *OrderDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderDetail.Marshal(b, m, deterministic)
}
func (m *OrderDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderDetail.Merge(m, src)
}
func (m *OrderDetail) XXX_Size() int {
	return xxx_messageInfo_OrderDetail.Size(m)
}
func (m *OrderDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderDetail.DiscardUnknown(m)
}

var xxx_messageInfo_OrderDetail proto.InternalMessageInfo

func (m *OrderDetail) GetProductInfos() []*ProductInfos {
	if m != nil {
		return m.ProductInfos
	}
	return nil
}

func (m *OrderDetail) GetPayInfo() *PayInfo {
	if m != nil {
		return m.PayInfo
	}
	return nil
}

func (m *OrderDetail) GetPriceInfo() *PriceInfo {
	if m != nil {
		return m.PriceInfo
	}
	return nil
}

// 产品详情
type ProductInfos struct {
	// 商家自定义商品ID
	OutProductId string `protobuf:"bytes,1,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	// 商家自定义商品skuID，可填空字符串（如果这个product_id下没有sku）
	OutSkuId string `protobuf:"bytes,2,opt,name=out_sku_id,json=outSkuId,proto3" json:"out_sku_id"`
	// 购买的数量
	ProductCnt int32 `protobuf:"varint,3,opt,name=product_cnt,json=productCnt,proto3" json:"product_cnt"`
	// 生成订单时商品的售卖价（单位：分），可以跟上传商品接口的价格不一致
	SalePrice int32 `protobuf:"varint,4,opt,name=sale_price,json=salePrice,proto3" json:"sale_price"`
	// sku总实付价
	SkuRealPrice int32 `protobuf:"varint,5,opt,name=sku_real_price,json=skuRealPrice,proto3" json:"sku_real_price"`
	// 绑定的小程序商品路径
	Path string `protobuf:"bytes,6,opt,name=path,proto3" json:"path"`
	// 生成订单时商品的标题
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title"`
	// 生成订单时商品的头图
	HeadImg              string   `protobuf:"bytes,8,opt,name=head_img,json=headImg,proto3" json:"head_img"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductInfos) Reset()         { *m = ProductInfos{} }
func (m *ProductInfos) String() string { return proto.CompactTextString(m) }
func (*ProductInfos) ProtoMessage()    {}
func (*ProductInfos) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{29}
}

func (m *ProductInfos) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductInfos.Unmarshal(m, b)
}
func (m *ProductInfos) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductInfos.Marshal(b, m, deterministic)
}
func (m *ProductInfos) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductInfos.Merge(m, src)
}
func (m *ProductInfos) XXX_Size() int {
	return xxx_messageInfo_ProductInfos.Size(m)
}
func (m *ProductInfos) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductInfos.DiscardUnknown(m)
}

var xxx_messageInfo_ProductInfos proto.InternalMessageInfo

func (m *ProductInfos) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

func (m *ProductInfos) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *ProductInfos) GetProductCnt() int32 {
	if m != nil {
		return m.ProductCnt
	}
	return 0
}

func (m *ProductInfos) GetSalePrice() int32 {
	if m != nil {
		return m.SalePrice
	}
	return 0
}

func (m *ProductInfos) GetSkuRealPrice() int32 {
	if m != nil {
		return m.SkuRealPrice
	}
	return 0
}

func (m *ProductInfos) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *ProductInfos) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ProductInfos) GetHeadImg() string {
	if m != nil {
		return m.HeadImg
	}
	return ""
}

// 支付信息
type PayInfo struct {
	// 支付方式，0，微信支付，1: 货到付款，2：商家会员储蓄卡（默认0）
	PayMethodType        int32    `protobuf:"varint,1,opt,name=pay_method_type,json=payMethodType,proto3" json:"pay_method_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayInfo) Reset()         { *m = PayInfo{} }
func (m *PayInfo) String() string { return proto.CompactTextString(m) }
func (*PayInfo) ProtoMessage()    {}
func (*PayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{30}
}

func (m *PayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayInfo.Unmarshal(m, b)
}
func (m *PayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayInfo.Marshal(b, m, deterministic)
}
func (m *PayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayInfo.Merge(m, src)
}
func (m *PayInfo) XXX_Size() int {
	return xxx_messageInfo_PayInfo.Size(m)
}
func (m *PayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PayInfo proto.InternalMessageInfo

func (m *PayInfo) GetPayMethodType() int32 {
	if m != nil {
		return m.PayMethodType
	}
	return 0
}

// 价格信息
type PriceInfo struct {
	// 该订单最终的金额（单位：分
	OrderPrice int32 `protobuf:"varint,1,opt,name=order_price,json=orderPrice,proto3" json:"order_price"`
	// 运费（单位：分）
	Freight int32 `protobuf:"varint,2,opt,name=freight,proto3" json:"freight"`
	// 优惠金额（单位：分））,不是必填
	DiscountedPrice int32 `protobuf:"varint,3,opt,name=discounted_price,json=discountedPrice,proto3" json:"discounted_price"`
	// 附加金额（单位：分）,不是必填
	AdditionalPrice int32 `protobuf:"varint,4,opt,name=additional_price,json=additionalPrice,proto3" json:"additional_price"`
	// 附加金额备注,不是必填
	AdditionalRemarks    string   `protobuf:"bytes,5,opt,name=additional_remarks,json=additionalRemarks,proto3" json:"additional_remarks"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PriceInfo) Reset()         { *m = PriceInfo{} }
func (m *PriceInfo) String() string { return proto.CompactTextString(m) }
func (*PriceInfo) ProtoMessage()    {}
func (*PriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{31}
}

func (m *PriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PriceInfo.Unmarshal(m, b)
}
func (m *PriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PriceInfo.Marshal(b, m, deterministic)
}
func (m *PriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PriceInfo.Merge(m, src)
}
func (m *PriceInfo) XXX_Size() int {
	return xxx_messageInfo_PriceInfo.Size(m)
}
func (m *PriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PriceInfo proto.InternalMessageInfo

func (m *PriceInfo) GetOrderPrice() int32 {
	if m != nil {
		return m.OrderPrice
	}
	return 0
}

func (m *PriceInfo) GetFreight() int32 {
	if m != nil {
		return m.Freight
	}
	return 0
}

func (m *PriceInfo) GetDiscountedPrice() int32 {
	if m != nil {
		return m.DiscountedPrice
	}
	return 0
}

func (m *PriceInfo) GetAdditionalPrice() int32 {
	if m != nil {
		return m.AdditionalPrice
	}
	return 0
}

func (m *PriceInfo) GetAdditionalRemarks() string {
	if m != nil {
		return m.AdditionalRemarks
	}
	return ""
}

// 配送方式信息
type DeliveryDetail struct {
	// 1: 正常快递, 2: 无需快递, 3: 线下配送, 4: 用户自提 （默认1）
	DeliveryType         int32    `protobuf:"varint,1,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeliveryDetail) Reset()         { *m = DeliveryDetail{} }
func (m *DeliveryDetail) String() string { return proto.CompactTextString(m) }
func (*DeliveryDetail) ProtoMessage()    {}
func (*DeliveryDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{32}
}

func (m *DeliveryDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryDetail.Unmarshal(m, b)
}
func (m *DeliveryDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryDetail.Marshal(b, m, deterministic)
}
func (m *DeliveryDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryDetail.Merge(m, src)
}
func (m *DeliveryDetail) XXX_Size() int {
	return xxx_messageInfo_DeliveryDetail.Size(m)
}
func (m *DeliveryDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryDetail proto.InternalMessageInfo

func (m *DeliveryDetail) GetDeliveryType() int32 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

// 地址信息
type AddressInfo struct {
	// 收件人姓名
	ReceiverName string `protobuf:"bytes,1,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name"`
	// 详细收货地址信息
	DetailedAddress string `protobuf:"bytes,2,opt,name=detailed_address,json=detailedAddress,proto3" json:"detailed_address"`
	// 收件人手机号码
	TelNumber string `protobuf:"bytes,3,opt,name=tel_number,json=telNumber,proto3" json:"tel_number"`
	// 国家,不是必填
	Country string `protobuf:"bytes,4,opt,name=country,proto3" json:"country"`
	// 省份,不是必填
	Province string `protobuf:"bytes,5,opt,name=province,proto3" json:"province"`
	// 城市,不是必填
	City string `protobuf:"bytes,6,opt,name=city,proto3" json:"city"`
	// 乡镇,不是必填
	Town                 string   `protobuf:"bytes,7,opt,name=town,proto3" json:"town"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddressInfo) Reset()         { *m = AddressInfo{} }
func (m *AddressInfo) String() string { return proto.CompactTextString(m) }
func (*AddressInfo) ProtoMessage()    {}
func (*AddressInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{33}
}

func (m *AddressInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddressInfo.Unmarshal(m, b)
}
func (m *AddressInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddressInfo.Marshal(b, m, deterministic)
}
func (m *AddressInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddressInfo.Merge(m, src)
}
func (m *AddressInfo) XXX_Size() int {
	return xxx_messageInfo_AddressInfo.Size(m)
}
func (m *AddressInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AddressInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AddressInfo proto.InternalMessageInfo

func (m *AddressInfo) GetReceiverName() string {
	if m != nil {
		return m.ReceiverName
	}
	return ""
}

func (m *AddressInfo) GetDetailedAddress() string {
	if m != nil {
		return m.DetailedAddress
	}
	return ""
}

func (m *AddressInfo) GetTelNumber() string {
	if m != nil {
		return m.TelNumber
	}
	return ""
}

func (m *AddressInfo) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *AddressInfo) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *AddressInfo) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *AddressInfo) GetTown() string {
	if m != nil {
		return m.Town
	}
	return ""
}

// 生成订单并获取ticket响应
type OrderAddResponse struct {
	// 错误码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 错误信息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	// 交易组件平台订单信息
	Data                 *OrderAddDataResponse `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *OrderAddResponse) Reset()         { *m = OrderAddResponse{} }
func (m *OrderAddResponse) String() string { return proto.CompactTextString(m) }
func (*OrderAddResponse) ProtoMessage()    {}
func (*OrderAddResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{34}
}

func (m *OrderAddResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderAddResponse.Unmarshal(m, b)
}
func (m *OrderAddResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderAddResponse.Marshal(b, m, deterministic)
}
func (m *OrderAddResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderAddResponse.Merge(m, src)
}
func (m *OrderAddResponse) XXX_Size() int {
	return xxx_messageInfo_OrderAddResponse.Size(m)
}
func (m *OrderAddResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderAddResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderAddResponse proto.InternalMessageInfo

func (m *OrderAddResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *OrderAddResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *OrderAddResponse) GetData() *OrderAddDataResponse {
	if m != nil {
		return m.Data
	}
	return nil
}

type OrderAddDataResponse struct {
	// 交易组件平台订单ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 交易组件平台订单ID
	OutOrderId           string   `protobuf:"bytes,2,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderAddDataResponse) Reset()         { *m = OrderAddDataResponse{} }
func (m *OrderAddDataResponse) String() string { return proto.CompactTextString(m) }
func (*OrderAddDataResponse) ProtoMessage()    {}
func (*OrderAddDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{35}
}

func (m *OrderAddDataResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderAddDataResponse.Unmarshal(m, b)
}
func (m *OrderAddDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderAddDataResponse.Marshal(b, m, deterministic)
}
func (m *OrderAddDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderAddDataResponse.Merge(m, src)
}
func (m *OrderAddDataResponse) XXX_Size() int {
	return xxx_messageInfo_OrderAddDataResponse.Size(m)
}
func (m *OrderAddDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderAddDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderAddDataResponse proto.InternalMessageInfo

func (m *OrderAddDataResponse) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *OrderAddDataResponse) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

type OrderGetPaymentParamsReq struct {
	// 微信侧订单id
	OrderId int32 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 商家自定义订单ID
	OutOrderId string `protobuf:"bytes,2,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	// 用户的openid
	Openid               string   `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderGetPaymentParamsReq) Reset()         { *m = OrderGetPaymentParamsReq{} }
func (m *OrderGetPaymentParamsReq) String() string { return proto.CompactTextString(m) }
func (*OrderGetPaymentParamsReq) ProtoMessage()    {}
func (*OrderGetPaymentParamsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{36}
}

func (m *OrderGetPaymentParamsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderGetPaymentParamsReq.Unmarshal(m, b)
}
func (m *OrderGetPaymentParamsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderGetPaymentParamsReq.Marshal(b, m, deterministic)
}
func (m *OrderGetPaymentParamsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderGetPaymentParamsReq.Merge(m, src)
}
func (m *OrderGetPaymentParamsReq) XXX_Size() int {
	return xxx_messageInfo_OrderGetPaymentParamsReq.Size(m)
}
func (m *OrderGetPaymentParamsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderGetPaymentParamsReq.DiscardUnknown(m)
}

var xxx_messageInfo_OrderGetPaymentParamsReq proto.InternalMessageInfo

func (m *OrderGetPaymentParamsReq) GetOrderId() int32 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *OrderGetPaymentParamsReq) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *OrderGetPaymentParamsReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

type OrderGetPaymentParamsRes struct {
	// 错误码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 错误信息
	Errmsg               string                         `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	PaymentParams        *OrderGetPaymentParamsRes_Data `protobuf:"bytes,3,opt,name=payment_params,json=paymentParams,proto3" json:"payment_params"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *OrderGetPaymentParamsRes) Reset()         { *m = OrderGetPaymentParamsRes{} }
func (m *OrderGetPaymentParamsRes) String() string { return proto.CompactTextString(m) }
func (*OrderGetPaymentParamsRes) ProtoMessage()    {}
func (*OrderGetPaymentParamsRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{37}
}

func (m *OrderGetPaymentParamsRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderGetPaymentParamsRes.Unmarshal(m, b)
}
func (m *OrderGetPaymentParamsRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderGetPaymentParamsRes.Marshal(b, m, deterministic)
}
func (m *OrderGetPaymentParamsRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderGetPaymentParamsRes.Merge(m, src)
}
func (m *OrderGetPaymentParamsRes) XXX_Size() int {
	return xxx_messageInfo_OrderGetPaymentParamsRes.Size(m)
}
func (m *OrderGetPaymentParamsRes) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderGetPaymentParamsRes.DiscardUnknown(m)
}

var xxx_messageInfo_OrderGetPaymentParamsRes proto.InternalMessageInfo

func (m *OrderGetPaymentParamsRes) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *OrderGetPaymentParamsRes) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *OrderGetPaymentParamsRes) GetPaymentParams() *OrderGetPaymentParamsRes_Data {
	if m != nil {
		return m.PaymentParams
	}
	return nil
}

type OrderGetPaymentParamsRes_Data struct {
	TimeStamp            int32    `protobuf:"varint,1,opt,name=timeStamp,proto3" json:"timeStamp"`
	NonceStr             string   `protobuf:"bytes,2,opt,name=nonceStr,proto3" json:"nonceStr"`
	Package              string   `protobuf:"bytes,3,opt,name=package,proto3" json:"package"`
	PaySign              string   `protobuf:"bytes,4,opt,name=paySign,proto3" json:"paySign"`
	SignType             string   `protobuf:"bytes,5,opt,name=signType,proto3" json:"signType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderGetPaymentParamsRes_Data) Reset()         { *m = OrderGetPaymentParamsRes_Data{} }
func (m *OrderGetPaymentParamsRes_Data) String() string { return proto.CompactTextString(m) }
func (*OrderGetPaymentParamsRes_Data) ProtoMessage()    {}
func (*OrderGetPaymentParamsRes_Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{37, 0}
}

func (m *OrderGetPaymentParamsRes_Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderGetPaymentParamsRes_Data.Unmarshal(m, b)
}
func (m *OrderGetPaymentParamsRes_Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderGetPaymentParamsRes_Data.Marshal(b, m, deterministic)
}
func (m *OrderGetPaymentParamsRes_Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderGetPaymentParamsRes_Data.Merge(m, src)
}
func (m *OrderGetPaymentParamsRes_Data) XXX_Size() int {
	return xxx_messageInfo_OrderGetPaymentParamsRes_Data.Size(m)
}
func (m *OrderGetPaymentParamsRes_Data) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderGetPaymentParamsRes_Data.DiscardUnknown(m)
}

var xxx_messageInfo_OrderGetPaymentParamsRes_Data proto.InternalMessageInfo

func (m *OrderGetPaymentParamsRes_Data) GetTimeStamp() int32 {
	if m != nil {
		return m.TimeStamp
	}
	return 0
}

func (m *OrderGetPaymentParamsRes_Data) GetNonceStr() string {
	if m != nil {
		return m.NonceStr
	}
	return ""
}

func (m *OrderGetPaymentParamsRes_Data) GetPackage() string {
	if m != nil {
		return m.Package
	}
	return ""
}

func (m *OrderGetPaymentParamsRes_Data) GetPaySign() string {
	if m != nil {
		return m.PaySign
	}
	return ""
}

func (m *OrderGetPaymentParamsRes_Data) GetSignType() string {
	if m != nil {
		return m.SignType
	}
	return ""
}

// 同步订单支付结果请求
type OrderPayStatusRequest struct {
	// 订单ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 商家自定义订单ID，与 order_id 二选一
	OutOrderId string `protobuf:"bytes,2,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	// 用户的openid
	Openid string `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	// 类型，默认1:支付成功,2:支付失败,3:用户取消,4:超时未支付;5:商家取消;10:其他原因取消
	ActionType int32 `protobuf:"varint,4,opt,name=action_type,json=actionType,proto3" json:"action_type"`
	// 其他具体原因，非必填
	ActionRemark string `protobuf:"bytes,5,opt,name=action_remark,json=actionRemark,proto3" json:"action_remark"`
	// 支付订单号，action_type=1且order/add时传的pay_method_type=0时必填，，非必填
	TransactionId string `protobuf:"bytes,6,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id"`
	// 支付完成时间，action_type=1时必填
	PayTime              string   `protobuf:"bytes,7,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderPayStatusRequest) Reset()         { *m = OrderPayStatusRequest{} }
func (m *OrderPayStatusRequest) String() string { return proto.CompactTextString(m) }
func (*OrderPayStatusRequest) ProtoMessage()    {}
func (*OrderPayStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{38}
}

func (m *OrderPayStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderPayStatusRequest.Unmarshal(m, b)
}
func (m *OrderPayStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderPayStatusRequest.Marshal(b, m, deterministic)
}
func (m *OrderPayStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderPayStatusRequest.Merge(m, src)
}
func (m *OrderPayStatusRequest) XXX_Size() int {
	return xxx_messageInfo_OrderPayStatusRequest.Size(m)
}
func (m *OrderPayStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderPayStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderPayStatusRequest proto.InternalMessageInfo

func (m *OrderPayStatusRequest) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *OrderPayStatusRequest) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *OrderPayStatusRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *OrderPayStatusRequest) GetActionType() int32 {
	if m != nil {
		return m.ActionType
	}
	return 0
}

func (m *OrderPayStatusRequest) GetActionRemark() string {
	if m != nil {
		return m.ActionRemark
	}
	return ""
}

func (m *OrderPayStatusRequest) GetTransactionId() string {
	if m != nil {
		return m.TransactionId
	}
	return ""
}

func (m *OrderPayStatusRequest) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

// 获取订单请求
type OrderListRequest struct {
	// 订单ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 商家自定义订单ID，与 order_id 二选一
	OutOrderId string `protobuf:"bytes,2,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	// 用户的openid
	Openid               string   `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListRequest) Reset()         { *m = OrderListRequest{} }
func (m *OrderListRequest) String() string { return proto.CompactTextString(m) }
func (*OrderListRequest) ProtoMessage()    {}
func (*OrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{39}
}

func (m *OrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListRequest.Unmarshal(m, b)
}
func (m *OrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListRequest.Marshal(b, m, deterministic)
}
func (m *OrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListRequest.Merge(m, src)
}
func (m *OrderListRequest) XXX_Size() int {
	return xxx_messageInfo_OrderListRequest.Size(m)
}
func (m *OrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListRequest proto.InternalMessageInfo

func (m *OrderListRequest) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *OrderListRequest) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *OrderListRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

type OrderListResponse struct {
	// 错误码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 错误信息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	// 交易组件平台订单信息
	Order                *OrderListDataResponse `protobuf:"bytes,3,opt,name=order,proto3" json:"order"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *OrderListResponse) Reset()         { *m = OrderListResponse{} }
func (m *OrderListResponse) String() string { return proto.CompactTextString(m) }
func (*OrderListResponse) ProtoMessage()    {}
func (*OrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{40}
}

func (m *OrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListResponse.Unmarshal(m, b)
}
func (m *OrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListResponse.Marshal(b, m, deterministic)
}
func (m *OrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListResponse.Merge(m, src)
}
func (m *OrderListResponse) XXX_Size() int {
	return xxx_messageInfo_OrderListResponse.Size(m)
}
func (m *OrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListResponse proto.InternalMessageInfo

func (m *OrderListResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *OrderListResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *OrderListResponse) GetOrder() *OrderListDataResponse {
	if m != nil {
		return m.Order
	}
	return nil
}

type OrderListDataResponse struct {
	OrderId              int64            `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	OutOrderId           string           `protobuf:"bytes,2,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	Status               int32            `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	Path                 string           `protobuf:"bytes,4,opt,name=path,proto3" json:"path"`
	OrderDetail          *OrderListDetail `protobuf:"bytes,5,opt,name=order_detail,json=orderDetail,proto3" json:"order_detail"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *OrderListDataResponse) Reset()         { *m = OrderListDataResponse{} }
func (m *OrderListDataResponse) String() string { return proto.CompactTextString(m) }
func (*OrderListDataResponse) ProtoMessage()    {}
func (*OrderListDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{41}
}

func (m *OrderListDataResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListDataResponse.Unmarshal(m, b)
}
func (m *OrderListDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListDataResponse.Marshal(b, m, deterministic)
}
func (m *OrderListDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListDataResponse.Merge(m, src)
}
func (m *OrderListDataResponse) XXX_Size() int {
	return xxx_messageInfo_OrderListDataResponse.Size(m)
}
func (m *OrderListDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListDataResponse proto.InternalMessageInfo

func (m *OrderListDataResponse) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *OrderListDataResponse) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *OrderListDataResponse) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *OrderListDataResponse) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *OrderListDataResponse) GetOrderDetail() *OrderListDetail {
	if m != nil {
		return m.OrderDetail
	}
	return nil
}

type OrderListDetail struct {
	PromotionInfo        *OrderListPromotionInfo  `protobuf:"bytes,1,opt,name=promotion_info,json=promotionInfo,proto3" json:"promotion_info"`
	ProductInfos         []*OrderListProductInfos `protobuf:"bytes,2,rep,name=product_infos,json=productInfos,proto3" json:"product_infos"`
	PayInfo              *OrderListPayInfo        `protobuf:"bytes,3,opt,name=pay_info,json=payInfo,proto3" json:"pay_info"`
	PriceInfo            *OrderListPriceInfo      `protobuf:"bytes,4,opt,name=price_info,json=priceInfo,proto3" json:"price_info"`
	DeliveryDetail       *OrderListDeliveryDetail `protobuf:"bytes,5,opt,name=delivery_detail,json=deliveryDetail,proto3" json:"delivery_detail"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *OrderListDetail) Reset()         { *m = OrderListDetail{} }
func (m *OrderListDetail) String() string { return proto.CompactTextString(m) }
func (*OrderListDetail) ProtoMessage()    {}
func (*OrderListDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{42}
}

func (m *OrderListDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListDetail.Unmarshal(m, b)
}
func (m *OrderListDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListDetail.Marshal(b, m, deterministic)
}
func (m *OrderListDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListDetail.Merge(m, src)
}
func (m *OrderListDetail) XXX_Size() int {
	return xxx_messageInfo_OrderListDetail.Size(m)
}
func (m *OrderListDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListDetail.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListDetail proto.InternalMessageInfo

func (m *OrderListDetail) GetPromotionInfo() *OrderListPromotionInfo {
	if m != nil {
		return m.PromotionInfo
	}
	return nil
}

func (m *OrderListDetail) GetProductInfos() []*OrderListProductInfos {
	if m != nil {
		return m.ProductInfos
	}
	return nil
}

func (m *OrderListDetail) GetPayInfo() *OrderListPayInfo {
	if m != nil {
		return m.PayInfo
	}
	return nil
}

func (m *OrderListDetail) GetPriceInfo() *OrderListPriceInfo {
	if m != nil {
		return m.PriceInfo
	}
	return nil
}

func (m *OrderListDetail) GetDeliveryDetail() *OrderListDeliveryDetail {
	if m != nil {
		return m.DeliveryDetail
	}
	return nil
}

type OrderListPromotionInfo struct {
	FinderUsername       string   `protobuf:"bytes,1,opt,name=finder_username,json=finderUsername,proto3" json:"finder_username"`
	FinderNickname       string   `protobuf:"bytes,2,opt,name=finder_nickname,json=finderNickname,proto3" json:"finder_nickname"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListPromotionInfo) Reset()         { *m = OrderListPromotionInfo{} }
func (m *OrderListPromotionInfo) String() string { return proto.CompactTextString(m) }
func (*OrderListPromotionInfo) ProtoMessage()    {}
func (*OrderListPromotionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{43}
}

func (m *OrderListPromotionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListPromotionInfo.Unmarshal(m, b)
}
func (m *OrderListPromotionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListPromotionInfo.Marshal(b, m, deterministic)
}
func (m *OrderListPromotionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListPromotionInfo.Merge(m, src)
}
func (m *OrderListPromotionInfo) XXX_Size() int {
	return xxx_messageInfo_OrderListPromotionInfo.Size(m)
}
func (m *OrderListPromotionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListPromotionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListPromotionInfo proto.InternalMessageInfo

func (m *OrderListPromotionInfo) GetFinderUsername() string {
	if m != nil {
		return m.FinderUsername
	}
	return ""
}

func (m *OrderListPromotionInfo) GetFinderNickname() string {
	if m != nil {
		return m.FinderNickname
	}
	return ""
}

type OrderListProductInfos struct {
	OutProductId         string   `protobuf:"bytes,1,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	OutSkuId             string   `protobuf:"bytes,2,opt,name=out_sku_id,json=outSkuId,proto3" json:"out_sku_id"`
	ProductCnt           int32    `protobuf:"varint,3,opt,name=product_cnt,json=productCnt,proto3" json:"product_cnt"`
	SalePrice            int32    `protobuf:"varint,4,opt,name=sale_price,json=salePrice,proto3" json:"sale_price"`
	Path                 string   `protobuf:"bytes,5,opt,name=path,proto3" json:"path"`
	Title                string   `protobuf:"bytes,6,opt,name=title,proto3" json:"title"`
	HeadImage            string   `protobuf:"bytes,7,opt,name=head_image,json=headImage,proto3" json:"head_image"`
	RealPrice            int32    `protobuf:"varint,8,opt,name=real_price,json=realPrice,proto3" json:"real_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListProductInfos) Reset()         { *m = OrderListProductInfos{} }
func (m *OrderListProductInfos) String() string { return proto.CompactTextString(m) }
func (*OrderListProductInfos) ProtoMessage()    {}
func (*OrderListProductInfos) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{44}
}

func (m *OrderListProductInfos) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListProductInfos.Unmarshal(m, b)
}
func (m *OrderListProductInfos) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListProductInfos.Marshal(b, m, deterministic)
}
func (m *OrderListProductInfos) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListProductInfos.Merge(m, src)
}
func (m *OrderListProductInfos) XXX_Size() int {
	return xxx_messageInfo_OrderListProductInfos.Size(m)
}
func (m *OrderListProductInfos) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListProductInfos.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListProductInfos proto.InternalMessageInfo

func (m *OrderListProductInfos) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

func (m *OrderListProductInfos) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *OrderListProductInfos) GetProductCnt() int32 {
	if m != nil {
		return m.ProductCnt
	}
	return 0
}

func (m *OrderListProductInfos) GetSalePrice() int32 {
	if m != nil {
		return m.SalePrice
	}
	return 0
}

func (m *OrderListProductInfos) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *OrderListProductInfos) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *OrderListProductInfos) GetHeadImage() string {
	if m != nil {
		return m.HeadImage
	}
	return ""
}

func (m *OrderListProductInfos) GetRealPrice() int32 {
	if m != nil {
		return m.RealPrice
	}
	return 0
}

type OrderListPayInfo struct {
	PayMethod            string   `protobuf:"bytes,1,opt,name=pay_method,json=payMethod,proto3" json:"pay_method"`
	PrepayId             string   `protobuf:"bytes,2,opt,name=prepay_id,json=prepayId,proto3" json:"prepay_id"`
	PrepayTime           string   `protobuf:"bytes,3,opt,name=prepay_time,json=prepayTime,proto3" json:"prepay_time"`
	TransactionId        string   `protobuf:"bytes,4,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id"`
	PayTime              string   `protobuf:"bytes,5,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	PayMethodType        int32    `protobuf:"varint,6,opt,name=pay_method_type,json=payMethodType,proto3" json:"pay_method_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListPayInfo) Reset()         { *m = OrderListPayInfo{} }
func (m *OrderListPayInfo) String() string { return proto.CompactTextString(m) }
func (*OrderListPayInfo) ProtoMessage()    {}
func (*OrderListPayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{45}
}

func (m *OrderListPayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListPayInfo.Unmarshal(m, b)
}
func (m *OrderListPayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListPayInfo.Marshal(b, m, deterministic)
}
func (m *OrderListPayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListPayInfo.Merge(m, src)
}
func (m *OrderListPayInfo) XXX_Size() int {
	return xxx_messageInfo_OrderListPayInfo.Size(m)
}
func (m *OrderListPayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListPayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListPayInfo proto.InternalMessageInfo

func (m *OrderListPayInfo) GetPayMethod() string {
	if m != nil {
		return m.PayMethod
	}
	return ""
}

func (m *OrderListPayInfo) GetPrepayId() string {
	if m != nil {
		return m.PrepayId
	}
	return ""
}

func (m *OrderListPayInfo) GetPrepayTime() string {
	if m != nil {
		return m.PrepayTime
	}
	return ""
}

func (m *OrderListPayInfo) GetTransactionId() string {
	if m != nil {
		return m.TransactionId
	}
	return ""
}

func (m *OrderListPayInfo) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *OrderListPayInfo) GetPayMethodType() int32 {
	if m != nil {
		return m.PayMethodType
	}
	return 0
}

type OrderListPriceInfo struct {
	OrderPrice           int32    `protobuf:"varint,1,opt,name=order_price,json=orderPrice,proto3" json:"order_price"`
	Freight              int32    `protobuf:"varint,2,opt,name=freight,proto3" json:"freight"`
	DiscountedPrice      int32    `protobuf:"varint,3,opt,name=discounted_price,json=discountedPrice,proto3" json:"discounted_price"`
	AdditionalPrice      int32    `protobuf:"varint,4,opt,name=additional_price,json=additionalPrice,proto3" json:"additional_price"`
	AdditionalRemarks    string   `protobuf:"bytes,5,opt,name=additional_remarks,json=additionalRemarks,proto3" json:"additional_remarks"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListPriceInfo) Reset()         { *m = OrderListPriceInfo{} }
func (m *OrderListPriceInfo) String() string { return proto.CompactTextString(m) }
func (*OrderListPriceInfo) ProtoMessage()    {}
func (*OrderListPriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{46}
}

func (m *OrderListPriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListPriceInfo.Unmarshal(m, b)
}
func (m *OrderListPriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListPriceInfo.Marshal(b, m, deterministic)
}
func (m *OrderListPriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListPriceInfo.Merge(m, src)
}
func (m *OrderListPriceInfo) XXX_Size() int {
	return xxx_messageInfo_OrderListPriceInfo.Size(m)
}
func (m *OrderListPriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListPriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListPriceInfo proto.InternalMessageInfo

func (m *OrderListPriceInfo) GetOrderPrice() int32 {
	if m != nil {
		return m.OrderPrice
	}
	return 0
}

func (m *OrderListPriceInfo) GetFreight() int32 {
	if m != nil {
		return m.Freight
	}
	return 0
}

func (m *OrderListPriceInfo) GetDiscountedPrice() int32 {
	if m != nil {
		return m.DiscountedPrice
	}
	return 0
}

func (m *OrderListPriceInfo) GetAdditionalPrice() int32 {
	if m != nil {
		return m.AdditionalPrice
	}
	return 0
}

func (m *OrderListPriceInfo) GetAdditionalRemarks() string {
	if m != nil {
		return m.AdditionalRemarks
	}
	return ""
}

type OrderListDeliveryDetail struct {
	DeliveryType         int32                          `protobuf:"varint,1,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type"`
	FinishAllDelivery    int32                          `protobuf:"varint,2,opt,name=finish_all_delivery,json=finishAllDelivery,proto3" json:"finish_all_delivery"`
	DeliveryList         []*OrderListDeliveryDetailList `protobuf:"bytes,3,rep,name=delivery_list,json=deliveryList,proto3" json:"delivery_list"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *OrderListDeliveryDetail) Reset()         { *m = OrderListDeliveryDetail{} }
func (m *OrderListDeliveryDetail) String() string { return proto.CompactTextString(m) }
func (*OrderListDeliveryDetail) ProtoMessage()    {}
func (*OrderListDeliveryDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{47}
}

func (m *OrderListDeliveryDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListDeliveryDetail.Unmarshal(m, b)
}
func (m *OrderListDeliveryDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListDeliveryDetail.Marshal(b, m, deterministic)
}
func (m *OrderListDeliveryDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListDeliveryDetail.Merge(m, src)
}
func (m *OrderListDeliveryDetail) XXX_Size() int {
	return xxx_messageInfo_OrderListDeliveryDetail.Size(m)
}
func (m *OrderListDeliveryDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListDeliveryDetail.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListDeliveryDetail proto.InternalMessageInfo

func (m *OrderListDeliveryDetail) GetDeliveryType() int32 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

func (m *OrderListDeliveryDetail) GetFinishAllDelivery() int32 {
	if m != nil {
		return m.FinishAllDelivery
	}
	return 0
}

func (m *OrderListDeliveryDetail) GetDeliveryList() []*OrderListDeliveryDetailList {
	if m != nil {
		return m.DeliveryList
	}
	return nil
}

type OrderListDeliveryDetailList struct {
	WaybillId            string   `protobuf:"bytes,1,opt,name=waybill_id,json=waybillId,proto3" json:"waybill_id"`
	DeliveryId           string   `protobuf:"bytes,2,opt,name=delivery_id,json=deliveryId,proto3" json:"delivery_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderListDeliveryDetailList) Reset()         { *m = OrderListDeliveryDetailList{} }
func (m *OrderListDeliveryDetailList) String() string { return proto.CompactTextString(m) }
func (*OrderListDeliveryDetailList) ProtoMessage()    {}
func (*OrderListDeliveryDetailList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{48}
}

func (m *OrderListDeliveryDetailList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderListDeliveryDetailList.Unmarshal(m, b)
}
func (m *OrderListDeliveryDetailList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderListDeliveryDetailList.Marshal(b, m, deterministic)
}
func (m *OrderListDeliveryDetailList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderListDeliveryDetailList.Merge(m, src)
}
func (m *OrderListDeliveryDetailList) XXX_Size() int {
	return xxx_messageInfo_OrderListDeliveryDetailList.Size(m)
}
func (m *OrderListDeliveryDetailList) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderListDeliveryDetailList.DiscardUnknown(m)
}

var xxx_messageInfo_OrderListDeliveryDetailList proto.InternalMessageInfo

func (m *OrderListDeliveryDetailList) GetWaybillId() string {
	if m != nil {
		return m.WaybillId
	}
	return ""
}

func (m *OrderListDeliveryDetailList) GetDeliveryId() string {
	if m != nil {
		return m.DeliveryId
	}
	return ""
}

// 获取快递公司列表响应
type DeliveryCompanyListResponse struct {
	// 错误码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 错误信息
	Errmsg string `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	// 快递公司信息
	CompanyList          []*DeliveryCompanyListData `protobuf:"bytes,3,rep,name=company_list,json=companyList,proto3" json:"company_list"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *DeliveryCompanyListResponse) Reset()         { *m = DeliveryCompanyListResponse{} }
func (m *DeliveryCompanyListResponse) String() string { return proto.CompactTextString(m) }
func (*DeliveryCompanyListResponse) ProtoMessage()    {}
func (*DeliveryCompanyListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{49}
}

func (m *DeliveryCompanyListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryCompanyListResponse.Unmarshal(m, b)
}
func (m *DeliveryCompanyListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryCompanyListResponse.Marshal(b, m, deterministic)
}
func (m *DeliveryCompanyListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryCompanyListResponse.Merge(m, src)
}
func (m *DeliveryCompanyListResponse) XXX_Size() int {
	return xxx_messageInfo_DeliveryCompanyListResponse.Size(m)
}
func (m *DeliveryCompanyListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryCompanyListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryCompanyListResponse proto.InternalMessageInfo

func (m *DeliveryCompanyListResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *DeliveryCompanyListResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *DeliveryCompanyListResponse) GetCompanyList() []*DeliveryCompanyListData {
	if m != nil {
		return m.CompanyList
	}
	return nil
}

type DeliveryCompanyListData struct {
	// 快递公司id
	DeliveryId string `protobuf:"bytes,1,opt,name=delivery_id,json=deliveryId,proto3" json:"delivery_id"`
	// 快递公司名称
	DeliveryName         string   `protobuf:"bytes,2,opt,name=delivery_name,json=deliveryName,proto3" json:"delivery_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeliveryCompanyListData) Reset()         { *m = DeliveryCompanyListData{} }
func (m *DeliveryCompanyListData) String() string { return proto.CompactTextString(m) }
func (*DeliveryCompanyListData) ProtoMessage()    {}
func (*DeliveryCompanyListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{50}
}

func (m *DeliveryCompanyListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryCompanyListData.Unmarshal(m, b)
}
func (m *DeliveryCompanyListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryCompanyListData.Marshal(b, m, deterministic)
}
func (m *DeliveryCompanyListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryCompanyListData.Merge(m, src)
}
func (m *DeliveryCompanyListData) XXX_Size() int {
	return xxx_messageInfo_DeliveryCompanyListData.Size(m)
}
func (m *DeliveryCompanyListData) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryCompanyListData.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryCompanyListData proto.InternalMessageInfo

func (m *DeliveryCompanyListData) GetDeliveryId() string {
	if m != nil {
		return m.DeliveryId
	}
	return ""
}

func (m *DeliveryCompanyListData) GetDeliveryName() string {
	if m != nil {
		return m.DeliveryName
	}
	return ""
}

// 订单发货请求
type DeliverySendRequest struct {
	// 订单ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 商家自定义订单ID，与 order_id 二选一
	OutOrderId string `protobuf:"bytes,2,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	// 用户的openid
	Openid string `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	// 发货完成标志位, 0: 未发完, 1:已发完
	FinishAllDelivery int32 `protobuf:"varint,4,opt,name=finish_all_delivery,json=finishAllDelivery,proto3" json:"finish_all_delivery"`
	// 快递信息
	DeliveryList         []*DeliveryListData `protobuf:"bytes,5,rep,name=delivery_list,json=deliveryList,proto3" json:"delivery_list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *DeliverySendRequest) Reset()         { *m = DeliverySendRequest{} }
func (m *DeliverySendRequest) String() string { return proto.CompactTextString(m) }
func (*DeliverySendRequest) ProtoMessage()    {}
func (*DeliverySendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{51}
}

func (m *DeliverySendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliverySendRequest.Unmarshal(m, b)
}
func (m *DeliverySendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliverySendRequest.Marshal(b, m, deterministic)
}
func (m *DeliverySendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliverySendRequest.Merge(m, src)
}
func (m *DeliverySendRequest) XXX_Size() int {
	return xxx_messageInfo_DeliverySendRequest.Size(m)
}
func (m *DeliverySendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliverySendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeliverySendRequest proto.InternalMessageInfo

func (m *DeliverySendRequest) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *DeliverySendRequest) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *DeliverySendRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *DeliverySendRequest) GetFinishAllDelivery() int32 {
	if m != nil {
		return m.FinishAllDelivery
	}
	return 0
}

func (m *DeliverySendRequest) GetDeliveryList() []*DeliveryListData {
	if m != nil {
		return m.DeliveryList
	}
	return nil
}

type DeliveryListData struct {
	// 快递公司ID，通过获取快递公司列表获取
	DeliveryId string `protobuf:"bytes,1,opt,name=delivery_id,json=deliveryId,proto3" json:"delivery_id"`
	// 快递单号
	WaybillId            string   `protobuf:"bytes,2,opt,name=waybill_id,json=waybillId,proto3" json:"waybill_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeliveryListData) Reset()         { *m = DeliveryListData{} }
func (m *DeliveryListData) String() string { return proto.CompactTextString(m) }
func (*DeliveryListData) ProtoMessage()    {}
func (*DeliveryListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{52}
}

func (m *DeliveryListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryListData.Unmarshal(m, b)
}
func (m *DeliveryListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryListData.Marshal(b, m, deterministic)
}
func (m *DeliveryListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryListData.Merge(m, src)
}
func (m *DeliveryListData) XXX_Size() int {
	return xxx_messageInfo_DeliveryListData.Size(m)
}
func (m *DeliveryListData) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryListData.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryListData proto.InternalMessageInfo

func (m *DeliveryListData) GetDeliveryId() string {
	if m != nil {
		return m.DeliveryId
	}
	return ""
}

func (m *DeliveryListData) GetWaybillId() string {
	if m != nil {
		return m.WaybillId
	}
	return ""
}

// 订单确认收货请求
type DeliveryRecieveRequest struct {
	// 订单ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 商家自定义订单ID，与 order_id 二选一
	OutOrderId string `protobuf:"bytes,2,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	// 用户的openid
	Openid               string   `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeliveryRecieveRequest) Reset()         { *m = DeliveryRecieveRequest{} }
func (m *DeliveryRecieveRequest) String() string { return proto.CompactTextString(m) }
func (*DeliveryRecieveRequest) ProtoMessage()    {}
func (*DeliveryRecieveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{53}
}

func (m *DeliveryRecieveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryRecieveRequest.Unmarshal(m, b)
}
func (m *DeliveryRecieveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryRecieveRequest.Marshal(b, m, deterministic)
}
func (m *DeliveryRecieveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryRecieveRequest.Merge(m, src)
}
func (m *DeliveryRecieveRequest) XXX_Size() int {
	return xxx_messageInfo_DeliveryRecieveRequest.Size(m)
}
func (m *DeliveryRecieveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryRecieveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryRecieveRequest proto.InternalMessageInfo

func (m *DeliveryRecieveRequest) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *DeliveryRecieveRequest) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *DeliveryRecieveRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

// 创建售后请求
type AftersaleAddRequest struct {
	// 商家自定义订单ID
	OutOrderId string `protobuf:"bytes,1,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	// 商家自定义售后ID
	OutAftersaleId string `protobuf:"bytes,2,opt,name=out_aftersale_id,json=outAftersaleId,proto3" json:"out_aftersale_id"`
	// 用户的openid
	Openid string `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	// 售后类型，1:退款,2:退款退货,3:换货
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	// 发起申请时间，yyyy-MM-dd HH:mm:ss
	CreateTime string `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 0:未受理,1:用户取消,2:商家受理中,3:商家逾期未处理,4:商家拒绝退款,5:商家拒绝退货退款,6:待买家退货,7:退货退款关闭,8:待商家收货,11:商家退款中,12:商家逾期未退款,13:退款完成,14:退货退款完成,15:换货完成,16:待商家发货,17:待用户确认收货,18:商家拒绝换货,19:商家已收到货
	Status int32 `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	// 0:订单可继续售后, 1:订单无继续售后
	FinishAllAftersale int32 `protobuf:"varint,7,opt,name=finish_all_aftersale,json=finishAllAftersale,proto3" json:"finish_all_aftersale"`
	// 商家小程序该售后单的页面path，不存在则使用订单path
	Path string `protobuf:"bytes,8,opt,name=path,proto3" json:"path"`
	// 退款金额,单位：分
	Refund int32 `protobuf:"varint,9,opt,name=refund,proto3" json:"refund"`
	// 退货相关商品列表
	ProductInfos         []*AfterSaleProductInfos `protobuf:"bytes,10,rep,name=product_infos,json=productInfos,proto3" json:"product_infos"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AftersaleAddRequest) Reset()         { *m = AftersaleAddRequest{} }
func (m *AftersaleAddRequest) String() string { return proto.CompactTextString(m) }
func (*AftersaleAddRequest) ProtoMessage()    {}
func (*AftersaleAddRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{54}
}

func (m *AftersaleAddRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AftersaleAddRequest.Unmarshal(m, b)
}
func (m *AftersaleAddRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AftersaleAddRequest.Marshal(b, m, deterministic)
}
func (m *AftersaleAddRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AftersaleAddRequest.Merge(m, src)
}
func (m *AftersaleAddRequest) XXX_Size() int {
	return xxx_messageInfo_AftersaleAddRequest.Size(m)
}
func (m *AftersaleAddRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AftersaleAddRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AftersaleAddRequest proto.InternalMessageInfo

func (m *AftersaleAddRequest) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *AftersaleAddRequest) GetOutAftersaleId() string {
	if m != nil {
		return m.OutAftersaleId
	}
	return ""
}

func (m *AftersaleAddRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *AftersaleAddRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AftersaleAddRequest) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *AftersaleAddRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AftersaleAddRequest) GetFinishAllAftersale() int32 {
	if m != nil {
		return m.FinishAllAftersale
	}
	return 0
}

func (m *AftersaleAddRequest) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *AftersaleAddRequest) GetRefund() int32 {
	if m != nil {
		return m.Refund
	}
	return 0
}

func (m *AftersaleAddRequest) GetProductInfos() []*AfterSaleProductInfos {
	if m != nil {
		return m.ProductInfos
	}
	return nil
}

type AfterSaleProductInfos struct {
	// 商家自定义商品ID
	OutProductId string `protobuf:"bytes,1,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	// 商家自定义sku ID, 如果没有则不填
	OutSkuId string `protobuf:"bytes,2,opt,name=out_sku_id,json=outSkuId,proto3" json:"out_sku_id"`
	// 参与售后的商品数量,product_infos存在时必填
	ProductCnt           int32    `protobuf:"varint,3,opt,name=product_cnt,json=productCnt,proto3" json:"product_cnt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AfterSaleProductInfos) Reset()         { *m = AfterSaleProductInfos{} }
func (m *AfterSaleProductInfos) String() string { return proto.CompactTextString(m) }
func (*AfterSaleProductInfos) ProtoMessage()    {}
func (*AfterSaleProductInfos) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{55}
}

func (m *AfterSaleProductInfos) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AfterSaleProductInfos.Unmarshal(m, b)
}
func (m *AfterSaleProductInfos) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AfterSaleProductInfos.Marshal(b, m, deterministic)
}
func (m *AfterSaleProductInfos) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AfterSaleProductInfos.Merge(m, src)
}
func (m *AfterSaleProductInfos) XXX_Size() int {
	return xxx_messageInfo_AfterSaleProductInfos.Size(m)
}
func (m *AfterSaleProductInfos) XXX_DiscardUnknown() {
	xxx_messageInfo_AfterSaleProductInfos.DiscardUnknown(m)
}

var xxx_messageInfo_AfterSaleProductInfos proto.InternalMessageInfo

func (m *AfterSaleProductInfos) GetOutProductId() string {
	if m != nil {
		return m.OutProductId
	}
	return ""
}

func (m *AfterSaleProductInfos) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *AfterSaleProductInfos) GetProductCnt() int32 {
	if m != nil {
		return m.ProductCnt
	}
	return 0
}

// 更新售后
type AftersaleUpdateRequest struct {
	// 商家订单ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 商家自定义订单ID，与 order_id 二选一
	OutOrderId string `protobuf:"bytes,2,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	// 用户的openid
	Openid string `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	// 商家自定义售后ID
	OutAftersaleId string `protobuf:"bytes,4,opt,name=out_aftersale_id,json=outAftersaleId,proto3" json:"out_aftersale_id"`
	// 0:未受理,1:用户取消,2:商家受理中,3:商家逾期未处理,4:商家拒绝退款,5:商家拒绝退货退款,6:待买家退货,7:退货退款关闭,8:待商家收货,11:商家退款中,12:商家逾期未退款,13:退款完成,14:退货退款完成,15:换货完成,16:待商家发货,17:待用户确认收货,18:商家拒绝换货,19:商家已收到货
	Status int32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	// 0:售后未结束, 1:售后结束且订单状态流转
	FinishAllAftersale   int32    `protobuf:"varint,6,opt,name=finish_all_aftersale,json=finishAllAftersale,proto3" json:"finish_all_aftersale"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AftersaleUpdateRequest) Reset()         { *m = AftersaleUpdateRequest{} }
func (m *AftersaleUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*AftersaleUpdateRequest) ProtoMessage()    {}
func (*AftersaleUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{56}
}

func (m *AftersaleUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AftersaleUpdateRequest.Unmarshal(m, b)
}
func (m *AftersaleUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AftersaleUpdateRequest.Marshal(b, m, deterministic)
}
func (m *AftersaleUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AftersaleUpdateRequest.Merge(m, src)
}
func (m *AftersaleUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_AftersaleUpdateRequest.Size(m)
}
func (m *AftersaleUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AftersaleUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AftersaleUpdateRequest proto.InternalMessageInfo

func (m *AftersaleUpdateRequest) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *AftersaleUpdateRequest) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *AftersaleUpdateRequest) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *AftersaleUpdateRequest) GetOutAftersaleId() string {
	if m != nil {
		return m.OutAftersaleId
	}
	return ""
}

func (m *AftersaleUpdateRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AftersaleUpdateRequest) GetFinishAllAftersale() int32 {
	if m != nil {
		return m.FinishAllAftersale
	}
	return 0
}

// 获取售后详情
type AftersaleGetRequest struct {
	AftersaleId          int64    `protobuf:"varint,1,opt,name=aftersale_id,json=aftersaleId,proto3" json:"aftersale_id"`
	OutAftersaleId       string   `protobuf:"bytes,2,opt,name=out_aftersale_id,json=outAftersaleId,proto3" json:"out_aftersale_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AftersaleGetRequest) Reset()         { *m = AftersaleGetRequest{} }
func (m *AftersaleGetRequest) String() string { return proto.CompactTextString(m) }
func (*AftersaleGetRequest) ProtoMessage()    {}
func (*AftersaleGetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{57}
}

func (m *AftersaleGetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AftersaleGetRequest.Unmarshal(m, b)
}
func (m *AftersaleGetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AftersaleGetRequest.Marshal(b, m, deterministic)
}
func (m *AftersaleGetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AftersaleGetRequest.Merge(m, src)
}
func (m *AftersaleGetRequest) XXX_Size() int {
	return xxx_messageInfo_AftersaleGetRequest.Size(m)
}
func (m *AftersaleGetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AftersaleGetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AftersaleGetRequest proto.InternalMessageInfo

func (m *AftersaleGetRequest) GetAftersaleId() int64 {
	if m != nil {
		return m.AftersaleId
	}
	return 0
}

func (m *AftersaleGetRequest) GetOutAftersaleId() string {
	if m != nil {
		return m.OutAftersaleId
	}
	return ""
}

type AftersaleGetResponse struct {
	Errcode              int32             `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	Errmsg               string            `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	AfterSalesOrder      *AftersaleGetData `protobuf:"bytes,3,opt,name=after_sales_order,json=afterSalesOrder,proto3" json:"after_sales_order"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AftersaleGetResponse) Reset()         { *m = AftersaleGetResponse{} }
func (m *AftersaleGetResponse) String() string { return proto.CompactTextString(m) }
func (*AftersaleGetResponse) ProtoMessage()    {}
func (*AftersaleGetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{58}
}

func (m *AftersaleGetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AftersaleGetResponse.Unmarshal(m, b)
}
func (m *AftersaleGetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AftersaleGetResponse.Marshal(b, m, deterministic)
}
func (m *AftersaleGetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AftersaleGetResponse.Merge(m, src)
}
func (m *AftersaleGetResponse) XXX_Size() int {
	return xxx_messageInfo_AftersaleGetResponse.Size(m)
}
func (m *AftersaleGetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AftersaleGetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AftersaleGetResponse proto.InternalMessageInfo

func (m *AftersaleGetResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *AftersaleGetResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *AftersaleGetResponse) GetAfterSalesOrder() *AftersaleGetData {
	if m != nil {
		return m.AfterSalesOrder
	}
	return nil
}

type AftersaleGetData struct {
	// 外部售后单号
	OutAftersaleId string `protobuf:"bytes,1,opt,name=out_aftersale_id,json=outAftersaleId,proto3" json:"out_aftersale_id"`
	// 微信侧售后单号
	AftersaleId int64 `protobuf:"varint,2,opt,name=aftersale_id,json=aftersaleId,proto3" json:"aftersale_id"`
	// 外部订单号
	OutOrderId string `protobuf:"bytes,3,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id"`
	// 微信侧订单号
	OrderId string `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	// 售后商品信息
	ProductInfo *AftersaleGetDataProductInfo `protobuf:"bytes,5,opt,name=product_info,json=productInfo,proto3" json:"product_info"`
	// 售后类型，1-仅退款，2-退货退款
	Type int32 `protobuf:"varint,6,opt,name=type,proto3" json:"type"`
	// 退货信息
	ReturnInfo *AftersaleGetDataReturnInfo `protobuf:"bytes,7,opt,name=return_info,json=returnInfo,proto3" json:"return_info"`
	// 退款金额，单位分
	Orderamt int64 `protobuf:"varint,8,opt,name=orderamt,proto3" json:"orderamt"`
	// 申请售后的理由类型，1-商品无货，2-发货时间问题，3-不想要了，4-废弃，5-地址信息填写错误，6-买多/买错/不想要了，7-商品损坏/包装脏污，8-少/错商品/与页面描述不符,9-无效的物流单号，10-物流超72小时停滞，11-快递无法送到指定地点，12-显示签收但未收到商品，13-废弃，14-质量问题，15-其他
	RefundReasonType int32 `protobuf:"varint,9,opt,name=refund_reason_type,json=refundReasonType,proto3" json:"refund_reason_type"`
	// 申请售后的理由（补充描述）
	RefundReason string `protobuf:"bytes,10,opt,name=refund_reason,json=refundReason,proto3" json:"refund_reason"`
	//售后状态，1-用户取消售后申请，2-商家处理退款申请中，4-商家拒绝退款，5-商家拒绝退货，6-待用户退货，7-售后单关闭，8-待商家收货，11-平台退款中，13-退款成功，21-平台处理退款申请中，22-废弃，23-商家处理退货申请中，24-平台处理退货申请中，25-平台退款失败
	Status int32 `protobuf:"varint,11,opt,name=status,proto3" json:"status"`
	// 申请时间,单位ms
	CreateTime string `protobuf:"bytes,12,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 更新时间，单位ms
	UpdateTime string `protobuf:"bytes,13,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	// 用户OpenID
	Openid string `protobuf:"bytes,14,opt,name=openid,proto3" json:"openid"`
	// 图片 or 视频附件，结构体，列表
	MediaList            []*AftersaleGetDataMediaList `protobuf:"bytes,15,rep,name=media_list,json=mediaList,proto3" json:"media_list"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AftersaleGetData) Reset()         { *m = AftersaleGetData{} }
func (m *AftersaleGetData) String() string { return proto.CompactTextString(m) }
func (*AftersaleGetData) ProtoMessage()    {}
func (*AftersaleGetData) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{59}
}

func (m *AftersaleGetData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AftersaleGetData.Unmarshal(m, b)
}
func (m *AftersaleGetData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AftersaleGetData.Marshal(b, m, deterministic)
}
func (m *AftersaleGetData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AftersaleGetData.Merge(m, src)
}
func (m *AftersaleGetData) XXX_Size() int {
	return xxx_messageInfo_AftersaleGetData.Size(m)
}
func (m *AftersaleGetData) XXX_DiscardUnknown() {
	xxx_messageInfo_AftersaleGetData.DiscardUnknown(m)
}

var xxx_messageInfo_AftersaleGetData proto.InternalMessageInfo

func (m *AftersaleGetData) GetOutAftersaleId() string {
	if m != nil {
		return m.OutAftersaleId
	}
	return ""
}

func (m *AftersaleGetData) GetAftersaleId() int64 {
	if m != nil {
		return m.AftersaleId
	}
	return 0
}

func (m *AftersaleGetData) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

func (m *AftersaleGetData) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AftersaleGetData) GetProductInfo() *AftersaleGetDataProductInfo {
	if m != nil {
		return m.ProductInfo
	}
	return nil
}

func (m *AftersaleGetData) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AftersaleGetData) GetReturnInfo() *AftersaleGetDataReturnInfo {
	if m != nil {
		return m.ReturnInfo
	}
	return nil
}

func (m *AftersaleGetData) GetOrderamt() int64 {
	if m != nil {
		return m.Orderamt
	}
	return 0
}

func (m *AftersaleGetData) GetRefundReasonType() int32 {
	if m != nil {
		return m.RefundReasonType
	}
	return 0
}

func (m *AftersaleGetData) GetRefundReason() string {
	if m != nil {
		return m.RefundReason
	}
	return ""
}

func (m *AftersaleGetData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AftersaleGetData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *AftersaleGetData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *AftersaleGetData) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *AftersaleGetData) GetMediaList() []*AftersaleGetDataMediaList {
	if m != nil {
		return m.MediaList
	}
	return nil
}

// 获取售后详情-售后商品信息
type AftersaleGetDataProductInfo struct {
	// 外部商品ID
	OutProductId int64 `protobuf:"varint,1,opt,name=out_product_id,json=outProductId,proto3" json:"out_product_id"`
	// 外部sku ID
	OutSkuId string `protobuf:"bytes,2,opt,name=out_sku_id,json=outSkuId,proto3" json:"out_sku_id"`
	// 商品数量
	ProductCnt           int32    `protobuf:"varint,3,opt,name=product_cnt,json=productCnt,proto3" json:"product_cnt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AftersaleGetDataProductInfo) Reset()         { *m = AftersaleGetDataProductInfo{} }
func (m *AftersaleGetDataProductInfo) String() string { return proto.CompactTextString(m) }
func (*AftersaleGetDataProductInfo) ProtoMessage()    {}
func (*AftersaleGetDataProductInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{60}
}

func (m *AftersaleGetDataProductInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AftersaleGetDataProductInfo.Unmarshal(m, b)
}
func (m *AftersaleGetDataProductInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AftersaleGetDataProductInfo.Marshal(b, m, deterministic)
}
func (m *AftersaleGetDataProductInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AftersaleGetDataProductInfo.Merge(m, src)
}
func (m *AftersaleGetDataProductInfo) XXX_Size() int {
	return xxx_messageInfo_AftersaleGetDataProductInfo.Size(m)
}
func (m *AftersaleGetDataProductInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AftersaleGetDataProductInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AftersaleGetDataProductInfo proto.InternalMessageInfo

func (m *AftersaleGetDataProductInfo) GetOutProductId() int64 {
	if m != nil {
		return m.OutProductId
	}
	return 0
}

func (m *AftersaleGetDataProductInfo) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *AftersaleGetDataProductInfo) GetProductCnt() int32 {
	if m != nil {
		return m.ProductCnt
	}
	return 0
}

// 获取售后详情-退货信息
type AftersaleGetDataReturnInfo struct {
	// 退货时间
	OrderReturnTime string `protobuf:"bytes,1,opt,name=order_return_time,json=orderReturnTime,proto3" json:"order_return_time"`
	// 快递公司id
	DeliveryId string `protobuf:"bytes,2,opt,name=delivery_id,json=deliveryId,proto3" json:"delivery_id"`
	// 快递单号
	WaybillId int32 `protobuf:"varint,3,opt,name=waybill_id,json=waybillId,proto3" json:"waybill_id"`
	// 快递公司名字
	DeliveryName         int32    `protobuf:"varint,4,opt,name=delivery_name,json=deliveryName,proto3" json:"delivery_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AftersaleGetDataReturnInfo) Reset()         { *m = AftersaleGetDataReturnInfo{} }
func (m *AftersaleGetDataReturnInfo) String() string { return proto.CompactTextString(m) }
func (*AftersaleGetDataReturnInfo) ProtoMessage()    {}
func (*AftersaleGetDataReturnInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{61}
}

func (m *AftersaleGetDataReturnInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AftersaleGetDataReturnInfo.Unmarshal(m, b)
}
func (m *AftersaleGetDataReturnInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AftersaleGetDataReturnInfo.Marshal(b, m, deterministic)
}
func (m *AftersaleGetDataReturnInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AftersaleGetDataReturnInfo.Merge(m, src)
}
func (m *AftersaleGetDataReturnInfo) XXX_Size() int {
	return xxx_messageInfo_AftersaleGetDataReturnInfo.Size(m)
}
func (m *AftersaleGetDataReturnInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AftersaleGetDataReturnInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AftersaleGetDataReturnInfo proto.InternalMessageInfo

func (m *AftersaleGetDataReturnInfo) GetOrderReturnTime() string {
	if m != nil {
		return m.OrderReturnTime
	}
	return ""
}

func (m *AftersaleGetDataReturnInfo) GetDeliveryId() string {
	if m != nil {
		return m.DeliveryId
	}
	return ""
}

func (m *AftersaleGetDataReturnInfo) GetWaybillId() int32 {
	if m != nil {
		return m.WaybillId
	}
	return 0
}

func (m *AftersaleGetDataReturnInfo) GetDeliveryName() int32 {
	if m != nil {
		return m.DeliveryName
	}
	return 0
}

// 获取售后详情-图片 or 视频附件
type AftersaleGetDataMediaList struct {
	// 固定为1
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	// 图片url
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url"`
	// 缩略图url
	ThumbUrl             string   `protobuf:"bytes,3,opt,name=thumb_url,json=thumbUrl,proto3" json:"thumb_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AftersaleGetDataMediaList) Reset()         { *m = AftersaleGetDataMediaList{} }
func (m *AftersaleGetDataMediaList) String() string { return proto.CompactTextString(m) }
func (*AftersaleGetDataMediaList) ProtoMessage()    {}
func (*AftersaleGetDataMediaList) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{62}
}

func (m *AftersaleGetDataMediaList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AftersaleGetDataMediaList.Unmarshal(m, b)
}
func (m *AftersaleGetDataMediaList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AftersaleGetDataMediaList.Marshal(b, m, deterministic)
}
func (m *AftersaleGetDataMediaList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AftersaleGetDataMediaList.Merge(m, src)
}
func (m *AftersaleGetDataMediaList) XXX_Size() int {
	return xxx_messageInfo_AftersaleGetDataMediaList.Size(m)
}
func (m *AftersaleGetDataMediaList) XXX_DiscardUnknown() {
	xxx_messageInfo_AftersaleGetDataMediaList.DiscardUnknown(m)
}

var xxx_messageInfo_AftersaleGetDataMediaList proto.InternalMessageInfo

func (m *AftersaleGetDataMediaList) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AftersaleGetDataMediaList) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *AftersaleGetDataMediaList) GetThumbUrl() string {
	if m != nil {
		return m.ThumbUrl
	}
	return ""
}

type ShopImgUploadRequest struct {
	// 0:此参数返回media_id，目前只用于品牌申请品牌和类目，推荐使用1：返回临时链接
	RespType int32 `protobuf:"varint,1,opt,name=resp_type,json=respType,proto3" json:"resp_type"`
	// 0:图片流，1:图片url
	UploadType int32 `protobuf:"varint,2,opt,name=upload_type,json=uploadType,proto3" json:"upload_type"`
	// upload_type=1时必传
	ImgUrl               string   `protobuf:"bytes,3,opt,name=img_url,json=imgUrl,proto3" json:"img_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopImgUploadRequest) Reset()         { *m = ShopImgUploadRequest{} }
func (m *ShopImgUploadRequest) String() string { return proto.CompactTextString(m) }
func (*ShopImgUploadRequest) ProtoMessage()    {}
func (*ShopImgUploadRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{63}
}

func (m *ShopImgUploadRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopImgUploadRequest.Unmarshal(m, b)
}
func (m *ShopImgUploadRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopImgUploadRequest.Marshal(b, m, deterministic)
}
func (m *ShopImgUploadRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopImgUploadRequest.Merge(m, src)
}
func (m *ShopImgUploadRequest) XXX_Size() int {
	return xxx_messageInfo_ShopImgUploadRequest.Size(m)
}
func (m *ShopImgUploadRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopImgUploadRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShopImgUploadRequest proto.InternalMessageInfo

func (m *ShopImgUploadRequest) GetRespType() int32 {
	if m != nil {
		return m.RespType
	}
	return 0
}

func (m *ShopImgUploadRequest) GetUploadType() int32 {
	if m != nil {
		return m.UploadType
	}
	return 0
}

func (m *ShopImgUploadRequest) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

type ShopImgInfo struct {
	// media_id
	MediaId string `protobuf:"bytes,1,opt,name=media_id,json=mediaId,proto3" json:"media_id"`
	// 	临时链接
	TempImgUrl           string   `protobuf:"bytes,2,opt,name=temp_img_url,json=tempImgUrl,proto3" json:"temp_img_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopImgInfo) Reset()         { *m = ShopImgInfo{} }
func (m *ShopImgInfo) String() string { return proto.CompactTextString(m) }
func (*ShopImgInfo) ProtoMessage()    {}
func (*ShopImgInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{64}
}

func (m *ShopImgInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopImgInfo.Unmarshal(m, b)
}
func (m *ShopImgInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopImgInfo.Marshal(b, m, deterministic)
}
func (m *ShopImgInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopImgInfo.Merge(m, src)
}
func (m *ShopImgInfo) XXX_Size() int {
	return xxx_messageInfo_ShopImgInfo.Size(m)
}
func (m *ShopImgInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopImgInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShopImgInfo proto.InternalMessageInfo

func (m *ShopImgInfo) GetMediaId() string {
	if m != nil {
		return m.MediaId
	}
	return ""
}

func (m *ShopImgInfo) GetTempImgUrl() string {
	if m != nil {
		return m.TempImgUrl
	}
	return ""
}

type ShopImgUploadResponse struct {
	// 错误码
	Errcode int32 `protobuf:"varint,1,opt,name=errcode,proto3" json:"errcode"`
	// 	错误信息
	Errmsg               string       `protobuf:"bytes,2,opt,name=errmsg,proto3" json:"errmsg"`
	ImgInfo              *ShopImgInfo `protobuf:"bytes,3,opt,name=img_info,json=imgInfo,proto3" json:"img_info"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ShopImgUploadResponse) Reset()         { *m = ShopImgUploadResponse{} }
func (m *ShopImgUploadResponse) String() string { return proto.CompactTextString(m) }
func (*ShopImgUploadResponse) ProtoMessage()    {}
func (*ShopImgUploadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1be7b4e07e55b12e, []int{65}
}

func (m *ShopImgUploadResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopImgUploadResponse.Unmarshal(m, b)
}
func (m *ShopImgUploadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopImgUploadResponse.Marshal(b, m, deterministic)
}
func (m *ShopImgUploadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopImgUploadResponse.Merge(m, src)
}
func (m *ShopImgUploadResponse) XXX_Size() int {
	return xxx_messageInfo_ShopImgUploadResponse.Size(m)
}
func (m *ShopImgUploadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopImgUploadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ShopImgUploadResponse proto.InternalMessageInfo

func (m *ShopImgUploadResponse) GetErrcode() int32 {
	if m != nil {
		return m.Errcode
	}
	return 0
}

func (m *ShopImgUploadResponse) GetErrmsg() string {
	if m != nil {
		return m.Errmsg
	}
	return ""
}

func (m *ShopImgUploadResponse) GetImgInfo() *ShopImgInfo {
	if m != nil {
		return m.ImgInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*Empty)(nil), "et.Empty")
	proto.RegisterType((*WxBaseResponse)(nil), "et.WxBaseResponse")
	proto.RegisterType((*ProductBaseRequest)(nil), "et.ProductBaseRequest")
	proto.RegisterType((*WxCategoryListResponse)(nil), "et.WxCategoryListResponse")
	proto.RegisterType((*WxCategoryList)(nil), "et.WxCategoryList")
	proto.RegisterType((*UploadPicRequest)(nil), "et.UploadPicRequest")
	proto.RegisterType((*UploadPicResponse)(nil), "et.UploadPicResponse")
	proto.RegisterType((*UploadPicData)(nil), "et.UploadPicData")
	proto.RegisterType((*UploadBrandInfoRequest)(nil), "et.UploadBrandInfoRequest")
	proto.RegisterType((*LicenseAndBrandInfo)(nil), "et.LicenseAndBrandInfo")
	proto.RegisterType((*BrandInfo)(nil), "et.BrandInfo")
	proto.RegisterType((*UploadBrandInfoResponse)(nil), "et.UploadBrandInfoResponse")
	proto.RegisterType((*UploadProductInfoRequest)(nil), "et.UploadProductInfoRequest")
	proto.RegisterType((*DescInfo)(nil), "et.DescInfo")
	proto.RegisterType((*SkuInfo)(nil), "et.SkuInfo")
	proto.RegisterType((*SkuAttrs)(nil), "et.SkuAttrs")
	proto.RegisterType((*AddProductInfoResponse)(nil), "et.AddProductInfoResponse")
	proto.RegisterType((*AddProductInfoResponseData)(nil), "et.AddProductInfoResponseData")
	proto.RegisterType((*ProductInfoResponseSkus)(nil), "et.ProductInfoResponseSkus")
	proto.RegisterType((*ProductInfoListRequest)(nil), "et.ProductInfoListRequest")
	proto.RegisterType((*ProductInfoListResponse)(nil), "et.ProductInfoListResponse")
	proto.RegisterType((*ProductInfo)(nil), "et.ProductInfo")
	proto.RegisterType((*AuditInfo)(nil), "et.AuditInfo")
	proto.RegisterType((*UpdateProductInfoResponse)(nil), "et.UpdateProductInfoResponse")
	proto.RegisterType((*UpdateProductInfoResponseData)(nil), "et.UpdateProductInfoResponseData")
	proto.RegisterType((*FreeUpdateProductInfoRequest)(nil), "et.FreeUpdateProductInfoRequest")
	proto.RegisterType((*FreeUpdateProductInfoSku)(nil), "et.FreeUpdateProductInfoSku")
	proto.RegisterType((*OrderAddRequest)(nil), "et.OrderAddRequest")
	proto.RegisterType((*OrderDetail)(nil), "et.OrderDetail")
	proto.RegisterType((*ProductInfos)(nil), "et.ProductInfos")
	proto.RegisterType((*PayInfo)(nil), "et.PayInfo")
	proto.RegisterType((*PriceInfo)(nil), "et.PriceInfo")
	proto.RegisterType((*DeliveryDetail)(nil), "et.DeliveryDetail")
	proto.RegisterType((*AddressInfo)(nil), "et.AddressInfo")
	proto.RegisterType((*OrderAddResponse)(nil), "et.OrderAddResponse")
	proto.RegisterType((*OrderAddDataResponse)(nil), "et.OrderAddDataResponse")
	proto.RegisterType((*OrderGetPaymentParamsReq)(nil), "et.OrderGetPaymentParamsReq")
	proto.RegisterType((*OrderGetPaymentParamsRes)(nil), "et.OrderGetPaymentParamsRes")
	proto.RegisterType((*OrderGetPaymentParamsRes_Data)(nil), "et.OrderGetPaymentParamsRes.Data")
	proto.RegisterType((*OrderPayStatusRequest)(nil), "et.OrderPayStatusRequest")
	proto.RegisterType((*OrderListRequest)(nil), "et.OrderListRequest")
	proto.RegisterType((*OrderListResponse)(nil), "et.OrderListResponse")
	proto.RegisterType((*OrderListDataResponse)(nil), "et.OrderListDataResponse")
	proto.RegisterType((*OrderListDetail)(nil), "et.OrderListDetail")
	proto.RegisterType((*OrderListPromotionInfo)(nil), "et.OrderListPromotionInfo")
	proto.RegisterType((*OrderListProductInfos)(nil), "et.OrderListProductInfos")
	proto.RegisterType((*OrderListPayInfo)(nil), "et.OrderListPayInfo")
	proto.RegisterType((*OrderListPriceInfo)(nil), "et.OrderListPriceInfo")
	proto.RegisterType((*OrderListDeliveryDetail)(nil), "et.OrderListDeliveryDetail")
	proto.RegisterType((*OrderListDeliveryDetailList)(nil), "et.OrderListDeliveryDetailList")
	proto.RegisterType((*DeliveryCompanyListResponse)(nil), "et.DeliveryCompanyListResponse")
	proto.RegisterType((*DeliveryCompanyListData)(nil), "et.DeliveryCompanyListData")
	proto.RegisterType((*DeliverySendRequest)(nil), "et.DeliverySendRequest")
	proto.RegisterType((*DeliveryListData)(nil), "et.DeliveryListData")
	proto.RegisterType((*DeliveryRecieveRequest)(nil), "et.DeliveryRecieveRequest")
	proto.RegisterType((*AftersaleAddRequest)(nil), "et.AftersaleAddRequest")
	proto.RegisterType((*AfterSaleProductInfos)(nil), "et.AfterSaleProductInfos")
	proto.RegisterType((*AftersaleUpdateRequest)(nil), "et.AftersaleUpdateRequest")
	proto.RegisterType((*AftersaleGetRequest)(nil), "et.AftersaleGetRequest")
	proto.RegisterType((*AftersaleGetResponse)(nil), "et.AftersaleGetResponse")
	proto.RegisterType((*AftersaleGetData)(nil), "et.AftersaleGetData")
	proto.RegisterType((*AftersaleGetDataProductInfo)(nil), "et.AftersaleGetDataProductInfo")
	proto.RegisterType((*AftersaleGetDataReturnInfo)(nil), "et.AftersaleGetDataReturnInfo")
	proto.RegisterType((*AftersaleGetDataMediaList)(nil), "et.AftersaleGetDataMediaList")
	proto.RegisterType((*ShopImgUploadRequest)(nil), "et.ShopImgUploadRequest")
	proto.RegisterType((*ShopImgInfo)(nil), "et.ShopImgInfo")
	proto.RegisterType((*ShopImgUploadResponse)(nil), "et.ShopImgUploadResponse")
}

func init() { proto.RegisterFile("et/externalWxVideoNum.proto", fileDescriptor_1be7b4e07e55b12e) }

var fileDescriptor_1be7b4e07e55b12e = []byte{
	// 3816 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x5b, 0x4b, 0x73, 0x24, 0xc7,
	0x71, 0x8e, 0x1e, 0xcc, 0x33, 0xe7, 0x05, 0x34, 0x5e, 0x83, 0xc1, 0x2e, 0x01, 0x36, 0x69, 0x7a,
	0xc9, 0xa0, 0x76, 0x25, 0xc8, 0x2b, 0xdb, 0x92, 0x2c, 0x0a, 0x5c, 0x48, 0x34, 0x28, 0x72, 0x09,
	0x0f, 0xbc, 0x5c, 0x5f, 0xe4, 0x89, 0x46, 0x77, 0x61, 0xd0, 0xc2, 0xf4, 0x63, 0xbb, 0xab, 0xb1,
	0x3b, 0x0c, 0x87, 0x0f, 0xb6, 0x4e, 0x8a, 0x70, 0xd8, 0x67, 0xfd, 0x03, 0x1d, 0x74, 0xb2, 0xc2,
	0xe1, 0xb3, 0x0f, 0xbe, 0xd8, 0xbe, 0xc9, 0x07, 0xfb, 0x64, 0x3b, 0xfc, 0x13, 0x7c, 0x75, 0x84,
	0xa3, 0x32, 0xab, 0xba, 0xab, 0x7b, 0x66, 0x80, 0x25, 0x96, 0x54, 0x98, 0xa7, 0xe9, 0xca, 0xca,
	0xca, 0xca, 0xca, 0xca, 0xcc, 0xfa, 0xea, 0x31, 0xb0, 0xcb, 0xf8, 0x03, 0xf6, 0x82, 0xb3, 0x38,
	0xb0, 0xa7, 0x4f, 0x5f, 0x7c, 0xea, 0xb9, 0x2c, 0x7c, 0x9c, 0xfa, 0xf7, 0xa3, 0x38, 0xe4, 0xa1,
	0x59, 0x61, 0x7c, 0xb8, 0x3b, 0x09, 0xc3, 0xc9, 0x94, 0x3d, 0x40, 0xca, 0x59, 0x7a, 0xfe, 0x80,
	0xf9, 0x11, 0x9f, 0x11, 0x83, 0xd5, 0x80, 0xda, 0x0f, 0x44, 0xd1, 0x7a, 0x1f, 0x7a, 0x4f, 0x5f,
	0xbc, 0x6f, 0x27, 0x6c, 0xc4, 0x92, 0x28, 0x0c, 0x12, 0x66, 0x0e, 0xa0, 0xc1, 0xe2, 0xd8, 0x09,
	0x5d, 0x36, 0x30, 0xf6, 0x8d, 0x7b, 0xb5, 0x91, 0x2a, 0x9a, 0x5b, 0x50, 0x67, 0x71, 0xec, 0x27,
	0x93, 0x41, 0x65, 0xdf, 0xb8, 0xd7, 0x1a, 0xc9, 0x92, 0xf5, 0x6d, 0x30, 0x4f, 0xe2, 0xd0, 0x4d,
	0x1d, 0x4e, 0x82, 0x9e, 0xa5, 0x2c, 0xe1, 0xe6, 0x9b, 0xd0, 0x0b, 0x53, 0x3e, 0x8e, 0xa8, 0x66,
	0xec, 0xb9, 0x28, 0xae, 0x35, 0xea, 0x84, 0x29, 0x97, 0xec, 0xc7, 0xae, 0xf5, 0x53, 0x03, 0xb6,
	0x9e, 0xbe, 0x78, 0x64, 0x73, 0x36, 0x09, 0xe3, 0xd9, 0x47, 0x5e, 0xc2, 0x6f, 0xaf, 0x88, 0xf9,
	0x7b, 0xd0, 0xe3, 0x17, 0x5e, 0xec, 0x8e, 0x1d, 0x9b, 0x8f, 0xa7, 0x5e, 0xc2, 0x07, 0x2b, 0xfb,
	0x2b, 0xf7, 0xda, 0x07, 0xe6, 0x7d, 0xc6, 0xef, 0x97, 0x7a, 0xe9, 0x20, 0xe7, 0x23, 0x9b, 0x8b,
	0x92, 0xf5, 0xab, 0x15, 0x61, 0x07, 0x9d, 0xc1, 0xdc, 0x87, 0x4e, 0x2e, 0x4c, 0x6a, 0x5f, 0x1b,
	0x81, 0x6a, 0x76, 0xec, 0x8a, 0x11, 0xe6, 0x1c, 0x81, 0xed, 0x33, 0xa9, 0x4e, 0x26, 0xfa, 0xb1,
	0xed, 0x33, 0xf3, 0x4d, 0xe8, 0x3e, 0x4b, 0xed, 0xa9, 0x77, 0xee, 0x39, 0x36, 0xf7, 0xc2, 0x60,
	0xb0, 0x82, 0x4c, 0x45, 0xa2, 0xf9, 0x35, 0x30, 0x0b, 0x84, 0x31, 0x9f, 0x45, 0x6c, 0x50, 0xc5,
	0x3e, 0xd7, 0x0a, 0x35, 0x7f, 0x3c, 0x8b, 0x98, 0xf9, 0x4d, 0xd8, 0x54, 0x86, 0x2d, 0x0a, 0xaf,
	0xa1, 0xf0, 0x0d, 0x59, 0xf9, 0x47, 0x85, 0x3e, 0xbe, 0x0b, 0xc3, 0x85, 0x8d, 0xa8, 0xaf, 0x3a,
	0xf6, 0x35, 0x58, 0xd4, 0x12, 0xbb, 0xb4, 0xa0, 0x9b, 0x30, 0x27, 0x0c, 0x32, 0x83, 0x34, 0xb0,
	0x41, 0x9b, 0x88, 0x64, 0x91, 0xb7, 0xa0, 0xaf, 0xf1, 0xa0, 0x49, 0x9a, 0x34, 0xda, 0x8c, 0x0b,
	0x6d, 0xb2, 0x0f, 0x9d, 0x73, 0x2f, 0x4e, 0xb8, 0x12, 0xd5, 0x22, 0xdb, 0x22, 0x2d, 0xb3, 0x6d,
	0xce, 0x81, 0x82, 0x80, 0x6c, 0xab, 0x78, 0x84, 0x1c, 0xeb, 0x10, 0x56, 0x9f, 0x44, 0xd3, 0xd0,
	0x76, 0x4f, 0x3c, 0x47, 0xf9, 0xdd, 0x2a, 0xac, 0xa4, 0xf1, 0x54, 0x3a, 0x9b, 0xf8, 0x34, 0x77,
	0xa1, 0x15, 0xb3, 0x24, 0xa2, 0x61, 0x56, 0xb0, 0xab, 0xa6, 0x20, 0x88, 0x61, 0x59, 0x09, 0xac,
	0x69, 0x22, 0x6e, 0xed, 0x7a, 0xef, 0x42, 0xd3, 0xf3, 0x27, 0x63, 0x2f, 0x38, 0x0f, 0x71, 0x82,
	0xdb, 0x07, 0x6b, 0xc2, 0xe9, 0x32, 0xd1, 0x47, 0x36, 0xb7, 0x47, 0x0d, 0xcf, 0x9f, 0x1c, 0x07,
	0xe7, 0xa1, 0xf5, 0x11, 0x74, 0x0b, 0x35, 0xe8, 0x6c, 0xcc, 0x8f, 0xc6, 0x42, 0x46, 0xae, 0x3d,
	0x08, 0xda, 0xb1, 0x3f, 0x79, 0x12, 0x4f, 0xcd, 0x1d, 0x68, 0xfa, 0xcc, 0xf5, 0x6c, 0x61, 0x2e,
	0xea, 0xba, 0x81, 0xe5, 0x63, 0xd7, 0x7a, 0x0c, 0x5b, 0x24, 0xed, 0xfd, 0xd8, 0x0e, 0x5c, 0xd1,
	0x81, 0xb2, 0xc5, 0xef, 0x40, 0xcb, 0x4e, 0x5d, 0x8f, 0x8f, 0x63, 0xf6, 0x0c, 0x65, 0xb6, 0x0f,
	0xb6, 0x85, 0x5a, 0x1f, 0x79, 0x0e, 0x0b, 0x12, 0x76, 0x18, 0x68, 0x4d, 0x9a, 0xc8, 0x39, 0x62,
	0xcf, 0xac, 0x1f, 0xc3, 0xfa, 0x02, 0x06, 0x61, 0x94, 0x29, 0x91, 0x07, 0xc6, 0xfe, 0x8a, 0x50,
	0x40, 0x16, 0xcd, 0x77, 0x01, 0xce, 0x04, 0x1b, 0x0d, 0xbf, 0x82, 0xfd, 0x74, 0x45, 0x3f, 0xb9,
	0xf4, 0xd6, 0x99, 0xfa, 0xb4, 0xfe, 0xb1, 0x0e, 0xad, 0x5c, 0xea, 0x3d, 0x58, 0xa5, 0xb6, 0xa4,
	0x28, 0xce, 0x11, 0xd9, 0xbc, 0x87, 0xf4, 0x43, 0x41, 0x46, 0x07, 0xfc, 0x2d, 0xe8, 0xf1, 0xd8,
	0x76, 0x99, 0x6f, 0xc7, 0x97, 0xfa, 0x5c, 0x76, 0x33, 0x2a, 0xb2, 0x1d, 0xc0, 0x26, 0x09, 0xf4,
	0xed, 0xc0, 0x9e, 0x30, 0x9f, 0x05, 0x52, 0xea, 0x0a, 0x72, 0xaf, 0x63, 0xe5, 0xc7, 0x59, 0x9d,
	0x6a, 0xe3, 0x84, 0xbe, 0x1f, 0xba, 0x1e, 0x9f, 0x8d, 0xc3, 0xd8, 0x9b, 0x78, 0x85, 0x00, 0x5c,
	0xcf, 0x2a, 0x3f, 0xc1, 0x3a, 0x6c, 0xf3, 0x06, 0x74, 0xa9, 0x9f, 0xe7, 0x61, 0xec, 0x7a, 0xc1,
	0x44, 0x86, 0x5e, 0x07, 0x89, 0x4f, 0x89, 0x26, 0xc2, 0x3a, 0xb1, 0xa7, 0x6c, 0x6c, 0xa7, 0xfc,
	0x22, 0x8c, 0xbd, 0xcf, 0x28, 0x48, 0xeb, 0x68, 0xbe, 0x35, 0x51, 0x73, 0xa8, 0x57, 0x98, 0x1f,
	0x81, 0x95, 0x0f, 0x31, 0x66, 0x13, 0x2f, 0xe1, 0x31, 0x85, 0xa8, 0xc3, 0x62, 0x4e, 0xf1, 0xc8,
	0x06, 0x0d, 0x6c, 0xbe, 0x9f, 0x71, 0x8e, 0x34, 0xc6, 0x47, 0x39, 0x9f, 0xf9, 0x7d, 0xb8, 0x93,
	0x4b, 0x73, 0x2e, 0xec, 0x60, 0xc2, 0x0a, 0x72, 0x9a, 0x28, 0x67, 0x98, 0xf1, 0x3c, 0x42, 0x16,
	0x5d, 0xc2, 0x37, 0x60, 0x63, 0x5e, 0x9f, 0x80, 0x63, 0xbc, 0xb6, 0x46, 0xeb, 0x73, 0x1a, 0x04,
	0xdc, 0xfc, 0x16, 0x6c, 0x2f, 0x6a, 0x32, 0x0e, 0x52, 0x19, 0xc1, 0x9b, 0x0b, 0x5a, 0x3d, 0x4e,
	0xcd, 0x23, 0x78, 0x2d, 0x6f, 0x57, 0x30, 0xd7, 0x38, 0x62, 0xb1, 0x17, 0xba, 0x83, 0x36, 0x36,
	0xcf, 0x87, 0x54, 0x30, 0xdd, 0x09, 0xf2, 0x5c, 0x63, 0x40, 0x3b, 0x8a, 0xa6, 0x2a, 0x49, 0x76,
	0xae, 0x31, 0xe0, 0x61, 0xce, 0x67, 0x3e, 0x80, 0x75, 0x4d, 0x27, 0xaa, 0x08, 0xf8, 0xa0, 0x8b,
	0x8a, 0x98, 0xb9, 0x22, 0xaa, 0x46, 0x64, 0xd8, 0xb9, 0x06, 0x94, 0x61, 0x3d, 0x9f, 0x0d, 0x7a,
	0xd8, 0x6e, 0x50, 0x6e, 0x87, 0x19, 0xd6, 0xf3, 0x99, 0x79, 0x1f, 0xd6, 0x3d, 0x3f, 0x0a, 0x63,
	0xce, 0xdc, 0xf1, 0x24, 0x0c, 0xdd, 0x64, 0x7c, 0x1e, 0xc6, 0xfe, 0xa0, 0x4f, 0xde, 0xa2, 0xaa,
	0x3e, 0x10, 0x35, 0x3f, 0x0c, 0x63, 0xdf, 0x3a, 0x87, 0xed, 0xb9, 0xb8, 0xbf, 0x75, 0x02, 0xdb,
	0x01, 0x4a, 0x00, 0x22, 0xbf, 0xd0, 0x0a, 0xd5, 0xc0, 0xf2, 0xb1, 0x6b, 0xfd, 0x47, 0x05, 0x06,
	0x32, 0x5d, 0xc9, 0x75, 0x5b, 0x4b, 0x31, 0x2f, 0xb5, 0xcc, 0x9b, 0x1b, 0x50, 0xe3, 0x1e, 0x9f,
	0xaa, 0x15, 0x92, 0x0a, 0xa6, 0x09, 0xd5, 0xc8, 0xe6, 0x17, 0xb2, 0x3f, 0xfc, 0x16, 0x7a, 0x5c,
	0x30, 0xdb, 0x15, 0x99, 0x70, 0x50, 0xa5, 0x34, 0x23, 0xca, 0xc7, 0xfe, 0x64, 0x7e, 0x8d, 0x8c,
	0x3c, 0x27, 0x19, 0xd4, 0xc8, 0x3c, 0x85, 0x9a, 0x13, 0xcf, 0x49, 0xcc, 0xb7, 0xa1, 0xe5, 0xb2,
	0xc4, 0xa1, 0xa4, 0x54, 0xc7, 0xa4, 0xd4, 0x11, 0x49, 0xe9, 0x88, 0x25, 0x0e, 0x65, 0x3c, 0x57,
	0x7e, 0xcd, 0xad, 0xf5, 0x8d, 0xb9, 0xb5, 0x7e, 0x07, 0x9a, 0x32, 0xc5, 0xb9, 0xb8, 0xa4, 0xad,
	0x8c, 0x1a, 0x94, 0xd1, 0x5c, 0xf3, 0x75, 0xe8, 0x88, 0x2e, 0xc6, 0x57, 0x2c, 0x4e, 0x84, 0x77,
	0x51, 0x70, 0xb4, 0x05, 0xed, 0x53, 0x22, 0x99, 0x7b, 0x50, 0x4d, 0x2e, 0xd3, 0x64, 0x00, 0x08,
	0x47, 0xda, 0x42, 0x8b, 0xd3, 0xcb, 0x14, 0x95, 0xc0, 0x0a, 0xeb, 0x00, 0x9a, 0x4a, 0x2d, 0x61,
	0x15, 0xa1, 0x98, 0xb4, 0x23, 0x7e, 0x0b, 0x9a, 0xe7, 0x4f, 0x92, 0x41, 0x05, 0x07, 0x8b, 0xdf,
	0xd6, 0x2f, 0x2a, 0xd0, 0x90, 0x52, 0x5e, 0x72, 0x16, 0xee, 0x00, 0x08, 0xae, 0xe4, 0x32, 0xcd,
	0x57, 0x91, 0x66, 0x98, 0x72, 0x21, 0xc5, 0x15, 0xcb, 0x24, 0xbf, 0x48, 0xfd, 0x33, 0x34, 0x3d,
	0x4d, 0x49, 0x13, 0x09, 0xc2, 0xf6, 0x77, 0x01, 0x30, 0x91, 0x45, 0xb1, 0xe7, 0xa8, 0xb4, 0xd8,
	0x12, 0x94, 0x13, 0x41, 0x10, 0x36, 0x10, 0x1e, 0xcd, 0xb8, 0x64, 0xa8, 0x11, 0x36, 0x20, 0x1a,
	0xb1, 0xec, 0x42, 0x2b, 0xe1, 0xa1, 0x73, 0x39, 0x0e, 0x52, 0x5f, 0x82, 0x8d, 0x26, 0x12, 0x1e,
	0xa7, 0xbe, 0xf0, 0xd7, 0x33, 0x9b, 0xfc, 0xb5, 0x41, 0xce, 0x27, 0x8b, 0xc2, 0xf0, 0x42, 0x5f,
	0xac, 0x22, 0x2c, 0xd1, 0x48, 0x2e, 0xd3, 0x47, 0xa2, 0xea, 0x6d, 0x68, 0x89, 0x2a, 0x9b, 0xf3,
	0x38, 0x19, 0xb4, 0xd0, 0xb4, 0x1d, 0x69, 0xda, 0x43, 0x41, 0x1b, 0x89, 0x96, 0xf8, 0x65, 0x1d,
	0x41, 0x53, 0x51, 0xd1, 0xd3, 0x39, 0x8f, 0xc7, 0x97, 0x6c, 0x26, 0xad, 0xd4, 0x10, 0xe5, 0x1f,
	0xb1, 0x99, 0x18, 0x25, 0x56, 0x5d, 0xd9, 0xd3, 0x54, 0xf9, 0x6a, 0x4b, 0x50, 0x3e, 0x15, 0x04,
	0xeb, 0xcf, 0x61, 0xeb, 0xd0, 0x2d, 0x06, 0xc1, 0xad, 0xe3, 0xed, 0x00, 0xaa, 0xae, 0xcd, 0x6d,
	0x09, 0x16, 0x5e, 0x13, 0x7a, 0x2f, 0x96, 0x8d, 0xc8, 0x01, 0x79, 0xad, 0xbf, 0x35, 0x60, 0xb8,
	0x9c, 0x49, 0x68, 0x5f, 0x72, 0x80, 0x95, 0x51, 0x2b, 0xca, 0x66, 0x7f, 0xde, 0x47, 0x2a, 0x0b,
	0x7c, 0x64, 0x0f, 0xda, 0x4e, 0xcc, 0x6c, 0xce, 0x28, 0x67, 0x91, 0x1f, 0x00, 0x91, 0x30, 0x4b,
	0x3d, 0x90, 0xbe, 0x5c, 0x45, 0x83, 0xef, 0x0a, 0xc5, 0x17, 0x28, 0x74, 0x7a, 0x99, 0x26, 0xd2,
	0xb7, 0x1f, 0xc3, 0xf6, 0x12, 0x06, 0x73, 0x13, 0xea, 0xd2, 0x19, 0x49, 0xdb, 0x5a, 0x82, 0x9e,
	0x78, 0xad, 0x9f, 0x5a, 0xbf, 0xac, 0xc0, 0x96, 0x26, 0x90, 0xf6, 0x0c, 0x94, 0x8c, 0xb6, 0xa0,
	0x9e, 0x70, 0x9b, 0xa7, 0x89, 0x9c, 0x05, 0x59, 0x32, 0xdf, 0x81, 0xb5, 0x84, 0xdb, 0x31, 0x1f,
	0xeb, 0x43, 0x23, 0xb9, 0x7d, 0xac, 0x78, 0x94, 0x8f, 0xef, 0x2d, 0xe8, 0x33, 0x01, 0x60, 0xe7,
	0x8c, 0xd0, 0x65, 0x81, 0xab, 0xf1, 0x65, 0x32, 0xd3, 0xc8, 0xcd, 0x38, 0xab, 0x9a, 0xcc, 0x27,
	0x48, 0xd7, 0x65, 0xea, 0x9c, 0xb5, 0x4c, 0xa6, 0xc6, 0x87, 0x09, 0x71, 0xa2, 0xb0, 0x38, 0x7e,
	0x8b, 0xb8, 0x11, 0xbf, 0xe3, 0xc4, 0xfb, 0x8c, 0xc9, 0xc4, 0xd4, 0x14, 0x84, 0x53, 0xef, 0x33,
	0x04, 0xe5, 0x01, 0x63, 0xee, 0x98, 0x89, 0xcc, 0x9d, 0x44, 0x29, 0x86, 0x48, 0x6d, 0xd4, 0x16,
	0xc4, 0x1f, 0xb8, 0x1e, 0x3f, 0x8d, 0x52, 0xeb, 0xaf, 0x8c, 0xc2, 0x04, 0xbc, 0xe2, 0x1e, 0x4b,
	0x64, 0x89, 0x90, 0xdb, 0x53, 0x0c, 0x63, 0x82, 0x54, 0x4d, 0x24, 0x88, 0x30, 0x7e, 0x03, 0xaa,
	0x49, 0x94, 0xf9, 0x46, 0xbf, 0xec, 0x1b, 0x58, 0x69, 0xfd, 0xcb, 0x0a, 0xb4, 0x35, 0xea, 0x6f,
	0x72, 0x05, 0x79, 0x17, 0x40, 0x2e, 0x72, 0x62, 0x4d, 0xa8, 0xe5, 0x40, 0x15, 0x51, 0x26, 0x01,
	0x55, 0x5b, 0x7d, 0x7e, 0x95, 0x16, 0x10, 0xcd, 0xf3, 0xdb, 0x05, 0xcf, 0xdf, 0x83, 0x36, 0xf9,
	0x06, 0x55, 0x76, 0x48, 0x2f, 0x41, 0x3a, 0xcd, 0x18, 0x74, 0x57, 0xef, 0xce, 0xc5, 0xfb, 0x1e,
	0xb4, 0x75, 0xbf, 0x25, 0x10, 0x03, 0x69, 0xe6, 0xb4, 0xd6, 0x27, 0xd0, 0xca, 0xcc, 0x87, 0x19,
	0x94, 0x80, 0xbc, 0x60, 0x36, 0x64, 0x06, 0x45, 0x0c, 0x2f, 0x84, 0xbd, 0x01, 0xdd, 0x98, 0xfd,
	0x84, 0x39, 0x62, 0x47, 0x62, 0x27, 0x61, 0xa0, 0x52, 0x10, 0x11, 0x47, 0x48, 0xb3, 0x7e, 0x6a,
	0xc0, 0x0e, 0x05, 0xc5, 0x17, 0x93, 0x6a, 0x1f, 0x16, 0x52, 0xed, 0xeb, 0xb4, 0x2f, 0x5b, 0x22,
	0x5e, 0xcb, 0xb6, 0x7f, 0x67, 0xc0, 0xdd, 0x6b, 0xf9, 0xbe, 0xb0, 0x84, 0xab, 0xdb, 0x77, 0xa5,
	0x6c, 0xdf, 0xcf, 0x9f, 0x70, 0x7f, 0x66, 0xc0, 0x9d, 0x1f, 0xc6, 0x8c, 0x2d, 0x50, 0xfe, 0xf3,
	0x60, 0x36, 0x15, 0x5b, 0x15, 0x2d, 0xb6, 0xbe, 0x2e, 0x75, 0xa1, 0x73, 0x95, 0x3b, 0x42, 0x97,
	0x85, 0x3d, 0x9d, 0x5e, 0xa6, 0x52, 0x99, 0x7f, 0x36, 0x60, 0xb0, 0x8c, 0xa5, 0x94, 0xe8, 0x8d,
	0x12, 0x20, 0x29, 0x62, 0x8e, 0xca, 0x4d, 0x98, 0x63, 0xe5, 0x06, 0xcc, 0x51, 0x5d, 0x8e, 0x39,
	0x6a, 0xcb, 0x31, 0x47, 0xbd, 0x80, 0x39, 0xac, 0x9f, 0x57, 0xa1, 0xff, 0x49, 0xec, 0xb2, 0xf8,
	0xd0, 0x75, 0x95, 0x39, 0x4b, 0x21, 0x64, 0xcc, 0x85, 0xd0, 0x3e, 0x08, 0xcb, 0x8e, 0x43, 0xd1,
	0x2e, 0x77, 0x03, 0x31, 0x74, 0x14, 0x75, 0xec, 0x0a, 0xd7, 0x0d, 0x23, 0x16, 0x64, 0xd8, 0x5b,
	0x96, 0xb2, 0x39, 0xa8, 0x6a, 0x73, 0xf0, 0x1a, 0xb4, 0x85, 0xb4, 0x34, 0x21, 0x61, 0xa4, 0x60,
	0x2b, 0x4c, 0xf9, 0x93, 0x04, 0x65, 0xed, 0x42, 0xeb, 0x3c, 0x0d, 0x5c, 0xda, 0xc0, 0xca, 0x05,
	0x43, 0x10, 0x70, 0xd7, 0x2a, 0xf2, 0xc1, 0x8b, 0xc8, 0x8b, 0xa5, 0xae, 0x4d, 0x99, 0x0f, 0x90,
	0x84, 0xba, 0x7e, 0x0d, 0x4c, 0xfb, 0x9c, 0xb3, 0x18, 0x2d, 0xef, 0xa6, 0xb4, 0x29, 0x92, 0x07,
	0x34, 0x6b, 0x59, 0xcd, 0x91, 0xac, 0x10, 0xa6, 0xe2, 0xb1, 0xed, 0x30, 0xa1, 0x09, 0xed, 0xef,
	0x1a, 0x58, 0x3e, 0x76, 0xcd, 0x03, 0xe8, 0xd0, 0x88, 0x5d, 0xc6, 0x6d, 0x6f, 0x8a, 0x89, 0x49,
	0x2e, 0x0a, 0x38, 0xec, 0x23, 0x24, 0x8f, 0xda, 0x61, 0x5e, 0x30, 0xbf, 0x03, 0x7d, 0x97, 0x4d,
	0xbd, 0x2b, 0x16, 0xcf, 0x54, 0xb3, 0x0e, 0x36, 0x33, 0x29, 0xf1, 0x52, 0x95, 0x6c, 0xd9, 0x73,
	0x0b, 0x65, 0xd1, 0xa1, 0xed, 0xba, 0x31, 0x4b, 0x12, 0x4a, 0xd9, 0xdd, 0xbc, 0xc3, 0x43, 0xa2,
	0x63, 0x10, 0xb4, 0xed, 0xbc, 0x60, 0xfe, 0x08, 0x76, 0x5c, 0x76, 0x6e, 0xa7, 0x53, 0x91, 0x91,
	0x1c, 0xe6, 0x5d, 0x79, 0xc1, 0x64, 0x2c, 0x19, 0x30, 0xd7, 0x2d, 0x10, 0xb0, 0x2d, 0x5b, 0x8c,
	0x54, 0x03, 0x59, 0x69, 0xfd, 0xdc, 0x80, 0xb6, 0x36, 0x34, 0xf3, 0x21, 0x74, 0xb3, 0x18, 0x0b,
	0xce, 0xc3, 0x04, 0xcf, 0x4d, 0xda, 0x07, 0xab, 0xa5, 0x10, 0x4e, 0x46, 0x9d, 0x48, 0x2b, 0x99,
	0x6f, 0x41, 0x33, 0xb2, 0x67, 0xfa, 0x61, 0x0a, 0x26, 0xfc, 0x13, 0x7b, 0x86, 0xdd, 0x37, 0x22,
	0xfa, 0x10, 0xab, 0x19, 0x7a, 0xbe, 0x7e, 0xea, 0xd4, 0x25, 0xd9, 0x9e, 0xc3, 0x68, 0x35, 0x8b,
	0xd4, 0xa7, 0xf5, 0xbf, 0x06, 0x74, 0xf4, 0x4e, 0xbf, 0x90, 0x3d, 0xc3, 0x1e, 0xb4, 0x55, 0x7b,
	0x27, 0xe0, 0x32, 0x04, 0x55, 0x52, 0x7c, 0x14, 0xf0, 0x9b, 0xf6, 0x0d, 0x6f, 0x42, 0x4f, 0x48,
	0x8e, 0x99, 0x3d, 0x2d, 0xec, 0x1c, 0x3a, 0xc9, 0x65, 0x3a, 0x62, 0xf6, 0x94, 0xb8, 0x54, 0x14,
	0xd4, 0xb5, 0x28, 0xc8, 0xf0, 0x40, 0x43, 0xc7, 0x03, 0xfa, 0xda, 0x2f, 0x77, 0x0b, 0x72, 0xed,
	0xb7, 0xbe, 0x01, 0x0d, 0x69, 0x41, 0x01, 0xc7, 0x84, 0x81, 0x7d, 0xc6, 0x2f, 0x42, 0x57, 0x3f,
	0x72, 0xea, 0x46, 0xf6, 0xec, 0x63, 0xa4, 0xe2, 0xd9, 0xe0, 0x3f, 0x19, 0xd0, 0xca, 0x6c, 0x29,
	0xc6, 0x4a, 0xfe, 0x4c, 0x8a, 0xca, 0xf3, 0x60, 0x24, 0x91, 0x9a, 0x03, 0x68, 0x9c, 0xc7, 0xcc,
	0x9b, 0x5c, 0x70, 0x99, 0xac, 0x54, 0xd1, 0x7c, 0x1b, 0x56, 0x5d, 0x2f, 0x71, 0xc2, 0x34, 0x10,
	0x7b, 0x7b, 0x3d, 0x5d, 0xf5, 0x73, 0x3a, 0x09, 0x79, 0x1b, 0x56, 0x6d, 0xd7, 0xf5, 0x44, 0x70,
	0x65, 0x36, 0x21, 0xb3, 0xf5, 0x73, 0x3a, 0xb1, 0x8a, 0x50, 0xcd, 0x59, 0x63, 0x3c, 0x53, 0x48,
	0x64, 0x2e, 0x5b, 0xcb, 0x6b, 0x46, 0x54, 0x61, 0x3d, 0x84, 0x5e, 0x31, 0x80, 0xc4, 0x6a, 0x9c,
	0x45, 0x9b, 0x66, 0x85, 0x8e, 0x22, 0xa2, 0x11, 0xfe, 0xdd, 0x80, 0xb6, 0xe6, 0xfd, 0xb4, 0x84,
	0x0b, 0xc7, 0x67, 0x31, 0x1d, 0xcc, 0x1a, 0x6a, 0x09, 0x27, 0x22, 0x1e, 0xf0, 0x8a, 0x01, 0x63,
	0x1f, 0xcc, 0xcd, 0xa2, 0x49, 0xe2, 0x6d, 0x45, 0x97, 0x32, 0x85, 0x87, 0x70, 0x86, 0x70, 0xf2,
	0x8c, 0xc5, 0x32, 0xfd, 0xb5, 0x38, 0x13, 0x78, 0xf2, 0x8c, 0xc5, 0xc2, 0xa8, 0x68, 0x9f, 0x78,
	0x26, 0x93, 0xa0, 0x2a, 0x9a, 0x43, 0x68, 0x46, 0x71, 0x78, 0xe5, 0x05, 0x8e, 0x4a, 0xe0, 0x59,
	0x59, 0x78, 0x8c, 0xe3, 0xf1, 0x99, 0xf2, 0x18, 0xf1, 0x2d, 0x68, 0x3c, 0x7c, 0x1e, 0x48, 0x87,
	0xc1, 0x6f, 0x2b, 0x86, 0xd5, 0x3c, 0x9b, 0xbf, 0xc2, 0xe1, 0xaf, 0x0e, 0x30, 0x06, 0x59, 0x86,
	0x3b, 0x74, 0x5d, 0xc4, 0x13, 0x52, 0xb2, 0xc4, 0x15, 0xa7, 0xb0, 0xb1, 0xa8, 0x56, 0xf8, 0x6e,
	0xb6, 0x42, 0x10, 0x96, 0x68, 0x84, 0x72, 0x79, 0xb8, 0x71, 0x01, 0xb1, 0x42, 0x18, 0xe0, 0xe7,
	0x07, 0x8c, 0x9f, 0xd8, 0x33, 0x9f, 0x05, 0xfc, 0xc4, 0x8e, 0x6d, 0x3f, 0x19, 0xb1, 0x67, 0x73,
	0x82, 0x6b, 0x9f, 0x43, 0xf0, 0xb2, 0x95, 0xc9, 0xfa, 0x45, 0x65, 0x69, 0x8f, 0xc9, 0x2d, 0x4c,
	0xf8, 0x87, 0xd0, 0x8b, 0x48, 0xca, 0x38, 0x42, 0x31, 0x3a, 0x5a, 0x5b, 0xd6, 0xcf, 0x7d, 0xb4,
	0x5f, 0x37, 0xd2, 0xc9, 0xc3, 0xbf, 0x31, 0xa0, 0x8a, 0xe8, 0xec, 0x0e, 0xb4, 0xc4, 0x1a, 0x77,
	0xca, 0x6d, 0x3f, 0x92, 0x6a, 0xe4, 0x04, 0xe1, 0x3d, 0x41, 0x18, 0x38, 0xec, 0x94, 0xc7, 0x2a,
	0xab, 0xa9, 0xb2, 0x50, 0x3f, 0xb2, 0x9d, 0x4b, 0xb1, 0x13, 0x93, 0x47, 0x61, 0xb2, 0x48, 0x35,
	0xb3, 0x53, 0x6f, 0x12, 0x28, 0x6f, 0x94, 0x45, 0x21, 0x2f, 0xf1, 0x26, 0x78, 0x34, 0xac, 0xbc,
	0x51, 0x95, 0xad, 0xff, 0x31, 0x60, 0x13, 0xc7, 0x70, 0x62, 0xcf, 0x08, 0x76, 0x2b, 0xe8, 0xf0,
	0x2a, 0x73, 0xbe, 0x14, 0x34, 0xec, 0x41, 0xdb, 0x76, 0xca, 0x97, 0x48, 0x40, 0x24, 0x75, 0x74,
	0x2d, 0x19, 0x28, 0x69, 0xa8, 0xa3, 0x6b, 0x22, 0x52, 0xbe, 0x90, 0xc7, 0xed, 0x41, 0x22, 0x39,
	0x33, 0xa4, 0xd1, 0xd5, 0xa8, 0xb4, 0xaf, 0x11, 0xb9, 0x14, 0xd1, 0x44, 0x23, 0x33, 0x09, 0x6e,
	0x0c, 0x26, 0x32, 0xb8, 0xf4, 0x1d, 0xfa, 0x97, 0x31, 0x60, 0xeb, 0x0a, 0xd6, 0xb4, 0x8e, 0x6e,
	0x1d, 0xc6, 0x0f, 0xa0, 0x86, 0x9d, 0x4b, 0xd7, 0xdb, 0xc9, 0x5c, 0x4f, 0xc8, 0x2d, 0x04, 0x32,
	0xf1, 0x59, 0x7f, 0xaf, 0xe6, 0xb5, 0xcc, 0xf0, 0xca, 0xc3, 0x94, 0xdb, 0xb5, 0x95, 0xc2, 0x5e,
	0x6e, 0x11, 0x18, 0xfc, 0x56, 0x09, 0x64, 0xd1, 0x9e, 0x76, 0xbd, 0xa8, 0xfa, 0x3c, 0xd0, 0xb2,
	0xfe, 0xa1, 0x22, 0x71, 0x6c, 0xce, 0x60, 0x1e, 0x42, 0x2f, 0x8a, 0x43, 0x3f, 0xa4, 0xf9, 0x16,
	0x98, 0x82, 0xae, 0x8c, 0x86, 0x05, 0x69, 0x27, 0x8a, 0x05, 0x01, 0x46, 0x37, 0xd2, 0x8b, 0xe6,
	0xf7, 0xca, 0x88, 0xa7, 0x82, 0x88, 0x67, 0xa7, 0x2c, 0x61, 0x19, 0xf4, 0x79, 0xa0, 0x41, 0x1f,
	0x9a, 0x85, 0x8d, 0x62, 0xd3, 0x32, 0x06, 0x7a, 0x58, 0xc0, 0x40, 0x55, 0x6c, 0xb2, 0x55, 0xea,
	0x6d, 0x1e, 0x0c, 0x99, 0x47, 0xf3, 0x38, 0x93, 0x2c, 0xb7, 0x5b, 0xb2, 0xdc, 0x75, 0x80, 0xd3,
	0xfa, 0x09, 0x6c, 0x2d, 0x36, 0x8b, 0xf9, 0xdb, 0xd0, 0x3f, 0xf7, 0x02, 0x31, 0x2f, 0x02, 0xa6,
	0x6b, 0xcb, 0x64, 0x8f, 0xc8, 0x4f, 0x24, 0x55, 0x63, 0x0c, 0x3c, 0xe7, 0x52, 0xbb, 0x44, 0x96,
	0x8c, 0x8f, 0x25, 0xd5, 0xfa, 0xcb, 0x8a, 0xe6, 0x6b, 0xff, 0xef, 0x70, 0x9c, 0x72, 0xcd, 0xda,
	0x22, 0x84, 0x56, 0xd7, 0x11, 0xda, 0x5d, 0x00, 0x89, 0xd0, 0x44, 0x7a, 0xa5, 0x8c, 0xd1, 0x22,
	0x8c, 0x26, 0x12, 0xec, 0x5d, 0x00, 0x0d, 0x0c, 0xd2, 0xf6, 0xa4, 0x15, 0x2b, 0x24, 0x68, 0xfd,
	0x9b, 0xa1, 0xe5, 0x14, 0x05, 0xe7, 0xc4, 0x36, 0x3c, 0x83, 0x73, 0xea, 0xcc, 0x21, 0x43, 0x72,
	0x78, 0x80, 0x16, 0x33, 0x74, 0xab, 0x6c, 0xe0, 0x44, 0x50, 0x03, 0x67, 0x59, 0x06, 0x93, 0xbb,
	0x6f, 0x22, 0xe1, 0x7e, 0x68, 0x3e, 0x0d, 0x56, 0x6f, 0x4a, 0x83, 0xb5, 0x42, 0x1a, 0x5c, 0x84,
	0x36, 0xeb, 0x8b, 0xd0, 0xe6, 0xaf, 0x0d, 0x30, 0xe7, 0xbd, 0xf6, 0x2b, 0x0f, 0x3b, 0x7f, 0x65,
	0xc0, 0xf6, 0x92, 0x80, 0x7a, 0x29, 0x00, 0x6a, 0xde, 0x87, 0xf5, 0x73, 0x2f, 0xf0, 0x92, 0x8b,
	0xb1, 0x3d, 0x9d, 0x8e, 0x55, 0x95, 0x1c, 0xeb, 0x1a, 0x55, 0x1d, 0x4e, 0xa7, 0x4a, 0xb4, 0x79,
	0xa4, 0x09, 0xd5, 0x1e, 0x81, 0xec, 0x5d, 0x13, 0xd9, 0xf4, 0x22, 0x44, 0xb5, 0xc2, 0x17, 0x21,
	0x3f, 0x86, 0xdd, 0x6b, 0x98, 0x85, 0xcf, 0x3d, 0xb7, 0x67, 0x67, 0xde, 0x74, 0x9a, 0x07, 0x5c,
	0x4b, 0x52, 0xc8, 0xad, 0x32, 0x1d, 0xf2, 0x1c, 0xaf, 0x48, 0xc7, 0xae, 0xf5, 0xd7, 0x06, 0xec,
	0x2a, 0xb1, 0x8f, 0x42, 0x3f, 0xb2, 0x83, 0x57, 0x7d, 0xfc, 0xf2, 0x3d, 0xe8, 0x38, 0x24, 0x48,
	0x1f, 0xf5, 0xae, 0xbe, 0x6f, 0xd6, 0x3a, 0xc2, 0xd5, 0xaa, 0xed, 0xe4, 0x04, 0x6b, 0x0c, 0xdb,
	0x4b, 0xf8, 0xca, 0xa3, 0x31, 0xca, 0xa3, 0x29, 0xcc, 0xa3, 0xfe, 0x10, 0x46, 0x11, 0xf1, 0xb1,
	0xc6, 0xbf, 0x1a, 0xb0, 0xae, 0x7a, 0x38, 0x65, 0x81, 0xfb, 0xa5, 0x62, 0xa0, 0x25, 0x4e, 0x53,
	0x5d, 0xe6, 0x34, 0xbf, 0x5f, 0x76, 0x9a, 0x1a, 0x9a, 0x6f, 0x43, 0x37, 0x5f, 0x66, 0xb7, 0xa2,
	0xa7, 0x8c, 0x60, 0xb5, 0xcc, 0x71, 0xb3, 0xc5, 0x8a, 0xfe, 0x53, 0x29, 0xf9, 0x8f, 0xe5, 0xc3,
	0x96, 0x92, 0x39, 0x62, 0x8e, 0xc7, 0xae, 0xd8, 0x97, 0x0a, 0xa0, 0xfe, 0xbb, 0x02, 0xeb, 0x87,
	0xea, 0x6c, 0x47, 0x3b, 0xd9, 0x2a, 0x4b, 0x34, 0xe6, 0x24, 0xde, 0x83, 0x55, 0xc1, 0x91, 0x1f,
	0x19, 0x65, 0xfd, 0x8a, 0x45, 0x29, 0x93, 0x79, 0xfd, 0x11, 0x97, 0x06, 0x53, 0xf1, 0xbb, 0x7c,
	0xa2, 0x56, 0x9b, 0x3b, 0x51, 0xcb, 0x21, 0x52, 0xbd, 0x00, 0x91, 0xbe, 0x0e, 0x1b, 0xda, 0xb4,
	0x67, 0x5a, 0xc9, 0x63, 0x30, 0x33, 0x9b, 0xf7, 0x4c, 0xb1, 0x6c, 0xe5, 0x6a, 0x6a, 0x2b, 0xd7,
	0x16, 0xd4, 0x63, 0x76, 0x9e, 0x06, 0xea, 0x61, 0x92, 0x2c, 0xcd, 0xa3, 0x1b, 0xc8, 0xd1, 0x0d,
	0x4a, 0x3c, 0xc5, 0x05, 0x71, 0x19, 0xba, 0xb1, 0xfe, 0x0c, 0x36, 0x17, 0xb2, 0xfd, 0x46, 0x96,
	0x70, 0xeb, 0x3f, 0x0d, 0xd8, 0xca, 0xc6, 0x4d, 0xc7, 0xb1, 0x5f, 0x6a, 0x08, 0x2e, 0x72, 0x8d,
	0xea, 0x32, 0xd7, 0x90, 0xb3, 0x59, 0x7b, 0xa9, 0xd9, 0xac, 0x2f, 0x9b, 0x4d, 0xeb, 0x4c, 0xf3,
	0xe3, 0x0f, 0x58, 0xb6, 0xeb, 0x78, 0x1d, 0x3a, 0x05, 0x35, 0x68, 0x8c, 0x6d, 0x5b, 0xd3, 0xe1,
	0xa5, 0x1d, 0xd9, 0xfa, 0x99, 0x01, 0x1b, 0xc5, 0x4e, 0x6e, 0x9d, 0xb3, 0xbf, 0x0f, 0x74, 0xa4,
	0x3a, 0x16, 0xa2, 0x92, 0xb1, 0xbe, 0xfb, 0xd8, 0xc8, 0x9c, 0x4a, 0x76, 0x83, 0x99, 0xa7, 0x6f,
	0x2b, 0xff, 0x49, 0xd0, 0xfc, 0xd6, 0xaf, 0xab, 0xb0, 0x5a, 0xe6, 0x5a, 0x38, 0x16, 0x63, 0xa1,
	0xe5, 0xcb, 0x86, 0xa9, 0xcc, 0x1b, 0xa6, 0xec, 0x00, 0x2b, 0x73, 0x0e, 0xa0, 0x7b, 0x8f, 0xdc,
	0x15, 0x2b, 0xef, 0x79, 0x1f, 0x3a, 0x7a, 0xc4, 0x48, 0x90, 0xbd, 0xb7, 0x68, 0x6c, 0xfa, 0x3d,
	0x45, 0x5b, 0x0b, 0x9b, 0x2c, 0x41, 0xd4, 0xb5, 0x04, 0xf1, 0x1e, 0xb4, 0x63, 0xc6, 0xd3, 0x58,
	0xee, 0x53, 0x1a, 0xda, 0x25, 0x7a, 0xd9, 0x64, 0xc8, 0x86, 0x52, 0x21, 0xce, 0xbe, 0xc5, 0x76,
	0x1d, 0x75, 0xb4, 0x7d, 0x2e, 0xaf, 0xe3, 0xb2, 0xb2, 0xf9, 0x2e, 0x98, 0x14, 0xf0, 0xf2, 0x92,
	0x8a, 0xa0, 0x09, 0xa5, 0x82, 0x55, 0xaa, 0xa1, 0x9b, 0x2a, 0xb5, 0x99, 0x2e, 0x70, 0xab, 0x87,
	0x8a, 0x3a, 0xe3, 0x75, 0xd7, 0x73, 0x7a, 0xa2, 0xeb, 0xdc, 0x74, 0xfb, 0xd6, 0x9d, 0xbb, 0x1d,
	0xca, 0xa3, 0xaf, 0x57, 0x88, 0xbe, 0xef, 0x02, 0xd0, 0x7b, 0x41, 0x5c, 0xcd, 0xfa, 0x98, 0xa8,
	0xee, 0x2e, 0x32, 0xd0, 0xc7, 0x82, 0x0b, 0xb1, 0x47, 0xcb, 0x57, 0x9f, 0xd6, 0x5f, 0x18, 0xb0,
	0x7b, 0xcd, 0x04, 0x2d, 0x49, 0x58, 0x2b, 0x5f, 0x6c, 0xc2, 0xfa, 0xa5, 0x01, 0xc3, 0xe5, 0xd3,
	0x69, 0xbe, 0x03, 0x6b, 0xe4, 0x76, 0xd2, 0x13, 0xb4, 0xcb, 0x97, 0x3e, 0x56, 0x10, 0xaf, 0x32,
	0xe3, 0xb5, 0x78, 0xac, 0xb4, 0x1e, 0x93, 0x2e, 0x1a, 0x9e, 0x9b, 0x03, 0x38, 0xd5, 0x22, 0x50,
	0x45, 0x80, 0xf3, 0xa7, 0xb0, 0xb3, 0xd4, 0xb8, 0x99, 0x17, 0x1b, 0x9a, 0x17, 0xcb, 0xa7, 0xaa,
	0x95, 0xc2, 0x53, 0x55, 0x7a, 0x83, 0x23, 0xe8, 0xfa, 0x1b, 0x9c, 0x27, 0xf1, 0xd4, 0xf2, 0x61,
	0xe3, 0xf4, 0x22, 0xc4, 0x07, 0xa1, 0xf8, 0x1a, 0x4b, 0x65, 0xb7, 0xc2, 0xfb, 0x56, 0xa3, 0xf8,
	0xbe, 0x95, 0x1c, 0x48, 0x70, 0xeb, 0x4f, 0x26, 0x81, 0x48, 0xc8, 0xb0, 0x0d, 0x0d, 0xf5, 0xea,
	0x54, 0xe6, 0x6f, 0x0f, 0x5f, 0x9c, 0x5a, 0x1f, 0x42, 0x5b, 0x76, 0x87, 0xe6, 0xd6, 0x1f, 0xa0,
	0x1a, 0x85, 0x07, 0xa8, 0x73, 0xaf, 0x57, 0x2b, 0xe5, 0xd7, 0xab, 0x56, 0x0a, 0x9b, 0x25, 0xd5,
	0x6f, 0x9d, 0x33, 0xdf, 0x99, 0x7b, 0x69, 0x8b, 0x17, 0x34, 0x9a, 0xaa, 0xd9, 0x3b, 0xdb, 0x83,
	0xff, 0x02, 0xe8, 0xc9, 0xc7, 0xf1, 0xa7, 0x2c, 0xbe, 0x12, 0xbb, 0x97, 0x6f, 0xc3, 0xda, 0x07,
	0x8c, 0x97, 0xde, 0x7a, 0xb7, 0x84, 0x04, 0x7c, 0x10, 0x3f, 0x1c, 0x2e, 0x78, 0x2b, 0xae, 0x94,
	0xfd, 0x10, 0x7a, 0xc5, 0xe7, 0x37, 0xe6, 0x1d, 0xed, 0x91, 0xef, 0xdc, 0x3d, 0x2b, 0xc9, 0x5a,
	0xf2, 0x62, 0xe8, 0x43, 0xe8, 0x97, 0x1e, 0x65, 0x98, 0xc3, 0xd2, 0xbd, 0x90, 0x76, 0x6e, 0x36,
	0xdc, 0x5d, 0x58, 0x27, 0x65, 0x9d, 0xc0, 0xda, 0xdc, 0xf5, 0xea, 0x0d, 0xaa, 0xdd, 0xbd, 0xf6,
	0x16, 0xdc, 0xfc, 0x13, 0xd8, 0x5c, 0x78, 0x69, 0x6b, 0xee, 0x2f, 0xbd, 0xf2, 0x7d, 0x49, 0xc9,
	0xef, 0xc1, 0xda, 0x11, 0x9b, 0xb2, 0xa2, 0xd4, 0x2d, 0x6d, 0x74, 0xda, 0x7f, 0x08, 0x86, 0xf2,
	0xe1, 0x7e, 0xe1, 0xff, 0x09, 0x87, 0xb0, 0xfe, 0xd4, 0xe3, 0x17, 0x6e, 0x6c, 0x3f, 0xbf, 0xad,
	0x88, 0xef, 0x40, 0xf7, 0x49, 0x74, 0xdb, 0xc6, 0x7f, 0x00, 0xfd, 0xa3, 0xf0, 0x79, 0x70, 0xdb,
	0xe6, 0x0f, 0xa1, 0xa9, 0x4e, 0xff, 0xcd, 0x75, 0xfd, 0xa6, 0x40, 0x35, 0xda, 0x28, 0x12, 0x65,
	0xb3, 0x53, 0x79, 0xfa, 0x53, 0x3e, 0x05, 0xa7, 0x69, 0x5e, 0x76, 0xf4, 0x3f, 0xbc, 0xae, 0x36,
	0x31, 0xdf, 0x83, 0x5e, 0xf1, 0x58, 0xda, 0xcc, 0x0f, 0xea, 0xca, 0x47, 0xd5, 0xcb, 0xe6, 0x62,
	0xc1, 0x9e, 0x51, 0x0f, 0xa7, 0xbd, 0x25, 0xfb, 0x4f, 0x6d, 0x2e, 0x3a, 0xfa, 0xa6, 0xd0, 0xdc,
	0xd6, 0x1b, 0x68, 0xdb, 0xc4, 0x25, 0xfd, 0xf7, 0x4b, 0xdb, 0x24, 0x0a, 0xa2, 0xc5, 0x7b, 0xa7,
	0x25, 0xbe, 0xd0, 0xd1, 0x77, 0x3e, 0xd4, 0xff, 0x82, 0xbd, 0xd0, 0xb2, 0xfe, 0x4b, 0x88, 0x9a,
	0xfa, 0x5f, 0x0c, 0xb3, 0x97, 0x88, 0xe8, 0xe8, 0x8b, 0x46, 0xa9, 0xff, 0x1c, 0xc3, 0x0e, 0x07,
	0xf3, 0x15, 0x52, 0xc4, 0xef, 0x4a, 0x97, 0x12, 0xcd, 0x8b, 0xc7, 0xa5, 0xaa, 0xed, 0x66, 0x89,
	0x2a, 0x1b, 0x1e, 0x41, 0xb7, 0x90, 0x95, 0xcd, 0x81, 0x96, 0x49, 0x0b, 0x6b, 0xcc, 0x70, 0x67,
	0x41, 0x0d, 0x49, 0x39, 0xab, 0xe3, 0x5f, 0x8a, 0xbe, 0xf9, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff,
	0x43, 0xe5, 0xe5, 0x3b, 0x92, 0x34, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// WxVideoServiceClient is the client API for WxVideoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type WxVideoServiceClient interface {
	//查询微信视频号类目详情
	GetWxCategoryList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*WxCategoryListResponse, error)
	// 上传商品到微信审核
	AddProductInfo(ctx context.Context, in *UploadProductInfoRequest, opts ...grpc.CallOption) (*AddProductInfoResponse, error)
	// 获取商品列表
	ProductInfoList(ctx context.Context, in *ProductInfoListRequest, opts ...grpc.CallOption) (*ProductInfoListResponse, error)
	// 更新商品
	UpdateProductInfo(ctx context.Context, in *UploadProductInfoRequest, opts ...grpc.CallOption) (*UpdateProductInfoResponse, error)
	// 免审核更新商品
	FreeUpdateProductInfo(ctx context.Context, in *FreeUpdateProductInfoRequest, opts ...grpc.CallOption) (*UpdateProductInfoResponse, error)
	// 删除商品
	DeleteProductInfo(ctx context.Context, in *ProductBaseRequest, opts ...grpc.CallOption) (*WxBaseResponse, error)
	// 撤回商品审核
	WithdrawProductInfo(ctx context.Context, in *ProductBaseRequest, opts ...grpc.CallOption) (*WxBaseResponse, error)
	// 上架商品
	UpProductInfo(ctx context.Context, in *ProductBaseRequest, opts ...grpc.CallOption) (*WxBaseResponse, error)
	// 下架商品
	DownProductInfo(ctx context.Context, in *ProductBaseRequest, opts ...grpc.CallOption) (*WxBaseResponse, error)
	// 生成订单
	OrderAdd(ctx context.Context, in *OrderAddRequest, opts ...grpc.CallOption) (*OrderAddResponse, error)
	// 生成支付参数
	OrderGetPaymentParams(ctx context.Context, in *OrderGetPaymentParamsReq, opts ...grpc.CallOption) (*OrderGetPaymentParamsRes, error)
	// 同步订单支付结果
	OrderPayStatus(ctx context.Context, in *OrderPayStatusRequest, opts ...grpc.CallOption) (*WxBaseResponse, error)
	// 获取快递公司列表
	DeliveryCompanyList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*DeliveryCompanyListResponse, error)
	// 订单发货
	DeliverySend(ctx context.Context, in *DeliverySendRequest, opts ...grpc.CallOption) (*WxBaseResponse, error)
	// 订单确认收货
	DeliveryRecieve(ctx context.Context, in *DeliveryRecieveRequest, opts ...grpc.CallOption) (*WxBaseResponse, error)
	// 创建售后
	AftersaleAdd(ctx context.Context, in *AftersaleAddRequest, opts ...grpc.CallOption) (*WxBaseResponse, error)
	// 更新售后
	AftersaleUpdate(ctx context.Context, in *AftersaleUpdateRequest, opts ...grpc.CallOption) (*WxBaseResponse, error)
	// 获取售后详情
	AftersaleGet(ctx context.Context, in *AftersaleGetRequest, opts ...grpc.CallOption) (*AftersaleGetResponse, error)
	// 获取订单
	OrderGet(ctx context.Context, in *OrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error)
	// 上传图片
	ShopImgUpload(ctx context.Context, in *ShopImgUploadRequest, opts ...grpc.CallOption) (*ShopImgUploadResponse, error)
}

type wxVideoServiceClient struct {
	cc *grpc.ClientConn
}

func NewWxVideoServiceClient(cc *grpc.ClientConn) WxVideoServiceClient {
	return &wxVideoServiceClient{cc}
}

func (c *wxVideoServiceClient) GetWxCategoryList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*WxCategoryListResponse, error) {
	out := new(WxCategoryListResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/GetWxCategoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) AddProductInfo(ctx context.Context, in *UploadProductInfoRequest, opts ...grpc.CallOption) (*AddProductInfoResponse, error) {
	out := new(AddProductInfoResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/AddProductInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) ProductInfoList(ctx context.Context, in *ProductInfoListRequest, opts ...grpc.CallOption) (*ProductInfoListResponse, error) {
	out := new(ProductInfoListResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/ProductInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) UpdateProductInfo(ctx context.Context, in *UploadProductInfoRequest, opts ...grpc.CallOption) (*UpdateProductInfoResponse, error) {
	out := new(UpdateProductInfoResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/UpdateProductInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) FreeUpdateProductInfo(ctx context.Context, in *FreeUpdateProductInfoRequest, opts ...grpc.CallOption) (*UpdateProductInfoResponse, error) {
	out := new(UpdateProductInfoResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/FreeUpdateProductInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) DeleteProductInfo(ctx context.Context, in *ProductBaseRequest, opts ...grpc.CallOption) (*WxBaseResponse, error) {
	out := new(WxBaseResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/DeleteProductInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) WithdrawProductInfo(ctx context.Context, in *ProductBaseRequest, opts ...grpc.CallOption) (*WxBaseResponse, error) {
	out := new(WxBaseResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/WithdrawProductInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) UpProductInfo(ctx context.Context, in *ProductBaseRequest, opts ...grpc.CallOption) (*WxBaseResponse, error) {
	out := new(WxBaseResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/UpProductInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) DownProductInfo(ctx context.Context, in *ProductBaseRequest, opts ...grpc.CallOption) (*WxBaseResponse, error) {
	out := new(WxBaseResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/DownProductInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) OrderAdd(ctx context.Context, in *OrderAddRequest, opts ...grpc.CallOption) (*OrderAddResponse, error) {
	out := new(OrderAddResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/OrderAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) OrderGetPaymentParams(ctx context.Context, in *OrderGetPaymentParamsReq, opts ...grpc.CallOption) (*OrderGetPaymentParamsRes, error) {
	out := new(OrderGetPaymentParamsRes)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/OrderGetPaymentParams", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) OrderPayStatus(ctx context.Context, in *OrderPayStatusRequest, opts ...grpc.CallOption) (*WxBaseResponse, error) {
	out := new(WxBaseResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/OrderPayStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) DeliveryCompanyList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*DeliveryCompanyListResponse, error) {
	out := new(DeliveryCompanyListResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/DeliveryCompanyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) DeliverySend(ctx context.Context, in *DeliverySendRequest, opts ...grpc.CallOption) (*WxBaseResponse, error) {
	out := new(WxBaseResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/DeliverySend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) DeliveryRecieve(ctx context.Context, in *DeliveryRecieveRequest, opts ...grpc.CallOption) (*WxBaseResponse, error) {
	out := new(WxBaseResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/DeliveryRecieve", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) AftersaleAdd(ctx context.Context, in *AftersaleAddRequest, opts ...grpc.CallOption) (*WxBaseResponse, error) {
	out := new(WxBaseResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/AftersaleAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) AftersaleUpdate(ctx context.Context, in *AftersaleUpdateRequest, opts ...grpc.CallOption) (*WxBaseResponse, error) {
	out := new(WxBaseResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/AftersaleUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) AftersaleGet(ctx context.Context, in *AftersaleGetRequest, opts ...grpc.CallOption) (*AftersaleGetResponse, error) {
	out := new(AftersaleGetResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/AftersaleGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) OrderGet(ctx context.Context, in *OrderListRequest, opts ...grpc.CallOption) (*OrderListResponse, error) {
	out := new(OrderListResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/OrderGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wxVideoServiceClient) ShopImgUpload(ctx context.Context, in *ShopImgUploadRequest, opts ...grpc.CallOption) (*ShopImgUploadResponse, error) {
	out := new(ShopImgUploadResponse)
	err := c.cc.Invoke(ctx, "/et.WxVideoService/ShopImgUpload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WxVideoServiceServer is the server API for WxVideoService service.
type WxVideoServiceServer interface {
	//查询微信视频号类目详情
	GetWxCategoryList(context.Context, *Empty) (*WxCategoryListResponse, error)
	// 上传商品到微信审核
	AddProductInfo(context.Context, *UploadProductInfoRequest) (*AddProductInfoResponse, error)
	// 获取商品列表
	ProductInfoList(context.Context, *ProductInfoListRequest) (*ProductInfoListResponse, error)
	// 更新商品
	UpdateProductInfo(context.Context, *UploadProductInfoRequest) (*UpdateProductInfoResponse, error)
	// 免审核更新商品
	FreeUpdateProductInfo(context.Context, *FreeUpdateProductInfoRequest) (*UpdateProductInfoResponse, error)
	// 删除商品
	DeleteProductInfo(context.Context, *ProductBaseRequest) (*WxBaseResponse, error)
	// 撤回商品审核
	WithdrawProductInfo(context.Context, *ProductBaseRequest) (*WxBaseResponse, error)
	// 上架商品
	UpProductInfo(context.Context, *ProductBaseRequest) (*WxBaseResponse, error)
	// 下架商品
	DownProductInfo(context.Context, *ProductBaseRequest) (*WxBaseResponse, error)
	// 生成订单
	OrderAdd(context.Context, *OrderAddRequest) (*OrderAddResponse, error)
	// 生成支付参数
	OrderGetPaymentParams(context.Context, *OrderGetPaymentParamsReq) (*OrderGetPaymentParamsRes, error)
	// 同步订单支付结果
	OrderPayStatus(context.Context, *OrderPayStatusRequest) (*WxBaseResponse, error)
	// 获取快递公司列表
	DeliveryCompanyList(context.Context, *Empty) (*DeliveryCompanyListResponse, error)
	// 订单发货
	DeliverySend(context.Context, *DeliverySendRequest) (*WxBaseResponse, error)
	// 订单确认收货
	DeliveryRecieve(context.Context, *DeliveryRecieveRequest) (*WxBaseResponse, error)
	// 创建售后
	AftersaleAdd(context.Context, *AftersaleAddRequest) (*WxBaseResponse, error)
	// 更新售后
	AftersaleUpdate(context.Context, *AftersaleUpdateRequest) (*WxBaseResponse, error)
	// 获取售后详情
	AftersaleGet(context.Context, *AftersaleGetRequest) (*AftersaleGetResponse, error)
	// 获取订单
	OrderGet(context.Context, *OrderListRequest) (*OrderListResponse, error)
	// 上传图片
	ShopImgUpload(context.Context, *ShopImgUploadRequest) (*ShopImgUploadResponse, error)
}

// UnimplementedWxVideoServiceServer can be embedded to have forward compatible implementations.
type UnimplementedWxVideoServiceServer struct {
}

func (*UnimplementedWxVideoServiceServer) GetWxCategoryList(ctx context.Context, req *Empty) (*WxCategoryListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWxCategoryList not implemented")
}
func (*UnimplementedWxVideoServiceServer) AddProductInfo(ctx context.Context, req *UploadProductInfoRequest) (*AddProductInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddProductInfo not implemented")
}
func (*UnimplementedWxVideoServiceServer) ProductInfoList(ctx context.Context, req *ProductInfoListRequest) (*ProductInfoListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductInfoList not implemented")
}
func (*UnimplementedWxVideoServiceServer) UpdateProductInfo(ctx context.Context, req *UploadProductInfoRequest) (*UpdateProductInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProductInfo not implemented")
}
func (*UnimplementedWxVideoServiceServer) FreeUpdateProductInfo(ctx context.Context, req *FreeUpdateProductInfoRequest) (*UpdateProductInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FreeUpdateProductInfo not implemented")
}
func (*UnimplementedWxVideoServiceServer) DeleteProductInfo(ctx context.Context, req *ProductBaseRequest) (*WxBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProductInfo not implemented")
}
func (*UnimplementedWxVideoServiceServer) WithdrawProductInfo(ctx context.Context, req *ProductBaseRequest) (*WxBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WithdrawProductInfo not implemented")
}
func (*UnimplementedWxVideoServiceServer) UpProductInfo(ctx context.Context, req *ProductBaseRequest) (*WxBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpProductInfo not implemented")
}
func (*UnimplementedWxVideoServiceServer) DownProductInfo(ctx context.Context, req *ProductBaseRequest) (*WxBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownProductInfo not implemented")
}
func (*UnimplementedWxVideoServiceServer) OrderAdd(ctx context.Context, req *OrderAddRequest) (*OrderAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderAdd not implemented")
}
func (*UnimplementedWxVideoServiceServer) OrderGetPaymentParams(ctx context.Context, req *OrderGetPaymentParamsReq) (*OrderGetPaymentParamsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderGetPaymentParams not implemented")
}
func (*UnimplementedWxVideoServiceServer) OrderPayStatus(ctx context.Context, req *OrderPayStatusRequest) (*WxBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderPayStatus not implemented")
}
func (*UnimplementedWxVideoServiceServer) DeliveryCompanyList(ctx context.Context, req *Empty) (*DeliveryCompanyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeliveryCompanyList not implemented")
}
func (*UnimplementedWxVideoServiceServer) DeliverySend(ctx context.Context, req *DeliverySendRequest) (*WxBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeliverySend not implemented")
}
func (*UnimplementedWxVideoServiceServer) DeliveryRecieve(ctx context.Context, req *DeliveryRecieveRequest) (*WxBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeliveryRecieve not implemented")
}
func (*UnimplementedWxVideoServiceServer) AftersaleAdd(ctx context.Context, req *AftersaleAddRequest) (*WxBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AftersaleAdd not implemented")
}
func (*UnimplementedWxVideoServiceServer) AftersaleUpdate(ctx context.Context, req *AftersaleUpdateRequest) (*WxBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AftersaleUpdate not implemented")
}
func (*UnimplementedWxVideoServiceServer) AftersaleGet(ctx context.Context, req *AftersaleGetRequest) (*AftersaleGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AftersaleGet not implemented")
}
func (*UnimplementedWxVideoServiceServer) OrderGet(ctx context.Context, req *OrderListRequest) (*OrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderGet not implemented")
}
func (*UnimplementedWxVideoServiceServer) ShopImgUpload(ctx context.Context, req *ShopImgUploadRequest) (*ShopImgUploadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopImgUpload not implemented")
}

func RegisterWxVideoServiceServer(s *grpc.Server, srv WxVideoServiceServer) {
	s.RegisterService(&_WxVideoService_serviceDesc, srv)
}

func _WxVideoService_GetWxCategoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).GetWxCategoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/GetWxCategoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).GetWxCategoryList(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_AddProductInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadProductInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).AddProductInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/AddProductInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).AddProductInfo(ctx, req.(*UploadProductInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_ProductInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductInfoListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).ProductInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/ProductInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).ProductInfoList(ctx, req.(*ProductInfoListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_UpdateProductInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadProductInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).UpdateProductInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/UpdateProductInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).UpdateProductInfo(ctx, req.(*UploadProductInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_FreeUpdateProductInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreeUpdateProductInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).FreeUpdateProductInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/FreeUpdateProductInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).FreeUpdateProductInfo(ctx, req.(*FreeUpdateProductInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_DeleteProductInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).DeleteProductInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/DeleteProductInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).DeleteProductInfo(ctx, req.(*ProductBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_WithdrawProductInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).WithdrawProductInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/WithdrawProductInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).WithdrawProductInfo(ctx, req.(*ProductBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_UpProductInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).UpProductInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/UpProductInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).UpProductInfo(ctx, req.(*ProductBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_DownProductInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).DownProductInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/DownProductInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).DownProductInfo(ctx, req.(*ProductBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_OrderAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).OrderAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/OrderAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).OrderAdd(ctx, req.(*OrderAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_OrderGetPaymentParams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderGetPaymentParamsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).OrderGetPaymentParams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/OrderGetPaymentParams",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).OrderGetPaymentParams(ctx, req.(*OrderGetPaymentParamsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_OrderPayStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderPayStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).OrderPayStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/OrderPayStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).OrderPayStatus(ctx, req.(*OrderPayStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_DeliveryCompanyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).DeliveryCompanyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/DeliveryCompanyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).DeliveryCompanyList(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_DeliverySend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeliverySendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).DeliverySend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/DeliverySend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).DeliverySend(ctx, req.(*DeliverySendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_DeliveryRecieve_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeliveryRecieveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).DeliveryRecieve(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/DeliveryRecieve",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).DeliveryRecieve(ctx, req.(*DeliveryRecieveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_AftersaleAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AftersaleAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).AftersaleAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/AftersaleAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).AftersaleAdd(ctx, req.(*AftersaleAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_AftersaleUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AftersaleUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).AftersaleUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/AftersaleUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).AftersaleUpdate(ctx, req.(*AftersaleUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_AftersaleGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AftersaleGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).AftersaleGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/AftersaleGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).AftersaleGet(ctx, req.(*AftersaleGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_OrderGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).OrderGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/OrderGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).OrderGet(ctx, req.(*OrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WxVideoService_ShopImgUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShopImgUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WxVideoServiceServer).ShopImgUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.WxVideoService/ShopImgUpload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WxVideoServiceServer).ShopImgUpload(ctx, req.(*ShopImgUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _WxVideoService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.WxVideoService",
	HandlerType: (*WxVideoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWxCategoryList",
			Handler:    _WxVideoService_GetWxCategoryList_Handler,
		},
		{
			MethodName: "AddProductInfo",
			Handler:    _WxVideoService_AddProductInfo_Handler,
		},
		{
			MethodName: "ProductInfoList",
			Handler:    _WxVideoService_ProductInfoList_Handler,
		},
		{
			MethodName: "UpdateProductInfo",
			Handler:    _WxVideoService_UpdateProductInfo_Handler,
		},
		{
			MethodName: "FreeUpdateProductInfo",
			Handler:    _WxVideoService_FreeUpdateProductInfo_Handler,
		},
		{
			MethodName: "DeleteProductInfo",
			Handler:    _WxVideoService_DeleteProductInfo_Handler,
		},
		{
			MethodName: "WithdrawProductInfo",
			Handler:    _WxVideoService_WithdrawProductInfo_Handler,
		},
		{
			MethodName: "UpProductInfo",
			Handler:    _WxVideoService_UpProductInfo_Handler,
		},
		{
			MethodName: "DownProductInfo",
			Handler:    _WxVideoService_DownProductInfo_Handler,
		},
		{
			MethodName: "OrderAdd",
			Handler:    _WxVideoService_OrderAdd_Handler,
		},
		{
			MethodName: "OrderGetPaymentParams",
			Handler:    _WxVideoService_OrderGetPaymentParams_Handler,
		},
		{
			MethodName: "OrderPayStatus",
			Handler:    _WxVideoService_OrderPayStatus_Handler,
		},
		{
			MethodName: "DeliveryCompanyList",
			Handler:    _WxVideoService_DeliveryCompanyList_Handler,
		},
		{
			MethodName: "DeliverySend",
			Handler:    _WxVideoService_DeliverySend_Handler,
		},
		{
			MethodName: "DeliveryRecieve",
			Handler:    _WxVideoService_DeliveryRecieve_Handler,
		},
		{
			MethodName: "AftersaleAdd",
			Handler:    _WxVideoService_AftersaleAdd_Handler,
		},
		{
			MethodName: "AftersaleUpdate",
			Handler:    _WxVideoService_AftersaleUpdate_Handler,
		},
		{
			MethodName: "AftersaleGet",
			Handler:    _WxVideoService_AftersaleGet_Handler,
		},
		{
			MethodName: "OrderGet",
			Handler:    _WxVideoService_OrderGet_Handler,
		},
		{
			MethodName: "ShopImgUpload",
			Handler:    _WxVideoService_ShopImgUpload_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/externalWxVideoNum.proto",
}
