// Code generated by protoc-gen-go. DO NOT EDIT.
// source: et/externalJddj.proto

package et

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type JddjGetBalanceBillListReq struct {
	// 店铺主体Id
	StoreMasterId int32 `protobuf:"varint,1,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	// 订单状态 1:已完成
	OrderStatus int32 `protobuf:"varint,2,opt,name=orderStatus,proto3" json:"orderStatus"`
	// 门店id
	ShopIds []int64 `protobuf:"varint,3,rep,packed,name=shopIds,proto3" json:"shopIds"`
	// 订单号
	OrderIds []int64 `protobuf:"varint,4,rep,packed,name=orderIds,proto3" json:"orderIds"`
	// 订单开始时间(开始) 2016-05-05 00:00:00
	OrderStartTime string `protobuf:"bytes,5,opt,name=orderStartTime,proto3" json:"orderStartTime"`
	// 订单开始时间(结束) 2016-05-08 23:00:00
	OrderEndTime string `protobuf:"bytes,6,opt,name=orderEndTime,proto3" json:"orderEndTime"`
	// 订单开始时间(开始) 2016-05-05 00:00:00
	FinishStartTime string `protobuf:"bytes,7,opt,name=finishStartTime,proto3" json:"finishStartTime"`
	// 订单开始时间(结束) 2016-05-08 23:00:00
	FinishEndTime        string   `protobuf:"bytes,8,opt,name=finishEndTime,proto3" json:"finishEndTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjGetBalanceBillListReq) Reset()         { *m = JddjGetBalanceBillListReq{} }
func (m *JddjGetBalanceBillListReq) String() string { return proto.CompactTextString(m) }
func (*JddjGetBalanceBillListReq) ProtoMessage()    {}
func (*JddjGetBalanceBillListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{0}
}

func (m *JddjGetBalanceBillListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjGetBalanceBillListReq.Unmarshal(m, b)
}
func (m *JddjGetBalanceBillListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjGetBalanceBillListReq.Marshal(b, m, deterministic)
}
func (m *JddjGetBalanceBillListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjGetBalanceBillListReq.Merge(m, src)
}
func (m *JddjGetBalanceBillListReq) XXX_Size() int {
	return xxx_messageInfo_JddjGetBalanceBillListReq.Size(m)
}
func (m *JddjGetBalanceBillListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjGetBalanceBillListReq.DiscardUnknown(m)
}

var xxx_messageInfo_JddjGetBalanceBillListReq proto.InternalMessageInfo

func (m *JddjGetBalanceBillListReq) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

func (m *JddjGetBalanceBillListReq) GetOrderStatus() int32 {
	if m != nil {
		return m.OrderStatus
	}
	return 0
}

func (m *JddjGetBalanceBillListReq) GetShopIds() []int64 {
	if m != nil {
		return m.ShopIds
	}
	return nil
}

func (m *JddjGetBalanceBillListReq) GetOrderIds() []int64 {
	if m != nil {
		return m.OrderIds
	}
	return nil
}

func (m *JddjGetBalanceBillListReq) GetOrderStartTime() string {
	if m != nil {
		return m.OrderStartTime
	}
	return ""
}

func (m *JddjGetBalanceBillListReq) GetOrderEndTime() string {
	if m != nil {
		return m.OrderEndTime
	}
	return ""
}

func (m *JddjGetBalanceBillListReq) GetFinishStartTime() string {
	if m != nil {
		return m.FinishStartTime
	}
	return ""
}

func (m *JddjGetBalanceBillListReq) GetFinishEndTime() string {
	if m != nil {
		return m.FinishEndTime
	}
	return ""
}

type JddjGetBalanceBillListRes struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//数据集合
	Data                 *BalanceBillList `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *JddjGetBalanceBillListRes) Reset()         { *m = JddjGetBalanceBillListRes{} }
func (m *JddjGetBalanceBillListRes) String() string { return proto.CompactTextString(m) }
func (*JddjGetBalanceBillListRes) ProtoMessage()    {}
func (*JddjGetBalanceBillListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{1}
}

func (m *JddjGetBalanceBillListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjGetBalanceBillListRes.Unmarshal(m, b)
}
func (m *JddjGetBalanceBillListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjGetBalanceBillListRes.Marshal(b, m, deterministic)
}
func (m *JddjGetBalanceBillListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjGetBalanceBillListRes.Merge(m, src)
}
func (m *JddjGetBalanceBillListRes) XXX_Size() int {
	return xxx_messageInfo_JddjGetBalanceBillListRes.Size(m)
}
func (m *JddjGetBalanceBillListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjGetBalanceBillListRes.DiscardUnknown(m)
}

var xxx_messageInfo_JddjGetBalanceBillListRes proto.InternalMessageInfo

func (m *JddjGetBalanceBillListRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *JddjGetBalanceBillListRes) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *JddjGetBalanceBillListRes) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *JddjGetBalanceBillListRes) GetData() *BalanceBillList {
	if m != nil {
		return m.Data
	}
	return nil
}

type BalanceBillList struct {
	PageNum              int64          `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum"`
	PageSize             int32          `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	Total                int64          `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	StartRow             int32          `protobuf:"varint,4,opt,name=startRow,proto3" json:"startRow"`
	EndRow               int32          `protobuf:"varint,5,opt,name=endRow,proto3" json:"endRow"`
	Pages                int32          `protobuf:"varint,6,opt,name=pages,proto3" json:"pages"`
	Result               []*BalanceBill `protobuf:"bytes,7,rep,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BalanceBillList) Reset()         { *m = BalanceBillList{} }
func (m *BalanceBillList) String() string { return proto.CompactTextString(m) }
func (*BalanceBillList) ProtoMessage()    {}
func (*BalanceBillList) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{2}
}

func (m *BalanceBillList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BalanceBillList.Unmarshal(m, b)
}
func (m *BalanceBillList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BalanceBillList.Marshal(b, m, deterministic)
}
func (m *BalanceBillList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BalanceBillList.Merge(m, src)
}
func (m *BalanceBillList) XXX_Size() int {
	return xxx_messageInfo_BalanceBillList.Size(m)
}
func (m *BalanceBillList) XXX_DiscardUnknown() {
	xxx_messageInfo_BalanceBillList.DiscardUnknown(m)
}

var xxx_messageInfo_BalanceBillList proto.InternalMessageInfo

func (m *BalanceBillList) GetPageNum() int64 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *BalanceBillList) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *BalanceBillList) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *BalanceBillList) GetStartRow() int32 {
	if m != nil {
		return m.StartRow
	}
	return 0
}

func (m *BalanceBillList) GetEndRow() int32 {
	if m != nil {
		return m.EndRow
	}
	return 0
}

func (m *BalanceBillList) GetPages() int32 {
	if m != nil {
		return m.Pages
	}
	return 0
}

func (m *BalanceBillList) GetResult() []*BalanceBill {
	if m != nil {
		return m.Result
	}
	return nil
}

type BalanceBill struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	OrgCode              int64    `protobuf:"varint,2,opt,name=orgCode,proto3" json:"orgCode"`
	OrgName              string   `protobuf:"bytes,3,opt,name=orgName,proto3" json:"orgName"`
	StationId            int64    `protobuf:"varint,4,opt,name=stationId,proto3" json:"stationId"`
	StationName          string   `protobuf:"bytes,5,opt,name=stationName,proto3" json:"stationName"`
	BusinessStartTime    string   `protobuf:"bytes,6,opt,name=businessStartTime,proto3" json:"businessStartTime"`
	BusinessFinishTime   string   `protobuf:"bytes,7,opt,name=businessFinishTime,proto3" json:"businessFinishTime"`
	OrderId              int64    `protobuf:"varint,8,opt,name=orderId,proto3" json:"orderId"`
	SrcOrderId           int64    `protobuf:"varint,9,opt,name=srcOrderId,proto3" json:"srcOrderId"`
	AccountTime          string   `protobuf:"bytes,10,opt,name=accountTime,proto3" json:"accountTime"`
	SettleFinishTime     string   `protobuf:"bytes,11,opt,name=settleFinishTime,proto3" json:"settleFinishTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BalanceBill) Reset()         { *m = BalanceBill{} }
func (m *BalanceBill) String() string { return proto.CompactTextString(m) }
func (*BalanceBill) ProtoMessage()    {}
func (*BalanceBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{3}
}

func (m *BalanceBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BalanceBill.Unmarshal(m, b)
}
func (m *BalanceBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BalanceBill.Marshal(b, m, deterministic)
}
func (m *BalanceBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BalanceBill.Merge(m, src)
}
func (m *BalanceBill) XXX_Size() int {
	return xxx_messageInfo_BalanceBill.Size(m)
}
func (m *BalanceBill) XXX_DiscardUnknown() {
	xxx_messageInfo_BalanceBill.DiscardUnknown(m)
}

var xxx_messageInfo_BalanceBill proto.InternalMessageInfo

func (m *BalanceBill) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BalanceBill) GetOrgCode() int64 {
	if m != nil {
		return m.OrgCode
	}
	return 0
}

func (m *BalanceBill) GetOrgName() string {
	if m != nil {
		return m.OrgName
	}
	return ""
}

func (m *BalanceBill) GetStationId() int64 {
	if m != nil {
		return m.StationId
	}
	return 0
}

func (m *BalanceBill) GetStationName() string {
	if m != nil {
		return m.StationName
	}
	return ""
}

func (m *BalanceBill) GetBusinessStartTime() string {
	if m != nil {
		return m.BusinessStartTime
	}
	return ""
}

func (m *BalanceBill) GetBusinessFinishTime() string {
	if m != nil {
		return m.BusinessFinishTime
	}
	return ""
}

func (m *BalanceBill) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *BalanceBill) GetSrcOrderId() int64 {
	if m != nil {
		return m.SrcOrderId
	}
	return 0
}

func (m *BalanceBill) GetAccountTime() string {
	if m != nil {
		return m.AccountTime
	}
	return ""
}

func (m *BalanceBill) GetSettleFinishTime() string {
	if m != nil {
		return m.SettleFinishTime
	}
	return ""
}

//批量同步门店营业状态
type SyncJddjStoreStatusRequest struct {
	Data []*SyncJddjStoreStatusRequestReqData `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	// shop_status_task表id
	TaskId               int64    `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SyncJddjStoreStatusRequest) Reset()         { *m = SyncJddjStoreStatusRequest{} }
func (m *SyncJddjStoreStatusRequest) String() string { return proto.CompactTextString(m) }
func (*SyncJddjStoreStatusRequest) ProtoMessage()    {}
func (*SyncJddjStoreStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{4}
}

func (m *SyncJddjStoreStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncJddjStoreStatusRequest.Unmarshal(m, b)
}
func (m *SyncJddjStoreStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncJddjStoreStatusRequest.Marshal(b, m, deterministic)
}
func (m *SyncJddjStoreStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncJddjStoreStatusRequest.Merge(m, src)
}
func (m *SyncJddjStoreStatusRequest) XXX_Size() int {
	return xxx_messageInfo_SyncJddjStoreStatusRequest.Size(m)
}
func (m *SyncJddjStoreStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncJddjStoreStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncJddjStoreStatusRequest proto.InternalMessageInfo

func (m *SyncJddjStoreStatusRequest) GetData() []*SyncJddjStoreStatusRequestReqData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *SyncJddjStoreStatusRequest) GetTaskId() int64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

type SyncJddjStoreStatusRequestReqData struct {
	//平台门店ID
	BaiduShopId string `protobuf:"bytes,1,opt,name=baidu_shop_id,json=baiduShopId,proto3" json:"baidu_shop_id"`
	//门店ID
	ShopId string `protobuf:"bytes,2,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	// 状态，0-暂停营业，1-营业
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	// app渠道 1.阿闻自有,2.TP代运营
	AppChannel           int32    `protobuf:"varint,4,opt,name=app_channel,json=appChannel,proto3" json:"app_channel"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SyncJddjStoreStatusRequestReqData) Reset()         { *m = SyncJddjStoreStatusRequestReqData{} }
func (m *SyncJddjStoreStatusRequestReqData) String() string { return proto.CompactTextString(m) }
func (*SyncJddjStoreStatusRequestReqData) ProtoMessage()    {}
func (*SyncJddjStoreStatusRequestReqData) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{4, 0}
}

func (m *SyncJddjStoreStatusRequestReqData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncJddjStoreStatusRequestReqData.Unmarshal(m, b)
}
func (m *SyncJddjStoreStatusRequestReqData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncJddjStoreStatusRequestReqData.Marshal(b, m, deterministic)
}
func (m *SyncJddjStoreStatusRequestReqData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncJddjStoreStatusRequestReqData.Merge(m, src)
}
func (m *SyncJddjStoreStatusRequestReqData) XXX_Size() int {
	return xxx_messageInfo_SyncJddjStoreStatusRequestReqData.Size(m)
}
func (m *SyncJddjStoreStatusRequestReqData) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncJddjStoreStatusRequestReqData.DiscardUnknown(m)
}

var xxx_messageInfo_SyncJddjStoreStatusRequestReqData proto.InternalMessageInfo

func (m *SyncJddjStoreStatusRequestReqData) GetBaiduShopId() string {
	if m != nil {
		return m.BaiduShopId
	}
	return ""
}

func (m *SyncJddjStoreStatusRequestReqData) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *SyncJddjStoreStatusRequestReqData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SyncJddjStoreStatusRequestReqData) GetAppChannel() int32 {
	if m != nil {
		return m.AppChannel
	}
	return 0
}

type SyncJddjStoreStatusResponse struct {
	Data                 []*SyncJddjStoreStatusResponseOutData `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *SyncJddjStoreStatusResponse) Reset()         { *m = SyncJddjStoreStatusResponse{} }
func (m *SyncJddjStoreStatusResponse) String() string { return proto.CompactTextString(m) }
func (*SyncJddjStoreStatusResponse) ProtoMessage()    {}
func (*SyncJddjStoreStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{5}
}

func (m *SyncJddjStoreStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncJddjStoreStatusResponse.Unmarshal(m, b)
}
func (m *SyncJddjStoreStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncJddjStoreStatusResponse.Marshal(b, m, deterministic)
}
func (m *SyncJddjStoreStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncJddjStoreStatusResponse.Merge(m, src)
}
func (m *SyncJddjStoreStatusResponse) XXX_Size() int {
	return xxx_messageInfo_SyncJddjStoreStatusResponse.Size(m)
}
func (m *SyncJddjStoreStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncJddjStoreStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SyncJddjStoreStatusResponse proto.InternalMessageInfo

func (m *SyncJddjStoreStatusResponse) GetData() []*SyncJddjStoreStatusResponseOutData {
	if m != nil {
		return m.Data
	}
	return nil
}

type SyncJddjStoreStatusResponseOutData struct {
	//门店ID
	ShopId string `protobuf:"bytes,1,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	// 状态,1-成功
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	// 错误信息
	Msg                  string   `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SyncJddjStoreStatusResponseOutData) Reset()         { *m = SyncJddjStoreStatusResponseOutData{} }
func (m *SyncJddjStoreStatusResponseOutData) String() string { return proto.CompactTextString(m) }
func (*SyncJddjStoreStatusResponseOutData) ProtoMessage()    {}
func (*SyncJddjStoreStatusResponseOutData) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{5, 0}
}

func (m *SyncJddjStoreStatusResponseOutData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncJddjStoreStatusResponseOutData.Unmarshal(m, b)
}
func (m *SyncJddjStoreStatusResponseOutData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncJddjStoreStatusResponseOutData.Marshal(b, m, deterministic)
}
func (m *SyncJddjStoreStatusResponseOutData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncJddjStoreStatusResponseOutData.Merge(m, src)
}
func (m *SyncJddjStoreStatusResponseOutData) XXX_Size() int {
	return xxx_messageInfo_SyncJddjStoreStatusResponseOutData.Size(m)
}
func (m *SyncJddjStoreStatusResponseOutData) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncJddjStoreStatusResponseOutData.DiscardUnknown(m)
}

var xxx_messageInfo_SyncJddjStoreStatusResponseOutData proto.InternalMessageInfo

func (m *SyncJddjStoreStatusResponseOutData) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *SyncJddjStoreStatusResponseOutData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SyncJddjStoreStatusResponseOutData) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type UpdateStockRequest struct {
	//商家商品编码
	OutStationNo string `protobuf:"bytes,1,opt,name=outStationNo,proto3" json:"outStationNo"`
	//京东门店编号
	StationNo string `protobuf:"bytes,2,opt,name=stationNo,proto3" json:"stationNo"`
	//操作人
	UserPin string `protobuf:"bytes,3,opt,name=userPin,proto3" json:"userPin"`
	//
	SkuStockList []*SkuStockList `protobuf:"bytes,4,rep,name=skuStockList,proto3" json:"skuStockList"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,5,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStockRequest) Reset()         { *m = UpdateStockRequest{} }
func (m *UpdateStockRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateStockRequest) ProtoMessage()    {}
func (*UpdateStockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{6}
}

func (m *UpdateStockRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStockRequest.Unmarshal(m, b)
}
func (m *UpdateStockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStockRequest.Marshal(b, m, deterministic)
}
func (m *UpdateStockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStockRequest.Merge(m, src)
}
func (m *UpdateStockRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateStockRequest.Size(m)
}
func (m *UpdateStockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStockRequest proto.InternalMessageInfo

func (m *UpdateStockRequest) GetOutStationNo() string {
	if m != nil {
		return m.OutStationNo
	}
	return ""
}

func (m *UpdateStockRequest) GetStationNo() string {
	if m != nil {
		return m.StationNo
	}
	return ""
}

func (m *UpdateStockRequest) GetUserPin() string {
	if m != nil {
		return m.UserPin
	}
	return ""
}

func (m *UpdateStockRequest) GetSkuStockList() []*SkuStockList {
	if m != nil {
		return m.SkuStockList
	}
	return nil
}

func (m *UpdateStockRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type UpdateStockOneRequest struct {
	//商家商品编码
	SkuId string `protobuf:"bytes,1,opt,name=skuId,proto3" json:"skuId"`
	//京东门店编号
	StationNo string `protobuf:"bytes,2,opt,name=stationNo,proto3" json:"stationNo"`
	// 库存数
	CurrentQty int32 `protobuf:"varint,3,opt,name=currentQty,proto3" json:"currentQty"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,4,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStockOneRequest) Reset()         { *m = UpdateStockOneRequest{} }
func (m *UpdateStockOneRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateStockOneRequest) ProtoMessage()    {}
func (*UpdateStockOneRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{7}
}

func (m *UpdateStockOneRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStockOneRequest.Unmarshal(m, b)
}
func (m *UpdateStockOneRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStockOneRequest.Marshal(b, m, deterministic)
}
func (m *UpdateStockOneRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStockOneRequest.Merge(m, src)
}
func (m *UpdateStockOneRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateStockOneRequest.Size(m)
}
func (m *UpdateStockOneRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStockOneRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStockOneRequest proto.InternalMessageInfo

func (m *UpdateStockOneRequest) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *UpdateStockOneRequest) GetStationNo() string {
	if m != nil {
		return m.StationNo
	}
	return ""
}

func (m *UpdateStockOneRequest) GetCurrentQty() int32 {
	if m != nil {
		return m.CurrentQty
	}
	return 0
}

func (m *UpdateStockOneRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type SkuStockList struct {
	OutSkuId             string   `protobuf:"bytes,1,opt,name=outSkuId,proto3" json:"outSkuId"`
	StockQty             int32    `protobuf:"varint,2,opt,name=stockQty,proto3" json:"stockQty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuStockList) Reset()         { *m = SkuStockList{} }
func (m *SkuStockList) String() string { return proto.CompactTextString(m) }
func (*SkuStockList) ProtoMessage()    {}
func (*SkuStockList) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{8}
}

func (m *SkuStockList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuStockList.Unmarshal(m, b)
}
func (m *SkuStockList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuStockList.Marshal(b, m, deterministic)
}
func (m *SkuStockList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuStockList.Merge(m, src)
}
func (m *SkuStockList) XXX_Size() int {
	return xxx_messageInfo_SkuStockList.Size(m)
}
func (m *SkuStockList) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuStockList.DiscardUnknown(m)
}

var xxx_messageInfo_SkuStockList proto.InternalMessageInfo

func (m *SkuStockList) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *SkuStockList) GetStockQty() int32 {
	if m != nil {
		return m.StockQty
	}
	return 0
}

type BatchUpdateVendibilityRequest struct {
	//商家门店编码(outerStationNo和stationNo不能同时为空)
	OutStationNo string `protobuf:"bytes,1,opt,name=outStationNo,proto3" json:"outStationNo"`
	//到家门店编码
	StationNo string `protobuf:"bytes,2,opt,name=stationNo,proto3" json:"stationNo"`
	//参数实体列表，单次最多50个商品
	StockVendibilityList []*JddjStockVendibility `protobuf:"bytes,3,rep,name=stockVendibilityList,proto3" json:"stockVendibilityList"`
	//操作人
	UserPin string `protobuf:"bytes,4,opt,name=userPin,proto3" json:"userPin"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,5,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateVendibilityRequest) Reset()         { *m = BatchUpdateVendibilityRequest{} }
func (m *BatchUpdateVendibilityRequest) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateVendibilityRequest) ProtoMessage()    {}
func (*BatchUpdateVendibilityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{9}
}

func (m *BatchUpdateVendibilityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateVendibilityRequest.Unmarshal(m, b)
}
func (m *BatchUpdateVendibilityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateVendibilityRequest.Marshal(b, m, deterministic)
}
func (m *BatchUpdateVendibilityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateVendibilityRequest.Merge(m, src)
}
func (m *BatchUpdateVendibilityRequest) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateVendibilityRequest.Size(m)
}
func (m *BatchUpdateVendibilityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateVendibilityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateVendibilityRequest proto.InternalMessageInfo

func (m *BatchUpdateVendibilityRequest) GetOutStationNo() string {
	if m != nil {
		return m.OutStationNo
	}
	return ""
}

func (m *BatchUpdateVendibilityRequest) GetStationNo() string {
	if m != nil {
		return m.StationNo
	}
	return ""
}

func (m *BatchUpdateVendibilityRequest) GetStockVendibilityList() []*JddjStockVendibility {
	if m != nil {
		return m.StockVendibilityList
	}
	return nil
}

func (m *BatchUpdateVendibilityRequest) GetUserPin() string {
	if m != nil {
		return m.UserPin
	}
	return ""
}

func (m *BatchUpdateVendibilityRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjStockVendibility struct {
	//商家商品编码
	OutSkuId string `protobuf:"bytes,1,opt,name=outSkuId,proto3" json:"outSkuId"`
	//可售状态（true:可售，false:不可售）
	DoSale               bool     `protobuf:"varint,2,opt,name=doSale,proto3" json:"doSale"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjStockVendibility) Reset()         { *m = JddjStockVendibility{} }
func (m *JddjStockVendibility) String() string { return proto.CompactTextString(m) }
func (*JddjStockVendibility) ProtoMessage()    {}
func (*JddjStockVendibility) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{10}
}

func (m *JddjStockVendibility) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjStockVendibility.Unmarshal(m, b)
}
func (m *JddjStockVendibility) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjStockVendibility.Marshal(b, m, deterministic)
}
func (m *JddjStockVendibility) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjStockVendibility.Merge(m, src)
}
func (m *JddjStockVendibility) XXX_Size() int {
	return xxx_messageInfo_JddjStockVendibility.Size(m)
}
func (m *JddjStockVendibility) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjStockVendibility.DiscardUnknown(m)
}

var xxx_messageInfo_JddjStockVendibility proto.InternalMessageInfo

func (m *JddjStockVendibility) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *JddjStockVendibility) GetDoSale() bool {
	if m != nil {
		return m.DoSale
	}
	return false
}

type BatchUpdateVendibilityResponse struct {
	//状态
	Ret bool `protobuf:"varint,1,opt,name=ret,proto3" json:"ret"`
	//编码
	RetCode string `protobuf:"bytes,2,opt,name=retCode,proto3" json:"retCode"`
	//成功或失败信息
	RetMsg               string                       `protobuf:"bytes,3,opt,name=retMsg,proto3" json:"retMsg"`
	Data                 []*UpdateVendibilityResponse `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchUpdateVendibilityResponse) Reset()         { *m = BatchUpdateVendibilityResponse{} }
func (m *BatchUpdateVendibilityResponse) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateVendibilityResponse) ProtoMessage()    {}
func (*BatchUpdateVendibilityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{11}
}

func (m *BatchUpdateVendibilityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateVendibilityResponse.Unmarshal(m, b)
}
func (m *BatchUpdateVendibilityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateVendibilityResponse.Marshal(b, m, deterministic)
}
func (m *BatchUpdateVendibilityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateVendibilityResponse.Merge(m, src)
}
func (m *BatchUpdateVendibilityResponse) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateVendibilityResponse.Size(m)
}
func (m *BatchUpdateVendibilityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateVendibilityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateVendibilityResponse proto.InternalMessageInfo

func (m *BatchUpdateVendibilityResponse) GetRet() bool {
	if m != nil {
		return m.Ret
	}
	return false
}

func (m *BatchUpdateVendibilityResponse) GetRetCode() string {
	if m != nil {
		return m.RetCode
	}
	return ""
}

func (m *BatchUpdateVendibilityResponse) GetRetMsg() string {
	if m != nil {
		return m.RetMsg
	}
	return ""
}

func (m *BatchUpdateVendibilityResponse) GetData() []*UpdateVendibilityResponse {
	if m != nil {
		return m.Data
	}
	return nil
}

type UpdateVendibilityResponse struct {
	//0：修改成功，1：修改时发生错误，2：修改失败，回滚失败，3：修改失败，回滚成功
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//操作描述信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	//商家商品编码
	OutSkuId             string   `protobuf:"bytes,3,opt,name=outSkuId,proto3" json:"outSkuId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateVendibilityResponse) Reset()         { *m = UpdateVendibilityResponse{} }
func (m *UpdateVendibilityResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateVendibilityResponse) ProtoMessage()    {}
func (*UpdateVendibilityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{12}
}

func (m *UpdateVendibilityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateVendibilityResponse.Unmarshal(m, b)
}
func (m *UpdateVendibilityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateVendibilityResponse.Marshal(b, m, deterministic)
}
func (m *UpdateVendibilityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateVendibilityResponse.Merge(m, src)
}
func (m *UpdateVendibilityResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateVendibilityResponse.Size(m)
}
func (m *UpdateVendibilityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateVendibilityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateVendibilityResponse proto.InternalMessageInfo

func (m *UpdateVendibilityResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdateVendibilityResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *UpdateVendibilityResponse) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

type VerificationUpdateTokenRequest struct {
	//旧Token
	OldToken string `protobuf:"bytes,1,opt,name=oldToken,proto3" json:"oldToken"`
	//新Token
	NewToken string `protobuf:"bytes,2,opt,name=newToken,proto3" json:"newToken"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VerificationUpdateTokenRequest) Reset()         { *m = VerificationUpdateTokenRequest{} }
func (m *VerificationUpdateTokenRequest) String() string { return proto.CompactTextString(m) }
func (*VerificationUpdateTokenRequest) ProtoMessage()    {}
func (*VerificationUpdateTokenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{13}
}

func (m *VerificationUpdateTokenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VerificationUpdateTokenRequest.Unmarshal(m, b)
}
func (m *VerificationUpdateTokenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VerificationUpdateTokenRequest.Marshal(b, m, deterministic)
}
func (m *VerificationUpdateTokenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerificationUpdateTokenRequest.Merge(m, src)
}
func (m *VerificationUpdateTokenRequest) XXX_Size() int {
	return xxx_messageInfo_VerificationUpdateTokenRequest.Size(m)
}
func (m *VerificationUpdateTokenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VerificationUpdateTokenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VerificationUpdateTokenRequest proto.InternalMessageInfo

func (m *VerificationUpdateTokenRequest) GetOldToken() string {
	if m != nil {
		return m.OldToken
	}
	return ""
}

func (m *VerificationUpdateTokenRequest) GetNewToken() string {
	if m != nil {
		return m.NewToken
	}
	return ""
}

func (m *VerificationUpdateTokenRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 商家审核用户取消申请（订单未完成取消订单）参数
type JddjOrderCancelOperateRequest struct {
	// 订单号
	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	// 操作类型 true同意 false驳回
	IsAgreed bool `protobuf:"varint,2,opt,name=isAgreed,proto3" json:"isAgreed"`
	// 操作人
	Operator string `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator"`
	// 取消备注	操作备注(isAgreed=false时此字段必填，isAgreed=true时可不填)
	Remark string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,5,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOrderCancelOperateRequest) Reset()         { *m = JddjOrderCancelOperateRequest{} }
func (m *JddjOrderCancelOperateRequest) String() string { return proto.CompactTextString(m) }
func (*JddjOrderCancelOperateRequest) ProtoMessage()    {}
func (*JddjOrderCancelOperateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{14}
}

func (m *JddjOrderCancelOperateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderCancelOperateRequest.Unmarshal(m, b)
}
func (m *JddjOrderCancelOperateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderCancelOperateRequest.Marshal(b, m, deterministic)
}
func (m *JddjOrderCancelOperateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderCancelOperateRequest.Merge(m, src)
}
func (m *JddjOrderCancelOperateRequest) XXX_Size() int {
	return xxx_messageInfo_JddjOrderCancelOperateRequest.Size(m)
}
func (m *JddjOrderCancelOperateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderCancelOperateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderCancelOperateRequest proto.InternalMessageInfo

func (m *JddjOrderCancelOperateRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JddjOrderCancelOperateRequest) GetIsAgreed() bool {
	if m != nil {
		return m.IsAgreed
	}
	return false
}

func (m *JddjOrderCancelOperateRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *JddjOrderCancelOperateRequest) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *JddjOrderCancelOperateRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 申请售后单审核接口（订单完成发起的售后单）
type AfsOpenApproveRequest struct {
	// 售后服务单号
	ServiceOrder string `protobuf:"bytes,1,opt,name=serviceOrder,proto3" json:"serviceOrder"`
	// 审核结果类型（1：退款 2：退货 3：驳回）
	ApproveType int32 `protobuf:"varint,2,opt,name=approveType,proto3" json:"approveType"`
	// 驳回原因(审核为驳回时必须，审核为退货或退款时不传)
	RejectReason string `protobuf:"bytes,3,opt,name=rejectReason,proto3" json:"rejectReason"`
	// 操作人
	OptPin string `protobuf:"bytes,4,opt,name=optPin,proto3" json:"optPin"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,5,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AfsOpenApproveRequest) Reset()         { *m = AfsOpenApproveRequest{} }
func (m *AfsOpenApproveRequest) String() string { return proto.CompactTextString(m) }
func (*AfsOpenApproveRequest) ProtoMessage()    {}
func (*AfsOpenApproveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{15}
}

func (m *AfsOpenApproveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AfsOpenApproveRequest.Unmarshal(m, b)
}
func (m *AfsOpenApproveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AfsOpenApproveRequest.Marshal(b, m, deterministic)
}
func (m *AfsOpenApproveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AfsOpenApproveRequest.Merge(m, src)
}
func (m *AfsOpenApproveRequest) XXX_Size() int {
	return xxx_messageInfo_AfsOpenApproveRequest.Size(m)
}
func (m *AfsOpenApproveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AfsOpenApproveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AfsOpenApproveRequest proto.InternalMessageInfo

func (m *AfsOpenApproveRequest) GetServiceOrder() string {
	if m != nil {
		return m.ServiceOrder
	}
	return ""
}

func (m *AfsOpenApproveRequest) GetApproveType() int32 {
	if m != nil {
		return m.ApproveType
	}
	return 0
}

func (m *AfsOpenApproveRequest) GetRejectReason() string {
	if m != nil {
		return m.RejectReason
	}
	return ""
}

func (m *AfsOpenApproveRequest) GetOptPin() string {
	if m != nil {
		return m.OptPin
	}
	return ""
}

func (m *AfsOpenApproveRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjCategoryInfo struct {
	//类目ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//父类目ID(一级类目ID为0)
	Pid int32 `protobuf:"varint,2,opt,name=pid,proto3" json:"pid"`
	//类目名称
	CategoryName string `protobuf:"bytes,3,opt,name=categoryName,proto3" json:"categoryName"`
	//类目等级(0：一级分类，1：二级分类，2：三级分类)
	CategoryLevel int32 `protobuf:"varint,4,opt,name=categoryLevel,proto3" json:"categoryLevel"`
	//类目状态(0：禁用，1：启用)
	CategoryStatus int32 `protobuf:"varint,5,opt,name=categoryStatus,proto3" json:"categoryStatus"`
	//类目层级映射关系
	FullPath string `protobuf:"bytes,6,opt,name=fullPath,proto3" json:"fullPath"`
	// 子分类
	Child []*JddjCategoryInfo `protobuf:"bytes,7,rep,name=child,proto3" json:"child"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,8,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjCategoryInfo) Reset()         { *m = JddjCategoryInfo{} }
func (m *JddjCategoryInfo) String() string { return proto.CompactTextString(m) }
func (*JddjCategoryInfo) ProtoMessage()    {}
func (*JddjCategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{16}
}

func (m *JddjCategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjCategoryInfo.Unmarshal(m, b)
}
func (m *JddjCategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjCategoryInfo.Marshal(b, m, deterministic)
}
func (m *JddjCategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjCategoryInfo.Merge(m, src)
}
func (m *JddjCategoryInfo) XXX_Size() int {
	return xxx_messageInfo_JddjCategoryInfo.Size(m)
}
func (m *JddjCategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjCategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_JddjCategoryInfo proto.InternalMessageInfo

func (m *JddjCategoryInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *JddjCategoryInfo) GetPid() int32 {
	if m != nil {
		return m.Pid
	}
	return 0
}

func (m *JddjCategoryInfo) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *JddjCategoryInfo) GetCategoryLevel() int32 {
	if m != nil {
		return m.CategoryLevel
	}
	return 0
}

func (m *JddjCategoryInfo) GetCategoryStatus() int32 {
	if m != nil {
		return m.CategoryStatus
	}
	return 0
}

func (m *JddjCategoryInfo) GetFullPath() string {
	if m != nil {
		return m.FullPath
	}
	return ""
}

func (m *JddjCategoryInfo) GetChild() []*JddjCategoryInfo {
	if m != nil {
		return m.Child
	}
	return nil
}

func (m *JddjCategoryInfo) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 订单取消请求参数
type JddjOrderCancelRequest struct {
	// 订单号
	OrderId int64 `protobuf:"varint,1,opt,name=orderId,proto3" json:"orderId"`
	// 操作人
	OperPin string `protobuf:"bytes,2,opt,name=operPin,proto3" json:"operPin"`
	// 操作备注
	OperRemark string `protobuf:"bytes,3,opt,name=operRemark,proto3" json:"operRemark"`
	// 操作时间
	OperTime string `protobuf:"bytes,4,opt,name=operTime,proto3" json:"operTime"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,5,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOrderCancelRequest) Reset()         { *m = JddjOrderCancelRequest{} }
func (m *JddjOrderCancelRequest) String() string { return proto.CompactTextString(m) }
func (*JddjOrderCancelRequest) ProtoMessage()    {}
func (*JddjOrderCancelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{17}
}

func (m *JddjOrderCancelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderCancelRequest.Unmarshal(m, b)
}
func (m *JddjOrderCancelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderCancelRequest.Marshal(b, m, deterministic)
}
func (m *JddjOrderCancelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderCancelRequest.Merge(m, src)
}
func (m *JddjOrderCancelRequest) XXX_Size() int {
	return xxx_messageInfo_JddjOrderCancelRequest.Size(m)
}
func (m *JddjOrderCancelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderCancelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderCancelRequest proto.InternalMessageInfo

func (m *JddjOrderCancelRequest) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *JddjOrderCancelRequest) GetOperPin() string {
	if m != nil {
		return m.OperPin
	}
	return ""
}

func (m *JddjOrderCancelRequest) GetOperRemark() string {
	if m != nil {
		return m.OperRemark
	}
	return ""
}

func (m *JddjOrderCancelRequest) GetOperTime() string {
	if m != nil {
		return m.OperTime
	}
	return ""
}

func (m *JddjOrderCancelRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 订单自提码核验参数
type JddjCheckSelfPickCodeRequest struct {
	// 自提码
	SelfPickCode string `protobuf:"bytes,1,opt,name=selfPickCode,proto3" json:"selfPickCode"`
	// 订单号
	OrderId string `protobuf:"bytes,2,opt,name=orderId,proto3" json:"orderId"`
	// 操作人
	OperPin string `protobuf:"bytes,3,opt,name=operPin,proto3" json:"operPin"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,4,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjCheckSelfPickCodeRequest) Reset()         { *m = JddjCheckSelfPickCodeRequest{} }
func (m *JddjCheckSelfPickCodeRequest) String() string { return proto.CompactTextString(m) }
func (*JddjCheckSelfPickCodeRequest) ProtoMessage()    {}
func (*JddjCheckSelfPickCodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{18}
}

func (m *JddjCheckSelfPickCodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjCheckSelfPickCodeRequest.Unmarshal(m, b)
}
func (m *JddjCheckSelfPickCodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjCheckSelfPickCodeRequest.Marshal(b, m, deterministic)
}
func (m *JddjCheckSelfPickCodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjCheckSelfPickCodeRequest.Merge(m, src)
}
func (m *JddjCheckSelfPickCodeRequest) XXX_Size() int {
	return xxx_messageInfo_JddjCheckSelfPickCodeRequest.Size(m)
}
func (m *JddjCheckSelfPickCodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjCheckSelfPickCodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjCheckSelfPickCodeRequest proto.InternalMessageInfo

func (m *JddjCheckSelfPickCodeRequest) GetSelfPickCode() string {
	if m != nil {
		return m.SelfPickCode
	}
	return ""
}

func (m *JddjCheckSelfPickCodeRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JddjCheckSelfPickCodeRequest) GetOperPin() string {
	if m != nil {
		return m.OperPin
	}
	return ""
}

func (m *JddjCheckSelfPickCodeRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 拣货完成且顾客自提接口
type JddjOrderSelfMentionRequest struct {
	// 订单号
	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	// 操作人
	Operator string `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOrderSelfMentionRequest) Reset()         { *m = JddjOrderSelfMentionRequest{} }
func (m *JddjOrderSelfMentionRequest) String() string { return proto.CompactTextString(m) }
func (*JddjOrderSelfMentionRequest) ProtoMessage()    {}
func (*JddjOrderSelfMentionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{19}
}

func (m *JddjOrderSelfMentionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderSelfMentionRequest.Unmarshal(m, b)
}
func (m *JddjOrderSelfMentionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderSelfMentionRequest.Marshal(b, m, deterministic)
}
func (m *JddjOrderSelfMentionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderSelfMentionRequest.Merge(m, src)
}
func (m *JddjOrderSelfMentionRequest) XXX_Size() int {
	return xxx_messageInfo_JddjOrderSelfMentionRequest.Size(m)
}
func (m *JddjOrderSelfMentionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderSelfMentionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderSelfMentionRequest proto.InternalMessageInfo

func (m *JddjOrderSelfMentionRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JddjOrderSelfMentionRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *JddjOrderSelfMentionRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 查询售后单详情接口
type JddjAfsServiceRequest struct {
	// 售后单号
	AfsServiceOrder string `protobuf:"bytes,1,opt,name=afsServiceOrder,proto3" json:"afsServiceOrder"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,2,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjAfsServiceRequest) Reset()         { *m = JddjAfsServiceRequest{} }
func (m *JddjAfsServiceRequest) String() string { return proto.CompactTextString(m) }
func (*JddjAfsServiceRequest) ProtoMessage()    {}
func (*JddjAfsServiceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{20}
}

func (m *JddjAfsServiceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjAfsServiceRequest.Unmarshal(m, b)
}
func (m *JddjAfsServiceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjAfsServiceRequest.Marshal(b, m, deterministic)
}
func (m *JddjAfsServiceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjAfsServiceRequest.Merge(m, src)
}
func (m *JddjAfsServiceRequest) XXX_Size() int {
	return xxx_messageInfo_JddjAfsServiceRequest.Size(m)
}
func (m *JddjAfsServiceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjAfsServiceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjAfsServiceRequest proto.InternalMessageInfo

func (m *JddjAfsServiceRequest) GetAfsServiceOrder() string {
	if m != nil {
		return m.AfsServiceOrder
	}
	return ""
}

func (m *JddjAfsServiceRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 售后单确认收货接口
type JddjConfirmReceiptRequest struct {
	// 售后单号
	AfsServiceOrder string `protobuf:"bytes,1,opt,name=afsServiceOrder,proto3" json:"afsServiceOrder"`
	// 操作人
	Pin string `protobuf:"bytes,2,opt,name=pin,proto3" json:"pin"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjConfirmReceiptRequest) Reset()         { *m = JddjConfirmReceiptRequest{} }
func (m *JddjConfirmReceiptRequest) String() string { return proto.CompactTextString(m) }
func (*JddjConfirmReceiptRequest) ProtoMessage()    {}
func (*JddjConfirmReceiptRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{21}
}

func (m *JddjConfirmReceiptRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjConfirmReceiptRequest.Unmarshal(m, b)
}
func (m *JddjConfirmReceiptRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjConfirmReceiptRequest.Marshal(b, m, deterministic)
}
func (m *JddjConfirmReceiptRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjConfirmReceiptRequest.Merge(m, src)
}
func (m *JddjConfirmReceiptRequest) XXX_Size() int {
	return xxx_messageInfo_JddjConfirmReceiptRequest.Size(m)
}
func (m *JddjConfirmReceiptRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjConfirmReceiptRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjConfirmReceiptRequest proto.InternalMessageInfo

func (m *JddjConfirmReceiptRequest) GetAfsServiceOrder() string {
	if m != nil {
		return m.AfsServiceOrder
	}
	return ""
}

func (m *JddjConfirmReceiptRequest) GetPin() string {
	if m != nil {
		return m.Pin
	}
	return ""
}

func (m *JddjConfirmReceiptRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 商家自主发起售后接口
type JddjMerchantInitiateAfterSaleRequest struct {
	//订单号
	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	//操作人pin
	Pin string `protobuf:"bytes,2,opt,name=pin,proto3" json:"pin"`
	//售后原因id（201:商品质量问题，202:送错货，203:缺件少件，501:全部商品未收到，208:包装脏污有破损，207:缺斤少两，210:商家通知我缺货，303:实物与原图不符，502:未在时效内送达）
	QuestionTypeCode string `protobuf:"bytes,3,opt,name=questionTypeCode,proto3" json:"questionTypeCode"`
	//问题描述
	QuestionDesc string `protobuf:"bytes,4,opt,name=questionDesc,proto3" json:"questionDesc"`
	//上传图片url逗号隔开
	QuestionPic string `protobuf:"bytes,5,opt,name=questionPic,proto3" json:"questionPic"`
	//客户名称
	CustomerName string `protobuf:"bytes,6,opt,name=customerName,proto3" json:"customerName"`
	//客户电话
	CustomerMobilePhone string `protobuf:"bytes,7,opt,name=customerMobilePhone,proto3" json:"customerMobilePhone"`
	//客户详细地址
	Address string `protobuf:"bytes,8,opt,name=address,proto3" json:"address"`
	// 商品信息集合
	SkuList []*JddVenderAfsSkuDto `protobuf:"bytes,9,rep,name=skuList,proto3" json:"skuList"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,10,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjMerchantInitiateAfterSaleRequest) Reset()         { *m = JddjMerchantInitiateAfterSaleRequest{} }
func (m *JddjMerchantInitiateAfterSaleRequest) String() string { return proto.CompactTextString(m) }
func (*JddjMerchantInitiateAfterSaleRequest) ProtoMessage()    {}
func (*JddjMerchantInitiateAfterSaleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{22}
}

func (m *JddjMerchantInitiateAfterSaleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjMerchantInitiateAfterSaleRequest.Unmarshal(m, b)
}
func (m *JddjMerchantInitiateAfterSaleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjMerchantInitiateAfterSaleRequest.Marshal(b, m, deterministic)
}
func (m *JddjMerchantInitiateAfterSaleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjMerchantInitiateAfterSaleRequest.Merge(m, src)
}
func (m *JddjMerchantInitiateAfterSaleRequest) XXX_Size() int {
	return xxx_messageInfo_JddjMerchantInitiateAfterSaleRequest.Size(m)
}
func (m *JddjMerchantInitiateAfterSaleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjMerchantInitiateAfterSaleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjMerchantInitiateAfterSaleRequest proto.InternalMessageInfo

func (m *JddjMerchantInitiateAfterSaleRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JddjMerchantInitiateAfterSaleRequest) GetPin() string {
	if m != nil {
		return m.Pin
	}
	return ""
}

func (m *JddjMerchantInitiateAfterSaleRequest) GetQuestionTypeCode() string {
	if m != nil {
		return m.QuestionTypeCode
	}
	return ""
}

func (m *JddjMerchantInitiateAfterSaleRequest) GetQuestionDesc() string {
	if m != nil {
		return m.QuestionDesc
	}
	return ""
}

func (m *JddjMerchantInitiateAfterSaleRequest) GetQuestionPic() string {
	if m != nil {
		return m.QuestionPic
	}
	return ""
}

func (m *JddjMerchantInitiateAfterSaleRequest) GetCustomerName() string {
	if m != nil {
		return m.CustomerName
	}
	return ""
}

func (m *JddjMerchantInitiateAfterSaleRequest) GetCustomerMobilePhone() string {
	if m != nil {
		return m.CustomerMobilePhone
	}
	return ""
}

func (m *JddjMerchantInitiateAfterSaleRequest) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *JddjMerchantInitiateAfterSaleRequest) GetSkuList() []*JddVenderAfsSkuDto {
	if m != nil {
		return m.SkuList
	}
	return nil
}

func (m *JddjMerchantInitiateAfterSaleRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 商家自主发起售后接口（商品信息集合）
type JddVenderAfsSkuDto struct {
	// 到家商品id
	SkuId int64 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	// 商品数量
	SkuCount int32 `protobuf:"varint,2,opt,name=skuCount,proto3" json:"skuCount"`
	// 商品促销类型（1正品，2秒杀促销，3单品直降促销，4限时抢购促销，6买赠，7新人专享，8第二件N折，9拼团购，1202加价购促销，1203满赠促销，8001轻松购会员价，9996组合购,9997捆绑除不尽的类型，9998捆绑参与其他单品，9999捆绑，10009单品预售，9000称重品最小促销类型，9040称重品会员价起始类型，9050称重品最大促销类型）
	PromotionType        int32    `protobuf:"varint,3,opt,name=promotionType,proto3" json:"promotionType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddVenderAfsSkuDto) Reset()         { *m = JddVenderAfsSkuDto{} }
func (m *JddVenderAfsSkuDto) String() string { return proto.CompactTextString(m) }
func (*JddVenderAfsSkuDto) ProtoMessage()    {}
func (*JddVenderAfsSkuDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{23}
}

func (m *JddVenderAfsSkuDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddVenderAfsSkuDto.Unmarshal(m, b)
}
func (m *JddVenderAfsSkuDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddVenderAfsSkuDto.Marshal(b, m, deterministic)
}
func (m *JddVenderAfsSkuDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddVenderAfsSkuDto.Merge(m, src)
}
func (m *JddVenderAfsSkuDto) XXX_Size() int {
	return xxx_messageInfo_JddVenderAfsSkuDto.Size(m)
}
func (m *JddVenderAfsSkuDto) XXX_DiscardUnknown() {
	xxx_messageInfo_JddVenderAfsSkuDto.DiscardUnknown(m)
}

var xxx_messageInfo_JddVenderAfsSkuDto proto.InternalMessageInfo

func (m *JddVenderAfsSkuDto) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *JddVenderAfsSkuDto) GetSkuCount() int32 {
	if m != nil {
		return m.SkuCount
	}
	return 0
}

func (m *JddVenderAfsSkuDto) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

// 查询订单可售后商品金额接口
type JddjOrderCalcMoneyRequest struct {
	// 订单ID
	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	// 商品信息集合
	SkuList []*JddVenderAfsSkuDto `protobuf:"bytes,2,rep,name=skuList,proto3" json:"skuList"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOrderCalcMoneyRequest) Reset()         { *m = JddjOrderCalcMoneyRequest{} }
func (m *JddjOrderCalcMoneyRequest) String() string { return proto.CompactTextString(m) }
func (*JddjOrderCalcMoneyRequest) ProtoMessage()    {}
func (*JddjOrderCalcMoneyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{24}
}

func (m *JddjOrderCalcMoneyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderCalcMoneyRequest.Unmarshal(m, b)
}
func (m *JddjOrderCalcMoneyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderCalcMoneyRequest.Marshal(b, m, deterministic)
}
func (m *JddjOrderCalcMoneyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderCalcMoneyRequest.Merge(m, src)
}
func (m *JddjOrderCalcMoneyRequest) XXX_Size() int {
	return xxx_messageInfo_JddjOrderCalcMoneyRequest.Size(m)
}
func (m *JddjOrderCalcMoneyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderCalcMoneyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderCalcMoneyRequest proto.InternalMessageInfo

func (m *JddjOrderCalcMoneyRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JddjOrderCalcMoneyRequest) GetSkuList() []*JddVenderAfsSkuDto {
	if m != nil {
		return m.SkuList
	}
	return nil
}

func (m *JddjOrderCalcMoneyRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

// 基础响应结果
type JddjBaseResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 结果描述
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 返回值
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjBaseResponse) Reset()         { *m = JddjBaseResponse{} }
func (m *JddjBaseResponse) String() string { return proto.CompactTextString(m) }
func (*JddjBaseResponse) ProtoMessage()    {}
func (*JddjBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{25}
}

func (m *JddjBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjBaseResponse.Unmarshal(m, b)
}
func (m *JddjBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjBaseResponse.Marshal(b, m, deterministic)
}
func (m *JddjBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjBaseResponse.Merge(m, src)
}
func (m *JddjBaseResponse) XXX_Size() int {
	return xxx_messageInfo_JddjBaseResponse.Size(m)
}
func (m *JddjBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjBaseResponse proto.InternalMessageInfo

func (m *JddjBaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *JddjBaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *JddjBaseResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type UpdateStationPriceRequest struct {
	//商家门店编码
	OutStationNo string `protobuf:"bytes,1,opt,name=outStationNo,proto3" json:"outStationNo"`
	//到家门店编码
	StationNo string `protobuf:"bytes,2,opt,name=stationNo,proto3" json:"stationNo"`
	//商品信息，最多50条
	SkuPriceInfoList []*JddjSkuPriceInfo `protobuf:"bytes,3,rep,name=skuPriceInfoList,proto3" json:"skuPriceInfoList"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,4,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStationPriceRequest) Reset()         { *m = UpdateStationPriceRequest{} }
func (m *UpdateStationPriceRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateStationPriceRequest) ProtoMessage()    {}
func (*UpdateStationPriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{26}
}

func (m *UpdateStationPriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStationPriceRequest.Unmarshal(m, b)
}
func (m *UpdateStationPriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStationPriceRequest.Marshal(b, m, deterministic)
}
func (m *UpdateStationPriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStationPriceRequest.Merge(m, src)
}
func (m *UpdateStationPriceRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateStationPriceRequest.Size(m)
}
func (m *UpdateStationPriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStationPriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStationPriceRequest proto.InternalMessageInfo

func (m *UpdateStationPriceRequest) GetOutStationNo() string {
	if m != nil {
		return m.OutStationNo
	}
	return ""
}

func (m *UpdateStationPriceRequest) GetStationNo() string {
	if m != nil {
		return m.StationNo
	}
	return ""
}

func (m *UpdateStationPriceRequest) GetSkuPriceInfoList() []*JddjSkuPriceInfo {
	if m != nil {
		return m.SkuPriceInfoList
	}
	return nil
}

func (m *UpdateStationPriceRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjSkuPriceInfo struct {
	//商家商品编码
	OutSkuId string `protobuf:"bytes,1,opt,name=outSkuId,proto3" json:"outSkuId"`
	//商品价格(单位 分)
	Price                int64    `protobuf:"varint,2,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjSkuPriceInfo) Reset()         { *m = JddjSkuPriceInfo{} }
func (m *JddjSkuPriceInfo) String() string { return proto.CompactTextString(m) }
func (*JddjSkuPriceInfo) ProtoMessage()    {}
func (*JddjSkuPriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{27}
}

func (m *JddjSkuPriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjSkuPriceInfo.Unmarshal(m, b)
}
func (m *JddjSkuPriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjSkuPriceInfo.Marshal(b, m, deterministic)
}
func (m *JddjSkuPriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjSkuPriceInfo.Merge(m, src)
}
func (m *JddjSkuPriceInfo) XXX_Size() int {
	return xxx_messageInfo_JddjSkuPriceInfo.Size(m)
}
func (m *JddjSkuPriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjSkuPriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_JddjSkuPriceInfo proto.InternalMessageInfo

func (m *JddjSkuPriceInfo) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *JddjSkuPriceInfo) GetPrice() int64 {
	if m != nil {
		return m.Price
	}
	return 0
}

type UpdateStationPriceResponse struct {
	//状态码
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	//返回描述信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	//结果数据，只返回更新失败的记录，价格无变化的不会更新，同时商家中心商品价格更新日志流水中也不会有相应记录。
	Result               []*UpdateStationPriceResult `protobuf:"bytes,3,rep,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *UpdateStationPriceResponse) Reset()         { *m = UpdateStationPriceResponse{} }
func (m *UpdateStationPriceResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateStationPriceResponse) ProtoMessage()    {}
func (*UpdateStationPriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{28}
}

func (m *UpdateStationPriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStationPriceResponse.Unmarshal(m, b)
}
func (m *UpdateStationPriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStationPriceResponse.Marshal(b, m, deterministic)
}
func (m *UpdateStationPriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStationPriceResponse.Merge(m, src)
}
func (m *UpdateStationPriceResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateStationPriceResponse.Size(m)
}
func (m *UpdateStationPriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStationPriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStationPriceResponse proto.InternalMessageInfo

func (m *UpdateStationPriceResponse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *UpdateStationPriceResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *UpdateStationPriceResponse) GetResult() []*UpdateStationPriceResult {
	if m != nil {
		return m.Result
	}
	return nil
}

type UpdateStationPriceResult struct {
	//错误码(190003:参数异常,190004:系统错误,190005部分处理失败！,19000C:没有对应京东到家门店,0:成功)
	ErrorCode string `protobuf:"bytes,1,opt,name=errorCode,proto3" json:"errorCode"`
	//错误信息
	ErrorMessage string `protobuf:"bytes,2,opt,name=errorMessage,proto3" json:"errorMessage"`
	//商家商品编码
	OutSkuId             string   `protobuf:"bytes,3,opt,name=outSkuId,proto3" json:"outSkuId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStationPriceResult) Reset()         { *m = UpdateStationPriceResult{} }
func (m *UpdateStationPriceResult) String() string { return proto.CompactTextString(m) }
func (*UpdateStationPriceResult) ProtoMessage()    {}
func (*UpdateStationPriceResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{29}
}

func (m *UpdateStationPriceResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStationPriceResult.Unmarshal(m, b)
}
func (m *UpdateStationPriceResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStationPriceResult.Marshal(b, m, deterministic)
}
func (m *UpdateStationPriceResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStationPriceResult.Merge(m, src)
}
func (m *UpdateStationPriceResult) XXX_Size() int {
	return xxx_messageInfo_UpdateStationPriceResult.Size(m)
}
func (m *UpdateStationPriceResult) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStationPriceResult.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStationPriceResult proto.InternalMessageInfo

func (m *UpdateStationPriceResult) GetErrorCode() string {
	if m != nil {
		return m.ErrorCode
	}
	return ""
}

func (m *UpdateStationPriceResult) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

func (m *UpdateStationPriceResult) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

type ChangeShopCategoryOrderRequest struct {
	//父级店内分类编码；如果调整一级店内分类顺序，父级分类传0即可。
	Pid int64 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid"`
	//子级店内分类集合；按照填写的分类编号顺序对分类顺序进行调整；该父分类下所有子分类都必须传入。
	ChildIds             []int64  `protobuf:"varint,2,rep,packed,name=childIds,proto3" json:"childIds"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeShopCategoryOrderRequest) Reset()         { *m = ChangeShopCategoryOrderRequest{} }
func (m *ChangeShopCategoryOrderRequest) String() string { return proto.CompactTextString(m) }
func (*ChangeShopCategoryOrderRequest) ProtoMessage()    {}
func (*ChangeShopCategoryOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{30}
}

func (m *ChangeShopCategoryOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeShopCategoryOrderRequest.Unmarshal(m, b)
}
func (m *ChangeShopCategoryOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeShopCategoryOrderRequest.Marshal(b, m, deterministic)
}
func (m *ChangeShopCategoryOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeShopCategoryOrderRequest.Merge(m, src)
}
func (m *ChangeShopCategoryOrderRequest) XXX_Size() int {
	return xxx_messageInfo_ChangeShopCategoryOrderRequest.Size(m)
}
func (m *ChangeShopCategoryOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeShopCategoryOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeShopCategoryOrderRequest proto.InternalMessageInfo

func (m *ChangeShopCategoryOrderRequest) GetPid() int64 {
	if m != nil {
		return m.Pid
	}
	return 0
}

func (m *ChangeShopCategoryOrderRequest) GetChildIds() []int64 {
	if m != nil {
		return m.ChildIds
	}
	return nil
}

type AddShopCategoryRequest struct {
	//若增加一级店内分类，其父级分类ID为0；若在已有一级店内分类下增加二级店内分类，则pid为该一级店内分类id；若在已有二级店内分类下增加三级店内分类，则pid为该二级店内分类id。
	Pid int64 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid"`
	//店内分类名称，12个字符以内
	ShopCategoryName string `protobuf:"bytes,2,opt,name=shopCategoryName,proto3" json:"shopCategoryName"`
	//店内分类等级
	ShopCategoryLevel int32 `protobuf:"varint,3,opt,name=shopCategoryLevel,proto3" json:"shopCategoryLevel"`
	//创建人
	CreatePin string `protobuf:"bytes,4,opt,name=createPin,proto3" json:"createPin"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,5,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddShopCategoryRequest) Reset()         { *m = AddShopCategoryRequest{} }
func (m *AddShopCategoryRequest) String() string { return proto.CompactTextString(m) }
func (*AddShopCategoryRequest) ProtoMessage()    {}
func (*AddShopCategoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{31}
}

func (m *AddShopCategoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddShopCategoryRequest.Unmarshal(m, b)
}
func (m *AddShopCategoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddShopCategoryRequest.Marshal(b, m, deterministic)
}
func (m *AddShopCategoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddShopCategoryRequest.Merge(m, src)
}
func (m *AddShopCategoryRequest) XXX_Size() int {
	return xxx_messageInfo_AddShopCategoryRequest.Size(m)
}
func (m *AddShopCategoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddShopCategoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddShopCategoryRequest proto.InternalMessageInfo

func (m *AddShopCategoryRequest) GetPid() int64 {
	if m != nil {
		return m.Pid
	}
	return 0
}

func (m *AddShopCategoryRequest) GetShopCategoryName() string {
	if m != nil {
		return m.ShopCategoryName
	}
	return ""
}

func (m *AddShopCategoryRequest) GetShopCategoryLevel() int32 {
	if m != nil {
		return m.ShopCategoryLevel
	}
	return 0
}

func (m *AddShopCategoryRequest) GetCreatePin() string {
	if m != nil {
		return m.CreatePin
	}
	return ""
}

func (m *AddShopCategoryRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type AddShopCategoryResponse struct {
	//店内分类编号
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddShopCategoryResponse) Reset()         { *m = AddShopCategoryResponse{} }
func (m *AddShopCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*AddShopCategoryResponse) ProtoMessage()    {}
func (*AddShopCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{32}
}

func (m *AddShopCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddShopCategoryResponse.Unmarshal(m, b)
}
func (m *AddShopCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddShopCategoryResponse.Marshal(b, m, deterministic)
}
func (m *AddShopCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddShopCategoryResponse.Merge(m, src)
}
func (m *AddShopCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_AddShopCategoryResponse.Size(m)
}
func (m *AddShopCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddShopCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddShopCategoryResponse proto.InternalMessageInfo

func (m *AddShopCategoryResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type AddSkuRequest struct {
	//请求唯一编码
	TraceId string `protobuf:"bytes,1,opt,name=traceId,proto3" json:"traceId"`
	//商家skuId编码（商家skuId）
	OutSkuId string `protobuf:"bytes,2,opt,name=outSkuId,proto3" json:"outSkuId"`
	//商家店内分类编号列表，商家店内分类分两级，一个商品可以绑定多个店内分类（上传的店内分类需为最末级分类， 即二级店内分类或没有子分类的一级店内分类），
	//店内分类编号通过查询商家店内分类信息接口获取
	ShopCategories []int64 `protobuf:"varint,3,rep,packed,name=shopCategories,proto3" json:"shopCategories"`
	//到家类目编号，需传入到家的第三级分类（通过查询到家类目信息接口获取）
	CategoryId int64 `protobuf:"varint,4,opt,name=categoryId,proto3" json:"categoryId"`
	//到家品牌编号（通过分页查询商品品牌信息接口获取）
	BrandId int64 `protobuf:"varint,5,opt,name=brandId,proto3" json:"brandId"`
	//商品名称（格式：名称+规格）, 校验字符数（1-45字符），不能包含js代码
	SkuName string `protobuf:"bytes,6,opt,name=skuName,proto3" json:"skuName"`
	//商家商品价格（商品价格不能大于10W）(单位：分)，用于初始商品门店价格，所有的商品门店价格都会初始化成该值。后续修改商品门店价格需要通过价格类接口修改。
	SkuPrice int64 `protobuf:"varint,7,opt,name=skuPrice,proto3" json:"skuPrice"`
	//重量（单位：公斤/KG），小数点后最多保留3位
	Weight float32 `protobuf:"fixed32,8,opt,name=weight,proto3" json:"weight"`
	//UPC编码（商品条码），限1-35个字符，包装类的商品要求UPC编码必填，且要符合条码编写的校验，否则商品会不予通过，接口返回错误状态码code为10059
	Upc string `protobuf:"bytes,9,opt,name=upc,proto3" json:"upc"`
	//商品图片地址，图片数组中的顺序即图片顺序，默认第一张主图；图片要求：800*800 ，后缀格式只支持png或者jpg，最大不要超过1M；
	//利用该接口上传图片时，图片为异步处理，请调用“查询商品图片处理结果接口”查看图片是否上传成功。图片不超过六张
	Images []string `protobuf:"bytes,10,rep,name=images,proto3" json:"images"`
	//商品描述。支持图片，图片大小规格：750px宽，小于1M，和文字，若有文字描述，则不能少于10字符。限制为源码 不超过“30000”。
	ProductDesc string `protobuf:"bytes,11,opt,name=productDesc,proto3" json:"productDesc"`
	//商品描述是否在app端展示（0展示 1不展示）
	IfViewDesc int32 `protobuf:"varint,12,opt,name=ifViewDesc,proto3" json:"ifViewDesc"`
	//广告词, 校验字符数1-45字符
	Slogan string `protobuf:"bytes,13,opt,name=slogan,proto3" json:"slogan"`
	//广告词生效时间，当广告词字段有值时，该字段必填
	SloganStartTime string `protobuf:"bytes,14,opt,name=sloganStartTime,proto3" json:"sloganStartTime"`
	//广告词失效时间，当广告词字段有值时，该字段必填。必须设置具体时间，时间长短无限制。
	SloganEndTime string `protobuf:"bytes,15,opt,name=sloganEndTime,proto3" json:"sloganEndTime"`
	//前缀编码
	PrefixKeyId string `protobuf:"bytes,16,opt,name=prefixKeyId,proto3" json:"prefixKeyId"`
	//前缀内容
	PrefixKey string `protobuf:"bytes,17,opt,name=prefixKey,proto3" json:"prefixKey"`
	//前缀开始时间，前缀编码有值时，该字段必填
	PreKeyStartTime string `protobuf:"bytes,18,opt,name=preKeyStartTime,proto3" json:"preKeyStartTime"`
	//前缀结束时间，前缀编码有值时，该字段必填。必须设置具体时间，时间长短无限制
	PreKeyEndTime string `protobuf:"bytes,19,opt,name=preKeyEndTime,proto3" json:"preKeyEndTime"`
	//长(mm)
	Length int32 `protobuf:"varint,20,opt,name=length,proto3" json:"length"`
	//宽(mm)
	Width int32 `protobuf:"varint,21,opt,name=width,proto3" json:"width"`
	//高(mm)
	Height int32 `protobuf:"varint,22,opt,name=height,proto3" json:"height"`
	//储藏方式(0常温,1冷藏,2冷冻)
	TransportAttribute string `protobuf:"bytes,23,opt,name=transportAttribute,proto3" json:"transportAttribute"`
	//是否液体(0是,1否)
	LiquidStatue string `protobuf:"bytes,24,opt,name=liquidStatue,proto3" json:"liquidStatue"`
	//是否处方药(1是,2否)
	Prescripition string `protobuf:"bytes,25,opt,name=prescripition,proto3" json:"prescripition"`
	//是否高单值(1否，2是)
	HighSingularValue string `protobuf:"bytes,26,opt,name=highSingularValue,proto3" json:"highSingularValue"`
	//是否易碎(0是,1否)
	IsBreakable string `protobuf:"bytes,27,opt,name=isBreakable,proto3" json:"isBreakable"`
	//商家商品上下架状态：1.上架 2.下架
	FixedStatus int32 `protobuf:"varint,28,opt,name=fixedStatus,proto3" json:"fixedStatus"`
	//门店商品可售状态(true/false)；新建商品时，如果为true，门店商品可售状态初始为可售，如果为false， 门店商品可售状态初始为不可售。
	//后续修改各个门店商品可售状态时，请使用：库存类“根据到家商品编码和到家门店编码批量修改门店商品可售状态接口”。
	IsSale bool `protobuf:"varint,29,opt,name=isSale,proto3" json:"isSale"`
	//城市ID，0为全国
	SellCities []int32 `protobuf:"varint,30,rep,packed,name=sellCities,proto3" json:"sellCities"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,31,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSkuRequest) Reset()         { *m = AddSkuRequest{} }
func (m *AddSkuRequest) String() string { return proto.CompactTextString(m) }
func (*AddSkuRequest) ProtoMessage()    {}
func (*AddSkuRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{33}
}

func (m *AddSkuRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSkuRequest.Unmarshal(m, b)
}
func (m *AddSkuRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSkuRequest.Marshal(b, m, deterministic)
}
func (m *AddSkuRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSkuRequest.Merge(m, src)
}
func (m *AddSkuRequest) XXX_Size() int {
	return xxx_messageInfo_AddSkuRequest.Size(m)
}
func (m *AddSkuRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSkuRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddSkuRequest proto.InternalMessageInfo

func (m *AddSkuRequest) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *AddSkuRequest) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *AddSkuRequest) GetShopCategories() []int64 {
	if m != nil {
		return m.ShopCategories
	}
	return nil
}

func (m *AddSkuRequest) GetCategoryId() int64 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *AddSkuRequest) GetBrandId() int64 {
	if m != nil {
		return m.BrandId
	}
	return 0
}

func (m *AddSkuRequest) GetSkuName() string {
	if m != nil {
		return m.SkuName
	}
	return ""
}

func (m *AddSkuRequest) GetSkuPrice() int64 {
	if m != nil {
		return m.SkuPrice
	}
	return 0
}

func (m *AddSkuRequest) GetWeight() float32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *AddSkuRequest) GetUpc() string {
	if m != nil {
		return m.Upc
	}
	return ""
}

func (m *AddSkuRequest) GetImages() []string {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *AddSkuRequest) GetProductDesc() string {
	if m != nil {
		return m.ProductDesc
	}
	return ""
}

func (m *AddSkuRequest) GetIfViewDesc() int32 {
	if m != nil {
		return m.IfViewDesc
	}
	return 0
}

func (m *AddSkuRequest) GetSlogan() string {
	if m != nil {
		return m.Slogan
	}
	return ""
}

func (m *AddSkuRequest) GetSloganStartTime() string {
	if m != nil {
		return m.SloganStartTime
	}
	return ""
}

func (m *AddSkuRequest) GetSloganEndTime() string {
	if m != nil {
		return m.SloganEndTime
	}
	return ""
}

func (m *AddSkuRequest) GetPrefixKeyId() string {
	if m != nil {
		return m.PrefixKeyId
	}
	return ""
}

func (m *AddSkuRequest) GetPrefixKey() string {
	if m != nil {
		return m.PrefixKey
	}
	return ""
}

func (m *AddSkuRequest) GetPreKeyStartTime() string {
	if m != nil {
		return m.PreKeyStartTime
	}
	return ""
}

func (m *AddSkuRequest) GetPreKeyEndTime() string {
	if m != nil {
		return m.PreKeyEndTime
	}
	return ""
}

func (m *AddSkuRequest) GetLength() int32 {
	if m != nil {
		return m.Length
	}
	return 0
}

func (m *AddSkuRequest) GetWidth() int32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *AddSkuRequest) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *AddSkuRequest) GetTransportAttribute() string {
	if m != nil {
		return m.TransportAttribute
	}
	return ""
}

func (m *AddSkuRequest) GetLiquidStatue() string {
	if m != nil {
		return m.LiquidStatue
	}
	return ""
}

func (m *AddSkuRequest) GetPrescripition() string {
	if m != nil {
		return m.Prescripition
	}
	return ""
}

func (m *AddSkuRequest) GetHighSingularValue() string {
	if m != nil {
		return m.HighSingularValue
	}
	return ""
}

func (m *AddSkuRequest) GetIsBreakable() string {
	if m != nil {
		return m.IsBreakable
	}
	return ""
}

func (m *AddSkuRequest) GetFixedStatus() int32 {
	if m != nil {
		return m.FixedStatus
	}
	return 0
}

func (m *AddSkuRequest) GetIsSale() bool {
	if m != nil {
		return m.IsSale
	}
	return false
}

func (m *AddSkuRequest) GetSellCities() []int32 {
	if m != nil {
		return m.SellCities
	}
	return nil
}

func (m *AddSkuRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type AddSkuResponse struct {
	//到家SKU编码
	SkuId int64 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//商家SKU编码
	OutSkuId string `protobuf:"bytes,2,opt,name=outSkuId,proto3" json:"outSkuId"`
	//返回结果状态码
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	//返回结果状态消息
	ResultMsg string `protobuf:"bytes,4,opt,name=resultMsg,proto3" json:"resultMsg"`
	//
	OrgCode string `protobuf:"bytes,5,opt,name=orgCode,proto3" json:"orgCode"`
	//
	FailedDetail string `protobuf:"bytes,6,opt,name=failedDetail,proto3" json:"failedDetail"`
	//
	Success bool `protobuf:"varint,7,opt,name=success,proto3" json:"success"`
	//
	Detail               string   `protobuf:"bytes,8,opt,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSkuResponse) Reset()         { *m = AddSkuResponse{} }
func (m *AddSkuResponse) String() string { return proto.CompactTextString(m) }
func (*AddSkuResponse) ProtoMessage()    {}
func (*AddSkuResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{34}
}

func (m *AddSkuResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSkuResponse.Unmarshal(m, b)
}
func (m *AddSkuResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSkuResponse.Marshal(b, m, deterministic)
}
func (m *AddSkuResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSkuResponse.Merge(m, src)
}
func (m *AddSkuResponse) XXX_Size() int {
	return xxx_messageInfo_AddSkuResponse.Size(m)
}
func (m *AddSkuResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSkuResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddSkuResponse proto.InternalMessageInfo

func (m *AddSkuResponse) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *AddSkuResponse) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *AddSkuResponse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *AddSkuResponse) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

func (m *AddSkuResponse) GetOrgCode() string {
	if m != nil {
		return m.OrgCode
	}
	return ""
}

func (m *AddSkuResponse) GetFailedDetail() string {
	if m != nil {
		return m.FailedDetail
	}
	return ""
}

func (m *AddSkuResponse) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *AddSkuResponse) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

type BatchUpdateCurrentQtysRequest struct {
	//商家门店编码
	OutStationNo string `protobuf:"bytes,1,opt,name=outStationNo,proto3" json:"outStationNo"`
	//到家门店编码
	StationNo string `protobuf:"bytes,2,opt,name=stationNo,proto3" json:"stationNo"`
	//操作人pin
	UserPin string `protobuf:"bytes,3,opt,name=userPin,proto3" json:"userPin"`
	//商品信息，最多50条
	SkuStockList []*JddjSkuStock `protobuf:"bytes,4,rep,name=skuStockList,proto3" json:"skuStockList"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,5,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateCurrentQtysRequest) Reset()         { *m = BatchUpdateCurrentQtysRequest{} }
func (m *BatchUpdateCurrentQtysRequest) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateCurrentQtysRequest) ProtoMessage()    {}
func (*BatchUpdateCurrentQtysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{35}
}

func (m *BatchUpdateCurrentQtysRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateCurrentQtysRequest.Unmarshal(m, b)
}
func (m *BatchUpdateCurrentQtysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateCurrentQtysRequest.Marshal(b, m, deterministic)
}
func (m *BatchUpdateCurrentQtysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateCurrentQtysRequest.Merge(m, src)
}
func (m *BatchUpdateCurrentQtysRequest) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateCurrentQtysRequest.Size(m)
}
func (m *BatchUpdateCurrentQtysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateCurrentQtysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateCurrentQtysRequest proto.InternalMessageInfo

func (m *BatchUpdateCurrentQtysRequest) GetOutStationNo() string {
	if m != nil {
		return m.OutStationNo
	}
	return ""
}

func (m *BatchUpdateCurrentQtysRequest) GetStationNo() string {
	if m != nil {
		return m.StationNo
	}
	return ""
}

func (m *BatchUpdateCurrentQtysRequest) GetUserPin() string {
	if m != nil {
		return m.UserPin
	}
	return ""
}

func (m *BatchUpdateCurrentQtysRequest) GetSkuStockList() []*JddjSkuStock {
	if m != nil {
		return m.SkuStockList
	}
	return nil
}

func (m *BatchUpdateCurrentQtysRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjSkuStock struct {
	//商家商品编码
	OutSkuId string `protobuf:"bytes,1,opt,name=outSkuId,proto3" json:"outSkuId"`
	//现货库存（数量不能小于0）：线上商品门店库存
	StockQty             int32    `protobuf:"varint,2,opt,name=stockQty,proto3" json:"stockQty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjSkuStock) Reset()         { *m = JddjSkuStock{} }
func (m *JddjSkuStock) String() string { return proto.CompactTextString(m) }
func (*JddjSkuStock) ProtoMessage()    {}
func (*JddjSkuStock) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{36}
}

func (m *JddjSkuStock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjSkuStock.Unmarshal(m, b)
}
func (m *JddjSkuStock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjSkuStock.Marshal(b, m, deterministic)
}
func (m *JddjSkuStock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjSkuStock.Merge(m, src)
}
func (m *JddjSkuStock) XXX_Size() int {
	return xxx_messageInfo_JddjSkuStock.Size(m)
}
func (m *JddjSkuStock) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjSkuStock.DiscardUnknown(m)
}

var xxx_messageInfo_JddjSkuStock proto.InternalMessageInfo

func (m *JddjSkuStock) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *JddjSkuStock) GetStockQty() int32 {
	if m != nil {
		return m.StockQty
	}
	return 0
}

type BatchUpdateCurrentQtysResponse struct {
	//状态
	Ret bool `protobuf:"varint,1,opt,name=ret,proto3" json:"ret"`
	//false,10025,非库存同步时间;flase,10026,门店编号错误;false,1,失败;false,0,请求参数不能为空;false,11,orgCode不能为空;false,19,外部门店编号和到家门店编号不能同时为空;
	//false,20,操作人userPin不能为空;false,21,请求的商品列表不能为空;false,24,商品数量不能超过50个;true,0,成功；false,23,未获取到到家门店编号;false,22,调用门店系统异常;
	RetCode string `protobuf:"bytes,2,opt,name=retCode,proto3" json:"retCode"`
	//返回描述信息
	RetMsg               string                 `protobuf:"bytes,3,opt,name=retMsg,proto3" json:"retMsg"`
	Data                 []*UpdateStockResponse `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchUpdateCurrentQtysResponse) Reset()         { *m = BatchUpdateCurrentQtysResponse{} }
func (m *BatchUpdateCurrentQtysResponse) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateCurrentQtysResponse) ProtoMessage()    {}
func (*BatchUpdateCurrentQtysResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{37}
}

func (m *BatchUpdateCurrentQtysResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateCurrentQtysResponse.Unmarshal(m, b)
}
func (m *BatchUpdateCurrentQtysResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateCurrentQtysResponse.Marshal(b, m, deterministic)
}
func (m *BatchUpdateCurrentQtysResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateCurrentQtysResponse.Merge(m, src)
}
func (m *BatchUpdateCurrentQtysResponse) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateCurrentQtysResponse.Size(m)
}
func (m *BatchUpdateCurrentQtysResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateCurrentQtysResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateCurrentQtysResponse proto.InternalMessageInfo

func (m *BatchUpdateCurrentQtysResponse) GetRet() bool {
	if m != nil {
		return m.Ret
	}
	return false
}

func (m *BatchUpdateCurrentQtysResponse) GetRetCode() string {
	if m != nil {
		return m.RetCode
	}
	return ""
}

func (m *BatchUpdateCurrentQtysResponse) GetRetMsg() string {
	if m != nil {
		return m.RetMsg
	}
	return ""
}

func (m *BatchUpdateCurrentQtysResponse) GetData() []*UpdateStockResponse {
	if m != nil {
		return m.Data
	}
	return nil
}

type UpdateStockResponse struct {
	//更新状态（0此sku更新成功，1更新失败）
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//更新状态描述
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	//外部sku编号
	OutSkuId             string   `protobuf:"bytes,3,opt,name=outSkuId,proto3" json:"outSkuId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStockResponse) Reset()         { *m = UpdateStockResponse{} }
func (m *UpdateStockResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateStockResponse) ProtoMessage()    {}
func (*UpdateStockResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{38}
}

func (m *UpdateStockResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStockResponse.Unmarshal(m, b)
}
func (m *UpdateStockResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStockResponse.Marshal(b, m, deterministic)
}
func (m *UpdateStockResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStockResponse.Merge(m, src)
}
func (m *UpdateStockResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateStockResponse.Size(m)
}
func (m *UpdateStockResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStockResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStockResponse proto.InternalMessageInfo

func (m *UpdateStockResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdateStockResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *UpdateStockResponse) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

type JddjUpdateGoodsListRequest struct {
	//	商家商品编码，商家系统中唯一编码，限1-35字符，与京东到家商品编码一对一对应。
	OutSkuId string `protobuf:"bytes,1,opt,name=outSkuId,proto3" json:"outSkuId"`
	//到家类目编号，需传入到家的第三级分类（通过查询到家类目信息接口获取）
	CategoryId int64 `protobuf:"varint,2,opt,name=categoryId,proto3" json:"categoryId"`
	//商家店内分类编号列表，商家店内分类分两级，一个商品可以绑定多个店内分类（上传的店内分类需为最末级分类，即二级店内分类或没有子分类的一级店内分类），店内分类编号通过查询商家店内分类信息接口获取。
	ShopCategories []string `protobuf:"bytes,3,rep,name=shopCategories,proto3" json:"shopCategories"`
	//到家品牌编号（通过分页查询商品品牌信息接口获取）。
	BrandId int64 `protobuf:"varint,4,opt,name=brandId,proto3" json:"brandId"`
	//商品名称（格式：名称+规格）, 校验字符数（1-45字符），不能包含js代码
	SkuName string `protobuf:"bytes,5,opt,name=skuName,proto3" json:"skuName"`
	//重量（单位：公斤/KG），小数点后最多保留3位。
	Weight float32 `protobuf:"fixed32,6,opt,name=weight,proto3" json:"weight"`
	//UPC编码（商品条码），限1-35个字符，包装类的商品要求UPC编码必填，且要符合条码编写的校验，否则商品会不予通过，接口返回错误状态码code为10059。
	UpcCode string `protobuf:"bytes,7,opt,name=upcCode,proto3" json:"upcCode"`
	//商家商品上下架状态 1.上架 2.下架，4.删除；注：已经运营下架状态的商品不能设置上架，如需上架，请联系到家运营处理。
	FixedStatus int32 `protobuf:"varint,8,opt,name=fixedStatus,proto3" json:"fixedStatus"`
	//商品图片地址，图片数组中的顺序即图片顺序，默认第一张主图；图片要求：800*800 ，后缀格式只支持png或者jpg，最大不要超过1M；利用该接口上传图片时，图片为异步处理，请调用“查询商品图片处理结果接口”查看图片是否上传成功
	Images []string `protobuf:"bytes,9,rep,name=images,proto3" json:"images"`
	//商品描述。支持图片和文字，若有文字描述，则不能少于10字符。（包含图片的，图片要求750px宽，小于1M）
	ProductDesc string `protobuf:"bytes,10,opt,name=productDesc,proto3" json:"productDesc"`
	//商品描述是否在app端展示（0展示 1不展示）
	IfViewDesc int32 `protobuf:"varint,11,opt,name=ifViewDesc,proto3" json:"ifViewDesc"`
	//长(mm)
	Length int32 `protobuf:"varint,12,opt,name=length,proto3" json:"length"`
	//宽(mm)
	Width int32 `protobuf:"varint,13,opt,name=width,proto3" json:"width"`
	//高(mm)
	Height string `protobuf:"bytes,14,opt,name=height,proto3" json:"height"`
	//广告词, 校验字符数1-45字符。需清空广告词，则该字段传空值
	Slogan string `protobuf:"bytes,15,opt,name=slogan,proto3" json:"slogan"`
	//广告词生效时间，当广告词字段有值时，该字段必填。
	SloganStartTime string `protobuf:"bytes,16,opt,name=sloganStartTime,proto3" json:"sloganStartTime"`
	//广告词失效时间，当广告词字段有值时，该字段必填。
	SloganEndTime string `protobuf:"bytes,17,opt,name=sloganEndTime,proto3" json:"sloganEndTime"`
	//前缀编码，需清空前缀，则该字段传空值
	PrefixKeyId string `protobuf:"bytes,18,opt,name=prefixKeyId,proto3" json:"prefixKeyId"`
	//前缀内容
	PrefixKey string `protobuf:"bytes,19,opt,name=prefixKey,proto3" json:"prefixKey"`
	//前缀开始时间，前缀编码有值时，该字段必填
	PreKeyStartTime string `protobuf:"bytes,20,opt,name=preKeyStartTime,proto3" json:"preKeyStartTime"`
	//前缀结束时间，前缀编码有值时，该字段必填
	PreKeyEndTime string `protobuf:"bytes,21,opt,name=preKeyEndTime,proto3" json:"preKeyEndTime"`
	//储藏方式(0常温,1冷藏,2冷冻)
	TransportAttribute string `protobuf:"bytes,22,opt,name=transportAttribute,proto3" json:"transportAttribute"`
	//是否液体(0是,1否)
	LiquidStatue string `protobuf:"bytes,23,opt,name=liquidStatue,proto3" json:"liquidStatue"`
	//是否处方药(1是,2否)
	Prescripition string `protobuf:"bytes,24,opt,name=prescripition,proto3" json:"prescripition"`
	//是否高单值(1否,2是)
	HighSingularValue string `protobuf:"bytes,25,opt,name=highSingularValue,proto3" json:"highSingularValue"`
	//是否易碎(0否,1是)
	IsBreakable string `protobuf:"bytes,26,opt,name=isBreakable,proto3" json:"isBreakable"`
	//城市ID，0为全国，其他城市ID需调用获取所有城市信息列表接口查询，如果不传该参数默认为全国。
	SellCities []string `protobuf:"bytes,27,rep,name=sellCities,proto3" json:"sellCities"`
	//请求唯一编码
	TraceId string `protobuf:"bytes,28,opt,name=traceId,proto3" json:"traceId"`
	//商品价格
	SkuPrice int32 `protobuf:"varint,29,opt,name=skuPrice,proto3" json:"skuPrice"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,30,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjUpdateGoodsListRequest) Reset()         { *m = JddjUpdateGoodsListRequest{} }
func (m *JddjUpdateGoodsListRequest) String() string { return proto.CompactTextString(m) }
func (*JddjUpdateGoodsListRequest) ProtoMessage()    {}
func (*JddjUpdateGoodsListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{39}
}

func (m *JddjUpdateGoodsListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjUpdateGoodsListRequest.Unmarshal(m, b)
}
func (m *JddjUpdateGoodsListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjUpdateGoodsListRequest.Marshal(b, m, deterministic)
}
func (m *JddjUpdateGoodsListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjUpdateGoodsListRequest.Merge(m, src)
}
func (m *JddjUpdateGoodsListRequest) XXX_Size() int {
	return xxx_messageInfo_JddjUpdateGoodsListRequest.Size(m)
}
func (m *JddjUpdateGoodsListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjUpdateGoodsListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjUpdateGoodsListRequest proto.InternalMessageInfo

func (m *JddjUpdateGoodsListRequest) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetCategoryId() int64 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *JddjUpdateGoodsListRequest) GetShopCategories() []string {
	if m != nil {
		return m.ShopCategories
	}
	return nil
}

func (m *JddjUpdateGoodsListRequest) GetBrandId() int64 {
	if m != nil {
		return m.BrandId
	}
	return 0
}

func (m *JddjUpdateGoodsListRequest) GetSkuName() string {
	if m != nil {
		return m.SkuName
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetWeight() float32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *JddjUpdateGoodsListRequest) GetUpcCode() string {
	if m != nil {
		return m.UpcCode
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetFixedStatus() int32 {
	if m != nil {
		return m.FixedStatus
	}
	return 0
}

func (m *JddjUpdateGoodsListRequest) GetImages() []string {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *JddjUpdateGoodsListRequest) GetProductDesc() string {
	if m != nil {
		return m.ProductDesc
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetIfViewDesc() int32 {
	if m != nil {
		return m.IfViewDesc
	}
	return 0
}

func (m *JddjUpdateGoodsListRequest) GetLength() int32 {
	if m != nil {
		return m.Length
	}
	return 0
}

func (m *JddjUpdateGoodsListRequest) GetWidth() int32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *JddjUpdateGoodsListRequest) GetHeight() string {
	if m != nil {
		return m.Height
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetSlogan() string {
	if m != nil {
		return m.Slogan
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetSloganStartTime() string {
	if m != nil {
		return m.SloganStartTime
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetSloganEndTime() string {
	if m != nil {
		return m.SloganEndTime
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetPrefixKeyId() string {
	if m != nil {
		return m.PrefixKeyId
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetPrefixKey() string {
	if m != nil {
		return m.PrefixKey
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetPreKeyStartTime() string {
	if m != nil {
		return m.PreKeyStartTime
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetPreKeyEndTime() string {
	if m != nil {
		return m.PreKeyEndTime
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetTransportAttribute() string {
	if m != nil {
		return m.TransportAttribute
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetLiquidStatue() string {
	if m != nil {
		return m.LiquidStatue
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetPrescripition() string {
	if m != nil {
		return m.Prescripition
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetHighSingularValue() string {
	if m != nil {
		return m.HighSingularValue
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetIsBreakable() string {
	if m != nil {
		return m.IsBreakable
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetSellCities() []string {
	if m != nil {
		return m.SellCities
	}
	return nil
}

func (m *JddjUpdateGoodsListRequest) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *JddjUpdateGoodsListRequest) GetSkuPrice() int32 {
	if m != nil {
		return m.SkuPrice
	}
	return 0
}

func (m *JddjUpdateGoodsListRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjUpdateGoodsListResponse struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Result               *Result  `protobuf:"bytes,3,opt,name=result,proto3" json:"result"`
	Succcess             string   `protobuf:"bytes,4,opt,name=succcess,proto3" json:"succcess"`
	Detail               string   `protobuf:"bytes,5,opt,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjUpdateGoodsListResponse) Reset()         { *m = JddjUpdateGoodsListResponse{} }
func (m *JddjUpdateGoodsListResponse) String() string { return proto.CompactTextString(m) }
func (*JddjUpdateGoodsListResponse) ProtoMessage()    {}
func (*JddjUpdateGoodsListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{40}
}

func (m *JddjUpdateGoodsListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjUpdateGoodsListResponse.Unmarshal(m, b)
}
func (m *JddjUpdateGoodsListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjUpdateGoodsListResponse.Marshal(b, m, deterministic)
}
func (m *JddjUpdateGoodsListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjUpdateGoodsListResponse.Merge(m, src)
}
func (m *JddjUpdateGoodsListResponse) XXX_Size() int {
	return xxx_messageInfo_JddjUpdateGoodsListResponse.Size(m)
}
func (m *JddjUpdateGoodsListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjUpdateGoodsListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjUpdateGoodsListResponse proto.InternalMessageInfo

func (m *JddjUpdateGoodsListResponse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *JddjUpdateGoodsListResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *JddjUpdateGoodsListResponse) GetResult() *Result {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *JddjUpdateGoodsListResponse) GetSucccess() string {
	if m != nil {
		return m.Succcess
	}
	return ""
}

func (m *JddjUpdateGoodsListResponse) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

type Result struct {
	OrgCode              string   `protobuf:"bytes,1,opt,name=orgCode,proto3" json:"orgCode"`
	ResultCode           string   `protobuf:"bytes,2,opt,name=resultCode,proto3" json:"resultCode"`
	OutSkuId             string   `protobuf:"bytes,3,opt,name=outSkuId,proto3" json:"outSkuId"`
	FailedDetail         string   `protobuf:"bytes,4,opt,name=failedDetail,proto3" json:"failedDetail"`
	SkuId                int32    `protobuf:"varint,5,opt,name=skuId,proto3" json:"skuId"`
	ResultMsg            string   `protobuf:"bytes,6,opt,name=resultMsg,proto3" json:"resultMsg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Result) Reset()         { *m = Result{} }
func (m *Result) String() string { return proto.CompactTextString(m) }
func (*Result) ProtoMessage()    {}
func (*Result) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{41}
}

func (m *Result) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Result.Unmarshal(m, b)
}
func (m *Result) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Result.Marshal(b, m, deterministic)
}
func (m *Result) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Result.Merge(m, src)
}
func (m *Result) XXX_Size() int {
	return xxx_messageInfo_Result.Size(m)
}
func (m *Result) XXX_DiscardUnknown() {
	xxx_messageInfo_Result.DiscardUnknown(m)
}

var xxx_messageInfo_Result proto.InternalMessageInfo

func (m *Result) GetOrgCode() string {
	if m != nil {
		return m.OrgCode
	}
	return ""
}

func (m *Result) GetResultCode() string {
	if m != nil {
		return m.ResultCode
	}
	return ""
}

func (m *Result) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *Result) GetFailedDetail() string {
	if m != nil {
		return m.FailedDetail
	}
	return ""
}

func (m *Result) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *Result) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

type JddjAddShopCategoryRequest struct {
	Pid               int64  `protobuf:"varint,1,opt,name=pid,proto3" json:"pid"`
	ShopCategoryName  string `protobuf:"bytes,2,opt,name=shopCategoryName,proto3" json:"shopCategoryName"`
	ShopCategoryLevel int32  `protobuf:"varint,3,opt,name=shopCategoryLevel,proto3" json:"shopCategoryLevel"`
	CreatePin         string `protobuf:"bytes,4,opt,name=createPin,proto3" json:"createPin"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,5,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjAddShopCategoryRequest) Reset()         { *m = JddjAddShopCategoryRequest{} }
func (m *JddjAddShopCategoryRequest) String() string { return proto.CompactTextString(m) }
func (*JddjAddShopCategoryRequest) ProtoMessage()    {}
func (*JddjAddShopCategoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{42}
}

func (m *JddjAddShopCategoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjAddShopCategoryRequest.Unmarshal(m, b)
}
func (m *JddjAddShopCategoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjAddShopCategoryRequest.Marshal(b, m, deterministic)
}
func (m *JddjAddShopCategoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjAddShopCategoryRequest.Merge(m, src)
}
func (m *JddjAddShopCategoryRequest) XXX_Size() int {
	return xxx_messageInfo_JddjAddShopCategoryRequest.Size(m)
}
func (m *JddjAddShopCategoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjAddShopCategoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjAddShopCategoryRequest proto.InternalMessageInfo

func (m *JddjAddShopCategoryRequest) GetPid() int64 {
	if m != nil {
		return m.Pid
	}
	return 0
}

func (m *JddjAddShopCategoryRequest) GetShopCategoryName() string {
	if m != nil {
		return m.ShopCategoryName
	}
	return ""
}

func (m *JddjAddShopCategoryRequest) GetShopCategoryLevel() int32 {
	if m != nil {
		return m.ShopCategoryLevel
	}
	return 0
}

func (m *JddjAddShopCategoryRequest) GetCreatePin() string {
	if m != nil {
		return m.CreatePin
	}
	return ""
}

func (m *JddjAddShopCategoryRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjAddShopCategoryResponse struct {
	Result               *ShopCategoryPartnerResponse `protobuf:"bytes,1,opt,name=result,proto3" json:"result"`
	Code                 string                       `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
	Msg                  string                       `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
	Detail               string                       `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *JddjAddShopCategoryResponse) Reset()         { *m = JddjAddShopCategoryResponse{} }
func (m *JddjAddShopCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*JddjAddShopCategoryResponse) ProtoMessage()    {}
func (*JddjAddShopCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{43}
}

func (m *JddjAddShopCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjAddShopCategoryResponse.Unmarshal(m, b)
}
func (m *JddjAddShopCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjAddShopCategoryResponse.Marshal(b, m, deterministic)
}
func (m *JddjAddShopCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjAddShopCategoryResponse.Merge(m, src)
}
func (m *JddjAddShopCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_JddjAddShopCategoryResponse.Size(m)
}
func (m *JddjAddShopCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjAddShopCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjAddShopCategoryResponse proto.InternalMessageInfo

func (m *JddjAddShopCategoryResponse) GetResult() *ShopCategoryPartnerResponse {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *JddjAddShopCategoryResponse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *JddjAddShopCategoryResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *JddjAddShopCategoryResponse) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

type ShopCategoryPartnerResponse struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopCategoryPartnerResponse) Reset()         { *m = ShopCategoryPartnerResponse{} }
func (m *ShopCategoryPartnerResponse) String() string { return proto.CompactTextString(m) }
func (*ShopCategoryPartnerResponse) ProtoMessage()    {}
func (*ShopCategoryPartnerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{44}
}

func (m *ShopCategoryPartnerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopCategoryPartnerResponse.Unmarshal(m, b)
}
func (m *ShopCategoryPartnerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopCategoryPartnerResponse.Marshal(b, m, deterministic)
}
func (m *ShopCategoryPartnerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopCategoryPartnerResponse.Merge(m, src)
}
func (m *ShopCategoryPartnerResponse) XXX_Size() int {
	return xxx_messageInfo_ShopCategoryPartnerResponse.Size(m)
}
func (m *ShopCategoryPartnerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopCategoryPartnerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ShopCategoryPartnerResponse proto.InternalMessageInfo

func (m *ShopCategoryPartnerResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type JddjUpdateShopCategoryRequest struct {
	//店内分类编号
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//店内分类名称
	ShopCategoryName string `protobuf:"bytes,2,opt,name=shopCategoryName,proto3" json:"shopCategoryName"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjUpdateShopCategoryRequest) Reset()         { *m = JddjUpdateShopCategoryRequest{} }
func (m *JddjUpdateShopCategoryRequest) String() string { return proto.CompactTextString(m) }
func (*JddjUpdateShopCategoryRequest) ProtoMessage()    {}
func (*JddjUpdateShopCategoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{45}
}

func (m *JddjUpdateShopCategoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjUpdateShopCategoryRequest.Unmarshal(m, b)
}
func (m *JddjUpdateShopCategoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjUpdateShopCategoryRequest.Marshal(b, m, deterministic)
}
func (m *JddjUpdateShopCategoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjUpdateShopCategoryRequest.Merge(m, src)
}
func (m *JddjUpdateShopCategoryRequest) XXX_Size() int {
	return xxx_messageInfo_JddjUpdateShopCategoryRequest.Size(m)
}
func (m *JddjUpdateShopCategoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjUpdateShopCategoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjUpdateShopCategoryRequest proto.InternalMessageInfo

func (m *JddjUpdateShopCategoryRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *JddjUpdateShopCategoryRequest) GetShopCategoryName() string {
	if m != nil {
		return m.ShopCategoryName
	}
	return ""
}

func (m *JddjUpdateShopCategoryRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjUpdateShopCategoryResponse struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjUpdateShopCategoryResponse) Reset()         { *m = JddjUpdateShopCategoryResponse{} }
func (m *JddjUpdateShopCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*JddjUpdateShopCategoryResponse) ProtoMessage()    {}
func (*JddjUpdateShopCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{46}
}

func (m *JddjUpdateShopCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjUpdateShopCategoryResponse.Unmarshal(m, b)
}
func (m *JddjUpdateShopCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjUpdateShopCategoryResponse.Marshal(b, m, deterministic)
}
func (m *JddjUpdateShopCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjUpdateShopCategoryResponse.Merge(m, src)
}
func (m *JddjUpdateShopCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_JddjUpdateShopCategoryResponse.Size(m)
}
func (m *JddjUpdateShopCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjUpdateShopCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjUpdateShopCategoryResponse proto.InternalMessageInfo

func (m *JddjUpdateShopCategoryResponse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *JddjUpdateShopCategoryResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type JddjDeleteShopCategoryRequest struct {
	//店内分类编码
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,2,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjDeleteShopCategoryRequest) Reset()         { *m = JddjDeleteShopCategoryRequest{} }
func (m *JddjDeleteShopCategoryRequest) String() string { return proto.CompactTextString(m) }
func (*JddjDeleteShopCategoryRequest) ProtoMessage()    {}
func (*JddjDeleteShopCategoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{47}
}

func (m *JddjDeleteShopCategoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjDeleteShopCategoryRequest.Unmarshal(m, b)
}
func (m *JddjDeleteShopCategoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjDeleteShopCategoryRequest.Marshal(b, m, deterministic)
}
func (m *JddjDeleteShopCategoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjDeleteShopCategoryRequest.Merge(m, src)
}
func (m *JddjDeleteShopCategoryRequest) XXX_Size() int {
	return xxx_messageInfo_JddjDeleteShopCategoryRequest.Size(m)
}
func (m *JddjDeleteShopCategoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjDeleteShopCategoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjDeleteShopCategoryRequest proto.InternalMessageInfo

func (m *JddjDeleteShopCategoryRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *JddjDeleteShopCategoryRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjDeleteShopCategoryResponse struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjDeleteShopCategoryResponse) Reset()         { *m = JddjDeleteShopCategoryResponse{} }
func (m *JddjDeleteShopCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*JddjDeleteShopCategoryResponse) ProtoMessage()    {}
func (*JddjDeleteShopCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{48}
}

func (m *JddjDeleteShopCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjDeleteShopCategoryResponse.Unmarshal(m, b)
}
func (m *JddjDeleteShopCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjDeleteShopCategoryResponse.Marshal(b, m, deterministic)
}
func (m *JddjDeleteShopCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjDeleteShopCategoryResponse.Merge(m, src)
}
func (m *JddjDeleteShopCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_JddjDeleteShopCategoryResponse.Size(m)
}
func (m *JddjDeleteShopCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjDeleteShopCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjDeleteShopCategoryResponse proto.InternalMessageInfo

func (m *JddjDeleteShopCategoryResponse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *JddjDeleteShopCategoryResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type JddjSortShopCategoryRequest struct {
	//父级店内分类编码；如果调整一级店内分类顺序，父级分类传0即可。
	Pid int64 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid"`
	//子级店内分类集合；按照填写的分类编号顺序对分类顺序进行调整；该父分类下所有子分类都必须传入。
	ChildIds string `protobuf:"bytes,2,opt,name=childIds,proto3" json:"childIds"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjSortShopCategoryRequest) Reset()         { *m = JddjSortShopCategoryRequest{} }
func (m *JddjSortShopCategoryRequest) String() string { return proto.CompactTextString(m) }
func (*JddjSortShopCategoryRequest) ProtoMessage()    {}
func (*JddjSortShopCategoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{49}
}

func (m *JddjSortShopCategoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjSortShopCategoryRequest.Unmarshal(m, b)
}
func (m *JddjSortShopCategoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjSortShopCategoryRequest.Marshal(b, m, deterministic)
}
func (m *JddjSortShopCategoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjSortShopCategoryRequest.Merge(m, src)
}
func (m *JddjSortShopCategoryRequest) XXX_Size() int {
	return xxx_messageInfo_JddjSortShopCategoryRequest.Size(m)
}
func (m *JddjSortShopCategoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjSortShopCategoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjSortShopCategoryRequest proto.InternalMessageInfo

func (m *JddjSortShopCategoryRequest) GetPid() int64 {
	if m != nil {
		return m.Pid
	}
	return 0
}

func (m *JddjSortShopCategoryRequest) GetChildIds() string {
	if m != nil {
		return m.ChildIds
	}
	return ""
}

func (m *JddjSortShopCategoryRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjSortShopCategoryResponse struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjSortShopCategoryResponse) Reset()         { *m = JddjSortShopCategoryResponse{} }
func (m *JddjSortShopCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*JddjSortShopCategoryResponse) ProtoMessage()    {}
func (*JddjSortShopCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{50}
}

func (m *JddjSortShopCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjSortShopCategoryResponse.Unmarshal(m, b)
}
func (m *JddjSortShopCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjSortShopCategoryResponse.Marshal(b, m, deterministic)
}
func (m *JddjSortShopCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjSortShopCategoryResponse.Merge(m, src)
}
func (m *JddjSortShopCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_JddjSortShopCategoryResponse.Size(m)
}
func (m *JddjSortShopCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjSortShopCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjSortShopCategoryResponse proto.InternalMessageInfo

func (m *JddjSortShopCategoryResponse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *JddjSortShopCategoryResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type QueryPageBrandInfoRequest struct {
	//品牌名称(模糊查询);不传代表查询所有
	BrandName string `protobuf:"bytes,1,opt,name=brandName,proto3" json:"brandName"`
	//到家品牌编码
	BrandId int32 `protobuf:"varint,2,opt,name=brandId,proto3" json:"brandId"`
	//定义需要返回的字段列表；所有字段列表：BRAND_ID--品牌编号,BRAND_NAME--品牌名称,BRAND_STATUS--品牌状态(1已禁用，2启用中)
	Fields []string `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields"`
	//页码
	PageNo int32 `protobuf:"varint,4,opt,name=pageNo,proto3" json:"pageNo"`
	//每页查询数量(最大值50)
	PageSize int32 `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,6,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPageBrandInfoRequest) Reset()         { *m = QueryPageBrandInfoRequest{} }
func (m *QueryPageBrandInfoRequest) String() string { return proto.CompactTextString(m) }
func (*QueryPageBrandInfoRequest) ProtoMessage()    {}
func (*QueryPageBrandInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{51}
}

func (m *QueryPageBrandInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPageBrandInfoRequest.Unmarshal(m, b)
}
func (m *QueryPageBrandInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPageBrandInfoRequest.Marshal(b, m, deterministic)
}
func (m *QueryPageBrandInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPageBrandInfoRequest.Merge(m, src)
}
func (m *QueryPageBrandInfoRequest) XXX_Size() int {
	return xxx_messageInfo_QueryPageBrandInfoRequest.Size(m)
}
func (m *QueryPageBrandInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPageBrandInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPageBrandInfoRequest proto.InternalMessageInfo

func (m *QueryPageBrandInfoRequest) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *QueryPageBrandInfoRequest) GetBrandId() int32 {
	if m != nil {
		return m.BrandId
	}
	return 0
}

func (m *QueryPageBrandInfoRequest) GetFields() []string {
	if m != nil {
		return m.Fields
	}
	return nil
}

func (m *QueryPageBrandInfoRequest) GetPageNo() int32 {
	if m != nil {
		return m.PageNo
	}
	return 0
}

func (m *QueryPageBrandInfoRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *QueryPageBrandInfoRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type QueryPageBrandInfoResponse struct {
	//总数量
	Count                int32            `protobuf:"varint,1,opt,name=count,proto3" json:"count"`
	Result               []*JddjBrandInfo `protobuf:"bytes,2,rep,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *QueryPageBrandInfoResponse) Reset()         { *m = QueryPageBrandInfoResponse{} }
func (m *QueryPageBrandInfoResponse) String() string { return proto.CompactTextString(m) }
func (*QueryPageBrandInfoResponse) ProtoMessage()    {}
func (*QueryPageBrandInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{52}
}

func (m *QueryPageBrandInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPageBrandInfoResponse.Unmarshal(m, b)
}
func (m *QueryPageBrandInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPageBrandInfoResponse.Marshal(b, m, deterministic)
}
func (m *QueryPageBrandInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPageBrandInfoResponse.Merge(m, src)
}
func (m *QueryPageBrandInfoResponse) XXX_Size() int {
	return xxx_messageInfo_QueryPageBrandInfoResponse.Size(m)
}
func (m *QueryPageBrandInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPageBrandInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPageBrandInfoResponse proto.InternalMessageInfo

func (m *QueryPageBrandInfoResponse) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *QueryPageBrandInfoResponse) GetResult() []*JddjBrandInfo {
	if m != nil {
		return m.Result
	}
	return nil
}

type JddjBrandInfo struct {
	//品牌编号
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//品牌名称
	BrandName string `protobuf:"bytes,2,opt,name=brandName,proto3" json:"brandName"`
	//品牌状态：-1:删除状态1：待审核,2：审核通过，3：驳回
	BrandStatus          int32    `protobuf:"varint,3,opt,name=brandStatus,proto3" json:"brandStatus"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjBrandInfo) Reset()         { *m = JddjBrandInfo{} }
func (m *JddjBrandInfo) String() string { return proto.CompactTextString(m) }
func (*JddjBrandInfo) ProtoMessage()    {}
func (*JddjBrandInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{53}
}

func (m *JddjBrandInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjBrandInfo.Unmarshal(m, b)
}
func (m *JddjBrandInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjBrandInfo.Marshal(b, m, deterministic)
}
func (m *JddjBrandInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjBrandInfo.Merge(m, src)
}
func (m *JddjBrandInfo) XXX_Size() int {
	return xxx_messageInfo_JddjBrandInfo.Size(m)
}
func (m *JddjBrandInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjBrandInfo.DiscardUnknown(m)
}

var xxx_messageInfo_JddjBrandInfo proto.InternalMessageInfo

func (m *JddjBrandInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *JddjBrandInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *JddjBrandInfo) GetBrandStatus() int32 {
	if m != nil {
		return m.BrandStatus
	}
	return 0
}

type QueryChildCategoriesForOPRequest struct {
	Id     int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Fields string `protobuf:"bytes,2,opt,name=fields,proto3" json:"fields"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryChildCategoriesForOPRequest) Reset()         { *m = QueryChildCategoriesForOPRequest{} }
func (m *QueryChildCategoriesForOPRequest) String() string { return proto.CompactTextString(m) }
func (*QueryChildCategoriesForOPRequest) ProtoMessage()    {}
func (*QueryChildCategoriesForOPRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{54}
}

func (m *QueryChildCategoriesForOPRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryChildCategoriesForOPRequest.Unmarshal(m, b)
}
func (m *QueryChildCategoriesForOPRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryChildCategoriesForOPRequest.Marshal(b, m, deterministic)
}
func (m *QueryChildCategoriesForOPRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryChildCategoriesForOPRequest.Merge(m, src)
}
func (m *QueryChildCategoriesForOPRequest) XXX_Size() int {
	return xxx_messageInfo_QueryChildCategoriesForOPRequest.Size(m)
}
func (m *QueryChildCategoriesForOPRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryChildCategoriesForOPRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryChildCategoriesForOPRequest proto.InternalMessageInfo

func (m *QueryChildCategoriesForOPRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *QueryChildCategoriesForOPRequest) GetFields() string {
	if m != nil {
		return m.Fields
	}
	return ""
}

func (m *QueryChildCategoriesForOPRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type QueryChildCategoriesForOPResponse struct {
	Result               []*JddjCategoryInfo `protobuf:"bytes,2,rep,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *QueryChildCategoriesForOPResponse) Reset()         { *m = QueryChildCategoriesForOPResponse{} }
func (m *QueryChildCategoriesForOPResponse) String() string { return proto.CompactTextString(m) }
func (*QueryChildCategoriesForOPResponse) ProtoMessage()    {}
func (*QueryChildCategoriesForOPResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{55}
}

func (m *QueryChildCategoriesForOPResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryChildCategoriesForOPResponse.Unmarshal(m, b)
}
func (m *QueryChildCategoriesForOPResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryChildCategoriesForOPResponse.Marshal(b, m, deterministic)
}
func (m *QueryChildCategoriesForOPResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryChildCategoriesForOPResponse.Merge(m, src)
}
func (m *QueryChildCategoriesForOPResponse) XXX_Size() int {
	return xxx_messageInfo_QueryChildCategoriesForOPResponse.Size(m)
}
func (m *QueryChildCategoriesForOPResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryChildCategoriesForOPResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryChildCategoriesForOPResponse proto.InternalMessageInfo

func (m *QueryChildCategoriesForOPResponse) GetResult() []*JddjCategoryInfo {
	if m != nil {
		return m.Result
	}
	return nil
}

//zhang start
type JddjOrderConfirmRequest struct {
	//订单ID
	OrderId  string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	IsAgreed bool   `protobuf:"varint,2,opt,name=isAgreed,proto3" json:"isAgreed"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOrderConfirmRequest) Reset()         { *m = JddjOrderConfirmRequest{} }
func (m *JddjOrderConfirmRequest) String() string { return proto.CompactTextString(m) }
func (*JddjOrderConfirmRequest) ProtoMessage()    {}
func (*JddjOrderConfirmRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{56}
}

func (m *JddjOrderConfirmRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderConfirmRequest.Unmarshal(m, b)
}
func (m *JddjOrderConfirmRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderConfirmRequest.Marshal(b, m, deterministic)
}
func (m *JddjOrderConfirmRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderConfirmRequest.Merge(m, src)
}
func (m *JddjOrderConfirmRequest) XXX_Size() int {
	return xxx_messageInfo_JddjOrderConfirmRequest.Size(m)
}
func (m *JddjOrderConfirmRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderConfirmRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderConfirmRequest proto.InternalMessageInfo

func (m *JddjOrderConfirmRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JddjOrderConfirmRequest) GetIsAgreed() bool {
	if m != nil {
		return m.IsAgreed
	}
	return false
}

func (m *JddjOrderConfirmRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjOrderConfirmlResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//结果
	Data                 string   `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOrderConfirmlResponse) Reset()         { *m = JddjOrderConfirmlResponse{} }
func (m *JddjOrderConfirmlResponse) String() string { return proto.CompactTextString(m) }
func (*JddjOrderConfirmlResponse) ProtoMessage()    {}
func (*JddjOrderConfirmlResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{57}
}

func (m *JddjOrderConfirmlResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderConfirmlResponse.Unmarshal(m, b)
}
func (m *JddjOrderConfirmlResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderConfirmlResponse.Marshal(b, m, deterministic)
}
func (m *JddjOrderConfirmlResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderConfirmlResponse.Merge(m, src)
}
func (m *JddjOrderConfirmlResponse) XXX_Size() int {
	return xxx_messageInfo_JddjOrderConfirmlResponse.Size(m)
}
func (m *JddjOrderConfirmlResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderConfirmlResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderConfirmlResponse proto.InternalMessageInfo

func (m *JddjOrderConfirmlResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *JddjOrderConfirmlResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *JddjOrderConfirmlResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *JddjOrderConfirmlResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type JddjOrderSerllerDeliveryRequest struct {
	//订单ID
	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,2,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOrderSerllerDeliveryRequest) Reset()         { *m = JddjOrderSerllerDeliveryRequest{} }
func (m *JddjOrderSerllerDeliveryRequest) String() string { return proto.CompactTextString(m) }
func (*JddjOrderSerllerDeliveryRequest) ProtoMessage()    {}
func (*JddjOrderSerllerDeliveryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{58}
}

func (m *JddjOrderSerllerDeliveryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderSerllerDeliveryRequest.Unmarshal(m, b)
}
func (m *JddjOrderSerllerDeliveryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderSerllerDeliveryRequest.Marshal(b, m, deterministic)
}
func (m *JddjOrderSerllerDeliveryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderSerllerDeliveryRequest.Merge(m, src)
}
func (m *JddjOrderSerllerDeliveryRequest) XXX_Size() int {
	return xxx_messageInfo_JddjOrderSerllerDeliveryRequest.Size(m)
}
func (m *JddjOrderSerllerDeliveryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderSerllerDeliveryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderSerllerDeliveryRequest proto.InternalMessageInfo

func (m *JddjOrderSerllerDeliveryRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JddjOrderSerllerDeliveryRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjOrderDetailRequest struct {
	// 订单ID
	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	// 商家门店编码
	DeliveryStationNoIsv string `protobuf:"bytes,2,opt,name=deliveryStationNoIsv,proto3" json:"deliveryStationNoIsv"`
	// 店铺主体Id
	StoreMasterId int32 `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	// 订单开始时间(开始) 2016-05-05 00:00:00
	OrderStartTimeBegin string `protobuf:"bytes,4,opt,name=orderStartTime_begin,json=orderStartTimeBegin,proto3" json:"orderStartTime_begin"`
	// 订单开始时间(结束) 2016-05-08 23:00:00
	OrderStartTimeEnd    string   `protobuf:"bytes,5,opt,name=orderStartTime_end,json=orderStartTimeEnd,proto3" json:"orderStartTime_end"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOrderDetailRequest) Reset()         { *m = JddjOrderDetailRequest{} }
func (m *JddjOrderDetailRequest) String() string { return proto.CompactTextString(m) }
func (*JddjOrderDetailRequest) ProtoMessage()    {}
func (*JddjOrderDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{59}
}

func (m *JddjOrderDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderDetailRequest.Unmarshal(m, b)
}
func (m *JddjOrderDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderDetailRequest.Marshal(b, m, deterministic)
}
func (m *JddjOrderDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderDetailRequest.Merge(m, src)
}
func (m *JddjOrderDetailRequest) XXX_Size() int {
	return xxx_messageInfo_JddjOrderDetailRequest.Size(m)
}
func (m *JddjOrderDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderDetailRequest proto.InternalMessageInfo

func (m *JddjOrderDetailRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JddjOrderDetailRequest) GetDeliveryStationNoIsv() string {
	if m != nil {
		return m.DeliveryStationNoIsv
	}
	return ""
}

func (m *JddjOrderDetailRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

func (m *JddjOrderDetailRequest) GetOrderStartTimeBegin() string {
	if m != nil {
		return m.OrderStartTimeBegin
	}
	return ""
}

func (m *JddjOrderDetailRequest) GetOrderStartTimeEnd() string {
	if m != nil {
		return m.OrderStartTimeEnd
	}
	return ""
}

//获取饿了么订单详情返回参数
type JddjOrderDetailResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//饿了么订单详情
	JddjData             *JddjData `protobuf:"bytes,4,opt,name=JddjData,proto3" json:"JddjData"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *JddjOrderDetailResponse) Reset()         { *m = JddjOrderDetailResponse{} }
func (m *JddjOrderDetailResponse) String() string { return proto.CompactTextString(m) }
func (*JddjOrderDetailResponse) ProtoMessage()    {}
func (*JddjOrderDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{60}
}

func (m *JddjOrderDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderDetailResponse.Unmarshal(m, b)
}
func (m *JddjOrderDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderDetailResponse.Marshal(b, m, deterministic)
}
func (m *JddjOrderDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderDetailResponse.Merge(m, src)
}
func (m *JddjOrderDetailResponse) XXX_Size() int {
	return xxx_messageInfo_JddjOrderDetailResponse.Size(m)
}
func (m *JddjOrderDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderDetailResponse proto.InternalMessageInfo

func (m *JddjOrderDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *JddjOrderDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *JddjOrderDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *JddjOrderDetailResponse) GetJddjData() *JddjData {
	if m != nil {
		return m.JddjData
	}
	return nil
}

type JddjData struct {
	//状态码
	Code                 string      `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Msg                  string      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	JddjResult           *JddjResult `protobuf:"bytes,3,opt,name=JddjResult,proto3" json:"JddjResult"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *JddjData) Reset()         { *m = JddjData{} }
func (m *JddjData) String() string { return proto.CompactTextString(m) }
func (*JddjData) ProtoMessage()    {}
func (*JddjData) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{61}
}

func (m *JddjData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjData.Unmarshal(m, b)
}
func (m *JddjData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjData.Marshal(b, m, deterministic)
}
func (m *JddjData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjData.Merge(m, src)
}
func (m *JddjData) XXX_Size() int {
	return xxx_messageInfo_JddjData.Size(m)
}
func (m *JddjData) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjData.DiscardUnknown(m)
}

var xxx_messageInfo_JddjData proto.InternalMessageInfo

func (m *JddjData) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *JddjData) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *JddjData) GetJddjResult() *JddjResult {
	if m != nil {
		return m.JddjResult
	}
	return nil
}

type JddjResult struct {
	PageNo               int32         `protobuf:"varint,1,opt,name=pageNo,proto3" json:"pageNo"`
	PageSize             int32         `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	MaxPageSize          int32         `protobuf:"varint,3,opt,name=maxPageSize,proto3" json:"maxPageSize"`
	TotalCount           int32         `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	JddjResultList       []*ResultList `protobuf:"bytes,5,rep,name=JddjResultList,proto3" json:"JddjResultList"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *JddjResult) Reset()         { *m = JddjResult{} }
func (m *JddjResult) String() string { return proto.CompactTextString(m) }
func (*JddjResult) ProtoMessage()    {}
func (*JddjResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{62}
}

func (m *JddjResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjResult.Unmarshal(m, b)
}
func (m *JddjResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjResult.Marshal(b, m, deterministic)
}
func (m *JddjResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjResult.Merge(m, src)
}
func (m *JddjResult) XXX_Size() int {
	return xxx_messageInfo_JddjResult.Size(m)
}
func (m *JddjResult) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjResult.DiscardUnknown(m)
}

var xxx_messageInfo_JddjResult proto.InternalMessageInfo

func (m *JddjResult) GetPageNo() int32 {
	if m != nil {
		return m.PageNo
	}
	return 0
}

func (m *JddjResult) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *JddjResult) GetMaxPageSize() int32 {
	if m != nil {
		return m.MaxPageSize
	}
	return 0
}

func (m *JddjResult) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *JddjResult) GetJddjResultList() []*ResultList {
	if m != nil {
		return m.JddjResultList
	}
	return nil
}

type ResultList struct {
	//订单号
	OrderId int64 `protobuf:"varint,1,opt,name=orderId,proto3" json:"orderId"`
	//31000:等待付款，31020:已付款，41000:待处理，32000:等待出库，33040:配送中，33060:已妥投，90000:订单完成）
	OrderStatus int32 `protobuf:"varint,2,opt,name=orderStatus,proto3" json:"orderStatus"`
	//预计送达开始时间
	OrderPreStartDeliveryTime string `protobuf:"bytes,3,opt,name=orderPreStartDeliveryTime,proto3" json:"orderPreStartDeliveryTime"`
	//预计送达结束时间
	OrderPreEndDeliveryTime string `protobuf:"bytes,4,opt,name=orderPreEndDeliveryTime,proto3" json:"orderPreEndDeliveryTime"`
	//商家编码
	OrgCode string `protobuf:"bytes,5,opt,name=orgCode,proto3" json:"orgCode"`
	//收货人名称
	BuyerFullName string `protobuf:"bytes,7,opt,name=buyerFullName,proto3" json:"buyerFullName"`
	//收货人地址
	BuyerFullAddress string `protobuf:"bytes,8,opt,name=buyerFullAddress,proto3" json:"buyerFullAddress"`
	//收货人电话
	BuyerTelephone string `protobuf:"bytes,9,opt,name=buyerTelephone,proto3" json:"buyerTelephone"`
	//收货人手机号
	BuyerMobile string `protobuf:"bytes,10,opt,name=buyerMobile,proto3" json:"buyerMobile"`
	//到家配送门店编码
	DeliveryStationNo string `protobuf:"bytes,11,opt,name=deliveryStationNo,proto3" json:"deliveryStationNo"`
	//商家门店编码
	DeliveryStationNoIsv string `protobuf:"bytes,12,opt,name=deliveryStationNoIsv,proto3" json:"deliveryStationNoIsv"`
	//配送门店名称
	DeliveryStationName string `protobuf:"bytes,13,opt,name=deliveryStationName,proto3" json:"deliveryStationName"`
	//承运商编号(9966:京东众包;2938:商家自送;1130:达达同城送;9999:到店自提)
	DeliveryCarrierNo string `protobuf:"bytes,14,opt,name=deliveryCarrierNo,proto3" json:"deliveryCarrierNo"`
	//承运商名称
	DeliveryCarrierName string `protobuf:"bytes,15,opt,name=deliveryCarrierName,proto3" json:"deliveryCarrierName"`
	//包裹重量（单位：kg）
	DeliveryPackageWeight float64 `protobuf:"fixed64,16,opt,name=deliveryPackageWeight,proto3" json:"deliveryPackageWeight"`
	//妥投时间
	DeliveryConfirmTime string `protobuf:"bytes,17,opt,name=deliveryConfirmTime,proto3" json:"deliveryConfirmTime"`
	//订单支付类型(1：货到付款，4:在线支付;)
	OrderPayType int32 `protobuf:"varint,18,opt,name=orderPayType,proto3" json:"orderPayType"`
	//订单支付渠道，8001：微信支付；8002：微信免密代扣；8003：微信找人代付；9000：
	//京东支付（无法判断具体京东支付子类型）；9002：京东银行卡支付；9004：京东白条支付；9012：京东余额支付；9022：京东小金库支付
	PayChannel int32 `protobuf:"varint,19,opt,name=payChannel,proto3" json:"payChannel"`
	//订单商品销售价总金额，等于sum（京东到家销售价skuJdPrice*商品下单数量skuCount）
	OrderTotalMoney int32 `protobuf:"varint,20,opt,name=orderTotalMoney,proto3" json:"orderTotalMoney"`
	//订单级别优惠商品金额：(不含单品促销类优惠金额及运费相关优惠金额)，
	//等于OrderDiscountlist表中，除优惠类型7，8，12，15，16外的优惠金额discountPrice累加和
	OrderDiscountMoney int32 `protobuf:"varint,21,opt,name=orderDiscountMoney,proto3" json:"orderDiscountMoney"`
	//用户支付的实际订单运费：订单应收运费（orderReceivableFreight）-运费优惠（OrderDiscountlist表中，
	//优惠类型7，8，12，15的优惠金额。运费优惠大于应收运费时，实际支付为0
	OrderFreightMoney int32 `protobuf:"varint,22,opt,name=orderFreightMoney,proto3" json:"orderFreightMoney"`
	//达达同城送运费(单位：分)
	LocalDeliveryMoney int32 `protobuf:"varint,23,opt,name=localDeliveryMoney,proto3" json:"localDeliveryMoney"`
	//商家支付远距离运费(单位：分)。达达配送默认只能服务半径2公里内的用户，
	//商家可与到家运营沟通开通远距离服务，超过2公里后每1公里加收2元运费。费用承担方为商家
	MerchantPaymentDistanceFreightMoney int32 `protobuf:"varint,24,opt,name=merchantPaymentDistanceFreightMoney,proto3" json:"merchantPaymentDistanceFreightMoney"`
	//订单应收运费：用户应该支付的订单运费，即未优惠前应付运费(不计满免运费，运费优惠券，VIP免基础运费等优惠)，
	//包含用户小费。订单对应门店配送方式为商家自送，则订单应收运费为设置的门店自送运费；订单对应门店配送方式为达达配送，则订单应收运费为用户支付给达达的配送费（平台规则统一设置，如基础运费、重量阶梯运费、距离阶梯运费、夜间或天气等因素的附加运费）
	OrderReceivableFreight int32 `protobuf:"varint,25,opt,name=orderReceivableFreight,proto3" json:"orderReceivableFreight"`
	//用户积分抵扣金额
	PlatformPointsDeductionMoney int32 `protobuf:"varint,26,opt,name=platformPointsDeductionMoney,proto3" json:"platformPointsDeductionMoney"`
	//用户应付金额（单位为分）=商品销售价总金额orderTotalMoney -订单优惠总金额 orderDiscountMoney+
	//实际订单运费orderFreightMoney +包装金额packagingMoney -用户积分抵扣金额platformPointsDeductionMoney
	OrderBuyerPayableMoney int32 `protobuf:"varint,27,opt,name=orderBuyerPayableMoney,proto3" json:"orderBuyerPayableMoney"`
	//包装金额
	PackagingMoney int32 `protobuf:"varint,28,opt,name=packagingMoney,proto3" json:"packagingMoney"`
	//收货人地址腾讯坐标经度
	BuyerLng float64 `protobuf:"fixed64,29,opt,name=buyerLng,proto3" json:"buyerLng"`
	//收货人地址腾讯坐标纬度
	BuyerLat float64 `protobuf:"fixed64,30,opt,name=buyerLat,proto3" json:"buyerLat"`
	//收货人市名称
	BuyerCityName string `protobuf:"bytes,31,opt,name=buyerCityName,proto3" json:"buyerCityName"`
	//收货人县(区)名称
	BuyerCountryName string `protobuf:"bytes,32,opt,name=buyerCountryName,proto3" json:"buyerCountryName"`
	//订单买家备注
	OrderBuyerRemark string `protobuf:"bytes,33,opt,name=orderBuyerRemark,proto3" json:"orderBuyerRemark"`
	//订单开发票标识（1.开发票；2.不开发票）
	OrderInvoiceOpenMark int32              `protobuf:"varint,34,opt,name=orderInvoiceOpenMark,proto3" json:"orderInvoiceOpenMark"`
	JddjOrderInvoice     *JddjOrderInvoice  `protobuf:"bytes,35,opt,name=JddjOrderInvoice,proto3" json:"JddjOrderInvoice"`
	JddjProductList      []*JddjProductList `protobuf:"bytes,36,rep,name=JddjProductList,proto3" json:"JddjProductList"`
	JddjDiscount         []*JddjDiscount    `protobuf:"bytes,37,rep,name=JddjDiscount,proto3" json:"JddjDiscount"`
	//下单时间
	OrderStartTime string `protobuf:"bytes,38,opt,name=orderStartTime,proto3" json:"orderStartTime"`
	BusinessTag    string `protobuf:"bytes,39,opt,name=businessTag,proto3" json:"businessTag"`
	// 订单来源系统
	SrcOrderType int32 `protobuf:"varint,40,opt,name=srcOrderType,proto3" json:"srcOrderType"`
	//订单取消时间
	OrderCancelTime      string   `protobuf:"bytes,41,opt,name=orderCancelTime,proto3" json:"orderCancelTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResultList) Reset()         { *m = ResultList{} }
func (m *ResultList) String() string { return proto.CompactTextString(m) }
func (*ResultList) ProtoMessage()    {}
func (*ResultList) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{63}
}

func (m *ResultList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResultList.Unmarshal(m, b)
}
func (m *ResultList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResultList.Marshal(b, m, deterministic)
}
func (m *ResultList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResultList.Merge(m, src)
}
func (m *ResultList) XXX_Size() int {
	return xxx_messageInfo_ResultList.Size(m)
}
func (m *ResultList) XXX_DiscardUnknown() {
	xxx_messageInfo_ResultList.DiscardUnknown(m)
}

var xxx_messageInfo_ResultList proto.InternalMessageInfo

func (m *ResultList) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *ResultList) GetOrderStatus() int32 {
	if m != nil {
		return m.OrderStatus
	}
	return 0
}

func (m *ResultList) GetOrderPreStartDeliveryTime() string {
	if m != nil {
		return m.OrderPreStartDeliveryTime
	}
	return ""
}

func (m *ResultList) GetOrderPreEndDeliveryTime() string {
	if m != nil {
		return m.OrderPreEndDeliveryTime
	}
	return ""
}

func (m *ResultList) GetOrgCode() string {
	if m != nil {
		return m.OrgCode
	}
	return ""
}

func (m *ResultList) GetBuyerFullName() string {
	if m != nil {
		return m.BuyerFullName
	}
	return ""
}

func (m *ResultList) GetBuyerFullAddress() string {
	if m != nil {
		return m.BuyerFullAddress
	}
	return ""
}

func (m *ResultList) GetBuyerTelephone() string {
	if m != nil {
		return m.BuyerTelephone
	}
	return ""
}

func (m *ResultList) GetBuyerMobile() string {
	if m != nil {
		return m.BuyerMobile
	}
	return ""
}

func (m *ResultList) GetDeliveryStationNo() string {
	if m != nil {
		return m.DeliveryStationNo
	}
	return ""
}

func (m *ResultList) GetDeliveryStationNoIsv() string {
	if m != nil {
		return m.DeliveryStationNoIsv
	}
	return ""
}

func (m *ResultList) GetDeliveryStationName() string {
	if m != nil {
		return m.DeliveryStationName
	}
	return ""
}

func (m *ResultList) GetDeliveryCarrierNo() string {
	if m != nil {
		return m.DeliveryCarrierNo
	}
	return ""
}

func (m *ResultList) GetDeliveryCarrierName() string {
	if m != nil {
		return m.DeliveryCarrierName
	}
	return ""
}

func (m *ResultList) GetDeliveryPackageWeight() float64 {
	if m != nil {
		return m.DeliveryPackageWeight
	}
	return 0
}

func (m *ResultList) GetDeliveryConfirmTime() string {
	if m != nil {
		return m.DeliveryConfirmTime
	}
	return ""
}

func (m *ResultList) GetOrderPayType() int32 {
	if m != nil {
		return m.OrderPayType
	}
	return 0
}

func (m *ResultList) GetPayChannel() int32 {
	if m != nil {
		return m.PayChannel
	}
	return 0
}

func (m *ResultList) GetOrderTotalMoney() int32 {
	if m != nil {
		return m.OrderTotalMoney
	}
	return 0
}

func (m *ResultList) GetOrderDiscountMoney() int32 {
	if m != nil {
		return m.OrderDiscountMoney
	}
	return 0
}

func (m *ResultList) GetOrderFreightMoney() int32 {
	if m != nil {
		return m.OrderFreightMoney
	}
	return 0
}

func (m *ResultList) GetLocalDeliveryMoney() int32 {
	if m != nil {
		return m.LocalDeliveryMoney
	}
	return 0
}

func (m *ResultList) GetMerchantPaymentDistanceFreightMoney() int32 {
	if m != nil {
		return m.MerchantPaymentDistanceFreightMoney
	}
	return 0
}

func (m *ResultList) GetOrderReceivableFreight() int32 {
	if m != nil {
		return m.OrderReceivableFreight
	}
	return 0
}

func (m *ResultList) GetPlatformPointsDeductionMoney() int32 {
	if m != nil {
		return m.PlatformPointsDeductionMoney
	}
	return 0
}

func (m *ResultList) GetOrderBuyerPayableMoney() int32 {
	if m != nil {
		return m.OrderBuyerPayableMoney
	}
	return 0
}

func (m *ResultList) GetPackagingMoney() int32 {
	if m != nil {
		return m.PackagingMoney
	}
	return 0
}

func (m *ResultList) GetBuyerLng() float64 {
	if m != nil {
		return m.BuyerLng
	}
	return 0
}

func (m *ResultList) GetBuyerLat() float64 {
	if m != nil {
		return m.BuyerLat
	}
	return 0
}

func (m *ResultList) GetBuyerCityName() string {
	if m != nil {
		return m.BuyerCityName
	}
	return ""
}

func (m *ResultList) GetBuyerCountryName() string {
	if m != nil {
		return m.BuyerCountryName
	}
	return ""
}

func (m *ResultList) GetOrderBuyerRemark() string {
	if m != nil {
		return m.OrderBuyerRemark
	}
	return ""
}

func (m *ResultList) GetOrderInvoiceOpenMark() int32 {
	if m != nil {
		return m.OrderInvoiceOpenMark
	}
	return 0
}

func (m *ResultList) GetJddjOrderInvoice() *JddjOrderInvoice {
	if m != nil {
		return m.JddjOrderInvoice
	}
	return nil
}

func (m *ResultList) GetJddjProductList() []*JddjProductList {
	if m != nil {
		return m.JddjProductList
	}
	return nil
}

func (m *ResultList) GetJddjDiscount() []*JddjDiscount {
	if m != nil {
		return m.JddjDiscount
	}
	return nil
}

func (m *ResultList) GetOrderStartTime() string {
	if m != nil {
		return m.OrderStartTime
	}
	return ""
}

func (m *ResultList) GetBusinessTag() string {
	if m != nil {
		return m.BusinessTag
	}
	return ""
}

func (m *ResultList) GetSrcOrderType() int32 {
	if m != nil {
		return m.SrcOrderType
	}
	return 0
}

func (m *ResultList) GetOrderCancelTime() string {
	if m != nil {
		return m.OrderCancelTime
	}
	return ""
}

type JddjOrderInvoice struct {
	//发票抬头
	InvoiceTitle string `protobuf:"bytes,1,opt,name=invoiceTitle,proto3" json:"invoiceTitle"`
	//发票税号
	InvoiceDutyNo        string   `protobuf:"bytes,2,opt,name=invoiceDutyNo,proto3" json:"invoiceDutyNo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOrderInvoice) Reset()         { *m = JddjOrderInvoice{} }
func (m *JddjOrderInvoice) String() string { return proto.CompactTextString(m) }
func (*JddjOrderInvoice) ProtoMessage()    {}
func (*JddjOrderInvoice) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{64}
}

func (m *JddjOrderInvoice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderInvoice.Unmarshal(m, b)
}
func (m *JddjOrderInvoice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderInvoice.Marshal(b, m, deterministic)
}
func (m *JddjOrderInvoice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderInvoice.Merge(m, src)
}
func (m *JddjOrderInvoice) XXX_Size() int {
	return xxx_messageInfo_JddjOrderInvoice.Size(m)
}
func (m *JddjOrderInvoice) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderInvoice.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderInvoice proto.InternalMessageInfo

func (m *JddjOrderInvoice) GetInvoiceTitle() string {
	if m != nil {
		return m.InvoiceTitle
	}
	return ""
}

func (m *JddjOrderInvoice) GetInvoiceDutyNo() string {
	if m != nil {
		return m.InvoiceDutyNo
	}
	return ""
}

type JddjProductList struct {
	//订单号
	OrderId int64 `protobuf:"varint,1,opt,name=orderId,proto3" json:"orderId"`
	//商品规格，多规格之间用英文分号;分隔
	SkuCostumeProperty string `protobuf:"bytes,2,opt,name=skuCostumeProperty,proto3" json:"skuCostumeProperty"`
	//调整单记录id（0:原单商品明细;非0:调整单id 或者 确认单id)
	AdjustId int64 `protobuf:"varint,3,opt,name=adjustId,proto3" json:"adjustId"`
	//到家商品编码
	SkuId int64 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//商品的名称
	SkuName string `protobuf:"bytes,5,opt,name=skuName,proto3" json:"skuName"`
	//商家商品编码
	SkuIdIsv string `protobuf:"bytes,6,opt,name=skuIdIsv,proto3" json:"skuIdIsv"`
	//到家商品销售价
	SkuJdPrice int32 `protobuf:"varint,7,opt,name=skuJdPrice,proto3" json:"skuJdPrice"`
	//下单数量
	SkuCount int32 `protobuf:"varint,8,opt,name=skuCount,proto3" json:"skuCount"`
	//调整方式(0:默认值，没调整，原订单明细;1:新增;2:删除;3:修改数量)
	AdjustMode int32 `protobuf:"varint,9,opt,name=adjustMode,proto3" json:"adjustMode"`
	//商品upc码
	UpcCode string `protobuf:"bytes,10,opt,name=upcCode,proto3" json:"upcCode"`
	//到家商品门店价
	SkuStorePrice int32 `protobuf:"varint,11,opt,name=skuStorePrice,proto3" json:"skuStorePrice"`
	//到家商品成本价
	SkuCostPrice int32 `protobuf:"varint,12,opt,name=skuCostPrice,proto3" json:"skuCostPrice"`
	//商品级别促销类型(1、无优惠;2、秒杀(已经下线);3、单品直降;4、限时抢购;1202、
	//加价购;1203、满赠(标识商品);6、买赠(买A送B，标识B);9999、表示一个普通商品参与捆绑促销，
	//设置的捆绑类型;9998、表示一个商品参与了捆绑促销，并且还参与了其他促销类型;9997、
	//表示一个商品参与了捆绑促销，但是金额拆分不尽,9996:组合购,8001:商家会员价,
	//8:第二件N折,9:拼团促销)
	PromotionType int32 `protobuf:"varint,13,opt,name=promotionType,proto3" json:"promotionType"`
	//税率
	SkuTaxRate string `protobuf:"bytes,14,opt,name=skuTaxRate,proto3" json:"skuTaxRate"`
	//促销活动编码
	PromotionId int32 `protobuf:"varint,15,opt,name=promotionId,proto3" json:"promotionId"`
	//赠品关联的主商品信息，多个商品之间英文逗号分隔
	RelatedSkus string `protobuf:"bytes,16,opt,name=relatedSkus,proto3" json:"relatedSkus"`
	//商品维度的单个商品重量（千克）
	SkuWeight float32 `protobuf:"fixed32,17,opt,name=skuWeight,proto3" json:"skuWeight"`
	//餐盒费（商品的总餐盒费）
	CanteenMoney         int32    `protobuf:"varint,18,opt,name=canteenMoney,proto3" json:"canteenMoney"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjProductList) Reset()         { *m = JddjProductList{} }
func (m *JddjProductList) String() string { return proto.CompactTextString(m) }
func (*JddjProductList) ProtoMessage()    {}
func (*JddjProductList) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{65}
}

func (m *JddjProductList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjProductList.Unmarshal(m, b)
}
func (m *JddjProductList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjProductList.Marshal(b, m, deterministic)
}
func (m *JddjProductList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjProductList.Merge(m, src)
}
func (m *JddjProductList) XXX_Size() int {
	return xxx_messageInfo_JddjProductList.Size(m)
}
func (m *JddjProductList) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjProductList.DiscardUnknown(m)
}

var xxx_messageInfo_JddjProductList proto.InternalMessageInfo

func (m *JddjProductList) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *JddjProductList) GetSkuCostumeProperty() string {
	if m != nil {
		return m.SkuCostumeProperty
	}
	return ""
}

func (m *JddjProductList) GetAdjustId() int64 {
	if m != nil {
		return m.AdjustId
	}
	return 0
}

func (m *JddjProductList) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *JddjProductList) GetSkuName() string {
	if m != nil {
		return m.SkuName
	}
	return ""
}

func (m *JddjProductList) GetSkuIdIsv() string {
	if m != nil {
		return m.SkuIdIsv
	}
	return ""
}

func (m *JddjProductList) GetSkuJdPrice() int32 {
	if m != nil {
		return m.SkuJdPrice
	}
	return 0
}

func (m *JddjProductList) GetSkuCount() int32 {
	if m != nil {
		return m.SkuCount
	}
	return 0
}

func (m *JddjProductList) GetAdjustMode() int32 {
	if m != nil {
		return m.AdjustMode
	}
	return 0
}

func (m *JddjProductList) GetUpcCode() string {
	if m != nil {
		return m.UpcCode
	}
	return ""
}

func (m *JddjProductList) GetSkuStorePrice() int32 {
	if m != nil {
		return m.SkuStorePrice
	}
	return 0
}

func (m *JddjProductList) GetSkuCostPrice() int32 {
	if m != nil {
		return m.SkuCostPrice
	}
	return 0
}

func (m *JddjProductList) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *JddjProductList) GetSkuTaxRate() string {
	if m != nil {
		return m.SkuTaxRate
	}
	return ""
}

func (m *JddjProductList) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *JddjProductList) GetRelatedSkus() string {
	if m != nil {
		return m.RelatedSkus
	}
	return ""
}

func (m *JddjProductList) GetSkuWeight() float32 {
	if m != nil {
		return m.SkuWeight
	}
	return 0
}

func (m *JddjProductList) GetCanteenMoney() int32 {
	if m != nil {
		return m.CanteenMoney
	}
	return 0
}

type JddjDiscount struct {
	//订单主表订单id
	OrderID int64 `protobuf:"varint,1,opt,name=OrderID,proto3" json:"OrderID"`
	//调整单记录id（0:原单商品明细;非0:调整单id 或者 确认单id)
	AdjustID int64 `protobuf:"varint,2,opt,name=AdjustID,proto3" json:"AdjustID"`
	//记录参加活动的sku数组
	SkuIds string `protobuf:"bytes,3,opt,name=SkuIds,proto3" json:"SkuIds"`
	//优惠类型(1:优惠码;3:优惠劵;4:满减;5:满折;6:首单优惠;7:VIP免运费;
	//8:商家满免运费;10:满件减;11:满件折;12:首单地推满免运费;15:运费券;16:单品免运)
	DiscountType int32 `protobuf:"varint,4,opt,name=DiscountType,proto3" json:"DiscountType"`
	//小优惠类型(优惠码(1:满减;2:立减;3:满折);优惠券(1:满减;2:立减;5满折);
	//满减(1:满减);商家满免运费(null或0：平台承担。1：商家全免运费（包括商家促销免运、商家会员免运），商家承担补贴。
	//2：商家免基础运费（包括商家促销免运），商家承担补贴。
	//3：商家会员免基础运费（包括商家会员免运），商家承担补贴。
	//4：拼团免运。);满件减(1206:满件减);满件折(1207:满件折);运费券（1：满减，2：立减）)
	DiscountDetailType int32 `protobuf:"varint,5,opt,name=DiscountDetailType,proto3" json:"DiscountDetailType"`
	//用户领券编号，用户领取优惠券的标识，返回值中，-和#中间的字符串为对应优惠券实际编码，不同用户领券标识不同
	DiscountCode string `protobuf:"bytes,6,opt,name=DiscountCode,proto3" json:"DiscountCode"`
	//优惠金额
	DiscountPrice int32 `protobuf:"varint,7,opt,name=DiscountPrice,proto3" json:"DiscountPrice"`
	//商家承担金额，有运费券（discountType=15）或单品免运（discountType=16）时才会有值，其他优惠无值
	VenderPayMoney int32 `protobuf:"varint,8,opt,name=VenderPayMoney,proto3" json:"VenderPayMoney"`
	//平台承担金额，有运费券（discountType=15）或单品免运（discountType=16）时才会有值，其他优惠无值
	PlatPayMoney         int32    `protobuf:"varint,9,opt,name=PlatPayMoney,proto3" json:"PlatPayMoney"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjDiscount) Reset()         { *m = JddjDiscount{} }
func (m *JddjDiscount) String() string { return proto.CompactTextString(m) }
func (*JddjDiscount) ProtoMessage()    {}
func (*JddjDiscount) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{66}
}

func (m *JddjDiscount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjDiscount.Unmarshal(m, b)
}
func (m *JddjDiscount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjDiscount.Marshal(b, m, deterministic)
}
func (m *JddjDiscount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjDiscount.Merge(m, src)
}
func (m *JddjDiscount) XXX_Size() int {
	return xxx_messageInfo_JddjDiscount.Size(m)
}
func (m *JddjDiscount) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjDiscount.DiscardUnknown(m)
}

var xxx_messageInfo_JddjDiscount proto.InternalMessageInfo

func (m *JddjDiscount) GetOrderID() int64 {
	if m != nil {
		return m.OrderID
	}
	return 0
}

func (m *JddjDiscount) GetAdjustID() int64 {
	if m != nil {
		return m.AdjustID
	}
	return 0
}

func (m *JddjDiscount) GetSkuIds() string {
	if m != nil {
		return m.SkuIds
	}
	return ""
}

func (m *JddjDiscount) GetDiscountType() int32 {
	if m != nil {
		return m.DiscountType
	}
	return 0
}

func (m *JddjDiscount) GetDiscountDetailType() int32 {
	if m != nil {
		return m.DiscountDetailType
	}
	return 0
}

func (m *JddjDiscount) GetDiscountCode() string {
	if m != nil {
		return m.DiscountCode
	}
	return ""
}

func (m *JddjDiscount) GetDiscountPrice() int32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *JddjDiscount) GetVenderPayMoney() int32 {
	if m != nil {
		return m.VenderPayMoney
	}
	return 0
}

func (m *JddjDiscount) GetPlatPayMoney() int32 {
	if m != nil {
		return m.PlatPayMoney
	}
	return 0
}

type JddjOrderShoudSettlementResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string                `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	OrderShoudSettlement *OrderShoudSettlement `protobuf:"bytes,4,opt,name=OrderShoudSettlement,proto3" json:"OrderShoudSettlement"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *JddjOrderShoudSettlementResponse) Reset()         { *m = JddjOrderShoudSettlementResponse{} }
func (m *JddjOrderShoudSettlementResponse) String() string { return proto.CompactTextString(m) }
func (*JddjOrderShoudSettlementResponse) ProtoMessage()    {}
func (*JddjOrderShoudSettlementResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{67}
}

func (m *JddjOrderShoudSettlementResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOrderShoudSettlementResponse.Unmarshal(m, b)
}
func (m *JddjOrderShoudSettlementResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOrderShoudSettlementResponse.Marshal(b, m, deterministic)
}
func (m *JddjOrderShoudSettlementResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOrderShoudSettlementResponse.Merge(m, src)
}
func (m *JddjOrderShoudSettlementResponse) XXX_Size() int {
	return xxx_messageInfo_JddjOrderShoudSettlementResponse.Size(m)
}
func (m *JddjOrderShoudSettlementResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOrderShoudSettlementResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOrderShoudSettlementResponse proto.InternalMessageInfo

func (m *JddjOrderShoudSettlementResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *JddjOrderShoudSettlementResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *JddjOrderShoudSettlementResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *JddjOrderShoudSettlementResponse) GetOrderShoudSettlement() *OrderShoudSettlement {
	if m != nil {
		return m.OrderShoudSettlement
	}
	return nil
}

type OrderShoudSettlement struct {
	//状态码
	OrderId              int64    `protobuf:"varint,1,opt,name=OrderId,proto3" json:"OrderId"`
	SettlementAmount     int64    `protobuf:"varint,2,opt,name=SettlementAmount,proto3" json:"SettlementAmount"`
	GoodsCommission      int64    `protobuf:"varint,3,opt,name=GoodsCommission,proto3" json:"GoodsCommission"`
	FreightCommission    int64    `protobuf:"varint,4,opt,name=FreightCommission,proto3" json:"FreightCommission"`
	PckageCommission     int64    `protobuf:"varint,5,opt,name=PckageCommission,proto3" json:"PckageCommission"`
	GuaranteedCommission int64    `protobuf:"varint,6,opt,name=GuaranteedCommission,proto3" json:"GuaranteedCommission"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderShoudSettlement) Reset()         { *m = OrderShoudSettlement{} }
func (m *OrderShoudSettlement) String() string { return proto.CompactTextString(m) }
func (*OrderShoudSettlement) ProtoMessage()    {}
func (*OrderShoudSettlement) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{68}
}

func (m *OrderShoudSettlement) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderShoudSettlement.Unmarshal(m, b)
}
func (m *OrderShoudSettlement) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderShoudSettlement.Marshal(b, m, deterministic)
}
func (m *OrderShoudSettlement) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderShoudSettlement.Merge(m, src)
}
func (m *OrderShoudSettlement) XXX_Size() int {
	return xxx_messageInfo_OrderShoudSettlement.Size(m)
}
func (m *OrderShoudSettlement) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderShoudSettlement.DiscardUnknown(m)
}

var xxx_messageInfo_OrderShoudSettlement proto.InternalMessageInfo

func (m *OrderShoudSettlement) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *OrderShoudSettlement) GetSettlementAmount() int64 {
	if m != nil {
		return m.SettlementAmount
	}
	return 0
}

func (m *OrderShoudSettlement) GetGoodsCommission() int64 {
	if m != nil {
		return m.GoodsCommission
	}
	return 0
}

func (m *OrderShoudSettlement) GetFreightCommission() int64 {
	if m != nil {
		return m.FreightCommission
	}
	return 0
}

func (m *OrderShoudSettlement) GetPckageCommission() int64 {
	if m != nil {
		return m.PckageCommission
	}
	return 0
}

func (m *OrderShoudSettlement) GetGuaranteedCommission() int64 {
	if m != nil {
		return m.GuaranteedCommission
	}
	return 0
}

type JddjAdjustOrderRequest struct {
	//外部订单号
	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	//操作人
	OperPin string `protobuf:"bytes,2,opt,name=operPin,proto3" json:"operPin"`
	//操作备注
	Remark string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`
	//订单调整后的所有商品明细（含未调整的商品），如果某商品数量调整为0时，商品明细中不能包含该商品；如果全部商品数量调整为0时，需联系顾客沟通确认后，由顾客取消订单。
	AdjustList []*JddjOAOSAdjust `protobuf:"bytes,4,rep,name=AdjustList,proto3" json:"AdjustList"`
	//订单id
	AwOrderId string `protobuf:"bytes,5,opt,name=awOrder_id,json=awOrderId,proto3" json:"awOrder_id"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,6,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjAdjustOrderRequest) Reset()         { *m = JddjAdjustOrderRequest{} }
func (m *JddjAdjustOrderRequest) String() string { return proto.CompactTextString(m) }
func (*JddjAdjustOrderRequest) ProtoMessage()    {}
func (*JddjAdjustOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{69}
}

func (m *JddjAdjustOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjAdjustOrderRequest.Unmarshal(m, b)
}
func (m *JddjAdjustOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjAdjustOrderRequest.Marshal(b, m, deterministic)
}
func (m *JddjAdjustOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjAdjustOrderRequest.Merge(m, src)
}
func (m *JddjAdjustOrderRequest) XXX_Size() int {
	return xxx_messageInfo_JddjAdjustOrderRequest.Size(m)
}
func (m *JddjAdjustOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjAdjustOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjAdjustOrderRequest proto.InternalMessageInfo

func (m *JddjAdjustOrderRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JddjAdjustOrderRequest) GetOperPin() string {
	if m != nil {
		return m.OperPin
	}
	return ""
}

func (m *JddjAdjustOrderRequest) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *JddjAdjustOrderRequest) GetAdjustList() []*JddjOAOSAdjust {
	if m != nil {
		return m.AdjustList
	}
	return nil
}

func (m *JddjAdjustOrderRequest) GetAwOrderId() string {
	if m != nil {
		return m.AwOrderId
	}
	return ""
}

func (m *JddjAdjustOrderRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjOAOSAdjust struct {
	//商家商品编码(skuId与outSkuId至少填一个)
	OutSkuId string `protobuf:"bytes,1,opt,name=outSkuId,proto3" json:"outSkuId"`
	//商品数量
	SkuCount             int32    `protobuf:"varint,2,opt,name=skuCount,proto3" json:"skuCount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjOAOSAdjust) Reset()         { *m = JddjOAOSAdjust{} }
func (m *JddjOAOSAdjust) String() string { return proto.CompactTextString(m) }
func (*JddjOAOSAdjust) ProtoMessage()    {}
func (*JddjOAOSAdjust) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{70}
}

func (m *JddjOAOSAdjust) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjOAOSAdjust.Unmarshal(m, b)
}
func (m *JddjOAOSAdjust) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjOAOSAdjust.Marshal(b, m, deterministic)
}
func (m *JddjOAOSAdjust) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjOAOSAdjust.Merge(m, src)
}
func (m *JddjOAOSAdjust) XXX_Size() int {
	return xxx_messageInfo_JddjOAOSAdjust.Size(m)
}
func (m *JddjOAOSAdjust) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjOAOSAdjust.DiscardUnknown(m)
}

var xxx_messageInfo_JddjOAOSAdjust proto.InternalMessageInfo

func (m *JddjOAOSAdjust) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *JddjOAOSAdjust) GetSkuCount() int32 {
	if m != nil {
		return m.SkuCount
	}
	return 0
}

type JddjAdjustOrderResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//订单号(外部订单号)
	OrderId int64 `protobuf:"varint,4,opt,name=orderId,proto3" json:"orderId"`
	//缺货商品总数
	AdjustCount int32 `protobuf:"varint,5,opt,name=adjustCount,proto3" json:"adjustCount"`
	//门店编码
	ProduceStationNo string `protobuf:"bytes,6,opt,name=ProduceStationNo,proto3" json:"ProduceStationNo"`
	//调整后的订单总金额
	OrderTotalMoney int64 `protobuf:"varint,7,opt,name=orderTotalMoney,proto3" json:"orderTotalMoney"`
	//调整后的订单优惠总金额
	OrderDiscountMoney int64 `protobuf:"varint,8,opt,name=orderDiscountMoney,proto3" json:"orderDiscountMoney"`
	//调整后的订单运费总金额
	OrderFreightMoney int64 `protobuf:"varint,9,opt,name=orderFreightMoney,proto3" json:"orderFreightMoney"`
	//调整后用户应付金额
	OrderBuyerPayableMoney int64 `protobuf:"varint,10,opt,name=orderBuyerPayableMoney,proto3" json:"orderBuyerPayableMoney"`
	//商品集合
	OaList []*JddjAdjust `protobuf:"bytes,11,rep,name=oaList,proto3" json:"oaList"`
	//订单号
	OrderSn              string   `protobuf:"bytes,12,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjAdjustOrderResponse) Reset()         { *m = JddjAdjustOrderResponse{} }
func (m *JddjAdjustOrderResponse) String() string { return proto.CompactTextString(m) }
func (*JddjAdjustOrderResponse) ProtoMessage()    {}
func (*JddjAdjustOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{71}
}

func (m *JddjAdjustOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjAdjustOrderResponse.Unmarshal(m, b)
}
func (m *JddjAdjustOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjAdjustOrderResponse.Marshal(b, m, deterministic)
}
func (m *JddjAdjustOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjAdjustOrderResponse.Merge(m, src)
}
func (m *JddjAdjustOrderResponse) XXX_Size() int {
	return xxx_messageInfo_JddjAdjustOrderResponse.Size(m)
}
func (m *JddjAdjustOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjAdjustOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjAdjustOrderResponse proto.InternalMessageInfo

func (m *JddjAdjustOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *JddjAdjustOrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *JddjAdjustOrderResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *JddjAdjustOrderResponse) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *JddjAdjustOrderResponse) GetAdjustCount() int32 {
	if m != nil {
		return m.AdjustCount
	}
	return 0
}

func (m *JddjAdjustOrderResponse) GetProduceStationNo() string {
	if m != nil {
		return m.ProduceStationNo
	}
	return ""
}

func (m *JddjAdjustOrderResponse) GetOrderTotalMoney() int64 {
	if m != nil {
		return m.OrderTotalMoney
	}
	return 0
}

func (m *JddjAdjustOrderResponse) GetOrderDiscountMoney() int64 {
	if m != nil {
		return m.OrderDiscountMoney
	}
	return 0
}

func (m *JddjAdjustOrderResponse) GetOrderFreightMoney() int64 {
	if m != nil {
		return m.OrderFreightMoney
	}
	return 0
}

func (m *JddjAdjustOrderResponse) GetOrderBuyerPayableMoney() int64 {
	if m != nil {
		return m.OrderBuyerPayableMoney
	}
	return 0
}

func (m *JddjAdjustOrderResponse) GetOaList() []*JddjAdjust {
	if m != nil {
		return m.OaList
	}
	return nil
}

func (m *JddjAdjustOrderResponse) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

type JddjAdjust struct {
	//调整后的到家商品编号
	SkuId int64 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//调整后的商品数量
	SkuCount int32 `protobuf:"varint,2,opt,name=skuCount,proto3" json:"skuCount"`
	//调整后的商品名称
	SkuName string `protobuf:"bytes,3,opt,name=skuName,proto3" json:"skuName"`
	//到家商品销售价格
	SkuJdPrice int64 `protobuf:"varint,4,opt,name=skuJdPrice,proto3" json:"skuJdPrice"`
	//商家商品编码
	OutSkuId string `protobuf:"bytes,5,opt,name=outSkuId,proto3" json:"outSkuId"`
	//商品upc码
	Upc                  string   `protobuf:"bytes,6,opt,name=upc,proto3" json:"upc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjAdjust) Reset()         { *m = JddjAdjust{} }
func (m *JddjAdjust) String() string { return proto.CompactTextString(m) }
func (*JddjAdjust) ProtoMessage()    {}
func (*JddjAdjust) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{72}
}

func (m *JddjAdjust) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjAdjust.Unmarshal(m, b)
}
func (m *JddjAdjust) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjAdjust.Marshal(b, m, deterministic)
}
func (m *JddjAdjust) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjAdjust.Merge(m, src)
}
func (m *JddjAdjust) XXX_Size() int {
	return xxx_messageInfo_JddjAdjust.Size(m)
}
func (m *JddjAdjust) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjAdjust.DiscardUnknown(m)
}

var xxx_messageInfo_JddjAdjust proto.InternalMessageInfo

func (m *JddjAdjust) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *JddjAdjust) GetSkuCount() int32 {
	if m != nil {
		return m.SkuCount
	}
	return 0
}

func (m *JddjAdjust) GetSkuName() string {
	if m != nil {
		return m.SkuName
	}
	return ""
}

func (m *JddjAdjust) GetSkuJdPrice() int64 {
	if m != nil {
		return m.SkuJdPrice
	}
	return 0
}

func (m *JddjAdjust) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *JddjAdjust) GetUpc() string {
	if m != nil {
		return m.Upc
	}
	return ""
}

type JddjQueryOassBussMoneyRequest struct {
	//订单ID
	OrderId int64 `protobuf:"varint,1,opt,name=orderId,proto3" json:"orderId"`
	//用户pin （非必填）
	UserPin string `protobuf:"bytes,2,opt,name=userPin,proto3" json:"userPin"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,3,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjQueryOassBussMoneyRequest) Reset()         { *m = JddjQueryOassBussMoneyRequest{} }
func (m *JddjQueryOassBussMoneyRequest) String() string { return proto.CompactTextString(m) }
func (*JddjQueryOassBussMoneyRequest) ProtoMessage()    {}
func (*JddjQueryOassBussMoneyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{73}
}

func (m *JddjQueryOassBussMoneyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjQueryOassBussMoneyRequest.Unmarshal(m, b)
}
func (m *JddjQueryOassBussMoneyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjQueryOassBussMoneyRequest.Marshal(b, m, deterministic)
}
func (m *JddjQueryOassBussMoneyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjQueryOassBussMoneyRequest.Merge(m, src)
}
func (m *JddjQueryOassBussMoneyRequest) XXX_Size() int {
	return xxx_messageInfo_JddjQueryOassBussMoneyRequest.Size(m)
}
func (m *JddjQueryOassBussMoneyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjQueryOassBussMoneyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjQueryOassBussMoneyRequest proto.InternalMessageInfo

func (m *JddjQueryOassBussMoneyRequest) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *JddjQueryOassBussMoneyRequest) GetUserPin() string {
	if m != nil {
		return m.UserPin
	}
	return ""
}

func (m *JddjQueryOassBussMoneyRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjQueryOassBussMoneyResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string               `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 []*OassBussinessSkus `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *JddjQueryOassBussMoneyResponse) Reset()         { *m = JddjQueryOassBussMoneyResponse{} }
func (m *JddjQueryOassBussMoneyResponse) String() string { return proto.CompactTextString(m) }
func (*JddjQueryOassBussMoneyResponse) ProtoMessage()    {}
func (*JddjQueryOassBussMoneyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{74}
}

func (m *JddjQueryOassBussMoneyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjQueryOassBussMoneyResponse.Unmarshal(m, b)
}
func (m *JddjQueryOassBussMoneyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjQueryOassBussMoneyResponse.Marshal(b, m, deterministic)
}
func (m *JddjQueryOassBussMoneyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjQueryOassBussMoneyResponse.Merge(m, src)
}
func (m *JddjQueryOassBussMoneyResponse) XXX_Size() int {
	return xxx_messageInfo_JddjQueryOassBussMoneyResponse.Size(m)
}
func (m *JddjQueryOassBussMoneyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjQueryOassBussMoneyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjQueryOassBussMoneyResponse proto.InternalMessageInfo

func (m *JddjQueryOassBussMoneyResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *JddjQueryOassBussMoneyResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *JddjQueryOassBussMoneyResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *JddjQueryOassBussMoneyResponse) GetData() []*OassBussinessSkus {
	if m != nil {
		return m.Data
	}
	return nil
}

type OassBussinessSkus struct {
	OrderId                     int64                      `protobuf:"varint,1,opt,name=orderId,proto3" json:"orderId"`
	SkuId                       int64                      `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	SkuCount                    int32                      `protobuf:"varint,3,opt,name=skuCount,proto3" json:"skuCount"`
	PromotionPrice              int32                      `protobuf:"varint,4,opt,name=promotionPrice,proto3" json:"promotionPrice"`
	PdjPrice                    int32                      `protobuf:"varint,5,opt,name=pdjPrice,proto3" json:"pdjPrice"`
	CostPrice                   int32                      `protobuf:"varint,6,opt,name=costPrice,proto3" json:"costPrice"`
	SkuPayMoney                 int64                      `protobuf:"varint,7,opt,name=skuPayMoney,proto3" json:"skuPayMoney"`
	CostRadio                   int32                      `protobuf:"varint,8,opt,name=costRadio,proto3" json:"costRadio"`
	SaleRadio                   int32                      `protobuf:"varint,9,opt,name=saleRadio,proto3" json:"saleRadio"`
	CostMoney                   int64                      `protobuf:"varint,10,opt,name=costMoney,proto3" json:"costMoney"`
	SaleMoney                   int64                      `protobuf:"varint,11,opt,name=saleMoney,proto3" json:"saleMoney"`
	PlatformIntegralDeductMoney int64                      `protobuf:"varint,12,opt,name=platformIntegralDeductMoney,proto3" json:"platformIntegralDeductMoney"`
	PromotionType               int32                      `protobuf:"varint,13,opt,name=promotionType,proto3" json:"promotionType"`
	OutActivityId               string                     `protobuf:"bytes,14,opt,name=outActivityId,proto3" json:"outActivityId"`
	Discountlist                []*OrderBussiDiscountMoney `protobuf:"bytes,15,rep,name=discountlist,proto3" json:"discountlist"`
	XXX_NoUnkeyedLiteral        struct{}                   `json:"-"`
	XXX_unrecognized            []byte                     `json:"-"`
	XXX_sizecache               int32                      `json:"-"`
}

func (m *OassBussinessSkus) Reset()         { *m = OassBussinessSkus{} }
func (m *OassBussinessSkus) String() string { return proto.CompactTextString(m) }
func (*OassBussinessSkus) ProtoMessage()    {}
func (*OassBussinessSkus) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{75}
}

func (m *OassBussinessSkus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OassBussinessSkus.Unmarshal(m, b)
}
func (m *OassBussinessSkus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OassBussinessSkus.Marshal(b, m, deterministic)
}
func (m *OassBussinessSkus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OassBussinessSkus.Merge(m, src)
}
func (m *OassBussinessSkus) XXX_Size() int {
	return xxx_messageInfo_OassBussinessSkus.Size(m)
}
func (m *OassBussinessSkus) XXX_DiscardUnknown() {
	xxx_messageInfo_OassBussinessSkus.DiscardUnknown(m)
}

var xxx_messageInfo_OassBussinessSkus proto.InternalMessageInfo

func (m *OassBussinessSkus) GetOrderId() int64 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *OassBussinessSkus) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *OassBussinessSkus) GetSkuCount() int32 {
	if m != nil {
		return m.SkuCount
	}
	return 0
}

func (m *OassBussinessSkus) GetPromotionPrice() int32 {
	if m != nil {
		return m.PromotionPrice
	}
	return 0
}

func (m *OassBussinessSkus) GetPdjPrice() int32 {
	if m != nil {
		return m.PdjPrice
	}
	return 0
}

func (m *OassBussinessSkus) GetCostPrice() int32 {
	if m != nil {
		return m.CostPrice
	}
	return 0
}

func (m *OassBussinessSkus) GetSkuPayMoney() int64 {
	if m != nil {
		return m.SkuPayMoney
	}
	return 0
}

func (m *OassBussinessSkus) GetCostRadio() int32 {
	if m != nil {
		return m.CostRadio
	}
	return 0
}

func (m *OassBussinessSkus) GetSaleRadio() int32 {
	if m != nil {
		return m.SaleRadio
	}
	return 0
}

func (m *OassBussinessSkus) GetCostMoney() int64 {
	if m != nil {
		return m.CostMoney
	}
	return 0
}

func (m *OassBussinessSkus) GetSaleMoney() int64 {
	if m != nil {
		return m.SaleMoney
	}
	return 0
}

func (m *OassBussinessSkus) GetPlatformIntegralDeductMoney() int64 {
	if m != nil {
		return m.PlatformIntegralDeductMoney
	}
	return 0
}

func (m *OassBussinessSkus) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *OassBussinessSkus) GetOutActivityId() string {
	if m != nil {
		return m.OutActivityId
	}
	return ""
}

func (m *OassBussinessSkus) GetDiscountlist() []*OrderBussiDiscountMoney {
	if m != nil {
		return m.Discountlist
	}
	return nil
}

type OrderBussiDiscountMoney struct {
	PromotionType        int32    `protobuf:"varint,1,opt,name=promotionType,proto3" json:"promotionType"`
	PromotionDetailType  int32    `protobuf:"varint,2,opt,name=promotionDetailType,proto3" json:"promotionDetailType"`
	SkuDiscountMoney     int64    `protobuf:"varint,3,opt,name=skuDiscountMoney,proto3" json:"skuDiscountMoney"`
	SaleRadio            int32    `protobuf:"varint,4,opt,name=saleRadio,proto3" json:"saleRadio"`
	CostMoney            int64    `protobuf:"varint,5,opt,name=costMoney,proto3" json:"costMoney"`
	SaleMoney            int64    `protobuf:"varint,6,opt,name=saleMoney,proto3" json:"saleMoney"`
	PromotionCode        string   `protobuf:"bytes,7,opt,name=promotionCode,proto3" json:"promotionCode"`
	OutActivityId        string   `protobuf:"bytes,8,opt,name=outActivityId,proto3" json:"outActivityId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderBussiDiscountMoney) Reset()         { *m = OrderBussiDiscountMoney{} }
func (m *OrderBussiDiscountMoney) String() string { return proto.CompactTextString(m) }
func (*OrderBussiDiscountMoney) ProtoMessage()    {}
func (*OrderBussiDiscountMoney) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{76}
}

func (m *OrderBussiDiscountMoney) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderBussiDiscountMoney.Unmarshal(m, b)
}
func (m *OrderBussiDiscountMoney) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderBussiDiscountMoney.Marshal(b, m, deterministic)
}
func (m *OrderBussiDiscountMoney) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderBussiDiscountMoney.Merge(m, src)
}
func (m *OrderBussiDiscountMoney) XXX_Size() int {
	return xxx_messageInfo_OrderBussiDiscountMoney.Size(m)
}
func (m *OrderBussiDiscountMoney) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderBussiDiscountMoney.DiscardUnknown(m)
}

var xxx_messageInfo_OrderBussiDiscountMoney proto.InternalMessageInfo

func (m *OrderBussiDiscountMoney) GetPromotionType() int32 {
	if m != nil {
		return m.PromotionType
	}
	return 0
}

func (m *OrderBussiDiscountMoney) GetPromotionDetailType() int32 {
	if m != nil {
		return m.PromotionDetailType
	}
	return 0
}

func (m *OrderBussiDiscountMoney) GetSkuDiscountMoney() int64 {
	if m != nil {
		return m.SkuDiscountMoney
	}
	return 0
}

func (m *OrderBussiDiscountMoney) GetSaleRadio() int32 {
	if m != nil {
		return m.SaleRadio
	}
	return 0
}

func (m *OrderBussiDiscountMoney) GetCostMoney() int64 {
	if m != nil {
		return m.CostMoney
	}
	return 0
}

func (m *OrderBussiDiscountMoney) GetSaleMoney() int64 {
	if m != nil {
		return m.SaleMoney
	}
	return 0
}

func (m *OrderBussiDiscountMoney) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *OrderBussiDiscountMoney) GetOutActivityId() string {
	if m != nil {
		return m.OutActivityId
	}
	return ""
}

type UpdatePriceOneRequest struct {
	//商家门店编码
	OutStationNo string `protobuf:"bytes,1,opt,name=outStationNo,proto3" json:"outStationNo"`
	//到家门店编码（与outStationNo不可同时为空）
	StationNo string `protobuf:"bytes,2,opt,name=stationNo,proto3" json:"stationNo"`
	//商家商品编码
	OutSkuId string `protobuf:"bytes,3,opt,name=outSkuId,proto3" json:"outSkuId"`
	//价格(必填且不能小于等于0，单位 分)
	Price int32 `protobuf:"varint,4,opt,name=price,proto3" json:"price"`
	//uuid（填写随机生成唯一的标识串）
	ServiceNo string `protobuf:"bytes,5,opt,name=serviceNo,proto3" json:"serviceNo"`
	// 店铺主体Id
	StoreMasterId        int32    `protobuf:"varint,6,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePriceOneRequest) Reset()         { *m = UpdatePriceOneRequest{} }
func (m *UpdatePriceOneRequest) String() string { return proto.CompactTextString(m) }
func (*UpdatePriceOneRequest) ProtoMessage()    {}
func (*UpdatePriceOneRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{77}
}

func (m *UpdatePriceOneRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePriceOneRequest.Unmarshal(m, b)
}
func (m *UpdatePriceOneRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePriceOneRequest.Marshal(b, m, deterministic)
}
func (m *UpdatePriceOneRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePriceOneRequest.Merge(m, src)
}
func (m *UpdatePriceOneRequest) XXX_Size() int {
	return xxx_messageInfo_UpdatePriceOneRequest.Size(m)
}
func (m *UpdatePriceOneRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePriceOneRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePriceOneRequest proto.InternalMessageInfo

func (m *UpdatePriceOneRequest) GetOutStationNo() string {
	if m != nil {
		return m.OutStationNo
	}
	return ""
}

func (m *UpdatePriceOneRequest) GetStationNo() string {
	if m != nil {
		return m.StationNo
	}
	return ""
}

func (m *UpdatePriceOneRequest) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *UpdatePriceOneRequest) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *UpdatePriceOneRequest) GetServiceNo() string {
	if m != nil {
		return m.ServiceNo
	}
	return ""
}

func (m *UpdatePriceOneRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

type JddjGetShopCategoryRequest struct {
	// 店铺主体Id
	StoreMasterId int32 `protobuf:"varint,1,opt,name=store_master_id,json=storeMasterId,proto3" json:"store_master_id"`
	// fields查询字段
	Fields               string   `protobuf:"bytes,2,opt,name=fields,proto3" json:"fields"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JddjGetShopCategoryRequest) Reset()         { *m = JddjGetShopCategoryRequest{} }
func (m *JddjGetShopCategoryRequest) String() string { return proto.CompactTextString(m) }
func (*JddjGetShopCategoryRequest) ProtoMessage()    {}
func (*JddjGetShopCategoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{78}
}

func (m *JddjGetShopCategoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjGetShopCategoryRequest.Unmarshal(m, b)
}
func (m *JddjGetShopCategoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjGetShopCategoryRequest.Marshal(b, m, deterministic)
}
func (m *JddjGetShopCategoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjGetShopCategoryRequest.Merge(m, src)
}
func (m *JddjGetShopCategoryRequest) XXX_Size() int {
	return xxx_messageInfo_JddjGetShopCategoryRequest.Size(m)
}
func (m *JddjGetShopCategoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjGetShopCategoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JddjGetShopCategoryRequest proto.InternalMessageInfo

func (m *JddjGetShopCategoryRequest) GetStoreMasterId() int32 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

func (m *JddjGetShopCategoryRequest) GetFields() string {
	if m != nil {
		return m.Fields
	}
	return ""
}

type JddjGetShopCategoryResponse struct {
	Code                 string              `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Msg                  string              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	Data                 string              `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	Result               []*JDCategoryResult `protobuf:"bytes,4,rep,name=result,proto3" json:"result"`
	Success              bool                `protobuf:"varint,5,opt,name=success,proto3" json:"success"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *JddjGetShopCategoryResponse) Reset()         { *m = JddjGetShopCategoryResponse{} }
func (m *JddjGetShopCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*JddjGetShopCategoryResponse) ProtoMessage()    {}
func (*JddjGetShopCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{79}
}

func (m *JddjGetShopCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JddjGetShopCategoryResponse.Unmarshal(m, b)
}
func (m *JddjGetShopCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JddjGetShopCategoryResponse.Marshal(b, m, deterministic)
}
func (m *JddjGetShopCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JddjGetShopCategoryResponse.Merge(m, src)
}
func (m *JddjGetShopCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_JddjGetShopCategoryResponse.Size(m)
}
func (m *JddjGetShopCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JddjGetShopCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JddjGetShopCategoryResponse proto.InternalMessageInfo

func (m *JddjGetShopCategoryResponse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *JddjGetShopCategoryResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *JddjGetShopCategoryResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func (m *JddjGetShopCategoryResponse) GetResult() []*JDCategoryResult {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *JddjGetShopCategoryResponse) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

type JDCategoryResult struct {
	ShopCategoryName     string   `protobuf:"bytes,1,opt,name=shopCategoryName,proto3" json:"shopCategoryName"`
	Pid                  int32    `protobuf:"varint,2,opt,name=pid,proto3" json:"pid"`
	Id                   int32    `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	Sort                 int32    `protobuf:"varint,4,opt,name=sort,proto3" json:"sort"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JDCategoryResult) Reset()         { *m = JDCategoryResult{} }
func (m *JDCategoryResult) String() string { return proto.CompactTextString(m) }
func (*JDCategoryResult) ProtoMessage()    {}
func (*JDCategoryResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{80}
}

func (m *JDCategoryResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JDCategoryResult.Unmarshal(m, b)
}
func (m *JDCategoryResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JDCategoryResult.Marshal(b, m, deterministic)
}
func (m *JDCategoryResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JDCategoryResult.Merge(m, src)
}
func (m *JDCategoryResult) XXX_Size() int {
	return xxx_messageInfo_JDCategoryResult.Size(m)
}
func (m *JDCategoryResult) XXX_DiscardUnknown() {
	xxx_messageInfo_JDCategoryResult.DiscardUnknown(m)
}

var xxx_messageInfo_JDCategoryResult proto.InternalMessageInfo

func (m *JDCategoryResult) GetShopCategoryName() string {
	if m != nil {
		return m.ShopCategoryName
	}
	return ""
}

func (m *JDCategoryResult) GetPid() int32 {
	if m != nil {
		return m.Pid
	}
	return 0
}

func (m *JDCategoryResult) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *JDCategoryResult) GetSort() int32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

type QuerySkuInfosRequest struct {
	// 商品名称(支持模糊查询)
	SkuName string `protobuf:"bytes,1,opt,name=skuName,proto3" json:"skuName"`
	// 商品UPC编码  6921041426579
	UpcCode string `protobuf:"bytes,2,opt,name=upcCode,proto3" json:"upcCode"`
	// 到家商品编码
	SkuId int64 `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	//当前页
	PageNo int64 `protobuf:"varint,4,opt,name=pageNo,proto3" json:"pageNo"`
	// 接口建议值：20 最大值50否则会报错
	PageSize int64 `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize"`
	// 是否查询出已删除的上传商品(0代表不查已删除商品,不填则查出全部商品)
	IsFilterDel string `protobuf:"bytes,6,opt,name=isFilterDel,proto3" json:"isFilterDel"`
	// 门店的appChannel
	StoreMasterId int64 `protobuf:"varint,7,opt,name=StoreMasterId,proto3" json:"StoreMasterId"`
	// 查询递进skuId 说明：pageNo=1时，searchAfterSkuId非必填；pageNo!=1时，必填，取上页调用结果返回的searchAfterSkuId值
	SearchAfterSkuId     int64    `protobuf:"varint,8,opt,name=searchAfterSkuId,proto3" json:"searchAfterSkuId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuerySkuInfosRequest) Reset()         { *m = QuerySkuInfosRequest{} }
func (m *QuerySkuInfosRequest) String() string { return proto.CompactTextString(m) }
func (*QuerySkuInfosRequest) ProtoMessage()    {}
func (*QuerySkuInfosRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{81}
}

func (m *QuerySkuInfosRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySkuInfosRequest.Unmarshal(m, b)
}
func (m *QuerySkuInfosRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySkuInfosRequest.Marshal(b, m, deterministic)
}
func (m *QuerySkuInfosRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySkuInfosRequest.Merge(m, src)
}
func (m *QuerySkuInfosRequest) XXX_Size() int {
	return xxx_messageInfo_QuerySkuInfosRequest.Size(m)
}
func (m *QuerySkuInfosRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySkuInfosRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySkuInfosRequest proto.InternalMessageInfo

func (m *QuerySkuInfosRequest) GetSkuName() string {
	if m != nil {
		return m.SkuName
	}
	return ""
}

func (m *QuerySkuInfosRequest) GetUpcCode() string {
	if m != nil {
		return m.UpcCode
	}
	return ""
}

func (m *QuerySkuInfosRequest) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *QuerySkuInfosRequest) GetPageNo() int64 {
	if m != nil {
		return m.PageNo
	}
	return 0
}

func (m *QuerySkuInfosRequest) GetPageSize() int64 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *QuerySkuInfosRequest) GetIsFilterDel() string {
	if m != nil {
		return m.IsFilterDel
	}
	return ""
}

func (m *QuerySkuInfosRequest) GetStoreMasterId() int64 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

func (m *QuerySkuInfosRequest) GetSearchAfterSkuId() int64 {
	if m != nil {
		return m.SearchAfterSkuId
	}
	return 0
}

type QuerySkuInfosResponse struct {
	// 0表示成功，其他均为失败。
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	//    结果描述
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 返回值，该属性值为字符串型JSON数据，请先按照字符串取值，再解析转换成JSON对象。
	Data string `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	// 解析的json返回
	Result               *SkuQueryResponse `protobuf:"bytes,4,opt,name=result,proto3" json:"result"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *QuerySkuInfosResponse) Reset()         { *m = QuerySkuInfosResponse{} }
func (m *QuerySkuInfosResponse) String() string { return proto.CompactTextString(m) }
func (*QuerySkuInfosResponse) ProtoMessage()    {}
func (*QuerySkuInfosResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{82}
}

func (m *QuerySkuInfosResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySkuInfosResponse.Unmarshal(m, b)
}
func (m *QuerySkuInfosResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySkuInfosResponse.Marshal(b, m, deterministic)
}
func (m *QuerySkuInfosResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySkuInfosResponse.Merge(m, src)
}
func (m *QuerySkuInfosResponse) XXX_Size() int {
	return xxx_messageInfo_QuerySkuInfosResponse.Size(m)
}
func (m *QuerySkuInfosResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySkuInfosResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySkuInfosResponse proto.InternalMessageInfo

func (m *QuerySkuInfosResponse) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *QuerySkuInfosResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *QuerySkuInfosResponse) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func (m *QuerySkuInfosResponse) GetResult() *SkuQueryResponse {
	if m != nil {
		return m.Result
	}
	return nil
}

type SkuQueryResponse struct {
	// 返回的数量
	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count"`
	// 返回的string类型的数据
	Result string `protobuf:"bytes,2,opt,name=result,proto3" json:"result"`
	// 解析的json格式返回
	JsonData []*SkuMain `protobuf:"bytes,3,rep,name=jsonData,proto3" json:"jsonData"`
	// 查询递进skuId 说明: 用作下次查询的searchAfterSkuId入参，当返回为空时则查询结束
	SearchAfterSkuId     int64    `protobuf:"varint,4,opt,name=searchAfterSkuId,proto3" json:"searchAfterSkuId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuQueryResponse) Reset()         { *m = SkuQueryResponse{} }
func (m *SkuQueryResponse) String() string { return proto.CompactTextString(m) }
func (*SkuQueryResponse) ProtoMessage()    {}
func (*SkuQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{83}
}

func (m *SkuQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuQueryResponse.Unmarshal(m, b)
}
func (m *SkuQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuQueryResponse.Marshal(b, m, deterministic)
}
func (m *SkuQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuQueryResponse.Merge(m, src)
}
func (m *SkuQueryResponse) XXX_Size() int {
	return xxx_messageInfo_SkuQueryResponse.Size(m)
}
func (m *SkuQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SkuQueryResponse proto.InternalMessageInfo

func (m *SkuQueryResponse) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SkuQueryResponse) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *SkuQueryResponse) GetJsonData() []*SkuMain {
	if m != nil {
		return m.JsonData
	}
	return nil
}

func (m *SkuQueryResponse) GetSearchAfterSkuId() int64 {
	if m != nil {
		return m.SearchAfterSkuId
	}
	return 0
}

type SkuMain struct {
	//    到家商品编码
	SkuId int64 `protobuf:"varint,1,opt,name=skuId,proto3" json:"skuId"`
	//    商家商品编码
	OutSkuId string `protobuf:"bytes,2,opt,name=outSkuId,proto3" json:"outSkuId"`
	//    到家类目编码，到家类目分三级，该字段展示最末级类目编码
	CategoryId int64 `protobuf:"varint,3,opt,name=categoryId,proto3" json:"categoryId"`
	//    到家品牌编码
	BrandId int64 `protobuf:"varint,4,opt,name=brandId,proto3" json:"brandId"`
	// 商品名称
	SkuName string `protobuf:"bytes,5,opt,name=skuName,proto3" json:"skuName"`
	//    重量(公斤) 单位:KG
	Weight float32 `protobuf:"fixed32,6,opt,name=weight,proto3" json:"weight"`
	//    商品UPC编码
	UpcCode string `protobuf:"bytes,7,opt,name=upcCode,proto3" json:"upcCode"`
	//    商家商品上下架状态(1:上架;2:下架;4:删除;)
	FixedStatus int64 `protobuf:"varint,8,opt,name=fixedStatus,proto3" json:"fixedStatus"`
	//    商家店内分类编码,店内分类分两级，该字段展示最末级分类编码；当一个商品绑定了多个店内分类时，该字段展示用逗号分隔的各个分类编码。
	ShopCategories []int64 `protobuf:"varint,9,rep,packed,name=shopCategories,proto3" json:"shopCategories"`
	//    广告词
	Slogan               string   `protobuf:"bytes,10,opt,name=slogan,proto3" json:"slogan"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuMain) Reset()         { *m = SkuMain{} }
func (m *SkuMain) String() string { return proto.CompactTextString(m) }
func (*SkuMain) ProtoMessage()    {}
func (*SkuMain) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{84}
}

func (m *SkuMain) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuMain.Unmarshal(m, b)
}
func (m *SkuMain) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuMain.Marshal(b, m, deterministic)
}
func (m *SkuMain) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuMain.Merge(m, src)
}
func (m *SkuMain) XXX_Size() int {
	return xxx_messageInfo_SkuMain.Size(m)
}
func (m *SkuMain) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuMain.DiscardUnknown(m)
}

var xxx_messageInfo_SkuMain proto.InternalMessageInfo

func (m *SkuMain) GetSkuId() int64 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *SkuMain) GetOutSkuId() string {
	if m != nil {
		return m.OutSkuId
	}
	return ""
}

func (m *SkuMain) GetCategoryId() int64 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *SkuMain) GetBrandId() int64 {
	if m != nil {
		return m.BrandId
	}
	return 0
}

func (m *SkuMain) GetSkuName() string {
	if m != nil {
		return m.SkuName
	}
	return ""
}

func (m *SkuMain) GetWeight() float32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *SkuMain) GetUpcCode() string {
	if m != nil {
		return m.UpcCode
	}
	return ""
}

func (m *SkuMain) GetFixedStatus() int64 {
	if m != nil {
		return m.FixedStatus
	}
	return 0
}

func (m *SkuMain) GetShopCategories() []int64 {
	if m != nil {
		return m.ShopCategories
	}
	return nil
}

func (m *SkuMain) GetSlogan() string {
	if m != nil {
		return m.Slogan
	}
	return ""
}

type QueryListBySkuIdsDto struct {
	//    京东到家商品编码列表,单次最大25个商品
	SkuIds []int64 `protobuf:"varint,1,rep,packed,name=skuIds,proto3" json:"skuIds"`
	//    图片类型 1：商品图片 2：商品描述图片
	ImgType int64 `protobuf:"varint,2,opt,name=imgType,proto3" json:"imgType"`
	//    处理状态,0:待处理,1: 成功,4:连接异常,5:IO异常,6:外部服务器异常,9:上传文件失败,10:图片格式错误,11:图片生成错误,100:其他错误
	HandleStatus         []int64  `protobuf:"varint,3,rep,packed,name=handleStatus,proto3" json:"handleStatus"`
	StoreMasterId        int64    `protobuf:"varint,4,opt,name=storeMasterId,proto3" json:"storeMasterId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryListBySkuIdsDto) Reset()         { *m = QueryListBySkuIdsDto{} }
func (m *QueryListBySkuIdsDto) String() string { return proto.CompactTextString(m) }
func (*QueryListBySkuIdsDto) ProtoMessage()    {}
func (*QueryListBySkuIdsDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_b34f06ba9db7d23f, []int{85}
}

func (m *QueryListBySkuIdsDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryListBySkuIdsDto.Unmarshal(m, b)
}
func (m *QueryListBySkuIdsDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryListBySkuIdsDto.Marshal(b, m, deterministic)
}
func (m *QueryListBySkuIdsDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryListBySkuIdsDto.Merge(m, src)
}
func (m *QueryListBySkuIdsDto) XXX_Size() int {
	return xxx_messageInfo_QueryListBySkuIdsDto.Size(m)
}
func (m *QueryListBySkuIdsDto) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryListBySkuIdsDto.DiscardUnknown(m)
}

var xxx_messageInfo_QueryListBySkuIdsDto proto.InternalMessageInfo

func (m *QueryListBySkuIdsDto) GetSkuIds() []int64 {
	if m != nil {
		return m.SkuIds
	}
	return nil
}

func (m *QueryListBySkuIdsDto) GetImgType() int64 {
	if m != nil {
		return m.ImgType
	}
	return 0
}

func (m *QueryListBySkuIdsDto) GetHandleStatus() []int64 {
	if m != nil {
		return m.HandleStatus
	}
	return nil
}

func (m *QueryListBySkuIdsDto) GetStoreMasterId() int64 {
	if m != nil {
		return m.StoreMasterId
	}
	return 0
}

func init() {
	proto.RegisterType((*JddjGetBalanceBillListReq)(nil), "et.JddjGetBalanceBillListReq")
	proto.RegisterType((*JddjGetBalanceBillListRes)(nil), "et.JddjGetBalanceBillListRes")
	proto.RegisterType((*BalanceBillList)(nil), "et.BalanceBillList")
	proto.RegisterType((*BalanceBill)(nil), "et.BalanceBill")
	proto.RegisterType((*SyncJddjStoreStatusRequest)(nil), "et.SyncJddjStoreStatusRequest")
	proto.RegisterType((*SyncJddjStoreStatusRequestReqData)(nil), "et.SyncJddjStoreStatusRequest.reqData")
	proto.RegisterType((*SyncJddjStoreStatusResponse)(nil), "et.SyncJddjStoreStatusResponse")
	proto.RegisterType((*SyncJddjStoreStatusResponseOutData)(nil), "et.SyncJddjStoreStatusResponse.outData")
	proto.RegisterType((*UpdateStockRequest)(nil), "et.UpdateStockRequest")
	proto.RegisterType((*UpdateStockOneRequest)(nil), "et.UpdateStockOneRequest")
	proto.RegisterType((*SkuStockList)(nil), "et.skuStockList")
	proto.RegisterType((*BatchUpdateVendibilityRequest)(nil), "et.BatchUpdateVendibilityRequest")
	proto.RegisterType((*JddjStockVendibility)(nil), "et.JddjStockVendibility")
	proto.RegisterType((*BatchUpdateVendibilityResponse)(nil), "et.BatchUpdateVendibilityResponse")
	proto.RegisterType((*UpdateVendibilityResponse)(nil), "et.UpdateVendibilityResponse")
	proto.RegisterType((*VerificationUpdateTokenRequest)(nil), "et.VerificationUpdateTokenRequest")
	proto.RegisterType((*JddjOrderCancelOperateRequest)(nil), "et.JddjOrderCancelOperateRequest")
	proto.RegisterType((*AfsOpenApproveRequest)(nil), "et.AfsOpenApproveRequest")
	proto.RegisterType((*JddjCategoryInfo)(nil), "et.JddjCategoryInfo")
	proto.RegisterType((*JddjOrderCancelRequest)(nil), "et.JddjOrderCancelRequest")
	proto.RegisterType((*JddjCheckSelfPickCodeRequest)(nil), "et.JddjCheckSelfPickCodeRequest")
	proto.RegisterType((*JddjOrderSelfMentionRequest)(nil), "et.JddjOrderSelfMentionRequest")
	proto.RegisterType((*JddjAfsServiceRequest)(nil), "et.JddjAfsServiceRequest")
	proto.RegisterType((*JddjConfirmReceiptRequest)(nil), "et.JddjConfirmReceiptRequest")
	proto.RegisterType((*JddjMerchantInitiateAfterSaleRequest)(nil), "et.JddjMerchantInitiateAfterSaleRequest")
	proto.RegisterType((*JddVenderAfsSkuDto)(nil), "et.JddVenderAfsSkuDto")
	proto.RegisterType((*JddjOrderCalcMoneyRequest)(nil), "et.JddjOrderCalcMoneyRequest")
	proto.RegisterType((*JddjBaseResponse)(nil), "et.JddjBaseResponse")
	proto.RegisterType((*UpdateStationPriceRequest)(nil), "et.UpdateStationPriceRequest")
	proto.RegisterType((*JddjSkuPriceInfo)(nil), "et.JddjSkuPriceInfo")
	proto.RegisterType((*UpdateStationPriceResponse)(nil), "et.UpdateStationPriceResponse")
	proto.RegisterType((*UpdateStationPriceResult)(nil), "et.UpdateStationPriceResult")
	proto.RegisterType((*ChangeShopCategoryOrderRequest)(nil), "et.ChangeShopCategoryOrderRequest")
	proto.RegisterType((*AddShopCategoryRequest)(nil), "et.AddShopCategoryRequest")
	proto.RegisterType((*AddShopCategoryResponse)(nil), "et.AddShopCategoryResponse")
	proto.RegisterType((*AddSkuRequest)(nil), "et.AddSkuRequest")
	proto.RegisterType((*AddSkuResponse)(nil), "et.AddSkuResponse")
	proto.RegisterType((*BatchUpdateCurrentQtysRequest)(nil), "et.BatchUpdateCurrentQtysRequest")
	proto.RegisterType((*JddjSkuStock)(nil), "et.JddjSkuStock")
	proto.RegisterType((*BatchUpdateCurrentQtysResponse)(nil), "et.BatchUpdateCurrentQtysResponse")
	proto.RegisterType((*UpdateStockResponse)(nil), "et.UpdateStockResponse")
	proto.RegisterType((*JddjUpdateGoodsListRequest)(nil), "et.JddjUpdateGoodsListRequest")
	proto.RegisterType((*JddjUpdateGoodsListResponse)(nil), "et.JddjUpdateGoodsListResponse")
	proto.RegisterType((*Result)(nil), "et.Result")
	proto.RegisterType((*JddjAddShopCategoryRequest)(nil), "et.JddjAddShopCategoryRequest")
	proto.RegisterType((*JddjAddShopCategoryResponse)(nil), "et.JddjAddShopCategoryResponse")
	proto.RegisterType((*ShopCategoryPartnerResponse)(nil), "et.ShopCategoryPartnerResponse")
	proto.RegisterType((*JddjUpdateShopCategoryRequest)(nil), "et.JddjUpdateShopCategoryRequest")
	proto.RegisterType((*JddjUpdateShopCategoryResponse)(nil), "et.JddjUpdateShopCategoryResponse")
	proto.RegisterType((*JddjDeleteShopCategoryRequest)(nil), "et.JddjDeleteShopCategoryRequest")
	proto.RegisterType((*JddjDeleteShopCategoryResponse)(nil), "et.JddjDeleteShopCategoryResponse")
	proto.RegisterType((*JddjSortShopCategoryRequest)(nil), "et.JddjSortShopCategoryRequest")
	proto.RegisterType((*JddjSortShopCategoryResponse)(nil), "et.JddjSortShopCategoryResponse")
	proto.RegisterType((*QueryPageBrandInfoRequest)(nil), "et.QueryPageBrandInfoRequest")
	proto.RegisterType((*QueryPageBrandInfoResponse)(nil), "et.QueryPageBrandInfoResponse")
	proto.RegisterType((*JddjBrandInfo)(nil), "et.JddjBrandInfo")
	proto.RegisterType((*QueryChildCategoriesForOPRequest)(nil), "et.QueryChildCategoriesForOPRequest")
	proto.RegisterType((*QueryChildCategoriesForOPResponse)(nil), "et.QueryChildCategoriesForOPResponse")
	proto.RegisterType((*JddjOrderConfirmRequest)(nil), "et.JddjOrderConfirmRequest")
	proto.RegisterType((*JddjOrderConfirmlResponse)(nil), "et.JddjOrderConfirmlResponse")
	proto.RegisterType((*JddjOrderSerllerDeliveryRequest)(nil), "et.JddjOrderSerllerDeliveryRequest")
	proto.RegisterType((*JddjOrderDetailRequest)(nil), "et.JddjOrderDetailRequest")
	proto.RegisterType((*JddjOrderDetailResponse)(nil), "et.JddjOrderDetailResponse")
	proto.RegisterType((*JddjData)(nil), "et.JddjData")
	proto.RegisterType((*JddjResult)(nil), "et.JddjResult")
	proto.RegisterType((*ResultList)(nil), "et.ResultList")
	proto.RegisterType((*JddjOrderInvoice)(nil), "et.JddjOrderInvoice")
	proto.RegisterType((*JddjProductList)(nil), "et.JddjProductList")
	proto.RegisterType((*JddjDiscount)(nil), "et.JddjDiscount")
	proto.RegisterType((*JddjOrderShoudSettlementResponse)(nil), "et.JddjOrderShoudSettlementResponse")
	proto.RegisterType((*OrderShoudSettlement)(nil), "et.OrderShoudSettlement")
	proto.RegisterType((*JddjAdjustOrderRequest)(nil), "et.JddjAdjustOrderRequest")
	proto.RegisterType((*JddjOAOSAdjust)(nil), "et.JddjOAOSAdjust")
	proto.RegisterType((*JddjAdjustOrderResponse)(nil), "et.JddjAdjustOrderResponse")
	proto.RegisterType((*JddjAdjust)(nil), "et.JddjAdjust")
	proto.RegisterType((*JddjQueryOassBussMoneyRequest)(nil), "et.JddjQueryOassBussMoneyRequest")
	proto.RegisterType((*JddjQueryOassBussMoneyResponse)(nil), "et.JddjQueryOassBussMoneyResponse")
	proto.RegisterType((*OassBussinessSkus)(nil), "et.OassBussinessSkus")
	proto.RegisterType((*OrderBussiDiscountMoney)(nil), "et.OrderBussiDiscountMoney")
	proto.RegisterType((*UpdatePriceOneRequest)(nil), "et.UpdatePriceOneRequest")
	proto.RegisterType((*JddjGetShopCategoryRequest)(nil), "et.JddjGetShopCategoryRequest")
	proto.RegisterType((*JddjGetShopCategoryResponse)(nil), "et.JddjGetShopCategoryResponse")
	proto.RegisterType((*JDCategoryResult)(nil), "et.JDCategoryResult")
	proto.RegisterType((*QuerySkuInfosRequest)(nil), "et.QuerySkuInfosRequest")
	proto.RegisterType((*QuerySkuInfosResponse)(nil), "et.QuerySkuInfosResponse")
	proto.RegisterType((*SkuQueryResponse)(nil), "et.SkuQueryResponse")
	proto.RegisterType((*SkuMain)(nil), "et.SkuMain")
	proto.RegisterType((*QueryListBySkuIdsDto)(nil), "et.QueryListBySkuIdsDto")
}

func init() { proto.RegisterFile("et/externalJddj.proto", fileDescriptor_b34f06ba9db7d23f) }

var fileDescriptor_b34f06ba9db7d23f = []byte{
	// 5632 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x5c, 0x4d, 0x8c, 0x1c, 0x49,
	0x56, 0x56, 0x55, 0x75, 0xf5, 0x4f, 0xf4, 0xaf, 0xb3, 0xdb, 0xed, 0x72, 0xb5, 0xdd, 0xee, 0xc9,
	0xf1, 0x7a, 0x7a, 0x86, 0xd9, 0x9e, 0x99, 0x66, 0xb4, 0x20, 0x7e, 0xc4, 0xf4, 0x8f, 0x3d, 0xb4,
	0xc7, 0xed, 0xae, 0xc9, 0xf2, 0x78, 0x04, 0xda, 0x95, 0xc9, 0xae, 0x8c, 0xae, 0x4e, 0x57, 0x56,
	0x66, 0x4d, 0x66, 0xa4, 0xed, 0x66, 0x0f, 0x83, 0xc4, 0x81, 0x3d, 0xb1, 0x02, 0x16, 0x38, 0x20,
	0xc1, 0x01, 0x09, 0x21, 0x4e, 0xac, 0xc4, 0x8f, 0xc4, 0x0d, 0x21, 0xb1, 0x88, 0xc3, 0x02, 0x47,
	0x4e, 0x5c, 0x38, 0x20, 0x71, 0x01, 0x2e, 0xdc, 0x10, 0x8a, 0xf7, 0x22, 0x32, 0x23, 0x32, 0x23,
	0xab, 0xcb, 0x1e, 0x0b, 0xa4, 0xbd, 0xe5, 0xfb, 0x22, 0x32, 0x7e, 0x5e, 0xbc, 0x78, 0xef, 0xc5,
	0x8b, 0x97, 0x49, 0xae, 0x52, 0xf6, 0x1e, 0x7d, 0xc1, 0x68, 0x1c, 0xba, 0xc1, 0x7d, 0xcf, 0x7b,
	0xba, 0x33, 0x8a, 0x23, 0x16, 0x59, 0x75, 0xca, 0xda, 0x1b, 0xfd, 0x28, 0xea, 0x07, 0xf4, 0x3d,
	0x40, 0x4e, 0xd3, 0xb3, 0xf7, 0xe8, 0x70, 0xc4, 0x2e, 0xb0, 0x82, 0xfd, 0x97, 0x75, 0x72, 0x9d,
	0xd7, 0xff, 0x98, 0xb2, 0x7d, 0x37, 0x70, 0xc3, 0x1e, 0xdd, 0xf7, 0x83, 0xe0, 0x81, 0x9f, 0x30,
	0x87, 0x7e, 0x61, 0xdd, 0x21, 0xcb, 0x09, 0x8b, 0x62, 0xfa, 0x64, 0xe8, 0x26, 0x8c, 0xc6, 0x4f,
	0x7c, 0xaf, 0x55, 0xdb, 0xaa, 0x6d, 0x37, 0x9d, 0x45, 0x80, 0x8f, 0x01, 0x3d, 0xf2, 0xac, 0x2d,
	0x32, 0x1f, 0xc5, 0x1e, 0x8d, 0xbb, 0xcc, 0x65, 0x69, 0xd2, 0xaa, 0x43, 0x1d, 0x15, 0xb2, 0x5a,
	0x64, 0x26, 0x39, 0x8f, 0x46, 0x47, 0x5e, 0xd2, 0x6a, 0x6c, 0x35, 0xb6, 0x1b, 0x8e, 0x24, 0xad,
	0x36, 0x99, 0x85, 0x8a, 0xbc, 0x68, 0x0a, 0x8a, 0x32, 0xda, 0xba, 0x43, 0x96, 0x64, 0x23, 0x31,
	0x7b, 0xe4, 0x0f, 0x69, 0xab, 0xb9, 0x55, 0xdb, 0x9e, 0x73, 0x0a, 0xa8, 0x65, 0x93, 0x05, 0x40,
	0xee, 0x86, 0x1e, 0xd4, 0x9a, 0x86, 0x5a, 0x1a, 0x66, 0x6d, 0x93, 0xe5, 0x33, 0x3f, 0xf4, 0x93,
	0xf3, 0xbc, 0xb1, 0x19, 0xa8, 0x56, 0x84, 0xad, 0xdb, 0x64, 0x11, 0x21, 0xd9, 0xdc, 0x2c, 0xd4,
	0xd3, 0x41, 0xfb, 0x3b, 0xb5, 0x6a, 0xce, 0x25, 0x96, 0x45, 0xa6, 0x7a, 0x91, 0x47, 0x05, 0xbb,
	0xe0, 0x99, 0xf3, 0x60, 0x48, 0x93, 0xc4, 0xed, 0x53, 0xe0, 0xd0, 0x9c, 0x23, 0x49, 0x6b, 0x8d,
	0x34, 0x69, 0x1c, 0x47, 0x71, 0xab, 0x01, 0x38, 0x12, 0xd6, 0x5b, 0x64, 0xca, 0x73, 0x99, 0xdb,
	0x9a, 0xda, 0xaa, 0x6d, 0xcf, 0xef, 0xae, 0xee, 0x50, 0xb6, 0x53, 0xec, 0x09, 0x2a, 0xd8, 0xff,
	0x58, 0x23, 0xcb, 0x85, 0x12, 0xde, 0xd9, 0xc8, 0xed, 0xd3, 0x87, 0xe9, 0x10, 0xc6, 0xd0, 0x70,
	0x24, 0xc9, 0x19, 0xce, 0x1f, 0xbb, 0xfe, 0x2f, 0x53, 0xb1, 0x52, 0x19, 0xcd, 0x07, 0xc2, 0x22,
	0xe6, 0x06, 0x30, 0x90, 0x86, 0x83, 0x04, 0x7f, 0x23, 0xe1, 0xdc, 0x71, 0xa2, 0xe7, 0x30, 0x98,
	0xa6, 0x93, 0xd1, 0xd6, 0x3a, 0x99, 0xa6, 0xa1, 0xc7, 0x4b, 0x9a, 0x50, 0x22, 0x28, 0xde, 0x12,
	0x6f, 0x35, 0x81, 0xb5, 0x68, 0x3a, 0x48, 0x58, 0x6f, 0x91, 0xe9, 0x98, 0x26, 0x69, 0xc0, 0x5a,
	0x33, 0x5b, 0x8d, 0xed, 0xf9, 0xdd, 0xe5, 0xc2, 0xa4, 0x1c, 0x51, 0x6c, 0xff, 0x77, 0x9d, 0xcc,
	0x2b, 0xb8, 0xb5, 0x44, 0xea, 0x99, 0xf0, 0xd5, 0x7d, 0x8f, 0x4f, 0x2f, 0x8a, 0xfb, 0x07, 0x9c,
	0xc5, 0x75, 0x9c, 0x9e, 0x20, 0x45, 0xc9, 0x43, 0x77, 0x48, 0x05, 0x37, 0x25, 0x69, 0xdd, 0x20,
	0x73, 0x09, 0x73, 0x99, 0x1f, 0x85, 0x47, 0x1e, 0xcc, 0xa3, 0xe1, 0xe4, 0x00, 0x97, 0x61, 0x41,
	0xc0, 0xbb, 0x28, 0x68, 0x2a, 0x64, 0xbd, 0x4b, 0xae, 0x9c, 0xa6, 0x89, 0x1f, 0xd2, 0x24, 0xc9,
	0x65, 0x08, 0x45, 0xad, 0x5c, 0x60, 0xed, 0x10, 0x4b, 0x82, 0xf7, 0x40, 0x70, 0x14, 0x91, 0x33,
	0x94, 0xe0, 0xb8, 0x41, 0xee, 0x41, 0xde, 0x60, 0x46, 0x40, 0x5a, 0x9b, 0x84, 0x24, 0x71, 0xef,
	0x44, 0x14, 0xce, 0x41, 0xa1, 0x82, 0xf0, 0x91, 0xbb, 0xbd, 0x5e, 0x94, 0x86, 0x38, 0x22, 0x82,
	0x23, 0x57, 0x20, 0xeb, 0x1d, 0xb2, 0x92, 0x50, 0xc6, 0x02, 0xaa, 0x8c, 0x64, 0x1e, 0xaa, 0x95,
	0x70, 0xfb, 0xbf, 0x6a, 0xa4, 0xdd, 0xbd, 0x08, 0x7b, 0x5c, 0xb6, 0xbb, 0x7c, 0x97, 0xe3, 0x0e,
	0x76, 0xe8, 0x17, 0x29, 0x4d, 0x98, 0xf5, 0x53, 0x42, 0x28, 0x6b, 0xb0, 0x7e, 0x77, 0xf8, 0xfa,
	0x55, 0xd7, 0xde, 0x89, 0xe9, 0x17, 0x87, 0x2e, 0x73, 0x51, 0x4e, 0xad, 0x6b, 0x64, 0x86, 0xb9,
	0xc9, 0x80, 0xab, 0x11, 0x5c, 0xb4, 0x69, 0x4e, 0x1e, 0x79, 0xed, 0x2f, 0xc9, 0x8c, 0xa8, 0x69,
	0xd9, 0x64, 0xf1, 0xd4, 0xf5, 0xbd, 0xf4, 0x09, 0xd7, 0x0f, 0x52, 0xe1, 0xcc, 0x39, 0xf3, 0x00,
	0x76, 0x41, 0x67, 0xf0, 0x76, 0x64, 0x29, 0x6e, 0xa4, 0x69, 0x54, 0x26, 0x5c, 0x18, 0x13, 0x54,
	0x41, 0x0d, 0x14, 0x46, 0xa4, 0xac, 0x5b, 0x64, 0xde, 0x1d, 0x8d, 0x9e, 0xf4, 0xce, 0xdd, 0x30,
	0xa4, 0x81, 0x90, 0x61, 0xe2, 0x8e, 0x46, 0x07, 0x88, 0xd8, 0x7f, 0x5c, 0x23, 0x1b, 0xc6, 0x69,
	0x24, 0xa3, 0x28, 0x4c, 0xa8, 0xf5, 0xd3, 0xda, 0xac, 0xdf, 0xaa, 0x9c, 0x35, 0x56, 0xdf, 0x89,
	0x52, 0x96, 0x4f, 0xbb, 0xfd, 0x80, 0xcc, 0x08, 0x40, 0x1d, 0x79, 0xad, 0x62, 0xe4, 0x75, 0x6d,
	0xe4, 0x2b, 0xa4, 0x31, 0x4c, 0xfa, 0x42, 0x92, 0xf9, 0xa3, 0xfd, 0xc3, 0x1a, 0xb1, 0x3e, 0x1b,
	0x79, 0x2e, 0xa3, 0x5d, 0x16, 0xf5, 0x06, 0x72, 0x5d, 0xb8, 0x0a, 0x4c, 0x59, 0x57, 0x88, 0x6b,
	0x24, 0x9a, 0xd7, 0x30, 0x65, 0x03, 0x3c, 0x8c, 0x04, 0xe7, 0x72, 0x80, 0x0b, 0x60, 0x9a, 0xd0,
	0xb8, 0xe3, 0x87, 0x72, 0xe3, 0x08, 0xd2, 0xfa, 0x90, 0x2c, 0x24, 0x83, 0x14, 0xba, 0xe3, 0xba,
	0x05, 0xd4, 0xf4, 0xfc, 0xee, 0x0a, 0xe7, 0x82, 0x8a, 0x3b, 0x5a, 0x2d, 0x93, 0xf1, 0x68, 0x1a,
	0x8c, 0x87, 0xfd, 0x5b, 0x35, 0x72, 0x55, 0x99, 0xd0, 0x49, 0x48, 0xe5, 0x9c, 0xd6, 0x48, 0x33,
	0x19, 0xa4, 0x47, 0x92, 0x57, 0x48, 0x5c, 0x32, 0x8b, 0x4d, 0x42, 0x7a, 0x69, 0x1c, 0xd3, 0x90,
	0x7d, 0xca, 0x2e, 0x84, 0x18, 0x28, 0x88, 0x69, 0x54, 0x53, 0xa6, 0x51, 0xdd, 0xd3, 0xe7, 0x0c,
	0x66, 0x2a, 0x65, 0x5d, 0x65, 0x38, 0x19, 0x8d, 0xfa, 0x31, 0xea, 0x0d, 0x78, 0x8f, 0x75, 0xa9,
	0x1f, 0x91, 0xb6, 0xff, 0xb3, 0x46, 0x6e, 0xee, 0xbb, 0xac, 0x77, 0x8e, 0x53, 0x7c, 0x4c, 0x43,
	0xcf, 0x3f, 0xf5, 0x03, 0x9f, 0x5d, 0xbc, 0xbe, 0x95, 0x7b, 0x40, 0xd6, 0xa0, 0x3f, 0xa5, 0x71,
	0x58, 0xa7, 0x06, 0xac, 0x53, 0x8b, 0xaf, 0x93, 0x90, 0x54, 0xad, 0x8e, 0x63, 0x7c, 0x4b, 0x95,
	0x83, 0x29, 0x5d, 0x0e, 0x26, 0x5d, 0xd1, 0xfb, 0x64, 0xcd, 0xd4, 0xdf, 0x58, 0x1e, 0xae, 0x93,
	0x69, 0x2f, 0xea, 0xba, 0x01, 0xea, 0xf3, 0x59, 0x47, 0x50, 0xf6, 0xef, 0xd6, 0xc8, 0x66, 0x15,
	0xff, 0xc4, 0xe6, 0x5c, 0x21, 0x8d, 0x98, 0x32, 0x68, 0x71, 0xd6, 0xe1, 0x8f, 0x7c, 0x0a, 0x31,
	0x65, 0x99, 0x75, 0x98, 0x73, 0x24, 0xc9, 0xbb, 0x89, 0x29, 0x3b, 0xce, 0xb6, 0x94, 0xa0, 0xac,
	0x0f, 0x32, 0x5b, 0xcb, 0x59, 0x76, 0x93, 0xb3, 0xac, 0xb2, 0x43, 0x61, 0x75, 0xbf, 0x45, 0xae,
	0x57, 0x8f, 0xc9, 0x64, 0xff, 0xc5, 0x5e, 0xae, 0x67, 0x7b, 0x59, 0x63, 0x48, 0x43, 0x67, 0x88,
	0xfd, 0x2b, 0x35, 0xb2, 0xf9, 0x98, 0xc6, 0xfe, 0x99, 0xdf, 0x83, 0x75, 0xc6, 0xbe, 0x1e, 0x45,
	0x03, 0x1a, 0x4a, 0xc9, 0xe1, 0xaf, 0x07, 0x1e, 0x40, 0x19, 0x3f, 0x05, 0xcd, 0xcb, 0x42, 0xfa,
	0x1c, 0xcb, 0xb0, 0xc7, 0x8c, 0x36, 0xad, 0x63, 0xc3, 0xb4, 0x8e, 0xdf, 0xaf, 0x91, 0x9b, 0x7c,
	0x21, 0xc1, 0xd0, 0x1c, 0x70, 0x5b, 0x1c, 0x9c, 0x8c, 0x68, 0xec, 0xb2, 0x6c, 0x87, 0x2a, 0x46,
	0xab, 0x26, 0x8d, 0x2d, 0x1a, 0xa5, 0x36, 0x99, 0xf5, 0x93, 0xbd, 0x7e, 0x4c, 0xa9, 0x27, 0x56,
	0x34, 0xa3, 0x61, 0xdc, 0xd0, 0x4e, 0xe6, 0xf1, 0x64, 0x34, 0x2e, 0xd0, 0xd0, 0x8d, 0x07, 0x42,
	0xf8, 0x04, 0x35, 0xb1, 0xec, 0xfd, 0x75, 0x8d, 0x5c, 0xdd, 0x3b, 0x4b, 0x4e, 0x46, 0x34, 0xdc,
	0x1b, 0x8d, 0xe2, 0xe8, 0x19, 0x55, 0xf6, 0x59, 0x42, 0xe3, 0x67, 0x7e, 0x8f, 0xc2, 0x7c, 0xe4,
	0x3e, 0x53, 0x31, 0x30, 0xa5, 0xf8, 0xd6, 0xa3, 0x8b, 0x91, 0x74, 0x8f, 0x54, 0x88, 0xb7, 0x12,
	0xd3, 0xa7, 0xb4, 0xc7, 0x1c, 0xea, 0x26, 0x91, 0x54, 0x95, 0x1a, 0xc6, 0xe7, 0x10, 0x8d, 0x58,
	0xbe, 0x81, 0x04, 0x35, 0xf1, 0x1c, 0x7e, 0xa7, 0x4e, 0x56, 0x38, 0xdf, 0x0f, 0x5c, 0x46, 0xfb,
	0x51, 0x7c, 0x71, 0x14, 0x9e, 0x45, 0x25, 0x0f, 0x68, 0x85, 0x34, 0x46, 0xc2, 0x00, 0x36, 0x1d,
	0xfe, 0xc8, 0x87, 0xd6, 0x13, 0x6f, 0x28, 0xee, 0x8f, 0x86, 0x71, 0xdf, 0x56, 0xd2, 0x0f, 0xe8,
	0xb3, 0xcc, 0x16, 0xea, 0x20, 0xf7, 0xbb, 0x25, 0x20, 0x5c, 0x7a, 0x1c, 0x67, 0x01, 0xe5, 0x0b,
	0x79, 0x96, 0x06, 0x41, 0xc7, 0x65, 0xe7, 0xc2, 0x11, 0xca, 0x68, 0xeb, 0x1d, 0xd2, 0xec, 0x9d,
	0xfb, 0x81, 0x27, 0x3c, 0xbd, 0x35, 0xa9, 0x85, 0xd4, 0x49, 0x39, 0x58, 0xc5, 0xc4, 0x98, 0x59,
	0x13, 0x63, 0xfe, 0xb4, 0x46, 0xd6, 0x0b, 0x02, 0x59, 0x21, 0x89, 0x8a, 0xfb, 0xc4, 0x4b, 0x46,
	0xa8, 0xcf, 0x84, 0x32, 0x10, 0x24, 0xb7, 0x15, 0xfc, 0xd1, 0x41, 0x79, 0x43, 0x76, 0x29, 0x88,
	0x94, 0x53, 0x70, 0x97, 0xa6, 0x72, 0x39, 0x05, 0x97, 0x6a, 0xd2, 0xb5, 0xfc, 0xfd, 0x1a, 0xb9,
	0x01, 0xd3, 0x3e, 0xa7, 0xbd, 0x41, 0x97, 0x06, 0x67, 0x1d, 0xbf, 0x37, 0xe0, 0xaa, 0x48, 0x13,
	0xcb, 0x1c, 0xce, 0xc5, 0x32, 0xc7, 0xd4, 0xc9, 0xd5, 0xf5, 0x6d, 0xa6, 0x4c, 0xae, 0xa1, 0x4f,
	0x6e, 0x52, 0x43, 0xf7, 0x6d, 0xb2, 0x91, 0xb1, 0x94, 0x8f, 0xef, 0x98, 0x86, 0x5c, 0xdd, 0x4c,
	0xb4, 0xc3, 0xb3, 0x5d, 0x5c, 0x2f, 0xec, 0xe2, 0x49, 0x35, 0x8c, 0x4f, 0xae, 0xf2, 0xce, 0xf7,
	0xce, 0x92, 0x2e, 0x6e, 0x43, 0xd9, 0xed, 0x36, 0x59, 0x76, 0x33, 0x50, 0xdd, 0xaf, 0x45, 0xd8,
	0xd4, 0x55, 0xdd, 0xd4, 0xd5, 0x97, 0x78, 0x5c, 0x3b, 0x88, 0xc2, 0x33, 0x3f, 0x1e, 0x3a, 0xb4,
	0x47, 0xfd, 0x11, 0x7b, 0xf9, 0xee, 0x60, 0xdb, 0x49, 0x49, 0xe2, 0x8f, 0x13, 0xcf, 0xf5, 0xd7,
	0x1b, 0xe4, 0x36, 0x1f, 0xc1, 0x31, 0x8d, 0xb9, 0x23, 0xca, 0x8e, 0x42, 0x9f, 0xf9, 0x2e, 0xa3,
	0x7b, 0x67, 0x8c, 0xc6, 0xdc, 0xd6, 0x5d, 0xce, 0xf2, 0x72, 0xe7, 0xef, 0x90, 0x15, 0x78, 0xc9,
	0x8f, 0x42, 0xae, 0x9e, 0x40, 0x82, 0x50, 0x10, 0x4a, 0x38, 0x97, 0x34, 0x89, 0x1d, 0xd2, 0xa4,
	0x27, 0x44, 0x5a, 0xc3, 0xb8, 0x02, 0x94, 0x74, 0xc7, 0xef, 0xc9, 0x53, 0x90, 0x02, 0x81, 0x96,
	0x49, 0x13, 0x16, 0x0d, 0x69, 0x0c, 0x5a, 0x46, 0x9c, 0xb5, 0x55, 0xcc, 0x7a, 0x9f, 0xac, 0x4a,
	0xfa, 0x38, 0x3a, 0xf5, 0x03, 0xda, 0x39, 0x8f, 0x42, 0x79, 0xf8, 0x31, 0x15, 0xf1, 0x39, 0xbb,
	0x9e, 0x17, 0xd3, 0x24, 0x11, 0xa7, 0x6d, 0x49, 0x5a, 0xef, 0x93, 0x99, 0x64, 0x90, 0x82, 0x3f,
	0x33, 0x07, 0x9a, 0x64, 0x5d, 0x68, 0x12, 0x6e, 0x76, 0x69, 0xcc, 0x45, 0x67, 0x90, 0x1e, 0xb2,
	0xc8, 0x91, 0xd5, 0x4c, 0x0b, 0x42, 0x4c, 0x0b, 0x12, 0x10, 0xab, 0xdc, 0x8c, 0xee, 0x74, 0x36,
	0xa4, 0xd3, 0xc9, 0x5d, 0xbc, 0x41, 0x7a, 0xc0, 0x4f, 0x54, 0x99, 0x8b, 0x27, 0x68, 0xae, 0x53,
	0x47, 0x71, 0x34, 0x8c, 0x24, 0xb3, 0xe5, 0xf2, 0x6b, 0xa0, 0xfd, 0x5d, 0x11, 0x2f, 0x10, 0xba,
	0x2b, 0xe8, 0x1d, 0x47, 0x21, 0xbd, 0xb8, 0x7c, 0xcd, 0x95, 0xf9, 0xd7, 0x5f, 0x79, 0xfe, 0x46,
	0x81, 0x7c, 0x84, 0x56, 0x66, 0xdf, 0x4d, 0xe8, 0x58, 0xbf, 0xa5, 0x3a, 0x6e, 0x61, 0x09, 0xaf,
	0x09, 0x25, 0x0e, 0xdd, 0xa2, 0x1f, 0xd4, 0xa4, 0x5f, 0x24, 0xdc, 0xd7, 0x4e, 0xac, 0xec, 0xeb,
	0xaf, 0xee, 0xec, 0x7e, 0x44, 0x56, 0x92, 0x41, 0x0a, 0x8d, 0x72, 0x13, 0xa2, 0x38, 0xba, 0x99,
	0x89, 0xe9, 0x2a, 0xe5, 0x4e, 0xa9, 0xf6, 0xc4, 0x9a, 0xf1, 0x10, 0xf9, 0xa3, 0xb6, 0x36, 0xd6,
	0x85, 0x5d, 0x23, 0xcd, 0x11, 0xaf, 0x28, 0x0e, 0xb7, 0x48, 0xd8, 0x2f, 0x48, 0xdb, 0xc4, 0x0e,
	0x03, 0xbf, 0xe7, 0x2a, 0xfd, 0xc4, 0x0f, 0xb3, 0xb0, 0x09, 0xce, 0xf4, 0x46, 0xee, 0x9f, 0x16,
	0x5a, 0x4d, 0x03, 0x96, 0xc5, 0x50, 0x5e, 0x90, 0x56, 0x55, 0x1d, 0xce, 0x63, 0x08, 0x32, 0x29,
	0x26, 0x27, 0x07, 0xf8, 0x2a, 0x01, 0x71, 0xac, 0x2d, 0xbb, 0x86, 0x8d, 0xf5, 0x5d, 0x1f, 0x92,
	0x4d, 0x7e, 0xb2, 0xee, 0x53, 0x7e, 0x60, 0x97, 0x06, 0x1f, 0x04, 0x5f, 0xca, 0x81, 0xf0, 0x5e,
	0x70, 0x8f, 0x81, 0xf7, 0xd2, 0x26, 0xb3, 0xe0, 0x0c, 0x1c, 0x79, 0x09, 0x08, 0x7a, 0xc3, 0xc9,
	0x68, 0xfb, 0xef, 0x6b, 0x64, 0x7d, 0xcf, 0xf3, 0xd4, 0xd6, 0xaa, 0x1b, 0x7a, 0x87, 0xac, 0x24,
	0x4a, 0x45, 0x50, 0x52, 0x75, 0x11, 0xec, 0x28, 0xe0, 0xd6, 0xbb, 0xe4, 0x8a, 0x8a, 0xa1, 0x4b,
	0x84, 0x9b, 0xa5, 0x5c, 0xc0, 0x99, 0xd6, 0x8b, 0xa9, 0xcb, 0x68, 0xee, 0xda, 0xe5, 0xc0, 0xc4,
	0x1e, 0xc1, 0xdb, 0xe4, 0x5a, 0x69, 0x2e, 0x42, 0x1a, 0x72, 0x1f, 0x6f, 0x8e, 0xfb, 0x78, 0xf6,
	0xbf, 0xcf, 0x90, 0x45, 0x5e, 0x77, 0x90, 0x2a, 0x7a, 0x82, 0xc5, 0x6e, 0x8f, 0xe6, 0x7a, 0x42,
	0x90, 0xda, 0x7a, 0xd4, 0x0b, 0x92, 0x79, 0x87, 0x2c, 0x29, 0xb3, 0xf1, 0xa9, 0x0c, 0xc2, 0x16,
	0x50, 0x38, 0x3c, 0x4b, 0xf7, 0x4c, 0x86, 0xc8, 0x14, 0x84, 0xf7, 0x7e, 0x1a, 0xbb, 0xa1, 0x77,
	0x84, 0x53, 0x6b, 0x38, 0x92, 0x84, 0xf8, 0xee, 0x20, 0x55, 0x0c, 0x82, 0x24, 0x85, 0xe6, 0x04,
	0xd9, 0x03, 0x03, 0xd0, 0x70, 0x32, 0x9a, 0x3b, 0xca, 0xcf, 0xa9, 0xdf, 0x3f, 0x67, 0xa0, 0xf4,
	0xeb, 0x8e, 0xa0, 0xf8, 0xa2, 0xa6, 0xa3, 0x1e, 0x84, 0xba, 0xe6, 0x1c, 0xfe, 0xc8, 0x6b, 0xfa,
	0x43, 0x88, 0x27, 0x92, 0xad, 0x06, 0x77, 0xa9, 0x91, 0xe2, 0xf6, 0x6a, 0x14, 0x47, 0x5e, 0xda,
	0x63, 0x60, 0xd2, 0x30, 0xa8, 0xa5, 0x42, 0x7c, 0x4e, 0xfe, 0xd9, 0x63, 0x9f, 0x3e, 0x87, 0x0a,
	0x0b, 0x18, 0x10, 0xc8, 0x11, 0x88, 0xbc, 0x04, 0x51, 0xdf, 0x0d, 0x5b, 0x8b, 0x22, 0x22, 0x03,
	0x14, 0x77, 0x09, 0xf0, 0x29, 0x8f, 0xf5, 0x2d, 0xa1, 0x4b, 0x50, 0x80, 0xb9, 0xfe, 0x47, 0x48,
	0xc6, 0x8b, 0x97, 0x31, 0x5e, 0xac, 0x81, 0x38, 0x52, 0x7a, 0xe6, 0xbf, 0xf8, 0x84, 0x72, 0xe6,
	0xae, 0xc8, 0x91, 0x66, 0x10, 0x17, 0xaf, 0x8c, 0x6c, 0x5d, 0x41, 0xf1, 0xca, 0x00, 0x3e, 0x9e,
	0x51, 0x4c, 0x3f, 0xa1, 0x17, 0xf9, 0x78, 0x2c, 0x1c, 0x4f, 0x01, 0x46, 0x7b, 0xc4, 0x21, 0x39,
	0x9e, 0x55, 0x1c, 0x8f, 0x06, 0xf2, 0x79, 0x07, 0x34, 0xec, 0xb3, 0xf3, 0xd6, 0x1a, 0x46, 0x9c,
	0x90, 0xe2, 0x5a, 0xec, 0xb9, 0xef, 0xb1, 0xf3, 0xd6, 0x55, 0x0c, 0xdc, 0x02, 0xc1, 0x6b, 0x9f,
	0xe3, 0x4a, 0xad, 0x63, 0x6d, 0xa4, 0xac, 0x1d, 0x62, 0xb1, 0xd8, 0x0d, 0x93, 0x51, 0x14, 0xb3,
	0x3d, 0xc6, 0x62, 0xff, 0x34, 0x65, 0xb4, 0x75, 0x0d, 0xa3, 0x9c, 0xe5, 0x12, 0xae, 0x59, 0x02,
	0xff, 0x8b, 0xd4, 0xf7, 0xe0, 0x04, 0x41, 0x5b, 0x2d, 0xd4, 0x2c, 0x2a, 0x26, 0xc6, 0x9f, 0xf4,
	0x62, 0x7f, 0xe4, 0x73, 0xbd, 0xd5, 0xba, 0x9e, 0x8d, 0x3f, 0x07, 0xf9, 0xd6, 0x3d, 0xf7, 0xfb,
	0xe7, 0x5d, 0x3f, 0xec, 0xa7, 0x81, 0x1b, 0x3f, 0x76, 0x83, 0x94, 0xb6, 0xda, 0x18, 0x8d, 0x2d,
	0x15, 0x70, 0xee, 0xfb, 0xc9, 0x7e, 0x4c, 0xdd, 0x81, 0x7b, 0x1a, 0xd0, 0xd6, 0x06, 0x72, 0x5f,
	0x81, 0x78, 0x8d, 0x33, 0xff, 0x05, 0xf5, 0xc4, 0x81, 0xe7, 0x06, 0x1e, 0xfd, 0x14, 0x08, 0x64,
	0x30, 0x81, 0x10, 0xc5, 0x4d, 0x0c, 0x51, 0x20, 0x05, 0xf1, 0x59, 0x1a, 0x04, 0x07, 0x3e, 0xe3,
	0x3b, 0x6b, 0x73, 0xab, 0xc1, 0x25, 0x2c, 0x47, 0x4c, 0x8a, 0xe1, 0x96, 0x49, 0x31, 0xfc, 0x6b,
	0x8d, 0x2c, 0xc9, 0xdd, 0x2e, 0x14, 0x42, 0xa5, 0x33, 0x52, 0xb9, 0xd5, 0xa5, 0x41, 0x69, 0x28,
	0x06, 0xe5, 0x06, 0x99, 0x43, 0x93, 0x70, 0x9c, 0xf4, 0xa5, 0xde, 0xca, 0x00, 0x35, 0x94, 0xde,
	0xcc, 0x02, 0xe6, 0xd2, 0x0c, 0x9c, 0xb9, 0x7e, 0x40, 0xbd, 0x43, 0xca, 0x5c, 0x3f, 0x90, 0xae,
	0x9e, 0x8a, 0xc1, 0xc6, 0x4f, 0x7b, 0x3d, 0xee, 0xb8, 0xcd, 0x00, 0x57, 0x24, 0x09, 0x11, 0x1d,
	0x7c, 0x0f, 0x3d, 0x3a, 0x41, 0xd9, 0xff, 0xac, 0x47, 0xc4, 0x0e, 0xb2, 0xd8, 0x5c, 0xf2, 0xff,
	0x1c, 0xcb, 0x14, 0xc6, 0x1e, 0xc3, 0xaa, 0xaf, 0x16, 0xcb, 0xbc, 0x47, 0x16, 0xd4, 0x56, 0x5e,
	0x39, 0x6a, 0xf8, 0x1b, 0x7a, 0xd4, 0x4b, 0xe3, 0xd1, 0x6b, 0x8c, 0x7a, 0xfd, 0x98, 0x16, 0xf5,
	0xba, 0xa6, 0x7a, 0x15, 0x10, 0x5a, 0xd6, 0xe2, 0x5d, 0x9f, 0x93, 0x55, 0x43, 0xe1, 0x6b, 0x88,
	0x74, 0xfd, 0xdb, 0x0c, 0x69, 0x73, 0xae, 0x61, 0xeb, 0x1f, 0x47, 0x91, 0x97, 0x88, 0x0b, 0xc8,
	0x2c, 0xca, 0x55, 0xc5, 0x43, 0xdd, 0x60, 0xd5, 0x4b, 0x06, 0xcb, 0x6c, 0xf8, 0xe6, 0x4a, 0x86,
	0x4f, 0x31, 0x6c, 0x53, 0x95, 0x86, 0xad, 0xa9, 0x1b, 0xb6, 0xdc, 0x78, 0x4d, 0x6b, 0xc6, 0x8b,
	0xcb, 0xde, 0xa8, 0x07, 0xcb, 0x30, 0x23, 0x64, 0x0f, 0xc9, 0xa2, 0x8a, 0x99, 0x35, 0xab, 0x18,
	0x34, 0x73, 0x73, 0xe3, 0xcc, 0x1c, 0xb9, 0xcc, 0xcc, 0xcd, 0x9b, 0xcc, 0x9c, 0x50, 0xf7, 0x0b,
	0x66, 0x75, 0xbf, 0x68, 0x56, 0xf7, 0x68, 0xf3, 0xa4, 0xba, 0xcf, 0x8d, 0xe5, 0xf2, 0x65, 0xc6,
	0x72, 0x65, 0x42, 0x63, 0x79, 0x65, 0x02, 0x63, 0x69, 0x5d, 0x62, 0x2c, 0x57, 0x27, 0x30, 0x96,
	0x6b, 0x13, 0x1a, 0xcb, 0xab, 0x26, 0x63, 0x69, 0x36, 0x73, 0xeb, 0x13, 0x9b, 0xb9, 0x6b, 0x93,
	0x98, 0xb9, 0xd6, 0xc4, 0x66, 0xee, 0xfa, 0x84, 0x66, 0xae, 0x5d, 0x36, 0x73, 0xba, 0xb1, 0xda,
	0x00, 0x29, 0x53, 0x8d, 0x95, 0xe2, 0x60, 0xde, 0x28, 0x39, 0x98, 0x99, 0x23, 0x77, 0x33, 0x3b,
	0x02, 0xa3, 0x23, 0x67, 0xd0, 0x8f, 0x9b, 0x26, 0xfd, 0xf8, 0x7b, 0x35, 0x8c, 0x36, 0x95, 0xb6,
	0xfa, 0x4b, 0x1d, 0x87, 0x6c, 0xe5, 0x38, 0x54, 0xdb, 0x9e, 0xdf, 0x25, 0x5c, 0x71, 0xe9, 0x87,
	0x1f, 0x18, 0x6d, 0xda, 0x43, 0xc3, 0x24, 0x62, 0x77, 0x92, 0x56, 0x2c, 0x53, 0x53, 0xb3, 0x4c,
	0x7f, 0x51, 0x23, 0xd3, 0xe2, 0x7c, 0xa4, 0x18, 0xc5, 0x9a, 0x6e, 0x14, 0x37, 0x09, 0xc1, 0x2e,
	0x14, 0x45, 0xab, 0x20, 0xe3, 0x34, 0x5d, 0xc9, 0xa0, 0x4e, 0x19, 0x0c, 0x6a, 0x66, 0xf2, 0xd1,
	0xc0, 0xe4, 0x97, 0x5e, 0xb9, 0x09, 0x9f, 0x2e, 0x98, 0x70, 0xfb, 0x87, 0x35, 0xd4, 0xa0, 0x3f,
	0x32, 0x67, 0xa4, 0xef, 0x09, 0x39, 0xa9, 0x3a, 0x28, 0xfd, 0x44, 0x26, 0x01, 0x35, 0x90, 0x80,
	0x5b, 0x70, 0x23, 0xab, 0xd4, 0xec, 0xb8, 0x31, 0x0b, 0xf9, 0x71, 0x53, 0x98, 0x30, 0x29, 0x16,
	0x52, 0xc0, 0xea, 0x65, 0x01, 0xcb, 0xef, 0x58, 0x15, 0x01, 0x99, 0xd2, 0x04, 0xe4, 0xeb, 0x64,
	0x63, 0x4c, 0x27, 0xa5, 0xe3, 0xdb, 0xaf, 0x8a, 0xfb, 0x13, 0x61, 0x36, 0x0d, 0x2b, 0x93, 0xbf,
	0xd1, 0x80, 0xa0, 0xfe, 0xcb, 0xac, 0xcb, 0xa4, 0x61, 0x9e, 0x7b, 0x64, 0xb3, 0x6a, 0x10, 0x2f,
	0xb3, 0xeb, 0xec, 0xcf, 0x71, 0x32, 0x87, 0x34, 0xa0, 0x93, 0x4d, 0x66, 0xd2, 0xc8, 0xac, 0x18,
	0xa0, 0xa9, 0xe1, 0x97, 0x1a, 0x60, 0x82, 0x32, 0xd3, 0x8d, 0x62, 0x36, 0xd9, 0x2e, 0xd0, 0x43,
	0x0e, 0xb0, 0x55, 0x25, 0x3d, 0x31, 0x77, 0x0f, 0x31, 0xbc, 0x5f, 0xee, 0xf4, 0xa5, 0x86, 0xfe,
	0x83, 0x1a, 0xb9, 0xfe, 0x69, 0x4a, 0xb9, 0x48, 0xf5, 0xe9, 0x3e, 0xb8, 0x1e, 0xe1, 0x59, 0x24,
	0x47, 0x7e, 0x83, 0xcc, 0x81, 0x3b, 0x02, 0xe2, 0x20, 0x82, 0x35, 0x19, 0xa0, 0xfa, 0x2e, 0xc8,
	0xde, 0xcc, 0x77, 0x59, 0x27, 0xd3, 0x67, 0x3e, 0x0d, 0x3c, 0xe9, 0xf5, 0x08, 0x8a, 0xe3, 0x90,
	0x0c, 0x14, 0x89, 0xb8, 0x97, 0xa0, 0xb4, 0xcc, 0xa0, 0x66, 0x21, 0x33, 0xc8, 0xc0, 0x8f, 0x69,
	0x13, 0x3f, 0xbe, 0x45, 0xda, 0xa6, 0x89, 0xe4, 0xe7, 0x19, 0xc8, 0x4a, 0x11, 0xde, 0x22, 0x12,
	0xd6, 0xdb, 0xd9, 0x6e, 0xc6, 0x08, 0xe7, 0x15, 0xe9, 0x8d, 0xe7, 0x0d, 0xc8, 0x98, 0xd6, 0x13,
	0xb2, 0xa8, 0x15, 0x94, 0xae, 0xc5, 0x34, 0x5e, 0xd5, 0x8b, 0xbc, 0xda, 0x22, 0xf3, 0x40, 0x74,
	0xd5, 0x2c, 0x11, 0x15, 0xb2, 0x63, 0xb2, 0x05, 0xe3, 0x3f, 0xe0, 0x82, 0x90, 0x7b, 0x88, 0xf7,
	0xa2, 0xf8, 0xa4, 0x53, 0x16, 0x74, 0xec, 0x33, 0xe7, 0xb3, 0x48, 0x47, 0x11, 0x7c, 0x9e, 0x54,
	0x86, 0x3e, 0x25, 0x6f, 0x8c, 0xe9, 0x53, 0xb0, 0xee, 0xdd, 0x02, 0x93, 0xcc, 0x17, 0x6a, 0x92,
	0x4f, 0xcf, 0xc9, 0xb5, 0x3c, 0xd8, 0x2c, 0xaf, 0x3c, 0xbe, 0xca, 0x9d, 0xed, 0xa4, 0x73, 0x49,
	0xd4, 0x28, 0x37, 0x76, 0x1c, 0xbc, 0x62, 0x74, 0xd9, 0x9c, 0x15, 0x67, 0x29, 0x59, 0x71, 0x32,
	0xe6, 0xdc, 0x23, 0xb7, 0x94, 0x3b, 0xac, 0x38, 0x08, 0x68, 0x7c, 0x48, 0x03, 0xff, 0x19, 0x8d,
	0x27, 0x08, 0xb0, 0x4f, 0xaa, 0xa6, 0xfe, 0x43, 0xbd, 0x7c, 0x44, 0x63, 0x7d, 0x79, 0xe3, 0xbb,
	0x64, 0xcd, 0x13, 0x23, 0xc9, 0xce, 0xae, 0x47, 0xc9, 0x33, 0x31, 0x55, 0x63, 0xd9, 0xa4, 0xac,
	0xb6, 0x3e, 0x20, 0x6b, 0x7a, 0x1e, 0xe4, 0x93, 0x53, 0xda, 0xcf, 0xac, 0xee, 0xaa, 0x5e, 0xb6,
	0xcf, 0x8b, 0xac, 0xaf, 0x13, 0xab, 0xf0, 0x0a, 0x0d, 0x3d, 0xe1, 0x05, 0x5d, 0xd1, 0x4b, 0xee,
	0x86, 0x9e, 0xfd, 0x6b, 0x35, 0x45, 0x8c, 0xe4, 0x94, 0x5f, 0xe3, 0x5a, 0x6e, 0x93, 0x59, 0xd0,
	0xfc, 0x79, 0x96, 0xe3, 0x82, 0x94, 0x6a, 0xc8, 0x9f, 0xca, 0x4a, 0xed, 0x5f, 0xca, 0x6b, 0x4e,
	0xe8, 0x24, 0xee, 0x10, 0xc2, 0xdf, 0x70, 0x54, 0x47, 0x71, 0x49, 0xb6, 0x2e, 0x9c, 0x45, 0xa5,
	0x86, 0xfd, 0x57, 0x35, 0xf5, 0x05, 0x45, 0x47, 0xd6, 0x2a, 0x75, 0x64, 0x31, 0x7b, 0x72, 0x8b,
	0xcc, 0x0f, 0xdd, 0x17, 0x1d, 0x59, 0x2c, 0xb4, 0x8b, 0x02, 0x71, 0xe7, 0x11, 0x52, 0x2a, 0xf1,
	0x22, 0x49, 0xe4, 0xa1, 0xe5, 0x88, 0xf5, 0x0d, 0xb2, 0x94, 0x8f, 0x01, 0xe2, 0x13, 0x4d, 0xd8,
	0xec, 0x4b, 0xb9, 0x87, 0x0b, 0xfe, 0x72, 0xa1, 0x96, 0xfd, 0xe7, 0x4b, 0x84, 0xe4, 0xe4, 0x98,
	0xcb, 0xf0, 0xcb, 0x33, 0x75, 0x7f, 0x86, 0x5c, 0x07, 0xb2, 0x03, 0x59, 0x6d, 0x31, 0x93, 0x1b,
	0x09, 0x0e, 0x47, 0xb8, 0x7a, 0xd5, 0x15, 0xac, 0x9f, 0x24, 0xd7, 0x64, 0xe1, 0xdd, 0xd0, 0xd3,
	0xde, 0x45, 0xb1, 0xac, 0x2a, 0x1e, 0x13, 0x86, 0xba, 0x4d, 0x16, 0x4f, 0xd3, 0x0b, 0x1a, 0xdf,
	0x4b, 0x83, 0x00, 0xd4, 0x3a, 0x1e, 0xab, 0x75, 0x90, 0xbb, 0x4e, 0x19, 0xb0, 0xa7, 0x5d, 0x25,
	0x96, 0x70, 0xeb, 0x0e, 0x59, 0x02, 0xec, 0x11, 0x0d, 0xe8, 0x08, 0xae, 0x26, 0x31, 0xd4, 0x5c,
	0x40, 0xc1, 0x5c, 0x70, 0x04, 0x6f, 0x2a, 0xe5, 0xb1, 0x5b, 0x81, 0xb8, 0x73, 0x5c, 0xda, 0xc3,
	0x22, 0x0a, 0x5d, 0x2e, 0xa8, 0xd4, 0x06, 0x0b, 0x63, 0xb4, 0xc1, 0xfb, 0x64, 0xb5, 0x88, 0x73,
	0x1e, 0x60, 0xb0, 0xda, 0x54, 0xa4, 0x8e, 0xe9, 0xc0, 0x8d, 0x63, 0x9f, 0xc6, 0x0f, 0x23, 0x71,
	0x8e, 0x2f, 0x17, 0xa8, 0xed, 0x4b, 0xd0, 0xcd, 0x62, 0xd8, 0xa6, 0x22, 0xeb, 0x43, 0x72, 0x55,
	0xc2, 0x1d, 0xb7, 0x37, 0x70, 0xfb, 0xf4, 0x73, 0x8c, 0x15, 0xf0, 0x23, 0x7f, 0xcd, 0x31, 0x17,
	0x6a, 0xfd, 0xa0, 0x5d, 0x50, 0x8e, 0xff, 0xa6, 0xa2, 0x2c, 0xab, 0xbb, 0xe3, 0x5e, 0xc0, 0xb5,
	0xaa, 0x05, 0xc2, 0xaa, 0x61, 0x7c, 0x43, 0x8d, 0xdc, 0x0b, 0x91, 0xc6, 0x09, 0x71, 0x80, 0xa6,
	0xa3, 0x20, 0xd6, 0x36, 0x59, 0x86, 0xfa, 0x8f, 0xf8, 0x1e, 0x83, 0x1b, 0x57, 0x11, 0xee, 0x2e,
	0xc2, 0xfc, 0x88, 0x0f, 0xd0, 0xa1, 0x9f, 0x80, 0x57, 0x82, 0x95, 0x31, 0x08, 0x6e, 0x28, 0xe1,
	0x5c, 0x06, 0xf4, 0x5e, 0x0c, 0xf3, 0xc3, 0xea, 0x18, 0x1c, 0x2f, 0x17, 0xf0, 0xd6, 0x83, 0xa8,
	0xe7, 0x06, 0x52, 0xe4, 0xb1, 0xfa, 0x35, 0x6c, 0xbd, 0x5c, 0x62, 0x75, 0xc8, 0x9b, 0x43, 0x91,
	0x27, 0xd0, 0x71, 0x2f, 0x86, 0x34, 0x64, 0x87, 0x7e, 0xc2, 0xdc, 0xb0, 0x47, 0xb5, 0xfe, 0x5a,
	0xd0, 0xc0, 0x24, 0x55, 0xad, 0x6f, 0x90, 0xf5, 0x08, 0x6f, 0xe0, 0x7a, 0xd4, 0x7f, 0xe6, 0x9e,
	0x06, 0xb2, 0x18, 0xa2, 0x09, 0x4d, 0xa7, 0xa2, 0xd4, 0xda, 0x27, 0x37, 0x46, 0x81, 0xcb, 0xce,
	0xa2, 0x78, 0xd8, 0x89, 0xfc, 0x90, 0x25, 0x87, 0xd4, 0x4b, 0x7b, 0x5c, 0xd8, 0x70, 0x08, 0x6d,
	0x78, 0x7b, 0x6c, 0x9d, 0xac, 0xef, 0x7d, 0xbe, 0x73, 0x3a, 0xee, 0x05, 0x6f, 0x1f, 0xdf, 0xde,
	0x50, 0xfa, 0x2e, 0x95, 0xf2, 0x7d, 0x3a, 0x02, 0x21, 0xf2, 0xc3, 0x3e, 0xd6, 0xc7, 0xb0, 0x7c,
	0x01, 0xe5, 0x4a, 0x19, 0x36, 0xe5, 0x83, 0xb0, 0x0f, 0xa1, 0x89, 0x9a, 0x93, 0xd1, 0x79, 0x99,
	0xcb, 0x20, 0x26, 0x91, 0x95, 0xb9, 0x2c, 0xd3, 0x2c, 0x07, 0x3e, 0xc3, 0xb3, 0xd6, 0x2d, 0x45,
	0xb3, 0x48, 0x30, 0xd3, 0x2c, 0xa0, 0xa2, 0xc5, 0xa1, 0x6c, 0x4b, 0xd1, 0x2c, 0x0a, 0xce, 0xeb,
	0xe6, 0x73, 0x11, 0x89, 0x45, 0x6f, 0x60, 0xdd, 0x22, 0xce, 0xb5, 0x01, 0xaa, 0xe5, 0xf0, 0x59,
	0xe4, 0xf7, 0xe8, 0xc9, 0x88, 0x86, 0xc7, 0xbc, 0xbe, 0x0d, 0x73, 0x34, 0x96, 0x59, 0x1f, 0xe1,
	0x9d, 0xf4, 0x89, 0x52, 0xd6, 0x7a, 0x13, 0x6c, 0x5b, 0xe6, 0x0f, 0xaa, 0x65, 0x4e, 0xa9, 0xb6,
	0xf5, 0xb3, 0x64, 0x99, 0x63, 0x1d, 0x8c, 0x1d, 0x82, 0x8d, 0xb9, 0x0d, 0x36, 0x66, 0x55, 0x36,
	0xa0, 0x14, 0x39, 0xc5, 0xba, 0xd6, 0x87, 0x18, 0xe1, 0x96, 0x7b, 0xa1, 0xf5, 0x35, 0x3d, 0x7e,
	0x2e, 0x71, 0x47, 0xab, 0x65, 0xf8, 0x90, 0xe3, 0x8e, 0xf1, 0x43, 0x0e, 0x50, 0xb8, 0x98, 0x1a,
	0xff, 0xc8, 0xed, 0xb7, 0xde, 0x92, 0x0a, 0x37, 0x83, 0x20, 0x5d, 0x4a, 0xa4, 0xbe, 0x83, 0x52,
	0xd8, 0x46, 0xa5, 0xa0, 0x62, 0xd9, 0xa6, 0xc7, 0x0c, 0x31, 0xe8, 0xee, 0x6d, 0x8c, 0xfe, 0x15,
	0x60, 0xfb, 0x9b, 0x65, 0x76, 0xf2, 0x1e, 0x7c, 0x7c, 0x7c, 0xe4, 0xb3, 0x20, 0x4b, 0xc8, 0x52,
	0x31, 0x2e, 0x38, 0x82, 0x3e, 0x4c, 0xd9, 0x45, 0x76, 0x03, 0xa1, 0x83, 0xf6, 0x3f, 0x4c, 0x95,
	0x78, 0x3d, 0xc6, 0x34, 0xef, 0x10, 0x0b, 0x52, 0x4a, 0x12, 0x96, 0x0e, 0x69, 0x27, 0x8e, 0x46,
	0x34, 0x16, 0x37, 0x03, 0x73, 0x8e, 0xa1, 0x84, 0x0b, 0xb6, 0xeb, 0x3d, 0x4d, 0x13, 0x26, 0x02,
	0x4d, 0x0d, 0x27, 0xa3, 0xf3, 0x20, 0xd2, 0x94, 0x7a, 0x6f, 0x54, 0x1d, 0xcb, 0xc6, 0xd8, 0xde,
	0x91, 0xc7, 0x8d, 0x91, 0x48, 0xe4, 0x93, 0x34, 0x44, 0x0c, 0x07, 0xe9, 0x7d, 0x2f, 0xbf, 0xc2,
	0x6d, 0x3a, 0x0a, 0xa2, 0xa5, 0xc6, 0xcc, 0x16, 0x52, 0x63, 0x36, 0x09, 0xc1, 0x31, 0x1d, 0x73,
	0xbb, 0x3e, 0x27, 0xf2, 0xee, 0x33, 0x44, 0x8d, 0x95, 0x13, 0x3d, 0x56, 0x7e, 0x9b, 0x2c, 0xe2,
	0x0d, 0x4c, 0x4c, 0xb1, 0xe3, 0x79, 0xe1, 0x02, 0xab, 0x20, 0x48, 0x03, 0x72, 0x06, 0x2b, 0x2d,
	0x08, 0x69, 0x50, 0xb0, 0x72, 0x7a, 0xce, 0xa2, 0x21, 0x3d, 0x47, 0xcc, 0xf2, 0x91, 0xfb, 0xc2,
	0x71, 0x99, 0xbc, 0xe9, 0x55, 0x10, 0x11, 0x81, 0xc7, 0x17, 0x8e, 0x3c, 0x30, 0x8f, 0x4d, 0x47,
	0x85, 0x78, 0x8d, 0x98, 0x06, 0x2e, 0xa3, 0x5e, 0x77, 0x90, 0x26, 0xf2, 0x82, 0x57, 0x81, 0xe0,
	0xce, 0x6a, 0x90, 0x0a, 0x63, 0x79, 0x05, 0x2e, 0x0d, 0x72, 0x00, 0xd3, 0x37, 0x43, 0x46, 0xa9,
	0x50, 0xac, 0xc2, 0xdc, 0xa9, 0x98, 0xfd, 0xb7, 0x75, 0x7d, 0xfb, 0x71, 0x06, 0xa2, 0xf0, 0x1e,
	0x4a, 0x71, 0x12, 0x24, 0x5f, 0x96, 0x3d, 0x14, 0x87, 0x43, 0x71, 0x31, 0x92, 0xd1, 0xdc, 0xb9,
	0x85, 0x80, 0x64, 0x22, 0xef, 0x83, 0x90, 0xe2, 0x43, 0x90, 0x2d, 0x03, 0xa7, 0xd0, 0x41, 0xd5,
	0x30, 0x2e, 0xa6, 0x92, 0xc6, 0x13, 0x01, 0xd4, 0xc4, 0x70, 0x81, 0xa1, 0x44, 0x6d, 0x13, 0xd6,
	0x59, 0x5c, 0x22, 0xaa, 0x18, 0x5f, 0x22, 0x49, 0xab, 0x52, 0xa6, 0x83, 0x5c, 0x89, 0x60, 0xce,
	0x53, 0xc7, 0x15, 0xf6, 0x13, 0xc5, 0xad, 0x80, 0xf2, 0x1e, 0x3b, 0x81, 0xcb, 0xb2, 0x5a, 0x28,
	0x76, 0x1a, 0x66, 0xff, 0x59, 0x8d, 0x6c, 0xe5, 0x47, 0xc6, 0xf3, 0x28, 0xf5, 0xba, 0xf0, 0x21,
	0x0c, 0x37, 0x9f, 0xaf, 0xf5, 0x88, 0xf3, 0x80, 0xac, 0x99, 0xfa, 0x10, 0xc7, 0x1d, 0xc8, 0xcd,
	0x37, 0x8e, 0xc1, 0xf8, 0x96, 0xfd, 0xbd, 0xba, 0xb9, 0xb9, 0x5c, 0x0e, 0x3c, 0x5d, 0x0e, 0x20,
	0xa4, 0x98, 0xd7, 0xdb, 0x1b, 0x66, 0x19, 0x6c, 0x0d, 0xa7, 0x84, 0x73, 0xc5, 0x09, 0x31, 0xf9,
	0x83, 0x68, 0x38, 0xf4, 0x93, 0xc4, 0x17, 0xf9, 0xcd, 0x0d, 0xa7, 0x08, 0x73, 0xef, 0x47, 0x38,
	0x08, 0x4a, 0x5d, 0x54, 0x36, 0xe5, 0x02, 0x3e, 0x86, 0x0e, 0xf8, 0x82, 0x4a, 0x65, 0x4c, 0x20,
	0x29, 0xe1, 0xdc, 0x2a, 0x7e, 0x9c, 0xba, 0x31, 0x48, 0xbd, 0xa7, 0xd4, 0x9f, 0x86, 0xfa, 0xc6,
	0x32, 0xfb, 0x5f, 0xc4, 0xd1, 0x1c, 0x05, 0x5c, 0x4b, 0x34, 0xaa, 0x3e, 0x9a, 0x57, 0xe7, 0x05,
	0xe7, 0x39, 0xe8, 0x0d, 0x2d, 0x07, 0x7d, 0x97, 0x10, 0xec, 0x41, 0xb9, 0x39, 0xb6, 0x32, 0xb3,
	0xbb, 0x77, 0xd2, 0xc5, 0x52, 0x47, 0xa9, 0x65, 0xdd, 0x24, 0xc4, 0x7d, 0x0e, 0x23, 0x92, 0xc1,
	0xee, 0x39, 0x67, 0x4e, 0x20, 0xe6, 0xe0, 0x83, 0x31, 0xac, 0xf6, 0xf3, 0x78, 0x30, 0xcc, 0x3b,
	0xb9, 0xf4, 0x6a, 0xb9, 0x22, 0x5b, 0xd1, 0xfe, 0xbb, 0x06, 0x9e, 0xe9, 0x35, 0x5e, 0xbd, 0x46,
	0x81, 0x57, 0x18, 0x3e, 0x55, 0x3a, 0x7b, 0xa2, 0xea, 0xc7, 0x81, 0x35, 0x45, 0x72, 0x7d, 0x0e,
	0x81, 0x9c, 0x80, 0xad, 0xa4, 0xf9, 0x61, 0x0a, 0xf5, 0x45, 0x09, 0x37, 0x79, 0xf6, 0x98, 0x5e,
	0x34, 0xa1, 0x67, 0x8f, 0x1f, 0xd9, 0x4d, 0xec, 0xd9, 0xe3, 0x67, 0x77, 0x06, 0xcf, 0xbe, 0xda,
	0xb7, 0x25, 0xf0, 0x4a, 0xb5, 0x6f, 0x3b, 0x1d, 0xb9, 0x20, 0x48, 0xf3, 0xf9, 0x11, 0x3f, 0x5f,
	0x18, 0x47, 0x94, 0x5a, 0xd7, 0xc5, 0xf7, 0xb1, 0x4f, 0x92, 0x50, 0x9c, 0x13, 0x91, 0xa1, 0xdd,
	0xd0, 0xfe, 0x23, 0x11, 0xb2, 0x10, 0x12, 0xf1, 0xf2, 0x99, 0xab, 0x8a, 0x43, 0xd0, 0xd0, 0x1d,
	0x02, 0xdd, 0xe8, 0x8b, 0x4c, 0x30, 0xdd, 0xe8, 0x67, 0xd2, 0xd7, 0x2c, 0x48, 0x9f, 0xc8, 0xde,
	0x9a, 0xce, 0xb2, 0xb7, 0xec, 0x6f, 0xe3, 0xd5, 0x01, 0x04, 0x39, 0x4f, 0xdc, 0x24, 0xd9, 0x4f,
	0x93, 0x64, 0x5c, 0xfa, 0xab, 0x9e, 0xbd, 0x2f, 0x33, 0x39, 0xea, 0x97, 0x7e, 0x8d, 0x64, 0x8c,
	0x48, 0x7e, 0xb7, 0x86, 0xf7, 0x0b, 0xa6, 0xde, 0x5f, 0xa3, 0xdc, 0xbf, 0xad, 0xe5, 0x52, 0x5c,
	0x05, 0xc5, 0x2e, 0x3a, 0xc3, 0x2f, 0x43, 0x07, 0x69, 0x22, 0xc2, 0x95, 0x7f, 0x32, 0x45, 0xae,
	0x94, 0xca, 0xc6, 0xf0, 0x20, 0x5b, 0xd8, 0x7a, 0xd5, 0xc2, 0x36, 0x0a, 0x0b, 0xcb, 0x0f, 0x4e,
	0xd2, 0x35, 0xc9, 0x97, 0x90, 0x1f, 0x9c, 0x34, 0x14, 0xa2, 0x59, 0xdc, 0x3f, 0xe5, 0x35, 0x64,
	0xc4, 0x5f, 0xd0, 0x70, 0x93, 0x97, 0x39, 0x56, 0xa8, 0x94, 0x72, 0x00, 0x3e, 0x97, 0x1d, 0xa4,
	0x99, 0x8d, 0xc5, 0xad, 0xa7, 0x42, 0xf2, 0x7d, 0xc7, 0xf5, 0xfc, 0x48, 0x58, 0xea, 0x1c, 0x00,
	0x5f, 0xc8, 0x0d, 0x28, 0x96, 0xa2, 0x85, 0xce, 0x01, 0xf9, 0xae, 0xba, 0x8f, 0x72, 0x40, 0xbe,
	0x8b, 0xa5, 0xf3, 0xe2, 0x43, 0x5e, 0x09, 0x58, 0x1f, 0x91, 0x0d, 0x79, 0x18, 0x3d, 0x0a, 0x19,
	0xed, 0xc7, 0xfc, 0x6c, 0xcd, 0xfd, 0x6f, 0xac, 0xbf, 0x00, 0xf5, 0xc7, 0x55, 0x99, 0xd0, 0x63,
	0xbc, 0x4d, 0x16, 0xa3, 0x94, 0xed, 0xf5, 0x98, 0xff, 0xcc, 0x67, 0x17, 0x47, 0x9e, 0x70, 0x1a,
	0x75, 0xd0, 0xfa, 0x39, 0xb2, 0xe0, 0x09, 0xed, 0x12, 0xf0, 0xcd, 0xbe, 0x0c, 0xe2, 0xb1, 0x91,
	0xd9, 0x7d, 0x90, 0x01, 0x4d, 0xff, 0x38, 0xda, 0x0b, 0xf6, 0xdf, 0xd4, 0xc9, 0xb5, 0x8a, 0x9a,
	0xe5, 0x81, 0xd6, 0x4c, 0x03, 0x7d, 0x9f, 0xac, 0x66, 0x80, 0xe2, 0xb2, 0xa1, 0x32, 0x30, 0x15,
	0xc1, 0x35, 0xe4, 0x20, 0xd5, 0xf5, 0x25, 0x3a, 0x02, 0x25, 0x5c, 0x5f, 0xc8, 0xa9, 0xb1, 0x0b,
	0xd9, 0x1c, 0xbb, 0x90, 0xd3, 0xc5, 0x85, 0x54, 0x67, 0xa7, 0xa4, 0xd3, 0xe8, 0x60, 0x79, 0x19,
	0x66, 0x0d, 0xcb, 0x60, 0xff, 0x53, 0xf6, 0x91, 0x29, 0x88, 0xaf, 0xf2, 0x91, 0xe9, 0x57, 0x4f,
	0x36, 0x1b, 0x77, 0xe3, 0x9f, 0xe5, 0x84, 0x4f, 0x89, 0xcf, 0xe0, 0xe5, 0xd6, 0x12, 0x9f, 0x9d,
	0x3d, 0x8c, 0xa4, 0x4f, 0x90, 0x01, 0x13, 0xfb, 0x04, 0xdf, 0xc4, 0x4b, 0xff, 0x8f, 0xa9, 0xf1,
	0xba, 0x73, 0xd2, 0x7f, 0x37, 0x54, 0x5c, 0x5e, 0xd9, 0x7f, 0x20, 0xae, 0xe0, 0x4b, 0xcd, 0xbf,
	0x54, 0xaa, 0x86, 0xe1, 0x0b, 0x01, 0xe5, 0x26, 0x6b, 0x4a, 0xb9, 0xc9, 0x3a, 0x54, 0x7a, 0x50,
	0x13, 0x39, 0x94, 0x04, 0xc3, 0xa6, 0x96, 0x60, 0x68, 0x33, 0xb2, 0x52, 0x7c, 0xcb, 0x78, 0x81,
	0x5e, 0xab, 0xb8, 0x40, 0x2f, 0x7f, 0x41, 0x87, 0x17, 0x7b, 0x8d, 0xec, 0x62, 0xcf, 0x22, 0x53,
	0x49, 0x14, 0xcb, 0x40, 0x3d, 0x3c, 0xdb, 0xdf, 0xa9, 0x93, 0x35, 0x30, 0x25, 0x7c, 0x81, 0xc3,
	0xb3, 0x28, 0x51, 0x6c, 0x98, 0x34, 0xa6, 0x35, 0xdd, 0x98, 0x2a, 0xa7, 0xdc, 0xba, 0x7e, 0xca,
	0xcd, 0x34, 0x7b, 0x43, 0xd5, 0xec, 0xfa, 0xfd, 0x6c, 0xa3, 0xf2, 0x7e, 0xb6, 0xa1, 0xdf, 0x3d,
	0xf8, 0xc9, 0x3d, 0x3f, 0x60, 0x70, 0xf5, 0x25, 0x8c, 0xaf, 0x0a, 0xf1, 0x8d, 0xd2, 0x55, 0x57,
	0x5e, 0xe8, 0x6c, 0x1d, 0xc4, 0x5f, 0x05, 0xb8, 0x71, 0xef, 0x1c, 0x3f, 0x4b, 0x82, 0xc1, 0xcd,
	0x8a, 0xad, 0x5f, 0xc0, 0xed, 0x2f, 0xc9, 0xd5, 0x02, 0x27, 0x5e, 0xab, 0x6c, 0x64, 0x51, 0xad,
	0xee, 0x20, 0x85, 0x7e, 0x8a, 0xd9, 0x1c, 0xf6, 0x6f, 0xd7, 0xc8, 0x4a, 0xb1, 0x50, 0xbf, 0x63,
	0x6e, 0xc8, 0x3b, 0xe6, 0x75, 0xe5, 0xfa, 0x54, 0xf8, 0xf4, 0x20, 0x30, 0x6f, 0x91, 0xd9, 0xa7,
	0x49, 0x14, 0x1e, 0xe2, 0x40, 0xb8, 0x38, 0xce, 0x8b, 0x2e, 0x8f, 0x5d, 0x3f, 0x74, 0xb2, 0x42,
	0x23, 0x63, 0xa6, 0x2a, 0x18, 0xf3, 0x87, 0x75, 0x32, 0x23, 0x5a, 0x78, 0x85, 0x14, 0x5e, 0x3d,
	0xa9, 0xb1, 0x31, 0x2e, 0x0b, 0xff, 0xff, 0x36, 0x59, 0xb1, 0xa1, 0x27, 0x2b, 0x96, 0x93, 0x2b,
	0xe7, 0x8c, 0x5f, 0x15, 0xe4, 0x49, 0x83, 0x44, 0x4d, 0x1a, 0xb4, 0x7f, 0xb3, 0x26, 0x76, 0x12,
	0xf7, 0x73, 0xf7, 0x2f, 0x30, 0x06, 0x71, 0xc8, 0x22, 0x78, 0x01, 0xc3, 0x13, 0x35, 0x68, 0x50,
	0x50, 0x7c, 0xb0, 0xfe, 0xb0, 0x9f, 0x19, 0xaf, 0x86, 0x23, 0x49, 0xae, 0xc4, 0xcf, 0xdd, 0xd0,
	0x0b, 0x68, 0x76, 0xb1, 0xcf, 0xdf, 0xd3, 0x30, 0x88, 0x28, 0x69, 0xf2, 0x8f, 0xcc, 0xd3, 0xc1,
	0xdd, 0xff, 0x99, 0x87, 0xaf, 0xc2, 0x64, 0xcc, 0x4e, 0x7c, 0xfb, 0x67, 0x3d, 0x26, 0xab, 0x86,
	0x7c, 0x24, 0x6b, 0x33, 0x77, 0xda, 0x4d, 0x99, 0x57, 0xed, 0x5b, 0x95, 0xe5, 0x42, 0x58, 0x3f,
	0x20, 0xd3, 0x98, 0xf2, 0x6d, 0x41, 0xd2, 0x83, 0xf6, 0xb1, 0x47, 0x3b, 0x0b, 0xe9, 0x6a, 0x9f,
	0x68, 0x7d, 0x82, 0x43, 0x29, 0xa4, 0xd0, 0xe5, 0x43, 0x31, 0xa7, 0xd1, 0x56, 0x34, 0xf6, 0x04,
	0x0f, 0xce, 0xe5, 0xe4, 0x20, 0xeb, 0x0d, 0xbd, 0x3d, 0xd3, 0xec, 0xec, 0x71, 0x55, 0xf4, 0x0e,
	0xca, 0xc9, 0x3d, 0x79, 0x07, 0x95, 0x19, 0x45, 0x79, 0x07, 0x63, 0x72, 0x83, 0x7e, 0x41, 0xfc,
	0x6c, 0xa0, 0x90, 0x80, 0x63, 0x65, 0xac, 0xaf, 0xc8, 0x07, 0x6a, 0x6f, 0x55, 0x57, 0x10, 0x4d,
	0x9f, 0x8b, 0xa4, 0x1c, 0x53, 0x5e, 0x86, 0x75, 0x9b, 0xbf, 0x7e, 0x59, 0xaa, 0x48, 0xfb, 0x6b,
	0x97, 0xd4, 0x12, 0x3d, 0x75, 0x89, 0x55, 0xce, 0x9a, 0xb1, 0x6e, 0x66, 0x2f, 0x9b, 0xd2, 0x82,
	0xda, 0x9b, 0x55, 0xc5, 0x39, 0xeb, 0xcd, 0x39, 0xe4, 0xc8, 0xfa, 0xb1, 0x39, 0xf8, 0xc8, 0xfa,
	0x4b, 0x52, 0xd0, 0xef, 0x93, 0xe5, 0xe2, 0x86, 0x68, 0x4b, 0x29, 0x36, 0x30, 0x7c, 0xc3, 0x58,
	0x96, 0x73, 0xa0, 0xfc, 0xb1, 0x9a, 0x75, 0xb3, 0xea, 0x43, 0x37, 0x85, 0x03, 0x63, 0xbe, 0xae,
	0xd3, 0x39, 0xa0, 0xfe, 0x8a, 0xa2, 0xc8, 0x81, 0xf2, 0x7f, 0x39, 0x4a, 0x1c, 0x30, 0xfd, 0xe6,
	0xe1, 0x88, 0xcc, 0x2b, 0x39, 0xf1, 0xd6, 0x7a, 0x29, 0x83, 0x7e, 0xf2, 0xa6, 0xf6, 0xc8, 0x92,
	0xee, 0xa0, 0x5a, 0xd7, 0xf3, 0xd6, 0x0a, 0x4e, 0x6b, 0xc5, 0x66, 0x3e, 0x25, 0xab, 0x1f, 0x53,
	0xa6, 0xe6, 0x04, 0x81, 0x66, 0x78, 0xad, 0x92, 0x9a, 0x0d, 0xf3, 0x24, 0x14, 0x93, 0xbe, 0x5e,
	0x98, 0xf4, 0xa5, 0xc3, 0x14, 0xba, 0xb4, 0xe0, 0x58, 0xe6, 0x0a, 0xcc, 0xec, 0xd0, 0xe6, 0xba,
	0xb4, 0xca, 0x23, 0x3d, 0x24, 0x8b, 0x9a, 0x3b, 0x62, 0xb5, 0xb2, 0x29, 0x15, 0x7c, 0xb5, 0xf6,
	0x75, 0x43, 0x09, 0xb6, 0xb2, 0xfb, 0xfd, 0x05, 0xe5, 0x4e, 0x48, 0xaa, 0xff, 0x07, 0xa4, 0x55,
	0xf8, 0xef, 0xc0, 0x5e, 0xe8, 0x39, 0xf4, 0x2c, 0x0d, 0x3d, 0x14, 0x79, 0xf3, 0x5f, 0x09, 0x2a,
	0x18, 0x70, 0x82, 0x5f, 0xbd, 0x97, 0x7e, 0x09, 0x60, 0x65, 0x2a, 0xa9, 0xea, 0x6f, 0x01, 0x15,
	0x0d, 0x1e, 0xa3, 0x0e, 0x2c, 0x7e, 0xc3, 0x9f, 0xeb, 0xc0, 0x8a, 0xaf, 0xfb, 0x2b, 0x9a, 0x3b,
	0x24, 0x57, 0x84, 0x1c, 0xe5, 0x1f, 0xe6, 0xe3, 0x32, 0x1b, 0x3f, 0xd6, 0xaf, 0x68, 0xe5, 0x01,
	0xb9, 0x2a, 0x5a, 0xd1, 0x3f, 0x79, 0xc6, 0x4d, 0x5d, 0xf9, 0x29, 0x74, 0xa5, 0xa1, 0xba, 0x39,
	0xf6, 0xe3, 0x79, 0x6b, 0x5b, 0xbe, 0x76, 0xd9, 0xf7, 0xf5, 0x15, 0x1d, 0x1c, 0xa1, 0xdd, 0xd7,
	0xff, 0x0f, 0x90, 0x8f, 0xd5, 0xf8, 0xdf, 0x80, 0x8a, 0xa6, 0x3e, 0x2d, 0xfd, 0xa5, 0x42, 0xfc,
	0x36, 0x25, 0xb7, 0x79, 0x95, 0xbf, 0x54, 0xa9, 0x68, 0xf2, 0x2e, 0x8e, 0x4e, 0xff, 0xb3, 0x09,
	0xae, 0x89, 0xf1, 0x6f, 0x27, 0xe3, 0x47, 0x86, 0xb7, 0xfc, 0xf4, 0x1e, 0xe4, 0x9d, 0xef, 0xa5,
	0x9e, 0xcf, 0x5e, 0x7d, 0x64, 0xc7, 0xd9, 0x6f, 0xf0, 0x50, 0x30, 0xfc, 0x67, 0xf8, 0xa7, 0x04,
	0xfc, 0x6b, 0x92, 0xd6, 0xaa, 0x96, 0x34, 0x57, 0xd9, 0x9c, 0xa5, 0x4a, 0x8d, 0x48, 0x8a, 0x1f,
	0xd7, 0xce, 0x86, 0xb1, 0x4c, 0x34, 0xf7, 0x48, 0x59, 0x8a, 0xbd, 0x5e, 0x8f, 0x8e, 0x98, 0x5c,
	0x0a, 0xfd, 0x35, 0x3d, 0x47, 0xb2, 0x7d, 0xd3, 0x54, 0x98, 0xb7, 0xfa, 0x99, 0xa2, 0x0e, 0x0a,
	0xf9, 0x86, 0xd6, 0x9b, 0x85, 0x3d, 0x67, 0xca, 0x46, 0xac, 0x98, 0xbb, 0x83, 0x7a, 0x01, 0x5e,
	0xbc, 0x7f, 0xf8, 0x8b, 0xfb, 0xaf, 0xa3, 0xcd, 0x4f, 0x51, 0x35, 0xc8, 0xca, 0x77, 0x43, 0x0f,
	0xff, 0x63, 0xf1, 0x15, 0x9a, 0xbc, 0x8f, 0xb7, 0xda, 0xca, 0x05, 0x42, 0xbe, 0x3e, 0xe5, 0x1b,
	0x98, 0x7c, 0x7d, 0x4c, 0x37, 0x0e, 0x81, 0x9a, 0xb9, 0xa9, 0xdf, 0x69, 0x49, 0xc5, 0x33, 0xd1,
	0x48, 0x6f, 0xeb, 0x95, 0x2a, 0x2e, 0xf4, 0x1e, 0xa3, 0x34, 0x94, 0x7f, 0xd9, 0x98, 0xef, 0x73,
	0xe3, 0x8f, 0x30, 0xdb, 0x63, 0x8b, 0x93, 0xdd, 0x18, 0x39, 0xb2, 0x9f, 0x26, 0x99, 0xba, 0x14,
	0x7e, 0x6f, 0x39, 0xe8, 0x9c, 0xef, 0xb4, 0xca, 0x70, 0x78, 0xee, 0xf7, 0x56, 0xc7, 0xac, 0x77,
	0x03, 0xb4, 0xa2, 0x1d, 0x11, 0xc7, 0x94, 0xfd, 0x7e, 0x46, 0xae, 0x55, 0xfc, 0x35, 0xca, 0x82,
	0x56, 0xc7, 0xff, 0x52, 0xaa, 0xbd, 0xbe, 0x83, 0x7f, 0x0b, 0xdd, 0x91, 0x7f, 0x0b, 0xdd, 0xb9,
	0x3b, 0x1c, 0xb1, 0x8b, 0xdd, 0xa7, 0xe2, 0x5f, 0x08, 0xf0, 0xb3, 0xbb, 0xfc, 0x4c, 0x64, 0xf8,
	0x09, 0x1e, 0xda, 0xf1, 0xea, 0x7f, 0x02, 0xa2, 0x1d, 0x1f, 0xf3, 0xf7, 0xbc, 0xd3, 0x69, 0xe8,
	0xfb, 0xc7, 0xff, 0x37, 0x00, 0x00, 0xff, 0xff, 0xd5, 0xf6, 0xf3, 0xb8, 0xd4, 0x54, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// JddjProductServiceClient is the client API for JddjProductService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type JddjProductServiceClient interface {
	//新增分类
	JddjAddShopCategory(ctx context.Context, in *JddjAddShopCategoryRequest, opts ...grpc.CallOption) (*JddjAddShopCategoryResponse, error)
	//新版新增商品信息接口
	AddSku(ctx context.Context, in *AddSkuRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	//更新京东商品信息
	JddjUpdateGoodsList(ctx context.Context, in *JddjUpdateGoodsListRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	//修改分类
	JddjUpdateShopCategory(ctx context.Context, in *JddjUpdateShopCategoryRequest, opts ...grpc.CallOption) (*JddjUpdateShopCategoryResponse, error)
	//删除分类
	JddjDeleteShopCategory(ctx context.Context, in *JddjDeleteShopCategoryRequest, opts ...grpc.CallOption) (*JddjDeleteShopCategoryResponse, error)
	//排序分类
	JddjSortShopCategory(ctx context.Context, in *JddjSortShopCategoryRequest, opts ...grpc.CallOption) (*JddjSortShopCategoryResponse, error)
	//获取京东到家后台商品类目
	QueryChildCategoriesForOP(ctx context.Context, in *QueryChildCategoriesForOPRequest, opts ...grpc.CallOption) (*QueryChildCategoriesForOPResponse, error)
	//获取京东到家品牌信息
	QueryPageBrandInfo(ctx context.Context, in *QueryPageBrandInfoRequest, opts ...grpc.CallOption) (*QueryPageBrandInfoResponse, error)
	//根据商家商品编码和商家门店编码批量修改现货库存接口
	BatchUpdateCurrentQtys(ctx context.Context, in *BatchUpdateCurrentQtysRequest, opts ...grpc.CallOption) (*BatchUpdateCurrentQtysResponse, error)
	//新增分类
	AddShopCategory(ctx context.Context, in *AddShopCategoryRequest, opts ...grpc.CallOption) (*AddShopCategoryResponse, error)
	//根据商家商品编码和商家门店编码批量修改门店价格接口
	UpdateStationPrice(ctx context.Context, in *UpdateStationPriceRequest, opts ...grpc.CallOption) (*UpdateStationPriceResponse, error)
	//根据商家商品编码和门店编码批量修改门店商品可售状态接口
	BatchUpdateVendibility(ctx context.Context, in *BatchUpdateVendibilityRequest, opts ...grpc.CallOption) (*BatchUpdateVendibilityResponse, error)
	//根据商家商品编码和门店编码更新现货库存接口
	UpdateStock(ctx context.Context, in *UpdateStockRequest, opts ...grpc.CallOption) (*BatchUpdateVendibilityResponse, error)
	//根据商家商品编码和商家门店编码修改门店价格接口
	UpdatePriceOne(ctx context.Context, in *UpdatePriceOneRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 获取jddj的分类
	GetJddjCategoryList(ctx context.Context, in *QueryChildCategoriesForOPRequest, opts ...grpc.CallOption) (*QueryChildCategoriesForOPResponse, error)
	//根据商家商品编码和门店编码更新现货库存接口单个的
	UpdateOneStock(ctx context.Context, in *UpdateStockOneRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	//查询jddj分类信息
	JddjGetShopCategory(ctx context.Context, in *JddjGetShopCategoryRequest, opts ...grpc.CallOption) (*JddjGetShopCategoryResponse, error)
	// 查询jd到家的品牌库商品信息
	QuerySkuInfos(ctx context.Context, in *QuerySkuInfosRequest, opts ...grpc.CallOption) (*QuerySkuInfosResponse, error)
}

type jddjProductServiceClient struct {
	cc *grpc.ClientConn
}

func NewJddjProductServiceClient(cc *grpc.ClientConn) JddjProductServiceClient {
	return &jddjProductServiceClient{cc}
}

func (c *jddjProductServiceClient) JddjAddShopCategory(ctx context.Context, in *JddjAddShopCategoryRequest, opts ...grpc.CallOption) (*JddjAddShopCategoryResponse, error) {
	out := new(JddjAddShopCategoryResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/JddjAddShopCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) AddSku(ctx context.Context, in *AddSkuRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/AddSku", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) JddjUpdateGoodsList(ctx context.Context, in *JddjUpdateGoodsListRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/JddjUpdateGoodsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) JddjUpdateShopCategory(ctx context.Context, in *JddjUpdateShopCategoryRequest, opts ...grpc.CallOption) (*JddjUpdateShopCategoryResponse, error) {
	out := new(JddjUpdateShopCategoryResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/JddjUpdateShopCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) JddjDeleteShopCategory(ctx context.Context, in *JddjDeleteShopCategoryRequest, opts ...grpc.CallOption) (*JddjDeleteShopCategoryResponse, error) {
	out := new(JddjDeleteShopCategoryResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/JddjDeleteShopCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) JddjSortShopCategory(ctx context.Context, in *JddjSortShopCategoryRequest, opts ...grpc.CallOption) (*JddjSortShopCategoryResponse, error) {
	out := new(JddjSortShopCategoryResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/JddjSortShopCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) QueryChildCategoriesForOP(ctx context.Context, in *QueryChildCategoriesForOPRequest, opts ...grpc.CallOption) (*QueryChildCategoriesForOPResponse, error) {
	out := new(QueryChildCategoriesForOPResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/QueryChildCategoriesForOP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) QueryPageBrandInfo(ctx context.Context, in *QueryPageBrandInfoRequest, opts ...grpc.CallOption) (*QueryPageBrandInfoResponse, error) {
	out := new(QueryPageBrandInfoResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/QueryPageBrandInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) BatchUpdateCurrentQtys(ctx context.Context, in *BatchUpdateCurrentQtysRequest, opts ...grpc.CallOption) (*BatchUpdateCurrentQtysResponse, error) {
	out := new(BatchUpdateCurrentQtysResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/BatchUpdateCurrentQtys", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) AddShopCategory(ctx context.Context, in *AddShopCategoryRequest, opts ...grpc.CallOption) (*AddShopCategoryResponse, error) {
	out := new(AddShopCategoryResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/AddShopCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) UpdateStationPrice(ctx context.Context, in *UpdateStationPriceRequest, opts ...grpc.CallOption) (*UpdateStationPriceResponse, error) {
	out := new(UpdateStationPriceResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/UpdateStationPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) BatchUpdateVendibility(ctx context.Context, in *BatchUpdateVendibilityRequest, opts ...grpc.CallOption) (*BatchUpdateVendibilityResponse, error) {
	out := new(BatchUpdateVendibilityResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/BatchUpdateVendibility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) UpdateStock(ctx context.Context, in *UpdateStockRequest, opts ...grpc.CallOption) (*BatchUpdateVendibilityResponse, error) {
	out := new(BatchUpdateVendibilityResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/UpdateStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) UpdatePriceOne(ctx context.Context, in *UpdatePriceOneRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/UpdatePriceOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) GetJddjCategoryList(ctx context.Context, in *QueryChildCategoriesForOPRequest, opts ...grpc.CallOption) (*QueryChildCategoriesForOPResponse, error) {
	out := new(QueryChildCategoriesForOPResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/GetJddjCategoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) UpdateOneStock(ctx context.Context, in *UpdateStockOneRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/UpdateOneStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) JddjGetShopCategory(ctx context.Context, in *JddjGetShopCategoryRequest, opts ...grpc.CallOption) (*JddjGetShopCategoryResponse, error) {
	out := new(JddjGetShopCategoryResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/JddjGetShopCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjProductServiceClient) QuerySkuInfos(ctx context.Context, in *QuerySkuInfosRequest, opts ...grpc.CallOption) (*QuerySkuInfosResponse, error) {
	out := new(QuerySkuInfosResponse)
	err := c.cc.Invoke(ctx, "/et.JddjProductService/QuerySkuInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JddjProductServiceServer is the server API for JddjProductService service.
type JddjProductServiceServer interface {
	//新增分类
	JddjAddShopCategory(context.Context, *JddjAddShopCategoryRequest) (*JddjAddShopCategoryResponse, error)
	//新版新增商品信息接口
	AddSku(context.Context, *AddSkuRequest) (*JddjBaseResponse, error)
	//更新京东商品信息
	JddjUpdateGoodsList(context.Context, *JddjUpdateGoodsListRequest) (*JddjBaseResponse, error)
	//修改分类
	JddjUpdateShopCategory(context.Context, *JddjUpdateShopCategoryRequest) (*JddjUpdateShopCategoryResponse, error)
	//删除分类
	JddjDeleteShopCategory(context.Context, *JddjDeleteShopCategoryRequest) (*JddjDeleteShopCategoryResponse, error)
	//排序分类
	JddjSortShopCategory(context.Context, *JddjSortShopCategoryRequest) (*JddjSortShopCategoryResponse, error)
	//获取京东到家后台商品类目
	QueryChildCategoriesForOP(context.Context, *QueryChildCategoriesForOPRequest) (*QueryChildCategoriesForOPResponse, error)
	//获取京东到家品牌信息
	QueryPageBrandInfo(context.Context, *QueryPageBrandInfoRequest) (*QueryPageBrandInfoResponse, error)
	//根据商家商品编码和商家门店编码批量修改现货库存接口
	BatchUpdateCurrentQtys(context.Context, *BatchUpdateCurrentQtysRequest) (*BatchUpdateCurrentQtysResponse, error)
	//新增分类
	AddShopCategory(context.Context, *AddShopCategoryRequest) (*AddShopCategoryResponse, error)
	//根据商家商品编码和商家门店编码批量修改门店价格接口
	UpdateStationPrice(context.Context, *UpdateStationPriceRequest) (*UpdateStationPriceResponse, error)
	//根据商家商品编码和门店编码批量修改门店商品可售状态接口
	BatchUpdateVendibility(context.Context, *BatchUpdateVendibilityRequest) (*BatchUpdateVendibilityResponse, error)
	//根据商家商品编码和门店编码更新现货库存接口
	UpdateStock(context.Context, *UpdateStockRequest) (*BatchUpdateVendibilityResponse, error)
	//根据商家商品编码和商家门店编码修改门店价格接口
	UpdatePriceOne(context.Context, *UpdatePriceOneRequest) (*JddjBaseResponse, error)
	// 获取jddj的分类
	GetJddjCategoryList(context.Context, *QueryChildCategoriesForOPRequest) (*QueryChildCategoriesForOPResponse, error)
	//根据商家商品编码和门店编码更新现货库存接口单个的
	UpdateOneStock(context.Context, *UpdateStockOneRequest) (*JddjBaseResponse, error)
	//查询jddj分类信息
	JddjGetShopCategory(context.Context, *JddjGetShopCategoryRequest) (*JddjGetShopCategoryResponse, error)
	// 查询jd到家的品牌库商品信息
	QuerySkuInfos(context.Context, *QuerySkuInfosRequest) (*QuerySkuInfosResponse, error)
}

// UnimplementedJddjProductServiceServer can be embedded to have forward compatible implementations.
type UnimplementedJddjProductServiceServer struct {
}

func (*UnimplementedJddjProductServiceServer) JddjAddShopCategory(ctx context.Context, req *JddjAddShopCategoryRequest) (*JddjAddShopCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjAddShopCategory not implemented")
}
func (*UnimplementedJddjProductServiceServer) AddSku(ctx context.Context, req *AddSkuRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddSku not implemented")
}
func (*UnimplementedJddjProductServiceServer) JddjUpdateGoodsList(ctx context.Context, req *JddjUpdateGoodsListRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjUpdateGoodsList not implemented")
}
func (*UnimplementedJddjProductServiceServer) JddjUpdateShopCategory(ctx context.Context, req *JddjUpdateShopCategoryRequest) (*JddjUpdateShopCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjUpdateShopCategory not implemented")
}
func (*UnimplementedJddjProductServiceServer) JddjDeleteShopCategory(ctx context.Context, req *JddjDeleteShopCategoryRequest) (*JddjDeleteShopCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjDeleteShopCategory not implemented")
}
func (*UnimplementedJddjProductServiceServer) JddjSortShopCategory(ctx context.Context, req *JddjSortShopCategoryRequest) (*JddjSortShopCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjSortShopCategory not implemented")
}
func (*UnimplementedJddjProductServiceServer) QueryChildCategoriesForOP(ctx context.Context, req *QueryChildCategoriesForOPRequest) (*QueryChildCategoriesForOPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryChildCategoriesForOP not implemented")
}
func (*UnimplementedJddjProductServiceServer) QueryPageBrandInfo(ctx context.Context, req *QueryPageBrandInfoRequest) (*QueryPageBrandInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPageBrandInfo not implemented")
}
func (*UnimplementedJddjProductServiceServer) BatchUpdateCurrentQtys(ctx context.Context, req *BatchUpdateCurrentQtysRequest) (*BatchUpdateCurrentQtysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateCurrentQtys not implemented")
}
func (*UnimplementedJddjProductServiceServer) AddShopCategory(ctx context.Context, req *AddShopCategoryRequest) (*AddShopCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddShopCategory not implemented")
}
func (*UnimplementedJddjProductServiceServer) UpdateStationPrice(ctx context.Context, req *UpdateStationPriceRequest) (*UpdateStationPriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStationPrice not implemented")
}
func (*UnimplementedJddjProductServiceServer) BatchUpdateVendibility(ctx context.Context, req *BatchUpdateVendibilityRequest) (*BatchUpdateVendibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateVendibility not implemented")
}
func (*UnimplementedJddjProductServiceServer) UpdateStock(ctx context.Context, req *UpdateStockRequest) (*BatchUpdateVendibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStock not implemented")
}
func (*UnimplementedJddjProductServiceServer) UpdatePriceOne(ctx context.Context, req *UpdatePriceOneRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePriceOne not implemented")
}
func (*UnimplementedJddjProductServiceServer) GetJddjCategoryList(ctx context.Context, req *QueryChildCategoriesForOPRequest) (*QueryChildCategoriesForOPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJddjCategoryList not implemented")
}
func (*UnimplementedJddjProductServiceServer) UpdateOneStock(ctx context.Context, req *UpdateStockOneRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOneStock not implemented")
}
func (*UnimplementedJddjProductServiceServer) JddjGetShopCategory(ctx context.Context, req *JddjGetShopCategoryRequest) (*JddjGetShopCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjGetShopCategory not implemented")
}
func (*UnimplementedJddjProductServiceServer) QuerySkuInfos(ctx context.Context, req *QuerySkuInfosRequest) (*QuerySkuInfosResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySkuInfos not implemented")
}

func RegisterJddjProductServiceServer(s *grpc.Server, srv JddjProductServiceServer) {
	s.RegisterService(&_JddjProductService_serviceDesc, srv)
}

func _JddjProductService_JddjAddShopCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjAddShopCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).JddjAddShopCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/JddjAddShopCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).JddjAddShopCategory(ctx, req.(*JddjAddShopCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_AddSku_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSkuRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).AddSku(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/AddSku",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).AddSku(ctx, req.(*AddSkuRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_JddjUpdateGoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjUpdateGoodsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).JddjUpdateGoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/JddjUpdateGoodsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).JddjUpdateGoodsList(ctx, req.(*JddjUpdateGoodsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_JddjUpdateShopCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjUpdateShopCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).JddjUpdateShopCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/JddjUpdateShopCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).JddjUpdateShopCategory(ctx, req.(*JddjUpdateShopCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_JddjDeleteShopCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjDeleteShopCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).JddjDeleteShopCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/JddjDeleteShopCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).JddjDeleteShopCategory(ctx, req.(*JddjDeleteShopCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_JddjSortShopCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjSortShopCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).JddjSortShopCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/JddjSortShopCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).JddjSortShopCategory(ctx, req.(*JddjSortShopCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_QueryChildCategoriesForOP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryChildCategoriesForOPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).QueryChildCategoriesForOP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/QueryChildCategoriesForOP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).QueryChildCategoriesForOP(ctx, req.(*QueryChildCategoriesForOPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_QueryPageBrandInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPageBrandInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).QueryPageBrandInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/QueryPageBrandInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).QueryPageBrandInfo(ctx, req.(*QueryPageBrandInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_BatchUpdateCurrentQtys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateCurrentQtysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).BatchUpdateCurrentQtys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/BatchUpdateCurrentQtys",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).BatchUpdateCurrentQtys(ctx, req.(*BatchUpdateCurrentQtysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_AddShopCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddShopCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).AddShopCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/AddShopCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).AddShopCategory(ctx, req.(*AddShopCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_UpdateStationPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStationPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).UpdateStationPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/UpdateStationPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).UpdateStationPrice(ctx, req.(*UpdateStationPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_BatchUpdateVendibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateVendibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).BatchUpdateVendibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/BatchUpdateVendibility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).BatchUpdateVendibility(ctx, req.(*BatchUpdateVendibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_UpdateStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).UpdateStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/UpdateStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).UpdateStock(ctx, req.(*UpdateStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_UpdatePriceOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePriceOneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).UpdatePriceOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/UpdatePriceOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).UpdatePriceOne(ctx, req.(*UpdatePriceOneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_GetJddjCategoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryChildCategoriesForOPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).GetJddjCategoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/GetJddjCategoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).GetJddjCategoryList(ctx, req.(*QueryChildCategoriesForOPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_UpdateOneStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStockOneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).UpdateOneStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/UpdateOneStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).UpdateOneStock(ctx, req.(*UpdateStockOneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_JddjGetShopCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjGetShopCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).JddjGetShopCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/JddjGetShopCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).JddjGetShopCategory(ctx, req.(*JddjGetShopCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjProductService_QuerySkuInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySkuInfosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjProductServiceServer).QuerySkuInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjProductService/QuerySkuInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjProductServiceServer).QuerySkuInfos(ctx, req.(*QuerySkuInfosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _JddjProductService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.JddjProductService",
	HandlerType: (*JddjProductServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "JddjAddShopCategory",
			Handler:    _JddjProductService_JddjAddShopCategory_Handler,
		},
		{
			MethodName: "AddSku",
			Handler:    _JddjProductService_AddSku_Handler,
		},
		{
			MethodName: "JddjUpdateGoodsList",
			Handler:    _JddjProductService_JddjUpdateGoodsList_Handler,
		},
		{
			MethodName: "JddjUpdateShopCategory",
			Handler:    _JddjProductService_JddjUpdateShopCategory_Handler,
		},
		{
			MethodName: "JddjDeleteShopCategory",
			Handler:    _JddjProductService_JddjDeleteShopCategory_Handler,
		},
		{
			MethodName: "JddjSortShopCategory",
			Handler:    _JddjProductService_JddjSortShopCategory_Handler,
		},
		{
			MethodName: "QueryChildCategoriesForOP",
			Handler:    _JddjProductService_QueryChildCategoriesForOP_Handler,
		},
		{
			MethodName: "QueryPageBrandInfo",
			Handler:    _JddjProductService_QueryPageBrandInfo_Handler,
		},
		{
			MethodName: "BatchUpdateCurrentQtys",
			Handler:    _JddjProductService_BatchUpdateCurrentQtys_Handler,
		},
		{
			MethodName: "AddShopCategory",
			Handler:    _JddjProductService_AddShopCategory_Handler,
		},
		{
			MethodName: "UpdateStationPrice",
			Handler:    _JddjProductService_UpdateStationPrice_Handler,
		},
		{
			MethodName: "BatchUpdateVendibility",
			Handler:    _JddjProductService_BatchUpdateVendibility_Handler,
		},
		{
			MethodName: "UpdateStock",
			Handler:    _JddjProductService_UpdateStock_Handler,
		},
		{
			MethodName: "UpdatePriceOne",
			Handler:    _JddjProductService_UpdatePriceOne_Handler,
		},
		{
			MethodName: "GetJddjCategoryList",
			Handler:    _JddjProductService_GetJddjCategoryList_Handler,
		},
		{
			MethodName: "UpdateOneStock",
			Handler:    _JddjProductService_UpdateOneStock_Handler,
		},
		{
			MethodName: "JddjGetShopCategory",
			Handler:    _JddjProductService_JddjGetShopCategory_Handler,
		},
		{
			MethodName: "QuerySkuInfos",
			Handler:    _JddjProductService_QuerySkuInfos_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/externalJddj.proto",
}

// JddjOrderServiceClient is the client API for JddjOrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type JddjOrderServiceClient interface {
	// 订单取消并退款
	JddjOrderCancelAndRefund(ctx context.Context, in *JddjOrderCancelRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 订单自提码核验
	JddjCheckSelfPickCode(ctx context.Context, in *JddjCheckSelfPickCodeRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 拣货完成且顾客自提接口
	JddjOrderSelfMention(ctx context.Context, in *JddjOrderSelfMentionRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 查询售后单详情接口
	GetJddjAfsService(ctx context.Context, in *JddjAfsServiceRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 查询订单可售后商品金额接口
	GetJddjOrderCalcMoney(ctx context.Context, in *JddjOrderCalcMoneyRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 商家自主发起售后接口
	JddjMerchantInitiateAfterSale(ctx context.Context, in *JddjMerchantInitiateAfterSaleRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 售后单确认收货接口
	JddjConfirmReceipt(ctx context.Context, in *JddjConfirmReceiptRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 商家审核用户取消申请（订单未完成取消订单）
	JddjOrderCancelOperate(ctx context.Context, in *JddjOrderCancelOperateRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 申请售后单审核接口（订单完成发起的售后单）
	JddjAfsOpenApprove(ctx context.Context, in *AfsOpenApproveRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 商家审核配送员取货失败接口
	JddjReceiveFailedAudit(ctx context.Context, in *JddjOrderCancelOperateRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 根据订单号查询所有的售后单信息接口
	JddjGetAfsSeriveOrderList(ctx context.Context, in *JddjOrderDetailRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	//获取京东到家订单详情
	GetJddjOrderDetail(ctx context.Context, in *JddjOrderDetailRequest, opts ...grpc.CallOption) (*JddjOrderDetailResponse, error)
	//商家确认接单接口
	JddjOrderAcceptOperate(ctx context.Context, in *JddjOrderConfirmRequest, opts ...grpc.CallOption) (*JddjOrderConfirmlResponse, error)
	// 拣货完成且商家自送接口
	JddjOrderSerllerDelivery(ctx context.Context, in *JddjOrderSerllerDeliveryRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 拣货完成且众包配送接口
	JddjOrderJDZBDelivery(ctx context.Context, in *JddjOrderSerllerDeliveryRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 订单妥投接口
	JddjDeliveryEndOrder(ctx context.Context, in *JddjOrderSerllerDeliveryRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error)
	// 订单调整接口
	JddjAdjustOrder(ctx context.Context, in *JddjAdjustOrderRequest, opts ...grpc.CallOption) (*JddjAdjustOrderResponse, error)
	JddjOrderShoudSettlementService(ctx context.Context, in *JddjOrderSerllerDeliveryRequest, opts ...grpc.CallOption) (*JddjOrderShoudSettlementResponse, error)
	// 分页查询对账单接口
	JddjGetBalanceBillList(ctx context.Context, in *JddjGetBalanceBillListReq, opts ...grpc.CallOption) (*JddjGetBalanceBillListRes, error)
}

type jddjOrderServiceClient struct {
	cc *grpc.ClientConn
}

func NewJddjOrderServiceClient(cc *grpc.ClientConn) JddjOrderServiceClient {
	return &jddjOrderServiceClient{cc}
}

func (c *jddjOrderServiceClient) JddjOrderCancelAndRefund(ctx context.Context, in *JddjOrderCancelRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjOrderCancelAndRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjCheckSelfPickCode(ctx context.Context, in *JddjCheckSelfPickCodeRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjCheckSelfPickCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjOrderSelfMention(ctx context.Context, in *JddjOrderSelfMentionRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjOrderSelfMention", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) GetJddjAfsService(ctx context.Context, in *JddjAfsServiceRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/GetJddjAfsService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) GetJddjOrderCalcMoney(ctx context.Context, in *JddjOrderCalcMoneyRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/GetJddjOrderCalcMoney", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjMerchantInitiateAfterSale(ctx context.Context, in *JddjMerchantInitiateAfterSaleRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjMerchantInitiateAfterSale", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjConfirmReceipt(ctx context.Context, in *JddjConfirmReceiptRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjConfirmReceipt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjOrderCancelOperate(ctx context.Context, in *JddjOrderCancelOperateRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjOrderCancelOperate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjAfsOpenApprove(ctx context.Context, in *AfsOpenApproveRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjAfsOpenApprove", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjReceiveFailedAudit(ctx context.Context, in *JddjOrderCancelOperateRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjReceiveFailedAudit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjGetAfsSeriveOrderList(ctx context.Context, in *JddjOrderDetailRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjGetAfsSeriveOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) GetJddjOrderDetail(ctx context.Context, in *JddjOrderDetailRequest, opts ...grpc.CallOption) (*JddjOrderDetailResponse, error) {
	out := new(JddjOrderDetailResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/GetJddjOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjOrderAcceptOperate(ctx context.Context, in *JddjOrderConfirmRequest, opts ...grpc.CallOption) (*JddjOrderConfirmlResponse, error) {
	out := new(JddjOrderConfirmlResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjOrderAcceptOperate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjOrderSerllerDelivery(ctx context.Context, in *JddjOrderSerllerDeliveryRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjOrderSerllerDelivery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjOrderJDZBDelivery(ctx context.Context, in *JddjOrderSerllerDeliveryRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjOrderJDZBDelivery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjDeliveryEndOrder(ctx context.Context, in *JddjOrderSerllerDeliveryRequest, opts ...grpc.CallOption) (*JddjBaseResponse, error) {
	out := new(JddjBaseResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjDeliveryEndOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjAdjustOrder(ctx context.Context, in *JddjAdjustOrderRequest, opts ...grpc.CallOption) (*JddjAdjustOrderResponse, error) {
	out := new(JddjAdjustOrderResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjAdjustOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjOrderShoudSettlementService(ctx context.Context, in *JddjOrderSerllerDeliveryRequest, opts ...grpc.CallOption) (*JddjOrderShoudSettlementResponse, error) {
	out := new(JddjOrderShoudSettlementResponse)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjOrderShoudSettlementService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jddjOrderServiceClient) JddjGetBalanceBillList(ctx context.Context, in *JddjGetBalanceBillListReq, opts ...grpc.CallOption) (*JddjGetBalanceBillListRes, error) {
	out := new(JddjGetBalanceBillListRes)
	err := c.cc.Invoke(ctx, "/et.JddjOrderService/JddjGetBalanceBillList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JddjOrderServiceServer is the server API for JddjOrderService service.
type JddjOrderServiceServer interface {
	// 订单取消并退款
	JddjOrderCancelAndRefund(context.Context, *JddjOrderCancelRequest) (*JddjBaseResponse, error)
	// 订单自提码核验
	JddjCheckSelfPickCode(context.Context, *JddjCheckSelfPickCodeRequest) (*JddjBaseResponse, error)
	// 拣货完成且顾客自提接口
	JddjOrderSelfMention(context.Context, *JddjOrderSelfMentionRequest) (*JddjBaseResponse, error)
	// 查询售后单详情接口
	GetJddjAfsService(context.Context, *JddjAfsServiceRequest) (*JddjBaseResponse, error)
	// 查询订单可售后商品金额接口
	GetJddjOrderCalcMoney(context.Context, *JddjOrderCalcMoneyRequest) (*JddjBaseResponse, error)
	// 商家自主发起售后接口
	JddjMerchantInitiateAfterSale(context.Context, *JddjMerchantInitiateAfterSaleRequest) (*JddjBaseResponse, error)
	// 售后单确认收货接口
	JddjConfirmReceipt(context.Context, *JddjConfirmReceiptRequest) (*JddjBaseResponse, error)
	// 商家审核用户取消申请（订单未完成取消订单）
	JddjOrderCancelOperate(context.Context, *JddjOrderCancelOperateRequest) (*JddjBaseResponse, error)
	// 申请售后单审核接口（订单完成发起的售后单）
	JddjAfsOpenApprove(context.Context, *AfsOpenApproveRequest) (*JddjBaseResponse, error)
	// 商家审核配送员取货失败接口
	JddjReceiveFailedAudit(context.Context, *JddjOrderCancelOperateRequest) (*JddjBaseResponse, error)
	// 根据订单号查询所有的售后单信息接口
	JddjGetAfsSeriveOrderList(context.Context, *JddjOrderDetailRequest) (*JddjBaseResponse, error)
	//获取京东到家订单详情
	GetJddjOrderDetail(context.Context, *JddjOrderDetailRequest) (*JddjOrderDetailResponse, error)
	//商家确认接单接口
	JddjOrderAcceptOperate(context.Context, *JddjOrderConfirmRequest) (*JddjOrderConfirmlResponse, error)
	// 拣货完成且商家自送接口
	JddjOrderSerllerDelivery(context.Context, *JddjOrderSerllerDeliveryRequest) (*JddjBaseResponse, error)
	// 拣货完成且众包配送接口
	JddjOrderJDZBDelivery(context.Context, *JddjOrderSerllerDeliveryRequest) (*JddjBaseResponse, error)
	// 订单妥投接口
	JddjDeliveryEndOrder(context.Context, *JddjOrderSerllerDeliveryRequest) (*JddjBaseResponse, error)
	// 订单调整接口
	JddjAdjustOrder(context.Context, *JddjAdjustOrderRequest) (*JddjAdjustOrderResponse, error)
	JddjOrderShoudSettlementService(context.Context, *JddjOrderSerllerDeliveryRequest) (*JddjOrderShoudSettlementResponse, error)
	// 分页查询对账单接口
	JddjGetBalanceBillList(context.Context, *JddjGetBalanceBillListReq) (*JddjGetBalanceBillListRes, error)
}

// UnimplementedJddjOrderServiceServer can be embedded to have forward compatible implementations.
type UnimplementedJddjOrderServiceServer struct {
}

func (*UnimplementedJddjOrderServiceServer) JddjOrderCancelAndRefund(ctx context.Context, req *JddjOrderCancelRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjOrderCancelAndRefund not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjCheckSelfPickCode(ctx context.Context, req *JddjCheckSelfPickCodeRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjCheckSelfPickCode not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjOrderSelfMention(ctx context.Context, req *JddjOrderSelfMentionRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjOrderSelfMention not implemented")
}
func (*UnimplementedJddjOrderServiceServer) GetJddjAfsService(ctx context.Context, req *JddjAfsServiceRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJddjAfsService not implemented")
}
func (*UnimplementedJddjOrderServiceServer) GetJddjOrderCalcMoney(ctx context.Context, req *JddjOrderCalcMoneyRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJddjOrderCalcMoney not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjMerchantInitiateAfterSale(ctx context.Context, req *JddjMerchantInitiateAfterSaleRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjMerchantInitiateAfterSale not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjConfirmReceipt(ctx context.Context, req *JddjConfirmReceiptRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjConfirmReceipt not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjOrderCancelOperate(ctx context.Context, req *JddjOrderCancelOperateRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjOrderCancelOperate not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjAfsOpenApprove(ctx context.Context, req *AfsOpenApproveRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjAfsOpenApprove not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjReceiveFailedAudit(ctx context.Context, req *JddjOrderCancelOperateRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjReceiveFailedAudit not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjGetAfsSeriveOrderList(ctx context.Context, req *JddjOrderDetailRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjGetAfsSeriveOrderList not implemented")
}
func (*UnimplementedJddjOrderServiceServer) GetJddjOrderDetail(ctx context.Context, req *JddjOrderDetailRequest) (*JddjOrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJddjOrderDetail not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjOrderAcceptOperate(ctx context.Context, req *JddjOrderConfirmRequest) (*JddjOrderConfirmlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjOrderAcceptOperate not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjOrderSerllerDelivery(ctx context.Context, req *JddjOrderSerllerDeliveryRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjOrderSerllerDelivery not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjOrderJDZBDelivery(ctx context.Context, req *JddjOrderSerllerDeliveryRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjOrderJDZBDelivery not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjDeliveryEndOrder(ctx context.Context, req *JddjOrderSerllerDeliveryRequest) (*JddjBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjDeliveryEndOrder not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjAdjustOrder(ctx context.Context, req *JddjAdjustOrderRequest) (*JddjAdjustOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjAdjustOrder not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjOrderShoudSettlementService(ctx context.Context, req *JddjOrderSerllerDeliveryRequest) (*JddjOrderShoudSettlementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjOrderShoudSettlementService not implemented")
}
func (*UnimplementedJddjOrderServiceServer) JddjGetBalanceBillList(ctx context.Context, req *JddjGetBalanceBillListReq) (*JddjGetBalanceBillListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjGetBalanceBillList not implemented")
}

func RegisterJddjOrderServiceServer(s *grpc.Server, srv JddjOrderServiceServer) {
	s.RegisterService(&_JddjOrderService_serviceDesc, srv)
}

func _JddjOrderService_JddjOrderCancelAndRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderCancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjOrderCancelAndRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjOrderCancelAndRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjOrderCancelAndRefund(ctx, req.(*JddjOrderCancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjCheckSelfPickCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjCheckSelfPickCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjCheckSelfPickCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjCheckSelfPickCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjCheckSelfPickCode(ctx, req.(*JddjCheckSelfPickCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjOrderSelfMention_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderSelfMentionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjOrderSelfMention(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjOrderSelfMention",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjOrderSelfMention(ctx, req.(*JddjOrderSelfMentionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_GetJddjAfsService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjAfsServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).GetJddjAfsService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/GetJddjAfsService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).GetJddjAfsService(ctx, req.(*JddjAfsServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_GetJddjOrderCalcMoney_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderCalcMoneyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).GetJddjOrderCalcMoney(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/GetJddjOrderCalcMoney",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).GetJddjOrderCalcMoney(ctx, req.(*JddjOrderCalcMoneyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjMerchantInitiateAfterSale_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjMerchantInitiateAfterSaleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjMerchantInitiateAfterSale(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjMerchantInitiateAfterSale",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjMerchantInitiateAfterSale(ctx, req.(*JddjMerchantInitiateAfterSaleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjConfirmReceipt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjConfirmReceiptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjConfirmReceipt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjConfirmReceipt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjConfirmReceipt(ctx, req.(*JddjConfirmReceiptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjOrderCancelOperate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderCancelOperateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjOrderCancelOperate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjOrderCancelOperate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjOrderCancelOperate(ctx, req.(*JddjOrderCancelOperateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjAfsOpenApprove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AfsOpenApproveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjAfsOpenApprove(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjAfsOpenApprove",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjAfsOpenApprove(ctx, req.(*AfsOpenApproveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjReceiveFailedAudit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderCancelOperateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjReceiveFailedAudit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjReceiveFailedAudit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjReceiveFailedAudit(ctx, req.(*JddjOrderCancelOperateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjGetAfsSeriveOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjGetAfsSeriveOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjGetAfsSeriveOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjGetAfsSeriveOrderList(ctx, req.(*JddjOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_GetJddjOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).GetJddjOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/GetJddjOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).GetJddjOrderDetail(ctx, req.(*JddjOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjOrderAcceptOperate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderConfirmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjOrderAcceptOperate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjOrderAcceptOperate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjOrderAcceptOperate(ctx, req.(*JddjOrderConfirmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjOrderSerllerDelivery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderSerllerDeliveryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjOrderSerllerDelivery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjOrderSerllerDelivery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjOrderSerllerDelivery(ctx, req.(*JddjOrderSerllerDeliveryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjOrderJDZBDelivery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderSerllerDeliveryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjOrderJDZBDelivery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjOrderJDZBDelivery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjOrderJDZBDelivery(ctx, req.(*JddjOrderSerllerDeliveryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjDeliveryEndOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderSerllerDeliveryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjDeliveryEndOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjDeliveryEndOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjDeliveryEndOrder(ctx, req.(*JddjOrderSerllerDeliveryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjAdjustOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjAdjustOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjAdjustOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjAdjustOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjAdjustOrder(ctx, req.(*JddjAdjustOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjOrderShoudSettlementService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjOrderSerllerDeliveryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjOrderShoudSettlementService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjOrderShoudSettlementService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjOrderShoudSettlementService(ctx, req.(*JddjOrderSerllerDeliveryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JddjOrderService_JddjGetBalanceBillList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjGetBalanceBillListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjOrderServiceServer).JddjGetBalanceBillList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjOrderService/JddjGetBalanceBillList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjOrderServiceServer).JddjGetBalanceBillList(ctx, req.(*JddjGetBalanceBillListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _JddjOrderService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.JddjOrderService",
	HandlerType: (*JddjOrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "JddjOrderCancelAndRefund",
			Handler:    _JddjOrderService_JddjOrderCancelAndRefund_Handler,
		},
		{
			MethodName: "JddjCheckSelfPickCode",
			Handler:    _JddjOrderService_JddjCheckSelfPickCode_Handler,
		},
		{
			MethodName: "JddjOrderSelfMention",
			Handler:    _JddjOrderService_JddjOrderSelfMention_Handler,
		},
		{
			MethodName: "GetJddjAfsService",
			Handler:    _JddjOrderService_GetJddjAfsService_Handler,
		},
		{
			MethodName: "GetJddjOrderCalcMoney",
			Handler:    _JddjOrderService_GetJddjOrderCalcMoney_Handler,
		},
		{
			MethodName: "JddjMerchantInitiateAfterSale",
			Handler:    _JddjOrderService_JddjMerchantInitiateAfterSale_Handler,
		},
		{
			MethodName: "JddjConfirmReceipt",
			Handler:    _JddjOrderService_JddjConfirmReceipt_Handler,
		},
		{
			MethodName: "JddjOrderCancelOperate",
			Handler:    _JddjOrderService_JddjOrderCancelOperate_Handler,
		},
		{
			MethodName: "JddjAfsOpenApprove",
			Handler:    _JddjOrderService_JddjAfsOpenApprove_Handler,
		},
		{
			MethodName: "JddjReceiveFailedAudit",
			Handler:    _JddjOrderService_JddjReceiveFailedAudit_Handler,
		},
		{
			MethodName: "JddjGetAfsSeriveOrderList",
			Handler:    _JddjOrderService_JddjGetAfsSeriveOrderList_Handler,
		},
		{
			MethodName: "GetJddjOrderDetail",
			Handler:    _JddjOrderService_GetJddjOrderDetail_Handler,
		},
		{
			MethodName: "JddjOrderAcceptOperate",
			Handler:    _JddjOrderService_JddjOrderAcceptOperate_Handler,
		},
		{
			MethodName: "JddjOrderSerllerDelivery",
			Handler:    _JddjOrderService_JddjOrderSerllerDelivery_Handler,
		},
		{
			MethodName: "JddjOrderJDZBDelivery",
			Handler:    _JddjOrderService_JddjOrderJDZBDelivery_Handler,
		},
		{
			MethodName: "JddjDeliveryEndOrder",
			Handler:    _JddjOrderService_JddjDeliveryEndOrder_Handler,
		},
		{
			MethodName: "JddjAdjustOrder",
			Handler:    _JddjOrderService_JddjAdjustOrder_Handler,
		},
		{
			MethodName: "JddjOrderShoudSettlementService",
			Handler:    _JddjOrderService_JddjOrderShoudSettlementService_Handler,
		},
		{
			MethodName: "JddjGetBalanceBillList",
			Handler:    _JddjOrderService_JddjGetBalanceBillList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/externalJddj.proto",
}

// JddjBussServiceClient is the client API for JddjBussService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type JddjBussServiceClient interface {
	JddjQueryOassBussMoney(ctx context.Context, in *JddjQueryOassBussMoneyRequest, opts ...grpc.CallOption) (*JddjQueryOassBussMoneyResponse, error)
}

type jddjBussServiceClient struct {
	cc *grpc.ClientConn
}

func NewJddjBussServiceClient(cc *grpc.ClientConn) JddjBussServiceClient {
	return &jddjBussServiceClient{cc}
}

func (c *jddjBussServiceClient) JddjQueryOassBussMoney(ctx context.Context, in *JddjQueryOassBussMoneyRequest, opts ...grpc.CallOption) (*JddjQueryOassBussMoneyResponse, error) {
	out := new(JddjQueryOassBussMoneyResponse)
	err := c.cc.Invoke(ctx, "/et.JddjBussService/JddjQueryOassBussMoney", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JddjBussServiceServer is the server API for JddjBussService service.
type JddjBussServiceServer interface {
	JddjQueryOassBussMoney(context.Context, *JddjQueryOassBussMoneyRequest) (*JddjQueryOassBussMoneyResponse, error)
}

// UnimplementedJddjBussServiceServer can be embedded to have forward compatible implementations.
type UnimplementedJddjBussServiceServer struct {
}

func (*UnimplementedJddjBussServiceServer) JddjQueryOassBussMoney(ctx context.Context, req *JddjQueryOassBussMoneyRequest) (*JddjQueryOassBussMoneyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JddjQueryOassBussMoney not implemented")
}

func RegisterJddjBussServiceServer(s *grpc.Server, srv JddjBussServiceServer) {
	s.RegisterService(&_JddjBussService_serviceDesc, srv)
}

func _JddjBussService_JddjQueryOassBussMoney_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JddjQueryOassBussMoneyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjBussServiceServer).JddjQueryOassBussMoney(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjBussService/JddjQueryOassBussMoney",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjBussServiceServer).JddjQueryOassBussMoney(ctx, req.(*JddjQueryOassBussMoneyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _JddjBussService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.JddjBussService",
	HandlerType: (*JddjBussServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "JddjQueryOassBussMoney",
			Handler:    _JddjBussService_JddjQueryOassBussMoney_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/externalJddj.proto",
}

// JddjPlatformServiceClient is the client API for JddjPlatformService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type JddjPlatformServiceClient interface {
	//token更新确认接口
	VerificationUpdateToken(ctx context.Context, in *VerificationUpdateTokenRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type jddjPlatformServiceClient struct {
	cc *grpc.ClientConn
}

func NewJddjPlatformServiceClient(cc *grpc.ClientConn) JddjPlatformServiceClient {
	return &jddjPlatformServiceClient{cc}
}

func (c *jddjPlatformServiceClient) VerificationUpdateToken(ctx context.Context, in *VerificationUpdateTokenRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/et.JddjPlatformService/VerificationUpdateToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JddjPlatformServiceServer is the server API for JddjPlatformService service.
type JddjPlatformServiceServer interface {
	//token更新确认接口
	VerificationUpdateToken(context.Context, *VerificationUpdateTokenRequest) (*empty.Empty, error)
}

// UnimplementedJddjPlatformServiceServer can be embedded to have forward compatible implementations.
type UnimplementedJddjPlatformServiceServer struct {
}

func (*UnimplementedJddjPlatformServiceServer) VerificationUpdateToken(ctx context.Context, req *VerificationUpdateTokenRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerificationUpdateToken not implemented")
}

func RegisterJddjPlatformServiceServer(s *grpc.Server, srv JddjPlatformServiceServer) {
	s.RegisterService(&_JddjPlatformService_serviceDesc, srv)
}

func _JddjPlatformService_VerificationUpdateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerificationUpdateTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjPlatformServiceServer).VerificationUpdateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjPlatformService/VerificationUpdateToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjPlatformServiceServer).VerificationUpdateToken(ctx, req.(*VerificationUpdateTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _JddjPlatformService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.JddjPlatformService",
	HandlerType: (*JddjPlatformServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "VerificationUpdateToken",
			Handler:    _JddjPlatformService_VerificationUpdateToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/externalJddj.proto",
}

// JddjStoreServiceClient is the client API for JddjStoreService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type JddjStoreServiceClient interface {
	// 订单调整接口
	SyncJddjStoreStatus(ctx context.Context, in *SyncJddjStoreStatusRequest, opts ...grpc.CallOption) (*SyncJddjStoreStatusResponse, error)
}

type jddjStoreServiceClient struct {
	cc *grpc.ClientConn
}

func NewJddjStoreServiceClient(cc *grpc.ClientConn) JddjStoreServiceClient {
	return &jddjStoreServiceClient{cc}
}

func (c *jddjStoreServiceClient) SyncJddjStoreStatus(ctx context.Context, in *SyncJddjStoreStatusRequest, opts ...grpc.CallOption) (*SyncJddjStoreStatusResponse, error) {
	out := new(SyncJddjStoreStatusResponse)
	err := c.cc.Invoke(ctx, "/et.JddjStoreService/SyncJddjStoreStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JddjStoreServiceServer is the server API for JddjStoreService service.
type JddjStoreServiceServer interface {
	// 订单调整接口
	SyncJddjStoreStatus(context.Context, *SyncJddjStoreStatusRequest) (*SyncJddjStoreStatusResponse, error)
}

// UnimplementedJddjStoreServiceServer can be embedded to have forward compatible implementations.
type UnimplementedJddjStoreServiceServer struct {
}

func (*UnimplementedJddjStoreServiceServer) SyncJddjStoreStatus(ctx context.Context, req *SyncJddjStoreStatusRequest) (*SyncJddjStoreStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncJddjStoreStatus not implemented")
}

func RegisterJddjStoreServiceServer(s *grpc.Server, srv JddjStoreServiceServer) {
	s.RegisterService(&_JddjStoreService_serviceDesc, srv)
}

func _JddjStoreService_SyncJddjStoreStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncJddjStoreStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JddjStoreServiceServer).SyncJddjStoreStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/et.JddjStoreService/SyncJddjStoreStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JddjStoreServiceServer).SyncJddjStoreStatus(ctx, req.(*SyncJddjStoreStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _JddjStoreService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "et.JddjStoreService",
	HandlerType: (*JddjStoreServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncJddjStoreStatus",
			Handler:    _JddjStoreService_SyncJddjStoreStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "et/externalJddj.proto",
}
